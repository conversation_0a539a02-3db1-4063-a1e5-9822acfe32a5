/*****************************************************************************************************************/
/* $HeadURL::                                                                                               $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "typedefs.h"
#include "timing.h"
#include "digio.h"
#include "recmgm.h"
#include "diagmgm_out.h"
#include "relaymgm.h"
#include "exhval_mgm.h"
#include "activeDiag.h"
#include "hbridge.h"
#include "MisfOBD2_out.h"
#include "lamp_mgm.h"

const uint8_T SelPhysLampGPIO[3][NUMPHYSLMP] = {
    {ODE_Warning_Lamp, ODE_DUMMY_Lamp,   ODE_DUMMY_Lamp, ODE_DUMMY_Lamp},
    {ODE_DUMMY_Lamp,   ODE_Warning_Lamp, ODE_DUMMY_Lamp, ODE_DUMMY_Lamp}, /* Insert here new possible config 2.... */
    {ODE_Warning_Lamp, ODE_DUMMY_Lamp,   ODE_DUMMY_Lamp, ODE_DUMMY_Lamp}  /* Insert here new possible config 3.... */
};

uint8_T LogLampOut[NUMLOGLMP];
uint8_T LogLampPeriod[NUMLOGLMP];
uint8_T LogLampTOn[NUMLOGLMP];

static uint8_T FlgExVPWLampSync = 0;

#ifdef _BUILD_LAMPMGM_

static void LampMgm_MIL(void);
static void LampMgm_Diag(void);
static void LampMgmLogLampSet(uint8_t log_lamp,typLampCmd cmd);
static void LampMgmLogLampMgm(void);
static void LampMgmPhysLampMgm(void);

/*   ----------------Output ------------------------ */
uint8_T PhysLampGPIO[NUMPHYSLMP];
uint8_t PhysLampPeriod[NUMPHYSLMP];
uint8_t PhysLampTOn[NUMPHYSLMP];
uint8_t PhysLampTimer[NUMPHYSLMP];
uint8_T PhysLampOut[NUMPHYSLMP];
uint8_T PhysLmpTest[NUMPHYSLMP];
typLampCmd PhysLampState[NUMPHYSLMP];

typLampCmd  WarningLmpCmd;
typLampCmd  SafetyLmpCmd;
typLampCmd  MILLmpCmd;
typLampCmd  MisfLmpCmd;
uint8_T     StMIL;

typLampCmd LogLampState[NUMLOGLMP];

int16_T WarnLampError; // debug !!!

extern uint8_T SELPHYSLAMPGPIO;
extern uint8_T LAMPTESTTIME[NUMPHYSLMP];
extern uint8_T LOGLAMPOUT[NUMLOGLMP];
extern uint8_T LOGLAMPPERIOD[NUMLOGLMP];
extern uint8_T LOGLAMPTON[NUMLOGLMP];
extern uint8_T WARNLAMPTESTSW;
extern uint8_T SAFLAMPTESTSW;
extern uint8_T MILLAMPTESTSW;
extern uint8_T MISFLAMPTESTSW;
extern uint8_T FORCELAMP;
extern uint8_T MILMILEN;

// WarningLmpCmd management
void WarningLmp_T100ms(void) 
{
#ifdef IDN_WRNLAMP
    static uint8_T wlampPSts = 0;

    if (WARNLAMPTESTSW != 0)
    {
        WarningLmpCmd = LAMP_ON;
    }
    else
    {
        if ((FlgExVPWLampSync != 0) || (wlampPSts != 0))
        {
            FlgExVPWLampSync = 0;
            WarningLmpCmd = 1;
            if (wlampPSts >= 15)
            {
                wlampPSts = 0;
            }
            else
            {
                wlampPSts++;
            }
        }
        else
        {
            WarningLmpCmd = (typLampCmd)VtRec[REC_WARNING_LAMP];
        }
    }
#endif
}

// Safety lamp command management
void SafetyLmp_T100ms(void) 
{  
#ifdef IDN_SAFLAMP
  
    if (SAFLAMPTESTSW != 0)
    {
        SafetyLmpCmd = LAMP_ON;
    }
    else
    {
        SafetyLmpCmd = (typLampCmd)VtRec[REC_SAFETY_LAMP];
    }
  
#endif
}

// MIL lamp command management
void MILLamp_T100ms(void) 
{  
#ifdef IDN_MILLAMP
  
    if (MILLAMPTESTSW != 0)
    {
        MILLmpCmd = LAMP_ON;
    }
    else
    {
        if (MILCmd_Flag_Busy == BUSY)
        {
            MILLmpCmd = (MIL_LampStatus != MIL_LAMP_GLOBAL_OFF);
        }
        else
        {
            if (DrivingCycle == DRVC_OFF)
            {
                MILLmpCmd = ((MILMILEN != 0) && (KeySignal != 0));
            }
            else
            {
                if (FlgOBD2MILFlashing == 0)
                {
                    MILLmpCmd = (MIL_LampStatus != MIL_LAMP_GLOBAL_OFF);
                }
                else
                {
                    MILLmpCmd = MIL_LAMP_GLOBAL_OFF;
                }
            }
        }
    }
    StMIL = (MIL_LampStatus != MIL_LAMP_GLOBAL_OFF);
  
#endif
}

// Misfire lamp command management
void MisfLmp_T100ms(void) 
{  
#ifdef IDN_MISFLAMP
  
    if (MISFLAMPTESTSW != 0)
    {
        MisfLmpCmd = LAMP_ON;
    }
    else
    {
        if (FlgOBD2MILFlashing != 0)
        {
            MisfLmpCmd = LAMP_ON;
        }
        else
        {
            MisfLmpCmd = LAMP_OFF;
        }
    }
  
#endif
}

// evento power on
void LampMgm_Init(void) 
{
    uint8_t i = 0;

    /* Logical lamps commands initialisation */
#ifdef IDN_WRNLAMP
    WarningLmpCmd  = LAMP_OFF;
#endif
#ifdef IDN_SAFLAMP
    SafetyLmpCmd  = LAMP_OFF;
#endif
#ifdef IDN_MILLAMP
    MILLmpCmd  = LAMP_OFF;
#endif
#ifdef IDN_MISFLAMP
    MisfLmpCmd  = LAMP_OFF;
#endif

    for (i = 0; i < NUMPHYSLMP; i++)
    {
        if (SELPHYSLAMPGPIO == 0)
        {
            PhysLampGPIO[i] = SelPhysLampGPIO[0][i];
        }
        else if (SELPHYSLAMPGPIO == 1)
        {
            PhysLampGPIO[i] = SelPhysLampGPIO[1][i];
        }
        else if (SELPHYSLAMPGPIO == 2)
        {
            PhysLampGPIO[i] = SelPhysLampGPIO[2][i];
        }
        else 
        {
            PhysLampGPIO[i] = SelPhysLampGPIO[0][i];
        }
    
        PhysLampOut[i] = LAMP_OFF;
        PhysLampTimer[i] = 0;
        PhysLampPeriod[i] = 0;
        PhysLampState[i] = LAMP_OFF;
        PhysLampTOn[i] = 0;
        PhysLmpTest[i] = LAMPTESTTIME[i];
    }

    for (i = 0; i < NUMLOGLMP; i++)
    {
        if (LOGLAMPOUT[i] < NUMPHYSLMP)
        {
            LogLampOut[i] = LOGLAMPOUT[i];
        }
        else 
        {
            LogLampOut[i] = 0; /* WARNING: CALIB CONFIG ERROR */
        }
        LogLampPeriod[i] = LOGLAMPPERIOD[i];
        if (LOGLAMPPERIOD[i] >= LOGLAMPTON[i])
        {
            LogLampTOn[i] = LOGLAMPTON[i];
        }
        else { /* MISRA */ }
    }

    if (FORCELAMP == ALL_LAMP || FORCELAMP == PIN_LAMP)
    {
        if (PhysLampGPIO[0] != ODE_DUMMY_Lamp)
        {
            DIGIO_OutCfgExt(PhysLampGPIO[0], PhysLampState[0], OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
        }
        else { /* MISRA */ }
        if (PhysLampGPIO[1] != ODE_DUMMY_Lamp)
        {
            DIGIO_OutCfgExt(PhysLampGPIO[1], PhysLampState[1], OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
        }
        else { /* MISRA */ }
        if (PhysLampGPIO[2] != ODE_DUMMY_Lamp)
        {
            DIGIO_OutCfgExt(PhysLampGPIO[2], PhysLampState[2], OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
        }
        else { /* MISRA */ }
        if (PhysLampGPIO[3] != ODE_DUMMY_Lamp)
        {
            DIGIO_OutCfgExt(PhysLampGPIO[3], PhysLampState[3], OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
        }
        else { /* MISRA */ }
    }
    else { /* MISRA */ }

}

void LampMgm_KeyOn(void) 
{
    LampMgm_Init();
}

void LampMgm_T10ms(void)
{
    if (FlgExVPWLamp != 0)
    {
        FlgExVPWLampSync = 1;
    }
    else { /* MISRA */ }
} 

void LampMgm_T100ms(void)
{
    /* Diagnostics */
    LampMgm_Diag();
    
    /* Application level management  */
    WarningLmp_T100ms();
    SafetyLmp_T100ms();
    MILLamp_T100ms();
    MisfLmp_T100ms();
     
    /* Logical lamps management */
    #ifdef IDN_WRNLAMP
    LampMgmLogLampSet(IDN_WRNLAMP, WarningLmpCmd);        
    #endif
    
    #ifdef IDN_SAFLAMP
    LampMgmLogLampSet(IDN_SAFLAMP, SafetyLmpCmd);        
    #endif

    #ifdef IDN_MILLAMP
    LampMgmLogLampSet(IDN_MILLAMP, MILLmpCmd);        
    #endif

    #ifdef IDN_MISFLAMP
    LampMgmLogLampSet(IDN_MISFLAMP, MisfLmpCmd);        
    #endif
    
    /* Logic lamp management */
    LampMgmLogLampMgm();

    /* Physical lamp management */
    LampMgmPhysLampMgm();

    /* Calcolo stato lampada MIL */
    LampMgm_MIL();
}

// MIL lamp management
static void LampMgm_MIL(void) 
{  
    static uint8_T tmpMILTrig = 0;

    if (StMIL != 0)
    {
        CntDiagWarmUpCycle = THRDIAGWUC;
    }
    else
    {
        if (WarmUpCycle == WUC_ENDED)
        {
            if (tmpMILTrig == 0)
            {
                if (CntDiagWarmUpCycle > 0)
                {
                    CntDiagWarmUpCycle--;
                }
                else { /* MISRA */ }
                tmpMILTrig = 1;
            }
            else { /* MISRA */ }
        }
        else
        {
            tmpMILTrig = 0;
        }
    }
}

static void LampMgm_Diag(void) 
{
    /* Test is defined only for lamp 1 at the moment */
    
#if defined(IDN_LAMP_1)
 #ifdef IDE_Lmp_1_Fault
   static  uint8_T PtFaultWarningLmp    = NO_PT_FAULT;
           uint8_T StDiagWarningLmp     = NO_FAULT;
 
    WarnLampError = LSD_GetFeedbackStatusAndError(OUT_LAMP_1, IDE_Lmp_1_Fault, &PtFaultWarningLmp);
   
    if (WarnLampError != LSD_DIAG_OFF)
    {
        DiagMgm_SetDiagState(DIAG_LSD_WARNLAMP, PtFaultWarningLmp, &StDiagWarningLmp);
    }  
 #endif
#endif 
}

static void LampMgmLogLampSet(uint8_t log_lamp, typLampCmd cmd)
{
    if (log_lamp < NUMLOGLMP)
    {
        LogLampState[log_lamp] = cmd;
    }
    else { /* MISRA */ }
}

static void LampMgmLogLampMgm(void)
{

    uint8_t phys_lamp = 0;
    uint8_t i = 0;
    typLampCmd TempPhysLampState[NUMPHYSLMP];
 
    for (i=0; i < NUMPHYSLMP; i++)
    {
        TempPhysLampState[i] = LAMP_OFF;
    }
    
    for (i=0; i < NUMLOGLMP; i++)
    {
        phys_lamp = LogLampOut[i];
        if (LogLampState[i] != LAMP_OFF)
        {
            TempPhysLampState[phys_lamp] = LAMP_ON;
            if (  (PhysLampState[phys_lamp] == LAMP_OFF)
                ||(PhysLampTOn[phys_lamp] != LogLampTOn[i])
                ||(PhysLampPeriod[phys_lamp] != LogLampPeriod[i])
               )
            {
                PhysLampTimer[phys_lamp] = 0;
                PhysLampPeriod[phys_lamp] = LogLampPeriod[i];
                PhysLampTOn[phys_lamp] = LogLampTOn[i];
                if (PhysLampTOn[phys_lamp] < PhysLampPeriod[phys_lamp])
                {
                    PhysLampState[phys_lamp] = LAMP_BLINK;
                }
                else
                {
                    PhysLampState[phys_lamp] = LAMP_ON;  
                }
            }
        }
    }

    for (i=0; i < NUMPHYSLMP; i++)
    {
        if (TempPhysLampState[i] == LAMP_OFF)
        {
            PhysLampTimer[i] = 0;
            PhysLampPeriod[i] = 0;
            PhysLampTOn[i] = 0;
            PhysLampState[i] = LAMP_OFF;
        }
    }
}

static void LampMgmPhysLampMgm(void)
{
    uint8_t i = 0;
    uint8_T tmp_lmpcmd = LAMP_OFF;

    for (i = 0; i < NUMPHYSLMP; i++)
    {
        if (PhysLmpTest[i] > 0)
        {
            PhysLmpTest[i]--;            
            tmp_lmpcmd = LAMP_ON;        
        }
        else if (PhysLampState[i] == LAMP_ON || PhysLampState[i] == LAMP_BLINK)
        {
            PhysLampTimer[i]++;
            if (PhysLampTimer[i] < PhysLampTOn[i])
            {
                tmp_lmpcmd = LAMP_ON;       
            }
            else if (PhysLampTimer[i] < PhysLampPeriod[i])
            {
                tmp_lmpcmd = LAMP_OFF;             
            }
            else
            {
                PhysLampTimer[i] = 0;
                tmp_lmpcmd = LAMP_ON;       
            }
        }
        else
        {
            tmp_lmpcmd = LAMP_OFF;   
            /* Paranoia setting */
            PhysLampTimer[i] = 0;
        }
        if (FORCELAMP == ALL_LAMP || FORCELAMP == CAN_LAMP)
        {
            PhysLampOut[i] = tmp_lmpcmd;
        }
        if ((FORCELAMP == ALL_LAMP || FORCELAMP == PIN_LAMP) && (PhysLampGPIO[i] != ODE_DUMMY_Lamp))
        {
            PhysLampOut[i] = tmp_lmpcmd;
            DIGIO_OutSet(PhysLampGPIO[i], tmp_lmpcmd);
        }
    }
}

typLampCmd Get_Lamp_Status (uint8_T phys_lamp)
{
    uint8_T LmpCmd = 0;
    
    if (PhysLampState[phys_lamp] == LAMP_OFF)
    {
        LmpCmd = LAMP_OFF; 
    }
    else 
    {
        if (PhysLampTOn[phys_lamp] == PhysLampPeriod[phys_lamp])
        {
            LmpCmd = LAMP_ON; 
        }
        else
        {
            if (PhysLampTOn[phys_lamp] < PhysLampPeriod[phys_lamp])
            {
                LmpCmd = LAMP_BLINK; 
            }
            else { /* MISRA */ }
        }
    }

    return LmpCmd;
}

#endif // _BUILD_LAMPMGM_
