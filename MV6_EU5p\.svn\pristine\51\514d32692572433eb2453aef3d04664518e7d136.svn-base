#include "typedefs.h"
#include "hbridge.h"
#include "SPIST_L9958.h"

#ifdef _BUILD_HBRIDGE_

#pragma ghs section rodata=".calib"

//DBW output command period [us]
__declspec(section ".calib") uint16_T HB1PERIOD = 2500;   //   2.5 ms // doppia calib.
//VScarico output command period [us]
__declspec(section ".calib") uint16_T HB2PERIOD = 2500;   //   2.5 ms // doppia calib.

#ifdef TEST_MSG_HB
__declspec(section ".calib") uint16_T SPISTL9958DEBFRAMEH1 = SPIST_L9958_TEST;
__declspec(section ".calib") uint16_T SPISTL9958DEBFRAMEH2 = SPIST_L9958_TEST;
#endif

#endif // _BUILD_HBRIDGE_
