/*****************************************************************************************************************/
/* $HeadURL:: http://************:8080/svn/Rep_Bo/EM/appl_calib/branches/AK_MV2_08/tree/BIOS/NEXUS/s#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1948   $                                                                                          */
/* $Date:: 2009-11-18 11:10:59 +0100 (mer, 18 nov 2009)   $                                                      */
/* $Author:: ToniS                   $                                                                       */
/*****************************************************************************************************************/



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "Censorship_recovery_ssd.h"
#include "typedefs.h"
#include "ssd_c90fl.h"
#include "sys.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* Externals */
extern SSD_CONFIG ssdConfig_BK0A0;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define CENSORSHIP_ADDRESS   (0x00FFFDE0)
#define CENSORSHIP_VALUE     (0x55AA55AA)
#define SERIAL_BOOT_ADDRESS  (0x00FFFDD8)
#define SERIAL_BOOT_ADDRESS1 (0x00FFFDDC)
#define SERIAL_BOOT_PWD1     (0xBABABABA)
#define SERIAL_BOOT_PWD2     (0xBEBEBEBE)

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * checkFlashPWD - Check flash serial pwd on shadow region
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void checkFlashPWD(void)
{

    const uint32_t serialPassword_buff[] =
    { 
        0xFFFFFFFF, 0xFFFFFFFF, SERIAL_BOOT_PWD1, SERIAL_BOOT_PWD2 
    };

    const uint32_t censorship_buff[] =
    { 
        CENSORSHIP_VALUE, CENSORSHIP_VALUE, 0xFFFFFFFF, 0xFFFFFFFF 
    };

    
        if((*((uint32_t*)SERIAL_BOOT_ADDRESS) == SERIAL_BOOT_PWD1)  && 
           (*((uint32_t*)SERIAL_BOOT_ADDRESS1) == SERIAL_BOOT_PWD2))
        {
            /* DO NOTHING. PASSWORD IS ALREADY CORRECTLY SET !!!*/
        }
        else
        {
                DisableAllInterrupts();
                SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_PRIMARY, 0, FLASH_LMLR_PASSWORD );
                SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_SECONDARY, 0, FLASH_SLMLR_PASSWORD );
                //if((*((uint32_t*)SERIAL_BOOT_ADDRESS) == 0xFFFFFFFF)  && 
                //   (*((uint32_t*)SERIAL_BOOT_ADDRESS1) == 0xFFFFFFFF))
                //{
                    FlashErase( &ssdConfig_BK0A0, 1, 0, 0, 0, (void(*)(void))NULL_CALLBACK );
                //}
                // program serial flash password                
                FlashProgram(&ssdConfig_BK0A0,(SERIAL_BOOT_ADDRESS - 0x8),sizeof(serialPassword_buff),(uint32_t)serialPassword_buff,(void(*)(void))NULL_CALLBACK);
                // programming censorship word
                FlashProgram(&ssdConfig_BK0A0,CENSORSHIP_ADDRESS,sizeof(censorship_buff),(uint32_t)censorship_buff,(void(*)(void))NULL_CALLBACK);
                SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_PRIMARY, 1, FLASH_LMLR_PASSWORD );
                SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_SECONDARY, 1, FLASH_SLMLR_PASSWORD );
                EnableAllInterrupts();
        }
    
    //return returnVal;
}

 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

