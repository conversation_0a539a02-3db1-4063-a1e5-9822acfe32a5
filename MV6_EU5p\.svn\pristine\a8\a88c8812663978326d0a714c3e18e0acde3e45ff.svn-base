/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "OS_api.h"
#include "tasksdefs.h"

#include "sci.h"
#include "scimgm.h"
#include "string.h"
#include "kline_exception.h"
#include "diagcanmgm.h"

#if defined(_BUILD_SCIMGM_) && defined(_BUILD_SCI_)


static  const   uint8_t     helloBuf[2][20] = 
{
    "SCI CH#A\n\r",
    "SCI CH#B\n\r"
};
static          uint8_t     helloFlg[2];
static          uint8_t     iBuf[2];


void SCIMGM_Test(uint8_t ch);
void SCIMGM_ChannelInit(uint8_t ch);

void SCIMGM_ChannelInit(uint8_t ch)
{
    SCI_TxEnable(ch, TX_ENABLE);
    SCI_RxEnable(ch, RX_ENABLE);

    helloFlg[ch]    = FALSE;
    iBuf[ch]        = 0;
}   // end SCIMGM_ChannelInit()

void SCIMGM_Test(uint8_t ch)
// input:
//      ch = SCI channel (SCI_CH_A / SCI_CH_B)
// output: 
//      <none>
{
    uint16_t    charB;
    int16_t     errChB;

    if (!helloFlg[ch])   // send hello buffer character by character
    {
        if (!helloBuf[ch][iBuf[ch]])
        {
            helloFlg[ch] = TRUE;
        }
        else
        {
            SCI_TxData(ch, helloBuf[ch][iBuf[ch]]);
            iBuf[ch]++;
        }
    }
    else        // hello buffer finished
    {
    
        // receive data from ch B
        errChB = SCI_RxData(ch, &charB);
        switch(errChB)
        {
            case NO_ERROR:			// rx data ok
                // send data to ch B
                SCI_TxData(ch, charB);
                break;
            case SCI_DATA_REGISTER_FAULT:     // rx data reg empty
                break;
            default:   // error
                break; 
        }
    }
}   // end SCIMGM_Test(.)

void SCIMGM_Init(void) 
{
    SCIMGM_ChannelInit(SCI_CH_A);
    SCIMGM_ChannelInit(SCI_CH_B);
} // end SCIMGM_Init()

void SCIMGM_Main(void) 
{
    SCIMGM_Test(SCI_CH_A);
    SCIMGM_Test(SCI_CH_B);
} // end SCIMGM_Send100ms()


/****** Exception boby eSCI_A module ******/

void FuncSCI_ExStopTxA(void)
{

  TerminateTask();
} /* End SCI_ExStopTxA */


void FuncSCI_ExStopRxA(void)
{

  TerminateTask();
} /* End SCI_ExStopRxA */


void FuncSCI_ExOverrunErrorA(void)
{

  TerminateTask();
} /* End SCI_ExOverrunErrorA */



/****** Exception boby eSCI_B module ******/

void FuncSCI_ExStopTxB(void)
{

  TerminateTask();
} /* End SCI_ExStopTxB */


void FuncSCI_ExStopRxB(void)
{

  TerminateTask();
} /* End SCI_ExStopRxB */


void FuncSCI_ExOverrunErrorB(void)
{

  TerminateTask();
} /* End SCI_ExOverrunErrorB */

#endif  // _BUILD_SCIMGM_

