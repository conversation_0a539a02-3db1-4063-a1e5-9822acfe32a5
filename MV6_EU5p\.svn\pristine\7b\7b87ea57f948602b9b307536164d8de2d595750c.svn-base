/*
 * File: C:\localmodules\local_AirMgm\slprj\ert\_sharedutils\mul_u32_u32_u32_sr8.c
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2401
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Wed Feb 24 13:37:14 2016
 */

#include "rtwtypes.h"
#include "rtw_shared_utils.h"

uint32_T mul_u32_u32_u32_sr8(uint32_T a, uint32_T b)
{
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_u32(a, b, &u32_chi, &u32_clo);
  u32_clo = (u32_chi << 24U) | (u32_clo >> 8U);
  return u32_clo;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
