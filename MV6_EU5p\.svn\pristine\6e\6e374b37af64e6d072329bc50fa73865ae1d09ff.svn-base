/** #########################################################################
**     Filename  : SCIMGM.H
**     Author    : <PERSON>. Toni
**     Project   :
**     Processor : MPC5554
**     Version   :
**     Date/Time : 22/09/2005
**     Abstract  :
**
**         SCI Device Driver Header File
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright ELDOR Corporation
**
** ######################################################################### */
#ifndef _LIN_TL_
#define _LIN_TL_


extern uint8_t LIN_lastPID;

/* timeout definition */
#define LIN_TOUT_DELAY      (0x7FF) /* half of the maximum delay */

/* errors definition */
#define LIN_TL_INVALID_BAUD_RATE   (-3)

/* header definitions */
#define LIN_HDCHK       ((uint8_t)0x80)
#define LIN_CSUM        ((uint8_t)0x40)
#define LIN_TX_FRM      ((uint8_t)0x10)



// PUBLIC METHODS:

int16_t LIN_Config(void);
int16_t LIN_TransmitFrame(const uint8_t * pTxBuffer, uint8_t size, uint8_t IDFrame);
int16_t LIN_ReceiveAnswer(uint8_t IDFrame);

int16_t LIN_GetStatus(uint16_t * pStatus);

int16_t LIN_SetDiagCmd(uint8_t * pTxBuffer, uint8_t NAD);
int16_t LIN_GetDiagRes(uint8_t * pRxBuffer, uint8_t NAD);

int16_t LIN_SetChecksumWord(uint32_t ChecksumWord);

int16_t LIN_GoToSleep(void);
int16_t LIN_WakeUp(void);


#endif //_LIN_TL_
