/*
 * File: rpm_limiter.h
 *
 * Code generated for Simulink model 'RpmLimiter'.
 *
 * Model version                  : 1.2468
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Sep 27 12:05:44 2022
 */

#ifndef RTW_HEADER_rpm_limiter_h_
#define RTW_HEADER_rpm_limiter_h_
#include "rtwtypes.h"

/* Exported define declaration */
#define DISABLED_LIM                   ((uint8_T) 0U)
#define NO_LIM                         ((uint8_T) 1U)
#define CMI_FILTER_LIM                 ((uint8_T) 2U)
#define PROTECTION_LIM                 ((uint8_T) 3U)
#define SMOOTH_LIM                     ((uint8_T) 4U)

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmiBlowByProtPMin;

/* Torque to limit BlowBy */
extern int16_T CmiMaxTrStop;

/* Predicted CMI target */
extern int32_T CmiRpmLimitINoSat;

/* Torque to limit rpm (not saturated) */
extern int32_T CmiRpmLimitP;

/* PI proportional term */
extern int32_T CmiRpmLimitPNoSat;

/* PI proportional term */
extern int32_T CmiRpmLimiter;

/* Torque to limit rpm */
extern int16_T CmiTargetI;

/* Instantaneous CMI target */
extern int16_T CmiTargetP;

/* Predicted CMI target */
extern uint8_T CtfLimiterFlg;

/* Enable cutoff to limit rpm (=1) */
extern uint16_T DeltaRpm;

/* Delta Rpm */
extern uint8_T EnRpmLimiter;

/* The rpm limiter is active (=1) */
extern uint8_T FlgCmiLimit;

/* The input torque is saturated */
extern uint8_T FlgPropRLimCtf;

/* Rpm limiter prop ctf */
extern uint8_T FlgRpmLIReduct;

/* Cutoff reduction flag */
extern int16_T GainRpmLimitProp;

/* Gain */
extern uint32_T IDRpmLimiter;

/* ID Version */
extern uint8_T IdxRpmLCutOff;

/* Index of CutOff */
extern uint8_T LimiterFlg;

/* Enable limit rpm (=1) */
extern int16_T RpmLimErr;

/* Rpm error for limiter */
extern int16_T RpmLimErrCtf;

/* Rpm error for limiter in cutoff */
extern int16_T RpmLimErrNoSat;

/* Rpm error for limiter */
extern uint16_T RpmMaxCorr;

/* Actual upper limit to rpm */
extern uint16_T RpmOverspeed;

/* Engine overspeed */
extern uint8_T StRpmLimiter;

/* Rpm limiter control state */
extern int32_T rpmlimitdeltaprop;

/* PI proportional term */
extern int32_T rpmlimitint;

/* PI integral term */
extern int32_T rpmlimitprop;

/* PI proportional term */
#endif                                 /* RTW_HEADER_rpm_limiter_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
