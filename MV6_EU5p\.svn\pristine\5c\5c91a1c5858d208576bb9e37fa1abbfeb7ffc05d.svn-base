#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_VSPEEDCTRL_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 

//vehicle speed err threshold to enable cutoff for vehicle speed limiter [Km/h]
__declspec(section ".calib") int16_T DVSPEEDOUTLIMIT = 1280;   //( 10.0000000*128)
//Activate the vehicle speed limiter (=1) [flag]
__declspec(section ".calib") uint8_T ENVSPEEDCTRL =  1;   // 1
//vehicle speed err threshold to enable cutoff for vehicle speed limiter [Km/h]
__declspec(section ".calib") int16_T VSPEEDLIMERRCTF = 1920;   //( 15.0000000*128)
//Max vehicle speed err for vehicle speed limiter [Km/h]
__declspec(section ".calib") int16_T VSPEEDLIMERRMAX = 384;   //(  3.0000000*128)
//Integral gain for vehicle speed limiter [coeff]
__declspec(section ".calib") uint16_T VSPEEDLIMINTGAIN = 164;   //( 0.040039062500*4096)
//Proportional gain for vehicle speed limiter [coeff]
__declspec(section ".calib") uint16_T VSPEEDLIMPROPGAIN = 6144;   //( 1.500000000000*4096)

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_VSPEEDCTRL_
