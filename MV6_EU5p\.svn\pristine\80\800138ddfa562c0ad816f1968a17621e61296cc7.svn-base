/*
 * File: idlectf_mgm.h
 *
 * Code generated for Simulink model 'IdleCtfMgm'.
 *
 * Model version                  : 1.2297
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Mar  1 09:28:46 2022
 */

#ifndef RTW_HEADER_idlectf_mgm_h_
#define RTW_HEADER_idlectf_mgm_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmeDriverMax;

/* Max CmeDriverP before decrease */
extern uint8_T CmeDriverMaxDisFilt;

/* CmeDriverMax Enable filtering */
extern uint16_T CutoffGain;

/* Cutoff gain factor */
extern uint32_T CutoffGainFHr;

/* CutoffGain high-res variable */
extern uint8_T EnCutOff;

/* Cutoff enabled condition */
extern uint8_T FlgCmeLow;

/* Cme under idle/cutoff threshold */
extern uint32_T IDIdleCtfMgm;

/* ID Version */
extern uint8_T IdleReqFlg;

/* CME enable for idle mode  */
extern uint16_T KfGainCutOff;

/* CmeDriverMax Kf Gain for CutOff */
extern uint16_T KfGearGnCutoff;

/* KfGearGnCutoff */
extern uint16_T RpmIdleEntry;

/* Rpm to activate idle */
extern uint8_T StCmeDriverMax;

/* Status of CmeDriverMax */
extern int16_T TmpCmeDriverMax;

/* Max CmeDriverP filtered before decrease */
extern uint32_T timerCutoff;

/* Last timer value of a cutoff exit */
#endif                                 /* RTW_HEADER_idlectf_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
