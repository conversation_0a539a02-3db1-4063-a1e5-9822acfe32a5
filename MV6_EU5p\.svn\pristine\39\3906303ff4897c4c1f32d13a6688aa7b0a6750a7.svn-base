/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Application/KNOCKCORRE/branches/KNOCKCORR_MV/KnockCorr_ert_rt#$  */
/* $Revision:: 8016                                                                                           $  */
/* $Date:: 2019-10-22 11:50:14 +0200 (mar, 22 ott 2019)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorr_types.h
 **  Date:          22-Oct-2019
 **
 **  Model Version: 1.1979
 ******************************************************************************
 **/
#ifndef RTW_HEADER_KnockCorr_types_h_
#define RTW_HEADER_KnockCorr_types_h_
#endif                                 /* RTW_HEADER_KnockCorr_types_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
