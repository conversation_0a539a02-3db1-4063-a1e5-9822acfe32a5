/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_PHASE_

/*--------------------------------------------------------------------*/
/* include files */
/*--------------------------------------------------------------------*/
#include "typedefs.h"
#include "pio.h"
#include "ETPU_Shared.h"
#include "phase.h"
#include "sys.h"

#include "..\include\ETPU_HostInterface.h"
#include "..\auto\etpu_angle_clock_func_auto.h"
#include "etpu_util.h"

#include "..\auto\etpu_PHASE_auto.h"
#include ".\include\ETPU_phase.h"


/*--------------------------------------------------------------------*/
/* driver functions                                                   */
/*--------------------------------------------------------------------*/
int16_t PHASE_Config        (void)
{
   uint32_t ParameterBaseAddress;
   uint32_t temp;
   int16_T  errtmp = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
       return PERIPHERAL_FAILURE;
#endif

//   ParameterBaseAddress = ETPU_ChannelInitRam(CAMSENSE_CHANNEL, PHASE_FUNC_NUM_PARMS);
   errtmp = ETPU_ChannelInitRam(CAMSENSE_CHANNEL, PHASE_FUNC_NUM_PARMS, &ParameterBaseAddress );

   if (errtmp != NO_ERROR)
   {
#ifdef CHECK_BIOS_FAULTS
       BIOS_Faults |= (BIOS_FAILURE_PHASE | BIOS_FAILURE_ETPU);
#endif /* CHECK_BIOS_FAULTS */
       return PHASE_EXEC_NOT_OK; 
   }
   
   /* Initialize channel 1 to manage phase signal */   
   returnCode = ETPU_ChannelInit( CAMSENSE_CHANNEL, 
                                 (uint16_t)FS_ETPU_PRIORITY_LOW, 
                                 (uint16_t)ETPU_COUNT_MODE_CONT, 
                                 (uint16_t)PHASE_FUNC_FUNCTION_NUMBER, 
                                 (uint16_t)PHASE_FUNC_TABLE_SELECT, 
                                 ParameterBaseAddress);

   if(returnCode != NO_ERROR) 
      return returnCode;
   
   temp = 0;
   write_phaseChanFlags(temp, CAMSENSE_CHANNEL);
   
   return NO_ERROR;
}


/*--------------------------------------------------------------------*/
int16_t PHASE_Init        (uint32_t * patternStructure, 
                           uint32_t  entriesNum)
{
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
        return PERIPHERAL_FAILURE;
#endif

   if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
      return PHASE_EXEC_NOT_OK;  

   write_Ph_StallPeriod(PH_STALL_PERIOD);                      // 100 msec
   write_Ph_BlankingPeriod(PH_BLANKING_PERIOD);             // 10 us
   write_Ph_StartUpBlankingPeriod(PH_STARTUP_BLK_PERIOD);   // 30 ms
   
   write_Ph_EdgesToSkip(PHASE_EDGES_TO_SKIP); //!!!
                                                                                    
   if (patternStructure != NULL)
   {  // normal mode
      PHASE_SetPattern  (patternStructure, entriesNum);
   }
   else
   {  // recovery mode
      //PHASE_SetMode(PHASE_RECOVERY_MODE);
   }
      
   ETPU_EnableInterruptUC (CAMSENSE_CHANNEL);
      
   return NO_ERROR;
}  




/*--------------------------------------------------------------------*/
int16_t PHASE_SetStatus   (t_State enablingStatus)
{
   uint32_t channelFlags;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
       return PERIPHERAL_FAILURE;
#endif

   if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
      return PHASE_EXEC_NOT_OK;
   
   channelFlags = 0x00FFFFFF & read_phaseChanFlags(CAMSENSE_CHANNEL);
        
   channelFlags |= ((enablingStatus == ENABLE)?EXCEPTIONS_ENABLED:0);
   
   if (enablingStatus == ENABLE)
   {
     write_phaseChanFlags(channelFlags, CAMSENSE_CHANNEL);

     if (channelFlags & PHASE_RECOVERY_MODE)
     {
        return ETPU_set_hsr(CAMSENSE_CHANNEL, HSR_RECOVERY_PHASE_VAL);  // HSR for RECOVERY
     }
     else
     {
        return ETPU_set_hsr(CAMSENSE_CHANNEL, HSR_INIT_PHASE_VAL);   // HSR for PHASE
     }
   }
  
   return NO_ERROR;
}   


/*--------------------------------------------------------------------*/
int16_t PHASE_SetMode     (uint32_t Mode)
{   
   volatile uint32_t tmpReg;
   
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
       return PERIPHERAL_FAILURE;
#endif

   if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
      return PHASE_EXEC_NOT_OK;
   
   switch(Mode)
   {
      case PHASE_NORMAL_MODE:
         tmpReg = 0x00FFFFFF & read_phaseChanFlags(CAMSENSE_CHANNEL);
         write_phaseChanFlags(tmpReg | PHASE_NORMAL_MODE, CAMSENSE_CHANNEL);
               write_PhaseStatus(NO_PHASED_STATUS);
      break;

      case PHASE_RECOVERY_MODE:
      
         write_Ph_StallPeriod(PH_STALL_PERIOD);                    // 100 msec
         write_Ph_BlankingPeriod(PH_BLANKING_PERIOD);             // 10 us
         write_Ph_StartUpBlankingPeriod(PH_STARTUP_BLK_PERIOD);   // 30 ms
      
         tmpReg = 0x00FFFFFF & read_phaseChanFlags(CAMSENSE_CHANNEL);
         write_phaseChanFlags(tmpReg | PHASE_RECOVERY_MODE, CAMSENSE_CHANNEL);
         write_PhaseStatus(SELF_LEARNING_STATUS);
      break;
      
      default:
         return PHASE_EXEC_NOT_OK;
      break;
   }
     
//   ETPU_set_hsr(CAMSENSE_CHANNEL, HSR_RECOVERY_PHASE_VAL);     // HSR for RECOVERY


   return NO_ERROR;
}


/*--------------------------------------------------------------------*/
int16_t PHASE_GetLevel    (uint32_t * LevelValue)
{
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
        return PERIPHERAL_FAILURE;
#endif

    if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
        return PHASE_EXEC_NOT_OK;
    *LevelValue = ETPU.CHAN[CAMSENSE_CHANNEL].SCR.B.IPS; 

    return NO_ERROR;
}   


/*--------------------------------------------------------------------*/
int16_t PHASE_GetTime     (uint32_t * TimeValue)
{
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
        return PERIPHERAL_FAILURE;
#endif

    if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
        return PHASE_EXEC_NOT_OK;
    *TimeValue = read_Ph_EdgeTime(); 

    return NO_ERROR;
}
    
                       
/*--------------------------------------------------------------------*/
int16_t PHASE_GetAngle    (uint32_t * AngleValue)
{
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
        return PERIPHERAL_FAILURE;
#endif

    if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
        return PHASE_EXEC_NOT_OK;

    if ((read_CrankStatus() == FULL_SYNC) || (read_CrankStatus() == HALF_SYNC))
        *AngleValue = read_Ph_AngleTransition(); 
    else
        *AngleValue = NOT_SYNCRO_ANGLE;

    return NO_ERROR;
}   

/*--------------------------------------------------------------------*/

int16_t PHASE_SetPattern  (uint32_t * PatternStructure,
                           uint32_t entriesNumber)
{
    uint32_t i;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
        return PERIPHERAL_FAILURE;
#endif

    if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
        return PHASE_EXEC_NOT_OK;

    /* Disable the eTPU channel */    
    fs_etpu_disable(CAMSENSE_CHANNEL);

    if ((!(entriesNumber % 2)) && (entriesNumber <= MAX_EDGE_NUMB_ADMITTED))
    {// only if the edged are an even number, and if < 10, it is possible to
        //  program the Pattern Table
        for (i=0; i < entriesNumber; i++)
        { /* Programming the Table */
            write_PhTableEntry(i,
            *PatternStructure++,
            *PatternStructure++,
            *PatternStructure++,
            *PatternStructure++);
        }

        /* Programming the Entry's Number */
        write_PhEntriesNumber(entriesNumber , CAMSENSE_CHANNEL); // value , channel
    }
    else
        return PHASE_PATTERN_NOT_CORRECT;

    /* Enable the eTPU channel */ 
    fs_etpu_enable(CAMSENSE_CHANNEL, FS_ETPU_PRIORITY_LOW);

    return NO_ERROR;
}


/*--------------------------------------------------------------------*/
int16_t PHASE_SetCurrentEdge (uint32_t CurrentEdge)
{
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_PHASE))
        return PERIPHERAL_FAILURE;
#endif

    if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
        return PHASE_EXEC_NOT_OK;

    /* Disable the eTPU channel */    
    fs_etpu_disable(CAMSENSE_CHANNEL);

    /* Setting the Current Edge */
    if(CurrentEdge < MAX_EDGE_NUMB_ADMITTED) 
        write_Ph_entryIndex(CurrentEdge);

    /* new insertion for the Recovery usage of the PHASE_SetCurrentEdge */
    /* is it correct the running of the HSR here and not after the eTPU enabling?? */
    if(NO_ERROR != ETPU_set_hsr(CAMSENSE_CHANNEL, HSR_SET_CURR_EDGE_PHASE_VAL))
        return PHASE_EXEC_NOT_OK;   // HSR for RECOVERY
    /* Enable the eTPU channel */ 
    fs_etpu_enable(CAMSENSE_CHANNEL, FS_ETPU_PRIORITY_LOW);

    return NO_ERROR;
}
   

/*--------------------------------------------------------------------*/
int16_t PHASE_GetCurrentEdge (uint32_t * CurrentEdge)
{
    uint32_t currEdge;

    if (!(ETPU.CHAN[CAMSENSE_CHANNEL].CR.B.CPBA > 0))
        return PHASE_EXEC_NOT_OK;

    //   *CurrentEdge = read_Ph_entryIndex();
    currEdge = read_Ph_entryIndex();

    if (currEdge != 0) 
    {
        *CurrentEdge = currEdge - 1;
    }
    else
    {
        *CurrentEdge = (read_PhEntriesNumber(CAMSENSE_CHANNEL) - 1);
    }

    return NO_ERROR;
}


#endif // _BUILD_PHASE_
