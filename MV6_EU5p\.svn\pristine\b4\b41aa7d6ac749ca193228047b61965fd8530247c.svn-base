/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _OSEK_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "os_api.h"
#include "TasksDefs.h"
#include "task.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

const TaskCfg   OsTaskCfgTable[OSNUMTSKS]=
{
    {(TASKENTRY)FuncTask5ms,                        Task5msID,                          S_IRQ6},
    {(TASKENTRY)FuncTask10ms,                       Task10msID,                         S_IRQ3},     
    {(TASKENTRY)FuncTask100ms,                      Task100msID,                        S_IRQ2},
    #if(CAN_CHA_BUF0_EXC || \
        CAN_CHA_BUF1_EXC || \
        CAN_CHA_BUF2_EXC || \
        CAN_CHA_BUF3_EXC || \
        CAN_CHA_BUF4_EXC || \
        CAN_CHA_BUF5_EXC || \
        CAN_CHA_BUF6_EXC || \
        CAN_CHA_BUF7_EXC || \
        CAN_CHA_BUF8_EXC || \
        CAN_CHA_BUF9_EXC || \
        CAN_CHA_BUF10_EXC || \
        CAN_CHA_BUF11_EXC || \
        CAN_CHA_BUF12_EXC || \
        CAN_CHA_BUF13_EXC || \
        CAN_CHA_BUF14_EXC || \
        CAN_CHA_BUF15_EXC || \
        CAN_CHA_BUF16_EXC || \
        CAN_CHA_BUF17_EXC || \
        CAN_CHA_BUF18_EXC || \
        CAN_CHA_BUF19_EXC || \
        CAN_CHA_BUF20_EXC || \
        CAN_CHA_BUF21_EXC || \
        CAN_CHA_BUF22_EXC || \
        CAN_CHA_BUF23_EXC || \
        CAN_CHA_BUF24_EXC || \
        CAN_CHA_BUF25_EXC || \
        CAN_CHA_BUF26_EXC || \
        CAN_CHA_BUF27_EXC || \
        CAN_CHA_BUF28_EXC || \
        CAN_CHA_BUF29_EXC || \
        CAN_CHA_BUF30_EXC || \
        CAN_CHA_BUF31_EXC || \
        CAN_CHA_BUF32_EXC || \
        CAN_CHA_BUF33_EXC || \
        CAN_CHA_BUF34_EXC || \
        CAN_CHA_BUF35_EXC || \
        CAN_CHA_BUF36_EXC || \
        CAN_CHA_BUF37_EXC || \
        CAN_CHA_BUF38_EXC || \
        CAN_CHA_BUF39_EXC || \
        CAN_CHA_BUF40_EXC || \
        CAN_CHA_BUF41_EXC || \
        CAN_CHA_BUF42_EXC || \
        CAN_CHA_BUF43_EXC || \
        CAN_CHA_BUF43_EXC || \
        CAN_CHA_BUF44_EXC || \
        CAN_CHA_BUF45_EXC || \
        CAN_CHA_BUF46_EXC || \
        CAN_CHA_BUF47_EXC || \
        CAN_CHA_BUF48_EXC || \
        CAN_CHA_BUF49_EXC || \
        CAN_CHA_BUF50_EXC || \
        CAN_CHA_BUF51_EXC || \
        CAN_CHA_BUF52_EXC || \
        CAN_CHA_BUF53_EXC || \
        CAN_CHA_BUF54_EXC || \
        CAN_CHA_BUF55_EXC || \
        CAN_CHA_BUF56_EXC || \
        CAN_CHA_BUF57_EXC || \
        CAN_CHA_BUF58_EXC || \
        CAN_CHA_BUF59_EXC || \
        CAN_CHA_BUF60_EXC || \
        CAN_CHA_BUF61_EXC || \
        CAN_CHA_BUF62_EXC || \
        CAN_CHA_BUF63_EXC )
    {(TASKENTRY)FuncCAN_ExRxDoneChA,                CAN_ExRxDoneChAID,                  S_IRQ7},
    {(TASKENTRY)FuncCAN_ExTxDoneChA,                CAN_ExTxDoneChAID,                  S_IRQ7},
    #endif
    #if(CAN_CHB_BUF0_EXC || \
        CAN_CHB_BUF1_EXC || \
        CAN_CHB_BUF2_EXC || \
        CAN_CHB_BUF3_EXC || \
        CAN_CHB_BUF4_EXC || \
        CAN_CHB_BUF5_EXC || \
        CAN_CHB_BUF6_EXC || \
        CAN_CHB_BUF7_EXC || \
        CAN_CHB_BUF8_EXC || \
        CAN_CHB_BUF9_EXC || \
        CAN_CHB_BUF10_EXC || \
        CAN_CHB_BUF11_EXC || \
        CAN_CHB_BUF12_EXC || \
        CAN_CHB_BUF13_EXC || \
        CAN_CHB_BUF14_EXC || \
        CAN_CHB_BUF15_EXC || \
        CAN_CHB_BUF16_EXC || \
        CAN_CHB_BUF17_EXC || \
        CAN_CHB_BUF18_EXC || \
        CAN_CHB_BUF19_EXC || \
        CAN_CHB_BUF20_EXC || \
        CAN_CHB_BUF21_EXC || \
        CAN_CHB_BUF22_EXC || \
        CAN_CHB_BUF23_EXC || \
        CAN_CHB_BUF24_EXC || \
        CAN_CHB_BUF25_EXC || \
        CAN_CHB_BUF26_EXC || \
        CAN_CHB_BUF27_EXC || \
        CAN_CHB_BUF28_EXC || \
        CAN_CHB_BUF29_EXC || \
        CAN_CHB_BUF30_EXC || \
        CAN_CHB_BUF31_EXC || \
        CAN_CHB_BUF32_EXC || \
        CAN_CHB_BUF33_EXC || \
        CAN_CHB_BUF34_EXC || \
        CAN_CHB_BUF35_EXC || \
        CAN_CHB_BUF36_EXC || \
        CAN_CHB_BUF37_EXC || \
        CAN_CHB_BUF38_EXC || \
        CAN_CHB_BUF39_EXC || \
        CAN_CHB_BUF40_EXC || \
        CAN_CHB_BUF41_EXC || \
        CAN_CHB_BUF42_EXC || \
        CAN_CHB_BUF43_EXC || \
        CAN_CHB_BUF43_EXC || \
        CAN_CHB_BUF44_EXC || \
        CAN_CHB_BUF45_EXC || \
        CAN_CHB_BUF46_EXC || \
        CAN_CHB_BUF47_EXC || \
        CAN_CHB_BUF48_EXC || \
        CAN_CHB_BUF49_EXC || \
        CAN_CHB_BUF50_EXC || \
        CAN_CHB_BUF51_EXC || \
        CAN_CHB_BUF52_EXC || \
        CAN_CHB_BUF53_EXC || \
        CAN_CHB_BUF54_EXC || \
        CAN_CHB_BUF55_EXC || \
        CAN_CHB_BUF56_EXC || \
        CAN_CHB_BUF57_EXC || \
        CAN_CHB_BUF58_EXC || \
        CAN_CHB_BUF59_EXC || \
        CAN_CHB_BUF60_EXC || \
        CAN_CHB_BUF61_EXC || \
        CAN_CHB_BUF62_EXC || \
        CAN_CHB_BUF63_EXC )
    {(TASKENTRY)FuncCAN_ExRxDoneChB,                CAN_ExRxDoneChBID,                  S_IRQ7},
    {(TASKENTRY)FuncCAN_ExTxDoneChB,                CAN_ExTxDoneChBID,                  S_IRQ7},
    #endif
    #if(CAN_CHC_BUF0_EXC || \
        CAN_CHC_BUF1_EXC || \
        CAN_CHC_BUF2_EXC || \
        CAN_CHC_BUF3_EXC || \
        CAN_CHC_BUF4_EXC || \
        CAN_CHC_BUF5_EXC || \
        CAN_CHC_BUF6_EXC || \
        CAN_CHC_BUF7_EXC || \
        CAN_CHC_BUF8_EXC || \
        CAN_CHC_BUF9_EXC || \
        CAN_CHC_BUF10_EXC || \
        CAN_CHC_BUF11_EXC || \
        CAN_CHC_BUF12_EXC || \
        CAN_CHC_BUF13_EXC || \
        CAN_CHC_BUF14_EXC || \
        CAN_CHC_BUF15_EXC || \
        CAN_CHC_BUF16_EXC || \
        CAN_CHC_BUF17_EXC || \
        CAN_CHC_BUF18_EXC || \
        CAN_CHC_BUF19_EXC || \
        CAN_CHC_BUF20_EXC || \
        CAN_CHC_BUF21_EXC || \
        CAN_CHC_BUF22_EXC || \
        CAN_CHC_BUF23_EXC || \
        CAN_CHC_BUF24_EXC || \
        CAN_CHC_BUF25_EXC || \
        CAN_CHC_BUF26_EXC || \
        CAN_CHC_BUF27_EXC || \
        CAN_CHC_BUF28_EXC || \
        CAN_CHC_BUF29_EXC || \
        CAN_CHC_BUF30_EXC || \
        CAN_CHC_BUF31_EXC || \
        CAN_CHC_BUF32_EXC || \
        CAN_CHC_BUF33_EXC || \
        CAN_CHC_BUF34_EXC || \
        CAN_CHC_BUF35_EXC || \
        CAN_CHC_BUF36_EXC || \
        CAN_CHC_BUF37_EXC || \
        CAN_CHC_BUF38_EXC || \
        CAN_CHC_BUF39_EXC || \
        CAN_CHC_BUF40_EXC || \
        CAN_CHC_BUF41_EXC || \
        CAN_CHC_BUF42_EXC || \
        CAN_CHC_BUF43_EXC || \
        CAN_CHC_BUF43_EXC || \
        CAN_CHC_BUF44_EXC || \
        CAN_CHC_BUF45_EXC || \
        CAN_CHC_BUF46_EXC || \
        CAN_CHC_BUF47_EXC || \
        CAN_CHC_BUF48_EXC || \
        CAN_CHC_BUF49_EXC || \
        CAN_CHC_BUF50_EXC || \
        CAN_CHC_BUF51_EXC || \
        CAN_CHC_BUF52_EXC || \
        CAN_CHC_BUF53_EXC || \
        CAN_CHC_BUF54_EXC || \
        CAN_CHC_BUF55_EXC || \
        CAN_CHC_BUF56_EXC || \
        CAN_CHC_BUF57_EXC || \
        CAN_CHC_BUF58_EXC || \
        CAN_CHC_BUF59_EXC || \
        CAN_CHC_BUF60_EXC || \
        CAN_CHC_BUF61_EXC || \
        CAN_CHC_BUF62_EXC || \
        CAN_CHC_BUF63_EXC )
    {(TASKENTRY)FuncCAN_ExRxDoneChC,                CAN_ExRxDoneChCID,                  S_IRQ7},
    {(TASKENTRY)FuncCAN_ExTxDoneChC,                CAN_ExTxDoneChCID,                  S_IRQ7},
    #endif

    {(TASKENTRY)FuncTaskINJ_PRG,                    TaskINJ_PRGID,                      S_IRQ7},
#ifdef _BUILD_INJCMD_
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_0,                INJCMD_Ex_Cyl_0ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_1,                INJCMD_Ex_Cyl_1ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_2,                INJCMD_Ex_Cyl_2ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_3,                INJCMD_Ex_Cyl_3ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_4,                INJCMD_Ex_Cyl_4ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_5,                INJCMD_Ex_Cyl_5ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_6,                INJCMD_Ex_Cyl_6ID,                  S_IRQ7},
    {(TASKENTRY)FuncINJCMD_Ex_Cyl_7,                INJCMD_Ex_Cyl_7ID,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSOI,                        TaskSOIID,                          S_IRQ7},
    {(TASKENTRY)FuncTaskEOI,                        TaskEOIID,                          S_IRQ7},
#endif
    {(TASKENTRY)FuncTaskIGN_PRG,                    TaskIGN_PRGID,                      S_IRQ7},
#ifdef _BUILD_IGNCMD_
    {(TASKENTRY)FuncIGNCMD_Ex_Coil_0,               IGNCMD_Ex_Coil_0ID,                 S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Ex_Coil_1,               IGNCMD_Ex_Coil_1ID,                 S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Ex_Coil_2,               IGNCMD_Ex_Coil_2ID,                 S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Ex_Coil_3,               IGNCMD_Ex_Coil_3ID,                 S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Diag_Coil_0,             IgnCmd_Diag_0ID,                    S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Diag_Coil_1,             IgnCmd_Diag_1ID,                    S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Diag_Coil_2,             IgnCmd_Diag_2ID,                    S_IRQ7},
    {(TASKENTRY)FuncIGNCMD_Diag_Coil_3,             IgnCmd_Diag_3ID,                    S_IRQ7},
#endif
    {(TASKENTRY)FuncTaskSparkOff,                   TaskSparkOffID,                     S_IRQ6},
    {(TASKENTRY)FuncTaskEOA,                        TaskEOAID,                          S_IRQ5},
    //{(TASKENTRY)FuncTaskPowerOff,                 TaskPowerOffID,                     S_IRQ0},
    {(TASKENTRY)FuncTaskAngle,                      TaskAngleID,                        S_IRQ6},
    {(TASKENTRY)FuncTaskTDC,                        TaskTDCID,                          S_IRQ4},
    {(TASKENTRY)FuncTaskHTDC,                       TaskHTDCID,                         S_IRQ4},
    {(TASKENTRY)FuncTaskPreTDC,                     TaskPreTDCID,                       S_IRQ7},
    {(TASKENTRY)FuncTaskPreHTDC,                    TaskPreHTDCID,                      S_IRQ7},
    {(TASKENTRY)FuncTaskKeyOn,                      TaskKeyOnID,                        S_IRQ0},
    {(TASKENTRY)FuncTaskKeyOff,                     TaskKeyOffID,                       S_IRQ0},

    {(TASKENTRY)FuncTaskSync,                       TaskSyncID,                         S_IRQ7},
    {(TASKENTRY)FuncTaskNoSync,                     TaskNoSyncID,                       S_IRQ0},
    {(TASKENTRY)FuncTaskCamEdge,                    TaskCamEdgeID,                      S_IRQ6},
#ifdef _BUILD_IONACQ_
    {(TASKENTRY)FuncIonAcq_StartOfAcq_A,            IonAcq_StartOfAcq_AID,              S_IRQ6},
    {(TASKENTRY)FuncIonAcq_StartOfAcq_B,            IonAcq_StartOfAcq_BID,              S_IRQ6},
    {(TASKENTRY)FuncTaskIONCircuitSelect,           SelIONCircuitID,                    S_IRQ4},

#ifdef SPK_EVN_A_CHANNEL
    {(TASKENTRY)FuncSparkEvent_A_triggered,         SparkEvent_A_triggeredID,           S_IRQ6},
#endif
#ifdef SPK_EVN_B_CHANNEL
    {(TASKENTRY)FuncSparkEvent_B_triggered,         SparkEvent_B_triggeredID,           S_IRQ6},
#endif
#ifdef ION_EVN_A_CHANNEL
    {(TASKENTRY)FuncIonEvent_A_triggered,           IonEvent_A_triggeredID,             S_IRQ6},
#endif
#ifdef ION_EVN_B_CHANNEL
    {(TASKENTRY)FuncIonEvent_B_triggered,           IonEvent_B_triggeredID,             S_IRQ6},
#endif
#endif
#ifdef _BUILD_SPI_
    {(TASKENTRY)FuncSPI_ExTxDoneChA_PCS0,           SPI_ExTxDoneChA_PCS0ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChA_PCS1,           SPI_ExTxDoneChA_PCS1ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChA_PCS2,           SPI_ExTxDoneChA_PCS2ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChA_PCS3,           SPI_ExTxDoneChA_PCS3ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChA_PCS4,           SPI_ExTxDoneChA_PCS4ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChA_PCS5,           SPI_ExTxDoneChA_PCS5ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS0,           SPI_ExTxDoneChB_PCS0ID,             S_IRQ4},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS1,           SPI_ExTxDoneChB_PCS1ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS2,           SPI_ExTxDoneChB_PCS2ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS3,           SPI_ExTxDoneChB_PCS3ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS4,           SPI_ExTxDoneChB_PCS4ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS5,           SPI_ExTxDoneChB_PCS5ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS0,           SPI_ExTxDoneChC_PCS0ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS1,           SPI_ExTxDoneChC_PCS1ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS2,           SPI_ExTxDoneChC_PCS2ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS3,           SPI_ExTxDoneChC_PCS3ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS4,           SPI_ExTxDoneChC_PCS4ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS5,           SPI_ExTxDoneChC_PCS5ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChD_PCS0,           SPI_ExTxDoneChD_PCS0ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChD_PCS1,           SPI_ExTxDoneChD_PCS1ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChD_PCS2,           SPI_ExTxDoneChD_PCS2ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChD_PCS3,           SPI_ExTxDoneChD_PCS3ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChD_PCS4,           SPI_ExTxDoneChD_PCS4ID,             S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChD_PCS5,           SPI_ExTxDoneChD_PCS5ID,             S_IRQ5},
#endif
#ifdef _BUILD_SCIMGM_
    {(TASKENTRY)FuncSCI_ExStopTxA,                  SCI_ExStopTxAID,                    S_IRQ5},
    {(TASKENTRY)FuncSCI_ExStopRxA,                  SCI_ExStopRxAID,                    S_IRQ5},
    {(TASKENTRY)FuncSCI_ExOverrunErrorA,            SCI_ExOverrunErrorAID,              S_IRQ5},
    {(TASKENTRY)FuncSCI_ExStopTxB,                  SCI_ExStopTxBID,                    S_IRQ5},
    {(TASKENTRY)FuncSCI_ExStopRxB,                  SCI_ExStopRxBID,                    S_IRQ5},
    {(TASKENTRY)FuncSCI_ExOverrunErrorB,            SCI_ExOverrunErrorBID,              S_IRQ5},
#endif
#ifdef _BUILD_KLINE_
    {(TASKENTRY)FuncKLINE_ExStopTx,                 KLINE_ExStopTxID,                   S_IRQ5},
    {(TASKENTRY)FuncKLINE_ExStopRx,                 KLINE_ExStopRxID,                   S_IRQ5},
    {(TASKENTRY)FuncKLINE_ExOverrunError,           KLINE_ExOverrunErrorID,             S_IRQ5},
#endif
#ifdef _BUILD_LINMGM_
    {(TASKENTRY)FuncLIN_ExStopTx,                   LIN_ExStopTxID,                     S_IRQ5},
    {(TASKENTRY)FuncLIN_ExStopRx,                   LIN_ExStopRxID,                     S_IRQ5},
#endif

#ifdef _BUILD_PIOTEST_
    {(TASKENTRY)FuncPIOChannel_triggered,           PIOChannel_triggeredID,             S_IRQ6},
#endif

#if (ION_TRG_SOURCE == ION_TRG_EXTERN)
#if (N_CYLINDER >= 1)   
    {(TASKENTRY)FuncChannel_1_triggered,            Channel_1_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 2)   
    {(TASKENTRY)FuncChannel_2_triggered,            Channel_2_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 3)   
    {(TASKENTRY)FuncChannel_3_triggered,            Channel_3_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 4)   
    {(TASKENTRY)FuncChannel_4_triggered,            Channel_4_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 5)   
    {(TASKENTRY)FuncChannel_5_triggered,            Channel_5_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 6)   
    {(TASKENTRY)FuncChannel_6_triggered,            Channel_6_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 7)   
    {(TASKENTRY)FuncChannel_7_triggered,            Channel_7_triggeredID,              S_IRQ6},
#endif
#if (N_CYLINDER >= 8)   
    {(TASKENTRY)FuncChannel_8_triggered,            Channel_8_triggeredID,              S_IRQ6},
#endif
#endif
#ifdef _BUILD_HUMSNS_
    {(TASKENTRY)FuncChannel_humidity1,              Channel_humidity1ID,                S_IRQ7},
    {(TASKENTRY)FuncChannel_humidity2,              Channel_humidity2ID,                S_IRQ7},
#endif
#ifdef _BUILD_SPICAN_
    {(TASKENTRY)FuncMPC2515_INT,                    MPC2515_INTID,                      S_IRQ4},
    {(TASKENTRY)FuncMPC2515_RX0BF,                  MPC2515_RX0BFID,                    S_IRQ7},
    {(TASKENTRY)FuncMPC2515_RX1BF,                  MPC2515_RX1BFID,                    S_IRQ7},
#endif

#ifdef _BUILD_UARTMGM_ 
    {(TASKENTRY)FuncUART_RxTask,                    UART_RxTaskID,                      S_IRQ6},
    {(TASKENTRY)FuncUART_RxLoop,                    UART_RxLoopID,                      S_IRQ7},
    {(TASKENTRY)FuncUART_TxTask,                    UART_TxTaskID,                      S_IRQ5},
#endif
    {(TASKENTRY)FuncCCPBackgroundTask,              CCPBackgroundTaskID,                S_IRQ2},
    {(TASKENTRY)FuncBackgroundTask,                 BackgroundTaskID,                   S_IRQ1},
#ifdef _BUILD_DIAGCANMGM_
    {(TASKENTRY)FuncKWPDecoding,                    KWPDecodingID,                      S_IRQ5}, 
#endif
    {(TASKENTRY)FuncTask1ms,                        Task1msID,                          S_IRQ7},
#ifdef _BUILD_DIAGCANMGM_
    {(TASKENTRY)FuncTaskEndOfLine,                  TaskEndOfLineID,                    S_IRQ0},

    {(TASKENTRY)FuncTaskWaitForReset,               TaskWaitForResetID,                 S_IRQ1},
    
#endif
#ifdef _BUILD_CCP_
    {(TASKENTRY)FuncTaskCheckSum,                   TaskCheckSumID,                     S_IRQ1},
#endif
#ifdef _BUILD_VCALIB_
    {(TASKENTRY)FuncTaskVCALIB_update_region,       TaskVCALIB_update_regionID,         S_IRQ1},
#endif

#ifdef _BUILD_PITTEST_
    {(TASKENTRY)FuncTask_PitTestCase01,             Task_PitTestCase01ID,               S_IRQ7},
#endif

    {(TASKENTRY)FuncMapAngAcq_EndOfAcq,             MapAngAcq_EndOfAcqID,               S_IRQ6},

    {(TASKENTRY)FuncTaskDelayed0,                   TaskDelayedEmios0ID,                S_IRQ7},
    {(TASKENTRY)FuncTaskDelayed1,                   TaskDelayedEmios1ID,                S_IRQ7},
    {(TASKENTRY)FuncTaskDelayed2,                   TaskDelayedEmios2ID,                S_IRQ7},
    {(TASKENTRY)FuncTaskDelayed3,                   TaskDelayedEmios3ID,                S_IRQ7},
    {(TASKENTRY)FuncTaskDelayed4,                   TaskDelayedEmios4ID,                S_IRQ7},

    {(TASKENTRY)FuncTaskExVPwmIn,                   TaskExVPwmInID,                     S_IRQ4},
};


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _OSEK_ */
