/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/


/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function sparkHandler, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

//#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_Shared.h"
#include "..\..\common\ETPU_SharedTypes.h"
#include "..\include\sparkHandler.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

int sparkHandlerUnexpectedEvent = 0;

//unsigned int sparkCaptureRegA= 0xFFFFFF;
//unsigned int sparkCaptureRegB= 0xFFFFFF;

#pragma library;
#pragma option +l;  // List the library
#pragma option v;


/********************************************************************************
* FUNCTION: sparkHandler                                                        *
* PURPOSE:  This function rise an ISR to the host at high to low or low to high *
* input pin transition
* CrankAngle parameter.                                                                           *
*                                                                               *
* INPUTS NOTES: This function has 2 parameters                                  *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
void sparkHandler(unsigned int captureTransition,
                  unsigned int sparkCaptureAngle, unsigned int sparkCaptureTime, unsigned int sparkHandlerChanFlags)
{
#ifdef _BUILD_SPARKHANDLER_
    if (HSR_INIT_SPARK_HANDLER)   // Required to initialize
    {
        if(sparkHandlerChanFlags & EXCEPTIONS_ENABLED)
        {
            //StructFlags[GetCurrentChanNum()] &= EXCEPTIONS_ENABLED;
            sparkCaptureAngle = 0xFFFFFF;
            sparkCaptureTime = 0xFFFFFF;

            SetChannelMode(sm_st);

//            SetupCaptureTrans_A(do_nothing, any_trans);
            SetupCaptureTrans_A(Mtcr2_Ctcr2_eq, any_trans);
        }
    }
    else if (IsMatchBOrTransitionAEvent())
    {
//          Active edge is detected
        if(sparkHandlerChanFlags & EXCEPTIONS_ENABLED)
    {
            sparkCaptureAngle = GetCapRegA();
            sparkCaptureTime = GetCapRegB();
            
            if(IsInputPinHigh())
            {
                if (captureTransition & EX_SPARK_ON)
                {
                    sparkHandlerChanFlags |= EX_SPARK_ON;
                    sparkHandlerChanFlags &= ~EXCEPTIONS_ENABLED;
                    SetChannelInterrupt();      //Inform the host that the crank status is updated
                }
            }
            else
            {
                if (captureTransition & EX_SPARK_OFF)
                {
                    sparkHandlerChanFlags |= EX_SPARK_OFF;
                    sparkHandlerChanFlags &= ~EXCEPTIONS_ENABLED;
                    SetChannelInterrupt();      //Inform the host that the crank status is updated
                }
            }
    }
    ClearTransLatch();
    }
    else
    {
//      ErrorCamDetect:
        //This else statement is used to catch all unspecified entry table conditions
        // Clear all possible event sources
        // And set the unexpected event error indicator
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
        sparkHandlerUnexpectedEvent = 1;
    }
#endif /* _BUILD_SPARKHANDLER_ */
}

#pragma endlibrary;

