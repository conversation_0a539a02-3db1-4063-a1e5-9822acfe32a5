/*
 * File: trac_ctrl.h
 *
 * Code generated for Simulink model 'TracCtrl'.
 *
 * Model version                  : 1.800
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue May 25 11:23:53 2021
 */

#ifndef RTW_HEADER_trac_ctrl_h_
#define RTW_HEADER_trac_ctrl_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define TC_ACC                         3
#define TC_ACC_GEARUP                  5
#define TC_ACC_GEARUP_FILT             6
#define TC_CHANGE                      2U
#define TC_DEC                         2
#define TC_DISABLED                    -1
#define TC_GEAR_DISABLE                1U
#define TC_NO_CHANGE                   0U
#define TC_REC_SMOOTH                  7
#define TC_SMOOTH                      4
#define TC_SPRING_UP                   3U
#define TC_WAIT_ACC                    0

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T AccDVSpdCtrl;

/* Acc DeltaVehSpeed error */
extern int16_T AccDVSpdCtrlF;

/* Acc DeltaVehSpeed error filtered */
extern int16_T AccDVSpdCtrlFMem;

/* Acc DeltaVehSpeed error filtered */
extern int16_T AccDeltaVehSpeedFO;

/* AccDeltaVehSpeed Forced */
extern int16_T CmeTcSmooth;

/* Cme SmoothRatio */
extern uint16_T CmeTcSmoothCorr;

/* Cme SmoothRatio */
extern uint16_T CmeTcSmoothStp;

/* Cme SmoothRatio */
extern int16_T CmiSmoothRatio;

/* Cmi SmoothRatio */
extern int16_T CmiTracCtrl;

/* Cmi saturation for TC */
extern int16_T CmiTracCtrlI;

/* Cmi saturation for TC */
extern int16_T CmiTracCtrlInitI;

/* CmiTracCtrl init value - I torque */
extern int16_T CmiTracCtrlInitP;

/* CmiTracCtrl init value - P torque */
extern int16_T CmiTracCtrlP;

/* Cmi saturation for TC */
extern int16_T CmiTracI;

/* Instantaneous CMI target after TC */
extern int16_T CmiTracINoSat;

/* Instantaneous CMI target after TC - not saturated */
extern int16_T CmiTracP;

/* Predicted CMI target after TC */
extern int16_T CmiTracPNoSat;

/* Predicted CMI target after TC - not saturated */
extern uint32_T CntTcSmooth;

/* TracCtrl smooth */
extern int32_T CntTimSmooth;

/* Counter to select smooth gain */
extern int16_T DVSCtrlErr;

/* error */
extern int16_T DVSCtrlErrMax;

/* Delta Error Max */
extern int16_T DVSModel;

/* Delta VehSpeed threshold model */
extern int16_T DVSModelGain;

/* Gain DVSModel */
extern int16_T DVSRollCorr;

/* Delta VehSpeed target value correction for RollCAN */
extern int16_T DVSSmInt;

/* Delta VehSpeed smooth int */
extern int16_T DVSSmIntGain;

/* Delta VehSpeed smooth int gain */
extern int16_T DVSSmoothLev;

/* Delta VehSpeed threshold smooth */
extern int16_T DVSTarg;

/* Delata VehSpeed target value */
extern int16_T DeltaVehSpeedFO;

/* DeltaVehSpeed Forced */
extern uint8_T DiagFlg01FO;

/* Forced flag */
extern uint8_T FlgDisGear;

/* Traction control disabled for gear change */
extern uint8_T FlgTracCtrl;

/* Traction control presence flag */
extern uint8_T FlgYawRecFO;

/* Forced flag */
extern uint32_T IDTracCtrlVer;

/* ID Version */
extern uint16_T IdTracCtrl;

/* SetTracCtrl converted as index */
extern uint16_T IdTracCtrlRid;

/* SetTracCtrl converted as index reduced */
extern uint8_T IdxTcCutOff;

/* Index of Trac CutOff */
extern int16_T OffCMITRACI;

/* Offset I */
extern int16_T OffCMITRACP;

/* Offset P */
extern uint8_T StTcGear;

/* Clutch_Gear control state */
extern int8_T StTracCtrl;

/* Traction control state */
extern int8_T StTracCtrlTrn;

/* Traction control state */
extern int32_T TCIntTerm;

/* TCIntTerm */
extern int32_T TCPropITerm;

/* TCPropITerm */
extern int32_T TCPropPTerm;

/* TCPropPTerm */
extern uint8_T TcDiagRec;

/* Tc Disable for External Recovery */
extern uint8_T TrgAccVSMax;

/* Acc max */
extern uint8_T flg_end_acc;

/* End acc by cmi */
extern uint8_T flg_end_smooth;

/* End Smooth */
extern int32_T tcintfactdvs;

/* tcintfact */
#endif                                 /* RTW_HEADER_trac_ctrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
