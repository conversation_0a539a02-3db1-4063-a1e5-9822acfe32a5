/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Breakpoint TBRPMINLIMIT, TBGNSPDLIMSAT [m/s^2]
CALQUAL int16_T BkIdxInt16[3] = 
{
    0, 1, 2
};

//Breakpoint TBRPMINLIMIT
CALQUAL int16_T BkIdx2Int16[7] = 
{
    0, 1, 2, 3, 4, 5, 6
};

//Breakpoint TBMAXTHRCORROBJ0 [%]
CALQUAL uint8_T BkIdxUInt8[6] = 
{
    1, 2, 3, 4, 5, 6
};

//Breakpoint TBCMEEWTCSAT [Nm]
CALQUAL uint16_T BkIdxUInt16[9] = 
{
    0, 1, 2, 3, 4, 5, 6, 7, 8
};


