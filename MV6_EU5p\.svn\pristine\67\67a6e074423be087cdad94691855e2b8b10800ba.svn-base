#include "typedefs.h"
#include "ETPU_EngineDefs.h"
#include "ionacq.h"
#include "rtwtypes.h"

#ifdef _BUILD_IONACQ_

#ifdef __MWERKS__
#pragma force_active on
#pragma section RW ".calib" ".calib"
#else
#pragma ghs section rodata=".calib"
#endif

#if (IONACQTYPE == 0)
CALQUAL uint16_T ION_GAIN[N_CIR] = 
{ 
    (0.0716976704775065*4096),
    (0.074280659342007 *4096),
    (0.0813110294713887*4096),
    (0.0846492443127554*4096),
    (0.0939015229117535*4096),
    (0.0983820622664228*4096),
    (0.111105499721626 *4096),
    (0.117433543289251 *4096),
    (0.154990700557372 *4096),
    (0.167588402881131 *4096),
    (0.208203206327233 *4096),
    (0.231588698467535 *4096),
    (0.317057704497246 *4096),
    (0.374672161847957 *4096),
    (0.664451827209764 *4096),
    (0.980392156767647 *4096)
};
#elif (IONACQTYPE == 1)
CALQUAL uint16_T ION_GAIN[N_CIR] = 
    {
    (1.428571429*4096),
    (0.714285714*4096),
    (0.357142857*4096),
    (0.285714286*4096),
    (0.178571429*4096),
    (0.142857143*4096),
    (0.089285714*4096),
    (0.044642857*4096)
    };
#endif


//IONACQ.BKRPMSACOMP: Rpm breakpoint for VTRPMSACOMP [rpm]
CALQUAL uint16_T BKRPMSACOMP[BKRPMSACOMP_dim] = 
{
    250, 1000, 1750, 2000, 2750, 3500, 3750, 4500, 5000, 6000, 6750, 7000, 8000,  8500, 9000, 13000
};

CALQUAL int8_T  FORCEIONSELECT = -1;
CALQUAL uint16_T BKACQRPM[6] = 
{ 2000, 3500, 5500, 6500, 7500, 8500	};
CALQUAL uint16_T BKACQLOAD[6] = 
{ (50.0*128), (55.0*128), (65.0*128), (75.0*128), (85.0*128), (95.0*128) };
CALQUAL uint8_T  TBIONSELECT[36] = 
{   2,3,3,3,3,4, 
    2,3,3,3,3,4, 
    2,3,3,3,3,4, 
    2,3,3,3,3,4, 
    2,3,3,3,4,4, 
    2,3,3,3,4,4 };
CALQUAL uint16_T BKIONDTRPM[7] = 
{ 800, 1000, 3300, 3900, 5000, 6000, 7000	};
CALQUAL uint16_T VTIONDT[7] = 
{ (10*256), (10*256), (10*256), (10*256), (10*256), (10*256), (10*256)  };
CALQUAL uint32_T MINTDWELL = 0;
CALQUAL int8_T     IONABSTDC = -3;    // from unsigned to signed !!
CALQUAL uint8_T VTIONWINDOW[7] = 
{ 70, 70, 70, 70, 70, 70, 70 };
CALQUAL uint16_T VIONMIN = ((0*16384)/5000);
CALQUAL uint16_T VIONMAX = ((5000*16384)/5000);
CALQUAL uint16_T VSPEAKMIN = ((0*65535)/5000);
CALQUAL uint16_T VSPEAKMAX = ((5000*65535)/5000);
CALQUAL uint16_T SWOFFDELAY = 0;

//IONACQ.VTRPMSACOMP: Spark Advance compensation f(Rpm) [degree]
CALQUAL int8_T VTRPMSACOMP[BKRPMSACOMP_dim] =
{ (0.10*16), (0.50*16), (0.35*16), (0.15*16), (0.10*16), (0.05*16), (0.00*16), (-0.15*16), (-0.2*16), (-0.25*16), (-0.35*16), (-0.4*16), (-0.45*16), (-0.5*16), (-0.55*16), (-0.60*16)};

CALQUAL uint8_T     ENSPIKEDETECT = 0;          // [flag]		2^0 	
CALQUAL uint16_T    SPIKEANGLE = (55 * 64);			// [deg] 		2^-6
CALQUAL uint16_T    SPIKEDIONTHR = (35 * 16);		// [uA/deg]	2^-4
CALQUAL uint16_T    SPIKEANGDIFF = (2 * 64);   	// [deg]		2^-6

CALQUAL uint16_T    RPMENIONPLOT = 10000u;

// Min difference between DeltaKnockNPow and ShownDeltaKnock to force IonSignal overwrite
CALQUAL int16_T THRDIFFKNOCKINT = (int16_T)(0.1f * 4096.0f);

CALQUAL uint8_T ENCOPYIONBUFFV = 0u;

// Threshold in Percentual of ThPeak Max for Ionsignal Ploting
CALQUAL  uint16_T THPEAKPERC = 512u; /* 50% 2^-10          */

// ThPeak threshold for Ionsignal Ploting
CALQUAL  uint16_T SHOWTHPEAKTHR = 64000u; /* 2000uA 2^-5  uA          */

#ifdef __MWERKS__
#pragma force_active off
#endif

#endif // _BUILD_IONACQ_

