/*
 * File: FuelMgm.h
 *
 * Code generated for Simulink model 'FuelMgm'.
 *
 * Model version                  : 1.1006
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Oct  3 13:30:05 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_FuelMgm_h_
#define RTW_HEADER_FuelMgm_h_
#ifndef FuelMgm_COMMON_INCLUDES_
# define FuelMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* FuelMgm_COMMON_INCLUDES_ */

#include "FuelMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "analogin.h"
#include "ETPU_EngineDefs.h"
#include "recmgm.h"
#include "fuel_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKANGQFSPLIT_dim               5U                        /* Referenced by:
                                                                  * '<S51>/BKANGQFSPLIT_dim'
                                                                  * '<S112>/BKANGQFSPLIT_dim'
                                                                  * '<S209>/BKANGQFSPLIT_dim'
                                                                  */
#define BKBATINJT_dim                  15U                       /* Referenced by:
                                                                  * '<S71>/BKBATINJT_dim'
                                                                  * '<S141>/BKBATINJT_dim'
                                                                  */
#define BKDPRES_dim                    11U                       /* Referenced by:
                                                                  * '<S32>/BKDPRES_dim'
                                                                  * '<S93>/BKDPRES_dim'
                                                                  * '<S190>/BKDPRES_dim'
                                                                  */
#define BKPRESFUEL_dim                 3U                        /* Referenced by:
                                                                  * '<S154>/BKPRESFUEL_dim'
                                                                  * '<S170>/BKPRESFUEL_dim'
                                                                  * '<S171>/BKPRESFUEL_dim'
                                                                  */
#define BKRPMFUEL_dim                  7U                        /* Referenced by:
                                                                  * '<S154>/BKRPMFUEL_dim'
                                                                  * '<S163>/BKRPMFUEL_dim'
                                                                  * '<S164>/BKRPMFUEL_dim'
                                                                  * '<S170>/BKRPMFUEL_dim'
                                                                  * '<S171>/BKRPMFUEL_dim'
                                                                  */
#define BKRPMQFSPLIT_dim               2U                        /* Referenced by:
                                                                  * '<S51>/BKRPMQFSPLIT_dim'
                                                                  * '<S112>/BKRPMQFSPLIT_dim'
                                                                  * '<S209>/BKRPMQFSPLIT_dim'
                                                                  */
#define BKSOIBASELOAD_dim              5U                        /* Referenced by:
                                                                  * '<S21>/BKSOIBASELOAD_dim'
                                                                  * '<S82>/BKSOIBASELOAD_dim'
                                                                  * '<S179>/BKSOIBASELOAD_dim'
                                                                  */
#define BKSOIBASERPM_dim               5U                        /* Referenced by:
                                                                  * '<S21>/BKSOIBASERPM_dim'
                                                                  * '<S51>/BKSOIBASERPM_dim'
                                                                  * '<S82>/BKSOIBASERPM_dim'
                                                                  * '<S112>/BKSOIBASERPM_dim'
                                                                  * '<S179>/BKSOIBASERPM_dim'
                                                                  * '<S209>/BKSOIBASERPM_dim'
                                                                  */
#define BKTDCFUEL_dim                  3U                        /* Referenced by: '<S154>/BKTDCFUEL_dim' */
#define BKTWFUEL_dim                   3U                        /* Referenced by:
                                                                  * '<S130>/BKTWFUEL_dim'
                                                                  * '<S154>/BKTWFUEL_dim'
                                                                  */
#define BOTH_BANKS                     1U                        /* Referenced by:
                                                                  * '<S57>/BOTH_BANKS'
                                                                  * '<S58>/BOTH_BANKS'
                                                                  * '<S59>/BOTH_BANKS'
                                                                  * '<S118>/BOTH_BANKS'
                                                                  * '<S119>/BOTH_BANKS'
                                                                  * '<S120>/BOTH_BANKS'
                                                                  * '<S215>/BOTH_BANKS'
                                                                  * '<S216>/BOTH_BANKS'
                                                                  * '<S217>/BOTH_BANKS'
                                                                  */
#define HIGH_BANK                      2U                        /* Referenced by:
                                                                  * '<S57>/HIGH_BANK'
                                                                  * '<S58>/HIGH_BANK'
                                                                  * '<S118>/HIGH_BANK'
                                                                  * '<S119>/HIGH_BANK'
                                                                  * '<S215>/HIGH_BANK'
                                                                  * '<S216>/HIGH_BANK'
                                                                  */
#define ID_FUEL_MGM                    18465004U                 /* Referenced by: '<S70>/ID_FUEL_MGM' */

/* mask */
#define LOW_BANK                       0U                        /* Referenced by:
                                                                  * '<S70>/LOW_BANK'
                                                                  * '<S57>/LOW_BANK'
                                                                  * '<S59>/LOW_BANK'
                                                                  * '<S65>/LOW_BANK'
                                                                  * '<S118>/LOW_BANK'
                                                                  * '<S120>/LOW_BANK'
                                                                  * '<S126>/LOW_BANK'
                                                                  * '<S215>/LOW_BANK'
                                                                  * '<S217>/LOW_BANK'
                                                                  * '<S223>/LOW_BANK'
                                                                  */
#define MAX_OVF                        4294967295U               /* Referenced by: '<S138>/Chart' */
#define MAX_XFILM                      24543                     /* Referenced by:
                                                                  * '<S164>/MAX_XFILM1'
                                                                  * '<S171>/MAX_XFILM'
                                                                  */
#define N_INJ_PRG                      2U                        /* Referenced by: '<S70>/Reset_Vett' */
#define OFF_HIGH_BANK                  4U                        /* Referenced by:
                                                                  * '<S2>/OFF_HIGH_BANK'
                                                                  * '<S4>/OFF_HIGH_BANK'
                                                                  * '<S8>/OFF_HIGH_BANK'
                                                                  * '<S164>/Chart'
                                                                  */
#define ST_QF_ACC                      1                         /* Referenced by:
                                                                  * '<S230>/ST_QF_ACC'
                                                                  * '<S231>/ST_QF_ACC'
                                                                  */
#define ST_QF_DEC                      -1                        /* Referenced by:
                                                                  * '<S163>/ST_QF_DEC'
                                                                  * '<S170>/ST_QF_DEC'
                                                                  * '<S230>/ST_QF_DEC'
                                                                  * '<S231>/ST_QF_DEC'
                                                                  */
#define ST_QF_STAB                     0                         /* Referenced by:
                                                                  * '<S70>/Reset_Vett'
                                                                  * '<S230>/ST_QF_STAB'
                                                                  * '<S231>/ST_QF_STAB'
                                                                  */

/* Block signals for system '<S8>/InjTime_Calc' */
typedef struct {
  uint16_T LookUp_IR_U16;              /* '<S193>/LookUp_IR_U16' */
  int16_T Sum2;                        /* '<S190>/Sum2' */
} rtB_InjTime_Calc_FuelMgm;

/* Block signals for system '<S8>/QAir_selection' */
typedef struct {
  uint16_T Selector2;                  /* '<S150>/Selector2' */
  uint16_T Switch;                     /* '<S150>/Switch' */
} rtB_QAir_selection_FuelMgm;

/* Block signals for system '<S151>/Fuel_Mass_Calculation' */
typedef struct {
  uint16_T Switch;                     /* '<S205>/Switch' */
} rtB_Fuel_Mass_Calculation_FuelM;

/* Block signals for system '<S8>/QFObj_Calc' */
typedef struct {
  rtB_Fuel_Mass_Calculation_FuelM Fuel_Mass_Calculation_Base;/* '<S151>/Fuel_Mass_Calculation_Base' */
  rtB_Fuel_Mass_Calculation_FuelM Fuel_Mass_Calculation;/* '<S151>/Fuel_Mass_Calculation' */
} rtB_QFObj_Calc_FuelMgm;

/* Block signals for system '<S1>/TDC' */
typedef struct {
  uint16_T PreLookUpIdSearch_U16_o1;   /* '<S160>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2;   /* '<S160>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o1_avwy;/* '<S161>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2_axaf;/* '<S161>/PreLookUpIdSearch_U16' */
  uint16_T Look2D_IR_U8;               /* '<S157>/Look2D_IR_U8' */
  uint16_T Selector;                   /* '<S228>/Selector' */
  int16_T RatioCalcS16;                /* '<S238>/RatioCalcS16' */
  int16_T RatioCalcS16_nc4k;           /* '<S237>/RatioCalcS16' */
  int16_T RatioCalcS16_ptkf;           /* '<S234>/RatioCalcS16' */
  int16_T RatioCalcS16_joa2;           /* '<S233>/RatioCalcS16' */
  boolean_T LogicalOperator;           /* '<S153>/Logical Operator' */
  rtB_Fuel_Mass_Calculation_FuelM Fuel_Mass_Calculation;/* '<S152>/Fuel_Mass_Calculation' */
  rtB_QFObj_Calc_FuelMgm QFObj_Calc;   /* '<S8>/QFObj_Calc' */
  rtB_QAir_selection_FuelMgm QAir_selection;/* '<S8>/QAir_selection' */
  rtB_InjTime_Calc_FuelMgm InjTime_Calc;/* '<S8>/InjTime_Calc' */
} rtB_TDC_FuelMgm;

/* Block states (default storage) for system '<S1>/TDC' */
typedef struct {
  uint16_T Memory_PreviousInput;       /* '<S155>/Memory' */
} rtDW_TDC_FuelMgm;

/* Block signals for system '<S1>/T100ms' */
typedef struct {
  uint32_T QFuelTot0Lth;               /* '<S138>/Chart' */
} rtB_T100ms_FuelMgm;

/* Block states (default storage) for system '<S1>/T100ms' */
typedef struct {
  uint32_T oldQFuelIntExtTot;          /* '<S138>/Chart' */
  uint32_T oldQFuelIntTot;             /* '<S138>/Chart' */
  uint32_T tmpQFuelTot0Lth;            /* '<S138>/Chart' */
  uint8_T cnt;                         /* '<S138>/Chart' */
} rtDW_T100ms_FuelMgm;

/* Block signals for system '<S1>/HTDC' */
typedef struct {
  rtB_QFObj_Calc_FuelMgm QFObj_Calc;   /* '<S2>/QFObj_Calc' */
  rtB_QAir_selection_FuelMgm QAir_selection;/* '<S2>/QAir_selection' */
  rtB_InjTime_Calc_FuelMgm InjTime_Calc;/* '<S2>/InjTime_Calc' */
} rtB_HTDC_FuelMgm;

/* Block signals for system '<S1>/PreTDC' */
typedef struct {
  rtB_QFObj_Calc_FuelMgm QFObj_Calc;   /* '<S4>/QFObj_Calc' */
  rtB_QAir_selection_FuelMgm QAir_selection;/* '<S4>/QAir_selection' */
  rtB_InjTime_Calc_FuelMgm InjTime_Calc;/* '<S4>/InjTime_Calc' */
} rtB_PreTDC_FuelMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState TriggeredSubsystem7_Trig_ZCE;/* '<S1>/Triggered Subsystem7' */
  ZCSigState TriggeredSubsystem6_Trig_ZCE;/* '<S1>/Triggered Subsystem6' */
  ZCSigState TriggeredSubsystem5_Trig_ZCE;/* '<S1>/Triggered Subsystem5' */
  ZCSigState TriggeredSubsystem4_Trig_ZCE;/* '<S1>/Triggered Subsystem4' */
  ZCSigState TriggeredSubsystem3_Trig_ZCE;/* '<S1>/Triggered Subsystem3' */
  ZCSigState TriggeredSubsystem2_Trig_ZCE;/* '<S1>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem1_Trig_ZCE[2];/* '<S1>/Triggered Subsystem1' */
} PrevZCSigStates_FuelMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
  uint8_T ev_100ms;                    /* '<Root>/ev_100ms' */
  uint8_T ev_Sync;                     /* '<Root>/ev_Sync' */
  uint8_T ev_TDC;                      /* '<Root>/ev_TDC' */
  uint8_T ev_HTDC;                     /* '<Root>/ev_HTDC' */
  uint8_T ev_PreTDC;                   /* '<Root>/ev_PreTDC' */
} ExternalInputs_FuelMgm;

/* Extern declarations of internal data for system '<S1>/TDC' */
extern rtB_TDC_FuelMgm FuelMgm_TDC_B;
extern rtDW_TDC_FuelMgm FuelMgm_TDC_DW;

/* Extern declarations of internal data for system '<S1>/T100ms' */
extern rtB_T100ms_FuelMgm FuelMgm_T100ms_B;
extern rtDW_T100ms_FuelMgm FuelMgm_T100ms_DW;

/* Extern declarations of internal data for system '<S1>/HTDC' */
extern rtB_HTDC_FuelMgm FuelMgm_HTDC_B;

/* Extern declarations of internal data for system '<S1>/PreTDC' */
extern rtB_PreTDC_FuelMgm FuelMgm_PreTDC_B;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_FuelMgm FuelMgm_U;

/* Model entry point functions */
extern void FuelMgm_initialize(void);
extern void FuelMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('fuelmgm_gen/FuelMgm')    - opens subsystem fuelmgm_gen/FuelMgm
 * hilite_system('fuelmgm_gen/FuelMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'fuelmgm_gen'
 * '<S1>'   : 'fuelmgm_gen/FuelMgm'
 * '<S2>'   : 'fuelmgm_gen/FuelMgm/HTDC'
 * '<S3>'   : 'fuelmgm_gen/FuelMgm/Init'
 * '<S4>'   : 'fuelmgm_gen/FuelMgm/PreTDC'
 * '<S5>'   : 'fuelmgm_gen/FuelMgm/Sync'
 * '<S6>'   : 'fuelmgm_gen/FuelMgm/T100ms'
 * '<S7>'   : 'fuelmgm_gen/FuelMgm/T10ms'
 * '<S8>'   : 'fuelmgm_gen/FuelMgm/TDC'
 * '<S9>'   : 'fuelmgm_gen/FuelMgm/Triggered Subsystem1'
 * '<S10>'  : 'fuelmgm_gen/FuelMgm/Triggered Subsystem2'
 * '<S11>'  : 'fuelmgm_gen/FuelMgm/Triggered Subsystem3'
 * '<S12>'  : 'fuelmgm_gen/FuelMgm/Triggered Subsystem4'
 * '<S13>'  : 'fuelmgm_gen/FuelMgm/Triggered Subsystem5'
 * '<S14>'  : 'fuelmgm_gen/FuelMgm/Triggered Subsystem6'
 * '<S15>'  : 'fuelmgm_gen/FuelMgm/Triggered Subsystem7'
 * '<S16>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc'
 * '<S17>'  : 'fuelmgm_gen/FuelMgm/HTDC/QAir_selection'
 * '<S18>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc'
 * '<S19>'  : 'fuelmgm_gen/FuelMgm/HTDC/fuel_cutoff'
 * '<S20>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl'
 * '<S21>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/SOI_Calculation'
 * '<S22>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Fuel_Film_Compensator'
 * '<S23>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Fuel_Film_Compensator_HighBank'
 * '<S24>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/HTDC_assignment'
 * '<S25>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation'
 * '<S26>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank'
 * '<S27>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/TDC_assignment'
 * '<S28>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_InjTime'
 * '<S29>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_QFuel'
 * '<S30>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/ExtraQFuel_Calculation'
 * '<S31>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Extra_Injection_Time_Calculation'
 * '<S32>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model'
 * '<S33>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation'
 * '<S34>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_S16'
 * '<S35>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_U16'
 * '<S36>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/PreLookUpIdSearch_U16'
 * '<S37>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S38>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S39>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation/Assign_FlgInjTMin'
 * '<S40>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_FlgInjTMin'
 * '<S41>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_InjTime'
 * '<S42>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_QFuel'
 * '<S43>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/InjTime_cyl/TDC_assignment/Assign_QFilm'
 * '<S44>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/SOI_Calculation/Assign_SOI'
 * '<S45>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/SOI_Calculation/Look2D_U8_U16_U16_1'
 * '<S46>'  : 'fuelmgm_gen/FuelMgm/HTDC/InjTime_Calc/SOI_Calculation/Look2D_U8_U16_U16_2'
 * '<S47>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Calculation'
 * '<S48>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Calculation_Base'
 * '<S49>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Splitter'
 * '<S50>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Splitter_Base'
 * '<S51>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation'
 * '<S52>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/TDC_assignment'
 * '<S53>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_HB'
 * '<S54>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_LB'
 * '<S55>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_HB'
 * '<S56>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_LB'
 * '<S57>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/BOTH_BANKS'
 * '<S58>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/HIGH_BANK'
 * '<S59>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/LOW_BANK'
 * '<S60>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/Look2D_U8_U16_U16_1'
 * '<S61>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_1'
 * '<S62>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_2'
 * '<S63>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/PreLookUpIdSearch_U16'
 * '<S64>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/RateLimiter_S16'
 * '<S65>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/default'
 * '<S66>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S67>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_2/Data Type Conversion Inherited3'
 * '<S68>'  : 'fuelmgm_gen/FuelMgm/HTDC/QFObj_Calc/QFuelSplitFrac_calculation/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S69>'  : 'fuelmgm_gen/FuelMgm/Init/InjEnable_Calc'
 * '<S70>'  : 'fuelmgm_gen/FuelMgm/Init/Reset'
 * '<S71>'  : 'fuelmgm_gen/FuelMgm/Init/VBat_Offset_Calc'
 * '<S72>'  : 'fuelmgm_gen/FuelMgm/Init/Reset/Reset_Vett'
 * '<S73>'  : 'fuelmgm_gen/FuelMgm/Init/VBat_Offset_Calc/LookUp_S16_U16'
 * '<S74>'  : 'fuelmgm_gen/FuelMgm/Init/VBat_Offset_Calc/LookUp_S16_U16_1'
 * '<S75>'  : 'fuelmgm_gen/FuelMgm/Init/VBat_Offset_Calc/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S76>'  : 'fuelmgm_gen/FuelMgm/Init/VBat_Offset_Calc/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S77>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc'
 * '<S78>'  : 'fuelmgm_gen/FuelMgm/PreTDC/QAir_selection'
 * '<S79>'  : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc'
 * '<S80>'  : 'fuelmgm_gen/FuelMgm/PreTDC/fuel_cutoff'
 * '<S81>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl'
 * '<S82>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/SOI_Calculation'
 * '<S83>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Fuel_Film_Compensator'
 * '<S84>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Fuel_Film_Compensator_HighBank'
 * '<S85>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/HTDC_assignment'
 * '<S86>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation'
 * '<S87>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank'
 * '<S88>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/TDC_assignment'
 * '<S89>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_InjTime'
 * '<S90>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_QFuel'
 * '<S91>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/ExtraQFuel_Calculation'
 * '<S92>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Extra_Injection_Time_Calculation'
 * '<S93>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model'
 * '<S94>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation'
 * '<S95>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_S16'
 * '<S96>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_U16'
 * '<S97>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/PreLookUpIdSearch_U16'
 * '<S98>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S99>'  : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S100>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation/Assign_FlgInjTMin'
 * '<S101>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_FlgInjTMin'
 * '<S102>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_InjTime'
 * '<S103>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_QFuel'
 * '<S104>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/InjTime_cyl/TDC_assignment/Assign_QFilm'
 * '<S105>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/SOI_Calculation/Assign_SOI'
 * '<S106>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/SOI_Calculation/Look2D_U8_U16_U16_1'
 * '<S107>' : 'fuelmgm_gen/FuelMgm/PreTDC/InjTime_Calc/SOI_Calculation/Look2D_U8_U16_U16_2'
 * '<S108>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Calculation'
 * '<S109>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Calculation_Base'
 * '<S110>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Splitter'
 * '<S111>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Splitter_Base'
 * '<S112>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation'
 * '<S113>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/TDC_assignment'
 * '<S114>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_HB'
 * '<S115>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_LB'
 * '<S116>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_HB'
 * '<S117>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_LB'
 * '<S118>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/BOTH_BANKS'
 * '<S119>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/HIGH_BANK'
 * '<S120>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/LOW_BANK'
 * '<S121>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/Look2D_U8_U16_U16_1'
 * '<S122>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_1'
 * '<S123>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_2'
 * '<S124>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/PreLookUpIdSearch_U16'
 * '<S125>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/RateLimiter_S16'
 * '<S126>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/default'
 * '<S127>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S128>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_2/Data Type Conversion Inherited3'
 * '<S129>' : 'fuelmgm_gen/FuelMgm/PreTDC/QFObj_Calc/QFuelSplitFrac_calculation/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S130>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc'
 * '<S131>' : 'fuelmgm_gen/FuelMgm/Sync/InjEnable_Calc'
 * '<S132>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc/FirstInj_Calc'
 * '<S133>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc/FirstSOI_Calc'
 * '<S134>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc/LookUp_U16_S16'
 * '<S135>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc/FirstInj_Calc/Assign_First_InjTime'
 * '<S136>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc/FirstSOI_Calc/Assign_First_SOI'
 * '<S137>' : 'fuelmgm_gen/FuelMgm/Sync/First_Inj_Calc/LookUp_U16_S16/Data Type Conversion Inherited3'
 * '<S138>' : 'fuelmgm_gen/FuelMgm/T100ms/QFuelLth_Calc'
 * '<S139>' : 'fuelmgm_gen/FuelMgm/T100ms/QFuelLth_Calc/Chart'
 * '<S140>' : 'fuelmgm_gen/FuelMgm/T10ms/InjEnable_Calc'
 * '<S141>' : 'fuelmgm_gen/FuelMgm/T10ms/VBat_Offset_Calc'
 * '<S142>' : 'fuelmgm_gen/FuelMgm/T10ms/VBat_Offset_Calc/LookUp_S16_U16'
 * '<S143>' : 'fuelmgm_gen/FuelMgm/T10ms/VBat_Offset_Calc/LookUp_S16_U16_1'
 * '<S144>' : 'fuelmgm_gen/FuelMgm/T10ms/VBat_Offset_Calc/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S145>' : 'fuelmgm_gen/FuelMgm/T10ms/VBat_Offset_Calc/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S146>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation'
 * '<S147>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB'
 * '<S148>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB'
 * '<S149>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc'
 * '<S150>' : 'fuelmgm_gen/FuelMgm/TDC/QAir_selection'
 * '<S151>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc'
 * '<S152>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc'
 * '<S153>' : 'fuelmgm_gen/FuelMgm/TDC/fuel_cutoff'
 * '<S154>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation'
 * '<S155>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Pressure_selection'
 * '<S156>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation/Look2D_IR_U8_2'
 * '<S157>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation/Look2D_IR_U8_3'
 * '<S158>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation/PreLookUpIdSearch_S16_twater'
 * '<S159>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation/PreLookUpIdSearch_U16_cnt'
 * '<S160>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation/PreLookUpIdSearch_U16_pres'
 * '<S161>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Interpolation/Index_Ratio_Calculation/PreLookUpIdSearch_U16_rpm'
 * '<S162>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/GainFilm_Calc_HB'
 * '<S163>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/KFFilm_Calc_HB'
 * '<S164>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/XFilm_Calculation_HB'
 * '<S165>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/KFFilm_Calc_HB/LookUp_IR_U8_1'
 * '<S166>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/KFFilm_Calc_HB/LookUp_IR_U8_2'
 * '<S167>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/XFilm_Calculation_HB/Chart'
 * '<S168>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_HB/XFilm_Calculation_HB/LookUp_IR_U8'
 * '<S169>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/GainFilm_Calc_LB'
 * '<S170>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB'
 * '<S171>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/XFilm_Calculation_LB'
 * '<S172>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/GainFilm_Calc_LB/Assign_GainFilm'
 * '<S173>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB/Assign_KFilm'
 * '<S174>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB/Look2D_IR_U8_1'
 * '<S175>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB/Look2D_IR_U8_2'
 * '<S176>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/XFilm_Calculation_LB/Assign_XFilm'
 * '<S177>' : 'fuelmgm_gen/FuelMgm/TDC/Film_Par_Calc_LB/XFilm_Calculation_LB/Look2D_IR_U8_1'
 * '<S178>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl'
 * '<S179>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/SOI_Calculation'
 * '<S180>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Fuel_Film_Compensator'
 * '<S181>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Fuel_Film_Compensator_HighBank'
 * '<S182>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/HTDC_assignment'
 * '<S183>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation'
 * '<S184>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank'
 * '<S185>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/TDC_assignment'
 * '<S186>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_InjTime'
 * '<S187>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_QFuel'
 * '<S188>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/ExtraQFuel_Calculation'
 * '<S189>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Extra_Injection_Time_Calculation'
 * '<S190>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model'
 * '<S191>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation'
 * '<S192>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_S16'
 * '<S193>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_U16'
 * '<S194>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/PreLookUpIdSearch_U16'
 * '<S195>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S196>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Injector_Model/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S197>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation/Assign_FlgInjTMin'
 * '<S198>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_FlgInjTMin'
 * '<S199>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_InjTime'
 * '<S200>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_QFuel'
 * '<S201>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/InjTime_cyl/TDC_assignment/Assign_QFilm'
 * '<S202>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/SOI_Calculation/Assign_SOI'
 * '<S203>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/SOI_Calculation/Look2D_U8_U16_U16_1'
 * '<S204>' : 'fuelmgm_gen/FuelMgm/TDC/InjTime_Calc/SOI_Calculation/Look2D_U8_U16_U16_2'
 * '<S205>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Calculation'
 * '<S206>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Calculation_Base'
 * '<S207>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Splitter'
 * '<S208>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Splitter_Base'
 * '<S209>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation'
 * '<S210>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/TDC_assignment'
 * '<S211>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_HB'
 * '<S212>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_LB'
 * '<S213>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_HB'
 * '<S214>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_LB'
 * '<S215>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/BOTH_BANKS'
 * '<S216>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/HIGH_BANK'
 * '<S217>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/LOW_BANK'
 * '<S218>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/Look2D_U8_U16_U16_1'
 * '<S219>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_1'
 * '<S220>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_2'
 * '<S221>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/PreLookUpIdSearch_U16'
 * '<S222>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/RateLimiter_S16'
 * '<S223>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/default'
 * '<S224>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S225>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/LookUp_IR_S16_2/Data Type Conversion Inherited3'
 * '<S226>' : 'fuelmgm_gen/FuelMgm/TDC/QFObj_Calc/QFuelSplitFrac_calculation/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S227>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc'
 * '<S228>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc_HighBank'
 * '<S229>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Fuel_Mass_Calculation'
 * '<S230>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/StQFAcc_Calc'
 * '<S231>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/StQFAcc_Calc_HighBank'
 * '<S232>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/Assign_QFObjOld'
 * '<S233>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/RatioCalcS1'
 * '<S234>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/RatioCalcS16'
 * '<S235>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/RatioCalcS1/Data Type Conversion Inherited1'
 * '<S236>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/RatioCalcS16/Data Type Conversion Inherited1'
 * '<S237>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc_HighBank/RatioCalcS1'
 * '<S238>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc_HighBank/RatioCalcS16'
 * '<S239>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc_HighBank/RatioCalcS1/Data Type Conversion Inherited1'
 * '<S240>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc_HighBank/RatioCalcS16/Data Type Conversion Inherited1'
 * '<S241>' : 'fuelmgm_gen/FuelMgm/TDC/QFuelAvg_Calc/StQFAcc_Calc/Assign_StQFAcc'
 */

/*-
 * Requirements for '<Root>': FuelMgm
 */
#endif                                 /* RTW_HEADER_FuelMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
