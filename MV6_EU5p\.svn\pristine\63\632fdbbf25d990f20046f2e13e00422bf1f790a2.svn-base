/*
 * File: GearMgm_private.h
 *
 * Code generated for Simulink model 'GearMgm'.
 *
 * Model version                  : 1.903
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb 28 14:57:43 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GearMgm_private_h_
#define RTW_HEADER_GearMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "GearMgm.h"

/* Includes for objects with custom storage classes. */
#include "engflag.h"
#include "gearshift_mgm.h"
#include "Digitalin.h"
#include "trq_est.h"
#include "EngFlag.h"
#include "PTrain_Diag.h"
#include "GearShift_Mgm.h"
#include "Vspeed_Mgm.h"
#include "SyncMgm.h"
#include "cmefilter_mgm.h"
#include "diagmgm_out.h"
#include "Analogin.h"
#include "trac_ctrl.h"
#include "analogin.h"
#include "ptrain_diag.h"
#include "Vspeed_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T DELTAGEARV;             /* Variable: DELTAGEARV
                                        * Referenced by: '<S9>/DetectGearPos'
                                        * half-amplitude of voltage band of each gear ratio
                                        */
extern int16_T VTDGEARDNCLV[7];        /* Variable: VTDGEARDNCLV
                                        * Referenced by: '<S9>/Calc_Thresholds_CL'
                                        * delta for CL
                                        */
extern int16_T VTDGEARUPCLV[7];        /* Variable: VTDGEARUPCLV
                                        * Referenced by: '<S9>/Calc_Thresholds_CL'
                                        * delta for CL
                                        */
extern int16_T VTGEARV[7];             /* Variable: VTGEARV
                                        * Referenced by:
                                        *   '<S2>/Init_GearRatio'
                                        *   '<S9>/Calc_Thresholds_CL'
                                        *   '<S9>/DetectGearPos'
                                        * Typical values of each gear ratio
                                        */
extern int16_T VTGEARRATIO[7];         /* Variable: VTGEARRATIO
                                        * Referenced by: '<S2>/Init_GearRatio'
                                        * gear ratio
                                        */
extern int16_T VTTHGRMAX[7];           /* Variable: VTTHGRMAX
                                        * Referenced by:
                                        *   '<S6>/VTTHGRMAX'
                                        *   '<S22>/Chart_Update_GearRatio'
                                        *   '<S27>/VTTHGRMAX'
                                        * gear ratio threshold
                                        */
extern int16_T VTTHGRMIN[7];           /* Variable: VTTHGRMIN
                                        * Referenced by:
                                        *   '<S6>/VTTHGRMIN'
                                        *   '<S22>/Chart_Update_GearRatio'
                                        *   '<S27>/VTTHGRMIN'
                                        * gear ratio threshold
                                        */
extern int16_T THCALCGR;               /* Variable: THCALCGR
                                        * Referenced by: '<S22>/Chart_Update_GearRatio'
                                        * CmeEst threshold
                                        */
extern uint16_T DELTPERCGR;            /* Variable: DELTPERCGR
                                        * Referenced by: '<S29>/DELTPERCGR'
                                        * delta gear ratio
                                        */
extern uint16_T THPERCGRUPD;           /* Variable: THPERCGRUPD
                                        * Referenced by: '<S22>/Chart_Update_GearRatio'
                                        * Min perc to confirm new Gear ratio
                                        */
extern uint16_T KFILTGEARRPM;          /* Variable: KFILTGEARRPM
                                        * Referenced by: '<S21>/KFILTGEARRPM'
                                        * Rpm k filter value
                                        */
extern uint16_T THRVSGEARDIAG;         /* Variable: THRVSGEARDIAG
                                        * Referenced by: '<S9>/Diag_Gear'
                                        * VehSpeed above threshold to enable plausibility diagnosis
                                        */
extern uint16_T CONVFACTOR;            /* Variable: CONVFACTOR
                                        * Referenced by: '<S21>/CONVFACTOR'
                                        * Convertion value for wheel speed
                                        */
extern uint16_T THRDBLIPHI;            /* Variable: THRDBLIPHI
                                        * Referenced by: '<S14>/Double_Blip'
                                        * blip threshold
                                        */
extern uint16_T THRDBLIPLOW;           /* Variable: THRDBLIPLOW
                                        * Referenced by: '<S14>/Double_Blip'
                                        * blip threshold
                                        */
extern uint16_T TIMGEARDIAG;           /* Variable: TIMGEARDIAG
                                        * Referenced by: '<S9>/Diag_Gear'
                                        * Diagnosis counter
                                        */
extern uint16_T TIMGROBSERVER;         /* Variable: TIMGROBSERVER
                                        * Referenced by: '<S29>/TIMGROBSERVER'
                                        * tim observer GR
                                        */
extern uint8_T ENDOUBLEBLIP;           /* Variable: ENDOUBLEBLIP
                                        * Referenced by: '<S14>/Double_Blip'
                                        * Enable QS double blip
                                        */
extern uint8_T ENGPDIAGRECHECK;        /* Variable: ENGPDIAGRECHECK
                                        * Referenced by: '<S9>/Diag_Gear'
                                        * Enable recheck diagnosis in no fault case
                                        */
extern uint8_T FLGONETRIPGR;           /* Variable: FLGONETRIPGR
                                        * Referenced by: '<S22>/Chart_Update_GearRatio'
                                        * flag GR one trip
                                        */
extern uint8_T GEARPOSRECDEB;          /* Variable: GEARPOSRECDEB
                                        * Referenced by: '<S18>/Chart'
                                        * GearPosRec debounce
                                        */
extern uint8_T GEARPOSRECDEFAULT;      /* Variable: GEARPOSRECDEFAULT
                                        * Referenced by: '<S18>/GEARPOSRECDEFAULT'
                                        * GearPosRec default value
                                        */
extern uint8_T NUMGEARPLAUSN;          /* Variable: NUMGEARPLAUSN
                                        * Referenced by: '<S9>/Diag_Gear'
                                        * Numver of gears not valid
                                        */
extern uint8_T NUMGEARPLAUSV;          /* Variable: NUMGEARPLAUSV
                                        * Referenced by: '<S9>/Diag_Gear'
                                        * Numver of gears valid
                                        */
extern uint8_T TIMDBLIP;               /* Variable: TIMDBLIP
                                        * Referenced by: '<S14>/Double_Blip'
                                        * blip confirmed time
                                        */
extern uint8_T TIMNFILT;               /* Variable: TIMNFILT
                                        * Referenced by: '<S9>/DetectGearPos'
                                        * Neutral filtering
                                        */
extern void GearMgm_Calc_GR_Threshold(void);
extern void GearMgm_Calc_InvGearRatio(uint8_T rtu_Idx, uint16_T rtu_GearRatio);
extern void GearMgm_fc_Diag(uint8_T rtu_ptFault, rtB_fc_Diag_GearMgm *localB);
extern void GearMgm_Calc_Stab_GearRatio(uint8_T rtu_ssReset, uint16_T
  rtu_gearRatioStart, uint16_T rtu_GearRatioCalc,
  rtB_Calc_Stab_GearRatio_GearMgm *localB, rtDW_Calc_Stab_GearRatio_GearMg
  *localDW);
extern void GearMgm_Init(void);
extern void GearMgm_CalcGearPos(void);
extern void GearMgm_T5ms(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint16_T GearRatio[7];          /* '<Root>/_DataStoreBlk_1' */

/* Gear ratio */
#endif                                 /* RTW_HEADER_GearMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
