/*
 * File: TrqSafLim_private.h
 *
 * Real-Time Workshop code generated for Simulink model TrqSafLim.
 *
 * Model version                        : 1.419
 * Real-Time Workshop file version      : 6.6  (R2007a)  01-Feb-2007
 * Real-Time Workshop file generated on : Fri Sep 21 17:24:57 2007
 * TLC version                          : 6.6 (Jan 16 2007)
 * C source code generated on           : Fri Sep 21 17:24:57 2007
 */

#ifndef _RTW_HEADER_TrqSafLim_private_h_
#define _RTW_HEADER_TrqSafLim_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "trq_est.h"
#include "vspeed_ctrl.h"
#include "recmgm.h"
#  include "rtlibsrc.h"
#define CALL_EVENT                     (MAX_uint8_T)
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Imported (extern) block parameters */
extern int16_T CMEMAX[2];              /* Variable: CMEMAX
                                        * '<S5>/Calc_Var'
                                        * Max cme (in torque limitation)
                                        */
extern int16_T CMIMAXRATE[2];          /* Variable: CMIMAXRATE
                                        * '<S5>/Calc_Var'
                                        * Max Cmi gradient in torque limitation
                                        */
extern int16_T CMIMINRATE;             /* Variable: CMIMINRATE
                                        * Referenced by blocks:
                                        * '<S7>/CMIMINRATE'
                                        * '<S8>/CMIMINRATE'
                                        * Min Cmi gradient in torque limitation
                                        */
void TrqSafLim_LimiterCmiTarget(uint8_T rtu_trqlimiterflag, int16_T rtu_1,
  int16_T rtu_cmimax, int16_T rtu_cmi_in_old, int16_T rtu_cmimaxrate, uint8_T
  rtu_en_limengslope_old, rtB_LimiterCmiTarget_TrqSafLim *localB);
void TrqSafLim_fcn_10ms_Init(void);
void TrqSafLim_fcn_10ms(void);
void TrqSafLim_fc_Reset(void);
 void TrqSafLim_step(void);
 void TrqSafLim_initialize(boolean_T firstTime);
 void TrqSafLim_Init(void);
 void TrqSafLim_T10ms(void);
 void TrqSafLim_stub(void);

#endif                                 /* _RTW_HEADER_TrqSafLim_private_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
