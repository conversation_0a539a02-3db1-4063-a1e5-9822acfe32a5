/*
 * File: TrqDriver_types.h
 *
 * Code generated for Simulink model 'TrqDriver'.
 *
 * Model version                  : 1.2235
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Aug  5 15:33:05 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TrqDriver_types_h_
#define RTW_HEADER_TrqDriver_types_h_

/*
 * Registered constraints for dimension variants
 */
/* Constraint 'REC_NO_GAS > 0' registered by:
 * '<S3>/CmeDriver_Management'
 */
#if REC_NO_GAS <= 0
# error "The preprocessor definition 'REC_NO_GAS' must be greater than '0'"
#endif

/* Constraint 'REC_NO_GAS < 22' registered by:
 * '<S3>/CmeDriver_Management'
 */
#if REC_NO_GAS >= 22
# error "The preprocessor definition 'REC_NO_GAS' must be less than '22'"
#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_TrqDriver RT_MODEL_TrqDriver;

#endif                                 /* RTW_HEADER_TrqDriver_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
