/*
 * File: TempMgm.c
 *
 * Code generated for Simulink model 'TempMgm'.
 *
 * Model version                  : 1.805
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jul  9 15:25:12 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "TempMgm.h"
#include "TempMgm_private.h"

/* user code (top of source file) */
/* System '<Root>/TempMgm' */
#ifdef _BUILD_TEMPMGM_

/* Block signals (default storage) */
BlockIO_TempMgm TempMgm_B;

/* Block states (default storage) */
D_Work_TempMgm TempMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_TempMgm TempMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_TempMgm TempMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T FlgTAirDiagOn;

/* TAir diagnosis tested */
uint8_T FlgTWat2DiagOn;

/* T Water 2 diagnosis tested */
uint8_T FlgTWatDiagOn;

/* T Water diagnosis tested */
uint32_T IDTempMgm;

/* ID Version */
int16_T TAir;

/* Air temperature */
int16_T TAirCrk;

/* Air temperature at crank */
int16_T TAirLin;

/* Interpolated air temperature */
int16_T TAirMax;

/* Air temperature max */
int16_T TAirMin;

/* Air temperature min */
int16_T TAirnofilt;

/* Air temperature not filtered */
int16_T TAtm;

/* Ambient temperature */
int16_T TWater;

/* Coolant temperature */
int16_T TWater2;

/* Coolant temperature 2 */
int16_T TWater2Crk;

/* Coolant temperature at crank */
int16_T TWater2Max;

/* Water 2 temperature max */
int16_T TWater2Min;

/* Water 2 temperature max */
int16_T TWaterCrk;

/* Coolant temperature at crank */
int16_T TWaterLin;

/* Interpolated coolant temperature */
int16_T TWaterLin2;

/* Interpolated coolant temperature 2 */
int16_T TWaterMax;

/* Water temperature max */
int16_T TWaterMin;

/* Water temperature max */
int16_T TWaterModel;

/* Modeled coolant temperature */
int16_T TWaternofilt;

/* Coolant temperature not filtered */
int16_T TWaternofilt2;

/* Coolant temperature 2 not filtered */
uint32_T TempMgmTimer;

/* counterd */
uint8_T flgTWat2DiagOn;

/* T Water 2 diagnosis tested */

/*
 * Output and update for enable system:
 *    '<S81>/Call_Diag'
 *    '<S73>/Call_Diag'
 */
void TempMgm_Call_Diag(boolean_T rtu_Enable, uint8_T rtu_diagId, uint8_T
  rtu_fault_elett, boolean_T rtu_fault_stuck, rtB_Call_Diag_TempMgm *localB)
{
  uint8_T rtb_Switch1_hhco;

  /* Outputs for Enabled SubSystem: '<S81>/Call_Diag' incorporates:
   *  EnablePort: '<S83>/Enable'
   */
  if (rtu_Enable) {
    /* Switch: '<S83>/Switch1' incorporates:
     *  Constant: '<S83>/NO_PT_FAULT'
     *  Switch: '<S83>/Switch'
     */
    if (rtu_fault_elett != 0) {
      rtb_Switch1_hhco = rtu_fault_elett;
    } else if (rtu_fault_stuck) {
      /* Switch: '<S83>/Switch' incorporates:
       *  Constant: '<S83>/SIGNAL_STUCK'
       */
      rtb_Switch1_hhco = SIGNAL_STUCK;
    } else {
      rtb_Switch1_hhco = NO_PT_FAULT;
    }

    /* End of Switch: '<S83>/Switch1' */

    /* S-Function (DiagMgm_SetDiagState): '<S86>/DiagMgm_SetDiagState' */
    DiagMgm_SetDiagState( rtu_diagId, rtb_Switch1_hhco,
                         &localB->DiagMgm_SetDiagState);
  }

  /* End of Outputs for SubSystem: '<S81>/Call_Diag' */
}

/* Output and update for function-call system: '<S30>/diag_twater2' */
void TempMgm_diag_twater2(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_RangeCheck_U16;
  boolean_T rtb_RelationalOperator5;
  boolean_T rtb_Switch1_f1dt;
  uint8_T rtb_Switch3;
  uint16_T rtb_DataTypeConversion1;
  uint16_T rtb_DataTypeConversion2;
  boolean_T rtb_RelationalOperator5_0;

  /* MinMax: '<S82>/MinMax1' incorporates:
   *  DataStoreRead: '<S82>/Data Store Read1'
   */
  if (TWater2Max > TWaterLin2) {
  } else {
    TWater2Max = TWaterLin2;
  }

  /* End of MinMax: '<S82>/MinMax1' */

  /* MinMax: '<S82>/MinMax' incorporates:
   *  DataStoreRead: '<S82>/Data Store Read2'
   */
  if (TWater2Min < TWaterLin2) {
  } else {
    TWater2Min = TWaterLin2;
  }

  /* End of MinMax: '<S82>/MinMax' */

  /* RelationalOperator: '<S82>/Relational Operator5' incorporates:
   *  Constant: '<S82>/TW2CRK'
   *  DataStoreRead: '<S82>/Data Store Read3'
   */
  rtb_RelationalOperator5 = (TWater2Crk > TW2CRK);

  /* Switch: '<S82>/Switch1' incorporates:
   *  Constant: '<S82>/STEPTWAT'
   *  Constant: '<S82>/STEPTWATHT'
   *  RelationalOperator: '<S82>/Relational Operator4'
   *  RelationalOperator: '<S82>/Relational Operator7'
   *  Sum: '<S82>/Add'
   */
  if (rtb_RelationalOperator5) {
    rtb_Switch1_f1dt = ((int16_T)(TWater2Max - TWater2Min) < STEPTWATHT);
  } else {
    rtb_Switch1_f1dt = ((int16_T)(TWater2Max - TWater2Min) < STEPTWAT);
  }

  /* End of Switch: '<S82>/Switch1' */

  /* Switch: '<S53>/ Switch3' incorporates:
   *  Constant: '<S53>/USE_TWATER_CAN'
   */
  if (USE_TWATER_CAN != 0) {
    /* Switch: '<S53>/ Switch4' incorporates:
     *  Constant: '<S53>/NO_PT_FAULT1'
     *  Constant: '<S53>/SIGNAL_NOT_VALID'
     *  Inport: '<Root>/VDTWaterCAN'
     */
    if (VDTWaterCAN != 0) {
      rtb_Switch3 = NO_PT_FAULT;
    } else {
      rtb_Switch3 = SIGNAL_NOT_VALID;
    }

    /* End of Switch: '<S53>/ Switch4' */
  } else {
    /* DataTypeConversion: '<S53>/Data Type Conversion2' incorporates:
     *  Constant: '<S53>/VTWATERMAX'
     */
    rtb_DataTypeConversion2 = VTWATERMAX;

    /* DataTypeConversion: '<S53>/Data Type Conversion1' incorporates:
     *  Constant: '<S53>/VTWATERMIN'
     */
    rtb_DataTypeConversion1 = VTWATERMIN;

    /* S-Function (DiagMgm_RangeCheck_U16): '<S79>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S53>/CC_TO_GND'
     *  Constant: '<S53>/CC_TO_VCC'
     *  Inport: '<Root>/VTWater2'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16, VTWater2,
      rtb_DataTypeConversion1, rtb_DataTypeConversion2, CC_TO_GND, CC_TO_VCC);

    /* Switch: '<S53>/ Switch1' incorporates:
     *  Constant: '<S53>/NO_PT_FAULT'
     *  RelationalOperator: '<S53>/Relational Operator'
     *  Switch: '<S53>/ Switch2'
     *  Switch: '<S53>/ Switch5'
     */
    if (rtb_DiagMgm_RangeCheck_U16 != NO_PT_FAULT) {
      rtb_Switch3 = rtb_DiagMgm_RangeCheck_U16;
    } else if (TempMgm_B.Call_Diag_lzy0.DiagMgm_SetDiagState != 0) {
      /* Switch: '<S53>/ Switch5' incorporates:
       *  Constant: '<S53>/NO_PT_FAULT2'
       */
      rtb_Switch3 = NO_PT_FAULT;
    } else if (TempMgm_B.ptfault_twater_func2 != 0) {
      /* Switch: '<S53>/ Switch2' incorporates:
       *  Constant: '<S53>/SIG_NOT_PLAUSIBLE'
       *  Switch: '<S53>/ Switch5'
       */
      rtb_Switch3 = SIG_NOT_PLAUSIBLE;
    } else {
      /* Switch: '<S53>/ Switch5' incorporates:
       *  Switch: '<S53>/ Switch2'
       */
      rtb_Switch3 = NO_PT_FAULT;
    }

    /* End of Switch: '<S53>/ Switch1' */
  }

  /* End of Switch: '<S53>/ Switch3' */

  /* Switch: '<S82>/Switch' incorporates:
   *  Constant: '<S82>/TW2TDCFORSTUCK'
   *  Constant: '<S82>/TW2TDCFORSTUCKHT'
   *  Inport: '<Root>/CntTdcCrk'
   *  RelationalOperator: '<S82>/Relational Operator3'
   *  RelationalOperator: '<S82>/Relational Operator6'
   */
  if (rtb_RelationalOperator5) {
    rtb_RelationalOperator5_0 = (CntTdcCrk > TW2TDCFORSTUCKHT);
  } else {
    rtb_RelationalOperator5_0 = (CntTdcCrk > TW2TDCFORSTUCK);
  }

  /* End of Switch: '<S82>/Switch' */

  /* Logic: '<S80>/Logical Operator' incorporates:
   *  Logic: '<S82>/Logical Operator'
   *  Logic: '<S82>/Logical Operator2'
   *  Logic: '<S82>/Logical Operator4'
   *  Logic: '<S82>/Logical Operator5'
   */
  rtb_RelationalOperator5 = ((!rtb_Switch1_f1dt) || (rtb_RelationalOperator5_0 &&
    (!rtb_RelationalOperator5)) || (rtb_Switch3 != 0));

  /* Logic: '<S81>/Logical Operator4' incorporates:
   *  Constant: '<S84>/Constant'
   *  DataStoreWrite: '<S53>/Data Store Write3'
   *  Inport: '<Root>/DrivingCycle'
   *  Logic: '<S81>/Logical Operator5'
   *  Memory: '<S81>/Memory'
   *  RelationalOperator: '<S84>/Compare'
   */
  flgTWat2DiagOn = (uint8_T)((rtb_RelationalOperator5 ||
    (TempMgm_DWork.Memory_PreviousInput_nvvx != 0)) && (DrivingCycle != DRVC_OFF));

  /* Outputs for Enabled SubSystem: '<S81>/Call_Diag' */
  /* Constant: '<S80>/DIAG_T_WATER_2' */
  TempMgm_Call_Diag(rtb_RelationalOperator5, DIAG_T_WATER_2, rtb_Switch3,
                    rtb_Switch1_f1dt, &TempMgm_B.Call_Diag_ba1w);

  /* End of Outputs for SubSystem: '<S81>/Call_Diag' */

  /* Update for Memory: '<S81>/Memory' incorporates:
   *  DataStoreWrite: '<S53>/Data Store Write3'
   */
  TempMgm_DWork.Memory_PreviousInput_nvvx = flgTWat2DiagOn;
}

/* Output and update for function-call system: '<S30>/diag_twater' */
void TempMgm_diag_twater(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_RangeCheck_U16_b2ji;
  boolean_T rtb_RelationalOperator3_kbwx;
  boolean_T rtb_Switch1_hx4b;
  uint8_T rtb_Switch3;
  uint16_T rtb_DataTypeConversion1;
  uint16_T rtb_DataTypeConversion;
  boolean_T rtb_RelationalOperator3_itxd;

  /* MinMax: '<S74>/MinMax' incorporates:
   *  DataStoreRead: '<S74>/Data Store Read2'
   */
  if (TWaterMin < TWaterLin) {
  } else {
    TWaterMin = TWaterLin;
  }

  /* End of MinMax: '<S74>/MinMax' */

  /* MinMax: '<S74>/MinMax1' incorporates:
   *  DataStoreRead: '<S74>/Data Store Read1'
   */
  if (TWaterMax > TWaterLin) {
  } else {
    TWaterMax = TWaterLin;
  }

  /* End of MinMax: '<S74>/MinMax1' */

  /* RelationalOperator: '<S74>/Relational Operator3' incorporates:
   *  Constant: '<S74>/TW1CRK'
   *  DataStoreRead: '<S74>/Data Store Read3'
   */
  rtb_RelationalOperator3_kbwx = (TWaterCrk > TW1CRK);

  /* Switch: '<S74>/Switch1' incorporates:
   *  Constant: '<S74>/STEPTWAT'
   *  Constant: '<S74>/STEPTWATHT'
   *  RelationalOperator: '<S74>/Relational Operator2'
   *  RelationalOperator: '<S74>/Relational Operator5'
   *  Sum: '<S74>/Add'
   */
  if (rtb_RelationalOperator3_kbwx) {
    rtb_Switch1_hx4b = ((int16_T)(TWaterMax - TWaterMin) < STEPTWATHT);
  } else {
    rtb_Switch1_hx4b = ((int16_T)(TWaterMax - TWaterMin) < STEPTWAT);
  }

  /* End of Switch: '<S74>/Switch1' */

  /* Switch: '<S52>/ Switch3' incorporates:
   *  Constant: '<S52>/USE_TWATER_CAN'
   */
  if (USE_TWATER_CAN != 0) {
    /* Switch: '<S52>/ Switch4' incorporates:
     *  Constant: '<S52>/NO_PT_FAULT1'
     *  Constant: '<S52>/SIGNAL_NOT_VALID'
     *  Inport: '<Root>/VDTWaterCAN'
     */
    if (VDTWaterCAN != 0) {
      rtb_Switch3 = NO_PT_FAULT;
    } else {
      rtb_Switch3 = SIGNAL_NOT_VALID;
    }

    /* End of Switch: '<S52>/ Switch4' */
  } else {
    /* DataTypeConversion: '<S52>/Data Type Conversion' incorporates:
     *  Constant: '<S52>/VTWATERMAX'
     */
    rtb_DataTypeConversion = VTWATERMAX;

    /* DataTypeConversion: '<S52>/Data Type Conversion1' incorporates:
     *  Constant: '<S52>/VTWATERMIN'
     */
    rtb_DataTypeConversion1 = VTWATERMIN;

    /* S-Function (DiagMgm_RangeCheck_U16): '<S71>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S52>/CC_TO_GND'
     *  Constant: '<S52>/CC_TO_VCC'
     *  Inport: '<Root>/VTWater'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_b2ji, VTWater,
      rtb_DataTypeConversion1, rtb_DataTypeConversion, CC_TO_GND, CC_TO_VCC);

    /* Switch: '<S52>/ Switch1' incorporates:
     *  Constant: '<S52>/NO_PT_FAULT'
     *  RelationalOperator: '<S52>/Relational Operator'
     *  Switch: '<S52>/ Switch2'
     */
    if (rtb_DiagMgm_RangeCheck_U16_b2ji != NO_PT_FAULT) {
      rtb_Switch3 = rtb_DiagMgm_RangeCheck_U16_b2ji;
    } else if (TempMgm_B.ptfault_twater_func != 0) {
      /* Switch: '<S52>/ Switch2' incorporates:
       *  Constant: '<S52>/SIG_NOT_PLAUSIBLE'
       */
      rtb_Switch3 = SIG_NOT_PLAUSIBLE;
    } else {
      rtb_Switch3 = NO_PT_FAULT;
    }

    /* End of Switch: '<S52>/ Switch1' */
  }

  /* End of Switch: '<S52>/ Switch3' */

  /* Switch: '<S74>/Switch' incorporates:
   *  Constant: '<S74>/TW1TDCFORSTUCK'
   *  Constant: '<S74>/TW1TDCFORSTUCKHT'
   *  Inport: '<Root>/CntTdcCrk'
   *  RelationalOperator: '<S74>/Relational Operator1'
   *  RelationalOperator: '<S74>/Relational Operator4'
   */
  if (rtb_RelationalOperator3_kbwx) {
    rtb_RelationalOperator3_itxd = (CntTdcCrk > TW1TDCFORSTUCKHT);
  } else {
    rtb_RelationalOperator3_itxd = (CntTdcCrk > TW1TDCFORSTUCK);
  }

  /* End of Switch: '<S74>/Switch' */

  /* Logic: '<S72>/Logical Operator' incorporates:
   *  Logic: '<S74>/Logical Operator'
   *  Logic: '<S74>/Logical Operator2'
   *  Logic: '<S74>/Logical Operator4'
   *  Logic: '<S74>/Logical Operator5'
   */
  rtb_RelationalOperator3_kbwx = ((!rtb_Switch1_hx4b) ||
    (rtb_RelationalOperator3_itxd && (!rtb_RelationalOperator3_kbwx)) ||
    (rtb_Switch3 != 0));

  /* Outputs for Enabled SubSystem: '<S73>/Call_Diag' */
  /* Constant: '<S72>/DIAG_T_WATER' */
  TempMgm_Call_Diag(rtb_RelationalOperator3_kbwx, DIAG_T_WATER, rtb_Switch3,
                    rtb_Switch1_hx4b, &TempMgm_B.Call_Diag_lzy0);

  /* End of Outputs for SubSystem: '<S73>/Call_Diag' */

  /* Logic: '<S73>/Logical Operator4' incorporates:
   *  Constant: '<S76>/Constant'
   *  Inport: '<Root>/DrivingCycle'
   *  Logic: '<S73>/Logical Operator5'
   *  Memory: '<S73>/Memory'
   *  RelationalOperator: '<S76>/Compare'
   */
  FlgTWatDiagOn = (uint8_T)((rtb_RelationalOperator3_kbwx || (FlgTWatDiagOn != 0))
    && (DrivingCycle != DRVC_OFF));
}

/* Output and update for function-call system: '<S30>/calc_twatertmp' */
void TempMgm_calc_twatertmp(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  int16_T rtb_LookUp_IR_S16;
  boolean_T rtb_RelationalOperator1_fvxr;
  int8_T s59_iter;
  uint16_T rtb_DataTypeConversion1[16];
  int16_T rtb_DataStoreRead1_okko;
  uint8_T rtb_Compare_nyqr;
  int32_T i;

  /* DataTypeConversion: '<S51>/Data Type Conversion' incorporates:
   *  Constant: '<S51>/BKTWATER'
   */
  for (i = 0; i < 16; i++) {
    rtb_DataTypeConversion1[i] = BKTWATER[i];
  }

  /* End of DataTypeConversion: '<S51>/Data Type Conversion' */

  /* S-Function (PreLookUpIdSearch_U16): '<S66>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S57>/BKTWATER_dim'
   *  Inport: '<Root>/VTWater'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o2,
                        &rtb_PreLookUpIdSearch_U16_o1, VTWater,
                        &rtb_DataTypeConversion1[0], ((uint8_T)BKTWATER_dim));

  /* S-Function (LookUp_IR_S16): '<S65>/LookUp_IR_S16' incorporates:
   *  Constant: '<S51>/VTTWATER'
   *  Constant: '<S57>/BKTWATER_dim'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTTWATER[0], rtb_PreLookUpIdSearch_U16_o2,
                rtb_PreLookUpIdSearch_U16_o1, ((uint8_T)BKTWATER_dim));

  /* DataTypeConversion: '<S67>/Conversion' */
  TWaterLin = rtb_LookUp_IR_S16;

  /* Outputs for Atomic SubSystem: '<S51>/Cool_Temp_Model' */
  /* Outputs for Iterator SubSystem: '<S56>/CmiPotEst_accumulator' incorporates:
   *  ForIterator: '<S59>/For Iterator'
   */
  /* InitializeConditions for Memory: '<S59>/Memory' */
  i = 0;

  /* Constant: '<S56>/N_CYLINDER' */
  rtb_Compare_nyqr = ((uint8_T)N_CYLINDER);
  if (((uint8_T)N_CYLINDER) > 127) {
    rtb_Compare_nyqr = 127U;
  }

  /* End of Constant: '<S56>/N_CYLINDER' */
  for (s59_iter = 0; s59_iter < (int8_T)rtb_Compare_nyqr; s59_iter++) {
    /* Sum: '<S59>/Add' incorporates:
     *  Inport: '<Root>/CmiPotEst'
     *  Memory: '<S59>/Memory'
     *  Selector: '<S59>/Selector'
     */
    TempMgm_B.Add = CmiPotEst[s59_iter] + i;

    /* Update for Memory: '<S59>/Memory' */
    i = TempMgm_B.Add;
  }

  /* End of Outputs for SubSystem: '<S56>/CmiPotEst_accumulator' */

  /* DataTypeConversion: '<S56>/Data Type Conversion' */
  i = TempMgm_B.Add;
  if (TempMgm_B.Add > 32767) {
    i = 32767;
  } else {
    if (TempMgm_B.Add < -32768) {
      i = -32768;
    }
  }

  rtb_LookUp_IR_S16 = (int16_T)i;

  /* End of DataTypeConversion: '<S56>/Data Type Conversion' */

  /* S-Function (Look2D_U16_S16_U16): '<S62>/Look2D_U16_S16_U16' incorporates:
   *  Constant: '<S56>/BKCMIKFTH2O'
   *  Constant: '<S56>/BKCMIKFTH2O_dim'
   *  Constant: '<S56>/BKRPMKFTH2O'
   *  Constant: '<S56>/BKRPMKFTH2O_dim'
   *  Constant: '<S56>/TBKFTH2O'
   *  Inport: '<Root>/Rpm'
   */
  Look2D_U16_S16_U16( &rtb_PreLookUpIdSearch_U16_o1, &TBKFTH2O[0],
                     rtb_LookUp_IR_S16, &BKCMIKFTH2O[0], ((uint8_T)
    BKCMIKFTH2O_dim), Rpm, &BKRPMKFTH2O[0], ((uint8_T)BKRPMKFTH2O_dim));

  /* DataStoreRead: '<S56>/Data Store Read1' */
  rtb_DataStoreRead1_okko = TWater;

  /* RelationalOperator: '<S60>/Compare' incorporates:
   *  Constant: '<S60>/Constant'
   *  Inport: '<Root>/Rpm'
   */
  rtb_Compare_nyqr = (uint8_T)(Rpm == 0);

  /* S-Function (FOF_Reset_S16_FXP): '<S61>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S56>/TH2OFINALVALUE'
   */
  FOF_Reset_S16_FXP( &rtb_LookUp_IR_S16, &rtb_FOF_Reset_S16_FXP_o2,
                    TH2OFINALVALUE, rtb_PreLookUpIdSearch_U16_o1,
                    rtb_DataStoreRead1_okko, rtb_Compare_nyqr,
                    TempMgm_DWork.twatermodel_hr);

  /* DataStoreWrite: '<S56>/Data Store Write' */
  TempMgm_DWork.twatermodel_hr = rtb_FOF_Reset_S16_FXP_o2;

  /* DataTypeConversion: '<S63>/Conversion' */
  TWaterModel = rtb_LookUp_IR_S16;

  /* End of Outputs for SubSystem: '<S51>/Cool_Temp_Model' */

  /* RelationalOperator: '<S51>/Relational Operator1' incorporates:
   *  Constant: '<S51>/THCOHWWATMOD'
   *  Sum: '<S51>/Sum'
   */
  rtb_RelationalOperator1_fvxr = (TWaterLin > (int16_T)(TWaterModel -
    THCOHWWATMOD));

  /* Switch: '<S51>/ Switch2' incorporates:
   *  Constant: '<S51>/USE_TWATER_CAN'
   *  Inport: '<Root>/TWaterCAN'
   *  Switch: '<S51>/ Switch1'
   */
  if (USE_TWATER_CAN != 0) {
    TempMgm_B.twaterlinsat = TWaterCAN;
  } else if (rtb_RelationalOperator1_fvxr) {
    /* Switch: '<S51>/ Switch1' */
    TempMgm_B.twaterlinsat = TWaterLin;
  } else {
    TempMgm_B.twaterlinsat = TWaterModel;
  }

  /* End of Switch: '<S51>/ Switch2' */

  /* DataTypeConversion: '<S51>/Data Type Conversion1' incorporates:
   *  Constant: '<S51>/BKTWATER1'
   */
  for (i = 0; i < 16; i++) {
    rtb_DataTypeConversion1[i] = BKTWATER[i];
  }

  /* End of DataTypeConversion: '<S51>/Data Type Conversion1' */

  /* S-Function (PreLookUpIdSearch_U16): '<S69>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S58>/BKTWATER_dim'
   *  Inport: '<Root>/VTWater2'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, VTWater2,
                        &rtb_DataTypeConversion1[0], ((uint8_T)BKTWATER_dim));

  /* S-Function (LookUp_IR_S16): '<S68>/LookUp_IR_S16' incorporates:
   *  Constant: '<S51>/VTTWATER1'
   *  Constant: '<S58>/BKTWATER_dim'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTTWATER[0], rtb_PreLookUpIdSearch_U16_o1,
                rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKTWATER_dim));

  /* DataTypeConversion: '<S70>/Conversion' */
  TWaterLin2 = rtb_LookUp_IR_S16;

  /* Switch: '<S51>/ Switch3' incorporates:
   *  Constant: '<S51>/USE_TWATER_CAN1'
   *  Inport: '<Root>/TWaterCAN'
   */
  if (USE_TWATER_CAN != 0) {
    TempMgm_B.twaterlinsat2 = TWaterCAN;
  } else {
    TempMgm_B.twaterlinsat2 = TWaterLin2;
  }

  /* End of Switch: '<S51>/ Switch3' */

  /* Switch: '<S51>/ Switch4' incorporates:
   *  Inport: '<Root>/TempMgmTimer'
   */
  if (rtb_RelationalOperator1_fvxr) {
    TempMgm_DWork.Memory_PreviousInput = TempMgmTimer;
  }

  /* End of Switch: '<S51>/ Switch4' */

  /* Switch: '<S51>/ Switch6' incorporates:
   *  Constant: '<S51>/THCOHWWATMOD1'
   *  Constant: '<S51>/THCOHWWATMOD2'
   *  Inport: '<Root>/TempMgmTimer'
   *  Logic: '<S51>/Logical Operator'
   *  RelationalOperator: '<S51>/Relational Operator3'
   *  RelationalOperator: '<S51>/Relational Operator5'
   */
  if ((TWaterLin2 >= THCOHTW2DIAGTW2) || (TempMgm_B.twaterlinsat <=
       THCOHTWDIAGTW2)) {
    TempMgm_DWork.Memory1_PreviousInput = TempMgmTimer;
  }

  /* End of Switch: '<S51>/ Switch6' */

  /* RelationalOperator: '<S51>/Relational Operator2' incorporates:
   *  Constant: '<S51>/TVALFTWFUNC'
   *  Inport: '<Root>/TempMgmTimer'
   *  Sum: '<S51>/Sum1'
   */
  TempMgm_B.ptfault_twater_func = (uint8_T)(TempMgmTimer >
    TempMgm_DWork.Memory_PreviousInput + ((uint32_T)TVALFTWFUNC << 7));

  /* RelationalOperator: '<S51>/Relational Operator4' incorporates:
   *  Constant: '<S51>/TVALFTWFUNC2'
   *  Inport: '<Root>/TempMgmTimer'
   *  Sum: '<S51>/Sum3'
   */
  TempMgm_B.ptfault_twater_func2 = (uint8_T)(TempMgmTimer >
    TempMgm_DWork.Memory1_PreviousInput + ((uint32_T)TVALFTWFUNC2 << 7));
}

/* Output and update for function-call system: '<S5>/T100ms' */
void TempMgm_T100ms(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2_bxtg;
  int32_T rtb_FOF_Reset_S16_FXP_o2_cdpi;
  int32_T rtb_FOF_Reset_S16_FXP_o2_e0hh;
  uint16_T rtb_PreLookUpIdSearch_U16__o3qv;
  int16_T rtb_FOF_Reset_S16_FXP_o1_pxsn;
  uint8_T rtb_DiagMgm_RangeCheck_U16_gdeb;
  uint8_T rtb_DiagMgm_RangeCheck_U16_jecr;
  boolean_T rtb_LogicalOperator1;
  uint16_T rtb_Memory_pxsk;
  boolean_T rtb_RelationalOperator1_c4ya;
  uint8_T rtb_Switch1_bruf;
  boolean_T rtb_LogicalOperator_hlqh;
  int16_T rtb_Switch_j5t3;
  uint16_T rtb_DataTypeConversion3[16];
  uint16_T rtb_DataTypeConversion1;
  int32_T i;

  {
    /* user code (Output function Header) */

    /* System '<S5>/T100ms' */
    uint64_T ticksTimer;
    uint64_T msTimer;
    TIMING_GetAbsTimer(&(ticksTimer));
    TIMING_TicksToMilliSeconds(ticksTimer, &msTimer);
    TempMgmTimer = (uint32_T)(msTimer & 0x00000000FFFFFFFF);

    /* DataTypeConversion: '<S29>/Data Type Conversion3' incorporates:
     *  Constant: '<S29>/BKTAIR'
     */
    for (i = 0; i < 16; i++) {
      rtb_DataTypeConversion3[i] = BKTAIR[i];
    }

    /* End of DataTypeConversion: '<S29>/Data Type Conversion3' */

    /* S-Function (PreLookUpIdSearch_U16): '<S48>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S35>/BKTAIR_dim'
     *  Inport: '<Root>/VTAir'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16__o3qv, &rtb_Memory_pxsk,
                          VTAir, &rtb_DataTypeConversion3[0], ((uint8_T)
      BKTAIR_dim));

    /* S-Function (LookUp_IR_S16): '<S47>/LookUp_IR_S16' incorporates:
     *  Constant: '<S29>/VTTAIR'
     *  Constant: '<S35>/BKTAIR_dim'
     */
    LookUp_IR_S16( &rtb_FOF_Reset_S16_FXP_o1_pxsn, &VTTAIR[0],
                  rtb_PreLookUpIdSearch_U16__o3qv, rtb_Memory_pxsk, ((uint8_T)
      BKTAIR_dim));

    /* DataTypeConversion: '<S49>/Conversion' */
    TAirLin = rtb_FOF_Reset_S16_FXP_o1_pxsn;

    /* DataStoreRead: '<S34>/Data Store Read1' */
    rtb_FOF_Reset_S16_FXP_o1_pxsn = TAirnofilt;

    /* S-Function (FOF_Reset_S16_FXP): '<S45>/FOF_Reset_S16_FXP' incorporates:
     *  Constant: '<S34>/KFTAIR'
     *  Constant: '<S34>/RESET'
     */
    FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1_pxsn,
                      &rtb_FOF_Reset_S16_FXP_o2_bxtg,
                      rtb_FOF_Reset_S16_FXP_o1_pxsn, KFTAIR,
                      rtb_FOF_Reset_S16_FXP_o1_pxsn, ((uint8_T)0U),
                      TempMgm_DWork.tair_hr);

    /* Switch: '<S34>/Switch' incorporates:
     *  Memory: '<S29>/Memory'
     *
     * Block description for '<S34>/Switch':
     *  FAULT
     */
    if (TempMgm_DWork.Memory_PreviousInput_kd10 > 1) {
      rtb_Switch_j5t3 = TAirLin;
    } else {
      rtb_Switch_j5t3 = rtb_FOF_Reset_S16_FXP_o1_pxsn;
    }

    /* End of Switch: '<S34>/Switch' */

    /* MinMax: '<S33>/MinMax' incorporates:
     *  DataStoreRead: '<S33>/Data Store Read2'
     */
    if (TAirMin < rtb_Switch_j5t3) {
    } else {
      TAirMin = rtb_Switch_j5t3;
    }

    /* End of MinMax: '<S33>/MinMax' */

    /* MinMax: '<S33>/MinMax1' incorporates:
     *  DataStoreRead: '<S33>/Data Store Read1'
     */
    if (TAirMax > rtb_Switch_j5t3) {
    } else {
      TAirMax = rtb_Switch_j5t3;
    }

    /* End of MinMax: '<S33>/MinMax1' */

    /* Sum: '<S33>/Add1' incorporates:
     *  Inport: '<Root>/TAirCAN'
     */
    rtb_Switch_j5t3 -= TAirCAN;

    /* Abs: '<S33>/Abs' */
    if (rtb_Switch_j5t3 < 0) {
      rtb_Switch_j5t3 = (int16_T)-rtb_Switch_j5t3;
    }

    /* End of Abs: '<S33>/Abs' */

    /* Switch: '<S29>/ Switch1' incorporates:
     *  Constant: '<S29>/USE_TAIR_CAN'
     */
    if (USE_TAIR_CAN != 0) {
      /* Switch: '<S29>/ Switch4' incorporates:
       *  Constant: '<S29>/NO_PT_FAULT'
       *  Constant: '<S29>/SIGNAL_NOT_VALID'
       *  Inport: '<Root>/VDTAirCAN'
       */
      if (VDTAirCAN != 0) {
        rtb_Switch1_bruf = SIGNAL_NOT_VALID;
      } else {
        rtb_Switch1_bruf = NO_PT_FAULT;
      }

      /* End of Switch: '<S29>/ Switch4' */
    } else {
      /* DataTypeConversion: '<S29>/Data Type Conversion2' incorporates:
       *  Constant: '<S29>/VTAIRMAX'
       */
      rtb_Memory_pxsk = VTAIRMAX;

      /* DataTypeConversion: '<S29>/Data Type Conversion1' incorporates:
       *  Constant: '<S29>/VTAIRMIN'
       */
      rtb_DataTypeConversion1 = VTAIRMIN;

      /* S-Function (DiagMgm_RangeCheck_U16): '<S31>/DiagMgm_RangeCheck_U16' incorporates:
       *  Constant: '<S29>/CC_TO_GND'
       *  Constant: '<S29>/CC_TO_VCC'
       *  Inport: '<Root>/VTAir'
       */
      DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_jecr, VTAir,
        rtb_DataTypeConversion1, rtb_Memory_pxsk, CC_TO_GND, CC_TO_VCC);

      /* Switch: '<S29>/ Switch3' */
      if (rtb_DiagMgm_RangeCheck_U16_jecr != 0) {
        rtb_Switch1_bruf = rtb_DiagMgm_RangeCheck_U16_jecr;
      } else {
        /* DataTypeConversion: '<S29>/Data Type Conversion5' incorporates:
         *  Constant: '<S29>/VTAIRHIGH'
         */
        rtb_Memory_pxsk = VTAIRHIGH;

        /* DataTypeConversion: '<S29>/Data Type Conversion4' incorporates:
         *  Constant: '<S29>/VTAIRLOW'
         */
        rtb_DataTypeConversion1 = VTAIRLOW;

        /* S-Function (DiagMgm_RangeCheck_U16): '<S32>/DiagMgm_RangeCheck_U16' incorporates:
         *  Constant: '<S29>/SIGNAL_OVERRANGE'
         *  Inport: '<Root>/VTAir'
         */
        DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_gdeb, VTAir,
          rtb_DataTypeConversion1, rtb_Memory_pxsk, SIGNAL_OVERRANGE,
          SIGNAL_OVERRANGE);
        rtb_Switch1_bruf = rtb_DiagMgm_RangeCheck_U16_gdeb;
      }

      /* End of Switch: '<S29>/ Switch3' */
    }

    /* End of Switch: '<S29>/ Switch1' */

    /* RelationalOperator: '<S33>/Relational Operator1' incorporates:
     *  Constant: '<S33>/TAIRTDCFORSTUCK'
     *  Inport: '<Root>/CntTdcCrk'
     */
    rtb_RelationalOperator1_c4ya = (CntTdcCrk > TAIRTDCFORSTUCK);

    /* RelationalOperator: '<S33>/Relational Operator3' incorporates:
     *  Constant: '<S33>/VEHDIAGTAIR'
     *  Inport: '<Root>/VehSpeed'
     */
    rtb_LogicalOperator1 = (VehSpeed > VEHDIAGTAIR);

    /* Memory: '<S40>/Memory' */
    rtb_Memory_pxsk = TempMgm_DWork.Memory_PreviousInput_cuov;

    /* Product: '<S40>/Product' incorporates:
     *  Sum: '<S40>/Add'
     */
    TempMgm_DWork.Memory_PreviousInput_cuov = (uint16_T)(rtb_LogicalOperator1 ?
      (int32_T)(uint16_T)((uint32_T)rtb_LogicalOperator1 + rtb_Memory_pxsk) : 0);

    /* Logic: '<S33>/Logical Operator1' incorporates:
     *  Constant: '<S37>/Constant'
     *  Constant: '<S38>/Constant'
     *  Constant: '<S40>/TAIRTIMCOMPARE'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/VDTAirCAN'
     *  RelationalOperator: '<S37>/Compare'
     *  RelationalOperator: '<S38>/Compare'
     *  RelationalOperator: '<S40>/Relational Operator3'
     */
    rtb_LogicalOperator1 = ((Rpm > 0) && (VDTAirCAN == 0) &&
      (TempMgm_DWork.Memory_PreviousInput_cuov > TAIRTIMCOMPARE));

    /* Logic: '<S33>/Logical Operator' */
    rtb_LogicalOperator_hlqh = ((rtb_Switch1_bruf != 0) ||
      rtb_RelationalOperator1_c4ya || rtb_LogicalOperator1);

    /* Outputs for Enabled SubSystem: '<S39>/Call_Diag' incorporates:
     *  EnablePort: '<S41>/Enable'
     */
    if (rtb_LogicalOperator_hlqh) {
      /* Switch: '<S41>/Switch1' incorporates:
       *  Constant: '<S33>/STEPTAIR'
       *  Constant: '<S33>/TAIRMAXERR'
       *  Logic: '<S33>/Logical Operator2'
       *  Logic: '<S33>/Logical Operator3'
       *  RelationalOperator: '<S33>/Relational Operator2'
       *  RelationalOperator: '<S33>/Relational Operator4'
       *  Sum: '<S33>/Add'
       *  Switch: '<S41>/Switch'
       *  Switch: '<S41>/Switch2'
       */
      if (rtb_Switch1_bruf != 0) {
      } else if (((int16_T)(TAirMax - TAirMin) < STEPTAIR) &&
                 rtb_RelationalOperator1_c4ya) {
        /* Switch: '<S41>/Switch' incorporates:
         *  Constant: '<S41>/SIGNAL_STUCK'
         */
        rtb_Switch1_bruf = SIGNAL_STUCK;
      } else if (rtb_LogicalOperator1 && (rtb_Switch_j5t3 > TAIRMAXERR)) {
        /* Switch: '<S41>/Switch2' incorporates:
         *  Constant: '<S41>/SIGNAL_NOT_VALID'
         *  Switch: '<S41>/Switch'
         */
        rtb_Switch1_bruf = SIGNAL_NOT_VALID;
      } else {
        /* Switch: '<S41>/Switch' incorporates:
         *  Constant: '<S41>/NO_PT_FAULT'
         *  Switch: '<S41>/Switch2'
         */
        rtb_Switch1_bruf = NO_PT_FAULT;
      }

      /* End of Switch: '<S41>/Switch1' */

      /* S-Function (DiagMgm_SetDiagState): '<S44>/DiagMgm_SetDiagState' incorporates:
       *  Constant: '<S33>/DIAG_T_AIR'
       */
      DiagMgm_SetDiagState( DIAG_T_AIR, rtb_Switch1_bruf,
                           &TempMgm_B.DiagMgm_SetDiagState);
    }

    /* End of Outputs for SubSystem: '<S39>/Call_Diag' */

    /* Logic: '<S39>/Logical Operator4' incorporates:
     *  Constant: '<S42>/Constant'
     *  Inport: '<Root>/DrivingCycle'
     *  Logic: '<S39>/Logical Operator5'
     *  Memory: '<S39>/Memory'
     *  RelationalOperator: '<S42>/Compare'
     */
    FlgTAirDiagOn = (uint8_T)((rtb_LogicalOperator_hlqh || (FlgTAirDiagOn != 0))
      && (DrivingCycle != DRVC_OFF));

    /* DataStoreWrite: '<S34>/Data Store Write' */
    TAir = rtb_FOF_Reset_S16_FXP_o1_pxsn;

    /* DataStoreWrite: '<S34>/Data Store Write1' */
    TempMgm_DWork.tair_hr = rtb_FOF_Reset_S16_FXP_o2_bxtg;

    /* Chart: '<S29>/Rec_TAir' */
    /* Gateway: TempMgm/T100ms/Temp_Air/Rec_TAir */
    /* During: TempMgm/T100ms/Temp_Air/Rec_TAir */
    /* Entry Internal: TempMgm/T100ms/Temp_Air/Rec_TAir */
    /* Transition: '<S36>:1' */
    if (TempMgm_B.DiagMgm_SetDiagState == FAULT) {
      /* Transition: '<S36>:4' */
      TAirnofilt = TAIRREC;
    } else {
      /* Transition: '<S36>:2' */
      if (TempMgm_B.DiagMgm_SetDiagState == FAULT_FILTERING) {
        /* Transition: '<S36>:3' */
      } else {
        /* Switch: '<S29>/ Switch2' incorporates:
         *  Constant: '<S29>/USE_TAIR_CAN'
         *  Inport: '<Root>/TAirCAN'
         */
        /* Transition: '<S36>:5' */
        if (USE_TAIR_CAN != 0) {
          TAirnofilt = TAirCAN;
        } else {
          TAirnofilt = TAirLin;
        }

        /* End of Switch: '<S29>/ Switch2' */
      }
    }

    /* End of Chart: '<S29>/Rec_TAir' */

    /* Chart: '<S30>/Calc_TWater' incorporates:
     *  SubSystem: '<S30>/calc_twatertmp'
     */
    /* Gateway: TempMgm/T100ms/Temp_Water/Calc_TWater */
    /* During: TempMgm/T100ms/Temp_Water/Calc_TWater */
    /* Entry Internal: TempMgm/T100ms/Temp_Water/Calc_TWater */
    /* Transition: '<S50>:56' */
    /* Event: '<S50>:24' */
    TempMgm_calc_twatertmp();

    /* Chart: '<S30>/Calc_TWater' incorporates:
     *  SubSystem: '<S30>/diag_twater'
     */
    /* Event: '<S50>:23' */
    TempMgm_diag_twater();
    if (TempMgm_B.Call_Diag_lzy0.DiagMgm_SetDiagState == FAULT) {
      /* Inport: '<Root>/Rpm' */
      /* Transition: '<S50>:4' */
      if (Rpm > 0) {
        /* Transition: '<S50>:7' */
        TWaternofilt = TWaterModel;
      } else {
        /* Transition: '<S50>:6' */
        TWaternofilt = TAir;

        /*  sbagliato, in attesa di avere
           un modello realistico. */
      }
    } else {
      /* Transition: '<S50>:2' */
      if (TempMgm_B.Call_Diag_lzy0.DiagMgm_SetDiagState == FAULT_FILTERING) {
        /* Transition: '<S50>:3' */
      } else {
        /* Transition: '<S50>:5' */
        TWaternofilt = TempMgm_B.twaterlinsat;
      }
    }

    /* Chart: '<S30>/Calc_TWater' incorporates:
     *  SubSystem: '<S30>/filt_twater'
     */
    /* DataStoreRead: '<S54>/Data Store Read1' */
    /* Transition: '<S50>:8' */
    /* Event: '<S50>:25' */
    TWater = TWaternofilt;

    /* S-Function (FOF_Reset_S16_FXP): '<S87>/FOF_Reset_S16_FXP' incorporates:
     *  Constant: '<S54>/Constant'
     *  Constant: '<S54>/KFTWATER'
     */
    FOF_Reset_S16_FXP( (&(TWater)), &rtb_FOF_Reset_S16_FXP_o2_cdpi, TWater,
                      KFTWATER, TWater, ((uint8_T)0U), TempMgm_DWork.twater_hr);

    /* DataStoreWrite: '<S54>/Data Store Write' */
    TempMgm_DWork.twater_hr = rtb_FOF_Reset_S16_FXP_o2_cdpi;
    if (USETWATER2 != 0) {
      /* Outputs for Function Call SubSystem: '<S30>/diag_twater2' */
      /* Transition: '<S50>:28' */
      /*  fc_calc_twater2; */
      /* Event: '<S50>:50' */
      TempMgm_diag_twater2();

      /* End of Outputs for SubSystem: '<S30>/diag_twater2' */
      FlgTWat2DiagOn = flgTWat2DiagOn;
      if (TempMgm_B.Call_Diag_ba1w.DiagMgm_SetDiagState == FAULT) {
        /* Transition: '<S50>:37' */
        TWaternofilt2 = TWaternofilt;
      } else {
        /* Transition: '<S50>:35' */
        if (TempMgm_B.Call_Diag_ba1w.DiagMgm_SetDiagState == FAULT_FILTERING) {
          /* Transition: '<S50>:36' */
        } else {
          /* Transition: '<S50>:38' */
          TWaternofilt2 = TempMgm_B.twaterlinsat2;
        }
      }
    } else {
      /* Transition: '<S50>:59' */
      TWaternofilt2 = TWaternofilt;
      FlgTWat2DiagOn = 1U;
    }

    /* Chart: '<S30>/Calc_TWater' incorporates:
     *  SubSystem: '<S30>/filt_twater2'
     */
    /* DataStoreRead: '<S55>/Data Store Read1' */
    /* Transition: '<S50>:41' */
    /* Event: '<S50>:51' */
    TWater2 = TWaternofilt2;

    /* S-Function (FOF_Reset_S16_FXP): '<S89>/FOF_Reset_S16_FXP' incorporates:
     *  Constant: '<S55>/Constant'
     *  Constant: '<S55>/KFTWATER'
     */
    FOF_Reset_S16_FXP( (&(TWater2)), &rtb_FOF_Reset_S16_FXP_o2_e0hh, TWater2,
                      KFTWATER, TWater2, ((uint8_T)0U), TempMgm_DWork.twater2_hr);

    /* DataStoreWrite: '<S55>/Data Store Write' */
    TempMgm_DWork.twater2_hr = rtb_FOF_Reset_S16_FXP_o2_e0hh;

    /* Chart: '<S9>/Crank_Management' incorporates:
     *  Inport: '<Root>/EndStartFlg'
     */
    /* Gateway: TempMgm/T100ms/Crank_Management */
    /* During: TempMgm/T100ms/Crank_Management */
    /* Entry Internal: TempMgm/T100ms/Crank_Management */
    /* Transition: '<S28>:1' */
    if (EndStartFlg == 0) {
      /* Transition: '<S28>:2' */
      TAirCrk = TAir;
      TWaterCrk = TWater;
      TWater2Crk = TWater2;
    } else {
      /* Transition: '<S28>:3' */
    }

    /* Transition: '<S28>:13' */
    TAtm = TAir;

    /* End of Chart: '<S9>/Crank_Management' */
  }

  /* Update for Memory: '<S29>/Memory' */
  TempMgm_DWork.Memory_PreviousInput_kd10 = TempMgm_B.DiagMgm_SetDiagState;
}

/* Output and update for function-call system: '<S5>/Init' */
void TempMgm_Init(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16__dvmv;
  uint16_T rtb_PreLookUpIdSearch_U16__osr0;
  uint16_T rtb_PreLookUpIdSearch_U16__otfu;
  uint16_T rtb_PreLookUpIdSearch_U16__bfnz;
  uint16_T rtb_PreLookUpIdSearch_U16__nbgp;
  uint16_T rtb_PreLookUpIdSearch_U16__d3md;
  int16_T rtb_LookUp_IR_S16_deb0;
  int16_T rtb_LookUp_IR_S16_g3nr;
  int16_T rtb_LookUp_IR_S16_jf4j;
  uint8_T rtb_DiagMgm_RangeCheck_U16_ezo0;
  uint8_T rtb_DiagMgm_RangeCheck_U16_e0ku;
  uint8_T rtb_DiagMgm_RangeCheck_U16_bqdr;
  uint8_T rtb_Switch7;
  uint16_T rtb_DataTypeConversion12;
  uint16_T rtb_DataTypeConversion11;
  uint16_T rtb_DataTypeConversion10[16];
  int32_T i;

  /* Switch: '<S8>/ Switch5' incorporates:
   *  Constant: '<S8>/NO_PT_FAULT1'
   *  Constant: '<S8>/USE_TAIR_CAN'
   */
  if (USE_TAIR_CAN != 0) {
    rtb_Switch7 = NO_PT_FAULT;
  } else {
    /* DataTypeConversion: '<S8>/Data Type Conversion11' incorporates:
     *  Constant: '<S8>/VTAIRMAX'
     */
    rtb_DataTypeConversion11 = VTAIRMAX;

    /* DataTypeConversion: '<S8>/Data Type Conversion12' incorporates:
     *  Constant: '<S8>/VTAIRMIN'
     */
    rtb_DataTypeConversion12 = VTAIRMIN;

    /* S-Function (DiagMgm_RangeCheck_U16): '<S13>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S8>/CC_TO_GND'
     *  Constant: '<S8>/CC_TO_VCC'
     *  Inport: '<Root>/VTAir'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_e0ku, VTAir,
      rtb_DataTypeConversion12, rtb_DataTypeConversion11, CC_TO_GND, CC_TO_VCC);
    rtb_Switch7 = rtb_DiagMgm_RangeCheck_U16_e0ku;
  }

  /* End of Switch: '<S8>/ Switch5' */

  /* Switch: '<S8>/ Switch' incorporates:
   *  Constant: '<S8>/NO_PT_FAULT3'
   *  Constant: '<S8>/TAIRREC'
   *  Constant: '<S8>/USE_TAIR_CAN1'
   *  DataStoreWrite: '<S10>/Data Store Write12'
   *  RelationalOperator: '<S8>/Relational Operator'
   *  Switch: '<S8>/ Switch6'
   */
  if (NO_PT_FAULT != rtb_Switch7) {
    TAirnofilt = TAIRREC;
  } else if (USE_TAIR_CAN != 0) {
    /* DataStoreWrite: '<S10>/Data Store Write12' incorporates:
     *  Inport: '<Root>/TAirCAN'
     *  Switch: '<S8>/ Switch6'
     */
    TAirnofilt = TAirCAN;
  } else {
    for (i = 0; i < 16; i++) {
      /* DataTypeConversion: '<S8>/Data Type Conversion10' incorporates:
       *  Constant: '<S8>/BKTAIR'
       *  Switch: '<S8>/ Switch6'
       */
      rtb_DataTypeConversion10[i] = BKTAIR[i];
    }

    /* S-Function (PreLookUpIdSearch_U16): '<S20>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S16>/BKTAIR_dim'
     *  Inport: '<Root>/VTAir'
     *  Switch: '<S8>/ Switch6'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16__dvmv,
                          &rtb_PreLookUpIdSearch_U16__osr0, VTAir,
                          &rtb_DataTypeConversion10[0], ((uint8_T)BKTAIR_dim));

    /* S-Function (LookUp_IR_S16): '<S19>/LookUp_IR_S16' incorporates:
     *  Constant: '<S16>/BKTAIR_dim'
     *  Constant: '<S8>/VTTAIR'
     *  Switch: '<S8>/ Switch6'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16_deb0, &VTTAIR[0],
                  rtb_PreLookUpIdSearch_U16__dvmv,
                  rtb_PreLookUpIdSearch_U16__osr0, ((uint8_T)BKTAIR_dim));

    /* DataStoreWrite: '<S10>/Data Store Write12' incorporates:
     *  Switch: '<S8>/ Switch6'
     */
    TAirnofilt = rtb_LookUp_IR_S16_deb0;
  }

  /* End of Switch: '<S8>/ Switch' */

  /* DataStoreWrite: '<S10>/Data Store Write13' incorporates:
   *  DataStoreWrite: '<S10>/Data Store Write12'
   */
  TAirMax = TAirnofilt;

  /* DataStoreWrite: '<S10>/Data Store Write15' incorporates:
   *  DataStoreWrite: '<S10>/Data Store Write12'
   */
  TAirMin = TAirnofilt;

  /* DataStoreWrite: '<S10>/Data Store Write2' incorporates:
   *  DataStoreWrite: '<S10>/Data Store Write12'
   */
  TAir = TAirnofilt;

  /* DataStoreWrite: '<S10>/Data Store Write4' incorporates:
   *  DataStoreWrite: '<S10>/Data Store Write12'
   */
  TAirCrk = TAirnofilt;

  /* DataTypeConversion: '<S10>/Data Type Conversion2' incorporates:
   *  DataStoreWrite: '<S10>/Data Store Write12'
   *  DataStoreWrite: '<S10>/Data Store Write6'
   */
  TempMgm_DWork.tair_hr = TAirnofilt << 14;

  /* DataStoreWrite: '<S10>/Data Store Write7' incorporates:
   *  DataStoreWrite: '<S10>/Data Store Write12'
   */
  TAtm = TAirnofilt;

  /* Switch: '<S8>/ Switch4' incorporates:
   *  Constant: '<S8>/NO_PT_FAULT'
   *  Constant: '<S8>/USE_TWATER_CAN1'
   */
  if (USE_TWATER_CAN != 0) {
    rtb_Switch7 = NO_PT_FAULT;
  } else {
    /* DataTypeConversion: '<S8>/Data Type Conversion8' incorporates:
     *  Constant: '<S8>/VTWATERMAX'
     */
    rtb_DataTypeConversion11 = VTWATERMAX;

    /* DataTypeConversion: '<S8>/Data Type Conversion9' incorporates:
     *  Constant: '<S8>/VTWATERMIN'
     */
    rtb_DataTypeConversion12 = VTWATERMIN;

    /* S-Function (DiagMgm_RangeCheck_U16): '<S14>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S8>/CC_TO_GND1'
     *  Constant: '<S8>/CC_TO_VCC1'
     *  Inport: '<Root>/VTWater'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_ezo0, VTWater,
      rtb_DataTypeConversion12, rtb_DataTypeConversion11, CC_TO_GND, CC_TO_VCC);
    rtb_Switch7 = rtb_DiagMgm_RangeCheck_U16_ezo0;
  }

  /* End of Switch: '<S8>/ Switch4' */

  /* Switch: '<S8>/ Switch1' incorporates:
   *  Constant: '<S8>/NO_PT_FAULT4'
   *  Constant: '<S8>/USE_TWATER_CAN'
   *  DataStoreWrite: '<S10>/Data Store Write12'
   *  DataStoreWrite: '<S11>/Data Store Write10'
   *  RelationalOperator: '<S8>/Relational Operator1'
   *  Switch: '<S8>/ Switch3'
   */
  if (rtb_Switch7 != NO_PT_FAULT) {
    TWaternofilt = TAirnofilt;
  } else if (USE_TWATER_CAN != 0) {
    /* DataStoreWrite: '<S11>/Data Store Write10' incorporates:
     *  Inport: '<Root>/TWaterCAN'
     *  Switch: '<S8>/ Switch3'
     */
    TWaternofilt = TWaterCAN;
  } else {
    for (i = 0; i < 16; i++) {
      /* DataTypeConversion: '<S8>/Data Type Conversion7' incorporates:
       *  Constant: '<S8>/BKTWATER'
       *  Switch: '<S8>/ Switch3'
       */
      rtb_DataTypeConversion10[i] = BKTWATER[i];
    }

    /* S-Function (PreLookUpIdSearch_U16): '<S23>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S17>/BKTWATER_dim'
     *  Inport: '<Root>/VTWater'
     *  Switch: '<S8>/ Switch3'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16__otfu,
                          &rtb_PreLookUpIdSearch_U16__bfnz, VTWater,
                          &rtb_DataTypeConversion10[0], ((uint8_T)BKTWATER_dim));

    /* S-Function (LookUp_IR_S16): '<S22>/LookUp_IR_S16' incorporates:
     *  Constant: '<S17>/BKTWATER_dim'
     *  Constant: '<S8>/VTTWATER'
     *  Switch: '<S8>/ Switch3'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16_jf4j, &VTTWATER[0],
                  rtb_PreLookUpIdSearch_U16__otfu,
                  rtb_PreLookUpIdSearch_U16__bfnz, ((uint8_T)BKTWATER_dim));

    /* Switch: '<S8>/ Switch2' incorporates:
     *  Constant: '<S8>/THCOHWWATAIR'
     *  DataStoreWrite: '<S10>/Data Store Write12'
     *  RelationalOperator: '<S8>/Relational Operator2'
     *  Sum: '<S8>/Sum'
     *  Switch: '<S8>/ Switch3'
     */
    if (rtb_LookUp_IR_S16_jf4j > (int16_T)(TAirnofilt - THCOHWWATAIR)) {
      /* DataStoreWrite: '<S11>/Data Store Write10' */
      TWaternofilt = rtb_LookUp_IR_S16_jf4j;
    } else {
      /* DataStoreWrite: '<S11>/Data Store Write10' */
      TWaternofilt = TAirnofilt;
    }

    /* End of Switch: '<S8>/ Switch2' */
  }

  /* End of Switch: '<S8>/ Switch1' */

  /* DataTypeConversion: '<S11>/Data Type Conversion1' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write10'
   *  DataTypeConversion: '<S11>/Data Type Conversion'
   */
  TempMgm_DWork.twatermodel_hr = TWaternofilt << 14;

  /* DataTypeConversion: '<S11>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write1'
   */
  TempMgm_DWork.twater_hr = TempMgm_DWork.twatermodel_hr;

  /* DataStoreWrite: '<S11>/Data Store Write2' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write10'
   */
  TWaterMax = TWaternofilt;

  /* DataStoreWrite: '<S11>/Data Store Write3' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write10'
   */
  TWater = TWaternofilt;

  /* DataStoreWrite: '<S11>/Data Store Write4' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write10'
   */
  TWaterMin = TWaternofilt;

  /* DataStoreWrite: '<S11>/Data Store Write5' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write10'
   */
  TWaterCrk = TWaternofilt;

  /* Switch: '<S8>/ Switch7' incorporates:
   *  Constant: '<S8>/NO_PT_FAULT2'
   *  Constant: '<S8>/USE_TWATER_CAN2'
   */
  if (USE_TWATER_CAN != 0) {
    rtb_Switch7 = NO_PT_FAULT;
  } else {
    /* DataTypeConversion: '<S8>/Data Type Conversion5' incorporates:
     *  Constant: '<S8>/VTWATERMAX1'
     */
    rtb_DataTypeConversion11 = VTWATERMAX;

    /* DataTypeConversion: '<S8>/Data Type Conversion6' incorporates:
     *  Constant: '<S8>/VTWATERMIN1'
     */
    rtb_DataTypeConversion12 = VTWATERMIN;

    /* S-Function (DiagMgm_RangeCheck_U16): '<S15>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S8>/CC_TO_GND2'
     *  Constant: '<S8>/CC_TO_VCC2'
     *  Inport: '<Root>/VTWater2'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_bqdr, VTWater2,
      rtb_DataTypeConversion12, rtb_DataTypeConversion11, CC_TO_GND, CC_TO_VCC);
    rtb_Switch7 = rtb_DiagMgm_RangeCheck_U16_bqdr;
  }

  /* End of Switch: '<S8>/ Switch7' */

  /* Switch: '<S8>/ Switch8' incorporates:
   *  Constant: '<S8>/NO_PT_FAULT5'
   *  Constant: '<S8>/USETWATER1'
   *  Constant: '<S8>/USE_TWATER_CAN3'
   *  DataStoreWrite: '<S11>/Data Store Write10'
   *  DataStoreWrite: '<S12>/Data Store Write1'
   *  Logic: '<S8>/Logical Operator'
   *  Logic: '<S8>/Logical Operator1'
   *  RelationalOperator: '<S8>/Relational Operator3'
   *  Switch: '<S8>/ Switch10'
   */
  if ((NO_PT_FAULT != rtb_Switch7) || (USETWATER2 == 0)) {
    TWater2Max = TWaternofilt;
  } else if (USE_TWATER_CAN != 0) {
    /* DataStoreWrite: '<S12>/Data Store Write1' incorporates:
     *  Inport: '<Root>/TWaterCAN'
     *  Switch: '<S8>/ Switch10'
     */
    TWater2Max = TWaterCAN;
  } else {
    for (i = 0; i < 16; i++) {
      /* DataTypeConversion: '<S8>/Data Type Conversion4' incorporates:
       *  Constant: '<S8>/BKTWATER1'
       *  Switch: '<S8>/ Switch10'
       */
      rtb_DataTypeConversion10[i] = BKTWATER[i];
    }

    /* S-Function (PreLookUpIdSearch_U16): '<S26>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S18>/BKTWATER_dim'
     *  Inport: '<Root>/VTWater2'
     *  Switch: '<S8>/ Switch10'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16__nbgp,
                          &rtb_PreLookUpIdSearch_U16__d3md, VTWater2,
                          &rtb_DataTypeConversion10[0], ((uint8_T)BKTWATER_dim));

    /* S-Function (LookUp_IR_S16): '<S25>/LookUp_IR_S16' incorporates:
     *  Constant: '<S18>/BKTWATER_dim'
     *  Constant: '<S8>/VTTWATER1'
     *  Switch: '<S8>/ Switch10'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16_g3nr, &VTTWATER[0],
                  rtb_PreLookUpIdSearch_U16__nbgp,
                  rtb_PreLookUpIdSearch_U16__d3md, ((uint8_T)BKTWATER_dim));

    /* DataStoreWrite: '<S12>/Data Store Write1' incorporates:
     *  Switch: '<S8>/ Switch10'
     */
    TWater2Max = rtb_LookUp_IR_S16_g3nr;
  }

  /* End of Switch: '<S8>/ Switch8' */

  /* DataStoreWrite: '<S12>/Data Store Write11' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write1'
   */
  TWaternofilt2 = TWater2Max;

  /* DataStoreWrite: '<S12>/Data Store Write2' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write1'
   */
  TWater2Min = TWater2Max;

  /* DataStoreWrite: '<S12>/Data Store Write3' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write1'
   */
  TWater2Crk = TWater2Max;

  /* DataStoreWrite: '<S12>/Data Store Write8' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write1'
   */
  TWater2 = TWater2Max;

  /* DataTypeConversion: '<S12>/Data Type Conversion3' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write1'
   *  DataStoreWrite: '<S12>/Data Store Write9'
   */
  TempMgm_DWork.twater2_hr = TWater2Max << 14;

  /* Constant: '<S8>/ID_TEMPMGM' */
  IDTempMgm = ID_TEMPMGM;
}

/* Model step function */
void TempMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/TempMgm' */

  /* Outputs for Triggered SubSystem: '<S5>/Call_PowerOn' incorporates:
   *  TriggerPort: '<S7>/ev_PowerOn'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((TempMgm_U.ev_PowerOn > 0) &&
      (TempMgm_PrevZCSigState.Call_PowerOn_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S5>/Init'
     */
    TempMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  TempMgm_PrevZCSigState.Call_PowerOn_Trig_ZCE = (ZCSigState)
    (TempMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S5>/Call_PowerOn' */

  /* Outputs for Triggered SubSystem: '<S5>/Call_100ms' incorporates:
   *  TriggerPort: '<S6>/ev_100ms'
   */
  /* Inport: '<Root>/ev_100ms' */
  if ((TempMgm_U.ev_100ms > 0) && (TempMgm_PrevZCSigState.Call_100ms_Trig_ZCE !=
       POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S5>/T100ms'
     */
    TempMgm_T100ms();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  TempMgm_PrevZCSigState.Call_100ms_Trig_ZCE = (ZCSigState)(TempMgm_U.ev_100ms >
    0);

  /* End of Inport: '<Root>/ev_100ms' */
  /* End of Outputs for SubSystem: '<S5>/Call_100ms' */

  /* End of Outputs for SubSystem: '<Root>/TempMgm' */
}

/* Model initialize function */
void TempMgm_initialize(void)
{
  TempMgm_PrevZCSigState.Call_100ms_Trig_ZCE = POS_ZCSIG;
  TempMgm_PrevZCSigState.Call_PowerOn_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/TempMgm' */
#else                                  //	_BUILD_TEMPMGM_

int16_T TWater;
int16_T TWater2;
int16_T TAir;
int16_T TAirCrk;
int16_T TWaterCrk;
void TempMgm_stub(void);
void TempMgm_Init(void)
{
  TempMgm_stub();
}

void TempMgm_T100ms(void)
{
  TempMgm_stub();
}

void TempMgm_stub(void)
{
  TWater = (20*16);
  TWater2 = (20*16);
  TAir = (20*16);
  TAirCrk = (20*16);
  TWaterCrk = (20*16);
}

#endif                                 //	_BUILD_TEMPMGM_

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
