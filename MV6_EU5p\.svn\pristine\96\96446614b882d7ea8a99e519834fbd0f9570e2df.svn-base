/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _SAF3_SPI_H_
#define _SAF3_SPI_H_

/**************** INCLUDE FILES ****************/
#include "sys.h"
#include "typedefs.h"

/**************** DEFINES ****************/

/* Coherency Check return values */
#define DATA_VAL_OK     0
#define DATA_VAL_NOT_OK 1

/* Comm Protocol Control Word Defs */
#define LOAD_ENABLE         1
#define TX_ANSW_RND_CODE    2
#define REQ_SMP_RND_CODE    3
#define REQ_SMP_FW          4
#define REQ_START_SEQ_ACK   5
#define REQ_SMP_STOP_CAUSE  6
#define REQ_ADCH_0          7
#define REQ_ADCH_1          8
#define KEY_OFF             9
/* SMP Boot Layer Comm Protocol Word Defs */
#define FLASH_CRC_CODE_RQ   (0x0A)   /* type 2 */
#define APPL_ADDRESS        (0x0B)   /* type 1 */
#define APPL_CORRECT        (0x0C)   /* type 0 */
#define T3_MSG              (0x0D)   /* type 3 */
#define END_APPL_DOWNLOAD   (0x0E)   /* type 0 */
#define IS_SMP_READY        (0x0F)   /* type 2 */
#define SMP_ERASE           (0x10)   /* type 0 */

/* SPI Comm message Type */
#define SPI_MSG_TYPE_0   0
#define SPI_MSG_TYPE_1   1
#define SPI_MSG_TYPE_2   2
#define SPI_MSG_TYPE_3   3

/* Comm Errors Codes Defs */
#define NORMAL                (0)
#define TOUT_RND_CODE         (1)
#define WRONG_RND_CODE_00     (2)
#define TOUT_FW_SMP           (3)
#define TOUT_START_SEQ_ACK    (4)
#define MAX_REP_CODE          (5)
#define TOUT_CNT_ERR          (6)
#define SMP_STOP_CAUSE        (7)
#define TOUT_READ_AD          (8)
#define WRONG_S3STATE         (9)
#define TOUT_CRC_CODE         (10)
#define FAIL_LOAD_TEST        (11)
#define WRONG_ACK_START_SEQ   (12)
#define MAXCNT_SMP_FLASHED    (13)
#define WRONG_SAF2_OUT_BUF    (14)
#define WRONG_S23_MODULE_COMP (15)
#define WRONG_EXECODE         (16)
#define SAF2_HALT             (17)
#define WRONG_LOAD_EN_ACK     (18)

/* SPI Comm message Lenght */
#define TXRX_MAX_LENGHT (5)
#define LEN_MSG_TYP0    (2)
#define LEN_MSG_TYP1    (4)
#define LEN_MSG_TYP2    (5)

/* RND code verify */
#define SMP_FIRST_RDC   (0)
#define SMP_FIRST_RND_MASK  (0x0001)
#define ACK_START_SEQ   (13)
#define ACK_LOAD_EN     (0xAA)
/* SPI Dummy Data */
#define SPI_DUMMY_DATA    (0xCC)

/**************** MACRO ****************/
#define SAF3_DATA_VAL_CHK(data,ndata) (~(data) == ndata) ? DATA_VAL_OK : DATA_VAL_NOT_OK



/**************** PROTOTYPES ****************/

extern void Saf3_SPI_MmpSmpComm (uint8_t CtrlWord, uint16_t TxData);
extern void Saf3_SPI_TxRxData(void);
extern uint8_t S3FlgSmpReady;
/**************** VARS ****************/
/**************** funzioni che gestiscono il boot ***********/
/* definitions */
/* state list */

/* type 3 message length */
#define LEN_MSG_TYP3    (36)


#endif /* _SAF3_SPI_H_*/
