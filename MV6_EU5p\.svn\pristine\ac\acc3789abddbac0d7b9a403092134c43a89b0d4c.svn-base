/*
 * File: WatPumpCtrl_private.h
 *
 * Real-Time Workshop code generated for Simulink model WatPumpCtrl.
 *
 * Model version                        : 1.701
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed Dec 19 09:43:59 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed Dec 19 09:43:59 2007
 */

#ifndef _RTW_HEADER_WatPumpCtrl_private_h_
#define _RTW_HEADER_WatPumpCtrl_private_h_

#include "rtwtypes.h"

# include "rtlibsrc.h"
#define CALL_EVENT                      (MAX_uint8_T)

/* Private macros used by the generated code to access rtModel */

#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file.
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else
/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                  /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                  /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                  /* __RTWTYPES_H__ */

extern uint8_T _sfEvent_WatPumpCtrl_;

/* Computed Parameter: Gain
 * '<S9>/Gain'
 */
#define rtcP_Gain_Gain                  (MIN_int32_T)

/* Computed Parameter: X0
 * '<S1>/Memory'
 */
#define rtcP_Memory_X0                  (0)

/* Computed Parameter: Gain
 * '<S2>/Gain'
 */
#define rtcP_Gain_Gain_h                (-32768)

/* Imported (extern) block signals */

extern uint16_T CntTdcCrk;              /* '<Root>/CntTdcCrk' */

extern uint16_T Rpm;                    /* '<Root>/Rpm' */

extern int16_T TWater;                  /* '<Root>/TWater' */

/* Prototipi delle funzioni */
void WatPumpCtrl_initialize(boolean_T firstTime);
void WatPumpCtrl_step(void);
void WatPumpCtrl_WatPumpCtrl_D(uint16_T rtu_0, int16_T rtu_1, int16_T rtu_2);
void WatPumpCtrl_WatPumpCtrl_R(void);
void WatPumpCtrl_Init(void);
void WatPumpCtrl_100ms(void);

#endif                                  /* _RTW_HEADER_WatPumpCtrl_private_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
