/** #########################################################################
**     Filename  : TIMING.H
**     Project   :
**     Processor : MPC5554
**     Version   :
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 11/05/2005
**     Abstract  :
**
**         Header file of timing management
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ######################################################################### */
#ifndef _TIMING_H_
#define _TIMING_H_
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "Engflag.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define TIMEOUT_PENDING     0x0
#define TIMEOUT_EXPIRED     0x1

///This is a very c-stylish way of writing 1.125 or (as in this case) 14.2% 
#define EXEC_TOLERANCE_NUM            8U     //1.142
///This is a very c-stylish way of writing 1.125 or (as in this case) 14.2% 
#define EXEC_TOLERANCE_DEN            7U     //1.142
///Number of us in one second
#define US_IN_1_S               1000000     
///Number of ms in one second
#define MS_100_IN_1_S               10U
//IIR filter weight for CPU load. BE CAREFUL: use only multiple of 2   
#define CPU_LOAD_IIR_FILTER     2

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
// None

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef uint64_t timeoutHandler_t;


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T Seconds;
extern uint8_T Minutes;
extern uint16_T Hours;

extern uint32_T OdometerKeyOn;
extern uint16_T OdoMILActive;

extern uint16_T EngStressWarm;      /* 01 - Engine Stress Warm in minutes */
extern uint16_T TrackUseTime;       /* 02 - Track Use Time in minutes */
extern uint32_T Odometer;           /* 03 - Odometer Data / 16 in km */
extern uint16_T EOHours;              /* 04 - Engine operating hours / 8*/
extern uint8_T CrashEventCnt;       /* 05 - Crash Event Counter */
extern uint8_T StressCondResetCnt;  /* 06 - Stress Condition Reset Counter */
extern uint8_T HourResetCnt;        /* 07 - Engine operating hours reset counter */
extern uint8_T EngOverCnt;          /* 08 - Engine Overdrive Counter */
extern uint8_T EngOverDur;          /* 09 - Last Engine Overdrive Duration in s / 10 1 decimals */
extern uint8_T MaxRpmOver;          /* 0A - Max Engine Overdrive (2^8) rpm */
extern uint8_T EcuFlashingCnt;      /* 0B - ECU Flashing Counter */
extern uint16_T OdometerLastFlash;  /* 0C - Last ecu flashing odometer km */
extern uint16_T WlampOnTime;        /* 0D - Warning Lamp On Time min */
extern uint16_T SafLampOnTime;      /* 0E - Safety On Time min */
extern uint16_T MILLmpOnTime;       /* T.B.D - MIL On Time min */
extern uint16_T EngStressCold;      /* 0F - Engine Stress Cold min */
extern uint8_T LampOnTimeResetCnt;  /* DE - Counter 0x0D/0x0E must be increased by one each time
                                    the IOLI command 0xDE is invoked RLI 0x0D, 0x0E must 
                                    not be deleted as a result of reprogramming*/

extern uint32_T CntRpmLimStress;    /* 01 - Engine Stress Rpm in sec */
extern uint16_T CntRpmLimStressRLI;
extern uint32_T EEECULoadMax;
extern uint32_T EECpuLoadPercMax;
extern uint32_T EEStoreInc;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern const uint16_T RPMSTRESSTHR;
extern const int16_T TWATERSTRESSTHRUP;
extern const uint16_T RPMTRACK;
extern const uint16_T THROTTLETRACK;
extern const uint16_T RPMOVERTHR;
extern const int16_T TWATERSTRESSTHRLOW;
extern const uint8_T ENTESTTIMING;
extern const uint8_T ENTESTISRTIMING;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void	TIMING_IvorExHandler(void);
int16_t TIMING_Config(void) ;
int16_t TIMING_Init(void);
void TIMING_GetAbsTimer(uint64_t *abstime);
void TIMING_GetAbsMsecTimer(uint32_t *msTime);
int16_t TIMING_TicksToSeconds(uint64_t abstime, uint64_t *sTime);
int16_t TIMING_TicksToMilliSeconds(uint64_t abstime, uint64_t *msTime);
int16_t TIMING_TicksToMicroSeconds(uint64_t abstime, uint64_t *usTime);
int16_t TIMING_SetTimeout(uint32_t us_timeout, timeoutHandler_t *handler);
int16_t TIMING_GetTimeoutStatus(timeoutHandler_t handler, uint8_t *status);
void DayTime_Init(void);
void DayTime_T100ms(void);

void RLI_T100ms(void);
void EngStressWarm_T100ms(void);
void EngStressRpm_T100ms(void);
void TrackUseTime_T100ms(void);
void Odometer_Init(void);
void Odometer_T100ms(void);
void Odometer_MILActive_T100ms(void);
void EOHours_T100ms(void);
void CrashEventCnt_T100ms(void);
void EngOverDur_T100ms(void);
void MaxRpmOver_T100ms(void);
void EcuFlashingCnt_(void);
void WlampOnTime_T100ms(void);
void SafLampOnTime_T100ms(void);
void MILLmpOnTime_T100ms(void);
void EngStressCold_T100ms(void);
void EraseStressCond(void);
void EraseHours(void);
void EraseLampOnTime(void);

#endif // TEMPLATE_H

/****************************************************************************
 ****************************************************************************/

