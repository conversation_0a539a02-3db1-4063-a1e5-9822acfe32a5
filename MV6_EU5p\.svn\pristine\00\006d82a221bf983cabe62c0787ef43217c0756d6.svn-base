#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_CPUMGM_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 

//threshold to raise CPU punctual fault [counter]
__declspec(section ".calib") uint8_T THRERRCPU =    3;   //   3

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_CPUMGM_
