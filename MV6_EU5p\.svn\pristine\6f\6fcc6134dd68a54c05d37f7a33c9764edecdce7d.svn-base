#ifndef _CENSORSHIP_RECOVERY_SSD_H_
#define _CENSORSHIP_RECOVERY_SSD_H_
   
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define NEXUS_ALREADY_ENABLED    (-10)
#define NEXUS_ALREADY_DISABLED   (-11)

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * checkFlashPWD - Check flash serial pwd on shadow region
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void checkFlashPWD(void);

 /*--------------------------------------------------------------------------*
 * NEXUS_Disable - Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t NEXUS_Disable(void);
   
#endif /* _CENSORSHIP_RECOVERY_SSD_H_ */
   
