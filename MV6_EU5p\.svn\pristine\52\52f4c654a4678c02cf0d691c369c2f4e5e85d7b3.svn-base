/*
 * File: CmeFilterMgm.h
 *
 * Code generated for Simulink model 'CmeFilterMgm'.
 *
 * Model version                  : 1.2383
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar 27 14:58:16 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (19), Warnings (6), Errors (8)
 */

#ifndef RTW_HEADER_CmeFilterMgm_h_
#define RTW_HEADER_CmeFilterMgm_h_
#ifndef CmeFilterMgm_COMMON_INCLUDES_
# define CmeFilterMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* CmeFilterMgm_COMMON_INCLUDES_ */

#include "CmeFilterMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "cmefilter_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKCMEDRCMEFILT_dim             5U                        /* Referenced by:
                                                                  * '<S12>/BKCMEDRCMEFILT_dim'
                                                                  * '<S16>/BKCMEDRCMEFILT_dim'
                                                                  */

/* BKCMEDRCMEFILT dimension */
#define BKCMEDRIVPDD_dim               5U                        /* Referenced by: '<S16>/BKCMEDRIVPDD_dim' */

/* BKCMEDRIVPDD dimension */
#define BKRPMTRQDRIV_dim               10U                       /* Referenced by:
                                                                  * '<S9>/BKRPMTRQDRIV_dim'
                                                                  * '<S16>/BKRPMTRQDRIV_dim'
                                                                  * '<S29>/BKRPMTRQDRIV_dim'
                                                                  * '<S47>/BKRPMTRQDRIV_dim'
                                                                  */

/* BKRPMDRIVTRQ dimension */
#define CME_I_CL                       2U                        /* Referenced by:
                                                                  * '<S24>/CmeDriverI_Offset'
                                                                  * '<S24>/TrigCL'
                                                                  */

/* Closed_Loop */
#define CME_I_INIT                     0U                        /* Referenced by: '<S24>/CmeDriverI_Offset' */

/* Init */
#define CME_I_OL                       1U                        /* Referenced by: '<S24>/CmeDriverI_Offset' */

/* Open_Loop */
#define CM_INIT_HIR                    0                         /* Referenced by:
                                                                  * '<S25>/Memory1'
                                                                  * '<S26>/Memory'
                                                                  * '<S47>/Memory'
                                                                  */

/* Torque initial value */
#define DIM_CMEI_WAVE_BUFF             17                        /* Referenced by: '<S24>/CmeDriverI_Offset' */

/* num Sample */
#define G1_UINT16T_EN7                 128U                      /* Referenced by: '<S28>/GasResp_calc' */

/* G1_UINT16T_EN7 */
#define ID_CME_FILT_MGM                20775227U                 /* Referenced by: '<S2>/ID_CME_FILT_MGM' */

/* mask */
#define IN_DU_CHANGE                   3U                        /* Referenced by: '<S24>/TrigCL' */

/* IN_DU_CHANGE */
#define IN_DU_CL                       1U                        /* Referenced by: '<S24>/TrigCL' */

/* IN_DU_CL */
#define MAX_INT32T_EN19                536870912                 /* Referenced by:
                                                                  * '<S2>/MAX_INT32T_EN'
                                                                  * '<S27>/MAX_INT32T_EN19'
                                                                  * '<S28>/MAX_INT32T_EN2'
                                                                  */

/* MAX_INT32T_EN19 */
#define MIN_INT16T_EN9                 -32768                    /* Referenced by:
                                                                  * '<S12>/MIN_INT16T_EN9'
                                                                  * '<S12>/MIN_INT16T_EN9_2'
                                                                  */

/* MIN_INT16T_EN9 */
#define MIN_INT32T_EN19                -536870912                /* Referenced by:
                                                                  * '<S2>/MIN_INT32T_EN19'
                                                                  * '<S27>/MIN_INT32T_EN19'
                                                                  */

/* MIN_INT32T_EN19 */
#define NUM_DCMEDRIV                   10U                       /* Referenced by: '<S8>/buffCmeDriver' */

/* NUM_DCMEDRIV dimension */
#define WAIT_DU_CL                     0U                        /* Referenced by: '<S24>/TrigCL' */

/* WAIT_DU_CL */
#define WAIT_DU_EXIT                   2U                        /* Referenced by: '<S24>/TrigCL' */

/* WAIT_DU_EXIT */

/* Block signals (default storage) */
typedef struct {
  uint16_T CntCDICLOff_n;              /* '<S24>/CmeDriverI_Offset' */
  uint16_T PreLookUpIdSearch_U16_o1;   /* '<S14>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2;   /* '<S14>/PreLookUpIdSearch_U16' */
  uint16_T outGainGR;                  /* '<S28>/GasResp_calc' */
  uint16_T Merge;                      /* '<S15>/Merge' */
  uint16_T Merge1;                     /* '<S15>/Merge1' */
  int16_T CmeDriverICLOffOut_l;        /* '<S24>/CmeDriverI_Offset' */
  int16_T CmeDriverICLOff;             /* '<S24>/CmeDriverI_Offset' */
  int16_T LookUp_IR_S16;               /* '<S50>/LookUp_IR_S16' */
  int16_T FOF_Reset_S16_FXP_o1;        /* '<S34>/FOF_Reset_S16_FXP' */
  int16_T FOF_Reset_S16_FXP_o1_a;      /* '<S36>/FOF_Reset_S16_FXP' */
  uint8_T flgCmeDrPMaxRSat;            /* '<S47>/Calc_flgCmeDrPMaxRSat' */
  uint8_T outLimGC;                    /* '<S29>/Lim_AfterChange' */
  uint8_T TrLimGCTrig_j;               /* '<S29>/Lim_AfterChange' */
  uint8_T outFlgKfCmeDGResp;           /* '<S28>/GasResp_calc' */
  uint8_T ountFlgGainAct;              /* '<S28>/GasResp_calc' */
  uint8_T FlgResCmeDriverI_b;          /* '<S24>/TrigResFilter' */
  uint8_T FlgCmeTrigCL;                /* '<S24>/TrigCL' */
  uint8_T FlgCmeExitCL;                /* '<S24>/TrigCL' */
  uint8_T StCmeITrCL_o;                /* '<S24>/TrigCL' */
  uint8_T flgQShift;                   /* '<S24>/TrigCL' */
  uint8_T StCmeICL_i;                  /* '<S24>/CmeDriverI_Offset' */
} BlockIO_CmeFilterMgm;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  int32_T grGain_hr;                   /* '<S1>/Data Store Memory2' */
  int32_T cmedriverp_hr;               /* '<S1>/Data Store Memory3' */
  int32_T Memory1_PreviousInput;       /* '<S25>/Memory1' */
  int32_T Memory_PreviousInput;        /* '<S26>/Memory' */
  struct {
    uint_T is_c6_CmeFilterMgm:3;       /* '<S24>/TrigCL' */
    uint_T is_c5_CmeFilterMgm:2;       /* '<S47>/Calc_flgCmeDrPMaxRSat' */
    uint_T is_c4_CmeFilterMgm:2;       /* '<S29>/Lim_AfterChange' */
    uint_T is_c3_CmeFilterMgm:2;       /* '<S28>/GasResp_calc' */
    uint_T is_c1_CmeFilterMgm:2;       /* '<S24>/TrigResFilter' */
    uint_T is_c10_CmeFilterMgm:2;      /* '<S24>/CmeDriverI_Offset' */
    uint_T is_active_c5_CmeFilterMgm:1;/* '<S47>/Calc_flgCmeDrPMaxRSat' */
    uint_T is_active_c4_CmeFilterMgm:1;/* '<S29>/Lim_AfterChange' */
    uint_T is_active_c3_CmeFilterMgm:1;/* '<S28>/GasResp_calc' */
    uint_T is_active_c1_CmeFilterMgm:1;/* '<S24>/TrigResFilter' */
    uint_T is_active_c6_CmeFilterMgm:1;/* '<S24>/TrigCL' */
    uint_T is_active_c10_CmeFilterMgm:1;/* '<S24>/CmeDriverI_Offset' */
  } bitsForTID0;

  int16_T UnitDelay2_DSTATE;           /* '<S27>/Unit Delay2' */
  int16_T UnitDelay1_DSTATE;           /* '<S28>/Unit Delay1' */
  int16_T UnitDelay1_DSTATE_m;         /* '<S25>/Unit Delay1' */
  uint16_T UnitDelay3_DSTATE;          /* '<S42>/Unit Delay3' */
  uint16_T UnitDelay1_DSTATE_mo;       /* '<S42>/Unit Delay1' */
  int16_T Memory_PreviousInput_j;      /* '<S47>/Memory' */
  int16_T Memory2_PreviousInput;       /* '<S29>/Memory2' */
  uint8_T UnitDelay_DSTATE;            /* '<S25>/Unit Delay' */
  uint8_T cntlgc;                      /* '<S29>/Lim_AfterChange' */
  uint8_T cntgr;                       /* '<S28>/GasResp_calc' */
  uint8_T cntDuChange;                 /* '<S24>/TrigCL' */
  uint8_T oldGearPosClu;               /* '<S24>/TrigCL' */
  uint8_T flgQShiftExit;               /* '<S24>/TrigCL' */
  uint8_T hDCmeDriverIdx;              /* '<S8>/buffCmeDriver' */
  uint8_T init;                        /* '<S8>/buffCmeDriver' */
  uint8_T tDCmeDriverIdx;              /* '<S8>/buffCmeDriver' */
} D_Work_CmeFilterMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
  ZCSigState trig_to_fc_Trig_ZCE;      /* '<S1>/trig_to_fc' */
} PrevZCSigStates_CmeFilterMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PowerOff;                 /* '<Root>/ev_PowerOff' */
} ExternalInputs_CmeFilterMgm;

/* Block signals (default storage) */
extern BlockIO_CmeFilterMgm CmeFilterMgm_B;

/* Block states (default storage) */
extern D_Work_CmeFilterMgm CmeFilterMgm_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_CmeFilterMgm CmeFilterMgm_U;

/* Model entry point functions */
extern void CmeFilterMgm_initialize(void);
extern void CmeFilterMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('CmeFilterMgm_gen/CmeFilterMgm')    - opens subsystem CmeFilterMgm_gen/CmeFilterMgm
 * hilite_system('CmeFilterMgm_gen/CmeFilterMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CmeFilterMgm_gen'
 * '<S1>'   : 'CmeFilterMgm_gen/CmeFilterMgm'
 * '<S2>'   : 'CmeFilterMgm_gen/CmeFilterMgm/Init'
 * '<S3>'   : 'CmeFilterMgm_gen/CmeFilterMgm/Off'
 * '<S4>'   : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms'
 * '<S5>'   : 'CmeFilterMgm_gen/CmeFilterMgm/trig_to_fc'
 * '<S6>'   : 'CmeFilterMgm_gen/CmeFilterMgm/trig_to_fc1'
 * '<S7>'   : 'CmeFilterMgm_gen/CmeFilterMgm/trig_to_fc2'
 * '<S8>'   : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Calc_DeltaCmeDriver'
 * '<S9>'   : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/CmeDriver_Filter_Parameter'
 * '<S10>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation'
 * '<S11>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim'
 * '<S12>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation'
 * '<S13>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Calc_DeltaCmeDriver/buffCmeDriver'
 * '<S14>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/CmeDriver_Filter_Parameter/PreLookUpIdSearch_U16'
 * '<S15>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Assign_KFiltCmeDriver'
 * '<S16>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP'
 * '<S17>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Reset_KFiltCmeDriverP'
 * '<S18>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP/Add_Filt_Gain'
 * '<S19>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP/Filt_Neutral'
 * '<S20>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP/LookUp_IR_U8_1'
 * '<S21>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP/LookUp_U8_S16_down'
 * '<S22>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP/LookUp_U8_S16_up'
 * '<S23>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/FiltGain_Calculation/Calc_KFiltCmeDriverP/Add_Filt_Gain/Compare To Zero'
 * '<S24>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector'
 * '<S25>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_Filter'
 * '<S26>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverP_Filter'
 * '<S27>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverP_Rate'
 * '<S28>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain'
 * '<S29>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector'
 * '<S30>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/CmeDriverI_Offset'
 * '<S31>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/Compare To Zero1'
 * '<S32>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigCL'
 * '<S33>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigResFilter'
 * '<S34>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_Filter/FOF_Reset_S16_FXP1'
 * '<S35>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_Filter/FOF_Reset_S16_FXP1/Data Type Conversion Inherited1'
 * '<S36>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverP_Filter/FOF_Reset_S16_FXP'
 * '<S37>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverP_Filter/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S38>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverP_Rate/RateLimiter_S32'
 * '<S39>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverP_Rate/RateLimiter_S32/Data Type Conversion Inherited1'
 * '<S40>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/GasResp_calc'
 * '<S41>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/RateLimiter_S1'
 * '<S42>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/min'
 * '<S43>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/RateLimiter_S1/Data Type Conversion Inherited1'
 * '<S44>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/Lim_AfterChange'
 * '<S45>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/LookUp_IR_S16_1'
 * '<S46>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S47>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc'
 * '<S48>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation/LookUp_S16_S16_1'
 * '<S49>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/Calc_flgCmeDrPMaxRSat'
 * '<S50>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/LookUp_IR_S16_3'
 * '<S51>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/LookUp_IR_S16_3/Data Type Conversion Inherited3'
 * '<S52>'  : 'CmeFilterMgm_gen/CmeFilterMgm/T10ms/RateLim_Calculation/LookUp_S16_S16_1/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': CmeFilterMgm
 */
#endif                                 /* RTW_HEADER_CmeFilterMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
