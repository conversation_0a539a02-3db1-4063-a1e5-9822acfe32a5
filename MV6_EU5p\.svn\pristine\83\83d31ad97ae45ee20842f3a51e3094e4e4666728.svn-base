/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              HeatGripDriveMgm.c
 *
 * Author(s): Lana L.
 *
 *
 * Implementation notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "HeatGripDriveMgm.h"
#include "activeDiag.h"
#include "digio.h"
#include "analogin.h"
#include "canmgm.h"

#ifdef _BUILD_HEATGRIP_DRIVEMGM_

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint16_T HeatGripOut;
uint8_T  HeatedGripSt;
uint8_T  EnHeatGripMaxLevel;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
static uint8_T heatGripCfg;

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FuelDriveMgm_Init - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void HeatGripDriveMgm_Init (void)
{
    HeatGripOut = 0;
    HeatedGripSt = 0;
    EnHeatGripMaxLevel = ENHGMAXLEVEL;
    SYS_OutPinConfig(OTE_Canister, CANISTER_PWM_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(CANISTER_PWM, CANISTER_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, (0*HEAT_GRIP_DUTY_SCALE), HEAT_GRIP_PERIOD, 0);
    PIO_PwmOutEnable(CANISTER_PWM);

    /* Inizializzazione avvenuta. */
    heatGripCfg = HEAT_GRIP_INIT_DONE;
}

/*--------------------------------------------------------------------------*
 * HeatGripDrive_T5m - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_T HeatGripDrive_T100m (void)
{
    int16_T ret;
    uint8_T tmpDisHeat;
    uint8_T heat_grip_index;
    uint16_T heat_grip_duty;
    static uint8_T tmpHysDisHeat = 1;
    static uint8_T cntHGActOrWait = 0;
    static uint8_T stHGSelf = 0;
    
    if (heatGripCfg == HEAT_GRIP_INIT_DONE)
    {
        ret = NO_ERROR;
        
        #ifdef HEATGRIP_DEBUG
        HeatGripOut = STUBFLGHEATGRIP;
        PIO_PwmOutSetDutyCicle(CANISTER_PWM, HeatGripOut);
        if (HeatGripOut != 0)
        {
            SetEv_FrontCnt_spiTLE6244X (NUMPINOUT_TLE_CANISTER, 1);
        }
        else
        {
            SetEv_FrontCnt_spiTLE6244X (NUMPINOUT_TLE_CANISTER, 0);
        }
        #else
        
        /* Disabilitazione per soglia giri */
        if (HeatedGripSt != 0)
        {
            if (Rpm < (DISHEATGRIPRPM + (HYSDISHEATGRIPRPM * tmpHysDisHeat)))
            {
                tmpDisHeat = 1;
                tmpHysDisHeat = 1;
            }
            else
            {
                tmpDisHeat = 0;
                tmpHysDisHeat = 0;
            }
        }
        else
        {
            /* Condizioni per test al PowerOn */
            if (EndStartFlg != 0)
            {
                tmpDisHeat = 0;
            }
            else
            {
                tmpDisHeat = 1;
            }
            tmpHysDisHeat = 0;
        }
        /* Condizioni di abilitazione */
        if ((KeySignal == 1) && (GetFilterPRPinStatus() != 0) && ((tmpDisHeat == 0) /* || (FlgIoli.BF.HeatedGripCtrl == BUSY)*/))
        {
            if (HeatedGripSt == 0)
            {
                if (stHGSelf == 0)
                {
                    if (TIM1SELFHG == 0) /* Test disable, HeatedGrip Enable. */
                    {
                        stHGSelf = 3;
                    }
                    else if (TIM1SELFHG == 255) /* Test disable, HeatedGrip Disable. */
                    {
                        stHGSelf = 2;
                    }
                    else if (cntHGActOrWait >= TIM1SELFHG)
                    {
                        stHGSelf = 1;
                        cntHGActOrWait = TIM2SELFHG;
                    }
                    else
                    {
                        cntHGActOrWait++;
                    }
                    
                    heat_grip_duty = (0 * HEAT_GRIP_DUTY_SCALE);
                }
                else if (stHGSelf == 1)
                {
                    if (cntHGActOrWait >= (2 * TIM2SELFHG))
                    {
                        stHGSelf = 2; /* Test fail. */
                    }
                    else if (cntHGActOrWait == 0)
                    {
                        stHGSelf = 3; /* Test pass. */
                    }
                    else
                    {
                        if (PtFault[DIAG_HEATEDGRIP] == OPEN_CIRCUIT)
                        {
                            cntHGActOrWait++;
                        }
                        else
                        {
                            cntHGActOrWait--;
                        }
                    }
                    heat_grip_duty = (0 * HEAT_GRIP_DUTY_SCALE);
                }
                else if (stHGSelf == 2)
                {
                    heat_grip_duty = (0 * HEAT_GRIP_DUTY_SCALE);
                }
                else
                {
                    HeatedGripSt = 1;
                    heat_grip_duty = (0 * HEAT_GRIP_DUTY_SCALE);
                }
                
            }
            else
            {
                if (GripHeatLevelCAN >= HEAT_GRIP_LEVELS)
                {
                    heat_grip_index = (HEAT_GRIP_LEVELS-1);
                }
                else
                {
                    heat_grip_index = GripHeatLevelCAN;
                }

                heat_grip_duty = VTHEATGRIPDUTY[heat_grip_index];
            }
            
            if (heat_grip_duty != HeatGripOut)
            {               
                HeatGripOut = heat_grip_duty;
                PIO_PwmOutSetDutyCicle(CANISTER_PWM, HeatGripOut);
                if (HeatGripOut != 0)
                {
                    SetEv_FrontCnt_spiTLE6244X (NUMPINOUT_TLE_CANISTER, 1);
                }
                else
                {
                    SetEv_FrontCnt_spiTLE6244X (NUMPINOUT_TLE_CANISTER, 0);
                }
            }
            else
            {
                /* None */
            }
        }
        else
        {
            if (stHGSelf < 2)
            {
                stHGSelf = 0;
            }
            else
            {
                /* */
            }
            cntHGActOrWait = 0;
            HeatGripOut = (0 * HEAT_GRIP_DUTY_SCALE);
            PIO_PwmOutSetDutyCicle(CANISTER_PWM, HeatGripOut);
            SetEv_FrontCnt_spiTLE6244X (NUMPINOUT_TLE_CANISTER, 0);
        }
        #endif
    }
    else
    {
        ret = PERIPHERAL_NOT_CONFIGURED;
    }
    return ret;
}

 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/

#endif

/****************************************************************************
 ****************************************************************************/

