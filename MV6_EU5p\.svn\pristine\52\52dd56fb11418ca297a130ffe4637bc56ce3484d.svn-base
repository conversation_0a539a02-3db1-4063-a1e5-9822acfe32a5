/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                          $   */
/* $Revision::                                                                                                             $   */
/* $Date::                                                                                                                 $   */
/* $Author::                                                                                                               $   */
/*******************************************************************************************************************************/
#ifndef _ANTITAMPERING_H_
#define _ANTITAMPERING_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "antitampering_out.h"

/*!
\defgroup PrivateDefines Private Defines
\sgroup
*/
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/

/*!\egroup*/

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern CALQUAL uint8_T  ENVINSTORAGE;
extern CALQUAL uint8_T  TIMATINJSTARTEREN;

/*!
\defgroup PrivateTypedef Private Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/

/*!\egroup*/


/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
 void AntiTampering_InjEn_T100ms(void);

#endif // _ANTITAMPERING_H_
