/*
 * File: AWheelingCtrl_private.h
 *
 * Code generated for Simulink model 'AWheelingCtrl'.
 *
 * Model version                  : 1.1046
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Wed May 07 14:55:42 2014
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_AWheelingCtrl_private_h_
#define RTW_HEADER_AWheelingCtrl_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "vspeed_mgm.h"
#include "canmgm.h"
#include "trq_est.h"
#include "trac_ctrl.h"
#include "diagmgm_out.h"
#include "trq_driver.h"
#include "GearPosClu_mgm.h"
#include "PTrain_Diag.h"
#include "syncmgm.h"
#include "gearshift_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error "Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compiler's limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, which will disable the preprocessor word size checks."
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error "Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Imported (extern) block parameters */
extern uint16_T AWTIMCHANGEW;          /* Variable: AWTIMCHANGEW
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Time to change gain
                                        */
extern int16_T AWCONFDVS;              /* Variable: AWCONFDVS
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Delta speed per confermare wheeling
                                        */
extern int16_T AWCONFDVSFRONT;         /* Variable: AWCONFDVSFRONT
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Delta Front speed per confermare wheeling
                                        */
extern int16_T AWCONFDVSREAR;          /* Variable: AWCONFDVSREAR
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Delta Rear speed per confermare wheeling
                                        */
extern int16_T AWDELLCMEPW;            /* Variable: AWDELLCMEPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Delta CmeEst wheeling
                                        */
extern int16_T AWMAXRETVS;             /* Variable: AWMAXRETVS
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Speed to force exit WheelingSpeed
                                        */
extern int16_T AWMINACCFRSEL;          /* Variable: AWMINACCFRSEL
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Min Acc Front speed per uscire dal wheeling
                                        */
extern int16_T AWMINACCRESEL;          /* Variable: AWMINACCRESEL
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Min Acc Rear speed per elaborare un possibile wheeling
                                        */
extern int16_T AWMINCMEERATE;          /* Variable: AWMINCMEERATE
                                        * Referenced by: '<S94>/AWMINCMEERATE'
                                        * Min Cme to disable Rate
                                        */
extern int16_T AWMINCMEEW;             /* Variable: AWMINCMEEW
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * CmeEst speed wheeling
                                        */
extern int16_T AWMINCMEPW;             /* Variable: AWMINCMEPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * CmeEst pitch wheeling
                                        */
extern int16_T AWMINCMEVW;             /* Variable: AWMINCMEVW
                                        * Referenced by: '<S74>/AwChart'
                                        * CmeEst pitch wheeling
                                        */
extern int16_T AWMINDECFRSEL;          /* Variable: AWMINDECFRSEL
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Min Acc Front speed per elaborare un possibile wheeling
                                        */
extern int16_T AWMINRETVS;             /* Variable: AWMINRETVS
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Speed to force exit WheelingSpeed
                                        */
extern int16_T AWNOCDVS;               /* Variable: AWNOCDVS
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Delta speed per rigettare una elaborazione di wheeling
                                        */
extern int16_T AWNOCDVSFRONT;          /* Variable: AWNOCDVSFRONT
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Delta Front speed per rigettare una elaborazione di wheeling
                                        */
extern int16_T AWNOCDVSREAR;           /* Variable: AWNOCDVSREAR
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Delta Rear speed per rigettare una elaborazione di wheeling
                                        */
extern int16_T AWSPDIFF;               /* Variable: AWSPDIFF
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Diff Speed
                                        */
extern uint16_T AWMINGASRATE;          /* Variable: AWMINGASRATE
                                        * Referenced by: '<S94>/AWMINGASRATE'
                                        * Min Gas to disable Rate
                                        */
extern int16_T AWDBXNCMI;              /* Variable: AWDBXNCMI
                                        * Referenced by: '<S48>/Chart'
                                        * DB cmi
                                        */
extern int16_T AWRATEIMAXAW;           /* Variable: AWRATEIMAXAW
                                        * Referenced by: '<S94>/AWRATEIMAXAW'
                                        * Max Rate I AW Control
                                        */
extern int16_T AWRATEIMAXPST;          /* Variable: AWRATEIMAXPST
                                        * Referenced by: '<S94>/AWRATEIMAXPST'
                                        * Max Rate I Post AW Control
                                        */
extern int16_T AWRATEPMAXAW;           /* Variable: AWRATEPMAXAW
                                        * Referenced by: '<S94>/AWRATEPMAXAW'
                                        * Max Rate P AW Control
                                        */
extern int16_T AWRATEPMAXCTRL;         /* Variable: AWRATEPMAXCTRL
                                        * Referenced by:
                                        *   '<S94>/AWRATEPMAXCTRL'
                                        *   '<S94>/AWRATEPMAXCTRL1'
                                        * Max Rate in control
                                        */
extern int16_T AWRATEPMAXPST;          /* Variable: AWRATEPMAXPST
                                        * Referenced by: '<S94>/AWRATEPMAXPST'
                                        * Max Rate P Post AW Control
                                        */
extern uint16_T AWTHWHEELING0;         /* Variable: AWTHWHEELING0
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Threshold 0 speed wheeling
                                        */
extern uint16_T AWTHWHEELING1;         /* Variable: AWTHWHEELING1
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Threshold 1 speed wheeling
                                        */
extern uint16_T AWTHWHEELING2;         /* Variable: AWTHWHEELING2
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Threshold 2 speed wheeling
                                        */
extern int16_T AWALFABAR;              /* Variable: AWALFABAR
                                        * Referenced by: '<S75>/AWALFABAR'
                                        * Baricenter angle
                                        */
extern int16_T AWDCTRL;                /* Variable: AWDCTRL
                                        * Referenced by: '<S74>/AwChart'
                                        * Delta Aw threshold
                                        */
extern int16_T AWDELNPW;               /* Variable: AWDELNPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Delta Pitch
                                        */
extern int16_T AWDELPW;                /* Variable: AWDELPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Delta Pitch
                                        */
extern int16_T AWFINTMAXAX;            /* Variable: AWFINTMAXAX
                                        * Referenced by: '<S67>/Upper Limit'
                                        * Dynamics of Filter AX
                                        */
extern int16_T AWFINTMINAX;            /* Variable: AWFINTMINAX
                                        * Referenced by: '<S67>/Lower Limit'
                                        * Dynamics of Filter AX
                                        */
extern int16_T AWHG0;                  /* Variable: AWHG0
                                        * Referenced by: '<S76>/AWHG0'
                                        * hG0
                                        */
extern int16_T AWRATEMAXAX;            /* Variable: AWRATEMAXAX
                                        * Referenced by: '<S65>/AWRATEMAXAX'
                                        * Dynamics of Rate AX
                                        */
extern int16_T AWRATEMINAX;            /* Variable: AWRATEMINAX
                                        * Referenced by: '<S65>/AWRATEMINAX'
                                        * Dynamics of Rate AX
                                        */
extern int16_T AWRCPX;                 /* Variable: AWRCPX
                                        * Referenced by: '<S77>/AWRCPX'
                                        * RCPx
                                        */
extern int16_T AWRCPZ;                 /* Variable: AWRCPZ
                                        * Referenced by: '<S77>/AWRCPZ'
                                        * RCPz
                                        */
extern int16_T AWSADX;                 /* Variable: AWSADX
                                        * Referenced by: '<S77>/AWSADX'
                                        * AWSADX
                                        */
extern int16_T AWSADZ;                 /* Variable: AWSADZ
                                        * Referenced by: '<S77>/AWSADZ'
                                        * AWSADZ
                                        */
extern int16_T AWSPREAR;               /* Variable: AWSPREAR
                                        * Referenced by: '<S76>/AWSPREAR'
                                        * SPr
                                        */
extern int16_T AWTHCTRL;               /* Variable: AWTHCTRL
                                        * Referenced by: '<S74>/AwChart'
                                        * Aw threshold
                                        */
extern int16_T BKDAW[7];               /* Variable: BKDAW
                                        * Referenced by: '<S48>/BKDAW'
                                        * AW breakpoint observer
                                        */
extern int16_T BKDPITCHAW[5];          /* Variable: BKDPITCHAW
                                        * Referenced by: '<S48>/BKDPITCHAW'
                                        * dP breakpoint observer
                                        */
extern uint16_T AWKFILTAX;             /* Variable: AWKFILTAX
                                        * Referenced by: '<S57>/Schedule_Filt_Abset'
                                        * KFilt Ax
                                        */
extern uint16_T AWKFILTWX;             /* Variable: AWKFILTWX
                                        * Referenced by: '<S57>/Schedule_Filt_Abset'
                                        * KFilt Wx
                                        */
extern uint8_T AWACCFRSELECT;          /* Variable: AWACCFRSELECT
                                        * Referenced by: '<S46>/AWACCFRSELECT'
                                        * Select source front of sample for acceleration
                                        */
extern uint8_T AWACCRESELECT;          /* Variable: AWACCRESELECT
                                        * Referenced by: '<S46>/AWACCRESELECT'
                                        * Select source rear of sample for acceleration
                                        */
extern uint8_T AWACCSPEEDFRSEL;        /* Variable: AWACCSPEEDFRSEL
                                        * Referenced by: '<S46>/AWACCSPEEDFRSEL'
                                        * Select source front of sample for acceleration or speed
                                        */
extern uint8_T AWACCSPEEDRESEL;        /* Variable: AWACCSPEEDRESEL
                                        * Referenced by: '<S46>/AWACCSPEEDRESEL'
                                        * Select source rear of sample for acceleration or speed
                                        */
extern uint8_T AWENCTRL;               /* Variable: AWENCTRL
                                        * Referenced by: '<S48>/AWENCTRL'
                                        * Enable AW Control
                                        */
extern uint8_T AWENPREINC;             /* Variable: AWENPREINC
                                        * Referenced by: '<S48>/Chart'
                                        * Select Pre Inc Aw and Pitch
                                        */
extern uint8_T AWMEANSPEEDFRSEL;       /* Variable: AWMEANSPEEDFRSEL
                                        * Referenced by: '<S46>/AWMEANSPEEDFRSEL'
                                        * Select mean deep front of sample for acceleration or speed
                                        */
extern uint8_T AWMEANSPEEDRESEL;       /* Variable: AWMEANSPEEDRESEL
                                        * Referenced by: '<S46>/AWMEANSPEEDRESEL'
                                        * Select mean deep rear of sample for acceleration or speed
                                        */
extern uint8_T AWMSTDIS;               /* Variable: AWMSTDIS
                                        * Referenced by: '<S38>/AwEnableFunction'
                                        * Master Anti Wheeling strategy disable
                                        */
extern uint8_T AWSELECT;               /* Variable: AWSELECT
                                        * Referenced by: '<S74>/AWSELECT'
                                        * Select Aw channel
                                        */
extern uint8_T AWSPEEDRESEL;           /* Variable: AWSPEEDRESEL
                                        * Referenced by: '<S46>/AWSPEEDRESEL'
                                        * Selector RearSpeed
                                        */
extern uint8_T AWTIMCPW;               /* Variable: AWTIMCPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Tempo per accettare un inizio pitch wheeling
                                        */
extern uint8_T AWTIMEXITSW;            /* Variable: AWTIMEXITSW
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Time to force exit WheelingSpeed
                                        */
extern uint8_T AWTIMFPW;               /* Variable: AWTIMFPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Tempo per accettare una fine pitch wheeling
                                        */
extern uint8_T AWTIMNOSW;              /* Variable: AWTIMNOSW
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Tempo per rigettare una elaborazione di wheeling
                                        */
extern uint8_T AWTIMOUTPW;             /* Variable: AWTIMOUTPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Timeout pitch relase
                                        */
extern uint8_T AWTIMRETPW;             /* Variable: AWTIMRETPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Tempo per uscire da un wheeling
                                        */
extern uint8_T AWTIMRETSW;             /* Variable: AWTIMRETSW
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Tempo per uscire da un wheeling
                                        */
extern uint8_T AWTIMRETVW;             /* Variable: AWTIMRETVW
                                        * Referenced by: '<S74>/AwChart'
                                        * Tempo per rigettare una elaborazione di wheeling
                                        */
extern uint8_T AWTIMSPEEDW;            /* Variable: AWTIMSPEEDW
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Tempo per confermare un wheeling
                                        */
extern uint8_T AWTIMSWPERM;            /* Variable: AWTIMSWPERM
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Tempo per uscire da un wheeling
                                        */
extern uint8_T AWTIMVW;                /* Variable: AWTIMVW
                                        * Referenced by: '<S74>/AwChart'
                                        * Tempo per validare una elaborazione di wheeling
                                        */
extern uint8_T AWTIMWAITPW;            /* Variable: AWTIMWAITPW
                                        * Referenced by: '<S59>/PitchChart1'
                                        * Tempo per confermare una elaborazione di wheeling
                                        */
extern uint8_T AWTIMWAITSW;            /* Variable: AWTIMWAITSW
                                        * Referenced by: '<S46>/FindWheeling_Speed'
                                        * Tempo per confermare una elaborazione di wheeling
                                        */
extern uint8_T AWPWGAIN0;              /* Variable: AWPWGAIN0
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain engine threshold 0 wheeling
                                        */
extern uint8_T AWPWGAIN1;              /* Variable: AWPWGAIN1
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain engine threshold 0 wheeling
                                        */
extern uint8_T AWPWGAIN2;              /* Variable: AWPWGAIN2
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain pitch threshold 0 wheeling
                                        */
extern uint8_T AWSWGAIN0;              /* Variable: AWSWGAIN0
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain speed threshold 0 wheeling
                                        */
extern uint8_T AWSWGAIN1;              /* Variable: AWSWGAIN1
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain speed threshold 0 wheeling
                                        */
extern uint8_T AWSWGAIN2;              /* Variable: AWSWGAIN2
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain speed threshold 0 wheeling
                                        */
extern uint8_T AWVWGAIN0;              /* Variable: AWVWGAIN0
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain vehicle threshold 0 wheeling
                                        */
extern uint8_T AWVWGAIN1;              /* Variable: AWVWGAIN1
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain vehicle threshold 1 wheeling
                                        */
extern uint8_T AWVWGAIN2;              /* Variable: AWVWGAIN2
                                        * Referenced by: '<S45>/AW_Find_Chart'
                                        * Gain vehicle threshold 1 wheeling
                                        */
extern uint8_T TBAWGAINCMII[35];       /* Variable: TBAWGAINCMII
                                        * Referenced by: '<S48>/TBAWGAINCMII'
                                        * Gain of CmiTracI
                                        */
extern uint8_T TBAWGAINCMIP[35];       /* Variable: TBAWGAINCMIP
                                        * Referenced by: '<S48>/TBAWGAINCMIP'
                                        * Gain of CmiTracP
                                        */
extern void AWheelingCtrl_Filt_Abset(int16_T rtu_Abset, uint8_T rtu_disRate,
  uint16_T rtu_KFilt, uint8_T rtu_absetSelect, int16_T *rtd_AxCANFilt, int16_T
  *rtd_AyCANFilt, int16_T *rtd_AzCANFilt, int16_T *rtd_PitchCANFilt, int16_T
  *rtd_RollCANFilt, int16_T *rtd_WxCANFilt, int16_T *rtd_WyCANFilt, int16_T
  *rtd_WzCANFilt);
extern void AWheelingCtrl_Sin_Cos_090(int16_T rtu_Alfa, uint8_T rtu_sc090Select,
  rtB_Sin_Cos_090_AWheelingCtrl *localB);
extern void AWheelingCtrl_NoCtrl(void);
extern void AWheelingCtrl_T10ms_Init(void);
extern void AWheelingCtrl_T10ms(void);
extern void AWheelingCtrl_Init(void);
extern void AWheelingCtrl_NoSync(void);

#endif                                 /* RTW_HEADER_AWheelingCtrl_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
