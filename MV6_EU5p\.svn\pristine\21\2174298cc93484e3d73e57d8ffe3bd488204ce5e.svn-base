/*
 * File: ExhValMgm_data.c
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1587
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 18 16:31:59 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_EXHVALMGM_

#include "ExhValMgm.h"
#include "ExhValMgm_private.h"

/* Invariant block signals (default storage) */
const ConstB_ExhValMgm_T ExhValMgm_ConstB = {
  0U                                   /* '<S139>/Constant' */
};

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
