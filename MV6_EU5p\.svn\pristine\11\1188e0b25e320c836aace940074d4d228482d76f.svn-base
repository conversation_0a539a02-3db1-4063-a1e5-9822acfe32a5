#ifndef _INJ_H_
#define _INJ_H_

#define INJ_CHANNEL_NOT_LOCKED  0
#define INJ_CHANNEL_LOCKED      1

int16_t INJ_Config        (t_INJ_CHANNELS chan, t_polarity activeLevel);

int16_t INJ_Enable        (t_INJ_CHANNELS chan, t_State enablingStatus);

int16_t INJ_Set           (t_INJ_CHANNELS chan, 
                           uint32_t       *start, 
                           uint32_t       *stop, 
                           uint8_t        numOfInj,
                           uint32_t       saturation, 
                           t_CompareMode  saturationMode,
                           t_PulseMode    pulseMode,
                           t_PriorityMode priority);
                           
int16_t INJ_Correct       (t_INJ_CHANNELS chan, 
                           uint32_t       *start, 
                           uint32_t       *stop, 
                           uint8_t        numOfInj);

int16_t INJ_EnableEx  (t_INJ_CHANNELS chan,
                       TaskType       taskID, 
                       t_EnableFlags  enablingFlags);
                           
                           
void    INJ_ExOpen        (t_INJ_CHANNELS chan);

void    INJ_ExClose       (t_INJ_CHANNELS chan);

int16_t INJ_GetCurrTransType(t_INJ_CHANNELS chan, uint32_t *type);

int16_t INJ_GetCurrTransTime(t_INJ_CHANNELS chan, uint32_t *time);

int16_t INJ_GetCurrTransAngle(t_INJ_CHANNELS chan, uint32_t *angle);

int16_t INJ_GetStatus(t_INJ_CHANNELS chan, uint16_t *status);

#endif /* _INJ_H_ */


