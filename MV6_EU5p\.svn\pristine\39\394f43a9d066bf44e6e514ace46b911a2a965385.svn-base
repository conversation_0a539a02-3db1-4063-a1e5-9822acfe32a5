/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/MV5/tree/EEPCOM/engflag_eep.c            $   */
/* $ Description:                                                                                                */
/* $Revision:: 3483   $                                                                                          */
/* $Date:: 2011-03-23 09:06:06 +0100 (mer, 23 mar 2011)   $                                                      */
/* $Author:: GirasoleG               $                                                                       */
/*****************************************************************************************************************/

const int32_T dummyID7n00 = 0;
const int32_T dummyID7n01 = 0;
const uint32_T EECntExVSelf = 0;
const int32_T EEFlgExVPos = 0;
const int32_T EEExVPercMax = (int16_T)(87.5f * 256.0f);
const int32_T EEExVPercMin = (int16_T)(12.5f * 256.0f);
#ifdef _BUILD_TRANSPORT_LOCK_
const uint32_T TempUnlockCntDownEE = 0;
const uint32_T TransportLockEE = 0;  /* Transport Lock Flag (TLF) */
#else
const int32_T dummyID7n06 = 0;
const int32_T dummyID7n07 = 0;
#endif // _BUILD_TRANSPORT_LOCK_

