/**
 ******************************************************************************
 **  Filename:      RpmLimiter.h
 **  Date:          01-Mar-2023
 **
 **  Model Version: 1.2476
 ******************************************************************************
 **/

#ifndef RTW_HEADER_RpmLimiter_h_
#define RTW_HEADER_RpmLimiter_h_
#ifndef RpmLimiter_COMMON_INCLUDES_
# define RpmLimiter_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* RpmLimiter_COMMON_INCLUDES_ */

#include "RpmLimiter_types.h"

/* Includes for objects with custom storage classes. */
#include "rpm_limiter.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKCMIPBBPROT_dim               5U                        /* Referenced by: '<S20>/BKCMIPBBPROT_dim' */
#define BKDELTARPMGAIN_dim             4U                        /* Referenced by: '<S53>/BKDELTARPMGAIN_dim' */
#define BKERRRPMLCTFIDX_dim            4U                        /* Referenced by: '<S32>/BKERRRPMLCTFIDX_dim' */
#define BKGASPOSREC_dim                5U                        /* Referenced by: '<S54>/BKGASPOSREC_dim' */
#define BKRPMLIMERRTRSTOP_dim          8U                        /* Referenced by: '<S10>/BKRPMLIMERRTRSTOP_dim' */
#define BKRPMLIMERR_dim                4U                        /* Referenced by:
                                                                  * '<S34>/BKRPMLIMERR_dim'
                                                                  * '<S35>/BKRPMLIMERR_dim'
                                                                  */
#define CMI_FILTER_LIM                 2U                        /* Referenced by: '<S3>/Calc_Lim' */
#define CMI_MAX                        1072693248                /* Referenced by:
                                                                  * '<Root>/_DataStoreBlk_1'
                                                                  * '<Root>/_DataStoreBlk_20'
                                                                  * '<S2>/fc_Reset'
                                                                  */
#define DISABLED_LIM                   0U                        /* Referenced by:
                                                                  * '<S3>/Calc_Lim'
                                                                  * '<S10>/DISABLED_LIM'
                                                                  */
#define ID_RPM_LIMITER                 20931255U                 /* Referenced by: '<S2>/ID_RPM_LIMITER' */

/* mask */
#define NO_LIM                         1U                        /* Referenced by:
                                                                  * '<S3>/Calc_Lim'
                                                                  * '<S10>/NO_LIM'
                                                                  */
#define PROTECTION_LIM                 3U                        /* Referenced by:
                                                                  * '<S3>/Calc_Lim'
                                                                  * '<S21>/Constant'
                                                                  * '<S32>/Calc_FlgRpmLIReduct'
                                                                  * '<S30>/Constant'
                                                                  */
#define SMOOTH_LIM                     4U                        /* Referenced by: '<S3>/Calc_Lim' */

/* Block signals for system '<S3>/CmiRpmFilter' */
typedef struct {
  boolean_T RelationalOperator;        /* '<S9>/Relational Operator' */
} rtB_CmiRpmFilter_RpmLimiter;

/* Block signals (default storage) */
typedef struct {
  int16_T Switch1;                     /* '<S14>/Switch1' */
  int16_T Switch8;                     /* '<S55>/Switch8' */
  int16_T Switch5;                     /* '<S55>/Switch5' */
  int16_T MinMax3;                     /* '<S14>/MinMax3' */
  uint8_T flg_rpmrec_limit;            /* '<S3>/Calc_Lim' */
  uint8_T LogicalOperator2;            /* '<S13>/Logical Operator2' */
  uint8_T FlgRpmLIReduct_h;            /* '<S32>/Calc_FlgRpmLIReduct' */
  boolean_T RelationalOperator;        /* '<S11>/Relational Operator' */
  rtB_CmiRpmFilter_RpmLimiter CmiRpmFilter;/* '<S3>/CmiRpmFilter' */
} BlockIO_RpmLimiter;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  struct {
    uint_T is_ENABLED:3;               /* '<S3>/Calc_Lim' */
    uint_T is_CONTROL:2;               /* '<S3>/Calc_Lim' */
    uint_T is_RPM_LIMIT_FLAG:2;        /* '<S3>/Calc_Lim' */
    uint_T is_c1_RpmLimiter:2;         /* '<S32>/Calc_FlgRpmLIReduct' */
    uint_T is_active_c3_RpmLimiter:1;  /* '<S3>/Calc_Lim' */
    uint_T is_active_c1_RpmLimiter:1;  /* '<S32>/Calc_FlgRpmLIReduct' */
  } bitsForTID0;

  uint8_T flg_disable_limiter;         /* '<Root>/_DataStoreBlk_24' */
  uint8_T mem_CtfLimiterFlg;           /* '<S1>/Data Store Memory1' */
  uint8_T tim;                         /* '<S32>/Calc_FlgRpmLIReduct' */
} D_Work_RpmLimiter;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState Trig_2_fc1_Trig_ZCE[2];   /* '<S1>/Trig_2_fc1' */
  ZCSigState Trig_2_fc_Trig_ZCE;       /* '<S1>/Trig_2_fc' */
} PrevZCSigStates_RpmLimiter;

/* Invariant block signals (default storage) */
typedef struct {
  const int16_T DataTypeConversion[3]; /* '<S54>/Data Type Conversion' */
  const int16_T DataTypeConversion2[7];/* '<S54>/Data Type Conversion2' */
  const int16_T DataTypeConversion_k[3];/* '<S32>/Data Type Conversion' */
} ConstBlockIO_RpmLimiter;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
} ExternalInputs_RpmLimiter;

/* Block signals (default storage) */
extern BlockIO_RpmLimiter RpmLimiter_B;

/* Block states (default storage) */
extern D_Work_RpmLimiter RpmLimiter_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_RpmLimiter RpmLimiter_U;
extern const ConstBlockIO_RpmLimiter RpmLimiter_ConstB;/* constant block i/o */

/* Model entry point functions */
extern void RpmLimiter_initialize(void);
extern void RpmLimiter_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('rpmlimiter_gen/RpmLimiter')    - opens subsystem rpmlimiter_gen/RpmLimiter
 * hilite_system('rpmlimiter_gen/RpmLimiter/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'rpmlimiter_gen'
 * '<S1>'   : 'rpmlimiter_gen/RpmLimiter'
 * '<S2>'   : 'rpmlimiter_gen/RpmLimiter/Reset'
 * '<S3>'   : 'rpmlimiter_gen/RpmLimiter/T10ms'
 * '<S4>'   : 'rpmlimiter_gen/RpmLimiter/Trig_2_fc'
 * '<S5>'   : 'rpmlimiter_gen/RpmLimiter/Trig_2_fc1'
 * '<S6>'   : 'rpmlimiter_gen/RpmLimiter/Reset/fc_Reset'
 * '<S7>'   : 'rpmlimiter_gen/RpmLimiter/T10ms/Calc_Lim'
 * '<S8>'   : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiNoLimit'
 * '<S9>'   : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiRpmFilter'
 * '<S10>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation'
 * '<S11>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSmoothing'
 * '<S12>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator'
 * '<S13>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_flgveh'
 * '<S14>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr'
 * '<S15>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiRpmFilter/Compare To Zero'
 * '<S16>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiRpmFilter/FOF_Reset_S16_FXP'
 * '<S17>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiRpmFilter/GenAbs'
 * '<S18>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiRpmFilter/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S19>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiRpmFilter/GenAbs/Data Type Conversion Inherited'
 * '<S20>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/BlowBy_Protection'
 * '<S21>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/Compare To Constant'
 * '<S22>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/LookUp_S16_S16'
 * '<S23>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/SatP'
 * '<S24>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/Saturation Dynamic'
 * '<S25>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/Saturation Dynamic1'
 * '<S26>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/Saturation Dynamic2'
 * '<S27>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/BlowBy_Protection/LookUp_S16_S16'
 * '<S28>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/BlowBy_Protection/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S29>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S30>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/CmiSaturation/SatP/Compare To Constant'
 * '<S31>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/CmiRpmNoLimit_calc'
 * '<S32>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Ctf_Limiter_Idx'
 * '<S33>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Flag_calc'
 * '<S34>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Integral_term_calc'
 * '<S35>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc'
 * '<S36>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/CmiRpmNoLimit_calc/Compare To Zero'
 * '<S37>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Ctf_Limiter_Idx/Calc_FlgRpmLIReduct'
 * '<S38>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Ctf_Limiter_Idx/Look2D_U16_S16_S16_1'
 * '<S39>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Ctf_Limiter_Idx/Look2D_U16_S16_S16_1/Data Type Conversion Inherited1'
 * '<S40>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Integral_term_calc/LookUp_U16_S16_1'
 * '<S41>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Integral_term_calc/LookUp_U16_S16_1/Data Type Conversion Inherited3'
 * '<S42>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc/LookUp_U16_S16'
 * '<S43>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc/Relase_Prop'
 * '<S44>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc/LookUp_U16_S16/Data Type Conversion Inherited3'
 * '<S45>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc/Relase_Prop/RateLimiter_S16'
 * '<S46>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc/Relase_Prop/Trigger'
 * '<S47>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/PI_regulator/Proportional_term_calc/Relase_Prop/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S48>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_flgveh/Compare To Zero1'
 * '<S49>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_flgveh/Compare To Zero3'
 * '<S50>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_flgveh/Compare To Zero4'
 * '<S51>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_flgveh/Compare To Zero5'
 * '<S52>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_flgveh/Compare To Zero6'
 * '<S53>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_DeltaRpm'
 * '<S54>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_RpmMaxCorr'
 * '<S55>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_rpmlimerrctf'
 * '<S56>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_DeltaRpm/LookUp_U16_S16_1'
 * '<S57>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_DeltaRpm/LookUp_U16_S16_1/Data Type Conversion Inherited3'
 * '<S58>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_RpmMaxCorr/Look2D_U16_S16_S16_1'
 * '<S59>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_RpmMaxCorr/LookUp_U16_U16'
 * '<S60>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_RpmMaxCorr/Look2D_U16_S16_S16_1/Data Type Conversion Inherited1'
 * '<S61>'  : 'rpmlimiter_gen/RpmLimiter/T10ms/calc_rpmmaxcorr/Calc_RpmMaxCorr/LookUp_U16_U16/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': RpmLimiter
 */
#endif                                 /* RTW_HEADER_RpmLimiter_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
