/*
 * File: PAtmModel.h
 *
 * Code generated for Simulink model 'PAtmModel'.
 *
 * Model version                  : 1.773
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 24 16:04:22 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PAtmModel_h_
#define RTW_HEADER_PAtmModel_h_
#ifndef PAtmModel_COMMON_INCLUDES_
# define PAtmModel_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "diagmgm_out.h"
#include "mathlib.h"
#endif                                 /* PAtmModel_COMMON_INCLUDES_ */

#include "PAtmModel_types.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "sync.h"
#include "recmgm.h"
#include "patm_model.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKRPMANGPATM_dim               5U                        /* Referenced by: '<S19>/BKRPMANGPATM_dim' */
#define BUFFER                         1U                        /* Referenced by: '<S4>/PowerOn_Chart' */
#define FAILED                         3U                        /* Referenced by: '<S4>/PowerOn_Chart' */
#define RUNNINGBUFFER                  1U                        /* Referenced by: '<S5>/Running_Chart' */
#define RUNNINGFAILED                  3U                        /* Referenced by: '<S5>/Running_Chart' */
#define RUNNINGSTOP                    2U                        /* Referenced by: '<S5>/Running_Chart' */
#define RUNNINGWAIT                    0U                        /* Referenced by: '<S5>/Running_Chart' */
#define SENSOR                         10U                       /* Referenced by:
                                                                  * '<S4>/PowerOn_Chart'
                                                                  * '<S5>/Running_Chart'
                                                                  */
#define STOP                           2U                        /* Referenced by: '<S4>/PowerOn_Chart' */
#define UNRELIABLE                     4U                        /* Referenced by: '<S4>/PowerOn_Chart' */
#define UNRELIABLERUN                  4U                        /* Referenced by: '<S5>/Running_Chart' */
#define WAIT                           0U                        /* Referenced by: '<S4>/PowerOn_Chart' */

/* Block signals for system '<S5>/AngThrPAtm_Calc' */
typedef struct {
  uint16_T DataTypeConversion2;        /* '<S23>/Data Type Conversion2' */
} rtB_AngThrPAtm_Calc_PAtmModel;

/* Block signals (default storage) */
typedef struct {
  rtB_AngThrPAtm_Calc_PAtmModel AngThrPAtm_Calc;/* '<S5>/AngThrPAtm_Calc' */
} BlockIO_PAtmModel;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  struct {
    uint_T is_RELIABLE:3;              /* '<S4>/PowerOn_Chart' */
    uint_T is_RELIABLE_p:3;            /* '<S5>/Running_Chart' */
    uint_T is_c1_PAtmModel:2;          /* '<S4>/PowerOn_Chart' */
    uint_T is_c2_PAtmModel:2;          /* '<S5>/Running_Chart' */
    uint_T is_active_c1_PAtmModel:1;   /* '<S4>/PowerOn_Chart' */
    uint_T is_active_c2_PAtmModel:1;   /* '<S5>/Running_Chart' */
  } bitsForTID0;

  uint16_T TaskCounter;                /* '<S4>/PowerOn_Chart' */
  uint16_T TaskCounter_m;              /* '<S5>/Running_Chart' */
  uint8_T IdxBuffer;                   /* '<S4>/PowerOn_Chart' */
  uint8_T IdxBuffer_a;                 /* '<S5>/Running_Chart' */
} D_Work_PAtmModel;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState TriggeredSubsystem4_Trig_ZCE;/* '<S1>/Triggered Subsystem4' */
  ZCSigState TriggeredSubsystem3_Trig_ZCE;/* '<S1>/Triggered Subsystem3' */
  ZCSigState TriggeredSubsystem2_Trig_ZCE;/* '<S1>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem1_Trig_ZCE;/* '<S1>/Triggered Subsystem1' */
} PrevZCSigStates_PAtmModel;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_100ms;                    /* '<Root>/ev_100ms' */
  uint8_T ev_5ms;                      /* '<Root>/ev_5ms' */
  uint8_T ev_TDC;                      /* '<Root>/ev_TDC' */
} ExternalInputs_PAtmModel;

/* Block signals (default storage) */
extern BlockIO_PAtmModel PAtmModel_B;

/* Block states (default storage) */
extern D_Work_PAtmModel PAtmModel_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_PAtmModel PAtmModel_U;

/* Model entry point functions */
extern void PAtmModel_initialize(void);
extern void PAtmModel_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('PAtmModel_gen/PAtmModel')    - opens subsystem PAtmModel_gen/PAtmModel
 * hilite_system('PAtmModel_gen/PAtmModel/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PAtmModel_gen'
 * '<S1>'   : 'PAtmModel_gen/PAtmModel'
 * '<S2>'   : 'PAtmModel_gen/PAtmModel/Init'
 * '<S3>'   : 'PAtmModel_gen/PAtmModel/T100ms'
 * '<S4>'   : 'PAtmModel_gen/PAtmModel/T5ms'
 * '<S5>'   : 'PAtmModel_gen/PAtmModel/TDC'
 * '<S6>'   : 'PAtmModel_gen/PAtmModel/Triggered Subsystem1'
 * '<S7>'   : 'PAtmModel_gen/PAtmModel/Triggered Subsystem2'
 * '<S8>'   : 'PAtmModel_gen/PAtmModel/Triggered Subsystem3'
 * '<S9>'   : 'PAtmModel_gen/PAtmModel/Triggered Subsystem4'
 * '<S10>'  : 'PAtmModel_gen/PAtmModel/Init/NO_SENSOR'
 * '<S11>'  : 'PAtmModel_gen/PAtmModel/Init/SENSOR'
 * '<S12>'  : 'PAtmModel_gen/PAtmModel/Init/SENSOR/DiagMgm_RangeCheck_U16'
 * '<S13>'  : 'PAtmModel_gen/PAtmModel/Init/SENSOR/SetDiagState'
 * '<S14>'  : 'PAtmModel_gen/PAtmModel/T100ms/Sensor'
 * '<S15>'  : 'PAtmModel_gen/PAtmModel/T100ms/Sensor/DiagMgm_RangeCheck_U16'
 * '<S16>'  : 'PAtmModel_gen/PAtmModel/T100ms/Sensor/SetDiagState'
 * '<S17>'  : 'PAtmModel_gen/PAtmModel/T5ms/PowerOn_Chart'
 * '<S18>'  : 'PAtmModel_gen/PAtmModel/T5ms/PresAtm_Saturation'
 * '<S19>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc'
 * '<S20>'  : 'PAtmModel_gen/PAtmModel/TDC/PresAtm_Adapt'
 * '<S21>'  : 'PAtmModel_gen/PAtmModel/TDC/PresAtm_Saturation'
 * '<S22>'  : 'PAtmModel_gen/PAtmModel/TDC/Running_Chart'
 * '<S23>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc/AngThrottle_Corr'
 * '<S24>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc/LookUp_IR_U16_max'
 * '<S25>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc/LookUp_IR_U16_min'
 * '<S26>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc/PreLookUpIdSearch_U16'
 * '<S27>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc/LookUp_IR_U16_max/Data Type Conversion Inherited3'
 * '<S28>'  : 'PAtmModel_gen/PAtmModel/TDC/AngThrPAtm_Calc/LookUp_IR_U16_min/Data Type Conversion Inherited3'
 */
#endif                                 /* RTW_HEADER_PAtmModel_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
