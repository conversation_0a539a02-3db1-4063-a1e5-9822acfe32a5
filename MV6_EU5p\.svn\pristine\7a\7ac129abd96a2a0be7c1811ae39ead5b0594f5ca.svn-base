/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/MV1/tree/APPLICATION/IMMO/sr#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1394   $                                                                                          */
/* $Date:: 2009-06-24 17:47:22 +0200 (mer, 24 giu 2009)   $                                                      */
/* $Author:: GelmettiA               $                                                                       */
/*****************************************************************************************************************/




#include "..\..\..\BIOS\SYS\auto\etpu_UART_auto.h"

#include "digitalin.h"
#include "fuel_mgm.h"  
#include <string.h>
#include "diagmgm_out.h"
#include "TransportLock_out.h"
#include "relaymgm.h"
#include "diagcanmgm.h"     // extern uint8_T FlgEOL;
#include "IMMOapp.h"
#include "canmgm.h"

#ifdef   _BUILD_IMMO_
#include "..\..\..\BIOS\LIN\inc\linbase.h"
#include "IMMO_app.h" 
#include "..\..\..\DD\LINMGM\include\immo.h"
#include "..\..\..\DD\LINMGM\include\immo_task.h"
#include "..\..\..\DD\LINMGM\include\ImmoE2mgm.h"

#define   UNIVERSALKEYCOUNTER 0x10

/*
==============GLOBALS==============================================
*/

uint8_t ImmoCtxState = IMMO_IDLE;
uint8_t ImmoCtxError = IMMO_NOERROR;
uint8_t ImmoCtxNRetry = 0;
uint8_t ImmoCtxLastKey[6] = {0, 0, 0, 0, 0, 0};
uint8_t ImmoCtxLedState = IMMO_LED_SIG1;
uint8_t ImmoEOLState;

uint8_t LINrxBuffer[40];
uint8_t txBuffer[8];
uint8_t ImmoRDPhmeas;
uint8_t ImmoWRSmplc;
uint8_t FlgUnivKey;


const uint8_t ImmoReadDelay  [IMMO_MAXRETRY]={ 2,2,4,4,4,6,6,8,8,10}; 

/*These variables are used only for the Storing/Restoring Procedure*/
uint8_t ImmoMainState = IMMO_KEY_IDLE;
extern uint8_t ImmoE2WriteFlag; /* This flag should be defined in the E2 Driver for the IMMO */

uint8_t ImmoE2RamCopyKeyStored[8][6] = 
{  
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 },
    {0, 0, 0, 0, 0, 0 }
};

uint8_t ImmoE2RamCopyKeyNumber = 0;
uint32_t keyOffTime; 
uint32_t keyOnTime; 
uint32_t clock;
uint32_t immo_WDtimer;
uint8_t  ImmoWaitForKeyStoring; 
uint8_T  InjEnableIMMO ;
uint8_t   ImmoKeyReadDone ;
/*
==============EXTERNN==============================================
*/
extern uint8_t ledSig1State;
extern uint8_t ledSig2State;
extern uint8_t ledSig3State;
extern uint8_t ledClk;
//extern uint8_T FlgEOL;


void IMMO_T5ms (void)
{
    static uint8_t ret;
    static int8_t waitCounter=0;
    static int8_t waitEOLCounter=0;
    int16_t pos;
    static uint8_t status;
    int8_t keyCounterValue=0;
    

    /* Automa gestione EOL */
    switch(ImmoEOLState)
    {
        case IMMO_NOEOL:
            if(FlgEOL)
            {
                waitEOLCounter = WAIT5ms * 20;
                ImmoEOLState = IMMO_WAITEOL;
            }
            break;

        case IMMO_WAITEOL:
            if(!FlgEOL)
            {
                ImmoEOLState = IMMO_NOEOL;
            }
            else 
            {
                waitEOLCounter--;
                if (waitEOLCounter > 0)
                {
                    break;
                }
                else
                {
                    ImmoEOLState = IMMO_EOL;
                    /* Inizializzo la procedura immo */
                    ImmoCtxState = IMMO_IDLE;
                }
            }
            break;

        case IMMO_EOL:
            if(!FlgEOL)
            {
                ImmoEOLState = IMMO_NOEOL;
            }
            break;
    }
    
    /* Automa gestione lettura codice chiave */
    switch (ImmoCtxState)
    {
        case IMMO_IDLE :/*wait KeySignal*/ 
            if (KeySignal) 
            {      
                ImmoCtxNRetry=0;
                ImmoCtxError=IMMO_NOERROR;
                ImmoCtxState=IMMO_START;

                /*LED FSM Initialization*/
                ImmoCtxLedState=IMMO_LED_SIG1;
                ledClk      =0;
                ledSig1State=0;
                ledSig2State=0;
                ledSig3State=0;
                ImmoKeyReadDone =0;
                

                 /*clear last key*/
                  memset((uint8_t *)ImmoCtxLastKey,0,6);

                //EtpuUartRxTxChannelModify (ETPU_UART_TX, ETPU_UART_RX, UART_BAUD_RATE_4800, BITS_PER_DATA_WORD_8, UART_TIMEBASE_FREQ);

                /*MM_Emulate*/
                EtpuUartRxTxChannelModify (ETPU_UART_TX, ETPU_UART_RX, SCI_9600_BR, BITS_PER_DATA_WORD_8, UART_TIMEBASE_FREQ); 
                  /*
                  ETPU_UART_TX            tx_channel: engine A
                ETPU_UART_RX            rx_channel: engine A
                 UART_BAUD_RATE_4800    baud_rate
                 BITS_PER_DATA_WORD_8    bits_per_data_word
                UART_TIMEBASE_FREQ        timebase_freq = sysclk/2/prescalers
                */

               LIN_Init();
               //waitCounter=WAIT160ms; /* Wait a stable condition at the startup */
               waitCounter=WAIT80ms;
            }
        break;
                          
        case IMMO_START :
            
            waitCounter--;
            if (waitCounter > 0) break;
                
            if ((ImmoCtxNRetry<IMMO_MAXRETRY)&&(ImmoCtxError!=IMMO_KEYNOTVALID))
            {
                ImmoCtxState=IMMO_WAKEUP;
                LIN_Init();
                //waitCounter=(5*WAIT20ms);
                waitCounter=(WAIT20ms);
                
            }
            else
            { 
                ImmoCtxState=IMMO_BLOCK;
            }
        break;                                        

        case IMMO_WAKEUP :
            waitCounter--;
            if (waitCounter > 0) break;
            
            LIN_Command(WAKE_UP_CMD_S);
            ImmoCtxState=IMMO_RESETXCVR;
            waitCounter=WAIT10ms;     
             
        break;

        case IMMO_RESETXCVR :
            waitCounter--;
            if (waitCounter > 0) break;
            
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_DCON0, 0x20,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_DISXTO;
            waitCounter = WAIT10ms;
        break;
                         
        case IMMO_DISXTO :
            waitCounter--;
            if (waitCounter > 0) break;
            
            ret = LIN_DriverStatus();
            if (ret & LIN_STATUS_PENDING) break;
            //PCF7992_Wr_SCFG( PCF7992_WR_SCFG_DCON0, 0x58,IMMO_API_NO_BLOCKING);
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_DCON0, 0x40,IMMO_API_NO_BLOCKING);
            //ImmoCtxState=IMMO_BR9600;
            ImmoCtxState=IMMO_WRCFG1_1;
            waitCounter = WAIT10ms;
        break;

        
        case IMMO_WRCFG1_1 :
            waitCounter--;
            if (waitCounter > 0) break;
            ret = LIN_DriverStatus();
            if (ret & LIN_STATUS_PENDING)
              {
                  break;
              }
#if 0            
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_DCON1, 0x70,IMMO_API_NO_BLOCKING);
#endif
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_DCON1, 0x60,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_WRCFG1_2;
            waitCounter=WAIT10ms;
        break;


        case IMMO_WRCFG1_2 :
            waitCounter--;
            if (waitCounter > 0) break;
            ret = LIN_DriverStatus();
             if (ret & LIN_STATUS_PENDING)
               {
                   break;
               }
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_COMCON, 0x01,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_WRCFG1_3;
            waitCounter=WAIT10ms;
        break;

        case IMMO_WRCFG1_3 :
            waitCounter--;
            if (waitCounter > 0) break;
            ret = LIN_DriverStatus();
              if (ret & LIN_STATUS_PENDING)
                {
                    break;
                }
            /*Short Circuit of Antenna Terminals against GND or VBAT */
#if 0
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_RCON0, 0x08,IMMO_API_NO_BLOCKING);
#endif            
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_RCON0, 0x18,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_WRCFG2_1;
            waitCounter=WAIT10ms;
        break;

        case IMMO_WRCFG2_1 :
            waitCounter--;
            if (waitCounter > 0) break;
            ret = LIN_DriverStatus();
               if (ret & LIN_STATUS_PENDING)
                 {
                     break;
                 }
               /*Short Circuit of Antenna Terminals against GND or VBAT*/
#if 0
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_RCON1, 0x60,IMMO_API_NO_BLOCKING);
#endif
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_DRVCON, 0x00,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_WRCFG2_2;
            waitCounter=WAIT10ms;
        break;
                         
        case IMMO_WRCFG2_2 :
            waitCounter--;
            if (waitCounter > 0) break;
            ret = LIN_DriverStatus();
                if (ret & LIN_STATUS_PENDING)
                  {
                      break;
                  }
            /*Short Circuit of Antenna Terminals against GND or VBAT */
#if 0
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_TXCON, 0x04,IMMO_API_NO_BLOCKING);
#endif
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_TXCON, 0x03,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_RDCFG;
            waitCounter=WAIT10ms;
        break;

        case IMMO_RDCFG :
            waitCounter--;
            if (waitCounter > 0) break;
            ret = LIN_DriverStatus();
                if (ret & LIN_STATUS_PENDING)
                  {
                      break;
                  }
            /*Short Circuit of Antenna Terminals against GND or VBAT */
#if 0
            PCF7992_Rd_SCFG (PCF7992_RD_SCFG_RCON1,&status,IMMO_API_NO_BLOCKING);
#endif            
            /*20-02-2008 clear status before read*/
            status=0;
            PCF7992_Rd_SCFG (PCF7992_RD_SCFG_DCON1,&status,IMMO_API_NO_BLOCKING);
            waitCounter=WAIT15ms;
            ImmoCtxState=IMMO_RDPHMEAS;
        break;
        
        case IMMO_RDPHMEAS :
             waitCounter--;
             if (waitCounter > 0) break;
             
             ret = LIN_DriverStatus();
                if (ret & LIN_STATUS_PENDING)
                  {
                      break;
                  }     
             /* Read message */
             ret = LIN_GetMsg( PCF7992_ID_COMMAND_TO_READ, &status );
     
             if (status&XCVR_ACTIVE)
             {
                  /*20-02-2008 clear status before read*/
                  status=0;         
                  PCF7992_Rd_SCFG (PCF7992_RD_SCFG_PHMEAS,&status,IMMO_API_NO_BLOCKING);
                  ImmoCtxState=IMMO_WRCFG3;
                  waitCounter=WAIT10ms;
             }
            else 
             {
                  ImmoCtxState=IMMO_START; // To do : Check here the number of Retry 
                  ImmoCtxError=IMMO_XCVR_ERROR;
                  ImmoCtxNRetry +=IMMO_INCR_FOR_XCVR_ERR;/*LG:20071217*/
             }
          
        break;

        case IMMO_WRCFG3 :
            waitCounter--;
            if (waitCounter > 0) break;
             
            ret = LIN_DriverStatus();
                if (ret & LIN_STATUS_PENDING)
                  {
                      break;
                  }
            /* Read message */
            ret = LIN_GetMsg( PCF7992_ID_COMMAND_TO_READ, &ImmoRDPhmeas );

            if ((ImmoRDPhmeas<=IMMOPHMEASMAX)&&(ImmoRDPhmeas>=IMMOPHMEASMIN))
            {
                PCF7992_Wr_SCFG( PCF7992_WR_SCFG_TXCON, 0x04,IMMO_API_NO_BLOCKING);
                ImmoWRSmplc = Immo_SampleTimeCalc(ImmoRDPhmeas);
                ImmoCtxState=IMMO_WRSMPLT;
                waitCounter=WAIT10ms;
            } 
            else 
            { 
                ImmoCtxNRetry +=IMMO_INCR_FOR_ANTENNA_ERR;/*LG:20071217*/
                ImmoCtxError=IMMO_ANTENNA_ERROR;
                ImmoCtxState=IMMO_START;
            } 

        break; 
                         
        case IMMO_WRSMPLT :
            waitCounter--;
            if (waitCounter > 0) break;
            
            ret = LIN_DriverStatus();
                if (ret & LIN_STATUS_PENDING)
                  {
                      break;
                  }
#if 0                
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_SMPLC ,0x3f,IMMO_API_NO_BLOCKING);
#endif
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_SMPLC ,ImmoWRSmplc,IMMO_API_NO_BLOCKING);
            waitCounter=WAIT10ms;
            ImmoCtxState=IMMO_ANTENNAEN;
        break;
                         
        case IMMO_ANTENNAEN :
            waitCounter--;
            if (waitCounter > 0) break;
            
            ret = LIN_DriverStatus();
            if (ret & LIN_STATUS_PENDING)
              {
                  break;
              }            
            PCF7992_Wr_SCFG( PCF7992_WR_SCFG_TXCON, 0x03,IMMO_API_NO_BLOCKING);
            ImmoCtxState=IMMO_WRRDBUF ;
            waitCounter=ImmoReadDelay[ImmoCtxNRetry];
        break;
                         
        case IMMO_WRRDBUF :
            waitCounter--;
            if (waitCounter > 0) break;
            
            ret = LIN_DriverStatus();
            if (ret & LIN_STATUS_PENDING)
            {
                break;
            }            
            PCF7992_Wr_Rd_Buf(LINrxBuffer,40,&status,IMMO_API_NO_BLOCKING);
            waitCounter=7*WAIT20ms;
            ImmoCtxState=IMMO_CHECKDATA ;
        
        break;

        case IMMO_CHECKDATA :
            waitCounter--;
            if (waitCounter <= 0)
            {
                ret = LIN_DriverStatus();
                if (ret & LIN_STATUS_PENDING)
                {
                    break;
                }            

                ret = LIN_GetMsg( PCF7992_ID_COMMAND_BUFRW, LINrxBuffer );

                PCF7992_Wr_SCFG( PCF7992_WR_SCFG_TXCON, 0x04,IMMO_API_NO_BLOCKING);
                pos = IMMO_FindKeyM (LINrxBuffer,ImmoCtxLastKey);
                
                keyCounterValue=IMMO_KeyCmp(ImmoCtxLastKey);
                if(keyCounterValue==UNIVERSALKEYCOUNTER)
                {
                    if(ImmoE2KeyNumber != 0)
                    {
                        FlgUnivKey=2;
                    }
                    else
                    {
                        FlgUnivKey=1;
                    }    
                }
                else
                {
                    FlgUnivKey=0;
                }    
                
                if (pos!=-1 && (keyCounterValue!=-1 && FlgUnivKey <= 1) )   
                {
                    ImmoCtxState=IMMO_UNBLOCK;
                }
                else if  (FlgUnivKey == 2)
                {
                    ImmoCtxState=IMMO_BLOCK;
                }
                else
                {
                    if (pos==-1) 
                    {
                        ImmoCtxNRetry+=IMMO_INCR_FOR_XPDR_ERR;/*LG:20071217*/
                        ImmoCtxError=IMMO_XPDR_ERROR;
                    }
                    else 
                    {
                        ImmoCtxNRetry+=IMMO_INCR_FOR_IMMO_KEYNOTVLD_ERR;/*LG:20071217*/
                        ImmoCtxError=IMMO_KEYNOTVALID;
                        
                    }

                    ImmoCtxState=IMMO_START;
                }
            }
        break;

        case IMMO_UNBLOCK :
            IMMO_UnblockEngine();
            ImmoCtxError=IMMO_NOERROR;
            ImmoCtxState=IMMO_END;
#ifndef _BUILD_IMMOLED_
            ImmoLed_ON(); 
#endif
        break;

        case IMMO_BLOCK :
            if(ENIMMOBLOCK)
            {
                IMMO_BlockEngine();
            }
            ImmoCtxState=IMMO_END;
#ifndef _BUILD_IMMOLED_
            ImmoLed_OFF();
#endif
        break;
                         
        case IMMO_END :
        if(KeySignal==0)
        {
            
            EtpuUartIrqDisable(ETPU_UART_RX);
            EtpuUartIrqDisable(ETPU_UART_TX);
            ImmoCtxState = IMMO_IDLE;
        }
        else 
        {                              
            ImmoCtxState = IMMO_END;
        }
        
        ImmoKeyReadDone =1;
#if defined(DIAG_IMMO)
        Immo_Diag();
#endif

        break;                          
                            
    }
}

/***********************************************************************
*
*    DESCRIPTION: BLOCK_ENGINE and  UNBLOCK_ENGINE
*                   
*
*
*    AUTHOR:       Luca G
*
*    HISTORY:     07/06/2007 First Version
*
***********************************************************************/  
void IMMO_BlockEngine (void)
{
    InjEnableIMMO = 0; 
}

void IMMO_UnblockEngine (void)
{
    InjEnableIMMO = 1;
}  

uint8_T IMMO_CheckEngine (void)
{
    return InjEnableIMMO;
}  

  
  
  


/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER KEY PARSER 2 of 3
*                   
*
*
*    AUTHOR:       Solinas
*
*    HISTORY:     19/02/2008 First Version
*
***********************************************************************/
int16_t IMMO_FindKeyM (uint8_t* buffer,uint8_t*key)
{
    uint8_t key1[6];
    uint8_t key2[6];
    uint8_t key3[6];
    int16_t pos;
    
    pos = IMMO_FindKey (buffer,key1);

    if (pos!=-1) 
    {
        pos += IMMO_FindKey (&buffer[pos],key2);
        if (pos!=-1)
        {
            if (memcmp((char *)key1,(char *)key2,6)==0)
            {
            memcpy (key,key2,6); /* Ok.. find two at the first sight !!! */
            }
            else
            {
                pos += IMMO_FindKey (&buffer[pos],key3);
                if (pos!=-1)
                {
                    if (memcmp((char *)key1,(char *)key3,6)==0)
                    {
                      memcpy (key,key1,6); /* Ok.. find two at the second sight !!! */
                    }
                    else
                    { 
                        if (memcmp((char *)key2,(char *)key3,6)==0)
                        {
                            memcpy (key,key2,6); /* Ok.. find two at the third sight !!! */
                        }
                        else
                        {
                            pos=-1; /* MHhmmmh.. 3 different keys... Very BED !!! */
                        }
                    }           
                }
            }
        }
    } 
    return(pos); 
}

/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER KEY PARSER
*                   
*
*
*    AUTHOR:       Solinas
*
*    HISTORY:     01/03/2007 First Version
*
***********************************************************************/

int16_t IMMO_FindKey (uint8_t* buffer,uint8_t*key)
{

    uint8_t shift;
    uint8_t mask;
    uint8_t dataMSB;
    uint8_t dataLSB;
    uint8_t i=0; //ByteCounter
    uint8_t byteCounter;
    uint8_t CheckHeader=1;

    while ((i<40)&&(CheckHeader))
    {
        for (shift=0;shift<8;shift++)
        {
            mask=(1<<shift)-1;
            dataMSB =((buffer[i]<<shift)&(~mask));
            dataMSB|=((buffer[i+1]>>(8-shift))&mask);
            dataLSB =((buffer[i+1]<<shift)&(~mask));
            dataLSB|=((buffer[i+2]>>(8-shift))&mask);
            if ((dataMSB==(IMMOHEADER>>8)) && (dataLSB==(IMMOHEADER&0x00ff))) 
            {
                CheckHeader=0; 
                break;
            }
        }
        if (CheckHeader) i++;
    }

    byteCounter=i+2;

    if (CheckHeader==0) 
    {
        for (i=0;i<6;i++) 
        {
            key[i] =((buffer[byteCounter+i]<<shift)&(~mask));
            key[i]|=((buffer[byteCounter+1+i]>>(8-shift))&mask);
        }
        
        return (byteCounter+i);
    }
    else 
    {
        return -1;
    }
}



/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER KEY COMPARE
*                   
*
*
*    AUTHOR:       Solinas
*
*    HISTORY:     05/04/2007 First Version
*
***********************************************************************/

int8_t IMMO_KeyCmp(uint8_t* key)
{
    int8_t keyCounter=0;
    int8_t foundKey=1;

    if(ENUNIVERSALKEY || FlgEOL)
    {
        foundKey=memcmp((char *)key,(char *)UNIVERSALKEYVALUE,6);
        if(foundKey==0)
        {
            keyCounter = UNIVERSALKEYCOUNTER+1;
        }
    }
    
    while (( keyCounter<ImmoE2KeyNumber) && (foundKey!=0))
    {
        foundKey=memcmp((char *)key,(char *)ImmoE2KeyStored[keyCounter],6);
        keyCounter++;
    }
    
    if (foundKey!=0)
    {
        keyCounter=0; /* -1 is the not Found Condition */
    }
    return (keyCounter-1);
}

/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER KEY COMPARE for KEY STORING PROCEDURE
*                   
*
*
*    AUTHOR:       Solinas
*
*    HISTORY:     22/05/2007 First Version
*
***********************************************************************/


int8_t IMMO_KeyCmpRam(uint8_t* key)
{

    int8_t keyCounter=0;
    int8_t foundKey=1;
    keyCounter=0;
    while (( keyCounter<ImmoE2RamCopyKeyNumber) && (foundKey!=0))
    {
        foundKey= memcmp((char *)key,(char *)ImmoE2RamCopyKeyStored[keyCounter],6);
        keyCounter++;
    }
    if (foundKey!=0)
    {
        keyCounter=0; /* -1 is the not Found Condition */
    }
    return (keyCounter-1);

}


/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER PowerOn
*                    Call this function at the KeyOff               
*
*
*    AUTHOR:         Solinas - Luca G
*
*    HISTORY:     04/04/2007 First Version
*                                    07/06/2007 Add ImmoWaitForKeyStoring in order to communicate 
*                                                   with FSM engine Machine
*                 ImmoMainState = NORMAL / STORING 
***********************************************************************/

void IMMO_PowerOn (void)
{ 
    uint8_t i = 0, j = 0;

    /*enable Inj at Power ON*/
    InjEnableIMMO         = 1;

    ImmoMainState        = IMMO_KEY_IDLE;

    /*disable wait for Power Latch*/
    ImmoWaitForKeyStoring = 0; 

    /*Arm WD*/
    immo_WDtimer = 0;

    clock=0;
    ImmoCtxState = IMMO_IDLE;
    ImmoCtxError = IMMO_NOERROR;
    ImmoCtxNRetry = 0;
    
    
    ImmoEOLState = IMMO_NOEOL;

    for (i= 0; i<6; i++)
    {
        ImmoCtxLastKey[i] = 0;
    }
    ImmoCtxLedState = IMMO_LED_SIG1;

    for (i= 0; i<6; i++)
    {
        for (j = 0; j < 8; j++)
        {
            ImmoE2RamCopyKeyStored[i][j] = 0;
        }
    }
    
    ImmoE2RamCopyKeyNumber = 0;
    memset ((uint8_t *)LINrxBuffer, 0,40);
    memset ((uint8_t *)txBuffer, 0,8);
    Immo_EE_PowerOn();
}






/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER Storing MGM
*                    Call this function in the 100 ms task               
*
*
*    AUTHOR:         Solinas
*
*    HISTORY:     04/04/2007 First Version
*
*                 
***********************************************************************/
void IMMO_KeyStoring (void)
{
      
     ++clock;
     
     switch (ImmoMainState)
     {
        case IMMO_KEY_IDLE : 
            if (KeySignal==1)
            { 
                keyOnTime=clock;
                ImmoMainState=IMMO_KEY_ON_NORMAL; 
                ImmoE2RamCopyKeyNumber=0;

                /*Arm WD*/
                immo_WDtimer = 0;
            }
            else
            {
                /*wait state Disable*/
                ImmoWaitForKeyStoring = 0;
            }
#ifndef _BUILD_IMMOLED_ 
            ImmoLed_OFF();
#endif 
        break; 
 
        case IMMO_WAIT_KEY_ON_STORING :
            if (KeySignal==1)
            {
                keyOnTime=clock;
                if ((keyOnTime-keyOffTime)> SECONDS_15) 
                {
                    ImmoMainState=IMMO_KEY_ON_NORMAL; /* Abort the key storing Procedure */
                    /*wait state Disable*/
                    ImmoWaitForKeyStoring = 0;
                } 
                else 
                {
                    ImmoMainState=IMMO_KEY_ON_STORING; /* Stay in the storing state */
                }
                /*Arm WD*/
                immo_WDtimer = 0;
            }
            else if(immo_WDtimer > IMMO_WD_TH)
                {
                    ImmoWaitForKeyStoring = 0;
                }
        break;
        
        case IMMO_KEY_ON_NORMAL :
            if (KeySignal==0)
            {
                keyOffTime=clock;
                ImmoMainState=IMMO_KEY_OFF_NORMAL; 
            }
            else
            {
                if(((ImmoCtxError == IMMO_NOERROR)&&(ImmoKeyReadDone))|| (ImmoCtxError == IMMO_KEYNOTVALID))
                {
                    if( ((!(ENUNIVERSALKEY || FlgEOL))||(memcmp((char *)UNIVERSALKEYVALUE,(char *)ImmoCtxLastKey,6)!=0))&&
                        ((memcmp((char *)ImmoE2KeyStored[0],(char *)ImmoCtxLastKey,6)==0)||(ImmoE2KeyNumber==0)))
                    /* Found Red Key */
                    {
                        /*wait state Enable*/
                        if(ENIMMOBLOCK)
                        {
                            ImmoWaitForKeyStoring = 1;
                        }
                    }
                }
                
            }
        break; 

        case IMMO_KEY_ON_STORING :
            if (KeySignal==0)
            {
                keyOffTime=clock;

                if ((keyOffTime-keyOnTime) > SECONDS_3 )
                {
                    ImmoMainState=IMMO_KEY_OFF_NORMAL; /* Abort the key storing Procedure */
                    /*wait state Disable*/
                    ImmoWaitForKeyStoring = 0;
                }
                else 
                {
                    ImmoMainState=IMMO_KEY_OFF_STORING; /* Stay in the storing state */
                }
                break; 
            } 

        break;

        case IMMO_KEY_OFF_NORMAL :
            if(((ImmoCtxError == IMMO_NOERROR)&&(ImmoKeyReadDone)) || (ImmoCtxError == IMMO_KEYNOTVALID))
            {
                if  (((keyOffTime-keyOnTime) <=  SECONDS_3  )&&
                ((!(ENUNIVERSALKEY || FlgEOL))||(memcmp((char *)UNIVERSALKEYVALUE,(char *)ImmoCtxLastKey,6)!=0))&&
                ((memcmp((char *)ImmoE2KeyStored[0],(char *)ImmoCtxLastKey,6)==0)||(ImmoE2KeyNumber==0)))
                /* Found Red Key */
                {
                    ImmoMainState=IMMO_WAIT_KEY_ON_STORING;
                    memcpy (ImmoE2RamCopyKeyStored[0],ImmoCtxLastKey,6);
                    ImmoE2RamCopyKeyNumber=1;
                }
                else 
                {
                    ImmoMainState=IMMO_KEY_IDLE;
                    /*wait state Disable*/
                    ImmoWaitForKeyStoring = 0;
                }
            }
            else
            {
                ImmoMainState=IMMO_KEY_IDLE;
                /*wait state Disable*/
                ImmoWaitForKeyStoring = 0;
            }
        break; 
                              
        case IMMO_KEY_OFF_STORING :
            if(((ImmoCtxError == IMMO_NOERROR) || (ImmoCtxError == IMMO_KEYNOTVALID)) && (memcmp((char *)UNIVERSALKEYVALUE,(char *)ImmoCtxLastKey,6)!=0))
            {
                if  ((memcmp((char *)ImmoE2RamCopyKeyStored[0],(char *)ImmoCtxLastKey,6)==0)) /* Found Red Key */
                { 
                    if (ImmoE2RamCopyKeyNumber>1) 
                    {
                        /* Store the temporary key tags to eeprom
                        */
                        uint8_t i = 0, j = 0;
                        for (i= 0; i<6; i++)
                        {
                            for (j = 0; j < 8; j++)
                            {
                                ImmoE2KeyStored[i][j] = ImmoE2RamCopyKeyStored[i][j];
                            }
                        }
                        ImmoE2WriteFlag = 1;
                        ImmoE2KeyNumber = ImmoE2RamCopyKeyNumber;

#ifndef _BUILD_IMMOLED_
                        ImmoLed_ON();
#endif
                        //DisableAllInterrupts(); /* Disable external interrupts */
                        //Immo_EE_PowerOff();
                        //EnableAllInterrupts();      /* Re-enable external interrupts    */

                    }
                    /* Program the keys Only if there is at least one black key within the red key*/
                    ImmoMainState=IMMO_KEY_IDLE;
                }
                else  
                {                                    
                    if (ImmoE2RamCopyKeyNumber>=8)  
                    {
                        ImmoMainState=IMMO_KEY_IDLE; /* Abort the key storing Procedure */
                        /*wait state Disable*/
                        ImmoWaitForKeyStoring = 0;
                    }
                    else 
                    {
                        /* Check if the Key is already stored in the sequence */
                        if ((IMMO_KeyCmpRam(ImmoCtxLastKey))==-1) 
                        {
                            memcpy (ImmoE2RamCopyKeyStored[ImmoE2RamCopyKeyNumber],ImmoCtxLastKey,6);
                            ImmoE2RamCopyKeyNumber++;
                            ImmoMainState=IMMO_WAIT_KEY_ON_STORING; 
                        } 
                        else 
                        {
                            ImmoMainState=IMMO_KEY_IDLE; /* Abort the key storing Procedure */
                            /*wait state Disable*/
                            ImmoWaitForKeyStoring = 0;
                        }
                    }   
                }
            }
            else 
            {
                ImmoMainState=IMMO_KEY_IDLE; /* Abort the key storing Procedure */
                /*wait state Disable*/
                ImmoWaitForKeyStoring = 0;
            }
        break;  
    }

    /*increment section for WD*/
    if (KeySignal==0)
    {
        ++immo_WDtimer;
    }
    else
    {
        immo_WDtimer = 0;
    }
}

/************************************/
/*                                                            */
/* uint8_t Get_ImmoctxState(uint8_t Type) */
/*                                                            */
/************************************/

uint8_t Get_ImmoctxState(uint8_t Type)
{
    uint8_t State;

    switch(Type)
    {      
        case STATE_IMMO :
            State=ImmoCtxState;
        break;
        
        case STATE_IMMO_ERROR:
            State=ImmoCtxError;
        break;
        
        case STATE_IMMO_nRETRY:
            State=ImmoCtxNRetry;
        break;
        
        case STATE_IMMO_LED:
            State=ImmoCtxLedState;
        break;

        default:

        break;

    }
    
    return State;
} 

/***************************/
/*                                            */
/* uint8_t Get_ImmoState(void) */
/*                                            */
/***************************/

uint8_t Get_ImmoState(void)
{
    return ImmoCtxState;
} 

/********************************/
/*                                                    */
/* void Set_ImmoState(uint8_t state) */
/*                                                    */
/********************************/  

void Set_ImmoState(uint8_t state)
{
    ImmoCtxState=state;
}    



/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER DIAG STATE  Record local indentifier : 0x63
*                   *
*
*    AUTHOR:       Solinas/Basciu
*    DESCRIPTION
*    Bit 0    1 = Chiave Riconosciuta OK
*    Bit 1    1 = ECU Vergine
*    Bit 2    1 = Chiave Errata 
*    Bit 3    1 = Antenna guasta
*   
*
*    HISTORY:     20/09/2007 First Version
*
***********************************************************************/

uint8_t IMMO_GetDiagState (void)
{
    uint8_t diagState=0;


    if (ImmoE2KeyNumber == 0 && FlgUnivKey == 0)
    {
        diagState =IMMO_DIAG_ECU_BLANK;
    }
    else 
    {
        if (ImmoCtxError==IMMO_NOERROR)
        {
            diagState=IMMO_DIAG_KEY_OK;
        }
        else
        {
            if ( ImmoCtxError==IMMO_ANTENNA_ERROR || 
            ImmoCtxError==IMMO_XPDR_ERROR )
            {
                diagState =IMMO_DIAG_ANTENNA_ERROR;
            }
            else
            {
                if(ImmoCtxError==IMMO_KEYNOTVALID)
                {
                    diagState =IMMO_DIAG_UNVALID_KEY;     
                }
                else if(ImmoCtxError==IMMO_XCVR_ERROR)
                    {
                         diagState =IMMO_DIAG_COMM_TIMEOUT;  
                    }
            }  
        }
    }


    return (diagState);
}

void Immo_Diag(void)
{
    uint8_T PtFaultImmo = NO_PT_FAULT;
    uint8_T StDiagImmo = NO_FAULT;


    if (ImmoE2KeyNumber == 0 && FlgUnivKey == 0)
    {
        PtFaultImmo =ECU_BLANK;
    }
    else
    {
        if (ImmoCtxError==IMMO_ANTENNA_ERROR)
        {
            PtFaultImmo =ANTENNA_ERROR;
        }
        else if(ImmoCtxError==IMMO_KEYNOTVALID)
        {
            PtFaultImmo =INVALID_KEY;     
        }
        else if(ImmoCtxError==IMMO_XPDR_ERROR)
        {
            PtFaultImmo =XPDR_ERROR;     
        }
        else if(ImmoCtxError==IMMO_XCVR_ERROR)
        {
            PtFaultImmo =XCVR_ERROR;     
        }
    }
    
    DiagMgm_SetDiagState(DIAG_IMMO, PtFaultImmo, &StDiagImmo);         
}

uint8_t Immo_SampleTimeCalc(uint8_t phmeas)
{
    int16_t deltaphmeas;
    int16_t tmpsmplc;
    uint8_t smplc;

    /* Calcolo il delta rispetto al valore nominale */
    deltaphmeas = (int16_t)phmeas - (int16_t)IMMOPHMEAS;

    /* Calcolo il valore di smplc rispetto al caso nominale */
    tmpsmplc = (int16_t)IMMOSMPLC + 2*deltaphmeas;

    if(tmpsmplc < 0x00)
    {
        tmpsmplc += 0x3f;
    }
    else if(tmpsmplc > 0x3f)
    {
        tmpsmplc -= 0x3f;
    }

    if(tmpsmplc >= 0x3f)
    {
        tmpsmplc = 0x3f;
    }
    else if(tmpsmplc <= 0x00)
    {
        tmpsmplc = 0x00;
    }
    else
    {
    }

    smplc = (uint8_t)tmpsmplc;

    return smplc;
    
}

#else //ifdef _BUILD_IMMO_

uint8_t  ImmoWaitForKeyStoring;
uint8_T  InjEnableIMMO;
uint8_T  InjDisableIMMOFdbk;

extern uint8_T TIMIMMOINJSTARTEREN;

/*stubs section*/
uint8_t ImmoE2KeyNumber = 0;
/**/

extern uint8_T InjEnableCAN;

void IMMO_PowerOn(void)
{
    /*enable Inj at Power ON*/
    InjEnableIMMO = 1;
    /*disable wait for Power Latch*/  
    ImmoWaitForKeyStoring = 0;
    /*disable fdbk*/
    InjDisableIMMOFdbk = 0;
}

void IMMO_T5ms(void)
{
    static uint8_T unlockInj = 0;
    static uint8_T cntUnlockInj = 0;
  
    if (ENIMMOBLOCK == 1)
    {
        InjDisableIMMOFdbk = 1;
        if ((InjDisableIMMO == 0) || (unlockInj != 0))
        {
            if (TransportLockEcu != 0)
            {
                InjEnableIMMO = 1;
            }
            else
            {
                InjEnableIMMO = 0;
            }
            unlockInj = 1;
        }
        else
        {
            if (cntUnlockInj >= TIMIMMOINJSTARTEREN)
            {
                InjEnableIMMO = 0;
            }
            else
            {
                cntUnlockInj++;
            }
        }
    }
    else
    {
        InjDisableIMMOFdbk = 1;
        if (TransportLockEcu != 0)
        {
            InjEnableIMMO = 1;
        }
        else
        {
            InjEnableIMMO = 0;
        }
    }
}

uint8_t IMMO_GetDiagState(void)
{
    return 0;
}
#endif
