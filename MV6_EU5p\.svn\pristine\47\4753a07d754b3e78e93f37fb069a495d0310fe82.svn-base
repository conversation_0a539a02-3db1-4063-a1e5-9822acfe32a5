#include "typedefs.h"


#pragma ghs section rodata=".ee_id7_data"

// Declare here all the variables to be stored in EEPROM with ID7

    #include "dummy_eep_07.c"

#if defined (_BUILD_EXHVALMGM_) || defined (_BUILD_EXHVALMGM_SBS_)
    #include "exhvalmgm_eep.c"
#endif
#ifdef _BUILD_SELFMGM_
    #include "selfmgm_eep.c"
#endif
#ifdef _BUILD_SYNCMGM_
    #include "syncmgm_eep.c"
#endif
#ifdef _BUILD_RELAYMGM_
    #include "relaymgm_eep.c"
#endif 
#ifdef _BUILD_ANTITAMPERING_
    #include "antiTampering_eep.c"
#endif
#ifdef _BUILD_LAUNCHCTRL_
    #include "launchctrl_eep.c"
#endif
#ifdef _BUILD_TIMING_
    #include "timing_eep_10.c"
#endif
#ifdef _BUILD_CANMGM_
    #include "can_eep_07.c"
#endif


