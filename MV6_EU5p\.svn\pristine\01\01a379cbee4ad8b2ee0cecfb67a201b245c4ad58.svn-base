/*  File    : ionlambdaann.h
 *  Author  : <PERSON><PERSON>
 *  Date    : 28/02/2007 14.23
 *  Revision: IonLambdaANN 2.2
 *  Note    : scalature calibrabili
 * 
 *  Copyright 2007 Eldor Corporation
 */
#ifndef _IONLAMBDAANN_H_
#define _IONLAMBDAANN_H_

/* include files */

/* local definitions */
#define IONERRORSTATUS_MASK 	0x03
#define MAX_RPM_PERC		(15000/100)
#define NEUTRAL_LAM_STATE	2
#define FILTLAMRESET_TDC_THR	10

#define BKRPMLAMFIL_ANN_dim	8U
#define BKANNTANSIG_dim		97U
#define BKTWATCRKLAM_ANN_dim 	8U
#define BKTDCCRKLAM_ANN_dim 	8U
#define BKLAMINDEX_ANN_dim 	5U
#define BKRPMLOADSAT_ANN_dim	16U
#define BKRPMLAMCORR_ANN_dim	12U
#define BKLOADLAMCORR_ANN_dim	10U

/* ANN input & neuron dimension and scale factor */
#define ANN_INPUT_dim				8U		/* numero di ingressi della rete */
#define ANN_LAYER_1_dim				10U		/* numero di neuroni della rete */
#define LAYER_1_out_scale			11U		/* scalatura di BKANNTANSIG */
#define LAYER_2_in_scale			14U 	/* scalatura di VTANNTANSIG */
#define LAYER_2_out_scale			15U		/* scalatura dell'uscita della rete */

/* external data */
extern uint16_T ChInt[8];
extern uint16_T ChPeakId;
extern uint16_T CntTdcCrk;
extern uint8_T  CutoffFlg;
extern uint16_T FFS[8];
extern uint8_T  IonAbsTdc;
extern uint8_T  IonDTheta;
extern uint8_T  IonErrorStatus[8];
extern uint16_T LamObj;
extern uint16_T Load;
extern uint16_T Rpm;
extern int16_T  SAoutCyl[8];
extern uint8_T  StMisf[8];
extern int16_T  TWaterCrk;
extern uint16_T ThInt[8];
extern uint16_T ThPeakId;

/* public functions */
extern void IonLambdaANN_Init(void);
extern void IonLambdaANN_NoSync(void);
extern void IonLambdaANN_TDC(void);

#endif /* _IONLAMBDAANN_H_ */
