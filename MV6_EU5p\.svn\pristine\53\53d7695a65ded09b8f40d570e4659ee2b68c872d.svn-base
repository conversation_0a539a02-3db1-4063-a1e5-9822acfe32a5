/*
 * File: CmeFilterMgm.c
 *
 * Code generated for Simulink model 'CmeFilterMgm'.
 *
 * Model version                  : 1.2383
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar 27 14:58:16 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (19), Warnings (6), Errors (8)
 */

#include "CmeFilterMgm.h"
#include "CmeFilterMgm_private.h"

/* Named constants for Chart: '<S24>/CmeDriverI_Offset' */
#define CmeFilterMgm_IN_CME_I_CL       ((uint8_T)1U)
#define CmeFilterMgm_IN_CME_I_INIT     ((uint8_T)2U)
#define CmeFilterMgm_IN_CME_I_OL       ((uint8_T)3U)

/* Named constants for Chart: '<S24>/TrigCL' */
#define CmeFilterMgm_IN_IN_DU_CHANGE   ((uint8_T)1U)
#define CmeFilterMgm_IN_IN_DU_CL       ((uint8_T)2U)
#define CmeFilterMgm_IN_WAIT_DU_CL     ((uint8_T)3U)
#define CmeFilterMgm_IN_WAIT_DU_EXIT   ((uint8_T)4U)

/* Named constants for Chart: '<S24>/TrigResFilter' */
#define CmeFi_IN_ST_NO_RES_CME_DRIVER_I ((uint8_T)1U)
#define CmeFilte_IN_ST_RES_CME_DRIVER_I ((uint8_T)2U)

/* Named constants for Chart: '<S28>/GasResp_calc' */
#define CmeFilterMgm_IN_IDLE_GAS_RESP  ((uint8_T)2U)
#define CmeFilterMgm_IN_RUN_GAS_RESP   ((uint8_T)3U)
#define CmeFilter_IN_FILT_DOWN_GAS_RESP ((uint8_T)1U)

/* Named constants for Chart: '<S29>/Lim_AfterChange' */
#define CmeFilterMgm_IN_LIM_CHANGE_GC  ((uint8_T)1U)
#define CmeFilterMgm_IN_LIM_FILT_GC    ((uint8_T)2U)
#define CmeFilterMgm_IN_LIM_IDLE_GC    ((uint8_T)3U)

/* Named constants for Chart: '<S47>/Calc_flgCmeDrPMaxRSat' */
#define CmeFilterMgm_IN_NO_STEP_CME    ((uint8_T)1U)
#define CmeFilterMgm_IN_RISE_STEP_CME  ((uint8_T)2U)

/* user code (top of source file) */
/* System '<Root>/CmeFilterMgm' */
#ifdef _BUILD_CMEFILTERMGM_

/* Block signals (default storage) */
BlockIO_CmeFilterMgm CmeFilterMgm_B;

/* Block states (default storage) */
D_Work_CmeFilterMgm CmeFilterMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_CmeFilterMgm CmeFilterMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_CmeFilterMgm CmeFilterMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T CmeDriverCIPostF;

/* CmiDriverI post filtered */
int16_T CmeDriverCLOff;

/* CmeDriverCLOff */
int16_T CmeDriverI;

/* CME fast */
int16_T CmeDriverICLOffOut;

/* CmeDriverICLOffOut */
int16_T CmeDriverP;

/* CME slow */
int16_T CmeDriverPMaxRSat;

/* CmeDriverPMaxRSat */
int16_T CmeDriverPOffMaxRSat;

/* CmeDriverPMaxRSat */
int16_T CmeDriverPRate;

/* CmeDriverP post rate */
int16_T CmeDriverPTmpFilt;

/* CmiDriverPTmp filtered (before rate limiter) */
int16_T CmeIBuffCLOff[17];

/* CmeDriverI shifted */
int32_T CmeRateMax;

/* CmiDriverP max variation in filtering phase */
int32_T CmeRateMin;

/* CmiDriverP min variation in filtering phase */
uint16_T CntCDICLOff;

/* CntCDICLOff */
int16_T DeltaCmeDriver;

/* CME fast */
uint8_T FlgCmeDrPMaxRSat;

/* FlgCmeDrPMaxRSat */
uint8_T FlgCmeDriverP;

/* FlgCmeDriverP */
uint8_T FlgCmeDriverPHiRpm;

/* FlgCmeDriverPHiRpm */
uint8_T FlgKfCmeDGResp;

/* Kf Filt CmeDriver Gas resp enable */
uint8_T FlgPhGain;

/* FlgPhGain */
uint8_T FlgResCmeDriverI;

/* FlgResCmeDriverI */
uint16_T GRGainLim;

/* Gain */
uint32_T IDCmeFilterMgm;

/* ID Version */
uint16_T KFiltCmeDriverI;

/* CmiDriverP filter constant in filtering phase */
uint16_T KFiltCmeDriverP;

/* CmiDriverP filter constant in filtering phase */
uint8_T StCmeICL;

/* StCmeICL */
uint8_T StCmeITrCL;

/* StCmeITrCL */
uint8_T TrLimGCTrig;

/* Trigger to reset Limiter to filter */
int16_T VtCmeDriver[11];

/* CmeDriver delta */

/* Output and update for atomic system: '<S4>/FiltGain_Calculation' */
void CmeFilterM_FiltGain_Calculation(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_U8_S16;
  uint16_T rtb_LookUp_U8_S16_c;
  uint16_T rtb_LookUp_IR_U8;
  boolean_T rtb_Compare;
  boolean_T rtb_RelationalOperator1;
  uint16_T rtb_Switch1;
  uint16_T rtb_DataTypeConversion;
  uint8_T rtb_Switch2[6];
  int32_T i;
  uint8_T rtb_RelationalOperator1_0;

  /* If: '<S10>/If' incorporates:
   *  Constant: '<S10>/DRIVEDOWN'
   *  Constant: '<S10>/DRIVEUP'
   *  Inport: '<Root>/StTrqDriv'
   *  Logic: '<S10>/Logical Operator1'
   *  RelationalOperator: '<S10>/Relational Operator4'
   *  RelationalOperator: '<S10>/Relational Operator5'
   */
  if ((StTrqDriv == DRIVEUP) || (StTrqDriv == DRIVEDOWN)) {
    /* Outputs for IfAction SubSystem: '<S10>/Calc_KFiltCmeDriverP' incorporates:
     *  ActionPort: '<S16>/Action Port'
     */
    /* RelationalOperator: '<S23>/Compare' incorporates:
     *  Constant: '<S23>/Constant'
     *  Inport: '<Root>/GearPosClu'
     */
    rtb_Compare = (GearPosClu != 0);

    /* RelationalOperator: '<S16>/Relational Operator1' incorporates:
     *  Constant: '<S16>/DRIVEDOWN'
     */
    rtb_RelationalOperator1 = (StTrqDriv == DRIVEDOWN);

    /* Switch: '<S16>/Switch' */
    if (rtb_RelationalOperator1) {
      /* Switch: '<S16>/Switch4' incorporates:
       *  Constant: '<S16>/VTKFILTCMEDOWN0'
       *  Constant: '<S16>/VTKFILTCMEDOWN1'
       *  Inport: '<Root>/EngResp'
       */
      for (i = 0; i < 6; i++) {
        if (EngResp != 0) {
          rtb_Switch2[i] = VTKFILTCMEDOWN1[i];
        } else {
          rtb_Switch2[i] = VTKFILTCMEDOWN0[i];
        }
      }

      /* End of Switch: '<S16>/Switch4' */

      /* S-Function (LookUp_U8_S16): '<S21>/LookUp_U8_S16' incorporates:
       *  Constant: '<S16>/BKCMEDRIVPDD'
       *  Constant: '<S16>/BKCMEDRIVPDD_dim'
       *  Inport: '<Root>/CmeDriverPDownDiff'
       */
      LookUp_U8_S16( &rtb_LookUp_U8_S16, &rtb_Switch2[0], CmeDriverPDownDiff,
                    &BKCMEDRIVPDD[0], ((uint8_T)BKCMEDRIVPDD_dim));
      rtb_Switch1 = rtb_LookUp_U8_S16;
    } else {
      /* Switch: '<S16>/Switch2' incorporates:
       *  Constant: '<S16>/VTKFILTCMEUP0'
       *  Constant: '<S16>/VTKFILTCMEUP1'
       *  Inport: '<Root>/EngResp'
       */
      for (i = 0; i < 6; i++) {
        if (EngResp != 0) {
          rtb_Switch2[i] = VTKFILTCMEUP1[i];
        } else {
          rtb_Switch2[i] = VTKFILTCMEUP0[i];
        }
      }

      /* End of Switch: '<S16>/Switch2' */

      /* S-Function (LookUp_U8_S16): '<S22>/LookUp_U8_S16' incorporates:
       *  Constant: '<S16>/BKCMEDRCMEFILT'
       *  Constant: '<S16>/BKCMEDRCMEFILT_dim'
       */
      LookUp_U8_S16( &rtb_LookUp_U8_S16_c, &rtb_Switch2[0], DeltaCmeDriver,
                    &BKCMEDRCMEFILT[0], ((uint8_T)BKCMEDRCMEFILT_dim));
      rtb_Switch1 = rtb_LookUp_U8_S16_c;
    }

    /* End of Switch: '<S16>/Switch' */

    /* DataTypeConversion: '<S16>/Data Type Conversion' */
    rtb_DataTypeConversion = (uint16_T)(((uint32_T)rtb_Switch1) >> 1);

    /* Selector: '<S18>/Selector' incorporates:
     *  Constant: '<S18>/ONE'
     *  Inport: '<Root>/GearPosClu'
     *  Sum: '<S18>/Subtract'
     */
    i = GearPosClu - 1;
    if (i < 0) {
      i = 0;
    }

    /* Switch: '<S18>/Switch7' incorporates:
     *  Constant: '<S18>/VTKFILTCMEGAIN'
     *  Constant: '<S19>/KFILTCMEINEUTRAL'
     *  Product: '<S18>/Product'
     *  Selector: '<S18>/Selector'
     *  Sum: '<S18>/Subtract'
     */
    if (rtb_Compare) {
      /* Switch: '<S16>/Switch8' incorporates:
       *  DataTypeConversion: '<S16>/Data Type Conversion2'
       */
      if (rtb_RelationalOperator1) {
        /* S-Function (LookUp_IR_U8): '<S20>/LookUp_IR_U8' incorporates:
         *  Constant: '<S16>/BKRPMTRQDRIV_dim'
         *  Constant: '<S16>/VTKFILTCMEI'
         */
        LookUp_IR_U8( &rtb_LookUp_IR_U8, &VTKFILTCMEI[0],
                     CmeFilterMgm_B.PreLookUpIdSearch_U16_o1,
                     CmeFilterMgm_B.PreLookUpIdSearch_U16_o2, ((uint8_T)
          BKRPMTRQDRIV_dim));
        rtb_Switch1 = (uint16_T)(((uint32_T)rtb_LookUp_IR_U8) >> 1);
      } else {
        rtb_Switch1 = rtb_DataTypeConversion;
      }

      /* End of Switch: '<S16>/Switch8' */
      rtb_Switch1 = (uint16_T)((((uint32_T)rtb_Switch1) * VTKFILTCMEGAIN[i]) >>
        3);
    } else {
      rtb_Switch1 = KFILTCMEINEUTRAL;
    }

    /* End of Switch: '<S18>/Switch7' */

    /* MinMax: '<S18>/MinMax' incorporates:
     *  Constant: '<S18>/ONE1'
     */
    if (16384 < rtb_Switch1) {
      CmeFilterMgm_B.Merge = 16384U;
    } else {
      CmeFilterMgm_B.Merge = rtb_Switch1;
    }

    /* End of MinMax: '<S18>/MinMax' */

    /* Switch: '<S18>/Switch1' incorporates:
     *  Constant: '<S18>/VTKFILTCMEGAIN'
     *  DataTypeConversion: '<S19>/Data Type Conversion1'
     *  Inport: '<Root>/EngResp'
     *  Product: '<S18>/Product1'
     *  Selector: '<S18>/Selector'
     *  Sum: '<S18>/Subtract'
     *  Switch: '<S19>/Switch1'
     *  Switch: '<S19>/Switch5'
     */
    if (rtb_Compare) {
      rtb_Switch1 = (uint16_T)((((uint32_T)VTKFILTCMEGAIN[i]) *
        rtb_DataTypeConversion) >> 3);
    } else {
      if (rtb_RelationalOperator1) {
        /* Switch: '<S19>/Switch3' incorporates:
         *  Constant: '<S19>/KFCMEDOWNNEUT0'
         *  Constant: '<S19>/KFCMEDOWNNEUT1'
         *  Inport: '<Root>/EngResp'
         *  Switch: '<S19>/Switch1'
         */
        if (EngResp != 0) {
          rtb_RelationalOperator1_0 = KFCMEDOWNNEUT1;
        } else {
          rtb_RelationalOperator1_0 = KFCMEDOWNNEUT0;
        }

        /* End of Switch: '<S19>/Switch3' */
      } else if (EngResp != 0) {
        /* Switch: '<S19>/Switch5' incorporates:
         *  Constant: '<S19>/KFCMEUPNEUT1'
         *  Switch: '<S19>/Switch1'
         */
        rtb_RelationalOperator1_0 = KFCMEUPNEUT1;
      } else {
        /* Switch: '<S19>/Switch1' incorporates:
         *  Constant: '<S19>/KFCMEUPNEUT0'
         *  Switch: '<S19>/Switch5'
         */
        rtb_RelationalOperator1_0 = KFCMEUPNEUT0;
      }

      rtb_Switch1 = (uint16_T)(rtb_RelationalOperator1_0 << 7);
    }

    /* End of Switch: '<S18>/Switch1' */

    /* MinMax: '<S18>/MinMax1' incorporates:
     *  Constant: '<S18>/ONE1'
     */
    if (16384 < rtb_Switch1) {
      CmeFilterMgm_B.Merge1 = 16384U;
    } else {
      CmeFilterMgm_B.Merge1 = rtb_Switch1;
    }

    /* End of MinMax: '<S18>/MinMax1' */
    /* End of Outputs for SubSystem: '<S10>/Calc_KFiltCmeDriverP' */
  } else {
    /* Outputs for IfAction SubSystem: '<S10>/Reset_KFiltCmeDriverP' incorporates:
     *  ActionPort: '<S17>/Action Port'
     */
    /* SignalConversion generated from: '<S17>/KFiltCmeDriverI' incorporates:
     *  Constant: '<S17>/ONE1'
     */
    CmeFilterMgm_B.Merge = 16384U;

    /* SignalConversion generated from: '<S17>/KFiltCmeDriverP' incorporates:
     *  Constant: '<S17>/ONE'
     */
    CmeFilterMgm_B.Merge1 = 16384U;

    /* End of Outputs for SubSystem: '<S10>/Reset_KFiltCmeDriverP' */
  }

  /* End of If: '<S10>/If' */
}

/* System initialize for atomic system: '<S4>/Filter_RateLim' */
void CmeFilterMg_Filter_RateLim_Init(void)
{
  /* InitializeConditions for Memory: '<S25>/Memory1' */
  CmeFilterMgm_DWork.Memory1_PreviousInput = CM_INIT_HIR;

  /* InitializeConditions for Memory: '<S26>/Memory' */
  CmeFilterMgm_DWork.Memory_PreviousInput = CM_INIT_HIR;
}

/* Output and update for atomic system: '<S4>/Filter_RateLim' */
void CmeFilterMgm_Filter_RateLim(void)
{
  /* local block i/o variables */
  int32_T rtb_RateLimiter_S32;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int32_T rtb_FOF_Reset_S16_FXP_o2_b;
  int8_T i;
  int8_T k;
  int16_T rtb_DataTypeConversion2_m;
  boolean_T rtb_LogicalOperator3;
  int16_T rtb_MinMax3;
  int32_T rtb_DataTypeConversion2_l;
  int32_T rtb_Switch9;
  int32_T rtb_DataTypeConversion1_j;
  int32_T rtb_Memory;
  uint16_T rtb_Switch10;
  int16_T rtb_Switch6;
  uint8_T rtb_Switch3_j;
  boolean_T guard1 = false;
  boolean_T guard2 = false;

  /* DataTypeConversion: '<S27>/Data Type Conversion2' incorporates:
   *  Inport: '<Root>/CmeDriverPTmp'
   */
  rtb_DataTypeConversion2_l = (CmeDriverPTmp << 14);

  /* S-Function (LookUp_IR_S16): '<S45>/LookUp_IR_S16' incorporates:
   *  Constant: '<S29>/BKRPMTRQDRIV_dim'
   *  Constant: '<S29>/VTCMEDPRATESEL'
   */
  LookUp_IR_S16( &rtb_DataTypeConversion2_m, &VTCMEDPRATESEL[0],
                CmeFilterMgm_B.PreLookUpIdSearch_U16_o1,
                CmeFilterMgm_B.PreLookUpIdSearch_U16_o2, ((uint8_T)
    BKRPMTRQDRIV_dim));

  /* Chart: '<S29>/Lim_AfterChange' incorporates:
   *  Inport: '<Root>/GearPosClu'
   */
  /* Gateway: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/Lim_AfterChange */
  /* During: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/Lim_AfterChange */
  if (CmeFilterMgm_DWork.bitsForTID0.is_active_c4_CmeFilterMgm == 0U) {
    /* Entry: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/Lim_AfterChange */
    CmeFilterMgm_DWork.bitsForTID0.is_active_c4_CmeFilterMgm = 1U;

    /* Entry Internal: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_Selector/Lim_AfterChange */
    /* Transition: '<S44>:16' */
    CmeFilterMgm_B.outLimGC = 0U;
    CmeFilterMgm_B.TrLimGCTrig_j = 1U;
    CmeFilterMgm_DWork.bitsForTID0.is_c4_CmeFilterMgm =
      CmeFilterMgm_IN_LIM_IDLE_GC;
  } else {
    switch (CmeFilterMgm_DWork.bitsForTID0.is_c4_CmeFilterMgm) {
     case CmeFilterMgm_IN_LIM_CHANGE_GC:
      /* During 'LIM_CHANGE_GC': '<S44>:4' */
      if (GearPosClu != 0) {
        /* Transition: '<S44>:3' */
        CmeFilterMgm_DWork.cntlgc = 0U;
        CmeFilterMgm_DWork.bitsForTID0.is_c4_CmeFilterMgm =
          CmeFilterMgm_IN_LIM_FILT_GC;
      }
      break;

     case CmeFilterMgm_IN_LIM_FILT_GC:
      /* During 'LIM_FILT_GC': '<S44>:5' */
      if (GearPosClu == 0) {
        /* Transition: '<S44>:13' */
        CmeFilterMgm_B.outLimGC = 1U;
        CmeFilterMgm_DWork.bitsForTID0.is_c4_CmeFilterMgm =
          CmeFilterMgm_IN_LIM_CHANGE_GC;
      } else {
        /* Transition: '<S44>:23' */
        if (CmeFilterMgm_DWork.cntlgc >= TIMLIMFILTGC) {
          /* Transition: '<S44>:10' */
          CmeFilterMgm_B.outLimGC = 0U;
          CmeFilterMgm_B.TrLimGCTrig_j = 0U;
          CmeFilterMgm_DWork.bitsForTID0.is_c4_CmeFilterMgm =
            CmeFilterMgm_IN_LIM_IDLE_GC;
        } else {
          /* Transition: '<S44>:7' */
          CmeFilterMgm_DWork.cntlgc++;
        }
      }
      break;

     default:
      /* During 'LIM_IDLE_GC': '<S44>:8' */
      if (GearPosClu == 0) {
        /* Transition: '<S44>:14' */
        CmeFilterMgm_B.outLimGC = 1U;
        CmeFilterMgm_B.TrLimGCTrig_j = 1U;
        CmeFilterMgm_DWork.bitsForTID0.is_c4_CmeFilterMgm =
          CmeFilterMgm_IN_LIM_CHANGE_GC;
      } else {
        /* Transition: '<S44>:21' */
        CmeFilterMgm_B.TrLimGCTrig_j = 1U;
      }
      break;
    }
  }

  /* End of Chart: '<S29>/Lim_AfterChange' */

  /* Logic: '<S29>/Logical Operator3' incorporates:
   *  Constant: '<S29>/DRIVECHANGE'
   *  Constant: '<S29>/DRIVEUP'
   *  Constant: '<S29>/PBYACTIVE'
   *  Inport: '<Root>/FlgSpringUp'
   *  Inport: '<Root>/StTrqDriv'
   *  Logic: '<S29>/Logical Operator1'
   *  Memory: '<S29>/Memory2'
   *  RelationalOperator: '<S29>/Relational Operator'
   *  RelationalOperator: '<S29>/Relational Operator1'
   *  RelationalOperator: '<S29>/Relational Operator3'
   *  RelationalOperator: '<S29>/Relational Operator4'
   */
  rtb_LogicalOperator3 = ((((((CmeFilterMgm_DWork.Memory2_PreviousInput >=
    rtb_DataTypeConversion2_m) || (StTrqDriv != DRIVEUP)) ||
    (CmeFilterMgm_B.outLimGC != 0)) || (FlgSpringUp != 0)) && (StTrqDriv !=
    DRIVECHANGE)) && (StTrqDriv != PBYACTIVE));

  /* Switch: '<S27>/Switch9' incorporates:
   *  DataStoreRead: '<S27>/Data Store Read2'
   *  DataTypeConversion: '<S27>/Data Type Conversion5'
   *  UnitDelay: '<S27>/Unit Delay2'
   */
  if (rtb_LogicalOperator3) {
    rtb_Switch9 = CmeFilterMgm_DWork.cmedriverp_hr;
  } else {
    rtb_Switch9 = (CmeFilterMgm_DWork.UnitDelay2_DSTATE << 14);
  }

  /* End of Switch: '<S27>/Switch9' */

  /* Switch: '<S27>/Switch2' incorporates:
   *  Constant: '<S27>/MAX_INT32T_EN19'
   *  Constant: '<S27>/MIN_INT32T_EN19'
   *  DataStoreWrite: '<S4>/Data Store Write5'
   *  DataStoreWrite: '<S4>/Data Store Write6'
   *  Inport: '<Root>/FlgCmeFilt'
   *  Switch: '<S27>/Switch1'
   */
  if (FlgCmeFilt != 0) {
    rtb_DataTypeConversion1_j = CmeRateMin;
    rtb_Memory = CmeRateMax;
  } else {
    rtb_DataTypeConversion1_j = MIN_INT32T_EN19;
    rtb_Memory = MAX_INT32T_EN19;
  }

  /* End of Switch: '<S27>/Switch2' */

  /* S-Function (RateLimiter_S32): '<S38>/RateLimiter_S32' */
  RateLimiter_S32( &rtb_RateLimiter_S32, rtb_DataTypeConversion2_l, rtb_Switch9,
                  rtb_DataTypeConversion1_j, rtb_Memory);

  /* Switch: '<S27>/Switch11' incorporates:
   *  DataStoreWrite: '<S27>/Data Store Write1'
   *  DataStoreWrite: '<S4>/Data Store Write12'
   */
  if (FlgCmeDrPMaxRSat != 0) {
    CmeFilterMgm_DWork.cmedriverp_hr = rtb_RateLimiter_S32;
  } else {
    /* DataTypeConversion: '<S27>/Data Type Conversion9' */
    rtb_DataTypeConversion1_j = (CmeFilterMgm_B.LookUp_IR_S16 << 14);

    /* MinMax: '<S27>/MinMax1' incorporates:
     *  DataStoreWrite: '<S27>/Data Store Write1'
     */
    if (rtb_RateLimiter_S32 < rtb_DataTypeConversion1_j) {
      CmeFilterMgm_DWork.cmedriverp_hr = rtb_RateLimiter_S32;
    } else {
      CmeFilterMgm_DWork.cmedriverp_hr = rtb_DataTypeConversion1_j;
    }

    /* End of MinMax: '<S27>/MinMax1' */
  }

  /* End of Switch: '<S27>/Switch11' */

  /* DataTypeConversion: '<S27>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S27>/Data Store Write1'
   *  DataStoreWrite: '<S4>/Data Store Write11'
   */
  CmeDriverPRate = (int16_T)(CmeFilterMgm_DWork.cmedriverp_hr >> 14);

  /* DataTypeConversion: '<S42>/Data Type Conversion7' incorporates:
   *  Inport: '<Root>/GasPosCC'
   */
  rtb_DataTypeConversion2_m = (int16_T)(((int16_T)GasPosCC) << 1);

  /* UnitDelay: '<S42>/Unit Delay3' */
  rtb_Switch10 = CmeFilterMgm_DWork.UnitDelay3_DSTATE;

  /* Sum: '<S42>/Add' incorporates:
   *  DataTypeConversion: '<S42>/Data Type Conversion8'
   */
  rtb_MinMax3 = (int16_T)(rtb_DataTypeConversion2_m - ((int16_T)(((int16_T)
    rtb_Switch10) << 1)));

  /* DataTypeConversion: '<S42>/Data Type Conversion2' incorporates:
   *  Inport: '<Root>/GasPos0'
   */
  rtb_DataTypeConversion2_m = (int16_T)GasPos0;

  /* UnitDelay: '<S42>/Unit Delay1' */
  rtb_Switch10 = CmeFilterMgm_DWork.UnitDelay1_DSTATE_mo;

  /* Sum: '<S42>/Add1' incorporates:
   *  DataTypeConversion: '<S42>/Data Type Conversion1'
   */
  rtb_Switch6 = (int16_T)(rtb_DataTypeConversion2_m - ((int16_T)rtb_Switch10));

  /* Switch: '<S42>/Switch' incorporates:
   *  RelationalOperator: '<S42>/Relational Operator'
   */
  if (rtb_MinMax3 <= rtb_Switch6) {
    rtb_DataTypeConversion2_m = rtb_MinMax3;
  } else {
    rtb_DataTypeConversion2_m = rtb_Switch6;
  }

  /* End of Switch: '<S42>/Switch' */

  /* Chart: '<S28>/GasResp_calc' incorporates:
   *  Inport: '<Root>/CmeDriver'
   *  Inport: '<Root>/FlgSpringUp'
   *  Inport: '<Root>/GearPosClu'
   *  Inport: '<Root>/StDiag'
   *  UnitDelay: '<S28>/Unit Delay1'
   */
  /* Gateway: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/GasResp_calc */
  /* During: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/GasResp_calc */
  if (CmeFilterMgm_DWork.bitsForTID0.is_active_c3_CmeFilterMgm == 0U) {
    /* Entry: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/GasResp_calc */
    CmeFilterMgm_DWork.bitsForTID0.is_active_c3_CmeFilterMgm = 1U;

    /* Entry Internal: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriver_RateGain/GasResp_calc */
    /* Transition: '<S40>:10' */
    CmeFilterMgm_B.outFlgKfCmeDGResp = 0U;
    CmeFilterMgm_B.outGainGR = ((uint16_T)G1_UINT16T_EN7);
    CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm =
      CmeFilterMgm_IN_IDLE_GAS_RESP;
  } else {
    switch (CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm) {
     case CmeFilter_IN_FILT_DOWN_GAS_RESP:
      /* During 'FILT_DOWN_GAS_RESP': '<S40>:40' */
      /* Transition: '<S40>:44' */
      if ((CmeFilterMgm_DWork.UnitDelay1_DSTATE <= (CmeDriver + THCMEDRVCIPF)) ||
          (((GearPosClu != 0) && (FlgSpringUp == 0)) || (StDiag[(DIAG_VEHSPEED)]
            == FAULT))) {
        /* Transition: '<S40>:42' */
        CmeFilterMgm_B.outFlgKfCmeDGResp = 0U;
        CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm =
          CmeFilterMgm_IN_IDLE_GAS_RESP;
      } else {
        /* Transition: '<S40>:58' */
      }
      break;

     case CmeFilterMgm_IN_IDLE_GAS_RESP:
      /* During 'IDLE_GAS_RESP': '<S40>:1' */
      /* Transition: '<S40>:6' */
      if ((((GearPosClu == 0) || (FlgSpringUp != 0)) &&
           (rtb_DataTypeConversion2_m > THDGASRESP)) && (StDiag[(DIAG_VEHSPEED)]
           != FAULT)) {
        /* Transition: '<S40>:4' */
        CmeFilterMgm_B.outGainGR = CMEGAINGASRESP;
        CmeFilterMgm_DWork.cntgr = 0U;
        CmeFilterMgm_B.ountFlgGainAct = 1U;
        CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm =
          CmeFilterMgm_IN_RUN_GAS_RESP;
      } else {
        /* Transition: '<S40>:12' */
      }
      break;

     default:
      /* During 'RUN_GAS_RESP': '<S40>:5' */
      if (((GearPosClu != 0) && (FlgSpringUp == 0)) || (StDiag[(DIAG_VEHSPEED)] ==
           FAULT)) {
        /* Transition: '<S40>:7' */
        CmeFilterMgm_B.outGainGR = ((uint16_T)G1_UINT16T_EN7);
        CmeFilterMgm_B.ountFlgGainAct = 0U;
        CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm =
          CmeFilterMgm_IN_IDLE_GAS_RESP;
      } else {
        /* Transition: '<S40>:39' */
        if ((rtb_DataTypeConversion2_m + HYSDGASRESP) <= THDGASRESP) {
          /* Transition: '<S40>:13' */
          CmeFilterMgm_DWork.cntgr++;
        } else {
          /* Transition: '<S40>:16' */
          CmeFilterMgm_DWork.cntgr = 0U;
        }

        if (CmeFilterMgm_DWork.cntgr >= TIMGASRESP) {
          /* Transition: '<S40>:18' */
          CmeFilterMgm_B.outGainGR = ((uint16_T)G1_UINT16T_EN7);
          CmeFilterMgm_B.ountFlgGainAct = 0U;
          if (ENFDGASRESP == 0) {
            /* Transition: '<S40>:52' */
            CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm =
              CmeFilterMgm_IN_IDLE_GAS_RESP;
          } else {
            /* Transition: '<S40>:57' */
            CmeFilterMgm_B.outFlgKfCmeDGResp = 1U;
            CmeFilterMgm_DWork.bitsForTID0.is_c3_CmeFilterMgm =
              CmeFilter_IN_FILT_DOWN_GAS_RESP;
          }
        } else {
          /* Transition: '<S40>:19' */
        }
      }
      break;
    }
  }

  /* End of Chart: '<S28>/GasResp_calc' */

  /* DataTypeConversion: '<S28>/Data Type Conversion4' */
  rtb_Memory = (CmeFilterMgm_B.outGainGR << 12);

  /* DataStoreRead: '<S28>/Data Store Read6' */
  rtb_DataTypeConversion2_l = CmeFilterMgm_DWork.grGain_hr;

  /* DataTypeConversion: '<S28>/Data Type Conversion1' incorporates:
   *  Constant: '<S28>/CMERATEMINGASRESP'
   */
  rtb_DataTypeConversion1_j = (CMERATEMINGASRESP << 10);

  /* S-Function (RateLimiter_S32): '<S41>/RateLimiter_S32' incorporates:
   *  Constant: '<S28>/MAX_INT32T_EN2'
   */
  RateLimiter_S32( &rtb_Memory, rtb_Memory, rtb_DataTypeConversion2_l,
                  rtb_DataTypeConversion1_j, MAX_INT32T_EN19);

  /* DataStoreWrite: '<S28>/Data Store Write4' */
  CmeFilterMgm_DWork.grGain_hr = rtb_Memory;

  /* DataTypeConversion: '<S28>/Data Type Conversion3' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write4'
   */
  GRGainLim = (uint16_T)(rtb_Memory >> 12);

  /* Product: '<S28>/Product' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write4'
   *  Inport: '<Root>/CmeDriver'
   */
  rtb_Memory = GRGainLim * CmeDriver;

  /* DataTypeConversion: '<S28>/Data Type Conversion6' */
  rtb_MinMax3 = (int16_T)(rtb_Memory >> 7);

  /* Logic: '<S28>/Logical Operator' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write23'
   */
  FlgPhGain = (uint8_T)((CmeFilterMgm_B.outFlgKfCmeDGResp != 0) ||
                        (CmeFilterMgm_B.ountFlgGainAct != 0));

  /* Switch: '<S25>/Switch10' incorporates:
   *  Constant: '<S25>/KFCMEDRVGASRESP'
   */
  if (CmeFilterMgm_B.outFlgKfCmeDGResp != 0) {
    rtb_Switch10 = KFCMEDRVGASRESP;
  } else {
    rtb_Switch10 = CmeFilterMgm_B.Merge;
  }

  /* End of Switch: '<S25>/Switch10' */

  /* UnitDelay: '<S25>/Unit Delay1' */
  rtb_Switch6 = CmeFilterMgm_DWork.UnitDelay1_DSTATE_m;

  /* UnitDelay: '<S25>/Unit Delay' */
  rtb_Switch3_j = CmeFilterMgm_DWork.UnitDelay_DSTATE;

  /* Switch: '<S25>/Switch8' */
  if (rtb_Switch3_j == 0) {
    rtb_Switch6 = rtb_MinMax3;
  }

  /* End of Switch: '<S25>/Switch8' */

  /* Memory: '<S25>/Memory1' */
  rtb_Memory = CmeFilterMgm_DWork.Memory1_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S34>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &CmeFilterMgm_B.FOF_Reset_S16_FXP_o1,
                    &rtb_FOF_Reset_S16_FXP_o2, rtb_MinMax3, rtb_Switch10,
                    rtb_Switch6, rtb_Switch3_j, rtb_Memory);

  /* MinMax: '<S24>/MinMax' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write11'
   */
  if (CmeFilterMgm_B.FOF_Reset_S16_FXP_o1 < CmeDriverPRate) {
    rtb_DataTypeConversion2_m = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1;
  } else {
    rtb_DataTypeConversion2_m = CmeDriverPRate;
  }

  /* End of MinMax: '<S24>/MinMax' */

  /* Chart: '<S24>/TrigResFilter' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write11'
   *  Logic: '<S24>/Logical Operator2'
   *  RelationalOperator: '<S24>/Relational Operator2'
   *  RelationalOperator: '<S31>/Compare'
   */
  /* Gateway: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigResFilter */
  /* During: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigResFilter */
  if (CmeFilterMgm_DWork.bitsForTID0.is_active_c1_CmeFilterMgm == 0U) {
    /* Entry: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigResFilter */
    CmeFilterMgm_DWork.bitsForTID0.is_active_c1_CmeFilterMgm = 1U;

    /* Entry Internal: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigResFilter */
    /* Transition: '<S33>:2' */
    CmeFilterMgm_B.FlgResCmeDriverI_b = 0U;
    CmeFilterMgm_DWork.bitsForTID0.is_c1_CmeFilterMgm =
      CmeFi_IN_ST_NO_RES_CME_DRIVER_I;
  } else if (CmeFilterMgm_DWork.bitsForTID0.is_c1_CmeFilterMgm ==
             CmeFi_IN_ST_NO_RES_CME_DRIVER_I) {
    /* During 'ST_NO_RES_CME_DRIVER_I': '<S33>:1' */
    if ((!rtb_LogicalOperator3) && (rtb_DataTypeConversion2_m == CmeDriverPRate))
    {
      /* Transition: '<S33>:4' */
      CmeFilterMgm_B.FlgResCmeDriverI_b = 1U;
      CmeFilterMgm_DWork.bitsForTID0.is_c1_CmeFilterMgm =
        CmeFilte_IN_ST_RES_CME_DRIVER_I;
    } else {
      /* Transition: '<S33>:10' */
      CmeFilterMgm_B.FlgResCmeDriverI_b = 0U;
    }
  } else {
    /* During 'ST_RES_CME_DRIVER_I': '<S33>:3' */
    if (rtb_LogicalOperator3 || (rtb_MinMax3 <
         CmeFilterMgm_B.FOF_Reset_S16_FXP_o1)) {
      /* Transition: '<S33>:5' */
      CmeFilterMgm_B.FlgResCmeDriverI_b = 1U;
      CmeFilterMgm_DWork.bitsForTID0.is_c1_CmeFilterMgm =
        CmeFi_IN_ST_NO_RES_CME_DRIVER_I;
    } else {
      /* Transition: '<S33>:9' */
      CmeFilterMgm_B.FlgResCmeDriverI_b = 0U;
    }
  }

  /* End of Chart: '<S24>/TrigResFilter' */

  /* Switch: '<S24>/Switch5' incorporates:
   *  Switch: '<S24>/Switch1'
   */
  if ((CmeFilterMgm_B.FlgResCmeDriverI_b == 0) && rtb_LogicalOperator3) {
    rtb_DataTypeConversion2_m = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1;
  }

  /* End of Switch: '<S24>/Switch5' */

  /* Chart: '<S24>/TrigCL' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write12'
   *  DataStoreWrite: '<S4>/Data Store Write15'
   *  DataStoreWrite: '<S4>/Data Store Write23'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/FlgSpringUp'
   *  Inport: '<Root>/GearPosClu'
   *  Inport: '<Root>/IdleFlg'
   *  Inport: '<Root>/StQShift'
   *  Inport: '<Root>/StTrqDriv'
   */
  /* Gateway: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigCL */
  /* During: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigCL */
  if (CmeFilterMgm_DWork.bitsForTID0.is_active_c6_CmeFilterMgm == 0U) {
    /* Entry: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigCL */
    CmeFilterMgm_DWork.bitsForTID0.is_active_c6_CmeFilterMgm = 1U;

    /* Entry Internal: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/TrigCL */
    /* Transition: '<S32>:3' */
    CmeFilterMgm_B.FlgCmeTrigCL = 0U;
    CmeFilterMgm_B.FlgCmeExitCL = 0U;
    CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
      CmeFilterMgm_IN_WAIT_DU_CL;

    /* Entry 'WAIT_DU_CL': '<S32>:1' */
    CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_CL);
  } else {
    guard1 = false;
    guard2 = false;
    switch (CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm) {
     case CmeFilterMgm_IN_IN_DU_CHANGE:
      CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CHANGE);

      /* During 'IN_DU_CHANGE': '<S32>:56' */
      /* Transition: '<S32>:112' */
      if ((((FlgPhGain == 1) || (IdleFlg == 1)) || (FlgSpringUp == 1)) ||
          (EndStartFlg == 0)) {
        /* Transition: '<S32>:59' */
        CmeFilterMgm_B.FlgCmeTrigCL = 0U;

        /* Transition: '<S32>:116' */
        /* Transition: '<S32>:115' */
        guard2 = true;
      } else {
        /* Transition: '<S32>:88' */
        if ((GearPosClu != CmeFilterMgm_DWork.oldGearPosClu) || (GearPosClu == 0))
        {
          /* Transition: '<S32>:89' */
          CmeFilterMgm_DWork.cntDuChange = 0U;
        } else {
          /* Transition: '<S32>:62' */
        }

        if (CmeFilterMgm_DWork.cntDuChange >= CNTDUCHANGE) {
          /* Transition: '<S32>:63' */
          if ((((!rtb_LogicalOperator3) && (FlgCmeDrPMaxRSat == 1)) &&
               (FlgCmeDriverPHiRpm == 0)) && (StTrqDriv != DRIVEDOWN)) {
            /* Transition: '<S32>:58' */
            CmeFilterMgm_B.FlgCmeExitCL = 0U;
            CmeFilterMgm_DWork.oldGearPosClu = GearPosClu;

            /* Transition: '<S32>:110' */
            CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
              CmeFilterMgm_IN_IN_DU_CL;

            /* Entry 'IN_DU_CL': '<S32>:4' */
            CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CL);
          } else {
            /* Transition: '<S32>:65' */
            CmeFilterMgm_B.FlgCmeTrigCL = 0U;
            guard2 = true;
          }
        } else {
          /* Transition: '<S32>:64' */
          CmeFilterMgm_DWork.oldGearPosClu = GearPosClu;
          rtb_DataTypeConversion2_l = CmeFilterMgm_DWork.cntDuChange + 1;
          if (rtb_DataTypeConversion2_l > 255) {
            rtb_DataTypeConversion2_l = 255;
          }

          CmeFilterMgm_DWork.cntDuChange = (uint8_T)rtb_DataTypeConversion2_l;
        }
      }
      break;

     case CmeFilterMgm_IN_IN_DU_CL:
      CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CL);

      /* During 'IN_DU_CL': '<S32>:4' */
      /* Transition: '<S32>:16' */
      if ((((FlgPhGain == 1) || (IdleFlg == 1)) || (FlgSpringUp == 1)) ||
          (EndStartFlg == 0)) {
        /* Transition: '<S32>:48' */
        CmeFilterMgm_B.FlgCmeTrigCL = 0U;
        CmeFilterMgm_B.FlgCmeExitCL = 1U;
        CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
          CmeFilterMgm_IN_WAIT_DU_CL;

        /* Entry 'WAIT_DU_CL': '<S32>:1' */
        CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_CL);
      } else {
        /* Transition: '<S32>:93' */
        if (((GearPosClu != CmeFilterMgm_DWork.oldGearPosClu) || (GearPosClu ==
              0)) && (StQShift != QSHIFT_CTF_WAIT)) {
          /* Transition: '<S32>:57' */
          CmeFilterMgm_B.FlgCmeExitCL = 1U;
          CmeFilterMgm_DWork.cntDuChange = 0U;

          /* Transition: '<S32>:95' */
          CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
            CmeFilterMgm_IN_IN_DU_CHANGE;

          /* Entry 'IN_DU_CHANGE': '<S32>:56' */
          CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CHANGE);
        } else {
          /* Transition: '<S32>:32' */
          if (((FlgCmeDrPMaxRSat == 0) || (FlgCmeDriverPHiRpm == 1)) ||
              (StQShift == QSHIFT_CTF_WAIT)) {
            /* Transition: '<S32>:6' */
            CmeFilterMgm_B.FlgCmeTrigCL = 0U;
            CmeFilterMgm_DWork.oldGearPosClu = GearPosClu;
            CmeFilterMgm_B.flgQShift = 0U;
            CmeFilterMgm_DWork.flgQShiftExit = 0U;

            /* Transition: '<S32>:97' */
            CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
              CmeFilterMgm_IN_WAIT_DU_EXIT;

            /* Entry 'WAIT_DU_EXIT': '<S32>:46' */
            CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_EXIT);
          } else {
            /* Transition: '<S32>:83' */
            CmeFilterMgm_DWork.oldGearPosClu = GearPosClu;
          }
        }
      }
      break;

     case CmeFilterMgm_IN_WAIT_DU_CL:
      CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_CL);

      /* During 'WAIT_DU_CL': '<S32>:1' */
      /* Transition: '<S32>:17' */
      if (((((((((!rtb_LogicalOperator3) && (FlgCmeDrPMaxRSat == 1)) &&
                (FlgCmeDriverPHiRpm == 0)) && (StTrqDriv == DRIVEUP)) &&
              (FlgPhGain == 0)) && (IdleFlg == 0)) && (FlgSpringUp == 0)) &&
           (EndStartFlg == 1)) || (StQShift == QSHIFT_CTF_WAIT)) {
        /* Transition: '<S32>:8' */
        CmeFilterMgm_B.FlgCmeTrigCL = 1U;
        CmeFilterMgm_B.FlgCmeExitCL = 0U;
        CmeFilterMgm_DWork.oldGearPosClu = GearPosClu;
        CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
          CmeFilterMgm_IN_IN_DU_CL;

        /* Entry 'IN_DU_CL': '<S32>:4' */
        CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CL);
      } else {
        /* Transition: '<S32>:31' */
      }
      break;

     default:
      CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_EXIT);

      /* During 'WAIT_DU_EXIT': '<S32>:46' */
      /* Transition: '<S32>:104' */
      if (((((FlgPhGain == 1) || (IdleFlg == 1)) || (FlgSpringUp == 1)) ||
           (EndStartFlg == 0)) && (StQShift != QSHIFT_CTF_WAIT)) {
        /* Transition: '<S32>:51' */
        CmeFilterMgm_B.FlgCmeExitCL = 1U;

        /* Transition: '<S32>:130' */
        /* Transition: '<S32>:102' */
        guard1 = true;
      } else {
        /* Transition: '<S32>:118' */
        if (StQShift == QSHIFT_CTF_WAIT) {
          /* Transition: '<S32>:119' */
          CmeFilterMgm_B.flgQShift = 1U;
        } else {
          /* Transition: '<S32>:52' */
        }

        /* Transition: '<S32>:128' */
        if ((CmeFilterMgm_B.flgQShift == 0) && (StQShift == QSHIFT_DISABLE)) {
          /* Transition: '<S32>:132' */
          CmeFilterMgm_DWork.flgQShiftExit = 1U;
        } else {
          /* Transition: '<S32>:133' */
        }

        /* Transition: '<S32>:135' */
        if (((CmeFilterMgm_DWork.flgQShiftExit != 0) && (StQShift !=
              QSHIFT_DISABLE)) && (StCmeICL != ((uint8_T)CME_I_CL))) {
          /* Transition: '<S32>:138' */
          CmeFilterMgm_B.FlgCmeTrigCL = 0U;
          CmeFilterMgm_B.FlgCmeExitCL = 1U;

          /* Transition: '<S32>:126' */
          /* Transition: '<S32>:99' */
          guard1 = true;
        } else {
          /* Transition: '<S32>:137' */
          if ((((GearPosClu != CmeFilterMgm_DWork.oldGearPosClu) || (GearPosClu ==
                 0)) && (CmeFilterMgm_B.flgQShift == 0)) ||
              ((CmeFilterMgm_B.flgQShift != 0) && (StQShift == QSHIFT_DISABLE)))
          {
            /* Transition: '<S32>:84' */
            CmeFilterMgm_DWork.cntDuChange = 0U;
            if (CmeFilterMgm_B.flgQShift == 0) {
              /* Transition: '<S32>:124' */
              CmeFilterMgm_B.FlgCmeTrigCL = 1U;
              CmeFilterMgm_B.FlgCmeExitCL = 1U;
              CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
                CmeFilterMgm_IN_IN_DU_CHANGE;

              /* Entry 'IN_DU_CHANGE': '<S32>:56' */
              CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CHANGE);
            } else {
              /* Transition: '<S32>:106' */
              CmeFilterMgm_B.FlgCmeTrigCL = 0U;

              /* Transition: '<S32>:140' */
              /* Transition: '<S32>:126' */
              /* Transition: '<S32>:99' */
              guard1 = true;
            }
          } else {
            /* Transition: '<S32>:86' */
            if (((((!rtb_LogicalOperator3) && (FlgCmeDrPMaxRSat == 1)) &&
                  (FlgCmeDriverPHiRpm == 0)) && (StTrqDriv == DRIVEUP)) &&
                (StQShift != QSHIFT_CTF_WAIT)) {
              /* Transition: '<S32>:54' */
              CmeFilterMgm_B.FlgCmeTrigCL = 1U;
              CmeFilterMgm_DWork.oldGearPosClu = GearPosClu;

              /* Transition: '<S32>:108' */
              CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
                CmeFilterMgm_IN_IN_DU_CL;

              /* Entry 'IN_DU_CL': '<S32>:4' */
              CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)IN_DU_CL);
            } else {
              /* Transition: '<S32>:53' */
            }
          }
        }
      }
      break;
    }

    if (guard2) {
      /* Transition: '<S32>:67' */
      /* Transition: '<S32>:140' */
      /* Transition: '<S32>:126' */
      /* Transition: '<S32>:99' */
      CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
        CmeFilterMgm_IN_WAIT_DU_CL;

      /* Entry 'WAIT_DU_CL': '<S32>:1' */
      CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_CL);
    }

    if (guard1) {
      CmeFilterMgm_DWork.bitsForTID0.is_c6_CmeFilterMgm =
        CmeFilterMgm_IN_WAIT_DU_CL;

      /* Entry 'WAIT_DU_CL': '<S32>:1' */
      CmeFilterMgm_B.StCmeITrCL_o = ((uint8_T)WAIT_DU_CL);
    }
  }

  /* End of Chart: '<S24>/TrigCL' */

  /* Switch: '<S24>/Switch' incorporates:
   *  Constant: '<S24>/VTKCMEIVSRB'
   *  Constant: '<S24>/VTKCMEIVSRBQS'
   *  Inport: '<Root>/GearPosClu'
   *  Selector: '<S24>/Selector'
   *  Selector: '<S24>/Selector1'
   */
  if (CmeFilterMgm_B.flgQShift != 0) {
    rtb_MinMax3 = VTKCMEIVSRBQS[GearPosClu - 1];
  } else {
    rtb_MinMax3 = VTKCMEIVSRB[GearPosClu - 1];
  }

  /* End of Switch: '<S24>/Switch' */

  /* Chart: '<S24>/CmeDriverI_Offset' incorporates:
   *  Inport: '<Root>/DVehSpeedRearRb'
   *  Inport: '<Root>/StDiag'
   */
  /* Gateway: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/CmeDriverI_Offset */
  /* During: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/CmeDriverI_Offset */
  if (CmeFilterMgm_DWork.bitsForTID0.is_active_c10_CmeFilterMgm == 0U) {
    /* Entry: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/CmeDriverI_Offset */
    CmeFilterMgm_DWork.bitsForTID0.is_active_c10_CmeFilterMgm = 1U;

    /* Entry Internal: CmeFilterMgm/T10ms/Filter_RateLim/CmeDriverI_CL_Selector/CmeDriverI_Offset */
    /* Transition: '<S30>:2' */
    CmeFilterMgm_B.CmeDriverICLOff = 0;
    CmeFilterMgm_B.CmeDriverICLOffOut_l = 0;
    CmeFilterMgm_B.CntCDICLOff_n = 0U;
    CmeFilterMgm_DWork.bitsForTID0.is_c10_CmeFilterMgm =
      CmeFilterMgm_IN_CME_I_INIT;

    /* Entry 'CME_I_INIT': '<S30>:77' */
    CmeFilterMgm_B.StCmeICL_i = ((uint8_T)CME_I_INIT);
  } else {
    switch (CmeFilterMgm_DWork.bitsForTID0.is_c10_CmeFilterMgm) {
     case CmeFilterMgm_IN_CME_I_CL:
      CmeFilterMgm_B.StCmeICL_i = ((uint8_T)CME_I_CL);

      /* Switch: '<S24>/Switch2' incorporates:
       *  Constant: '<S24>/TIMENDCIOL'
       *  Constant: '<S24>/TIMENDCIOLQS'
       */
      /* During 'CME_I_CL': '<S30>:3' */
      if (CmeFilterMgm_B.flgQShift != 0) {
        rtb_Switch10 = TIMENDCIOLQS;
      } else {
        rtb_Switch10 = TIMENDCIOL;
      }

      /* End of Switch: '<S24>/Switch2' */
      if (((CmeFilterMgm_B.CntCDICLOff_n >= rtb_Switch10) ||
           (CmeFilterMgm_B.FlgCmeExitCL == 1)) || ((StDiag[(DIAG_VEHSPEED)] ==
            FAULT) || (StDiag[(DIAG_VEH_CAN_NODE_3)] == FAULT))) {
        /* Transition: '<S30>:7' */
        CmeFilterMgm_B.CmeDriverICLOff = 0;
        CmeFilterMgm_B.CmeDriverICLOffOut_l = 0;
        CmeFilterMgm_B.CntCDICLOff_n = 0U;
        i = 0;
        while ((i < ((int8_T)DIM_CMEI_WAVE_BUFF)) && (i <= (DIMCMEIWB + 1))) {
          /* Transition: '<S30>:75' */
          CmeIBuffCLOff[(i)] = 0;
          i++;
        }

        /* Transition: '<S30>:76' */
        CmeFilterMgm_DWork.bitsForTID0.is_c10_CmeFilterMgm =
          CmeFilterMgm_IN_CME_I_OL;

        /* Entry 'CME_I_OL': '<S30>:1' */
        CmeFilterMgm_B.StCmeICL_i = ((uint8_T)CME_I_OL);
      } else {
        /* Transition: '<S30>:5' */
        rtb_DataTypeConversion2_l = ((DVehSpeedRearRb * rtb_MinMax3) >> 5);
        if (rtb_DataTypeConversion2_l > 32767) {
          rtb_DataTypeConversion2_l = 32767;
        } else {
          if (rtb_DataTypeConversion2_l < -32768) {
            rtb_DataTypeConversion2_l = -32768;
          }
        }

        CmeFilterMgm_B.CmeDriverICLOff = (int16_T)rtb_DataTypeConversion2_l;
        CmeIBuffCLOff[0] = CmeFilterMgm_B.CmeDriverICLOff;
        rtb_DataTypeConversion2_l = DIMCMEIWB + 1;
        if (rtb_DataTypeConversion2_l > 127) {
          rtb_DataTypeConversion2_l = 127;
        }

        if (CmeFilterMgm_B.FlgCmeTrigCL == 0) {
          /* Transition: '<S30>:85' */
          rtb_Switch9 = CmeFilterMgm_B.CntCDICLOff_n + 1;
          if (rtb_Switch9 > 65535) {
            rtb_Switch9 = 65535;
          }

          CmeFilterMgm_B.CntCDICLOff_n = (uint16_T)rtb_Switch9;
        } else {
          /* Transition: '<S30>:84' */
          CmeFilterMgm_B.CntCDICLOff_n = 0U;
        }

        rtb_Switch9 = ((int8_T)DIM_CMEI_WAVE_BUFF) - 1;
        if (((int8_T)rtb_DataTypeConversion2_l) >= rtb_Switch9) {
          /* Transition: '<S30>:71' */
          if (rtb_Switch9 < -128) {
            rtb_Switch9 = -128;
          }

          i = (int8_T)rtb_Switch9;
          k = 0;
        } else {
          /* Transition: '<S30>:70' */
          i = (int8_T)rtb_DataTypeConversion2_l;
          k = 0;
        }

        while (i > 0) {
          /* Transition: '<S30>:59' */
          rtb_DataTypeConversion2_l = i - 1;
          CmeIBuffCLOff[(i)] = CmeIBuffCLOff[(rtb_DataTypeConversion2_l)];
          i = (int8_T)rtb_DataTypeConversion2_l;
          rtb_DataTypeConversion2_l = k + 1;
          if (rtb_DataTypeConversion2_l > 127) {
            rtb_DataTypeConversion2_l = 127;
          }

          k = (int8_T)rtb_DataTypeConversion2_l;
        }

        /* Transition: '<S30>:61' */
        CmeFilterMgm_B.CmeDriverICLOffOut_l = CmeIBuffCLOff[(k)];
      }
      break;

     case CmeFilterMgm_IN_CME_I_INIT:
      /* During 'CME_I_INIT': '<S30>:77' */
      /* Transition: '<S30>:78' */
      i = 0;
      while ((i < ((int8_T)DIM_CMEI_WAVE_BUFF)) && (i <= (DIMCMEIWB + 1))) {
        /* Transition: '<S30>:80' */
        CmeIBuffCLOff[(i)] = 0;
        i++;
      }

      /* Transition: '<S30>:81' */
      CmeFilterMgm_DWork.bitsForTID0.is_c10_CmeFilterMgm =
        CmeFilterMgm_IN_CME_I_OL;

      /* Entry 'CME_I_OL': '<S30>:1' */
      CmeFilterMgm_B.StCmeICL_i = ((uint8_T)CME_I_OL);
      break;

     default:
      CmeFilterMgm_B.StCmeICL_i = ((uint8_T)CME_I_OL);

      /* During 'CME_I_OL': '<S30>:1' */
      /* Transition: '<S30>:82' */
      if (((CmeFilterMgm_B.FlgCmeTrigCL == 1) && (CmeFilterMgm_B.FlgCmeExitCL ==
            0)) && ((StDiag[(DIAG_VEHSPEED)] != FAULT) && (StDiag
            [(DIAG_VEH_CAN_NODE_3)] != FAULT))) {
        /* Transition: '<S30>:4' */
        CmeFilterMgm_DWork.bitsForTID0.is_c10_CmeFilterMgm =
          CmeFilterMgm_IN_CME_I_CL;

        /* Entry 'CME_I_CL': '<S30>:3' */
        CmeFilterMgm_B.StCmeICL_i = ((uint8_T)CME_I_CL);
      } else {
        /* Transition: '<S30>:68' */
      }
      break;
    }
  }

  /* End of Chart: '<S24>/CmeDriverI_Offset' */

  /* Saturate: '<S24>/Saturation' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write20'
   *
   * Block description for '<S24>/Saturation':
   *  MINCMEIOFFSAT
   *  MAXCMEIOFFSAT
   */
  if (CmeFilterMgm_B.CmeDriverICLOffOut_l > MAXCMEIOFFSAT) {
    CmeDriverICLOffOut = MAXCMEIOFFSAT;
  } else if (CmeFilterMgm_B.CmeDriverICLOffOut_l < MINCMEIOFFSAT) {
    CmeDriverICLOffOut = MINCMEIOFFSAT;
  } else {
    CmeDriverICLOffOut = CmeFilterMgm_B.CmeDriverICLOffOut_l;
  }

  /* End of Saturate: '<S24>/Saturation' */

  /* Sum: '<S24>/Add' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write20'
   */
  rtb_Switch6 = (int16_T)(rtb_DataTypeConversion2_m + CmeDriverICLOffOut);

  /* MinMax: '<S24>/MinMax3' incorporates:
   *  Inport: '<Root>/CmeDriverPTmp'
   */
  if (CmeDriverPTmp > rtb_DataTypeConversion2_m) {
    rtb_MinMax3 = CmeDriverPTmp;
  } else {
    rtb_MinMax3 = rtb_DataTypeConversion2_m;
  }

  /* End of MinMax: '<S24>/MinMax3' */

  /* MinMax: '<S24>/MinMax2' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write1'
   */
  if (rtb_MinMax3 < rtb_Switch6) {
    CmeDriverI = rtb_MinMax3;
  } else {
    CmeDriverI = rtb_Switch6;
  }

  /* End of MinMax: '<S24>/MinMax2' */

  /* Switch: '<S26>/Switch6' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write11'
   *  Inport: '<Root>/CmeDriverPTmp'
   */
  if (rtb_LogicalOperator3) {
    rtb_Switch6 = CmeDriverPTmp;
  } else {
    rtb_Switch6 = CmeDriverPRate;
  }

  /* End of Switch: '<S26>/Switch6' */

  /* Switch: '<S26>/Switch3' incorporates:
   *  Inport: '<Root>/FlgCmeFilt'
   *  Logic: '<S26>/Logical Operator'
   */
  rtb_Switch3_j = (uint8_T)((FlgCmeFilt == 0) || (!rtb_LogicalOperator3));

  /* Memory: '<S26>/Memory' */
  rtb_Memory = CmeFilterMgm_DWork.Memory_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S36>/FOF_Reset_S16_FXP' incorporates:
   *  Inport: '<Root>/CmeDriverPTmp'
   */
  FOF_Reset_S16_FXP( &CmeFilterMgm_B.FOF_Reset_S16_FXP_o1_a,
                    &rtb_FOF_Reset_S16_FXP_o2_b, CmeDriverPTmp,
                    CmeFilterMgm_B.Merge1, rtb_Switch6, rtb_Switch3_j,
                    rtb_Memory);

  /* Switch: '<S29>/Switch4' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write'
   *  DataStoreWrite: '<S4>/Data Store Write11'
   */
  if (rtb_LogicalOperator3) {
    CmeDriverP = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1_a;
  } else {
    CmeDriverP = CmeDriverPRate;
  }

  /* End of Switch: '<S29>/Switch4' */

  /* DataTypeConversion: '<S11>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write9'
   */
  FlgCmeDriverP = (uint8_T)rtb_LogicalOperator3;

  /* Update for Memory: '<S29>/Memory2' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write'
   */
  CmeFilterMgm_DWork.Memory2_PreviousInput = CmeDriverP;

  /* Update for UnitDelay: '<S27>/Unit Delay2' */
  CmeFilterMgm_DWork.UnitDelay2_DSTATE = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1_a;

  /* Update for UnitDelay: '<S42>/Unit Delay3' incorporates:
   *  Inport: '<Root>/GasPosCC'
   */
  CmeFilterMgm_DWork.UnitDelay3_DSTATE = GasPosCC;

  /* Update for UnitDelay: '<S42>/Unit Delay1' incorporates:
   *  Inport: '<Root>/GasPos0'
   */
  CmeFilterMgm_DWork.UnitDelay1_DSTATE_mo = GasPos0;

  /* Update for UnitDelay: '<S28>/Unit Delay1' */
  CmeFilterMgm_DWork.UnitDelay1_DSTATE = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1;

  /* Update for UnitDelay: '<S25>/Unit Delay1' */
  CmeFilterMgm_DWork.UnitDelay1_DSTATE_m = rtb_DataTypeConversion2_m;

  /* Update for UnitDelay: '<S25>/Unit Delay' */
  CmeFilterMgm_DWork.UnitDelay_DSTATE = CmeFilterMgm_B.FlgResCmeDriverI_b;

  /* Update for Memory: '<S25>/Memory1' */
  CmeFilterMgm_DWork.Memory1_PreviousInput = rtb_FOF_Reset_S16_FXP_o2;

  /* Update for Memory: '<S26>/Memory' */
  CmeFilterMgm_DWork.Memory_PreviousInput = rtb_FOF_Reset_S16_FXP_o2_b;
}

/* System initialize for atomic system: '<S4>/RateLim_Calculation' */
void CmeFil_RateLim_Calculation_Init(void)
{
  int32_T tmp;

  /* InitializeConditions for Memory: '<S47>/Memory' */
  tmp = ((CM_INIT_HIR & 8192U) != 0U) + (CM_INIT_HIR >> 14);
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  CmeFilterMgm_DWork.Memory_PreviousInput_j = (int16_T)tmp;

  /* End of InitializeConditions for Memory: '<S47>/Memory' */
}

/* Output and update for atomic system: '<S4>/RateLim_Calculation' */
void CmeFilterMg_RateLim_Calculation(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_S16_S16;
  boolean_T rtb_RelationalOperator_p;
  boolean_T rtb_RelationalOperator4_m;
  int16_T rtb_Switch5_m;
  int16_T rtb_Switch1[6];
  int32_T i;

  /* S-Function (LookUp_IR_S16): '<S50>/LookUp_IR_S16' incorporates:
   *  Constant: '<S47>/BKRPMTRQDRIV_dim'
   *  Constant: '<S47>/VTRTMAXLORPMCP'
   */
  LookUp_IR_S16( &CmeFilterMgm_B.LookUp_IR_S16, &VTRTMAXLORPMCP[0],
                CmeFilterMgm_B.PreLookUpIdSearch_U16_o1,
                CmeFilterMgm_B.PreLookUpIdSearch_U16_o2, ((uint8_T)
    BKRPMTRQDRIV_dim));

  /* Sum: '<S47>/Add' incorporates:
   *  Constant: '<S47>/OFFRTMAXLORPMCP'
   *  DataStoreWrite: '<S4>/Data Store Write16'
   */
  CmeDriverPOffMaxRSat = (int16_T)(CmeFilterMgm_B.LookUp_IR_S16 +
    OFFRTMAXLORPMCP);

  /* RelationalOperator: '<S47>/Relational Operator' incorporates:
   *  Memory: '<S47>/Memory'
   */
  rtb_RelationalOperator_p = (CmeFilterMgm_DWork.Memory_PreviousInput_j >=
    CmeFilterMgm_B.LookUp_IR_S16);

  /* Chart: '<S47>/Calc_flgCmeDrPMaxRSat' incorporates:
   *  DataStoreRead: '<S47>/Data Store Read5'
   *  Logic: '<S47>/Logical Operator2'
   *  RelationalOperator: '<S47>/Relational Operator2'
   */
  /* Gateway: CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/Calc_flgCmeDrPMaxRSat */
  /* During: CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/Calc_flgCmeDrPMaxRSat */
  if (CmeFilterMgm_DWork.bitsForTID0.is_active_c5_CmeFilterMgm == 0U) {
    /* Entry: CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/Calc_flgCmeDrPMaxRSat */
    CmeFilterMgm_DWork.bitsForTID0.is_active_c5_CmeFilterMgm = 1U;

    /* Entry Internal: CmeFilterMgm/T10ms/RateLim_Calculation/CmeDriverPMaxRSat_Calc/Calc_flgCmeDrPMaxRSat */
    /* Transition: '<S49>:1' */
    CmeFilterMgm_B.flgCmeDrPMaxRSat = 1U;
    CmeFilterMgm_DWork.bitsForTID0.is_c5_CmeFilterMgm =
      CmeFilterMgm_IN_NO_STEP_CME;
  } else if (CmeFilterMgm_DWork.bitsForTID0.is_c5_CmeFilterMgm ==
             CmeFilterMgm_IN_NO_STEP_CME) {
    /* During 'NO_STEP_CME': '<S49>:6' */
    if ((!rtb_RelationalOperator_p) && (CmeDriverP <
         CmeFilterMgm_B.LookUp_IR_S16)) {
      /* Transition: '<S49>:8' */
      CmeFilterMgm_B.flgCmeDrPMaxRSat = 0U;
      CmeFilterMgm_DWork.bitsForTID0.is_c5_CmeFilterMgm =
        CmeFilterMgm_IN_RISE_STEP_CME;
    }
  } else {
    /* During 'RISE_STEP_CME': '<S49>:7' */
    if (rtb_RelationalOperator_p) {
      /* Transition: '<S49>:9' */
      CmeFilterMgm_B.flgCmeDrPMaxRSat = 1U;
      CmeFilterMgm_DWork.bitsForTID0.is_c5_CmeFilterMgm =
        CmeFilterMgm_IN_NO_STEP_CME;
    }
  }

  /* End of Chart: '<S47>/Calc_flgCmeDrPMaxRSat' */

  /* RelationalOperator: '<S47>/Relational Operator1' incorporates:
   *  DataStoreRead: '<S47>/Data Store Read5'
   *  DataStoreWrite: '<S4>/Data Store Write15'
   *  DataStoreWrite: '<S4>/Data Store Write16'
   */
  FlgCmeDriverPHiRpm = (uint8_T)(CmeDriverPOffMaxRSat < CmeDriverP);

  /* RelationalOperator: '<S12>/Relational Operator1' incorporates:
   *  Constant: '<S12>/PBYACTIVE'
   *  Inport: '<Root>/StTrqDriv'
   */
  rtb_RelationalOperator_p = (StTrqDriv == PBYACTIVE);

  /* RelationalOperator: '<S12>/Relational Operator4' incorporates:
   *  Constant: '<S12>/DRIVECHANGE'
   *  Inport: '<Root>/StTrqDriv'
   */
  rtb_RelationalOperator4_m = (StTrqDriv == DRIVECHANGE);

  /* Switch: '<S12>/Switch' incorporates:
   *  Constant: '<S12>/VTCMERATEMAXPBY'
   *  Inport: '<Root>/GearPosClu'
   *  Selector: '<S12>/Selector1'
   *  Switch: '<S12>/Switch3'
   *  Switch: '<S12>/Switch8'
   */
  if (rtb_RelationalOperator_p) {
    rtb_Switch5_m = VTCMERATEMAXPBY[GearPosClu];
  } else if (rtb_RelationalOperator4_m) {
    /* Switch: '<S12>/Switch3' incorporates:
     *  Constant: '<S12>/CMERATERDMODEMAX'
     */
    rtb_Switch5_m = CMERATERDMODEMAX;
  } else if (CmeFilterMgm_B.flgCmeDrPMaxRSat != 0) {
    for (i = 0; i < 6; i++) {
      /* Switch: '<S12>/Switch1' incorporates:
       *  Constant: '<S12>/VTCMERATEMAX0'
       *  Constant: '<S12>/VTCMERATEMAX1'
       *  Inport: '<Root>/EngResp'
       *  Switch: '<S12>/Switch3'
       *  Switch: '<S12>/Switch8'
       */
      if (EngResp != 0) {
        rtb_Switch1[i] = VTCMERATEMAX1[i];
      } else {
        rtb_Switch1[i] = VTCMERATEMAX0[i];
      }

      /* End of Switch: '<S12>/Switch1' */
    }

    /* S-Function (LookUp_S16_S16): '<S48>/LookUp_S16_S16' incorporates:
     *  Constant: '<S12>/BKCMEDRCMEFILT'
     *  Constant: '<S12>/BKCMEDRCMEFILT_dim'
     *  Switch: '<S12>/Switch3'
     *  Switch: '<S12>/Switch8'
     */
    LookUp_S16_S16( &rtb_LookUp_S16_S16, &rtb_Switch1[0], DeltaCmeDriver,
                   &BKCMEDRCMEFILT[0], ((uint8_T)BKCMEDRCMEFILT_dim));

    /* Selector: '<S12>/Selector' incorporates:
     *  Constant: '<S12>/ONE'
     *  Inport: '<Root>/GearPosClu'
     *  Sum: '<S12>/Subtract'
     *  Switch: '<S12>/Switch3'
     *  Switch: '<S12>/Switch8'
     */
    i = GearPosClu - 1;
    if (i < 0) {
      i = 0;
    }

    /* Product: '<S12>/Product' incorporates:
     *  Constant: '<S12>/VTRATECMEGAIN'
     *  Selector: '<S12>/Selector'
     *  Sum: '<S12>/Subtract'
     *  Switch: '<S12>/Switch3'
     *  Switch: '<S12>/Switch8'
     */
    i = rtb_LookUp_S16_S16 * VTRATECMEGAIN[i];
    i = ((((i < 0) ? 7 : 0) + i) >> 3);
    if (i > 32767) {
      i = 32767;
    } else {
      if (i < -32768) {
        i = -32768;
      }
    }

    /* Switch: '<S12>/Switch8' incorporates:
     *  Product: '<S12>/Product'
     *  Switch: '<S12>/Switch3'
     */
    rtb_Switch5_m = (int16_T)i;
  } else {
    /* Switch: '<S12>/Switch8' incorporates:
     *  Constant: '<S12>/THRTMAXLORPMCP'
     *  Switch: '<S12>/Switch3'
     */
    rtb_Switch5_m = THRTMAXLORPMCP;
  }

  /* End of Switch: '<S12>/Switch' */

  /* DataTypeConversion: '<S12>/Data Type Conversion1' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write5'
   */
  CmeRateMax = (rtb_Switch5_m << 10);

  /* Switch: '<S12>/Switch5' incorporates:
   *  Constant: '<S12>/CMERATEMIN'
   *  Constant: '<S12>/MIN_INT16T_EN9'
   *  Switch: '<S12>/Switch4'
   */
  if (rtb_RelationalOperator_p) {
    rtb_Switch5_m = ((int16_T)MIN_INT16T_EN9);
  } else if (rtb_RelationalOperator4_m) {
    /* Switch: '<S12>/Switch9' incorporates:
     *  Constant: '<S12>/CMERATERDMODEMIN'
     *  Constant: '<S12>/MIN_INT16T_EN9_2'
     *  Inport: '<Root>/FlgFromPby'
     *  Switch: '<S12>/Switch4'
     */
    if (FlgFromPby != 0) {
      rtb_Switch5_m = ((int16_T)MIN_INT16T_EN9);
    } else {
      rtb_Switch5_m = CMERATERDMODEMIN;
    }

    /* End of Switch: '<S12>/Switch9' */
  } else {
    rtb_Switch5_m = CMERATEMIN;
  }

  /* End of Switch: '<S12>/Switch5' */

  /* DataTypeConversion: '<S12>/Data Type Conversion10' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write6'
   */
  CmeRateMin = (rtb_Switch5_m << 10);

  /* Logic: '<S12>/Logical Operator1' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write12'
   */
  FlgCmeDrPMaxRSat = (uint8_T)((rtb_RelationalOperator_p ||
    rtb_RelationalOperator4_m) || (CmeFilterMgm_B.flgCmeDrPMaxRSat != 0));

  /* Update for Memory: '<S47>/Memory' incorporates:
   *  Inport: '<Root>/CmeDriverPTmp'
   */
  CmeFilterMgm_DWork.Memory_PreviousInput_j = CmeDriverPTmp;
}

/* System initialize for function-call system: '<S1>/T10ms' */
void CmeFilterMgm_T10ms_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S4>/RateLim_Calculation' */
  CmeFil_RateLim_Calculation_Init();

  /* End of SystemInitialize for SubSystem: '<S4>/RateLim_Calculation' */

  /* SystemInitialize for Atomic SubSystem: '<S4>/Filter_RateLim' */
  CmeFilterMg_Filter_RateLim_Init();

  /* End of SystemInitialize for SubSystem: '<S4>/Filter_RateLim' */
}

/* Output and update for function-call system: '<S1>/T10ms' */
void CmeFilterMgm_T10ms(void)
{
  int32_T tmp;

  /* Chart: '<S8>/buffCmeDriver' incorporates:
   *  Inport: '<Root>/CmeDriver'
   */
  /* Gateway: CmeFilterMgm/T10ms/Calc_DeltaCmeDriver/buffCmeDriver */
  /* During: CmeFilterMgm/T10ms/Calc_DeltaCmeDriver/buffCmeDriver */
  /* Entry Internal: CmeFilterMgm/T10ms/Calc_DeltaCmeDriver/buffCmeDriver */
  /* Transition: '<S13>:9' */
  if (CmeFilterMgm_DWork.init == 0) {
    /* Transition: '<S13>:33' */
    VtCmeDriver[(CmeFilterMgm_DWork.tDCmeDriverIdx)] = CmeDriver;
    CmeFilterMgm_DWork.init = 1U;
  } else {
    /* Transition: '<S13>:34' */
  }

  /* Transition: '<S13>:11' */
  tmp = CmeFilterMgm_DWork.hDCmeDriverIdx + 1;
  if (tmp > 255) {
    tmp = 255;
  }

  CmeFilterMgm_DWork.hDCmeDriverIdx = (uint8_T)tmp;
  if ((CmeFilterMgm_DWork.hDCmeDriverIdx >= NUMDCMEDRIV) ||
      (CmeFilterMgm_DWork.hDCmeDriverIdx >= ((uint8_T)NUM_DCMEDRIV))) {
    /* Transition: '<S13>:13' */
    CmeFilterMgm_DWork.hDCmeDriverIdx = 0U;
  } else {
    /* Transition: '<S13>:14' */
  }

  /* Transition: '<S13>:16' */
  VtCmeDriver[(CmeFilterMgm_DWork.hDCmeDriverIdx)] = CmeDriver;
  if (CmeFilterMgm_DWork.hDCmeDriverIdx == CmeFilterMgm_DWork.tDCmeDriverIdx) {
    /* Transition: '<S13>:18' */
    tmp = CmeFilterMgm_DWork.tDCmeDriverIdx + 1;
    if (tmp > 255) {
      tmp = 255;
    }

    CmeFilterMgm_DWork.tDCmeDriverIdx = (uint8_T)tmp;
  } else {
    /* Transition: '<S13>:19' */
  }

  /* Transition: '<S13>:21' */
  if ((CmeFilterMgm_DWork.tDCmeDriverIdx >= NUMDCMEDRIV) ||
      (CmeFilterMgm_DWork.tDCmeDriverIdx >= ((uint8_T)NUM_DCMEDRIV))) {
    /* Transition: '<S13>:23' */
    CmeFilterMgm_DWork.tDCmeDriverIdx = 0U;
  } else {
    /* Transition: '<S13>:26' */
  }

  /* Transition: '<S13>:31' */
  tmp = VtCmeDriver[(CmeFilterMgm_DWork.hDCmeDriverIdx)] - VtCmeDriver
    [(CmeFilterMgm_DWork.tDCmeDriverIdx)];
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  DeltaCmeDriver = (int16_T)tmp;

  /* End of Chart: '<S8>/buffCmeDriver' */

  /* Outputs for Atomic SubSystem: '<S4>/CmeDriver_Filter_Parameter' */
  /* S-Function (PreLookUpIdSearch_U16): '<S14>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S9>/BKRPMTRQDRIV'
   *  Constant: '<S9>/BKRPMTRQDRIV_dim'
   *  Inport: '<Root>/Rpm'
   */
  PreLookUpIdSearch_U16( &CmeFilterMgm_B.PreLookUpIdSearch_U16_o1,
                        &CmeFilterMgm_B.PreLookUpIdSearch_U16_o2, Rpm,
                        &BKRPMTRQDRIV[0], ((uint8_T)BKRPMTRQDRIV_dim));

  /* End of Outputs for SubSystem: '<S4>/CmeDriver_Filter_Parameter' */

  /* Outputs for Atomic SubSystem: '<S4>/RateLim_Calculation' */
  CmeFilterMg_RateLim_Calculation();

  /* End of Outputs for SubSystem: '<S4>/RateLim_Calculation' */

  /* Outputs for Atomic SubSystem: '<S4>/FiltGain_Calculation' */
  CmeFilterM_FiltGain_Calculation();

  /* End of Outputs for SubSystem: '<S4>/FiltGain_Calculation' */

  /* Outputs for Atomic SubSystem: '<S4>/Filter_RateLim' */
  CmeFilterMgm_Filter_RateLim();

  /* End of Outputs for SubSystem: '<S4>/Filter_RateLim' */

  /* DataStoreWrite: '<S4>/Data Store Write10' */
  FlgResCmeDriverI = CmeFilterMgm_B.FlgResCmeDriverI_b;

  /* DataStoreWrite: '<S4>/Data Store Write13' */
  FlgKfCmeDGResp = CmeFilterMgm_B.outFlgKfCmeDGResp;

  /* DataStoreWrite: '<S4>/Data Store Write14' */
  CmeDriverCIPostF = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1;

  /* DataStoreWrite: '<S4>/Data Store Write17' */
  CmeDriverPMaxRSat = CmeFilterMgm_B.LookUp_IR_S16;

  /* DataStoreWrite: '<S4>/Data Store Write18' */
  CmeDriverCLOff = CmeFilterMgm_B.CmeDriverICLOff;

  /* DataStoreWrite: '<S4>/Data Store Write19' */
  CntCDICLOff = CmeFilterMgm_B.CntCDICLOff_n;

  /* DataStoreWrite: '<S4>/Data Store Write2' */
  CmeDriverPTmpFilt = CmeFilterMgm_B.FOF_Reset_S16_FXP_o1_a;

  /* DataStoreWrite: '<S4>/Data Store Write21' */
  StCmeITrCL = CmeFilterMgm_B.StCmeITrCL_o;

  /* DataStoreWrite: '<S4>/Data Store Write22' */
  StCmeICL = CmeFilterMgm_B.StCmeICL_i;

  /* DataStoreWrite: '<S4>/Data Store Write3' */
  TrLimGCTrig = CmeFilterMgm_B.TrLimGCTrig_j;

  /* DataStoreWrite: '<S4>/Data Store Write7' */
  KFiltCmeDriverP = CmeFilterMgm_B.Merge1;

  /* DataStoreWrite: '<S4>/Data Store Write8' */
  KFiltCmeDriverI = CmeFilterMgm_B.Merge;

  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/Init' */
void CmeFilterMgm_Init(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/ZERO1'
   */
  CmeDriverI = 0;

  /* DataStoreWrite: '<S2>/Data Store Write10' incorporates:
   *  Constant: '<S2>/ONE2'
   */
  CmeFilterMgm_DWork.grGain_hr = 524288;

  /* DataStoreWrite: '<S2>/Data Store Write11' incorporates:
   *  Constant: '<S2>/ONE3'
   */
  GRGainLim = 128U;

  /* DataStoreWrite: '<S2>/Data Store Write12' incorporates:
   *  Constant: '<S2>/ZERO7'
   */
  CmeFilterMgm_DWork.cmedriverp_hr = 0;

  /* DataStoreWrite: '<S2>/Data Store Write13' incorporates:
   *  Constant: '<S2>/ZERO5'
   */
  CmeDriverICLOffOut = 0;

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Constant: '<S2>/MAX_INT32T_EN'
   */
  CmeRateMax = MAX_INT32T_EN19;

  /* DataStoreWrite: '<S2>/Data Store Write3' incorporates:
   *  Constant: '<S2>/ZERO2'
   */
  CmeDriverPTmpFilt = 0;

  /* DataStoreWrite: '<S2>/Data Store Write4' incorporates:
   *  Constant: '<S2>/MIN_INT32T_EN19'
   */
  CmeRateMin = MIN_INT32T_EN19;

  /* DataStoreWrite: '<S2>/Data Store Write5' incorporates:
   *  Constant: '<S2>/ONE'
   */
  KFiltCmeDriverP = 16384U;

  /* DataStoreWrite: '<S2>/Data Store Write6' incorporates:
   *  Constant: '<S2>/ONE1'
   */
  KFiltCmeDriverI = 16384U;

  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  CmeDriverP = 0;

  /* DataStoreWrite: '<S2>/Data Store Write8' incorporates:
   *  Constant: '<S2>/ZERO3'
   */
  CmeDriverCIPostF = 0;

  /* DataStoreWrite: '<S2>/Data Store Write9' incorporates:
   *  Constant: '<S2>/ZERO4'
   */
  FlgKfCmeDGResp = 0U;

  /* Constant: '<S2>/ID_CME_FILT_MGM' */
  IDCmeFilterMgm = ID_CME_FILT_MGM;

  /* user code (Output function Trailer) */

  /* System '<S1>/Init' */

  /* PILOTAGGIO USCITE - INIT */
  /* Read static variables */
  /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
  CmeFilterMgm_initialize();
}

/* Output and update for function-call system: '<S1>/Off' */
void CmeFilterMgm_Off(void)
{
  /* user code (Output function Trailer) */

  /* System '<S1>/Off' */
  /* PILOTAGGIO USCITE - OFF */
}

/* Model step function */
void CmeFilterMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/CmeFilterMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((CmeFilterMgm_U.ev_PowerOn > 0) &&
      (CmeFilterMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    CmeFilterMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  CmeFilterMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (CmeFilterMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((CmeFilterMgm_U.ev_T10ms > 0) &&
      (CmeFilterMgm_PrevZCSigState.trig_to_fc_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    CmeFilterMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  CmeFilterMgm_PrevZCSigState.trig_to_fc_Trig_ZCE = (ZCSigState)
    (CmeFilterMgm_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOff' */
  if ((CmeFilterMgm_U.ev_PowerOff > 0) &&
      (CmeFilterMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Off'
     */
    CmeFilterMgm_Off();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  CmeFilterMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (CmeFilterMgm_U.ev_PowerOff > 0);

  /* End of Inport: '<Root>/ev_PowerOff' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* End of Outputs for SubSystem: '<Root>/CmeFilterMgm' */
}

/* Model initialize function */
void CmeFilterMgm_initialize(void)
{
  CmeFilterMgm_PrevZCSigState.trig_to_fc_Trig_ZCE = POS_ZCSIG;
  CmeFilterMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  CmeFilterMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/CmeFilterMgm' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/trig_to_fc' */
  /* SystemInitialize for S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  CmeFilterMgm_T10ms_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  /* End of SystemInitialize for SubSystem: '<S1>/trig_to_fc' */

  /* End of SystemInitialize for SubSystem: '<Root>/CmeFilterMgm' */
}

/* user code (bottom of source file) */
/* System '<Root>/CmeFilterMgm' */
#endif                                 // _BUILD_CMEFILTERMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
