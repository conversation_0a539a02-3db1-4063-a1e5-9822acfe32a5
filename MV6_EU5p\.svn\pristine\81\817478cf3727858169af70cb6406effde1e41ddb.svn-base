/*
 * File: IdxCtfCtrl.c
 *
 * Code generated for Simulink model 'IdxCtfCtrl'.
 *
 * Model version                  : 1.480
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Feb 17 15:53:24 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (29), Warnings (3), Error (0)
 */

#include "IdxCtfCtrl.h"
#include "IdxCtfCtrl_private.h"

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IDXCTFCTRL_

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T CntCtfTDC;

/* counter */
uint32_T IDIdxCutoff;

/* ID Version */
uint8_T IdxCtfFlg;

/* IdxCtfCtrl */
uint8_T IdxCutoff;

/* IdxCtfCtrl */
uint8_T IdxSpareCutOff;

/* IdxCtfCtrl */
uint8_T IdxSpareCutOff0;

/* IdxCtfCtrl */
uint8_T PatternCtfTDC;

/* counter */
uint8_T VtIdxCtfFlg[4];

/* Cutoff selective */
uint8_T VtIdxCtfFlgBuff[4];

/* Cutoff selective */

/* Declare variables for internal data of system '<S1>/PreTDC' */
rtDW_PreTDC_IdxCtfCtrl_T IdxCtfCtrl_PreTDC_DW;

/* Declare variables for internal data of system '<S1>/T10ms' */
rtDW_T10ms_IdxCtfCtrl_T IdxCtfCtrl_T10ms_DW;

/* Output and update for function-call system: '<S1>/Init' */
void IdxCtfCtrl_Init(void)
{
  int32_T i;

  /* user code (Output function Body for TID1) */

  /* System '<S1>/Init' */
  IdxCtfCtrl_initialize();

  /* Chart: '<S3>/Init_Scheduler' incorporates:
   *  SubSystem: '<S3>/Init_Data'
   */
  /* Constant: '<S8>/ID_IDX_CTFCTRL' */
  /* Gateway: IdxCtfCtrl/Init/Init_Scheduler */
  /* During: IdxCtfCtrl/Init/Init_Scheduler */
  /* Entry Internal: IdxCtfCtrl/Init/Init_Scheduler */
  /* Transition: '<S9>:2' */
  /* Transition: '<S9>:4' */
  /* Event: '<S9>:6' */
  IDIdxCutoff = ID_IDX_CTFCTRL;

  /* SignalConversion generated from: '<S3>/IdxCtfFlg' incorporates:
   *  Constant: '<S8>/ZERO1'
   */
  IdxCtfFlg = 0U;

  /* SignalConversion generated from: '<S3>/IdxSpareCutOff' incorporates:
   *  Constant: '<S8>/ZERO'
   */
  IdxSpareCutOff = 0U;
  for (i = 0; i < 4; i++) {
    /* SignalConversion generated from: '<S3>/VtIdxCtfFlg' */
    VtIdxCtfFlg[(i)] = 0U;

    /* SignalConversion generated from: '<S3>/VtIdxCtfFlgBuff' incorporates:
     *  SignalConversion generated from: '<S3>/VtIdxCtfFlg'
     */
    VtIdxCtfFlgBuff[(i)] = 0U;
  }

  /* user code (Output function Trailer for TID1) */

  /* System '<S1>/Init' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* System initialize for function-call system: '<S1>/PreTDC' */
void IdxCtfCtrl_PreTDC_Init(void)
{
  int32_T i;

  /* SystemInitialize for Chart: '<S5>/Calc_VtIdxCtfFlgBuff' */
  for (i = 0; i < 4; i++) {
    IdxCtfCtrl_PreTDC_DW.x[i] = 0U;
    IdxCtfCtrl_PreTDC_DW.VtIdxCtfFlgBuff_pie[i] = 0U;
  }

  /* End of SystemInitialize for Chart: '<S5>/Calc_VtIdxCtfFlgBuff' */

  /* SystemInitialize for Chart: '<S5>/playIdxCutOff' */
  IdxCtfCtrl_PreTDC_DW.oldidxCutOff = 0U;
}

/* Output and update for function-call system: '<S1>/PreTDC' */
void IdxCtfCtrl_PreTDC(void)
{
  uint8_T rtb_ctfFlg;
  uint8_T rtb_MinMax2_ixj;
  uint8_T rtb_MinMax1;
  uint8_T rtb_outReset;
  uint16_T rtb_Product;
  uint8_T rtb_u_1_2_3_4_5_6_7_8[9];
  int32_T s19_iter;
  uint8_T tmp[8];
  uint8_T CtfGearShift_0;
  uint8_T CutoffFlg_0;
  uint8_T MinMax;
  uint8_T CntCtfTDC_0;
  uint8_T IdxTcCutOff_0;
  uint8_T IdxLcCutOff_0;
  uint8_T IdxAwCutOff_0;
  uint8_T IdxRpmLCutOff_0;
  uint8_T AbsPreTdc_0;
  uint8_T IdxCutoff_0;
  boolean_T guard1 = false;

  /* Inport: '<Root>/AbsPreTdc' */
  AbsPreTdc_0 = AbsPreTdc;

  /* Inport: '<Root>/IdxRpmLCutOff' */
  IdxRpmLCutOff_0 = IdxRpmLCutOff;

  /* Inport: '<Root>/IdxAwCutOff' */
  IdxAwCutOff_0 = IdxAwCutOff;

  /* Inport: '<Root>/IdxLcCutOff' */
  IdxLcCutOff_0 = IdxLcCutOff;

  /* Inport: '<Root>/IdxTcCutOff' */
  IdxTcCutOff_0 = IdxTcCutOff;

  /* Product: '<S14>/Product' */
  CntCtfTDC_0 = CntCtfTDC;

  /* Product: '<S5>/Product' */
  MinMax = IdxCtfCtrl_PreTDC_DW.MinMax;

  /* Inport: '<Root>/CutoffFlg' */
  CutoffFlg_0 = CutoffFlg;

  /* Inport: '<Root>/CtfGearShift' */
  CtfGearShift_0 = CtfGearShift;

  /* Sum: '<S14>/Add' incorporates:
   *  Memory: '<S5>/Memory'
   */
  PatternCtfTDC = CntCtfTDC_0;

  /* MinMax: '<S10>/MinMax1' incorporates:
   *  Constant: '<S10>/N_CYL_MAX_EM-1'
   *  Inport: '<Root>/AbsPreTdc'
   */
  rtb_MinMax1 = (uint8_T)(((uint32_T)N_CYL_MAX_EM) - 1U);
  if (AbsPreTdc_0 < rtb_MinMax1) {
    rtb_MinMax1 = AbsPreTdc_0;
  }

  /* End of MinMax: '<S10>/MinMax1' */

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' */
  rtb_u_1_2_3_4_5_6_7_8[0] = IdxCtfCtrl_T10ms_DW.Merge;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Inport: '<Root>/IdxTcCutOff'
   */
  rtb_u_1_2_3_4_5_6_7_8[1] = IdxTcCutOff_0;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Inport: '<Root>/IdxLcCutOff'
   */
  rtb_u_1_2_3_4_5_6_7_8[2] = IdxLcCutOff_0;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Inport: '<Root>/IdxAwCutOff'
   */
  rtb_u_1_2_3_4_5_6_7_8[3] = IdxAwCutOff_0;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Inport: '<Root>/IdxRpmLCutOff'
   */
  rtb_u_1_2_3_4_5_6_7_8[4] = IdxRpmLCutOff_0;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Inport: '<Root>/CutoffFlg'
   */
  rtb_u_1_2_3_4_5_6_7_8[5] = CutoffFlg_0;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Constant: '<S5>/STUB_7'
   */
  rtb_u_1_2_3_4_5_6_7_8[6] = 0U;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Constant: '<S5>/STUB_6'
   */
  rtb_u_1_2_3_4_5_6_7_8[7] = 0U;

  /* SignalConversion generated from: '<S15>/0_1_2_3_4_5_6_7_8' incorporates:
   *  Inport: '<Root>/CtfGearShift'
   */
  rtb_u_1_2_3_4_5_6_7_8[8] = CtfGearShift_0;

  /* Outputs for Iterator SubSystem: '<S15>/For Iterator Subsystem' incorporates:
   *  ForIterator: '<S19>/For Iterator'
   */
  /* Constant: '<S15>/N_TYPE_CUTOFF' */
  for (s19_iter = 0; s19_iter < ((int32_T)((uint8_T)N_TYPE_CUTOFF)); s19_iter++)
  {
    /* Switch: '<S19>/Switch' incorporates:
     *  Constant: '<S19>/Constant'
     *  Constant: '<S19>/VTIDXCTFTYPE'
     *  Selector: '<S19>/Selector1'
     *  Selector: '<S19>/Selector2'
     */
    if (((int32_T)rtb_u_1_2_3_4_5_6_7_8[s19_iter]) != 0) {
      rtb_MinMax2_ixj = VTIDXCTFTYPE[s19_iter];
    } else {
      rtb_MinMax2_ixj = 0U;
    }

    /* End of Switch: '<S19>/Switch' */

    /* Switch: '<S19>/Switch1' incorporates:
     *  Constant: '<S19>/Constant'
     */
    if (s19_iter != 0) {
    } else {
      MinMax = 0U;
    }

    /* MinMax: '<S19>/MinMax' */
    if (MinMax > rtb_MinMax2_ixj) {
    } else {
      /* Switch: '<S19>/Switch1' */
      MinMax = rtb_MinMax2_ixj;
    }

    /* End of MinMax: '<S19>/MinMax' */
  }

  /* End of Constant: '<S15>/N_TYPE_CUTOFF' */
  /* End of Outputs for SubSystem: '<S15>/For Iterator Subsystem' */

  /* MinMax: '<S15>/MinMax2' incorporates:
   *  Inport: '<Root>/IdxAwCutOff'
   *  Inport: '<Root>/IdxLcCutOff'
   *  Inport: '<Root>/IdxRpmLCutOff'
   *  Inport: '<Root>/IdxTcCutOff'
   */
  if (IdxCtfCtrl_T10ms_DW.Merge > IdxTcCutOff_0) {
    rtb_outReset = IdxCtfCtrl_T10ms_DW.Merge;
  } else {
    rtb_outReset = IdxTcCutOff_0;
  }

  if (rtb_outReset > IdxLcCutOff_0) {
  } else {
    rtb_outReset = IdxLcCutOff_0;
  }

  if (rtb_outReset > IdxAwCutOff_0) {
  } else {
    rtb_outReset = IdxAwCutOff_0;
  }

  if (rtb_outReset > IdxRpmLCutOff_0) {
  } else {
    rtb_outReset = IdxRpmLCutOff_0;
  }

  /* Product: '<S18>/Product' incorporates:
   *  Constant: '<S18>/CTF_NUM_COIDX'
   *  Inport: '<Root>/CtfGearShift'
   *  Inport: '<Root>/CutoffFlg'
   *  Logic: '<S18>/Logical Operator'
   */
  rtb_MinMax2_ixj = (uint8_T)(((uint32_T)(((((int32_T)CutoffFlg_0) != 0) ||
    (((int32_T)CtfGearShift_0) != 0)) ? 1 : 0)) * ((uint32_T)((uint8_T)
    CTF_NUM_COIDX)));

  /* MinMax: '<S15>/MinMax2' */
  if (rtb_outReset > rtb_MinMax2_ixj) {
    rtb_MinMax2_ixj = rtb_outReset;
  }

  /* MinMax: '<S5>/MinMax' incorporates:
   *  Constant: '<S5>/CTF_NUM_COIDX'
   */
  if (rtb_MinMax2_ixj < ((uint8_T)CTF_NUM_COIDX)) {
    IdxCutoff_0 = rtb_MinMax2_ixj;
  } else {
    IdxCutoff_0 = ((uint8_T)CTF_NUM_COIDX);
  }

  /* End of MinMax: '<S5>/MinMax' */

  /* SignalConversion generated from: '<S5>/Selector4' incorporates:
   *  Constant: '<S11>/VTIDXCUTOFF1'
   *  Constant: '<S11>/VTIDXCUTOFF2'
   *  Constant: '<S11>/VTIDXCUTOFF3'
   *  Constant: '<S11>/VTIDXCUTOFF4'
   *  Constant: '<S11>/VTIDXCUTOFF5'
   *  Constant: '<S11>/VTIDXCUTOFF6'
   *  Constant: '<S11>/ZERO'
   *  Selector: '<S11>/Selector'
   *  Selector: '<S11>/Selector1'
   *  Selector: '<S11>/Selector2'
   *  Selector: '<S11>/Selector3'
   *  Selector: '<S11>/Selector5'
   *  Selector: '<S11>/Selector6'
   */
  tmp[0] = 0U;
  tmp[1] = VTIDXCUTOFF1[CntCtfTDC_0];
  tmp[2] = VTIDXCUTOFF2[CntCtfTDC_0];
  tmp[3] = VTIDXCUTOFF3[CntCtfTDC_0];
  tmp[4] = VTIDXCUTOFF4[CntCtfTDC_0];
  tmp[5] = VTIDXCUTOFF5[CntCtfTDC_0];
  tmp[6] = VTIDXCUTOFF6[CntCtfTDC_0];
  tmp[7] = 1U;

  /* Product: '<S5>/Product' incorporates:
   *  Selector: '<S5>/Selector4'
   */
  rtb_Product = (uint16_T)(((uint32_T)MinMax) * ((uint32_T)tmp[IdxCutoff_0]));

  /* Chart: '<S5>/playIdxCutOff' incorporates:
   *  Constant: '<S5>/VTIDXCTFMAXPTR'
   *  Selector: '<S5>/Selector1'
   */
  /* Gateway: IdxCtfCtrl/PreTDC/playIdxCutOff */
  /* During: IdxCtfCtrl/PreTDC/playIdxCutOff */
  /* Entry Internal: IdxCtfCtrl/PreTDC/playIdxCutOff */
  /* Transition: '<S16>:2' */
  rtb_outReset = 1U;
  guard1 = false;
  if (IdxCutoff_0 != IdxCtfCtrl_PreTDC_DW.oldidxCutOff) {
    /* Transition: '<S16>:4' */
    if ((((int32_T)rtb_Product) == 0) && (((int32_T)ENIDXCUTOFF) == 2)) {
      /* Transition: '<S16>:29' */
      rtb_outReset = 0U;
      rtb_ctfFlg = 0U;
    } else {
      /* Transition: '<S16>:28' */
      if (((int32_T)ENIDXCUTOFF) > 2) {
        /* Transition: '<S16>:31' */
        rtb_outReset = 0U;
        rtb_ctfFlg = MinMax;
      } else {
        /* Transition: '<S16>:32' */
        guard1 = true;
      }
    }
  } else {
    /* Transition: '<S16>:5' */
    guard1 = true;
  }

  if (guard1) {
    if ((CntCtfTDC_0 >= ((uint8_T)CTF_NUM_COSAMP)) || (CntCtfTDC_0 >=
         VTIDXCTFMAXPTR[IdxCutoff_0])) {
      /* Transition: '<S16>:16' */
      rtb_outReset = 0U;
      rtb_ctfFlg = (uint8_T)rtb_Product;
    } else {
      /* Transition: '<S16>:17' */
      rtb_ctfFlg = (uint8_T)rtb_Product;
    }
  }

  /* Transition: '<S16>:8' */
  IdxCtfCtrl_PreTDC_DW.oldidxCutOff = IdxCutoff_0;

  /* End of Chart: '<S5>/playIdxCutOff' */

  /* Switch: '<S10>/Switch' incorporates:
   *  Constant: '<S10>/ENIDXCUTOFF'
   */
  if (((int32_T)ENIDXCUTOFF) != 0) {
    /* MinMax: '<S10>/MinMax2' incorporates:
     *  Inport: '<Root>/VtFOInjCutoff'
     *  Selector: '<S17>/Selector'
     */
    if (rtb_ctfFlg > VtFOInjCutoff[(rtb_MinMax1)]) {
      /* Assignment: '<S10>/Assignment' */
      IdxCtfCtrl_PreTDC_DW.Memory_PreviousInput_o4d[rtb_MinMax1] = rtb_ctfFlg;
    } else {
      /* Assignment: '<S10>/Assignment' */
      IdxCtfCtrl_PreTDC_DW.Memory_PreviousInput_o4d[rtb_MinMax1] =
        VtFOInjCutoff[(rtb_MinMax1)];
    }

    /* End of MinMax: '<S10>/MinMax2' */
  } else {
    /* Assignment: '<S10>/Assignment' incorporates:
     *  Constant: '<S10>/Constant'
     */
    IdxCtfCtrl_PreTDC_DW.Memory_PreviousInput_o4d[rtb_MinMax1] = 0U;
  }

  /* End of Switch: '<S10>/Switch' */

  /* SignalConversion generated from: '<S5>/VtIdxCtfFlg' */
  for (s19_iter = 0; s19_iter < 4; s19_iter++) {
    VtIdxCtfFlg[(s19_iter)] =
      IdxCtfCtrl_PreTDC_DW.Memory_PreviousInput_o4d[s19_iter];
  }

  /* End of SignalConversion generated from: '<S5>/VtIdxCtfFlg' */

  /* Assignment: '<S10>/Assignment1' */
  IdxCtfCtrl_PreTDC_DW.Memory1_PreviousInput[rtb_MinMax1] = rtb_ctfFlg;

  /* Chart: '<S5>/Calc_IdxCtfFlg' */
  /* Gateway: IdxCtfCtrl/PreTDC/Calc_IdxCtfFlg */
  /* During: IdxCtfCtrl/PreTDC/Calc_IdxCtfFlg */
  /* Entry Internal: IdxCtfCtrl/PreTDC/Calc_IdxCtfFlg */
  /* Transition: '<S12>:4' */
  rtb_MinMax1 = 0U;

  /* SignalConversion generated from: '<S5>/IdxCtfFlg' incorporates:
   *  Chart: '<S5>/Calc_IdxCtfFlg'
   */
  IdxCtfFlg = 0U;

  /* Chart: '<S5>/Calc_IdxCtfFlg' */
  while (rtb_MinMax1 < N_CYL_MAX_EM) {
    /* Transition: '<S12>:6' */
    if (((int32_T)IdxCtfCtrl_PreTDC_DW.Memory1_PreviousInput[rtb_MinMax1]) != 0)
    {
      /* SignalConversion generated from: '<S5>/IdxCtfFlg' */
      /* Transition: '<S12>:10' */
      IdxCtfFlg = 1U;
    } else {
      /* Transition: '<S12>:9' */
    }

    /* Transition: '<S12>:7' */
    rtb_MinMax1 = (uint8_T)((int32_T)(((int32_T)rtb_MinMax1) + 1));
  }

  /* Chart: '<S5>/Calc_VtIdxCtfFlgBuff' incorporates:
   *  Inport: '<Root>/AbsPreTdc'
   */
  /* Transition: '<S12>:5' */
  /*  Exit */
  /* Gateway: IdxCtfCtrl/PreTDC/Calc_VtIdxCtfFlgBuff */
  /* During: IdxCtfCtrl/PreTDC/Calc_VtIdxCtfFlgBuff */
  /* Entry Internal: IdxCtfCtrl/PreTDC/Calc_VtIdxCtfFlgBuff */
  /* Transition: '<S13>:27' */
  IdxCtfCtrl_PreTDC_DW.VtIdxCtfFlgBuff_pie[AbsPreTdc_0] =
    IdxCtfCtrl_PreTDC_DW.x[AbsPreTdc_0];
  IdxCtfCtrl_PreTDC_DW.x[AbsPreTdc_0] =
    IdxCtfCtrl_PreTDC_DW.Memory1_PreviousInput[AbsPreTdc_0];

  /* SignalConversion generated from: '<S5>/VtIdxCtfFlgBuff' */
  for (s19_iter = 0; s19_iter < 4; s19_iter++) {
    VtIdxCtfFlgBuff[(s19_iter)] =
      IdxCtfCtrl_PreTDC_DW.VtIdxCtfFlgBuff_pie[s19_iter];
  }

  /* End of SignalConversion generated from: '<S5>/VtIdxCtfFlgBuff' */

  /* Product: '<S14>/Product' incorporates:
   *  Constant: '<S14>/Inc'
   *  Constant: '<S5>/zero'
   *  Logic: '<S5>/Logical Operator'
   *  RelationalOperator: '<S5>/Relational Operator'
   *  Sum: '<S14>/Add'
   */
  CntCtfTDC_0 = (uint8_T)(((uint32_T)((uint8_T)(((uint32_T)CntCtfTDC_0) + 1U))) *
    ((uint32_T)(((0 != ((int32_T)rtb_MinMax2_ixj)) && (((int32_T)rtb_outReset)
    != 0)) ? 1 : 0)));

  /* Product: '<S5>/Product' */
  IdxCtfCtrl_PreTDC_DW.MinMax = MinMax;

  /* Product: '<S14>/Product' */
  CntCtfTDC = CntCtfTDC_0;
  IdxCutoff = IdxCutoff_0;
}

/* System initialize for function-call system: '<S1>/T10ms' */
void IdxCtfCtrl_T10ms_Init(void)
{
  /* SystemInitialize for IfAction SubSystem: '<S6>/Subsystem' */
  /* SystemInitialize for Chart: '<S21>/Select_StPlasObjB' */
  IdxCtfCtrl_T10ms_DW.is_active_c6_IdxCtfCtrl = 0U;
  IdxCtfCtrl_T10ms_DW.mem = 0U;

  /* End of SystemInitialize for SubSystem: '<S6>/Subsystem' */

  /* SystemInitialize for Merge: '<S6>/Merge' */
  IdxCtfCtrl_T10ms_DW.Merge = 0U;

  /* SystemInitialize for Merge: '<S6>/Merge1' */
  IdxSpareCutOff0 = 0U;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void IdxCtfCtrl_T10ms(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o_mta;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o_b24;
  uint16_T rtb_PreLookUpIdSearch_U16_o_i5w;
  uint16_T rtb_PreLookUpIdSearch_U16_o_ggm;
  uint16_T rtb_Look2D_IR_U8;
  uint16_T rtb_Look2D_IR_U8_dxk;
  int16_T rtb_RateLimiter_S16;
  uint8_T rtb_DataTypeConversion;
  uint8_T rtb_DataTypeConversion1;
  boolean_T rtb_RelationalOperator_lqg;
  uint8_T rtb_Conversion6;
  uint16_T rtb_Add_dna;
  int16_T rtb_DataTypeConversion3;
  uint8_T mem;
  uint8_T IdxSpareCutOff0_0;
  mem = IdxCtfCtrl_T10ms_DW.mem;

  /* If: '<S6>/If' incorporates:
   *  Constant: '<S6>/ENIDXSPARECUTOFF'
   */
  if (((int32_T)ENIDXSPARECUTOFF) != 0) {
    /* Outputs for IfAction SubSystem: '<S6>/Subsystem' incorporates:
     *  ActionPort: '<S21>/Action Port'
     */
    /* Sum: '<S22>/Add' incorporates:
     *  Constant: '<S22>/HYSRPMCTFSPARE'
     *  Inport: '<Root>/Rpm'
     */
    rtb_Add_dna = (uint16_T)(((uint32_T)Rpm) + ((uint32_T)HYSRPMCTFSPARE));

    /* DataTypeConversion: '<S27>/Data Type Conversion8' incorporates:
     *  Constant: '<S22>/BKRPMCTFSPARE_dim'
     */
    rtb_Conversion6 = (uint8_T)BKRPMCTFSPARE_dim;

    /* S-Function (PreLookUpIdSearch_U16): '<S27>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S22>/BKRPMCTFSPARE'
     *  Inport: '<Root>/Rpm'
     */
    PreLookUpIdSearch_U16( &rtb_Look2D_IR_U8, &rtb_PreLookUpIdSearch_U16_o2, Rpm,
                          &BKRPMCTFSPARE[0], rtb_Conversion6);

    /* DataTypeConversion: '<S28>/Data Type Conversion8' incorporates:
     *  Constant: '<S22>/BKRPMCTFSPARE_dim'
     */
    rtb_Conversion6 = (uint8_T)BKRPMCTFSPARE_dim;

    /* S-Function (PreLookUpIdSearch_U16): '<S28>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S22>/BKRPMCTFSPARE'
     */
    PreLookUpIdSearch_U16( &rtb_Look2D_IR_U8_dxk,
                          &rtb_PreLookUpIdSearch_U16_o_mta, rtb_Add_dna,
                          &BKRPMCTFSPARE[0], rtb_Conversion6);

    /* DataTypeConversion: '<S29>/Data Type Conversion8' incorporates:
     *  Constant: '<S22>/BKANGTHCTFSPARE_dim'
     */
    rtb_Conversion6 = (uint8_T)BKANGTHCTFSPARE_dim;

    /* S-Function (PreLookUpIdSearch_U16): '<S29>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S22>/BKANGTHCTFSPARE'
     *  Inport: '<Root>/AngThrottle'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                          &rtb_PreLookUpIdSearch_U16_o_b24, AngThrottle,
                          &BKANGTHCTFSPARE[0], rtb_Conversion6);

    /* Sum: '<S22>/Add1' incorporates:
     *  Constant: '<S22>/HYSANGTHCTFSPARE'
     *  Inport: '<Root>/AngThrottle'
     */
    rtb_Add_dna = (uint16_T)(((uint32_T)AngThrottle) + ((uint32_T)
      HYSANGTHCTFSPARE));

    /* DataTypeConversion: '<S30>/Data Type Conversion8' incorporates:
     *  Constant: '<S22>/BKANGTHCTFSPARE_dim'
     */
    rtb_Conversion6 = (uint8_T)BKANGTHCTFSPARE_dim;

    /* S-Function (PreLookUpIdSearch_U16): '<S30>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S22>/BKANGTHCTFSPARE'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_i5w,
                          &rtb_PreLookUpIdSearch_U16_o_ggm, rtb_Add_dna,
                          &BKANGTHCTFSPARE[0], rtb_Conversion6);

    /* DataTypeConversion: '<S24>/Conversion6' incorporates:
     *  Constant: '<S22>/BKRPMCTFSPARE_dim'
     */
    rtb_Conversion6 = (uint8_T)BKRPMCTFSPARE_dim;

    /* DataTypeConversion: '<S24>/Conversion7' incorporates:
     *  Constant: '<S22>/BKANGTHCTFSPARE_dim'
     */
    rtb_DataTypeConversion = (uint8_T)BKANGTHCTFSPARE_dim;

    /* S-Function (Look2D_IR_U8): '<S24>/Look2D_IR_U8' incorporates:
     *  Constant: '<S21>/TBIDXCTFSPARE'
     */
    Look2D_IR_U8( &rtb_Look2D_IR_U8, &TBIDXCTFSPARE[0], rtb_Look2D_IR_U8,
                 rtb_PreLookUpIdSearch_U16_o2, rtb_Conversion6,
                 rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o_b24,
                 rtb_DataTypeConversion);

    /* DataTypeConversion: '<S21>/Data Type Conversion' */
    rtb_DataTypeConversion = (uint8_T)(((uint32_T)rtb_Look2D_IR_U8) >>
      ((uint32_T)8));

    /* DataTypeConversion: '<S23>/Conversion6' incorporates:
     *  Constant: '<S22>/BKRPMCTFSPARE_dim'
     */
    rtb_Conversion6 = (uint8_T)BKRPMCTFSPARE_dim;

    /* DataTypeConversion: '<S23>/Conversion7' incorporates:
     *  Constant: '<S22>/BKANGTHCTFSPARE_dim'
     */
    rtb_DataTypeConversion1 = (uint8_T)BKANGTHCTFSPARE_dim;

    /* S-Function (Look2D_IR_U8): '<S23>/Look2D_IR_U8' incorporates:
     *  Constant: '<S21>/TBIDXCTFSPARE'
     */
    Look2D_IR_U8( &rtb_Look2D_IR_U8_dxk, &TBIDXCTFSPARE[0], rtb_Look2D_IR_U8_dxk,
                 rtb_PreLookUpIdSearch_U16_o_mta, rtb_Conversion6,
                 rtb_PreLookUpIdSearch_U16_o_i5w,
                 rtb_PreLookUpIdSearch_U16_o_ggm, rtb_DataTypeConversion1);

    /* DataTypeConversion: '<S21>/Data Type Conversion1' */
    rtb_DataTypeConversion1 = (uint8_T)(((uint32_T)rtb_Look2D_IR_U8_dxk) >>
      ((uint32_T)8));

    /* RelationalOperator: '<S21>/Relational Operator' */
    rtb_RelationalOperator_lqg = (rtb_DataTypeConversion >=
      rtb_DataTypeConversion1);

    /* Switch: '<S21>/Switch' */
    if (rtb_RelationalOperator_lqg) {
      rtb_Conversion6 = rtb_DataTypeConversion;
    } else {
      rtb_Conversion6 = rtb_DataTypeConversion1;
    }

    /* End of Switch: '<S21>/Switch' */

    /* Switch: '<S21>/Switch1' */
    if (rtb_RelationalOperator_lqg) {
      rtb_DataTypeConversion = rtb_DataTypeConversion1;
    }

    /* End of Switch: '<S21>/Switch1' */

    /* Chart: '<S21>/Select_StPlasObjB' incorporates:
     *  Constant: '<S21>/TBIDXCTFSPARE'
     */
    /* Gateway: IdxCtfCtrl/T10ms/Subsystem/Select_StPlasObjB */
    /* During: IdxCtfCtrl/T10ms/Subsystem/Select_StPlasObjB */
    if (((uint32_T)IdxCtfCtrl_T10ms_DW.is_active_c6_IdxCtfCtrl) == 0U) {
      /* Entry: IdxCtfCtrl/T10ms/Subsystem/Select_StPlasObjB */
      IdxCtfCtrl_T10ms_DW.is_active_c6_IdxCtfCtrl = 1U;

      /* Entry Internal: IdxCtfCtrl/T10ms/Subsystem/Select_StPlasObjB */
      /* Transition: '<S26>:2' */
      mem = TBIDXCTFSPARE[0];
    } else {
      /* During 'MEM': '<S26>:1' */
      /* Transition: '<S26>:5' */
      if (rtb_Conversion6 == rtb_DataTypeConversion) {
        /* Transition: '<S26>:9' */
        mem = rtb_Conversion6;
      } else {
        /* Transition: '<S26>:8' */
        if (rtb_DataTypeConversion > mem) {
          /* Transition: '<S26>:10' */
          mem = rtb_DataTypeConversion;
        } else {
          /* Transition: '<S26>:12' */
          if (mem > rtb_Conversion6) {
            /* Transition: '<S26>:13' */
            mem = rtb_Conversion6;
          } else {
            /* Transition: '<S26>:6' */
          }
        }
      }
    }

    /* End of Chart: '<S21>/Select_StPlasObjB' */

    /* Outputs for IfAction SubSystem: '<S6>/If Action Subsystem1' incorporates:
     *  ActionPort: '<S20>/Action Port'
     */
    /* SignalConversion generated from: '<S20>/IdxSpareCutOff0' incorporates:
     *  SignalConversion generated from: '<S21>/IdxSpareCutOff0'
     */
    IdxSpareCutOff0_0 = mem;

    /* End of Outputs for SubSystem: '<S6>/If Action Subsystem1' */

    /* DataTypeConversion: '<S25>/Data Type Conversion3' */
    rtb_DataTypeConversion3 = (int16_T)((uint32_T)(((uint32_T)mem) << ((uint32_T)
      11)));

    /* S-Function (RateLimiter_S16): '<S31>/RateLimiter_S16' incorporates:
     *  Constant: '<S25>/MAXRTIDXSPCTF'
     *  Constant: '<S25>/MINRTIDXSPCTF'
     */
    RateLimiter_S16( &rtb_RateLimiter_S16, rtb_DataTypeConversion3,
                    IdxCtfCtrl_T10ms_DW.Memory_PreviousInput, MINRTIDXSPCTF,
                    MAXRTIDXSPCTF);

    /* DataTypeConversion: '<S25>/Data Type Conversion2' */
    IdxSpareCutOff = (uint8_T)((int32_T)(((int32_T)rtb_RateLimiter_S16) >>
      ((uint32_T)11)));

    /* Update for Memory: '<S25>/Memory' */
    IdxCtfCtrl_T10ms_DW.Memory_PreviousInput = rtb_RateLimiter_S16;

    /* End of Outputs for SubSystem: '<S6>/Subsystem' */
  } else {
    /* Outputs for IfAction SubSystem: '<S6>/If Action Subsystem1' incorporates:
     *  ActionPort: '<S20>/Action Port'
     */
    /* SignalConversion generated from: '<S20>/IdxSpareCutOff' incorporates:
     *  Constant: '<S20>/ZERO'
     */
    IdxSpareCutOff = 0U;

    /* SignalConversion generated from: '<S20>/IdxSpareCutOff0' incorporates:
     *  Constant: '<S20>/ZERO'
     */
    IdxSpareCutOff0_0 = 0U;

    /* End of Outputs for SubSystem: '<S6>/If Action Subsystem1' */
  }

  /* DataTypeConversion: '<S6>/Data Type Conversion' */
  IdxCtfCtrl_T10ms_DW.Merge = IdxSpareCutOff;
  IdxCtfCtrl_T10ms_DW.mem = mem;

  /* Outputs for IfAction SubSystem: '<S6>/If Action Subsystem1' incorporates:
   *  ActionPort: '<S20>/Action Port'
   */
  /* If: '<S6>/If' incorporates:
   *  SignalConversion generated from: '<S20>/IdxSpareCutOff0'
   */
  IdxSpareCutOff0 = IdxSpareCutOff0_0;

  /* End of Outputs for SubSystem: '<S6>/If Action Subsystem1' */
}

/* Model step function */
void Trig_IdxCtfCtrl_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S1>/Init'
   */
  IdxCtfCtrl_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_IdxCtfCtrl_PreTdc(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTDC' incorporates:
   *  SubSystem: '<S1>/PreTDC'
   */
  IdxCtfCtrl_PreTDC();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTDC' */
}

/* Model step function */
void Trig_IdxCtfCtrl_T10ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  IdxCtfCtrl_T10ms();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
}

/* Initialize for function-call system: '<S1>/Init' */
void IdxCtfCtrl_Init_initialize(void)
{
  /* custom signals */
  IDIdxCutoff = 0U;
}

/* Initialize for function-call system: '<S1>/PreTDC' */
void IdxCtfCtrl_PreTDC_initialize(void)
{
  /* custom signals */
  PatternCtfTDC = 0U;
  IdxCutoff = 0U;
  CntCtfTDC = 0U;
  (void) memset((void *)&IdxCtfCtrl_PreTDC_DW, 0,
                sizeof(rtDW_PreTDC_IdxCtfCtrl_T));
}

/* Initialize for function-call system: '<S1>/T10ms' */
void IdxCtfCtrl_T10ms_initialize(void)
{
  /* custom signals */
  IdxSpareCutOff0 = 0U;
  (void) memset((void *)&IdxCtfCtrl_T10ms_DW, 0,
                sizeof(rtDW_T10ms_IdxCtfCtrl_T));
}

/* Model initialize function */
void IdxCtfCtrl_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  IdxCtfFlg = 0U;

  {
    int32_T i;
    for (i = 0; i < 4; i++) {
      VtIdxCtfFlg[i] = 0U;
    }
  }

  {
    int32_T i;
    for (i = 0; i < 4; i++) {
      VtIdxCtfFlgBuff[i] = 0U;
    }
  }

  IdxSpareCutOff = 0U;

  /* Initialize subsystem data */
  IdxCtfCtrl_Init_initialize();
  IdxCtfCtrl_PreTDC_initialize();
  IdxCtfCtrl_T10ms_initialize();

  {
    int32_T i;

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTDC' incorporates:
     *  SubSystem: '<S1>/PreTDC'
     */
    IdxCtfCtrl_PreTDC_Init();

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTDC' */
    /* SystemInitialize for Merge: '<S4>/Merge2' */
    IdxCtfFlg = 0U;
    for (i = 0; i < 4; i++) {
      /* SystemInitialize for Merge: '<S4>/Merge3' */
      VtIdxCtfFlg[(i)] = 0U;

      /* SystemInitialize for Merge: '<S4>/Merge4' incorporates:
       *  Merge: '<S4>/Merge3'
       */
      VtIdxCtfFlgBuff[(i)] = 0U;
    }

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    IdxCtfCtrl_T10ms_Init();

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */

    /* SystemInitialize for Merge: '<S4>/Merge1' */
    IdxSpareCutOff = 0U;
  }
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_IDXCTFCTRL_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
