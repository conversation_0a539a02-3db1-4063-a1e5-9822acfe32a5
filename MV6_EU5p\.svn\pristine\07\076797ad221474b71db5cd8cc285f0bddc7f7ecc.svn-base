/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_CCP_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ccp.h"
#include "typedefs.h"
#include "can.h"
#include "sys.h"
#include "canmgm.h"
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint8_t buffsel;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CAN_TxCcpData -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None 
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 /* static int16_t CAN_TxCcpData(uint8_t* pData); */


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * GetBuffer - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t GetBuffer(void)
{
    int16_t retVal = NO_ERROR;
    
    if(CAN_GetTxBufferStatus(CCP_CAN, buffsel)==NO_ERROR)
    {
        buffsel = CCP_START_OF_RANGE;
    }
    else
    {
        buffsel++;

        if(buffsel>CCP_END_OF_RANGE)
        {
            buffsel = CCP_END_OF_RANGE;
            retVal = CAN_TX_BUSY;
        }
        else
        {
            if(CAN_GetTxBufferStatus(CCP_CAN, buffsel)!=NO_ERROR)
            {
                retVal = CAN_TX_BUSY;
            }
        }
    }
    return retVal;
} 
 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CAN_TxCcpData - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
/* AM - unbuild to avoid MISRA 2004 Rule 14.1. */
#if 0 
static int16_t CAN_TxCcpData(uint8_t* pData)
{

    int16_t res = CAN_TX_BUSY;


#if CAN_CHA_EN 

    if(CCP_CAN==FLEXCAN_A)
    {

#ifdef CAN_CHA_BUF0_DIR
#if ( (CAN_CHA_BUF0_DIR==CAN_TX)  &&  (CAN_CHA_BUF0_LID == ID_CCP_DTO ) )
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 0)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,0, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF1_DIR
#if ((CAN_CHA_BUF1_DIR==CAN_TX) && (CAN_CHA_BUF1_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 1)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,1, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF2_DIR
#if ((CAN_CHA_BUF2_DIR==CAN_TX) && (CAN_CHA_BUF2_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 2)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,2, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF3_DIR
#if ((CAN_CHA_BUF3_DIR==CAN_TX) && (CAN_CHA_BUF3_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 3)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,3, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF4_DIR
#if ((CAN_CHA_BUF4_DIR==CAN_TX) && (CAN_CHA_BUF4_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 4)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,4, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF5_DIR
#if ((CAN_CHA_BUF5_DIR==CAN_TX) && (CAN_CHA_BUF5_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 5)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,5, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF6_DIR
#if ((CAN_CHA_BUF6_DIR==CAN_TX) && (CAN_CHA_BUF6_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 6)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,6, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF7_DIR
#if ((CAN_CHA_BUF7_DIR==CAN_TX) && (CAN_CHA_BUF7_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 7)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,7, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF8_DIR
#if ((CAN_CHA_BUF8_DIR==CAN_TX) && (CAN_CHA_BUF8_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 8)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,8, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF9_DIR
#if ((CAN_CHA_BUF9_DIR==CAN_TX) && (CAN_CHA_BUF9_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 9)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,9, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF10_DIR
#if ((CAN_CHA_BUF10_DIR==CAN_TX) && (CAN_CHA_BUF10_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 10)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,10, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF11_DIR
#if ((CAN_CHA_BUF11_DIR==CAN_TX) && (CAN_CHA_BUF11_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 11)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,11, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF12_DIR
#if ((CAN_CHA_BUF12_DIR==CAN_TX) && (CAN_CHA_BUF12_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 12)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,12, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF13_DIR
#if ((CAN_CHA_BUF13_DIR==CAN_TX) && (CAN_CHA_BUF13_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 13)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,13, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF14_DIR
#if ((CAN_CHA_BUF14_DIR==CAN_TX) && (CAN_CHA_BUF14_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 14)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,14, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF15_DIR
#if ((CAN_CHA_BUF15_DIR==CAN_TX) && (CAN_CHA_BUF15_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 15)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,15, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF16_DIR
#if ((CAN_CHA_BUF16_DIR==CAN_TX) && (CAN_CHA_BUF16_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 16)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,16, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF17_DIR
#if ((CAN_CHA_BUF17_DIR==CAN_TX) && (CAN_CHA_BUF17_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 17)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,17, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF18_DIR
#if ((CAN_CHA_BUF18_DIR==CAN_TX) && (CAN_CHA_BUF18_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 18)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,18, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF19_DIR
#if ((CAN_CHA_BUF19_DIR==CAN_TX) && (CAN_CHA_BUF19_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 19)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,19, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF20_DIR
#if ((CAN_CHA_BUF20_DIR==CAN_TX) && (CAN_CHA_BUF20_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 20)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,20, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF21_DIR
#if ((CAN_CHA_BUF21_DIR==CAN_TX) && (CAN_CHA_BUF21_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 21)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,21, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF22_DIR
#if ((CAN_CHA_BUF22_DIR==CAN_TX) && (CAN_CHA_BUF22_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 22)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,22, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF23_DIR
#if ((CAN_CHA_BUF23_DIR==CAN_TX) && (CAN_CHA_BUF23_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 23)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,23, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF24_DIR
#if ((CAN_CHA_BUF24_DIR==CAN_TX) && (CAN_CHA_BUF24_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 24)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,24, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF25_DIR
#if ((CAN_CHA_BUF25_DIR==CAN_TX) && (CAN_CHA_BUF25_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 25)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,25, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF26_DIR
#if ((CAN_CHA_BUF26_DIR==CAN_TX) && (CAN_CHA_BUF26_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 26)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,26, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF27_DIR
#if ((CAN_CHA_BUF27_DIR==CAN_TX) && (CAN_CHA_BUF27_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 27)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,27, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF28_DIR
#if ((CAN_CHA_BUF28_DIR==CAN_TX) && (CAN_CHA_BUF28_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 28)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,28, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF29_DIR
#if ((CAN_CHA_BUF29_DIR==CAN_TX) && (CAN_CHA_BUF29_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 29)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,29, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF30_DIR
#if ((CAN_CHA_BUF30_DIR==CAN_TX) && (CAN_CHA_BUF30_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 30)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,30, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF31_DIR
#if ((CAN_CHA_BUF31_DIR==CAN_TX) && (CAN_CHA_BUF31_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 31)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,31, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF32_DIR
#if ((CAN_CHA_BUF32_DIR==CAN_TX) && (CAN_CHA_BUF32_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN,32)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,32, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF33_DIR
#if ((CAN_CHA_BUF33_DIR==CAN_TX) && (CAN_CHA_BUF33_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 33)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,33, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF34_DIR
#if ((CAN_CHA_BUF34_DIR==CAN_TX) && (CAN_CHA_BUF34_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 34)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,34, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF35_DIR
#if ((CAN_CHA_BUF35_DIR==CAN_TX) && (CAN_CHB_BUF35_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 35)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,35, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF36_DIR
#if ((CAN_CHA_BUF36_DIR==CAN_TX) && (CAN_CHA_BUF36_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 36)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,36, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF37_DIR
#if ((CAN_CHA_BUF37_DIR==CAN_TX) && (CAN_CHA_BUF37_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 37)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,37, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF38_DIR
#if ((CAN_CHA_BUF38_DIR==CAN_TX) && (CAN_CHA_BUF38_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 38)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,38, pData);
        //return res;Misra 14.7
        }
#endif
#endif


#ifdef CAN_CHA_BUF39_DIR
#if ((CAN_CHA_BUF39_DIR==CAN_TX) && (CAN_CHA_BUF39_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 39)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,39, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF40_DIR
#if ((CAN_CHA_BUF40_DIR==CAN_TX) && (CAN_CHA_BUF40_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 40)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,40, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF41_DIR
#if ((CAN_CHA_BUF41_DIR==CAN_TX) && (CAN_CHA_BUF41_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 41)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,41, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF42_DIR
#if ((CAN_CHA_BUF42_DIR==CAN_TX) && (CAN_CHA_BUF42_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 42)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,42, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF43_DIR
#if ((CAN_CHA_BUF43_DIR==CAN_TX) && (CAN_CHA_BUF43_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 43)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,43, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF44_DIR
#if ((CAN_CHA_BUF44_DIR==CAN_TX) && (CAN_CHA_BUF44_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 44)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,44, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF45_DIR
#if ((CAN_CHA_BUF45_DIR==CAN_TX) && (CAN_CHA_BUF45_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 45)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,45, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF46_DIR
#if ((CAN_CHA_BUF46_DIR==CAN_TX) && (CAN_CHA_BUF46_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 46)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,46, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF47_DIR
#if ((CAN_CHA_BUF47_DIR==CAN_TX) && (CAN_CHA_BUF47_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 47)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,47, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF48_DIR
#if ((CAN_CHA_BUF48_DIR==CAN_TX) && (CAN_CHA_BUF48_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 48)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,48, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF49_DIR
#if ((CAN_CHA_BUF49_DIR==CAN_TX) && (CAN_CHA_BUF49_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 49)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,49, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF50_DIR
#if ((CAN_CHA_BUF50_DIR==CAN_TX) && (CAN_CHA_BUF50_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 50)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,50, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF51_DIR
#if ((CAN_CHA_BUF51_DIR==CAN_TX) && (CAN_CHA_BUF51_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 51)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,51, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF52_DIR
#if ((CAN_CHA_BUF52_DIR==CAN_TX) && (CAN_CHA_BUF52_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 52)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,52, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF53_DIR
#if ((CAN_CHA_BUF53_DIR==CAN_TX) && (CAN_CHA_BUF53_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 53)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,53, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF54_DIR
#if ((CAN_CHA_BUF54_DIR==CAN_TX) && (CAN_CHA_BUF54_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 54)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,54, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF55_DIR
#if ((CAN_CHA_BUF55_DIR==CAN_TX) && (CAN_CHA_BUF55_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 55)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,55, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF56_DIR
#if ((CAN_CHA_BUF56_DIR==CAN_TX) && (CAN_CHA_BUF56_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 56)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,56, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF57_DIR
#if ((CAN_CHA_BUF57_DIR==CAN_TX) && (CAN_CHA_BUF57_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 57)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,57, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF58_DIR
#if ((CAN_CHA_BUF58_DIR==CAN_TX) && (CAN_CHA_BUF58_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 58)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,58, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF59_DIR
#if ((CAN_CHA_BUF59_DIR==CAN_TX) && (CAN_CHA_BUF59_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 59)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,59, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF60_DIR
#if ((CAN_CHA_BUF60_DIR==CAN_TX) && (CAN_CHA_BUF60_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 60)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,60, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF61_DIR
#if ((CAN_CHA_BUF61_DIR==CAN_TX) && (CAN_CHA_BUF61_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 61)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,61, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF62_DIR
#if ((CAN_CHA_BUF62_DIR==CAN_TX) && (CAN_CHA_BUF62_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 62)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,62, pData);
        //return res;Misra 14.7
        }
#endif
#endif

#ifdef CAN_CHA_BUF63_DIR
#if ((CAN_CHA_BUF63_DIR==CAN_TX) && (CAN_CHA_BUF63_LID == ID_CCP_DTO))
        if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 63)==NO_ERROR)){
        res=CAN_TxData (CCP_CAN,63, pData);
        //return res;Misra 14.7
        }
#endif
#endif

        }

#endif  //CAN_CHA_EN

#if (TARGET_TYPE == MPC5554)
#if CAN_CHB_EN 

    if(CCP_CAN==FLEXCAN_B){

#ifdef CAN_CHB_BUF0_DIR
#if ( (CAN_CHB_BUF0_DIR==CAN_TX)  &&  (CAN_CHB_BUF0_LID == ID_CCP_DTO ) )
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 0)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,0, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF1_DIR
#if ((CAN_CHB_BUF1_DIR==CAN_TX) && (CAN_CHB_BUF1_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 1)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,1, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF2_DIR
#if ((CAN_CHB_BUF2_DIR==CAN_TX) && (CAN_CHB_BUF2_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 2)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,2, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF3_DIR
#if ((CAN_CHB_BUF3_DIR==CAN_TX) && (CAN_CHB_BUF3_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 3)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,3, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF4_DIR
#if ((CAN_CHB_BUF4_DIR==CAN_TX) && (CAN_CHB_BUF4_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 4)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,4, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF5_DIR
#if ((CAN_CHB_BUF5_DIR==CAN_TX) && (CAN_CHB_BUF5_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 5)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,5, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF6_DIR
#if ((CAN_CHB_BUF6_DIR==CAN_TX) && (CAN_CHB_BUF6_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 6)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,6, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF7_DIR
#if ((CAN_CHB_BUF7_DIR==CAN_TX) && (CAN_CHB_BUF7_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 7)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,7, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF8_DIR
#if ((CAN_CHB_BUF8_DIR==CAN_TX) && (CAN_CHB_BUF8_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 8)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,8, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF9_DIR
#if ((CAN_CHB_BUF9_DIR==CAN_TX) && (CAN_CHB_BUF9_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 9)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,9, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF10_DIR
#if ((CAN_CHB_BUF10_DIR==CAN_TX) && (CAN_CHB_BUF10_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 10)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,10, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF11_DIR
#if ((CAN_CHB_BUF11_DIR==CAN_TX) && (CAN_CHB_BUF11_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 11)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,11, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF12_DIR
#if ((CAN_CHB_BUF12_DIR==CAN_TX) && (CAN_CHB_BUF12_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 12)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,12, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF13_DIR
#if ((CAN_CHB_BUF13_DIR==CAN_TX) && (CAN_CHB_BUF13_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 13)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,13, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF14_DIR
#if ((CAN_CHB_BUF14_DIR==CAN_TX) && (CAN_CHB_BUF14_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 14)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,14, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF15_DIR
#if ((CAN_CHB_BUF15_DIR==CAN_TX) && (CAN_CHB_BUF15_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 15)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,15, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF16_DIR
#if ((CAN_CHB_BUF16_DIR==CAN_TX) && (CAN_CHB_BUF16_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 16)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,16, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF17_DIR
#if ((CAN_CHB_BUF17_DIR==CAN_TX) && (CAN_CHB_BUF17_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 17)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,17, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF18_DIR
#if ((CAN_CHB_BUF18_DIR==CAN_TX) && (CAN_CHB_BUF18_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 18)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,18, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF19_DIR
#if ((CAN_CHB_BUF19_DIR==CAN_TX) && (CAN_CHB_BUF19_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 19)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,19, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF20_DIR
#if ((CAN_CHB_BUF20_DIR==CAN_TX) && (CAN_CHB_BUF20_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 20)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,20, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF21_DIR
#if ((CAN_CHB_BUF21_DIR==CAN_TX) && (CAN_CHB_BUF21_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 21)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,21, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF22_DIR
#if ((CAN_CHB_BUF22_DIR==CAN_TX) && (CAN_CHB_BUF22_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 22)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,22, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF23_DIR
#if ((CAN_CHB_BUF23_DIR==CAN_TX) && (CAN_CHB_BUF23_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 23)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,23, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF24_DIR
#if ((CAN_CHB_BUF24_DIR==CAN_TX) && (CAN_CHB_BUF24_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 24)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,24, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF25_DIR
#if ((CAN_CHB_BUF25_DIR==CAN_TX) && (CAN_CHB_BUF25_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 25)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,25, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF26_DIR
#if ((CAN_CHB_BUF26_DIR==CAN_TX) && (CAN_CHB_BUF26_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 26)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,26, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF27_DIR
#if ((CAN_CHB_BUF27_DIR==CAN_TX) && (CAN_CHB_BUF27_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 27)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,27, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF28_DIR
#if ((CAN_CHB_BUF28_DIR==CAN_TX) && (CAN_CHB_BUF28_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 28)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,28, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF29_DIR
#if ((CAN_CHB_BUF29_DIR==CAN_TX) && (CAN_CHB_BUF29_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 29)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,29, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF30_DIR
#if ((CAN_CHB_BUF30_DIR==CAN_TX) && (CAN_CHB_BUF30_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 30)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,30, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF31_DIR
#if ((CAN_CHB_BUF31_DIR==CAN_TX) && (CAN_CHB_BUF31_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 31)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,31, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF32_DIR
#if ((CAN_CHB_BUF32_DIR==CAN_TX) && (CAN_CHB_BUF32_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 32)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,32, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF33_DIR
#if ((CAN_CHB_BUF33_DIR==CAN_TX) && (CAN_CHB_BUF33_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 33)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,33, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF34_DIR
#if ((CAN_CHB_BUF34_DIR==CAN_TX) && (CAN_CHB_BUF34_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 34)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,34, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF35_DIR
#if ((CAN_CHB_BUF35_DIR==CAN_TX) && (CAN_CHB_BUF35_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 35)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,35, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF36_DIR
#if ((CAN_CHB_BUF36_DIR==CAN_TX) && (CAN_CHB_BUF36_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 36)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,36, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF37_DIR
#if ((CAN_CHB_BUF37_DIR==CAN_TX) && (CAN_CHB_BUF37_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 37)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,37, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF38_DIR
#if ((CAN_CHB_BUF38_DIR==CAN_TX) && (CAN_CHB_BUF38_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 38)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,38, pData);
    //return res;Misra 14.7
    }
#endif
#endif


#ifdef CAN_CHB_BUF39_DIR
#if ((CAN_CHB_BUF39_DIR==CAN_TX) && (CAN_CHB_BUF39_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 39)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,39, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF40_DIR
#if ((CAN_CHB_BUF40_DIR==CAN_TX) && (CAN_CHB_BUF40_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 40)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,40, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF41_DIR
#if ((CAN_CHB_BUF41_DIR==CAN_TX) && (CAN_CHB_BUF41_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 41)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,41, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF42_DIR
#if ((CAN_CHB_BUF42_DIR==CAN_TX) && (CAN_CHB_BUF42_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 42)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,42, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF43_DIR
#if ((CAN_CHB_BUF43_DIR==CAN_TX) && (CAN_CHB_BUF43_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 43)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,43, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF44_DIR
#if ((CAN_CHB_BUF44_DIR==CAN_TX) && (CAN_CHB_BUF44_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 44)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,44, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF45_DIR
#if ((CAN_CHB_BUF45_DIR==CAN_TX) && (CAN_CHB_BUF45_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 45)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,45, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF46_DIR
#if ((CAN_CHB_BUF46_DIR==CAN_TX) && (CAN_CHB_BUF46_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 46)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,46, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF47_DIR
#if ((CAN_CHB_BUF47_DIR==CAN_TX) && (CAN_CHB_BUF47_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 47)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,47, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF48_DIR
#if ((CAN_CHB_BUF48_DIR==CAN_TX) && (CAN_CHB_BUF48_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 48)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,48, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF49_DIR
#if ((CAN_CHB_BUF49_DIR==CAN_TX) && (CAN_CHB_BUF49_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 49)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,49, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF50_DIR
#if ((CAN_CHB_BUF50_DIR==CAN_TX) && (CAN_CHB_BUF50_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 50)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,50, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF51_DIR
#if ((CAN_CHB_BUF51_DIR==CAN_TX) && (CAN_CHB_BUF51_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 51)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,51, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF52_DIR
#if ((CAN_CHB_BUF52_DIR==CAN_TX) && (CAN_CHB_BUF52_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 52)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,52, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF53_DIR
#if ((CAN_CHB_BUF53_DIR==CAN_TX) && (CAN_CHB_BUF53_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 53)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,53, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF54_DIR
#if ((CAN_CHB_BUF54_DIR==CAN_TX) && (CAN_CHB_BUF54_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 54)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,54, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF55_DIR
#if ((CAN_CHB_BUF55_DIR==CAN_TX) && (CAN_CHB_BUF55_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 55)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,55, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF56_DIR
#if ((CAN_CHB_BUF56_DIR==CAN_TX) && (CAN_CHB_BUF56_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 56)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,56, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF57_DIR
#if ((CAN_CHB_BUF57_DIR==CAN_TX) && (CAN_CHB_BUF57_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 57)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,57, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF58_DIR
#if ((CAN_CHB_BUF58_DIR==CAN_TX) && (CAN_CHB_BUF58_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 58)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,58, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF59_DIR
#if ((CAN_CHB_BUF59_DIR==CAN_TX) && (CAN_CHB_BUF59_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 59)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,59, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF60_DIR
#if ((CAN_CHB_BUF60_DIR==CAN_TX) && (CAN_CHB_BUF60_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 60)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,60, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF61_DIR
#if ((CAN_CHB_BUF61_DIR==CAN_TX) && (CAN_CHB_BUF61_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 61)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,61, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF62_DIR
#if ((CAN_CHB_BUF62_DIR==CAN_TX) && (CAN_CHB_BUF62_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 62)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,62, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHB_BUF63_DIR
#if ((CAN_CHB_BUF63_DIR==CAN_TX) && (CAN_CHB_BUF63_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 63)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,63, pData);
    //return res;Misra 14.7
    }
#endif
#endif
    }


#endif    //CAN_CHB_EN
#endif /* (TARGET_TYPE == MPC5554) */

#if CAN_CHC_EN 

    if(CCP_CAN==FLEXCAN_C) {

#ifdef CAN_CHC_BUF0_DIR
#if ( (CAN_CHC_BUF0_DIR==CAN_TX)  &&  (CAN_CHC_BUF0_LID == ID_CCP_DTO ) )
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 0)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,0, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF1_DIR
#if ((CAN_CHC_BUF1_DIR==CAN_TX) && (CAN_CHC_BUF1_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 1)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,1, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF2_DIR
#if ((CAN_CHC_BUF2_DIR==CAN_TX) && (CAN_CHC_BUF2_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 2)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,2, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF3_DIR
#if ((CAN_CHC_BUF3_DIR==CAN_TX) && (CAN_CHC_BUF3_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 3)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,3, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF4_DIR
#if ((CAN_CHC_BUF4_DIR==CAN_TX) && (CAN_CHC_BUF4_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 4)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,4, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF5_DIR
#if ((CAN_CHC_BUF5_DIR==CAN_TX) && (CAN_CHC_BUF5_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 5)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,5, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF6_DIR
#if ((CAN_CHC_BUF6_DIR==CAN_TX) && (CAN_CHC_BUF6_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 6)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,6, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF7_DIR
#if ((CAN_CHC_BUF7_DIR==CAN_TX) && (CAN_CHC_BUF7_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 7)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,7, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF8_DIR
#if ((CAN_CHC_BUF8_DIR==CAN_TX) && (CAN_CHC_BUF8_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 8)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,8, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF9_DIR
#if ((CAN_CHC_BUF9_DIR==CAN_TX) && (CAN_CHC_BUF9_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 9)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,9, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF10_DIR
#if ((CAN_CHC_BUF10_DIR==CAN_TX) && (CAN_CHC_BUF10_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 10)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,10, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF11_DIR
#if ((CAN_CHC_BUF11_DIR==CAN_TX) && (CAN_CHC_BUF11_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 11)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,11, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF12_DIR
#if ((CAN_CHC_BUF12_DIR==CAN_TX) && (CAN_CHC_BUF12_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 12)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,12, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF13_DIR
#if ((CAN_CHC_BUF13_DIR==CAN_TX) && (CAN_CHC_BUF13_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 13)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,13, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF14_DIR
#if ((CAN_CHC_BUF14_DIR==CAN_TX) && (CAN_CHC_BUF14_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 14)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,14, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF15_DIR
#if ((CAN_CHC_BUF15_DIR==CAN_TX) && (CAN_CHC_BUF15_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 15)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,15, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF16_DIR
#if ((CAN_CHC_BUF16_DIR==CAN_TX) && (CAN_CHC_BUF16_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 16)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,16, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF17_DIR
#if ((CAN_CHC_BUF17_DIR==CAN_TX) && (CAN_CHC_BUF17_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 17)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,17, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF18_DIR
#if ((CAN_CHC_BUF18_DIR==CAN_TX) && (CAN_CHC_BUF18_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 18)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,18, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF19_DIR
#if ((CAN_CHC_BUF19_DIR==CAN_TX) && (CAN_CHC_BUF19_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 19)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,19, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF20_DIR
#if ((CAN_CHC_BUF20_DIR==CAN_TX) && (CAN_CHC_BUF20_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 20)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,20, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF21_DIR
#if ((CAN_CHC_BUF21_DIR==CAN_TX) && (CAN_CHC_BUF21_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 21)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,21, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF22_DIR
#if ((CAN_CHC_BUF22_DIR==CAN_TX) && (CAN_CHC_BUF22_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 22)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,22, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF23_DIR
#if ((CAN_CHC_BUF23_DIR==CAN_TX) && (CAN_CHC_BUF23_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 23)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,23, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF24_DIR
#if ((CAN_CHC_BUF24_DIR==CAN_TX) && (CAN_CHC_BUF24_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 24)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,24, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF25_DIR
#if ((CAN_CHC_BUF25_DIR==CAN_TX) && (CAN_CHC_BUF25_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 25)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,25, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF26_DIR
#if ((CAN_CHC_BUF26_DIR==CAN_TX) && (CAN_CHC_BUF26_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 26)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,26, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF27_DIR
#if ((CAN_CHC_BUF27_DIR==CAN_TX) && (CAN_CHC_BUF27_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 27)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,27, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF28_DIR
#if ((CAN_CHC_BUF28_DIR==CAN_TX) && (CAN_CHC_BUF28_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 28)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,28, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF29_DIR
#if ((CAN_CHC_BUF29_DIR==CAN_TX) && (CAN_CHC_BUF29_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 29)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,29, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF30_DIR
#if ((CAN_CHC_BUF30_DIR==CAN_TX) && (CAN_CHC_BUF30_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 30)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,30, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF31_DIR
#if ((CAN_CHC_BUF31_DIR==CAN_TX) && (CAN_CHC_BUF31_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 31)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,31, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF32_DIR
#if ((CAN_CHC_BUF32_DIR==CAN_TX) && (CAN_CHC_BUF32_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN,32)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,32, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF33_DIR
#if ((CAN_CHC_BUF33_DIR==CAN_TX) && (CAN_CHC_BUF33_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 33)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,33, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF34_DIR
#if ((CAN_CHC_BUF34_DIR==CAN_TX) && (CAN_CHC_BUF34_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 34)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,34, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF35_DIR
#if ((CAN_CHC_BUF35_DIR==CAN_TX) && (CAN_CHC_BUF35_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 35)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,35, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF36_DIR
#if ((CAN_CHC_BUF36_DIR==CAN_TX) && (CAN_CHC_BUF36_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 36)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,36, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF37_DIR
#if ((CAN_CHC_BUF37_DIR==CAN_TX) && (CAN_CHC_BUF37_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 37)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,37, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF38_DIR
#if ((CAN_CHC_BUF38_DIR==CAN_TX) && (CAN_CHC_BUF38_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 38)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,38, pData);
    //return res;Misra 14.7
    }
#endif
#endif


#ifdef CAN_CHC_BUF39_DIR
#if ((CAN_CHC_BUF39_DIR==CAN_TX) && (CAN_CHC_BUF39_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 39)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,39, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF40_DIR
#if ((CAN_CHC_BUF40_DIR==CAN_TX) && (CAN_CHC_BUF40_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 40)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,40, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF41_DIR
#if ((CAN_CHC_BUF41_DIR==CAN_TX) && (CAN_CHC_BUF41_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 41)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,41, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF42_DIR
#if ((CAN_CHC_BUF42_DIR==CAN_TX) && (CAN_CHC_BUF42_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 42)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,42, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF43_DIR
#if ((CAN_CHC_BUF43_DIR==CAN_TX) && (CAN_CHC_BUF43_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 43)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,43, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF44_DIR
#if ((CAN_CHC_BUF44_DIR==CAN_TX) && (CAN_CHC_BUF44_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 44)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,44, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF45_DIR
#if ((CAN_CHC_BUF45_DIR==CAN_TX) && (CAN_CHC_BUF45_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 45)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,45, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF46_DIR
#if ((CAN_CHC_BUF46_DIR==CAN_TX) && (CAN_CHC_BUF46_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 46)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,46, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF47_DIR
#if ((CAN_CHC_BUF47_DIR==CAN_TX) && (CAN_CHC_BUF47_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 47)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,47, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF48_DIR
#if ((CAN_CHC_BUF48_DIR==CAN_TX) && (CAN_CHC_BUF48_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 48)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,48, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF49_DIR
#if ((CAN_CHC_BUF49_DIR==CAN_TX) && (CAN_CHC_BUF49_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 49)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,49, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF50_DIR
#if ((CAN_CHC_BUF50_DIR==CAN_TX) && (CAN_CHC_BUF50_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 50)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,50, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF51_DIR
#if ((CAN_CHC_BUF51_DIR==CAN_TX) && (CAN_CHC_BUF51_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 51)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,51, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF52_DIR
#if ((CAN_CHC_BUF52_DIR==CAN_TX) && (CAN_CHC_BUF52_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 52)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,52, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF53_DIR
#if ((CAN_CHC_BUF53_DIR==CAN_TX) && (CAN_CHC_BUF53_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 53)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,53, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF54_DIR
#if ((CAN_CHC_BUF54_DIR==CAN_TX) && (CAN_CHC_BUF54_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 54)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,54, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF55_DIR
#if ((CAN_CHC_BUF55_DIR==CAN_TX) && (CAN_CHC_BUF55_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 55)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,55, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF56_DIR
#if ((CAN_CHC_BUF56_DIR==CAN_TX) && (CAN_CHC_BUF56_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 56)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,56, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF57_DIR
#if ((CAN_CHC_BUF57_DIR==CAN_TX) && (CAN_CHC_BUF57_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 57)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,57, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF58_DIR
#if ((CAN_CHC_BUF58_DIR==CAN_TX) && (CAN_CHC_BUF58_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 58)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,58, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF59_DIR
#if ((CAN_CHC_BUF59_DIR==CAN_TX) && (CAN_CHC_BUF59_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 59)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,59, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF60_DIR
#if ((CAN_CHC_BUF60_DIR==CAN_TX) && (CAN_CHC_BUF60_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 60)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,60, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF61_DIR
#if ((CAN_CHC_BUF61_DIR==CAN_TX) && (CAN_CHC_BUF61_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 61)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,61, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF62_DIR
#if ((CAN_CHC_BUF62_DIR==CAN_TX) && (CAN_CHC_BUF62_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 62)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,62, pData);
    //return res;Misra 14.7
    }
#endif
#endif

#ifdef CAN_CHC_BUF63_DIR
#if ((CAN_CHC_BUF63_DIR==CAN_TX) && (CAN_CHC_BUF63_LID == ID_CCP_DTO))
    if((res==CAN_TX_BUSY) && (CAN_GetTxBufferStatus(CCP_CAN, 63)==NO_ERROR)){
    res=CAN_TxData (CCP_CAN,63, pData);
    //return res;Misra 14.7
    }
#endif
#endif

    }

#endif     //CAN_CHC_EN

    return res;
}
#endif

/*********************************************************************************************/
/* Porting dal ramo FE4 */



#endif // _BUILD_CCP_
