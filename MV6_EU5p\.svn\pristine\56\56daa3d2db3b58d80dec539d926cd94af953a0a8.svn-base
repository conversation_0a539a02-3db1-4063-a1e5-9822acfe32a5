/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_EXHVALMGM_SBS_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Breakpoints of target CME [N*m]
CALQUAL int16_T BKEXHVALCME[5] = 
{
 160, 320, 640, 1280, 2240
};
//Breakpoints of target Rpm [rpm]
CALQUAL uint16_T BKEXHVALRPM[6] = 
{
   3500u,   4500u,   5500u,   6500u,   7500u,   9000u
};
//Breakpoints of time for time-history [ms]
CALQUAL uint16_T BKEXHVALTIME[4] = 
{
 0u, 4000u, 4200u, 8000u
};
//Duty ExVDutyOut difference to start the searching plausibility [%]
CALQUAL uint16_T EXHVDIFSRC = 512u;   //(  2.00000000*256)
//Force ExhV Self trigger [flag]
CALQUAL uint8_T FOEXHVSELF =  0u;   // 0
//Force exh. gas valve target: 0 = Torque Law, 1 = no operations, 2 = Time history, 3 = no operations [status]
CALQUAL uint8_T FORCEEXHOBJ =  0u;   // 0
//Max Rate target limiter [%]
CALQUAL int16_T RATEANGTGRMAX = 512;   //(  2.00000000*256)
//Min Rate target limiter [%]
CALQUAL int16_T RATEANGTGRMIN = -512;   //( -2.00000000*256)
//Max Rate target Pby limiter [%]
CALQUAL int16_T RATEANGTGRPBYMAX = 51;   //(  0.19921875*256)
//Exh. valve angle target [%]
CALQUAL int16_T TBEXHVALANGTGT[5*6] = 
{
 1280, 1280, 1280, 1280, 1280, 1280,
 1280, 1280, 1280, 1280, 1280, 1280,
 11520, 11520, 11520, 11520, 11520, 11520,
 23040, 23040, 23040, 23040, 23040, 23040,
 24320, 24320, 24320, 24320, 24320, 24320
};
//Threshold Output and feedback position diagnosis plausibility [%]
CALQUAL uint16_T THREXHVPLADIAG = 768u;   //(  3.00000000*256)
//Threshold ExVDutyOut stability diag plausibility [%]
CALQUAL uint16_T THREXVDOUTSTAB = 384u;   //(  1.50000000*256)
//Threshold minimum of VBatt to enable ExhValve diagnosis and self [V]
CALQUAL uint16_T THVBATTEXHVDIAGEN = 176u;   //(11.0000*16)
//Time ExVDutyOut stability diag plausibility [ms]
CALQUAL uint16_T TIMEXVDOUTSTAB =     10u;   //    10
//Time to wait to execute Self and Position in EOL manual procedure [ms]
CALQUAL uint16_T TIMEXVSL =    700u;   //   700
//Target angle points of time-history [%]
CALQUAL int16_T VTEXHVALWAVE[4] = 
{
 -25600, 0, 0, 25600
};
//Angle target in Idle condition [%]
CALQUAL int16_T VTEXHVANGTGTIDLE[6] = 
{
 1280, 5632, 12800, 20480, 23040, 23040
};

#endif /* _BUILD_EXHVALMGM_ */

