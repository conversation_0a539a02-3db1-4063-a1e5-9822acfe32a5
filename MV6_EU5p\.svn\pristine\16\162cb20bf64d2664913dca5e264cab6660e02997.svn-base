/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           GearShiftMgm.c
 **  File Creation Date: 28-Feb-2024
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         GearShiftMgm
 **  Model Description:
 **  Model Version:      1.1792
 **  Model Author:       <PERSON> - Wed Jul 01 14:50:43 2009
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: Reilab - Wed Feb 28 16:56:03 2024
 **
 **  Last Saved Modification:   -
 **
 **
 *******************************************************************************
 **/

#include "GearShiftMgm.h"
#include "GearShiftMgm_private.h"

/*  Defines */

/* Named constants for Chart: '<S110>/Hys_<_x' */
#define GearShiftMgm_IN_ONE            ((uint8_T)1U)
#define GearShiftMgm_IN_ZERO           ((uint8_T)2U)

/* Named constants for Chart: '<S112>/Hys_>_x' */
#define GearShiftMgm_IN_ONE_ombf       ((uint8_T)1U)
#define GearShiftMgm_IN_ZERO_eh2f      ((uint8_T)2U)

/* Named constants for Chart: '<S7>/Chart' */
#define GearShif_IN_QSHIFT_BLIP_REQUEST ((uint8_T)1U)
#define GearShiftMg_IN_QSHIFT_BLIP_WAIT ((uint8_T)2U)
#define GearShiftMgm_IN_QSHIFT_CTF_WAIT ((uint8_T)5U)
#define GearShiftMgm_IN_QSHIFT_DISABLE ((uint8_T)6U)
#define GearShift_IN_QSHIFT_CTF_REDUCED ((uint8_T)3U)
#define GearShift_IN_QSHIFT_CTF_REQUEST ((uint8_T)4U)

/* Named constants for Chart: '<S6>/Chart' */
#define GearShi_IN_QSHIFT_CTF_WAIT_gbpu ((uint8_T)7U)
#define GearShif_IN_QSHIFT_DISABLE_o1bn ((uint8_T)8U)
#define GearShiftM_IN_QSHIFT_CTF_DOUBLE ((uint8_T)4U)
#define GearShift_IN_QSHIFT_BLP_REDUCED ((uint8_T)3U)
#define Gear_IN_QSHIFT_CTF_REDUCED_iegi ((uint8_T)5U)
#define Gear_IN_QSHIFT_CTF_REQUEST_lgvf ((uint8_T)6U)

/*  Data Types */

/* user code (top of source file) */
/* System '<Root>/GearShiftMgm' */
#ifdef _BUILD_GEARSHIFTMGM_

/**************************** GLOBAL DATA *************************************/
/*  Definitions */

/* Block signals (default storage) */
BlockIO_GearShiftMgm GearShiftMgm_B;

/* Block states (default storage) */
D_Work_GearShiftMgm GearShiftMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_GearShiftMgm GearShiftMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_GearShiftMgm GearShiftMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T CmeDriverQShift;

/* CmeDriver freezed at gearshift request */
int32_T CmeQsBlpIFiltHr;

/* CmeI quick shift blip High resolution filter output */
int32_T CmeQsBlpPFiltHr;

/* CmeP quick shift blip High resolution filter output */
int32_T CmeQsCtfIFiltHr;

/* CmeI quick shift ctf High resolution filter output */
int16_T CmeQsI;

/* CmeI quickShift */
int16_T CmeQsIFilt;

/* CmeI quickShift filtered */
uint8_T CmeQsIPeriod;

/* CmeQsI period of application */
int16_T CmeQsP;

/* CmeP quickShift */
int16_T CmeQsPFilt;

/* CmeP quickShift filtered */
uint8_T CtfGearShift;

/* Cut off Gear Shift Flag */
uint8_T FlgEnQuickShift;

/* Flag Enable Quick shift */
uint8_T FlgEnQuickShiftDn;

/* Flag Enable down Quick shift */
uint8_T FlgEnQuickShiftN;

/* Enable Neutral */
uint8_T FlgEnQuickShiftUp;

/* Flag Enable up Quick shift */
uint8_T FlgQSLow;

/* FlgQSLow */
uint8_T GearPosQShift;

/* Gear position at time of gear shift request */
uint16_T GearShiftBlpDnFOFK;

/* Gear shift Blip FOF K parameter */
uint16_T GearShiftBlpUpFOFK;

/* Gear shift Blip FOF K parameter */
uint16_T GearShiftCtfDnFOFK;

/* Gear shift Ctf FOF K parameter */
uint16_T GearShiftCtfUpFOFK;

/* Gear shift Ctf FOF K parameter */
uint32_T IDGearShiftMgm;

/* ID Version */
uint8_T QSBlpTime;

/* Blip Quick shift Cme increment Duration */
uint8_T QSBlpToTime;

/* Blip Quick shift Cme increment Duration */
uint8_T QSCntDlbTime;

/* QSCntDlbTime */
uint8_T QSCntDlbToTime;

/* QSCntDlbToTime */
uint8_T QSCtfPeriod;

/* Gear shift Cut Off Period withought offset applied */
uint8_T QSCtfTime;

/* Ctf Quick shift Cme increment Duration */
uint8_T QSGearDnSignal;

/* Quick Shift conditioned Down Signal */
uint8_T QSGearUpSignal;

/* Quick Shift conditioned Up Signal */
uint8_T QShiftCnt;

/* Count number of QGearShift Calculations (10ms) */
uint8_T QuickGearShiftBlp;

/* Flag blip Quick shift running */
uint8_T QuickGearShiftBlpOn;

/* Flag blip Quick shift running internal sig */
uint8_T QuickGearShiftCtf;

/* Flag ctf Quick shift running */
uint8_T QuickGearShiftCtfOn;

/* Flag ctf Quick shift running internal sig */
uint8_T QuickShiftPresence;

/* Quick Shift Strategy Enabled */
uint16_T RpmQShift;

/* Rpm at the beguinning of Quick Shift request */
uint8_T StQShift;

/* Quick Gear Shift status */

/*  Declarations  */

/* Forward declaration for local functions */
static void GearShiftMgm_QSHIFT_DISABLE(void);
static void GearShiftMg_QSHIFT_BLIP_REQUEST(void);
static void GearShiftMgm_QSHIFT_BLIP_WAIT(void);

/***************************** FILE SCOPE DATA ********************************/

/*************************** FUNCTIONS ****************************************/

/* Output and update for function-call system: '<S96>/fc_QsDnBlp_Calc' */
void GearShiftMgm_fc_QsDnBlp_Calc(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16;
  uint8_T rtb_Conversion3;
  int16_T rtb_Switch[8];
  int32_T i;

  /* Switch: '<S153>/Switch1' incorporates:
   *  Constant: '<S153>/VTCMEQSBLPDNI'
   *  Constant: '<S153>/VTCMEQSBLPDNICTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPDNICTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPDNI[i];
    }
  }

  /* End of Switch: '<S153>/Switch1' */

  /* DataTypeConversion: '<S155>/Conversion3' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S155>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S153>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_edjv = rtb_LookUp_IR_S16;

  /* Switch: '<S153>/Switch' incorporates:
   *  Constant: '<S153>/VTCMEQSBLPDNP'
   *  Constant: '<S153>/VTCMEQSBLPDNPCTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPDNPCTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPDNP[i];
    }
  }

  /* End of Switch: '<S153>/Switch' */

  /* DataTypeConversion: '<S156>/Conversion3' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S156>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S153>/Data Type Conversion1' */
  GearShiftMgm_B.CmeQsP_h0dh = rtb_LookUp_IR_S16;

  /* Selector: '<S154>/Selector2' incorporates:
   *  Constant: '<S154>/VTQSBLPDNTIME'
   *  Constant: '<S154>/VTQSBLPDNTIMECTF'
   *  Inport: '<Root>/CutoffFlg'
   *  Switch: '<S154>/Switch'
   */
  if (CutoffFlg != 0) {
    GearShiftMgm_B.QsBlpTime_co1y =
      VTQSBLPDNTIMECTF[GearShiftMgm_B.GearPosQShift_l0lg];
  } else {
    GearShiftMgm_B.QsBlpTime_co1y =
      VTQSBLPDNTIME[GearShiftMgm_B.GearPosQShift_l0lg];
  }

  /* End of Selector: '<S154>/Selector2' */
}

/* Output and update for function-call system: '<S96>/fc_QsUpBlp_Calc' */
void GearShiftMgm_fc_QsUpBlp_Calc(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_pgrc;
  uint8_T rtb_Conversion3;
  int16_T rtb_Switch[8];
  int32_T i;

  /* Switch: '<S166>/Switch1' incorporates:
   *  Constant: '<S166>/VTCMEQSBLPUPI'
   *  Constant: '<S166>/VTCMEQSBLPUPICTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPUPICTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPUPI[i];
    }
  }

  /* End of Switch: '<S166>/Switch1' */

  /* DataTypeConversion: '<S168>/Conversion3' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S168>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_pgrc, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S166>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_edjv = rtb_LookUp_IR_S16_pgrc;

  /* Switch: '<S166>/Switch' incorporates:
   *  Constant: '<S166>/VTCMEQSBLPUPP'
   *  Constant: '<S166>/VTCMEQSBLPUPPCTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPUPPCTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPUPP[i];
    }
  }

  /* End of Switch: '<S166>/Switch' */

  /* DataTypeConversion: '<S169>/Conversion3' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S169>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_pgrc, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S166>/Data Type Conversion1' */
  GearShiftMgm_B.CmeQsP_h0dh = rtb_LookUp_IR_S16_pgrc;

  /* Selector: '<S167>/Selector1' incorporates:
   *  Constant: '<S167>/VTQSBLPUPTIME'
   *  Constant: '<S167>/VTQSBLPUPTIMECTF'
   *  Inport: '<Root>/CutoffFlg'
   *  Switch: '<S167>/Switch'
   */
  if (CutoffFlg != 0) {
    GearShiftMgm_B.QsBlpTime_co1y =
      VTQSBLPUPTIMECTF[GearShiftMgm_B.GearPosQShift_l0lg];
  } else {
    GearShiftMgm_B.QsBlpTime_co1y =
      VTQSBLPUPTIME[GearShiftMgm_B.GearPosQShift_l0lg];
  }

  /* End of Selector: '<S167>/Selector1' */
}

/* Output and update for function-call system: '<S96>/fc_QsDnCtf_Calc' */
void GearShiftMgm_fc_QsDnCtf_Calc(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_jve1;
  uint8_T rtb_Conversion7;
  uint16_T rtb_Conversion2;
  uint8_T rtb_Conversion3;
  int32_T tmp;

  /* DataTypeConversion: '<S165>/Conversion6' */
  rtb_Conversion7 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S165>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_l0lg;

  /* DataTypeConversion: '<S165>/Conversion7' incorporates:
   *  Constant: '<S160>/Constant3'
   */
  rtb_Conversion3 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S165>/Look2D_IR_U8' incorporates:
   *  Constant: '<S160>/Constant2'
   *  Constant: '<S160>/TBQSCTFDNPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBQSCTFDNPERIOD[0],
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1, rtb_Conversion7,
               rtb_Conversion2, ((uint16_T)0U), rtb_Conversion3);

  /* DataTypeConversion: '<S160>/Gateway In7' incorporates:
   *  DataStoreWrite: '<S160>/Data Store Write3'
   */
  QSCtfPeriod = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S164>/Conversion6' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S164>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_l0lg;

  /* DataTypeConversion: '<S164>/Conversion7' incorporates:
   *  Constant: '<S160>/Constant1'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S164>/Look2D_IR_U8' incorporates:
   *  Constant: '<S160>/Constant'
   *  Constant: '<S160>/TBCMEQSDNIPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBCMEQSDNIPERIOD[0],
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1, rtb_Conversion3,
               rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* DataTypeConversion: '<S160>/Gateway In1' */
  GearShiftMgm_B.CmeQsIPeriod_gdc0 = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S163>/Conversion6' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMQSCTFOFFSET_dim_a3bk;

  /* DataTypeConversion: '<S163>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_l0lg;

  /* DataTypeConversion: '<S163>/Conversion7' incorporates:
   *  Constant: '<S160>/Constant5'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_S8): '<S163>/Look2D_IR_S8' incorporates:
   *  Constant: '<S160>/Constant4'
   *  Constant: '<S160>/TBQSCTFDNOFFSET'
   */
  Look2D_IR_S8( &rtb_LookUp_IR_S16_jve1, &TBQSCTFDNOFFSET[0],
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1_gdfa,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_c1sk,
               rtb_Conversion3, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* Sum: '<S160>/Sum1' incorporates:
   *  DataStoreWrite: '<S160>/Data Store Write3'
   *  DataTypeConversion: '<S160>/Gateway In2'
   */
  tmp = QSCtfPeriod + (rtb_LookUp_IR_S16_jve1 >> 8);
  if (tmp < 0) {
    tmp = 0;
  } else {
    if (tmp > 255) {
      tmp = 255;
    }
  }

  GearShiftMgm_B.QsCtfTime_nkkv = (uint8_T)tmp;

  /* End of Sum: '<S160>/Sum1' */

  /* DataTypeConversion: '<S161>/Conversion3' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5;

  /* S-Function (LookUp_IR_S16): '<S161>/LookUp_IR_S16' incorporates:
   *  Constant: '<S159>/VTCMEQSCTFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_jve1, &VTCMEQSCTFI[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1, rtb_Conversion3);

  /* DataTypeConversion: '<S159>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_edjv = rtb_LookUp_IR_S16_jve1;

  /* DataTypeConversion: '<S150>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  GearShiftMgm_B.CmeQsP_h0dh = CmeDriverP;
}

/* Output and update for function-call system: '<S96>/fc_QsUpCtf_Calc' */
void GearShiftMgm_fc_QsUpCtf_Calc(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_niog;
  uint8_T rtb_Conversion7;
  uint16_T rtb_Conversion2;
  uint8_T rtb_Conversion3;
  int32_T tmp;

  /* DataTypeConversion: '<S178>/Conversion6' */
  rtb_Conversion7 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S178>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_l0lg;

  /* DataTypeConversion: '<S178>/Conversion7' incorporates:
   *  Constant: '<S173>/Constant3'
   */
  rtb_Conversion3 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S178>/Look2D_IR_U8' incorporates:
   *  Constant: '<S173>/Constant2'
   *  Constant: '<S173>/TBQSCTFUPPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBQSCTFUPPERIOD[0],
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1, rtb_Conversion7,
               rtb_Conversion2, ((uint16_T)0U), rtb_Conversion3);

  /* DataTypeConversion: '<S173>/Gateway In7' incorporates:
   *  DataStoreWrite: '<S173>/Data Store Write3'
   */
  QSCtfPeriod = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S177>/Conversion6' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S177>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_l0lg;

  /* DataTypeConversion: '<S177>/Conversion7' incorporates:
   *  Constant: '<S173>/Constant1'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S177>/Look2D_IR_U8' incorporates:
   *  Constant: '<S173>/Constant'
   *  Constant: '<S173>/TBCMEQSUPIPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBCMEQSUPIPERIOD[0],
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1, rtb_Conversion3,
               rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* DataTypeConversion: '<S173>/Gateway In1' */
  GearShiftMgm_B.CmeQsIPeriod_gdc0 = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S176>/Conversion6' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMQSCTFOFFSET_dim_a3bk;

  /* DataTypeConversion: '<S176>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_l0lg;

  /* DataTypeConversion: '<S176>/Conversion7' incorporates:
   *  Constant: '<S173>/Constant5'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_S8): '<S176>/Look2D_IR_S8' incorporates:
   *  Constant: '<S173>/Constant4'
   *  Constant: '<S173>/TBQSCTFUPOFFSET'
   */
  Look2D_IR_S8( &rtb_LookUp_IR_S16_niog, &TBQSCTFUPOFFSET[0],
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1_gdfa,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_c1sk,
               rtb_Conversion3, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* Sum: '<S173>/Sum1' incorporates:
   *  DataStoreWrite: '<S173>/Data Store Write3'
   *  DataTypeConversion: '<S173>/Gateway In2'
   */
  tmp = QSCtfPeriod + (rtb_LookUp_IR_S16_niog >> 8);
  if (tmp < 0) {
    tmp = 0;
  } else {
    if (tmp > 255) {
      tmp = 255;
    }
  }

  GearShiftMgm_B.QsCtfTime_nkkv = (uint8_T)tmp;

  /* End of Sum: '<S173>/Sum1' */

  /* DataTypeConversion: '<S174>/Conversion3' */
  rtb_Conversion3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5;

  /* S-Function (LookUp_IR_S16): '<S174>/LookUp_IR_S16' incorporates:
   *  Constant: '<S172>/VTCMEQSCTFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_niog, &VTCMEQSCTFI[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1, rtb_Conversion3);

  /* DataTypeConversion: '<S172>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_edjv = rtb_LookUp_IR_S16_niog;

  /* DataTypeConversion: '<S152>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  GearShiftMgm_B.CmeQsP_h0dh = CmeDriverP;
}

/*
 * Output and update for function-call system:
 *    '<S7>/Calc_Ratio'
 *    '<S6>/Calc_Ratio'
 */
void GearShiftMgm_Calc_Ratio(int16_T rtu_outBus, uint16_T rtu_outBus_d4ae,
  int16_T rtu_inBus, uint16_T rtu_inBus_nye2, rtB_Calc_Ratio_GearShiftMgm
  *localB)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint8_T rtb_DataTypeConversion8;

  /* Constant: '<S89>/BKCMEGEARSHIFT_dim' */
  localB->BKCMEGEARSHIFT_dim_ddf5 = BKCMEGEARSHIFT_dim;

  /* Constant: '<S89>/BKRPMGEARSHIFT_dim' */
  localB->BKRPMGEARSHIFT_dim_emnh = BKRPMGEARSHIFT_dim;

  /* Constant: '<S89>/BKRPMQSCTFOFFSET_dim' */
  localB->BKRPMQSCTFOFFSET_dim_a3bk = BKRPMQSCTFOFFSET_dim;

  /* Constant: '<S89>/BKRPMQSGASPOS_dim' */
  localB->BKRPMQSGASPOS_dim_nyed = BKRPMQSGASPOS_dim;

  /* DataTypeConversion: '<S97>/Data Type Conversion8' */
  rtb_DataTypeConversion8 = (uint8_T)localB->BKCMEGEARSHIFT_dim_ddf5;

  /* S-Function (PreLookUpIdSearch_S16): '<S97>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S89>/BKCMEGEARSHIFT'
   */
  PreLookUpIdSearch_S16( &localB->PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, rtu_outBus,
                        &BKCMEGEARSHIFT[0], rtb_DataTypeConversion8);

  /* DataTypeConversion: '<S97>/Data Type Conversion1' */
  localB->DataTypeConversion1 = rtb_PreLookUpIdSearch_U16_o2;

  /* DataTypeConversion: '<S98>/Data Type Conversion8' */
  rtb_DataTypeConversion8 = (uint8_T)localB->BKCMEGEARSHIFT_dim_ddf5;

  /* S-Function (PreLookUpIdSearch_S16): '<S98>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S89>/BKCMEGEARSHIFT'
   */
  PreLookUpIdSearch_S16( &localB->PreLookUpIdSearch_S16_o1_ol1s,
                        &rtb_PreLookUpIdSearch_U16_o2, rtu_inBus,
                        &BKCMEGEARSHIFT[0], rtb_DataTypeConversion8);

  /* DataTypeConversion: '<S98>/Data Type Conversion1' */
  localB->DataTypeConversion1_ce1j = rtb_PreLookUpIdSearch_U16_o2;

  /* DataTypeConversion: '<S99>/Data Type Conversion8' */
  rtb_DataTypeConversion8 = (uint8_T)localB->BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (PreLookUpIdSearch_U16): '<S99>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S89>/BKRPMGEARSHIFT'
   */
  PreLookUpIdSearch_U16( &localB->PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, rtu_outBus_d4ae,
                        &BKRPMGEARSHIFT[0], rtb_DataTypeConversion8);

  /* DataTypeConversion: '<S99>/Data Type Conversion1' */
  localB->DataTypeConversion1_hijm = rtb_PreLookUpIdSearch_U16_o2;

  /* DataTypeConversion: '<S100>/Data Type Conversion8' */
  rtb_DataTypeConversion8 = (uint8_T)localB->BKRPMQSCTFOFFSET_dim_a3bk;

  /* S-Function (PreLookUpIdSearch_U16): '<S100>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S89>/BKRPMQSCTFOFFSET'
   */
  PreLookUpIdSearch_U16( &localB->PreLookUpIdSearch_U16_o1_gdfa,
                        &rtb_PreLookUpIdSearch_U16_o2, rtu_outBus_d4ae,
                        &BKRPMQSCTFOFFSET[0], rtb_DataTypeConversion8);

  /* DataTypeConversion: '<S100>/Data Type Conversion1' */
  localB->DataTypeConversion1_c1sk = rtb_PreLookUpIdSearch_U16_o2;

  /* DataTypeConversion: '<S101>/Data Type Conversion8' */
  rtb_DataTypeConversion8 = (uint8_T)localB->BKRPMQSGASPOS_dim_nyed;

  /* S-Function (PreLookUpIdSearch_U16): '<S101>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S89>/BKRPMQSGASPOS'
   */
  PreLookUpIdSearch_U16( &localB->PreLookUpIdSearch_U16_o1_n2je,
                        &rtb_PreLookUpIdSearch_U16_o2, rtu_inBus_nye2,
                        &BKRPMQSGASPOS[0], rtb_DataTypeConversion8);

  /* DataTypeConversion: '<S101>/Data Type Conversion1' */
  localB->DataTypeConversion1_jci4 = rtb_PreLookUpIdSearch_U16_o2;
}

/* Output and update for function-call system: '<S7>/ReadLookUpTables' */
void GearShiftMgm_ReadLookUpTables(void)
{
  /* local block i/o variables */
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int16_T rtb_Abs;
  boolean_T rtb_RelationalOperator;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint8_T rtb_LogicalOperator4;

  /* Switch: '<S139>/Switch2' incorporates:
   *  Inport: '<Root>/CmeDriverI'
   */
  if (GearShiftMgm_B.QuickGearShiftBlpOn_gcmm != 0) {
    rtb_Abs = GearShiftMgm_B.CmeQsI_edjv;
  } else {
    rtb_Abs = CmeDriverI;
  }

  /* End of Switch: '<S139>/Switch2' */

  /* Switch: '<S139>/Switch7' incorporates:
   *  DataStoreRead: '<S139>/Data Store Read5'
   */
  if (GearShiftMgm_B.reset_d5yl != 0) {
    rtb_FOF_Reset_S16_FXP_o2 = rtb_Abs << 14;
  } else {
    rtb_FOF_Reset_S16_FXP_o2 = CmeQsBlpIFiltHr;
  }

  /* End of Switch: '<S139>/Switch7' */

  /* S-Function (FOF_Reset_S16_FXP): '<S141>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_Abs, GearShiftMgm_B.Calc_KFilt_Blp.Switch, rtb_Abs,
                    GearShiftMgm_B.QuickGearShiftBlpOn_gcmm,
                    rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S139>/Data Store Write10' */
  CmeQsBlpIFiltHr = rtb_FOF_Reset_S16_FXP_o2;

  /* Switch: '<S139>/Switch3' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  if (GearShiftMgm_B.QuickGearShiftBlpOn_gcmm != 0) {
    rtb_Abs = GearShiftMgm_B.CmeQsP_h0dh;
  } else {
    rtb_Abs = CmeDriverP;
  }

  /* End of Switch: '<S139>/Switch3' */

  /* Switch: '<S139>/Switch6' incorporates:
   *  DataStoreRead: '<S139>/Data Store Read6'
   */
  if (GearShiftMgm_B.reset_d5yl != 0) {
    rtb_FOF_Reset_S16_FXP_o2 = rtb_Abs << 14;
  } else {
    rtb_FOF_Reset_S16_FXP_o2 = CmeQsBlpPFiltHr;
  }

  /* End of Switch: '<S139>/Switch6' */

  /* S-Function (FOF_Reset_S16_FXP): '<S142>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_Abs, &rtb_FOF_Reset_S16_FXP_o2, rtb_Abs,
                    GearShiftMgm_B.Calc_KFilt_Blp.Switch, rtb_Abs,
                    GearShiftMgm_B.QuickGearShiftBlpOn_gcmm,
                    rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S139>/Data Store Write11' */
  CmeQsBlpPFiltHr = rtb_FOF_Reset_S16_FXP_o2;

  /* DataTypeConversion: '<S144>/Conversion' */
  GearShiftMgm_B.Conversion_egs1 = rtb_Abs;

  /* Logic: '<S136>/Logical Operator4' incorporates:
   *  RelationalOperator: '<S136>/Relational Operator2'
   *  Sum: '<S136>/Sum'
   */
  rtb_LogicalOperator4 = (uint8_T)((GearShiftMgm_B.QuickGearShiftCtfOn_fgyi != 0)
    && (GearShiftMgm_B.QShiftCnt_lib2 < (uint8_T)((uint32_T)
    GearShiftMgm_B.CmeQsIPeriod_gdc0 + GearShiftMgm_B.QsCtfTime_nkkv)));

  /* Switch: '<S140>/Switch' incorporates:
   *  Inport: '<Root>/CmeDriverI'
   */
  if (rtb_LogicalOperator4 != 0) {
    /* Switch: '<S140>/Switch5' incorporates:
     *  Constant: '<S145>/Constant'
     *  Gain: '<S140>/Gain'
     *  Inport: '<Root>/CmfP'
     *  RelationalOperator: '<S145>/Compare'
     */
    if (GearShiftMgm_B.CmeQsIPeriod_gdc0 == 0) {
      rtb_Abs = (int16_T)-CmfP;
    } else {
      rtb_Abs = GearShiftMgm_B.CmeQsI_edjv;
    }

    /* End of Switch: '<S140>/Switch5' */
  } else {
    rtb_Abs = CmeDriverI;
  }

  /* End of Switch: '<S140>/Switch' */

  /* Switch: '<S140>/Switch8' incorporates:
   *  DataStoreRead: '<S140>/Data Store Read4'
   */
  if (GearShiftMgm_B.reset_d5yl != 0) {
    rtb_FOF_Reset_S16_FXP_o2 = rtb_Abs << 14;
  } else {
    rtb_FOF_Reset_S16_FXP_o2 = CmeQsCtfIFiltHr;
  }

  /* End of Switch: '<S140>/Switch8' */

  /* S-Function (FOF_Reset_S16_FXP): '<S146>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_Abs, &rtb_FOF_Reset_S16_FXP_o2, rtb_Abs,
                    GearShiftMgm_B.Calc_KFilt_Ctf.Switch, rtb_Abs,
                    rtb_LogicalOperator4, rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S140>/Data Store Write9' */
  CmeQsCtfIFiltHr = rtb_FOF_Reset_S16_FXP_o2;

  /* Switch: '<S137>/Switch1' incorporates:
   *  DataStoreRead: '<S137>/Data Store Read7'
   *  DataStoreRead: '<S137>/Data Store Read8'
   *  Inport: '<Root>/CmeDriverI'
   *  Logic: '<S137>/Logical Operator1'
   *  Logic: '<S137>/Logical Operator2'
   *  Switch: '<S137>/Switch4'
   */
  if ((GearShiftMgm_B.QuickGearShiftCtfOn_fgyi != 0) || (QuickGearShiftCtf != 0))
  {
    GearShiftMgm_B.Switch1_b2ua = rtb_Abs;
  } else if ((QuickGearShiftBlp != 0) ||
             (GearShiftMgm_B.QuickGearShiftBlpOn_gcmm != 0)) {
    /* Switch: '<S137>/Switch4' */
    GearShiftMgm_B.Switch1_b2ua = rtb_FOF_Reset_S16_FXP_o1;
  } else {
    GearShiftMgm_B.Switch1_b2ua = CmeDriverI;
  }

  /* End of Switch: '<S137>/Switch1' */

  /* Logic: '<S136>/Logical Operator1' incorporates:
   *  Constant: '<S136>/GEAR_SHIFT_WAIT'
   *  Inport: '<Root>/GearShiftWait'
   *  RelationalOperator: '<S136>/Relational Operator1'
   *  RelationalOperator: '<S136>/Relational Operator4'
   */
  GearShiftMgm_B.LogicalOperator1_k5sx = (uint8_T)
    ((GearShiftMgm_B.QuickGearShiftCtfOn_fgyi != 0) &&
     (GearShiftMgm_B.QShiftCnt_lib2 < GearShiftMgm_B.QsCtfTime_nkkv) &&
     (GearShiftWait < ((uint8_T)GEAR_SHIFT_WAIT)));

  /* Sum: '<S138>/Add' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  rtb_Abs = (int16_T)(CmeDriverP - GearShiftMgm_B.Conversion_egs1);

  /* Abs: '<S138>/Abs' */
  if (rtb_Abs < 0) {
    rtb_Abs = (int16_T)-rtb_Abs;
  }

  /* End of Abs: '<S138>/Abs' */

  /* RelationalOperator: '<S138>/Relational Operator1' incorporates:
   *  Constant: '<S138>/CMEQSIDIFF'
   *  Inport: '<Root>/CmeDriverI'
   *  Sum: '<S138>/Add1'
   */
  rtb_RelationalOperator = ((int16_T)(CmeDriverI - GearShiftMgm_B.Switch1_b2ua) >=
    CMEQSIDIFF);

  /* Logic: '<S138>/Logical Operator2' incorporates:
   *  DataStoreRead: '<S138>/Data Store Read7'
   *  Logic: '<S138>/Logical Operator5'
   */
  GearShiftMgm_B.LogicalOperator2_hkya = (uint8_T)
    ((GearShiftMgm_B.QuickGearShiftCtfOn_fgyi != 0) || ((QuickGearShiftCtf != 0)
      && rtb_RelationalOperator));

  /* Logic: '<S138>/Logical Operator4' incorporates:
   *  Constant: '<S138>/CMEQSPDIFF'
   *  DataStoreRead: '<S138>/Data Store Read8'
   *  Logic: '<S138>/Logical Operator6'
   *  Logic: '<S138>/Logical Operator7'
   *  RelationalOperator: '<S138>/Relational Operator'
   */
  GearShiftMgm_B.LogicalOperator4_obph = (uint8_T)((rtb_RelationalOperator &&
    (QuickGearShiftBlp != 0)) || ((QuickGearShiftBlp != 0) && (rtb_Abs >=
    CMEQSPDIFF)) || (GearShiftMgm_B.QuickGearShiftBlpOn_gcmm != 0));
}

/*
 * Output and update for atomic system:
 *    '<S110>/Hys_<_x'
 *    '<S27>/Hys_<_x'
 */
void GearShiftMgm_Hys__x(uint16_T rtu_inSig, uint16_T rtu_cal, uint16_T rtu_hys,
  rtB_Hys__x_GearShiftMgm *localB, rtDW_Hys__x_GearShiftMgm *localDW)
{
  uint32_T tmp;

  /* Chart: '<S110>/Hys_<_x' */
  /* Gateway: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Hys_<_x */
  /* During: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Hys_<_x */
  if (localDW->bitsForTID0.is_active_c3_GearShiftMgm == 0U) {
    /* Entry: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Hys_<_x */
    localDW->bitsForTID0.is_active_c3_GearShiftMgm = 1U;

    /* Entry Internal: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Hys_<_x */
    /* Transition: '<S114>:2' */
    localB->out = 0U;
    localDW->bitsForTID0.is_c3_GearShiftMgm = GearShiftMgm_IN_ZERO;
  } else if (localDW->bitsForTID0.is_c3_GearShiftMgm == GearShiftMgm_IN_ONE) {
    /* During 'ONE': '<S114>:3' */
    /* Transition: '<S114>:8' */
    if (rtu_inSig >= rtu_cal) {
      /* Transition: '<S114>:9' */
      localB->out = 0U;
      localDW->bitsForTID0.is_c3_GearShiftMgm = GearShiftMgm_IN_ZERO;
    } else {
      /* Transition: '<S114>:12' */
    }
  } else {
    /* During 'ZERO': '<S114>:1' */
    /* Transition: '<S114>:5' */
    tmp = (uint32_T)rtu_inSig + rtu_hys;
    if (tmp > 65535U) {
      tmp = 65535U;
    }

    if ((int32_T)tmp < rtu_cal) {
      /* Transition: '<S114>:6' */
      localB->out = 1U;
      localDW->bitsForTID0.is_c3_GearShiftMgm = GearShiftMgm_IN_ONE;
    } else {
      /* Transition: '<S114>:13' */
    }
  }

  /* End of Chart: '<S110>/Hys_<_x' */
}

/*
 * Output and update for atomic system:
 *    '<S112>/Hys_>_x'
 *    '<S28>/Hys_>_x'
 */
void GearShiftMgm_Hys__x_iuw3(uint16_T rtu_inSig, uint16_T rtu_cal, uint16_T
  rtu_hys, rtB_Hys__x_GearShiftMgm_hazx *localB, rtDW_Hys__x_GearShiftMgm_knoo
  *localDW)
{
  uint32_T tmp;

  /* Chart: '<S112>/Hys_>_x' */
  /* Gateway: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Hys_>_x */
  /* During: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Hys_>_x */
  if (localDW->bitsForTID0.is_active_c12_GearShiftMgm == 0U) {
    /* Entry: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Hys_>_x */
    localDW->bitsForTID0.is_active_c12_GearShiftMgm = 1U;

    /* Entry Internal: GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Hys_>_x */
    /* Transition: '<S123>:2' */
    localB->out = 0U;
    localDW->bitsForTID0.is_c12_GearShiftMgm = GearShiftMgm_IN_ZERO_eh2f;
  } else if (localDW->bitsForTID0.is_c12_GearShiftMgm ==
             GearShiftMgm_IN_ONE_ombf) {
    /* During 'ONE': '<S123>:3' */
    /* Transition: '<S123>:8' */
    tmp = (uint32_T)rtu_inSig + rtu_hys;
    if (tmp > 65535U) {
      tmp = 65535U;
    }

    if ((int32_T)tmp <= rtu_cal) {
      /* Transition: '<S123>:9' */
      localB->out = 0U;
      localDW->bitsForTID0.is_c12_GearShiftMgm = GearShiftMgm_IN_ZERO_eh2f;
    } else {
      /* Transition: '<S123>:12' */
    }
  } else {
    /* During 'ZERO': '<S123>:1' */
    /* Transition: '<S123>:5' */
    if (rtu_inSig > rtu_cal) {
      /* Transition: '<S123>:6' */
      localB->out = 1U;
      localDW->bitsForTID0.is_c12_GearShiftMgm = GearShiftMgm_IN_ONE_ombf;
    } else {
      /* Transition: '<S123>:13' */
    }
  }

  /* End of Chart: '<S112>/Hys_>_x' */
}

/* Output and update for atomic system: '<S92>/FlgEnQShift_Calc' */
void GearShiftMgm_FlgEnQShift_Calc(void)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_IR_U16;
  boolean_T rtb_Compare_oogo;
  uint8_T rtb_LogicalOperator2_gigy;
  boolean_T rtb_LogicalOperator_eoop;
  uint8_T rtb_Memory3;
  boolean_T rtb_LogicalOperator1_i353;
  uint8_T rtb_Conversion7;
  uint16_T rtb_Conversion2;

  /* RelationalOperator: '<S109>/Compare' incorporates:
   *  Constant: '<S109>/Constant'
   */
  rtb_Compare_oogo = (GearShiftMgm_B.StQShift_gx4w == ((uint8_T)QSHIFT_DISABLE));

  /* Constant: '<S106>/DIAG_EXHVALVPOS1' */
  GearShiftMgm_B.DIAG_EXHVALVPOS1 = 1U;

  /* Logic: '<S106>/Logical Operator1' incorporates:
   *  Constant: '<S106>/DIAG_EXHVALVPOS10'
   *  Constant: '<S106>/DIAG_EXHVALVPOS11'
   *  Constant: '<S106>/DIAG_EXHVALVPOS12'
   *  Constant: '<S106>/DIAG_EXHVALVPOS13'
   *  Constant: '<S106>/DIAG_EXHVALVPOS14'
   *  Constant: '<S106>/DIAG_EXHVALVPOS15'
   *  Constant: '<S106>/DIAG_EXHVALVPOS16'
   *  Constant: '<S106>/DIAG_EXHVALVPOS17'
   *  Constant: '<S106>/DIAG_EXHVALVPOS18'
   *  Constant: '<S106>/DIAG_EXHVALVPOS5'
   *  Constant: '<S106>/DIAG_EXHVALVPOS6'
   *  Constant: '<S106>/DIAG_EXHVALVPOS7'
   *  Constant: '<S106>/DIAG_EXHVALVPOS8'
   *  Constant: '<S106>/DIAG_EXHVALVPOS9'
   *  Constant: '<S106>/ENQUICKSHIFT'
   *  Inport: '<Root>/DiagFlg02'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S106>/Logical Operator'
   *  RelationalOperator: '<S106>/Relational Operator10'
   *  RelationalOperator: '<S106>/Relational Operator11'
   *  RelationalOperator: '<S106>/Relational Operator4'
   *  RelationalOperator: '<S106>/Relational Operator6'
   *  RelationalOperator: '<S106>/Relational Operator7'
   *  RelationalOperator: '<S106>/Relational Operator8'
   *  RelationalOperator: '<S106>/Relational Operator9'
   *  Selector: '<S106>/Selector10'
   *  Selector: '<S106>/Selector4'
   *  Selector: '<S106>/Selector5'
   *  Selector: '<S106>/Selector6'
   *  Selector: '<S106>/Selector7'
   *  Selector: '<S106>/Selector8'
   *  Selector: '<S106>/Selector9'
   */
  GearShiftMgm_B.LogicalOperator1_poxk = (uint8_T)
    ((GearShiftMgm_B.DIAG_EXHVALVPOS1 != 0) && (ENQUICKSHIFT != 0) && (DiagFlg02
      == 0) && (0 == VtRec[((uint8_T)REC_NO_GAS)]) && (0 == VtRec[((uint8_T)
       REC_ENG_OFF)]) && (0 == VtRec[((uint8_T)REC_HEAVY_TRQ_RED)]) && (0 ==
      VtRec[((uint8_T)REC_SLIGHT_TRQ_RED)]) && (0 == VtRec[((uint8_T)
       REC_FORCE_LH)]) && (0 == VtRec[((uint8_T)REC_LIMIT_VEH_SPEED)]) && (0 ==
      VtRec[((uint8_T)REC_LIMIT_RPM)]));

  /* Product: '<S108>/Product' incorporates:
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/GearPos'
   */
  rtb_Conversion7 = (uint8_T)((uint32_T)ClutchSignal * GearPos);

  /* Chart: '<S112>/Hys_>_x' incorporates:
   *  Constant: '<S112>/QSRPMMAXHYS'
   *  Constant: '<S112>/VTRPMQSHIFTUPMIN'
   *  Inport: '<Root>/Rpm'
   *  Selector: '<S112>/Selector'
   */
  GearShiftMgm_Hys__x_iuw3(Rpm, VTRPMQSHIFTUPMIN[rtb_Conversion7], QSRPMMAXHYS,
    &GearShiftMgm_B.sf_Hys__x_iuw3, &GearShiftMgm_DWork.sf_Hys__x_iuw3);

  /* Logic: '<S111>/Logical Operator2' incorporates:
   *  Constant: '<S118>/Constant'
   *  Constant: '<S119>/Constant'
   *  Constant: '<S120>/Constant'
   *  Constant: '<S121>/Constant'
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/IdleFlg'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/VehSpeed'
   *  Logic: '<S111>/Logical Operator'
   *  RelationalOperator: '<S117>/Compare'
   *  RelationalOperator: '<S118>/Compare'
   *  RelationalOperator: '<S119>/Compare'
   *  RelationalOperator: '<S120>/Compare'
   *  RelationalOperator: '<S121>/Compare'
   */
  rtb_LogicalOperator2_gigy = (uint8_T)((GearShiftMgm_B.LogicalOperator1_poxk !=
    0) && (VehSpeed == 0) && (GearPos != 0) && (ClutchSignal == 0) && ((IdleFlg
    != 0) || (Rpm == 0)));

  /* Logic: '<S108>/Logical Operator' incorporates:
   *  Constant: '<S112>/MINTIMEQSHIFT'
   *  Constant: '<S112>/NEUTRAL'
   *  Constant: '<S112>/QSMAXGEARPOS'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/IdleFlg'
   *  Inport: '<Root>/VehSpeed'
   *  Logic: '<S112>/Logical Operator'
   *  Logic: '<S112>/Logical Operator1'
   *  Logic: '<S112>/Logical Operator2'
   *  Logic: '<S112>/Logical Operator3'
   *  Logic: '<S112>/Logical Operator4'
   *  RelationalOperator: '<S112>/Relational Operator1'
   *  RelationalOperator: '<S112>/Relational Operator12'
   *  RelationalOperator: '<S112>/Relational Operator2'
   *  RelationalOperator: '<S122>/Compare'
   */
  rtb_LogicalOperator_eoop = (((GearShiftMgm_B.LogicalOperator1_poxk != 0) &&
    (GearShiftMgm_B.QShiftCnt_lib2 >= MINTIMEQSHIFT) &&
    (((GearShiftMgm_B.sf_Hys__x_iuw3.out != 0) && (GearPos < ((uint8_T)
    QSMAXGEARPOS))) || ((GearPos == 0) && (VehSpeed == 0) && (IdleFlg != 0)))) ||
    (rtb_LogicalOperator2_gigy != 0));

  /* Logic: '<S105>/Logical Operator' */
  GearShiftMgm_B.LogicalOperator = (uint8_T)(rtb_Compare_oogo &&
    rtb_LogicalOperator_eoop);

  /* DataTypeConversion: '<S115>/Conversion6' */
  rtb_Memory3 = (uint8_T)GearShiftMgm_B.Calc_Ratio.BKRPMQSGASPOS_dim_nyed;

  /* DataTypeConversion: '<S115>/Conversion2' */
  rtb_Conversion2 = rtb_Conversion7;

  /* DataTypeConversion: '<S115>/Conversion7' incorporates:
   *  Constant: '<S110>/Constant3'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U16): '<S115>/Look2D_IR_U16' incorporates:
   *  Constant: '<S110>/Constant2'
   *  Constant: '<S110>/TBRPMQSHIFTDNMAX'
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBRPMQSHIFTDNMAX[0],
                GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1_n2je,
                GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_jci4, rtb_Memory3,
                rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* Chart: '<S110>/Hys_<_x' incorporates:
   *  Constant: '<S110>/QSRPMMAXHYS'
   *  Inport: '<Root>/Rpm'
   */
  GearShiftMgm_Hys__x(Rpm, rtb_Look2D_IR_U16, QSRPMMAXHYS,
                      &GearShiftMgm_B.sf_Hys__x, &GearShiftMgm_DWork.sf_Hys__x);

  /* Logic: '<S108>/Logical Operator1' incorporates:
   *  Constant: '<S110>/NEUTRAL'
   *  Constant: '<S110>/QSMINGEARPOS'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/IdleFlg'
   *  Inport: '<Root>/VehSpeed'
   *  Logic: '<S110>/Logical Operator'
   *  Logic: '<S110>/Logical Operator2'
   *  Logic: '<S110>/Logical Operator3'
   *  Logic: '<S110>/Logical Operator4'
   *  RelationalOperator: '<S110>/Relational Operator15'
   *  RelationalOperator: '<S110>/Relational Operator2'
   *  RelationalOperator: '<S113>/Compare'
   */
  rtb_LogicalOperator1_i353 = (((GearShiftMgm_B.LogicalOperator1_poxk != 0) &&
    (((GearShiftMgm_B.sf_Hys__x.out != 0) && (GearPos >= QSMINGEARPOS)) ||
     ((GearPos == 0) && (VehSpeed == 0) && (IdleFlg != 0)))) ||
    (rtb_LogicalOperator2_gigy != 0));

  /* Logic: '<S105>/Logical Operator1' */
  GearShiftMgm_B.LogicalOperator1_dent = (uint8_T)(rtb_Compare_oogo &&
    rtb_LogicalOperator1_i353);

  /* Logic: '<S105>/Logical Operator4' */
  GearShiftMgm_B.LogicalOperator4_dvla = (uint8_T)(rtb_Compare_oogo &&
    (rtb_LogicalOperator2_gigy != 0));

  /* Memory: '<S107>/Memory2' */
  rtb_Memory3 = GearShiftMgm_DWork.Memory2_PreviousInput;

  /* Logic: '<S107>/Logical Operator2' incorporates:
   *  Inport: '<Root>/GearUpSignal'
   *  Logic: '<S107>/Logical Operator'
   */
  GearShiftMgm_B.LogicalOperator2_fmjj = (uint8_T)((GearUpSignal != 0) &&
    (rtb_Memory3 == 0) && rtb_LogicalOperator_eoop);

  /* Memory: '<S107>/Memory3' */
  rtb_Memory3 = GearShiftMgm_DWork.Memory3_PreviousInput;

  /* Logic: '<S107>/Logical Operator3' incorporates:
   *  Inport: '<Root>/GearDownSignal'
   *  Logic: '<S107>/Logical Operator1'
   */
  GearShiftMgm_B.LogicalOperator3 = (uint8_T)((GearDownSignal != 0) &&
    (rtb_Memory3 == 0) && rtb_LogicalOperator1_i353);

  /* Update for Memory: '<S107>/Memory2' incorporates:
   *  Inport: '<Root>/GearUpSignal'
   */
  GearShiftMgm_DWork.Memory2_PreviousInput = GearUpSignal;

  /* Update for Memory: '<S107>/Memory3' incorporates:
   *  Inport: '<Root>/GearDownSignal'
   */
  GearShiftMgm_DWork.Memory3_PreviousInput = GearDownSignal;
}

/* Output and update for function-call system: '<S7>/EnQs' */
void GearShiftMgm_EnQs(void)
{
  /* Outputs for Atomic SubSystem: '<S92>/FlgEnQShift_Calc' */
  GearShiftMgm_FlgEnQShift_Calc();

  /* End of Outputs for SubSystem: '<S92>/FlgEnQShift_Calc' */
}

/*
 * Output and update for function-call system:
 *    '<S124>/Calc_KFilt_Ctf'
 *    '<S34>/Calc_KFilt_Ctf'
 */
void GearShiftMgm_Calc_KFilt_Ctf(uint32_T rtu_ratioBus, uint16_T
  rtu_ratioBus_exde, uint16_T rtu_ratioBus_imf2, uint8_T rtu_outBus,
  rtB_Calc_KFilt_Ctf_GearShiftMgm *localB)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_octp;
  uint8_T rtb_Conversion3_nacm;

  /* DataTypeConversion: '<S132>/Conversion3' */
  rtb_Conversion3_nacm = (uint8_T)rtu_ratioBus;

  /* S-Function (LookUp_IR_U16): '<S132>/LookUp_IR_U16' incorporates:
   *  Constant: '<S126>/VTQSCTFDNFOFK'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTQSCTFDNFOFK[0], rtu_ratioBus_exde,
                rtu_ratioBus_imf2, rtb_Conversion3_nacm);

  /* DataStoreWrite: '<S126>/Data Store Write12' */
  GearShiftCtfDnFOFK = rtb_LookUp_IR_U16;

  /* DataTypeConversion: '<S131>/Conversion3' */
  rtb_Conversion3_nacm = (uint8_T)rtu_ratioBus;

  /* S-Function (LookUp_IR_U16): '<S131>/LookUp_IR_U16' incorporates:
   *  Constant: '<S126>/VTQSCTFUPFOFK'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16_octp, &VTQSCTFUPFOFK[0], rtu_ratioBus_exde,
                rtu_ratioBus_imf2, rtb_Conversion3_nacm);

  /* DataStoreWrite: '<S126>/Data Store Write4' */
  GearShiftCtfUpFOFK = rtb_LookUp_IR_U16_octp;

  /* Switch: '<S126>/Switch' */
  if (rtu_outBus != 0) {
    localB->Switch = rtb_LookUp_IR_U16_octp;
  } else {
    localB->Switch = rtb_LookUp_IR_U16;
  }

  /* End of Switch: '<S126>/Switch' */
}

/*
 * Output and update for function-call system:
 *    '<S124>/Calc_KFilt_Blp'
 *    '<S34>/Calc_KFilt_Blp'
 */
void GearShiftMgm_Calc_KFilt_Blp(uint32_T rtu_ratioBus, uint16_T
  rtu_ratioBus_pjxr, uint16_T rtu_ratioBus_lolj, uint8_T rtu_outBus,
  rtB_Calc_KFilt_Blp_GearShiftMgm *localB)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_IR_U16_j1pd;
  uint16_T rtb_LookUp_IR_U16_ne2l;
  uint8_T rtb_Conversion3_hynd;

  /* DataTypeConversion: '<S127>/Conversion3' */
  rtb_Conversion3_hynd = (uint8_T)rtu_ratioBus;

  /* S-Function (LookUp_IR_U16): '<S127>/LookUp_IR_U16' incorporates:
   *  Constant: '<S125>/VTQSBLPUPFOFK'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16_j1pd, &VTQSBLPUPFOFK[0], rtu_ratioBus_pjxr,
                rtu_ratioBus_lolj, rtb_Conversion3_hynd);

  /* DataStoreWrite: '<S125>/Data Store Write1' */
  GearShiftBlpUpFOFK = rtb_LookUp_IR_U16_j1pd;

  /* DataTypeConversion: '<S128>/Conversion3' */
  rtb_Conversion3_hynd = (uint8_T)rtu_ratioBus;

  /* S-Function (LookUp_IR_U16): '<S128>/LookUp_IR_U16' incorporates:
   *  Constant: '<S125>/VTQSBLPDNFOFK'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16_ne2l, &VTQSBLPDNFOFK[0], rtu_ratioBus_pjxr,
                rtu_ratioBus_lolj, rtb_Conversion3_hynd);

  /* DataStoreWrite: '<S125>/Data Store Write2' */
  GearShiftBlpDnFOFK = rtb_LookUp_IR_U16_ne2l;

  /* Switch: '<S125>/Switch' */
  if (rtu_outBus != 0) {
    localB->Switch = rtb_LookUp_IR_U16_j1pd;
  } else {
    localB->Switch = rtb_LookUp_IR_U16_ne2l;
  }

  /* End of Switch: '<S125>/Switch' */
}

/* Output and update for function-call system: '<S15>/fc_QsDnBlp_Calc' */
void GearShiftM_fc_QsDnBlp_Calc_llms(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_ksc2;
  int16_T rtb_LookUp_IR_S16_brth;
  uint8_T rtb_Conversion3;
  int16_T rtb_Switch[8];
  int32_T i;

  /* Switch: '<S63>/Switch1' incorporates:
   *  Constant: '<S63>/VTCMEQSBLPDNI'
   *  Constant: '<S63>/VTCMEQSBLPDNICTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPDNICTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPDNI[i];
    }
  }

  /* End of Switch: '<S63>/Switch1' */

  /* DataTypeConversion: '<S65>/Conversion3' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S65>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_ksc2, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S63>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_crco = rtb_LookUp_IR_S16_ksc2;

  /* Switch: '<S63>/Switch' incorporates:
   *  Constant: '<S63>/VTCMEQSBLPDNP'
   *  Constant: '<S63>/VTCMEQSBLPDNPCTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPDNPCTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPDNP[i];
    }
  }

  /* End of Switch: '<S63>/Switch' */

  /* DataTypeConversion: '<S66>/Conversion3' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S66>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_brth, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S63>/Data Type Conversion1' */
  GearShiftMgm_B.CmeQsP_btqz = rtb_LookUp_IR_S16_brth;

  /* Product: '<S63>/Product' incorporates:
   *  Constant: '<S63>/VTGNCMEQSBLPDN'
   *  Selector: '<S63>/Selector'
   */
  GearShiftMgm_B.CmeQsRedI = (int16_T)((rtb_LookUp_IR_S16_ksc2 *
    VTGNCMEQSBLPDN[GearShiftMgm_B.GearPosQShift_p4ss]) >> 8);

  /* Product: '<S63>/Product1' incorporates:
   *  Constant: '<S63>/VTGNCMEQSBLPDN'
   *  Selector: '<S63>/Selector'
   */
  GearShiftMgm_B.CmeQsRedP = (int16_T)((rtb_LookUp_IR_S16_brth *
    VTGNCMEQSBLPDN[GearShiftMgm_B.GearPosQShift_p4ss]) >> 8);

  /* Selector: '<S64>/Selector1' incorporates:
   *  Constant: '<S64>/VTQSBLPDNTOTIME'
   */
  GearShiftMgm_B.QsBlpToTime = VTQSBLPDNTOTIME[GearShiftMgm_B.GearPosQShift_p4ss];

  /* Selector: '<S64>/Selector2' incorporates:
   *  Constant: '<S64>/VTQSBLPDNTIME'
   *  Constant: '<S64>/VTQSBLPDNTIMECTF'
   *  Inport: '<Root>/CutoffFlg'
   *  Switch: '<S64>/Switch'
   */
  if (CutoffFlg != 0) {
    GearShiftMgm_B.QsBlpTime =
      VTQSBLPDNTIMECTF[GearShiftMgm_B.GearPosQShift_p4ss];
  } else {
    GearShiftMgm_B.QsBlpTime = VTQSBLPDNTIME[GearShiftMgm_B.GearPosQShift_p4ss];
  }

  /* End of Selector: '<S64>/Selector2' */
}

/* Output and update for function-call system: '<S15>/fc_QsUpBlp_Calc' */
void GearShiftM_fc_QsUpBlp_Calc_hvvt(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_d2at;
  int16_T rtb_LookUp_IR_S16_kqkv;
  uint8_T rtb_Conversion3;
  int16_T rtb_Switch[8];
  int32_T i;

  /* Switch: '<S76>/Switch1' incorporates:
   *  Constant: '<S76>/VTCMEQSBLPUPI'
   *  Constant: '<S76>/VTCMEQSBLPUPICTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPUPICTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPUPI[i];
    }
  }

  /* End of Switch: '<S76>/Switch1' */

  /* DataTypeConversion: '<S78>/Conversion3' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S78>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_d2at, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S76>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_crco = rtb_LookUp_IR_S16_d2at;

  /* Switch: '<S76>/Switch' incorporates:
   *  Constant: '<S76>/VTCMEQSBLPUPP'
   *  Constant: '<S76>/VTCMEQSBLPUPPCTF'
   *  Inport: '<Root>/CutoffFlg'
   */
  for (i = 0; i < 8; i++) {
    if (CutoffFlg != 0) {
      rtb_Switch[i] = VTCMEQSBLPUPPCTF[i];
    } else {
      rtb_Switch[i] = VTCMEQSBLPUPP[i];
    }
  }

  /* End of Switch: '<S76>/Switch' */

  /* DataTypeConversion: '<S79>/Conversion3' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh;

  /* S-Function (LookUp_IR_S16): '<S79>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_kqkv, &rtb_Switch[0],
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
                rtb_Conversion3);

  /* DataTypeConversion: '<S76>/Data Type Conversion1' */
  GearShiftMgm_B.CmeQsP_btqz = rtb_LookUp_IR_S16_kqkv;

  /* Product: '<S76>/Product1' incorporates:
   *  Constant: '<S76>/VTGNCMEQSBLPUP'
   *  Selector: '<S76>/Selector'
   */
  GearShiftMgm_B.CmeQsRedP = (int16_T)((rtb_LookUp_IR_S16_kqkv *
    VTGNCMEQSBLPUP[GearShiftMgm_B.GearPosQShift_p4ss]) >> 8);

  /* Product: '<S76>/Product2' incorporates:
   *  Constant: '<S76>/VTGNCMEQSBLPUP'
   *  Selector: '<S76>/Selector'
   */
  GearShiftMgm_B.CmeQsRedI = (int16_T)((rtb_LookUp_IR_S16_d2at *
    VTGNCMEQSBLPUP[GearShiftMgm_B.GearPosQShift_p4ss]) >> 8);

  /* Selector: '<S77>/Selector1' incorporates:
   *  Constant: '<S77>/VTQSBLPUPTIME'
   *  Constant: '<S77>/VTQSBLPUPTIMECTF'
   *  Inport: '<Root>/CutoffFlg'
   *  Switch: '<S77>/Switch'
   */
  if (CutoffFlg != 0) {
    GearShiftMgm_B.QsBlpTime =
      VTQSBLPUPTIMECTF[GearShiftMgm_B.GearPosQShift_p4ss];
  } else {
    GearShiftMgm_B.QsBlpTime = VTQSBLPUPTIME[GearShiftMgm_B.GearPosQShift_p4ss];
  }

  /* End of Selector: '<S77>/Selector1' */

  /* Selector: '<S77>/Selector2' incorporates:
   *  Constant: '<S77>/VTQSBLPUPTOTIME'
   */
  GearShiftMgm_B.QsBlpToTime = VTQSBLPUPTOTIME[GearShiftMgm_B.GearPosQShift_p4ss];
}

/* Output and update for function-call system: '<S15>/fc_QsDnCtf_Calc' */
void GearShiftM_fc_QsDnCtf_Calc_ds5q(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_n0bs;
  uint8_T rtb_Conversion7;
  uint16_T rtb_Conversion2;
  uint8_T rtb_Conversion3;
  int32_T tmp;

  /* DataTypeConversion: '<S75>/Conversion6' */
  rtb_Conversion7 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S75>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_p4ss;

  /* DataTypeConversion: '<S75>/Conversion7' incorporates:
   *  Constant: '<S70>/Constant3'
   */
  rtb_Conversion3 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S75>/Look2D_IR_U8' incorporates:
   *  Constant: '<S70>/Constant2'
   *  Constant: '<S70>/TBQSCTFDNPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBQSCTFDNPERIOD[0],
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1,
               rtb_Conversion7, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion3);

  /* DataTypeConversion: '<S70>/Gateway In7' incorporates:
   *  DataStoreWrite: '<S70>/Data Store Write3'
   */
  QSCtfPeriod = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S74>/Conversion6' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S74>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_p4ss;

  /* DataTypeConversion: '<S74>/Conversion7' incorporates:
   *  Constant: '<S70>/Constant1'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S74>/Look2D_IR_U8' incorporates:
   *  Constant: '<S70>/Constant'
   *  Constant: '<S70>/TBCMEQSDNIPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBCMEQSDNIPERIOD[0],
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1,
               rtb_Conversion3, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* DataTypeConversion: '<S70>/Gateway In1' */
  GearShiftMgm_B.CmeQsIPeriod_gnn3 = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S73>/Conversion6' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSCTFOFFSET_dim_a3bk;

  /* DataTypeConversion: '<S73>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_p4ss;

  /* DataTypeConversion: '<S73>/Conversion7' incorporates:
   *  Constant: '<S70>/Constant5'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_S8): '<S73>/Look2D_IR_S8' incorporates:
   *  Constant: '<S70>/Constant4'
   *  Constant: '<S70>/TBQSCTFDNOFFSET'
   */
  Look2D_IR_S8( &rtb_LookUp_IR_S16_n0bs, &TBQSCTFDNOFFSET[0],
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_gdfa,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_c1sk,
               rtb_Conversion3, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* Selector: '<S70>/Selector' incorporates:
   *  Constant: '<S70>/VTQSDNDBLCTFTIME'
   */
  GearShiftMgm_B.QSCntDlbTime_a5p1 =
    VTQSDNDBLCTFTIME[GearShiftMgm_B.GearPosQShift_p4ss];

  /* Selector: '<S70>/Selector1' incorporates:
   *  Constant: '<S70>/VTQSDNDBLCTFTOTIME'
   */
  GearShiftMgm_B.QSCntDlbToTime_dn1n =
    VTQSDNDBLCTFTOTIME[GearShiftMgm_B.GearPosQShift_p4ss];

  /* Sum: '<S70>/Sum1' incorporates:
   *  DataStoreWrite: '<S70>/Data Store Write3'
   *  DataTypeConversion: '<S70>/Gateway In2'
   */
  tmp = QSCtfPeriod + (rtb_LookUp_IR_S16_n0bs >> 8);
  if (tmp < 0) {
    tmp = 0;
  } else {
    if (tmp > 255) {
      tmp = 255;
    }
  }

  GearShiftMgm_B.QsCtfTime = (uint8_T)tmp;

  /* End of Sum: '<S70>/Sum1' */

  /* DataTypeConversion: '<S71>/Conversion3' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5;

  /* S-Function (LookUp_IR_S16): '<S71>/LookUp_IR_S16' incorporates:
   *  Constant: '<S69>/VTCMEQSCTFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_n0bs, &VTCMEQSCTFI[0],
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1,
                rtb_Conversion3);

  /* DataTypeConversion: '<S69>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_crco = rtb_LookUp_IR_S16_n0bs;

  /* DataTypeConversion: '<S60>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  GearShiftMgm_B.CmeQsP_btqz = CmeDriverP;
}

/* Output and update for function-call system: '<S15>/fc_QsUpCtf_Calc' */
void GearShiftM_fc_QsUpCtf_Calc_lq0y(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16_jsa1;
  uint8_T rtb_Conversion7;
  uint16_T rtb_Conversion2;
  uint8_T rtb_Conversion3;
  int32_T tmp;

  /* DataTypeConversion: '<S88>/Conversion6' */
  rtb_Conversion7 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S88>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_p4ss;

  /* DataTypeConversion: '<S88>/Conversion7' incorporates:
   *  Constant: '<S83>/Constant3'
   */
  rtb_Conversion3 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S88>/Look2D_IR_U8' incorporates:
   *  Constant: '<S83>/Constant2'
   *  Constant: '<S83>/TBQSCTFUPPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBQSCTFUPPERIOD[0],
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1,
               rtb_Conversion7, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion3);

  /* DataTypeConversion: '<S83>/Gateway In7' incorporates:
   *  DataStoreWrite: '<S83>/Data Store Write3'
   */
  QSCtfPeriod = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S87>/Conversion6' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5;

  /* DataTypeConversion: '<S87>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_p4ss;

  /* DataTypeConversion: '<S87>/Conversion7' incorporates:
   *  Constant: '<S83>/Constant1'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U8): '<S87>/Look2D_IR_U8' incorporates:
   *  Constant: '<S83>/Constant'
   *  Constant: '<S83>/TBCMEQSUPIPERIOD'
   */
  Look2D_IR_U8( &rtb_Conversion2, &TBCMEQSUPIPERIOD[0],
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1,
               rtb_Conversion3, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* DataTypeConversion: '<S83>/Gateway In1' */
  GearShiftMgm_B.CmeQsIPeriod_gnn3 = (uint8_T)((uint32_T)rtb_Conversion2 >> 8);

  /* DataTypeConversion: '<S86>/Conversion6' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSCTFOFFSET_dim_a3bk;

  /* DataTypeConversion: '<S86>/Conversion2' */
  rtb_Conversion2 = GearShiftMgm_B.GearPosQShift_p4ss;

  /* DataTypeConversion: '<S86>/Conversion7' incorporates:
   *  Constant: '<S83>/Constant5'
   */
  rtb_Conversion7 = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_S8): '<S86>/Look2D_IR_S8' incorporates:
   *  Constant: '<S83>/Constant4'
   *  Constant: '<S83>/TBQSCTFUPOFFSET'
   */
  Look2D_IR_S8( &rtb_LookUp_IR_S16_jsa1, &TBQSCTFUPOFFSET[0],
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_gdfa,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_c1sk,
               rtb_Conversion3, rtb_Conversion2, ((uint16_T)0U), rtb_Conversion7);

  /* Selector: '<S83>/Selector' incorporates:
   *  Constant: '<S83>/VTQSUPDBLCTFTIME'
   */
  GearShiftMgm_B.QSCntDlbTime_a5p1 =
    VTQSUPDBLCTFTIME[GearShiftMgm_B.GearPosQShift_p4ss];

  /* Selector: '<S83>/Selector1' incorporates:
   *  Constant: '<S83>/VTQSUPDBLCTFTOTIME'
   */
  GearShiftMgm_B.QSCntDlbToTime_dn1n =
    VTQSUPDBLCTFTOTIME[GearShiftMgm_B.GearPosQShift_p4ss];

  /* Sum: '<S83>/Sum1' incorporates:
   *  DataStoreWrite: '<S83>/Data Store Write3'
   *  DataTypeConversion: '<S83>/Gateway In2'
   */
  tmp = QSCtfPeriod + (rtb_LookUp_IR_S16_jsa1 >> 8);
  if (tmp < 0) {
    tmp = 0;
  } else {
    if (tmp > 255) {
      tmp = 255;
    }
  }

  GearShiftMgm_B.QsCtfTime = (uint8_T)tmp;

  /* End of Sum: '<S83>/Sum1' */

  /* DataTypeConversion: '<S84>/Conversion3' */
  rtb_Conversion3 = (uint8_T)
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5;

  /* S-Function (LookUp_IR_S16): '<S84>/LookUp_IR_S16' incorporates:
   *  Constant: '<S82>/VTCMEQSCTFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_jsa1, &VTCMEQSCTFI[0],
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1,
                rtb_Conversion3);

  /* DataTypeConversion: '<S82>/Data Type Conversion' */
  GearShiftMgm_B.CmeQsI_crco = rtb_LookUp_IR_S16_jsa1;

  /* DataTypeConversion: '<S62>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  GearShiftMgm_B.CmeQsP_btqz = CmeDriverP;
}

/* Output and update for function-call system: '<S6>/ReadLookUpTables' */
void GearShift_ReadLookUpTables_b1et(void)
{
  /* local block i/o variables */
  int16_T rtb_FOF_Reset_S16_FXP_o1_j5yw;
  boolean_T rtb_RelationalOperator;
  int16_T rtb_Abs;
  uint8_T rtb_Conversion3;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_Switch1;

  /* Switch: '<S49>/Switch2' incorporates:
   *  Inport: '<Root>/CmeDriverI'
   */
  if (GearShiftMgm_B.QuickGearShiftBlpOn_a3xq != 0) {
    /* Switch: '<S49>/Switch1' */
    if (GearShiftMgm_B.cmeGain != 0) {
      rtb_Abs = GearShiftMgm_B.CmeQsRedI;
    } else {
      rtb_Abs = GearShiftMgm_B.CmeQsI_crco;
    }

    /* End of Switch: '<S49>/Switch1' */
  } else {
    rtb_Abs = CmeDriverI;
  }

  /* End of Switch: '<S49>/Switch2' */

  /* Logic: '<S49>/Logical Operator' */
  rtb_RelationalOperator = ((GearShiftMgm_B.QuickGearShiftBlpOn_a3xq != 0) ||
    (GearShiftMgm_B.cluReset != 0));

  /* DataTypeConversion: '<S51>/Conversion3' */
  rtb_Conversion3 = rtb_RelationalOperator;

  /* Switch: '<S49>/Switch7' incorporates:
   *  DataStoreRead: '<S49>/Data Store Read5'
   */
  if (GearShiftMgm_B.reset != 0) {
    rtb_FOF_Reset_S16_FXP_o2 = rtb_Abs << 14;
  } else {
    rtb_FOF_Reset_S16_FXP_o2 = CmeQsBlpIFiltHr;
  }

  /* End of Switch: '<S49>/Switch7' */

  /* S-Function (FOF_Reset_S16_FXP): '<S51>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1_j5yw, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_Abs, GearShiftMgm_B.Calc_KFilt_Blp_jcgws.Switch, rtb_Abs,
                    rtb_Conversion3, rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S49>/Data Store Write10' */
  CmeQsBlpIFiltHr = rtb_FOF_Reset_S16_FXP_o2;

  /* Switch: '<S49>/Switch3' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  if (GearShiftMgm_B.QuickGearShiftBlpOn_a3xq != 0) {
    /* Switch: '<S49>/Switch4' */
    if (GearShiftMgm_B.cmeGain != 0) {
      rtb_Abs = GearShiftMgm_B.CmeQsRedP;
    } else {
      rtb_Abs = GearShiftMgm_B.CmeQsP_btqz;
    }

    /* End of Switch: '<S49>/Switch4' */
  } else {
    rtb_Abs = CmeDriverP;
  }

  /* End of Switch: '<S49>/Switch3' */

  /* DataTypeConversion: '<S52>/Conversion3' */
  rtb_Conversion3 = rtb_RelationalOperator;

  /* Switch: '<S49>/Switch6' incorporates:
   *  DataStoreRead: '<S49>/Data Store Read6'
   */
  if (GearShiftMgm_B.reset != 0) {
    rtb_FOF_Reset_S16_FXP_o2 = rtb_Abs << 14;
  } else {
    rtb_FOF_Reset_S16_FXP_o2 = CmeQsBlpPFiltHr;
  }

  /* End of Switch: '<S49>/Switch6' */

  /* S-Function (FOF_Reset_S16_FXP): '<S52>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_Abs, &rtb_FOF_Reset_S16_FXP_o2, rtb_Abs,
                    GearShiftMgm_B.Calc_KFilt_Blp_jcgws.Switch, rtb_Abs,
                    rtb_Conversion3, rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S49>/Data Store Write11' */
  CmeQsBlpPFiltHr = rtb_FOF_Reset_S16_FXP_o2;

  /* DataTypeConversion: '<S54>/Conversion' */
  GearShiftMgm_B.Conversion = rtb_Abs;

  /* Logic: '<S46>/Logical Operator4' incorporates:
   *  RelationalOperator: '<S46>/Relational Operator2'
   *  Sum: '<S46>/Sum'
   */
  rtb_Conversion3 = (uint8_T)((GearShiftMgm_B.QuickGearShiftCtfOn_cwxy != 0) &&
    (GearShiftMgm_B.QShiftCnt_oz3p < (uint8_T)((uint32_T)
    GearShiftMgm_B.CmeQsIPeriod_gnn3 + GearShiftMgm_B.QsCtfTime)));

  /* Switch: '<S50>/Switch' incorporates:
   *  Inport: '<Root>/CmeDriverI'
   *  Logic: '<S50>/Logical Operator1'
   *  Logic: '<S50>/Logical Operator2'
   */
  if ((GearShiftMgm_B.cluReset == 0) && (rtb_Conversion3 != 0)) {
    /* Switch: '<S50>/Switch5' incorporates:
     *  Constant: '<S55>/Constant'
     *  Gain: '<S50>/Gain'
     *  Inport: '<Root>/CmfP'
     *  RelationalOperator: '<S55>/Compare'
     */
    if (GearShiftMgm_B.CmeQsIPeriod_gnn3 == 0) {
      rtb_Abs = (int16_T)-CmfP;
    } else {
      rtb_Abs = GearShiftMgm_B.CmeQsI_crco;
    }

    /* End of Switch: '<S50>/Switch5' */
  } else {
    rtb_Abs = CmeDriverI;
  }

  /* End of Switch: '<S50>/Switch' */

  /* Switch: '<S50>/Switch1' incorporates:
   *  Constant: '<S50>/QSGNFILTTOTIME'
   *  Product: '<S50>/Product'
   */
  if (GearShiftMgm_B.filtGain != 0) {
    rtb_Switch1 = (uint16_T)(((uint32_T)QSGNFILTTOTIME *
      GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp.Switch) >> 7);
  } else {
    rtb_Switch1 = GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp.Switch;
  }

  /* End of Switch: '<S50>/Switch1' */

  /* DataTypeConversion: '<S56>/Conversion3' incorporates:
   *  Logic: '<S50>/Logical Operator'
   */
  rtb_Conversion3 = (uint8_T)((rtb_Conversion3 != 0) || (GearShiftMgm_B.cluReset
    != 0));

  /* Switch: '<S50>/Switch8' incorporates:
   *  DataStoreRead: '<S50>/Data Store Read4'
   */
  if (GearShiftMgm_B.reset != 0) {
    rtb_FOF_Reset_S16_FXP_o2 = rtb_Abs << 14;
  } else {
    rtb_FOF_Reset_S16_FXP_o2 = CmeQsCtfIFiltHr;
  }

  /* End of Switch: '<S50>/Switch8' */

  /* S-Function (FOF_Reset_S16_FXP): '<S56>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_Abs, &rtb_FOF_Reset_S16_FXP_o2, rtb_Abs, rtb_Switch1,
                    rtb_Abs, rtb_Conversion3, rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S50>/Data Store Write9' */
  CmeQsCtfIFiltHr = rtb_FOF_Reset_S16_FXP_o2;

  /* Switch: '<S47>/Switch1' incorporates:
   *  DataStoreRead: '<S47>/Data Store Read7'
   *  DataStoreRead: '<S47>/Data Store Read8'
   *  Inport: '<Root>/CmeDriverI'
   *  Logic: '<S47>/Logical Operator1'
   *  Logic: '<S47>/Logical Operator2'
   *  Switch: '<S47>/Switch4'
   */
  if ((GearShiftMgm_B.QuickGearShiftCtfOn_cwxy != 0) || (QuickGearShiftCtf != 0))
  {
    GearShiftMgm_B.Switch1 = rtb_Abs;
  } else if ((QuickGearShiftBlp != 0) ||
             (GearShiftMgm_B.QuickGearShiftBlpOn_a3xq != 0)) {
    /* Switch: '<S47>/Switch4' */
    GearShiftMgm_B.Switch1 = rtb_FOF_Reset_S16_FXP_o1_j5yw;
  } else {
    GearShiftMgm_B.Switch1 = CmeDriverI;
  }

  /* End of Switch: '<S47>/Switch1' */

  /* Logic: '<S46>/Logical Operator1' incorporates:
   *  Logic: '<S46>/Logical Operator2'
   *  RelationalOperator: '<S46>/Relational Operator1'
   */
  GearShiftMgm_B.LogicalOperator1 = (uint8_T)
    ((GearShiftMgm_B.QuickGearShiftCtfOn_cwxy != 0) && ((GearShiftMgm_B.flgDbl
       != 0) || (GearShiftMgm_B.QShiftCnt_oz3p < GearShiftMgm_B.QsCtfTime)));

  /* Sum: '<S48>/Add' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  rtb_Abs = (int16_T)(CmeDriverP - GearShiftMgm_B.Conversion);

  /* Abs: '<S48>/Abs' */
  if (rtb_Abs < 0) {
    rtb_Abs = (int16_T)-rtb_Abs;
  }

  /* End of Abs: '<S48>/Abs' */

  /* RelationalOperator: '<S48>/Relational Operator1' incorporates:
   *  Constant: '<S48>/CMEQSIDIFF'
   *  Inport: '<Root>/CmeDriverI'
   *  Sum: '<S48>/Add1'
   */
  rtb_RelationalOperator = ((int16_T)(CmeDriverI - GearShiftMgm_B.Switch1) >=
    CMEQSIDIFF);

  /* Logic: '<S48>/Logical Operator2' incorporates:
   *  DataStoreRead: '<S48>/Data Store Read7'
   *  Logic: '<S48>/Logical Operator5'
   */
  GearShiftMgm_B.LogicalOperator2 = (uint8_T)
    ((GearShiftMgm_B.QuickGearShiftCtfOn_cwxy != 0) || ((QuickGearShiftCtf != 0)
      && rtb_RelationalOperator));

  /* Logic: '<S48>/Logical Operator4' incorporates:
   *  Constant: '<S48>/CMEQSPDIFF'
   *  DataStoreRead: '<S48>/Data Store Read8'
   *  Logic: '<S48>/Logical Operator6'
   *  Logic: '<S48>/Logical Operator7'
   *  RelationalOperator: '<S48>/Relational Operator'
   */
  GearShiftMgm_B.LogicalOperator4 = (uint8_T)((rtb_RelationalOperator &&
    (QuickGearShiftBlp != 0)) || ((QuickGearShiftBlp != 0) && (rtb_Abs >=
    CMEQSPDIFF)) || (GearShiftMgm_B.QuickGearShiftBlpOn_a3xq != 0));
}

/* Output and update for function-call system: '<S6>/EnQs' */
void GearShiftMgm_EnQs_lgkn(uint8_T rtu_outBus, uint8_T rtu_inBus, uint8_T
  rtu_inBus_pt3c, const uint8_T rtu_inBus_bm5j[22], uint8_T rtu_inBus_bvaj,
  uint8_T rtu_inBus_o5cv, uint8_T rtu_inBus_kg1o, uint8_T rtu_inBus_cf2k,
  uint8_T rtu_inBus_nqsl, uint8_T rtu_inBus_je2r, uint8_T rtu_inBus_ie1h,
  uint8_T rtu_inBus_ezdu, uint16_T rtu_inBus_ahd1, uint32_T rtu_ratioBus,
  uint16_T rtu_ratioBus_ozbd, uint16_T rtu_ratioBus_knrk, rtB_EnQs_GearShiftMgm *
  localB, rtDW_EnQs_GearShiftMgm *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_IR_U16_kz4c;
  uint8_T rtb_LogicalOperator_adei;
  uint8_T rtb_LogicalOperator3_kawc;
  uint8_T rtb_LogicalOperator7_bbmk;
  uint8_T rtb_Conversion7_lm4o;
  uint16_T rtb_Conversion2_codi;

  /* DataTypeConversion: '<S24>/Data Type Conversion' */
  localB->DataTypeConversion = rtu_inBus;

  /* Logic: '<S24>/Logical Operator1' incorporates:
   *  Constant: '<S24>/DIAG_EXHVALVPOS10'
   *  Constant: '<S24>/DIAG_EXHVALVPOS11'
   *  Constant: '<S24>/DIAG_EXHVALVPOS12'
   *  Constant: '<S24>/DIAG_EXHVALVPOS13'
   *  Constant: '<S24>/DIAG_EXHVALVPOS14'
   *  Constant: '<S24>/DIAG_EXHVALVPOS15'
   *  Constant: '<S24>/DIAG_EXHVALVPOS16'
   *  Constant: '<S24>/DIAG_EXHVALVPOS17'
   *  Constant: '<S24>/DIAG_EXHVALVPOS18'
   *  Constant: '<S24>/DIAG_EXHVALVPOS5'
   *  Constant: '<S24>/DIAG_EXHVALVPOS6'
   *  Constant: '<S24>/DIAG_EXHVALVPOS7'
   *  Constant: '<S24>/DIAG_EXHVALVPOS8'
   *  Constant: '<S24>/DIAG_EXHVALVPOS9'
   *  Constant: '<S24>/ENQUICKSHIFT'
   *  Logic: '<S24>/Logical Operator'
   *  RelationalOperator: '<S24>/Relational Operator10'
   *  RelationalOperator: '<S24>/Relational Operator11'
   *  RelationalOperator: '<S24>/Relational Operator4'
   *  RelationalOperator: '<S24>/Relational Operator6'
   *  RelationalOperator: '<S24>/Relational Operator7'
   *  RelationalOperator: '<S24>/Relational Operator8'
   *  RelationalOperator: '<S24>/Relational Operator9'
   *  Selector: '<S24>/Selector10'
   *  Selector: '<S24>/Selector4'
   *  Selector: '<S24>/Selector5'
   *  Selector: '<S24>/Selector6'
   *  Selector: '<S24>/Selector7'
   *  Selector: '<S24>/Selector8'
   *  Selector: '<S24>/Selector9'
   */
  localB->LogicalOperator1 = (uint8_T)((localB->DataTypeConversion != 0) &&
    (rtu_inBus_bvaj != 0) && (ENQUICKSHIFT != 0) && (rtu_inBus_pt3c == 0) && (0 ==
    rtu_inBus_bm5j[((uint8_T)REC_NO_GAS)]) && (0 == rtu_inBus_bm5j[((uint8_T)
    REC_ENG_OFF)]) && (0 == rtu_inBus_bm5j[((uint8_T)REC_HEAVY_TRQ_RED)]) && (0 ==
    rtu_inBus_bm5j[((uint8_T)REC_SLIGHT_TRQ_RED)]) && (0 == rtu_inBus_bm5j
    [((uint8_T)REC_FORCE_LH)]) && (0 == rtu_inBus_bm5j[((uint8_T)
    REC_LIMIT_VEH_SPEED)]) && (0 == rtu_inBus_bm5j[((uint8_T)REC_LIMIT_RPM)]));

  /* Memory: '<S25>/Memory1' */
  rtb_Conversion7_lm4o = localDW->Memory1_PreviousInput;

  /* Logic: '<S25>/Logical Operator' */
  rtb_LogicalOperator_adei = (uint8_T)(rtb_Conversion7_lm4o == 0);

  /* Memory: '<S25>/Memory2' */
  rtb_Conversion7_lm4o = localDW->Memory2_PreviousInput;

  /* Logic: '<S25>/Logical Operator3' incorporates:
   *  Logic: '<S25>/Logical Operator1'
   */
  rtb_LogicalOperator3_kawc = (uint8_T)((rtu_inBus_kg1o != 0) &&
    (rtb_Conversion7_lm4o == 0));

  /* Product: '<S26>/Product' */
  rtb_Conversion7_lm4o = (uint8_T)((uint32_T)rtu_inBus_nqsl * rtu_inBus_cf2k);

  /* Chart: '<S28>/Hys_>_x' incorporates:
   *  Constant: '<S28>/QSRPMMAXHYS'
   *  Constant: '<S28>/VTRPMQSHIFTUPMIN'
   *  Selector: '<S28>/Selector'
   */
  GearShiftMgm_Hys__x_iuw3(rtu_inBus_ahd1, VTRPMQSHIFTUPMIN[rtb_Conversion7_lm4o],
    QSRPMMAXHYS, &localB->sf_Hys__x_apvf, &localDW->sf_Hys__x_apvf);

  /* Logic: '<S28>/Logical Operator3' incorporates:
   *  Constant: '<S28>/MINTIMEQSHIFT'
   *  Constant: '<S28>/QSMAXGEARPOS'
   *  RelationalOperator: '<S28>/Relational Operator12'
   *  RelationalOperator: '<S28>/Relational Operator2'
   */
  localB->LogicalOperator3 = (uint8_T)((localB->LogicalOperator1 != 0) &&
    (rtu_inBus_cf2k < ((uint8_T)QSMAXGEARPOS)) && (localB->sf_Hys__x_apvf.out !=
    0) && (rtu_outBus >= MINTIMEQSHIFT));

  /* Logic: '<S25>/Logical Operator2' incorporates:
   *  Logic: '<S25>/Logical Operator5'
   *  Logic: '<S25>/Logical Operator6'
   */
  localB->LogicalOperator2 = (uint8_T)(((rtb_LogicalOperator3_kawc != 0) ||
    ((rtu_inBus_o5cv != 0) && (rtb_LogicalOperator_adei != 0))) &&
    (localB->LogicalOperator3 != 0));

  /* Memory: '<S25>/Memory3' */
  rtb_LogicalOperator_adei = localDW->Memory3_PreviousInput;

  /* Logic: '<S25>/Logical Operator10' incorporates:
   *  Logic: '<S25>/Logical Operator4'
   */
  rtb_LogicalOperator3_kawc = (uint8_T)((rtu_inBus_je2r != 0) &&
    (rtb_LogicalOperator_adei == 0));

  /* Memory: '<S25>/Memory4' */
  rtb_LogicalOperator_adei = localDW->Memory4_PreviousInput;

  /* Logic: '<S25>/Logical Operator7' incorporates:
   *  Logic: '<S25>/Logical Operator9'
   */
  rtb_LogicalOperator7_bbmk = (uint8_T)((rtu_inBus_ie1h != 0) &&
    (rtb_LogicalOperator_adei == 0));

  /* DataTypeConversion: '<S31>/Conversion6' */
  rtb_LogicalOperator_adei = (uint8_T)rtu_ratioBus;

  /* DataTypeConversion: '<S31>/Conversion2' */
  rtb_Conversion2_codi = rtb_Conversion7_lm4o;

  /* DataTypeConversion: '<S31>/Conversion7' incorporates:
   *  Constant: '<S27>/Constant3'
   */
  rtb_Conversion7_lm4o = (uint8_T)BKGEARPOS_dim;

  /* S-Function (Look2D_IR_U16): '<S31>/Look2D_IR_U16' incorporates:
   *  Constant: '<S27>/Constant2'
   *  Constant: '<S27>/TBRPMQSHIFTDNMAX'
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16_kz4c, &TBRPMQSHIFTDNMAX[0],
                rtu_ratioBus_ozbd, rtu_ratioBus_knrk, rtb_LogicalOperator_adei,
                rtb_Conversion2_codi, ((uint16_T)0U), rtb_Conversion7_lm4o);

  /* Chart: '<S27>/Hys_<_x' incorporates:
   *  Constant: '<S27>/QSRPMMAXHYS'
   */
  GearShiftMgm_Hys__x(rtu_inBus_ahd1, rtb_Look2D_IR_U16_kz4c, QSRPMMAXHYS,
                      &localB->sf_Hys__x, &localDW->sf_Hys__x);

  /* Logic: '<S27>/Logical Operator2' incorporates:
   *  Constant: '<S27>/QSMINGEARPOS'
   *  Constant: '<S29>/Constant'
   *  RelationalOperator: '<S27>/Relational Operator15'
   *  RelationalOperator: '<S29>/Compare'
   */
  localB->LogicalOperator2_jub4 = (uint8_T)((localB->LogicalOperator1 != 0) &&
    (rtu_inBus_ezdu != 0) && (rtu_inBus_cf2k >= QSMINGEARPOS) &&
    (localB->sf_Hys__x.out != 0));

  /* Logic: '<S25>/Logical Operator8' incorporates:
   *  Logic: '<S25>/Logical Operator11'
   */
  localB->LogicalOperator8 = (uint8_T)(((rtb_LogicalOperator7_bbmk != 0) ||
    (rtb_LogicalOperator3_kawc != 0)) && (localB->LogicalOperator2_jub4 != 0));

  /* Update for Memory: '<S25>/Memory1' */
  localDW->Memory1_PreviousInput = rtu_inBus_o5cv;

  /* Update for Memory: '<S25>/Memory2' */
  localDW->Memory2_PreviousInput = rtu_inBus_kg1o;

  /* Update for Memory: '<S25>/Memory3' */
  localDW->Memory3_PreviousInput = rtu_inBus_je2r;

  /* Update for Memory: '<S25>/Memory4' */
  localDW->Memory4_PreviousInput = rtu_inBus_ie1h;
}

/* Function for Chart: '<S6>/Chart' */
static void GearShiftMgm_QSHIFT_DISABLE(void)
{
  int32_T tmp;
  boolean_T guard1 = false;
  boolean_T guard2 = false;
  boolean_T guard3 = false;

  /* During 'QSHIFT_DISABLE': '<S9>:143' */
  /* Transition: '<S9>:147' */
  tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
  if (tmp > 255) {
    tmp = 255;
  }

  GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

  /* Inport: '<Root>/GearPos' */
  GearShiftMgm_B.GearPosQShift_p4ss = GearPos;

  /* Inport: '<Root>/CmeDriver' */
  GearShiftMgm_B.CmeDriverQShift_g55o = CmeDriver;

  /* Inport: '<Root>/Rpm' */
  GearShiftMgm_B.RpmQShift_jdme = Rpm;

  /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
  /* Inport: '<Root>/CmeDriver' incorporates:
   *  Inport: '<Root>/GasPosCC'
   */
  /* Event: '<S9>:115' */
  GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
    GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
    &GearShiftMgm_B.Calc_Ratio_lg2t3);

  /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

  /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
  /* Inport: '<Root>/FlgEnQs' incorporates:
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/DiagFlg02'
   *  Inport: '<Root>/FoGearDownSignal'
   *  Inport: '<Root>/FoGearUpSignal'
   *  Inport: '<Root>/GearDownSignal'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/GearUpSignal'
   *  Inport: '<Root>/QuickShiftEnCAN'
   *  Inport: '<Root>/QuickShiftTypeCAN'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/VtRec'
   */
  /* Event: '<S9>:55' */
  GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs, DiagFlg02,
    (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal, FoGearUpSignal, GearPos,
    ClutchSignal, GearDownSignal, FoGearDownSignal, QuickShiftTypeCAN, Rpm,
    GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
    GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
    GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
    &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

  /* End of Outputs for SubSystem: '<S6>/EnQs' */
  guard1 = false;
  guard2 = false;
  guard3 = false;
  if (SELQS2 != 0) {
    /* Transition: '<S9>:248' */
    if (FlgQSLow == 0) {
      /* Transition: '<S9>:149' */
      /* Transition: '<S9>:172' */
      if (GearShiftMgm_B.EnQs_lgkn.LogicalOperator8 != 0) {
        /* Transition: '<S9>:175' */
        GearShiftMgm_B.upDown = 0U;
        GearShiftMgm_B.QShiftCnt_oz3p = 0U;

        /* Outputs for Function Call SubSystem: '<S15>/fc_QsDnCtf_Calc' */
        /* Event: '<S9>:219' */
        GearShiftM_fc_QsDnCtf_Calc_ds5q();

        /* End of Outputs for SubSystem: '<S15>/fc_QsDnCtf_Calc' */
        tmp = GearShiftMgm_B.QsCtfTime + GearShiftMgm_B.CmeQsIPeriod_gnn3;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_DWork.ctfPeriod = (uint8_T)tmp;

        /* Transition: '<S9>:178' */
        guard1 = true;
      } else {
        /* Transition: '<S9>:170' */
        if (GearShiftMgm_B.EnQs_lgkn.LogicalOperator2 != 0) {
          /* Transition: '<S9>:173' */
          GearShiftMgm_B.upDown = 1U;
          GearShiftMgm_B.QShiftCnt_oz3p = 0U;

          /* Outputs for Function Call SubSystem: '<S15>/fc_QsUpCtf_Calc' */
          /* Event: '<S9>:220' */
          GearShiftM_fc_QsUpCtf_Calc_lq0y();

          /* End of Outputs for SubSystem: '<S15>/fc_QsUpCtf_Calc' */
          tmp = GearShiftMgm_B.QsCtfTime + GearShiftMgm_B.CmeQsIPeriod_gnn3;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_DWork.ctfPeriod = (uint8_T)tmp;
          guard1 = true;
        } else {
          /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
          /* Transition: '<S9>:194' */
          /* Event: '<S9>:267' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

          /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */
          guard2 = true;
        }
      }
    } else {
      /* Transition: '<S9>:151' */
      if (GearShiftMgm_B.EnQs_lgkn.LogicalOperator8 != 0) {
        /* Transition: '<S9>:153' */
        GearShiftMgm_B.upDown = 0U;
        GearShiftMgm_B.QShiftCnt_oz3p = 0U;

        /* Outputs for Function Call SubSystem: '<S15>/fc_QsDnBlp_Calc' */
        /* Event: '<S9>:68' */
        GearShiftM_fc_QsDnBlp_Calc_llms();

        /* End of Outputs for SubSystem: '<S15>/fc_QsDnBlp_Calc' */
        /* Transition: '<S9>:158' */
        guard3 = true;
      } else {
        /* Transition: '<S9>:155' */
        if (GearShiftMgm_B.EnQs_lgkn.LogicalOperator2 != 0) {
          /* Transition: '<S9>:157' */
          GearShiftMgm_B.upDown = 1U;
          GearShiftMgm_B.QShiftCnt_oz3p = 0U;

          /* Outputs for Function Call SubSystem: '<S15>/fc_QsUpBlp_Calc' */
          /* Event: '<S9>:218' */
          GearShiftM_fc_QsUpBlp_Calc_hvvt();

          /* End of Outputs for SubSystem: '<S15>/fc_QsUpBlp_Calc' */
          guard3 = true;
        } else {
          /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
          /* Transition: '<S9>:193' */
          /* Event: '<S9>:268' */
          GearShiftMgm_Calc_KFilt_Blp
            (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
             GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
             GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
             GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

          /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */
          guard2 = true;
        }
      }
    }
  } else {
    /* Transition: '<S9>:250' */
    /* Transition: '<S9>:251' */
    if ((GearShiftMgm_B.EnQs_lgkn.LogicalOperator8 != 0) && (CmeDriver <
         QSDNCMEDRIVERMAX)) {
      /* Transition: '<S9>:242' */
      GearShiftMgm_B.upDown = 0U;
      GearShiftMgm_B.QShiftCnt_oz3p = 0U;

      /* Outputs for Function Call SubSystem: '<S15>/fc_QsDnBlp_Calc' */
      /* Event: '<S9>:68' */
      GearShiftM_fc_QsDnBlp_Calc_llms();

      /* End of Outputs for SubSystem: '<S15>/fc_QsDnBlp_Calc' */
      /* Transition: '<S9>:244' */
      /* Transition: '<S9>:262' */
      /* Transition: '<S9>:158' */
      guard3 = true;
    } else {
      /* Transition: '<S9>:239' */
      if ((GearShiftMgm_B.EnQs_lgkn.LogicalOperator2 != 0) && (CmeDriver >
           QSUPCMEDRIVERMIN)) {
        /* Transition: '<S9>:238' */
        GearShiftMgm_B.upDown = 1U;
        GearShiftMgm_B.QShiftCnt_oz3p = 0U;

        /* Outputs for Function Call SubSystem: '<S15>/fc_QsUpCtf_Calc' */
        /* Event: '<S9>:220' */
        GearShiftM_fc_QsUpCtf_Calc_lq0y();

        /* End of Outputs for SubSystem: '<S15>/fc_QsUpCtf_Calc' */
        tmp = GearShiftMgm_B.QsCtfTime + GearShiftMgm_B.CmeQsIPeriod_gnn3;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_DWork.ctfPeriod = (uint8_T)tmp;

        /* Transition: '<S9>:237' */
        /* Transition: '<S9>:255' */
        /* Transition: '<S9>:178' */
        guard1 = true;
      } else {
        /* Transition: '<S9>:241' */
        /* Transition: '<S9>:246' */
        /* Transition: '<S9>:259' */
        /* Transition: '<S9>:260' */
        guard2 = true;
      }
    }
  }

  if (guard3) {
    /* Transition: '<S9>:160' */
    GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_BLIP_REQUEST);
    GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 1U;

    /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
    /* Event: '<S9>:268' */
    GearShiftMgm_Calc_KFilt_Blp
      (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
       GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
       GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
       GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

    /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */
    GearShiftMgm_B.reset = 1U;

    /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
    /* Event: '<S9>:116' */
    GearShift_ReadLookUpTables_b1et();

    /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
    GearShiftMgm_B.reset = 0U;

    /* Outputs for Function Call SubSystem: '<S6>/Output' */
    /* DataStoreWrite: '<S13>/Data Store Write1' */
    /* Event: '<S9>:227' */
    RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

    /* DataStoreWrite: '<S13>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

    /* DataStoreWrite: '<S13>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

    /* DataStoreWrite: '<S13>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

    /* DataStoreWrite: '<S13>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

    /* DataStoreWrite: '<S13>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_h20l;

    /* DataStoreWrite: '<S13>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

    /* DataStoreWrite: '<S13>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

    /* DataStoreWrite: '<S13>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

    /* DataStoreWrite: '<S13>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime;

    /* DataStoreWrite: '<S13>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_crco;

    /* DataStoreWrite: '<S13>/Data Store Write24' */
    QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

    /* DataStoreWrite: '<S13>/Data Store Write25' */
    QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

    /* DataStoreWrite: '<S13>/Data Store Write26' */
    QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

    /* DataStoreWrite: '<S13>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime;

    /* DataStoreWrite: '<S13>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion;

    /* DataStoreWrite: '<S13>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

    /* DataStoreWrite: '<S13>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1;

    /* DataStoreWrite: '<S13>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

    /* DataStoreWrite: '<S13>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

    /* DataStoreWrite: '<S13>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

    /* DataStoreWrite: '<S13>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

    /* End of Outputs for SubSystem: '<S6>/Output' */
    GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
      GearShif_IN_QSHIFT_BLIP_REQUEST;
  }

  if (guard2) {
    /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
    /* Transition: '<S9>:190' */
    /* Event: '<S9>:116' */
    GearShift_ReadLookUpTables_b1et();

    /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

    /* Outputs for Function Call SubSystem: '<S6>/Output' */
    /* DataStoreWrite: '<S13>/Data Store Write1' */
    /* Event: '<S9>:227' */
    RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

    /* DataStoreWrite: '<S13>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

    /* DataStoreWrite: '<S13>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

    /* DataStoreWrite: '<S13>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

    /* DataStoreWrite: '<S13>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

    /* DataStoreWrite: '<S13>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_h20l;

    /* DataStoreWrite: '<S13>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

    /* DataStoreWrite: '<S13>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

    /* DataStoreWrite: '<S13>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

    /* DataStoreWrite: '<S13>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime;

    /* DataStoreWrite: '<S13>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_crco;

    /* DataStoreWrite: '<S13>/Data Store Write24' */
    QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

    /* DataStoreWrite: '<S13>/Data Store Write25' */
    QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

    /* DataStoreWrite: '<S13>/Data Store Write26' */
    QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

    /* DataStoreWrite: '<S13>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime;

    /* DataStoreWrite: '<S13>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion;

    /* DataStoreWrite: '<S13>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

    /* DataStoreWrite: '<S13>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1;

    /* DataStoreWrite: '<S13>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

    /* DataStoreWrite: '<S13>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

    /* DataStoreWrite: '<S13>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

    /* DataStoreWrite: '<S13>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

    /* End of Outputs for SubSystem: '<S6>/Output' */
  }

  if (guard1) {
    /* Transition: '<S9>:184' */
    GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_CTF_REQUEST);
    GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 1U;

    /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
    /* Event: '<S9>:267' */
    GearShiftMgm_Calc_KFilt_Ctf
      (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
       GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
       GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
       GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

    /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */
    GearShiftMgm_B.reset = 1U;

    /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
    /* Event: '<S9>:116' */
    GearShift_ReadLookUpTables_b1et();

    /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
    GearShiftMgm_B.reset = 0U;

    /* Outputs for Function Call SubSystem: '<S6>/Output' */
    /* DataStoreWrite: '<S13>/Data Store Write1' */
    /* Event: '<S9>:227' */
    RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

    /* DataStoreWrite: '<S13>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

    /* DataStoreWrite: '<S13>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

    /* DataStoreWrite: '<S13>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

    /* DataStoreWrite: '<S13>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

    /* DataStoreWrite: '<S13>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_h20l;

    /* DataStoreWrite: '<S13>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

    /* DataStoreWrite: '<S13>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

    /* DataStoreWrite: '<S13>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

    /* DataStoreWrite: '<S13>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime;

    /* DataStoreWrite: '<S13>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_crco;

    /* DataStoreWrite: '<S13>/Data Store Write24' */
    QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

    /* DataStoreWrite: '<S13>/Data Store Write25' */
    QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

    /* DataStoreWrite: '<S13>/Data Store Write26' */
    QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

    /* DataStoreWrite: '<S13>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime;

    /* DataStoreWrite: '<S13>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion;

    /* DataStoreWrite: '<S13>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

    /* DataStoreWrite: '<S13>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1;

    /* DataStoreWrite: '<S13>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

    /* DataStoreWrite: '<S13>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

    /* DataStoreWrite: '<S13>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

    /* DataStoreWrite: '<S13>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

    /* End of Outputs for SubSystem: '<S6>/Output' */
    GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
      Gear_IN_QSHIFT_CTF_REQUEST_lgvf;
  }
}

/* Function for Chart: '<S6>/Chart' */
static void GearShiftMg_QSHIFT_BLIP_REQUEST(void)
{
  int32_T tmp;

  /* During 'QSHIFT_BLIP_REQUEST': '<S9>:159' */
  /* Transition: '<S9>:169' */
  tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
  if (tmp > 255) {
    tmp = 255;
  }

  GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

  /* Inport: '<Root>/ClutchSignal' */
  if ((ClutchSignal == 0) || (GearShiftMgm_B.QShiftCnt_oz3p >=
       GearShiftMgm_B.QsBlpTime)) {
    /* Transition: '<S9>:168' */
    if (GearShiftMgm_B.QsBlpToTime == 0) {
      /* Transition: '<S9>:327' */
      GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_BLIP_WAIT);
      GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;

      /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
      /* Inport: '<Root>/CmeDriver' incorporates:
       *  Inport: '<Root>/GasPosCC'
       */
      /* Event: '<S9>:115' */
      GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
        GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
        &GearShiftMgm_B.Calc_Ratio_lg2t3);

      /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

      /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
      /* Event: '<S9>:268' */
      GearShiftMgm_Calc_KFilt_Blp
        (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
         GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
         GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
         GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

      /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */

      /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
      /* Event: '<S9>:116' */
      GearShift_ReadLookUpTables_b1et();

      /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

      /* Outputs for Function Call SubSystem: '<S6>/Output' */
      /* DataStoreWrite: '<S13>/Data Store Write1' */
      /* Event: '<S9>:227' */
      RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

      /* DataStoreWrite: '<S13>/Data Store Write17' */
      QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

      /* DataStoreWrite: '<S13>/Data Store Write3' */
      QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

      /* DataStoreWrite: '<S13>/Data Store Write4' */
      QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

      /* DataStoreWrite: '<S13>/Data Store Write5' */
      CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

      /* DataStoreWrite: '<S13>/Data Store Write6' */
      StQShift = GearShiftMgm_B.StQShift_h20l;

      /* DataStoreWrite: '<S13>/Data Store Write8' */
      GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

      /* DataStoreWrite: '<S13>/Data Store Write10' */
      CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

      /* DataStoreWrite: '<S13>/Data Store Write11' */
      CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

      /* DataStoreWrite: '<S13>/Data Store Write12' */
      QSBlpTime = GearShiftMgm_B.QsBlpTime;

      /* DataStoreWrite: '<S13>/Data Store Write13' */
      CmeQsI = GearShiftMgm_B.CmeQsI_crco;

      /* DataStoreWrite: '<S13>/Data Store Write24' */
      QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

      /* DataStoreWrite: '<S13>/Data Store Write25' */
      QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

      /* DataStoreWrite: '<S13>/Data Store Write26' */
      QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

      /* DataStoreWrite: '<S13>/Data Store Write9' */
      QSCtfTime = GearShiftMgm_B.QsCtfTime;

      /* DataStoreWrite: '<S13>/Data Store Write14' */
      CmeQsPFilt = GearShiftMgm_B.Conversion;

      /* DataStoreWrite: '<S13>/Data Store Write15' */
      QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

      /* DataStoreWrite: '<S13>/Data Store Write16' */
      CtfGearShift = GearShiftMgm_B.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write2' */
      CmeQsIFilt = GearShiftMgm_B.Switch1;

      /* DataStoreWrite: '<S13>/Data Store Write7' */
      QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write18' */
      QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write19' */
      QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

      /* DataStoreWrite: '<S13>/Data Store Write20' */
      FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write21' */
      QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

      /* DataStoreWrite: '<S13>/Data Store Write22' */
      FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

      /* DataStoreWrite: '<S13>/Data Store Write23' */
      FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

      /* End of Outputs for SubSystem: '<S6>/Output' */
      /* Transition: '<S9>:326' */
      /* Transition: '<S9>:325' */
      GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
        GearShiftMg_IN_QSHIFT_BLIP_WAIT;
    } else {
      /* Transition: '<S9>:322' */
      GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_BLP_REDUCED);
      GearShiftMgm_B.cmeGain = 1U;

      /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
      /* Inport: '<Root>/CmeDriver' incorporates:
       *  Inport: '<Root>/GasPosCC'
       */
      /* Event: '<S9>:115' */
      GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
        GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
        &GearShiftMgm_B.Calc_Ratio_lg2t3);

      /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

      /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
      /* Event: '<S9>:268' */
      GearShiftMgm_Calc_KFilt_Blp
        (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
         GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
         GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
         GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

      /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */

      /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
      /* Event: '<S9>:116' */
      GearShift_ReadLookUpTables_b1et();

      /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

      /* Outputs for Function Call SubSystem: '<S6>/Output' */
      /* DataStoreWrite: '<S13>/Data Store Write1' */
      /* Event: '<S9>:227' */
      RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

      /* DataStoreWrite: '<S13>/Data Store Write17' */
      QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

      /* DataStoreWrite: '<S13>/Data Store Write3' */
      QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

      /* DataStoreWrite: '<S13>/Data Store Write4' */
      QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

      /* DataStoreWrite: '<S13>/Data Store Write5' */
      CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

      /* DataStoreWrite: '<S13>/Data Store Write6' */
      StQShift = GearShiftMgm_B.StQShift_h20l;

      /* DataStoreWrite: '<S13>/Data Store Write8' */
      GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

      /* DataStoreWrite: '<S13>/Data Store Write10' */
      CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

      /* DataStoreWrite: '<S13>/Data Store Write11' */
      CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

      /* DataStoreWrite: '<S13>/Data Store Write12' */
      QSBlpTime = GearShiftMgm_B.QsBlpTime;

      /* DataStoreWrite: '<S13>/Data Store Write13' */
      CmeQsI = GearShiftMgm_B.CmeQsI_crco;

      /* DataStoreWrite: '<S13>/Data Store Write24' */
      QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

      /* DataStoreWrite: '<S13>/Data Store Write25' */
      QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

      /* DataStoreWrite: '<S13>/Data Store Write26' */
      QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

      /* DataStoreWrite: '<S13>/Data Store Write9' */
      QSCtfTime = GearShiftMgm_B.QsCtfTime;

      /* DataStoreWrite: '<S13>/Data Store Write14' */
      CmeQsPFilt = GearShiftMgm_B.Conversion;

      /* DataStoreWrite: '<S13>/Data Store Write15' */
      QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

      /* DataStoreWrite: '<S13>/Data Store Write16' */
      CtfGearShift = GearShiftMgm_B.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write2' */
      CmeQsIFilt = GearShiftMgm_B.Switch1;

      /* DataStoreWrite: '<S13>/Data Store Write7' */
      QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write18' */
      QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write19' */
      QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

      /* DataStoreWrite: '<S13>/Data Store Write20' */
      FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write21' */
      QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

      /* DataStoreWrite: '<S13>/Data Store Write22' */
      FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

      /* DataStoreWrite: '<S13>/Data Store Write23' */
      FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

      /* End of Outputs for SubSystem: '<S6>/Output' */
      GearShiftMgm_B.QShiftCnt_oz3p = 0U;
      GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
        GearShift_IN_QSHIFT_BLP_REDUCED;
    }
  } else {
    /* Inport: '<Root>/GearUpSignal' incorporates:
     *  Inport: '<Root>/FoTrgQS'
     *  Inport: '<Root>/GearDownSignal'
     */
    /* Transition: '<S9>:167' */
    if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
        ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) || (FoTrgQS != 0))
    {
      /* Transition: '<S9>:209' */
      /* Transition: '<S9>:201' */
      GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
      GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
      GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
      tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
      if (tmp > 255) {
        tmp = 255;
      }

      GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

      /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
      /* Inport: '<Root>/CmeDriver' incorporates:
       *  Inport: '<Root>/GasPosCC'
       */
      /* Event: '<S9>:115' */
      GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
        GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
        &GearShiftMgm_B.Calc_Ratio_lg2t3);

      /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

      /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
      /* Inport: '<Root>/FlgEnQs' incorporates:
       *  Inport: '<Root>/DiagFlg02'
       *  Inport: '<Root>/FoGearDownSignal'
       *  Inport: '<Root>/FoGearUpSignal'
       *  Inport: '<Root>/GearPos'
       *  Inport: '<Root>/QuickShiftEnCAN'
       *  Inport: '<Root>/QuickShiftTypeCAN'
       *  Inport: '<Root>/Rpm'
       *  Inport: '<Root>/VtRec'
       */
      /* Event: '<S9>:55' */
      GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs, DiagFlg02,
        (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal, FoGearUpSignal, GearPos,
        ClutchSignal, GearDownSignal, FoGearDownSignal, QuickShiftTypeCAN, Rpm,
        GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
        GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
        GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
        &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

      /* End of Outputs for SubSystem: '<S6>/EnQs' */

      /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
      /* Event: '<S9>:116' */
      GearShift_ReadLookUpTables_b1et();

      /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
      GearShiftMgm_B.cluReset = 0U;

      /* Outputs for Function Call SubSystem: '<S6>/Output' */
      /* DataStoreWrite: '<S13>/Data Store Write1' */
      /* Event: '<S9>:227' */
      RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

      /* DataStoreWrite: '<S13>/Data Store Write17' */
      QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

      /* DataStoreWrite: '<S13>/Data Store Write3' */
      QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

      /* DataStoreWrite: '<S13>/Data Store Write4' */
      QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

      /* DataStoreWrite: '<S13>/Data Store Write5' */
      CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

      /* DataStoreWrite: '<S13>/Data Store Write6' */
      StQShift = GearShiftMgm_B.StQShift_h20l;

      /* DataStoreWrite: '<S13>/Data Store Write8' */
      GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

      /* DataStoreWrite: '<S13>/Data Store Write10' */
      CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

      /* DataStoreWrite: '<S13>/Data Store Write11' */
      CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

      /* DataStoreWrite: '<S13>/Data Store Write12' */
      QSBlpTime = GearShiftMgm_B.QsBlpTime;

      /* DataStoreWrite: '<S13>/Data Store Write13' */
      CmeQsI = GearShiftMgm_B.CmeQsI_crco;

      /* DataStoreWrite: '<S13>/Data Store Write24' */
      QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

      /* DataStoreWrite: '<S13>/Data Store Write25' */
      QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

      /* DataStoreWrite: '<S13>/Data Store Write26' */
      QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

      /* DataStoreWrite: '<S13>/Data Store Write9' */
      QSCtfTime = GearShiftMgm_B.QsCtfTime;

      /* DataStoreWrite: '<S13>/Data Store Write14' */
      CmeQsPFilt = GearShiftMgm_B.Conversion;

      /* DataStoreWrite: '<S13>/Data Store Write15' */
      QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

      /* DataStoreWrite: '<S13>/Data Store Write16' */
      CtfGearShift = GearShiftMgm_B.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write2' */
      CmeQsIFilt = GearShiftMgm_B.Switch1;

      /* DataStoreWrite: '<S13>/Data Store Write7' */
      QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write18' */
      QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write19' */
      QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

      /* DataStoreWrite: '<S13>/Data Store Write20' */
      FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write21' */
      QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

      /* DataStoreWrite: '<S13>/Data Store Write22' */
      FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

      /* DataStoreWrite: '<S13>/Data Store Write23' */
      FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

      /* End of Outputs for SubSystem: '<S6>/Output' */
      GearShiftMgm_B.cmeGain = 0U;
      GearShiftMgm_B.filtGain = 0U;
      GearShiftMgm_DWork.flgJustDbl = 0U;
      GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
        GearShif_IN_QSHIFT_DISABLE_o1bn;
    } else {
      /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
      /* Inport: '<Root>/CmeDriver' incorporates:
       *  Inport: '<Root>/GasPosCC'
       */
      /* Transition: '<S9>:210' */
      /* Event: '<S9>:115' */
      GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
        GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
        &GearShiftMgm_B.Calc_Ratio_lg2t3);

      /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

      /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
      /* Event: '<S9>:268' */
      GearShiftMgm_Calc_KFilt_Blp
        (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
         GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
         GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
         GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

      /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */

      /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
      /* Event: '<S9>:116' */
      GearShift_ReadLookUpTables_b1et();

      /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

      /* Outputs for Function Call SubSystem: '<S6>/Output' */
      /* DataStoreWrite: '<S13>/Data Store Write1' */
      /* Event: '<S9>:227' */
      RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

      /* DataStoreWrite: '<S13>/Data Store Write17' */
      QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

      /* DataStoreWrite: '<S13>/Data Store Write3' */
      QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

      /* DataStoreWrite: '<S13>/Data Store Write4' */
      QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

      /* DataStoreWrite: '<S13>/Data Store Write5' */
      CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

      /* DataStoreWrite: '<S13>/Data Store Write6' */
      StQShift = GearShiftMgm_B.StQShift_h20l;

      /* DataStoreWrite: '<S13>/Data Store Write8' */
      GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

      /* DataStoreWrite: '<S13>/Data Store Write10' */
      CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

      /* DataStoreWrite: '<S13>/Data Store Write11' */
      CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

      /* DataStoreWrite: '<S13>/Data Store Write12' */
      QSBlpTime = GearShiftMgm_B.QsBlpTime;

      /* DataStoreWrite: '<S13>/Data Store Write13' */
      CmeQsI = GearShiftMgm_B.CmeQsI_crco;

      /* DataStoreWrite: '<S13>/Data Store Write24' */
      QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

      /* DataStoreWrite: '<S13>/Data Store Write25' */
      QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

      /* DataStoreWrite: '<S13>/Data Store Write26' */
      QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

      /* DataStoreWrite: '<S13>/Data Store Write9' */
      QSCtfTime = GearShiftMgm_B.QsCtfTime;

      /* DataStoreWrite: '<S13>/Data Store Write14' */
      CmeQsPFilt = GearShiftMgm_B.Conversion;

      /* DataStoreWrite: '<S13>/Data Store Write15' */
      QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

      /* DataStoreWrite: '<S13>/Data Store Write16' */
      CtfGearShift = GearShiftMgm_B.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write2' */
      CmeQsIFilt = GearShiftMgm_B.Switch1;

      /* DataStoreWrite: '<S13>/Data Store Write7' */
      QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write18' */
      QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write19' */
      QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

      /* DataStoreWrite: '<S13>/Data Store Write20' */
      FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write21' */
      QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

      /* DataStoreWrite: '<S13>/Data Store Write22' */
      FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

      /* DataStoreWrite: '<S13>/Data Store Write23' */
      FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

      /* End of Outputs for SubSystem: '<S6>/Output' */
    }

    /* End of Inport: '<Root>/GearUpSignal' */
  }

  /* End of Inport: '<Root>/ClutchSignal' */
}

/* Function for Chart: '<S6>/Chart' */
static void GearShiftMgm_QSHIFT_BLIP_WAIT(void)
{
  int32_T tmp;
  boolean_T guard1 = false;

  /* Inport: '<Root>/ClutchSignal' */
  /* During 'QSHIFT_BLIP_WAIT': '<S9>:202' */
  /* Transition: '<S9>:281' */
  guard1 = false;
  if (ClutchSignal == 0) {
    /* Transition: '<S9>:283' */
    GearShiftMgm_B.cluReset = 1U;

    /* Transition: '<S9>:284' */
    /* Transition: '<S9>:277' */
    guard1 = true;
  } else {
    /* Transition: '<S9>:207' */
    if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
        ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) || (FoTrgQS != 0))
    {
      /* Transition: '<S9>:204' */
      guard1 = true;
    } else {
      /* Transition: '<S9>:205' */
      tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
      if (tmp > 255) {
        tmp = 255;
      }

      GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

      /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
      /* Inport: '<Root>/CmeDriver' incorporates:
       *  Inport: '<Root>/GasPosCC'
       */
      /* Event: '<S9>:115' */
      GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
        GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
        &GearShiftMgm_B.Calc_Ratio_lg2t3);

      /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

      /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
      /* Event: '<S9>:268' */
      GearShiftMgm_Calc_KFilt_Blp
        (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
         GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
         GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
         GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

      /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */

      /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
      /* Event: '<S9>:116' */
      GearShift_ReadLookUpTables_b1et();

      /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

      /* Outputs for Function Call SubSystem: '<S6>/Output' */
      /* DataStoreWrite: '<S13>/Data Store Write1' */
      /* Event: '<S9>:227' */
      RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

      /* DataStoreWrite: '<S13>/Data Store Write17' */
      QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

      /* DataStoreWrite: '<S13>/Data Store Write3' */
      QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

      /* DataStoreWrite: '<S13>/Data Store Write4' */
      QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

      /* DataStoreWrite: '<S13>/Data Store Write5' */
      CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

      /* DataStoreWrite: '<S13>/Data Store Write6' */
      StQShift = GearShiftMgm_B.StQShift_h20l;

      /* DataStoreWrite: '<S13>/Data Store Write8' */
      GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

      /* DataStoreWrite: '<S13>/Data Store Write10' */
      CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

      /* DataStoreWrite: '<S13>/Data Store Write11' */
      CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

      /* DataStoreWrite: '<S13>/Data Store Write12' */
      QSBlpTime = GearShiftMgm_B.QsBlpTime;

      /* DataStoreWrite: '<S13>/Data Store Write13' */
      CmeQsI = GearShiftMgm_B.CmeQsI_crco;

      /* DataStoreWrite: '<S13>/Data Store Write24' */
      QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

      /* DataStoreWrite: '<S13>/Data Store Write25' */
      QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

      /* DataStoreWrite: '<S13>/Data Store Write26' */
      QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

      /* DataStoreWrite: '<S13>/Data Store Write9' */
      QSCtfTime = GearShiftMgm_B.QsCtfTime;

      /* DataStoreWrite: '<S13>/Data Store Write14' */
      CmeQsPFilt = GearShiftMgm_B.Conversion;

      /* DataStoreWrite: '<S13>/Data Store Write15' */
      QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

      /* DataStoreWrite: '<S13>/Data Store Write16' */
      CtfGearShift = GearShiftMgm_B.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write2' */
      CmeQsIFilt = GearShiftMgm_B.Switch1;

      /* DataStoreWrite: '<S13>/Data Store Write7' */
      QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write18' */
      QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

      /* DataStoreWrite: '<S13>/Data Store Write19' */
      QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

      /* DataStoreWrite: '<S13>/Data Store Write20' */
      FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

      /* DataStoreWrite: '<S13>/Data Store Write21' */
      QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

      /* DataStoreWrite: '<S13>/Data Store Write22' */
      FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

      /* DataStoreWrite: '<S13>/Data Store Write23' */
      FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

      /* End of Outputs for SubSystem: '<S6>/Output' */
    }
  }

  if (guard1) {
    /* Transition: '<S9>:340' */
    /* Transition: '<S9>:200' */
    /* Transition: '<S9>:294' */
    /* Transition: '<S9>:201' */
    GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
    GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
    GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
    tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
    if (tmp > 255) {
      tmp = 255;
    }

    GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

    /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
    /* Inport: '<Root>/CmeDriver' incorporates:
     *  Inport: '<Root>/GasPosCC'
     */
    /* Event: '<S9>:115' */
    GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
      GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
      &GearShiftMgm_B.Calc_Ratio_lg2t3);

    /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

    /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
    /* Inport: '<Root>/FlgEnQs' incorporates:
     *  Inport: '<Root>/DiagFlg02'
     *  Inport: '<Root>/FoGearDownSignal'
     *  Inport: '<Root>/FoGearUpSignal'
     *  Inport: '<Root>/GearDownSignal'
     *  Inport: '<Root>/GearPos'
     *  Inport: '<Root>/GearUpSignal'
     *  Inport: '<Root>/QuickShiftEnCAN'
     *  Inport: '<Root>/QuickShiftTypeCAN'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/VtRec'
     */
    /* Event: '<S9>:55' */
    GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs, DiagFlg02,
      (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal, FoGearUpSignal, GearPos,
      ClutchSignal, GearDownSignal, FoGearDownSignal, QuickShiftTypeCAN, Rpm,
      GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
      GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
      GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
      &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

    /* End of Outputs for SubSystem: '<S6>/EnQs' */

    /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
    /* Event: '<S9>:116' */
    GearShift_ReadLookUpTables_b1et();

    /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
    GearShiftMgm_B.cluReset = 0U;

    /* Outputs for Function Call SubSystem: '<S6>/Output' */
    /* DataStoreWrite: '<S13>/Data Store Write1' */
    /* Event: '<S9>:227' */
    RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

    /* DataStoreWrite: '<S13>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

    /* DataStoreWrite: '<S13>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

    /* DataStoreWrite: '<S13>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

    /* DataStoreWrite: '<S13>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

    /* DataStoreWrite: '<S13>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_h20l;

    /* DataStoreWrite: '<S13>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

    /* DataStoreWrite: '<S13>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

    /* DataStoreWrite: '<S13>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

    /* DataStoreWrite: '<S13>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime;

    /* DataStoreWrite: '<S13>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_crco;

    /* DataStoreWrite: '<S13>/Data Store Write24' */
    QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

    /* DataStoreWrite: '<S13>/Data Store Write25' */
    QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

    /* DataStoreWrite: '<S13>/Data Store Write26' */
    QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

    /* DataStoreWrite: '<S13>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime;

    /* DataStoreWrite: '<S13>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion;

    /* DataStoreWrite: '<S13>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

    /* DataStoreWrite: '<S13>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1;

    /* DataStoreWrite: '<S13>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

    /* DataStoreWrite: '<S13>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

    /* DataStoreWrite: '<S13>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

    /* DataStoreWrite: '<S13>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

    /* End of Outputs for SubSystem: '<S6>/Output' */
    GearShiftMgm_B.cmeGain = 0U;
    GearShiftMgm_B.filtGain = 0U;
    GearShiftMgm_DWork.flgJustDbl = 0U;
    GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
      GearShif_IN_QSHIFT_DISABLE_o1bn;
  }

  /* End of Inport: '<Root>/ClutchSignal' */
}

/* Output and update for function-call system: '<S1>/T10ms' */
void GearShiftMgm_T10ms(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_S16_U16;
  int16_T rtb_LookUp_S16_U16_g1ie;
  uint8_T rtb_Conversion3;
  int32_T tmp;
  boolean_T guard1 = false;
  boolean_T guard2 = false;
  boolean_T guard3 = false;
  boolean_T guard4 = false;

  /* If: '<S3>/If' incorporates:
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/CmeDriver'
   *  Inport: '<Root>/DiagFlg02'
   *  Inport: '<Root>/EECANNode4En'
   *  Inport: '<Root>/FlgEnQs'
   *  Inport: '<Root>/FlgGearDnCL'
   *  Inport: '<Root>/FlgGearUpCL'
   *  Inport: '<Root>/FoGearDownSignal'
   *  Inport: '<Root>/FoGearUpSignal'
   *  Inport: '<Root>/FoTrgQS'
   *  Inport: '<Root>/GasPosCC'
   *  Inport: '<Root>/GearDownSignal'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/GearShiftWait'
   *  Inport: '<Root>/GearUpSignal'
   *  Inport: '<Root>/QuickShiftEnCAN'
   *  Inport: '<Root>/QuickShiftTypeCAN'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/VtRec'
   */
  guard1 = false;
  guard2 = false;
  guard3 = false;
  guard4 = false;
  if (EECANNode4En != 0) {
    /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
     *  ActionPort: '<S7>/else'
     */
    /* DataTypeConversion: '<S102>/Conversion3' incorporates:
     *  Constant: '<S91>/BKFLGQSLOW_dim'
     */
    rtb_Conversion3 = (uint8_T)BKFLGQSLOW_dim;

    /* S-Function (LookUp_S16_U16): '<S102>/LookUp_S16_U16' incorporates:
     *  Constant: '<S91>/BKFLGQSLOW'
     *  Constant: '<S91>/VTFLGQSLOW'
     *  Inport: '<Root>/Rpm'
     */
    LookUp_S16_U16( &rtb_LookUp_S16_U16_g1ie, &VTFLGQSLOW[0], Rpm, &BKFLGQSLOW[0],
                   rtb_Conversion3);

    /* RelationalOperator: '<S91>/Relational Operator' incorporates:
     *  DataStoreWrite: '<S91>/Data Store Write13'
     *  Inport: '<Root>/CmeDriver'
     */
    FlgQSLow = (uint8_T)(CmeDriver < rtb_LookUp_S16_U16_g1ie);

    /* End of Outputs for SubSystem: '<S3>/Digital' */
    /* Gateway: GearShiftMgm/T10ms/Digital/Chart */
    /* During: GearShiftMgm/T10ms/Digital/Chart */
    if (GearShiftMgm_DWork.bitsForTID0.is_active_c2_GearShiftMgm == 0U) {
      /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
       *  ActionPort: '<S7>/else'
       */
      /* Entry: GearShiftMgm/T10ms/Digital/Chart */
      GearShiftMgm_DWork.bitsForTID0.is_active_c2_GearShiftMgm = 1U;

      /* Entry Internal: GearShiftMgm/T10ms/Digital/Chart */
      /* Transition: '<S90>:161' */
      GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_DISABLE);
      GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
        GearShiftMgm_IN_QSHIFT_DISABLE;

      /* End of Outputs for SubSystem: '<S3>/Digital' */
    } else {
      switch (GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm) {
       case GearShif_IN_QSHIFT_BLIP_REQUEST:
        /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
         *  ActionPort: '<S7>/else'
         */
        /* During 'QSHIFT_BLIP_REQUEST': '<S90>:159' */
        /* Transition: '<S90>:169' */
        tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

        /*    */
        if ((ClutchSignal == 0) || (GearShiftWait > ((uint8_T)GEAR_SHIFT_WAIT)) ||
            (GearShiftMgm_B.QShiftCnt_lib2 >= GearShiftMgm_B.QsBlpTime_co1y)) {
          /* Transition: '<S90>:168' */
          GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_BLIP_WAIT);
          GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 0U;
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Blp' */
          /* Event: '<S90>:247' */
          GearShiftMgm_Calc_KFilt_Blp
            (GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh,
             GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
             GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
             GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Blp);

          /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Blp' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
            GearShiftMg_IN_QSHIFT_BLIP_WAIT;
        } else {
          /* Transition: '<S90>:167' */
          if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 1)) ||
              ((GearDownSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 0) &&
               (GearShiftWait == ((uint8_T)GEAR_SHIFT_IDLE))) || (GearShiftWait >
               ((uint8_T)GEAR_SHIFT_RETURN))) {
            /* Transition: '<S90>:209' */
            /* Transition: '<S90>:201' */
            GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_DISABLE);
            GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 0U;
            GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 0U;
            tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

            /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
            /* Event: '<S90>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
              GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio);

            /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S7>/EnQs' */
            /* Event: '<S90>:55' */
            GearShiftMgm_EnQs();

            /* End of Outputs for SubSystem: '<S7>/EnQs' */

            /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
            /* Event: '<S90>:116' */
            GearShiftMgm_ReadLookUpTables();

            /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S7>/Output' */
            /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S90>:231' */
            RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

            /* DataStoreWrite: '<S94>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

            /* DataStoreWrite: '<S94>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

            /* DataStoreWrite: '<S94>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

            /* DataStoreWrite: '<S94>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

            /* DataStoreWrite: '<S94>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_gx4w;

            /* DataStoreWrite: '<S94>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

            /* DataStoreWrite: '<S94>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

            /* DataStoreWrite: '<S94>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

            /* DataStoreWrite: '<S94>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

            /* DataStoreWrite: '<S94>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

            /* DataStoreWrite: '<S94>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

            /* DataStoreWrite: '<S94>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

            /* DataStoreWrite: '<S94>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

            /* DataStoreWrite: '<S94>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

            /* DataStoreWrite: '<S94>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

            /* DataStoreWrite: '<S94>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

            /* DataStoreWrite: '<S94>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

            /* DataStoreWrite: '<S94>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

            /* DataStoreWrite: '<S94>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

            /* DataStoreWrite: '<S94>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

            /* DataStoreWrite: '<S94>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

            /* DataStoreWrite: '<S94>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

            /* DataStoreWrite: '<S94>/Data Store Write24' */
            FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

            /* End of Outputs for SubSystem: '<S7>/Output' */
            GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
              GearShiftMgm_IN_QSHIFT_DISABLE;
          } else {
            /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
            /* Transition: '<S90>:210' */
            /* Event: '<S90>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
              GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio);

            /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Blp' */
            /* Event: '<S90>:247' */
            GearShiftMgm_Calc_KFilt_Blp
              (GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh,
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
               GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Blp);

            /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Blp' */

            /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
            /* Event: '<S90>:116' */
            GearShiftMgm_ReadLookUpTables();

            /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S7>/Output' */
            /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S90>:231' */
            RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

            /* DataStoreWrite: '<S94>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

            /* DataStoreWrite: '<S94>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

            /* DataStoreWrite: '<S94>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

            /* DataStoreWrite: '<S94>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

            /* DataStoreWrite: '<S94>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_gx4w;

            /* DataStoreWrite: '<S94>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

            /* DataStoreWrite: '<S94>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

            /* DataStoreWrite: '<S94>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

            /* DataStoreWrite: '<S94>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

            /* DataStoreWrite: '<S94>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

            /* DataStoreWrite: '<S94>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

            /* DataStoreWrite: '<S94>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

            /* DataStoreWrite: '<S94>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

            /* DataStoreWrite: '<S94>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

            /* DataStoreWrite: '<S94>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

            /* DataStoreWrite: '<S94>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

            /* DataStoreWrite: '<S94>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

            /* DataStoreWrite: '<S94>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

            /* DataStoreWrite: '<S94>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

            /* DataStoreWrite: '<S94>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

            /* DataStoreWrite: '<S94>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

            /* DataStoreWrite: '<S94>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

            /* DataStoreWrite: '<S94>/Data Store Write24' */
            FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

            /* End of Outputs for SubSystem: '<S7>/Output' */
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Digital' */
        break;

       case GearShiftMg_IN_QSHIFT_BLIP_WAIT:
        /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
         *  ActionPort: '<S7>/else'
         */
        /* During 'QSHIFT_BLIP_WAIT': '<S90>:202' */
        /* Transition: '<S90>:207' */
        if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 1)) ||
            ((GearDownSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 0) &&
             (GearShiftWait == ((uint8_T)GEAR_SHIFT_IDLE))) || (GearShiftWait >
             ((uint8_T)GEAR_SHIFT_RETURN))) {
          /* Transition: '<S90>:204' */
          /* Transition: '<S90>:242' */
          /* Transition: '<S90>:200' */
          /* Transition: '<S90>:201' */
          GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_DISABLE);
          GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 0U;
          GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 0U;
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S7>/EnQs' */
          /* Event: '<S90>:55' */
          GearShiftMgm_EnQs();

          /* End of Outputs for SubSystem: '<S7>/EnQs' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
            GearShiftMgm_IN_QSHIFT_DISABLE;
        } else {
          /* Transition: '<S90>:205' */
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Blp' */
          /* Event: '<S90>:247' */
          GearShiftMgm_Calc_KFilt_Blp
            (GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh,
             GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
             GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
             GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Blp);

          /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Blp' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
        }

        /* End of Outputs for SubSystem: '<S3>/Digital' */
        break;

       case GearShift_IN_QSHIFT_CTF_REDUCED:
        /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
         *  ActionPort: '<S7>/else'
         */
        /* During 'QSHIFT_CTF_REDUCED': '<S90>:234' */
        /* Transition: '<S90>:237' */
        tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;
        if ((ClutchSignal == 0) || (GearShiftWait > ((uint8_T)GEAR_SHIFT_WAIT)) ||
            (GearShiftMgm_B.QShiftCnt_lib2 >= GearShiftMgm_DWork.ctfPeriod_csmp))
        {
          /* Transition: '<S90>:240' */
          GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_CTF_WAIT);
          GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 0U;
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Ctf' */
          /* Event: '<S90>:246' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);

          /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Ctf' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
            GearShiftMgm_IN_QSHIFT_CTF_WAIT;
        } else {
          /* Transition: '<S90>:238' */
          if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 1)) ||
              ((GearDownSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 0) &&
               (GearShiftWait == ((uint8_T)GEAR_SHIFT_IDLE))) || (GearShiftWait >
               ((uint8_T)GEAR_SHIFT_RETURN))) {
            /* Transition: '<S90>:243' */
            /* Transition: '<S90>:200' */
            /* Transition: '<S90>:201' */
            GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_DISABLE);
            GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 0U;
            GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 0U;
            tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

            /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
            /* Event: '<S90>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
              GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio);

            /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S7>/EnQs' */
            /* Event: '<S90>:55' */
            GearShiftMgm_EnQs();

            /* End of Outputs for SubSystem: '<S7>/EnQs' */

            /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
            /* Event: '<S90>:116' */
            GearShiftMgm_ReadLookUpTables();

            /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S7>/Output' */
            /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S90>:231' */
            RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

            /* DataStoreWrite: '<S94>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

            /* DataStoreWrite: '<S94>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

            /* DataStoreWrite: '<S94>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

            /* DataStoreWrite: '<S94>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

            /* DataStoreWrite: '<S94>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_gx4w;

            /* DataStoreWrite: '<S94>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

            /* DataStoreWrite: '<S94>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

            /* DataStoreWrite: '<S94>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

            /* DataStoreWrite: '<S94>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

            /* DataStoreWrite: '<S94>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

            /* DataStoreWrite: '<S94>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

            /* DataStoreWrite: '<S94>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

            /* DataStoreWrite: '<S94>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

            /* DataStoreWrite: '<S94>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

            /* DataStoreWrite: '<S94>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

            /* DataStoreWrite: '<S94>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

            /* DataStoreWrite: '<S94>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

            /* DataStoreWrite: '<S94>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

            /* DataStoreWrite: '<S94>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

            /* DataStoreWrite: '<S94>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

            /* DataStoreWrite: '<S94>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

            /* DataStoreWrite: '<S94>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

            /* DataStoreWrite: '<S94>/Data Store Write24' */
            FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

            /* End of Outputs for SubSystem: '<S7>/Output' */
            GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
              GearShiftMgm_IN_QSHIFT_DISABLE;
          } else {
            /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
            /* Transition: '<S90>:239' */
            /* Event: '<S90>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
              GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio);

            /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Ctf' */
            /* Event: '<S90>:246' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);

            /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Ctf' */

            /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
            /* Event: '<S90>:116' */
            GearShiftMgm_ReadLookUpTables();

            /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S7>/Output' */
            /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S90>:231' */
            RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

            /* DataStoreWrite: '<S94>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

            /* DataStoreWrite: '<S94>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

            /* DataStoreWrite: '<S94>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

            /* DataStoreWrite: '<S94>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

            /* DataStoreWrite: '<S94>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_gx4w;

            /* DataStoreWrite: '<S94>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

            /* DataStoreWrite: '<S94>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

            /* DataStoreWrite: '<S94>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

            /* DataStoreWrite: '<S94>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

            /* DataStoreWrite: '<S94>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

            /* DataStoreWrite: '<S94>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

            /* DataStoreWrite: '<S94>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

            /* DataStoreWrite: '<S94>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

            /* DataStoreWrite: '<S94>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

            /* DataStoreWrite: '<S94>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

            /* DataStoreWrite: '<S94>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

            /* DataStoreWrite: '<S94>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

            /* DataStoreWrite: '<S94>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

            /* DataStoreWrite: '<S94>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

            /* DataStoreWrite: '<S94>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

            /* DataStoreWrite: '<S94>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

            /* DataStoreWrite: '<S94>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

            /* DataStoreWrite: '<S94>/Data Store Write24' */
            FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

            /* End of Outputs for SubSystem: '<S7>/Output' */
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Digital' */
        break;

       case GearShift_IN_QSHIFT_CTF_REQUEST:
        /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
         *  ActionPort: '<S7>/else'
         */
        /* During 'QSHIFT_CTF_REQUEST': '<S90>:181' */
        /* Transition: '<S90>:179' */
        tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;
        if ((ClutchSignal == 0) || (GearShiftWait > ((uint8_T)GEAR_SHIFT_MOVE)) ||
            (GearShiftMgm_B.QShiftCnt_lib2 >= GearShiftMgm_B.QsCtfTime_nkkv)) {
          /* Transition: '<S90>:182' */
          GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_CTF_REDUCED);
          GearShiftMgm_B.QShiftCnt_lib2 = GearShiftMgm_B.QsCtfTime_nkkv;
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Ctf' */
          /* Event: '<S90>:246' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);

          /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Ctf' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
            GearShift_IN_QSHIFT_CTF_REDUCED;
        } else {
          /* Transition: '<S90>:183' */
          if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 1)) ||
              ((GearDownSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 0) &&
               (GearShiftWait == ((uint8_T)GEAR_SHIFT_IDLE))) || (GearShiftWait >
               ((uint8_T)GEAR_SHIFT_RETURN))) {
            /* Transition: '<S90>:196' */
            /* Transition: '<S90>:201' */
            GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_DISABLE);
            GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 0U;
            GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 0U;
            tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

            /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
            /* Event: '<S90>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
              GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio);

            /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S7>/EnQs' */
            /* Event: '<S90>:55' */
            GearShiftMgm_EnQs();

            /* End of Outputs for SubSystem: '<S7>/EnQs' */

            /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
            /* Event: '<S90>:116' */
            GearShiftMgm_ReadLookUpTables();

            /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S7>/Output' */
            /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S90>:231' */
            RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

            /* DataStoreWrite: '<S94>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

            /* DataStoreWrite: '<S94>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

            /* DataStoreWrite: '<S94>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

            /* DataStoreWrite: '<S94>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

            /* DataStoreWrite: '<S94>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_gx4w;

            /* DataStoreWrite: '<S94>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

            /* DataStoreWrite: '<S94>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

            /* DataStoreWrite: '<S94>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

            /* DataStoreWrite: '<S94>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

            /* DataStoreWrite: '<S94>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

            /* DataStoreWrite: '<S94>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

            /* DataStoreWrite: '<S94>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

            /* DataStoreWrite: '<S94>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

            /* DataStoreWrite: '<S94>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

            /* DataStoreWrite: '<S94>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

            /* DataStoreWrite: '<S94>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

            /* DataStoreWrite: '<S94>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

            /* DataStoreWrite: '<S94>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

            /* DataStoreWrite: '<S94>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

            /* DataStoreWrite: '<S94>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

            /* DataStoreWrite: '<S94>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

            /* DataStoreWrite: '<S94>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

            /* DataStoreWrite: '<S94>/Data Store Write24' */
            FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

            /* End of Outputs for SubSystem: '<S7>/Output' */
            GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
              GearShiftMgm_IN_QSHIFT_DISABLE;
          } else {
            /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
            /* Transition: '<S90>:197' */
            /* Event: '<S90>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
              GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio);

            /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Ctf' */
            /* Event: '<S90>:246' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);

            /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Ctf' */

            /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
            /* Event: '<S90>:116' */
            GearShiftMgm_ReadLookUpTables();

            /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S7>/Output' */
            /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S90>:231' */
            RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

            /* DataStoreWrite: '<S94>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

            /* DataStoreWrite: '<S94>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

            /* DataStoreWrite: '<S94>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

            /* DataStoreWrite: '<S94>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

            /* DataStoreWrite: '<S94>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_gx4w;

            /* DataStoreWrite: '<S94>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

            /* DataStoreWrite: '<S94>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

            /* DataStoreWrite: '<S94>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

            /* DataStoreWrite: '<S94>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

            /* DataStoreWrite: '<S94>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

            /* DataStoreWrite: '<S94>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

            /* DataStoreWrite: '<S94>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

            /* DataStoreWrite: '<S94>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

            /* DataStoreWrite: '<S94>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

            /* DataStoreWrite: '<S94>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

            /* DataStoreWrite: '<S94>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

            /* DataStoreWrite: '<S94>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

            /* DataStoreWrite: '<S94>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

            /* DataStoreWrite: '<S94>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

            /* DataStoreWrite: '<S94>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

            /* DataStoreWrite: '<S94>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

            /* DataStoreWrite: '<S94>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

            /* DataStoreWrite: '<S94>/Data Store Write24' */
            FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

            /* End of Outputs for SubSystem: '<S7>/Output' */
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Digital' */
        break;

       case GearShiftMgm_IN_QSHIFT_CTF_WAIT:
        /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
         *  ActionPort: '<S7>/else'
         */
        /* During 'QSHIFT_CTF_WAIT': '<S90>:162' */
        /* Transition: '<S90>:189' */
        if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 1)) ||
            ((GearDownSignal == 0) && (GearShiftMgm_B.upDown_nqyh == 0) &&
             (GearShiftWait == ((uint8_T)GEAR_SHIFT_IDLE))) || (GearShiftWait >
             ((uint8_T)GEAR_SHIFT_RETURN))) {
          /* Transition: '<S90>:186' */
          /* Transition: '<S90>:242' */
          /* Transition: '<S90>:200' */
          /* Transition: '<S90>:201' */
          GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_DISABLE);
          GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 0U;
          GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 0U;
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S7>/EnQs' */
          /* Event: '<S90>:55' */
          GearShiftMgm_EnQs();

          /* End of Outputs for SubSystem: '<S7>/EnQs' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
            GearShiftMgm_IN_QSHIFT_DISABLE;
        } else {
          /* Transition: '<S90>:187' */
          tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S7>/Calc_Ratio' */
          /* Event: '<S90>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
            GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio);

          /* End of Outputs for SubSystem: '<S7>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S124>/Calc_KFilt_Ctf' */
          /* Event: '<S90>:246' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);

          /* End of Outputs for SubSystem: '<S124>/Calc_KFilt_Ctf' */

          /* Outputs for Function Call SubSystem: '<S7>/ReadLookUpTables' */
          /* Event: '<S90>:116' */
          GearShiftMgm_ReadLookUpTables();

          /* End of Outputs for SubSystem: '<S7>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S7>/Output' */
          /* DataStoreWrite: '<S94>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S90>:231' */
          RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

          /* DataStoreWrite: '<S94>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

          /* DataStoreWrite: '<S94>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

          /* DataStoreWrite: '<S94>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

          /* DataStoreWrite: '<S94>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

          /* DataStoreWrite: '<S94>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_gx4w;

          /* DataStoreWrite: '<S94>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

          /* DataStoreWrite: '<S94>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

          /* DataStoreWrite: '<S94>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

          /* DataStoreWrite: '<S94>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

          /* DataStoreWrite: '<S94>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

          /* DataStoreWrite: '<S94>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

          /* DataStoreWrite: '<S94>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

          /* DataStoreWrite: '<S94>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

          /* DataStoreWrite: '<S94>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

          /* DataStoreWrite: '<S94>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

          /* DataStoreWrite: '<S94>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

          /* DataStoreWrite: '<S94>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

          /* DataStoreWrite: '<S94>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

          /* DataStoreWrite: '<S94>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

          /* DataStoreWrite: '<S94>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

          /* DataStoreWrite: '<S94>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

          /* DataStoreWrite: '<S94>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

          /* DataStoreWrite: '<S94>/Data Store Write24' */
          FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

          /* End of Outputs for SubSystem: '<S7>/Output' */
        }

        /* End of Outputs for SubSystem: '<S3>/Digital' */
        break;

       default:
        /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
         *  ActionPort: '<S7>/else'
         */
        /* During 'QSHIFT_DISABLE': '<S90>:143' */
        /* Transition: '<S90>:147' */
        tmp = GearShiftMgm_B.QShiftCnt_lib2 + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_lib2 = (uint8_T)tmp;
        GearShiftMgm_B.GearPosQShift_l0lg = GearPos;
        GearShiftMgm_B.CmeDriverQShift_kcqy = CmeDriver;
        GearShiftMgm_B.RpmQShift_avy2 = Rpm;

        /* Chart: '<S7>/Chart' incorporates:
         *  SubSystem: '<S7>/Calc_Ratio'
         */
        /* Event: '<S90>:115' */
        GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_kcqy,
          GearShiftMgm_B.RpmQShift_avy2, CmeDriver, GasPosCC,
          &GearShiftMgm_B.Calc_Ratio);

        /* Chart: '<S7>/Chart' incorporates:
         *  SubSystem: '<S7>/EnQs'
         */
        /* Event: '<S90>:55' */
        GearShiftMgm_EnQs();

        /* End of Outputs for SubSystem: '<S3>/Digital' */
        if (FlgQSLow == 0) {
          /* Transition: '<S90>:149' */
          /* Transition: '<S90>:172' */
          if (GearShiftMgm_B.LogicalOperator3 != 0) {
            /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
             *  ActionPort: '<S7>/else'
             */
            /* Transition: '<S90>:175' */
            GearShiftMgm_B.upDown_nqyh = 0U;
            GearShiftMgm_B.QShiftCnt_lib2 = 0U;

            /* Chart: '<S7>/Chart' incorporates:
             *  SubSystem: '<S96>/fc_QsDnCtf_Calc'
             */
            /* Event: '<S90>:219' */
            GearShiftMgm_fc_QsDnCtf_Calc();
            tmp = GearShiftMgm_B.QsCtfTime_nkkv +
              GearShiftMgm_B.CmeQsIPeriod_gdc0;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_DWork.ctfPeriod_csmp = (uint8_T)tmp;

            /* End of Outputs for SubSystem: '<S3>/Digital' */
            /* Transition: '<S90>:178' */
            guard2 = true;
          } else {
            /* Transition: '<S90>:170' */
            if (GearShiftMgm_B.LogicalOperator2_fmjj != 0) {
              /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
               *  ActionPort: '<S7>/else'
               */
              /* Transition: '<S90>:173' */
              GearShiftMgm_B.upDown_nqyh = 1U;
              GearShiftMgm_B.QShiftCnt_lib2 = 0U;

              /* Chart: '<S7>/Chart' incorporates:
               *  SubSystem: '<S96>/fc_QsUpCtf_Calc'
               */
              /* Event: '<S90>:220' */
              GearShiftMgm_fc_QsUpCtf_Calc();
              tmp = GearShiftMgm_B.QsCtfTime_nkkv +
                GearShiftMgm_B.CmeQsIPeriod_gdc0;
              if (tmp > 255) {
                tmp = 255;
              }

              GearShiftMgm_DWork.ctfPeriod_csmp = (uint8_T)tmp;

              /* End of Outputs for SubSystem: '<S3>/Digital' */
              guard2 = true;
            } else {
              /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
               *  ActionPort: '<S7>/else'
               */
              /* Chart: '<S7>/Chart' incorporates:
               *  SubSystem: '<S124>/Calc_KFilt_Ctf'
               */
              /* Transition: '<S90>:194' */
              /* Event: '<S90>:246' */
              GearShiftMgm_Calc_KFilt_Ctf
                (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
                 GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
                 GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
                 GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);

              /* End of Outputs for SubSystem: '<S3>/Digital' */
              guard3 = true;
            }
          }
        } else {
          /* Transition: '<S90>:151' */
          if (GearShiftMgm_B.LogicalOperator3 != 0) {
            /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
             *  ActionPort: '<S7>/else'
             */
            /* Transition: '<S90>:153' */
            GearShiftMgm_B.upDown_nqyh = 0U;
            GearShiftMgm_B.QShiftCnt_lib2 = 0U;

            /* Chart: '<S7>/Chart' incorporates:
             *  SubSystem: '<S96>/fc_QsDnBlp_Calc'
             */
            /* Event: '<S90>:68' */
            GearShiftMgm_fc_QsDnBlp_Calc();

            /* End of Outputs for SubSystem: '<S3>/Digital' */
            /* Transition: '<S90>:158' */
            guard4 = true;
          } else {
            /* Transition: '<S90>:155' */
            if (GearShiftMgm_B.LogicalOperator2_fmjj != 0) {
              /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
               *  ActionPort: '<S7>/else'
               */
              /* Transition: '<S90>:157' */
              GearShiftMgm_B.upDown_nqyh = 1U;
              GearShiftMgm_B.QShiftCnt_lib2 = 0U;

              /* Chart: '<S7>/Chart' incorporates:
               *  SubSystem: '<S96>/fc_QsUpBlp_Calc'
               */
              /* Event: '<S90>:218' */
              GearShiftMgm_fc_QsUpBlp_Calc();

              /* End of Outputs for SubSystem: '<S3>/Digital' */
              guard4 = true;
            } else {
              /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
               *  ActionPort: '<S7>/else'
               */
              /* Chart: '<S7>/Chart' incorporates:
               *  SubSystem: '<S124>/Calc_KFilt_Blp'
               */
              /* Transition: '<S90>:193' */
              /* Event: '<S90>:247' */
              GearShiftMgm_Calc_KFilt_Blp
                (GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh,
                 GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
                 GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
                 GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Blp);

              /* End of Outputs for SubSystem: '<S3>/Digital' */
              guard3 = true;
            }
          }
        }
        break;
      }
    }
  } else {
    /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
     *  ActionPort: '<S6>/else'
     */
    /* DataTypeConversion: '<S21>/Conversion3' incorporates:
     *  Constant: '<S10>/BKFLGQSLOW_dim'
     */
    rtb_Conversion3 = (uint8_T)BKFLGQSLOW_dim;

    /* S-Function (LookUp_S16_U16): '<S21>/LookUp_S16_U16' incorporates:
     *  Constant: '<S10>/BKFLGQSLOW'
     *  Constant: '<S10>/VTFLGQSLOW'
     *  Inport: '<Root>/Rpm'
     */
    LookUp_S16_U16( &rtb_LookUp_S16_U16, &VTFLGQSLOW[0], Rpm, &BKFLGQSLOW[0],
                   rtb_Conversion3);

    /* RelationalOperator: '<S10>/Relational Operator' incorporates:
     *  DataStoreWrite: '<S10>/Data Store Write13'
     *  Inport: '<Root>/CmeDriver'
     */
    FlgQSLow = (uint8_T)(CmeDriver < rtb_LookUp_S16_U16);

    /* End of Outputs for SubSystem: '<S3>/Analog' */
    /* Gateway: GearShiftMgm/T10ms/Analog/Chart */
    /* During: GearShiftMgm/T10ms/Analog/Chart */
    if (GearShiftMgm_DWork.bitsForTID0.is_active_c1_GearShiftMgm == 0U) {
      /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
       *  ActionPort: '<S6>/else'
       */
      /* Entry: GearShiftMgm/T10ms/Analog/Chart */
      GearShiftMgm_DWork.bitsForTID0.is_active_c1_GearShiftMgm = 1U;

      /* Entry Internal: GearShiftMgm/T10ms/Analog/Chart */
      /* Transition: '<S9>:161' */
      GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
      GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
        GearShif_IN_QSHIFT_DISABLE_o1bn;

      /* End of Outputs for SubSystem: '<S3>/Analog' */
    } else {
      switch (GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm) {
       case GearShif_IN_QSHIFT_BLIP_REQUEST:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        GearShiftMg_QSHIFT_BLIP_REQUEST();

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;

       case GearShiftMg_IN_QSHIFT_BLIP_WAIT:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        GearShiftMgm_QSHIFT_BLIP_WAIT();

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;

       case GearShift_IN_QSHIFT_BLP_REDUCED:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        /* During 'QSHIFT_BLP_REDUCED': '<S9>:310' */
        /* Transition: '<S9>:314' */
        tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;
        if (GearShiftMgm_B.upDown != 0) {
          /* Transition: '<S9>:315' */
          GearShiftMgm_DWork.flgGearCL = FlgGearUpCL;
        } else {
          /* Transition: '<S9>:317' */
          GearShiftMgm_DWork.flgGearCL = FlgGearDnCL;
        }

        if ((ClutchSignal == 0) || (GearShiftMgm_DWork.flgGearCL != 0) ||
            (GearShiftMgm_B.QShiftCnt_oz3p >= GearShiftMgm_B.QsBlpToTime)) {
          /* Transition: '<S9>:319' */
          GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_BLIP_WAIT);
          GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
          GearShiftMgm_B.QShiftCnt_oz3p = GearShiftMgm_B.QsBlpToTime;
          tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
          /* Event: '<S9>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
            GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio_lg2t3);

          /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Blp' */
          /* Event: '<S9>:268' */
          GearShiftMgm_Calc_KFilt_Blp
            (GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMGEARSHIFT_dim_emnh,
             GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1,
             GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_hijm,
             GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Blp_jcgws);

          /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Blp' */

          /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
          /* Event: '<S9>:116' */
          GearShift_ReadLookUpTables_b1et();

          /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S6>/Output' */
          /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S9>:227' */
          RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

          /* DataStoreWrite: '<S13>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

          /* DataStoreWrite: '<S13>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

          /* DataStoreWrite: '<S13>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

          /* DataStoreWrite: '<S13>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

          /* DataStoreWrite: '<S13>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_h20l;

          /* DataStoreWrite: '<S13>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

          /* DataStoreWrite: '<S13>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

          /* DataStoreWrite: '<S13>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

          /* DataStoreWrite: '<S13>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime;

          /* DataStoreWrite: '<S13>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_crco;

          /* DataStoreWrite: '<S13>/Data Store Write24' */
          QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

          /* DataStoreWrite: '<S13>/Data Store Write25' */
          QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

          /* DataStoreWrite: '<S13>/Data Store Write26' */
          QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

          /* DataStoreWrite: '<S13>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime;

          /* DataStoreWrite: '<S13>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion;

          /* DataStoreWrite: '<S13>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

          /* DataStoreWrite: '<S13>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1;

          /* DataStoreWrite: '<S13>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

          /* DataStoreWrite: '<S13>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

          /* DataStoreWrite: '<S13>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

          /* DataStoreWrite: '<S13>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

          /* End of Outputs for SubSystem: '<S6>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
            GearShiftMg_IN_QSHIFT_BLIP_WAIT;
        } else {
          /* Transition: '<S9>:316' */
          if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
              ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) ||
              (FoTrgQS != 0)) {
            /* Transition: '<S9>:320' */
            /* Transition: '<S9>:294' */
            /* Transition: '<S9>:201' */
            GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
            GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
            GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
            tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

            /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
            /* Event: '<S9>:55' */
            GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs,
              DiagFlg02, (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal,
              FoGearUpSignal, GearPos, ClutchSignal, GearDownSignal,
              FoGearDownSignal, QuickShiftTypeCAN, Rpm,
              GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
              GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
              GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
              &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

            /* End of Outputs for SubSystem: '<S6>/EnQs' */

            /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
            GearShiftMgm_B.cluReset = 0U;

            /* Outputs for Function Call SubSystem: '<S6>/Output' */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/DiagFlg02'
             *  Inport: '<Root>/FlgEnQs'
             *  Inport: '<Root>/FoGearDownSignal'
             *  Inport: '<Root>/FoGearUpSignal'
             *  Inport: '<Root>/GasPosCC'
             *  Inport: '<Root>/GearPos'
             *  Inport: '<Root>/QuickShiftEnCAN'
             *  Inport: '<Root>/QuickShiftTypeCAN'
             *  Inport: '<Root>/Rpm'
             *  Inport: '<Root>/VtRec'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S6>/Output' */
            GearShiftMgm_B.cmeGain = 0U;
            GearShiftMgm_B.filtGain = 0U;
            GearShiftMgm_DWork.flgJustDbl = 0U;
            GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
              GearShif_IN_QSHIFT_DISABLE_o1bn;
          } else {
            /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
            /* Transition: '<S9>:318' */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
            /* Event: '<S9>:267' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

            /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

            /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S6>/Output' */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S6>/Output' */
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;

       case GearShiftM_IN_QSHIFT_CTF_DOUBLE:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        /* During 'QSHIFT_CTF_DOUBLE': '<S9>:331' */
        /* Transition: '<S9>:337' */
        tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;
        tmp = GearShiftMgm_DWork.qsCntTo + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_DWork.qsCntTo = (uint8_T)tmp;
        if ((ClutchSignal == 0) || (GearShiftMgm_DWork.flgGearCL != 0) ||
            (GearShiftMgm_B.QShiftCnt_oz3p >= GearShiftMgm_DWork.ctfPeriod)) {
          /* Transition: '<S9>:342' */
          GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_CTF_WAIT);
          GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
          GearShiftMgm_B.flgDbl = 0U;
          if (GearShiftMgm_B.QShiftCnt_oz3p >= GearShiftMgm_DWork.ctfPeriod) {
            /* Transition: '<S9>:360' */
            GearShiftMgm_B.filtGain = 1U;
          } else {
            /* Transition: '<S9>:361' */
          }

          /* Transition: '<S9>:358' */
          GearShiftMgm_B.QShiftCnt_oz3p = GearShiftMgm_DWork.ctfPeriod;
          tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
          /* Event: '<S9>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
            GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio_lg2t3);

          /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
          /* Event: '<S9>:267' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

          /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

          /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
          /* Event: '<S9>:116' */
          GearShift_ReadLookUpTables_b1et();

          /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S6>/Output' */
          /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S9>:227' */
          RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

          /* DataStoreWrite: '<S13>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

          /* DataStoreWrite: '<S13>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

          /* DataStoreWrite: '<S13>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

          /* DataStoreWrite: '<S13>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

          /* DataStoreWrite: '<S13>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_h20l;

          /* DataStoreWrite: '<S13>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

          /* DataStoreWrite: '<S13>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

          /* DataStoreWrite: '<S13>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

          /* DataStoreWrite: '<S13>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime;

          /* DataStoreWrite: '<S13>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_crco;

          /* DataStoreWrite: '<S13>/Data Store Write24' */
          QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

          /* DataStoreWrite: '<S13>/Data Store Write25' */
          QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

          /* DataStoreWrite: '<S13>/Data Store Write26' */
          QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

          /* DataStoreWrite: '<S13>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime;

          /* DataStoreWrite: '<S13>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion;

          /* DataStoreWrite: '<S13>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

          /* DataStoreWrite: '<S13>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1;

          /* DataStoreWrite: '<S13>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

          /* DataStoreWrite: '<S13>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

          /* DataStoreWrite: '<S13>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

          /* DataStoreWrite: '<S13>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

          /* End of Outputs for SubSystem: '<S6>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
            GearShi_IN_QSHIFT_CTF_WAIT_gbpu;
        } else {
          /* Transition: '<S9>:338' */
          if (GearShiftMgm_DWork.qsCntTo >= GearShiftMgm_B.QSCntDlbTime_a5p1) {
            /* Transition: '<S9>:365' */
            GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_CTF_REDUCED);
            GearShiftMgm_DWork.qsCntTo = 0U;
            GearShiftMgm_B.flgDbl = 0U;

            /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
            /* Event: '<S9>:267' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

            /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

            /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S6>/Output' */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S6>/Output' */
            GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
              Gear_IN_QSHIFT_CTF_REDUCED_iegi;
          } else {
            /* Transition: '<S9>:363' */
            if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
                ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) ||
                (FoTrgQS != 0)) {
              /* Transition: '<S9>:341' */
              /* Transition: '<S9>:200' */
              /* Transition: '<S9>:294' */
              /* Transition: '<S9>:201' */
              GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
              GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
              GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
              tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
              if (tmp > 255) {
                tmp = 255;
              }

              GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

              /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
              /* Event: '<S9>:115' */
              GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
                GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
                &GearShiftMgm_B.Calc_Ratio_lg2t3);

              /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

              /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
              /* Event: '<S9>:55' */
              GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs,
                DiagFlg02, (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal,
                FoGearUpSignal, GearPos, ClutchSignal, GearDownSignal,
                FoGearDownSignal, QuickShiftTypeCAN, Rpm,
                GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
                &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

              /* End of Outputs for SubSystem: '<S6>/EnQs' */

              /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
              /* Event: '<S9>:116' */
              GearShift_ReadLookUpTables_b1et();

              /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
              GearShiftMgm_B.cluReset = 0U;

              /* Outputs for Function Call SubSystem: '<S6>/Output' */
              /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
               *  Inport: '<Root>/CmeDriver'
               *  Inport: '<Root>/DiagFlg02'
               *  Inport: '<Root>/FlgEnQs'
               *  Inport: '<Root>/FoGearDownSignal'
               *  Inport: '<Root>/FoGearUpSignal'
               *  Inport: '<Root>/GasPosCC'
               *  Inport: '<Root>/GearPos'
               *  Inport: '<Root>/QuickShiftEnCAN'
               *  Inport: '<Root>/QuickShiftTypeCAN'
               *  Inport: '<Root>/Rpm'
               *  Inport: '<Root>/VtRec'
               */
              /* Event: '<S9>:227' */
              RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

              /* DataStoreWrite: '<S13>/Data Store Write17' */
              QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

              /* DataStoreWrite: '<S13>/Data Store Write3' */
              QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

              /* DataStoreWrite: '<S13>/Data Store Write4' */
              QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

              /* DataStoreWrite: '<S13>/Data Store Write5' */
              CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

              /* DataStoreWrite: '<S13>/Data Store Write6' */
              StQShift = GearShiftMgm_B.StQShift_h20l;

              /* DataStoreWrite: '<S13>/Data Store Write8' */
              GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

              /* DataStoreWrite: '<S13>/Data Store Write10' */
              CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

              /* DataStoreWrite: '<S13>/Data Store Write11' */
              CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

              /* DataStoreWrite: '<S13>/Data Store Write12' */
              QSBlpTime = GearShiftMgm_B.QsBlpTime;

              /* DataStoreWrite: '<S13>/Data Store Write13' */
              CmeQsI = GearShiftMgm_B.CmeQsI_crco;

              /* DataStoreWrite: '<S13>/Data Store Write24' */
              QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

              /* DataStoreWrite: '<S13>/Data Store Write25' */
              QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

              /* DataStoreWrite: '<S13>/Data Store Write26' */
              QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

              /* DataStoreWrite: '<S13>/Data Store Write9' */
              QSCtfTime = GearShiftMgm_B.QsCtfTime;

              /* DataStoreWrite: '<S13>/Data Store Write14' */
              CmeQsPFilt = GearShiftMgm_B.Conversion;

              /* DataStoreWrite: '<S13>/Data Store Write15' */
              QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

              /* DataStoreWrite: '<S13>/Data Store Write16' */
              CtfGearShift = GearShiftMgm_B.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write2' */
              CmeQsIFilt = GearShiftMgm_B.Switch1;

              /* DataStoreWrite: '<S13>/Data Store Write7' */
              QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write18' */
              QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write19' */
              QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

              /* DataStoreWrite: '<S13>/Data Store Write20' */
              FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write21' */
              QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

              /* DataStoreWrite: '<S13>/Data Store Write22' */
              FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

              /* DataStoreWrite: '<S13>/Data Store Write23' */
              FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

              /* End of Outputs for SubSystem: '<S6>/Output' */
              GearShiftMgm_B.cmeGain = 0U;
              GearShiftMgm_B.filtGain = 0U;
              GearShiftMgm_DWork.flgJustDbl = 0U;
              GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
                GearShif_IN_QSHIFT_DISABLE_o1bn;
            } else {
              /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
              /* Transition: '<S9>:335' */
              /* Event: '<S9>:115' */
              GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
                GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
                &GearShiftMgm_B.Calc_Ratio_lg2t3);

              /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

              /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
              /* Event: '<S9>:267' */
              GearShiftMgm_Calc_KFilt_Ctf
                (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
                 GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
                 GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
                 GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

              /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

              /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
              /* Event: '<S9>:116' */
              GearShift_ReadLookUpTables_b1et();

              /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

              /* Outputs for Function Call SubSystem: '<S6>/Output' */
              /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
               *  Inport: '<Root>/CmeDriver'
               *  Inport: '<Root>/GasPosCC'
               */
              /* Event: '<S9>:227' */
              RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

              /* DataStoreWrite: '<S13>/Data Store Write17' */
              QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

              /* DataStoreWrite: '<S13>/Data Store Write3' */
              QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

              /* DataStoreWrite: '<S13>/Data Store Write4' */
              QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

              /* DataStoreWrite: '<S13>/Data Store Write5' */
              CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

              /* DataStoreWrite: '<S13>/Data Store Write6' */
              StQShift = GearShiftMgm_B.StQShift_h20l;

              /* DataStoreWrite: '<S13>/Data Store Write8' */
              GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

              /* DataStoreWrite: '<S13>/Data Store Write10' */
              CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

              /* DataStoreWrite: '<S13>/Data Store Write11' */
              CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

              /* DataStoreWrite: '<S13>/Data Store Write12' */
              QSBlpTime = GearShiftMgm_B.QsBlpTime;

              /* DataStoreWrite: '<S13>/Data Store Write13' */
              CmeQsI = GearShiftMgm_B.CmeQsI_crco;

              /* DataStoreWrite: '<S13>/Data Store Write24' */
              QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

              /* DataStoreWrite: '<S13>/Data Store Write25' */
              QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

              /* DataStoreWrite: '<S13>/Data Store Write26' */
              QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

              /* DataStoreWrite: '<S13>/Data Store Write9' */
              QSCtfTime = GearShiftMgm_B.QsCtfTime;

              /* DataStoreWrite: '<S13>/Data Store Write14' */
              CmeQsPFilt = GearShiftMgm_B.Conversion;

              /* DataStoreWrite: '<S13>/Data Store Write15' */
              QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

              /* DataStoreWrite: '<S13>/Data Store Write16' */
              CtfGearShift = GearShiftMgm_B.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write2' */
              CmeQsIFilt = GearShiftMgm_B.Switch1;

              /* DataStoreWrite: '<S13>/Data Store Write7' */
              QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write18' */
              QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write19' */
              QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

              /* DataStoreWrite: '<S13>/Data Store Write20' */
              FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write21' */
              QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

              /* DataStoreWrite: '<S13>/Data Store Write22' */
              FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

              /* DataStoreWrite: '<S13>/Data Store Write23' */
              FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

              /* End of Outputs for SubSystem: '<S6>/Output' */
            }
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;

       case Gear_IN_QSHIFT_CTF_REDUCED_iegi:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        /* During 'QSHIFT_CTF_REDUCED': '<S9>:286' */
        /* Transition: '<S9>:290' */
        tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;
        tmp = GearShiftMgm_DWork.qsCntTo + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_DWork.qsCntTo = (uint8_T)tmp;
        if (GearShiftMgm_B.upDown != 0) {
          /* Transition: '<S9>:300' */
          GearShiftMgm_DWork.flgGearCL = FlgGearUpCL;
        } else {
          /* Transition: '<S9>:301' */
          GearShiftMgm_DWork.flgGearCL = FlgGearDnCL;
        }

        if ((ClutchSignal == 0) || (GearShiftMgm_DWork.flgGearCL != 0) ||
            (GearShiftMgm_B.QShiftCnt_oz3p >= GearShiftMgm_DWork.ctfPeriod)) {
          /* Transition: '<S9>:292' */
          GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_CTF_WAIT);
          GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
          if (GearShiftMgm_B.QShiftCnt_oz3p >= GearShiftMgm_DWork.ctfPeriod) {
            /* Transition: '<S9>:351' */
            GearShiftMgm_B.filtGain = 1U;
          } else {
            /* Transition: '<S9>:352' */
          }

          /* Transition: '<S9>:349' */
          /* Transition: '<S9>:346' */
          GearShiftMgm_B.QShiftCnt_oz3p = GearShiftMgm_DWork.ctfPeriod;
          tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

          /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
          /* Event: '<S9>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
            GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio_lg2t3);

          /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
          /* Event: '<S9>:267' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

          /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

          /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
          /* Event: '<S9>:116' */
          GearShift_ReadLookUpTables_b1et();

          /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S6>/Output' */
          /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S9>:227' */
          RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

          /* DataStoreWrite: '<S13>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

          /* DataStoreWrite: '<S13>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

          /* DataStoreWrite: '<S13>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

          /* DataStoreWrite: '<S13>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

          /* DataStoreWrite: '<S13>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_h20l;

          /* DataStoreWrite: '<S13>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

          /* DataStoreWrite: '<S13>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

          /* DataStoreWrite: '<S13>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

          /* DataStoreWrite: '<S13>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime;

          /* DataStoreWrite: '<S13>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_crco;

          /* DataStoreWrite: '<S13>/Data Store Write24' */
          QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

          /* DataStoreWrite: '<S13>/Data Store Write25' */
          QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

          /* DataStoreWrite: '<S13>/Data Store Write26' */
          QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

          /* DataStoreWrite: '<S13>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime;

          /* DataStoreWrite: '<S13>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion;

          /* DataStoreWrite: '<S13>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

          /* DataStoreWrite: '<S13>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1;

          /* DataStoreWrite: '<S13>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

          /* DataStoreWrite: '<S13>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

          /* DataStoreWrite: '<S13>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

          /* DataStoreWrite: '<S13>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

          /* End of Outputs for SubSystem: '<S6>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
            GearShi_IN_QSHIFT_CTF_WAIT_gbpu;
        } else {
          /* Transition: '<S9>:354' */
          if ((GearShiftMgm_DWork.qsCntTo >= GearShiftMgm_B.QSCntDlbToTime_dn1n)
              && (GearShiftMgm_DWork.flgJustDbl < NUMQSDBLCTF)) {
            /* Transition: '<S9>:355' */
            GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_CTF_DOUBLE);
            GearShiftMgm_DWork.qsCntTo = 0U;
            GearShiftMgm_B.flgDbl = 1U;
            tmp = GearShiftMgm_DWork.flgJustDbl + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_DWork.flgJustDbl = (uint8_T)tmp;

            /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
            /* Event: '<S9>:267' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

            /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

            /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S6>/Output' */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S6>/Output' */
            GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
              GearShiftM_IN_QSHIFT_CTF_DOUBLE;
          } else {
            /* Transition: '<S9>:291' */
            if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
                ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) ||
                (FoTrgQS != 0)) {
              /* Transition: '<S9>:295' */
              /* Transition: '<S9>:294' */
              /* Transition: '<S9>:201' */
              GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
              GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
              GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
              tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
              if (tmp > 255) {
                tmp = 255;
              }

              GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

              /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
              /* Event: '<S9>:115' */
              GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
                GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
                &GearShiftMgm_B.Calc_Ratio_lg2t3);

              /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

              /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
              /* Event: '<S9>:55' */
              GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs,
                DiagFlg02, (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal,
                FoGearUpSignal, GearPos, ClutchSignal, GearDownSignal,
                FoGearDownSignal, QuickShiftTypeCAN, Rpm,
                GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
                GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
                GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
                &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

              /* End of Outputs for SubSystem: '<S6>/EnQs' */

              /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
              /* Event: '<S9>:116' */
              GearShift_ReadLookUpTables_b1et();

              /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
              GearShiftMgm_B.cluReset = 0U;

              /* Outputs for Function Call SubSystem: '<S6>/Output' */
              /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
               *  Inport: '<Root>/CmeDriver'
               *  Inport: '<Root>/DiagFlg02'
               *  Inport: '<Root>/FlgEnQs'
               *  Inport: '<Root>/FoGearDownSignal'
               *  Inport: '<Root>/FoGearUpSignal'
               *  Inport: '<Root>/GasPosCC'
               *  Inport: '<Root>/GearPos'
               *  Inport: '<Root>/QuickShiftEnCAN'
               *  Inport: '<Root>/QuickShiftTypeCAN'
               *  Inport: '<Root>/Rpm'
               *  Inport: '<Root>/VtRec'
               */
              /* Event: '<S9>:227' */
              RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

              /* DataStoreWrite: '<S13>/Data Store Write17' */
              QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

              /* DataStoreWrite: '<S13>/Data Store Write3' */
              QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

              /* DataStoreWrite: '<S13>/Data Store Write4' */
              QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

              /* DataStoreWrite: '<S13>/Data Store Write5' */
              CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

              /* DataStoreWrite: '<S13>/Data Store Write6' */
              StQShift = GearShiftMgm_B.StQShift_h20l;

              /* DataStoreWrite: '<S13>/Data Store Write8' */
              GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

              /* DataStoreWrite: '<S13>/Data Store Write10' */
              CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

              /* DataStoreWrite: '<S13>/Data Store Write11' */
              CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

              /* DataStoreWrite: '<S13>/Data Store Write12' */
              QSBlpTime = GearShiftMgm_B.QsBlpTime;

              /* DataStoreWrite: '<S13>/Data Store Write13' */
              CmeQsI = GearShiftMgm_B.CmeQsI_crco;

              /* DataStoreWrite: '<S13>/Data Store Write24' */
              QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

              /* DataStoreWrite: '<S13>/Data Store Write25' */
              QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

              /* DataStoreWrite: '<S13>/Data Store Write26' */
              QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

              /* DataStoreWrite: '<S13>/Data Store Write9' */
              QSCtfTime = GearShiftMgm_B.QsCtfTime;

              /* DataStoreWrite: '<S13>/Data Store Write14' */
              CmeQsPFilt = GearShiftMgm_B.Conversion;

              /* DataStoreWrite: '<S13>/Data Store Write15' */
              QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

              /* DataStoreWrite: '<S13>/Data Store Write16' */
              CtfGearShift = GearShiftMgm_B.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write2' */
              CmeQsIFilt = GearShiftMgm_B.Switch1;

              /* DataStoreWrite: '<S13>/Data Store Write7' */
              QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write18' */
              QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write19' */
              QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

              /* DataStoreWrite: '<S13>/Data Store Write20' */
              FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write21' */
              QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

              /* DataStoreWrite: '<S13>/Data Store Write22' */
              FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

              /* DataStoreWrite: '<S13>/Data Store Write23' */
              FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

              /* End of Outputs for SubSystem: '<S6>/Output' */
              GearShiftMgm_B.cmeGain = 0U;
              GearShiftMgm_B.filtGain = 0U;
              GearShiftMgm_DWork.flgJustDbl = 0U;
              GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
                GearShif_IN_QSHIFT_DISABLE_o1bn;
            } else {
              /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
              /* Transition: '<S9>:287' */
              /* Event: '<S9>:115' */
              GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
                GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
                &GearShiftMgm_B.Calc_Ratio_lg2t3);

              /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

              /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
              /* Event: '<S9>:267' */
              GearShiftMgm_Calc_KFilt_Ctf
                (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
                 GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
                 GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
                 GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

              /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

              /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
              /* Event: '<S9>:116' */
              GearShift_ReadLookUpTables_b1et();

              /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

              /* Outputs for Function Call SubSystem: '<S6>/Output' */
              /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
               *  Inport: '<Root>/CmeDriver'
               *  Inport: '<Root>/GasPosCC'
               */
              /* Event: '<S9>:227' */
              RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

              /* DataStoreWrite: '<S13>/Data Store Write17' */
              QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

              /* DataStoreWrite: '<S13>/Data Store Write3' */
              QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

              /* DataStoreWrite: '<S13>/Data Store Write4' */
              QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

              /* DataStoreWrite: '<S13>/Data Store Write5' */
              CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

              /* DataStoreWrite: '<S13>/Data Store Write6' */
              StQShift = GearShiftMgm_B.StQShift_h20l;

              /* DataStoreWrite: '<S13>/Data Store Write8' */
              GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

              /* DataStoreWrite: '<S13>/Data Store Write10' */
              CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

              /* DataStoreWrite: '<S13>/Data Store Write11' */
              CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

              /* DataStoreWrite: '<S13>/Data Store Write12' */
              QSBlpTime = GearShiftMgm_B.QsBlpTime;

              /* DataStoreWrite: '<S13>/Data Store Write13' */
              CmeQsI = GearShiftMgm_B.CmeQsI_crco;

              /* DataStoreWrite: '<S13>/Data Store Write24' */
              QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

              /* DataStoreWrite: '<S13>/Data Store Write25' */
              QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

              /* DataStoreWrite: '<S13>/Data Store Write26' */
              QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

              /* DataStoreWrite: '<S13>/Data Store Write9' */
              QSCtfTime = GearShiftMgm_B.QsCtfTime;

              /* DataStoreWrite: '<S13>/Data Store Write14' */
              CmeQsPFilt = GearShiftMgm_B.Conversion;

              /* DataStoreWrite: '<S13>/Data Store Write15' */
              QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

              /* DataStoreWrite: '<S13>/Data Store Write16' */
              CtfGearShift = GearShiftMgm_B.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write2' */
              CmeQsIFilt = GearShiftMgm_B.Switch1;

              /* DataStoreWrite: '<S13>/Data Store Write7' */
              QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write18' */
              QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

              /* DataStoreWrite: '<S13>/Data Store Write19' */
              QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

              /* DataStoreWrite: '<S13>/Data Store Write20' */
              FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

              /* DataStoreWrite: '<S13>/Data Store Write21' */
              QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

              /* DataStoreWrite: '<S13>/Data Store Write22' */
              FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

              /* DataStoreWrite: '<S13>/Data Store Write23' */
              FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

              /* End of Outputs for SubSystem: '<S6>/Output' */
            }
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;

       case Gear_IN_QSHIFT_CTF_REQUEST_lgvf:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        /* During 'QSHIFT_CTF_REQUEST': '<S9>:181' */
        /* Transition: '<S9>:179' */
        tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;
        if ((ClutchSignal == 0) || (GearShiftMgm_B.QShiftCnt_oz3p >=
             GearShiftMgm_B.QsCtfTime)) {
          /* Transition: '<S9>:182' */
          GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_CTF_REDUCED);
          GearShiftMgm_B.QShiftCnt_oz3p = GearShiftMgm_B.QsCtfTime;
          GearShiftMgm_DWork.qsCntTo = 0U;

          /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
          /* Event: '<S9>:115' */
          GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
            GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
            &GearShiftMgm_B.Calc_Ratio_lg2t3);

          /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

          /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
          /* Event: '<S9>:267' */
          GearShiftMgm_Calc_KFilt_Ctf
            (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
             GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
             GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
             GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

          /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

          /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
          /* Event: '<S9>:116' */
          GearShift_ReadLookUpTables_b1et();

          /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

          /* Outputs for Function Call SubSystem: '<S6>/Output' */
          /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
           *  Inport: '<Root>/CmeDriver'
           *  Inport: '<Root>/GasPosCC'
           */
          /* Event: '<S9>:227' */
          RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

          /* DataStoreWrite: '<S13>/Data Store Write17' */
          QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

          /* DataStoreWrite: '<S13>/Data Store Write3' */
          QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

          /* DataStoreWrite: '<S13>/Data Store Write4' */
          QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

          /* DataStoreWrite: '<S13>/Data Store Write5' */
          CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

          /* DataStoreWrite: '<S13>/Data Store Write6' */
          StQShift = GearShiftMgm_B.StQShift_h20l;

          /* DataStoreWrite: '<S13>/Data Store Write8' */
          GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

          /* DataStoreWrite: '<S13>/Data Store Write10' */
          CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

          /* DataStoreWrite: '<S13>/Data Store Write11' */
          CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

          /* DataStoreWrite: '<S13>/Data Store Write12' */
          QSBlpTime = GearShiftMgm_B.QsBlpTime;

          /* DataStoreWrite: '<S13>/Data Store Write13' */
          CmeQsI = GearShiftMgm_B.CmeQsI_crco;

          /* DataStoreWrite: '<S13>/Data Store Write24' */
          QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

          /* DataStoreWrite: '<S13>/Data Store Write25' */
          QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

          /* DataStoreWrite: '<S13>/Data Store Write26' */
          QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

          /* DataStoreWrite: '<S13>/Data Store Write9' */
          QSCtfTime = GearShiftMgm_B.QsCtfTime;

          /* DataStoreWrite: '<S13>/Data Store Write14' */
          CmeQsPFilt = GearShiftMgm_B.Conversion;

          /* DataStoreWrite: '<S13>/Data Store Write15' */
          QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

          /* DataStoreWrite: '<S13>/Data Store Write16' */
          CtfGearShift = GearShiftMgm_B.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write2' */
          CmeQsIFilt = GearShiftMgm_B.Switch1;

          /* DataStoreWrite: '<S13>/Data Store Write7' */
          QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write18' */
          QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

          /* DataStoreWrite: '<S13>/Data Store Write19' */
          QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

          /* DataStoreWrite: '<S13>/Data Store Write20' */
          FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

          /* DataStoreWrite: '<S13>/Data Store Write21' */
          QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

          /* DataStoreWrite: '<S13>/Data Store Write22' */
          FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

          /* DataStoreWrite: '<S13>/Data Store Write23' */
          FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

          /* End of Outputs for SubSystem: '<S6>/Output' */
          GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
            Gear_IN_QSHIFT_CTF_REDUCED_iegi;
        } else {
          /* Transition: '<S9>:183' */
          if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
              ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) ||
              (FoTrgQS != 0)) {
            /* Transition: '<S9>:196' */
            /* Transition: '<S9>:201' */
            GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
            GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
            GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
            tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

            /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S6>/EnQs' */
            /* Event: '<S9>:55' */
            GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs,
              DiagFlg02, (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal,
              FoGearUpSignal, GearPos, ClutchSignal, GearDownSignal,
              FoGearDownSignal, QuickShiftTypeCAN, Rpm,
              GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
              GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
              GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
              &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

            /* End of Outputs for SubSystem: '<S6>/EnQs' */

            /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */
            GearShiftMgm_B.cluReset = 0U;

            /* Outputs for Function Call SubSystem: '<S6>/Output' */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/DiagFlg02'
             *  Inport: '<Root>/FlgEnQs'
             *  Inport: '<Root>/FoGearDownSignal'
             *  Inport: '<Root>/FoGearUpSignal'
             *  Inport: '<Root>/GasPosCC'
             *  Inport: '<Root>/GearPos'
             *  Inport: '<Root>/QuickShiftEnCAN'
             *  Inport: '<Root>/QuickShiftTypeCAN'
             *  Inport: '<Root>/Rpm'
             *  Inport: '<Root>/VtRec'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S6>/Output' */
            GearShiftMgm_B.cmeGain = 0U;
            GearShiftMgm_B.filtGain = 0U;
            GearShiftMgm_DWork.flgJustDbl = 0U;
            GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
              GearShif_IN_QSHIFT_DISABLE_o1bn;
          } else {
            /* Outputs for Function Call SubSystem: '<S6>/Calc_Ratio' */
            /* Transition: '<S9>:197' */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* End of Outputs for SubSystem: '<S6>/Calc_Ratio' */

            /* Outputs for Function Call SubSystem: '<S34>/Calc_KFilt_Ctf' */
            /* Event: '<S9>:267' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

            /* End of Outputs for SubSystem: '<S34>/Calc_KFilt_Ctf' */

            /* Outputs for Function Call SubSystem: '<S6>/ReadLookUpTables' */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* End of Outputs for SubSystem: '<S6>/ReadLookUpTables' */

            /* Outputs for Function Call SubSystem: '<S6>/Output' */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S6>/Output' */
          }
        }

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;

       case GearShi_IN_QSHIFT_CTF_WAIT_gbpu:
        /* During 'QSHIFT_CTF_WAIT': '<S9>:162' */
        /* Transition: '<S9>:271' */
        if (ClutchSignal == 0) {
          /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
           *  ActionPort: '<S6>/else'
           */
          /* Transition: '<S9>:273' */
          GearShiftMgm_B.cluReset = 1U;

          /* End of Outputs for SubSystem: '<S3>/Analog' */
          /* Transition: '<S9>:275' */
          /* Transition: '<S9>:277' */
          guard1 = true;
        } else {
          /* Transition: '<S9>:189' */
          if (((GearUpSignal == 0) && (GearShiftMgm_B.upDown == 1)) ||
              ((GearDownSignal == 0) && (GearShiftMgm_B.upDown == 0)) ||
              (FoTrgQS != 0)) {
            /* Transition: '<S9>:186' */
            guard1 = true;
          } else {
            /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
             *  ActionPort: '<S6>/else'
             */
            /* Transition: '<S9>:187' */
            tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

            /* Chart: '<S6>/Chart' incorporates:
             *  SubSystem: '<S6>/Calc_Ratio'
             */
            /* Event: '<S9>:115' */
            GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
              GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
              &GearShiftMgm_B.Calc_Ratio_lg2t3);

            /* Chart: '<S6>/Chart' incorporates:
             *  SubSystem: '<S34>/Calc_KFilt_Ctf'
             */
            /* Event: '<S9>:267' */
            GearShiftMgm_Calc_KFilt_Ctf
              (GearShiftMgm_B.Calc_Ratio_lg2t3.BKCMEGEARSHIFT_dim_ddf5,
               GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_S16_o1_ol1s,
               GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_ce1j,
               GearShiftMgm_B.upDown, &GearShiftMgm_B.Calc_KFilt_Ctf_jyzjp);

            /* Chart: '<S6>/Chart' incorporates:
             *  SubSystem: '<S6>/ReadLookUpTables'
             */
            /* Event: '<S9>:116' */
            GearShift_ReadLookUpTables_b1et();

            /* Chart: '<S6>/Chart' incorporates:
             *  SubSystem: '<S6>/Output'
             */
            /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
             *  Inport: '<Root>/CmeDriver'
             *  Inport: '<Root>/GasPosCC'
             */
            /* Event: '<S9>:227' */
            RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

            /* DataStoreWrite: '<S13>/Data Store Write17' */
            QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

            /* DataStoreWrite: '<S13>/Data Store Write3' */
            QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

            /* DataStoreWrite: '<S13>/Data Store Write4' */
            QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

            /* DataStoreWrite: '<S13>/Data Store Write5' */
            CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

            /* DataStoreWrite: '<S13>/Data Store Write6' */
            StQShift = GearShiftMgm_B.StQShift_h20l;

            /* DataStoreWrite: '<S13>/Data Store Write8' */
            GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

            /* DataStoreWrite: '<S13>/Data Store Write10' */
            CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

            /* DataStoreWrite: '<S13>/Data Store Write11' */
            CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

            /* DataStoreWrite: '<S13>/Data Store Write12' */
            QSBlpTime = GearShiftMgm_B.QsBlpTime;

            /* DataStoreWrite: '<S13>/Data Store Write13' */
            CmeQsI = GearShiftMgm_B.CmeQsI_crco;

            /* DataStoreWrite: '<S13>/Data Store Write24' */
            QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

            /* DataStoreWrite: '<S13>/Data Store Write25' */
            QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

            /* DataStoreWrite: '<S13>/Data Store Write26' */
            QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

            /* DataStoreWrite: '<S13>/Data Store Write9' */
            QSCtfTime = GearShiftMgm_B.QsCtfTime;

            /* DataStoreWrite: '<S13>/Data Store Write14' */
            CmeQsPFilt = GearShiftMgm_B.Conversion;

            /* DataStoreWrite: '<S13>/Data Store Write15' */
            QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

            /* DataStoreWrite: '<S13>/Data Store Write16' */
            CtfGearShift = GearShiftMgm_B.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write2' */
            CmeQsIFilt = GearShiftMgm_B.Switch1;

            /* DataStoreWrite: '<S13>/Data Store Write7' */
            QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write18' */
            QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

            /* DataStoreWrite: '<S13>/Data Store Write19' */
            QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

            /* DataStoreWrite: '<S13>/Data Store Write20' */
            FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

            /* DataStoreWrite: '<S13>/Data Store Write21' */
            QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

            /* DataStoreWrite: '<S13>/Data Store Write22' */
            FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

            /* DataStoreWrite: '<S13>/Data Store Write23' */
            FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;

            /* End of Outputs for SubSystem: '<S3>/Analog' */
          }
        }
        break;

       default:
        /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
         *  ActionPort: '<S6>/else'
         */
        GearShiftMgm_QSHIFT_DISABLE();

        /* End of Outputs for SubSystem: '<S3>/Analog' */
        break;
      }
    }
  }

  if (guard4) {
    /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
     *  ActionPort: '<S7>/else'
     */
    /* Transition: '<S90>:160' */
    GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_BLIP_REQUEST);
    GearShiftMgm_B.QuickGearShiftBlpOn_gcmm = 1U;

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S124>/Calc_KFilt_Blp'
     */
    /* Event: '<S90>:247' */
    GearShiftMgm_Calc_KFilt_Blp
      (GearShiftMgm_B.Calc_Ratio.BKRPMGEARSHIFT_dim_emnh,
       GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_U16_o1,
       GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_hijm,
       GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Blp);
    GearShiftMgm_B.reset_d5yl = 1U;

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S7>/ReadLookUpTables'
     */
    /* Event: '<S90>:116' */
    GearShiftMgm_ReadLookUpTables();
    GearShiftMgm_B.reset_d5yl = 0U;

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S7>/Output'
     */
    /* DataStoreWrite: '<S94>/Data Store Write1' */
    /* Event: '<S90>:231' */
    RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

    /* DataStoreWrite: '<S94>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

    /* DataStoreWrite: '<S94>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

    /* DataStoreWrite: '<S94>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

    /* DataStoreWrite: '<S94>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

    /* DataStoreWrite: '<S94>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_gx4w;

    /* DataStoreWrite: '<S94>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

    /* DataStoreWrite: '<S94>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

    /* DataStoreWrite: '<S94>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

    /* DataStoreWrite: '<S94>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

    /* DataStoreWrite: '<S94>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

    /* DataStoreWrite: '<S94>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

    /* DataStoreWrite: '<S94>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

    /* DataStoreWrite: '<S94>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

    /* DataStoreWrite: '<S94>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

    /* DataStoreWrite: '<S94>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

    /* DataStoreWrite: '<S94>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

    /* DataStoreWrite: '<S94>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

    /* DataStoreWrite: '<S94>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

    /* DataStoreWrite: '<S94>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

    /* DataStoreWrite: '<S94>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

    /* DataStoreWrite: '<S94>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

    /* DataStoreWrite: '<S94>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

    /* DataStoreWrite: '<S94>/Data Store Write24' */
    FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;
    GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
      GearShif_IN_QSHIFT_BLIP_REQUEST;

    /* End of Outputs for SubSystem: '<S3>/Digital' */
  }

  if (guard3) {
    /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
     *  ActionPort: '<S7>/else'
     */
    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S7>/ReadLookUpTables'
     */
    /* Transition: '<S90>:190' */
    /* Event: '<S90>:116' */
    GearShiftMgm_ReadLookUpTables();

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S7>/Output'
     */
    /* DataStoreWrite: '<S94>/Data Store Write1' */
    /* Event: '<S90>:231' */
    RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

    /* DataStoreWrite: '<S94>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

    /* DataStoreWrite: '<S94>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

    /* DataStoreWrite: '<S94>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

    /* DataStoreWrite: '<S94>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

    /* DataStoreWrite: '<S94>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_gx4w;

    /* DataStoreWrite: '<S94>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

    /* DataStoreWrite: '<S94>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

    /* DataStoreWrite: '<S94>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

    /* DataStoreWrite: '<S94>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

    /* DataStoreWrite: '<S94>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

    /* DataStoreWrite: '<S94>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

    /* DataStoreWrite: '<S94>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

    /* DataStoreWrite: '<S94>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

    /* DataStoreWrite: '<S94>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

    /* DataStoreWrite: '<S94>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

    /* DataStoreWrite: '<S94>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

    /* DataStoreWrite: '<S94>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

    /* DataStoreWrite: '<S94>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

    /* DataStoreWrite: '<S94>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

    /* DataStoreWrite: '<S94>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

    /* DataStoreWrite: '<S94>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

    /* DataStoreWrite: '<S94>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

    /* DataStoreWrite: '<S94>/Data Store Write24' */
    FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;

    /* End of Outputs for SubSystem: '<S3>/Digital' */
  }

  if (guard2) {
    /* Outputs for IfAction SubSystem: '<S3>/Digital' incorporates:
     *  ActionPort: '<S7>/else'
     */
    /* Transition: '<S90>:184' */
    GearShiftMgm_B.StQShift_gx4w = ((uint8_T)QSHIFT_CTF_REQUEST);
    GearShiftMgm_B.QuickGearShiftCtfOn_fgyi = 1U;

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S124>/Calc_KFilt_Ctf'
     */
    /* Event: '<S90>:246' */
    GearShiftMgm_Calc_KFilt_Ctf
      (GearShiftMgm_B.Calc_Ratio.BKCMEGEARSHIFT_dim_ddf5,
       GearShiftMgm_B.Calc_Ratio.PreLookUpIdSearch_S16_o1_ol1s,
       GearShiftMgm_B.Calc_Ratio.DataTypeConversion1_ce1j,
       GearShiftMgm_B.upDown_nqyh, &GearShiftMgm_B.Calc_KFilt_Ctf);
    GearShiftMgm_B.reset_d5yl = 1U;

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S7>/ReadLookUpTables'
     */
    /* Event: '<S90>:116' */
    GearShiftMgm_ReadLookUpTables();
    GearShiftMgm_B.reset_d5yl = 0U;

    /* Chart: '<S7>/Chart' incorporates:
     *  SubSystem: '<S7>/Output'
     */
    /* DataStoreWrite: '<S94>/Data Store Write1' */
    /* Event: '<S90>:231' */
    RpmQShift = GearShiftMgm_B.RpmQShift_avy2;

    /* DataStoreWrite: '<S94>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_lib2;

    /* DataStoreWrite: '<S94>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_gcmm;

    /* DataStoreWrite: '<S94>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_fgyi;

    /* DataStoreWrite: '<S94>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_kcqy;

    /* DataStoreWrite: '<S94>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_gx4w;

    /* DataStoreWrite: '<S94>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_l0lg;

    /* DataStoreWrite: '<S94>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_h0dh;

    /* DataStoreWrite: '<S94>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gdc0;

    /* DataStoreWrite: '<S94>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime_co1y;

    /* DataStoreWrite: '<S94>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_edjv;

    /* DataStoreWrite: '<S94>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime_nkkv;

    /* DataStoreWrite: '<S94>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion_egs1;

    /* DataStoreWrite: '<S94>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4_obph;

    /* DataStoreWrite: '<S94>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1_k5sx;

    /* DataStoreWrite: '<S94>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1_b2ua;

    /* DataStoreWrite: '<S94>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2_hkya;

    /* DataStoreWrite: '<S94>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.LogicalOperator2_fmjj;

    /* DataStoreWrite: '<S94>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.LogicalOperator3;

    /* DataStoreWrite: '<S94>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.LogicalOperator1_poxk;

    /* DataStoreWrite: '<S94>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.DIAG_EXHVALVPOS1;

    /* DataStoreWrite: '<S94>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.LogicalOperator1_dent;

    /* DataStoreWrite: '<S94>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.LogicalOperator;

    /* DataStoreWrite: '<S94>/Data Store Write24' */
    FlgEnQuickShiftN = GearShiftMgm_B.LogicalOperator4_dvla;
    GearShiftMgm_DWork.bitsForTID0.is_c2_GearShiftMgm =
      GearShift_IN_QSHIFT_CTF_REQUEST;

    /* End of Outputs for SubSystem: '<S3>/Digital' */
  }

  if (guard1) {
    /* Outputs for IfAction SubSystem: '<S3>/Analog' incorporates:
     *  ActionPort: '<S6>/else'
     */
    /* Transition: '<S9>:340' */
    /* Transition: '<S9>:200' */
    /* Transition: '<S9>:294' */
    /* Transition: '<S9>:201' */
    GearShiftMgm_B.StQShift_h20l = ((uint8_T)QSHIFT_DISABLE);
    GearShiftMgm_B.QuickGearShiftBlpOn_a3xq = 0U;
    GearShiftMgm_B.QuickGearShiftCtfOn_cwxy = 0U;
    tmp = GearShiftMgm_B.QShiftCnt_oz3p + 1;
    if (tmp > 255) {
      tmp = 255;
    }

    GearShiftMgm_B.QShiftCnt_oz3p = (uint8_T)tmp;

    /* Chart: '<S6>/Chart' incorporates:
     *  SubSystem: '<S6>/Calc_Ratio'
     */
    /* Event: '<S9>:115' */
    GearShiftMgm_Calc_Ratio(GearShiftMgm_B.CmeDriverQShift_g55o,
      GearShiftMgm_B.RpmQShift_jdme, CmeDriver, GasPosCC,
      &GearShiftMgm_B.Calc_Ratio_lg2t3);

    /* Chart: '<S6>/Chart' incorporates:
     *  SubSystem: '<S6>/EnQs'
     */
    /* Event: '<S9>:55' */
    GearShiftMgm_EnQs_lgkn(GearShiftMgm_B.QShiftCnt_oz3p, FlgEnQs, DiagFlg02,
      (&(VtRec[0])), QuickShiftEnCAN, GearUpSignal, FoGearUpSignal, GearPos,
      ClutchSignal, GearDownSignal, FoGearDownSignal, QuickShiftTypeCAN, Rpm,
      GearShiftMgm_B.Calc_Ratio_lg2t3.BKRPMQSGASPOS_dim_nyed,
      GearShiftMgm_B.Calc_Ratio_lg2t3.PreLookUpIdSearch_U16_o1_n2je,
      GearShiftMgm_B.Calc_Ratio_lg2t3.DataTypeConversion1_jci4,
      &GearShiftMgm_B.EnQs_lgkn, &GearShiftMgm_DWork.EnQs_lgkn);

    /* Chart: '<S6>/Chart' incorporates:
     *  SubSystem: '<S6>/ReadLookUpTables'
     */
    /* Event: '<S9>:116' */
    GearShift_ReadLookUpTables_b1et();
    GearShiftMgm_B.cluReset = 0U;

    /* Chart: '<S6>/Chart' incorporates:
     *  SubSystem: '<S6>/Output'
     */
    /* DataStoreWrite: '<S13>/Data Store Write1' incorporates:
     *  Inport: '<Root>/ClutchSignal'
     *  Inport: '<Root>/CmeDriver'
     *  Inport: '<Root>/DiagFlg02'
     *  Inport: '<Root>/FlgEnQs'
     *  Inport: '<Root>/FoGearDownSignal'
     *  Inport: '<Root>/FoGearUpSignal'
     *  Inport: '<Root>/GasPosCC'
     *  Inport: '<Root>/GearDownSignal'
     *  Inport: '<Root>/GearPos'
     *  Inport: '<Root>/GearUpSignal'
     *  Inport: '<Root>/QuickShiftEnCAN'
     *  Inport: '<Root>/QuickShiftTypeCAN'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/VtRec'
     */
    /* Event: '<S9>:227' */
    RpmQShift = GearShiftMgm_B.RpmQShift_jdme;

    /* DataStoreWrite: '<S13>/Data Store Write17' */
    QShiftCnt = GearShiftMgm_B.QShiftCnt_oz3p;

    /* DataStoreWrite: '<S13>/Data Store Write3' */
    QuickGearShiftBlpOn = GearShiftMgm_B.QuickGearShiftBlpOn_a3xq;

    /* DataStoreWrite: '<S13>/Data Store Write4' */
    QuickGearShiftCtfOn = GearShiftMgm_B.QuickGearShiftCtfOn_cwxy;

    /* DataStoreWrite: '<S13>/Data Store Write5' */
    CmeDriverQShift = GearShiftMgm_B.CmeDriverQShift_g55o;

    /* DataStoreWrite: '<S13>/Data Store Write6' */
    StQShift = GearShiftMgm_B.StQShift_h20l;

    /* DataStoreWrite: '<S13>/Data Store Write8' */
    GearPosQShift = GearShiftMgm_B.GearPosQShift_p4ss;

    /* DataStoreWrite: '<S13>/Data Store Write10' */
    CmeQsP = GearShiftMgm_B.CmeQsP_btqz;

    /* DataStoreWrite: '<S13>/Data Store Write11' */
    CmeQsIPeriod = GearShiftMgm_B.CmeQsIPeriod_gnn3;

    /* DataStoreWrite: '<S13>/Data Store Write12' */
    QSBlpTime = GearShiftMgm_B.QsBlpTime;

    /* DataStoreWrite: '<S13>/Data Store Write13' */
    CmeQsI = GearShiftMgm_B.CmeQsI_crco;

    /* DataStoreWrite: '<S13>/Data Store Write24' */
    QSBlpToTime = GearShiftMgm_B.QsBlpToTime;

    /* DataStoreWrite: '<S13>/Data Store Write25' */
    QSCntDlbTime = GearShiftMgm_B.QSCntDlbTime_a5p1;

    /* DataStoreWrite: '<S13>/Data Store Write26' */
    QSCntDlbToTime = GearShiftMgm_B.QSCntDlbToTime_dn1n;

    /* DataStoreWrite: '<S13>/Data Store Write9' */
    QSCtfTime = GearShiftMgm_B.QsCtfTime;

    /* DataStoreWrite: '<S13>/Data Store Write14' */
    CmeQsPFilt = GearShiftMgm_B.Conversion;

    /* DataStoreWrite: '<S13>/Data Store Write15' */
    QuickGearShiftBlp = GearShiftMgm_B.LogicalOperator4;

    /* DataStoreWrite: '<S13>/Data Store Write16' */
    CtfGearShift = GearShiftMgm_B.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write2' */
    CmeQsIFilt = GearShiftMgm_B.Switch1;

    /* DataStoreWrite: '<S13>/Data Store Write7' */
    QuickGearShiftCtf = GearShiftMgm_B.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write18' */
    QSGearUpSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2;

    /* DataStoreWrite: '<S13>/Data Store Write19' */
    QSGearDnSignal = GearShiftMgm_B.EnQs_lgkn.LogicalOperator8;

    /* DataStoreWrite: '<S13>/Data Store Write20' */
    FlgEnQuickShift = GearShiftMgm_B.EnQs_lgkn.LogicalOperator1;

    /* DataStoreWrite: '<S13>/Data Store Write21' */
    QuickShiftPresence = GearShiftMgm_B.EnQs_lgkn.DataTypeConversion;

    /* DataStoreWrite: '<S13>/Data Store Write22' */
    FlgEnQuickShiftDn = GearShiftMgm_B.EnQs_lgkn.LogicalOperator2_jub4;

    /* DataStoreWrite: '<S13>/Data Store Write23' */
    FlgEnQuickShiftUp = GearShiftMgm_B.EnQs_lgkn.LogicalOperator3;
    GearShiftMgm_B.cmeGain = 0U;
    GearShiftMgm_B.filtGain = 0U;
    GearShiftMgm_DWork.flgJustDbl = 0U;
    GearShiftMgm_DWork.bitsForTID0.is_c1_GearShiftMgm =
      GearShif_IN_QSHIFT_DISABLE_o1bn;

    /* End of Outputs for SubSystem: '<S3>/Analog' */
  }

  /* End of If: '<S3>/If' */
}

/* Output and update for function-call system: '<S1>/Init' */
void GearShiftMgm_Init(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Inport: '<Root>/FlgEnQs'
   */
  QuickShiftPresence = FlgEnQs;

  /* DataStoreWrite: '<S2>/Data Store Write10' incorporates:
   *  Constant: '<S2>/zero2'
   */
  CmeQsBlpPFiltHr = 0;

  /* DataStoreWrite: '<S2>/Data Store Write8' incorporates:
   *  Constant: '<S2>/zero2'
   */
  CmeQsCtfIFiltHr = 0;

  /* DataStoreWrite: '<S2>/Data Store Write9' incorporates:
   *  Constant: '<S2>/zero2'
   */
  CmeQsBlpIFiltHr = 0;

  /* DataStoreWrite: '<S2>/Data Store Write11' incorporates:
   *  Constant: '<S2>/one'
   */
  GearShiftCtfUpFOFK = 16384U;

  /* DataStoreWrite: '<S2>/Data Store Write14' incorporates:
   *  Constant: '<S2>/one'
   */
  GearShiftBlpDnFOFK = 16384U;

  /* DataStoreWrite: '<S2>/Data Store Write25' incorporates:
   *  Constant: '<S2>/one'
   */
  GearShiftBlpUpFOFK = 16384U;

  /* DataStoreWrite: '<S2>/Data Store Write3' incorporates:
   *  Constant: '<S2>/one'
   */
  GearShiftCtfDnFOFK = 16384U;

  /* DataStoreWrite: '<S2>/Data Store Write12' incorporates:
   *  Constant: '<S2>/zero'
   */
  QuickGearShiftCtf = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write13' incorporates:
   *  Constant: '<S2>/zero'
   */
  QuickGearShiftBlp = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write15' incorporates:
   *  Constant: '<S2>/zero'
   */
  FlgEnQuickShiftDn = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write16' incorporates:
   *  Constant: '<S2>/zero'
   */
  FlgEnQuickShiftUp = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write17' incorporates:
   *  Constant: '<S2>/zero'
   */
  FlgEnQuickShift = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write18' incorporates:
   *  Constant: '<S2>/zero'
   */
  QShiftCnt = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write20' incorporates:
   *  Constant: '<S2>/zero'
   */
  QSGearUpSignal = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write21' incorporates:
   *  Constant: '<S2>/zero'
   */
  QSBlpTime = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write22' incorporates:
   *  Constant: '<S2>/zero'
   */
  QSCtfPeriod = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write23' incorporates:
   *  Constant: '<S2>/zero'
   */
  QuickGearShiftCtfOn = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write24' incorporates:
   *  Constant: '<S2>/zero'
   */
  QuickGearShiftBlpOn = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write28' incorporates:
   *  Constant: '<S2>/zero'
   */
  QSGearDnSignal = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write29' incorporates:
   *  Constant: '<S2>/zero'
   */
  GearPosQShift = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write30' incorporates:
   *  Constant: '<S2>/zero'
   */
  QSCtfTime = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write32' incorporates:
   *  Constant: '<S2>/zero'
   */
  FlgEnQuickShiftN = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write4' incorporates:
   *  Constant: '<S2>/zero'
   */
  CtfGearShift = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Constant: '<S2>/zero1'
   */
  CmeQsI = 0;

  /* DataStoreWrite: '<S2>/Data Store Write27' incorporates:
   *  Constant: '<S2>/zero1'
   */
  CmeDriverQShift = 0;

  /* DataStoreWrite: '<S2>/Data Store Write5' incorporates:
   *  Constant: '<S2>/zero1'
   */
  CmeQsP = 0;

  /* DataStoreWrite: '<S2>/Data Store Write6' incorporates:
   *  Constant: '<S2>/zero1'
   */
  CmeQsPFilt = 0;

  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  Constant: '<S2>/zero1'
   */
  CmeQsIFilt = 0;

  /* DataStoreWrite: '<S2>/Data Store Write26' incorporates:
   *  Constant: '<S2>/zero3'
   */
  RpmQShift = 0U;

  /* Constant: '<S2>/ID_GEARSHIFT_MGM' */
  IDGearShiftMgm = ID_GEARSHIFT_MGM;
}

/* Model step function */
void GearShiftMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/GearShiftMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_GearShiftMgm_Init' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' incorporates:
   *  Inport: '<Root>/ev_NoSync'
   */
  if (((GearShiftMgm_U.ev_PowerOn > 0) &&
       (GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Init_Trig_ZCE[0] !=
        POS_ZCSIG)) || ((GearShiftMgm_U.ev_NoSync > 0) &&
                        (GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Init_Trig_ZCE
                         [1] != POS_ZCSIG))) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    GearShiftMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Init_Trig_ZCE[0] = (ZCSigState)
    (GearShiftMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */

  /* Inport: '<Root>/ev_NoSync' */
  GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Init_Trig_ZCE[1] = (ZCSigState)
    (GearShiftMgm_U.ev_NoSync > 0);

  /* End of Outputs for SubSystem: '<S1>/fc_GearShiftMgm_Init' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_GearShiftMgm_Calc' incorporates:
   *  TriggerPort: '<S4>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((GearShiftMgm_U.ev_T10ms > 0) &&
      (GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Calc_Trig_ZCE != POS_ZCSIG))
  {
    /* S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    GearShiftMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S4>/Function-Call Generator' */
  }

  GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Calc_Trig_ZCE = (ZCSigState)
    (GearShiftMgm_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/fc_GearShiftMgm_Calc' */

  /* End of Outputs for SubSystem: '<Root>/GearShiftMgm' */
}

/* Model initialize function */
void GearShiftMgm_initialize(void)
{
  GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Calc_Trig_ZCE = POS_ZCSIG;
  GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Init_Trig_ZCE[0] = POS_ZCSIG;
  GearShiftMgm_PrevZCSigState.fc_GearShiftMgm_Init_Trig_ZCE[1] = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/GearShiftMgm' */
void GearShiftMgm_NoSync(void)
{
  GearShiftMgm_Init();
}

#endif                                 // _BUILD_GEARSHIFTMGM_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
