#ifdef _BUILD_DIAGCANMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "diagcanmgm.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
ECUcodeStrucTagNoWrite     ECUcodeIDOPT =
{ 
    "800B5431   ",             /* 0x91 */
    "0000000NEMO01",           /* 0x96 */
    {0x00,0x00,0x00,0x00,0x00} /* 0x97 */
};
#elif (ENGINE_TYPE == MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
ECUcodeStrucTagNoWrite     ECUcodeIDOPT =
{ 
    "800B5431   ",
    "0000000NEMO01",
    {0x00,0x00,0x00,0x00,0x00}
};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
ECUcodeStrucTagNoWrite     ECUcodeIDOPT =
{ 
    "800B5431   ",
    "0000000NEMO01",
    {0x00,0x00,0x00,0x00,0x00}
};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
ECUcodeStrucTagNoWrite     ECUcodeIDOPT =
{ 
    "800B5431   ",
    "0000000NEMO01",
    {0x00,0x00,0x00,0x00,0x00}
};
#else
 #error KWP not implemented for this target
#endif



/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _BUILD_DIAGCANMGM_ */

