#ifdef _BUILD_DIAGCANMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "diagcanmgm.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
#if (ENGINE_TYPE == PI_250_1C_DBW) 
const ECUcodeStrucTagID2    ECUcodeID2 =
{
    "H0002400   ",
    {0},  
    {'0','0','0','0','0','0','0','0','0'},
    {0, 0},
    {' ',' ',' ',' ',' '},
    "                 "
};
#elif (ENGINE_TYPE == PI_250_1C_HYBRID)
const ECUcodeStrucTagID2    ECUcodeID2 =
{
    "H0002500   ",
    {0},
    {'0','0','0','0','0','0','0','0','0'},
    {0, 0},
    {' ',' ',' ',' ',' '},
    "                 "
};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
const ECUcodeStrucTagID2    ECUcodeID2 =
{
    "H0000704   ",
    {0},
    {'0','0','0','0','0','0','0','0','0'},
    {0, 0},
    {' ',' ',' ',' ',' '},
    "                 "
};
#elif (ENGINE_TYPE == MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
const ECUcodeStrucTagID2    ECUcodeID2 =
{
    "H0000704   ",
    {0},
    {'0','0','0','0','0','0','0','0','0'},
    {0, 0},
    {' ',' ',' ',' ',' '},
    "                 "
};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
const ECUcodeStrucTagID2    ECUcodeID2 =
{
    "H0000704   ",
    {0},
    {'0','0','0','0','0','0','0','0','0'},
    {0, 0},
    {' ',' ',' ',' ',' '},
    "                 "
};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
const ECUcodeStrucTagID2    ECUcodeID2 =
{
    "H0000704   ",
    {0},
    {'0','0','0','0','0','0','0','0','0'},
    {0, 0},
    {' ',' ',' ',' ',' '},
    "                 "
};

#else
#error KWP not implemented for this target
#endif

const ECUcodeEldor_T ECUcodeEldor =
{
    {"           "},        // Eldor ECU SW Number, 11 ASCII, 0xF0
};


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _BUILD_DIAGCANMGM_ */

