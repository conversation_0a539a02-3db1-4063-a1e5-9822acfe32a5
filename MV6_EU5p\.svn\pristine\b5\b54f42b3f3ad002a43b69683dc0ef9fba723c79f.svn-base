/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_INJ_

#include "typedefs.h"
#include "pio.h"
#include "inj.h"
#include "..\sys\include\ETPU_HostInterface.h"
#include "ETPU_VrsDefs.h"
#include "..\sys\auto\etpu_angle_clock_func_auto.h"
#include "etpu_util.h"
#include "ETPU_Shared.h"

#include "sys.h"

#include "..\sys\auto\etpu_PULSE_auto.h"
#include "etpu_util.h"

int16_t INJ_Config        (t_INJ_CHANNELS chan, t_polarity activeLevel)
{
    uint32_t ParameterBaseAddress;
    t_EdgePolarity polarity;
    uint32_t polarityTmp;
    int16_t returnCode = NO_ERROR;
    uint32_t i;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        polarity = (activeLevel == ACTIVE_HIGH_POL)?RISING_EDGE:FALLING_EDGE;
         
    //    ParameterBaseAddress = ETPU_ChannelInitRam(chan, PULSE_FUNC_NUM_PARMS);
        returnCode = ETPU_ChannelInitRam(chan, PULSE_FUNC_NUM_PARMS, &ParameterBaseAddress);

        if (returnCode != NO_ERROR)
        {
#ifdef CHECK_BIOS_FAULTS        
            BIOS_Faults |= (BIOS_FAILURE_INJ | BIOS_FAILURE_ETPU);
#endif /* CHECK_BIOS_FAULTS */
            returnCode = INJ_EXEC_NOT_OK;
        }
        else
        {
            /* Initialize channel 0 to manage crank signal */
            returnCode = ETPU_ChannelInit( chan, 
                                        (uint16_t)FS_ETPU_PRIORITY_LOW, 
                                        (uint16_t)ETPU_COUNT_MODE_CONT, 
                            (uint16_t)PULSE_FUNC_FUNCTION_NUMBER, 
                            (uint16_t)PULSE_FUNC_TABLE_SELECT, 
                                        ParameterBaseAddress);

            if(returnCode == NO_ERROR)
            {
                /* clear the handle */
                for (i=0;i<25;i++)
                {
                    write_handleVal(chan, i, 0);
                }

                
                if(polarity==RISING_EDGE)
                { 
                    polarityTmp = read_handleVal(chan, 3) | (POLARITY_MASK);
                    write_handleVal(chan, 8, 1);
                } 
                else
                {
                    polarityTmp = read_handleVal(chan, 3) & ~(POLARITY_MASK);
                    write_handleVal(chan, 8, 0);
                }
                write_handleVal(chan, 3, polarityTmp);
                returnCode = ETPU_set_hsr(chan,HSR_FORCE_INIT_PULSE_VAL);
            }
        }
    }
    return returnCode;
}

int16_t INJ_Enable        (t_INJ_CHANNELS chan, t_State enablingStatus)
{
    int32_t channelFlags;
    int32_t enablingFlag;
    int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        enablingFlag = (enablingStatus == ENABLE)?EXCEPTIONS_ENABLED:0;
        if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
        {
            returnCode = INJ_EXEC_NOT_OK;
        }
        else
        {

            if(enablingStatus == DISABLE)
            {
                returnCode = ETPU_set_hsr(chan,HSR_FORCE_CLOSE_PULSE_VAL);
            }
            else
            {

                channelFlags = read_handleVal(chan, 3);

                write_handleVal(chan, 3, (channelFlags | enablingFlag));

                returnCode = ETPU_set_hsr(chan,HSR_INIT_PULSE_VAL);
            }
        }
    }
    return returnCode;
}


int16_t INJ_Set   (t_INJ_CHANNELS chan, 
                           uint32_t       *start, 
                           uint32_t       *stop, 
                           uint8_t        numOfInj,
                           uint32_t       saturation, 
                           t_CompareMode  saturationMode,
                           t_PulseMode    pulseMode,
                           t_PriorityMode priority)
{
    uint32_t status;
    int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
        {
            returnCode = INJ_EXEC_NOT_OK;
        }
        else
        {
            status = read_handleVal(chan, 0);
            if (status != 0)
            {
                returnCode = IGN_EXEC_NOT_OK;
            }
            else
            {

                write_handleVal(chan, 7, numOfInj);
                write_handleVal(chan, 19, 1);  //not strict correction

                switch(pulseMode)
                {
                    case ANGLE_ANGLE:
                        write_handleVal(chan, 4, 1);
                        write_handleVal(chan, 5, 1);

                        write_handleVal(chan, 9, saturation); // time

                        write_handleVal(chan, 10, start[0]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 11, start[1]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 12, start[2]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 13, stop[0]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 14, stop[1]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 15, stop[2]/TCR2_TICK_PRESCALER);
                    break;

                    case ANGLE_TIME:
                        write_handleVal(chan, 4, 1);
                        write_handleVal(chan, 5, 0);

                        write_handleVal(chan, 9, saturation/TCR2_TICK_PRESCALER); // angle

                        write_handleVal(chan, 10, start[0]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 11, start[1]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 12, start[2]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 13, stop[0]);
                        write_handleVal(chan, 14, stop[1]);
                        write_handleVal(chan, 15, stop[2]);
                        break;

                    case TIME_ANGLE:
                        write_handleVal(chan, 4, 0);
                        write_handleVal(chan, 5, 1);

                        write_handleVal(chan, 9, saturation); // time

                        write_handleVal(chan, 10, start[0]);
                        write_handleVal(chan, 11, start[1]);
                        write_handleVal(chan, 12, start[2]);
                        write_handleVal(chan, 13, stop[0]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 14, stop[1]/TCR2_TICK_PRESCALER);
                        write_handleVal(chan, 15, stop[2]/TCR2_TICK_PRESCALER);
                    break;

                    case TIME_TIME:
                        write_handleVal(chan, 4, 0);
                        write_handleVal(chan, 5, 0);

                        write_handleVal(chan, 9, saturation/TCR2_TICK_PRESCALER); // angle

                        write_handleVal(chan, 10, start[0]);
                        write_handleVal(chan, 11, start[1]);
                        write_handleVal(chan, 12, start[2]);
                        write_handleVal(chan, 13, etpu_subtime(stop[0], start[0]));
                        write_handleVal(chan, 14, etpu_subtime(stop[1], start[1]));
                        write_handleVal(chan, 15, etpu_subtime(stop[2], start[2]));
                    break;

                    default:
                        returnCode = INJ_INCORRECT_PULSE_MODE;
                    break;
                }
            }
        }
    }
    return returnCode;
}

int16_t INJ_Correct       (t_INJ_CHANNELS chan, 
                          uint32_t       *start, 
                          uint32_t       *stop, 
                          uint8_t        numOfInj)
{
   
    int32_t channelFlags;
    uint16_t pulseMode;
    uint16_t openMode;
    uint16_t closeMode;
    int16_t returnCode = NO_ERROR;
    
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
        {
           returnCode = INJ_EXEC_NOT_OK;
        }
        else
        {
            channelFlags = read_handleVal(chan, 3);
            if (!(channelFlags & INIT_STATUS_MASK))
            {
                returnCode = INJ_EXEC_NOT_OK;
            }
            else
            {
                if (read_handleVal(chan, 7) != numOfInj)
                {
                   returnCode = INJ_EXEC_NOT_OK;
                }
                else
                {
                    openMode = read_handleVal(chan, 4);
                    closeMode = read_handleVal(chan, 5);

                    if (openMode == 0) // time open mode
                    {
                        if (closeMode == 0) // time close mode
                        {
                            pulseMode = TIME_TIME;
                        }
                        else // time close mode
                        {
                            pulseMode = TIME_ANGLE;
                        }
                    }
                    else // time open mode
                    {
                        if (closeMode == 0) // time close mode
                        {
                            pulseMode = ANGLE_TIME;
                        }
                        else // time close mode
                        {
                            pulseMode = ANGLE_ANGLE;
                        }
                    }

                    switch(pulseMode)
                    {
                        /* The only case that works */
                        case ANGLE_ANGLE:
                            if(numOfInj==1)
                            {
                                write_pulseStartAdjVal(chan, 0, start[0]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 0, stop[0]/TCR2_TICK_PRESCALER); 
                            }
                            else if(numOfInj==2)
                            {
                                write_pulseStartAdjVal(chan, 1, start[1]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 1, stop[1]/TCR2_TICK_PRESCALER); 
                                write_pulseStartAdjVal(chan, 0, start[0]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 0, stop[0]/TCR2_TICK_PRESCALER); 
                            }
                            else if(numOfInj==3)
                            {
                                write_pulseStartAdjVal(chan, 2, start[2]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 2, stop[2]/TCR2_TICK_PRESCALER); 

                                write_pulseStartAdjVal(chan, 1, start[1]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 1, stop[1]/TCR2_TICK_PRESCALER); 

                                write_pulseStartAdjVal(chan, 0, start[0]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 0, stop[0]/TCR2_TICK_PRESCALER); 
                            }
                            else 
                            {
                            }
                            /*switch(numOfInj) App misra 15.2*/
                            
                        break;        
                        case ANGLE_TIME:
                            if(numOfInj==1)
                            {
                                write_pulseStartAdjVal(chan, 0, start[0]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 0, stop[0]); 
                            }
                            else if(numOfInj==2)
                            {
                                write_pulseStartAdjVal(chan, 1, start[1]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 1, stop[1]); 
                                write_pulseStartAdjVal(chan, 0, start[0]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 0, stop[0]); 
                            }
                            else if(numOfInj==3)
                            {
                                write_pulseStartAdjVal(chan, 2, start[2]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 2, stop[2]); 

                                write_pulseStartAdjVal(chan, 1, start[1]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 1, stop[1]); 

                                write_pulseStartAdjVal(chan, 0, start[0]/TCR2_TICK_PRESCALER);  
                                write_pulseStopAdjVal (chan, 0, stop[0]); 
                            }
                            else
                            {
                            }

                            /*switch(numOfInj) App misra 15.2*/
                            
                        break;
                        case TIME_TIME:
                            if(numOfInj==1)
                            {
                                write_pulseStartAdjVal(chan, 0, start[0]);  
                                write_pulseStopAdjVal (chan, 0, stop[0]-start[0]); 
                            }
                            else if(numOfInj==2)
                            {
                                write_pulseStartAdjVal(chan, 1, start[1]);  
                                write_pulseStopAdjVal (chan, 1, stop[1]-start[1]); 
                                write_pulseStartAdjVal(chan, 0, start[0]);  
                                write_pulseStopAdjVal (chan, 0, stop[0]-start[0]); 
                            }
                            else if(numOfInj==3)
                            {
                                write_pulseStartAdjVal(chan, 2, start[2]);  
                                write_pulseStopAdjVal (chan, 2, stop[2]-start[2]); 

                                write_pulseStartAdjVal(chan, 1, start[1]);  
                                write_pulseStopAdjVal (chan, 1, stop[1]-start[1]); 

                                write_pulseStartAdjVal(chan, 0, start[0]);  
                                write_pulseStopAdjVal (chan, 0, stop[0]-start[0]); 
                            }
                            else 
                            {
                            }
                            /*switch(numOfInj)  App misra 15.2*/
                            
                        break;
                        case TIME_ANGLE:
                            if(numOfInj==1)
                            {
                                write_pulseStartAdjVal(chan, 0, start[0]);  
                                write_pulseStopAdjVal (chan, 0, stop[0] / TCR2_TICK_PRESCALER); 
                            }
                            else if(numOfInj==2)
                            {
                                write_pulseStartAdjVal(chan, 1, start[1]);  
                                write_pulseStopAdjVal (chan, 1, stop[1] / TCR2_TICK_PRESCALER); 
                                write_pulseStartAdjVal(chan, 0, start[0]);  
                                write_pulseStopAdjVal (chan, 0, stop[0] / TCR2_TICK_PRESCALER); 
                            }
                            else if(numOfInj==3)
                            {
                                write_pulseStartAdjVal(chan, 2, start[2]);  
                                write_pulseStopAdjVal (chan, 2, stop[2] / TCR2_TICK_PRESCALER); 

                                write_pulseStartAdjVal(chan, 1, start[1]);  
                                write_pulseStopAdjVal (chan, 1, stop[1] / TCR2_TICK_PRESCALER); 

                                write_pulseStartAdjVal(chan, 0, start[0]);  
                                write_pulseStopAdjVal (chan, 0, stop[0] / TCR2_TICK_PRESCALER); 
                            }
                            else 
                            {
                            }
                            /*switch(numOfInj)  App misra 15.2*/
                           
                        break;
                        default:
                          returnCode =  INJ_INCORRECT_PULSE_MODE; 
                        break;
                    }
                    if (returnCode == NO_ERROR)
                    {
                        returnCode =  ETPU_set_hsr(chan,HSR_ADJUST_PULSE_VAL);
                    }
                }
            }
        }
    }
    return returnCode;
    
}

int16_t INJ_EnableEx  (t_INJ_CHANNELS chan,
                       TaskType       taskID, 
                       t_EnableFlags  enablingFlags)
{
    int32_t channelFlags;   
    int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
        {
            returnCode = INJ_NO_EX_ENABLED;
        }
        else
        {
        
            channelFlags = read_handleVal(chan, 3) & 0x00FFFFFF;

            ETPU_SetInterruptHandlerUC(chan, taskID);

            if (enablingFlags.open)
            {
                channelFlags |= OPEN_INJ_EXCEPTION;
                channelFlags |= EXCEPTIONS_ENABLED;
            }

            if (enablingFlags.close)
            { 
                channelFlags |= CLOSE_INJ_EXCEPTION; 
                channelFlags |= EXCEPTIONS_ENABLED; 
            }

            write_handleVal(chan, 3, channelFlags);

            ETPU_EnableInterruptUC(chan);
        }
    }
    return returnCode;
}

int16_t INJ_GetCurrTransType(t_INJ_CHANNELS chan, uint32_t *type)
{
    int16_t errorCode = NO_ERROR;
    volatile uint32_t exType;

    if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
    {
        errorCode = INJ_EXEC_NOT_OK;
    }
    else
    {

        exType = 0x00FFFFFF & read_handleVal(chan, 3);

        write_handleVal(chan, 3, (exType & (~(OPEN_INJ_EXCEPTION | CLOSE_INJ_EXCEPTION))));

        if(exType & OPEN_INJ_EXCEPTION) 
        {
            *type = OPEN_INJ_EXCEPTION;
        }
        else if(exType & CLOSE_INJ_EXCEPTION) 
        {
            *type = CLOSE_INJ_EXCEPTION;
        } 
        else
        {
            errorCode = INJ_EXEC_NOT_OK;
            *type = 0;
        }
    }
    return (errorCode); 
}

int16_t INJ_GetCurrTransTime(t_INJ_CHANNELS chan, uint32_t *time)
{
    int16_t errorCode = NO_ERROR;
    
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        *time = 0x00ffffff & read_handleVal(chan, 16);
    }
    return (errorCode); 
}

int16_t INJ_GetCurrTransAngle(t_INJ_CHANNELS chan, uint32_t *angle)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        *angle = 0x00ffffff & read_handleVal(chan, 17);
        *angle *= TCR2_TICK_PRESCALER;
    }
    return (errorCode); 
}


int16_t INJ_GetStatus(t_INJ_CHANNELS chan, uint16_t *status)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {

        if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
        {
            errorCode = INJ_EXEC_NOT_OK;
        }
        else
        {

            if (0x00FFFFFF & (read_handleVal(chan, 3) & INIT_STATUS_MASK)) // ap: remove 0x00FFFFFF
            {
                *status = INJ_CHANNEL_LOCKED;
            }
            else
            {
                *status = INJ_CHANNEL_NOT_LOCKED;
            }
        }

    }
    return (errorCode); 
}

#endif // _BUILD_INJ_
