/* Precompiler defines for SCI module */
/*************************************************************
     Peripheral defines 
 *************************************************************/

/****** eSCI CHANNELS CONFIGURED ******/

#define SCI_CH_A        0 
#define SCI_CH_B        1 

#define SCI_CH_A_ENABLED   1
#define SCI_CH_B_ENABLED   0

#define LIN_CH_A_ENABLED   0
#define LIN_CH_B_ENABLED   1


/****** SCI STATIC CONFIGURATION PARAMETERS  ******/

#define SCI_M_8BIT 0x00
#define SCI_M_9BIT 0x01

#define SCI_PE_DISABLE     0x00
#define SCI_PE_ENABLE      0x01

#define SCI_EVEN_PARITY    0x00
#define SCI_ODD_PARITY	   0x01


/****************************************/
#define TXDA        (89)           /* GPIO89 */
#define RXDA        (90)           /* GPIO90 */
#define TXDB_PCSD1  (91)           /* TXDB_PCSD1_GPIO91 */
#define RXDB_PCSD5  (92)           /* GPIO92 */
/****************************************/

/* The user can use only one of this standard baud rate configuration */
/* to initialize the SCIX_BAUD_RATE_CONFIG.                           */
#define SCI_300_BR         300
#define SCI_600_BR         600
#define SCI_1200_BR       1200
#define SCI_2400_BR       2400
#define SCI_4800_BR       4800
#define SCI_9600_BR       9600
#define SCI_10400_BR     10400
#define SCI_19200_BR     19200
#define SCI_38400_BR     38400
#define SCI_57600_BR     57600
#define SCI_115200_BR   115200

/* LIN definitions */
#define LIN_2400_BR         SCI_2400_BR       
#define LIN_9600_BR         SCI_9600_BR       
#define LIN_19200_BR        SCI_19200_BR     

/****** GENERAL SCI CONFIGURATION PARAMETERS ******/

/*---------------------- SCIA --------------------*/
#define SCIA_BAUD_RATE_CONFIG   SCI_10400_BR
#define SCIA_BIT_NUMBER		    SCI_M_8BIT
#define SCIA_PARITY			    SCI_PE_DISABLE 
#define SCIA_PARITY_TYPE        SCI_ODD_PARITY
/*------------------------------------------------*/

/*---------------------- SCIB --------------------*/
#define SCIB_BAUD_RATE_CONFIG   SCI_9600_BR
#define SCIB_BIT_NUMBER		    SCI_M_8BIT
#define SCIB_PARITY			    SCI_PE_DISABLE
#define SCIB_PARITY_TYPE        SCI_ODD_PARITY
/*------------------------------------------------*/

#define SCILIN_BAUD_RATE_CONFIG SCI_19200_BR

/***************************************************/


#if   (SCIA_BAUD_RATE_CONFIG == SCI_300_BR)  

   #define SCIA_BAUD_RATE       SCI_300_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_600_BR) 

   #define SCIA_BAUD_RATE       SCI_600_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_1200_BR) 

   #define SCIA_BAUD_RATE       SCI_1200_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_2400_BR) 

   #define SCIA_BAUD_RATE       SCI_2400_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_4800_BR) 

   #define SCIA_BAUD_RATE       SCI_4800_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_9600_BR) 

   #define SCIA_BAUD_RATE       SCI_9600_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_10400_BR) 

   #define SCIA_BAUD_RATE       SCI_10400_BR     

#elif (SCIA_BAUD_RATE_CONFIG == SCI_19200_BR) 

   #define SCIA_BAUD_RATE       SCI_19200_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_38400_BR) 

   #define SCIA_BAUD_RATE       SCI_38400_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_57600_BR) 

   #define SCIA_BAUD_RATE       SCI_57600_BR     
   
#elif (SCIA_BAUD_RATE_CONFIG == SCI_115200_BR) 

   #define SCIA_BAUD_RATE       SCI_115200_BR

#else
   #error       No standard Baud Rate set for SCI_A! 
#endif

#if   (SCIB_BAUD_RATE_CONFIG == SCI_300_BR)  

   #define SCIB_BAUD_RATE       SCI_300_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_600_BR) 

   #define SCIB_BAUD_RATE       SCI_600_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_1200_BR) 

   #define SCIB_BAUD_RATE       SCI_1200_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_2400_BR) 

   #define SCIB_BAUD_RATE       SCI_2400_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_4800_BR) 

   #define SCIB_BAUD_RATE       SCI_4800_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_9600_BR) 

   #define SCIB_BAUD_RATE       SCI_9600_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_19200_BR) 

   #define SCIB_BAUD_RATE       SCI_19200_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_38400_BR) 

   #define SCIB_BAUD_RATE       SCI_38400_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_57600_BR) 

   #define SCIB_BAUD_RATE       SCI_57600_BR     
   
#elif (SCIB_BAUD_RATE_CONFIG == SCI_115200_BR) 

   #define SCIB_BAUD_RATE       SCI_115200_BR

#else
   #error       No standard Baud Rate set for SCI_B! 
#endif

#if   (SCILIN_BAUD_RATE_CONFIG == SCI_2400_BR)  

   #define SCILIN_BAUD_RATE       SCI_2400_BR     
   
#elif (SCILIN_BAUD_RATE_CONFIG == SCI_9600_BR) 

   #define SCILIN_BAUD_RATE       SCI_9600_BR     
   
#elif (SCILIN_BAUD_RATE_CONFIG == SCI_19200_BR) 

   #define SCILIN_BAUD_RATE       SCI_19200_BR     
   
#else
   #error       No standard Baud Rate set for LIN! 
#endif


#define SCIB_SBR_BAUD_RATE(baud)  (uint32_t)( ( ((FSYS * (uint32_t)1000000)>>4) + (uint32_t)(baud>>1)) / ((uint32_t)(baud)) )
#define SCIA_SBR_BAUD_RATE(baud)  (uint32_t)( ( ((FSYS * (uint32_t)1000000)>>4) + (uint32_t)(baud>>1)) / ((uint32_t)(baud)) )

/*#define SCIB_SBR_BAUD_RATE 	  (uint16_t)( ( ((FSYS * (uint32_t)1000000)>>4) + (uint32_t)(SCIB_BAUD_RATE>>1)) / ((uint32_t)(SCIB_BAUD_RATE)) ) */
/*#define SCIA_SBR_BAUD_RATE 	  (uint16_t)( ( ((FSYS * (uint32_t)1000000)>>4) + (uint32_t)(SCIA_BAUD_RATE>>1)) / ((uint32_t)(SCIA_BAUD_RATE)) ) */



#if ( (SCIA_PARITY == SCI_PE_DISABLE) && (SCIA_BIT_NUMBER == SCI_M_8BIT))    

    #define  SCIA_RX_DATA_MASK  0x00ff                    /* 8-bit, no parity */

#elif ( (SCIA_PARITY == SCI_PE_DISABLE) && (SCIA_BIT_NUMBER == SCI_M_9BIT)) 

    #define  SCIA_RX_DATA_MASK  0x01ff                    /* 9-bit, no parity */

#elif ( (SCIA_PARITY == SCI_PE_ENABLE) && (SCIA_BIT_NUMBER == SCI_M_8BIT)) 

    #define  SCIA_RX_DATA_MASK  0x007f                    /* 8-bit, parity    */

#else

    #define  SCIA_RX_DATA_MASK  0x00ff                    /* 9-bit, parity    */

#endif


#if ( (SCIB_PARITY == SCI_PE_DISABLE) && (SCIB_BIT_NUMBER == SCI_M_8BIT))    

    #define  SCIB_RX_DATA_MASK  0x00ff                    /* 8-bit, no parity */

#elif ( (SCIB_PARITY == SCI_PE_DISABLE) && (SCIB_BIT_NUMBER == SCI_M_9BIT)) 

    #define  SCIB_RX_DATA_MASK  0x01ff                    /* 9-bit, no parity */

#elif ( (SCIB_PARITY == SCI_PE_ENABLE) && (SCIB_BIT_NUMBER == SCI_M_8BIT)) 

    #define  SCIB_RX_DATA_MASK  0x007f                    /* 8-bit, parity    */

#else

    #define  SCIB_RX_DATA_MASK  0x00ff                    /* 9-bit, parity    */

#endif
