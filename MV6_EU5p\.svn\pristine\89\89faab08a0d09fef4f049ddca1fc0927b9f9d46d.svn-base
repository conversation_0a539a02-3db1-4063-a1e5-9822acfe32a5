/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#include "mpc5500_spr_macros.h"

#include "wdt.h"        

#include "eemgm.h"
#ifdef  _BUILD_ADC_ 
#include "adc.h"
#endif /* _BUILD_ADC_ */

#ifdef  _BUILD_CAN_
#include "can.h"
#endif /* _BUILD_CAN_ */

#ifdef  _BUILD_DIGIO_
#include "digio.h"
#endif /* _BUILD_DIGIO_ */

#ifdef  _BUILD_DMA_
#include "dma.h"
#endif /* _BUILD_DMA_ */

#ifdef  _BUILD_EXTIRQ_
#include "extirq.h"
#endif /* _BUILD_EXTIRQ_ */

#ifdef  _BUILD_MATHLIB_
#include "mathlib.h"
#endif /* _BUILD_MATHLIB_ */

#ifdef  _BUILD_PIO_
#include "pio.h"
#endif /* _BUILD_PIO_ */

#ifdef  _BUILD_PIT_
#include "pit.h"
#endif /* _BUILD_PIT_ */

#ifdef  _BUILD_SCI_
#include "sci.h"
#endif /* _BUILD_SCI_ */

#ifdef  _BUILD_SPI_
#include "spi.h"
#endif /* _BUILD_SPI_ */

#include "ivor.h"

#ifdef  _BUILD_SYNC_
#include "sync.h"
#endif /* _BUILD_SYNC_ */

#ifdef  _BUILD_SYS_
#include "sys.h"
#endif /* _BUILD_SYS_ */

#ifdef  _BUILD_TIMING_
#include "timing.h"
#endif /* _BUILD_TIMING_ */

#ifdef  _BUILD_UART_
#include "uart.h"
#endif /* _BUILD_UART_ */

#ifdef  _BUILD_PHASE_
#include "phase.h"
#endif /* _BUILD_PHASE */


#ifdef  _BUILD_VCALIB_
#include "vcalib.h"
#endif /* _BUILD_VCALIB_ */

#ifdef  _BUILD_RECOVERY_
#include "recovery.h"
#include "ee.h"
#endif /* _BUILD_RECOVERY_ */

#include "Flash_out.h"

/* Local function declarations */
void app_data_section_init(void);
void calibration_section_init(void);
void SIU_Config(void);
void CPU_TIMER_Config(void);
void main(void);
void appManager(void);

void * GetApplicationStartup (void);
void Init_Global_start(void);
void Init_Global_start(void);
void Init_calib_RAM_start(void); 
void Init_calib_RAM_end(void);

uint8_t System_Reset;
uint64_T starttimer_PORold;
uint64_T starttimerPOR;

/* ------------------------------------------------------------------------- */
void IVOR_Common_ISR(void)      
/* ------------------------------------------------------------------------- */
{
   EEMGM_SetEventID(EE_BEFORE_SW_RESET_IVOR);
   ShutdownOS(E_OS_SYS_IVOR_ERROR);

   SYS_SwRST();
}

#ifdef _BUILD_RECOVERY_
void SetECSM(void)
{
    DisableAllInterrupts();

    /* enable ECC error report for flash memory only */
    ECSM.ECR.B.EFNCR = 0x01;

    EnableAllInterrupts();
}
#endif

/* ------------------------------------------------------------------------- */
void appManager(void)
/* ------------------------------------------------------------------------- */
{
    int16_T errtmp = 0;
    uint8_t EE_error = NO_ERROR;

#ifdef _TEST_TIMING_   
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_PORold);
    }
    else { /* MISRA */ }
#endif
    
#ifdef _BUILD_RECOVERY_
    IVOR2_UserFunction = IVOR2_faultmgm;
#else /*_BUILD_RECOVERY_*/
    IVOR2_UserFunction = IVOR_Common_ISR;
#endif

    IVOR10_UserFunction = (void (*) (void)) 0;
    IVOR_Common_ManagerUserFunction = IVOR_Common_ISR;
    IVOR_Config();
    SYS_AssignBitBiosInit(IVOR_IDX, 1);

#ifdef _BUILD_VSRAMMGM_
    {
        uint8_t rstType;

        SYS_GetLastReset(&rstType); 
        
        if(rstType == POWERONRESET) /*VSRAM has not valid data for sure*/
        {
            VSRAMMGM_Init((uint32_T)0);
            VSRAMMGM_Update();
        }
        else
        {
            VsramState = VSRAMMGM_Verify();
            if(VsramState != NO_ERROR) 
            {
              VSRAMMGM_Init(0);
              VSRAMMGM_Update();
            }
        }
    }
#endif


#ifdef _BUILD_RECOVERY_

    SetECSM(); /* enable ECC error */

    #ifdef _BUILD_VSRAMMGM_

        VSRAMMGM_LoadIvor2Data(); //<<<--- has it's own chksum.

        //read from last faulty value:
        //this will generate Ivor2 if it is faulty yet.
        {
            EE_error = checkFaultyAddrFromLastIvor2();
            
            if(EE_error == DO_EE_CORRUPTED_SETUP)
            {
                EE_SetupToDo = EE_CorruptedSetup;
            }
            else
            {
                EE_SetupToDo = EE_Setup;
            }
        }
    #endif
#endif

#ifndef _BUILD_VCALIB_
//    calibration_section_init();
#endif /* _BUILD_TASK_ */

#ifdef _BUILD_TASK_
    errtmp = 0;
    TASK_Config();        /** ????????? Introdurre param. di uscita int16_T !!! **/
    SYS_AssignBitBiosInit(TASK_IDX, 1);
#endif /* _BUILD_TASK_ */

  System_Reset=0;

#ifdef  _BUILD_TIMING_
    errtmp = errtmp | TIMING_Config();       /* codice di errore significativo??? */
    CPU_TIMER_Config();                    /* Initialize the timer units */
    errtmp = errtmp | TIMING_Init();         /* codice di errore significativo??? */
    SYS_AssignBitBiosInit(TMR_IDX, 1);
#endif /* _BUILD_TIMING_ */

    SIU_Config();     /** ????????? Introdurre param. di uscita int16_T !!! **/
    SYS_AssignBitBiosInit(SIU_IDX, 1);

#ifdef  _BUILD_FLASH_
    FLASH_ConfigStatus = FLASH_STATUS_NOT_CONFIGURED;

    if(EE_error == DO_EE_CORRUPTED_SETUP)
    {
        errtmp = errtmp | FLASH_ConfigEEPROM_Corrupted();
    }
    else
    {
        errtmp = errtmp | FLASH_Config(); /*** ????? da cambiare TIPO DELL'ERROR CODE !!! ***/
    }

    SYS_AssignBitBiosInit(FLASH_IDX, 1);
#endif /* _BUILD_FLASH_ */

#ifdef _BUILD_EEMGM_ 
    #ifdef _BUILD_WDT_
                            WDT_SetTimeout(TCR_WP_1600ms, TCR_WPEXT_1600ms);
                            WDT_PeriodicServiceRoutine()
    #endif /* _BUILD_WDT_ */ 

 
    EEMGM_PowerOn();
  

    #ifdef _BUILD_WDT_
                             WDT_SetTimeout(TCR_WP_1600ms, TCR_WPEXT_1600ms);
                             WDT_PeriodicServiceRoutine()
    #endif /* _BUILD_WDT_ */
#endif  

#ifdef  _BUILD_ADC_
    ADC_ConfigStatus = 0;
    errtmp = errtmp | ADC_Config();
    SYS_AssignBitBiosInit(ADC_IDX, 1);
#endif /* _BUILD_ADC_ */

#ifdef  _BUILD_DMA_
    DMA_ConfigStatus = 0;
    errtmp = errtmp | DMA_Config();
    SYS_AssignBitBiosInit(DMA_IDX, 1);
#endif /* _BUILD_DMA_ */

#ifdef _BUILD_EXTIRQ_

    errtmp = errtmp | EXTIRQ_Config();
    SYS_AssignBitBiosInit(EXTIRQ_IDX, 1);

#endif /* _BUILD_EXTIRQ_*/

#ifdef  _BUILD_PIO_
    errtmp = errtmp | PIO_Config();
    SYS_AssignBitBiosInit(PIO_IDX, 1);
#endif /* _BUILD_PIO_ */

#ifdef _BUILD_PIT_
    errtmp = errtmp | PIT_Config();
#endif 

#ifdef  _BUILD_SPI_ 
    SPI_ConfigStatus=0;
    SPI_ChannelStatus[SPI_CH_A]=0;
    SPI_ChannelStatus[SPI_CH_B]=0;
    SPI_ChannelStatus[SPI_CH_C]=0;
    SPI_ChannelStatus[SPI_CH_D]=0;
    errtmp = errtmp | SPI_Config();
    SYS_AssignBitBiosInit(SPI_IDX, 1);
#endif /* _BUILD_SPI_ */

#ifdef  _BUILD_SYS_
    sysInternalState = SYI_STATE_PRE_CONFIGURATION;
    errtmp = errtmp | SYS_Config();
    SYS_AssignBitBiosInit(ETPU_IDX, 1);
#endif /* _BUILD_SYS_ */

#ifdef  _BUILD_SYNC_
    syncInternalState = SI_STATE_PRE_CONFIGURATION;
    errtmp = errtmp | SYNC_Config();
#endif /* _BUILD_SYNC_ */

#ifdef _BUILD_PHASE_ 
    errtmp = errtmp | PHASE_Config();
#endif /* _BUILD_PHASE_ */

#ifdef _BUILD_WDT_
    WDT_Config();               /*** ????? da cambiare TIPO DELL'ERROR CODE !!! ***/
    WDT_PeriodicServiceRoutine()
#endif /* _BUILD_WDT_ */ 

#ifdef  _BUILD_CAN_ 
    CAN_ConfigStatus=0;
    errtmp = errtmp | CAN_Config() ;
    SYS_AssignBitBiosInit(CAN_IDX, 1);
#endif /* _BUILD_CAN_ */
#ifdef  _BUILD_SCI_
    SCI_ConfigurationStatus = 0;
    errtmp = errtmp | SCI_Config();
#ifdef _BUILD_LINMGM_    
    SCI_LINConfigurationStatus = 0;
    errtmp = errtmp | SCI_LINConfig();
#endif    
    SYS_AssignBitBiosInit(SCI_IDX, 1);
#endif /* _BUILD_SCI_ */

#ifdef  _BUILD_UART_
    errtmp = errtmp | EtpuUartConfig();  
#endif /* _BUILD_UART_ */

#ifdef _BUILD_VCALIB_
    VCALIB_Config();
#endif /* _BUILD_VCALIB_ */

#ifdef _BUILD_VSRAMMGM_
//    VSRAMMGM_Init( (uint32_T)0 );
#endif


    /* Jump to main entry point */
    if (errtmp == NO_ERROR)
    {
        /* Normal Run. */
    }
    else
    {
        ShutdownOS(E_NO_ERROR);
    }
    main(); /* Jump to main entry point */
}


/*
** ===================================================================
**     Method          :  SIU_Config (Akhela )
**     Programmer      :  Solinas
**     Date Last Modify:  21/02/2005
**     Description :
**         This method comprises all the entry points for 
**         the SIU System Integration Unit user initialization
**         Ported from the SIU bean of the Processor Expert 
**         
** ===================================================================
*/

void  SIU_Config(void)
{
  /* SIU_DIRER: EIRE15=0,EIRE14=0,EIRE13=0,EIRE12=0,EIRE11=0,EIRE10=0,EIRE9=0,EIRE8=0,EIRE7=0,EIRE6=0,EIRE5=0,EIRE4=0,EIRE3=0,EIRE2=0,EIRE1=0,EIRE0=0 */
  SIU.DIRER.R= 0x00;            
  /* Solinas : DMA/interrupt request enable register 
  */
  
  /* SIU_DIRSR: DIRS3=0,DIRS2=0,DIRS1=0,DIRS0=0 */
  SIU.DIRSR.R= 0x00;            
  /* Solinas : DMA/interrupt request select register
  */
  
  /* SIU_ORER: ORE15=0,ORE14=0,ORE13=0,ORE12=0,ORE11=0,ORE10=0,ORE9=0,ORE8=0,ORE7=0,ORE6=0,ORE5=0,ORE4=0,ORE3=0,ORE2=0,ORE1=0,ORE0=0 */
  SIU.ORER.R= 0x00;             
  /* Solinas : Overrun request enable register
  */
  
  /* SIU_IREER: IREE15=1,IREE14=1,IREE13=1,IREE12=1,IREE11=1,IREE10=1,IREE9=1,IREE8=1,IREE7=1,IREE6=1,IREE5=1,IREE4=1,IREE3=1,IREE2=1,IREE1=1,IREE0=1 */
  SIU.IREER.R=0x00;          
  /* Solinas : IRQ rising-edge event enable register
               IRQ pins: [0-15] */
  
  /* SIU_IFEER: IREE15=0,IREE14=0,IREE13=0,IREE12=0,IREE11=0,IREE10=0,IREE9=0,IREE8=0,IREE7=0,IREE6=0,IREE5=0,IREE4=0,IREE3=0,IREE2=0,IREE1=0,IREE0=0 */
  SIU.IFEER.R=0x00;            
  /* Solinas : IRQ falling-edge event enable register
  */
  
  /* SIU_IDFR: DFL=0 */
  SIU.IDFR.R= 0x00;             
  /* Solinas : IRQ digital filter register
  */
  
  
  /* SIU_PCR4: PA=1,IBE=0,DSC=0,ODE=0,HYS=0,WPE=0,WPS=0 */   
  /* EBI  initialization */ 
  /* Solinas : Pad configuration registers 0-230
               Note: check this configuiration cause conflict with
               a previous initialization
  */
}  

/*
** ===================================================================
**     Method          :  
**     Programmer      :  
**     Date Last Modify:  
**     Description :
**         
**         
**         
** ===================================================================
*/
/* AM - Modified; it uses only GHS/MWERKS internal macroses */  
void  	CPU_TIMER_Config(void)
{
    /*Insert here the initialization code for the CPU Timer*/
    /*
    ** -------------------------------------
    ** Timer facilities initialization:
    **  Time Base disabled (TBEN = 0)
    ** Decrementer initialization:
    **  Decrementer interval: 0 ns
    **  Auto-reload:          Disabled
    **  Reloaded interval:    0 ns
    **  Interrupt:            Disabled
    ** NOTE: next operations are performed by TIMING_Init and WDT_Config
    ** Fixed-interval timer initialization:
    **  Timer period:         (1/FSYS)*1000 ns
    **  Interrupt:            Disabled
    ** Watchdog timer initialization:
    **  Timer period:         (1/FSYS)*1000 ns
    **  After time-out:       No action
    **  Interrupt:            Disabled
    */
#ifdef __MWERKS__
    /* Time base Disabled */
    setSpecReg32SPR_HID0(getSpecReg32SPR_HID0() & ~0x00004000); 
    /* Decrementer interval: 0 ns  Auto-reload: Disabled */
    setSpecReg32SPR_DEC(0x00);
    setSpecReg32SPR_DECAR(0x00);
#endif
#ifdef __GHS__
    /* Time base Disabled */
    __MTSPR(SPR_HID0,(__MFSPR(SPR_HID0)& (~0x00004000)));
    /* Decrementer interval: 0 ns  Auto-reload: Disabled */
    __MTSPR(SPR_DEC, 0x00);
    __MTSPR(SPR_DECAR, 0x00);
#endif  
}


void app_data_section_init(void)
{
  extern uint32_t __DATA_ROM;
  extern uint32_t __SRAM_CPY_START; 
  extern uint32_t __ROM_COPY_SIZE; 

  uint8_t* src = (uint8_t* ) (& __DATA_ROM);
  uint8_t* dst = (uint8_t* ) (& __SRAM_CPY_START);
  uint32_t i;
  
  for(i=0;i<((uint32_t)(& __ROM_COPY_SIZE));i++)
  {
    *dst=*src;
    dst++;
    src++;
  }
}

#ifndef _BUILD_VCALIB_
void calibration_section_init(void)
{
  extern uint32_t __CALIB_ROM_START;
  extern uint32_t __CALIB_ROM_END; 
  extern uint32_t __LOCAL_CALIB_RAM_START; 

  uint32_t i;
  uint8_t* src = (uint8_t* ) (& __CALIB_ROM_START);
  uint8_t* dst = (uint8_t* ) (& __LOCAL_CALIB_RAM_START);
  
  if(src != dst)
  {
      for(i=0;i< ((uint32_t)(& __CALIB_ROM_END)-(uint32_t)(& __CALIB_ROM_START)) ;i++)
      {
        *dst=*src;
		dst++;
		src++;
      }
  }
}
#endif

void * GetApplicationStartup (void)
{
  return ((void*)appManager);
}

#ifdef __MWERKS__
 
#pragma force_active on
 
uint8_t* emty_src;

void Init_Global_start(void)
{
    extern uint32_t __GLOBAL_START;
     
    emty_src = (uint8_t* ) (& __GLOBAL_START);
    
    return;         
} 
  
void Init_Global_end(void)
{
    extern uint32_t __GLOBAL_END;
   
    emty_src = (uint8_t* ) (& __GLOBAL_END);
     
    return;         
}  
 
void Init_calib_RAM_start(void)
{
    extern uint32_t __CALIB_RAM_START;
   
    emty_src = (uint8_t* ) (& __CALIB_RAM_START);
     
    return;         
} 

void Init_calib_RAM_end(void)
{
    extern uint32_t __CALIB_RAM_END; 

    emty_src = (uint8_t* ) (& __CALIB_RAM_END);
    
    return;         
} 

#pragma force_active off


#endif
