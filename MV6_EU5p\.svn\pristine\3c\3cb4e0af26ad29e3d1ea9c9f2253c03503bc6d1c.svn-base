/*
 * File: TrqDriver.h
 *
 * Code generated for Simulink model 'TrqDriver'.
 *
 * Model version                  : 1.2235
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Aug  5 15:33:05 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TrqDriver_h_
#define RTW_HEADER_TrqDriver_h_
#include <string.h>
#include <stddef.h>
#ifndef TrqDriver_COMMON_INCLUDES_
# define TrqDriver_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "diagmgm_out.h"
#include "mathlib.h"
#endif                                 /* TrqDriver_COMMON_INCLUDES_ */

#include "TrqDriver_types.h"

/* Includes for objects with custom storage classes. */
#include "trq_driver.h"
#include "canmgm.h"
#include "diagmgm_out.h"
#include "recmgm.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((rtm)->errorStatus)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((rtm)->errorStatus = (val))
#endif

/* Exported data define */

/* Definition for custom storage class: Define */
#define BITMASK_BIT0                   1U                        /* Referenced by: '<S7>/BITMASK_BIT0' */
#define BITMASK_BIT1                   2U                        /* Referenced by: '<S7>/BITMASK_BIT1' */
#define BITMASK_BIT2                   4U                        /* Referenced by: '<S7>/BITMASK_BIT2' */
#define BKDVEHSPEEDSETUP_dim           7U                        /* Referenced by: '<S55>/BKDVEHSPEEDSETUP_dim' */
#define BKVEHSPSETUPCAN_dim            3U                        /* Referenced by: '<S17>/BKVEHSPSETUPCAN_dim' */
#define BKVSCERROR_dim                 10U                       /* Referenced by: '<S72>/BKVSCERROR_dim' */
#define CME_CAN_FREEZE                 2U                        /* Referenced by: '<S3>/CmeDriver_Management' */
#define CME_CAN_OK                     1U                        /* Referenced by: '<S3>/CmeDriver_Management' */
#define CME_GAS_RPM                    0U                        /* Referenced by: '<S3>/CmeDriver_Management' */
#define CME_INIT                       0                         /* Referenced by: '<S2>/CME_INIT' */
#define CME_MAX                        32736                     /* Referenced by: '<S26>/CME_MAX' */
#define CME_REC_ACTIVE                 3U                        /* Referenced by: '<S3>/CmeDriver_Management' */
#define ID_TRQ_DRIVER                  20526924U                 /* Referenced by: '<S2>/ID_TRQ_DRIVER' */

/* mask */
#define MAX_GASPOS_CC                  1600U                     /* Referenced by: '<S73>/MAX_GASPOS_CC' */
#define MIN_GASPOS_CC                  0U                        /* Referenced by: '<S73>/MIN_GASPOS_CC' */

/* Block signals for system '<S1>/T10ms' */
typedef struct {
  uint32_T Conversion7;                /* '<S57>/Conversion7' */
  uint16_T Switch1;                    /* '<S6>/Switch1' */
  uint16_T Conversion;                 /* '<S61>/Conversion' */
  uint16_T Conversion4;                /* '<S51>/Conversion4' */
  uint8_T LogicalOperator;             /* '<S15>/Logical Operator' */
  uint8_T LogicalOperator1;            /* '<S13>/Logical Operator1' */
  uint8_T RelationalOperator;          /* '<S9>/Relational Operator' */
  uint8_T RelationalOperator_p;        /* '<S7>/Relational Operator' */
} rtB_T10ms_TrqDriver;

/* Block states (default storage) for system '<S1>/T10ms' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S12>/Memory' */
  uint16_T vehSpeedSetUpCANOld;        /* '<S3>/CmeDriver_Management' */
  int16_T Memory4_PreviousInput;       /* '<S43>/Memory4' */
  int16_T Memory_PreviousInput_c;      /* '<S43>/Memory' */
  int16_T Memory1_PreviousInput;       /* '<S43>/Memory1' */
  int16_T Memory2_PreviousInput;       /* '<S43>/Memory2' */
  int16_T Memory3_PreviousInput;       /* '<S43>/Memory3' */
  uint8_T is_active_c1_TrqDriver;      /* '<S3>/CmeDriver_Management' */
  uint8_T is_CMECAN_Calculation;       /* '<S3>/CmeDriver_Management' */
  uint8_T is_CME_CAN;                  /* '<S3>/CmeDriver_Management' */
  uint8_T is_VehicleSpeed_Control;     /* '<S3>/CmeDriver_Management' */
  uint8_T is_USED;                     /* '<S3>/CmeDriver_Management' */
  uint8_T is_NORMAL;                   /* '<S3>/CmeDriver_Management' */
  uint8_T Memory_PreviousInput_d;      /* '<S15>/Memory' */
} rtDW_T10ms_TrqDriver;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_TrqDriver_Reset_Trig_ZCE[2];/* '<S1>/fc_TrqDriver_Reset' */
  ZCSigState fc_TrqDriver_Calc_Trig_ZCE;/* '<S1>/fc_TrqDriver_Calc' */
} PrevZCSigStates_TrqDriver;

/* Invariant block signals for system '<S1>/T10ms' */
typedef struct {
  const int16_T DataTypeConversion1;   /* '<S19>/Data Type Conversion1' */
} rtC_T10ms_TrqDriver;

/* Invariant block signals for system '<S1>/Init' */
typedef struct {
  const uint16_T DataTypeConversion2;  /* '<S2>/Data Type Conversion2' */
} rtC_Init_TrqDriver;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint16_T ev_PowerOn;                 /* '<Root>/ev_PowerOn' */
  uint16_T ev_NoSync;                  /* '<Root>/ev_NoSync' */
  uint16_T ev_T10ms;                   /* '<Root>/ev_T10ms' */
} ExternalInputs_TrqDriver;

/* Real-time Model Data Structure */
struct tag_RTM_TrqDriver {
  const char_T *errorStatus;
};

/* Extern declarations of internal data for system '<S1>/T10ms' */
extern rtB_T10ms_TrqDriver TrqDriver_T10ms_B;
extern rtDW_T10ms_TrqDriver TrqDriver_T10ms_DW;
extern const rtC_T10ms_TrqDriver TrqDriver_T10ms_C;

/* Extern declarations of internal data for system '<S1>/Init' */
extern const rtC_Init_TrqDriver TrqDriver_Init_C;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_TrqDriver TrqDriver_U;

/* Model entry point functions */
extern void TrqDriver_initialize(void);
extern void TrqDriver_step(void);

/* Real-time Model object */
extern RT_MODEL_TrqDriver *const TrqDriver_M;

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver')    - opens subsystem TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver
 * hilite_system('TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TrqDriver_fxp/TrqDriver/TrqDriver'
 * '<S1>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver'
 * '<S2>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/Init'
 * '<S3>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms'
 * '<S4>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/fc_TrqDriver_Calc'
 * '<S5>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/fc_TrqDriver_Reset'
 * '<S6>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/Bypass_TC2WZero_Ctrl'
 * '<S7>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Diagnosis'
 * '<S8>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Selection'
 * '<S9>'   : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Smooth'
 * '<S10>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriver_Management'
 * '<S11>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc'
 * '<S12>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Selection'
 * '<S13>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCDisable_Calc'
 * '<S14>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc'
 * '<S15>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc'
 * '<S16>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL'
 * '<S17>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL_Init'
 * '<S18>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL_Suspend'
 * '<S19>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_Reset'
 * '<S20>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Diagnosis/FlgCmeDriverCANOK_calculation'
 * '<S21>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Diagnosis/SetDiagState'
 * '<S22>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Smooth/RateLimiter_S16'
 * '<S23>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeDriverCAN_Smooth/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S24>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeGasRpm0'
 * '<S25>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeTargetWot'
 * '<S26>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_MaxCmeTrq'
 * '<S27>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio'
 * '<S28>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeGasRpm0/Look2D_IR_S16_1'
 * '<S29>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeGasRpm0/Look2D_IR_S16_2'
 * '<S30>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeGasRpm0/Look2D_IR_S16_1/Data Type Conversion Inherited1'
 * '<S31>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeGasRpm0/Look2D_IR_S16_2/Data Type Conversion Inherited1'
 * '<S32>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_CmeTargetWot/LookUp_IR_U8_1'
 * '<S33>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_MaxCmeTrq/Compare To Constant3'
 * '<S34>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_MaxCmeTrq/Compare To Constant5'
 * '<S35>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_MaxCmeTrq/LookUp_IR_U8'
 * '<S36>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/Calc_BkGasDriv'
 * '<S37>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/GasPos_IndexRatio'
 * '<S38>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/Rpm_IndexRatio'
 * '<S39>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/Calc_BkGasDriv/Compare To Constant1'
 * '<S40>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/Calc_BkGasDriv/Compare To Constant2'
 * '<S41>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/Calc_BkGasDriv/Compare To Zero3'
 * '<S42>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Calc/Calc_Ratio/Calc_BkGasDriv/Compare To Zero4'
 * '<S43>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Selection/DeltaCmeEstWheelF'
 * '<S44>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Selection/FOF_Reset_S16_FXP'
 * '<S45>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/CmeGasRpm_Selection/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S46>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCDisable_Calc/Compare To Constant1'
 * '<S47>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCDisable_Calc/Compare To Constant2'
 * '<S48>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/Compare To Zero'
 * '<S49>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VEHSPEEDSTEP_Cnt'
 * '<S50>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF'
 * '<S51>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpRateLim'
 * '<S52>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUp_nosat'
 * '<S53>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUp_sat'
 * '<S54>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VEHSPEEDSTEP_Cnt/ResetRateLimiter'
 * '<S55>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/Calc_KFilt'
 * '<S56>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/Compare To Zero'
 * '<S57>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/FOF_Reset_S16_FXP'
 * '<S58>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/Calc_KFilt/LookUp_IR_U16'
 * '<S59>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/Calc_KFilt/PreLookUpIdSearch_S8'
 * '<S60>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/Calc_KFilt/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S61>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpFOF/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S62>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpRateLim/RateLimiter_S32'
 * '<S63>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCFilt_Calc/VehSpeedSetUpRateLim/RateLimiter_S32/Data Type Conversion Inherited1'
 * '<S64>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant1'
 * '<S65>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant2'
 * '<S66>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant3'
 * '<S67>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant4'
 * '<S68>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant5'
 * '<S69>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant6'
 * '<S70>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/FlgVSCPause_Calc/Compare To Constant7'
 * '<S71>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/GasPosCC_calc'
 * '<S72>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/PI_term_calc'
 * '<S73>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/tmpGasPosCC_calc'
 * '<S74>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/GasPosCC_calc/Closed_Loop_Active'
 * '<S75>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/GasPosCC_calc/GasPos_overwrite'
 * '<S76>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/PI_term_calc/LookUp_IR_S16_int'
 * '<S77>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/PI_term_calc/LookUp_IR_S16_prop'
 * '<S78>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/PI_term_calc/PreLookUpIdSearch_S16'
 * '<S79>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/PI_term_calc/LookUp_IR_S16_int/Data Type Conversion Inherited3'
 * '<S80>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/PI_term_calc/LookUp_IR_S16_prop/Data Type Conversion Inherited3'
 * '<S81>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/tmpGasPosCC_calc/Saturation Dynamic'
 * '<S82>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL/tmpGasPosCC_calc/Saturation Dynamic1'
 * '<S83>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL_Init/LookUp_U16_U16'
 * '<S84>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_CL_Init/LookUp_U16_U16/Data Type Conversion Inherited3'
 * '<S85>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_Reset/Compare To Constant1'
 * '<S86>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_Reset/Compare To Constant2'
 * '<S87>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_Reset/Compare To Constant3'
 * '<S88>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_Reset/FOF_Reset_S16_FXP'
 * '<S89>'  : 'TrqDriver_fxp/TrqDriver/TrqDriver/TrqDriver/T10ms/VehCtrl_Reset/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': TrqDriver
 */
#endif                                 /* RTW_HEADER_TrqDriver_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
