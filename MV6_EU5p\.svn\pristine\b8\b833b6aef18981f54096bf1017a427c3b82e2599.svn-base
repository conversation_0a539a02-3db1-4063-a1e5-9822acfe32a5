#include "typedefs.h"
 
#pragma ghs section rodata=".ee_id3_data"

// Declare here all the variables to be stored in EEPROM with ID3

    #include "dummy_eep_03.c"

#ifdef _BUILD_DIAGMGM_
    #include "..\diagmgm\diagmgm_eep.c"
#endif
#ifdef _BUILD_SAF2MGM_
    #include "..\Saf2Mgm\Saf2Mgm_eep.c"
#endif 
#ifdef _BUILD_SAF3MGM_
    #include "..\Saf3Mgm\Saf3Mgm_eep.c"
#endif 
#ifdef _BUILD_CPUMGM_
    #include "..\cpumgm\cpumgm_eep.c"
#endif
#ifdef _BUILD_LAMPMGM_
    #include "lampmgm_eep.c"
#endif
#ifdef _BUILD_MISFOBD2_
    #include "misfobd2_eep.c"
#endif

