/*
 * File: exhval_mgm.h
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1693
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Aug  9 10:02:47 2024
 */

#ifndef RTW_HEADER_exhval_mgm_h_
#define RTW_HEADER_exhval_mgm_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T AngExhErr;

/* exh. valve angle error */
extern int16_T AngExhErr1;

/* Exh. valve angle error at time step -1 */
extern int16_T AngExhTrg;

/* exh. valve angle target filtered */
extern int16_T AngExhTrg0;

/* exh. valve angle target */
extern int32_T AngExhValPerc;

/* valve opening angle [%] */
extern uint8_T CntExhVA;

/* Plausibility ok counter */
extern uint8_T CntExhVB;

/* Plausibility ok counter */
extern uint16_T CntExhVEval;

/* Time counter to evaluate diagnosis */
extern uint8_T CntExhVMiss;

/* Miss diagnosis */
extern uint8_T CntExhVWrong;

/* Plausibility error counter */
extern uint16_T CntVOutExh;

/* counter diagnosis */
extern int16_T ExhVMaxTrg;

/* Near max saturation  */
extern int16_T ExhVMinTrg;

/* Near min saturation  */
extern uint32_T ExhValModuleTime;

/* Module timing counter */
extern uint16_T ExhvalRelTime;

/* Time-history relative time */
extern uint8_T FlgExVPWLamp;

/* WLamp Pos or Self completed */
extern uint8_T FlgExhVDiagOn;

/* Diagnosis flag confirmed */
extern uint8_T FlgExhVZeroPos;

/* flag of Active Diag to search zero position */
extern uint8_T FlgExhValHBEna;

/* enable flag of H-bridge driving exhaust gas valve */
extern uint8_T FlgSelfExhLMSDone;

/* flag to indicate the LMS self-learning has been done */
extern uint8_T FlgSelfExhReq;

/* flag to request self-learning of exh. valve by I/O control */
extern uint8_T FlgSelfExhUMSDone;

/* flag to indicate the UMS self-learning has been done */
extern int16_T FrzAngExhValPerc;

/* valve opening angle freezed [%] */
extern int16_T FrzVOutExh;

/* freeze direction diagnosis */
extern uint32_T IDExhValMgm;

/* ID Version */
extern uint8_T SelfExhVStab;

/* Stability flag */
extern uint8_T StDiagExhVPos;

/* status diag pos */
extern uint8_T StSelfExh;

/* status of the self-learning of the exh. valve */
extern uint8_T StSelfExhVStab;

/* Stability status */
extern uint8_T TrigExhVMinMaxTrg;

/* Trig min max trg calculus */
extern int16_T VAngExhValF;

/* angle voltage valve filtered */
extern int16_T VExhPID;

/* Exh. valve PID regulator output voltage at current time step */
extern int32_T VExhPID1;

/* Exh. valve PID regulator output voltage at time step -1 */
extern int32_T VExhSatPIDMax;

/* Max saturation PI */
extern int32_T VExhSatPIDMin;

/* Min saturation PI */
extern int16_T VOutExh;

/* Exh. gas valve command voltage */

extern uint8_T CntReqExhSelf;

/* ExhV Self trigger counter */

extern uint8_T CntExhVMgmSelf;

/* Self armed */

/* EE */
extern uint8_T FlgSelfExhLMSOnce;
extern uint8_T FlgSelfExhUMSOnce;
extern uint16_T VAngExhClosed;
extern uint16_T VAngExhOpen;
extern  uint16_T CntSelfExhTripEnable;
extern uint8_T  FlgLMSTripEnable;
extern uint32_T FlgUMSTripEnable;

void ExhValMgm_Init(void);
void ExhValMgm_NoSync(void);
void ExhValMgm_T10ms(void);
#endif                                 /* RTW_HEADER_exhval_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
