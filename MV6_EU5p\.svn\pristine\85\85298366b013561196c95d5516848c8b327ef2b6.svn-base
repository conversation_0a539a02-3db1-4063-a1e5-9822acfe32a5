/*
 * File: PresTarget.c
 *
 * Code generated for Simulink model 'PresTarget'.
 *
 * Model version                  : 1.2290
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Sep 12 15:33:58 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (21), Warnings (4), Errors (8)
 */

#include "PresTarget.h"
#include "PresTarget_private.h"
#include "div_s32.h"

/* user code (top of source file) */
/* System '<Root>/PresTarget' */
#ifdef _BUILD_PRESTARGET_

/* Block states (default storage) */
D_Work_PresTarget PresTarget_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_PresTarget PresTarget_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_PresTarget PresTarget_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T DQAirTarget0F;

/* DQAirTarget0 filtered */
uint16_T PresObj;

/* PresObj */
uint16_T QAirTargetMod;

/* Air flow rate target modified */

/* Output and update for function-call system: '<S1>/T10ms' */
void PresTarget_T10ms(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_Look2D_U16_U16_U16;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int16_T rtb_Subtract;
  uint16_T rtb_DataStoreRead1;
  uint8_T rtb_Conversion3;
  int32_T rtb_DataStoreRead4;

  /* If: '<S4>/If' incorporates:
   *  Inport: '<Root>/RpmF'
   */
  if (RpmF != 0) {
    /* Outputs for IfAction SubSystem: '<S4>/Calc_PresObj' incorporates:
     *  ActionPort: '<S8>/Action Port'
     */
    /* DataStoreRead: '<S8>/Data Store Read1' */
    rtb_DataStoreRead1 = QAirTargetMod;

    /* S-Function (LookUp_U16_U16): '<S16>/LookUp_U16_U16' incorporates:
     *  Constant: '<S8>/BKQAIRTMOD'
     *  Constant: '<S8>/BKQAIRTMOD_dim'
     *  Constant: '<S8>/VTKFILTDQAIRT'
     */
    LookUp_U16_U16( &rtb_Look2D_U16_U16_U16, &VTKFILTDQAIRT[0],
                   rtb_DataStoreRead1, &BKQAIRTMOD[0], ((uint8_T)BKQAIRTMOD_dim));

    /* DataTypeConversion: '<S14>/Conversion3' incorporates:
     *  Constant: '<S12>/Constant'
     *  Inport: '<Root>/CntAbsTdc'
     *  RelationalOperator: '<S12>/Compare'
     */
    rtb_Conversion3 = (uint8_T)(CntAbsTdc <= 1U);

    /* DataStoreRead: '<S8>/Data Store Read4' */
    rtb_DataStoreRead4 = PresTarget_DWork.DQAirTHiR;

    /* S-Function (FOF_Reset_S16_FXP): '<S14>/FOF_Reset_S16_FXP' incorporates:
     *  Inport: '<Root>/DQAirTarget0'
     */
    FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                      DQAirTarget0, rtb_Look2D_U16_U16_U16, DQAirTarget0,
                      rtb_Conversion3, rtb_DataStoreRead4);

    /* Sum: '<S8>/Subtract' incorporates:
     *  DataTypeConversion: '<S8>/Data Type Conversion5'
     *  Inport: '<Root>/QAirTarget0'
     */
    rtb_Subtract = (int16_T)(((int16_T)QAirTarget0) - rtb_FOF_Reset_S16_FXP_o1);

    /* If: '<S8>/If' incorporates:
     *  Constant: '<S11>/ZERO2'
     *  RelationalOperator: '<S13>/Compare'
     */
    if (rtb_Subtract > 0) {
      /* Outputs for IfAction SubSystem: '<S8>/Calc_QAirTargetMod' incorporates:
       *  ActionPort: '<S10>/Action Port'
       */
      /* Product: '<S10>/Divide' incorporates:
       *  DataTypeConversion: '<S10>/Data Type Conversion5'
       *  Inport: '<Root>/QAirFactor'
       *  Product: '<S10>/Product'
       */
      rtb_DataStoreRead4 = div_s32(rtb_Subtract << 10, QAirFactor);
      if (rtb_DataStoreRead4 < 0) {
        rtb_DataStoreRead4 = 0;
      } else {
        if (rtb_DataStoreRead4 > 65535) {
          rtb_DataStoreRead4 = 65535;
        }
      }

      rtb_Look2D_U16_U16_U16 = (uint16_T)rtb_DataStoreRead4;

      /* End of Product: '<S10>/Divide' */
      /* End of Outputs for SubSystem: '<S8>/Calc_QAirTargetMod' */
    } else {
      /* Outputs for IfAction SubSystem: '<S8>/Clear_QAirTargetMod' incorporates:
       *  ActionPort: '<S11>/Action Port'
       */
      rtb_Look2D_U16_U16_U16 = 0U;

      /* End of Outputs for SubSystem: '<S8>/Clear_QAirTargetMod' */
    }

    /* End of If: '<S8>/If' */

    /* DataStoreWrite: '<S8>/Data Store Write1' */
    QAirTargetMod = rtb_Look2D_U16_U16_U16;

    /* DataStoreWrite: '<S8>/Data Store Write3' */
    DQAirTarget0F = rtb_FOF_Reset_S16_FXP_o1;

    /* S-Function (Look2D_U16_U16_U16): '<S15>/Look2D_U16_U16_U16' incorporates:
     *  Constant: '<S8>/BKQAIRTMOD1'
     *  Constant: '<S8>/BKQAIRTMOD_dim1'
     *  Constant: '<S8>/BKRPMPRESOBJ'
     *  Constant: '<S8>/BKRPMPRESOBJ_dim'
     *  Constant: '<S8>/TBPRESOBJ'
     */
    Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBPRESOBJ[0],
                       rtb_Look2D_U16_U16_U16, &BKQAIRTMOD[0], ((uint8_T)
      BKQAIRTMOD_dim), RpmF, &BKRPMPRESOBJ[0], ((uint8_T)BKRPMPRESOBJ_dim));

    /* DataStoreWrite: '<S8>/Data Store Write5' */
    PresObj = rtb_Look2D_U16_U16_U16;

    /* DataStoreWrite: '<S8>/Data Store Write6' */
    PresTarget_DWork.DQAirTHiR = rtb_FOF_Reset_S16_FXP_o2;

    /* End of Outputs for SubSystem: '<S4>/Calc_PresObj' */
  } else {
    /* Outputs for IfAction SubSystem: '<S4>/Reset_var' incorporates:
     *  ActionPort: '<S9>/Action Port'
     */
    /* DataStoreWrite: '<S9>/Data Store Write1' incorporates:
     *  Constant: '<S9>/ZERO2'
     */
    QAirTargetMod = 0U;

    /* DataStoreWrite: '<S9>/Data Store Write2' incorporates:
     *  Constant: '<S9>/ZERO3'
     */
    PresTarget_DWork.DQAirTHiR = 0;

    /* DataStoreWrite: '<S9>/Data Store Write3' incorporates:
     *  Constant: '<S9>/ZERO5'
     */
    DQAirTarget0F = 0;

    /* DataStoreWrite: '<S9>/Data Store Write7' incorporates:
     *  Constant: '<S9>/ZERO4'
     */
    PresObj = 0U;

    /* End of Outputs for SubSystem: '<S4>/Reset_var' */
  }

  /* End of If: '<S4>/If' */
  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/Init' */
void PresTarget_Init(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/ZERO2'
   */
  QAirTargetMod = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Constant: '<S2>/ZERO3'
   */
  PresTarget_DWork.DQAirTHiR = 0;

  /* DataStoreWrite: '<S2>/Data Store Write3' incorporates:
   *  Constant: '<S2>/ZERO5'
   */
  DQAirTarget0F = 0;

  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  Constant: '<S2>/ZERO4'
   */
  PresObj = 0U;

  /* user code (Output function Trailer) */

  /* System '<S1>/Init' */

  /* PILOTAGGIO USCITE - INIT */
  /* Read static variables */
  /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
  PresTarget_initialize();
}

/* Output and update for function-call system: '<S1>/NoSync' */
void PresTarget_NoSync(void)
{
  /* DataStoreWrite: '<S3>/Data Store Write1' incorporates:
   *  Constant: '<S3>/ZERO2'
   */
  QAirTargetMod = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write2' incorporates:
   *  Constant: '<S3>/ZERO3'
   */
  PresTarget_DWork.DQAirTHiR = 0;

  /* DataStoreWrite: '<S3>/Data Store Write3' incorporates:
   *  Constant: '<S3>/ZERO5'
   */
  DQAirTarget0F = 0;

  /* DataStoreWrite: '<S3>/Data Store Write7' incorporates:
   *  Constant: '<S3>/ZERO4'
   */
  PresObj = 0U;

  /* user code (Output function Trailer) */

  /* System '<S1>/NoSync' */

  /* PILOTAGGIO USCITE - OFF */
}

/* Model step function */
void PresTarget_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/PresTarget' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((PresTarget_U.ev_PowerOn > 0) &&
      (PresTarget_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    PresTarget_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  PresTarget_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (PresTarget_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((PresTarget_U.ev_T10ms > 0) &&
      (PresTarget_PrevZCSigState.trig_to_fc_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    PresTarget_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  PresTarget_PrevZCSigState.trig_to_fc_Trig_ZCE = (ZCSigState)
    (PresTarget_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOff' */
  if ((PresTarget_U.ev_PowerOff > 0) &&
      (PresTarget_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/NoSync'
     */
    PresTarget_NoSync();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  PresTarget_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (PresTarget_U.ev_PowerOff > 0);

  /* End of Inport: '<Root>/ev_PowerOff' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* End of Outputs for SubSystem: '<Root>/PresTarget' */
}

/* Model initialize function */
void PresTarget_initialize(void)
{
  PresTarget_PrevZCSigState.trig_to_fc_Trig_ZCE = POS_ZCSIG;
  PresTarget_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  PresTarget_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/PresTarget' */
#endif                                 // _BUILD_PRESTARGET_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
