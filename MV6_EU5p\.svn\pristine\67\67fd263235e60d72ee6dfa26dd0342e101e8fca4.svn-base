/*
 * File: CmeFilterMgm_private.h
 *
 * Code generated for Simulink model 'CmeFilterMgm'.
 *
 * Model version                  : 1.2383
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar 27 14:58:16 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (19), Warnings (6), Errors (8)
 */

#ifndef RTW_HEADER_CmeFilterMgm_private_h_
#define RTW_HEADER_CmeFilterMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "CmeFilterMgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "trq_drivmgm.h"
#include "gearshift_mgm.h"
#include "trq_driver.h"
#include "PTrain_Diag.h"
#include "Engflag.h"
#include "gaspos_mgm.h"
#include "Trq_Driver.h"
#include "GearPosClu_mgm.h"
#include "idle_mgm.h"
#include "syncmgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKCMEDRCMEFILT[6];      /* Variable: BKCMEDRCMEFILT
                                        * Referenced by:
                                        *   '<S12>/BKCMEDRCMEFILT'
                                        *   '<S16>/BKCMEDRCMEFILT'
                                        * CmeDriver filter rate breakpoint
                                        */
extern int16_T BKCMEDRIVPDD[6];        /* Variable: BKCMEDRIVPDD
                                        * Referenced by: '<S16>/BKCMEDRIVPDD'
                                        * CmeDriverMax filter rate breakpoint
                                        */
extern int16_T MAXCMEIOFFSAT;          /* Variable: MAXCMEIOFFSAT
                                        * Referenced by: '<S24>/Saturation'
                                        * Saturazione CmeDriver offset
                                        */
extern int16_T MINCMEIOFFSAT;          /* Variable: MINCMEIOFFSAT
                                        * Referenced by: '<S24>/Saturation'
                                        * Saturazione CmeDriver offset
                                        */
extern int16_T VTCMEDPRATESEL[11];     /* Variable: VTCMEDPRATESEL
                                        * Referenced by: '<S29>/VTCMEDPRATESEL'
                                        * Threshold CmeDrivP RateSelector
                                        */
extern int16_T VTRTMAXLORPMCP[11];     /* Variable: VTRTMAXLORPMCP
                                        * Referenced by: '<S47>/VTRTMAXLORPMCP'
                                        * Threshold CmeDrivP to start RateLimiter
                                        */
extern int16_T VTKCMEIVSRB[7];         /* Variable: VTKCMEIVSRB
                                        * Referenced by: '<S24>/VTKCMEIVSRB'
                                        * KVCmeI
                                        */
extern int16_T VTKCMEIVSRBQS[7];       /* Variable: VTKCMEIVSRBQS
                                        * Referenced by: '<S24>/VTKCMEIVSRBQS'
                                        * KVCmeI
                                        */
extern int16_T CMERATEMIN;             /* Variable: CMERATEMIN
                                        * Referenced by: '<S12>/CMERATEMIN'
                                        * Min rate limiter value
                                        */
extern int16_T CMERATEMINGASRESP;      /* Variable: CMERATEMINGASRESP
                                        * Referenced by: '<S28>/CMERATEMINGASRESP'
                                        * Rate min of Gas Resp
                                        */
extern int16_T CMERATERDMODEMAX;       /* Variable: CMERATERDMODEMAX
                                        * Referenced by: '<S12>/CMERATERDMODEMAX'
                                        * CmeRateMax during Eco mode enabling
                                        */
extern int16_T CMERATERDMODEMIN;       /* Variable: CMERATERDMODEMIN
                                        * Referenced by: '<S12>/CMERATERDMODEMIN'
                                        * CmeRateMax during Eco mode enabling
                                        */
extern int16_T THRTMAXLORPMCP;         /* Variable: THRTMAXLORPMCP
                                        * Referenced by: '<S12>/THRTMAXLORPMCP'
                                        * CmeDriverP MaxRate
                                        */
extern int16_T VTCMERATEMAX0[6];       /* Variable: VTCMERATEMAX0
                                        * Referenced by: '<S12>/VTCMERATEMAX0'
                                        * CmeRateMax vector
                                        */
extern int16_T VTCMERATEMAX1[6];       /* Variable: VTCMERATEMAX1
                                        * Referenced by: '<S12>/VTCMERATEMAX1'
                                        * CmeRateMax vector
                                        */
extern int16_T VTCMERATEMAXPBY[7];     /* Variable: VTCMERATEMAXPBY
                                        * Referenced by: '<S12>/VTCMERATEMAXPBY'
                                        * CmeRateMax for pass by
                                        */
extern uint16_T KFCMEDRVGASRESP;       /* Variable: KFCMEDRVGASRESP
                                        * Referenced by: '<S25>/KFCMEDRVGASRESP'
                                        * Kf CmeDriver Gas resp
                                        */
extern uint16_T KFILTCMEINEUTRAL;      /* Variable: KFILTCMEINEUTRAL
                                        * Referenced by: '<S19>/KFILTCMEINEUTRAL'
                                        * KFiltCmeDriverI vector (DRIVEDOWN) in neutral gear
                                        */
extern uint16_T HYSDGASRESP;           /* Variable: HYSDGASRESP
                                        * Referenced by: '<S28>/GasResp_calc'
                                        * GasPosCC hysteresis to return resp
                                        */
extern uint16_T OFFRTMAXLORPMCP;       /* Variable: OFFRTMAXLORPMCP
                                        * Referenced by: '<S47>/OFFRTMAXLORPMCP'
                                        * OFFRTMAXLORPMCP
                                        */
extern uint16_T THCMEDRVCIPF;          /* Variable: THCMEDRVCIPF
                                        * Referenced by: '<S28>/GasResp_calc'
                                        * Threshold CmeDriver to exit gas resp
                                        */
extern uint16_T THDGASRESP;            /* Variable: THDGASRESP
                                        * Referenced by: '<S28>/GasResp_calc'
                                        * GasPosCC threshold to enable resp
                                        */
extern uint16_T CMEGAINGASRESP;        /* Variable: CMEGAINGASRESP
                                        * Referenced by: '<S28>/GasResp_calc'
                                        * Gain of Gas Resp
                                        */
extern uint16_T BKRPMTRQDRIV[11];      /* Variable: BKRPMTRQDRIV
                                        * Referenced by: '<S9>/BKRPMTRQDRIV'
                                        * Rpm breakpoint
                                        */
extern uint16_T TIMENDCIOL;            /* Variable: TIMENDCIOL
                                        * Referenced by: '<S24>/TIMENDCIOL'
                                        * TIMENDCIOL
                                        */
extern uint16_T TIMENDCIOLQS;          /* Variable: TIMENDCIOLQS
                                        * Referenced by: '<S24>/TIMENDCIOLQS'
                                        * TIMENDCIOL
                                        */
extern uint8_T VTKFILTCMEGAIN[6];      /* Variable: VTKFILTCMEGAIN
                                        * Referenced by: '<S18>/VTKFILTCMEGAIN'
                                        * Gain applied to KFilt
                                        */
extern uint8_T VTRATECMEGAIN[6];       /* Variable: VTRATECMEGAIN
                                        * Referenced by: '<S12>/VTRATECMEGAIN'
                                        * Gain applied to Rate Limiter
                                        */
extern uint8_T KFCMEDOWNNEUT0;         /* Variable: KFCMEDOWNNEUT0
                                        * Referenced by: '<S19>/KFCMEDOWNNEUT0'
                                        * KFiltCmeDriverP vector (low torque - neutral)
                                        */
extern uint8_T KFCMEDOWNNEUT1;         /* Variable: KFCMEDOWNNEUT1
                                        * Referenced by: '<S19>/KFCMEDOWNNEUT1'
                                        * KFiltCmeDriverP vector (low torque - eco drive mode - neutral)
                                        */
extern uint8_T KFCMEUPNEUT0;           /* Variable: KFCMEUPNEUT0
                                        * Referenced by: '<S19>/KFCMEUPNEUT0'
                                        * KFiltCmeDriverP vector (high torque - neutral)
                                        */
extern uint8_T KFCMEUPNEUT1;           /* Variable: KFCMEUPNEUT1
                                        * Referenced by: '<S19>/KFCMEUPNEUT1'
                                        * KFiltCmeDriverP vector (high torque - eco drive mode - neutral)
                                        */
extern uint8_T VTKFILTCMEDOWN0[6];     /* Variable: VTKFILTCMEDOWN0
                                        * Referenced by: '<S16>/VTKFILTCMEDOWN0'
                                        * KFiltCmeDriverP vector (low torque)
                                        */
extern uint8_T VTKFILTCMEDOWN1[6];     /* Variable: VTKFILTCMEDOWN1
                                        * Referenced by: '<S16>/VTKFILTCMEDOWN1'
                                        * KFiltCmeDriverP vector (low torque - eco drive mode)
                                        */
extern uint8_T VTKFILTCMEI[11];        /* Variable: VTKFILTCMEI
                                        * Referenced by: '<S16>/VTKFILTCMEI'
                                        * KFiltCmeDriverI vector (DRIVEDOWN)
                                        */
extern uint8_T VTKFILTCMEUP0[6];       /* Variable: VTKFILTCMEUP0
                                        * Referenced by: '<S16>/VTKFILTCMEUP0'
                                        * KFiltCmeDriverP vector (high torque)
                                        */
extern uint8_T VTKFILTCMEUP1[6];       /* Variable: VTKFILTCMEUP1
                                        * Referenced by: '<S16>/VTKFILTCMEUP1'
                                        * KFiltCmeDriverP vector (high torque - eco drive mode)
                                        */
extern uint8_T CNTDUCHANGE;            /* Variable: CNTDUCHANGE
                                        * Referenced by: '<S24>/TrigCL'
                                        * TIMENDCIOL
                                        */
extern uint8_T DIMCMEIWB;              /* Variable: DIMCMEIWB
                                        * Referenced by: '<S24>/CmeDriverI_Offset'
                                        * CmeDriverI shifted
                                        */
extern uint8_T ENFDGASRESP;            /* Variable: ENFDGASRESP
                                        * Referenced by: '<S28>/GasResp_calc'
                                        * Enable Filterig CmeDriver gas resp
                                        */
extern uint8_T NUMDCMEDRIV;            /* Variable: NUMDCMEDRIV
                                        * Referenced by: '<S8>/buffCmeDriver'
                                        * buffer deep
                                        */
extern uint8_T TIMGASRESP;             /* Variable: TIMGASRESP
                                        * Referenced by: '<S28>/GasResp_calc'
                                        * Duration time of return of Gas resp
                                        */
extern uint8_T TIMLIMFILTGC;           /* Variable: TIMLIMFILTGC
                                        * Referenced by: '<S29>/Lim_AfterChange'
                                        * Duration time of limiter GC gear
                                        */
extern void CmeFilterM_FiltGain_Calculation(void);
extern void CmeFilterMg_Filter_RateLim_Init(void);
extern void CmeFilterMgm_Filter_RateLim(void);
extern void CmeFil_RateLim_Calculation_Init(void);
extern void CmeFilterMg_RateLim_Calculation(void);
extern void CmeFilterMgm_T10ms_Init(void);
extern void CmeFilterMgm_T10ms(void);
extern void CmeFilterMgm_Init(void);
extern void CmeFilterMgm_Off(void);

#endif                                 /* RTW_HEADER_CmeFilterMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
