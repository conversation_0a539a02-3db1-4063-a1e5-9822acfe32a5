/*
 * File: IonMisf.h
 *
 * Real-Time Workshop code generated for Simulink model IonMisf.
 *
 * Model version                        : 1.613
 * Real-Time Workshop file version      : 7.0  (R2007b)  02-Aug-2007
 * Real-Time Workshop file generated on : Thu Feb 28 11:29:28 2008
 * TLC version                          : 7.0 (Jul 26 2007)
 * C source code generated on           : Thu Feb 28 11:29:29 2008
 */

#ifndef RTW_HEADER_IonMisf_h_
#define RTW_HEADER_IonMisf_h_
#ifndef IonMisf_COMMON_INCLUDES_
# define IonMisf_COMMON_INCLUDES_
#include <stddef.h>
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* IonMisf_COMMON_INCLUDES_ */

#include "IonMisf_types.h"

/* Includes for objects with custom storage classes. */
#include "ion_misf.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

#define BAD_COMB_INC                   1U
#define BKMISFLOAD_dim                 8U                        /* Length of BKMISFLOAD */
#define BKMISFRPM_dim                  11U                       /* Length of BKMISFRPM */
#define BKTAIRIONMISF_dim              3U                        /* Length of BKTAIRIONMISF */
#define BKTWATIONMISF_dim              5U                        /* Length of BKTWATIONMISF */
#define MAX_MISF                       1U
#define MISF_INC                       0U
#define PAR_MISF_INC                   1U

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  uint_T is_c7_IonMisf : 2;            /* '<S1>/Control_flow' */
  uint_T is_active_c7_IonMisf : 1;     /* '<S1>/Control_flow' */
  uint_T is_active_c1_IonMisf : 1;     /* '<S8>/State_Mgm' */
  uint_T is_c1_IonMisf : 1;            /* '<S8>/State_Mgm' */
} D_Work_IonMisf;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState Control_flow_Trig_ZCE[3]; /* '<S1>/Control_flow' */
} PrevZCSigStates_IonMisf;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_EOA;                      /* '<Root>/ev_EOA' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
} ExternalInputs_IonMisf;

/* Block states (auto storage) */
extern D_Work_IonMisf IonMisf_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_IonMisf IonMisf_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  RTW declares the memory for these signals
 * and exports their symbols.
 *
 */
extern uint16_T MisfThr;               /* '<S8>/Product3'
                                        * Total misfire threshold
                                        */
extern uint16_T BadCombInt;            /* '<S8>/Product'
                                        * Bad Combustion ThInt Threshold
                                        */
extern uint16_T ParMisfInt;            /* '<S8>/Product1'
                                        * Partial Misfire ThInt Threshold
                                        */
extern uint16_T MisfInt;               /* '<S8>/Product2'
                                        * Total Misfire ThInt Threshold
                                        */

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  RTW declares the memory for these states
 * and exports their symbols.
 *
 */
extern uint16_T MisfAbsCnt[8];         /* '<Root>/_DataStoreBlk_3'
                                        * Total Misfire Cylinder Counter
                                        */
extern uint16_T ParMisfAbsCnt[8];      /* '<Root>/_DataStoreBlk_4'
                                        * Partial Misfire Cylinder Counter
                                        */
extern uint16_T BadCombAbsCnt[8];      /* '<Root>/_DataStoreBlk_5'
                                        * Bad Combustion Cylinder Counter
                                        */

/* External data declarations for dependent source files */

/* Zero-crossing (trigger) state */
extern PrevZCSigStates_IonMisf IonMisf_PrevZCSigState;

/* Model entry point functions */
extern void IonMisf_initialize(void);
extern void IonMisf_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('ionmisf_gen/IonMisf')    - opens subsystem ionmisf_gen/IonMisf
 * hilite_system('ionmisf_gen/IonMisf/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : ionmisf_gen
 * '<S1>'   : ionmisf_gen/IonMisf
 * '<S6>'   : ionmisf_gen/IonMisf/Control_flow
 * '<S7>'   : ionmisf_gen/IonMisf/Reset_Variables
 * '<S8>'   : ionmisf_gen/IonMisf/fcn_EOA
 * '<S9>'   : ionmisf_gen/IonMisf/Reset_Variables/Init
 * '<S10>'  : ionmisf_gen/IonMisf/fcn_EOA/Lood2D_TBCORRTHRMISF
 * '<S11>'  : ionmisf_gen/IonMisf/fcn_EOA/Look2D_TBMISFTHR
 * '<S12>'  : ionmisf_gen/IonMisf/fcn_EOA/State_Mgm
 * '<S13>'  : ionmisf_gen/IonMisf/fcn_EOA/Lood2D_TBCORRTHRMISF/Look2D_U16_S16_S16
 * '<S14>'  : ionmisf_gen/IonMisf/fcn_EOA/Lood2D_TBCORRTHRMISF/Look2D_U16_S16_S16/Data Type Conversion Inherited1
 * '<S15>'  : ionmisf_gen/IonMisf/fcn_EOA/Look2D_TBMISFTHR/Look2D_U8_U16_U16
 */

/*
 * Requirements for '<Root>' : IonMisf
 */
#endif                                 /* RTW_HEADER_IonMisf_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
