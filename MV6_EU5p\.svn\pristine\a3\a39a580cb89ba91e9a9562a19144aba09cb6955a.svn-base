/**
 ******************************************************************************
 **  Filename:      RpmLimiter_types.h
 **  Date:          01-Mar-2023
 **
 **  Model Version: 1.2476
 ******************************************************************************
 **/

#ifndef RTW_HEADER_RpmLimiter_types_h_
#define RTW_HEADER_RpmLimiter_types_h_

/*
 * Registered constraints for dimension variants
 */
/* Constraint 'REC_LIMIT_RPM > 0' registered by:
 * '<S3>/Calc_Lim'
 */
#if 0
#if REC_LIMIT_RPM <= 0
# error "The preprocessor definition 'REC_LIMIT_RPM' must be greater than '0'"
#endif

/* Constraint 'REC_LIMIT_RPM < 2147483647' registered by:
 * '<S3>/Calc_Lim'
 */
#if REC_LIMIT_RPM >= 2147483647
# error "The preprocessor definition 'REC_LIMIT_RPM' must be less than '2147483647'"
#endif
#endif
#endif                                 /* RTW_HEADER_RpmLimiter_types_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
