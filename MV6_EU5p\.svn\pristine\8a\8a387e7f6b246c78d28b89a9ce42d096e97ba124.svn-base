/*
 * File: CmiDriverMgm.h
 *
 * Code generated for Simulink model 'CmiDriverMgm'.
 *
 * Model version                  : 1.2254
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 11:51:07 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (22), Warnings (3), Errors (8)
 */

#ifndef RTW_HEADER_CmiDriverMgm_h_
#define RTW_HEADER_CmiDriverMgm_h_
#ifndef CmiDriverMgm_COMMON_INCLUDES_
# define CmiDriverMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#endif                                 /* CmiDriverMgm_COMMON_INCLUDES_ */

#include "CmiDriverMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "cmidriver_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define ID_CMIDRIVER_MGM               20558800U                 /* Referenced by: '<S2>/ID_CMIDRIVER_MGM' */

/* mask */

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
  ZCSigState trig_to_fc_Trig_ZCE;      /* '<S1>/trig_to_fc' */
} PrevZCSigStates_CmiDriverMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PowerOff;                 /* '<Root>/ev_PowerOff' */
} ExternalInputs_CmiDriverMgm;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_CmiDriverMgm CmiDriverMgm_U;

/* Model entry point functions */
extern void CmiDriverMgm_initialize(void);
extern void CmiDriverMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm')    - opens subsystem CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm
 * hilite_system('CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0'
 * '<S1>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm'
 * '<S2>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/Init'
 * '<S3>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/Off'
 * '<S4>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/T10ms'
 * '<S5>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/trig_to_fc'
 * '<S6>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/trig_to_fc1'
 * '<S7>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/trig_to_fc2'
 * '<S8>'   : 'CmiDriverMgm_fxp/CmiDriverMgm/CmiDriverMgm_1_0/CmiDriverMgm/T10ms/Cme2Cmi'
 */

/*-
 * Requirements for '<Root>': CmiDriverMgm
 */
#endif                                 /* RTW_HEADER_CmiDriverMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
