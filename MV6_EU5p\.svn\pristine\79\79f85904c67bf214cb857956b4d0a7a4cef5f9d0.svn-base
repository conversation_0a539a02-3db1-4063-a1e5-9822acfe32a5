/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef __PT_TRAIN_DIAG__
#define __PT_TRAIN_DIAG__

#include "typedefs.h"
#include "PTrain_Diag.h"

#define VEH_STOP    0
#define VEH_WAIT    1
#define VEH_RUN     2

#define BKCOMPREARSPEED_dim  6
#define BKVEHSPMEDIANLEN_dim 4
#define BKKFRPMF2_dim  5
#define BKKFDISTRF2_dim 3

extern uint16_T VehSpeed;
extern uint8_T  FlgVehStop;


void PTDiag_Init (void);
void PTDiag_T10ms (void);
void PTDiag_T100ms (void);

#endif

