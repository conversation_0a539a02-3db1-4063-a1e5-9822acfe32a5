#include "typedefs.h"
#include "syncmgm.h"

#ifdef _BUILD_SYNCMGM_

#ifdef __MWERKS__
#pragma force_active on
#pragma section RW ".calib" ".calib"
#else
#pragma ghs section rodata=".calib"
#endif

/* Flag to enable phase detection at sync event */
__declspec(section ".calib") uint8_T  ENGAPPHASE = 0;
/* PresAtm variation to detect phase */
__declspec(section ".calib") int16_T  DELTAMAPGAPPHASE = 100;

__declspec(section ".calib") uint8_T  DRPMDEEP = 15;
__declspec(section ".calib") uint16_T RPMSYNCLOSS = 200;
__declspec(section ".calib") uint16_T KRPMF = (0.0898*1024);
__declspec(section ".calib") uint32_T NOSYNCTIMEOUT = 500;
__declspec(section ".calib") uint32_T STARTUPBLANKINGPERIOD = INITIAL_STARTUPBLANKINGPERIOD;
__declspec(section ".calib") uint32_T STARTUPSKIPPEDTEETH   = INITIAL_STARTUPSKIPPEDTEETH;
__declspec(section ".calib") uint32_T RATIOSYNCLOW          = INITIAL_RATIOSYNCLOW;
__declspec(section ".calib") uint32_T RATIOSYNCHIGH         = INITIAL_RATIOSYNCHIGH;
__declspec(section ".calib") uint32_T RATIOTOOTHLOW         = INITIAL_RATIOTOOTHLOW;
__declspec(section ".calib") uint32_T RATIOTOOTHHIGH        = INITIAL_RATIOTOOTHHIGH;
__declspec(section ".calib") uint32_T RATIOHOLELOW          = INITIAL_RATIOHOLELOW;
__declspec(section ".calib") uint32_T RATIOHOLEHIGH         = INITIAL_RATIOHOLEHIGH;
__declspec(section ".calib") uint32_T RATIOFIRSTTOOTHLOW    = INITIAL_RATIOFIRSTTOOTHLOW;
__declspec(section ".calib") uint32_T RATIOFIRSTTOOTHHIGH   = INITIAL_RATIOFIRSTTOOTHHIGH;
__declspec(section ".calib") uint32_T BLANKINGPERIOD        = INITIAL_BLANKINGPERIOD;
__declspec(section ".calib") uint32_T STALLPERIOD           = INITIAL_STALL_PERIOD;
__declspec(section ".calib") uint16_T RPMCANERROR = 20000; 
__declspec(section ".calib") uint8_T ENRESYNCVBAT = 0;
__declspec(section ".calib") uint8_T FORCERESYNCVBAT = 0;
__declspec(section ".calib") uint8_T NVBATPHASERETRY = 5;
__declspec(section ".calib") int16_T VBATTHRGROW = 50*1024/5000;
__declspec(section ".calib") int16_T VBATTHRFALL = -100*1024/5000;
__declspec(section ".calib") uint8_T VTNTDCRPMFSTAB[4] =
{
    50, 50, 50, 50
};

__declspec(section ".calib") uint16_T BKRPMFSTAB[4] =
{
    1000, 2000, 3000, 5000
};

__declspec(section ".calib") uint16_T DRPMFSTAB = 100;

#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
__declspec(section ".calib") uint8_T ENGAINHOLETDC = 0;
__declspec(section ".calib") uint8_T RPMGAINHOLETDC = (2040/16); /* scale 2^2 */
__declspec(section ".calib") uint8_T GAINTOOTHTDCSLOW = 4*16; /* scale 2^-4 */
__declspec(section ".calib") uint8_T GAINTOOTHSLOWREF = 0.4*64; /* scale 2^-6 */
__declspec(section ".calib") uint8_T MAXGAINTOOTHTDC = 0.75*16;
__declspec(section ".calib") uint8_T CRANKEVENTDIVIDER = 4;
#endif

#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
__declspec(section ".calib") uint8_T ENGAINHOLETDC = 0;
__declspec(section ".calib") uint8_T RPMGAINHOLETDC = (2040/16); /* scale 2^2 */
__declspec(section ".calib") uint8_T MAXGAINHOLETDC = 0.7*16; /* scale 2^-4 */
__declspec(section ".calib") uint8_T MINGAINTOOTHTDC = 1.2*16; /* scale 2^-4 */
__declspec(section ".calib") uint8_T MAXGAINTOOTHTDC = 0.8*16; /* scale 2^-4 */
__declspec(section ".calib") uint8_T CRANKEVENTDIVIDERP = 4;
__declspec(section ".calib") uint8_T CRANKEVENTDIVIDERN = 4;
#endif

#ifdef ODL_SENSE_LOW
__declspec(section ".calib") uint16_T ODLRPMHIGH = 60000;   //Soglia Rpm per passare alla soglia alta
__declspec(section ".calib") uint16_T ODLRPMLOW =  60000;   //Soglia Rpm per passare alla soglia bassa
#endif

__declspec(section ".calib") uint8_T  MAXCNTEOAMAPBLOCKED =  2;
#ifdef __MWERKS__
#pragma force_active off
#endif

#endif // _BUILD_SYNCMGM_

