@echo off

set TARGET_1=em_3cyl
set TARGET_2=em_4cyl
set TARGET_3=em_3cyl_new
set TARGET_4=em_3cyl_36_2
set TARGET_5=em_4cyl_36_2

if exist "C:\Program Files\Byte Craft\ETPUC\ETPU_C.EXE" set COMPILER="C:\Program Files\Byte Craft\ETPUC\ETPU_C.EXE"
if exist "C:\Programmi\Byte Craft\ETPUC\ETPU_C.EXE" set COMPILER="C:\Programmi\Byte Craft\ETPUC\ETPU_C.EXE"
if exist "C:\Program Files\Byte Craft\ETPUC\BCLinkW.exe" set LINKER="C:\Program Files\Byte Craft\ETPUC\BCLinkW.exe"
if exist "C:\Programmi\Byte Craft\ETPUC\BCLinkW.exe" set LINKER="C:\Programmi\Byte Craft\ETPUC\BCLinkW.exe"

echo Removing old files.
if exist ..\tree\bios\sys\auto\*.* del ..\tree\bios\sys\auto\*.*

goto BUILD_TARGET_1

rem if "%1" == "%TARGET_1%" goto BUILD_TARGET_1
rem if "%1" == "%TARGET_2%" goto BUILD_TARGET_2
rem if "%1" == "%TARGET_3%" goto BUILD_TARGET_3
rem if "%1" == "%TARGET_4%" goto BUILD_TARGET_4

rem if "%1" == "all" goto BUILD_TARGET_1
rem echo Target %1 not supported
rem echo The supported targets are: 
rem echo %TARGET_1%
rem echo %TARGET_2%
rem echo %TARGET_3%
rem echo %TARGET_4%

rem goto EXITING

:BUILD_TARGET_1
rem Building MVAgusta 3cyl eTPU code
echo Building eTPU code for target %TARGET_1%
%COMPILER% ..\etpu\src\ETPU_globals.c       +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PHASE.c         +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\sparkHandler.c       +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\angleClock.c         +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\angleExGenerator.c   +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PULSE.c         +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM.c           +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\adc_trigger.c        +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ion_trigger.c        +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_Delay.c         +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_in.c  +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_out.c +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_dir.c +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_UART.c          +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM_in.c        +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_angTrigger.c    +q -m -i +l +e -x +o -dEM_3CYL n=..\include
%COMPILER% ..\etpu\src\autofilesGenerator.c +q -m -i +l +e -x +o -dEM_3CYL n=..\include

%LINKER% +q -rCOMPILERFULLPATH=%COMPILER% -rTARGET=EM_3CYL synch.lnk

rem if not "%1" == "all" goto EXITING

:BUILD_TARGET_2
rem Building MVAgusta 4cyl eTPU code
echo Building eTPU code for target %TARGET_2%
%COMPILER% ..\etpu\src\ETPU_globals.c       +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PHASE.c         +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\sparkHandler.c       +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\angleClock.c         +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\angleExGenerator.c   +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PULSE.c         +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM.c           +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\adc_trigger.c        +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ion_trigger.c        +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_Delay.c         +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_in.c  +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_out.c +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_dir.c +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_UART.c          +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM_in.c        +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\ETPU_angTrigger.c    +q -m -i +l +e -x +o -dEM_4CYL n=..\include
%COMPILER% ..\etpu\src\autofilesGenerator.c +q -m -i +l +e -x +o -dEM_4CYL n=..\include

%LINKER% +q -rCOMPILERFULLPATH=%COMPILER% -rTARGET=EM_4CYL synch.lnk

rem if not "%1" == "all" goto EXITING

:BUILD_TARGET_3
rem Building MVAgusta 3cyl eTPU code
echo Building eTPU code for target %TARGET_3%
%COMPILER% ..\etpu\src\ETPU_globals.c       +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_PHASE.c         +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\sparkHandler.c       +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\angleClock.c         +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\angleExGenerator.c   +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_PULSE.c         +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM.c           +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\adc_trigger.c        +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ion_trigger.c        +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_Delay.c         +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_in.c  +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_out.c +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_dir.c +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_UART.c          +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM_in.c        +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\ETPU_angTrigger.c    +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include
%COMPILER% ..\etpu\src\autofilesGenerator.c +q -m -i +l +e -x +o -dEM_3CYL_NEW n=..\include

%LINKER% +q -rCOMPILERFULLPATH=%COMPILER% -rTARGET=EM_3CYL_NEW synch.lnk

rem if not "%1" == "all" goto EXITING

:BUILD_TARGET_4
rem Building MVAgusta 3cyl eTPU code
echo Building eTPU code for target %TARGET_4%
%COMPILER% ..\etpu\src\ETPU_globals.c       +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PHASE.c         +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\sparkHandler.c       +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\angleClock.c         +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\angleExGenerator.c   +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PULSE.c         +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM.c           +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\adc_trigger.c        +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ion_trigger.c        +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_Delay.c         +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_in.c  +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_out.c +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_dir.c +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_UART.c          +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM_in.c        +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_angTrigger.c    +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\autofilesGenerator.c +q -m -i +l +e -x +o -dEM_3CYL_36_2 n=..\include

%LINKER% +q -rCOMPILERFULLPATH=%COMPILER% -rTARGET=EM_3CYL_36_2 synch.lnk

rem :EXITING

:BUILD_TARGET_5
rem Building MVAgusta 4cyl eTPU code
echo Building eTPU code for target %TARGET_5%
%COMPILER% ..\etpu\src\ETPU_globals.c       +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PHASE.c         +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\sparkHandler.c       +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\angleClock.c         +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\angleExGenerator.c   +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PULSE.c         +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM.c           +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\adc_trigger.c        +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ion_trigger.c        +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_Delay.c         +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_in.c  +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_out.c +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\FastLinkedChan_dir.c +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_UART.c          +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_PWM_in.c        +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\ETPU_angTrigger.c    +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include
%COMPILER% ..\etpu\src\autofilesGenerator.c +q -m -i +l +e -x +o -dEM_4CYL_36_2 n=..\include

%LINKER% +q -rCOMPILERFULLPATH=%COMPILER% -rTARGET=EM_4CYL_36_2 synch.lnk

rem :EXITING

set TARGET_1=
set TARGET_2=
set TARGET_3=
set TARGET_4=
set COMPILER=
set LINKER=

echo Moving generated files:
if exist *.h move *.h ..\tree\bios\sys\auto\
if exist *.c move *.c ..\tree\bios\sys\auto\

echo Cleaning.
if exist ..\etpu\src\*.obj del ..\etpu\src\*.obj
if exist ..\etpu\src\*.lst del ..\etpu\src\*.lst

echo Done.
echo .
echo WARNING: Verify the SVN green icons for the *.err files.
echo They are placed here and in ..\etpu\src\.
echo If red icons appared for them, the etpu code is not correct.

