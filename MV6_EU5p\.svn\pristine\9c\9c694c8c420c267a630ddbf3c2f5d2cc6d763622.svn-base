/*******************************************************************
 *
 *    DESCRIPTION:
 *
 *    AUTHOR:
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _DIGITALOUT_H_
#define _DIGITALOUT_H_

/** include files **/
#include "typedefs.h"

/** default settings **/

/** external functions **/

/** external data **/

/** internal functions **/

/** public data **/

/** private data **/

/** public functions **/
void DigitalOut_Init(void);
void DigitalOut_T10ms(void);
void DigitalOut_T100ms(void);

/** private functions **/

#endif
