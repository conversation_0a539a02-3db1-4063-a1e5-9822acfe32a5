/*
 * File: Trc2WZero.c
 *
 * Code generated for Simulink model 'Trc2WZero'.
 *
 * Model version                  : 1.252
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Sep 30 10:26:41 2020
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#include "Trc2WZero.h"
#include "Trc2WZero_private.h"

/* Named constants for Chart: '<S26>/TPMS_Ack' */
#define Trc2WZero_IN_NO_ACTIVE_CHILD   ((uint8_T)0U)
#define Trc2WZero_IN_TPMS_ABSENT       ((uint8_T)1U)
#define Trc2WZero_IN_TPMS_ACK          ((uint8_T)2U)
#define Trc2WZero_IN_TPMS_FRONT        ((uint8_T)3U)
#define Trc2WZero_IN_TPMS_INIT         ((uint8_T)4U)
#define Trc2WZero_IN_TPMS_REAR         ((uint8_T)5U)

/* Named constants for Chart: '<S4>/Chart_Trc2WZero' */
#define Trc2WZer_IN_NO_ACTIVE_CHILD_mlj ((uint8_T)0U)
#define Trc2WZer_IN_ST_TC2WZERO_DISABLE ((uint8_T)3U)
#define Trc2WZer_IN_ST_TC2WZERO_STOPPED ((uint8_T)7U)
#define Trc2WZero_IN_ST_TC2WZERO_BUTTON ((uint8_T)1U)
#define Trc2WZero_IN_ST_TC2WZERO_CRUISE ((uint8_T)2U)
#define Trc2WZero_IN_ST_TC2WZERO_END   ((uint8_T)4U)
#define Trc2WZero_IN_ST_TC2WZERO_IDLE  ((uint8_T)5U)
#define Trc2WZero_IN_ST_TC2WZERO_OK    ((uint8_T)6U)
#define Trc2WZero_IN_ST_TC2WZERO_WAIT  ((uint8_T)8U)

/* user code (top of source file) */
/* System '<Root>/Trc2WZero' */
#ifdef _BUILD_TRC2WZERO_

/* Block signals and states (default storage) */
D_Work_Trc2WZero_T Trc2WZero_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_Trc2WZero_T Trc2WZero_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_Trc2WZero_T Trc2WZero_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T CntTc2WZero;

/* counter */
uint8_T EnTC2WZAutoTst;

/* flag */
uint8_T EnTC2WZeroCC;

/* flag */
uint8_T FlgTc2WZeroAck;

/* flag */
uint16_T FrontWheelPressure;

/* pressure */
uint16_T GnVehSpeedFront;

/* VehSpeedFront gain */
uint32_T IDTrc2WZero;

/* ID Version */
uint32_T IntVehSpeedFrontStab;

/* int */
uint32_T IntVehSpeedRearStab;

/* int */
uint16_T RearWheelPressure;

/* pressure */
uint8_T SlTc2W;

/* SL TC2W */
uint8_T SlTcTimeout;

/* timeout */
uint8_T StTPMS;

/* flag */
uint8_T StTc2WZAck1;

/* flag */
uint8_T StTc2WZAck2;

/* flag */
uint8_T StTc2WZAck3;

/* flag */
uint8_T StTc2WZAck4;

/* flag */
uint8_T StTc2WZAck5;

/* flag */
uint8_T StTc2WZAck6;

/* flag */
uint8_T StTc2WZero;

/* flag */
uint8_T TPMSDisable;

/* flag */
uint16_T VehSpeedTC2WZero;

/* Cruise set-point */

/* Forward declaration for local functions */
static void Trc2WZero_TRC_2_W_Z_CONTROL(void);

/* Output and update for function-call system: '<S1>/Init' */
void Trc2WZero_Init(void)
{
  {
    /* user code (Output function Header) */

    /* System '<S1>/Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    Trc2WZero_initialize();

    /* DataStoreRead: '<S2>/Data Store Read3' incorporates:
     *  DataStoreWrite: '<S2>/Data Store Write6'
     */
    GnVehSpeedFront = EEGnVehSpeedFront;

    /* Chart: '<S2>/Sat' incorporates:
     *  DataStoreWrite: '<S2>/Data Store Write6'
     */
    /* Gateway: Trc2WZero/Init/Sat */
    /* During: Trc2WZero/Init/Sat */
    /* Entry Internal: Trc2WZero/Init/Sat */
    /* Transition: '<S8>:2' */
    if ((((uint16_T)TOLL_TC_ZERO_0_98) < GnVehSpeedFront) && (GnVehSpeedFront <
         ((uint16_T)TOLL_TC_ZERO_1_02))) {
      /* Transition: '<S8>:4' */
    } else {
      /* DataStoreWrite: '<S2>/Data Store Write5' */
      /* Transition: '<S8>:5' */
      /*  Init value */
      EEGnVehSpeedFront = DEFTCZERO;
    }

    /* End of Chart: '<S2>/Sat' */

    /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
     *  Constant: '<S2>/ZERO2'
     */
    EnTC2WZAutoTst = 0U;

    /* DataStoreWrite: '<S2>/Data Store Write10' incorporates:
     *  Constant: '<S2>/ZERO8'
     */
    StTc2WZero = 0U;

    /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
     *  Constant: '<S2>/ZERO1'
     */
    SlTc2W = 0U;

    /* DataStoreWrite: '<S2>/Data Store Write3' incorporates:
     *  Constant: '<S2>/ZERO3'
     */
    EnTC2WZeroCC = 0U;

    /* DataStoreWrite: '<S2>/Data Store Write4' incorporates:
     *  Constant: '<S2>/ZERO4'
     */
    FlgTc2WZeroAck = 0U;

    /* Constant: '<S2>/ID_TRC_2W_ZERO' */
    IDTrc2WZero = ID_TRC_2W_ZERO;

    /* user code (Output function Trailer) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Output and update for function-call system: '<S4>/Calc_Ack' */
void Trc2WZero_Calc_Ack(uint8_T rtu_resetAck, uint8_T rtu_GearPos, uint16_T
  rtu_VehSpeed, uint8_T rtu_ClutchSignal, uint16_T rtu_Rpm, uint16_T
  rtu_AbsRollCAN, uint16_T rtu_GasPos, uint16_T rtu_GasPosCC, uint8_T
  rtu_BrakeSignal, uint8_T rtu_BrakeSignalCAN, boolean_T *rty_ackOn, uint8_T
  *rty_ackCond, uint8_T *rty_stabReset, uint16_T *rty_VehSpeedTC2WZero,
  rtDW_Calc_Ack_Trc2WZero_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint8_T rtb_SigStab_o2;
  uint16_T rtb_Memory1;

  /* Logic: '<S15>/Logical Operator' incorporates:
   *  Constant: '<S15>/MAXROLLTC2WZ'
   *  Constant: '<S15>/MAX_PLUS_15'
   *  Constant: '<S15>/THRTC2WZEROHI'
   *  Constant: '<S17>/Constant'
   *  Constant: '<S18>/Constant'
   *  Constant: '<S19>/Constant'
   *  Logic: '<S15>/Logical Operator4'
   *  RelationalOperator: '<S15>/Relational Operator'
   *  RelationalOperator: '<S15>/Relational Operator1'
   *  RelationalOperator: '<S17>/Compare'
   *  RelationalOperator: '<S18>/Compare'
   *  RelationalOperator: '<S19>/Compare'
   *  Sum: '<S15>/Add'
   */
  *rty_ackCond = (uint8_T)((((((rtu_AbsRollCAN <= MAXROLLTC2WZ) && (rtu_GearPos ==
    2)) && (rtu_ClutchSignal != 0)) && (rtu_Rpm != 0)) && ((rtu_BrakeSignal == 0)
    && (rtu_BrakeSignalCAN == 0))) && (rtu_VehSpeed <= ((uint16_T)(THRTC2WZEROHI
    + 240U))));

  /* DataStoreWrite: '<S9>/Data Store Write1' */
  StTc2WZAck1 = *rty_ackCond;

  /* Logic: '<S9>/Logical Operator2' incorporates:
   *  Constant: '<S9>/TOLL'
   *  RelationalOperator: '<S9>/Relational Operator1'
   *  Sum: '<S9>/Add1'
   */
  *rty_stabReset = (uint8_T)(((*rty_ackCond) == 0) || (((uint16_T)(rtu_GasPos +
    12U)) >= rtu_GasPosCC));

  /* DataStoreWrite: '<S9>/Data Store Write5' */
  StTc2WZAck5 = *rty_stabReset;

  /* Switch: '<S23>/Switch' incorporates:
   *  Constant: '<S16>/THRTC2WZEROHI'
   *  Constant: '<S16>/THRTC2WZEROLO'
   *  RelationalOperator: '<S23>/u_GTE_up'
   *  RelationalOperator: '<S23>/u_GT_lo'
   *  Switch: '<S23>/Switch1'
   */
  if (rtu_VehSpeed >= THRTC2WZEROHI) {
    rtb_Memory1 = THRTC2WZEROHI;
  } else if (rtu_VehSpeed > THRTC2WZEROLO) {
    /* Switch: '<S23>/Switch1' */
    rtb_Memory1 = rtu_VehSpeed;
  } else {
    rtb_Memory1 = THRTC2WZEROLO;
  }

  /* End of Switch: '<S23>/Switch' */

  /* RelationalOperator: '<S22>/Compare' incorporates:
   *  DataStoreWrite: '<S16>/Data Store Write2'
   *  Sum: '<S23>/Diff'
   */
  StTc2WZAck2 = (uint8_T)(((uint16_T)(rtu_VehSpeed - rtb_Memory1)) == 0);

  /* Memory: '<S24>/Memory1' */
  rtb_Memory1 = localDW->Memory1_PreviousInput;

  /* S-Function (SigStab): '<S24>/SigStab' incorporates:
   *  Constant: '<S16>/DBTC2WZERO'
   *  Constant: '<S16>/TIMTC2WZSTAB'
   */
  SigStab( &localDW->Memory_PreviousInput_epk, &rtb_SigStab_o2, &rtb_SigStab_o3,
          &rtb_SigStab_o4, rtu_VehSpeed, rtu_resetAck, DBTC2WZERO, TIMTC2WZSTAB,
          localDW->Memory_PreviousInput_epk, rtb_Memory1,
          localDW->Memory_PreviousInput);

  /* DataStoreWrite: '<S16>/Data Store Write3' */
  StTc2WZAck3 = localDW->Memory_PreviousInput_epk;

  /* DataStoreWrite: '<S16>/Data Store Write4' */
  StTc2WZAck4 = rtb_SigStab_o2;

  /* Outputs for Atomic SubSystem: '<S16>/Bit Shift' */
  /* MATLAB Function: '<S21>/bit_shift' incorporates:
   *  Constant: '<S16>/THRTC2WZEROHI'
   *  Constant: '<S16>/THRTC2WZEROLO'
   *  Sum: '<S16>/Add'
   */
  /* MATLAB Function 'Logic and Bit Operations/Bit Shift/bit_shift': '<S25>:1' */
  /* '<S25>:1:8' */
  *rty_VehSpeedTC2WZero = (uint16_T)(((uint32_T)((uint16_T)(((uint32_T)
    THRTC2WZEROLO) + THRTC2WZEROHI))) >> 1);

  /* End of Outputs for SubSystem: '<S16>/Bit Shift' */

  /* Logic: '<S9>/Logical Operator3' incorporates:
   *  DataStoreWrite: '<S16>/Data Store Write2'
   *  Logic: '<S16>/Logical Operator1'
   */
  *rty_ackOn = (((StTc2WZAck2 != 0) && (localDW->Memory_PreviousInput_epk != 0))
                && ((*rty_ackCond) != 0));

  /* Update for Memory: '<S16>/Memory' */
  localDW->Memory_PreviousInput_epk = rtb_SigStab_o2;

  /* Update for Memory: '<S24>/Memory1' */
  localDW->Memory1_PreviousInput = rtb_SigStab_o3;

  /* Update for Memory: '<S24>/Memory' */
  localDW->Memory_PreviousInput = rtb_SigStab_o4;
}

/* System initialize for function-call system: '<S4>/Calc_Diag' */
void Trc2WZero_Calc_Diag_Init(rtDW_Calc_Diag_Trc2WZero_T *localDW)
{
  /* SystemInitialize for Chart: '<S26>/TPMS_Ack' */
  localDW->is_active_c4_Trc2WZero = 0U;
  localDW->is_c4_Trc2WZero = Trc2WZero_IN_NO_ACTIVE_CHILD;
  localDW->cntNode = 0U;
  TPMSDisable = 0U;
}

/* Output and update for function-call system: '<S4>/Calc_Diag' */
void Trc2WZero_Calc_Diag(const uint8_T rtu_StDiag[80], uint8_T rtu_TPMSPre,
  uint8_T rtu_TPMSLoc, uint8_T rtu_TPMSSda, uint8_T rtu_TPMSRst, uint8_T
  rtu_TPMSWfc, uint8_T rtu_TPMSNode, uint8_T rtu_FlgYawRec, boolean_T *rty_diag,
  rtDW_Calc_Diag_Trc2WZero_T *localDW)
{
  uint16_T rtb_Product;
  int32_T tmp;

  /* Product: '<S26>/Product' incorporates:
   *  Constant: '<S26>/PRES_WHEEL_K_CONV'
   */
  rtb_Product = (uint16_T)(((uint32_T)rtu_TPMSPre) * ((uint8_T)PRES_WHEEL_K_CONV));

  /* Chart: '<S26>/TPMS_Ack' */
  /* Gateway: Trc2WZero/T10ms/Calc_Diag/Calc_TPMS/TPMS_Ack */
  /* During: Trc2WZero/T10ms/Calc_Diag/Calc_TPMS/TPMS_Ack */
  if (localDW->is_active_c4_Trc2WZero == 0U) {
    /* Entry: Trc2WZero/T10ms/Calc_Diag/Calc_TPMS/TPMS_Ack */
    localDW->is_active_c4_Trc2WZero = 1U;

    /* Entry Internal: Trc2WZero/T10ms/Calc_Diag/Calc_TPMS/TPMS_Ack */
    /* Transition: '<S32>:2' */
    localDW->cntNode = 0U;
    TPMSDisable = 1U;
    StTPMS = ((uint8_T)TPMS_INIT);
    localDW->is_c4_Trc2WZero = Trc2WZero_IN_TPMS_INIT;
  } else {
    switch (localDW->is_c4_Trc2WZero) {
     case Trc2WZero_IN_TPMS_ABSENT:
      /* During 'TPMS_ABSENT': '<S32>:47' */
      /* Transition: '<S32>:54' */
      /* Transition: '<S32>:53' */
      break;

     case Trc2WZero_IN_TPMS_ACK:
      /* During 'TPMS_ACK': '<S32>:17' */
      /* Transition: '<S32>:28' */
      /* Transition: '<S32>:29' */
      localDW->cntNode = 0U;
      StTPMS = ((uint8_T)TPMS_INIT);
      localDW->is_c4_Trc2WZero = Trc2WZero_IN_TPMS_INIT;
      break;

     case Trc2WZero_IN_TPMS_FRONT:
      /* During 'TPMS_FRONT': '<S32>:13' */
      /* Transition: '<S32>:15' */
      if ((((rtu_TPMSLoc == ((uint8_T)FRONT_WHEEL)) && (rtu_TPMSWfc < 4)) &&
           (rtu_TPMSRst == 0)) && (rtu_TPMSSda == 0)) {
        /* Transition: '<S32>:18' */
        FrontWheelPressure = rtb_Product;
        if (rtb_Product >= ((uint16_T)TPMS_PRE_150KP)) {
          /* Transition: '<S32>:26' */
          TPMSDisable = 0U;
          StTPMS = ((uint8_T)TPMS_ACK);
          localDW->is_c4_Trc2WZero = Trc2WZero_IN_TPMS_ACK;
        } else {
          /* Transition: '<S32>:27' */
          TPMSDisable = 1U;
        }
      } else {
        /* Transition: '<S32>:16' */
      }
      break;

     case Trc2WZero_IN_TPMS_INIT:
      /* During 'TPMS_INIT': '<S32>:1' */
      /* Transition: '<S32>:4' */
      if (rtu_TPMSNode != 0) {
        /* Transition: '<S32>:7' */
        StTPMS = ((uint8_T)TPMS_REAR);
        localDW->is_c4_Trc2WZero = Trc2WZero_IN_TPMS_REAR;
      } else {
        /* Transition: '<S32>:5' */
        if (localDW->cntNode > TIMTPMSNODE) {
          /* Transition: '<S32>:50' */
          TPMSDisable = 0U;
          StTPMS = ((uint8_T)TPMS_ABSENT);
          localDW->is_c4_Trc2WZero = Trc2WZero_IN_TPMS_ABSENT;
        } else {
          /* Transition: '<S32>:49' */
          tmp = localDW->cntNode + 1;
          if (tmp > 65535) {
            tmp = 65535;
          }

          localDW->cntNode = (uint16_T)tmp;
        }
      }
      break;

     default:
      /* During 'TPMS_REAR': '<S32>:6' */
      /* Transition: '<S32>:9' */
      if ((((rtu_TPMSLoc == ((uint8_T)REAR_WHEEL)) && (rtu_TPMSWfc < 4)) &&
           (rtu_TPMSRst == 0)) && (rtu_TPMSSda == 0)) {
        /* Transition: '<S32>:12' */
        RearWheelPressure = rtb_Product;
        if (rtb_Product >= ((uint16_T)TPMS_PRE_150KP)) {
          /* Transition: '<S32>:23' */
          StTPMS = ((uint8_T)TPMS_FRONT);
          localDW->is_c4_Trc2WZero = Trc2WZero_IN_TPMS_FRONT;
        } else {
          /* Transition: '<S32>:24' */
          TPMSDisable = 1U;
        }
      } else {
        /* Transition: '<S32>:10' */
      }
      break;
    }
  }

  /* End of Chart: '<S26>/TPMS_Ack' */

  /* Logic: '<S10>/Logical Operator' incorporates:
   *  Constant: '<S10>/DIAG_CLUTCH'
   *  Constant: '<S10>/DIAG_GEAR_SENSOR'
   *  Constant: '<S10>/DIAG_VEHSPEED'
   *  Constant: '<S10>/DIAG_VEHSPEED_FRONT'
   *  Constant: '<S27>/Constant'
   *  Constant: '<S28>/Constant'
   *  Constant: '<S29>/Constant'
   *  Constant: '<S30>/Constant'
   *  Constant: '<S31>/Constant'
   *  RelationalOperator: '<S27>/Compare'
   *  RelationalOperator: '<S28>/Compare'
   *  RelationalOperator: '<S29>/Compare'
   *  RelationalOperator: '<S30>/Compare'
   *  RelationalOperator: '<S31>/Compare'
   *  Selector: '<S10>/Selector'
   *  Selector: '<S10>/Selector1'
   *  Selector: '<S10>/Selector2'
   *  Selector: '<S10>/Selector3'
   */
  *rty_diag = ((((((TPMSDisable != 0) || (rtu_FlgYawRec == 1)) ||
                  (rtu_StDiag[DIAG_VEHSPEED] == FAULT)) ||
                 (rtu_StDiag[DIAG_VEHSPEED_FRONT] == FAULT)) ||
                (rtu_StDiag[DIAG_GEAR_SENSOR] == FAULT)) ||
               (rtu_StDiag[DIAG_CLUTCH] == FAULT));
}

/* Output and update for function-call system: '<S4>/Calc_VSFGain' */
void Trc2WZero_Calc_VSFGain(uint8_T rtu_resetOff, uint8_T rtu_stabReset,
  uint16_T rtu_VehSpeedRear, uint16_T rtu_VehSpeedFrontNz,
  rtDW_Calc_VSFGain_Trc2WZero_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3_j1t;
  uint16_T rtb_SigStab_o4_hgq;
  uint8_T rtb_SigStab_o2_eh1;
  uint8_T rtb_Switch1_bqk;
  uint8_T rtb_ok;
  uint16_T rtb_Add2;
  uint32_T tmp;

  /* Switch: '<S36>/Switch3' incorporates:
   *  Constant: '<S36>/ZERO1'
   *  Constant: '<S41>/Constant'
   *  DataStoreWrite: '<S36>/Data Store Write5'
   *  RelationalOperator: '<S41>/Compare'
   *  Sum: '<S36>/Add4'
   */
  if (localDW->Memory_PreviousInput_d5y != 2) {
    IntVehSpeedRearStab = 0U;
  }

  IntVehSpeedRearStab = IntVehSpeedRearStab + (((uint32_T)rtu_VehSpeedRear) <<
    10);

  /* End of Switch: '<S36>/Switch3' */

  /* Switch: '<S36>/Switch2' incorporates:
   *  Constant: '<S36>/ZERO'
   *  Constant: '<S40>/Constant'
   *  DataStoreWrite: '<S36>/Data Store Write3'
   *  RelationalOperator: '<S40>/Compare'
   *  Sum: '<S36>/Add1'
   */
  if (localDW->Memory_PreviousInput_d5y != 2) {
    IntVehSpeedFrontStab = 0U;
  }

  IntVehSpeedFrontStab = IntVehSpeedFrontStab + rtu_VehSpeedFrontNz;

  /* End of Switch: '<S36>/Switch2' */

  /* Sum: '<S12>/Add2' incorporates:
   *  Constant: '<S12>/WorkAround_U16_SIG_STAB'
   *  Sum: '<S12>/Add'
   */
  rtb_Add2 = (uint16_T)(((int16_T)(rtu_VehSpeedFrontNz - rtu_VehSpeedRear)) +
                        1600);

  /* DataTypeConversion: '<S39>/Conversion4' incorporates:
   *  Logic: '<S12>/Logical Operator1'
   */
  rtb_Switch1_bqk = (uint8_T)((rtu_resetOff != 0) || (rtu_stabReset != 0));

  /* S-Function (SigStab): '<S39>/SigStab' incorporates:
   *  Constant: '<S12>/DBTC2WZERO2'
   *  Constant: '<S12>/TIMTC2WZSTAB2'
   */
  SigStab( &rtb_Switch1_bqk, &rtb_SigStab_o2_eh1, &rtb_SigStab_o3_j1t,
          &rtb_SigStab_o4_hgq, rtb_Add2, rtb_Switch1_bqk, DBTC2WZERO2,
          TIMTC2WZSTAB2, localDW->Memory_PreviousInput_d5y,
          localDW->Memory1_PreviousInput, localDW->Memory_PreviousInput);

  /* Logic: '<S12>/Logical Operator' incorporates:
   *  Constant: '<S37>/Constant'
   *  RelationalOperator: '<S37>/Compare'
   */
  rtb_Switch1_bqk = (uint8_T)((rtb_Switch1_bqk != 0) &&
    (localDW->Memory_PreviousInput_d5y == 2));

  /* Switch: '<S12>/Switch' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read3'
   *  DataStoreWrite: '<S12>/Data Store Write3'
   *  Product: '<S36>/Divide'
   */
  if (rtb_Switch1_bqk != 0) {
    /* Product: '<S36>/Divide' incorporates:
     *  DataStoreWrite: '<S36>/Data Store Write3'
     *  DataStoreWrite: '<S36>/Data Store Write5'
     */
    tmp = IntVehSpeedRearStab / IntVehSpeedFrontStab;
    if (tmp > 65535U) {
      tmp = 65535U;
    }

    GnVehSpeedFront = (uint16_T)tmp;
  } else {
    GnVehSpeedFront = EEGnVehSpeedFront;
  }

  /* End of Switch: '<S12>/Switch' */

  /* Chart: '<S12>/Sat' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write3'
   */
  /* Gateway: Trc2WZero/T10ms/Calc_VSFGain/Sat */
  /* During: Trc2WZero/T10ms/Calc_VSFGain/Sat */
  /* Entry Internal: Trc2WZero/T10ms/Calc_VSFGain/Sat */
  /* Transition: '<S38>:17' */
  if (GnVehSpeedFront < EEGnVehSpeedFrontMin) {
    /* Transition: '<S38>:16' */
    EEGnVehSpeedFrontMin = GnVehSpeedFront;
  } else {
    /* Transition: '<S38>:18' */
  }

  if (GnVehSpeedFront > EEGnVehSpeedFrontMax) {
    /* Transition: '<S38>:13' */
    EEGnVehSpeedFrontMax = GnVehSpeedFront;
  } else {
    /* Transition: '<S38>:14' */
  }

  if ((TOLLTCZEROMIN < GnVehSpeedFront) && (GnVehSpeedFront < TOLLTCZEROMAX)) {
    /* DataStoreWrite: '<S12>/Data Store Write2' */
    /* Transition: '<S38>:4' */
    EEGnVehSpeedFront = GnVehSpeedFront;
    rtb_ok = 1U;
  } else {
    /* Transition: '<S38>:5' */
    rtb_ok = 0U;
  }

  /* End of Chart: '<S12>/Sat' */

  /* Switch: '<S12>/Switch1' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read1'
   */
  if (FlgTc2WZeroAck != 0) {
  } else {
    /* DataStoreWrite: '<S12>/Data Store Write1' incorporates:
     *  Logic: '<S12>/Logical Operator2'
     */
    FlgTc2WZeroAck = (uint8_T)((rtb_ok != 0) && (rtb_Switch1_bqk != 0));
  }

  /* End of Switch: '<S12>/Switch1' */

  /* DataStoreWrite: '<S12>/Data Store Write4' */
  StTc2WZAck6 = localDW->Memory_PreviousInput_d5y;

  /* Update for Memory: '<S12>/Memory' */
  localDW->Memory_PreviousInput_d5y = rtb_SigStab_o2_eh1;

  /* Update for Memory: '<S39>/Memory1' */
  localDW->Memory1_PreviousInput = rtb_SigStab_o3_j1t;

  /* Update for Memory: '<S39>/Memory' */
  localDW->Memory_PreviousInput = rtb_SigStab_o4_hgq;
}

/* Function for Chart: '<S4>/Chart_Trc2WZero' */
static void Trc2WZero_TRC_2_W_Z_CONTROL(void)
{
  int32_T tmp;
  int32_T entryg1;
  int32_T exitg2;
  boolean_T guard1 = false;
  boolean_T guard2 = false;

  /* During 'TRC_2_W_Z_CONTROL': '<S13>:86' */
  guard1 = false;
  switch (Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL) {
   case Trc2WZero_IN_ST_TC2WZERO_BUTTON:
    /* Inport: '<Root>/TCSetupRequest' */
    /* During 'ST_TC2WZERO_BUTTON': '<S13>:5' */
    /* Transition: '<S13>:9' */
    if ((TCSetupRequest == 2) || (EnTC2WZAutoTst != 0)) {
      /* Transition: '<S13>:11' */
      StTc2WZero = 2U;
      Trc2WZero_DWork.SlTc2W_ml3 = 1U;
      Trc2WZero_DWork.resetAck = 1U;

      /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
      /* Inport: '<Root>/StDiag' incorporates:
       *  Inport: '<Root>/FlgYawRec'
       *  Inport: '<Root>/TPMSLoc'
       *  Inport: '<Root>/TPMSNode'
       *  Inport: '<Root>/TPMSPre'
       *  Inport: '<Root>/TPMSRst'
       *  Inport: '<Root>/TPMSSda'
       *  Inport: '<Root>/TPMSWfc'
       */
      /* Event: '<S13>:59' */
      Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                          TPMSWfc, TPMSNode, FlgYawRec,
                          &Trc2WZero_DWork.LogicalOperator_noc,
                          &Trc2WZero_DWork.Calc_Diag);

      /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */

      /* Outputs for Function Call SubSystem: '<S4>/Calc_Ack' */
      /* Inport: '<Root>/GearPos' incorporates:
       *  Inport: '<Root>/AbsRollCAN'
       *  Inport: '<Root>/BrakeSignal'
       *  Inport: '<Root>/BrakeSignalCAN'
       *  Inport: '<Root>/ClutchSignal'
       *  Inport: '<Root>/GasPos'
       *  Inport: '<Root>/GasPosCC'
       *  Inport: '<Root>/Rpm'
       *  Inport: '<Root>/VehSpeed'
       */
      /* Event: '<S13>:60' */
      Trc2WZero_Calc_Ack(Trc2WZero_DWork.resetAck, GearPos, VehSpeed,
                         ClutchSignal, Rpm, AbsRollCAN, GasPos, GasPosCC,
                         BrakeSignal, BrakeSignalCAN,
                         &Trc2WZero_DWork.LogicalOperator3,
                         &Trc2WZero_DWork.LogicalOperator,
                         &Trc2WZero_DWork.LogicalOperator2, &Trc2WZero_DWork.y,
                         &Trc2WZero_DWork.Calc_Ack);

      /* End of Outputs for SubSystem: '<S4>/Calc_Ack' */
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_WAIT;
    } else {
      /* Transition: '<S13>:14' */
      if ((TCSetupRequest == 0) && (EnTC2WZAutoTst == 0)) {
        /* Transition: '<S13>:15' */
        StTc2WZero = 0U;
        Trc2WZero_DWork.SlTc2W_ml3 = 0U;
        Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_IDLE;
      } else {
        /* Transition: '<S13>:16' */
        StTc2WZero = 1U;
        Trc2WZero_DWork.SlTc2W_ml3 = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
        /* Inport: '<Root>/StDiag' incorporates:
         *  Inport: '<Root>/FlgYawRec'
         *  Inport: '<Root>/TPMSLoc'
         *  Inport: '<Root>/TPMSNode'
         *  Inport: '<Root>/TPMSPre'
         *  Inport: '<Root>/TPMSRst'
         *  Inport: '<Root>/TPMSSda'
         *  Inport: '<Root>/TPMSWfc'
         */
        /* Event: '<S13>:59' */
        Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                            TPMSWfc, TPMSNode, FlgYawRec,
                            &Trc2WZero_DWork.LogicalOperator_noc,
                            &Trc2WZero_DWork.Calc_Diag);

        /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */
      }
    }
    break;

   case Trc2WZero_IN_ST_TC2WZERO_CRUISE:
    /* During 'ST_TC2WZERO_CRUISE': '<S13>:19' */
    /* Transition: '<S13>:31' */
    Trc2WZero_DWork.EnTC2WZeroCC_e4f = 1U;

    /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
    /* Inport: '<Root>/StDiag' incorporates:
     *  Inport: '<Root>/FlgYawRec'
     *  Inport: '<Root>/TPMSLoc'
     *  Inport: '<Root>/TPMSNode'
     *  Inport: '<Root>/TPMSPre'
     *  Inport: '<Root>/TPMSRst'
     *  Inport: '<Root>/TPMSSda'
     *  Inport: '<Root>/TPMSWfc'
     */
    /* Event: '<S13>:59' */
    Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                        TPMSWfc, TPMSNode, FlgYawRec,
                        &Trc2WZero_DWork.LogicalOperator_noc,
                        &Trc2WZero_DWork.Calc_Diag);

    /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */

    /* Outputs for Function Call SubSystem: '<S4>/Calc_Ack' */
    /* Inport: '<Root>/GearPos' incorporates:
     *  Inport: '<Root>/AbsRollCAN'
     *  Inport: '<Root>/BrakeSignal'
     *  Inport: '<Root>/BrakeSignalCAN'
     *  Inport: '<Root>/ClutchSignal'
     *  Inport: '<Root>/GasPos'
     *  Inport: '<Root>/GasPosCC'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/VehSpeed'
     */
    /* Event: '<S13>:60' */
    Trc2WZero_Calc_Ack(Trc2WZero_DWork.resetAck, GearPos, VehSpeed, ClutchSignal,
                       Rpm, AbsRollCAN, GasPos, GasPosCC, BrakeSignal,
                       BrakeSignalCAN, &Trc2WZero_DWork.LogicalOperator3,
                       &Trc2WZero_DWork.LogicalOperator,
                       &Trc2WZero_DWork.LogicalOperator2, &Trc2WZero_DWork.y,
                       &Trc2WZero_DWork.Calc_Ack);

    /* End of Outputs for SubSystem: '<S4>/Calc_Ack' */

    /* Outputs for Function Call SubSystem: '<S4>/Calc_VSFGain' */
    /* Inport: '<Root>/VehSpeedRear' incorporates:
     *  Inport: '<Root>/VehSpeedFrontNz'
     */
    /* Event: '<S13>:61' */
    Trc2WZero_Calc_VSFGain(0, Trc2WZero_DWork.LogicalOperator2, VehSpeedRear,
      VehSpeedFrontNz, &Trc2WZero_DWork.Calc_VSFGain);

    /* End of Outputs for SubSystem: '<S4>/Calc_VSFGain' */
    if ((((TCSetupRequest == 5) && (EnTC2WZAutoTst == 0)) ||
         (Trc2WZero_DWork.LogicalOperator_noc)) ||
        (Trc2WZero_DWork.LogicalOperator == 0)) {
      /* Transition: '<S13>:34' */
      Trc2WZero_DWork.EnTC2WZeroCC_e4f = 0U;
      entryg1 = 1;
      guard1 = true;
    } else {
      /* Transition: '<S13>:37' */
      if (SlTcTimeout == 0) {
        /* Transition: '<S13>:38' */
        Trc2WZero_DWork.EnTC2WZeroCC_e4f = 0U;
        entryg1 = 0;
        guard1 = true;
      } else {
        /* Transition: '<S13>:39' */
        StTc2WZero = 3U;
        Trc2WZero_DWork.SlTc2W_ml3 = 2U;
        if ((Trc2WZero_DWork.LogicalOperator2 == 0) ||
            (Trc2WZero_DWork.interlock == 0)) {
          /* Transition: '<S13>:80' */
          Trc2WZero_DWork.interlock = 0U;
          if (CntTc2WZero > 99) {
            /* Transition: '<S13>:76' */
            CntTc2WZero = 0U;
            tmp = SlTcTimeout - 1;
            if (tmp < 0) {
              tmp = 0;
            }

            SlTcTimeout = (uint8_T)tmp;
          } else {
            /* Transition: '<S13>:77' */
            tmp = CntTc2WZero + 1;
            if (tmp > 255) {
              tmp = 255;
            }

            CntTc2WZero = (uint8_T)tmp;
          }
        } else {
          /* Transition: '<S13>:81' */
        }
      }
    }
    break;

   case Trc2WZer_IN_ST_TC2WZERO_DISABLE:
    /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
    /* Inport: '<Root>/StDiag' incorporates:
     *  Inport: '<Root>/FlgYawRec'
     *  Inport: '<Root>/TPMSLoc'
     *  Inport: '<Root>/TPMSNode'
     *  Inport: '<Root>/TPMSPre'
     *  Inport: '<Root>/TPMSRst'
     *  Inport: '<Root>/TPMSSda'
     *  Inport: '<Root>/TPMSWfc'
     */
    /* During 'ST_TC2WZERO_DISABLE': '<S13>:103' */
    /* Transition: '<S13>:109' */
    /* Event: '<S13>:59' */
    Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                        TPMSWfc, TPMSNode, FlgYawRec,
                        &Trc2WZero_DWork.LogicalOperator_noc,
                        &Trc2WZero_DWork.Calc_Diag);

    /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */
    if (!Trc2WZero_DWork.LogicalOperator_noc) {
      /* Transition: '<S13>:111' */
      StTc2WZero = 0U;
      Trc2WZero_DWork.SlTc2W_ml3 = 0U;
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_IDLE;
    } else {
      /* Transition: '<S13>:110' */
    }
    break;

   case Trc2WZero_IN_ST_TC2WZERO_END:
    /* Inport: '<Root>/TCSetupRequest' */
    /* During 'ST_TC2WZERO_END': '<S13>:43' */
    /* Transition: '<S13>:48' */
    if (((TCSetupRequest == 0) && (ENTC2WZERORET != 0)) || (EnTC2WZAutoTst != 0))
    {
      /* Transition: '<S13>:50' */
      StTc2WZero = 0U;
      Trc2WZero_DWork.SlTc2W_ml3 = 0U;
      EnTC2WZAutoTst = 0U;
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_IDLE;
    } else {
      /* Transition: '<S13>:49' */
      StTc2WZero = 6U;
      Trc2WZero_DWork.SlTc2W_ml3 = 0U;
      Trc2WZero_DWork.EnTC2WZeroCC_e4f = 0U;
    }
    break;

   case Trc2WZero_IN_ST_TC2WZERO_IDLE:
    /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
    /* Inport: '<Root>/StDiag' incorporates:
     *  Inport: '<Root>/FlgYawRec'
     *  Inport: '<Root>/TPMSLoc'
     *  Inport: '<Root>/TPMSNode'
     *  Inport: '<Root>/TPMSPre'
     *  Inport: '<Root>/TPMSRst'
     *  Inport: '<Root>/TPMSSda'
     *  Inport: '<Root>/TPMSWfc'
     */
    /* During 'ST_TC2WZERO_IDLE': '<S13>:1' */
    /* Transition: '<S13>:106' */
    /* Event: '<S13>:59' */
    Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                        TPMSWfc, TPMSNode, FlgYawRec,
                        &Trc2WZero_DWork.LogicalOperator_noc,
                        &Trc2WZero_DWork.Calc_Diag);

    /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */
    if (Trc2WZero_DWork.LogicalOperator_noc) {
      /* Transition: '<S13>:107' */
      StTc2WZero = 7U;
      Trc2WZero_DWork.SlTc2W_ml3 = 5U;
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZer_IN_ST_TC2WZERO_DISABLE;
    } else {
      /* Inport: '<Root>/TCSetupRequest' */
      /* Transition: '<S13>:4' */
      if ((TCSetupRequest != 0) || (EnTC2WZAutoTst != 0)) {
        /* Transition: '<S13>:6' */
        StTc2WZero = 1U;
        Trc2WZero_DWork.SlTc2W_ml3 = 0U;
        Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_BUTTON;
      } else {
        /* Transition: '<S13>:7' */
        StTc2WZero = 0U;
        Trc2WZero_DWork.SlTc2W_ml3 = 0U;
        Trc2WZero_DWork.EnTC2WZeroCC_e4f = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Calc_Tst' */
        /* Logic: '<S11>/Logical Operator1' incorporates:
         *  Constant: '<S11>/FORCETC2WZTST'
         *  Constant: '<S33>/Constant'
         *  Constant: '<S35>/Constant'
         *  DataStoreWrite: '<S11>/Data Store Write2'
         *  Inport: '<Root>/GearPos'
         *  Inport: '<Root>/VehSpeed'
         *  RelationalOperator: '<S33>/Compare'
         *  RelationalOperator: '<S34>/Compare'
         *  RelationalOperator: '<S35>/Compare'
         */
        /* Event: '<S13>:71' */
        EnTC2WZAutoTst = (uint8_T)(((FORCETC2WZTST != 0) && (GearPos == 0)) &&
          (VehSpeed == 0));

        /* End of Outputs for SubSystem: '<S4>/Calc_Tst' */
      }
    }
    break;

   case Trc2WZero_IN_ST_TC2WZERO_OK:
    /* Inport: '<Root>/TCSetupRequest' */
    /* During 'ST_TC2WZERO_OK': '<S13>:32' */
    /* Transition: '<S13>:41' */
    if ((TCSetupRequest == 6) || (EnTC2WZAutoTst != 0)) {
      /* Transition: '<S13>:45' */
      StTc2WZero = 6U;
      Trc2WZero_DWork.SlTc2W_ml3 = 0U;

      /* Transition: '<S13>:46' */
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_END;
    } else {
      /* Transition: '<S13>:42' */
      StTc2WZero = 5U;
      Trc2WZero_DWork.SlTc2W_ml3 = 3U;
    }
    break;

   case Trc2WZer_IN_ST_TC2WZERO_STOPPED:
    /* Inport: '<Root>/TCSetupRequest' */
    /* During 'ST_TC2WZERO_STOPPED': '<S13>:21' */
    /* Transition: '<S13>:27' */
    if ((TCSetupRequest == 0) || (EnTC2WZAutoTst != 0)) {
      /* Transition: '<S13>:28' */
      StTc2WZero = 0U;
      Trc2WZero_DWork.SlTc2W_ml3 = 0U;
      EnTC2WZAutoTst = 0U;
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_IDLE;
    } else {
      /* Transition: '<S13>:29' */
      StTc2WZero = 4U;
      Trc2WZero_DWork.SlTc2W_ml3 = 4U;
    }
    break;

   default:
    /* During 'ST_TC2WZERO_WAIT': '<S13>:10' */
    /* Transition: '<S13>:18' */
    if ((!Trc2WZero_DWork.LogicalOperator_noc) &&
        (Trc2WZero_DWork.LogicalOperator3)) {
      /* Transition: '<S13>:20' */
      StTc2WZero = 3U;
      Trc2WZero_DWork.SlTc2W_ml3 = 2U;
      SlTcTimeout = TIMTC2WZERO;
      CntTc2WZero = 0U;
      FlgTc2WZeroAck = 0U;
      Trc2WZero_DWork.interlock = 1U;

      /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
      /* Inport: '<Root>/StDiag' incorporates:
       *  Inport: '<Root>/FlgYawRec'
       *  Inport: '<Root>/TPMSLoc'
       *  Inport: '<Root>/TPMSNode'
       *  Inport: '<Root>/TPMSPre'
       *  Inport: '<Root>/TPMSRst'
       *  Inport: '<Root>/TPMSSda'
       *  Inport: '<Root>/TPMSWfc'
       */
      /* Event: '<S13>:59' */
      Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                          TPMSWfc, TPMSNode, FlgYawRec,
                          &Trc2WZero_DWork.LogicalOperator_noc,
                          &Trc2WZero_DWork.Calc_Diag);

      /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */

      /* Outputs for Function Call SubSystem: '<S4>/Calc_Ack' */
      /* Inport: '<Root>/GearPos' incorporates:
       *  Inport: '<Root>/AbsRollCAN'
       *  Inport: '<Root>/BrakeSignal'
       *  Inport: '<Root>/BrakeSignalCAN'
       *  Inport: '<Root>/ClutchSignal'
       *  Inport: '<Root>/GasPos'
       *  Inport: '<Root>/GasPosCC'
       *  Inport: '<Root>/Rpm'
       *  Inport: '<Root>/VehSpeed'
       */
      /* Event: '<S13>:60' */
      Trc2WZero_Calc_Ack(Trc2WZero_DWork.resetAck, GearPos, VehSpeed,
                         ClutchSignal, Rpm, AbsRollCAN, GasPos, GasPosCC,
                         BrakeSignal, BrakeSignalCAN,
                         &Trc2WZero_DWork.LogicalOperator3,
                         &Trc2WZero_DWork.LogicalOperator,
                         &Trc2WZero_DWork.LogicalOperator2, &Trc2WZero_DWork.y,
                         &Trc2WZero_DWork.Calc_Ack);

      /* End of Outputs for SubSystem: '<S4>/Calc_Ack' */

      /* Outputs for Function Call SubSystem: '<S4>/Calc_VSFGain' */
      /* Inport: '<Root>/VehSpeedRear' incorporates:
       *  Inport: '<Root>/VehSpeedFrontNz'
       */
      /* Event: '<S13>:61' */
      Trc2WZero_Calc_VSFGain(1, Trc2WZero_DWork.LogicalOperator2, VehSpeedRear,
        VehSpeedFrontNz, &Trc2WZero_DWork.Calc_VSFGain);

      /* End of Outputs for SubSystem: '<S4>/Calc_VSFGain' */
      Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_CRUISE;
    } else {
      /* Inport: '<Root>/TCSetupRequest' */
      /* Transition: '<S13>:24' */
      if (((TCSetupRequest != 2) && (EnTC2WZAutoTst == 0)) ||
          (Trc2WZero_DWork.LogicalOperator_noc)) {
        /* Transition: '<S13>:22' */
        StTc2WZero = 4U;
        Trc2WZero_DWork.SlTc2W_ml3 = 4U;
        Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZer_IN_ST_TC2WZERO_STOPPED;
      } else {
        /* Transition: '<S13>:25' */
        StTc2WZero = 2U;
        Trc2WZero_DWork.SlTc2W_ml3 = 1U;
        Trc2WZero_DWork.resetAck = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Calc_Diag' */
        /* Inport: '<Root>/StDiag' incorporates:
         *  Inport: '<Root>/FlgYawRec'
         *  Inport: '<Root>/TPMSLoc'
         *  Inport: '<Root>/TPMSNode'
         *  Inport: '<Root>/TPMSPre'
         *  Inport: '<Root>/TPMSRst'
         *  Inport: '<Root>/TPMSSda'
         *  Inport: '<Root>/TPMSWfc'
         */
        /* Event: '<S13>:59' */
        Trc2WZero_Calc_Diag((&(StDiag[0])), TPMSPre, TPMSLoc, TPMSSda, TPMSRst,
                            TPMSWfc, TPMSNode, FlgYawRec,
                            &Trc2WZero_DWork.LogicalOperator_noc,
                            &Trc2WZero_DWork.Calc_Diag);

        /* End of Outputs for SubSystem: '<S4>/Calc_Diag' */

        /* Outputs for Function Call SubSystem: '<S4>/Calc_Ack' */
        /* Inport: '<Root>/GearPos' incorporates:
         *  Inport: '<Root>/AbsRollCAN'
         *  Inport: '<Root>/BrakeSignal'
         *  Inport: '<Root>/BrakeSignalCAN'
         *  Inport: '<Root>/ClutchSignal'
         *  Inport: '<Root>/GasPos'
         *  Inport: '<Root>/GasPosCC'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/VehSpeed'
         */
        /* Event: '<S13>:60' */
        Trc2WZero_Calc_Ack(Trc2WZero_DWork.resetAck, GearPos, VehSpeed,
                           ClutchSignal, Rpm, AbsRollCAN, GasPos, GasPosCC,
                           BrakeSignal, BrakeSignalCAN,
                           &Trc2WZero_DWork.LogicalOperator3,
                           &Trc2WZero_DWork.LogicalOperator,
                           &Trc2WZero_DWork.LogicalOperator2, &Trc2WZero_DWork.y,
                           &Trc2WZero_DWork.Calc_Ack);

        /* End of Outputs for SubSystem: '<S4>/Calc_Ack' */
      }
    }
    break;
  }

  if (guard1) {
    do {
      exitg2 = 0;
      guard2 = false;
      if (entryg1 == 0) {
        if (FlgTc2WZeroAck == 0) {
          /* Transition: '<S13>:84' */
          guard2 = true;
        } else {
          /* Transition: '<S13>:64' */
          StTc2WZero = 5U;
          Trc2WZero_DWork.SlTc2W_ml3 = 3U;
          SlTcTimeout = 0U;
          Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_OK;
          exitg2 = 1;
        }
      } else {
        entryg1 = 0;
        guard2 = true;
      }

      if (guard2) {
        if (FlgTc2WZeroAck != 0) {
          /* Transition: '<S13>:85' */
        } else {
          /* Transition: '<S13>:35' */
          StTc2WZero = 4U;
          Trc2WZero_DWork.SlTc2W_ml3 = 4U;
          Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZer_IN_ST_TC2WZERO_STOPPED;
          exitg2 = 1;
        }
      }
    } while (exitg2 == 0);
  }
}

/* System initialize for function-call system: '<S1>/T10ms' */
void Trc2WZero_T10ms_Init(void)
{
  Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZer_IN_NO_ACTIVE_CHILD_mlj;
  Trc2WZero_DWork.is_active_c3_Trc2WZero = 0U;
  Trc2WZero_DWork.interlock = 0U;
  Trc2WZero_DWork.resetAck = 0U;
  Trc2WZero_DWork.SlTc2W_ml3 = 0U;
  Trc2WZero_DWork.EnTC2WZeroCC_e4f = 0U;

  /* SystemInitialize for Chart: '<S4>/Chart_Trc2WZero' incorporates:
   *  SubSystem: '<S4>/Calc_Diag'
   */
  Trc2WZero_Calc_Diag_Init(&Trc2WZero_DWork.Calc_Diag);
}

/* Output and update for function-call system: '<S1>/T10ms' */
void Trc2WZero_T10ms(void)
{
  /* Chart: '<S4>/Chart_Trc2WZero' incorporates:
   *  Inport: '<Root>/FlgTrc2WZReset'
   */
  /* Gateway: Trc2WZero/T10ms/Chart_Trc2WZero */
  /* During: Trc2WZero/T10ms/Chart_Trc2WZero */
  if (Trc2WZero_DWork.is_active_c3_Trc2WZero == 0U) {
    /* Entry: Trc2WZero/T10ms/Chart_Trc2WZero */
    Trc2WZero_DWork.is_active_c3_Trc2WZero = 1U;

    /* Entry Internal: Trc2WZero/T10ms/Chart_Trc2WZero */
    /* Entry Internal 'TRC_2_W_Z_CONTROL': '<S13>:86' */
    /* Transition: '<S13>:2' */
    StTc2WZero = 0U;
    Trc2WZero_DWork.SlTc2W_ml3 = 0U;
    Trc2WZero_DWork.EnTC2WZeroCC_e4f = 0U;
    EnTC2WZAutoTst = 0U;
    Trc2WZero_DWork.is_TRC_2_W_Z_CONTROL = Trc2WZero_IN_ST_TC2WZERO_IDLE;

    /* Entry Internal 'TRC_2_W_Z_RESET': '<S13>:87' */
    /* Transition: '<S13>:93' */
  } else {
    Trc2WZero_TRC_2_W_Z_CONTROL();

    /* During 'TRC_2_W_Z_RESET': '<S13>:87' */
    /* During 'ROUTINE_RESET': '<S13>:92' */
    /* Transition: '<S13>:95' */
    if (FlgTrc2WZReset != 0) {
      /* Outputs for Function Call SubSystem: '<S4>/Reset_Gain' */
      /* DataStoreWrite: '<S14>/Data Store Write4' incorporates:
       *  Constant: '<S14>/DEFTCZERO'
       */
      /* Transition: '<S13>:96' */
      /* Event: '<S13>:101' */
      EEGnVehSpeedFront = DEFTCZERO;

      /* End of Outputs for SubSystem: '<S4>/Reset_Gain' */
    } else {
      /* Transition: '<S13>:97' */
    }
  }

  /* End of Chart: '<S4>/Chart_Trc2WZero' */

  /* DataStoreWrite: '<S4>/Data Store Write1' */
  VehSpeedTC2WZero = Trc2WZero_DWork.y;

  /* DataStoreWrite: '<S4>/Data Store Write2' */
  SlTc2W = Trc2WZero_DWork.SlTc2W_ml3;

  /* DataStoreWrite: '<S4>/Data Store Write3' */
  EnTC2WZeroCC = Trc2WZero_DWork.EnTC2WZeroCC_e4f;

  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/PreTDC' */
void Trc2WZero_PreTDC(void)
{
  {
    /* user code (Output function Header) */

    /* System '<S1>/PreTDC' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */

    /* user code (Output function Trailer) */

    /* System '<S1>/PreTDC' */
    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void Trc2WZero_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/Trc2WZero' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((Trc2WZero_U.ev_PowerOn > 0) &&
      (Trc2WZero_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    Trc2WZero_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  Trc2WZero_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (Trc2WZero_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((Trc2WZero_U.ev_T10ms > 0) &&
      (Trc2WZero_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    Trc2WZero_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  Trc2WZero_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (Trc2WZero_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc3' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PreTDC' */
  if ((Trc2WZero_U.ev_PreTDC > 0) &&
      (Trc2WZero_PrevZCSigState.trig_to_fc3_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PreTDC'
     */
    Trc2WZero_PreTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  Trc2WZero_PrevZCSigState.trig_to_fc3_Trig_ZCE = (ZCSigState)
    (Trc2WZero_U.ev_PreTDC > 0);

  /* End of Inport: '<Root>/ev_PreTDC' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc3' */

  /* End of Outputs for SubSystem: '<Root>/Trc2WZero' */
}

/* Model initialize function */
void Trc2WZero_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  IDTrc2WZero = 0U;
  TPMSDisable = 0U;

  /* states (dwork) */
  (void) memset((void *)&Trc2WZero_DWork, 0,
                sizeof(D_Work_Trc2WZero_T));

  /* custom states */
  IntVehSpeedRearStab = 0U;
  IntVehSpeedFrontStab = 0U;
  RearWheelPressure = 0U;
  FrontWheelPressure = 0U;
  GnVehSpeedFront = 0U;
  VehSpeedTC2WZero = 0U;
  StTc2WZero = 0U;
  SlTcTimeout = 0U;
  FlgTc2WZeroAck = 0U;
  StTPMS = 0U;
  EnTC2WZeroCC = 0U;
  EnTC2WZAutoTst = 0U;
  StTc2WZAck5 = 0U;
  StTc2WZAck1 = 0U;
  StTc2WZAck2 = 0U;
  StTc2WZAck3 = 0U;
  StTc2WZAck4 = 0U;
  CntTc2WZero = 0U;
  StTc2WZAck6 = 0U;
  SlTc2W = 0U;

  /* external inputs */
  (void)memset(&Trc2WZero_U, 0, sizeof(ExternalInputs_Trc2WZero_T));
  Trc2WZero_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  Trc2WZero_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
  Trc2WZero_PrevZCSigState.trig_to_fc3_Trig_ZCE = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/Trc2WZero' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/trig_to_fc2' */
  /* SystemInitialize for S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  Trc2WZero_T10ms_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  /* End of SystemInitialize for SubSystem: '<S1>/trig_to_fc2' */

  /* End of SystemInitialize for SubSystem: '<Root>/Trc2WZero' */
}

/* user code (bottom of source file) */
/* System '<Root>/Trc2WZero' */
#endif                                 // _BUILD_TRC2WZERO_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
