/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV6/tree/DD/HEATGRIPDRIVEMGM/Hea#$   */
/* $ Description:                                                                                                */
/* $Revision:: 9603   $                                                                                          */
/* $Date:: 2019-06-27 15:12:55 +0200 (gio, 27 giu 2019)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              ExhValPwm_calib.c
 *
 * Author(s): Lana L.
 *
 *
 * Implementation notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifdef _BUILD_EXHVALPWM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ExhValPwm_out.h"

/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/
#pragma ghs section rodata=".calib"

/// Time of shake
CALQUAL uint8_T EXVPWMTIMESHAKE = 25u;
/// Prefilter diag runnig
CALQUAL int16_T EXVPWMDELTSHAKE = ((int16_T)(20.0f * 256.0f));
/// Prefilter diag runnig
CALQUAL uint16_T EXVPWMCONFON = 100u;
/// Prefilter diag boot
CALQUAL uint16_T EXVPWMCONFOFF = 200u;
/// Number self
CALQUAL uint16_T NUMCNTEXVSELF = 65535u;
/// Enable 0--100
CALQUAL uint8_T ENEXVBOUNDS = 0u;
/// Enable shake
CALQUAL uint8_T ENEXVSHAKE = 1u;
/// Pwm Touch min
CALQUAL int16_T EXVTOUCHMIN = ((uint16_T)(-1.0f * 256.0f));
/// Pwm Touch max
CALQUAL int16_T EXVTOUCHMAX = ((uint16_T)(101.0f * 256.0f));
/// Pwm min step
CALQUAL uint16_T EXVPWMSTEP = ((uint16_T)(5.0f * 256.0f));
/// Pwm Polarity
CALQUAL uint8_T EXVPWMINPOL = 1u;
/// Pwm Polarity
CALQUAL uint8_T EXVPWMOUTPOL = 0u;
/// Pwm Duty out
CALQUAL int16_T FOEXVDUTYOUT = (-1 * 256);
/// Enable Pwm Polarity
CALQUAL uint8_T ENSBSMAPPED = 0u;
/// Pwm Bk mapped
CALQUAL uint16_T BKEXVDUTY[BKEXVDUTY_dim] =
{
    ((uint16_T)(5.0f * 256.0f)), ((uint16_T)(95.0f * 256.0f))
};
/// Pwm mapped
CALQUAL uint16_T VTEXVDUTY[BKEXVDUTY_dim] =
{
    ((uint16_T)(15.0f * 256.0f)), ((uint16_T)(85.0f * 256.0f))
};

#endif

/****************************************************************************
 ****************************************************************************/

