/*******************************************************************
**
**  Filename: Utils.h
**
********************************************************************/
#ifndef _UTILS_H_
#define _UTILS_H_



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define MSEC 0
#define USEC 1

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

// Public variables
extern uint32_t debug00;
extern uint32_t debug01;
extern uint32_t debug02;
extern uint32_t debug03;
extern uint32_t debug04;
extern uint32_t debug05;
extern uint32_t debug06;
extern uint32_t debug07;
extern int32_t  debug08;
extern int32_t  debug09;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * UTILS_nop - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
inline void UTILS_nop(void)
{
    asm("nop");
}

/*--------------------------------------------------------------------------*
 * UTILS_Delay_ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     UTILS_Delay_ms(uint32_t ms);

/*--------------------------------------------------------------------------*
 * UTILS_Atoi - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 uint32_t UTILS_Atoi(char chr);

/*--------------------------------------------------------------------------*
 * UTILS_Itoa - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     UTILS_Itoa (uint32_t val,
                      uint8_t *buf,
                      unsigned radix);

/*--------------------------------------------------------------------------*
 * UTILS_vectcat - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     UTILS_vectcat(uint8_t *dest,
                        uint8_t offset,
                        const uint8_t *src, 
                        uint8_t size);

/*--------------------------------------------------------------------------*
 * DigDebounce - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     DigDebounce(uint8_T *pOut,
                      uint8_T *pOldval,
                      uint8_T newval,
                      uint8_T *pCntDeb,
                      uint8_T nf);

/*--------------------------------------------------------------------------*
 * DigDebounceTwoWay - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     DigDebounceTwoWay(uint8_T *pOut, 
                            uint8_T *pOldval,
                            uint8_T newval,
                            uint8_T *pCntDeb,
                            uint8_T nf0, 
                            uint8_T nf1);

/*--------------------------------------------------------------------------*
 * vectcat -  Function to copy a vector
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void vectcat(uint8_t *dest,
                     uint8_t offset,
                     const uint8_t *src,
                     uint8_t size);


#endif /* _UTILS_H_ */
