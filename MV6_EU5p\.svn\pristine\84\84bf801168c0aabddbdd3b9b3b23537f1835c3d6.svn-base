/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#ifdef _BUILD_DMA_



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "dma.h"
#include "sys.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

uint16_t DMA_ConfigStatus = 0;


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/

static uint64_t    DMA_ChInitDone;


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/

/*
** ===================================================================
**     Method      :  DMA_Config
**
**     Description :
**         This method initializes registers of the eDMA module
**         according to this Peripheral Initialization Bean settings.
**         Call this method in user code to initialize the module.
**         By default, the method is called automatically; see "Call
**         init method" property for more details.
**     Parameters  : None
**     Returns     : Nothing
** ===================================================================
*/
int16_t DMA_Config(void)
{
    int16_t retValue = NO_ERROR;

    if(DMA_ConfigStatus == 0) 
    {
        DMA_ConfigStatus = 1;
        /* EDMA_CR: GRP3PRI=3,GRP2PRI=2,GRP1PRI=1,GRP0PRI=0,ERGA=1,ERCA=0,EDBG=0 */
        EDMA.CR.R = 0xE400;                  /* eDMA Control Register */ 
        /***** eDMA Channel Priority Registers *****/
        /* EDMA_CPR0: ECP=0,CHPRI=15 */
        EDMA.CPR[DMA_EQADC_FISR0_CFFF0].R = DMA_EQADC_FISR0_CFFF0_PRIORITY;
        /* EDMA_CPR1: ECP=0,CHPRI=13 */
        EDMA.CPR[DMA_EQADC_FISR0_RFDF0].R = DMA_EQADC_FISR0_RFDF0_PRIORITY;            
        /* EDMA_CPR2: ECP=0,CHPRI=14 */
        EDMA.CPR[DMA_EQADC_FISR1_CFFF1].R = DMA_EQADC_FISR1_CFFF1_PRIORITY;
        /* EDMA_CPR3: ECP=0,CHPRI=12 */
        EDMA.CPR[DMA_EQADC_FISR1_RFDF1].R = DMA_EQADC_FISR1_RFDF1_PRIORITY;            
        /* EDMA_CPR4: ECP=0,CHPRI=11 */
        EDMA.CPR[DMA_EQADC_FISR2_CFFF2].R = DMA_EQADC_FISR2_CFFF2_PRIORITY;
        /* EDMA_CPR5: ECP=0,CHPRI=10 */
        EDMA.CPR[DMA_EQADC_FISR2_RFDF2].R = DMA_EQADC_FISR2_RFDF2_PRIORITY;            
        /* EDMA_CPR6: ECP=0,CHPRI=9 */
        EDMA.CPR[DMA_EQADC_FISR3_CFFF3].R = DMA_EQADC_FISR3_CFFF3_PRIORITY;
        /* EDMA_CPR7: ECP=0,CHPRI=8 */
        EDMA.CPR[DMA_EQADC_FISR3_RFDF3].R = DMA_EQADC_FISR3_RFDF3_PRIORITY;            
        /* EDMA_CPR8: ECP=0,CHPRI=7 */
        EDMA.CPR[DMA_EQADC_FISR4_CFFF4].R = DMA_EQADC_FISR4_CFFF4_PRIORITY;
        /* EDMA_CPR9: ECP=0,CHPRI=6 */
        EDMA.CPR[DMA_EQADC_FISR4_RFDF4].R = DMA_EQADC_FISR4_RFDF4_PRIORITY;            
        /* EDMA_CPR10: ECP=0,CHPRI=5 */
        EDMA.CPR[DMA_EQADC_FISR5_CFFF5].R = DMA_EQADC_FISR5_CFFF5_PRIORITY;
        /* EDMA_CPR11: ECP=0,CHPRI=4 */
        EDMA.CPR[DMA_EQADC_FISR5_RFDF5].R = DMA_EQADC_FISR5_RFDF5_PRIORITY;            
        /* EDMA_CPR12: ECP=0,CHPRI=3 */
        EDMA.CPR[DMA_DSPIB_SR_TFFF].R = DMA_DSPIB_SR_TFFF_PRIORITY;            
        /* EDMA_CPR13: ECP=0,CHPRI=2 */
        EDMA.CPR[DMA_DSPIB_SR_RFDF].R = DMA_DSPIB_SR_RFDF_PRIORITY;            
        /* EDMA_CPR14: ECP=0,CHPRI=1 */
        EDMA.CPR[DMA_DSPIC_SR_TFFF].R = DMA_DSPIC_SR_TFFF_PRIORITY;            
        /* EDMA_CPR15: ECP=0,CHPRI=0 */
        EDMA.CPR[DMA_DSPIC_SR_RFDF].R = DMA_DSPIC_SR_RFDF_PRIORITY;            
        /****                                   ****/

        /*****  eDMA Error Interrupt Registers *****/
        /* EDMA_EEIRH: EEI63=0,EEI62=0,EEI61=0,EEI60=0,EEI59=0,EEI58=0,EEI57=0,EEI56=0,EEI55=0,EEI54=0,EEI53=0,EEI52=0,EEI51=0,EEI50=0,EEI49=0,EEI48=0,EEI47=0,EEI46=0,EEI45=0,EEI44=0,EEI43=0,EEI42=0,EEI41=0,EEI40=0,EEI39=0,EEI38=0,EEI37=0,EEI36=0,EEI35=0,EEI34=0,EEI33=0,EEI32=0 */
#if(TARGET_TYPE == MPC5554)
        EDMA.EEIRH.R = 0x00;                 /* eDMA Enable Error Interrupt Register */ 
#endif    
        /* EDMA_EEIRL: EEI31=0,EEI30=0,EEI29=0,EEI28=0,EEI27=0,EEI26=0,EEI25=0,EEI24=0,EEI23=0,EEI22=0,EEI21=0,EEI20=0,EEI19=0,EEI18=0,EEI17=0,EEI16=0,EEI15=0,EEI14=0,EEI13=0,EEI12=0,EEI11=0,EEI10=0,EEI9=0,EEI8=0,EEI7=0,EEI6=0,EEI5=0,EEI4=0,EEI3=0,EEI2=0,EEI1=0,EEI0=0 */
        EDMA.EEIRL.R = 0x00;                 /* eDMA Enable Error Interrupt Register */ 
        /****                                   ****/

        /*****  eDMA Enable Request Registers  *****/
        /* EDMA_ERQRH: ERQ63=0,ERQ62=0,ERQ61=0,ERQ60=0,ERQ59=0,ERQ58=0,ERQ57=0,ERQ56=0,ERQ55=0,ERQ54=0,ERQ53=0,ERQ52=0,ERQ51=0,ERQ50=0,ERQ49=0,ERQ48=0,ERQ47=0,ERQ46=0,ERQ45=0,ERQ44=0,ERQ43=0,ERQ42=0,ERQ41=0,ERQ40=0,ERQ39=0,ERQ38=0,ERQ37=0,ERQ36=0,ERQ35=0,ERQ34=0,ERQ33=0,ERQ32=0 */
#if(TARGET_TYPE == MPC5554)
        EDMA.ERQRH.R = 0x00;               /* eDMA Enable Request Register */ 
#endif    
        /* EDMA_ERQRL: ERQ31=0,ERQ30=0,ERQ29=0,ERQ28=0,ERQ27=0,ERQ26=0,ERQ25=0,ERQ24=0,ERQ23=0,ERQ22=0,ERQ21=0,ERQ20=0,ERQ19=0,ERQ18=0,ERQ17=0,ERQ16=0,ERQ15=0,ERQ14=0,ERQ13=0,ERQ12=0,ERQ11=0,ERQ10=0,ERQ9=0,ERQ8=0,ERQ7=0,ERQ6=0,ERQ5=0,ERQ4=0,ERQ3=0,ERQ2=0,ERQ1=0,ERQ0=0 */
        EDMA.ERQRL.R = 0x00;               /* eDMA Enable Request Register */
        /****                                   ****/

        /* initializing the Channel init */
        DMA_ChInitDone = 0;

        } 
    else
    {
#ifdef CHECK_BIOS_FAULTS
        BIOS_Faults |= BIOS_FAILURE_DMA;
#endif /* CHECK_BIOS_FAULTS */
        retValue = PERIPHERAL_ALREADY_CONFIGURED;
    }
    return (retValue);
}


/*
** ===================================================================
**     Method      :  DMA_Init
**
**     Description :
**         This method initializes the required eDMA module Channel 
**         according to this Peripheral Initialization Bean settings.
**         Call this method in user code to initialize the channel.
**     Parameters  : Channel Number
**     Returns     : Nothing
** ===================================================================
*/
int16_t DMA_Init(uint16_t channelNumber, 
                 uint32_t *dest, 
                 uint16_t destOffset, 
                 uint32_t *src,  
                 uint16_t srcOffset,
                 uint16_t innerLoopSize, 
                 uint16_t outerLoopSize,
                 uint16_t interruptEnabled)
{
    int16_t retValue = NO_ERROR;

    DisableAllInterrupts();
    if ((((uint64_t)1<<channelNumber) & DMA_ChInitDone) == 0)
    {
        DMA_ChInitDone |= ((uint64_t)1<<channelNumber);
    }
    else
    {
        if (EDMA.TCD[channelNumber].B.DONE == 0)
        {
            retValue = DMA_CHANNEL_BUSY;
        }
    }
    EnableAllInterrupts();

    if (retValue == NO_ERROR)
    {  
        switch(channelNumber) 
        {
            case DMA_DSPIA_SR_TFFF:
            case DMA_DSPIA_SR_RFDF:
            case DMA_DSPIB_SR_TFFF:
            case DMA_DSPIB_SR_RFDF:
            case DMA_DSPIC_SR_TFFF:
            case DMA_DSPIC_SR_RFDF:
            case DMA_DSPID_SR_TFFF:
            case DMA_DSPID_SR_RFDF:
            {
                /* EDMA_TCD_0_31: 
                SADDR=src */
                EDMA.TCD[channelNumber].R[0] = ((vuint32_t)src);
                /* EDMA_TCD_32_63: 
                SMOD=0,SSIZE=2 (32 bit),DMOD=0,DSIZE=2 (32 bit),SOFF=srcOffset */
                EDMA.TCD[channelNumber].R[1] = 0x02020000 | (((uint32_t)srcOffset)&0x0000ffff);
                /* EDMA_TCD_64_95:
                NBYTES = innerLoopSize */
                EDMA.TCD[channelNumber].R[2] = (vuint32_t)innerLoopSize;     
                /* EDMA_TCD_96_127: 
                SLAST= srcOffset*outerLoopSize */
                if(srcOffset>0)
                {
                    EDMA.TCD[channelNumber].R[3] = -(uint32_t)innerLoopSize*outerLoopSize;
                }
                else if((int16_t)srcOffset<0)
                {
                    EDMA.TCD[channelNumber].R[3] = (uint32_t)innerLoopSize*outerLoopSize;
                }
                else
                {
                    EDMA.TCD[channelNumber].R[3] = (uint32_t)0;
                }
                /* EDMA_TCD_128_159: 
                DADDR=dest*/
                EDMA.TCD[channelNumber].R[4] = (vuint32_t)dest; 
                /* EDMA_TCD_160_191: 
                CITER_E_LINK=0,CITER_LINKCH=0,CITER=1,DOFF=destOffset */
                EDMA.TCD[channelNumber].R[5] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)destOffset)&0x0000ffff); 
                /* EDMA_TCD_192_223: 
                DLAST_SGA=0 */
                if(destOffset>0)
                {
                    EDMA.TCD[channelNumber].R[6] = -(uint32_t)innerLoopSize*outerLoopSize;
                }
                else if((int16_t)destOffset<0)
                {
                    EDMA.TCD[channelNumber].R[6] = (uint32_t)innerLoopSize*outerLoopSize;
                }
                else
                {
                    EDMA.TCD[channelNumber].R[6] = (uint32_t)0;
                }
                /* EDMA_TCD_224_255: 
                BITER_E_LINK=0,BITER_LINKCH=0,BITER=1,BWC=0,MAJOR_LINKCH=0,
                DONE=0,ACTIVE=0,MAJOR_E_LINK=0,E_SG=0,D_REQ=1,INT_HALF=0,
                INT_MAJ=0,START=0 */
                EDMA.TCD[channelNumber].R[7] = (((uint32_t)outerLoopSize)<<16) | (0x01<<3) | (((uint32_t)interruptEnabled&0x01)<<1); 
                break;
            }
            case DMA_EQADC_FISR0_RFDF0:
            case DMA_EQADC_FISR1_RFDF1:
            case DMA_EQADC_FISR2_RFDF2:
            case DMA_EQADC_FISR3_RFDF3:
            case DMA_EQADC_FISR4_RFDF4:
            {
                /* EDMA_TCD_0_31: 
                SADDR=src */
                EDMA.TCD[channelNumber].R[0] = ((vuint32_t)src);
                /* EDMA_TCD_32_63: 
                SMOD=0,SSIZE=1 (16 bit),DMOD=0,DSIZE=1 (16 bit),SOFF=srcOffset */
                EDMA.TCD[channelNumber].R[1] = 0x01010000 | (((uint32_t)srcOffset)&0x0000ffff);
                /* EDMA_TCD_64_95:
                NBYTES = innerLoopSize */
                EDMA.TCD[channelNumber].R[2] = (vuint32_t)innerLoopSize;     
                /* EDMA_TCD_96_127: 
                SLAST= srcOffset*outerLoopSize */
                if(srcOffset>0)
                {
                    EDMA.TCD[channelNumber].R[3] = -(uint32_t)innerLoopSize*outerLoopSize;
                }
                else if((int16_t)srcOffset<0)
                {
                    EDMA.TCD[channelNumber].R[3] = (uint32_t)innerLoopSize*outerLoopSize;
                }
                else
                {
                    EDMA.TCD[channelNumber].R[3] = (uint32_t)0;
                }

                /* EDMA_TCD_128_159: 
                DADDR=dest*/
                EDMA.TCD[channelNumber].R[4] = (vuint32_t)dest; 
                /* EDMA_TCD_160_191: 
                CITER_E_LINK=0,CITER_LINKCH=0,CITER=1,DOFF=destOffset */
                EDMA.TCD[channelNumber].R[5] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)destOffset)&0x0000ffff); 
                /* EDMA_TCD_192_223: 
                DLAST_SGA=0 */
                if(destOffset>0)
                {
                    EDMA.TCD[channelNumber].R[6] = -(uint32_t)innerLoopSize*outerLoopSize;
                }
                else if((int16_t)destOffset<0)
                {
                    EDMA.TCD[channelNumber].R[6] = (uint32_t)innerLoopSize*outerLoopSize;
                }
                else
                {
                    EDMA.TCD[channelNumber].R[6] = (uint32_t)0;
                }
                /* EDMA_TCD_224_255: 
                BITER_E_LINK=0,BITER_LINKCH=0,BITER=1,BWC=0,MAJOR_LINKCH=0,
                DONE=0,ACTIVE=0,MAJOR_E_LINK=0,E_SG=0,D_REQ=1,INT_HALF=0,
                INT_MAJ=0,START=0 */
                EDMA.TCD[channelNumber].R[7] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)interruptEnabled&0x01)<<1);
                break;
            }
            case DMA_EQADC_FISR5_CFFF5:
            case DMA_EQADC_FISR5_RFDF5:
            {
                /* EDMA_TCD_0_31: 
                SADDR=src */
                EDMA.TCD[channelNumber].R[0] = ((vuint32_t)src);
                /* EDMA_TCD_32_63: 
                SMOD=0,SSIZE=1 (16 bit),DMOD=0,DSIZE=1 (16 bit),SOFF=srcOffset */
                EDMA.TCD[channelNumber].R[1] = 0x01010000 | (((uint32_t)srcOffset)&0x0000ffff);
                /* EDMA_TCD_64_95:
                NBYTES = innerLoopSize */
                EDMA.TCD[channelNumber].R[2] = (vuint32_t)innerLoopSize;     
                /* EDMA_TCD_96_127: 
                SLAST= srcOffset*outerLoopSize */
                EDMA.TCD[channelNumber].R[3] = -(uint32_t)outerLoopSize*innerLoopSize;    
                /* EDMA_TCD_128_159: 
                DADDR=dest*/
                EDMA.TCD[channelNumber].R[4] = (vuint32_t)dest; 
                /* EDMA_TCD_160_191: 
                CITER_E_LINK=0,CITER_LINKCH=0,CITER=1,DOFF=destOffset */
                EDMA.TCD[channelNumber].R[5] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)destOffset)&0x0000ffff); 
                /* EDMA_TCD_192_223: 
                DLAST_SGA=0 */
                EDMA.TCD[channelNumber].R[6] = -(uint32_t)outerLoopSize*innerLoopSize;   
                /* EDMA_TCD_224_255: 
                BITER_E_LINK=0,BITER_LINKCH=0,BITER=1,BWC=0,MAJOR_LINKCH=0,
                DONE=0,ACTIVE=0,MAJOR_E_LINK=0,E_SG=0,D_REQ=0,INT_HALF=0,
                INT_MAJ=0,START=0 */
                EDMA.TCD[channelNumber].R[7] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)interruptEnabled&0x01)<<1);
                break;
            }
            default:
            {
                /* EDMA_TCD_0_31: 
                SADDR=src */
                EDMA.TCD[channelNumber].R[0] = ((vuint32_t)src);
                /* EDMA_TCD_32_63: 
                SMOD=0,SSIZE=2 (32 bit),DMOD=0,DSIZE=2 (32 bit),SOFF=srcOffset */
                EDMA.TCD[channelNumber].R[1] = 0x02020000 | (((uint32_t)srcOffset)&0x0000ffff);
                /* EDMA_TCD_64_95:
                NBYTES = innerLoopSize */
                EDMA.TCD[channelNumber].R[2] = (vuint32_t)innerLoopSize;     
                /* EDMA_TCD_96_127: 
                SLAST= srcOffset*outerLoopSize */
                if(srcOffset>0)
                {
                    EDMA.TCD[channelNumber].R[3] = -(uint32_t)innerLoopSize*outerLoopSize;
                }
                else if((int16_t)srcOffset<0)
                {
                    EDMA.TCD[channelNumber].R[3] = (uint32_t)innerLoopSize*outerLoopSize;
                }
                else
                {
                    EDMA.TCD[channelNumber].R[3] = (uint32_t)0;
                }
                /* EDMA_TCD_128_159: 
                DADDR=dest*/
                EDMA.TCD[channelNumber].R[4] = (vuint32_t)dest; 
                /* EDMA_TCD_160_191: 
                CITER_E_LINK=0,CITER_LINKCH=0,CITER=1,DOFF=destOffset */
                EDMA.TCD[channelNumber].R[5] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)destOffset)&0x0000ffff); 
                /* EDMA_TCD_192_223: 
                DLAST_SGA=0 */
                if(destOffset>0)
                {
                    EDMA.TCD[channelNumber].R[6] = -(uint32_t)innerLoopSize*outerLoopSize;
                }
                else if((int16_t)destOffset<0)
                {
                    EDMA.TCD[channelNumber].R[6] = (uint32_t)innerLoopSize*outerLoopSize;
                }
                else
                {
                    EDMA.TCD[channelNumber].R[6] = (uint32_t)0;
                }
                /* EDMA_TCD_224_255: 
                BITER_E_LINK=0,BITER_LINKCH=0,BITER=1,BWC=0,MAJOR_LINKCH=0,
                DONE=0,ACTIVE=0,MAJOR_E_LINK=0,E_SG=0,D_REQ=0,INT_HALF=0,
                INT_MAJ=0,START=0 */
                EDMA.TCD[channelNumber].R[7] = (((uint32_t)outerLoopSize)<<16) | (((uint32_t)interruptEnabled&0x01)<<1); 
                break;
            }
        }
    }

    return (retValue); 
}

/*
** ===================================================================
**     Method      :  DMA_SetMinorLinking
**
**     Description :
**         This method links togheter 2 DMA channels on the inner loop 
**         completion.
**         Call this method in user code only when channels are disabled.
**     Parameters  : Channel Number
**                   Linked Channel
**     Returns     : Nothing
** ===================================================================
*/
int16_t DMA_SetMinorLinking(uint16_t channelNumber, 
                            uint16_t linkedChannel) 
{
    EDMA.TCD[channelNumber].B.CITERE_LINK = 1; 
    EDMA.TCD[channelNumber].B.BITERE_LINK = 1;
    /* EDMA.TCD[channelNumber].B.CITERLINKCH = linkedChannel; */ /* AM - Deprecated in mpc563xm.h v2.0 */
    /* EDMA.TCD[channelNumber].B.BITERLINKCH = linkedChannel; */ /* AM - Deprecated in mpc563xm.h v2.0 */

    return NO_ERROR;
}

/*
** ===================================================================
**     Method      :  DMA_SetMajorLinking
**
**     Description :
**         This method links togheter 2 DMA channels on the outer loop 
**         completion.
**         Call this method in user code only when channels are disabled.
**     Parameters  : Channel Number
**                   Linked Channel
**     Returns     : Nothing
** ===================================================================
*/
int16_t DMA_SetMajorLinking(uint16_t channelNumber,
                            uint16_t linkedChannel) 
{
    EDMA.TCD[channelNumber].B.MAJORE_LINK = 1;
    EDMA.TCD[channelNumber].B.MAJORLINKCH = linkedChannel;

    return NO_ERROR;
}

/*
** ===================================================================
**     Method      :  DMA_Enable
**
**     Description :
**         This method enable the acquisition through the required 
**         eDMA module Channel according to the channel settings.
**         Call this method in user code to get samples from the module.
**     Parameters  : Channel Number
**     Returns     : Nothing
** ===================================================================
*/
int16_t DMA_Enable(uint16_t channelNumber) 
{
    EDMA.SERQR.R = (uint8_t)channelNumber; 

    return NO_ERROR;
}


/*
** ===================================================================
**     Method      :  DMA_Disable
**
**     Description :
**         This method enable the acquisition through the required 
**         eDMA module Channel according to the channel settings.
**         Call this method in user code to get samples from the module.
**     Parameters  : Channel Number
**     Returns     : Nothing
** ===================================================================
*/
int16_t DMA_Disable(uint16_t channelNumber) 
{
    EDMA.CERQR.R = (uint8_t)channelNumber; 

    DisableAllInterrupts();
    DMA_ChInitDone &= (~ ((uint64_t)1<<channelNumber) );
    EnableAllInterrupts();

    return NO_ERROR;
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _BUILD_DMA_ */
