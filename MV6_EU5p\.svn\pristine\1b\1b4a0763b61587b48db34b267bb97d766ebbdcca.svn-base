/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL$ */
/* $Description:  $ */
/* $Revision$ */
/* $Date$ */
/* $Author$ */
/*****************************************************************************************************************/
/*
 * File: lamheater_mgm.h
 *
 * Code generated for Simulink model 'LamHeaterMgm'.
 *
 * Model version                  : 1.744
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon May 13 17:31:42 2024
 */

#ifndef RTW_HEADER_lamheater_mgm_h_
#define RTW_HEADER_lamheater_mgm_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define ACTIVE_DIAG                    6U
#define LAM_HEAT_A                     2U
#define LAM_HEAT_B                     3U
#define LAM_HEAT_C                     4U
#define LAM_HEAT_D                     5U
#define LAM_HEAT_INIT                  0U
#define LAM_HEAT_OFF                   1U

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint16_T DCLamHeater;

/* Duty cycle of the lambda heater */
extern uint16_T DCLamHeater2;

/* Duty cycle of the lambda heater */
extern uint16_T DCLamHeater2A;

/* Raw duty cycle of the lambda heater in phase A */
extern uint16_T DCLamHeater2C;

/* Raw duty cycle of the lambda heater in phase C */
extern uint16_T DCLamHeater2D;

/* Raw duty cycle of the lambda heater in phase D */
extern uint16_T DCLamHeater2NoSat;

/* Non saturated duty cycle of the lambda heater */
extern uint16_T DCLamHeaterA;

/* Raw duty cycle of the lambda heater in phase A */
extern uint16_T DCLamHeaterC;

/* Raw duty cycle of the lambda heater in phase C */
extern uint16_T DCLamHeaterD;

/* Raw duty cycle of the lambda heater in phase D */
extern uint16_T DCLamHeaterNoSat;

/* Non saturated duty cycle of the lambda heater */
extern uint8_T FlgLamHeater2ExitPwrLatch;

/* Lambda heater job End flag to get ECU OFF */
extern uint8_T FlgLamHeaterExitPwrLatch;

/* Lambda heater job End flag to get ECU OFF */
extern uint8_T FlgO2H2DiagOn;

/* Enable diagnosis */
extern uint8_T FlgO2HDiagOn;

/* Enable diagnosis */
extern uint32_T IDLamHeaterMgm;

/* ID Version */
extern uint16_T KNormDc;

/* Gain to compensate the lambda heater DC */
extern uint16_T LoadLH;

/* Load type */
extern uint8_T StLamHeater;

/* Lambda heater status */
extern uint8_T StLamHeater2;

/* Lambda heater status */
extern uint16_T THeat2B;

/* Duration of phase B for lambda sensor heating */
extern uint16_T THeat2C;

/* Duration of phase C for lambda sensor heating */
extern uint16_T THeatB;

/* Duration of phase B for lambda sensor heating */
extern uint16_T THeatC;

/* Duration of phase C for lambda sensor heating */
extern uint16_T ThrVO2H;

/* VLambda threshold diagnosis */
extern uint16_T ThrVO2H2;

/* VLambda threshold diagnosis */
extern uint16_T ThrVO2H2Max;

/* VLambda threshold diagnosis */
extern uint16_T ThrVO2H2Min;

/* VLambda threshold diagnosis */
extern uint16_T ThrVO2HMax;

/* VLambda threshold diagnosis */
extern uint16_T ThrVO2HMin;

/* VLambda threshold diagnosis */

#define LAMHEAT_DIAG_PER               50U                       /* Referenced by: '<S9>/Lambda Heater' */
#define LAM_HEAT_PERIOD                100000U                   /* Referenced by: '<S9>/Lambda Heater' */

extern void LamHeaterMgm_T100ms(void);

#endif                                 /* RTW_HEADER_lamheater_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
