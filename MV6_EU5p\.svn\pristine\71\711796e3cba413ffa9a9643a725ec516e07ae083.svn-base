/*
 * File: IonMisf_private.h
 *
 * Real-Time Workshop code generated for Simulink model IonMisf.
 *
 * Model version                        : 1.613
 * Real-Time Workshop file version      : 7.0  (R2007b)  02-Aug-2007
 * Real-Time Workshop file generated on : Thu Feb 28 11:29:28 2008
 * TLC version                          : 7.0 (Jul 26 2007)
 * C source code generated on           : Thu Feb 28 11:29:29 2008
 */

#ifndef RTW_HEADER_IonMisf_private_h_
#define RTW_HEADER_IonMisf_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "sparkmgm.h"
#include "ionmgm.h"
#include "ionacq.h"
#include "loadmgm.h"
#include "null.h"
#include "syncmgm.h"
#include "temp_mgm.h"
#  include "rtlibsrc.h"
#define CALL_EVENT                     (MAX_uint8_T)
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error "Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error "Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

extern uint8_T _sfEvent_IonMisf_;

/* Imported (extern) block parameters */

extern uint16_T BKMISFTHRDEFF[5];
extern uint16_T VTMISFTHRDEFF[5];

extern uint16_T BKMISFRPM[12];         /* Variable: BKMISFRPM
                                        * '<S8>/BKMISFRPM'
                                        * Rpm Breakpoint vector for TBMISFTHR
                                        */
extern uint16_T BKMISFLOAD[9];         /* Variable: BKMISFLOAD
                                        * '<S8>/BKMISFLOAD'
                                        * Load Breakpoint vector for TBMISFTHR
                                        */
extern int16_T BKTAIRIONMISF[4];       /* Variable: BKTAIRIONMISF
                                        * '<S8>/BKTAIRIONMISF'
                                        * Inlet air temperature breakpoint vector for TBCORRTHRMISF
                                        */
extern int16_T BKTWATIONMISF[6];       /* Variable: BKTWATIONMISF
                                        * '<S8>/BKTWATIONMISF'
                                        * Coolant temperature breakpoints vector for TBCORRTHRMISF
                                        */
extern uint16_T BADCOMBPER;            /* Variable: BADCOMBPER
                                        * '<S8>/BADCOMBPER'
                                        * Bad Combustion Threshold Gain
                                        */
extern uint16_T MISFPER;               /* Variable: MISFPER
                                        * '<S8>/MISFPER'
                                        * Total Misfire Threshold Gain
                                        */
extern uint16_T PARMISFPER;            /* Variable: PARMISFPER
                                        * '<S8>/PARMISFPER'
                                        * Partial Misfire Threshold Gain
                                        */
extern uint16_T TBCORRTHRMISF[24];     /* Variable: TBCORRTHRMISF
                                        * '<S8>/TBCORRTHRMISF'
                                        * Table of gain to calculate the misfire threshold
                                        */
extern uint8_T ENIONMISF;              /* Variable: ENIONMISF
                                        * '<S1>/Control_flow'
                                        * Enable IonMisf (=1)
                                        */
extern uint8_T MISFRESET;              /* Variable: MISFRESET
                                        * '<S8>/State_Mgm'
                                        * Misfire counters reset flag
                                        */
extern uint16_T TBMISFTHR[108];         /* Variable: TBMISFTHR
                                        * '<S8>/TBMISFTHR'
                                        * Misfire Threshold Table
                                        */
void IonMisf_Reset_Variables(void);
void IonMisf_fcn_EOA_Init(void);
void IonMisf_fcn_EOA(void);
void IonMisf_Control_flow_Init(void);
void IonMisf_Control_flow(void);
void IonMisf_Init(void);
void IonMisf_NoSync(void);
void IonMisf_EOA(void);

#endif                                 /* RTW_HEADER_IonMisf_private_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
