#ifndef _FLASHMGM_H_
#define _FLASHMGM_H_

#define FLASH_CRC_NOT_VALID -255

extern uint8_T  FlashFaultCnt;
extern uint8_T  start_checksum_appli;
extern uint8_T  start_crc_appli;

extern uint16_T THRRPMTFLASH;

void FLASHMGM_FlashTestStart(void);
void FLASHMGM_FlashTestCRC16Start(void);
void FLASHMGM_FlashTest(void);
void FLASHMGM_FlashTestCRC16(void);
int16_t FLASHMGM_GetCrc(uint16_t *crc_16);

#endif
