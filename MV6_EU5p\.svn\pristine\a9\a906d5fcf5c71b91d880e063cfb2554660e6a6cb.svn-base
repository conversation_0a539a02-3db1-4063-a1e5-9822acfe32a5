/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _BUILD_DIGIO_

#include "DIGIO.h"


/* ========================================================================= */
/* ### Functions definition */
/* ========================================================================= */

/*
** =======================================================================
**     Method      : DIGIO_InCfgExt
**
**     Description : fake function
**                   
**
**     Parameters  : Channel_Id, type
**     Returns     : None
** =======================================================================
*/
void DIGIO_InCfgExt(uint16_t Channel_Id, 
                    uint8_t type)
{
    SIU.PCR[Channel_Id].B.PA  = 0;            /* Configure PA bit to enable GPIO */
                                              /* pin function                    */
    SIU.PCR[Channel_Id].B.IBE = SET_BIT_HIGH; /* Configure IBE filed in pad      */

    SIU.PCR[Channel_Id].B.WPE = (type & 0x02) >> 1;
    SIU.PCR[Channel_Id].B.WPS = (type & 0x01);
}

/*
** =======================================================================
**     Method      : DIGIO_OutCfgExt
**
**     Description : fake function
**                   
**
**     Parameters  : Channel_Id, ResetLvl, openDrnEna, drvStrength, slewRate
**     Returns     : None
** =======================================================================
*/
void DIGIO_OutCfgExt(uint16_t Channel_Id, 
                     uint8_t ResetLvl,
                     uint8_t openDrnEna,
                     uint8_t drvStrength,
                     uint8_t slewRate)
{
    SIU.PCR [Channel_Id].B.PA  = 0;             /* Configure PA bit to enable GPIO*/

    SIU.PCR [Channel_Id].B.OBE = SET_BIT_HIGH;  /* Configure OBE bit for GPO pin function */   

    SIU.PCR [Channel_Id].B.IBE = SET_BIT_HIGH;  /* Configure IBE filed in pad      */

    SIU.PCR [Channel_Id].B.HYS = 1;

    SIU.GPDO[Channel_Id].B.PDO = ResetLvl;      /* Set pin data out with initial  */

    SIU.PCR [Channel_Id].B.DSC = drvStrength;   /* drive strength */
    
    SIU.PCR [Channel_Id].B.SRC = slewRate;      /* slew rate */
    
    SIU.PCR [Channel_Id].B.ODE = openDrnEna;    /* open drain */

}

/*
** =======================================================================
**     Method      : DIGIO_InGet
**
**     Description : fake function
**                   
**
**     Parameters  : Channel_Id, status
**     Returns     : None
** =======================================================================
*/
int16_t DIGIO_InGet(uint16_t Channel_Id, 
                    uint8_t* status)
{
    int16_t retValue = NO_ERROR;

    if (SIU.PCR[Channel_Id].B.IBE != 0)         /* Check if the pin is rightly    */
    {
        *status = SIU.GPDI[Channel_Id].B.PDI;   /* configured as input and gets   */
    }
    else
    {
        retValue = PERIPHERAL_NOT_CONFIGURED;   /* if not, returns an error code  */
    }
    return retValue;
}

/*
** =======================================================================
**     Method      : DIGIO_OutSet
**
**     Description : fake function
**                   
**
**     Parameters  : Channel_Id, status
**     Returns     : None
** =======================================================================
*/
int16_t DIGIO_OutSet(uint16_t Channel_Id,
                     uint8_t status)
{
    int16_t retValue = NO_ERROR;

    if (SIU.PCR[Channel_Id].B.OBE != 0)         /* Check if the pin is rightly    */
    {
        SIU.GPDO[Channel_Id].B.PDO = status;    /* configured as output and drive */
    }
    else
    {
        retValue = PERIPHERAL_NOT_CONFIGURED;   /* if not, returns an error code  */
    }
    return retValue;
}

/*
** =======================================================================
**     Method      : DIGIO_OutGet
**
**     Description : fake function
**                   
**
**     Parameters  : Channel_Id, status
**     Returns     : None
** =======================================================================
*/
int16_t DIGIO_OutGet(uint16_t Channel_Id,
                     uint8_t* status)
{
    int16_t retValue = NO_ERROR;

    if ( SIU.PCR[Channel_Id].B.OBE != 0)        /* Check if the pin is rightly     */
    {
        *status = SIU.GPDO[Channel_Id].B.PDO;   /* configured as output and fetch  */
    }
    else
    {
        retValue = PERIPHERAL_NOT_CONFIGURED;   /* if not, returns an error code   */
    }
    return retValue;
}

#endif /* _BUILD_DIGIO_ */
