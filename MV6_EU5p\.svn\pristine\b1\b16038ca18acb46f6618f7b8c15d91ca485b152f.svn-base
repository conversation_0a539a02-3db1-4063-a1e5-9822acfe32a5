/*
 * File: GasPosMgm_types.h
 *
 * Code generated for Simulink model 'GasPosMgm'.
 *
 * Model version                  : 1.898
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb  1 14:21:31 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GasPosMgm_types_h_
#define RTW_HEADER_GasPosMgm_types_h_

/*
 * Registered constraints for dimension variants
 */
/* Constraint 'REC_NO_GAS > 0' registered by:
 * '<S13>/Calc_Final_GasPos'
 */
#if REC_NO_GAS <= 0
# error "The preprocessor definition 'REC_NO_GAS' must be greater than '0'"
#endif

/* Constraint 'REC_NO_GAS < 2147483647' registered by:
 * '<S13>/Calc_Final_GasPos'
 */
#if REC_NO_GAS >= 2147483647
# error "The preprocessor definition 'REC_NO_GAS' must be less than '2147483647'"
#endif
#endif                                 /* RTW_HEADER_GasPosMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
