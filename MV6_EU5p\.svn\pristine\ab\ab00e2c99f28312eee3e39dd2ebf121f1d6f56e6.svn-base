/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Application/KNOCKCORRE/branches/KNOCKCORR_MV/KnockCorr_ert_rt#$  */
/* $Revision:: 8016                                                                                           $  */
/* $Date:: 2019-10-22 11:50:14 +0200 (mar, 22 ott 2019)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorr_private.h
 **  Date:          22-Oct-2019
 **
 **  Model Version: 1.1979
 ******************************************************************************
 **/
#ifndef RTW_HEADER_KnockCorr_private_h_
#define RTW_HEADER_KnockCorr_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "KnockCorr.h"

/* Includes for objects with custom storage classes. */
#include "ionknock.h"
#include "diagmgm_out.h"
#include "ETPU_EngineDefs.h"
#include "recmgm.h"
#include "diagcanmgm.h"
#include "Stub.h"
#include "syncmgm.h"
#include "ionacq.h"
#include "loadmgm.h"
#include "temp_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint16_T BKRPMKNOCK12[12];      /* Variable: BKRPMKNOCK12
                                        * Referenced by: '<S21>/ENKNOCKAD1'
                                        * Breakpoints of engine speed for knock learning
                                        */
extern uint16_T BKRPMKNOCK5[5];        /* Variable: BKRPMKNOCK5
                                        * Referenced by: '<S54>/BKRPMKNOCK5'
                                        * Breakpoints of engine speed for knock control
                                        */
extern uint16_T BKRPMKNOCK8[8];        /* Variable: BKRPMKNOCK8
                                        * Referenced by: '<S30>/BKRPMKNOCK4'
                                        * Breakpoints of engine speed for knock control
                                        */
extern uint16_T BKRPMKNOCKTIN[5];      /* Variable: BKRPMKNOCKTIN
                                        * Referenced by:
                                        *   '<S49>/BKRPMKNOCKTIN'
                                        *   '<S50>/BKRPMKNOCKTIN'
                                        * Breakpoints of engine speed for tip in
                                        */
extern uint16_T KNOCKLEARNDUR;         /* Variable: KNOCKLEARNDUR
                                        * Referenced by:
                                        *   '<S70>/KNOCKLEARNDUR'
                                        *   '<S72>/KNOCKLEARNDUR'
                                        * Knock learning duration
                                        */
extern uint16_T THDEVALIDCOH;          /* Variable: THDEVALIDCOH
                                        * Referenced by: '<S1>/Control_flow'
                                        * Thr of counter after activation of rec before devalidating the knock coh diag
                                        */
extern uint16_T THRNCOMBOBS;           /* Variable: THRNCOMBOBS
                                        * Referenced by: '<S21>/TipIn'
                                        * Maximum number of combustion during which tipin is being detected
                                        */
extern uint16_T VTTDCSTABKNOCK[12];    /* Variable: VTTDCSTABKNOCK
                                        * Referenced by: '<S60>/VTTDCSTABKNOCK'
                                        * Number of TDCs to detect stability of Load and RpmF for knock learning
                                        */
extern uint16_T BKETACSIKNOCK[6];      /* Variable: BKETACSIKNOCK
                                        * Referenced by: '<S87>/BKETACSIKNOCK'
                                        * Vector of breakpoints for the rpm and load ratios
                                        */
extern int16_T DELTAKCORRMAX;          /* Variable: DELTAKCORRMAX
                                        * Referenced by: '<S87>/DELTAKCORRMAX'
                                        * Delta on maximum adaptive knock correction
                                        */
extern int16_T HYSTSAKKNOCKCOH;        /* Variable: HYSTSAKKNOCKCOH
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * Hyst on the threshold on the knock offset for the coherence diagnosis
                                        */
extern int16_T KCORRINCI;              /* Variable: KCORRINCI
                                        * Referenced by: '<S108>/KCORRINCI'
                                        * Step to increase the knock correction in torque control
                                        */
extern int16_T KCSTEP;                 /* Variable: KCSTEP
                                        * Referenced by: '<S16>/KnockCorr_Slew'
                                        * Delta on minimum adaptive knock correction
                                        */
extern int16_T MAXNEGKCORRINDAVG;      /* Variable: MAXNEGKCORRINDAVG
                                        * Referenced by: '<S71>/Calc_indices_ad'
                                        * Max negative KCorrIndAvgCyl to adapt
                                        */
extern int16_T TAIRMAXKCOH;            /* Variable: TAIRMAXKCOH
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * Threshold on the air temperature to disable knock coherence
                                        */
extern int16_T THSAKKNOCKCOH;          /* Variable: THSAKKNOCKCOH
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * Threshold on the SAKnockMin for the coherence diagnosis
                                        */
extern int16_T THWATKNOCKAD;           /* Variable: THWATKNOCKAD
                                        * Referenced by: '<S53>/THWATKNOCKAD'
                                        * Threshold on the coolant temperature to enable knock learning
                                        */
extern int16_T TWATMAXKCOH;            /* Variable: TWATMAXKCOH
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * Threshold on the coolant temperature to disable knock coherence
                                        */
extern int16_T VTDELTAKCORRMIN[12];    /* Variable: VTDELTAKCORRMIN
                                        * Referenced by: '<S87>/VTDELTAKCORRMIN'
                                        * Delta on minimum adaptive knock correction
                                        */
extern int16_T VTKCORRMIN[12];         /* Variable: VTKCORRMIN
                                        * Referenced by:
                                        *   '<S55>/VTKCORRMIN'
                                        *   '<S87>/VTKCORRMIN'
                                        * Minimum total knock correction
                                        */
extern int16_T VTSAKNOCKFORCED[8];     /* Variable: VTSAKNOCKFORCED
                                        * Referenced by: '<S15>/Force_SAKnock'
                                        * Forced knock correction
                                        */
extern int16_T KNOCKRECRLDIFF;         /* Variable: KNOCKRECRLDIFF
                                        * Referenced by: '<S37>/KNOCKRECRLDIFF'
                                        * Rate limiter input-output difference threshold
                                        */
extern int16_T KNOCKRECRLMAX;          /* Variable: KNOCKRECRLMAX
                                        * Referenced by: '<S28>/KNOCKRECRLMAX'
                                        * KnockRec positive rate limiter
                                        */
extern int16_T KNOCKRECRLMIN;          /* Variable: KNOCKRECRLMIN
                                        * Referenced by: '<S28>/KNOCKRECRLMIN'
                                        * KnockRec negative rate limiter
                                        */
extern uint16_T BKLOADADKNOCK[5];      /* Variable: BKLOADADKNOCK
                                        * Referenced by: '<S21>/ENKNOCKAD2'
                                        * Breakpoints of load for knock learning
                                        */
extern uint16_T BKLOADKNOCK3[3];       /* Variable: BKLOADKNOCK3
                                        * Referenced by: '<S30>/BKLOADKNOCK4'
                                        * Breakpoints of load for knock learning
                                        */
extern uint16_T BKLOADKNOCKTIN[4];     /* Variable: BKLOADKNOCKTIN
                                        * Referenced by: '<S50>/BKLOADKNOCKTIN'
                                        * Breakpoints of load for tip in
                                        */
extern uint16_T HYSTLOADRECKNOCK;      /* Variable: HYSTLOADRECKNOCK
                                        * Referenced by: '<S17>/HYSTLOADRECKNOCK'
                                        * Hyst applied to min Load value to apply recovery correction
                                        */
extern uint16_T MINLOADRECKNOCK;       /* Variable: MINLOADRECKNOCK
                                        * Referenced by: '<S17>/MINLOADRECKNOCK'
                                        * Min Load value to apply recovery correction
                                        */
extern uint16_T THRSTABLDKNOCK;        /* Variable: THRSTABLDKNOCK
                                        * Referenced by: '<S60>/THRSTABLDKNOCK'
                                        * Threshold to detect stability for Load
                                        */
extern uint16_T THRSTABRPMKNOCK;       /* Variable: THRSTABRPMKNOCK
                                        * Referenced by: '<S60>/THRSTABRPMKNOCK'
                                        * Threshold to detect stability for RpmF
                                        */
extern int16_T BKDELTAKNOCK[4];        /* Variable: BKDELTAKNOCK
                                        * Referenced by: '<S107>/BKDELTAKNOCK'
                                        * Breakpoints of DeltaKnockNPow
                                        */
extern uint16_T GNKNAD;                /* Variable: GNKNAD
                                        * Referenced by: '<S87>/GNKNAD'
                                        * Threshold on the coolant temperature to enable knock learning
                                        */
extern uint16_T TBGNAD[36];            /* Variable: TBGNAD
                                        * Referenced by: '<S87>/TBGNAD'
                                        * Table of gains for adaptive coefficients spreading
                                        */
extern uint16_T THKCORRINDAD;          /* Variable: THKCORRINDAD
                                        * Referenced by: '<S70>/THKCORRINDAD'
                                        * Threshold on the abs. average knocking corr. to trigger the learning
                                        */
extern uint8_T CNTKNOCKCOHDEC;         /* Variable: CNTKNOCKCOHDEC
                                        * Referenced by: '<S18>/Reset'
                                        * CntKnockCohEE decrement step
                                        */
extern uint8_T CNTKNOCKCOHINC;         /* Variable: CNTKNOCKCOHINC
                                        * Referenced by:
                                        *   '<S17>/Rec_Single'
                                        *   '<S107>/Knock_Coh_ptfault'
                                        * CntKnockCohEE increment step
                                        */
extern uint8_T ENKNOCKAD;              /* Variable: ENKNOCKAD
                                        * Referenced by: '<S53>/ENKNOCKAD'
                                        * Enables the execution of the learning procedure and the update of the adaptive table (=1)
                                        */
extern uint8_T ENKNOCKADCORR;          /* Variable: ENKNOCKADCORR
                                        * Referenced by:
                                        *   '<S53>/ENKNOCKADCORR'
                                        *   '<S53>/ENKNOCKADCORR1'
                                        * Enables the adaptive correction for knocking on the spark advance (=1)
                                        */
extern uint8_T ENKNOCKCORR;            /* Variable: ENKNOCKCORR
                                        * Referenced by: '<S1>/Control_flow'
                                        * Enables the correction for knocking on the spark advance (=1)
                                        */
extern uint8_T ENTIPIN;                /* Variable: ENTIPIN
                                        * Referenced by: '<S21>/TipIn'
                                        * Enable tipin correction (=1)
                                        */
extern uint8_T KCOHDIAGSTEP;           /* Variable: KCOHDIAGSTEP
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * KCohDiagCnt increment step
                                        */
extern uint8_T KCORRINCDELAYI;         /* Variable: KCORRINCDELAYI
                                        * Referenced by: '<S108>/KCORRINCDELAYI'
                                        * Delay before further incrementing the knock correction (positive corr case)
                                        */
extern uint8_T NCYLKNOCKCOHMIN;        /* Variable: NCYLKNOCKCOHMIN
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * NCylKnockCoh threshold
                                        */
extern uint8_T THRCNTKNOCKCOH;         /* Variable: THRCNTKNOCKCOH
                                        * Referenced by:
                                        *   '<S17>/Rec_Single'
                                        *   '<S107>/Knock_Coh_ptfault'
                                        * CntKnockCohEE threshold
                                        */
extern uint8_T THRCNTKNOCKTIPIN;       /* Variable: THRCNTKNOCKTIPIN
                                        * Referenced by: '<S21>/TipIn'
                                        * Number of knock events to activate tipin correction
                                        */
extern uint8_T THRKCOHDIAGCNT;         /* Variable: THRKCOHDIAGCNT
                                        * Referenced by: '<S107>/Knock_Coh_ptfault'
                                        * KCohDiagCnt threshold
                                        */
extern uint8_T VTFORCESAK[8];          /* Variable: VTFORCESAK
                                        * Referenced by: '<S1>/Control_flow'
                                        * Force the correction for knocking on the spark advance (=1)
                                        */
extern uint8_T VTKCORRINCDELAYN[5];    /* Variable: VTKCORRINCDELAYN
                                        * Referenced by: '<S108>/VTKCORRINCDELAYN'
                                        * Delay before further incrementing the knock correction (normal case)
                                        */
extern int8_T TBKCORRDEC[20];          /* Variable: TBKCORRDEC
                                        * Referenced by: '<S107>/TBKCORRDEC'
                                        * Table of steps to reduce the spark advance in case of knocking
                                        */
extern int8_T TBKCORRMAX[60];          /* Variable: TBKCORRMAX
                                        * Referenced by:
                                        *   '<S55>/TBKCORRMAX'
                                        *   '<S87>/TBKCORRMAX'
                                        * Maximum total knock correction
                                        */
extern int8_T TBKNOCKREC[24];          /* Variable: TBKNOCKREC
                                        * Referenced by: '<S30>/TBKNOCKREC'
                                        * Table of steps to reduce the SA in case of knock recovery
                                        */
extern int8_T TBSATIPININIT[20];       /* Variable: TBSATIPININIT
                                        * Referenced by: '<S50>/TBSATIPININIT'
                                        * Table of init values for tipin offset
                                        */
extern int8_T VTSATIPINSTEP[5];        /* Variable: VTSATIPINSTEP
                                        * Referenced by: '<S49>/VTSATIPINSTEP'
                                        * Array of values for tipin steps
                                        */
extern void KnockCorr_Reset_CntKnockCoh(void);
extern void KnockCorr_Reset_Variables(void);
extern void KnockCorr_Rec_Single(void);
extern void KnockCor_Reset_Single_Variables(void);
extern void KnockCorr_Force_SAKnock(void);
extern void KnockCorr_KnockCorr_Slew(void);
extern void KnockCorr_TipIn(void);
extern void KnockCorr_Write_TbKnockAd(void);
extern void KnockCorr_Up_Ad_Table(void);
extern void KnockCorr_Mgm_Ad_Table_Disable(void);
extern void KnockCorr_Mgm_Ad_Table(void);
extern void KnockCorr_Adaptive_Correction(void);
extern void KnockCorr_Delta_Corr(void);
extern void KnockCorr_Ind_Cyl_Knock_Corr(void);
extern void KnockCorr_Tot_Knock_Corr(void);
extern void KnockCorr_fcn_EOA(void);
extern void KnockCorr_Control_flow(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint8_T CntKnockCohEE[8];       /* Knocking coherence diagnosis pre-filter */
extern int16_T TbKnockAd[480];         /* Tables of the adaptive coefficients for all cylinders */

/* Declaration for custom storage class: ImportFromFile */
extern uint8_T FlgResetAdaptParam;
extern uint8_T FlgTipIn;

#endif                                 /* RTW_HEADER_KnockCorr_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
