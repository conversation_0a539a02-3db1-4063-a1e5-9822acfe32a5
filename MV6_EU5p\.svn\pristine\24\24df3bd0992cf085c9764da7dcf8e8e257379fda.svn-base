/*
 * File: ExhValMgm_private.h
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1587
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 18 16:31:59 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#ifndef RTW_HEADER_ExhValMgm_private_h_
#define RTW_HEADER_ExhValMgm_private_h_
#include "rtwtypes.h"
#include "ExhValMgm.h"

/* Includes for objects with custom storage classes. */
#include "pby_mgm.h"
#include "diagmgm_out.h"
#include "engflag.h"
#include "digitalin.h"
#include "Trq_driver.h"
#include "exhvalmgm_eep.h"
#include "activeDiag.h"
#include "PTrain_Diag.h"
#include "gear_mgm.h"
#include "Syncmgm.h"
#include "saf3_mgm.h"
#include "analogin.h"
#include "Analogin.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T FORCEEXHVOUT;           /* Variable: FORCEEXHVOUT
                                        * Referenced by: '<S12>/FORCEEXHVOUT'
                                        * Manual exh. gas valve command voltage
                                        */
extern int16_T VOUTEXHVLMSHOLD;        /* Variable: VOUTEXHVLMSHOLD
                                        * Referenced by: '<S8>/Self_Machine'
                                        * Exh. valve command voltage to Hold valve
                                        */
extern int16_T VOUTEXHVPEAK;           /* Variable: VOUTEXHVPEAK
                                        * Referenced by: '<S8>/Self_Machine'
                                        * Exh. valve command voltage to Peak valve
                                        */
extern int16_T VOUTEXHVUMSHOLD;        /* Variable: VOUTEXHVUMSHOLD
                                        * Referenced by:
                                        *   '<S8>/Self_Machine'
                                        *   '<S135>/SafePosition'
                                        * Exh. valve command voltage to Hold valve
                                        */
extern int16_T BKEXHVALCME[5];         /* Variable: BKEXHVALCME
                                        * Referenced by: '<S71>/BKEXHVALCME'
                                        * Breakpoints of Coppia Media Effettiva (CME)
                                        */
extern int16_T BKMAXVEXHOUTOPEN[3];    /* Variable: BKMAXVEXHOUTOPEN
                                        * Referenced by:
                                        *   '<S122>/BKMAXVEXHOUTOPEN'
                                        *   '<S20>/BKMAXVEXHOUTOPEN'
                                        * Breakpoints
                                        */
extern int16_T RATEANGTGRMAX;          /* Variable: RATEANGTGRMAX
                                        * Referenced by: '<S70>/RATEANGTGRMAX'
                                        * Max Rate limiter
                                        */
extern int16_T RATEANGTGRMIN;          /* Variable: RATEANGTGRMIN
                                        * Referenced by: '<S70>/RATEANGTGRMIN'
                                        * Min Rate limiter
                                        */
extern int16_T RATEANGTGRPBYMAX;       /* Variable: RATEANGTGRPBYMAX
                                        * Referenced by: '<S70>/RATEANGTGRPBYMAX'
                                        * Max Rate Pby limiter
                                        */
extern int16_T TBEXHVALANGTGT[30];     /* Variable: TBEXHVALANGTGT
                                        * Referenced by:
                                        *   '<S71>/TBEXHVALANGTGT'
                                        *   '<S25>/TBEXHVALANGTGT'
                                        * Exh. valve angle target
                                        */
extern int16_T THRANGSELFEXH;          /* Variable: THRANGSELFEXH
                                        * Referenced by:
                                        *   '<S58>/THRANGSELFEXH'
                                        *   '<S59>/THRANGSELFEXH'
                                        * Angle threshold to launch the selflearning for LMS/UMS
                                        */
extern int16_T VTEXHVALWAVE[4];        /* Variable: VTEXHVALWAVE
                                        * Referenced by: '<S66>/VTEXHVALWAVE'
                                        * target angle points of time-history
                                        */
extern int16_T VTEXHVANGTGTIDLE[6];    /* Variable: VTEXHVANGTGTIDLE
                                        * Referenced by:
                                        *   '<S71>/VTEXHVANGTGTIDLE'
                                        *   '<S24>/VTEXHVANGTGTIDLE'
                                        * Angle target in Idle condition
                                        */
extern uint16_T PIDEXHKI;              /* Variable: PIDEXHKI
                                        * Referenced by: '<S113>/PIDEXHKI'
                                        * PID regulator integral gain factor
                                        */
extern uint16_T PIDEXHKP;              /* Variable: PIDEXHKP
                                        * Referenced by: '<S113>/PIDEXHKP'
                                        * PID regulator proportional gain factor
                                        */
extern uint16_T KFILTVANGEXHVAL;       /* Variable: KFILTVANGEXHVAL
                                        * Referenced by: '<S16>/KFILTVANGEXHVAL'
                                        * K Filter Input VAngExhValv
                                        */
extern uint16_T THVBATTEXHVDIAGEN;     /* Variable: THVBATTEXHVDIAGEN
                                        * Referenced by: '<S42>/THVBATTEXHVDIAGEN'
                                        * Threshold minimum of VBatt to enable ExhValve diagnosis and self
                                        */
extern uint16_T VTMAXVEXHOUTCLOSED[3]; /* Variable: VTMAXVEXHOUTCLOSED
                                        * Referenced by: '<S122>/VTMAXVEXHOUTCLOSED'
                                        * Saturator PI
                                        */
extern uint16_T VTMAXVEXHOUTOPEN[3];   /* Variable: VTMAXVEXHOUTOPEN
                                        * Referenced by: '<S122>/VTMAXVEXHOUTOPEN'
                                        * Saturator PI
                                        */
extern uint16_T GNVEXHOUTIDLE;         /* Variable: GNVEXHOUTIDLE
                                        * Referenced by: '<S122>/GNVEXHOUTIDLE'
                                        * Saturator gain idle
                                        */
extern uint16_T EXHERRTHRHIGH;         /* Variable: EXHERRTHRHIGH
                                        * Referenced by:
                                        *   '<S10>/Diag_Position'
                                        *   '<S111>/EXHERRTHRHIGH'
                                        * error High threshold below which to disable the exh. valve
                                        */
extern uint16_T EXHERRTHRLOW;          /* Variable: EXHERRTHRLOW
                                        * Referenced by: '<S111>/EXHERRTHRLOW'
                                        * error Low threshold below which to disable the exh. valve
                                        */
extern uint16_T EXHVALFORCEVPOS;       /* Variable: EXHVALFORCEVPOS
                                        * Referenced by: '<S65>/EXHVALFORCEVPOS'
                                        * Target voltage level for Zero position of actuator
                                        */
extern uint16_T EXHVMAXFBKACTIVEDIAG;  /* Variable: EXHVMAXFBKACTIVEDIAG
                                        * Referenced by:
                                        *   '<S15>/EXHVMAXFBKACTIVEDIAG'
                                        *   '<S65>/EXHVMAXFBKACTIVEDIAG'
                                        * Max voltage level produced by sensor
                                        */
extern uint16_T THREXHVSLSTAB;         /* Variable: THREXHVSLSTAB
                                        * Referenced by: '<S34>/THREXHVSLSTAB'
                                        * Self Stability threshold
                                        */
extern uint16_T BKEXHVALRPM[6];        /* Variable: BKEXHVALRPM
                                        * Referenced by: '<S71>/BKEXHVALRPM'
                                        * Breakpoints of rpm
                                        */
extern uint16_T BKEXHVALTIME[4];       /* Variable: BKEXHVALTIME
                                        * Referenced by:
                                        *   '<S66>/BKEXHVALTIME'
                                        *   '<S72>/BKEXHVALTIME'
                                        * Breakpoints of time for time-history
                                        */
extern uint16_T RPMTHRSELFEXH;         /* Variable: RPMTHRSELFEXH
                                        * Referenced by:
                                        *   '<S58>/RPMTHRSELFEXH'
                                        *   '<S59>/RPMTHRSELFEXH1'
                                        * min. threshold of angular speed to make exh. valve self-learning
                                        */
extern uint16_T TIMDISEXHVMOTION;      /* Variable: TIMDISEXHVMOTION
                                        * Referenced by: '<S135>/RecExhValve'
                                        * Time to confirm Recovery ExhValve position
                                        */
extern uint16_T TIMENEXHVMOTION;       /* Variable: TIMENEXHVMOTION
                                        * Referenced by: '<S135>/RecExhValve'
                                        * Time to deconfirm Recovery ExhValve position
                                        */
extern uint16_T TIMEXHADFBINACTIVE;    /* Variable: TIMEXHADFBINACTIVE
                                        * Referenced by: '<S11>/TIMEXHADFBINACTIVE'
                                        * Active Diag Time with PID in idle in zero position
                                        */
extern uint16_T TIMEXHADFBTIMEOUT;     /* Variable: TIMEXHADFBTIMEOUT
                                        * Referenced by: '<S11>/TIMEXHADFBTIMEOUT'
                                        * Active Diag Timeout to serch ExhValve zero position
                                        */
extern uint16_T TIMEXHTOCLOSED;        /* Variable: TIMEXHTOCLOSED
                                        * Referenced by: '<S8>/Self_Machine'
                                        * Time to close exh. valve
                                        */
extern uint16_T TIMEXHTOOPEN;          /* Variable: TIMEXHTOOPEN
                                        * Referenced by: '<S8>/Self_Machine'
                                        * Time to open exh. valve
                                        */
extern uint16_T TIMEXHTOSAFEOPEN;      /* Variable: TIMEXHTOSAFEOPEN
                                        * Referenced by: '<S135>/SafePosition'
                                        * Time ExhValve to obtain safe open recovery
                                        */
extern uint16_T TIMEXVSL;              /* Variable: TIMEXVSL
                                        * Referenced by: '<S98>/TIMEXVSL'
                                        * Counter
                                        */
extern uint16_T TIMOBSEXHVPOSHIP;      /* Variable: TIMOBSEXHVPOSHIP
                                        * Referenced by: '<S10>/Diag_Position'
                                        * Time ExhValve to observe position in heavy power movimentation
                                        */
extern uint16_T TIMOBSEXHVPOSLOP;      /* Variable: TIMOBSEXHVPOSLOP
                                        * Referenced by: '<S10>/Diag_Position'
                                        * Time ExhValve to observe position in low power movimentation
                                        */
extern uint16_T TIMPWONSELFDIS;        /* Variable: TIMPWONSELFDIS
                                        * Referenced by: '<S42>/TIMPWONSELFDIS'
                                        * Time to Enable possibile Self and funcional diagnosis
                                        */
extern uint8_T FOEXHVSELF;             /* Variable: FOEXHVSELF
                                        * Referenced by: '<S28>/FOEXHVSELF'
                                        * ExhV Self trigger
                                        */
extern uint8_T FORCEEXHOBJ;            /* Variable: FORCEEXHOBJ
                                        * Referenced by:
                                        *   '<S10>/Calc_FlgExhVDiagOn'
                                        *   '<S12>/FORCEEXHOBJ'
                                        *   '<S14>/FORCEEXHOBJ'
                                        *   '<S66>/FORCEEXHOBJ'
                                        *   '<S67>/FORCEEXHOBJ'
                                        *   '<S109>/FORCEEXHOBJ'
                                        *   '<S116>/FORCEEXHOBJ'
                                        *   '<S117>/FORCEEXHOBJ'
                                        * force exh. gas valve target: 0=Torque Law, 1=no op., 2=Time history, 3= force output
                                        */
extern uint8_T SELEXHVZEROPI;          /* Variable: SELEXHVZEROPI
                                        * Referenced by: '<S111>/SELEXHVZEROPI'
                                        * select zero PI
                                        */
extern uint8_T THANGEXHVPOSNUM;        /* Variable: THANGEXHVPOSNUM
                                        * Referenced by: '<S10>/Diag_Position'
                                        * Counter
                                        */
extern uint8_T THANGEXHVPOSNUMW;       /* Variable: THANGEXHVPOSNUMW
                                        * Referenced by: '<S10>/Diag_Position'
                                        * Counter
                                        */
extern uint8_T THEXHSELFTRIPENABLE;    /* Variable: THEXHSELFTRIPENABLE
                                        * Referenced by: '<S8>/Self_Machine'
                                        * Number of trip for enable Self request in running
                                        */
extern uint8_T TIMEXHVSLPEAK;          /* Variable: TIMEXHVSLPEAK
                                        * Referenced by: '<S8>/Self_Machine'
                                        * Exh. valve command time to peak valve
                                        */
extern uint8_T TIMEXHVSLSTAB;          /* Variable: TIMEXHVSLSTAB
                                        * Referenced by: '<S34>/TIMEXHVSLSTAB'
                                        * Exh. valve command time to stable valve
                                        */
extern void ExhValMgm_Subsystem(int32_T rtu_AngExhValPerc0, int16_T
  rtu_ExhVMaxTrg, int16_T rtu_ExhVMinTrg, int32_T *rty_AngExhValPerc, int32_T
  *rty_AngExhValPercTP, boolean_T *rty_FlgExhVNearMax, boolean_T
  *rty_FlgExhVNearMin);
extern void ExhValMgm_fc_Calc_MinMax_Init(DW_fc_Calc_MinMax_ExhValMgm_T *localDW);
extern void ExhValMgm_fc_Calc_MinMax(DW_fc_Calc_MinMax_ExhValMgm_T *localDW);
extern void ExhValMgm_fc_ExhVSelfSteadySt(int16_T rtu_VAngExhValF, uint8_T
  rtu_resetStab, DW_fc_ExhVSelfSteadySt_ExhVal_T *localDW);
extern void ExhValMgm_fc_EvaluateSelf(int16_T rtu_AngExhValPerc, boolean_T
  rtu_MasterDisable, uint16_T rtu_Rpm, uint8_T rtu_FlgSelfExhLMSOnce, uint8_T
  rtu_FlgSelfExhUMSOnce, uint32_T rtu_FlgLMSTripEnable, uint32_T
  rtu_FlgUMSTripEnable, uint8_T rtu_FlgSelfExhLMSDone, uint8_T
  rtu_FlgSelfExhUMSDone, uint8_T rtu_FlgSelfExhReq, uint16_T rtu_VBattery,
  int16_T rtu_ExhVMinTrg, int16_T rtu_ExhVMaxTrg, uint8_T *rty_LMSEnSelfCond,
  uint8_T *rty_UMSEnSelfCond, boolean_T *rty_FlgExVSelfWLamp,
  DW_fc_EvaluateSelf_ExhValMgm_T *localDW);
extern void ExhValMgm_fc_diag_calc(uint8_T rtu_ptfault, uint8_T *rty_stDiag,
  uint8_T *rty_cntDiagCall, DW_fc_diag_calc_ExhValMgm_T *localDW);
extern void ExhValMgm_Init(void);
extern void ExhValMgm_T10ms_Init(void);
extern void ExhValMgm_T10ms_Start(void);
extern void ExhValMgm_T10ms(void);

#endif                                 /* RTW_HEADER_ExhValMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
