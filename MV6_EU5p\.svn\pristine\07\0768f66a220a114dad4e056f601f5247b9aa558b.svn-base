/*
 * File: IdleCtfMgm_private.h
 *
 * Code generated for Simulink model 'IdleCtfMgm'.
 *
 * Model version                  : 1.2297
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Mar  1 09:28:46 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_IdleCtfMgm_private_h_
#define RTW_HEADER_IdleCtfMgm_private_h_
#include "rtwtypes.h"
#include "IdleCtfMgm.h"

/* Includes for objects with custom storage classes. */
#include "trq_driver.h"
#include "sparkmgm.h"
#include "canmgm.h"
#include "engflag.h"
#include "diagcanmgm.h"
#include "gaspos_mgm.h"
#include "GearPosClu_mgm.h"
#include "Gearshift_mgm.h"
#include "idle_mgm.h"
#include "syncmgm.h"
#include "temp_mgm.h"
#include "throttle_target.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T TWCUTOFF;               /* Variable: TWCUTOFF
                                        * Referenced by: '<S27>/TWCUTOFF'
                                        * Twater Threshold for cutoff enable
                                        */
extern int16_T BKCMEDRIVERMAX[8];      /* Variable: BKCMEDRIVERMAX
                                        * Referenced by: '<S13>/BKCMEDRIVERMAX'
                                        * Bkp values for VTKFCTFENTRYGAIN
                                        */
extern int16_T BKKFCTFEXITNEUT[3];     /* Variable: BKKFCTFEXITNEUT
                                        * Referenced by: '<S9>/BKKFCTFEXITNEUT'
                                        * Bk BKKFCTFEXITNEUT
                                        */
extern int16_T CMETHRCTF;              /* Variable: CMETHRCTF
                                        * Referenced by: '<S29>/CMETHRCTF'
                                        * CmeDriver threshold for cutoff enable
                                        */
extern int16_T CMETHRCTFHYST;          /* Variable: CMETHRCTFHYST
                                        * Referenced by: '<S29>/CMETHRCTFHYST'
                                        * Threshold applied to CMETHRCTF
                                        */
extern int16_T DCMEDRIVERMAX;          /* Variable: DCMEDRIVERMAX
                                        * Referenced by: '<S6>/Chart'
                                        * CmeDriverMax value to recognize Cme decrease
                                        */
extern uint16_T KFILTCMEDRIVERMAX;     /* Variable: KFILTCMEDRIVERMAX
                                        * Referenced by: '<S8>/KFILTCMEDRIVERMAX'
                                        * CmeDriverMax Filtering factor
                                        */
extern uint16_T VTKFCTFENTRY0[7];      /* Variable: VTKFCTFENTRY0
                                        * Referenced by: '<S6>/VTKFCTFENTRY0'
                                        * Cutoff entry filter coeff.
                                        */
extern uint16_T VTKFCTFENTRY1[7];      /* Variable: VTKFCTFENTRY1
                                        * Referenced by: '<S6>/VTKFCTFENTRY1'
                                        * Cutoff entry filter coeff. (EngBrake != 0)
                                        */
extern uint16_T VTKFCTFENTRYDWSF[7];   /* Variable: VTKFCTFENTRYDWSF
                                        * Referenced by: '<S6>/VTKFCTFENTRYDWSF'
                                        * Cutoff entry filter coeff on Downshift
                                        */
extern uint16_T VTKFCTFENTRYGAIN0[8];  /* Variable: VTKFCTFENTRYGAIN0
                                        * Referenced by: '<S13>/VTKFCTFENTRYGAIN0'
                                        * Gain applied to cutoff-entry filter constant
                                        */
extern uint16_T VTKFCTFENTRYGAIN1[8];  /* Variable: VTKFCTFENTRYGAIN1
                                        * Referenced by: '<S13>/VTKFCTFENTRYGAIN1'
                                        * Gain applied to cutoff-entry filter constant - (EngBrake != 0)
                                        */
extern uint16_T VTKFCTFEXIT[4];        /* Variable: VTKFCTFEXIT
                                        * Referenced by: '<S9>/VTKFCTFEXIT'
                                        * Cutoff exit filter coeff.
                                        */
extern uint16_T VTKFCTFEXITNEUT[3];    /* Variable: VTKFCTFEXITNEUT
                                        * Referenced by: '<S9>/VTKFCTFEXITNEUT'
                                        * Kf
                                        */
extern uint16_T VTKFGAINDNCUTOFF[5];   /* Variable: VTKFGAINDNCUTOFF
                                        * Referenced by: '<S13>/VTKFGAINDNCUTOFF'
                                        * KfGain Cutoff in down shift
                                        */
extern uint16_T BKGASPOSCTF[4];        /* Variable: BKGASPOSCTF
                                        * Referenced by: '<S9>/BKGASPOSCTF'
                                        * Breakpoints of GasPosCC for VTKFCTFEXIT
                                        */
extern uint16_T BKKFGAINDNCUTOFF[5];   /* Variable: BKKFGAINDNCUTOFF
                                        * Referenced by: '<S13>/BKKFGAINDNCUTOFF'
                                        * Bkp values for VTKFGAINDNCUTOFF
                                        */
extern uint16_T TIMEDISABLECUTOFF;     /* Variable: TIMEDISABLECUTOFF
                                        * Referenced by: '<S27>/TIMEDISABLECUTOFF'
                                        * Cutoff Disable Time after Cutoff
                                        */
extern uint16_T VTRPMIDLETHR0[7];      /* Variable: VTRPMIDLETHR0
                                        * Referenced by: '<S30>/VTRPMIDLETHR0'
                                        * (SR) Idle entry rpm basic offset
                                        */
extern uint16_T VTRPMIDLETHR1[7];      /* Variable: VTRPMIDLETHR1
                                        * Referenced by: '<S30>/VTRPMIDLETHR1'
                                        * (SR) Idle entry rpm basic offset (EngBrake = 0)
                                        */
extern uint8_T RPMIDLETHRHYST;         /* Variable: RPMIDLETHRHYST
                                        * Referenced by: '<S30>/RPMIDLETHRHYST'
                                        * Idle entry rpm hysteresis
                                        */
extern uint8_T ENCUTOFF;               /* Variable: ENCUTOFF
                                        * Referenced by: '<S27>/ENCUTOFF'
                                        * Cutoff enable flag
                                        */
extern void IdleCtfMgm_CalcCmeDriveMax(int16_T rtu_CmeDriver,
  rtDW_CalcCmeDriveMax_IdleCtfMgm *localDW);
extern void IdleCtfMgm_Init(void);
extern void IdleCtfMgm_Calc(void);
extern void IdleCtfMgm_T10ms(void);

#endif                                 /* RTW_HEADER_IdleCtfMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
