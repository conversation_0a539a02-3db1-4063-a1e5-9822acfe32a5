/*****************************************************************************************************************/
/* $HeadURL::                                                                                                    $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif
#include "lamp_mgm.h"

#ifdef _BUILD_LAMPMGM_

#pragma ghs section rodata=".calib" 
//Initial pad lamps [s]
__declspec(section ".calib") uint8_T SELPHYSLAMPGPIO = 0;
//Initial lamps logic setup
__declspec(section ".calib") uint8_T LOGLAMPOUT[NUMLOGLMP] = {LOGLAMP_1_MAP, LOGLAMP_2_MAP, LOGLAMP_3_MAP, LOGLAMP_4_MAP};
//Initial lamps logic setup
__declspec(section ".calib") uint8_T LOGLAMPPERIOD[NUMLOGLMP] = {LOGLAMP_1_PERIOD, LOGLAMP_2_PERIOD, LOGLAMP_3_PERIOD, LOGLAMP_4_PERIOD};
//Initial lamps logic setup
__declspec(section ".calib") uint8_T LOGLAMPTON[NUMLOGLMP] = {LOGLAMP_1_DUTY, LOGLAMP_2_DUTY, LOGLAMP_3_DUTY, LOGLAMP_4_DUTY};
//Initial lamps test time [s]
__declspec(section ".calib") uint8_T LAMPTESTTIME[NUMPHYSLMP] = {0, 0, 0, 0}; //(3.00000000000000000*10)
//Logic safety lamp test switch [flag]
__declspec(section ".calib") uint8_T SAFLAMPTESTSW =  0;   // 0
//Logic safety lamp test switch [flag]
__declspec(section ".calib") uint8_T MILLAMPTESTSW =  0;   // 0
//Logic warning lamp test switch [flag]
__declspec(section ".calib") uint8_T WARNLAMPTESTSW =  0;   // 0
//Logic warning lamp test switch [flag]
__declspec(section ".calib") uint8_T MISFLAMPTESTSW =  0;   // 0
//Choose lamp command (0=direct; 1=CAN; 2=ALL) [state]
__declspec(section ".calib") uint8_T FORCELAMP =  ALL_LAMP;   // 0
//MIL with MIL lamp
__declspec(section ".calib") uint8_T MILMILEN =  1;   // 1

#endif // _BUILD_LAMPMGM_
