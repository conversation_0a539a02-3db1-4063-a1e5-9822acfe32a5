/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                          $   */
/* $Revision::                                                                                                             $   */
/* $Date::                                                                                                                 $   */
/* $Author::                                                                                                               $   */
/*******************************************************************************************************************************/
#ifndef _ANTITAMPERING_OUT_H_
#define _ANTITAMPERING_OUT_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"

/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

/*!\egroup*/
/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/

/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T ATEnable;
extern uint8_T ATRoutineEnable;
extern uint8_T InjVINEnable;
extern uint16_T CntInjVINEE;
extern uint8_T ATRoutineDashVINCheck;
extern uint8_T InjEnableAT;

extern CALQUAL uint8_T  ECUVINLOCK;


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
extern void AntiTampering_Init(void);
extern void AntiTampering_100ms(void);
extern void AntiTampering_UpdateVINFailCnt(void);




#endif // _ANTITAMPERING_OUT_H_

/****************************************************************************
 ****************************************************************************/


