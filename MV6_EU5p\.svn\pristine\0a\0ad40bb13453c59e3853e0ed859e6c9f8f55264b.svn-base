/*
 * File: PAtmModel_private.h
 *
 * Code generated for Simulink model 'PAtmModel'.
 *
 * Model version                  : 1.773
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 24 16:04:22 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PAtmModel_private_h_
#define RTW_HEADER_PAtmModel_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "PAtmModel.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint16_T PRESATMGAIN;           /* Variable: PRESATMGAIN
                                        * Referenced by:
                                        *   '<S11>/PRESATMGAIN'
                                        *   '<S14>/PRESATMGAIN'
                                        * PATMMODEL.PRESATMGAIN: Atmospheric Pressure Gain
                                        */
extern uint16_T PATMADAPTGAIN;         /* Variable: PATMADAPTGAIN
                                        * Referenced by: '<S20>/PATMADAPTGAIN'
                                        * PATMMODEL.PATMADAPTGAIN: Gain for atmpspheric pressure correction in running mode
                                        */
extern uint16_T VTANGTHRPATMMAX[6];    /* Variable: VTANGTHRPATMMAX
                                        * Referenced by: '<S19>/VTANGTHRPATMMAX'
                                        * PATMMODEL.VTANGTHRPATMMAX: AngThrPAtmMax vector
                                        */
extern uint16_T VTANGTHRPATMMIN[6];    /* Variable: VTANGTHRPATMMIN
                                        * Referenced by: '<S19>/VTANGTHRPATMMIN'
                                        * PATMMODEL.VTANGTHRPATMMIN: AngThrPAtmMin vector
                                        */
extern uint16_T VPRESATMMAX;           /* Variable: VPRESATMMAX
                                        * Referenced by:
                                        *   '<S11>/VPRESATMMAX'
                                        *   '<S14>/VPRESATMMAX'
                                        * PATMMODEL.VPRESATMMAX: Maximum value of VPresAtm for diagnosis
                                        */
extern uint16_T VPRESATMMIN;           /* Variable: VPRESATMMIN
                                        * Referenced by:
                                        *   '<S11>/VPRESATMMIN'
                                        *   '<S14>/VPRESATMMIN'
                                        * PATMMODEL.VPRESATMMIN: Minimum value of VPresAtm for diagnosis
                                        */
extern uint16_T BKRPMANGPATM[6];       /* Variable: BKRPMANGPATM
                                        * Referenced by: '<S19>/BKRPMANGPATM'
                                        * PATMMODEL.BKRPMANGPATM: Rpm breakpoint values
                                        */
extern uint16_T NRUNWAITPATM;          /* Variable: NRUNWAITPATM
                                        * Referenced by: '<S5>/Running_Chart'
                                        * PATMMODEL.NRUNWAITPATM: Number of TDC tasks before MapSignal acquisition
                                        */
extern uint16_T NWAITPATM;             /* Variable: NWAITPATM
                                        * Referenced by: '<S4>/PowerOn_Chart'
                                        * PATMMODEL.NWAITPATM: Number of 5ms tasks before MapSignal acquisition
                                        */
extern uint16_T PRESATMMAX;            /* Variable: PRESATMMAX
                                        * Referenced by:
                                        *   '<S11>/PRESATMMAX'
                                        *   '<S14>/PRESATMMAX'
                                        *   '<S18>/PRESATMMAX'
                                        *   '<S21>/PRESATMMAX'
                                        * PATMMODEL.PRESATMMAX: (SR) Maximum Atmospheric Pressure
                                        */
extern uint16_T PRESATMMIN;            /* Variable: PRESATMMIN
                                        * Referenced by:
                                        *   '<S11>/PRESATMMIN'
                                        *   '<S14>/PRESATMMIN'
                                        *   '<S18>/PRESATMMIN'
                                        *   '<S21>/PRESATMMIN'
                                        * PATMMODEL.PRESATMMIN: (SR) Minimum Atmospheric Pressure
                                        */
extern uint16_T PRESATMNOM;            /* Variable: PRESATMNOM
                                        * Referenced by:
                                        *   '<S4>/PowerOn_Chart'
                                        *   '<S5>/Running_Chart'
                                        *   '<S11>/PRESATMNOM'
                                        * PATMMODEL.PRESATMNOM: (SR) Nominal atmospheric pressure
                                        */
extern uint16_T PRESATMOFFSET;         /* Variable: PRESATMOFFSET
                                        * Referenced by:
                                        *   '<S11>/PRESATMOFFSET'
                                        *   '<S14>/PRESATMOFFSET'
                                        * PATMMODEL.PRESATMOFFSET: Atmospheric Pressure Offset
                                        */
extern uint8_T PATMBUFFSIZE;           /* Variable: PATMBUFFSIZE
                                        * Referenced by:
                                        *   '<S4>/PowerOn_Chart'
                                        *   '<S5>/Running_Chart'
                                        * PATMMODEL.PATMBUFFSIZE: MapSignal buffer size
                                        */
extern uint8_T USEPATMSENSOR;          /* Variable: USEPATMSENSOR
                                        * Referenced by:
                                        *   '<S2>/USEPATMSENSOR'
                                        *   '<S3>/USEPATMSENSOR'
                                        *   '<S4>/PowerOn_Chart'
                                        *   '<S5>/Running_Chart'
                                        * PATMMODEL.USEPATMSENSOR: Calibration flag to enable PresAtm from sensor
                                        */
extern void PAtmModel_PresAtm_Saturation(void);
extern void PAtmModel_AngThrPAtm_Calc(uint16_T rtu_Rpm, uint16_T rtu_AngThrottle,
  int16_T rtu_DAngThr, rtB_AngThrPAtm_Calc_PAtmModel *localB);
extern void PAtmModel_T100ms(void);
extern void PAtmModel_TDC(void);
extern void PAtmModel_Init(void);
extern void PAtmModel_T5ms(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint16_T PresAtm;               /* '<Root>/_DataStoreBlk_1' */

/* PATMMODEL.PresAtm: Atmospheric pressure */

/* Declaration for custom storage class: ImportFromFile */
extern uint16_T AngThrottle;           /* '<Root>/AngThrottle' */
extern uint32_T CntAbsTdc;             /* '<Root>/CntAbsTdc' */
extern int16_T DAngThr;                /* '<Root>/DAngThr' */
extern uint8_T FlgSyncReady;           /* '<Root>/FlgSyncReady' */
extern uint16_T MapSignalPAtm;         /* '<Root>/MapSignalPAtm' */
extern uint16_T Rpm;                   /* '<Root>/Rpm' */
extern uint8_T StSync;                 /* '<Root>/StSync' */
extern uint16_T VPresAtm;              /* '<Root>/VPresAtm' */
extern uint8_T VtRec[22];              /* '<Root>/VtRec' */

#endif                                 /* RTW_HEADER_PAtmModel_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
