/** #####################################################################
**     Filename  : RLI.H
**     Project   :
**     Processor : MPC5554
**     Version   : 
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 18/09/2007
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** #####################################################################*/

#ifndef _RLI_H_
#define _RLI_H_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "injcmd.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

#define NUM_OF_RLIFUNC (8)
#define RLI_LENGTH  180
//#define USE_REVERSE_BYTE // if active, byte order is inverted VAR_SW[n-1,n-2,...0] ~ VAR_TOOL[0..,n-2,n-1]

#define FUNC_RLICONV      (0)
#define FUNC_CONVONOFF    (1)
#define FUNC_SESSIONCONV  (2)
#define FUNC_INJCONV      (3)
#define FUNC_GEAR_SHIFT   (4)
#define FUNC_WRNLAMPST    (5)
#define FUNC_ANTITAMPER   (6)
#define FUNC_CONVSTMISF   (7)

//#define SPARKADVANCE_LENGHT (20)
#define INJECTION_LENGHT (20)
#define STRESSCONTROL_LENGHT (28+36+ESV_LENGTH+2+2)
#define IUMPR_LENGTH     (40)
//#define LAMBDACONTROL_LENGHT (10)



#define RLISTATUS_BIT0  0x01
#define RLISTATUS_BIT1  0x02
#define RLISTATUS_BIT2  0x04
#define RLISTATUS_BIT3  0x08
#define RLISTATUS_BIT4  0x10
#define RLISTATUS_BIT5  0x20
#define RLISTATUS_BIT6  0x40
#define RLISTATUS_BIT7  0x80
#define RESET_RLISTATUS RLISTATUS_BIT0

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

typedef enum {
    ENGSTREEWARM_ID     = 0x01,
    TRACKUSETIME_ID     = 0x02,
    ODOMETER_ID         = 0x03,
    OPERATINGHOURS_ID   = 0x04,
    CRASHEVENTCNT_ID    = 0x05,
    STRESSCONDEVTCNT_ID = 0x06,
    OPERGHOURSRSTCNT_ID = 0x07,
    OVERDRIVECNT_ID     = 0x08,
    LENGOVERDRIVEDUR_ID = 0x09,
    MAXENGOVERDRIVE_ID  = 0x0A,
    ECUFLSHCNT_ID       = 0x0B,
    LASTECUFLSHODOM_ID  = 0x0C,
    WARLAMPONTIME_ID    = 0x0D,
    SAFONTIME_ID        = 0x0E,
    ENGINESTREESCOLD_ID = 0x0F,
    DRLSIGNAL_ID        = 0x10,
    S2FAULT_ID          = 0x1A,
    S3STOPCAUSE_ID      = 0x1B,
    VLC4RAW_ID          = 0x1C,
    BUTTONMAP_ID        = 0x1D,
    KWPSESSION_ID       = 0x1F,
    ODOMILACTIVE_ID     = 0x21,
    RESERVED_1_ID       = 0x2A,
    RESERVED_2_ID       = 0x2B,
    RPM_ID              = 0x30,
    LOAD_ID             = 0x31,
    TWATER_ID           = 0x32,
    TWATERMODEL_ID      = 0x33,
    VTWATER_ID          = 0x34,
    TAIR_ID             = 0x35,
    VTAIR_ID            = 0x36,
    GASPOS_ID           = 0x37,
    INDUSEDSENS_ID      = 0x38,
    GASSWITCHSTATUS_ID  = 0x39,
    VGASPOS1_ID         = 0x3A,
    VGASPOS2_ID         = 0x3C,
    ANGTHROTTLE_ID      = 0x3D,
    STTHRACTIVE_ID      = 0x3E,
    VANGTHROTTLE1_ID    = 0x3F,
    VANGTHROTTLE2_ID    = 0x40,
    VANGTHRLH1_ID       = 0x41,
    VANGTHRLH2_ID       = 0x42,
    VANGTHRMIN1_ID      = 0x43,
    VANGTHRMIN2_ID      = 0x44,
    STDBWSELF_ID        = 0x45,
    STDBWSELFERROR_ID   = 0x46,
    DANGTHR_ID          = 0x47,
    GEARPOS_ID          = 0x48,
    VGEARPOS_ID         = 0x49,
    EXVALVSENSV_ID      = 0x4A,
    EXVALVPOS_ID        = 0x4B,
    EXVALVTARGPOS_ID    = 0x4C,
    CLUTCHSTATE_ID      = 0x4D,
    GASSWITCH_ID        = 0x50,
    VSPEEDFRONT_ID      = 0x51,
    VSPEEDREAR_ID       = 0x52,
    PRESINTAKE_ID       = 0x53,
    VMAPSIGNAL_ID       = 0x54,
    PRESATM_ID          = 0x55,
    PRESINTK0_ID        = 0x56,
    VBATTERY_ID         = 0x57,
    SAOUT_ID            = 0x58,
    STECU_ID            = 0x59,
    VGEARPOS0_ID        = 0x5A,
    VLAMP_ID            = 0x5B,
    VSPARKPEAKCYL_ID    = 0x5C,
    VCRASHSIGNAL_ID     = 0x5D,
    DIAGBRAKELAMP_ID    = 0x5E,
    USELB3SIGNAL_ID     = 0x5F,
    EFFDWELLTIME1_ID    = 0x60,
    EFFDWELLTIME2_ID    = 0x61,
    EFFDWELLTIME3_ID    = 0x62,
    EFFDWELLTIME4_ID    = 0x63,
    INJTIME1_ID         = 0x64,
    INJTIME2_ID         = 0x65,
    INJTIME3_ID         = 0x66,
    INJTIMR4_ID         = 0x67,
    INJTIME5_ID         = 0x68,
    INJTIME6_ID         = 0x69,
    INJTIME7_ID         = 0x6A,
    INJTIMR8_ID         = 0x6B,
    GEARDOWNSIGNAL_ID   = 0x6C,
    GEARUPSIGNAL_ID     = 0x6D,
    REARPOS_ID          = 0x6E,
    HEATEDGRIP_ID       = 0x6F,
    RPMIDLEOBJ_ID       = 0x70,
    IDLETRQ_ID          = 0x71,
    CMFP_ID             = 0x72,
    QAIRCYL_ID          = 0x73,
    QFUELCY_ID          = 0x74,
    VLAMBDA_ID          = 0x75,
    VLAMBDASTATE_ID     = 0x76,
    STAFCTRL_ID         = 0x77,
    DELTALAMCL_ID       = 0x78,
    DELTALAMCORRAD_ID   = 0x79,
    ENGCOOLTEMP2_ID     = 0x7A,
    ENGCOOLTEMPSENS2_ID = 0x7B,
    VSENSSUPPLY1_ID     = 0x7C,
    VSENSSUPPLY2_ID     = 0x7D,
    BRAKESIGNAL_ID      = 0x7E,
    USELC4SIGNAL_ID     = 0x7F,
    INJENABLE_ID        = 0x80,
    KEYSIGNAL_ID        = 0x81,
    STOPSIGNAL_ID       = 0x82,
    CRASHSIGNAL_ID      = 0x83,
    TRESTLESIGNAL_ID    = 0x84,
    S2FLGDISRELAY_ID    = 0x85,
    S3FLGALLOWSTART_ID  = 0x86,
    S3FLGDISRELAY_ID    = 0x87,
    INJENABLECAN_ID     = 0x88,
    VTREC11_ID          = 0x89,
    FANCOILCMD_ID       = 0x8A,
    FANCOILCMD2_ID      = 0x8B,
    STARTSIGNAL_ID      = 0x8C,
    ENGINESTARTCMD_ID   = 0x8D,
    LOADCMD_ID          = 0x8E,
    LAMCAMCMD_ID        = 0x8F,
    LOWBEAMCMD_ID       = 0x90,
    REARLIGHTSTATUS_ID  = 0x91,
    RIDINGMODE_ID       = 0x92,
    SETTRACCTRL_ID      = 0x93,
    STSYNC_ID           = 0x94,
    FLGSYNCPHASED_ID    = 0x95,
    FLGVBATRESSYNC_ID   = 0x96,
    STPURGEFUELLINE_ID  = 0x97,
    GEARSHIFT_ID        = 0x98,
    RECTHRREQ_ID        = 0x99,
    RECLIMTORQUEGAS_ID  = 0x9A,
    VEHOPTCONFIG_ID     = 0x9B,
    ROLLCAN_ID          = 0x9C,
    VGEARSHIFTUP_ID     = 0x9D,
    PITCHCAN_ID         = 0x9E,
    VLB3RAW_ID          = 0x9F,
    INJECTION_ID        = 0xA0,
    STRESS_CONTROL_ID   = 0xA1,
    ION_STATE_CYL0_ID   = 0xA2,
    ION_STATE_CYL1_ID   = 0xA3,
    ION_STATE_CYL2_ID   = 0xA4,
    ION_STATE_CYL3_ID   = 0xA5,
    INT_ION_CYL0_ID     = 0xA6,
    INT_ION_CYL1_ID     = 0xA7,
    INT_ION_CYL2_ID     = 0xA8,
    INT_ION_CYL3_ID     = 0xA9,
    CNTCRANK_ID         = 0xB3,
    LAMMILCMD_ID        = 0xB4,
    BUTTONMAPSIGNAL_ID  = 0xB5,
    USELA3SIGNAL_ID     = 0xB6,
    FLG_SELF_DISABLE_ID = 0xB7,
    ANTITAMPEN_ID       = 0xB8,
    ANTITAMPREN_ID      = 0xB9,
    ANTITAMPRRES_ID     = 0xBA,
    INJVINEN_ID         = 0xBB,
    INJVINFAILCNT_ID    = 0xBC,
    VINDASH_ID          = 0xBD,
    VINECU_ID           = 0xBE,
    MAINRELAY_ID        = 0xBF,
    RPMLIMSTRESS_ID     = 0xC1,
    VSFOFFSET_ID        = 0xC2,
    QSHIFTEN_ID         = 0xC3,
    QSHIFTTYPE_ID       = 0xC4,
    TCZERO_ID           = 0xC5,
    IDVERSION_ID        = 0xC6,
    MISFIRE_STATE_ID    = 0xC7,
    CIR_SEL_ION_ACQ_ID  = 0xC8,
    START_CH_ID         = 0xC9,
    START_TH_ID         = 0xCA,
    THPEAK_ID           = 0xCB,
    IONABSTDC_ID        = 0xCD,
    IUMPR_ID            = 0xCE,
    DRL_ID              = 0xCF,
    CNTTDCCRK_ID        = 0xD0,
    LAMPONTIMERSTCNT_ID = 0xDE,
    TEMPUNLOCKCNTDOWN_ID= 0xE2,
    TRANSPORTLOCKST_ID  = 0xE3,
    CRANKSTREN_ID       = 0xE4,
    EELCTRIP_ID         = 0xE5,
    VLAMBDA_2_ID        = 0xE6,
    CAT_ID              = 0xE7,
    DCLAMHEATER2_ID     = 0xE8,
    FLGEOL_ID           = 0xEF
} typeRLI;

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * getRLI - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void getRLI(uint8_t * data,
             uint8_t * dataLength,
             uint8_t rli);



#endif //_RLI_H_
