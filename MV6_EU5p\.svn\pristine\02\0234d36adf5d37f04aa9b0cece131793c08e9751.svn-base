/*
 * File: AnalogQS.h
 *
 * Code generated for Simulink model 'AnalogQS'.
 *
 * Model version                  : 1.655
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Nov 16 11:29:39 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_AnalogQS_h_
#define RTW_HEADER_AnalogQS_h_
#ifndef AnalogQS_COMMON_INCLUDES_
# define AnalogQS_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "diagmgm_out.h"
#endif                                 /* AnalogQS_COMMON_INCLUDES_ */

#include "AnalogQS_types.h"

/* Includes for objects with custom storage classes. */
#include "canmgm.h"
#include "analog_QS.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define ID_ANALOG_QS                   27766292U                 /* Referenced by: '<S2>/ID_ANALOG_QS' */

/* mask */
#define ST_QS_CHANGE_DOWN              6U                        /* Referenced by: '<S7>/QS_Manager' */
#define ST_QS_CHANGE_UP                2U                        /* Referenced by: '<S7>/QS_Manager' */
#define ST_QS_FAULT                    4U                        /* Referenced by: '<S7>/QS_Manager' */
#define ST_QS_FIND_DOWN                5U                        /* Referenced by: '<S7>/QS_Manager' */
#define ST_QS_FIND_UP                  1U                        /* Referenced by: '<S7>/QS_Manager' */
#define ST_QS_IDLE                     0U                        /* Referenced by: '<S7>/QS_Manager' */
#define TIM_QS_STEP_INC                1U                        /* Referenced by: '<S7>/QS_Manager' */

/* Block signals (default storage) */
typedef struct {
  uint8_T gearDownSignal;              /* '<S7>/QS_Manager' */
  uint8_T gearUpSignal;                /* '<S7>/QS_Manager' */
  uint8_T stVQuickShift;               /* '<S7>/QS_Manager' */
  uint8_T relAQSCmd;                   /* '<S7>/QS_Manager' */
  uint8_T out;                         /* '<S16>/Chart' */
  uint8_T GearDownSignal_k;            /* '<S13>/Chart' */
  uint8_T GearNSignal_c;               /* '<S13>/Chart' */
  uint8_T GearShiftWait_g;             /* '<S13>/Chart' */
  uint8_T GearUpSignal_j;              /* '<S13>/Chart' */
  uint8_T diagId;                      /* '<S13>/Chart' */
  uint8_T ptFault;                     /* '<S13>/Chart' */
} BlockIO_AnalogQS;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  uint32_T cntThMatch;                 /* '<S7>/QS_Manager' */
  struct {
    uint_T is_ST_QS_FIND:3;            /* '<S7>/QS_Manager' */
    uint_T is_ST_QS_DIAG:3;            /* '<S7>/QS_Manager' */
    uint_T is_c4_AnalogQS:3;           /* '<S16>/Chart' */
    uint_T is_active_c1_AnalogQS:1;    /* '<S7>/QS_Manager' */
    uint_T is_active_c4_AnalogQS:1;    /* '<S16>/Chart' */
    uint_T is_active_c3_AnalogQS:1;    /* '<S13>/Chart' */
  } bitsForTID0;

  uint8_T Memory3_PreviousInput;       /* '<S16>/Memory3' */
  uint8_T Memory_PreviousInput;        /* '<S16>/Memory' */
  uint8_T Memory1_PreviousInput;       /* '<S16>/Memory1' */
  uint8_T Memory2_PreviousInput;       /* '<S16>/Memory2' */
  uint8_T cnt;                         /* '<S16>/Chart' */
} D_Work_AnalogQS;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState Trig_2_fc1_Trig_ZCE;      /* '<S1>/Trig_2_fc1' */
  ZCSigState Trig_2_fc_Trig_ZCE;       /* '<S1>/Trig_2_fc' */
} PrevZCSigStates_AnalogQS;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_5ms;                      /* '<Root>/ev_5ms' */
} ExternalInputs_AnalogQS;

/* Block signals (default storage) */
extern BlockIO_AnalogQS AnalogQS_B;

/* Block states (default storage) */
extern D_Work_AnalogQS AnalogQS_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_AnalogQS AnalogQS_U;

/* Model entry point functions */
extern void AnalogQS_initialize(void);
extern void AnalogQS_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('analogQS_gen/AnalogQS')    - opens subsystem analogQS_gen/AnalogQS
 * hilite_system('analogQS_gen/AnalogQS/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'analogQS_gen'
 * '<S1>'   : 'analogQS_gen/AnalogQS'
 * '<S2>'   : 'analogQS_gen/AnalogQS/Reset'
 * '<S3>'   : 'analogQS_gen/AnalogQS/T5ms'
 * '<S4>'   : 'analogQS_gen/AnalogQS/Trig_2_fc'
 * '<S5>'   : 'analogQS_gen/AnalogQS/Trig_2_fc1'
 * '<S6>'   : 'analogQS_gen/AnalogQS/Reset/fc_Reset'
 * '<S7>'   : 'analogQS_gen/AnalogQS/T5ms/Analog_Chart'
 * '<S8>'   : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart'
 * '<S9>'   : 'analogQS_gen/AnalogQS/T5ms/Analog_Chart/DiagQS'
 * '<S10>'  : 'analogQS_gen/AnalogQS/T5ms/Analog_Chart/QS_Manager'
 * '<S11>'  : 'analogQS_gen/AnalogQS/T5ms/Analog_Chart/QS_interp'
 * '<S12>'  : 'analogQS_gen/AnalogQS/T5ms/Analog_Chart/DiagQS/Set_DiagState'
 * '<S13>'  : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart/Subsystem'
 * '<S14>'  : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart/Subsystem/Chart'
 * '<S15>'  : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart/Subsystem/DiagQS'
 * '<S16>'  : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart/Subsystem/FO_Input'
 * '<S17>'  : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart/Subsystem/DiagQS/Set_DiagState'
 * '<S18>'  : 'analogQS_gen/AnalogQS/T5ms/Digital_Chart/Subsystem/FO_Input/Chart'
 */
#endif                                 /* RTW_HEADER_AnalogQS_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
