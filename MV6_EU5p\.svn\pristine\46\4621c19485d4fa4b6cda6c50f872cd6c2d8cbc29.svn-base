/*
 * File: FOInjCtfMgm.c
 *
 * Code generated for Simulink model 'FOInjCtfMgm'.
 *
 * Model version                  : 1.360
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jan  5 15:31:29 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (30), Warnings (2), Error (0)
 */

#include "FOInjCtfMgm.h"
#include "FOInjCtfMgm_private.h"

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_FOINJCTFMGM_

/* Block signals and states (default storage) */
D_Work_FOInjCtfMgm_T FOInjCtfMgm_DWork;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint32_T IDFOInjCtfMgm;

/* ID Version */
uint8_T IdxFOInjCutoff;

/* Idx */
uint8_T SymFOInjCutoff[4];

/* Cutoff FO symulaterd */
uint16_T TimesFOInjCutoff;

/* counter */
uint8_T VtFOInjCutoff[4];

/* Cutoff FO Inj */

/* Output and update for function-call system: '<S1>/Init' */
void FOInjCtfMgm_Init(void)
{
  int32_T i;

  {
    /* user code (Output function Header for TID1) */

    /* System '<S1>/Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    FOInjCtfMgm_initialize();
    for (i = 0; i < 4; i++) {
      /* SignalConversion generated from: '<S3>/SymFOInjCutoff' */
      SymFOInjCutoff[(i)] = 0U;

      /* SignalConversion generated from: '<S3>/VtFOInjCutoff' incorporates:
       *  SignalConversion generated from: '<S3>/SymFOInjCutoff'
       */
      VtFOInjCutoff[(i)] = 0U;
    }

    /* Constant: '<S3>/ID_FOINJ_CTF_MGM' */
    IDFOInjCtfMgm = ID_FOINJ_CTF_MGM;

    /* Constant: '<S3>/ZERO1' */
    /* Gateway: FOInjCtfMgm/Init/Dummy_Export/Dummy_Chart */
    /* During: FOInjCtfMgm/Init/Dummy_Export/Dummy_Chart */
    /* Entry Internal: FOInjCtfMgm/Init/Dummy_Export/Dummy_Chart */
    /* Transition: '<S9>:2' */
    IdxFOInjCutoff = 0U;

    /* Constant: '<S3>/ZERO2' */
    TimesFOInjCutoff = 0U;

    /* user code (Output function Trailer for TID1) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/*
 * System initialize for atomic system:
 *    '<S14>/Player'
 *    '<S15>/Player'
 *    '<S16>/Player'
 *    '<S17>/Player'
 */
void FOInjCtfMgm_Player_Init(uint8_T *rty_idx, rtDW_Player_FOInjCtfMgm_T
  *localDW)
{
  localDW->absLocalCnt = 0U;
  localDW->localCnt = 0U;
  *rty_idx = 0U;
}

/*
 * Output and update for atomic system:
 *    '<S14>/Player'
 *    '<S15>/Player'
 *    '<S16>/Player'
 *    '<S17>/Player'
 */
void FOInjCtfMgm_Player(uint32_T rtu_TimFOInjCtf, uint8_T rtu_sync, uint16_T
  rtu_times, uint8_T *rty_idx, rtDW_Player_FOInjCtfMgm_T *localDW)
{
  uint16_T localCnt;
  uint32_T absLocalCnt;
  absLocalCnt = localDW->absLocalCnt;
  localCnt = localDW->localCnt;

  /* Chart: '<S14>/Player' */
  /* Gateway: FOInjCtfMgm/PreTdc/Subsystem1/fc_1/Player */
  /* During: FOInjCtfMgm/PreTdc/Subsystem1/fc_1/Player */
  /* Entry Internal: FOInjCtfMgm/PreTdc/Subsystem1/fc_1/Player */
  /* Transition: '<S28>:2' */
  if (((int32_T)rtu_sync) != 0) {
    /* Transition: '<S28>:15' */
    *rty_idx = 0U;
    localCnt = 0U;
    absLocalCnt = rtu_TimFOInjCtf;
  } else {
    /* Transition: '<S28>:23' */
  }

  if (((int32_T)SELTIMFOINJCTF) != 0) {
    /* Transition: '<S28>:18' */
    localCnt = (uint16_T)(rtu_TimFOInjCtf - absLocalCnt);
  } else {
    /* Transition: '<S28>:19' */
    localCnt = (uint16_T)((int32_T)(((int32_T)localCnt) + 1));
  }

  if (localCnt > rtu_times) {
    /* Transition: '<S28>:4' */
    localCnt = 0U;
    absLocalCnt = rtu_TimFOInjCtf;
    *rty_idx = (uint8_T)((int32_T)(((int32_T)(*rty_idx)) + 1));
  } else {
    /* Transition: '<S28>:5' */
  }

  if (((int32_T)(*rty_idx)) >= 12) {
    /* Transition: '<S28>:7' */
    *rty_idx = 0U;
  } else {
    /* Transition: '<S28>:8' */
  }

  /* End of Chart: '<S14>/Player' */
  localDW->localCnt = localCnt;
  localDW->absLocalCnt = absLocalCnt;
}

/*
 * System initialize for atomic system:
 *    '<S25>/Calc_SymFOInjCutoff'
 *    '<S24>/Calc_SymFOInjCutoff'
 */
void FOInjC_Calc_SymFOInjCutoff_Init(uint8_T rty_SymFOInjCutoff[4],
  rtDW_Calc_SymFOInjCutoff_FOIn_T *localDW)
{
  int32_T i;
  for (i = 0; i < 4; i++) {
    localDW->x[i] = 0U;
    rty_SymFOInjCutoff[i] = 0U;
  }
}

/*
 * Output and update for atomic system:
 *    '<S25>/Calc_SymFOInjCutoff'
 *    '<S24>/Calc_SymFOInjCutoff'
 */
void FOInjCtfMgm_Calc_SymFOInjCutoff(uint8_T rtu_AbsPreTdc, const uint8_T
  rtu_VtFOInjCutoff[4], uint8_T rty_SymFOInjCutoff[4],
  rtDW_Calc_SymFOInjCutoff_FOIn_T *localDW)
{
  /* Chart: '<S25>/Calc_SymFOInjCutoff' */
  /* Gateway: FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/if/Calc_SymFOInjCutoff */
  /* During: FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/if/Calc_SymFOInjCutoff */
  /* Entry Internal: FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/if/Calc_SymFOInjCutoff */
  /* Transition: '<S27>:27' */
  rty_SymFOInjCutoff[rtu_AbsPreTdc] = localDW->x[rtu_AbsPreTdc];
  localDW->x[rtu_AbsPreTdc] = rtu_VtFOInjCutoff[rtu_AbsPreTdc];
}

/* System initialize for function-call system: '<S1>/PreTdc' */
void FOInjCtfMgm_PreTdc_Init(void)
{
  int32_T i;

  /* SystemInitialize for IfAction SubSystem: '<S5>/Subsystem1' */
  /* SystemInitialize for Chart: '<S13>/Chart' */
  for (i = 0; i < 4; i++) {
    FOInjCtfMgm_DWork.enSync[i] = 0U;
    FOInjCtfMgm_DWork.VtFOInjCutoff_gmp[i] = 0U;
  }

  /* End of SystemInitialize for Chart: '<S13>/Chart' */

  /* SystemInitialize for Chart: '<S12>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S11>/fc_1'
   */
  /* SystemInitialize for Chart: '<S14>/Player' */
  FOInjCtfMgm_Player_Init(&FOInjCtfMgm_DWork.idx_jwk,
    &FOInjCtfMgm_DWork.sf_Player);

  /* SystemInitialize for Chart: '<S12>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S11>/fc_2'
   */
  /* SystemInitialize for Chart: '<S15>/Player' */
  FOInjCtfMgm_Player_Init(&FOInjCtfMgm_DWork.idx_lah,
    &FOInjCtfMgm_DWork.sf_Player_jao);

  /* SystemInitialize for Chart: '<S12>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S11>/fc_3'
   */
  /* SystemInitialize for Chart: '<S16>/Player' */
  FOInjCtfMgm_Player_Init(&FOInjCtfMgm_DWork.idx_otj,
    &FOInjCtfMgm_DWork.sf_Player_nzn);

  /* SystemInitialize for Chart: '<S12>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S11>/fc_4'
   */
  /* SystemInitialize for Chart: '<S17>/Player' */
  FOInjCtfMgm_Player_Init(&FOInjCtfMgm_DWork.idx,
    &FOInjCtfMgm_DWork.sf_Player_afc);

  /* SystemInitialize for IfAction SubSystem: '<S22>/if' */
  /* SystemInitialize for Chart: '<S25>/Calc_SymFOInjCutoff' */
  FOInjC_Calc_SymFOInjCutoff_Init(FOInjCtfMgm_DWork.SymFOInjCutoff_d0v,
    &FOInjCtfMgm_DWork.sf_Calc_SymFOInjCutoff);

  /* End of SystemInitialize for SubSystem: '<S22>/if' */

  /* SystemInitialize for IfAction SubSystem: '<S22>/elseif' */
  /* SystemInitialize for Chart: '<S24>/Calc_SymFOInjCutoff' */
  FOInjC_Calc_SymFOInjCutoff_Init(FOInjCtfMgm_DWork.SymFOInjCutoff_khl,
    &FOInjCtfMgm_DWork.sf_Calc_SymFOInjCutoff_c3u);

  /* End of SystemInitialize for SubSystem: '<S22>/elseif' */
  /* End of SystemInitialize for SubSystem: '<S5>/Subsystem1' */
}

/* Output and update for function-call system: '<S1>/PreTdc' */
void FOInjCtfMgm_PreTdc(void)
{
  uint8_T rtb_MultiportSwitch_cyv;
  uint16_T rtb_MultiportSwitch;
  int32_T i;
  uint8_T AbsPreTdc_0;
  uint8_T MultiportSwitch1;
  uint8_T Memory_PreviousInput_k2b;
  uint8_T Memory1_PreviousInput;
  uint8_T Memory2_PreviousInput;
  uint8_T Memory3_PreviousInput;
  Memory3_PreviousInput = FOInjCtfMgm_DWork.Memory3_PreviousInput;
  Memory2_PreviousInput = FOInjCtfMgm_DWork.Memory2_PreviousInput;
  Memory1_PreviousInput = FOInjCtfMgm_DWork.Memory1_PreviousInput;
  Memory_PreviousInput_k2b = FOInjCtfMgm_DWork.Memory_PreviousInput_k2b;
  MultiportSwitch1 = FOInjCtfMgm_DWork.MultiportSwitch1;

  /* Inport: '<Root>/AbsPreTdc'
   *
   * Block description for '<Root>/AbsPreTdc':
   *  Pre Tdc Index
   */
  AbsPreTdc_0 = AbsPreTdc;

  /* If: '<S5>/If' incorporates:
   *  Constant: '<S5>/ENFOINJCUTOFF'
   */
  if (((int32_T)ENFOINJCUTOFF) != 0) {
    /* Outputs for IfAction SubSystem: '<S5>/Subsystem1' incorporates:
     *  ActionPort: '<S11>/Action Port'
     */
    /* MultiPortSwitch: '<S11>/Multiport Switch' incorporates:
     *  Inport: '<Root>/AbsPreTdc'
     *
     * Block description for '<Root>/AbsPreTdc':
     *  Pre Tdc Index
     */
    switch (AbsPreTdc_0) {
     case 0:
      rtb_MultiportSwitch_cyv = Memory_PreviousInput_k2b;
      break;

     case 1:
      rtb_MultiportSwitch_cyv = Memory1_PreviousInput;
      break;

     case 2:
      rtb_MultiportSwitch_cyv = Memory2_PreviousInput;
      break;

     default:
      rtb_MultiportSwitch_cyv = Memory3_PreviousInput;
      break;
    }

    /* End of MultiPortSwitch: '<S11>/Multiport Switch' */

    /* MultiPortSwitch: '<S11>/Multiport Switch1' incorporates:
     *  Constant: '<S11>/SELIDXFOCTFTOVIEW'
     */
    switch (SELIDXFOCTFTOVIEW) {
     case 0:
      MultiportSwitch1 = Memory_PreviousInput_k2b;
      break;

     case 1:
      MultiportSwitch1 = Memory1_PreviousInput;
      break;

     case 2:
      MultiportSwitch1 = Memory2_PreviousInput;
      break;

     default:
      MultiportSwitch1 = Memory3_PreviousInput;
      break;
    }

    /* End of MultiPortSwitch: '<S11>/Multiport Switch1' */

    /* MultiPortSwitch: '<S18>/Multiport Switch' incorporates:
     *  Constant: '<S12>/TBFOINJCTF0'
     *  Constant: '<S12>/TBFOINJCTF1'
     *  Constant: '<S12>/TBFOINJCTF2'
     *  Constant: '<S12>/TBFOINJCTF3'
     *  Inport: '<Root>/AbsPreTdc'
     *  Selector: '<S18>/Selector'
     *  Selector: '<S18>/Selector1'
     *  Selector: '<S18>/Selector2'
     *  Selector: '<S18>/Selector3'
     *
     * Block description for '<Root>/AbsPreTdc':
     *  Pre Tdc Index
     */
    switch (AbsPreTdc_0) {
     case 0:
      rtb_MultiportSwitch = TBFOINJCTF0[rtb_MultiportSwitch_cyv];
      break;

     case 1:
      rtb_MultiportSwitch = TBFOINJCTF1[rtb_MultiportSwitch_cyv];
      break;

     case 2:
      rtb_MultiportSwitch = TBFOINJCTF2[rtb_MultiportSwitch_cyv];
      break;

     default:
      rtb_MultiportSwitch = TBFOINJCTF3[rtb_MultiportSwitch_cyv];
      break;
    }

    /* End of MultiPortSwitch: '<S18>/Multiport Switch' */

    /* MultiPortSwitch: '<S19>/Multiport Switch' incorporates:
     *  Constant: '<S12>/TBFOINJCTF0'
     *  Constant: '<S12>/TBFOINJCTF1'
     *  Constant: '<S12>/TBFOINJCTF2'
     *  Constant: '<S12>/TBFOINJCTF3'
     *  Constant: '<S19>/Constant'
     *  Inport: '<Root>/AbsPreTdc'
     *  Selector: '<S19>/Selector'
     *  Selector: '<S19>/Selector1'
     *  Selector: '<S19>/Selector2'
     *  Selector: '<S19>/Selector3'
     *
     * Block description for '<Root>/AbsPreTdc':
     *  Pre Tdc Index
     */
    switch (AbsPreTdc_0) {
     case 0:
      FOInjCtfMgm_DWork.MultiportSwitch = TBFOINJCTF0[((int32_T)
        rtb_MultiportSwitch_cyv) + 12];
      break;

     case 1:
      FOInjCtfMgm_DWork.MultiportSwitch = TBFOINJCTF1[((int32_T)
        rtb_MultiportSwitch_cyv) + 12];
      break;

     case 2:
      FOInjCtfMgm_DWork.MultiportSwitch = TBFOINJCTF2[((int32_T)
        rtb_MultiportSwitch_cyv) + 12];
      break;

     default:
      FOInjCtfMgm_DWork.MultiportSwitch = TBFOINJCTF3[((int32_T)
        rtb_MultiportSwitch_cyv) + 12];
      break;
    }

    /* End of MultiPortSwitch: '<S19>/Multiport Switch' */

    /* Chart: '<S13>/Chart' incorporates:
     *  Constant: '<S13>/ENFOINJCUTOFF'
     *  Inport: '<Root>/AbsPreTdc'
     *  Inport: '<Root>/EnMisfOBD2'
     *
     * Block description for '<Root>/AbsPreTdc':
     *  Pre Tdc Index
     *
     * Block description for '<Root>/EnMisfOBD2':
     *  Enable Misfire monitoring
     */
    /* Gateway: FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/Chart */
    /* During: FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/Chart */
    /* Entry Internal: FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/Chart */
    /* Transition: '<S21>:2' */
    i = (int32_T)((uint32_T)(((uint32_T)ENFOINJCUTOFF) >> ((uint32_T)AbsPreTdc_0)));
    if (((((uint32_T)i) & 1U) != 0U) && (((int32_T)EnMisfOBD2) != 0)) {
      /* Transition: '<S21>:10' */
      FOInjCtfMgm_DWork.VtFOInjCutoff_gmp[AbsPreTdc_0] = (uint8_T)
        rtb_MultiportSwitch;
      i &= (int32_T)0x01;
      if (i != ((int32_T)FOInjCtfMgm_DWork.enSync[AbsPreTdc_0])) {
        /* Transition: '<S21>:13' */
        FOInjCtfMgm_DWork.enSync[AbsPreTdc_0] = (uint8_T)i;
        rtb_MultiportSwitch_cyv = 1U;

        /* Transition: '<S21>:19' */
      } else {
        /* Transition: '<S21>:15' */
        rtb_MultiportSwitch_cyv = 0U;

        /* Transition: '<S21>:18' */
      }
    } else {
      /* Transition: '<S21>:9' */
      FOInjCtfMgm_DWork.VtFOInjCutoff_gmp[AbsPreTdc_0] = 0U;
      FOInjCtfMgm_DWork.enSync[AbsPreTdc_0] = 0U;
      rtb_MultiportSwitch_cyv = 0U;

      /* Transition: '<S21>:16' */
      /* Transition: '<S21>:18' */
    }

    /* End of Chart: '<S13>/Chart' */

    /* Chart: '<S12>/Trigger_Cylinder' incorporates:
     *  Inport: '<Root>/AbsPreTdc'
     *
     * Block description for '<Root>/AbsPreTdc':
     *  Pre Tdc Index
     */
    /* Gateway: FOInjCtfMgm/PreTdc/Subsystem1/Subsystem/Trigger_Cylinder */
    /* During: FOInjCtfMgm/PreTdc/Subsystem1/Subsystem/Trigger_Cylinder */
    /* Entry Internal: FOInjCtfMgm/PreTdc/Subsystem1/Subsystem/Trigger_Cylinder */
    /* Transition: '<S20>:2' */
    switch (AbsPreTdc_0) {
     case 0:
      /* Outputs for Function Call SubSystem: '<S11>/fc_1' */
      /* Chart: '<S14>/Player' */
      /* Transition: '<S20>:6' */
      /* Event: '<S20>:20' */
      FOInjCtfMgm_Player(FOInjCtfMgm_DWork.Add, rtb_MultiportSwitch_cyv,
                         FOInjCtfMgm_DWork.MultiportSwitch,
                         &FOInjCtfMgm_DWork.idx_jwk,
                         &FOInjCtfMgm_DWork.sf_Player);

      /* End of Outputs for SubSystem: '<S11>/fc_1' */
      /* Transition: '<S20>:17' */
      /* Transition: '<S20>:18' */
      /* Transition: '<S20>:19' */
      break;

     case 1:
      /* Outputs for Function Call SubSystem: '<S11>/fc_2' */
      /* Chart: '<S15>/Player' */
      /* Transition: '<S20>:7' */
      /* Transition: '<S20>:8' */
      /* Event: '<S20>:21' */
      FOInjCtfMgm_Player(FOInjCtfMgm_DWork.Add, rtb_MultiportSwitch_cyv,
                         FOInjCtfMgm_DWork.MultiportSwitch,
                         &FOInjCtfMgm_DWork.idx_lah,
                         &FOInjCtfMgm_DWork.sf_Player_jao);

      /* End of Outputs for SubSystem: '<S11>/fc_2' */
      /* Transition: '<S20>:18' */
      /* Transition: '<S20>:19' */
      break;

     case 2:
      /* Outputs for Function Call SubSystem: '<S11>/fc_3' */
      /* Chart: '<S16>/Player' */
      /* Transition: '<S20>:10' */
      /* Transition: '<S20>:12' */
      /* Event: '<S20>:22' */
      FOInjCtfMgm_Player(FOInjCtfMgm_DWork.Add, rtb_MultiportSwitch_cyv,
                         FOInjCtfMgm_DWork.MultiportSwitch,
                         &FOInjCtfMgm_DWork.idx_otj,
                         &FOInjCtfMgm_DWork.sf_Player_nzn);

      /* End of Outputs for SubSystem: '<S11>/fc_3' */
      /* Transition: '<S20>:19' */
      break;

     case 3:
      /* Outputs for Function Call SubSystem: '<S11>/fc_4' */
      /* Chart: '<S17>/Player' */
      /* Transition: '<S20>:14' */
      /* Transition: '<S20>:16' */
      /* Event: '<S20>:23' */
      FOInjCtfMgm_Player(FOInjCtfMgm_DWork.Add, rtb_MultiportSwitch_cyv,
                         FOInjCtfMgm_DWork.MultiportSwitch,
                         &FOInjCtfMgm_DWork.idx,
                         &FOInjCtfMgm_DWork.sf_Player_afc);

      /* End of Outputs for SubSystem: '<S11>/fc_4' */
      break;

     default:
      /* no actions */
      break;
    }

    /* End of Chart: '<S12>/Trigger_Cylinder' */

    /* If: '<S22>/If' incorporates:
     *  Constant: '<S22>/ENFOINJCUTOFFTST'
     *  Constant: '<S23>/ZERO'
     *  Constant: '<S24>/ZERO'
     *  Inport: '<S23>/in_VtFOInjCutoff'
     *  SignalConversion generated from: '<S24>/SymFOInjCutoff'
     */
    if (((int32_T)ENFOINJCUTOFFTST) == 1) {
      /* Outputs for IfAction SubSystem: '<S22>/if' incorporates:
       *  ActionPort: '<S25>/Action Port'
       */
      /* Chart: '<S25>/Calc_SymFOInjCutoff' incorporates:
       *  Inport: '<Root>/AbsPreTdc'
       *
       * Block description for '<Root>/AbsPreTdc':
       *  Pre Tdc Index
       */
      FOInjCtfMgm_Calc_SymFOInjCutoff(AbsPreTdc_0,
        FOInjCtfMgm_DWork.VtFOInjCutoff_gmp,
        FOInjCtfMgm_DWork.SymFOInjCutoff_d0v,
        &FOInjCtfMgm_DWork.sf_Calc_SymFOInjCutoff);
      for (i = 0; i < 4; i++) {
        /* SignalConversion generated from: '<S25>/SymFOInjCutoff' */
        SymFOInjCutoff[(i)] = FOInjCtfMgm_DWork.SymFOInjCutoff_d0v[i];

        /* SignalConversion generated from: '<S25>/VtFOInjCutoff' incorporates:
         *  Inport: '<S25>/in_VtFOInjCutoff'
         *  SignalConversion generated from: '<S25>/SymFOInjCutoff'
         */
        VtFOInjCutoff[(i)] = FOInjCtfMgm_DWork.VtFOInjCutoff_gmp[i];
      }

      /* End of Outputs for SubSystem: '<S22>/if' */
    } else if (((int32_T)ENFOINJCUTOFFTST) > 1) {
      /* Outputs for IfAction SubSystem: '<S22>/elseif' incorporates:
       *  ActionPort: '<S24>/Action Port'
       */
      /* Chart: '<S24>/Calc_SymFOInjCutoff' incorporates:
       *  Inport: '<Root>/AbsPreTdc'
       *
       * Block description for '<Root>/AbsPreTdc':
       *  Pre Tdc Index
       */
      FOInjCtfMgm_Calc_SymFOInjCutoff(AbsPreTdc_0,
        FOInjCtfMgm_DWork.VtFOInjCutoff_gmp,
        FOInjCtfMgm_DWork.SymFOInjCutoff_khl,
        &FOInjCtfMgm_DWork.sf_Calc_SymFOInjCutoff_c3u);
      for (i = 0; i < 4; i++) {
        /* SignalConversion generated from: '<S24>/SymFOInjCutoff' */
        SymFOInjCutoff[(i)] = FOInjCtfMgm_DWork.SymFOInjCutoff_khl[i];
        VtFOInjCutoff[(i)] = 0U;
      }

      /* End of Outputs for SubSystem: '<S22>/elseif' */
    } else {
      /* Outputs for IfAction SubSystem: '<S22>/else' incorporates:
       *  ActionPort: '<S23>/Action Port'
       */
      for (i = 0; i < 4; i++) {
        SymFOInjCutoff[(i)] = 0U;
        VtFOInjCutoff[(i)] = FOInjCtfMgm_DWork.VtFOInjCutoff_gmp[i];
      }

      /* End of Outputs for SubSystem: '<S22>/else' */
    }

    /* End of If: '<S22>/If' */

    /* Update for Memory: '<S11>/Memory' */
    Memory_PreviousInput_k2b = FOInjCtfMgm_DWork.idx_jwk;

    /* Update for Memory: '<S11>/Memory1' */
    Memory1_PreviousInput = FOInjCtfMgm_DWork.idx_lah;

    /* Update for Memory: '<S11>/Memory2' */
    Memory2_PreviousInput = FOInjCtfMgm_DWork.idx_otj;

    /* Update for Memory: '<S11>/Memory3' */
    Memory3_PreviousInput = FOInjCtfMgm_DWork.idx;

    /* End of Outputs for SubSystem: '<S5>/Subsystem1' */
  } else {
    /* Outputs for IfAction SubSystem: '<S5>/If Action Subsystem1' incorporates:
     *  ActionPort: '<S10>/Action Port'
     */
    for (i = 0; i < 4; i++) {
      /* SignalConversion generated from: '<S10>/SymFOInjCutoff' */
      SymFOInjCutoff[(i)] = 0U;

      /* SignalConversion generated from: '<S10>/VtFOInjCutoff' incorporates:
       *  SignalConversion generated from: '<S10>/SymFOInjCutoff'
       */
      VtFOInjCutoff[(i)] = 0U;
    }

    /* End of Outputs for SubSystem: '<S5>/If Action Subsystem1' */
  }

  /* End of If: '<S5>/If' */

  /* SignalConversion generated from: '<S5>/IdxFOInjCutoff' */
  IdxFOInjCutoff = MultiportSwitch1;

  /* SignalConversion generated from: '<S5>/TimesFOInjCutoff' */
  TimesFOInjCutoff = FOInjCtfMgm_DWork.MultiportSwitch;

  /* user code (Output function Trailer for TID2) */

  /* System '<S1>/PreTdc' */

  /* PILOTAGGIO USCITE - 10ms */
  FOInjCtfMgm_DWork.MultiportSwitch1 = MultiportSwitch1;
  FOInjCtfMgm_DWork.Memory_PreviousInput_k2b = Memory_PreviousInput_k2b;
  FOInjCtfMgm_DWork.Memory1_PreviousInput = Memory1_PreviousInput;
  FOInjCtfMgm_DWork.Memory2_PreviousInput = Memory2_PreviousInput;
  FOInjCtfMgm_DWork.Memory3_PreviousInput = Memory3_PreviousInput;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void FOInjCtfMgm_T10ms(void)
{
  {
    /* user code (Output function Header for TID3) */

    /* System '<S1>/T10ms' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */

    /* Sum: '<S6>/Add' incorporates:
     *  Constant: '<S6>/Constant'
     *  Memory: '<S6>/Memory'
     */
    FOInjCtfMgm_DWork.Add++;

    /* user code (Output function Trailer for TID3) */

    /* System '<S1>/T10ms' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void Trig_FOInjCtfMgm_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S1>/Init'
   */
  FOInjCtfMgm_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_FOInjCtfMgm_PreTdc(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' incorporates:
   *  SubSystem: '<S1>/PreTdc'
   */
  FOInjCtfMgm_PreTdc();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' */
}

/* Model step function */
void Trig_FOInjCtfMgm_T10ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  FOInjCtfMgm_T10ms();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
}

/* Model initialize function */
void FOInjCtfMgm_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  IDFOInjCtfMgm = 0U;
  TimesFOInjCutoff = 0U;

  {
    int32_T i;
    for (i = 0; i < 4; i++) {
      VtFOInjCutoff[i] = 0U;
    }
  }

  {
    int32_T i;
    for (i = 0; i < 4; i++) {
      SymFOInjCutoff[i] = 0U;
    }
  }

  IdxFOInjCutoff = 0U;

  /* states (dwork) */
  (void) memset((void *)&FOInjCtfMgm_DWork, 0,
                sizeof(D_Work_FOInjCtfMgm_T));

  {
    int32_T i;

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' incorporates:
     *  SubSystem: '<S1>/PreTdc'
     */
    FOInjCtfMgm_PreTdc_Init();

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' */
    for (i = 0; i < 4; i++) {
      /* SystemInitialize for Merge: '<S4>/Merge2' */
      VtFOInjCutoff[(i)] = 0U;

      /* SystemInitialize for Merge: '<S4>/Merge4' incorporates:
       *  Merge: '<S4>/Merge2'
       */
      SymFOInjCutoff[(i)] = 0U;
    }

    /* SystemInitialize for Merge: '<S4>/Merge1' */
    IdxFOInjCutoff = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge3' */
    TimesFOInjCutoff = 0U;
  }
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_FOINJCTFMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
