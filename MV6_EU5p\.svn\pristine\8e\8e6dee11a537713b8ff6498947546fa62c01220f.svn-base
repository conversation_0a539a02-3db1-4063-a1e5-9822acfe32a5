#include "saf3_smp_dummy.h"

#ifdef  _BUILD_SAF3MGM_ 

#ifdef __MWERKS__ 
#pragma force_active on
#endif

#ifdef _SMP_DUMMY_APPLICATION_ 

const uint8_t smp_boot_code [SMP_CODE_SIZE] = 
{
0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 
0xe9, 0x67, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 
0xe9, 0xcd, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 
0xe2, 0xaf, 0xe2, 0xaf, 0xe9, 0x35, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0xe2, 0xaf, 0x00, 0x00, 
0x00, 0x02, 0x01, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0x1e, 0x02, 0x9d, 0x20, 0xfd, 0x0f, 0x53, 0x02, 0x5e, 0x55, 0x81, 0x0b, 0x53, 0x02, 0x7e, 0x55, 
0x81, 0x4d, 0x27, 0x0b, 0x41, 0x01, 0x14, 0x4f, 0xc7, 0x01, 0x0a, 0xc7, 0x01, 0x0b, 0x81, 0xa6, 
0x01, 0xc7, 0x01, 0x0a, 0x8b, 0x86, 0xb7, 0x26, 0xbf, 0x27, 0x81, 0xc7, 0x01, 0x0b, 0x8b, 0x86, 
0xb7, 0x29, 0xbf, 0x2a, 0x81, 0x45, 0x01, 0x00, 0x7c, 0xb6, 0x25, 0x1f, 0x25, 0xc6, 0x01, 0x0a, 
0x27, 0x24, 0xc6, 0x01, 0x00, 0xa5, 0x01, 0x27, 0x08, 0x32, 0x01, 0x01, 0xaf, 0x01, 0x96, 0x01, 
0x01, 0xc6, 0x01, 0x06, 0x45, 0x01, 0x03, 0xeb, 0x01, 0xe7, 0x01, 0xc6, 0x01, 0x05, 0xf9, 0xf7, 
0x9e, 0xae, 0x4f, 0xcc, 0xe9, 0x11, 0x81, 0xc6, 0x01, 0x09, 0x48, 0x45, 0x01, 0x0c, 0xab, 0x0c, 
0x87, 0xa6, 0x01, 0xa9, 0x00, 0x87, 0x8a, 0x88, 0x5e, 0x12, 0xc6, 0x01, 0x09, 0x48, 0xab, 0x0c, 
0x87, 0xa6, 0x01, 0xa9, 0x00, 0xbe, 0x13, 0x87, 0x8a, 0x89, 0x9e, 0xee, 0x02, 0x86, 0xe7, 0x01, 
0x45, 0x01, 0x09, 0x7c, 0xce, 0x01, 0x09, 0xa3, 0x02, 0x8a, 0x26, 0x10, 0x4f, 0xc7, 0x01, 0x09, 
0x4c, 0xc7, 0x01, 0x07, 0xcd, 0xea, 0xa2, 0x4f, 0xc7, 0x01, 0x08, 0x81, 0x58, 0x8c, 0x9e, 0xbe, 
0x00, 0xfa, 0xcc, 0xe9, 0xb5, 0x89, 0x8b, 0x8b, 0x95, 0xe6, 0x01, 0xa4, 0x1f, 0xf7, 0xee, 0x02, 
0x5b, 0x04, 0xaa, 0x40, 0x95, 0xf7, 0x95, 0xf6, 0xb7, 0x10, 0xa7, 0x03, 0x81, 0xa7, 0xfe, 0x45, 
0x00, 0xf4, 0xcd, 0xe9, 0x05, 0xb6, 0xf4, 0x95, 0xe7, 0x01, 0xbe, 0xf0, 0xa3, 0x06, 0x22, 0x5f, 
0x4f, 0xcd, 0xe6, 0xd6, 0x07, 0x9d, 0x06, 0x12, 0x58, 0x63, 0x6c, 0x7c, 0x83, 0x4e, 0xf4, 0xf6, 
0x6e, 0x01, 0xf0, 0xb6, 0xf4, 0x43, 0xb7, 0xf5, 0x20, 0x38, 0xb6, 0xf5, 0xb1, 0xf4, 0x26, 0x0c, 
0xb6, 0xf6, 0xcd, 0xea, 0x95, 0xb7, 0xf2, 0x6e, 0x01, 0xf7, 0x20, 0x07, 0x3f, 0xf7, 0xc6, 0x01, 
0x12, 0xb7, 0xf2, 0xbe, 0xf2, 0xa3, 0x02, 0x22, 0x23, 0x4f, 0xcd, 0xe6, 0xd6, 0x03, 0x1d, 0x02, 
0x0e, 0x12, 0x3f, 0xf0, 0x3d, 0xf1, 0x27, 0x03, 0x4f, 0x20, 0x36, 0xa6, 0x01, 0x20, 0x32, 0x6e, 
0x05, 0xf0, 0x20, 0x58, 0x6e, 0x02, 0xf0, 0x6e, 0x01, 0xf9, 0x20, 0x50, 0x6e, 0x07, 0xf0, 0x20, 
0x42, 0x6e, 0x03, 0xf0, 0xb6, 0xf3, 0x95, 0xe7, 0x01, 0x33, 0xf3, 0x20, 0x3f, 0xb6, 0xf3, 0x95, 
0xe7, 0x01, 0x6e, 0x04, 0xf0, 0x20, 0x35, 0x3f, 0xf0, 0x3d, 0xf1, 0x27, 0x02, 0x4f, 0x65, 0xa6, 
0x01, 0x95, 0xf7, 0xfe, 0xbf, 0xf1, 0x20, 0x24, 0x6e, 0x06, 0xf0, 0x4e, 0xf4, 0xf8, 0x20, 0x1c, 
0x3f, 0xf9, 0x3d, 0xf1, 0x27, 0x02, 0x4f, 0x65, 0xa6, 0x01, 0x95, 0xf7, 0xfe, 0xbf, 0xf1, 0x3f, 
0xf0, 0x20, 0x09, 0x1f, 0x02, 0x1c, 0x03, 0x1c, 0x02, 0xcd, 0xea, 0xa3, 0x95, 0xaf, 0x01, 0xcd, 
0xe9, 0x0b, 0xa7, 0x02, 0x81, 0xa1, 0x0a, 0x24, 0x06, 0x8c, 0x97, 0xd6, 0xe8, 0x40, 0x81, 0xa6, 
0xff, 0x81, 0x81, 0x9d, 0x9d, 0x9d, 0x20, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff 

};

#endif /* _SMP_DUMMY_APPLICATION_ */ 

#ifdef __MWERKS__
#pragma force_active off
#endif

#endif
