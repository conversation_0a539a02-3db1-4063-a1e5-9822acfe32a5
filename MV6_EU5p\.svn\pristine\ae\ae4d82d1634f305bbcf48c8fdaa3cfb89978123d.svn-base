/*
 * File: GearPosCluMgm_types.h
 *
 * Code generated for Simulink model 'GearPosCluMgm'.
 *
 * Model version                  : 1.323
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Jun 12 09:40:12 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GearPosCluMgm_types_h_
#define RTW_HEADER_GearPosCluMgm_types_h_
#endif                                 /* RTW_HEADER_GearPosCluMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
