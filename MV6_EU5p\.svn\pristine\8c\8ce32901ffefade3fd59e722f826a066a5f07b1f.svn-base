/**********************************************************************
 *                                                                    *
 *   LIN 2.1 API                                                      *
 *   Akhela, 2nd April 2009                                           *
 *                                                                    *
 **********************************************************************/

#ifndef  LIN_API_H
#define  LIN_API_H

/**********************************************************************
 * Constant and Macro Definitions using #define
 *********************************************************************/
/* Defines for "l_ifc_read_status()" return values.  */
#define LIN_ID_NOTSET           0xFF
#define LIN_IFC_GOTOSLEEP       0x08
#define LIN_IFC_OVERRUN         0x04
#define LIN_IFC_COMPLETE        0x02
#define LIN_IFC_ERROR           0x01

/* Defines for "ld_check_response()" return values.  */
#define LD_SUCCESS             0x00
#define LD_NEGATIVE            0x01
#define LD_NO_RESPONSE         0x02
#define LD_OVERWRITTEN         0x04

/* Defines for the Node Configuration "wildcards".  */
#define LD_BROADCAST           0x7F     /* Any NAD.  */
#define LD_ANY_SUPPLIER        0x7FFF
#define LD_ANY_FUNCTION        0xFFFF
#define LD_ANY_MESSAGE         0xFFFF

/* Defines for "ld_raw_tx_status()" and "ld_raw_rx_status()" return values.  */
#define LD_QUEUE_FULL          0x01     /* "ld_raw_tx_status()" only.  */
#define LD_QUEUE_EMPTY         0x02     /* "ld_raw_tx_status()" only.  */
#define LD_DATA_AVAILABLE      0x02     /* "ld_raw_rx_status()" only.  */
#define LD_TRANSFER_ERROR      0x04

/* Defines for "ld_tx_status()" and "ld_rx_status()" return values.  */
#define LD_COMPLETED           0x00
#define LD_IN_PROGRESS         0x01
#define LD_FAILED              0x02

/* Following defines "enumerate" the flags in the "LIN_flags[]" array.
 * The two flag routines, "l_flg_tst()" and "l_flg_clr()", subtract 252
 * from these values before indexing the "LIN_flags[]" array.  */
#define LIN_TX_END_FLG          252
#define LIN_RX_END_FLG          253
#define LIN_AWAKE_FLG           254
#define LIN_DIAG_RESP_FLG       255

/* Definitions related to errors in the Transport Layer module */
#define NO_ERROR                                  0
#define LIN_ALREADY_CONFIGURED                  (-1)
#define LIN_PERIPHERAL_NOT_CONFIGURED           (-2)
#define LIN_IDFRAME_NOT_VALID                   (-3)
#define LIN_SIZE_NOT_VALID                      (-4)
#define LIN_SET_CHKSUM_ERROR                    (-5)

/* Definitions related to errors in the eSCI LIN module */
#define LIN_IDLE                (0x01)
#define LIN_BUS_SLEEPING        (0x02)
#define LIN_BUS_ERROR           (0x04)
#define LIN_BUSY                (0x08)
#define LIN_TX_OK               (0x10)

#define LIN_MSG_4B              (4)
#define LIN_MSG_8B              (8)

/* node definitions */
#define LIN_WRONG_NAD       (-1)
#define LIN_NAD_ALTERNATOR  (0x35)  //TBDef

/**********************************************************************
 * Enumerations, Structures and Typedefs
 *********************************************************************/
/* The following typedefs are used in the LIN API function prototypes and
 * function definitions.  It is not required that these same types be used
 * in the application code, as long as equivalent types are used.  */
typedef uint8_t         l_bool;
typedef uint8_t         l_ioctl_op;
typedef uint8_t         l_irqmask;
typedef uint8_t         l_u8;
typedef uint16_t        l_u16;
typedef uint8_t         l_signal_handle;
typedef uint8_t         l_flag_handle;
typedef uint8_t         l_ifc_handle;
typedef uint8_t         l_schedule_handle;

/**********************************************************************
 * Global Variable extern Declarations
 *********************************************************************/

/**********************************************************************
 * Function Prototypes
 *********************************************************************/

/* DRIVER AND CLUSTER MANAGEMENT                                     */
/*-------------------------------------------------------------------*/
/* l_sys_init   see paragraph 7.2.1.1 of LIN 2.1 spec. package       */
int16_t l_sys_init(void);
/*-------------------------------------------------------------------*/



/* SIGNAL INTERACTION                                                */
/*-------------------------------------------------------------------*/
/* Scalar signal read                                                */
/* l_bool_rd   see paragraph 7.2.2.2 of LIN 2.1 spec. package        */
l_bool l_bool_rd(l_signal_handle signal_name);
/* l_u8_rd   see paragraph 7.2.2.2 of LIN 2.1 spec. package          */
l_u8 l_u8_rd(l_signal_handle signal_name);
/* l_u16_rd   see paragraph 7.2.2.2 of LIN 2.1 spec. package         */
l_u16 l_u16_rd(l_signal_handle signal_name);

/* Scalar signal write                                               */
/* l_bool_wr   see paragraph 7.2.2.3 of LIN 2.1 spec. package        */
void l_bool_wr(l_signal_handle signal_name, l_bool write_data);
/* l_u8_wr   see paragraph 7.2.2.3 of LIN 2.1 spec. package          */
void l_u8_wr(l_signal_handle signal_name, l_u8 write_data);
/* l_u16_wr   see paragraph 7.2.2.3 of LIN 2.1 spec. package         */
void l_u16_wr(l_signal_handle signal_name, l_u16 write_data);

/* Byte array read                                                   */
/* l_bytes_rd   see paragraph 7.2.2.4 of LIN 2.1 spec. package       */
void l_bytes_rd(l_signal_handle signal_name, l_u8 start, l_u8 count,
                l_u8 * const data);

/* Byte array write                                                  */
/* l_bytes_wr   see paragraph 7.2.2.5 of LIN 2.1 spec. package       */
void l_bytes_wr(l_signal_handle signal_name, l_u8 start, l_u8 count,
                const l_u8 * const data);
/*-------------------------------------------------------------------*/



/* NOTIFICATION                                                      */
/*-------------------------------------------------------------------*/
/* l_flg_tst   see paragraph 7.2.3.1 of LIN 2.1 spec. package        */
l_bool l_flg_tst(l_flag_handle flg);
/* l_flg_clr   see paragraph 7.2.3.2 of LIN 2.1 spec. package        */
void l_flg_clr(l_flag_handle flg);
/*-------------------------------------------------------------------*/



/* SCHEDULE MANAGEMENT                                               */
/*-------------------------------------------------------------------*/
/* l_sch_tick   see paragraph 7.2.4.1 of LIN 2.1 spec. package       */
l_u8 l_sch_tick(l_ifc_handle ifc_name);
/* l_sch_set   see paragraph 7.2.4.2 of LIN 2.1 spec. package        */
void l_sch_set(l_ifc_handle ifc_name, l_schedule_handle schedule,
               l_u8 entry);
/*-------------------------------------------------------------------*/



/* INTERFACE MANAGEMENT                                              */
/*-------------------------------------------------------------------*/
/* l_ifc_init   see paragraph 7.2.5.1 of LIN 2.1 spec. package       */
int16_t l_ifc_init(l_ifc_handle ifc_name);
/* l_ifc_goto_sleep   see paragraph 7.2.5.2 of LIN 2.1 spec. package */
int16_t l_ifc_goto_sleep(l_ifc_handle ifc_name);
/* l_ifc_wake_up   see paragraph 7.2.5.3 of LIN 2.1 spec. package    */
int16_t l_ifc_wake_up(l_ifc_handle ifc_name);
/* l_ifc_ioctl   see paragraph 7.2.5.4 of LIN 2.1 spec. package      */
l_u16 l_ifc_ioctl(l_ifc_handle ifc_name, l_ioctl_op op, void *pv);
/* l_ifc_rx   see paragraph 7.2.5.5 of LIN 2.1 spec. package         */
int16_t l_ifc_rx(void);
/* l_ifc_tx   see paragraph 7.2.5.6 of LIN 2.1 spec. package         */
int16_t l_ifc_tx(l_ifc_handle ifc_name);
/* l_ifc_aux   see paragraph 7.2.5.7 of LIN 2.1 spec. package        */
void l_ifc_aux(l_ifc_handle ifc_name);
/* l_ifc_read_status see paragraph 7.2.5.8 of LIN 2.1 spec. package  */
int16_t l_ifc_read_status(l_ifc_handle ifc_name);
/*-------------------------------------------------------------------*/



/* USER PROVIDED CALL-OUTS                                           */
/*-------------------------------------------------------------------*/
/* l_sys_irq_disable see paragraph 7.2.6.1 of LIN 2.1 spec. package  */
l_irqmask l_sys_irq_disable(void);
/* l_sys_irq_restore see paragraph 7.2.6.2 of LIN 2.1 spec. package  */
void l_sys_irq_restore(l_irqmask flag_state);
/*-------------------------------------------------------------------*/



/* NODE CONFIGURATION                                                */
/*-------------------------------------------------------------------*/
/* ld_is_ready   see paragraph 7.3.1.1 of LIN 2.1 spec. package      */
l_bool ld_is_ready(l_ifc_handle ifc_name);
/* ld_check_response see paragraph 7.3.1.2 of LIN 2.1 spec. package  */
l_u8 ld_check_response(l_ifc_handle ifc_name, l_u8 * const RSID,
                       l_u8 * const error_code);
/* ld_assign_frame_id_range see paragraph 7.3.1.3 of LIN 2.1 spec.   */
void ld_assign_frame_id_range(l_ifc_handle ifc_name, l_u8 NAD,
                              l_u8 start_idx, const l_u8* const PIDs);
/* ld_assign_NAD see paragraph 7.3.1.4 of LIN 2.1 spec. package      */
void ld_assign_NAD(l_ifc_handle ifc_name, l_u8 NAD, l_u16 supplier_id,
                   l_u16 function_id, l_u8 new_NAD);
/* ld_save_configuration see paragraph 7.3.1.5 of LIN 2.1 spec.      */
void ld_save_configuration(l_ifc_handle ifc_name, l_u8 NAD);
/* ld_read_configuration see paragraph 7.3.1.6 of LIN 2.1 spec.      */
l_u8 ld_read_configuration(l_ifc_handle ifc_name, l_u8* const data,
                           l_u8* const length);
/* ld_set_configuration see paragraph 7.3.1.7 of LIN 2.1 spec.       */
l_u8 ld_set_configuration(l_ifc_handle ifc_name, const l_u8* const data,
                           l_u16 length);
/* ld_conditional_change_NAD see paragraph 7.3.2 of LIN 2.1 spec.    */
void ld_conditional_change_NAD(l_ifc_handle ifc_name, l_u8 NAD, l_u8 id,
                               l_u8 byte, l_u8 mask, l_u8 invert,
                               l_u8 new_NAD);
/*-------------------------------------------------------------------*/



/* IDENTIFICATION                                                    */
/*-------------------------------------------------------------------*/
/* ld_read_by_id see paragraph 7.3.3.1 of LIN 2.1 spec. package      */
void ld_read_by_id(l_ifc_handle ifc_name, l_u8 NAD, l_u16 supplier_id,
                   l_u16 function_id, l_u8 id, l_u8 * const data);
/* ld_read_by_id_callout see paragraph 7.3.3.2 of LIN 2.1 spec.      */
l_u8 ld_read_by_id_callout(l_ifc_handle ifc_name, l_u8 id,
                           l_u8 * const data);
/*-------------------------------------------------------------------*/



/* TRANSPORT LAYER: INITIALIZATION                                   */
/*-------------------------------------------------------------------*/
/* ld_init   see paragraph 7.4.2 of LIN 2.1 spec. package            */
void ld_init(l_ifc_handle ifc_name);
/*-------------------------------------------------------------------*/



/* TRANSPORT LAYER: RAW API                                          */
/*-------------------------------------------------------------------*/
/* ld_put_raw   see paragraph 7.4.3.1 of LIN 2.1 spec. package       */
void ld_put_raw(l_ifc_handle ifc_name, const l_u8 * const data);
/* ld_get_raw   see paragraph 7.4.3.2 of LIN 2.1 spec. package       */
void ld_get_raw(l_ifc_handle ifc_name, l_u8 * const data);
/* ld_raw_tx_status see paragraph 7.4.3.3 of LIN 2.1 spec. package   */
l_u8 ld_raw_tx_status(l_ifc_handle ifc_name);
/* ld_raw_rx_status   see paragraph 7.4.3.4 of LIN 2.1 spec. package */
l_u8 ld_raw_rx_status(l_ifc_handle ifc_name);
/*-------------------------------------------------------------------*/



/* TRANSPORT LAYER: COOKED API                                       */
/*-------------------------------------------------------------------*/
/* ld_send_message see paragraph 7.4.4.1 of LIN 2.1 spec. package    */
void ld_send_message_master(l_u16 length, l_u8 NAD,
                     const l_u8 * const data);
/* ld_receive_message see paragraph 7.4.4.2 of LIN 2.1 spec. package */
void ld_receive_message_master(l_u16 * const length,
                        l_u8 * const NAD, l_u8 * const data);
/* ld_tx_status see paragraph 7.4.4.3 of LIN 2.1 spec. package       */
l_u8 ld_tx_status(l_ifc_handle ifc_name);
/* ld_rx_status see paragraph 7.4.4.4 of LIN 2.1 spec. package       */
l_u8 ld_rx_status(l_ifc_handle ifc_name);
/*-------------------------------------------------------------------*/


#endif   /* LIN_API_H  */
