/*
 * File: gear_mgm.h
 *
 * Code generated for Simulink model 'GearMgm'.
 *
 * Model version                  : 1.902
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb 28 14:29:19 2024
 */

#ifndef RTW_HEADER_gear_mgm_h_
#define RTW_HEADER_gear_mgm_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define MAX_GEAR                       6U

/* number of gear ratios */
#define N_GEAR_POS                     7U

/* number of gear ratios */
#define ONE_GEARRATIO                  2048U

/* 1 */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T FlgGearDiagOn;

/* Diagnosis gear position tested */
extern uint8_T FlgGearDnCL;

/* Disable CL */
extern uint8_T FlgGearUpCL;

/* Disable CL */
extern uint8_T FoGearDownSignal;

/* Force QS */
extern uint8_T FoGearUpSignal;

/* Force QS */
extern uint8_T FoTrgQS;

/* Force QS */
extern uint16_T GRConvFactor;

/* Convertion value for wheel speed */
extern uint8_T GearPos;

/* Indicates the gear detected */
extern uint8_T GearPosRec;

/* Indicates the gear recovered */
extern uint8_T GearPosRecSum;

/* GearPosRec before debounce */
extern uint16_T GearRatio1LowThr;

/* GearRatio Low threshold Gear 1 */
extern uint16_T GearRatio2LowThr;

/* GearRatio Low threshold Gear 2 */
extern uint16_T GearRatio3LowThr;

/* GearRatio Low threshold Gear 3 */
extern uint16_T GearRatio4LowThr;

/* GearRatio Low threshold Gear 4 */
extern uint16_T GearRatio5LowThr;

/* GearRatio Low threshold Gear 5 */
extern uint16_T GearRatio6LowThr;

/* GearRatio Low threshold Gear 6 */
extern uint16_T GearRatio7LowThr;

/* GearRatio Low threshold Gear 7 */
extern uint16_T GearRatioBand;

/* GearRatio Band */
extern uint16_T GearRatioCalc;

/* Gear Ratio calculated in GearMgm */
extern uint32_T IDGearPos;

/* ID Version */
extern uint16_T InvGearRatio[7];

/* Gear ratio */
extern uint8_T StDBlip;

/* Double blip status */
extern uint8_T StGPPlaDiag;

/* Plausibility Diagnosis status */
extern uint8_T UpdateGRMask[7];

/* flag disable */
extern int16_T VDeltGearDnCL;

/* threshold */
extern int16_T VDeltGearUpCL;

/* threshold */
extern uint8_T VtGearAck[7];

/* Ack diagnosis */
extern int16_T VtGearV;

/* Indicates the gear detected */
extern uint16_T buffGearRatio[8];

/* buffer GR */

/* EE */
extern uint16_T GearRatio[7];          /* Gear ratio */

#endif                                 /* RTW_HEADER_gear_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
