/*
 * File: trq_driver.h
 *
 * Code generated for Simulink model 'TrqDriver'.
 *
 * Model version                  : 1.2235
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Aug  5 15:33:05 2024
 */

#ifndef RTW_HEADER_trq_driver_h_
#define RTW_HEADER_trq_driver_h_
#include "rtwtypes.h"

/* Exported data define */
#define MAXEFF  16384U

/* Definition for custom storage class: Define */
#define BKGASDRIV_dim                  9U

/* BKGASDRIV_dim */
#define BKRPMDRIV_dim                  15U

/* BKRPMDRIV_dim */
#define ST_VSC_DISABLED                1U

/* data */
#define ST_VSC_NORMAL_CL               4U

/* data */
#define ST_VSC_NORMAL_CL_CTF           5U

/* data */
#define ST_VSC_NORMAL_WAIT             2U

/* data */
#define ST_VSC_PAUSED                  3U

/* data */
#define ST_VSC_UNUSED                  0U

/* data */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmeDriver;

/* Final CME driver/CAN request */
extern int16_T CmeDriverCANF;

/* CMI target for WOT strategy */
extern int16_T CmeDriverF;

/* Final CME driver/CAN request */
extern int16_T CmeDriverTmp;

/* CME driver/CAN request (no sat) */
extern int16_T CmeGasRpm;

/* CME pedal request */
extern int16_T CmeGasRpmX0;

/* CME pedal request */
extern int16_T CmeTargetWot;

/* CME target for WOT strategy */
extern int16_T CmiTargetWot;

/* CMI target for WOT strategy */
extern int16_T DVehSpeedSetUp;

/* VehSpeedLim - VehSpeedSetUp */
extern int16_T DeltaCmeDriverF;

/* Final CME driver/CAN request */
extern uint8_T FlgCmeDriverCANOK;

/* Plausibility test for CME from CAN */
extern uint8_T FlgGasPosCCFilt;

/* Flag to indicate GasPosCC filter while disabling/pausing */
extern uint8_T FlgVSCActive;

/* Flag to indicate control active */
extern uint8_T FlgVSCDisable;

/* Flag to disable vehicle speed control */
extern uint8_T FlgVSCPause;

/* Flag to pause vehicle speed control */
extern uint16_T GasPosCC;

/* GasPos corrected by vehicle speed control */
extern int32_T GasPosCCFiltHiR;

/* GasPosCC filtered */
extern int32_T GasPosCCIntHiR;

/* VSC integral term */
extern uint32_T IDTrqDriver;

/* ID Version */
extern uint8_T StCmeCAN;

/* CAN torque management state */
extern uint8_T StVehSpeedCtrl;

/* Vehicle speed control status */
extern uint16_T TDrivGasIndex;

/* TDrivRpmIndex */
extern uint16_T TDrivGasRatio;

/* TDrivRpmRatio */
extern uint16_T TDrivRpmIndex;

/* TDrivRpmIndex */
extern uint16_T TDrivRpmRatio;

/* TDrivRpmRatio */
extern int16_T VSCError;

/* Cruise control error */
extern int16_T VSCGasPosCCInt;

/* Vehicle speed control - integral term */
extern int16_T VSCGasPosCCProp;

/* Vehicle speed control - proportional term */
extern uint16_T VSCKFilt;

/* VehSpeedSetupCAN filter gain */
extern uint8_T VSCUseFilt;

/* Flag to indicate VSC is using the filter */
extern uint16_T VehSpeedSetUp;

/* Vehicle speed setup */
extern uint32_T VehSpeedSetUpHiR;

/* Vehicle speed setup - high resolution var */
extern uint16_T VehSpeedSetUpInit;

/* Last VehSpeedLim value */
extern int32_T VehSpeedSetUpRL;

/* Last VehSpeedSetUpCANRL value */
extern uint8_T VehSpeedSetUpRateCnt;

/* Counter from new VehSpeedLim value */

void TrqDriver_Init(void);
void TrqDriver_T10ms(void);

#endif                                 /* RTW_HEADER_trq_driver_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
