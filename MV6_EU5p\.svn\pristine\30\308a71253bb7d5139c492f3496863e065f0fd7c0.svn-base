#define USE_RPM_CAN     0

#define MAX_INDEX       11
#define INDEX_GAP0      1
#define INDEX_GAP1      6

#define INDEX_TOOTH_2_GROWING    3
#define INDEX_TOOTH_2_FALLING    8

#define CAM_TYPE            CAM_MAP_SENS    // =0 sensore assente =1 solo livello (DIGIO) =2 livello + fronti (PIO) =3 uso pressione collettore
#ifdef CAMSENSE_CHANNEL
#define CAM_EDGE        (ETPUA_UC0+CAMSENSE_CHANNEL) // Canale di eTPU usato per generare interrupt ad ogni fronte di Camma
#endif

#define TDC2PREHTDC 3
#define TDC2PRETDC  1
#define TDC2HTDC    2

#define INDEX_CAM0                   3
#define INDEX_CAM1                   8
#define N_CAM_EDGE                   2
#define ENABLE_RESYNC_ON_EX_SYNC     0
#define CAM_LEVEL_ON_GAP1            0
#define ENABLE_RESYNC_ON_EV_CAMTEST  1

#define MAX_TOOTH_DELETED            2

/* Struttura contenente tutti gli eventi di interrupt */
typedef struct 
{
  uint8_T   ToothNumber;  // Numero di dente sul quale deve essere generata l'eccezione
  uint8_T   AbsTdcIndex;  // Absolute Tdc index table
  uint16_T  EventType;    // Event table type
} EVENT_STRUCT;

