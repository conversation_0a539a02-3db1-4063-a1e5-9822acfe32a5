#!/usr/bin/perl -w

#MI: CI sara' una command line con path della directiry degli a2l e se ATI o 
#    INCA template 

#if $#ARGV != 3 && die "USAGE: A2lUnion.pl -format [ATI/INCA]";
die "Usage: A2lUnion.pl -format [ATI/INCA/GENERAL/GENERAL_TEST] -target [] -vmemory [NO_VIRTUAL_MEMORY/VIRTUAL_MEMORY] \n" if $#ARGV != 5;

$vmemory= $ARGV[$#ARGV];  

if ($ARGV[1] eq "ATI")
{
  $file = "../../AAU/ASAP2_ati_tm.a2l";
  $a2l_out_file = $ARGV[3].'_ati.a2l';
}
elsif ($ARGV[1] eq "INCA")
{
  $file = "../../AAU/ASAP2_inca_tm.a2l";
  $a2l_out_file = $ARGV[3].'_inca.a2l';
}
elsif ($ARGV[1] eq "GENERAL")
{
  $file = "../../AAU/ASAP2_prj_header.a2l";
  $a2l_out_file = $ARGV[3].'_no_addr.a2l' ;
}
elsif ($ARGV[1] eq "GENERAL_TEST")
{
  $file = "../../AAU/ASAP2_ati_inca_test_tm.a2l";
  $a2l_out_file = $ARGV[3].'_ati_inca.a2l' ;
}

open(OUTF, ">../../AAU/$a2l_out_file") or die "Cannot open $a2l_out_file for write :$!";

# copy EldorECU.a2l into ASAP folder
use File::Copy;
copy ("../../AAU/EldorEcu.a2l", "../../AAU/ASAP/EldorEcu.a2l");

my @input;
open(FILEI, "$file") || die "File $file doesn't found";
@inputs =  <FILEI>;
close(FILEI);


$lineNum = scalar(@inputs);

for ($i=0; $i<$lineNum; $i++)
{
  print  (OUTF  $inputs[$i]);
}

if($ARGV[1] eq "GENERAL_TEST")
{
  $dir = "..\\..\\AAU\\ASAP_Test";  
}
else
{
  $dir = "..\\..\\AAU\\ASAP";
}

opendir (DIR, $dir) or die "Can not open directory $dir";

@files=grep !/^\./, readdir(DIR); #remove . and .. from readdir

$lineNum = scalar(@files);

chdir($dir);
{
#parse EldorECU.a2l as first file
   my   @lines ;
 # read file
    if($ARGV[1] eq "GENERAL_TEST")
    {
      open (FILEA2L, "EldorECU_test.a2l")|| die "File $files[$i] doesn't found";  
    }
    else
    {
      open (FILEA2L, "EldorECU.a2l")|| die "File $files[$i] doesn't found";
    } 
 
 
    
    @lines = <FILEA2L>;
    close (FILEA2L) ;
# remove a2l lines until first measurements or characteristic is found
    until (($lines[0]=~/CHARACTERISTIC/) || ($lines[0]=~/MEASUREMENT/))
    {
      shift(@lines);
    }  
    $lineNumj = scalar(@lines);
    print  (OUTF  "\n\n");
    for ($j=0; $j<$lineNumj-2; $j++)
    {
      print  (OUTF  $lines[$j]);
    }    

}
for ($i=0; $i<$lineNum; $i++)
{
  #reject files which name containes A2LUnion or not ends with a2l or containes 
  # EldorECU already parsed
  if (($files[$i]=~/A2LUnion/)||($files[$i]!~/\.a2l$/)||($files[$i]=~/EldorECU/))
  {
  }
  else
  {
 #   print ("$files[$i]\n");
    $FocFlg = 0;
    my   @lines ;
 # read file
    open (FILEA2L, $files[$i])|| die "File $files[$i] doesn't found";
    @lines = <FILEA2L>;
    close (FILEA2L) ;
    #print "$files[$i]\n";
    #find the module name
    $ModuleN = ModuleName($files[$i]);
  #if Module is a FOC module, creates also the MEASUREMENT and CARACTERISTIC for
  # CORE1 
    if (($vmemory eq "VIRTUAL_MEMORY")&&(($ModuleN eq "AnalogIn") ||($ModuleN eq "Modulator") || ($ModuleN eq "AngleTrackObsv") || ($ModuleN eq "CControl")|| ($ModuleN eq "CurrDerConv") ))
    {
      $FocFlg = 1;
    }
# remove a2l lines until first measurements or characteristic is found
    until (($lines[0]=~/CHARACTERISTIC/) || ($lines[0]=~/MEASUREMENT/) || ($lines[0]=~/EOF/))
    {
      shift(@lines);
    }  
    $lineNumj = scalar(@lines);
    print  (OUTF  "\n\n");
    for ($j=0; $j<$lineNumj-6; $j++)
    {
      print  (OUTF  $lines[$j]);
    }
    if ($FocFlg == 1)
    {
      CopyA2lVal($files[$i]);
    }
 
  }
}

print(OUTF "\/end MODULE\n\n"); 

print(OUTF "\/end PROJECT\n"); 
                                                                                
close (OUTF);

print "\n";
print "***********************************************************************\n";
print "*              ASAP2 $ARGV[1] Union Created          \n";
print "***********************************************************************\n";

sub ModuleName
{
  my $fileN = shift;  
  my $ModuleN = "";
  #print "Sub $fileN\n";
  @ModulesN =split(/\./, "$fileN\n");
  $ModuleN = $ModulesN[0];
  return($ModuleN);
}

sub CopyA2lVal
{
  my $fileN = shift;
  my @lines2;
  my $j;

  open (FILEA2L, $files[$i])|| die "File doesn't found";
  @lines2 = <FILEA2L>;
  close (FILEA2L) ;
  until (($lines2[0]=~/CHARACTERISTIC/) || ($lines2[0]=~/MEASUREMENT/))
  {
     shift(@lines2);
  } 
 
  $lineNumk = scalar(@lines2);
  for ($j=0; $j<$lineNumk-6; $j++)
  {
    if (($lines2[$j]=~/begin\ MEASUREMENT/) || ($lines2[$j]=~/begin\ CHARACTERISTIC/))
    {   
        $jLine = $j;
        until (($lines2[$jLine]=~/end\ CHARACTERISTIC/) || ($lines2[$jLine]=~/end\ MEASUREMENT/))
        {
            $jLine++;
        }
        
#        print "A $lines2[$j]\n";
        chop($lines2[$j+1])  ;
#        chop($lines2[$j+1])  ;
#       remove all space at line end       
        $lines2[$j+1]=~s/\s+$//; 
        $tmp = $lines2[$j+1]."2"."\n";
        $lines2[$j+1]= $tmp;
        
#        print "B $lines2[$j+1] $j\n";
        
        for ($k=$j; $k <= ($jLine+1); $k++)
        { 
          print  (OUTF $lines2[$k]);
        }
     }
  }
  return
  
}