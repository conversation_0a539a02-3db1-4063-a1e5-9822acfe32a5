/*
 * File: ThrottleAdapt.h
 *
 * Code generated for Simulink model 'ThrottleAdapt'.
 *
 * Model version                  : 1.736
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun  8 17:18:28 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ThrottleAdapt_h_
#define RTW_HEADER_ThrottleAdapt_h_
#ifndef ThrottleAdapt_COMMON_INCLUDES_
# define ThrottleAdapt_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* ThrottleAdapt_COMMON_INCLUDES_ */

#include "ThrottleAdapt_types.h"

/* Includes for objects with custom storage classes. */
#include "throttle_adapt.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKANGTHRTARG_dim               6U                        /* Referenced by:
                                                                  * '<S14>/BKANGTHRTARG_dim'
                                                                  * '<S15>/BKANGTHRTARG_dim'
                                                                  * '<S31>/BKANGTHRTARG_dim'
                                                                  * '<S18>/BKANGTHRTARG_dim'
                                                                  */
#define BKDANGDSQ_dim                  4U                        /* Referenced by: '<S19>/BKDANGDSQ_dim' */
#define ERROR_INIT                     0                         /* Referenced by:
                                                                  * '<S12>/ERROR_INIT'
                                                                  * '<S12>/ERROR_INIT1'
                                                                  * '<S12>/ERROR_INIT2'
                                                                  */
#define ID_THROTTLEADAPT               29125247U                 /* Referenced by: '<S12>/ID_THROTTLEADAPT' */

/* mask */
#define PRES_INIT                      1024U                     /* Referenced by: '<S12>/PRES_INIT' */
#define PRES_INIT_HR                   16777216U                 /* Referenced by: '<S12>/PRES_INIT_HR' */

/* Block signals (default storage) */
typedef struct {
  uint16_T idxOut;                     /* '<S15>/Calc_indices_ad' */
  uint16_T ds;                         /* '<S15>/Calc_indices_ad' */
} BlockIO_ThrottleAdapt;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  int16_T Memory_PreviousInput;        /* '<S33>/Memory' */
  uint16_T Memory1_PreviousInput;      /* '<S26>/Memory1' */
  uint16_T Memory_PreviousInput_j;     /* '<S26>/Memory' */
  uint16_T Memory1_PreviousInput_g;    /* '<S28>/Memory1' */
  uint16_T Memory_PreviousInput_e;     /* '<S28>/Memory' */
  uint8_T UnitDelay_DSTATE;            /* '<S16>/Unit Delay' */
  uint8_T UnitDelay2_DSTATE;           /* '<S16>/Unit Delay2' */
  uint8_T UnitDelay1_DSTATE;           /* '<S16>/Unit Delay1' */
} D_Work_ThrottleAdapt;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState ThrottleAdapt_Sched_Trig_ZCE[3];/* '<S9>/ThrottleAdapt_Sched' */
} PrevZCSigStates_ThrottleAdapt;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint16_T ev_PowerOn;                 /* '<Root>/ev_PowerOn' */
  uint16_T ev_NoSync;                  /* '<Root>/ev_NoSync' */
  uint16_T ev_10ms;                    /* '<Root>/ev_10ms' */
} ExternalInputs_ThrottleAdapt;

/* Block signals (default storage) */
extern BlockIO_ThrottleAdapt ThrottleAdapt_B;

/* Block states (default storage) */
extern D_Work_ThrottleAdapt ThrottleAdapt_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_ThrottleAdapt ThrottleAdapt_U;

/* Model entry point functions */
extern void ThrottleAdapt_initialize(void);
extern void ThrottleAdapt_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('ThrottleAdapt_gen/ThrottleAdapt')    - opens subsystem ThrottleAdapt_gen/ThrottleAdapt
 * hilite_system('ThrottleAdapt_gen/ThrottleAdapt/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ThrottleAdapt_gen'
 * '<S9>'   : 'ThrottleAdapt_gen/ThrottleAdapt'
 * '<S10>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt'
 * '<S11>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc'
 * '<S12>'  : 'ThrottleAdapt_gen/ThrottleAdapt/ThrottleAdapt_Init'
 * '<S13>'  : 'ThrottleAdapt_gen/ThrottleAdapt/ThrottleAdapt_Sched'
 * '<S14>'  : 'ThrottleAdapt_gen/ThrottleAdapt/VtDAngThr_Reset'
 * '<S15>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity'
 * '<S16>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Stability_Flag_Calc'
 * '<S17>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/Calc_indices_ad'
 * '<S18>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/ControlReset'
 * '<S19>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/Update_VtDAngThr'
 * '<S20>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/ControlReset/LookUp_IR_S16_1'
 * '<S21>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/ControlReset/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S22>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/Update_VtDAngThr/LookUp_U16_U16'
 * '<S23>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/Update_VtDAngThr/Saturation Dynamic'
 * '<S24>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/Update_VtDAngThr/Saturation Dynamic1'
 * '<S25>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Adaptivity/Update_VtDAngThr/LookUp_U16_U16/Data Type Conversion Inherited3'
 * '<S26>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Stability_Flag_Calc/AngThrModelTarg_Stability'
 * '<S27>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Stability_Flag_Calc/Compare To Constant'
 * '<S28>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Stability_Flag_Calc/DAngThr_Stability'
 * '<S29>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Stability_Flag_Calc/GenAbs'
 * '<S30>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Adapt/Stability_Flag_Calc/GenAbs/Data Type Conversion Inherited'
 * '<S31>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/DAngThr_Sat_Filt'
 * '<S32>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/PresError_Calculation'
 * '<S33>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/Pres_Control'
 * '<S34>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/DAngThr_Sat_Filt/FOF_Reset_S16_FXP'
 * '<S35>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/DAngThr_Sat_Filt/LookUp_IR_S16'
 * '<S36>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/DAngThr_Sat_Filt/PreLookUpIdSearch_U16'
 * '<S37>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/DAngThr_Sat_Filt/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S38>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/DAngThr_Sat_Filt/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S39>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/PresError_Calculation/FOF_Reset_S16_FXP_1'
 * '<S40>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/PresError_Calculation/GenAbs'
 * '<S41>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/PresError_Calculation/FOF_Reset_S16_FXP_1/Data Type Conversion Inherited1'
 * '<S42>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/PresError_Calculation/GenAbs/Data Type Conversion Inherited'
 * '<S43>'  : 'ThrottleAdapt_gen/ThrottleAdapt/DAngThr_Calc/Pres_Control/Compare To Constant'
 * '<S44>'  : 'ThrottleAdapt_gen/ThrottleAdapt/VtDAngThr_Reset/For Iterator Subsystem'
 */
#endif                                 /* RTW_HEADER_ThrottleAdapt_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
