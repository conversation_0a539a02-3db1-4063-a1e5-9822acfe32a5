/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Application/KNOCKCORRE/branches/KNOCKCORR_MV/KnockCorr_ert_rt#$  */
/* $Revision:: 8016                                                                                           $  */
/* $Date:: 2019-10-22 11:50:14 +0200 (mar, 22 ott 2019)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorr.h
 **  Date:          22-Oct-2019
 **
 **  Model Version: 1.1979
 ******************************************************************************
 **/
#ifndef RTW_HEADER_KnockCorr_h_
#define RTW_HEADER_KnockCorr_h_
#ifndef KnockCorr_COMMON_INCLUDES_
# define KnockCorr_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#endif                                 /* KnockCorr_COMMON_INCLUDES_ */

#include "KnockCorr_types.h"

/* Includes for objects with custom storage classes. */
#include "knock_corr.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKDELTAKNOCK_dim               3U
#define BKETACSIKNOCK_dim              5U
#define BKLOADADKNOCK_dim              4U
#define BKLOADKNOCK3_dim               2U
#define BKLOADKNOCKTIN_dim             3U
#define BKRPMKNOCK12_dim               11U
#define BKRPMKNOCK5_dim                4U
#define BKRPMKNOCK8_dim                7U
#define BKRPMKNOCKTIN_dim              4U
#define KCORRINCN                      8
#define MAXMAPWEIGHT                   2048
#define MAX_RPM_DIV_100                100U
#define TIPIN_FIXED_OFFSET             2U
#define TIPIN_NORMAL                   0U
#define TIPIN_SLEW                     3U
#define TIPIN_W_KNOCK                  1U
#define TIPIN_W_RESTART                4U

/* Block signals (auto storage) */
typedef struct {
  uint16_T PreLookUpIdSearch_U16_o1;   /* '<S109>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2;   /* '<S109>/PreLookUpIdSearch_U16' */
  uint16_T RpmF_h;                     /* '<S21>/TipIn' */
  uint16_T RpmF_b;                     /* '<S21>/TipIn' */
  int16_T sakcorrind_cyl;              /* '<S107>/MinMax' */
  uint16_T Load_i;                     /* '<S21>/TipIn' */
  uint8_T ind_cyl_rec;                 /* '<S1>/Control_flow' */
  uint8_T ptfault;                     /* '<S1>/Control_flow' */
  uint8_T knockstate_old[8];           /* '<S22>/Memory2' */
  boolean_T LogicalOperator;           /* '<S53>/Logical Operator' */
  boolean_T flg_act_tipin;             /* '<S21>/TipIn' */
} BlockIO_KnockCorr;

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  uint8_T Memory2_PreviousInput[8];    /* '<S22>/Memory2' */
  struct {
    uint_T is_c24_KnockCorr:3;         /* '<S21>/TipIn' */
    uint_T is_Normal:2;                /* '<S1>/Control_flow' */
    uint_T is_c20_KnockCorr:2;         /* '<S53>/RecAdaptEnable_Flag' */
    uint_T is_active_c7_KnockCorr:1;   /* '<S1>/Control_flow' */
    uint_T is_active_c20_KnockCorr:1;  /* '<S53>/RecAdaptEnable_Flag' */
    uint_T is_active_c24_KnockCorr:1;  /* '<S21>/TipIn' */
    uint_T is_active_c21_KnockCorr:1;  /* '<S16>/KnockCorr_Slew' */
  } bitsForTID0;

  uint16_T Memory1_PreviousInput;      /* '<S104>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S104>/Memory' */
  uint16_T Memory1_PreviousInput_e;    /* '<S103>/Memory1' */
  uint16_T Memory_PreviousInput_n;     /* '<S103>/Memory' */
  uint16_T ncomb_obs;                  /* '<S21>/TipIn' */
  uint16_T ncomb_corr;                 /* '<S21>/TipIn' */
  boolean_T Mgm_Ad_Table_MODE;         /* '<S53>/Mgm_Ad_Table' */
  boolean_T Calc_Ad_Corr_MODE;         /* '<S53>/Calc_Ad_Corr' */
} D_Work_KnockCorr;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState Control_flow_Trig_ZCE[4]; /* '<S1>/Control_flow' */
} PrevZCSigStates_KnockCorr;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_EOA;                      /* '<Root>/ev_EOA' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
} ExternalInputs_KnockCorr;

/* Block signals (auto storage) */
extern BlockIO_KnockCorr KnockCorr_B;

/* Block states (auto storage) */
extern D_Work_KnockCorr KnockCorr_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_KnockCorr KnockCorr_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  Code generation will declare the memory for
 * these signals and export their symbols.
 *
 */
extern int16_T DeltaKCorrCyl;          /* '<S107>/Switch1' */
extern int16_T kcorradmax;             /* '<S87>/Sum2' */
extern int16_T kcorrindsumcyl;         /* '<S70>/Switch1' */
extern int16_T deltasaknockcorrad;     /* '<S56>/Add2' */
extern int16_T eta_knock;              /* '<S71>/Calc_indices_ad' */
extern int16_T csi_knock;              /* '<S71>/Calc_indices_ad' */
extern uint8_T flgsakpos;              /* '<S108>/Logical Operator' */
extern uint8_T rmax;                   /* '<S71>/ENKNOCKAD3' */
extern uint8_T ofsr;                   /* '<S71>/Product' */
extern uint8_T indr_knock;             /* '<S71>/Calc_indices_ad' */
extern uint8_T indc_knock;             /* '<S71>/Calc_indices_ad' */
extern uint8_T endknocklearncyl;       /* '<S72>/Relational Operator' */
extern boolean_T triggeradat;          /* '<S70>/Switch' */

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern int16_T SAKnockCyl;             /* '<Root>/_DataStoreBlk_14' */
extern int16_T kcorrindsum[8];         /* '<Root>/_DataStoreBlk_4' */
extern uint8_T eninc[8];               /* '<Root>/_DataStoreBlk_29' */
extern uint8_T endknocklearn[8];       /* '<Root>/_DataStoreBlk_3' */
extern uint8_T count[8];               /* '<Root>/_DataStoreBlk_32' */
extern uint8_T knock_sstab_rpm;        /* '<Root>/_DataStoreBlk_6' */
extern uint8_T knock_sstab_load;       /* '<Root>/_DataStoreBlk_7' */

/* Model entry point functions */
extern void KnockCorr_initialize(void);
extern void KnockCorr_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('KnockCorr_gen/KnockCorr')    - opens subsystem KnockCorr_gen/KnockCorr
 * hilite_system('KnockCorr_gen/KnockCorr/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'KnockCorr_gen'
 * '<S1>'   : 'KnockCorr_gen/KnockCorr'
 * '<S14>'  : 'KnockCorr_gen/KnockCorr/Control_flow'
 * '<S15>'  : 'KnockCorr_gen/KnockCorr/Force_SAKnock'
 * '<S16>'  : 'KnockCorr_gen/KnockCorr/KnockCorr_Slew'
 * '<S17>'  : 'KnockCorr_gen/KnockCorr/Rec_Single'
 * '<S18>'  : 'KnockCorr_gen/KnockCorr/Reset_CntKnockCoh'
 * '<S19>'  : 'KnockCorr_gen/KnockCorr/Reset_Single_Variables'
 * '<S20>'  : 'KnockCorr_gen/KnockCorr/Reset_Variables'
 * '<S21>'  : 'KnockCorr_gen/KnockCorr/TipIn'
 * '<S22>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA'
 * '<S23>'  : 'KnockCorr_gen/KnockCorr/Control_flow/Normal.Enable.Recovery_Management.diagknockcoh'
 * '<S24>'  : 'KnockCorr_gen/KnockCorr/Control_flow/Normal.Enable.Recovery_Management.diagknockcoh/DiagMgm_SetDiagState'
 * '<S25>'  : 'KnockCorr_gen/KnockCorr/Force_SAKnock/Force_SAKnock'
 * '<S26>'  : 'KnockCorr_gen/KnockCorr/KnockCorr_Slew/KnockCorr_Slew'
 * '<S27>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Calc_Ad_Corr'
 * '<S28>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc'
 * '<S29>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Rec_Single'
 * '<S30>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/knockrec_calc'
 * '<S31>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Calc_Ad_Corr/Decrement Real World'
 * '<S32>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Calc_Ad_Corr/Increment Real World'
 * '<S33>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Calc_Ad_Corr/Increment Real World1'
 * '<S34>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Calc_Ad_Corr/Look2D_TbKnockAd'
 * '<S35>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/Calc_Ad_Corr/Look2D_TbKnockAd/Data Type Conversion Inherited1'
 * '<S36>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc/Assign_KnockRec'
 * '<S37>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc/KnockRecRL_calc'
 * '<S38>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc/RateLimiter_S16'
 * '<S39>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc/KnockRecRL_calc/Assign_FlgDisKnockRL'
 * '<S40>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc/KnockRecRL_calc/Compare To Zero'
 * '<S41>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/KnockRecRLCalc/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S42>'  : 'KnockCorr_gen/KnockCorr/Rec_Single/knockrec_calc/Look2D_S8_U16_U16'
 * '<S43>'  : 'KnockCorr_gen/KnockCorr/Reset_CntKnockCoh/Reset'
 * '<S44>'  : 'KnockCorr_gen/KnockCorr/Reset_Single_Variables/Reset_Single_Variables'
 * '<S45>'  : 'KnockCorr_gen/KnockCorr/Reset_Variables/Reset_Variables'
 * '<S46>'  : 'KnockCorr_gen/KnockCorr/TipIn/PreLookUpIdSearch_U1'
 * '<S47>'  : 'KnockCorr_gen/KnockCorr/TipIn/PreLookUpIdSearch_U16'
 * '<S48>'  : 'KnockCorr_gen/KnockCorr/TipIn/TipIn'
 * '<S49>'  : 'KnockCorr_gen/KnockCorr/TipIn/TipIn/fc_InterpSaTipInStep'
 * '<S50>'  : 'KnockCorr_gen/KnockCorr/TipIn/TipIn/fc_InterpSaTipInit'
 * '<S51>'  : 'KnockCorr_gen/KnockCorr/TipIn/TipIn/fc_InterpSaTipInStep/LookUp_S8_U16'
 * '<S52>'  : 'KnockCorr_gen/KnockCorr/TipIn/TipIn/fc_InterpSaTipInit/Look2D_S8_U16_U16'
 * '<S53>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction'
 * '<S54>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr'
 * '<S55>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr'
 * '<S56>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr'
 * '<S57>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Compare To Zero'
 * '<S58>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table'
 * '<S59>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/RecAdaptEnable_Flag'
 * '<S60>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Zones_Learn'
 * '<S61>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Assign_SAKnockCorrAd'
 * '<S62>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Decrement Real World'
 * '<S63>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Increment Real World'
 * '<S64>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Increment Real World1'
 * '<S65>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Look2D_TbKnockAd'
 * '<S66>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Assign_SAKnockCorrAd/Assign_SAKnockCorrAd'
 * '<S67>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Calc_Ad_Corr/Look2D_TbKnockAd/Data Type Conversion Inherited1'
 * '<S68>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State'
 * '<S69>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Assignement_TrigKnockAdat'
 * '<S70>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/TriggerToLearn'
 * '<S71>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table'
 * '<S72>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning'
 * '<S73>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning'
 * '<S74>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Assign_CntKnockLearn'
 * '<S75>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Assign_KnockLearnState'
 * '<S76>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Assign_endknocklearn'
 * '<S77>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Increment Real World'
 * '<S78>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Assign_CntKnockLearn/Assign_CntKnockLearn'
 * '<S79>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Assign_KnockLearnState/Assign_KnockLearnState'
 * '<S80>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Assign_endknocklearn/Assign_endknocklearn'
 * '<S81>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning/Reset_Variables'
 * '<S82>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Assignement_TrigKnockAdat/Assignement_TrigKnockAdat'
 * '<S83>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/TriggerToLearn/Assign_kcorrindsum'
 * '<S84>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/TriggerToLearn/Assign_kcorrindsum/Assign_kcorrindsum'
 * '<S85>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_indices_ad'
 * '<S86>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Increment Real World'
 * '<S87>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd'
 * '<S88>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/Assign_TbKnockAd'
 * '<S89>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 1-D VTDELTAKCORRMIN'
 * '<S90>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 1-D VTKCORRMIN'
 * '<S91>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 2-D TBGNAD'
 * '<S92>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 2-D TBKCORRMAX'
 * '<S93>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/Saturation Dynamic'
 * '<S94>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/Assign_TbKnockAd/Assign_TbKnockAd'
 * '<S95>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 1-D VTDELTAKCORRMIN/LookUp_IR_S16'
 * '<S96>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 1-D VTDELTAKCORRMIN/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S97>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 1-D VTKCORRMIN/LookUp_IR_S16'
 * '<S98>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 1-D VTKCORRMIN/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S99>'  : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 2-D TBGNAD/Look2D_U16_U16_U16'
 * '<S100>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 2-D TBGNAD/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S101>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Write_TbKnockAd/LookUp 2-D TBKCORRMAX/Look2D_IR_S8'
 * '<S102>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Zones_Learn/LookUp 1-D VTTDCSTABKNOCK'
 * '<S103>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Zones_Learn/Signal_Stability1'
 * '<S104>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Zones_Learn/Signal_Stability2'
 * '<S105>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Zones_Learn/LookUp 1-D VTTDCSTABKNOCK/LookUp_IR_U16'
 * '<S106>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Adaptive_Correction/Zones_Learn/LookUp 1-D VTTDCSTABKNOCK/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S107>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr'
 * '<S108>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Param_Inc'
 * '<S109>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/PreLookUpIdSearch_U16'
 * '<S110>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Knock_Coh_ptfault'
 * '<S111>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/LookUp2D_TBKCORRDEC'
 * '<S112>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/PreLookUpIdSearch_S16'
 * '<S113>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/SAKCorrInd[IonAbsTdcEOA]'
 * '<S114>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Timer_enable_inc'
 * '<S115>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/LookUp2D_TBKCORRDEC/Look2D_IR_S8'
 * '<S116>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/SAKCorrInd[IonAbsTdcEOA]/Select_SAKCorrInd'
 * '<S117>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Timer_enable_inc/Assign_count(i)'
 * '<S118>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Timer_enable_inc/Assign_eninccyl(i)'
 * '<S119>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Timer_enable_inc/Increment Real World'
 * '<S120>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Timer_enable_inc/Assign_count(i)/Assign_count'
 * '<S121>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Delta_Corr/Timer_enable_inc/Assign_eninccyl(i)/Assign_eninccyl'
 * '<S122>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Param_Inc/LookUp_VTKCORRINCDELAYN'
 * '<S123>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Param_Inc/Parameters_choice'
 * '<S124>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Ind_Cyl_Knock_Corr/Param_Inc/LookUp_VTKCORRINCDELAYN/LookUp_IR_U8'
 * '<S125>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_FlgSAKIndInc(i)'
 * '<S126>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_FlgSAKnockInc(i)'
 * '<S127>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_FlgSAKnockSat(i)'
 * '<S128>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_SAKCorrInd(i)'
 * '<S129>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_SAKCorrIndMax(i)'
 * '<S130>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_SAKnock'
 * '<S131>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/LookUp 1-D VTKCORRMIN'
 * '<S132>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/LookUp 2-D TBKCORRMAX'
 * '<S133>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Positive'
 * '<S134>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Positive1'
 * '<S135>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Saturation Dynamic'
 * '<S136>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Saturation Dynamic1'
 * '<S137>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_FlgSAKIndInc(i)/Assign_FlgSAKIndInc'
 * '<S138>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_FlgSAKnockInc(i)/Assign_FlgSAKnockInc'
 * '<S139>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_FlgSAKnockSat(i)/Assign_FlgSAKnockSat'
 * '<S140>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_SAKCorrInd(i)/Assign_SAKCorrInd'
 * '<S141>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/Assign_SAKCorrIndMax(i)/Assign_SAKCorrIndMax'
 * '<S142>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/LookUp 1-D VTKCORRMIN/LookUp_IR_S16'
 * '<S143>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/LookUp 1-D VTKCORRMIN/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S144>' : 'KnockCorr_gen/KnockCorr/fcn_EOA/Tot_Knock_Corr/LookUp 2-D TBKCORRMAX/Look2D_IR_S8'
 */

/*-
 * Requirements for '<Root>': KnockCorr
 */
#endif                                 /* RTW_HEADER_KnockCorr_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
