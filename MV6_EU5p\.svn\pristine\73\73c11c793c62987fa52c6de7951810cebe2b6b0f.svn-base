/*
 * File: IdleCtfMgm.h
 *
 * Code generated for Simulink model 'IdleCtfMgm'.
 *
 * Model version                  : 1.2297
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Mar  1 09:28:46 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_IdleCtfMgm_h_
#define RTW_HEADER_IdleCtfMgm_h_
#ifndef IdleCtfMgm_COMMON_INCLUDES_
# define IdleCtfMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* IdleCtfMgm_COMMON_INCLUDES_ */

#include "IdleCtfMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "canmgm.h"
#include "idlectf_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKCMEDRIVERMAX_dim             7U                        /* Referenced by: '<S13>/BKCMEDRIVERMAX_dim' */

/* BKCMEDRIVERMAX dimension */
#define BKGASPOSCTF_dim                3U                        /* Referenced by: '<S9>/BKGASPOSCTF_dim' */

/* BKGASPOSCTF dimension */
#define BKKFCTFEXITNEUT_dim            2U                        /* Referenced by: '<S9>/BKKFCTFEXITNEUT_dim' */

/* BKKFCTFEXITNEUT dimension */
#define BKKFGAINDNCUTOFF_dim           4U                        /* Referenced by: '<S13>/BKKFGAINDNCUTOFF_dim' */

/* BKKFGAINDNCUTOFF dimension */
#define CM_INIT                        0                         /* Referenced by: '<S3>/CM_INIT1' */

/* Torque initial value */
#define ID_IDLE_CTF_MGM                20630943U                 /* Referenced by: '<S3>/ID_IDLE_CTF_MGM' */

/* mask */

/* Block states (default storage) for system '<S6>/CalcCmeDriveMax' */
typedef struct {
  int32_T UnitDelay3_DSTATE;           /* '<S8>/Unit Delay3' */
} rtDW_CalcCmeDriveMax_IdleCtfMgm;

/* Block signals (default storage) */
typedef struct {
  uint16_T Switch2;                    /* '<S6>/Switch2' */
  uint16_T Switch2_g;                  /* '<S13>/Switch2' */
  uint8_T quickGearShiftDnOld;         /* '<S6>/Chart' */
} BlockIO_IdleCtfMgm;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  struct {
    uint_T is_c1_IdleCtfMgm:2;         /* '<S6>/Chart' */
    uint_T is_NO_CTF:2;                /* '<S6>/Chart' */
    uint_T is_active_c1_IdleCtfMgm:1;  /* '<S6>/Chart' */
  } bitsForTID3;

  int16_T cmedriver_old;               /* '<S6>/Chart' */
  uint8_T UnitDelay_DSTATE;            /* '<S6>/Unit Delay' */
  rtDW_CalcCmeDriveMax_IdleCtfMgm CalcCmeDriveMax;/* '<S6>/CalcCmeDriveMax' */
} D_Work_IdleCtfMgm;

/* Block signals (default storage) */
extern BlockIO_IdleCtfMgm IdleCtfMgm_B;

/* Block states (default storage) */
extern D_Work_IdleCtfMgm IdleCtfMgm_DWork;

/* Model entry point functions */
extern void IdleCtfMgm_initialize(void);

/* Exported entry point function */
extern void Trig_IdleCtfMgm_NoSync(void);

/* Exported entry point function */
extern void Trig_IdleCtfMgm_PowerOn(void);

/* Exported entry point function */
extern void Trig_IdleCtfMgm_T10ms(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IdleCtfMgm'
 * '<S1>'   : 'IdleCtfMgm/IdleCtfMgm'
 * '<S2>'   : 'IdleCtfMgm/Model Info'
 * '<S3>'   : 'IdleCtfMgm/IdleCtfMgm/Init'
 * '<S4>'   : 'IdleCtfMgm/IdleCtfMgm/T10ms'
 * '<S5>'   : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc'
 * '<S6>'   : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient'
 * '<S7>'   : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable'
 * '<S8>'   : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/CalcCmeDriveMax'
 * '<S9>'   : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Calc_KfGainCutOff'
 * '<S10>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Chart'
 * '<S11>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Compare To Zero2'
 * '<S12>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/FOF_Reset_S16_FXP'
 * '<S13>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/KFGain_calc'
 * '<S14>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/CalcCmeDriveMax/FOF_Reset_S16_FXP'
 * '<S15>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/CalcCmeDriveMax/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S16>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Calc_KfGainCutOff/Compare To Zero'
 * '<S17>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Calc_KfGainCutOff/LookUp_U16_S16_1'
 * '<S18>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Calc_KfGainCutOff/LookUp_U16_U16_1'
 * '<S19>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Calc_KfGainCutOff/LookUp_U16_S16_1/Data Type Conversion Inherited3'
 * '<S20>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/Calc_KfGainCutOff/LookUp_U16_U16_1/Data Type Conversion Inherited3'
 * '<S21>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S22>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/KFGain_calc/Compare To Constant'
 * '<S23>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/KFGain_calc/LookUp_U16_S1'
 * '<S24>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/KFGain_calc/LookUp_U16_S16'
 * '<S25>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/KFGain_calc/LookUp_U16_S1/Data Type Conversion Inherited3'
 * '<S26>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/Cutoff_Transient/KFGain_calc/LookUp_U16_S16/Data Type Conversion Inherited3'
 * '<S27>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable/Calc_EnCutOff'
 * '<S28>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable/Calc_timerCutoff'
 * '<S29>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable/Clac_FlgCmeLow'
 * '<S30>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable/Clac_RpmIdleEntry'
 * '<S31>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable/Calc_timerCutoff/Compare To Zero1'
 * '<S32>'  : 'IdleCtfMgm/IdleCtfMgm/T10ms/Calc/IdleCutoff_Enable/Clac_RpmIdleEntry/Compare To Zero2'
 */

/*-
 * Requirements for '<Root>': IdleCtfMgm
 */
#endif                                 /* RTW_HEADER_IdleCtfMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
