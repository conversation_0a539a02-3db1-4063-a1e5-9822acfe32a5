#ifndef TACH_CONS_H
#define TACH_CONS_H

/* Module working modes */
#define TACH                     (0)
#define TACH_CONS                (1)

#define TACHCONS_MODE            TACH_CONS

#define TACHCONS_ANGLE_PERIOD          ((N_TEETH_CYCLE * DEGREE_PER_TOOTH * DEGREE_PRECISION)/4)

#define CONS_DUTY_OFFSET	(10*256)    /*  = 10*256 (10% rescaled)                */
#define CONS_DUTY_GAIN		(80*256)   /*  = 80*256 (80% rescaled) moltiplica */
                                    /* una variabile normalizzata 0..1         */
#define CONS_DUTY_MAX		(CONS_DUTY_OFFSET+CONS_DUTY_GAIN)

/* Module error definitions */
#define ERROR_ALREADY_CONFIGURED (-1)
#define ERROR_NOT_CONFIGURED     (-2)
#define ERROR_TACHCONS_RUNNING   (-3)
#define ERROR_TACHCONS_STOPPED   (-4)


int16_t TACHCONS_Config(uint8_t tc_matchMode, uint32_t tc_duty, uint32_t tc_period);
int16_t TACHCONS_SetParams(uint8_t tc_matchMode, uint32_t tc_duty, uint32_t tc_period);
int16_t TACHCONS_SetDuty(uint8_t tc_matchMode, uint16_t tc_duty);
int16_t TACHCONS_Start(void);
int16_t TACHCONS_Stop(void);
void    TACHCONS_Ex_Angle(void);
void    TACHCONS_Diagnosis(void);
void    SYNCMGM_Dashboard_Init(void);
void    SYNCMGM_Dashboard_Sync(void); 
void    SYNCMGM_Dashboard_NoSync(void);
void    SYNCMGM_Dashboard(void);

#endif /* TACH_CONS_H */
