/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _SELFMGM_H_
#define _SELFMGM_H_
/** include files **/
#include "typedefs.h"
#include "dbw_mgm.h"
#include "activeDiag.h"

/** local definitions **/

// valori di StDbwSelf
typedef uint8_T typStDbwSelf;

#define DBW_SELF_INIT               0
#define DBW_SELF_CLOSING            2
#define DBW_SELF_CLOSED             3
#define DBW_SELF_RELEASE            4
#define DBW_SELF_RELEASED           5
#define DBW_SELF_END                6
#define DBW_SELF_FAILED             7

#define IDN_EE_SELFMGM              0


#define DBW_SELF_NO_ERROR           1
#define DBW_SELF_INIT_ERROR        2
#define DBW_SELF_LIMP_HOME_ERROR  4
#define DBW_SELF_CLOSING_ERROR      8
#define DBW_SELF_CLOSED_ERROR       16
#define DBW_SELF_LMS_ERROR     32
#define DBW_SELF_RELEASE_ERROR      64
#define DBW_SELF_RELEASED_ERROR     128

#define SELF_NORMAL_COND        0
#define NOSELF_FORCE_DIS_COND   1
#define NOSELF_KEYOFF_COND      2
#define NOSELF_VBATT_COND       3   
#define NOSELF_REC_NO_DBW_COND  4   
#define NOSELF_REC_DBW_OFF_COND 5
#define NOSELF_RPM_COND         6
#define NOSELF_POWERON_COND     7

/** external data **/
extern uint8_T  KeySignal;
extern uint16_T VAngThrottle1;
extern uint16_T VAngThrottle2;
extern uint16_T VBattery;
extern uint16_T Rpm;
extern uint8_T  PowerOnType;
extern uint8_T  S3FlgAllowStart;
//extern typStDbwCtrl StDbwCtrl;
extern uint8_T  StDbwCtrl;

/** public data **/
extern uint8_T      FlgSelfRequest;
extern uint8_T      FlgSelf_FirstDone;
extern uint8_T      StDbwSelfError;
extern typStDbwSelf StDbwSelf;
extern int16_T      VDbwIn;                         // 2^-10
extern uint8_T      FlgSelfLearning;

/** public functions **/
extern void SelfMgm_Init(void);
extern void SelfMgm_T5ms(void);
extern void SelfMgm_FlgSelfDisable(void);
extern void SelfMgm_StDbwSelf(void);

/** private functions **/

#endif    // _SELFMGM__H_
