/*******************************************************************
 *
 *    DESCRIPTION:
 *
 *    AUTHOR:
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _ION_MISF_H_
#define _ION_MISF_H_

/** public definitions **/
#define NO_MISF               0U
#define BAD_COMB              1U
#define PAR_MISF              2U
#define MISF                  3U

/** public data **/
extern uint8_T LookIndideTheCode; /* LL: HAZARD Look to the body */
extern uint16_T FaultCnt[];
extern uint8_T StMisf[];
extern uint16_T MisfInt;

/** public functions **/
void IonMisf_Init(void);
void IonMisf_EOA(void);
void IonMisf_NoSync(void);

#endif
