/*
 * File: IonLambda.c
 *
 * Real-Time Workshop code generated for Simulink model IonLambda.
 *
 * Model version                        : 1.657
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed May 23 15:23:33 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed May 23 15:23:38 2007
 */

#include "IonLambda_Y.h"
#include "IonLambda_private_Y.h"

/* Named constants for block: '<S4>/Median' */
#define IonLambda_IN_BUF                (1)
#define IonLambda_IN_RESET              (2)

/* Named constants for block: '<S4>/Median1' */
#define IonLambda_IN_BUF_f              (1)
#define IonLambda_IN_RESET_g            (2)

/* Named constants for block: '<S16>/Calc_indices_ad' */
#define IonLambda_event_fc_FFSGainStore (1U)

/* Named constants for block: '<S16>/SelfAdjFSM' */
#define IonLambda_IN_NO_FORCED          (2)
#define IonLambda_IN_WAIT_OL_FORCED     (6)
#define IonLambda_IN_WAIT_MONITORING    (5)
#define IonLambda_IN_MONITORING         (1)
#define IonLambda_IN_OL_FORCED          (3)
#define IonLambda_IN_POST_OL_FORCED     (4)

/* Named constants for block: '<S1>/Chart' */
#define IonLambda_event_ev_PowerOn      (1U)
#define IonLambda_event_ev_TDC          (0U)
#define IonLambda_event_ev_NoSync       (4U)
#define IonLambda_b                     (4)
#define IonLambda_c                     (0)
#define IonLambda_d                     (1)

/* user code (top of source file) */
/* System: <Root> */
#ifdef _BUILD_IONLAMBDA_

#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

uint8_T _sfEvent_IonLambda_;

/* Exported block signals */
int32_T AccumDelta;                     /* '<S16>/SelfAdjFSM' */
uint32_T AccumFFS;                      /* '<S16>/SelfAdjFSM' */
uint32_T AccumBkFFS;                    /* '<S16>/SelfAdjFSM' */
uint32_T BkFFSNCOut;                    /* '<S142>/BkFFSNCOut_Convert' */
uint32_T AccumLam;                      /* '<S16>/SelfAdjFSM' */
uint16_T IDZoneFFSRpm;                  /* '<S84>/PreLookUpIdSearch_U16' */
uint16_T IDZoneFFSLoad;                 /* '<S83>/PreLookUpIdSearch_U16' */
uint16_T AvgIntIonCyl;                  /* '<S4>/Data Type Conversion1' */
uint16_T IntIonMedianCorr;              /* '<S4>/Data Type Conversion10' */
uint16_T AvgIntIon[8];                  /* '<S4>/Data Type Conversion4' */
uint16_T IntIonMedian;                  /* '<S4>/Data Type Conversion7' */
uint16_T BkIntIon[9];                   /* '<S28>/Product' */
uint16_T BkFFSAdj;                      /* '<S87>/Conversion' */
uint16_T RtZoneFFSRpm;                  /* '<S76>/Data Type Conversion' */
uint16_T RtZoneFFSLoad;                 /* '<S76>/Data Type Conversion1' */
uint16_T FFSGainSelfAdj;                /* '<S145>/Divide2' */
uint16_T LamObjSelfAdj;                 /* '<S146>/Add' */
uint16_T LamIntIonCyl;                  /* '<S109>/Signal Conversion' */
uint16_T GnLaMod;                       /* '<S120>/Multiply' */
uint16_T LamFFSCyl;                     /* '<S109>/Signal Conversion1' */
uint16_T LamEstSlow[8];                 /* '<S113>/Assignment1' */
uint16_T AvgFFSCyl;                     /* '<S4>/Data Type Conversion16' */
uint16_T AvgFFS[8];                     /* '<S4>/Data Type Conversion3' */
uint16_T FFSMedian;                     /* '<S4>/Data Type Conversion6' */
uint16_T FFSMedianCorr;                 /* '<S4>/Data Type Conversion9' */
uint16_T BkFFSRef;                      /* '<S145>/Divide3' */
uint16_T BkFFS[9];                      /* '<S25>/Product2' */
int16_T D1LamFil;                       /* '<S78>/Data Type Conversion1' */
int16_T D2LamFil;                       /* '<S78>/Data Type Conversion2' */
int16_T KFiltLamEstAvg;                 /* '<S77>/Switch1' */
int16_T DeltaLamCLMem;                  /* '<S146>/Divide3' */
uint16_T DSAGainFFS;                    /* '<S25>/Add' */
uint16_T DSAGainINT;                    /* '<S28>/Add' */
int16_T FreqNorm;                       /* '<S91>/Conversion' */
uint16_T N0LamFil;                      /* '<S78>/Data Type Conversion' */
uint8_T StabRpmLamTr;                   /* '<S93>/SteadyStateDetect' */
uint8_T sstab_rpm_lam;                  /* '<S93>/SteadyStateDetect' */
uint8_T StabLoadLamTr;                  /* '<S92>/SteadyStateDetect' */
uint8_T sstab_load_lam;                 /* '<S92>/SteadyStateDetect' */
uint8_T FiltParReset;                   /* '<S75>/FixPt Relational Operator' */
uint8_T EnFilButter;                    /* '<S77>/Selector2' */
uint8_T FStabRpmSelfAdj;                /* '<S85>/SigStab' */
uint8_T sstab_rpm_ffs;                  /* '<S85>/SigStab' */
uint8_T FStabLoadSelfAdj;               /* '<S86>/SigStab' */
uint8_T sstab_load_ffs;                 /* '<S86>/SigStab' */
uint8_T EnFilLamEst;                    /* '<S77>/Selector3' */
uint8_T SelfAdjState;                   /* '<S16>/SelfAdjFSM' */
uint8_T EnSelfAdj;                      /* '<S144>/Logical Operator' */
uint8_T IndBkLam;                       /* '<S107>/Data Type Conversion' */
uint8_T LambdaState[8];                 /* '<S106>/Assignment2' */
uint8_T FlgLamIntIonRel[8];             /* '<S108>/Switch2' */
uint8_T FlgLamFFSRel[8];                /* '<S108>/Switch3' */
uint8_T FlgLamRel[8];                   /* '<S108>/Switch4' */
uint16_T GainIntIonCrk;                 /* '<S24>/Conversion' */
uint16_T GainFFSCrk;                    /* '<S20>/Conversion' */

/* Exported block states */
uint32_T Avg_HR_FFS[8];                 /* '<Root>/_DataStoreBlk_18' */
uint32_T Avg_HR_old_FFS[8];             /* '<Root>/_DataStoreBlk_3' */
uint32_T Avg_HR_old_IntIon[8];          /* '<Root>/_DataStoreBlk_6' */
uint32_T Avg_HR_IntIon[8];              /* '<Root>/_DataStoreBlk_9' */
uint32_T In_x_N0_FFS[8];                /* '<Root>/_DataStoreBlk_4' */
uint32_T InSum_FFS[8];                  /* '<Root>/_DataStoreBlk_5' */
uint32_T In_x_N0_IntIon[8];             /* '<Root>/_DataStoreBlk_7' */
uint32_T InSum_IntIon[8];               /* '<Root>/_DataStoreBlk_8' */
uint16_T LamObjMem;                     /* '<Root>/_DataStoreBlk_10' */
uint16_T LamEstAvg;                     /* '<Root>/_DataStoreBlk_14' */
uint16_T LamEst[8];                     /* '<Root>/_DataStoreBlk_15' */
uint8_T TbFlgSelfAdjOK[42];             /* '<Root>/_DataStoreBlk_1' */
uint8_T FiltLamEnable;                  /* '<Root>/_DataStoreBlk_11' */
uint8_T FiltLamFreeze;                  /* '<Root>/_DataStoreBlk_12' */
uint8_T FlgForceOL;                     /* '<Root>/_DataStoreBlk_13' */

/* Block signals (auto storage) */
BlockIO_IonLambda IonLambda_B;

/* Block states (auto storage) */
D_Work_IonLambda IonLambda_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_IonLambda IonLambda_PrevZC;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_IonLambda IonLambda_U;

/* Real-time model */
RT_MODEL_IonLambda IonLambda_M_;
RT_MODEL_IonLambda *IonLambda_M = &IonLambda_M_;



/* Output and update for atomic system: '<S4>/ColdCorrection' */
void IonLambda_ColdCorrection(int16_T rtu_0, uint16_T rtu_1, const uint16_T
 rtu_TBGAINCRK[64], uint16_T rtu_Input, rtB_IonLambda_ColdCorrection *localB)
{
  /* local block i/o variables*/
  uint16_T rtb_Conversion6;
  uint16_T rtb_Conversion7;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_u16_temp41;
  uint16_T rtb_u16_temp42;
  uint16_T rtb_u16_temp43;

  /* S-Function (PreLookUpIdSearch_S16): '<S18>/PreLookUpIdSearch_S16' incorporates:
   *  DataTypeConversion: '<S18>/Data Type Conversion4'
   *  Constant: '<S6>/BKTWATCRKLAM'
   */

  PreLookUpIdSearch_S16( &rtb_u16_temp41, &rtb_PreLookUpIdSearch_S16_o2, rtu_0,
   &(BKTWATCRKLAM[0]), BKTWATCRKLAM_dim);

  /* DataTypeConversion: '<S19>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/CntTdcCrk'
   */
  rtb_u16_temp43 = rtu_1;

  /* S-Function (PreLookUpIdSearch_U16): '<S19>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S6>/BKTDCCRKLAM'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp43, &rtb_u16_temp42, rtb_u16_temp43,
   &(BKTDCCRKLAM[0]), BKTDCCRKLAM_dim);

  /* S-Function (Look2D_IR_U16): '<S17>/Look2D_IR_U16' */

  Look2D_IR_U16( &rtb_u16_temp41, rtu_TBGAINCRK, rtb_u16_temp41,
   rtb_PreLookUpIdSearch_S16_o2, BKTWATCRKLAM_dim, rtb_u16_temp43,
   rtb_u16_temp42, BKTDCCRKLAM_dim);

  /* DataTypeConversion: '<S20>/Conversion' */
  GainFFSCrk = rtb_u16_temp41;

  /* Product: '<S6>/MulCorrect' */
  localB->MulCorrect = (uint16_T)((uint32_T)rtu_Input * (uint32_T)GainFFSCrk >>
    12);
}

/* Output and update for atomic system: '<S4>/ColdCorrection1' */
void IonLambda_ColdCorrection1(int16_T rtu_0, uint16_T rtu_1, const uint16_T
 rtu_TBGAINCRK[64], uint16_T rtu_Input, rtB_IonLambda_ColdCorrection1 *localB)
{
  /* local block i/o variables*/
  uint16_T rtb_Conversion6_o;
  uint16_T rtb_Conversion7_d;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_k;
  uint16_T rtb_u16_temp48;
  uint16_T rtb_u16_temp49;
  uint16_T rtb_u16_temp50;

  /* S-Function (PreLookUpIdSearch_S16): '<S22>/PreLookUpIdSearch_S16' incorporates:
   *  DataTypeConversion: '<S22>/Data Type Conversion4'
   *  Constant: '<S7>/BKTWATCRKLAM'
   */

  PreLookUpIdSearch_S16( &rtb_u16_temp48, &rtb_PreLookUpIdSearch_S16_o2_k,
   rtu_0, &(BKTWATCRKLAM[0]), BKTWATCRKLAM_dim);

  /* DataTypeConversion: '<S23>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/CntTdcCrk'
   */
  rtb_u16_temp50 = rtu_1;

  /* S-Function (PreLookUpIdSearch_U16): '<S23>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S7>/BKTDCCRKLAM'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp50, &rtb_u16_temp49, rtb_u16_temp50,
   &(BKTDCCRKLAM[0]), BKTDCCRKLAM_dim);

  /* S-Function (Look2D_IR_U16): '<S21>/Look2D_IR_U16' */

  Look2D_IR_U16( &rtb_u16_temp48, rtu_TBGAINCRK, rtb_u16_temp48,
   rtb_PreLookUpIdSearch_S16_o2_k, BKTWATCRKLAM_dim, rtb_u16_temp50,
   rtb_u16_temp49, BKTDCCRKLAM_dim);

  /* DataTypeConversion: '<S24>/Conversion' */
  GainIntIonCrk = rtb_u16_temp48;

  /* Product: '<S7>/MulCorrect' */
  localB->MulCorrect_b = (uint16_T)((uint32_T)rtu_Input *
    (uint32_T)GainIntIonCrk >> 12);
}

/* Initial conditions for atomic system:
 *   '<S4>/Filtering'
 *   '<S4>/Filtering1'
 */
void IonLambda_Filtering_Init(rtDW_IonLambda_Filtering *localDW)
{

  /* InitializeConditions for UnitDelay: '<S95>/Unit Delay1' */
  localDW->UnitDelay1_DSTATE = rtcP_pooled10;
}

/* Output and update for atomic system:
 *   '<S4>/Filtering'
 *   '<S4>/Filtering1'
 */
void IonLambda_Filtering(uint16_T rtu_N0LamFil, uint8_T rtu_FiltParReset, const
 uint32_T rtu_In_x_N0_Old[8], uint8_T rtu_3, const uint32_T rtu_InSum_Old[8],
 const uint32_T rtu_Avg_HR_Old[8], const uint32_T rtu_Avg_HR_old2[8], uint8_T
 rtu_FiltLamEnable, uint8_T rtu_EnFilButter, uint16_T rtu_IC, uint16_T
 rtu_Input, uint8_T rtu_FiltLamFreeze, int16_T rtu_D1LamFil, int16_T
 rtu_D2LamFil, rtB_IonLambda_Filtering *localB, rtDW_IonLambda_Filtering
 *localDW)
{
  /* local block i/o variables*/
  uint32_T rtb_Switch3_o;
  uint32_T rtb_Switch2_h[8];
  uint32_T rtb_Switch8;
  uint32_T rtb_Selector_Input_x_N0;
  uint32_T rtb_Product1_i[8];
  uint32_T rtb_Product2[8];
  uint32_T rtb_Switch_d[8];
  uint32_T rtb_Switch6;
  uint16_T rtb_Product7[8];
  uint16_T rtb_Product3[8];
  uint16_T rtb_UnitDelay1;

  {
    int32_T i1;
    uint32_T rtb_u32_tmp_b;
    uint32_T rtb_u32_tmp;
    int32_T rtb_s32_tmp;
    int32_T rtb_s32_tmp_b;

    /* UnitDelay: '<S95>/Unit Delay1' */
    rtb_UnitDelay1 = localDW->UnitDelay1_DSTATE;

    /* Switch: '<S95>/Switch' incorporates:
     *  Product: '<S95>/Product1'
     */
    if(rtu_FiltParReset != 0U) {

      /* Product: '<S95>/Product7'
       *
       * Regarding '<S95>/Product7':
       *  y[i] = 1 / u0 * u1[i]    i = 0 to 7
       *
       * Input0  Data Type:  Fixed Point    U16  2^-20
       * Input1  Data Type:  Fixed Point    U32  2^-20
       * Output0 Data Type:  Integer        U16
       */
      {
        int_T i1;

        const uint32_T *u1 = rtu_In_x_N0_Old;
        uint16_T *y0 = rtb_Product7;

        for (i1=0; i1 < 8; i1++) {
          {
            uint32_T fixpt_temp_quotient;

            DIV_MACRO_u32(fixpt_temp_quotient,(0x00100000U),((uint32_T)rtb_UnitDelay1));

            y0[i1] = ((uint16_T)fixpt_temp_quotient);
          }
          MUL_U16_U32_U16_SR20(y0[i1],u1[i1],y0[i1]);
        }
      }

      {
        int32_T i1;
        for(i1 = 0; i1 < 8; i1++) {

          /* Product: '<S95>/Product1' */
          rtb_Product1_i[i1] = (uint32_T)rtu_N0LamFil *
            (uint32_T)rtb_Product7[i1];
        }
      }

      for(i1 = 0; i1 < 8; i1++) {
        rtb_Switch_d[i1] = rtb_Product1_i[i1];
      }
    } else {
      for(i1 = 0; i1 < 8; i1++) {
        rtb_Switch_d[i1] = rtu_In_x_N0_Old[i1];
      }
    }

    /* Selector: '<S11>/Selector_Input_x_N0' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_Selector_Input_x_N0 = rtb_Switch_d[(int32_T)rtu_3];

    /* Switch: '<S95>/Switch1' incorporates:
     *  Product: '<S95>/Product2'
     */
    if(rtu_FiltParReset != 0U) {

      /* Product: '<S95>/Product3'
       *
       * Regarding '<S95>/Product3':
       *  y[i] = 1 / u0 * u1[i]    i = 0 to 7
       *
       * Input0  Data Type:  Fixed Point    U16  2^-20
       * Input1  Data Type:  Fixed Point    U32  2^-20
       * Output0 Data Type:  Integer        U16
       */
      {
        int_T i1;

        const uint32_T *u1 = rtu_InSum_Old;
        uint16_T *y0 = rtb_Product3;

        for (i1=0; i1 < 8; i1++) {
          {
            uint32_T fixpt_temp_quotient;

            DIV_MACRO_u32(fixpt_temp_quotient,(0x00100000U),((uint32_T)rtb_UnitDelay1));

            y0[i1] = ((uint16_T)fixpt_temp_quotient);
          }
          MUL_U16_U32_U16_SR20(y0[i1],u1[i1],y0[i1]);
        }
      }

      {
        int32_T i1;
        for(i1 = 0; i1 < 8; i1++) {

          /* Product: '<S95>/Product2' */
          rtb_Product2[i1] = (uint32_T)rtu_N0LamFil * (uint32_T)rtb_Product3[i1];
        }
      }

      for(i1 = 0; i1 < 8; i1++) {
        rtb_Switch_d[i1] = rtb_Product2[i1];
      }
    } else {
      for(i1 = 0; i1 < 8; i1++) {
        rtb_Switch_d[i1] = rtu_InSum_Old[i1];
      }
    }

    /* Selector: '<S11>/Selector_InputSum' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_u32_tmp_b = rtb_Switch_d[(int32_T)rtu_3];

    /* Selector: '<S11>/Selector_Outold1' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_Switch3_o = rtu_Avg_HR_Old[(int32_T)rtu_3];
    for(i1 = 0; i1 < 8; i1++) {

      /* Switch: '<S95>/Switch2' */
      if(rtu_FiltParReset != 0U) {
        rtb_Switch2_h[i1] = rtu_Avg_HR_Old[i1];
      } else {
        rtb_Switch2_h[i1] = rtu_Avg_HR_old2[i1];
      }
    }

    /* Selector: '<S11>/Selector_Outold2' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_Switch8 = rtb_Switch2_h[(int32_T)rtu_3];

    /* SubSystem: '<S94>/Butterworth_2order_LOWPASS' */
    if(rtu_FiltLamEnable > 0U) {

      /* Product: '<S96>/Product7' */
      rtb_Switch6 = (uint32_T)rtu_Input * (uint32_T)rtu_N0LamFil;

      /* Switch: '<S96>/Switch' */
      if(rtu_FiltLamFreeze != 0U) {
        localB->Switch = rtb_u32_tmp_b;
      } else {
        localB->Switch = rtb_Selector_Input_x_N0;
      }

      /* Switch: '<S96>/Switch1' */
      if(rtu_FiltLamFreeze != 0U) {
        localB->Switch1 = rtb_Selector_Input_x_N0;
      } else {
        localB->Switch1 = rtb_Switch6;
      }

      /* Switch: '<S96>/Switch2' incorporates:
       *  ArithShift: '<S99>/shift'
       *  Sum: '<S96>/HeadSum2'
       *  Sum: '<S96>/HeadSum3'
       *  Product: '<S96>/Product1'
       *  Product: '<S96>/Product11'
       *  Sum: '<S96>/BodyRSum1'
       *  Sum: '<S96>/HeadSum1'
       */
      if(rtu_FiltLamFreeze != 0U) {
        localB->Switch2 = rtb_Switch3_o;
      } else {
        i1 = mul_s32_s32_u32_sr16((int32_T)rtu_D2LamFil, rtb_Switch8);
        rtb_s32_tmp_b = 0 - i1;
        if(0 && (i1 >= 0) && (rtb_s32_tmp_b >= 0)) {
          rtb_s32_tmp_b = MIN_int32_T;
        } else if(true && (i1 < 0) && (rtb_s32_tmp_b < 0)) {
          rtb_s32_tmp_b = MAX_int32_T;
        }
        i1 = mul_s32_s32_u32_sr16((int32_T)rtu_D1LamFil, rtb_Switch3_o);
        rtb_s32_tmp = rtb_s32_tmp_b - i1;
        if((rtb_s32_tmp_b < 0) && (i1 >= 0) && (rtb_s32_tmp >= 0)) {
          rtb_s32_tmp = MIN_int32_T;
        } else if((rtb_s32_tmp_b >= 0) && (i1 < 0) && (rtb_s32_tmp < 0)) {
          rtb_s32_tmp = MAX_int32_T;
        }
        if(rtb_s32_tmp <= 0) {
          rtb_u32_tmp = 0U;
        } else if(rtb_s32_tmp > 1073741823) {
          rtb_u32_tmp = MAX_uint32_T;
        } else {
          rtb_u32_tmp = (uint32_T)rtb_s32_tmp << 2;
        }
        rtb_u32_tmp_b = (((rtb_Selector_Input_x_N0 << 1U) + rtb_Switch6) +
          rtb_u32_tmp_b) >> 4;
        rtb_u32_tmp += rtb_u32_tmp_b;
        if(rtb_u32_tmp < rtb_u32_tmp_b) {
          rtb_u32_tmp = MAX_uint32_T;
        }
        localB->Switch2 = rtb_u32_tmp;
      }

      /* Switch: '<S96>/Switch3' */
      if(rtu_FiltLamFreeze != 0U) {
        localB->Switch3 = rtb_Switch8;
      } else {
        localB->Switch3 = rtb_Switch3_o;
      }
    }

    /* end of Outputs for SubSystem: '<S94>/Butterworth_2order_LOWPASS' */

    /* SubSystem: '<S94>/InitialCondition' incorporates:
     *  Logic: '<S94>/Logical Operator'
     */
    if(!rtu_FiltLamEnable) {

      /* Product: '<S98>/Product1' */
      localB->Product1 = (uint32_T)rtu_IC * (uint32_T)rtu_N0LamFil;
    }

    /* end of Outputs for SubSystem: '<S94>/InitialCondition' */

    /* SubSystem: '<S94>/ByPassValues' incorporates:
     *  Logic: '<S94>/Logical Operator1'
     */
    if(!rtu_EnFilButter) {

      /* Product: '<S97>/Product1' */
      localB->Product1_d = (uint32_T)rtu_Input * (uint32_T)rtu_N0LamFil;
    }

    /* end of Outputs for SubSystem: '<S94>/ByPassValues' */

    /* Switch: '<S94>/Switch5' */
    if(rtu_EnFilButter != 0U) {

      /* Switch: '<S94>/Switch1' */
      if(rtu_FiltLamEnable != 0U) {
        rtb_Switch6 = localB->Switch1;
      } else {
        rtb_Switch6 = localB->Product1;
      }
    } else {
      rtb_Switch6 = localB->Product1_d;
    }

    /* Assignment: '<S11>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      localB->Assignment1[i1] = rtu_In_x_N0_Old[i1];
    }
    localB->Assignment1[(int32_T)rtu_3] = rtb_Switch6;

    /* Switch: '<S94>/Switch6' */
    if(rtu_EnFilButter != 0U) {

      /* Switch: '<S94>/Switch2' */
      if(rtu_FiltLamEnable != 0U) {
        rtb_Switch6 = localB->Switch;
      } else {
        rtb_Switch6 = localB->Product1;
      }
    } else {
      rtb_Switch6 = localB->Product1_d;
    }

    /* Assignment: '<S11>/Assignment2' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      localB->Assignment2[i1] = rtu_InSum_Old[i1];
    }
    localB->Assignment2[(int32_T)rtu_3] = rtb_Switch6;

    /* DataTypeConversion: '<S94>/Data Type Conversion4' */
    rtb_Switch8 = (uint32_T)rtu_IC << 16U;

    /* DataTypeConversion: '<S94>/Data Type Conversion1' */
    rtb_u32_tmp = (uint32_T)rtu_Input << 16U;

    /* Switch: '<S94>/Switch7' */
    if(rtu_EnFilButter != 0U) {

      /* Switch: '<S94>/Switch3' */
      if(rtu_FiltLamEnable != 0U) {
        rtb_Switch3_o = localB->Switch2;
      } else {
        rtb_Switch3_o = rtb_Switch8;
      }
      localB->Switch7 = rtb_Switch3_o;
    } else {
      localB->Switch7 = rtb_u32_tmp;
    }

    /* Assignment: '<S11>/Assignment3' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      localB->Assignment3[i1] = rtu_Avg_HR_Old[i1];
    }
    localB->Assignment3[(int32_T)rtu_3] = localB->Switch7;

    /* Switch: '<S94>/Switch8' */
    if(rtu_EnFilButter != 0U) {

      /* Switch: '<S94>/Switch4' */
      if(rtu_FiltLamEnable != 0U) {
        rtb_Switch8 = localB->Switch3;
      }
    } else {
      rtb_Switch8 = rtb_u32_tmp;
    }

    /* Assignment: '<S11>/Assignment4' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      localB->Assignment4[i1] = rtu_Avg_HR_old2[i1];
    }
    localB->Assignment4[(int32_T)rtu_3] = rtb_Switch8;

    /* Update for UnitDelay: '<S95>/Unit Delay1' */
    localDW->UnitDelay1_DSTATE = rtu_N0LamFil;
  }
}

/* Output and update for atomic system:
 *   '<S121>/CalcLamRel'
 *   '<S122>/CalcLamRel'
 */
void IonLambda_CalcLamRel(int16_T rtu_ratio, uint8_T rtu_IdLam, const uint16_T
 rtu_bkx[9], uint16_T rtu_THAVGX, uint16_T rtu_Avg_x, rtB_IonLambda_CalcLamRel
 *localB)
{
  /* local block i/o variables*/
  int16_T rtb_Add3;

  {
    int32_T i1;
    uint32_T rtb_u32_tmp;
    uint16_T rtb_u16_tmp;

    /* Sum: '<S128>/Add3' */
    rtb_Add3 = (int16_T)((rtu_ratio >> 4) + ((int16_T)rtu_IdLam << 8U));

    /* Switch: '<S128>/Switch' incorporates:
     *  DataTypeConversion: '<S128>/Conversion2'
     *  Selector: '<S128>/Selector1'
     *  Sum: '<S128>/Add1'
     *  RelationalOperator: '<S128>/Relational Operator2'
     *  Sum: '<S128>/Add2'
     *  RelationalOperator: '<S128>/Relational Operator3'
     *  RelationalOperator: '<S128>/Relational Operator'
     *  RelationalOperator: '<S128>/Relational Operator1'
     *  Logic: '<S128>/Logical Operator'
     *  Constant: '<S128>/LEN_BKLAM'
     *  Constant: '<S109>/THAVGINTION'
     */
    if((rtb_Add3 == 0) || (LEN_BKLAM << 8U == rtb_Add3)) {
      rtb_u32_tmp = (uint32_T)rtu_Avg_x + (uint32_T)rtu_THAVGX;
      if(rtb_u32_tmp > 65535U) {
        rtb_u16_tmp = MAX_uint16_T;
      } else {
        rtb_u16_tmp = (uint16_T)rtb_u32_tmp;
      }
      localB->Switch_g = rtb_u16_tmp >= rtu_bkx[rtb_Add3 >> 8];
    } else {

      /* MinMax: '<S128>/MinMax' */
      rtb_u16_tmp = rtu_bkx[0];
      for(i1 = 1; i1 < 9; i1++) {
        rtb_u16_tmp = rt_MAX(rtb_u16_tmp, rtu_bkx[i1]);
      }
      rtb_u32_tmp = (uint32_T)rtb_u16_tmp + (uint32_T)rtu_THAVGX;
      if(rtb_u32_tmp > 65535U) {
        rtb_u16_tmp = MAX_uint16_T;
      } else {
        rtb_u16_tmp = (uint16_T)rtb_u32_tmp;
      }
      localB->Switch_g = rtb_u16_tmp >= rtu_Avg_x;
    }
  }
}

/* Output and update for atomic system:
 *   '<S121>/CalcLambda'
 *   '<S122>/CalcLambda'
 */
void IonLambda_CalcLambda(int16_T rtu_ratio, uint8_T rtu_IdLam, const uint16_T
 rtu_BKLAM[9], rtB_IonLambda_CalcLambda *localB)
{
  /* local block i/o variables*/
  uint16_T rtb_Conversion3;
  uint16_T rtb_u16_temp94;
  uint16_T rtb_Conversion4;

  {
    uint16_T rtb_u16_tmp;

    /* DataTypeConversion: '<S129>/Conversion4' */
    if(rtu_ratio <= 0) {
      rtb_u16_tmp = 0U;
    } else if(rtu_ratio > 4095) {
      rtb_u16_tmp = MAX_uint16_T;
    } else {
      rtb_u16_tmp = (uint16_T)((uint16_T)rtu_ratio << 4);
    }
    rtb_Conversion4 = rtb_u16_tmp;

    /* DataTypeConversion: '<S132>/Conversion2' */
    rtb_u16_temp94 = (uint16_T)rtu_IdLam;
  }

  /* S-Function (LookUp_IR_U16): '<S132>/LookUp_IR_U16' */

  LookUp_IR_U16( &rtb_u16_temp94, rtu_BKLAM, rtb_u16_temp94, rtb_Conversion4,
   LEN_BKLAM);

  /* DataTypeConversion: '<S133>/Conversion' */
  localB->Conversion = rtb_u16_temp94;
}

/* Output and update for function-call system:
 *   '<S121>/CalcStep'
 *   '<S122>/CalcStep'
 */
void IonLambda_CalcStep(uint8_T rtu_IdLam, const uint16_T rtu_bkx[9], uint16_T
 rtu_Avg_x, rtB_IonLambda_CalcStep *localB)
{
  /* local block i/o variables*/
  int16_T rtb_Add1_b;
  uint16_T rtb_Selector2;

  {
    uint16_T rtb_u16_min_b;
    uint16_T rtb_u16_min;

    /* Selector: '<S130>/Selector2' */
    rtb_Selector2 = rtu_bkx[(int32_T)rtu_IdLam];

    /* Selector: '<S130>/Selector3' incorporates:
     *  Sum: '<S130>/Add1'
     */
    rtb_u16_min = rtu_bkx[(uint32_T)rtu_IdLam+1];

    /* Sum: '<S134>/Add1' */
    rtb_Add1_b = (int16_T)((int16_T)rtb_u16_min - (int16_T)rtb_Selector2);

    /* Switch: '<S134>/Switch' incorporates:
     *  MinMax: '<S134>/MinMax3'
     *  Sum: '<S134>/Add2'
     *  Product: '<S134>/Divide'
     */
    if(rtb_Add1_b != 0) {

      /* MinMax: '<S134>/MinMax1' */
      rtb_u16_min_b = rt_MIN(rtb_u16_min, rtb_Selector2);

      /* MinMax: '<S134>/MinMax' */
      rtb_u16_min = rt_MAX(rtb_u16_min, rtb_Selector2);

      /* MinMax: '<S134>/MinMax2' */
      rtb_u16_min = rt_MIN(rtb_u16_min, rtu_Avg_x);
      localB->Switch_m = div_s16s32_floor(((int16_T)rt_MAX(rtb_u16_min,
         rtb_u16_min_b) - (int16_T)rtb_Selector2) << 12U, (int32_T)rtb_Add1_b);
    } else {
      localB->Switch_m = 0;
    }
  }
}

/* Initial conditions for atomic system: '<S109>/Lam_by_x1' */
void IonLambda_Lam_by_x1_Init(rtB_IonLambda_Lam_by_x1 *localB)
{

  /*atomic Subsystem Block: <S121>/Search_lambda */

  /* Initialize code for chart: '<S121>/Search_lambda' */
  localB->IdLam = 0U;
}

/* Output and update for atomic system: '<S109>/Lam_by_x1' */
void IonLambda_Lam_by_x1(uint8_T rtu_IndBkLam, const uint16_T rtu_bkx[9],
 uint16_T rtu_THAVGX, uint16_T rtu_Avg_x, const uint16_T rtu_BKLAM[9],
 rtB_IonLambda_Lam_by_x1 *localB)
{

  {

    /* Stateflow: '<S121>/Search_lambda' */

    {
      int16_T ratio_old;
      localB->IdLam = rtu_IndBkLam;

      IonLambda_CalcStep(localB->IdLam, rtu_bkx, rtu_Avg_x, &localB->CalcStep);

      ratio_old = localB->CalcStep.Switch_m;
      sf_label_34_0_2:;
      if(((localB->CalcStep.Switch_m == 0) || (localB->CalcStep.Switch_m ==
         4096)) && (localB->CalcStep.Switch_m == ratio_old)) {
        if(localB->CalcStep.Switch_m == 0) {
          if(localB->IdLam <= 0) {
            goto sf_label_34_0_1;
          } else {
            localB->IdLam = (uint8_T)(localB->IdLam - 1);
          }
        } else if(localB->IdLam >= LEN_BKLAM - 1) {
          goto sf_label_34_0_1;
        } else {
          localB->IdLam = (uint8_T)(localB->IdLam + 1);
        }
      } else {
        goto sf_label_34_0_1;
      }
      ratio_old = localB->CalcStep.Switch_m;

      IonLambda_CalcStep(localB->IdLam, rtu_bkx, rtu_Avg_x, &localB->CalcStep);

      goto sf_label_34_0_2;
      sf_label_34_0_1:;
    }
  }

  /* SubSystem: '<S121>/CalcLamRel' */
  IonLambda_CalcLamRel(localB->CalcStep.Switch_m, localB->IdLam, rtu_bkx,
   rtu_THAVGX, rtu_Avg_x, &localB->CalcLamRel);

  /* SubSystem: '<S121>/CalcLambda' */
  IonLambda_CalcLambda(localB->CalcStep.Switch_m, localB->IdLam, rtu_BKLAM,
   &localB->CalcLambda);
}

/* Initial conditions for atomic system: '<S109>/Lam_by_x2' */
void IonLambda_Lam_by_x2_Init(rtB_IonLambda_Lam_by_x2 *localB)
{

  /*atomic Subsystem Block: <S122>/Search_lambda */

  /* Initialize code for chart: '<S122>/Search_lambda' */
  localB->IdLam_c = 0U;
}

/* Output and update for atomic system: '<S109>/Lam_by_x2' */
void IonLambda_Lam_by_x2(uint8_T rtu_IndBkLam, const uint16_T rtu_bkx[9],
 uint16_T rtu_THAVGX, uint16_T rtu_Avg_x, const uint16_T rtu_BKLAM[9],
 rtB_IonLambda_Lam_by_x2 *localB)
{

  {

    /* Stateflow: '<S122>/Search_lambda' */

    {
      int16_T ratio_old;
      localB->IdLam_c = rtu_IndBkLam;

      IonLambda_CalcStep(localB->IdLam_c, rtu_bkx, rtu_Avg_x,
       &localB->CalcStep_e);

      ratio_old = localB->CalcStep_e.Switch_m;
      sf_label_36_0_2:;
      if(((localB->CalcStep_e.Switch_m == 0) || (localB->CalcStep_e.Switch_m ==
         4096)) && (localB->CalcStep_e.Switch_m == ratio_old)) {
        if(localB->CalcStep_e.Switch_m == 0) {
          if(localB->IdLam_c <= 0) {
            goto sf_label_36_0_1;
          } else {
            localB->IdLam_c = (uint8_T)(localB->IdLam_c - 1);
          }
        } else if(localB->IdLam_c >= LEN_BKLAM - 1) {
          goto sf_label_36_0_1;
        } else {
          localB->IdLam_c = (uint8_T)(localB->IdLam_c + 1);
        }
      } else {
        goto sf_label_36_0_1;
      }
      ratio_old = localB->CalcStep_e.Switch_m;

      IonLambda_CalcStep(localB->IdLam_c, rtu_bkx, rtu_Avg_x,
       &localB->CalcStep_e);

      goto sf_label_36_0_2;
      sf_label_36_0_1:;
    }
  }

  /* SubSystem: '<S122>/CalcLamRel' */
  IonLambda_CalcLamRel(localB->CalcStep_e.Switch_m, localB->IdLam_c, rtu_bkx,
   rtu_THAVGX, rtu_Avg_x, &localB->CalcLamRel_j);

  /* SubSystem: '<S122>/CalcLambda' */
  IonLambda_CalcLambda(localB->CalcStep_e.Switch_m, localB->IdLam_c, rtu_BKLAM,
   &localB->CalcLambda_k);
}

/* Initial conditions for atomic system: '<S4>/LambdaMeter' */
void IonLambda_LambdaMeter_Init(void)
{

  /*atomic Subsystem Block: <S109>/Lam_by_x1 */
  IonLambda_Lam_by_x1_Init(&IonLambda_B.Lam_by_x1);

  /*atomic Subsystem Block: <S109>/Lam_by_x2 */
  IonLambda_Lam_by_x2_Init(&IonLambda_B.Lam_by_x2);

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* InitializeConditions for UnitDelay: '<S106>/DelayOut1' */
      IonLambda_DWork.DelayOut1_DSTATE[i1] = (IonLambda_ConstP.DelayOut1_X0[i1]);
    }
  }

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* InitializeConditions for Memory: '<S113>/Memory1' */
      IonLambda_DWork.Memory1_PreviousInput_k[i1] =
        (IonLambda_ConstP.pooled7[i1]);

      /* InitializeConditions for Memory: '<S113>/Memory2' */
      IonLambda_DWork.Memory2_PreviousInput[i1] = (IonLambda_ConstP.pooled1[i1]);
    }
  }

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* InitializeConditions for SubSystem: '<S13>/Lambda_Reliable' */

      /* InitializeConditions for UnitDelay: '<S108>/DelayOut1' */
      IonLambda_DWork.DelayOut1_DSTATE_p[i1] =
        (IonLambda_ConstP.DelayOut1_X0_p[i1]);

      /* InitializeConditions for UnitDelay: '<S108>/DelayOut3' */
      IonLambda_DWork.DelayOut3_DSTATE[i1] = (IonLambda_ConstP.pooled13[i1]);

      /* InitializeConditions for UnitDelay: '<S108>/DelayOut4' */
      IonLambda_DWork.DelayOut4_DSTATE[i1] = (IonLambda_ConstP.pooled13[i1]);

      /* end of InitializeConditions for SubSystem: '<S13>/Lambda_Reliable' */
    }
  }
}

/* Start for atomic system: '<S4>/LambdaMeter' */
void IonLambda_LambdaMeter_Start(void)
{

  /* Start for SubSystem: '<S106>/Accumulator' */

  /* InitializeConditions for Memory: '<S110>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_a = rtcP_pooled6;

  /* end of Start for SubSystem: '<S106>/Accumulator' */
}

/* Output and update for atomic system: '<S4>/LambdaMeter' */
void IonLambda_LambdaMeter(void)
{
  /* local block i/o variables*/
  int32_T rtb_i32_temp106;
  int32_T rtb_Selector_j;
  int32_T rtb_Selector_p;
  int32_T rtb_Assignment3_i[8];
  int32_T rtb_DataStoreRead1[8];
  int32_T rtb_i32_temp111[8];
  int16_T rtb_i16_temp112;
  uint16_T rtb_u16_temp113;
  uint16_T rtb_u16_temp114;
  uint16_T rtb_u16_temp115;
  uint16_T rtb_u16_temp116;
  uint16_T rtb_LamEstFilt[8];
  uint16_T rtb_Switch1_i;
  uint16_T rtb_DataStoreRead6[8];
  uint16_T rtb_LamEst_o[8];
  uint16_T rtb_LamEst_cyl;
  uint16_T rtb_u16_temp127;
  uint16_T rtb_u16_temp128[8];
  uint16_T rtb_u16_temp129;
  int16_T rtb_Conversion5;
  int16_T rtb_Conversion5_d;
  uint16_T rtb_lamintioncylweighed;
  int8_T rtb_ForIterator;
  uint8_T rtb_LogicalOperator1_m;
  uint8_T rtb_Assignment3_d[8];
  uint8_T rtb_Assignment4_o[8];
  uint8_T rtb_DelayOut1[8];
  uint8_T rtb_u8_temp147;
  boolean_T rtb_Assignment1_h[8];
  boolean_T rtb_Switch1_k;

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreRead: '<S106>/Data Store Read6' */
      rtb_DataStoreRead6[i1] = LamEst[i1];
    }

    /* Switch: '<S107>/Switch' incorporates:
     *  Constant: '<S107>/IONLAMOBJTUN'
     *  Inport: '<Root>/LamObj'
     */
    if(IONLAMOBJTUN > rtcP_pooled6) {
      rtb_u16_temp127 = IONLAMOBJTUN;
    } else {

      /* Switch: '<S107>/Switch1' */
      if(IonLambda_B.DataStoreRead18 != 0U) {
        rtb_Switch1_i = LamObj;
      } else {
        rtb_Switch1_i = 1024U;
      }
      rtb_u16_temp127 = rtb_Switch1_i;
    }

  }

  /* S-Function (PreLookUpIdSearch_U16): '<S119>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S107>/BKLAM'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp113, &rtb_u16_temp116, rtb_u16_temp127,
   &(BKLAM[0]), rtcP_pooled15);

  /* DataTypeConversion: '<S107>/Data Type Conversion' */
  IndBkLam = (uint8_T)rtb_u16_temp113;

  /* SubSystem: '<S109>/Lam_by_x1' incorporates:
   *  Constant: '<S109>/THAVGINTION'
   *  Constant: '<S109>/BKLAM'
   */
  IonLambda_Lam_by_x1(IndBkLam, &BkIntIon[0], THAVGINTION, AvgIntIonCyl,
   &(BKLAM[0]), &IonLambda_B.Lam_by_x1);

  /* SignalConversion: '<S109>/Signal Conversion' incorporates:
   *  SignalConversion: '<S109>/TmpHiddenBufferAtSignal ConversionInport1'
   */
  LamIntIonCyl = IonLambda_B.Lam_by_x1.CalcLambda.Conversion;

  /* S-Function (PreLookUpIdSearch_U16): '<S125>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S120>/BKLAMINDEX'
   *  Constant: '<S120>/BKLAMINDEX_dim'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp116, &rtb_u16_temp115, rtb_u16_temp127,
   &(BKLAMINDEX[0]), BKLAMINDEX_dim);

  /* S-Function (LookUp_IR_U16): '<S124>/LookUp_IR_U16' incorporates:
   *  Constant: '<S120>/VTWEIGHLAM'
   *  Constant: '<S120>/BKLAMINDEX_dim'
   */

  LookUp_IR_U16( &rtb_u16_temp116, &(VTWEIGHLAM[0]), rtb_u16_temp116,
   rtb_u16_temp115, BKLAMINDEX_dim);

  /* S-Function (Look2D_IR_U16): '<S123>/Look2D_IR_U16' incorporates:
   *  Constant: '<S120>/TBGNLAMOD'
   *  Constant: '<S120>/BKRPMLAMOD_dim'
   *  Constant: '<S120>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_u16_temp115, &(TBGNLAMOD[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  {
    uint32_T rtb_u32_tmp;
    uint16_T rtb_u16_tmp;

    /* Product: '<S120>/Multiply' */
    GnLaMod = (uint16_T)((uint32_T)rtb_u16_temp116 * (uint32_T)rtb_u16_temp115
      >> 10);

    /* Product: '<S106>/Multiply' */
    rtb_lamintioncylweighed = (uint16_T)((uint32_T)LamIntIonCyl *
      (uint32_T)GnLaMod >> 9);

    /* Sum: '<S106>/Subtract' */
    rtb_u32_tmp = 1024U - (uint32_T)GnLaMod;
    if(rtb_u32_tmp > 1024U) {
      rtb_u32_tmp = (uint32_T)0U;
    }
    if(rtb_u32_tmp > 65535U) {
      rtb_u16_tmp = MAX_uint16_T;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_u32_tmp;
    }
    rtb_u16_temp113 = rtb_u16_tmp;
  }

  /* SubSystem: '<S109>/Lam_by_x2' incorporates:
   *  Constant: '<S109>/THAVGFFS'
   *  Constant: '<S109>/BKLAM'
   */
  IonLambda_Lam_by_x2(IndBkLam, &BkFFS[0], THAVGFFS, AvgFFSCyl, &(BKLAM[0]),
   &IonLambda_B.Lam_by_x2);

  {
    int32_T i1;

    /* SignalConversion: '<S109>/Signal Conversion1' incorporates:
     *  SignalConversion: '<S109>/TmpHiddenBufferAtSignal Conversion1Inport1'
     */
    LamFFSCyl = IonLambda_B.Lam_by_x2.CalcLambda_k.Conversion;

    /* Sum: '<S106>/Subtract1' incorporates:
     *  Product: '<S106>/Multiply1'
     *  Product: '<S106>/Multiply'
     */
    rtb_u16_temp127 = (uint16_T)((uint32_T)((uint16_T)((uint32_T)rtb_u16_temp113
      * (uint32_T)LamFFSCyl >> 9) >> 1) + (uint32_T)(rtb_lamintioncylweighed >>
      1));

    /* Assignment: '<S106>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      rtb_LamEst_o[i1] = rtb_DataStoreRead6[i1];
    }
    rtb_LamEst_o[(int32_T)IonAbsTdc] = rtb_u16_temp127;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreWrite: '<S106>/Data Store Write3' */
      LamEst[i1] = rtb_LamEst_o[i1];

      /* DataStoreRead: '<S111>/Data Store Read1' */
      rtb_DataStoreRead1[i1] = IonLambda_DWork.LamEstFilt_HR[i1];
    }

    /* Selector: '<S111>/Selector1' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_LamEst_cyl = rtb_LamEst_o[(int32_T)IonAbsTdc];

    /* DataTypeConversion: '<S111>/Conversion5' */
    rtb_Conversion5 = (int16_T)rtb_LamEst_cyl;

    /* DataTypeConversion: '<S115>/Conversion5' incorporates:
     *  DataStoreRead: '<S115>/Data Store Read1'
     */
    rtb_Conversion5_d = (int16_T)LamObjMem;

    /* Logic: '<S115>/Logical Operator1' incorporates:
     *  Logic: '<S115>/Logical Operator'
     *  Logic: '<S115>/Logical Operator2'
     *  DataStoreRead: '<S115>/Data Store Read15'
     */
    rtb_LogicalOperator1_m = (!IonLambda_B.DataStoreRead18) || (!EnFilLamEst) ||
      FlgForceOL;

    /* Selector: '<S111>/Selector' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_Selector_j = rtb_DataStoreRead1[(int32_T)IonAbsTdc];
  }

  /* S-Function (FOF_Reset_S16_FXP): '<S114>/FOF_Reset_S16_FXP' */

  FOF_Reset_S16_FXP( &rtb_i16_temp112, &rtb_i32_temp106, rtb_Conversion5,
   KFiltLamEstAvg, rtb_Conversion5_d, rtb_LogicalOperator1_m, rtb_Selector_j);

  {
    int8_T rtb_s8_iter;
    int32_T i1;

    /* Assignment: '<S111>/Assignment3' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      rtb_i32_temp111[i1] = rtb_DataStoreRead1[i1];
    }
    rtb_i32_temp111[(int32_T)IonAbsTdc] = rtb_i32_temp106;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreWrite: '<S111>/Data Store Write1' */
      IonLambda_DWork.LamEstFilt_HR[i1] = rtb_i32_temp111[i1];
      rtb_LamEstFilt[i1] = IonLambda_DWork.LamEstFilt[i1];
    }

    /* Assignment: '<S111>/Assignment1' incorporates:
     *  DataStoreRead: '<S111>/Data Store Read6'
     *  DataTypeConversion: '<S111>/Conversion1'
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_LamEstFilt[(int32_T)IonAbsTdc] = (uint16_T)rtb_i16_temp112;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreWrite: '<S111>/Data Store Write3' */
      IonLambda_DWork.LamEstFilt[i1] = rtb_LamEstFilt[i1];
    }

    /* DataStoreRead: '<S115>/Data Store Read2' */
    rtb_u8_temp147 = FlgForceOL;

    /* SubSystem: '<S106>/Accumulator' incorporates:
     *  Constant: '<S106>/N_CYLINDER'
     */
    if(!rtmIsFirstInitCond(IonLambda_M)) {

      /* InitializeConditions for Memory: '<S110>/Memory' */
      IonLambda_DWork.Memory_PreviousInput_a = rtcP_pooled6;
    }
    for(rtb_s8_iter = 0; rtb_s8_iter < N_CYLINDER; rtb_s8_iter++) {
      rtb_ForIterator = rtb_s8_iter;

      /* Sum: '<S110>/Sum1' incorporates:
       *  Memory: '<S110>/Memory'
       *  Selector: '<S110>/Selector'
       */
      IonLambda_B.sum =
        (uint16_T)((uint32_T)rtb_LamEstFilt[(int32_T)rtb_ForIterator] +
        (uint32_T)IonLambda_DWork.Memory_PreviousInput_a);

      /* Update for Memory: '<S110>/Memory' */
      IonLambda_DWork.Memory_PreviousInput_a = IonLambda_B.sum;
    }

    /* end of Outputs for SubSystem: '<S106>/Accumulator' */
    for(i1 = 0; i1 < 8; i1++) {

      /* UnitDelay: '<S106>/DelayOut1' */
      rtb_DelayOut1[i1] = IonLambda_DWork.DelayOut1_DSTATE[i1];
    }
  }

  /* S-Function (PreLookUpIdSearch_U16): '<S112>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S106>/BKLAMINDEX'
   *  Constant: '<S106>/BKLAMINDEX_dim'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp129, &rtb_u16_temp114, rtb_u16_temp127,
   &(BKLAMINDEX[0]), BKLAMINDEX_dim);

  {
    int32_T i1;
    uint32_T rtb_u32_tmp;

    /* Assignment: '<S106>/Assignment2' incorporates:
     *  Selector: '<S106>/Selector'
     *  Constant: '<S106>/VTLAMBDASTATE'
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      LambdaState[i1] = rtb_DelayOut1[i1];

      /* Memory: '<S113>/Memory1' */
      rtb_u16_temp128[i1] = IonLambda_DWork.Memory1_PreviousInput_k[i1];

      /* Memory: '<S113>/Memory2' */
      rtb_i32_temp111[i1] = IonLambda_DWork.Memory2_PreviousInput[i1];
    }
    LambdaState[(int32_T)IonAbsTdc] = VTLAMBDASTATE[rtb_u16_temp129];

    /* Product: '<S106>/Product' incorporates:
     *  Constant: '<S106>/N_CYLINDER'
     */
    rtb_u32_tmp = (uint32_T)N_CYLINDER;
    IonLambda_B.LamEstAvg_f = (uint16_T)(rtb_u32_tmp == (uint32_T)0 ?
      MAX_uint32_T : (uint32_T)((uint32_T)IonLambda_B.sum / rtb_u32_tmp));

    /* Selector: '<S113>/Selector' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_Selector_p = rtb_i32_temp111[(int32_T)IonAbsTdc];
  }

  /* S-Function (FOF_Reset_S16_FXP): '<S117>/FOF_Reset_S16_FXP' incorporates:
   *  DataTypeConversion: '<S113>/Conversion5'
   *  Constant: '<S113>/KFILTLAMESTSLOW'
   *  Constant: '<S113>/KFILTLAMESTSLOW1'
   *  Constant: '<S113>/KFILTLAMESTSLOW2'
   */

  FOF_Reset_S16_FXP( &rtb_i16_temp112, &rtb_i32_temp106,
   ((int16_T)rtb_u16_temp127), KFILTLAMESTSLOW, rtcP_KFILTLAMESTSLOW1_Value,
   rtcP_pooled12, rtb_Selector_p);

  {
    int32_T i1;

    /* Assignment: '<S113>/Assignment1' incorporates:
     *  DataTypeConversion: '<S113>/Conversion1'
     *  Assignment: '<S113>/Assignment3'
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      LamEstSlow[i1] = rtb_u16_temp128[i1];
      rtb_Assignment3_i[i1] = rtb_i32_temp111[i1];
    }
    LamEstSlow[(int32_T)IonAbsTdc] = (uint16_T)rtb_i16_temp112;

    /* Assignment: '<S113>/Assignment3' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_Assignment3_i[(int32_T)IonAbsTdc] = rtb_i32_temp106;

    /* SubSystem: '<S13>/Lambda_Reliable' incorporates:
     *  Constant: '<S108>/ENFLGLAMREL'
     *  Constant: '<S108>/ONES_N_CYL_MAX'
     *  Inport: '<Root>/IonAbsTdc'
     */

    /* Switch: '<S108>/Switch' incorporates:
     *  RelationalOperator: '<S108>/Relational Operator'
     *  SignalConversion: '<S109>/TmpHiddenBufferAtflglamFFSrelInport1'
     */
    if(0U == GnLaMod) {
      rtb_Switch1_k = (IonLambda_B.Lam_by_x2.CalcLamRel_j.Switch_g != 0U);
    } else {

      /* Switch: '<S108>/Switch1' incorporates:
       *  RelationalOperator: '<S108>/Relational Operator1'
       *  Logic: '<S108>/Logical Operator'
       *  SignalConversion: '<S109>/TmpHiddenBufferAtflglamIntIonrelInport1'
       *  SignalConversion: '<S109>/TmpHiddenBufferAtflglamFFSrelInport1'
       */
      if(1024U == GnLaMod) {
        rtb_Switch1_k = (IonLambda_B.Lam_by_x1.CalcLamRel.Switch_g != 0U);
      } else {
        rtb_Switch1_k = (IonLambda_B.Lam_by_x1.CalcLamRel.Switch_g &&
          IonLambda_B.Lam_by_x2.CalcLamRel_j.Switch_g);
      }
    }

    /* Assignment: '<S108>/Assignment1' incorporates:
     *  UnitDelay: '<S108>/DelayOut1'
     *  Logic: '<S108>/Logical Operator2'
     *  Logic: '<S108>/Logical Operator1'
     *  UnitDelay: '<S108>/DelayOut3'
     *  Assignment: '<S108>/Assignment3'
     *  UnitDelay: '<S108>/DelayOut4'
     *  Assignment: '<S108>/Assignment4'
     *  SignalConversion: '<S109>/TmpHiddenBufferAtflglamIntIonrelInport1'
     *  SignalConversion: '<S109>/TmpHiddenBufferAtflglamFFSrelInport1'
     */
    for(i1 = 0; i1 < 8; i1++) {
      rtb_Assignment1_h[i1] = IonLambda_DWork.DelayOut1_DSTATE_p[i1];
      rtb_Assignment3_d[i1] = IonLambda_DWork.DelayOut3_DSTATE[i1];
      rtb_Assignment4_o[i1] = IonLambda_DWork.DelayOut4_DSTATE[i1];
    }
    rtb_Assignment1_h[(int32_T)IonAbsTdc] = (rtb_Switch1_k &&
      (!IonLambda_B.DataStoreRead19));

    /* Assignment: '<S108>/Assignment3' */
    rtb_Assignment3_d[(int32_T)IonAbsTdc] =
      IonLambda_B.Lam_by_x1.CalcLamRel.Switch_g;

    /* Assignment: '<S108>/Assignment4' */
    rtb_Assignment4_o[(int32_T)IonAbsTdc] =
      IonLambda_B.Lam_by_x2.CalcLamRel_j.Switch_g;
    for(i1 = 0; i1 < 8; i1++) {

      /* Switch: '<S108>/Switch2' */
      if(ENFLGLAMREL != 0U) {
        FlgLamIntIonRel[i1] = rtb_Assignment3_d[i1];
      } else {
        FlgLamIntIonRel[i1] = (IonLambda_ConstP.pooled13[i1]);
      }

      /* Switch: '<S108>/Switch3' */
      if(ENFLGLAMREL != 0U) {
        FlgLamFFSRel[i1] = rtb_Assignment4_o[i1];
      } else {
        FlgLamFFSRel[i1] = (IonLambda_ConstP.pooled13[i1]);
      }

      /* Switch: '<S108>/Switch4' */
      if(ENFLGLAMREL != 0U) {
        FlgLamRel[i1] = (uint8_T)rtb_Assignment1_h[i1];
      } else {
        FlgLamRel[i1] = (IonLambda_ConstP.pooled13[i1]);
      }

      /* Update for UnitDelay: '<S108>/DelayOut1' */
      IonLambda_DWork.DelayOut1_DSTATE_p[i1] = rtb_Assignment1_h[i1];

      /* Update for UnitDelay: '<S108>/DelayOut3' */
      IonLambda_DWork.DelayOut3_DSTATE[i1] = rtb_Assignment3_d[i1];

      /* Update for UnitDelay: '<S108>/DelayOut4' */
      IonLambda_DWork.DelayOut4_DSTATE[i1] = rtb_Assignment4_o[i1];
    }

    /* end of Outputs for SubSystem: '<S13>/Lambda_Reliable' */
  }

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* Update for UnitDelay: '<S106>/DelayOut1' */
      IonLambda_DWork.DelayOut1_DSTATE[i1] = LambdaState[i1];
    }
  }

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* Update for Memory: '<S113>/Memory1' */
      IonLambda_DWork.Memory1_PreviousInput_k[i1] = LamEstSlow[i1];

      /* Update for Memory: '<S113>/Memory2' */
      IonLambda_DWork.Memory2_PreviousInput[i1] = rtb_Assignment3_i[i1];
    }
  }
}

/* Initial conditions for atomic system: '<S4>/Median' */
void IonLambda_Median_Init(rtB_IonLambda_Median *localB, rtDW_IonLambda_Median
 *localDW)
{

  /* Initialize code for chart: '<S4>/Median' */

  {
    int32_T sf_i0;
    int32_T sf_i1;
    localDW->Median.is_active_c3_IonLambda = 0U;
    localDW->Median.is_c3_IonLambda = 0U;
    for(sf_i0 = 0; sf_i0 < 8; sf_i0++) {
      for(sf_i1 = 0; sf_i1 < 7; sf_i1++) {
        localDW->Median.Buffer[sf_i0][sf_i1] = 0U;
      }
      localDW->Median.Index[sf_i0] = 0U;
    }
    for(sf_i1 = 0; sf_i1 < 7; sf_i1++) {
      localDW->Median.VettSort[sf_i1] = 0U;
    }
    localB->median = 0U;
  }
}

/* Output and update for atomic system: '<S4>/Median' */
void IonLambda_Median(const uint16_T rtu_Input[8], uint8_T rtu_1, uint8_T
 rtu_FiltLamEnable, uint8_T rtu_LENBUFMED, uint8_T rtu_N_CYLINDER, uint8_T
 rtu_FiltLamFreeze, rtB_IonLambda_Median *localB, rtDW_IonLambda_Median *localDW)
{

  /* Stateflow: '<S4>/Median' */

  {
    uint8_T idx1;
    uint8_T idx2;
    uint8_T _rlt1;
    uint8_T idx_median;
    uint16_T _rlt3;
    uint16_T _rlt2;
    uint16_T temp;
    if(localDW->Median.is_active_c3_IonLambda == 0) {
      localDW->Median.is_active_c3_IonLambda = 1U;
      for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
        for(idx2 = 0U; idx2 < rtu_N_CYLINDER; idx2++) {
          localDW->Median.Buffer[(int32_T)idx2][(int32_T)idx1] =
            rtu_Input[(int32_T)rtu_1];
          localDW->Median.Index[(int32_T)idx2] = 0U;
        }
      }
      localB->median = rtu_Input[(int32_T)rtu_1];
      localDW->Median.is_c3_IonLambda = (uint8_T)IonLambda_IN_BUF;
    } else {
      switch(localDW->Median.is_c3_IonLambda) {
       case IonLambda_IN_BUF:
        if(!(rtu_FiltLamEnable != 0)) {
          localDW->Median.is_c3_IonLambda = (uint8_T)IonLambda_IN_RESET;
        } else {
          if(!(rtu_FiltLamFreeze != 0)) {
            localDW->Median.Buffer[(int32_T)rtu_1][(int32_T)localDW->Median.Index[(int32_T)rtu_1]]
            = rtu_Input[(int32_T)rtu_1];
          }
          _rlt1 = localDW->Median.Index[(int32_T)rtu_1];
          if(_rlt1 < rtu_LENBUFMED - 1) {
            localDW->Median.Index[(int32_T)rtu_1] = (uint8_T)(_rlt1 + 1);
          } else {
            localDW->Median.Index[(int32_T)rtu_1] = 0U;
          }
          for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
            localDW->Median.VettSort[(int32_T)idx1] =
              localDW->Median.Buffer[(int32_T)rtu_1][(int32_T)idx1];
          }
          idx1 = 0U;
          idx_median = (uint8_T)(rtu_LENBUFMED / 2);
          while(idx1 <= idx_median) {
            idx2 = (uint8_T)(idx1 + 1);
            while(idx2 < rtu_LENBUFMED) {
              _rlt3 = localDW->Median.VettSort[(int32_T)idx1];
              _rlt2 = localDW->Median.VettSort[(int32_T)idx2];
              if(_rlt2 < _rlt3) {
                temp = _rlt2;
                localDW->Median.VettSort[(int32_T)idx2] = _rlt3;
                localDW->Median.VettSort[(int32_T)idx1] = temp;
                idx2++;
              } else {
                idx2++;
              }
            }
            idx1++;
          }
          localB->median = localDW->Median.VettSort[(int32_T)idx_median];
          localDW->Median.is_c3_IonLambda = (uint8_T)IonLambda_IN_BUF;
        }
        break;
       case IonLambda_IN_RESET:
        if(rtu_FiltLamEnable != 0) {
          for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
            for(idx2 = 0U; idx2 < rtu_N_CYLINDER; idx2++) {
              localDW->Median.Buffer[(int32_T)idx2][(int32_T)idx1] =
                rtu_Input[(int32_T)rtu_1];
              localDW->Median.Index[(int32_T)idx2] = 0U;
            }
          }
          localB->median = rtu_Input[(int32_T)rtu_1];
          localDW->Median.is_c3_IonLambda = (uint8_T)IonLambda_IN_BUF;
        }
        break;
       default:
        for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
          for(idx2 = 0U; idx2 < rtu_N_CYLINDER; idx2++) {
            localDW->Median.Buffer[(int32_T)idx2][(int32_T)idx1] =
              rtu_Input[(int32_T)rtu_1];
            localDW->Median.Index[(int32_T)idx2] = 0U;
          }
        }
        localB->median = rtu_Input[(int32_T)rtu_1];
        localDW->Median.is_c3_IonLambda = (uint8_T)IonLambda_IN_BUF;
        break;
      }
    }
  }
}

/* Initial conditions for atomic system: '<S4>/Median1' */
void IonLambda_Median1_Init(rtB_IonLambda_Median1 *localB,
 rtDW_IonLambda_Median1 *localDW)
{

  /* Initialize code for chart: '<S4>/Median1' */

  {
    int32_T sf_i0;
    int32_T sf_i1;
    localDW->Median1.is_active_c2_IonLambda = 0U;
    localDW->Median1.is_c2_IonLambda = 0U;
    for(sf_i0 = 0; sf_i0 < 8; sf_i0++) {
      for(sf_i1 = 0; sf_i1 < 7; sf_i1++) {
        localDW->Median1.Buffer[sf_i0][sf_i1] = 0U;
      }
      localDW->Median1.Index[sf_i0] = 0U;
    }
    for(sf_i1 = 0; sf_i1 < 7; sf_i1++) {
      localDW->Median1.VettSort[sf_i1] = 0U;
    }
    localB->median_e = 0U;
  }
}

/* Output and update for atomic system: '<S4>/Median1' */
void IonLambda_Median1(const uint16_T rtu_0[8], uint8_T rtu_1, uint8_T
 rtu_FiltLamEnable, uint8_T rtu_LENBUFMED, uint8_T rtu_N_CYLINDER, uint8_T
 rtu_FiltLamFreeze, rtB_IonLambda_Median1 *localB, rtDW_IonLambda_Median1
 *localDW)
{

  /* Stateflow: '<S4>/Median1' */

  {
    uint8_T idx1;
    uint8_T idx2;
    uint8_T _rlt1;
    uint8_T idx_median;
    uint16_T _rlt3;
    uint16_T _rlt2;
    uint16_T temp;
    if(localDW->Median1.is_active_c2_IonLambda == 0) {
      localDW->Median1.is_active_c2_IonLambda = 1U;
      for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
        for(idx2 = 0U; idx2 < rtu_N_CYLINDER; idx2++) {
          localDW->Median1.Buffer[(int32_T)idx2][(int32_T)idx1] =
            rtu_0[(int32_T)rtu_1];
          localDW->Median1.Index[(int32_T)idx2] = 0U;
        }
      }
      localB->median_e = rtu_0[(int32_T)rtu_1];
      localDW->Median1.is_c2_IonLambda = (uint8_T)IonLambda_IN_BUF_f;
    } else {
      switch(localDW->Median1.is_c2_IonLambda) {
       case IonLambda_IN_BUF_f:
        if(!(rtu_FiltLamEnable != 0)) {
          localDW->Median1.is_c2_IonLambda = (uint8_T)IonLambda_IN_RESET_g;
        } else {
          if(!(rtu_FiltLamFreeze != 0)) {
            localDW->Median1.Buffer[(int32_T)rtu_1][(int32_T)localDW->Median1.Index[(int32_T)rtu_1]]
            = rtu_0[(int32_T)rtu_1];
          }
          _rlt1 = localDW->Median1.Index[(int32_T)rtu_1];
          if(_rlt1 < rtu_LENBUFMED - 1) {
            localDW->Median1.Index[(int32_T)rtu_1] = (uint8_T)(_rlt1 + 1);
          } else {
            localDW->Median1.Index[(int32_T)rtu_1] = 0U;
          }
          for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
            localDW->Median1.VettSort[(int32_T)idx1] =
              localDW->Median1.Buffer[(int32_T)rtu_1][(int32_T)idx1];
          }
          idx1 = 0U;
          idx_median = (uint8_T)(rtu_LENBUFMED / 2);
          while(idx1 <= idx_median) {
            idx2 = (uint8_T)(idx1 + 1);
            while(idx2 < rtu_LENBUFMED) {
              _rlt3 = localDW->Median1.VettSort[(int32_T)idx1];
              _rlt2 = localDW->Median1.VettSort[(int32_T)idx2];
              if(_rlt2 < _rlt3) {
                temp = _rlt2;
                localDW->Median1.VettSort[(int32_T)idx2] = _rlt3;
                localDW->Median1.VettSort[(int32_T)idx1] = temp;
                idx2++;
              } else {
                idx2++;
              }
            }
            idx1++;
          }
          localB->median_e = localDW->Median1.VettSort[(int32_T)idx_median];
          localDW->Median1.is_c2_IonLambda = (uint8_T)IonLambda_IN_BUF_f;
        }
        break;
       case IonLambda_IN_RESET_g:
        if(rtu_FiltLamEnable != 0) {
          for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
            for(idx2 = 0U; idx2 < rtu_N_CYLINDER; idx2++) {
              localDW->Median1.Buffer[(int32_T)idx2][(int32_T)idx1] =
                rtu_0[(int32_T)rtu_1];
              localDW->Median1.Index[(int32_T)idx2] = 0U;
            }
          }
          localB->median_e = rtu_0[(int32_T)rtu_1];
          localDW->Median1.is_c2_IonLambda = (uint8_T)IonLambda_IN_BUF_f;
        }
        break;
       default:
        for(idx1 = 0U; idx1 < rtu_LENBUFMED; idx1++) {
          for(idx2 = 0U; idx2 < rtu_N_CYLINDER; idx2++) {
            localDW->Median1.Buffer[(int32_T)idx2][(int32_T)idx1] =
              rtu_0[(int32_T)rtu_1];
            localDW->Median1.Index[(int32_T)idx2] = 0U;
          }
        }
        localB->median_e = rtu_0[(int32_T)rtu_1];
        localDW->Median1.is_c2_IonLambda = (uint8_T)IonLambda_IN_BUF_f;
        break;
      }
    }
  }
}

/* Output and update for function-call system: '<S16>/Update_TbFFSAdj' */
void IonLambda_Update_TbFFSAdj(void)
{
  /* local block i/o variables*/
  uint16_T rtb_LookUp_U16_U16;
  uint16_T rtb_DataStoreRead1_d[42];
  uint16_T rtb_Selector_c;
  uint16_T rtb_Assignment[42];
  int16_T rtb_ffsgainselfadj_table;

  {
    int32_T i1;
    for(i1 = 0; i1 < 42; i1++) {

      /* DataStoreRead: '<S148>/Data Store Read1' */
      rtb_DataStoreRead1_d[i1] = TbBkFFSAdj[i1];
    }
  }

  /* Selector: '<S148>/Selector' */
  IonLambda_DWork.Selector_DWORK1_g = (int32_T)(IonLambda_B.indr);

  IonLambda_DWork.Selector_DWORK2_p = (int32_T)(IonLambda_B.indc);

  rtb_Selector_c =
    rtb_DataStoreRead1_d[(7*IonLambda_DWork.Selector_DWORK2_p)+IonLambda_DWork.Selector_DWORK1_g];

  /* S-Function (LookUp_U16_U16): '<S154>/LookUp_U16_U16' incorporates:
   *  Constant: '<S148>/VTFFSGNAD'
   *  Constant: '<S148>/BKFFSDSQ'
   *  Constant: '<S148>/BKFFSDSQ_dim'
   */

  LookUp_U16_U16( &rtb_LookUp_U16_U16, &(VTFFSGNAD[0]), IonLambda_B.dsq,
   &(BKFFSDSQ[0]), BKFFSDSQ_dim);

  {
    int32_T i1;

    /* Product: '<S148>/Product' incorporates:
     *  Sum: '<S148>/Add'
     *  Product: '<S148>/Product4'
     *  Constant: '<S148>/GNFFSCORRINDAD'
     */
    rtb_ffsgainselfadj_table = (int16_T)((((int16_T)FFSGainSelfAdj -
      (int16_T)rtb_Selector_c) * GNFFSCORRINDAD >> 10) * rtb_LookUp_U16_U16 >>
      10);

    /* MinMax: '<S155>/Enforce upper limit' incorporates:
     *  Constant: '<S148>/FFSCORRADMIN'
     */
    rtb_ffsgainselfadj_table = rt_MAX(FFSCORRADMIN, rtb_ffsgainselfadj_table);

    /* Assignment: '<S148>/Assignment' incorporates:
     *  MinMax: '<S155>/Enforce lower limit'
     *  MinMax: '<S155>/Enforce upper limit'
     *  Sum: '<S148>/Add1'
     *  DataTypeConversion: '<S148>/Data Type Conversion'
     *  Constant: '<S148>/FFSCORRADMAX'
     */
    for(i1 = 0; i1 < 42; i1++) {
      rtb_Assignment[i1] = rtb_DataStoreRead1_d[i1];
    }
    rtb_Assignment[IonLambda_B.indc * 7 + IonLambda_B.indr] =
      (uint16_T)((int16_T)rtb_Selector_c + rt_MIN(rtb_ffsgainselfadj_table,
      FFSCORRADMAX));
    for(i1 = 0; i1 < 42; i1++) {

      /* DataStoreWrite: '<S148>/Data Store Write1' */
      TbBkFFSAdj[i1] = rtb_Assignment[i1];
    }
  }
}

/* Initial conditions for function-call system: '<S1>/IonLambda_TDC' */
void IonLambda_IonLambda_TDC_Init(void)
{

  /*atomic Subsystem Block: <S4>/Median1 */
  IonLambda_Median1_Init(&IonLambda_B.sf_Median1, &IonLambda_DWork.sf_Median1);

  /* Initialize code for chart: '<S77>/Chart' */
  IonLambda_B.i_out = 0U;

  /* InitializeConditions for Memory: '<S93>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput = rtcP_pooled6;

  /* InitializeConditions for Memory: '<S93>/Memory' */
  IonLambda_DWork.Memory_PreviousInput = rtcP_pooled6;

  /* InitializeConditions for Memory: '<S92>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput_b = rtcP_pooled6;

  /* InitializeConditions for Memory: '<S92>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_o = rtcP_pooled6;

  /* InitializeConditions for UnitDelay: '<S10>/Unit Delay' */
  IonLambda_DWork.UnitDelay_DSTATE = rtcP_UnitDelay_X0;

  /*atomic Subsystem Block: <S4>/Filtering1 */
  IonLambda_Filtering_Init(&IonLambda_DWork.Filtering1);

  /*atomic Subsystem Block: <S4>/Median */
  IonLambda_Median_Init(&IonLambda_B.sf_Median, &IonLambda_DWork.sf_Median);

  /*atomic Subsystem Block: <S4>/Filtering */
  IonLambda_Filtering_Init(&IonLambda_DWork.Filtering);

  /* InitializeConditions for Memory: '<S85>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput_g = rtcP_pooled6;

  /* InitializeConditions for Memory: '<S85>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_f = rtcP_pooled6;

  /* InitializeConditions for Memory: '<S86>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput_l = rtcP_pooled6;

  /* InitializeConditions for Memory: '<S86>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_i = rtcP_pooled6;

  /*atomic Subsystem Block: <S4>/LambdaMeter */
  IonLambda_LambdaMeter_Init();
}

/* Disable for function-call system: '<S1>/IonLambda_TDC' */
void IonLambda_IonLambda_TDC_Disable(void)
{

  /* enable Subsystem Block: <S4>/SelfAdjust */

  /* Disable for enable system: '<S4>/SelfAdjust' */
  /* DisableFcn of enable SubSystem Block: <S4>/SelfAdjust */
  if (IonLambda_DWork.SelfAdjust_MODE[0] == (int_T) SUBSYS_ENABLED) {

    /* (Virtual) Outport Block: <S16>/FlgForceOL */

    IonLambda_B.FlgForceOL_b = rtcP_pooled12;

    /* (Virtual) Outport Block: <S16>/LamObjSelfAdj */

    LamObjSelfAdj = rtcP_pooled5;

    /* (Virtual) Outport Block: <S16>/FFSGainSelfAdj */

    FFSGainSelfAdj = rtcP_pooled5;

    /* (Virtual) Outport Block: <S16>/EnSelfAdj */

    EnSelfAdj = rtcP_pooled12;

    /* (Virtual) Outport Block: <S16>/SelfAdjState */

    SelfAdjState = rtcP_pooled12;

    IonLambda_DWork.SelfAdjust_MODE[0] = (int_T) SUBSYS_DISABLED;
  }

  /* (Virtual) Outport Block: <S4>/LamObjSelfAdj */

  LamObjSelfAdj = rtcP_pooled5;

  /* (Virtual) Outport Block: <S4>/FFSGainSelfAdj */

  FFSGainSelfAdj = rtcP_pooled5;

  /* (Virtual) Outport Block: <S4>/EnSelfAdj */

  EnSelfAdj = rtcP_pooled12;

  /* (Virtual) Outport Block: <S4>/SelfAdjState */

  SelfAdjState = rtcP_pooled12;
}

/* Start for function-call system: '<S1>/IonLambda_TDC' */
void IonLambda_IonLambda_TDC_Start(void)
{

  /*enable Subsystem Block: <S4>/SelfAdjust */

  /* Start for enable system: '<S4>/SelfAdjust' */

  /* Initial conditions for enable system: '<S4>/SelfAdjust' */

  /*atomic Subsystem Block: <S16>/SelfAdjFSM */

  /* Initialize code for chart: '<S16>/SelfAdjFSM' */
  IonLambda_DWork.SelfAdjFSM.is_active_c6_IonLambda = 0U;
  IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda = 0U;
  IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
  IonLambda_DWork.SelfAdjFSM.cnttaskthr = 0U;
  IonLambda_B.FlgForceOL_b = 0U;
  SelfAdjState = 0U;
  AccumDelta = 0;
  AccumLam = 0U;
  AccumFFS = 0U;
  AccumBkFFS = 0U;

  if (rtmIsFirstInitCond(IonLambda_M)) {

    /* Initialize code for chart: '<S16>/Calc_indices_ad' */
    IonLambda_DWork.Calc_indices_ad.rtc2_b = 0U;
    IonLambda_B.indr = 0U;
    IonLambda_B.indc = 0U;
    IonLambda_B.dsq = 0U;
  }

  /* virtual outports code */

  /* (Virtual) Outport Block: <S16>/FlgForceOL */

  IonLambda_B.FlgForceOL_b = rtcP_pooled12;

  /* (Virtual) Outport Block: <S16>/LamObjSelfAdj */

  LamObjSelfAdj = rtcP_pooled5;

  /* (Virtual) Outport Block: <S16>/FFSGainSelfAdj */

  FFSGainSelfAdj = rtcP_pooled5;

  /* (Virtual) Outport Block: <S16>/SelfAdjState */

  SelfAdjState = rtcP_pooled12;

  /*atomic Subsystem Block: <S4>/LambdaMeter */
  IonLambda_LambdaMeter_Start();

  /* virtual outports code */

  /* (Virtual) Outport Block: <S4>/LamObjSelfAdj */

  LamObjSelfAdj = rtcP_pooled5;

  /* (Virtual) Outport Block: <S4>/FFSGainSelfAdj */

  FFSGainSelfAdj = rtcP_pooled5;

  /* (Virtual) Outport Block: <S4>/SelfAdjState */

  SelfAdjState = rtcP_pooled12;
}

/* Output and update for function-call system: '<S1>/IonLambda_TDC' */
void IonLambda_IonLambda_TDC(void)
{
  /* local block i/o variables*/
  uint32_T rtb_DataStoreRead10[8];
  uint32_T rtb_DataStoreRead13[8];
  uint32_T rtb_DataStoreRead6_b[8];
  uint32_T rtb_DataStoreRead9[8];
  uint32_T rtb_DataStoreRead11[8];
  uint32_T rtb_DataStoreRead12[8];
  uint32_T rtb_DataStoreRead7[8];
  uint32_T rtb_DataStoreRead8[8];
  int32_T rtb_DeltaLamCL_Convert;
  uint32_T rtb_FFS_Convert;
  uint32_T rtb_LamObj_Convert;
  int32_T rtb_Mul1;
  int16_T rtb_RateLimiter_S16;
  uint16_T rtb_TmpHiddenBufferAtColdCorrec;
  uint16_T rtb_TmpHiddenBufferAtDataTyp_i;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_a;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_m;
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_SteadyStateDetect_o3_n;
  uint16_T rtb_SteadyStateDetect_o4_n;
  uint16_T rtb_DataTypeConversion2[8];
  uint16_T rtb_TmpHiddenBufferAtColdCorr_i;
  uint16_T rtb_TmpHiddenBufferAtDataTyp_d;
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_SigStab_o3_k;
  uint16_T rtb_SigStab_o4_i;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_k;
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_n;
  uint16_T rtb_LookUp_IR_U16_b;
  uint16_T rtb_Look2D_IR_U16_l;
  uint16_T rtb_Look2D_IR_U16_d;
  uint16_T rtb_Look2D_IR_U16_lz;
  uint16_T rtb_Look2D_IR_U16_e;
  uint16_T rtb_Look2D_IR_U16_m;
  uint16_T rtb_Look2D_IR_U16_n;
  uint16_T rtb_Look2D_IR_U16_j;
  uint16_T rtb_Look2D_IR_U16_g;
  uint16_T rtb_Look2D_IR_U16_ji;
  uint16_T rtb_Look2D_IR_U16_dd;
  uint16_T rtb_Look2D_IR_U16_a;
  uint16_T rtb_Look2D_IR_U16_k;
  uint16_T rtb_Look2D_IR_U16_p;
  uint16_T rtb_Look2D_IR_U16_lj;
  uint16_T rtb_Look2D_IR_U16_d1;
  uint16_T rtb_Look2D_IR_U16_o;
  uint16_T rtb_Look2D_IR_U16_i;
  uint16_T rtb_Look2D_IR_U16_c;
  uint16_T rtb_LookUp_IR_U16_e;
  uint16_T rtb_DataTypeConversion4_g;
  uint16_T rtb_u16_temp257;
  uint16_T rtb_u16_temp258;
  uint16_T rtb_u16_temp259;
  uint16_T rtb_u16_temp260;
  uint16_T rtb_u16_temp261;
  uint16_T rtb_u16_temp262;
  uint16_T rtb_DataStoreRead1_l[42];
  int16_T rtb_Selector_Outold1_j;
  uint16_T rtb_DSAOut2;
  int16_T rtb_UnitDelay;
  int16_T rtb_i16_temp274;
  uint8_T rtb_DataStoreRead4;
  uint8_T rtb_DataStoreRead16;
  uint8_T rtb_DataStoreRead17;
  uint8_T rtb_DataStoreRead5;
  uint8_T rtb_UnitDelay_l;
  uint8_T rtb_UnitDelay_l5;
  uint8_T rtb_DataStoreRead2_o;
  uint8_T rtb_DataStoreRead14;
  uint8_T rtb_DataStoreRead15_l;
  uint8_T rtb_DataStoreRead3;
  uint8_T rtb_UnitDelay_i;
  uint8_T rtb_UnitDelay1_e;
  uint8_T rtb_DataStoreRead1_it;
  uint8_T rtb_DataStoreRead[42];
  uint8_T rtb_FiltLamEnable_i;
  uint8_T rtb_FiltLamFreeze_i;
  uint8_T rtb_Selector_a;
  uint8_T rtb_Selector_Outold1_o;
  boolean_T rtb_LogicalOperator_n;

  {
    int32_T i1;

    /* Output and update for atomic system: '<S4>/EnFilter' */

    /* Logic: '<S8>/Logical Operator' incorporates:
     *  Inport: '<Root>/CutoffFlg'
     */
    rtb_FiltLamEnable_i = (uint8_T)!CutoffFlg;

    /* Selector: '<S8>/Selector_Outold1' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     *  Inport: '<Root>/StMisf'
     */
    rtb_Selector_Outold1_o = StMisf[(int32_T)IonAbsTdc];

    /* Logic: '<S8>/Logical Operator2' incorporates:
     *  RelationalOperator: '<S8>/Relational Operator2'
     *  RelationalOperator: '<S8>/Relational Operator1'
     *  Constant: '<S8>/MISF'
     *  Selector: '<S8>/Selector_Outold2'
     *  Constant: '<S8>/PAR_MISF'
     *  S-Function (sfix_bitop): '<S8>/Bitwise Operator'
     *  Inport: '<Root>/CutoffFlg'
     *  Inport: '<Root>/IonAbsTdc'
     *  Inport: '<Root>/IonErrorStatus'
     */
    rtb_FiltLamFreeze_i = CutoffFlg || (IonErrorStatus[(int32_T)IonAbsTdc] &
      IONERRORSTATUS_MASK) || (rtb_Selector_Outold1_o == PAR_MISF) ||
      (rtb_Selector_Outold1_o == MISF);

    /* DataStoreWrite: '<S4>/Data Store Write1' */
    FiltLamEnable = rtb_FiltLamEnable_i;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreRead: '<S4>/Data Store Read10' */
      rtb_DataStoreRead10[i1] = Avg_HR_IntIon[i1];

      /* DataStoreRead: '<S4>/Data Store Read11' */
      rtb_DataStoreRead11[i1] = In_x_N0_IntIon[i1];

      /* DataStoreRead: '<S4>/Data Store Read12' */
      rtb_DataStoreRead12[i1] = InSum_IntIon[i1];

      /* DataStoreRead: '<S4>/Data Store Read13' */
      rtb_DataStoreRead13[i1] = Avg_HR_old_IntIon[i1];
    }

    /* DataStoreRead: '<S4>/Data Store Read4' */
    rtb_DataStoreRead4 = FiltLamEnable;

    /* DataStoreRead: '<S4>/Data Store Read16' */
    rtb_DataStoreRead16 = FiltLamEnable;

    /* DataStoreRead: '<S4>/Data Store Read17' */
    rtb_DataStoreRead17 = FiltLamFreeze;
  }

  /* Stateflow: '<S4>/Median1' incorporates:
   *   Inport: '<Root>/ChInt'
   *   Inport: '<Root>/IonAbsTdc'
   *  Constant: '<S4>/LENBUFMED'
   *  Constant: '<S4>/N_CYLINDER'
   */
  IonLambda_Median1(&ChInt[0], IonAbsTdc, rtb_DataStoreRead16, LENBUFMED,
   N_CYLINDER, rtb_DataStoreRead17, &IonLambda_B.sf_Median1,
   &IonLambda_DWork.sf_Median1);

  /* SignalConversion: '<S4>/TmpHiddenBufferAtColdCorrection1Inport4' */
  rtb_TmpHiddenBufferAtColdCorrec = IonLambda_B.sf_Median1.median_e;

  /* SubSystem: '<S4>/ColdCorrection1' incorporates:
   *   Inport: '<Root>/TWaterCrk'
   *   Inport: '<Root>/CntTdcCrk'
   *  Constant: '<S4>/TBGAININTCRK'
   */
  IonLambda_ColdCorrection1(TWaterCrk, CntTdcCrk, &(TBGAININTCRK[0]),
   rtb_TmpHiddenBufferAtColdCorrec, &IonLambda_B.ColdCorrection1);

  {
    int32_T i1;

    /* SignalConversion: '<S4>/TmpHiddenBufferAtData Type Conversion10Inport1' */
    rtb_TmpHiddenBufferAtDataTyp_i = IonLambda_B.ColdCorrection1.MulCorrect_b;
    for(i1 = 0; i1 < 42; i1++) {

      /* DataStoreRead: '<S4>/Data Store Read1' */
      rtb_DataStoreRead1_l[i1] = TbBkFFSAdj[i1];
    }

    /* DataTypeConversion: '<S84>/Data Type Conversion4' incorporates:
     *  Inport: '<Root>/Rpm'
     */
    rtb_u16_temp257 = Rpm;
  }

  /* S-Function (PreLookUpIdSearch_U16): '<S84>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S76>/BKRPMADFFS'
   *  Constant: '<S76>/BKRPMADFFS_dim'
   */

  PreLookUpIdSearch_U16( &IDZoneFFSRpm, &rtb_PreLookUpIdSearch_U16_o2_k,
   rtb_u16_temp257, &(BKRPMADFFS[0]), BKRPMADFFS_dim);

  /* S-Function (PreLookUpIdSearch_U16): '<S83>/PreLookUpIdSearch_U16' incorporates:
   *  DataTypeConversion: '<S83>/Data Type Conversion4'
   *  Constant: '<S76>/BKLOADADFFS'
   *  Constant: '<S76>/BKLOADADFFS_dim'
   */

  PreLookUpIdSearch_U16( &IDZoneFFSLoad, &rtb_u16_temp257, Load,
   &(BKLOADADFFS[0]), BKLOADADFFS_dim);

  /* S-Function (Look2D_IR_U16): '<S82>/Look2D_IR_U16' incorporates:
   *  Constant: '<S76>/BKRPMADFFS_dim1'
   *  Constant: '<S76>/BKLOADADFFS_dim1'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16, rtb_DataStoreRead1_l, IDZoneFFSRpm,
   rtb_PreLookUpIdSearch_U16_o2_k, BKRPMADFFS_dim, IDZoneFFSLoad,
   rtb_u16_temp257, BKLOADADFFS_dim);

  /* DataTypeConversion: '<S87>/Conversion' */
  BkFFSAdj = rtb_Look2D_IR_U16;

  /* Output and update for atomic system: '<S4>/FFS_IntIon_Talbes' */

  /* Output and update for atomic system: '<S9>/Index_Ratio_Calc' */

  /* DataTypeConversion: '<S54>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/RpmF'
   */
  rtb_DataTypeConversion4_g = RpmF;

  /* S-Function (PreLookUpIdSearch_U16): '<S54>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S27>/BKRPMLAMOD'
   *  Constant: '<S27>/BKRPMLAMOD_dim'
   */

  PreLookUpIdSearch_U16( &IonLambda_B.PreLookUpIdSearch_U16_o1,
   &IonLambda_B.PreLookUpIdSearch_U16_o2, rtb_DataTypeConversion4_g,
   &(BKRPMLAMOD[0]), BKRPMLAMOD_dim);

  /* S-Function (PreLookUpIdSearch_U16): '<S53>/PreLookUpIdSearch_U16' incorporates:
   *  DataTypeConversion: '<S53>/Data Type Conversion4'
   *  Constant: '<S27>/BKLOADLAMOD_dim'
   */

  PreLookUpIdSearch_U16( &IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   &IonLambda_B.PreLookUpIdSearch_U16_o2_m, Load, &(BKLOADLAMOD[0]),
   BKLOADLAMOD_dim);

  /* S-Function (LookUp_IR_U16): '<S38>/LookUp_IR_U16' incorporates:
   *  Constant: '<S25>/VTDSAGAINFFS'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   */

  LookUp_IR_U16( &rtb_LookUp_IR_U16_b, &(VTDSAGAINFFS[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim);

  {
    int32_T rtb_s32_tmp;
    uint32_T rtb_u32_tmp;
    uint16_T rtb_u16_tmp;

    /* Selector: '<S9>/Selector_Outold1' incorporates:
     *  Inport: '<Root>/IonAbsTdc'
     *  Inport: '<Root>/DSAOut'
     */
    rtb_Selector_Outold1_j = DSAOut[(int32_T)IonAbsTdc];

    /* Product: '<S9>/Product2' */
    rtb_s32_tmp = rtb_Selector_Outold1_j * rtb_Selector_Outold1_j >> 4;
    if(rtb_s32_tmp <= 0) {
      rtb_u16_tmp = 0U;
    } else if(rtb_s32_tmp > 65535) {
      rtb_u16_tmp = MAX_uint16_T;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_s32_tmp;
    }
    rtb_DSAOut2 = rtb_u16_tmp;

    /* Sum: '<S25>/Add' incorporates:
     *  Product: '<S25>/Product1'
     */
    rtb_u32_tmp = (uint32_T)rtb_LookUp_IR_U16_b * (uint32_T)rtb_DSAOut2 >> 4;
    if(rtb_u32_tmp > 65535U) {
      rtb_u16_tmp = MAX_uint16_T;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_u32_tmp;
    }
    DSAGainFFS = (uint16_T)(65535U - (uint32_T)rtb_u16_tmp);
  }

  /* S-Function (Look2D_IR_U16): '<S29>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_1'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_l, &(TBBK_FFS_1[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S30>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_2'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_d, &(TBBK_FFS_2[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S31>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_4'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_lz, &(TBBK_FFS_4[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S32>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_5'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_e, &(TBBK_FFS_5[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S33>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_6'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_m, &(TBBK_FFS_6[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S34>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_7'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_n, &(TBBK_FFS_7[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S35>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_8'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_j, &(TBBK_FFS_8[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S36>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_9'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_g, &(TBBK_FFS_9[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S37>/Look2D_IR_U16' incorporates:
   *  Constant: '<S25>/TBBK_FFS_3'
   *  Constant: '<S25>/BKRPMLAMOD_dim'
   *  Constant: '<S25>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_ji, &(TBBK_FFS_3[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  {
    uint16_T rtb_u16_U[9];
    int32_T i1;

    /* Product: '<S25>/Product' */
    rtb_u16_U[0] = rtb_Look2D_IR_U16_l;
    rtb_u16_U[1] = rtb_Look2D_IR_U16_d;
    rtb_u16_U[2] = rtb_Look2D_IR_U16_ji;
    rtb_u16_U[3] = rtb_Look2D_IR_U16_lz;
    rtb_u16_U[4] = rtb_Look2D_IR_U16_e;
    rtb_u16_U[5] = rtb_Look2D_IR_U16_m;
    rtb_u16_U[6] = rtb_Look2D_IR_U16_n;
    rtb_u16_U[7] = rtb_Look2D_IR_U16_j;
    rtb_u16_U[8] = rtb_Look2D_IR_U16_g;
    for(i1 = 0; i1 < 9; i1++) {
      IonLambda_B.Product[i1] = (uint16_T)((uint32_T)rtb_u16_U[i1] *
        (uint32_T)DSAGainFFS >> 16);

      /* Product: '<S25>/Product2' */
      BkFFS[i1] = (uint16_T)((uint32_T)IonLambda_B.Product[i1] *
        (uint32_T)BkFFSAdj >> 10);
    }
  }

  /* S-Function (Look2D_IR_U16): '<S55>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_1'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_dd, &(TBBK_IntIon_1[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S56>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_2'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_a, &(TBBK_IntIon_2[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S63>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_3'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_k, &(TBBK_IntIon_3[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S57>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_4'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_p, &(TBBK_IntIon_4[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S58>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_5'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_lj, &(TBBK_IntIon_5[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S59>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_6'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_d1, &(TBBK_IntIon_6[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S60>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_7'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_o, &(TBBK_IntIon_7[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S61>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_8'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_i, &(TBBK_IntIon_8[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (Look2D_IR_U16): '<S62>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBBK_IntIon_9'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   *  Constant: '<S28>/BKLOADLAMOD_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16_c, &(TBBK_IntIon_9[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim, IonLambda_B.PreLookUpIdSearch_U16_o1_c,
   IonLambda_B.PreLookUpIdSearch_U16_o2_m, BKLOADLAMOD_dim);

  /* S-Function (LookUp_IR_U16): '<S64>/LookUp_IR_U16' incorporates:
   *  Constant: '<S28>/VTDSAGAININT'
   *  Constant: '<S28>/BKRPMLAMOD_dim'
   */

  LookUp_IR_U16( &rtb_LookUp_IR_U16_e, &(VTDSAGAININT[0]),
   IonLambda_B.PreLookUpIdSearch_U16_o1, IonLambda_B.PreLookUpIdSearch_U16_o2,
   BKRPMLAMOD_dim);

  {
    uint16_T rtb_u16_U[9];
    int32_T i1;
    uint32_T rtb_u32_tmp;
    uint16_T rtb_u16_tmp;

    /* Sum: '<S28>/Add' incorporates:
     *  Product: '<S28>/Product1'
     */
    rtb_u32_tmp = (uint32_T)rtb_LookUp_IR_U16_e * (uint32_T)rtb_DSAOut2 >> 4;
    if(rtb_u32_tmp > 65535U) {
      rtb_u16_tmp = MAX_uint16_T;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_u32_tmp;
    }
    DSAGainINT = (uint16_T)(65535U - (uint32_T)rtb_u16_tmp);

    /* Product: '<S28>/Product' */
    rtb_u16_U[0] = rtb_Look2D_IR_U16_dd;
    rtb_u16_U[1] = rtb_Look2D_IR_U16_a;
    rtb_u16_U[2] = rtb_Look2D_IR_U16_k;
    rtb_u16_U[3] = rtb_Look2D_IR_U16_p;
    rtb_u16_U[4] = rtb_Look2D_IR_U16_lj;
    rtb_u16_U[5] = rtb_Look2D_IR_U16_d1;
    rtb_u16_U[6] = rtb_Look2D_IR_U16_o;
    rtb_u16_U[7] = rtb_Look2D_IR_U16_i;
    rtb_u16_U[8] = rtb_Look2D_IR_U16_c;
    for(i1 = 0; i1 < 9; i1++) {
      BkIntIon[i1] = (uint16_T)((uint32_T)rtb_u16_U[i1] * (uint32_T)DSAGainINT
        >> 16);
    }

    if((!rtb_FiltLamEnable_i)) {

      /* Output and update for enable system: '<S9>/IC_Calc' */

      /* S-Function (LookUp_U16_U16): '<S49>/LookUp_U16_U16' incorporates:
       *  Constant: '<S26>/LAM_1'
       *  Constant: '<S26>/BKLAM'
       */

      LookUp_U16_U16( &rtb_u16_temp258, &BkIntIon[0], rtcP_pooled5, &(BKLAM[0]),
       LEN_BKLAM);

      /* DataTypeConversion: '<S51>/Conversion' */
      IonLambda_B.Conversion_c = rtb_u16_temp258;


      /* S-Function (LookUp_U16_U16): '<S50>/LookUp_U16_U16' incorporates:
       *  Constant: '<S26>/LAM_1'
       *  Constant: '<S26>/BKLAM'
       */

      LookUp_U16_U16( &rtb_u16_temp259, &BkFFS[0], rtcP_pooled5, &(BKLAM[0]),
       LEN_BKLAM);

      /* DataTypeConversion: '<S52>/Conversion' */
      IonLambda_B.Conversion_k = rtb_u16_temp259;
    }
  }

  /* DataStoreRead: '<S4>/Data Store Read5' */
  rtb_DataStoreRead5 = FiltLamFreeze;

  /* DataTypeConversion: '<S90>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_u16_temp260 = Rpm;

  /* S-Function (PreLookUpIdSearch_U16): '<S90>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S77>/BKRPMLAMFIL'
   *  Constant: '<S77>/BKRPMLAMFIL_dim'
   */

  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_a, &rtb_u16_temp262,
   rtb_u16_temp260, &(BKRPMLAMFIL[0]), BKRPMLAMFIL_dim);

  /* S-Function (PreLookUpIdSearch_U16): '<S89>/PreLookUpIdSearch_U16' incorporates:
   *  Sum: '<S77>/Add4'
   *  Constant: '<S77>/BKRPMLAMFIL'
   *  Constant: '<S77>/BKRPMLAMFIL_dim'
   */

  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_m, &rtb_u16_temp262,
   ((uint16_T)((uint32_T)Rpm + (uint32_T)RPMHYSTFN)), &(BKRPMLAMFIL[0]),
   BKRPMLAMFIL_dim);

  {
    uint32_T rtb_u32_tmp;

    /* Stateflow: '<S77>/Chart' */

    if((rtb_PreLookUpIdSearch_U16_o1_a == rtb_PreLookUpIdSearch_U16_o1_m) ||
     ((rtb_PreLookUpIdSearch_U16_o1_a != IonLambda_B.i_out) &&
      (rtb_PreLookUpIdSearch_U16_o1_m != IonLambda_B.i_out))) {
      IonLambda_B.i_out = rtb_PreLookUpIdSearch_U16_o1_a;
    }

    /* Product: '<S81>/Product' incorporates:
     *  Constant: '<S81>/RPM_2_PERC'
     *  Inport: '<Root>/Rpm'
     */
    rtb_u32_tmp = (uint32_T)RPM_2_PERC;
   rtb_u16_temp262 = (uint16_T)(rtb_u32_tmp == (uint32_T)0 ? MAX_uint32_T :
      (uint32_T)(((uint32_T)Rpm << 7U) / rtb_u32_tmp));

    /* UnitDelay: '<S81>/Unit Delay' */
    rtb_UnitDelay_l = IonLambda_DWork.UnitDelay_DSTATE_f;

    /* Memory: '<S93>/Memory1' */
    rtb_u16_temp260 = IonLambda_DWork.Memory1_PreviousInput;

    /* Memory: '<S93>/Memory' */
    rtb_u16_temp261 = IonLambda_DWork.Memory_PreviousInput;
  }

  /* S-Function (SteadyStateDetect): '<S93>/SteadyStateDetect' incorporates:
   *  Constant: '<S81>/THRSTABRPMLAMTR'
   *  Constant: '<S81>/TDCSTABRPMLAMTR'
   */

  SteadyStateDetect( &StabRpmLamTr, &sstab_rpm_lam, &rtb_SteadyStateDetect_o3,
   &rtb_SteadyStateDetect_o4, rtb_u16_temp262, IonLambda_ConstB.Conversion4_k,
   THRSTABRPMLAMTR, TDCSTABRPMLAMTR, rtb_UnitDelay_l, rtb_u16_temp260,
   rtb_u16_temp261);

  /* UnitDelay: '<S80>/Unit Delay' */
  rtb_UnitDelay_l5 = IonLambda_DWork.UnitDelay_DSTATE_j;

  /* Memory: '<S92>/Memory1' */
  rtb_u16_temp261 = IonLambda_DWork.Memory1_PreviousInput_b;

  /* Memory: '<S92>/Memory' */
  rtb_u16_temp262 = IonLambda_DWork.Memory_PreviousInput_o;

  /* S-Function (SteadyStateDetect): '<S92>/SteadyStateDetect' incorporates:
   *  DataTypeConversion: '<S92>/Conversion2'
   *  Constant: '<S80>/THRSTABLOADLAMTR'
   *  Constant: '<S80>/TDCSTABLOADLAMTR'
   */

  SteadyStateDetect( &StabLoadLamTr, &sstab_load_lam,
   &rtb_SteadyStateDetect_o3_n, &rtb_SteadyStateDetect_o4_n, Load,
   IonLambda_ConstB.Conversion4_l, THRSTABLOADLAMTR, TDCSTABLOADLAMTR,
   rtb_UnitDelay_l5, rtb_u16_temp261, rtb_u16_temp262);

  /* Logic: '<S10>/Logical Operator' */
  rtb_LogicalOperator_n = StabRpmLamTr && StabLoadLamTr;

  /* Switch: '<S77>/Switch' incorporates:
   *  Selector: '<S77>/Selector'
   *  Constant: '<S77>/FREQNORMLAMTR'
   *  Constant: '<S77>/VTFREQNORMCUT'
   */
  if(rtb_LogicalOperator_n) {
    rtb_i16_temp274 = VTFREQNORMCUT[IonLambda_B.i_out];
  } else {
    rtb_i16_temp274 = FREQNORMLAMTR;
  }

  /* UnitDelay: '<S10>/Unit Delay' */
  rtb_UnitDelay = IonLambda_DWork.UnitDelay_DSTATE;

  /* S-Function (RateLimiter_S16): '<S79>/RateLimiter_S16' incorporates:
   *  Constant: '<S10>/FREQRATEMIN'
   *  Constant: '<S10>/FREQRATEMAX'
   */

  RateLimiter_S16( &rtb_RateLimiter_S16, rtb_i16_temp274, rtb_UnitDelay,
   FREQRATEMIN, FREQRATEMAX);

  /* DataTypeConversion: '<S91>/Conversion' */
  FreqNorm = rtb_RateLimiter_S16;

  /* RelationalOperator: '<S75>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S75>/Delay Input1'
   *
   * Block description for '<S75>/Delay Input1':
   *  
   *  Store in Global RAM
   */
  FiltParReset = FreqNorm != IonLambda_DWork.DelayInput1_DSTATE;

  /* Product: '<S78>/Mul1' */
  rtb_Mul1 = FreqNorm * FreqNorm;

  /* DataTypeConversion: '<S78>/Data Type Conversion1' incorporates:
   *  Product: '<S78>/Mul3'
   *  Product: '<S78>/Mul2'
   *  Sum: '<S78>/Add2'
   *  Sum: '<S78>/Add3'
   *  Constant: '<S78>/D1LAM_POLY_P1'
   *  Constant: '<S78>/D1LAM_POLY_P2'
   *  Constant: '<S78>/D1LAM_POLY_P3'
   */
  D1LamFil = (int16_T)(((FreqNorm * D1LAM_POLY_P2 +
    (mul_s32_s32_s32_sr12(rtb_Mul1, (int32_T)D1LAM_POLY_P1) << 2U)) +
    D1LAM_POLY_P3) >> 8);

  /* DataTypeConversion: '<S78>/Data Type Conversion2' incorporates:
   *  Product: '<S78>/Mul5'
   *  Product: '<S78>/Mul4'
   *  Sum: '<S78>/Add4'
   *  Sum: '<S78>/Add5'
   *  Constant: '<S78>/D2LAM_POLY_P1'
   *  Constant: '<S78>/D2LAM_POLY_P2'
   *  Constant: '<S78>/D2LAM_POLY_P3'
   */
  D2LamFil = (int16_T)(((FreqNorm * D2LAM_POLY_P2 +
    (mul_s32_s32_s32_sr12(rtb_Mul1, (int32_T)D2LAM_POLY_P1) << 2U)) +
    D2LAM_POLY_P3) >> 8);

  /* DataTypeConversion: '<S78>/Data Type Conversion' incorporates:
   *  Sum: '<S78>/Add'
   *  Sum: '<S78>/Add1'
   *  Product: '<S78>/Divide'
   */
  N0LamFil = (uint16_T)(((D1LamFil + D2LamFil) + 16384) << 4U);

  /* Selector: '<S77>/Selector2' incorporates:
   *  Constant: '<S77>/VTENFILBUTTER'
   */
  EnFilButter = VTENFILBUTTER[IonLambda_B.i_out];

  /* SubSystem: '<S4>/Filtering1' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */
  IonLambda_Filtering(N0LamFil, FiltParReset, rtb_DataStoreRead11, IonAbsTdc,
   rtb_DataStoreRead12, rtb_DataStoreRead10, rtb_DataStoreRead13,
   rtb_DataStoreRead4, EnFilButter, IonLambda_B.Conversion_c,
   rtb_TmpHiddenBufferAtDataTyp_i, rtb_DataStoreRead5, D1LamFil, D2LamFil,
   &IonLambda_B.Filtering1, &IonLambda_DWork.Filtering1);

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreWrite: '<S4>/Data Store Write10' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write10Inport1'
       */
      In_x_N0_IntIon[i1] = IonLambda_B.Filtering1.Assignment1[i1];

      /* DataStoreWrite: '<S4>/Data Store Write11' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write11Inport1'
       */
      InSum_IntIon[i1] = IonLambda_B.Filtering1.Assignment2[i1];

      /* DataStoreWrite: '<S4>/Data Store Write12' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write12Inport1'
       */
      Avg_HR_old_IntIon[i1] = IonLambda_B.Filtering1.Assignment4[i1];

      /* DataStoreRead: '<S4>/Data Store Read6' */
      rtb_DataStoreRead6_b[i1] = Avg_HR_FFS[i1];

      /* DataStoreRead: '<S4>/Data Store Read7' */
      rtb_DataStoreRead7[i1] = In_x_N0_FFS[i1];

      /* DataStoreRead: '<S4>/Data Store Read8' */
      rtb_DataStoreRead8[i1] = InSum_FFS[i1];

      /* DataStoreRead: '<S4>/Data Store Read9' */
      rtb_DataStoreRead9[i1] = Avg_HR_old_FFS[i1];

      /* DataTypeConversion: '<S4>/Data Type Conversion2' incorporates:
       *  Inport: '<Root>/FFS'
       */
      rtb_DataTypeConversion2[i1] = FFS[i1];
    }

    /* DataStoreWrite: '<S4>/Data Store Write2' incorporates:
     *  Constant: '<S8>/MISF'
     *  Selector: '<S8>/Selector_Outold2'
     *  RelationalOperator: '<S8>/Relational Operator2'
     *  RelationalOperator: '<S8>/Relational Operator1'
     *  Constant: '<S8>/PAR_MISF'
     *  Logic: '<S8>/Logical Operator2'
     *  S-Function (sfix_bitop): '<S8>/Bitwise Operator'
     *  Inport: '<Root>/CutoffFlg'
     *  Inport: '<Root>/IonAbsTdc'
     *  Inport: '<Root>/IonErrorStatus'
     */
    FiltLamFreeze = rtb_FiltLamFreeze_i;

    /* DataStoreRead: '<S4>/Data Store Read2' */
    rtb_DataStoreRead2_o = FiltLamEnable;

    /* DataStoreRead: '<S4>/Data Store Read14' */
    rtb_DataStoreRead14 = FiltLamEnable;

    /* DataStoreRead: '<S4>/Data Store Read15' */
    rtb_DataStoreRead15_l = FiltLamFreeze;
  }

  /* Stateflow: '<S4>/Median' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   *  Constant: '<S4>/LENBUFMED'
   *  Constant: '<S4>/N_CYLINDER'
   */
  IonLambda_Median(rtb_DataTypeConversion2, IonAbsTdc, rtb_DataStoreRead14,
   LENBUFMED, N_CYLINDER, rtb_DataStoreRead15_l, &IonLambda_B.sf_Median,
   &IonLambda_DWork.sf_Median);

  /* SignalConversion: '<S4>/TmpHiddenBufferAtColdCorrectionInport4' */
  rtb_TmpHiddenBufferAtColdCorr_i = IonLambda_B.sf_Median.median;

  /* SubSystem: '<S4>/ColdCorrection' incorporates:
   *   Inport: '<Root>/TWaterCrk'
   *   Inport: '<Root>/CntTdcCrk'
   *  Constant: '<S4>/TBGAINFFSCRK'
   */
  IonLambda_ColdCorrection(TWaterCrk, CntTdcCrk, &(TBGAINFFSCRK[0]),
   rtb_TmpHiddenBufferAtColdCorr_i, &IonLambda_B.ColdCorrection);

  /* SignalConversion: '<S4>/TmpHiddenBufferAtData Type Conversion9Inport1' */
  rtb_TmpHiddenBufferAtDataTyp_d = IonLambda_B.ColdCorrection.MulCorrect;

  /* DataStoreRead: '<S4>/Data Store Read3' */
  rtb_DataStoreRead3 = FiltLamFreeze;

  /* SubSystem: '<S4>/Filtering' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */
  IonLambda_Filtering(N0LamFil, FiltParReset, rtb_DataStoreRead7, IonAbsTdc,
   rtb_DataStoreRead8, rtb_DataStoreRead6_b, rtb_DataStoreRead9,
   rtb_DataStoreRead2_o, EnFilButter, IonLambda_B.Conversion_k,
   rtb_TmpHiddenBufferAtDataTyp_d, rtb_DataStoreRead3, D1LamFil, D2LamFil,
   &IonLambda_B.Filtering, &IonLambda_DWork.Filtering);

  {
    uint32_T rtb_u32_tmp;

    /* DataTypeConversion: '<S4>/Data Type Conversion16' incorporates:
     *  SignalConversion: '<S4>/TmpHiddenBufferAtData Type Conversion15Inport1'
     *  DataTypeConversion: '<S4>/Data Type Conversion15'
     */
    AvgFFSCyl = (uint16_T)(IonLambda_B.Filtering.Switch7 >> 16);

    /* DataTypeConversion: '<S76>/Data Type Conversion' */
    RtZoneFFSRpm = (uint16_T)(rtb_PreLookUpIdSearch_U16_o2_k >> 6);

    /* Product: '<S76>/Product' incorporates:
     *  Constant: '<S76>/RPM_2_PERC'
     *  Inport: '<Root>/Rpm'
     */
    rtb_u32_tmp = (uint32_T)RPM_2_PERC;
   rtb_u16_temp261 = (uint16_T)(rtb_u32_tmp == (uint32_T)0 ? MAX_uint32_T :
      (uint32_T)(((uint32_T)Rpm << 7U) / rtb_u32_tmp));

    /* UnitDelay: '<S76>/Unit Delay' */
    rtb_UnitDelay_i = IonLambda_DWork.UnitDelay_DSTATE_h;

    /* Memory: '<S85>/Memory1' */
    rtb_u16_temp262 = IonLambda_DWork.Memory1_PreviousInput_g;

    /* Memory: '<S85>/Memory' */
    rtb_u16_temp260 = IonLambda_DWork.Memory_PreviousInput_f;
  }

  /* S-Function (SigStab): '<S85>/SigStab' incorporates:
   *  Constant: '<S76>/Constant'
   *  Constant: '<S76>/THRSTABRPMADFFS'
   *  Constant: '<S76>/TDCRLSTABADFFS'
   */

  SigStab( &FStabRpmSelfAdj, &sstab_rpm_ffs, &rtb_SigStab_o3, &rtb_SigStab_o4,
   rtb_u16_temp261, rtcP_pooled12, THRSTABRPMADFFS, TDCRLSTABADFFS,
   rtb_UnitDelay_i, rtb_u16_temp262, rtb_u16_temp260);

  /* DataTypeConversion: '<S76>/Data Type Conversion1' */
  RtZoneFFSLoad = (uint16_T)(rtb_u16_temp257 >> 6);

  /* UnitDelay: '<S76>/Unit Delay1' */
  rtb_UnitDelay1_e = IonLambda_DWork.UnitDelay1_DSTATE_f;

  /* Memory: '<S86>/Memory1' */
  rtb_u16_temp261 = IonLambda_DWork.Memory1_PreviousInput_l;

  /* Memory: '<S86>/Memory' */
  rtb_u16_temp262 = IonLambda_DWork.Memory_PreviousInput_i;

  /* S-Function (SigStab): '<S86>/SigStab' incorporates:
   *  DataTypeConversion: '<S86>/Conversion2'
   *  Constant: '<S76>/Constant1'
   *  Constant: '<S76>/THRSTABLOADADFFS'
   *  Constant: '<S76>/TDCRLSTABADFFS'
   */

  SigStab( &FStabLoadSelfAdj, &sstab_load_ffs, &rtb_SigStab_o3_k,
   &rtb_SigStab_o4_i, Load, rtcP_pooled12, THRSTABLOADADFFS, TDCRLSTABADFFS,
   rtb_UnitDelay1_e, rtb_u16_temp261, rtb_u16_temp262);

  /* SubSystem: '<S4>/SelfAdjust' incorporates:
   *  Constant: '<S4>/ENFFSADJ'
   */

  /* Output and update for enable system: '<S4>/SelfAdjust' */
  /* switch on enable state */
  switch (rt_EnableState((boolean_T)(ENFFSADJ > 0U),
    IonLambda_DWork.SelfAdjust_MODE[0])) {
   case SUBSYS_BECOMING_ENABLED:
    /* protect against calling initialize function twice at startup */
    if (!(rtmIsFirstInitCond(IonLambda_M))) {

      /* Initial conditions for enable system: '<S4>/SelfAdjust' */

      /*atomic Subsystem Block: <S16>/SelfAdjFSM */

      /* Initialize code for chart: '<S16>/SelfAdjFSM' */
      IonLambda_DWork.SelfAdjFSM.is_active_c6_IonLambda = 0U;
      IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda = 0U;
      IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
      IonLambda_DWork.SelfAdjFSM.cnttaskthr = 0U;
      IonLambda_B.FlgForceOL_b = 0U;
      SelfAdjState = 0U;
      AccumDelta = 0;
      AccumLam = 0U;
      AccumFFS = 0U;
      AccumBkFFS = 0U;

      if (rtmIsFirstInitCond(IonLambda_M)) {

        /* Initialize code for chart: '<S16>/Calc_indices_ad' */
        IonLambda_DWork.Calc_indices_ad.rtc2_b = 0U;
        IonLambda_B.indr = 0U;
        IonLambda_B.indc = 0U;
        IonLambda_B.dsq = 0U;
      }
    }

    /* Enable for enable system: '<S4>/SelfAdjust' */

    /* Enable code for chart: '<S16>/SelfAdjFSM' */

    /* protect against calling initialize function twice at startup */
    if (!(rtmIsFirstInitCond(IonLambda_M))) {

      /* Initialize code for chart: '<S16>/Calc_indices_ad' */
      IonLambda_DWork.Calc_indices_ad.rtc2_b = 0U;
      IonLambda_B.indr = 0U;
      IonLambda_B.indc = 0U;
      IonLambda_B.dsq = 0U;
    }

    IonLambda_DWork.SelfAdjust_MODE[0] = (int_T) SUBSYS_ENABLED;
    /*FALLTHROUGH*/
   case SUBSYS_ENABLED:

    {
      int32_T i1;

      /* DataTypeConversion: '<S16>/DeltaLamCL_Convert' incorporates:
       *  Inport: '<Root>/DeltaLamCL'
       */
      rtb_DeltaLamCL_Convert = (int32_T)DeltaLamCL;

      /* DataTypeConversion: '<S16>/FFS_Convert' */
      rtb_FFS_Convert = (uint32_T)AvgFFSCyl;

      /* DataTypeConversion: '<S16>/LamObj_Convert' incorporates:
       *  Inport: '<Root>/LamObj'
       */
      rtb_LamObj_Convert = (uint32_T)LamObj;

      /* DataStoreRead: '<S16>/Data Store Read1' */
      rtb_DataStoreRead1_it = FlgForceOL;
      for(i1 = 0; i1 < 42; i1++) {

        /* DataStoreRead: '<S16>/Data Store Read' */
        rtb_DataStoreRead[i1] = TbFlgSelfAdjOK[i1];
      }

      /* Output and update for atomic system: '<S16>/EnSelfAdj_Calc' */

      /* Selector: '<S144>/Selector' */
      IonLambda_DWork.Selector_DWORK1 = (int32_T)(IDZoneFFSRpm);

      IonLambda_DWork.Selector_DWORK2 = (int32_T)(IDZoneFFSLoad);

      rtb_Selector_a =
        rtb_DataStoreRead[(7*IonLambda_DWork.Selector_DWORK2)+IonLambda_DWork.Selector_DWORK1];

      /* S-Function (LookUp_IR_U16): '<S150>/LookUp_IR_U16' incorporates:
       *  Constant: '<S144>/VTSELFADJLOADMAX'
       *  Constant: '<S144>/BKRPMADFFS_dim'
       */

      LookUp_IR_U16( &rtb_LookUp_IR_U16, &(VTSELFADJLOADMAX[0]), IDZoneFFSRpm,
       RtZoneFFSRpm, BKRPMADFFS_dim);

      /* S-Function (LookUp_IR_U16): '<S151>/LookUp_IR_U16' incorporates:
       *  Constant: '<S144>/VTSELFADJLOADMIN'
       *  Constant: '<S144>/BKRPMADFFS_dim'
       */

      LookUp_IR_U16( &rtb_LookUp_IR_U16_n, &(VTSELFADJLOADMIN[0]), IDZoneFFSRpm,
       RtZoneFFSRpm, BKRPMADFFS_dim);

      /* Logic: '<S144>/Logical Operator' incorporates:
       *  RelationalOperator: '<S144>/Relational Operator4'
       *  Constant: '<S144>/LAMFFSMAX'
       *  Constant: '<S144>/LAMFFSMIN'
       *  Logic: '<S144>/Logical Operator1'
       *  Logic: '<S144>/Logical Operator2'
       *  Logic: '<S144>/Logical Operator3'
       *  Constant: '<S144>/NADJMAX'
       *  RelationalOperator: '<S144>/Relational Operator'
       *  RelationalOperator: '<S144>/Relational Operator1'
       *  RelationalOperator: '<S144>/Relational Operator2'
       *  RelationalOperator: '<S144>/Relational Operator3'
       *  Inport: '<Root>/Load'
       *  Inport: '<Root>/LamObj'
       *  Inport: '<Root>/ClosedLoopFlg'
       */
      EnSelfAdj = FStabRpmSelfAdj && FStabLoadSelfAdj && (StAFCtrl == AF_CL ||
        rtb_DataStoreRead1_it) && (rtb_DataStoreRead1_it || (LAMFFSMAX >=
        LamObj)) && (rtb_DataStoreRead1_it || (LamObj >= LAMFFSMIN)) &&
        (rtb_Selector_a < NADJMAX) && (Load < rtb_LookUp_IR_U16) && (Load >
        rtb_LookUp_IR_U16_n);

      /* Selector: '<S16>/Selector' incorporates:
       *  Constant: '<S16>/VTNTASKOL'
       */
      IonLambda_B.Selector = VTNTASKOL[IDZoneFFSRpm];
    }

    {

      /* Stateflow: '<S16>/SelfAdjFSM' incorporates:
       *  Constant: '<S16>/NTASKMON'
       *  Constant: '<S16>/NTASKDISOL'
       *  Constant: '<S16>/NTASKPOSTOL'
       *  Constant: '<S16>/NTASKWAITMON'
       *  Constant: '<S16>/NTASKDISWAITOL'
       *  Constant: '<S16>/NTASKWAITOL'
       */

      if(IonLambda_DWork.SelfAdjFSM.is_active_c6_IonLambda == 0) {
        IonLambda_DWork.SelfAdjFSM.is_active_c6_IonLambda = 1U;
        SelfAdjState = (uint8_T)NO_FORCED;
        IonLambda_B.FlgForceOL_b = 0U;
        IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
          (uint8_T)IonLambda_IN_NO_FORCED;
      } else {
        switch(IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda) {
         case IonLambda_IN_MONITORING:
          if(!(EnSelfAdj != 0)) {
            SelfAdjState = (uint8_T)NO_FORCED;
            IonLambda_B.FlgForceOL_b = 0U;
            IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
              (uint8_T)IonLambda_IN_NO_FORCED;
          } else {
            IonLambda_DWork.SelfAdjFSM.cntTask++;
            if(IonLambda_DWork.SelfAdjFSM.cntTask > NTASKMON) {
              IonLambda_DWork.SelfAdjFSM.cntTask = 0U;

              /* Output and update for function-call system: '<S16>/OLParamCalc' */
              {
                /* local block i/o variables*/
                uint16_T rtb_Divide1;

                {
                  uint32_T rtb_u32_tmp;
                  int32_T rtb_s32_tmp;

                  /* Product: '<S146>/Divide1' incorporates:
                   *  Constant: '<S16>/NTASKMON'
                   */
                  rtb_u32_tmp = (uint32_T)NTASKMON;
                  rtb_Divide1 = (uint16_T)(rtb_u32_tmp == (uint32_T)0 ?
                    MAX_uint32_T : (uint32_T)(AccumLam / rtb_u32_tmp));

                  /* DataStoreWrite: '<S146>/Data Store Write' */
                  LamObjMem = rtb_Divide1;

                  /* Product: '<S146>/Divide3' incorporates:
                   *  Constant: '<S16>/NTASKMON'
                   */
                  DeltaLamCLMem = div_s16s32_floor(AccumDelta,
                   (int32_T)NTASKMON);

                  /* Sum: '<S146>/Add' incorporates:
                   *  DataTypeConversion: '<S146>/LamSelf_Convert'
                   *  Product: '<S146>/Divide2'
                   *  Product: '<S146>/Product'
                   *  Product: '<S146>/Product1'
                   *  Constant: '<S146>/GAINDELTACORR'
                   *  Constant: '<S146>/LAMSELFADJ'
                   */
                  rtb_u32_tmp = (uint32_T)rtb_Divide1;
                  rtb_s32_tmp = (uint16_T)(rtb_u32_tmp == (uint32_T)0 ?
                    MAX_uint32_T : (uint32_T)(((uint32_T)LAMSELFADJ << 12U) /
                    rtb_u32_tmp)) * DeltaLamCLMem;
                  rtb_s32_tmp = (((rtb_s32_tmp < 0) && (rtb_s32_tmp & 65535)) +
                    (rtb_s32_tmp >> 16)) * GAINDELTACORR;
                  LamObjSelfAdj = (uint16_T)((uint32_T)(((rtb_s32_tmp < 0) &&
                    (rtb_s32_tmp & 1023)) + (rtb_s32_tmp >> 10)) +
                    (uint32_T)LAMSELFADJ);
                }
              }

              IonLambda_B.FlgForceOL_b = 1U;
              SelfAdjState = (uint8_T)WAIT_OL_FORCED;
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_WAIT_OL_FORCED;
            } else {
              AccumDelta = AccumDelta + rtb_DeltaLamCL_Convert;
              AccumLam = AccumLam + rtb_LamObj_Convert;
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_MONITORING;
            }
          }
          goto sf_label_47_0_1;
          break;
         case IonLambda_IN_NO_FORCED:
          if(EnSelfAdj != 0) {
            IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
            SelfAdjState = (uint8_T)WAIT_MONITORING;
            IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
              (uint8_T)IonLambda_IN_WAIT_MONITORING;
          }
          goto sf_label_47_0_1;
          break;
         case IonLambda_IN_OL_FORCED:
          if(!(EnSelfAdj != 0)) {
            IonLambda_DWork.SelfAdjFSM.cnttaskthr = NTASKDISOL;
          } else {
            IonLambda_DWork.SelfAdjFSM.cntTask++;
            if(IonLambda_DWork.SelfAdjFSM.cntTask > IonLambda_B.Selector) {

              /* Output and update for function-call system: '<S16>/FFSGainCalc' */
              {
                /* local block i/o variables*/
                uint8_T rtb_DataStoreRead2[42];
                uint8_T rtb_Selector_d;
                uint8_T rtb_Assignment_c[42];

                {
                  int32_T i1;
                  for(i1 = 0; i1 < 42; i1++) {

                    /* DataStoreRead: '<S145>/Data Store Read2' */
                    rtb_DataStoreRead2[i1] = TbFlgSelfAdjOK[i1];
                  }
                }

                /* Selector: '<S145>/Selector' */
                IonLambda_DWork.Selector_DWORK1_m = (int32_T)(IDZoneFFSRpm);

                IonLambda_DWork.Selector_DWORK2_e = (int32_T)(IDZoneFFSLoad);

                rtb_Selector_d =
                  rtb_DataStoreRead2[(7*IonLambda_DWork.Selector_DWORK2_e)+IonLambda_DWork.Selector_DWORK1_m];

                {
                  int32_T i1;
                  uint32_T rtb_u32_tmp;
                  uint32_T rtb_u32_tmp_b;

                  /* Assignment: '<S145>/Assignment' incorporates:
                   *  Sum: '<S145>/Add'
                   */
                  for(i1 = 0; i1 < 42; i1++) {
                    rtb_Assignment_c[i1] = rtb_DataStoreRead2[i1];
                  }
                  rtb_Assignment_c[IDZoneFFSLoad * 7 + IDZoneFFSRpm] =
                    (uint8_T)((uint32_T)rtb_Selector_d + 1U);
                  for(i1 = 0; i1 < 42; i1++) {

                    /* DataStoreWrite: '<S145>/Data Store Write' */
                    TbFlgSelfAdjOK[i1] = rtb_Assignment_c[i1];
                  }

                  /* Product: '<S145>/Divide3' */
                  rtb_u32_tmp = (uint32_T)IonLambda_B.Selector;
                  BkFFSRef = (uint16_T)(rtb_u32_tmp == (uint32_T)0 ?
                    MAX_uint32_T : (uint32_T)(AccumBkFFS / rtb_u32_tmp));

                  /* Product: '<S145>/Divide2' incorporates:
                   *  Product: '<S145>/Divide1'
                   */
                  rtb_u32_tmp = (uint32_T)IonLambda_B.Selector;
                  rtb_u32_tmp_b = (uint32_T)BkFFSRef;
                  FFSGainSelfAdj = (uint16_T)(rtb_u32_tmp_b == (uint32_T)0 ?
                    MAX_uint32_T : (uint32_T)(((uint32_T)(rtb_u32_tmp ==
                    (uint32_T)0 ? MAX_uint32_T : (uint32_T)(AccumFFS /
                    rtb_u32_tmp)) << 10U) / rtb_u32_tmp_b));
                }
              }

              /* Stateflow: '<S16>/Calc_indices_ad' incorporates:
               *  Constant: '<S16>/BKLOADADFFS_dim'
               *  Constant: '<S16>/BKRPMADFFS_dim'
               */

              {
                uint8_T k;
                uint16_T rtr2;
                uint16_T rtc2_a;
                uint8_T b_previousEvent;
                b_previousEvent = _sfEvent_IonLambda_;
                _sfEvent_IonLambda_ = IonLambda_event_fc_FFSGainStore;
                if(_sfEvent_IonLambda_ == IonLambda_event_fc_FFSGainStore) {
                  k = 1U;
                  IonLambda_B.indr = (uint8_T)IDZoneFFSRpm;
                  IonLambda_B.indc = (uint8_T)IDZoneFFSLoad;
                  rtr2 = (uint16_T)((uint32_T)RtZoneFFSRpm *
                    (uint32_T)RtZoneFFSRpm >> 8);
                  rtc2_a = (uint16_T)((uint32_T)RtZoneFFSLoad *
                    (uint32_T)RtZoneFFSLoad >> 8);
                  IonLambda_B.dsq = (uint16_T)(rtr2 + rtc2_a);

                  IonLambda_Update_TbFFSAdj();

                  if(IDZoneFFSLoad < BKLOADADFFS_dim) {
                    k = 2U;
                    /*  indr=   r; */
                    IonLambda_B.indc = (uint8_T)(IDZoneFFSLoad + 1);
                    /*  rtr2:=  RtZoneFFSRpm*RtZoneFFSRpm.
                       rtc2:= (1-RtZoneFFSLoad)*(1-RtZoneFFSLoad); */
                    IonLambda_DWork.Calc_indices_ad.rtc2_b =
                      (uint16_T)((uint32_T)(4096 + rtc2_a) -
                      ((uint32_T)(RtZoneFFSLoad << 1) << 2));
                    IonLambda_B.dsq = (uint16_T)(rtr2 +
                      IonLambda_DWork.Calc_indices_ad.rtc2_b);

                    IonLambda_Update_TbFFSAdj();
                  }
                  if(IDZoneFFSRpm < BKRPMADFFS_dim) {
                    k++;
                    IonLambda_B.indr = (uint8_T)(IDZoneFFSRpm + 1);
                    IonLambda_B.indc = (uint8_T)IDZoneFFSLoad;
                    /*  rtr2:= (1-RtZoneFFSRpm)*(1-RtZoneFFSRpm); */
                    rtr2 = (uint16_T)((uint32_T)(4096 + rtr2) -
                      ((uint32_T)(RtZoneFFSRpm << 1) << 2));
                    IonLambda_B.dsq = (uint16_T)(rtr2 + rtc2_a);

                    IonLambda_Update_TbFFSAdj();

                    if(k == 3) {
                      /* indr=   r+1; */
                      IonLambda_B.indc = (uint8_T)(IDZoneFFSLoad + 1);
                      /*  rtr2:= (1-RtZoneFFSRpm)*(1-RtZoneFFSRpm); */
                      IonLambda_B.dsq = (uint16_T)(rtr2 +
                        IonLambda_DWork.Calc_indices_ad.rtc2_b);

                      IonLambda_Update_TbFFSAdj();
                    }
                  }
                }
                _sfEvent_IonLambda_ = b_previousEvent;
              }

              IonLambda_DWork.SelfAdjFSM.cnttaskthr = NTASKPOSTOL;
            } else {
              AccumFFS = AccumFFS + rtb_FFS_Convert;

              /* Output and update for function-call system: '<S16>/BkFFSNCOutCalc' */
              {
                /* local block i/o variables*/
                uint16_T rtb_PreLookUpIdSearch_U16_o2_ma;
                uint16_T rtb_u16_temp180;

                /* S-Function (PreLookUpIdSearch_U16): '<S149>/PreLookUpIdSearch_U16' incorporates:
                 *  Constant: '<S142>/LAMSELFADJ'
                 *  Constant: '<S142>/BKLAM'
                 */

                PreLookUpIdSearch_U16( &rtb_u16_temp180,
                 &rtb_PreLookUpIdSearch_U16_o2_ma, LAMSELFADJ, &(BKLAM[0]),
                 rtcP_pooled15);

                /* DataTypeConversion: '<S142>/BkFFSNCOut_Convert' incorporates:
                 *  Selector: '<S142>/Selector'
                 */
                BkFFSNCOut = (uint32_T)IonLambda_B.Product[rtcP_pooled15];
              }

              AccumBkFFS = AccumBkFFS + BkFFSNCOut;
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_OL_FORCED;
              goto sf_label_47_0_1;
            }
          }
          break;
         case IonLambda_IN_POST_OL_FORCED:
          IonLambda_DWork.SelfAdjFSM.cntTask++;
          if(IonLambda_DWork.SelfAdjFSM.cntTask >
           IonLambda_DWork.SelfAdjFSM.cnttaskthr) {
            IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
            SelfAdjState = (uint8_T)NO_FORCED;
            IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
              (uint8_T)IonLambda_IN_NO_FORCED;
          } else {
            IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
              (uint8_T)IonLambda_IN_POST_OL_FORCED;
          }
          goto sf_label_47_0_1;
          break;
         case IonLambda_IN_WAIT_MONITORING:
          if(!(EnSelfAdj != 0)) {
            SelfAdjState = (uint8_T)NO_FORCED;
            IonLambda_B.FlgForceOL_b = 0U;
            IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
              (uint8_T)IonLambda_IN_NO_FORCED;
          } else {
            IonLambda_DWork.SelfAdjFSM.cntTask++;
            if(IonLambda_DWork.SelfAdjFSM.cntTask > NTASKWAITMON) {
              IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
              AccumDelta = 0;
              AccumLam = 0U;
              SelfAdjState = (uint8_T)MONITORING;
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_MONITORING;
            } else {
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_WAIT_MONITORING;
            }
          }
          goto sf_label_47_0_1;
          break;
         case IonLambda_IN_WAIT_OL_FORCED:
          if(!(EnSelfAdj != 0)) {
            IonLambda_DWork.SelfAdjFSM.cnttaskthr = NTASKDISWAITOL;
            IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
            SelfAdjState = (uint8_T)POST_OL_FORCED;
            IonLambda_B.FlgForceOL_b = 0U;
            IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
              (uint8_T)IonLambda_IN_POST_OL_FORCED;
          } else {
            IonLambda_DWork.SelfAdjFSM.cntTask++;
            if(IonLambda_DWork.SelfAdjFSM.cntTask > NTASKWAITOL) {
              IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
              AccumFFS = 0U;
              AccumBkFFS = 0U;
              SelfAdjState = (uint8_T)OL_FORCED;
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_OL_FORCED;
            } else {
              IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
                (uint8_T)IonLambda_IN_WAIT_OL_FORCED;
            }
          }
          goto sf_label_47_0_1;
          break;
         default:
          SelfAdjState = (uint8_T)NO_FORCED;
          IonLambda_B.FlgForceOL_b = 0U;
          IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
            (uint8_T)IonLambda_IN_NO_FORCED;
          goto sf_label_47_0_1;
          break;
        }
        IonLambda_DWork.SelfAdjFSM.cntTask = 0U;
        SelfAdjState = (uint8_T)POST_OL_FORCED;
        IonLambda_B.FlgForceOL_b = 0U;
        IonLambda_DWork.SelfAdjFSM.is_c6_IonLambda =
          (uint8_T)IonLambda_IN_POST_OL_FORCED;
        sf_label_47_0_1:;
      }
    }

    break;
   case SUBSYS_BECOMING_DISABLED:

    /* Disable for enable system: '<S4>/SelfAdjust' */
    /* DisableFcn of enable SubSystem Block: <S4>/SelfAdjust */
    if (IonLambda_DWork.SelfAdjust_MODE[0] == (int_T) SUBSYS_ENABLED) {

      /* (Virtual) Outport Block: <S16>/FlgForceOL */

      IonLambda_B.FlgForceOL_b = rtcP_pooled12;

      /* (Virtual) Outport Block: <S16>/LamObjSelfAdj */

      LamObjSelfAdj = rtcP_pooled5;

      /* (Virtual) Outport Block: <S16>/FFSGainSelfAdj */

      FFSGainSelfAdj = rtcP_pooled5;

      /* (Virtual) Outport Block: <S16>/EnSelfAdj */

      EnSelfAdj = rtcP_pooled12;

      /* (Virtual) Outport Block: <S16>/SelfAdjState */

      SelfAdjState = rtcP_pooled12;

      IonLambda_DWork.SelfAdjust_MODE[0] = (int_T) SUBSYS_DISABLED;
    }

    /*FALLTHROUGH*/
   case SUBSYS_DISABLED:
    /* no action required while system is disabled */
    break;
   default:
    break;
  }

  /* DataStoreWrite: '<S4>/Data Store Write3' */
  FlgForceOL = IonLambda_B.FlgForceOL_b;

  /* DataTypeConversion: '<S4>/Data Type Conversion1' incorporates:
   *  SignalConversion: '<S4>/TmpHiddenBufferAtData Type Conversion1Inport1'
   */
  AvgIntIonCyl = (uint16_T)(IonLambda_B.Filtering1.Switch7 >> 16);

  /* DataStoreRead: '<S4>/Data Store Read18' */
  IonLambda_B.DataStoreRead18 = FiltLamEnable;

  /* DataStoreRead: '<S4>/Data Store Read19' */
  IonLambda_B.DataStoreRead19 = FiltLamFreeze;

  /* Selector: '<S77>/Selector3' incorporates:
   *  Constant: '<S77>/VTENFILLAMEST'
   */
  EnFilLamEst = VTENFILLAMEST[IonLambda_B.i_out];

  /* Switch: '<S77>/Switch1' incorporates:
   *  Selector: '<S77>/Selector1'
   *  Selector: '<S77>/Selector4'
   *  Constant: '<S77>/VTKFILTLAMEST'
   *  Constant: '<S77>/VTKFILTLAMESTTR'
   */
  if(rtb_LogicalOperator_n) {
    KFiltLamEstAvg = VTKFILTLAMEST[IonLambda_B.i_out];
  } else {
    KFiltLamEstAvg = VTKFILTLAMESTTR[IonLambda_B.i_out];
  }

  /* SubSystem: '<S4>/LambdaMeter' */
  IonLambda_LambdaMeter();

  {
    int32_T i1;

    /* DataStoreWrite: '<S4>/Data Store Write4' */
    LamEstAvg = IonLambda_B.LamEstAvg_f;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreWrite: '<S4>/Data Store Write5' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write5Inport1'
       */
      Avg_HR_FFS[i1] = IonLambda_B.Filtering.Assignment3[i1];

      /* DataStoreWrite: '<S4>/Data Store Write6' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write6Inport1'
       */
      In_x_N0_FFS[i1] = IonLambda_B.Filtering.Assignment1[i1];

      /* DataStoreWrite: '<S4>/Data Store Write7' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write7Inport1'
       */
      InSum_FFS[i1] = IonLambda_B.Filtering.Assignment2[i1];

      /* DataStoreWrite: '<S4>/Data Store Write8' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write8Inport1'
       */
      Avg_HR_old_FFS[i1] = IonLambda_B.Filtering.Assignment4[i1];

      /* DataStoreWrite: '<S4>/Data Store Write9' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write9Inport1'
       */
      Avg_HR_IntIon[i1] = IonLambda_B.Filtering1.Assignment3[i1];

      /* DataTypeConversion: '<S4>/Data Type Conversion3' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write5Inport1'
       *  DataTypeConversion: '<S4>/Data Type Conversion14'
       */
      AvgFFS[i1] = (uint16_T)(IonLambda_B.Filtering.Assignment3[i1] >> 16);

      /* DataTypeConversion: '<S4>/Data Type Conversion4' incorporates:
       *  SignalConversion: '<S4>/TmpHiddenBufferAtData Store Write9Inport1'
       */
      AvgIntIon[i1] = (uint16_T)(IonLambda_B.Filtering1.Assignment3[i1] >> 16);
    }

    /* DataTypeConversion: '<S4>/Data Type Conversion10' */
    IntIonMedianCorr = rtb_TmpHiddenBufferAtDataTyp_i;

    /* DataTypeConversion: '<S4>/Data Type Conversion6' */
    FFSMedian = rtb_TmpHiddenBufferAtColdCorr_i;

    /* DataTypeConversion: '<S4>/Data Type Conversion7' */
    IntIonMedian = rtb_TmpHiddenBufferAtColdCorrec;

    /* DataTypeConversion: '<S4>/Data Type Conversion9' */
    FFSMedianCorr = rtb_TmpHiddenBufferAtDataTyp_d;
  }

  /* Update for UnitDelay: '<S81>/Unit Delay' */
  IonLambda_DWork.UnitDelay_DSTATE_f = sstab_rpm_lam;

  /* Update for Memory: '<S93>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput = rtb_SteadyStateDetect_o3;

  /* Update for Memory: '<S93>/Memory' */
  IonLambda_DWork.Memory_PreviousInput = rtb_SteadyStateDetect_o4;

  /* Update for UnitDelay: '<S80>/Unit Delay' */
  IonLambda_DWork.UnitDelay_DSTATE_j = sstab_load_lam;

  /* Update for Memory: '<S92>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput_b = rtb_SteadyStateDetect_o3_n;

  /* Update for Memory: '<S92>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_o = rtb_SteadyStateDetect_o4_n;

  /* Update for UnitDelay: '<S10>/Unit Delay' */
  IonLambda_DWork.UnitDelay_DSTATE = FreqNorm;

  /* Update for UnitDelay: '<S75>/Delay Input1'
   * Block description for '<S75>/Delay Input1':
   *  
   *  Store in Global RAM
   */
  IonLambda_DWork.DelayInput1_DSTATE = FreqNorm;

  /* Update for UnitDelay: '<S76>/Unit Delay' */
  IonLambda_DWork.UnitDelay_DSTATE_h = sstab_rpm_ffs;

  /* Update for Memory: '<S85>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput_g = rtb_SigStab_o3;

  /* Update for Memory: '<S85>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_f = rtb_SigStab_o4;

  /* Update for UnitDelay: '<S76>/Unit Delay1' */
  IonLambda_DWork.UnitDelay1_DSTATE_f = sstab_load_ffs;

  /* Update for Memory: '<S86>/Memory1' */
  IonLambda_DWork.Memory1_PreviousInput_l = rtb_SigStab_o3_k;

  /* Update for Memory: '<S86>/Memory' */
  IonLambda_DWork.Memory_PreviousInput_i = rtb_SigStab_o4_i;
}

/* Functions for block: '<S1>/Chart' */

static void IonLambda_c4_IonLambda(void );

static void IonLambda_c4_IonLambda(void )
{
  uint8_T idx_c;
  uint8_T idx_r;
  if(_sfEvent_IonLambda_ == IonLambda_event_ev_NoSync) {
  } else if(_sfEvent_IonLambda_ == IonLambda_event_ev_TDC) {

    IonLambda_IonLambda_TDC();

    return;
  }
  else {
    return;
  }

  /* Output and update for function-call system: '<S1>/IonLambda_Reset' */

  /* DataStoreWrite: '<S3>/Data Store Write1' */
  FiltLamEnable = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write2' */
  FiltLamFreeze = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write3' */
  FlgForceOL = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write4' */
  LamEstAvg = 1024U;

  /* Stateflow: '<S3>/Lam_Vector_Reset' incorporates:
   *  Constant: '<S3>/ONE'
   */

  {
    uint8_T idx;
    for(idx = 0U; idx < rtcP_pooled11; idx++) {
      LamEst[(int32_T)idx] = rtcP_pooled5;

      IonLambda_DWork.LamEstFilt[(int32_T)idx] = rtcP_pooled5;

      IonLambda_DWork.LamEstFilt_HR[(int32_T)idx] =
        IonLambda_ConstB.DataTypeConversion_d;

      Avg_HR_FFS[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion1_l;

      Avg_HR_old_FFS[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion1_l;

      In_x_N0_FFS[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion2_l;

      InSum_FFS[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion2_l;

      Avg_HR_IntIon[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion1_l;

      Avg_HR_old_IntIon[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion1_l;

      In_x_N0_IntIon[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion2_l;

      InSum_IntIon[(int32_T)idx] = IonLambda_ConstB.DataTypeConversion2_l;
    }
  }
}

/* Initial conditions for trigger system: '<S1>/Chart' */
void IonLambda_Chart_Init(void)
{

  if (rtmIsFirstInitCond(IonLambda_M)) {
    IonLambda_IonLambda_TDC_Init();
  }
}

/* Disable for trigger system: '<S1>/Chart' */
void IonLambda_Chart_Disable(void)
{

  /* Disable code for chart: '<S1>/Chart' */

  IonLambda_IonLambda_TDC_Disable();
}

/* Start for trigger system: '<S1>/Chart' */
void IonLambda_Chart_Start(void)
{

  IonLambda_IonLambda_TDC_Start();
}

/* Output and update for trigger system: '<S1>/Chart' */
void IonLambda_Chart(void)
{
  /* local block i/o variables*/
  int8_T rtb_inputevents[3];

  {
    ZCEventType trigEvent;
    ZCEventType zcEvents[3];

    /* subsystem trigger input */
    trigEvent = NO_ZCEVENT;

    zcEvents[0] = (ZCEventType) ((IonLambda_U.ev_PowerOn > 0) &&
      (IonLambda_PrevZC.sf_Chart_ZCE[0] == 0));
    IonLambda_PrevZC.sf_Chart_ZCE[0] = (ZCSigState) IonLambda_U.ev_PowerOn;
    trigEvent = (zcEvents[0] == NO_ZCEVENT)? trigEvent : zcEvents[0];

    zcEvents[1] = (ZCEventType) ((IonLambda_U.ev_TDC > 0) &&
      (IonLambda_PrevZC.sf_Chart_ZCE[1] == 0));
    IonLambda_PrevZC.sf_Chart_ZCE[1] = (ZCSigState) IonLambda_U.ev_TDC;
    trigEvent = (zcEvents[1] == NO_ZCEVENT)? trigEvent : zcEvents[1];

    zcEvents[2] = (ZCEventType) ((IonLambda_U.ev_NoSync > 0) &&
      (IonLambda_PrevZC.sf_Chart_ZCE[2] == 0));
    IonLambda_PrevZC.sf_Chart_ZCE[2] = (ZCSigState) IonLambda_U.ev_NoSync;
    trigEvent = (zcEvents[2] == NO_ZCEVENT)? trigEvent : zcEvents[2];
    /* conditionally execute */
    if (trigEvent != NO_ZCEVENT) {

      /* update trigger block outputs */

      rtb_inputevents[0] = (int8_T) zcEvents[0];

      rtb_inputevents[1] = (int8_T) zcEvents[1];

      rtb_inputevents[2] = (int8_T) zcEvents[2];

      /* Stateflow: '<S1>/Chart' incorporates:
       *  Constant: '<S1>/IDN_EE_IONLAMBDA'
       *   Inport: '<Root>/EERecStatus'
       *  Constant: '<S1>/NO_ERROR'
       *  Constant: '<S1>/BKLOADADFFS_dim'
       *  Constant: '<S1>/BKRPMADFFS_dim'
       */

      {
        uint8_T b_previousEvent;
        if(rtb_inputevents[0] == 1) {
          b_previousEvent = _sfEvent_IonLambda_;
          _sfEvent_IonLambda_ = IonLambda_event_ev_PowerOn;
          IonLambda_c4_IonLambda();
          _sfEvent_IonLambda_ = b_previousEvent;
        }
        if(rtb_inputevents[1] == 1) {
          b_previousEvent = _sfEvent_IonLambda_;
          _sfEvent_IonLambda_ = IonLambda_event_ev_TDC;
          IonLambda_c4_IonLambda();
          _sfEvent_IonLambda_ = b_previousEvent;
        }
        if(rtb_inputevents[2] == 1) {
          b_previousEvent = _sfEvent_IonLambda_;
          _sfEvent_IonLambda_ = IonLambda_event_ev_NoSync;
          IonLambda_c4_IonLambda();
          _sfEvent_IonLambda_ = b_previousEvent;
        }
      }
    }
  }
}

/* Model step function */
void IonLambda_step(void)
{

  /* Stateflow: '<S1>/Chart' incorporates:
   *   Inport: '<Root>/ev_PowerOn'
   *   Inport: '<Root>/ev_TDC'
   *   Inport: '<Root>/ev_NoSync'
   */
  /* trigger Stateflow Block: <S1>/Chart */

  IonLambda_Chart();

  /* set "at time zero" to false */
  if (rtmIsFirstInitCond(IonLambda_M)) {
    rtmSetFirstInitCond(IonLambda_M, 0);
  }
}

/* Model initialize function */

void IonLambda_initialize(boolean_T firstTime)
{

  if (firstTime) {
    /* initialize real-time model */
    (void) memset((char_T *)IonLambda_M,0,sizeof(RT_MODEL_IonLambda));

    rtmSetFirstInitCond(IonLambda_M, 1);

    /* block I/O */
    (void) memset(((void *) &IonLambda_B),0,sizeof(BlockIO_IonLambda));

    /* exported global signals */
    AccumDelta = 0;
    AccumFFS = 0U;
    AccumBkFFS = 0U;
    BkFFSNCOut = 0U;
    AccumLam = 0U;
    IDZoneFFSRpm = 0;
    IDZoneFFSLoad = 0;
    AvgIntIonCyl = 0;
    IntIonMedianCorr = 0;
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        AvgIntIon[i] = 0;
      }
    }
    IntIonMedian = 0;
    {
      int_T i;
      for (i = 0; i < 9; i++) {
        BkIntIon[i] = 0;
      }
    }
    BkFFSAdj = 0U;
    RtZoneFFSRpm = 0U;
    RtZoneFFSLoad = 0U;
    FFSGainSelfAdj = 0U;
    LamObjSelfAdj = 0U;
    LamIntIonCyl = 0U;
    GnLaMod = 0U;
    LamFFSCyl = 0U;
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        LamEstSlow[i] = 0U;
      }
    }
    AvgFFSCyl = 0U;
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        AvgFFS[i] = 0U;
      }
    }
    FFSMedian = 0U;
    FFSMedianCorr = 0U;
    BkFFSRef = 0U;
    {
      int_T i;
      for (i = 0; i < 9; i++) {
        BkFFS[i] = 0U;
      }
    }
    D1LamFil = 0;
    D2LamFil = 0;
    KFiltLamEstAvg = 0;
    DeltaLamCLMem = 0;
    DSAGainFFS = 0U;
    DSAGainINT = 0U;
    FreqNorm = 0;
    N0LamFil = 0U;
    StabRpmLamTr = 0;
    sstab_rpm_lam = 0;
    StabLoadLamTr = 0;
    sstab_load_lam = 0;
    FiltParReset = 0;
    EnFilButter = 0;
    FStabRpmSelfAdj = 0;
    sstab_rpm_ffs = 0;
    FStabLoadSelfAdj = 0;
    sstab_load_ffs = 0;
    EnFilLamEst = 0;
    SelfAdjState = 0;
    EnSelfAdj = 0;
    IndBkLam = 0;
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        LambdaState[i] = 0;
      }
    }
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        FlgLamIntIonRel[i] = 0;
      }
    }
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        FlgLamFFSRel[i] = 0;
      }
    }
    {
      int_T i;
      for (i = 0; i < 8; i++) {
        FlgLamRel[i] = 0;
      }
    }
    GainIntIonCrk = 0U;
    GainFFSCrk = 0U;

    /* data type work */
    (void) memset((char_T *) &IonLambda_DWork,0,sizeof(D_Work_IonLambda));

    /* exported global states */
    (void) memset(&Avg_HR_FFS,0,8*sizeof(uint32_T));
    (void) memset(&Avg_HR_old_FFS,0,8*sizeof(uint32_T));
    (void) memset(&Avg_HR_old_IntIon,0,8*sizeof(uint32_T));
    (void) memset(&Avg_HR_IntIon,0,8*sizeof(uint32_T));
    (void) memset(&In_x_N0_FFS,0,8*sizeof(uint32_T));
    (void) memset(&InSum_FFS,0,8*sizeof(uint32_T));
    (void) memset(&In_x_N0_IntIon,0,8*sizeof(uint32_T));
    (void) memset(&InSum_IntIon,0,8*sizeof(uint32_T));
    LamObjMem = 0U;
    LamEstAvg = 0U;
    (void) memset(&LamEst,0,8*sizeof(uint16_T));
    (void) memset(&TbFlgSelfAdjOK,0,42*sizeof(uint8_T));
    FiltLamEnable = 0;
    FiltLamFreeze = 0;
    FlgForceOL = 0;

    /* custom states */
    {
      int_T i;
      for (i = 0; i < 42; i++) {
        TbBkFFSAdj[i] = 0U;
      }
    }

    /*trigger Subsystem Block: <S1>/Chart */
    IonLambda_Chart_Start();

    {
      int32_T i1;
      for(i1 = 0; i1 < 8; i1++) {

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_15' */
        LamEst[i1] = (IonLambda_ConstP.pooled7[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_16' */
        IonLambda_DWork.LamEstFilt_HR[i1] = (IonLambda_ConstP.pooled1[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_17' */
        IonLambda_DWork.LamEstFilt[i1] = (IonLambda_ConstP.pooled7[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_18' */
        Avg_HR_FFS[i1] = (IonLambda_ConstP.pooled2[i1]);
      }
      for(i1 = 0; i1 < 42; i1++) {

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_2' */
        TbBkFFSAdj[i1] = (IonLambda_ConstP._DataStoreBlk_2_Initia[i1]);
      }
      for(i1 = 0; i1 < 8; i1++) {

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_3' */
        Avg_HR_old_FFS[i1] = (IonLambda_ConstP.pooled2[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_4' */
        In_x_N0_FFS[i1] = (IonLambda_ConstP.pooled3[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_5' */
        InSum_FFS[i1] = (IonLambda_ConstP.pooled3[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_6' */
        Avg_HR_old_IntIon[i1] = (IonLambda_ConstP.pooled2[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_7' */
        In_x_N0_IntIon[i1] = (IonLambda_ConstP.pooled3[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_8' */
        InSum_IntIon[i1] = (IonLambda_ConstP.pooled3[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_9' */
        Avg_HR_IntIon[i1] = (IonLambda_ConstP.pooled2[i1]);
      }
    }
  }

  /* Zero-crossing state initialization */
  {
    int idx;
    for (idx = 0; idx < 3; idx ++) {
      IonLambda_PrevZC.sf_Chart_ZCE[idx] = POS_ZCSIG;
    }
  }

  /*trigger Subsystem Block: <S1>/Chart */
  IonLambda_Chart_Init();

  /* Machine initializer */
  _sfEvent_IonLambda_ = CALL_EVENT;
}

/* user code (bottom of source file) */
/* System: <Root> */
void IonLambda_Init(void)
{
  IonLambda_U.ev_PowerOn = 0;

  IonLambda_U.ev_TDC = 0;

  IonLambda_U.ev_NoSync = 0;

  IonLambda_step();

  IonLambda_U.ev_PowerOn = 1;

  IonLambda_U.ev_TDC = 0;

  IonLambda_U.ev_NoSync = 0;

  IonLambda_step();
}

void IonLambda_TDC(void)
{
  IonLambda_U.ev_PowerOn = 0;

  IonLambda_U.ev_TDC = 0;

  IonLambda_U.ev_NoSync = 0;

  IonLambda_step();

  IonLambda_U.ev_PowerOn = 0;

  IonLambda_U.ev_TDC = 1;

  IonLambda_U.ev_NoSync = 0;

  IonLambda_step();
}

void IonLambda_NoSync(void)
{
  IonLambda_U.ev_PowerOn = 0;

  IonLambda_U.ev_TDC = 0;

  IonLambda_U.ev_NoSync = 0;

  IonLambda_step();

  IonLambda_U.ev_PowerOn = 0;

  IonLambda_U.ev_TDC = 0;

  IonLambda_U.ev_NoSync = 1;

  IonLambda_step();
}
#else
uint16_T LamEstAvg = FIX_10;
uint16_T LamEstSlow[8] = { FIX_10, FIX_10, FIX_10, FIX_10, FIX_10, FIX_10, FIX_10, FIX_10 };
uint8_T  FlgLamRel[8] = { 1, 1, 1, 1, 1, 1, 1, 1 };
uint8_T FlgForceOL = 0;
uint16_T LamObjSelfAdj = 1024;
#endif                                  // _BUILD_IONLAMBDA_

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
