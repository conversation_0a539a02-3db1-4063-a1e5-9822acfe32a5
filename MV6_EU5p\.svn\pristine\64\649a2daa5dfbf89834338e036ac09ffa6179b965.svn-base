/*
 * File: MisfOBD2.h
 *
 * Code generated for Simulink model 'MisfOBD2'.
 *
 * Model version                  : 1.588
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Aug 19 10:02:22 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (26), Warnings (6), Error (0)
 */

#ifndef RTW_HEADER_MisfOBD2_h_
#define RTW_HEADER_MisfOBD2_h_
#include <string.h>
#ifndef MisfOBD2_COMMON_INCLUDES_
# define MisfOBD2_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#endif                                 /* MisfOBD2_COMMON_INCLUDES_ */

#include "MisfOBD2_types.h"

/* Includes for objects with custom storage classes. */
#include "MisfOBD2_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKENCMICATOBD2_dim             6U                        /* Referenced by:
                                                                  * '<S47>/BKENCMICATOBD2_dim'
                                                                  * '<S49>/BKENCMICATOBD2_dim'
                                                                  */

/* dim */
#define BKENRPMCATOBD2_dim             6U                        /* Referenced by:
                                                                  * '<S47>/BKENRPMCATOBD2_dim'
                                                                  * '<S49>/BKENRPMCATOBD2_dim'
                                                                  */

/* dim */
#define BKLOADMISFOBD2_dim             5U                        /* Referenced by:
                                                                  * '<S11>/BKLOADMISFOBD2_dim'
                                                                  * '<S12>/BKLOADMISFOBD2_dim'
                                                                  * '<S41>/BKLOADMISFOBD2_dim'
                                                                  */

/* dim */
#define BKNREVOBD2EMISS_dim            3U                        /* Referenced by:
                                                                  * '<S48>/BKNREVOBD2EMISS_dim'
                                                                  * '<S49>/BKNREVOBD2EMISS_dim'
                                                                  */

/* dim */
#define BKRPMMISFOBD2_dim              5U                        /* Referenced by:
                                                                  * '<S11>/BKRPMMISFOBD2_dim'
                                                                  * '<S12>/BKRPMMISFOBD2_dim'
                                                                  * '<S41>/BKRPMMISFOBD2_dim'
                                                                  */

/* dim */
#define BKSTEPEMISSOBD2_dim            4U                        /* Referenced by: '<S12>/BKSTEPEMISSOBD2_dim' */

/* dim */
#define ID_MISF_OBD2                   26642219U                 /* Referenced by: '<S3>/ID_MISF_OBD2' */

/* mask */
#define PERC_MISF_SCALE                128U                      /* Referenced by:
                                                                  * '<S11>/Calc_CylMisfire'
                                                                  * '<S12>/Calc_CylMisfire'
                                                                  */

/* scale */

/* Block signals and states (default storage) for system '<S14>/Cyl_Counter' */
typedef struct {
  uint8_T is_active_c4_MisfOBD2;       /* '<S14>/Cyl_Counter' */
} rtDW_Cyl_Counter_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S8>/fc_1' */
typedef struct {
  rtDW_Cyl_Counter_MisfOBD2_T sf_Cyl_Counter;/* '<S14>/Cyl_Counter' */
} rtDW_fc_1_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S8>/fc_2' */
typedef struct {
  rtDW_Cyl_Counter_MisfOBD2_T sf_Cyl_Counter;/* '<S15>/Cyl_Counter' */
} rtDW_fc_2_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S8>/fc_3' */
typedef struct {
  rtDW_Cyl_Counter_MisfOBD2_T sf_Cyl_Counter;/* '<S16>/Cyl_Counter' */
} rtDW_fc_3_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S8>/fc_4' */
typedef struct {
  rtDW_Cyl_Counter_MisfOBD2_T sf_Cyl_Counter;/* '<S17>/Cyl_Counter' */
} rtDW_fc_4_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S5>/fc_ThrCAT' */
typedef struct {
  uint8_T Memory_PreviousInput;        /* '<S11>/Memory' */
  uint8_T Memory1_PreviousInput;       /* '<S11>/Memory1' */
} rtDW_fc_ThrCAT_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S5>/fc_ThrEmiss' */
typedef struct {
  uint32_T Memory8_PreviousInput;      /* '<S12>/Memory8' */
} rtDW_fc_ThrEmiss_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S5>/fc_revolution' */
typedef struct {
  rtDW_fc_ThrEmiss_MisfOBD2_T fc_ThrEmiss;/* '<S5>/fc_ThrEmiss' */
  rtDW_fc_ThrCAT_MisfOBD2_T fc_ThrCAT; /* '<S5>/fc_ThrCAT' */
  uint16_T PreLookUpIdSearch_U16_o1;   /* '<S45>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o1_go0;/* '<S44>/PreLookUpIdSearch_U16' */
  uint16_T DataTypeConversion1;        /* '<S44>/Data Type Conversion1' */
  uint16_T DataTypeConversion1_bti;    /* '<S45>/Data Type Conversion1' */
} rtDW_fc_revolution_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S50>/EnTrig' */
typedef struct {
  uint8_T is_active_c14_MisfOBD2;      /* '<S50>/EnTrig' */
  uint8_T is_c14_MisfOBD2;             /* '<S50>/EnTrig' */
} rtDW_EnTrig_MisfOBD2_T;

/* Block signals and states (default storage) for system '<S50>/fc_write_PCode' */
typedef struct {
  uint32_T CCCodeBlock;                /* '<S62>/C//C++ Code Block' */
  uint16_T DataTypeConversion;         /* '<S62>/Data Type Conversion' */
  uint16_T pCode;                      /* '<S62>/C//C++ Code Block1' */
} rtDW_fc_write_PCode_MisfOBD2_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  rtDW_fc_write_PCode_MisfOBD2_T fc_write_PCode;/* '<S50>/fc_write_PCode' */
  rtDW_EnTrig_MisfOBD2_T sf_EnTrig1;   /* '<S50>/EnTrig1' */
  rtDW_EnTrig_MisfOBD2_T sf_EnTrig;    /* '<S50>/EnTrig' */
  rtDW_fc_revolution_MisfOBD2_T fc_revolution;/* '<S5>/fc_revolution' */
  rtDW_fc_4_MisfOBD2_T fc_4;           /* '<S8>/fc_4' */
  rtDW_fc_3_MisfOBD2_T fc_3;           /* '<S8>/fc_3' */
  rtDW_fc_2_MisfOBD2_T fc_2;           /* '<S8>/fc_2' */
  rtDW_fc_1_MisfOBD2_T fc_1;           /* '<S8>/fc_1' */
  uint32_T Add1;                       /* '<S12>/Add1' */
  uint16_T vtCntMisfEmiss[4];          /* '<S12>/vtCntMisfEmiss' */
  uint16_T vtCntMisfCAT[4];            /* '<S11>/vtCntMisfCAT' */
  uint16_T NRevOBD2Cat;
  uint16_T NRevOBD2Emiss_kdt;
  uint16_T pCode;                      /* '<S50>/Select_Diag' */
  uint16_T NRevEmiss_ebn;              /* '<S13>/OBD2_EMISSIONS' */
  uint16_T DataTypeConversion1;        /* '<S12>/Data Type Conversion1' */
  uint16_T CntEmissOBD2_p22;           /* '<S12>/Calc_EmissFlg' */
  uint16_T NRevCAT_fez;                /* '<S13>/OBD2_CAT' */
  uint16_T DataTypeConversion1_ey0;    /* '<S11>/Data Type Conversion1' */
  uint16_T cntMisfCAT;                 /* '<S17>/Cyl_Counter' */
  uint16_T cntMisfEmiss;               /* '<S17>/Cyl_Counter' */
  uint16_T cntMisfCAT_e5z;             /* '<S16>/Cyl_Counter' */
  uint16_T cntMisfEmiss_hs5;           /* '<S16>/Cyl_Counter' */
  uint16_T cntMisfCAT_fz1;             /* '<S15>/Cyl_Counter' */
  uint16_T cntMisfEmiss_kyl;           /* '<S15>/Cyl_Counter' */
  uint16_T cntMisfCAT_jbl;             /* '<S14>/Cyl_Counter' */
  uint16_T cntMisfEmiss_cud;           /* '<S14>/Cyl_Counter' */
  uint16_T Memory7_PreviousInput;      /* '<S8>/Memory7' */
  uint16_T Memory6_PreviousInput;      /* '<S8>/Memory6' */
  uint16_T Memory5_PreviousInput;      /* '<S8>/Memory5' */
  uint16_T Memory4_PreviousInput;      /* '<S8>/Memory4' */
  uint16_T Memory_PreviousInput;       /* '<S8>/Memory' */
  uint16_T Memory1_PreviousInput;      /* '<S8>/Memory1' */
  uint16_T Memory2_PreviousInput;      /* '<S8>/Memory2' */
  uint16_T Memory3_PreviousInput;      /* '<S8>/Memory3' */
  int8_T MisfCylEmiss_ne2;             /* '<S12>/Calc_CylMisfire' */
  int8_T MisfCylCAT_ptp;               /* '<S11>/Calc_CylMisfire' */
  uint8_T cntFiltMisfOBD2[4];          /* '<S9>/Misfire_Filter' */
  uint8_T EnMisfOBD2_brz;
  uint8_T resSync;
  uint8_T resSync_htn;                 /* '<S50>/res_Sync' */
  uint8_T FlgMisfDiagOn_nn4;           /* '<S50>/Select_Diag' */
  uint8_T ptFault;                     /* '<S50>/Select_Diag' */
  uint8_T ackSync;                     /* '<S5>/Trigger_Cylinder' */
  uint8_T resetEmiss;                  /* '<S13>/OBD2_EMISSIONS' */
  uint8_T FlgEmissMisfOBD2_ot3;        /* '<S12>/Calc_EmissFlg' */
  uint8_T resetCAT;                    /* '<S13>/OBD2_CAT' */
  uint8_T flg;                         /* '<S27>/flg' */
  uint8_T is_active_c17_MisfOBD2;      /* '<S50>/res_Sync' */
  uint8_T is_c17_MisfOBD2;             /* '<S50>/res_Sync' */
  uint8_T oldDrivingCycle;             /* '<S50>/res_Count' */
  uint8_T is_active_c16_MisfOBD2;      /* '<S50>/Select_Diag' */
  uint8_T is_c16_MisfOBD2;             /* '<S50>/Select_Diag' */
  uint8_T oldCntClearDiag;             /* '<S50>/Select_Diag' */
  uint8_T oldFlgCatMisfOBD2;           /* '<S50>/Select_Diag' */
  boolean_T EnMisfCATOBD2_eas;
  boolean_T EnMisfEmissOBD2_ekh;
} D_Work_MisfOBD2_T;

/* Block signals and states (default storage) */
extern D_Work_MisfOBD2_T MisfOBD2_DWork;

/* Model entry point functions */
extern void MisfOBD2_initialize(void);

/* Exported entry point function */
extern void Trig_MisfOBD2_Init(void);

/* Exported entry point function */
extern void Trig_MisfOBD2_PreTdc(void);

/* Exported entry point function */
extern void Trig_MisfOBD2_T10(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Duplicate' : Unused code path elimination
 * Block '<S36>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Constant' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Propagation' : Unused code path elimination
 * Block '<S52>/Constant' : Unused code path elimination
 * Block '<S52>/Data Type Duplicate' : Unused code path elimination
 * Block '<S52>/Data Type Propagation' : Unused code path elimination
 * Block '<S54>/Data Type Duplicate' : Unused code path elimination
 * Block '<S55>/Data Type Duplicate' : Unused code path elimination
 * Block '<S56>/Data Type Duplicate' : Unused code path elimination
 * Block '<S57>/Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/Switch1' : Unused code path elimination
 * Block '<S50>/Switch2' : Unused code path elimination
 * Block '<S65>/Data Type Duplicate' : Unused code path elimination
 * Block '<S5>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S5>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S5>/Data Type Conversion2' : Eliminate redundant data type conversion
 * Block '<S5>/Data Type Conversion3' : Eliminate redundant data type conversion
 * Block '<S5>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion' : Eliminate redundant data type conversion
 * Block '<S28>/Reshape' : Reshape block reduction
 * Block '<S35>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion' : Eliminate redundant data type conversion
 * Block '<S35>/Reshape' : Reshape block reduction
 * Block '<S36>/Conversion' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion' : Eliminate redundant data type conversion
 * Block '<S44>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S44>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S45>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S51>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S51>/Reshape' : Reshape block reduction
 * Block '<S52>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S52>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S52>/Reshape' : Reshape block reduction
 * Block '<S53>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S53>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S55>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S55>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S55>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S56>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S56>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S56>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S57>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S57>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S6>/Data Type Conversion3' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'MisfOBD2'
 * '<S1>'   : 'MisfOBD2/MisfOBD2'
 * '<S2>'   : 'MisfOBD2/Model Info'
 * '<S3>'   : 'MisfOBD2/MisfOBD2/Init'
 * '<S4>'   : 'MisfOBD2/MisfOBD2/Merger'
 * '<S5>'   : 'MisfOBD2/MisfOBD2/PreTdc'
 * '<S6>'   : 'MisfOBD2/MisfOBD2/T10ms'
 * '<S7>'   : 'MisfOBD2/MisfOBD2/TP'
 * '<S8>'   : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf'
 * '<S9>'   : 'MisfOBD2/MisfOBD2/PreTdc/FOMisf'
 * '<S10>'  : 'MisfOBD2/MisfOBD2/PreTdc/Trigger_Cylinder'
 * '<S11>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT'
 * '<S12>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss'
 * '<S13>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_revolution'
 * '<S14>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_1'
 * '<S15>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_2'
 * '<S16>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_3'
 * '<S17>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_4'
 * '<S18>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_1/Cyl_Counter'
 * '<S19>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_2/Cyl_Counter'
 * '<S20>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_3/Cyl_Counter'
 * '<S21>'  : 'MisfOBD2/MisfOBD2/PreTdc/Counter_Misf/fc_4/Cyl_Counter'
 * '<S22>'  : 'MisfOBD2/MisfOBD2/PreTdc/FOMisf/Compare To Constant'
 * '<S23>'  : 'MisfOBD2/MisfOBD2/PreTdc/FOMisf/Compare To Constant1'
 * '<S24>'  : 'MisfOBD2/MisfOBD2/PreTdc/FOMisf/Misfire_Filter'
 * '<S25>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT/Calc_CylMisfire'
 * '<S26>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT/Compare To Constant'
 * '<S27>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT/Latch_Status'
 * '<S28>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT/Look2D_IR_U16'
 * '<S29>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT/Sum'
 * '<S30>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrCAT/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S31>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Calc_Counters'
 * '<S32>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Calc_CylMisfire'
 * '<S33>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Calc_EmissFlg'
 * '<S34>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Compare To Zero'
 * '<S35>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Look2D_IR_U16'
 * '<S36>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/LookUp_S16_S16'
 * '<S37>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Sum'
 * '<S38>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Calc_Counters/Calc_Misfire_Counters'
 * '<S39>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S40>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_ThrEmiss/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S41>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_revolution/Calc_ratio'
 * '<S42>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_revolution/OBD2_CAT'
 * '<S43>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_revolution/OBD2_EMISSIONS'
 * '<S44>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_revolution/Calc_ratio/PreLookUpIdSearch_U1'
 * '<S45>'  : 'MisfOBD2/MisfOBD2/PreTdc/fc_revolution/Calc_ratio/PreLookUpIdSearch_U16'
 * '<S46>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_Enable_Cond'
 * '<S47>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_Enable_Flags'
 * '<S48>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_NRevOBD2Emiss'
 * '<S49>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_ratio'
 * '<S50>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2'
 * '<S51>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_Enable_Flags/Look2D_IR_U1'
 * '<S52>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_Enable_Flags/Look2D_IR_U8'
 * '<S53>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_NRevOBD2Emiss/LookUp_IR_S16'
 * '<S54>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_NRevOBD2Emiss/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S55>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_ratio/PreLookUpIdSearch_S1'
 * '<S56>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_ratio/PreLookUpIdSearch_S16'
 * '<S57>'  : 'MisfOBD2/MisfOBD2/T10ms/Calc_ratio/PreLookUpIdSearch_U16'
 * '<S58>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/EnTrig'
 * '<S59>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/EnTrig1'
 * '<S60>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/Select_Diag'
 * '<S61>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/fc_globalCyl'
 * '<S62>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/fc_write_PCode'
 * '<S63>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/res_Count'
 * '<S64>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/res_Sync'
 * '<S65>'  : 'MisfOBD2/MisfOBD2/T10ms/Diag_OBD2/fc_globalCyl/SetDiagState'
 */

/*-
 * Requirements for '<Root>': MisfOBD2
 */
#endif                                 /* RTW_HEADER_MisfOBD2_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
