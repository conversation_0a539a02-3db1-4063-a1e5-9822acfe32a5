#ifndef _SYS_H_
#define _SYS_H_

/* MODULE SYS. */

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#if (TARGET_TYPE == MPC5554)
#include "mpc5554.h"
#elif (TARGET_TYPE == MPC5534)
#include "mpc5534.h"
#elif (TARGET_TYPE == MPC5633  || TARGET_TYPE == MPC5634)
#include "mpc563m.h"
#else
#error ERROR: Target not supported
#endif
#include "OS_api.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

/* Reset types */
#define POWERONRESET					0x00
#define EXTERNALRESET  				    0x01
#define LOSSOFLOCKRESET  				0x02
#define LOSSOFCLOCKRESET				0x03
#define WATCHDOGRESET					0x04
#define CHECKSTOPRESET					0x05
#define SOFTWARESYSTEMRESET				0x06
#define SOFTWAREEXTERNALRESET			0x07

/* IVOR Index */
#define IVOR0_INDX   0
#define IVOR1_INDX   1
#define IVOR2_INDX   2
#define IVOR3_INDX   3
#define IVOR5_INDX   5
#define IVOR6_INDX   6
#define IVOR7_INDX   7
#define IVOR8_INDX   8
#define IVOR9_INDX   9
#define IVOR10_INDX 10
#define IVOR11_INDX 11
#define IVOR12_INDX 12
#define IVOR13_INDX 13
#define IVOR14_INDX 14
#define IVOR15_INDX 15
#define IVOR32_INDX 32
#define IVOR33_INDX 33
#define IVOR34_INDX 34

/* Special Purpose Register ESR bit-field defines */
#define SPR_ESR_FIELD_ST     0x00800000
#define SPR_ESR_FIELD_SPE    0x00000080
#define SPR_ESR_FIELD_DLK    0x00200000
#define SPR_ESR_FIELD_ILK    0x00100000
#define SPR_ESR_FIELD_BO     0x00020000
#define SPR_ESR_FIELD_XTE    0x00000001
#define SPR_ESR_FIELD_PIL    0x08000000
#define SPR_ESR_FIELD_PPR    0x04000000
#define SPR_ESR_FIELD_PTR    0x02000000
#define SPR_ESR_FIELD_PUO    0x00040000
#define SPR_ESR_FIELD_FP     0x01000000

/* Special Purpose Register MCSR bit-field defines */
#define SPR_MCSR_FIELD_BUS_WRERR   0x00000004
#define SPR_MCSR_FIELD_EXCP_ERR	   0x08000000
#define SPR_MCSR_FIELD_CPERR	     0x10000000
#define SPR_MCSR_FIELD_CP_PERR	   0x20000000
#define SPR_MCSR_FIELD_MCP		     0x80000000

/* IVOR Exception description based on ESR status */ 
#define IVOR1_MC_INPUT_PIN			     0x00001
#define IVOR1_CACHE_PUSH_PARITY_ERR	 0x00002
#define IVOR1_CACHE_PARITY_ERR		   0x00004
#define IVOR1_EXCP_ERR				       0x00008
#define IVOR1_BUS_ERR				         0x00010
#define IVOR2_ACCESS_EXECPT	         0x00020
#define IVOR2_BYTE_ORDER	           0x00040
#define IVOR2_CACHE_LOCK	           0x00080
#define IVOR2_EXTERN_TERMIN_ERR      0x00100
#define IVOR3_INSTRC_STOR            0x00200
#define IVOR5_ALLIGN_INTERR          0x00400
#define IVOR6_ILLEGAL				         0x00800
#define IVOR6_PRIVILEGED   		       0x01000
#define IVOR6_TRAP					         0x02000
#define IVOR6_UNIMPLEMENT			       0x04000
#define IVOR13_DATA_TLB_ERR			     0x08000
#define IVOR32_SPE_APU_UNAVAIL		   0x10000
#define IVOR33_SPE_FP_DATA_INTERR    0x20000
#define IVOR34_SPE_FP_ROUND_INTERR   0x40000

/* IVOR methods Internal macros defines */
#define IVOR1_RECOVERABLE					          0
#define IVOR1_UNRECOVERABLE                 (-13)
#define SYS_GetESRExceptionType_FCN_ERROR   (-12)

#define VALID_REGION (0xFFFFFFFF)

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/

#define SYS_IsCacheEnabled() ((getSpecReg32SPR_L1CSR0() & 0x01) ? TRUE: FALSE)
#define SYS_CacheEnable() {uint32_t tmp;tmp=getSpecReg32SPR_L1CSR0();asm("msync");asm("isync");setSpecReg32SPR_L1CSR0(tmp|0x1);}                      
#define SYS_CacheDisable() {uint32_t tmp;tmp=getSpecReg32SPR_L1CSR0();asm("msync");asm("isync");setSpecReg32SPR_L1CSR0(tmp & 0xfffffffe);}
#define DisableAllInterrupts() asm(" wrteei 0")
#define EnableAllInterrupts()  asm(" wrteei 1")
#define SYS_DisableWatchdog()     setSpecReg32SPR_HID0(getSpecReg32SPR_HID0() & (~0x00004000))
#define SYS_SwRST()    (SIU.SRCR.B.SSR=1)

//#define SYS_PinConfig(pinNumber, PA, OBE, IBE, DSC, ODE, HYS, SRC, WP)   SIU.PCR[pinNumber].R = (PA|OBE|IBE|DSC|ODE|HYS|SRC|WP)
#define SYS_InPinConfig(pinNumber, PA, HYS, WP)                         SIU.PCR[pinNumber].R = (PA | INPUT_ENABLE | HYS | WP )
#define SYS_OutPinConfig(pinNumber, PA, IBE, DSC, ODE, SRC)             SIU.PCR[pinNumber].R = (PA | OUTPUT_ENABLE| IBE | DSC | ODE | SRC)

#define CHECK_VALID_REGION(r) (((r)->validMemoryRegion == VALID_REGION) ? (NO_ERROR) : (-1))

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

/* possible module internal states*/
enum SYS_INTERNAL_STATE
{
   SYI_STATE_PRE_CONFIGURATION,     /*pre configuration state*/
   SYI_STATE_CONFIGURED,            /* configuration completed*/
   SYI_STATE_INITIALIZED,           /* init completed*/
   SYI_STATE_ERROR                  /* generic error state*/
};

/* possible module enabling states*/
enum SYS_ENABLE_STATE
{
   SYE_STATE_DISABLED,          /*pre initializiation state*/
   SYE_STATE_ENABLED            /* init completed*/
};


typedef enum { TRIGGER_ON, TRIGGER_OFF         } t_TriggerStatus;
typedef enum { TIME_BASE, ANGLE_BASE, EXT_BASE } t_TriggerBase;
typedef enum { GPIO_SOURCE, 
               ETPUA_SOURCE, 
               EMIOS_SOURCE, 
               ETRIG_SOURCE } t_AdcSource;

typedef struct BlockDescription
{
    uint32_t  blockType;             /* 0x1 = Miniboot, 0x2 = Boot, 0x3 = Application, 0x4 = Calibration */
    uint32_t  startAddress;          /* Block Starting Address */
    uint32_t  blockSize;             /* Block Size (Bytes) */
    uint8_t   blockVersionLabel[11 /*26*/];    /* sw number */
    uint8_t   blockPaddingBytes[25 /*10*/]; /* Padding Bytes needed for the alignment */
    uint32_t  blockVersion;          /* Data Timestamp of the block code */
    uint32_t  validMemoryRegion;              /* Burner block code Identifier */
    uint32_t  blockChecksum;         /* Block Data Checksum (CRC32) */
    uint32_t    blockStatusOK;       /* Block Status Integrity: 0x0 = status OK; (! 0x0) = status NOT OK */
} BlockDescription __attribute__ ((aligned(32)));

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
 
extern uint32_t tempOsObjId;     /* for first parameter */
/* public variables */
#ifdef CHECK_BIOS_FAULTS
extern uint32_t  BIOS_Faults;     /* variable for storing bios failures       */
#endif 

extern  OSSERVICEIDTYPE tempOsService;   /* for OSErrorGetServiceId() from ErrorHook */
extern uint16_t sysInternalState;


/* MEMORY REGIONS imported from LD file */ 
extern uint32_t     __APP_TAG_START;
extern uint32_t     __APP_TAG_SIZE;

extern uint32_t     __KWP_START_BOOT_ADD;
extern uint32_t     __KWP_CODE_BOOT_SIZE;
extern uint32_t     __KWP_START_APPL_ADD;
extern uint32_t     __KWP_CODE_APPL_SIZE;
extern uint32_t     __KWP_CODE_CALIB_SIZE;

extern uint32_t     __FLASH_START;
extern uint32_t     __FLASH_END;

extern uint32_t     __MINIBOOT_START;
extern uint32_t     __MINIBOOT_SIZE;
extern uint32_t     __EEPROM_START;
extern uint32_t     __EEPROM_SIZE;
extern uint32_t     __EEPROM_END;
extern uint32_t     __BOOT_START;
extern uint32_t     __BOOT_SIZE;
extern uint32_t     __CALIB_ROM_START;
extern uint32_t     __CALIB_ROM_SIZE;
extern uint32_t     __VCALIB_START;
extern uint32_t     __VCALIB_SIZE;
extern uint32_t     __VCALIB_CACHE_START;
extern uint32_t     __APP_START;
extern uint32_t     __APP_SIZE;
extern uint32_t     __BACKUP_START;
extern uint32_t     __BACKUP_SIZE;

extern uint32_t     __SRAM_START_ADDR;
extern uint32_t     __SRAM_END_ADDR;
extern uint32_t     __SRAM_START_ADDR_1;
extern uint32_t     __SRAM_SIZE_1;
extern uint32_t     __CALIB_RAM_START;
extern uint32_t     __CALIB_RAM_END;

/* used by app_checkVersion.c */
extern uint32_t     __2APP_START;
extern uint32_t     __2APP_END;
/* used by calib_checkVersion.c */
extern uint32_t     __3CALIB_ROM_START;
extern uint32_t     __3CALIB_ROM_END;
/* used by vsram.c */
extern uint32_t     __VSRAM_SRAM_START; 
extern uint32_t     __VSRAM_SRAM_END;



/* EEPROM IDs labels imported from LD file */
extern uint32_t    __EE_ID0_START;
extern uint32_t    __EE_ID0_END;
extern uint32_t    __EE_ID0_DATA_START;
extern uint32_t    __EE_ID0_DATA_END;
extern uint32_t    __EE_ID1_START;
extern uint32_t    __EE_ID1_END;
extern uint32_t    __EE_ID1_DATA_START;
extern uint32_t    __EE_ID1_DATA_END;
extern uint32_t    __EE_ID2_START;
extern uint32_t    __EE_ID2_END;
extern uint32_t    __EE_ID2_DATA_START;
extern uint32_t    __EE_ID2_DATA_END;
extern uint32_t    __EE_ID3_START;
extern uint32_t    __EE_ID3_END;
extern uint32_t    __EE_ID3_DATA_START;
extern uint32_t    __EE_ID3_DATA_END;
extern uint32_t    __EE_ID4_START;
extern uint32_t    __EE_ID4_END;
extern uint32_t    __EE_ID4_DATA_START;
extern uint32_t    __EE_ID4_DATA_END;
extern uint32_t    __EE_ID5_START;
extern uint32_t    __EE_ID5_END;
extern uint32_t    __EE_ID5_DATA_START;
extern uint32_t    __EE_ID5_DATA_END;
extern uint32_t    __EE_ID6_START;
extern uint32_t    __EE_ID6_END;
extern uint32_t    __EE_ID6_DATA_START;
extern uint32_t    __EE_ID6_DATA_END;
extern uint32_t    __EE_ID7_START;
extern uint32_t    __EE_ID7_END;
extern uint32_t    __EE_ID7_DATA_START;
extern uint32_t    __EE_ID7_DATA_END;
extern uint32_t    __EE_ID8_START;
extern uint32_t    __EE_ID8_END;
extern uint32_t    __EE_ID8_DATA_START;
extern uint32_t    __EE_ID8_DATA_END;
extern uint32_t    __EE_ID9_START;
extern uint32_t    __EE_ID9_END;
extern uint32_t    __EE_ID9_DATA_START;
extern uint32_t    __EE_ID9_DATA_END;
extern uint32_t    __EE_ID10_START;
extern uint32_t    __EE_ID10_END;
extern uint32_t    __EE_ID10_DATA_START;
extern uint32_t    __EE_ID10_DATA_END;


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*===========================================================================*/
/**
**    \par Method
**    SYS_Config 
**
**    \par Description :
**    Driver Configuration: initialize driver memory and configuration
**
**         
**    \par none 
**    \return error code
** ===================================================================
*/
int16_t SYS_Config(void);

/*===========================================================================*/
/**
**    \par Method
**    SYS_ADC_SourceConfig 
**
**    \par Description :
**    ADC queue trigger source configuration: not supported for SOFTWARE_TRIGGERED mode
**
**         
**    \param uint16_t _samplingMode
**    \param t_TriggerBase _triggerBase
**    \return error code
** ===================================================================
*/
int16_t SYS_ADC_SourceConfig(uint16_t _samplingMode,
                             t_TriggerBase _triggerBase);

/*===========================================================================*/
/**
**    \par Method
**    SYS_ADC_SetPeriod 
**
**    \par Description :
**    ADC queue trigger period configuration: _period could be expressed in [us] or [degree/16]
**
**         
**    \param uint16_t _samplingMode
**    \param uint32_t _period
**    \return error code
** ===================================================================
*/
int16_t SYS_ADC_SetPeriod(uint16_t _samplingMode,
                          uint32_t _period);


/*===========================================================================*/
/**
**    \par Method
**    SYS_ADC_SetStatus 
**
**    \par Description :
**    ADC queue trigger start/stop method
**
**         
**    \param uint16_t _samplingMode
**    \param uint32_t _status
**    \return error code
** ===================================================================
*/
int16_t SYS_ADC_SetStatus(uint16_t _samplingMode, 
                          t_TriggerStatus _status);

/*===========================================================================*/
/**
**    \par Method
**    SYS_ADC_GetCFIFOStatus 
**
**    \par Description :
**    Return command FIFO status related to idxQueue
**
**         
**    \param uint16_t idxQueue
**    
**    \return error code
** ===================================================================
*/
uint16_t SYS_ADC_GetCFIFOStatus(uint16_t idxQueue);

/*===========================================================================*/
/**
**    \par Method
**    SYS_ADC_GetCFIFOStatus 
**
**    \par Description :
**    Return command FIFO status related to idxQueue
**
**         
**    \param uint16_t idxQueue
**    
**    \return error code
** ===================================================================
*/
uint16_t SYS_ADC_GetSampMode(uint16_t idxQueue);


/*===========================================================================*/
/**
**    \par Method
**    SYS_ADC_GetCFIFOStatus 
**
**    \par Description :
**    Return command FIFO status related to idxQueue
**
**         
**    \param uint16_t idxQueue
**    
**    \return error code
** ===================================================================
*/
int16_t SYS_ADC_SetAngTrigger(uint32_t *angTrigBuf, 
                              uint32_t _period, 
                              uint8_t trigNum);

/*===========================================================================*/
/**
**    \par Method
**    SYS_SRAM_test_failure 
**
**    \par Description :
**         
**    \param  none
**    \return none
** ===========================================================================
*/
void SYS_SRAM_Test_FailureRoutine(void);


/*===========================================================================*/
/**
**    \par Method
**    SYS_ProgShutdownDelayed 
**
**    \par Description :
**     Prepares system for shutdown by disabling peripherals interrupts and   
**     watchdog; it also calls an internal function that activate countdown
**     piloted by IVOR 10
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
void SYS_ProgDelayedShutdown (void);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_GetLastReset(uint8_t *lastReset);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
void    IVOR_Common_ISR(void);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_GetBiosFaults(uint32_t *faults);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_GetBiosInit(uint32_t *outvar);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_WriteBiosInit(uint32_t value);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_WriteBiosFaults(uint32_t value);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_AssignBitBiosInit(uint8_T bitidx, uint8_T bitvalue);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetLastReset 
**
**    \par Description :
**     IVOR 10 decrementer interrupt configuration function
**
**    Note: 
**         
**    \param  none
**    \return none
** ===================================================================
*/
int16_t SYS_AssignBitBiosFaults(uint8_T bitidx, uint8_T bitvalue);




#endif /*_SYS_H_ */
