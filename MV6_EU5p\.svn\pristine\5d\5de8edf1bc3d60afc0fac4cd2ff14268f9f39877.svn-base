/***************************************************************************

Date:       20/04/2005
Programmer: Solinas


***************************************************************************/
/* Precompiler defines for SPI module */
/****************************************************************************
     Peripheral defines 
     The configuration parameters are defined below 
 ****************************************************************************/

/* Baud Rate Scaler */

#define BR_SCALER_2         0x0
#define BR_SCALER_4         0x1
#define BR_SCALER_6         0x2
#define BR_SCALER_8         0x3
#define BR_SCALER_16        0x4
#define BR_SCALER_32        0x5
#define BR_SCALER_64        0x6
#define BR_SCALER_128       0x7
#define BR_SCALER_256       0x8
#define BR_SCALER_512       0x9
#define BR_SCALER_1024      0xa
#define BR_SCALER_2048      0xb
#define BR_SCALER_4096      0xc
#define BR_SCALER_8192      0xd
#define BR_SCALER_16384     0xe
#define BR_SCALER_32768     0xf

#define BRP_SCALER_2        0x0
#define BRP_SCALER_3        0x1
#define BRP_SCALER_5        0x2
#define BRP_SCALER_7        0x3


#define FRAMESIZE_4         0x3
#define FRAMESIZE_5         0x4
#define FRAMESIZE_6         0x5
#define FRAMESIZE_7         0x6
#define FRAMESIZE_8         0x7
#define FRAMESIZE_9         0x8
#define FRAMESIZE_10        0x9
#define FRAMESIZE_11        0xa
#define FRAMESIZE_12        0xb
#define FRAMESIZE_13        0xc
#define FRAMESIZE_14        0xd
#define FRAMESIZE_15        0xe
#define FRAMESIZE_16        0xf

#define SPI_MASTER        1
#define SPI_SLAVE         0

#define SPI_LSB_FIRST   1           /* Data is transferred LSB first */
#define SPI_MSB_FIRST   0           /* Data is transferred MSB first */

#define SPI_CPOL_HIGH   0           /* Clock Polarity. The active state value of the SCK is high */
#define SPI_CPOL_LOW    1           /* Clock Polarity. The active state value of the SCK is low  */

#define SPI_CPHA_HIGH   1           /* Clock Phase. Data is changed on the leading edge and captured on the following edge*/
#define SPI_CPHA_LOW    0           /* Clock Phase. Data is captured on the leading edge and changed on the following    */

#define PCS_ACTIVE_LOW   1 /* Chip Select Active Low  */     
#define PCS_ACTIVE_HIGH  0 /* Chip Select Active High */     


#if (FSYS==128) 
  #define DBR_VAL_8M        0
  #define DBR_VAL_4M        0
  #define DBR_VAL_2M        0
  #define DBR_VAL_1M        0
  #define DBR_VAL_500K      0
  #define DBR_VAL_250K      0
  #define DBR_VAL_125K      0
  #define DBR_VAL_62500     0
  #define DBR_VAL_31250     0
  
  #define SPI_BRP_8M        BRP_SCALER_2
  #define SPI_BRP_4M        BRP_SCALER_2
  #define SPI_BRP_2M        BRP_SCALER_2
  #define SPI_BRP_1M        BRP_SCALER_2
  #define SPI_BRP_500K      BRP_SCALER_2
  #define SPI_BRP_250K      BRP_SCALER_2
  #define SPI_BRP_125K      BRP_SCALER_2
  #define SPI_BRP_62500     BRP_SCALER_2
  #define SPI_BRP_31250     BRP_SCALER_2

  #define SPI_BR_8M         BR_SCALER_8 
  #define SPI_BR_4M         BR_SCALER_16 
  #define SPI_BR_2M         BR_SCALER_32 
  #define SPI_BR_1M         BR_SCALER_64 
  #define SPI_BR_500K       BR_SCALER_128 
  #define SPI_BR_250K       BR_SCALER_256
  #define SPI_BR_125K       BR_SCALER_512
  #define SPI_BR_62500      BR_SCALER_1024
  #define SPI_BR_31250      BR_SCALER_2048

#elif (FSYS==80)
  #define DBR_VAL_8M        0
  #define DBR_VAL_4M        0
  #define DBR_VAL_2M        0
  #define DBR_VAL_1M        0
  #define DBR_VAL_500K      0
  #define DBR_VAL_250K      0
  #define DBR_VAL_208K      0
  #define DBR_VAL_178K      0
  #define DBR_VAL_125K      0
  #define DBR_VAL_62500     0
  #define DBR_VAL_31250     0

  #define SPI_BRP_8M        BRP_SCALER_5
  #define SPI_BRP_4M        BRP_SCALER_5
  #define SPI_BRP_2M        BRP_SCALER_5
  #define SPI_BRP_1M        BRP_SCALER_5
  #define SPI_BRP_500K      BRP_SCALER_5
  #define SPI_BRP_250K      BRP_SCALER_5
  #define SPI_BRP_208K      BRP_SCALER_3
  #define SPI_BRP_178K      BRP_SCALER_7
  #define SPI_BRP_125K      BRP_SCALER_5
  #define SPI_BRP_62500     BRP_SCALER_5
  #define SPI_BRP_31250     BRP_SCALER_5

  #define SPI_BR_8M         BR_SCALER_2 
  #define SPI_BR_4M         BR_SCALER_4 
  #define SPI_BR_2M         BR_SCALER_8 
  #define SPI_BR_1M         BR_SCALER_16 
  #define SPI_BR_500K       BR_SCALER_32 
  #define SPI_BR_250K       BR_SCALER_64
  #define SPI_BR_208K       BR_SCALER_128
  #define SPI_BR_178K       BR_SCALER_64
  #define SPI_BR_125K       BR_SCALER_128
  #define SPI_BR_62500      BR_SCALER_256
  #define SPI_BR_31250      BR_SCALER_512
#elif (FSYS==60)
  #define DBR_VAL_6M        0
  #define DBR_VAL_4M        1
  #define DBR_VAL_2M        0
  #define DBR_VAL_1250K     0
  #define DBR_VAL_750K      0
  #define DBR_VAL_187K      0
  
  #define SPI_BRP_6M        BRP_SCALER_5
  #define SPI_BRP_4M        BRP_SCALER_5
  #define SPI_BRP_2M        BRP_SCALER_5
  #define SPI_BRP_1250K     BRP_SCALER_3
  #define SPI_BRP_750K      BRP_SCALER_5  
  #define SPI_BRP_187K      BRP_SCALER_5

  #define SPI_BR_6M         BR_SCALER_2 
  #define SPI_BR_4M         BR_SCALER_6 
  #define SPI_BR_2M         BR_SCALER_6
  #define SPI_BR_1250K      BR_SCALER_16 
  #define SPI_BR_750K       BR_SCALER_16 
  #define SPI_BR_187K       BR_SCALER_64       

#endif


/* DSPI Channel Pointers */

#define SPI_CH_A 0
#define SPI_CH_B 1
#define SPI_CH_C 2
#define SPI_CH_D 3

#define SPI_NUM_OF_CS 6

/*#define TX_BUFFER_SIZE 16   */
#define TX_BUFFER_SIZE 36

/*#define RX_BUFFER_SIZE TX_BUFFER_SIZE */
/*#define RX_BUFFER_SIZE 16             */
#define RX_BUFFER_SIZE 36

#define SPI_WRONG_SIZE -5
