@echo off

set TGT_PATH=%CD%
echo target path: %TGT_PATH%

cd ..
set BIN_PATH=%CD%

echo bin path: %BIN_PATH%

cd ..\..
set PRJ_PATH=%CD%
echo proj path: %PRJ_PATH%
set NAME=M3M46R

set NAME_FILE=%NAME%.elf
echo %NAME_FILE%

set ELF_FILE="%TGT_PATH%\%NAME_FILE%"

set ELF_FILE=%ELF_FILE:"=%

set ELF_FILE="%ELF_FILE%"
echo %ELF_FILE%

set LOG_FILE="%BIN_PATH%\AAU_log.txt"

set ASAP_IN_FILE="%PRJ_PATH%\AAU\EldorECU.a2l"

set ASAP_OUT_FILE=%ELF_FILE:.elf=.a2l%

set MAP_FILE=%ELF_FILE:.elf=.map% 
echo %MAP_FILE%
echo %BIN_PATH%
rem cd %BIN_PATH%
cd %TGT_PATH%

"%PRJ_PATH%\AAU\elf" %ELF_FILE%

set MAP_FILE="%TGT_PATH%\out.map"

"%PRJ_PATH%\AAU\aau" -i %ASAP_IN_FILE% -o %ASAP_OUT_FILE% -m %MAP_FILE% -r -c metrowerks -l %LOG_FILE% -s -y

del out.map prmfile.txt prmfile1.txt prmfile2.txt swvercal.txt
pause
