#**************************************************************************/
#* FILE NAME: intc_sw_handlers.s            COPYRIGHT (c) Freescale 2004  */
#*                                                All Rights Reserved     */
#* DESCRIPTION:                                                           */
#*        This file creates prolog, epilog for C ISR and enables nested   */
#*        interrupts. This file starts in memory at the beginning of the  */
#*        ".xcptn" section designated by the label "IVOR4Handler".        */
#* WARNING:  This stack frame does not save the SPE s Accumulator, which  */
#*           is required if SPE instructions are used in ISRs.   If SPE   */
#*           instructions are used, the stack frame must include the      */
#*           accumulator, and prologue and epilogue must be modified.     */
#=========================================================================*/
#*                                                                        */
#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
#* ---   -----------   ----------   ---------------------                 */
#* 1.0:  S. Mihalik    23/Apr/04     Initial version                      */
#* 1.1:  B. Terry      29/Jul/04    Modified read of IACKR using          */
#*                                  pointer to determine vector  address. */
#* 1.2   G. Jackson    30/Jul/04    Added ".xcptn" section designation    */
#*                                   for placement into mpc5500cfg.       */
#* 1.3   G. Jackson    12/Oct/04    Green Hills now does not require      */
#*                                    quotation marks around the section  */
#*                                  Added syntax to generate symbols for  */
#*                                    debug.                              */
#**************************************************************************/

# STACK FRAME DESIGN: Depth: 20 words (0x50, or 80 bytes)
#            ************* ______________
#   0x4C     *  GPR12    *    ^
#   0x48     *  GPR11    *    |
#   0x44     *  GPR10    *    |
#   0x40     *  GPR9     *    |
#   0x3C     *  GPR8     *    |
#   0x38     *  GPR7     *  GPRs (32 bit)
#   0x34     *  GPR6     *    |
#   0x30     *  GPR5     *    |
#   0x2C     *  GPR4     *    |
#   0x28     *  GPR3     *    |
#   0x24     *  GPR0     * ___v__________
#   0x20     *  CR       * __CR__________
#   0x1C     *  XER      *    ^
#   0x18     *  CTR      *    |
#   0x14     *  LR       * locals & padding for 16 B alignment
#   0x10     *  SRR1	 *    |
#   0x0C     *  SRR0     *    |
#   0x08     *  padding  * ___v__________
#   0x04     * resvd- LR * Reserved for calling function 
#   0x00     *  SP       * Backchain (same as gpr1 in GPRs)
#            ************* 

    .include "mpc5500_usrdefs.inc"
		.include "wdt.cfg"
		
    .globl   IVOR4Handler
    .ifdef WDT_PSR_INTH	      # defined in WDT.cfg file
    .globl   IVOR12Handler
    .extern  WDT_ISR		  # defined in WDT_Events.c file
    .endif

#################################################
#       This is the start of the .xcptn section.

    .if __PEGNU__
    .section ".xcptn","ax"    # The "ax" is required to generate "non-text" code
    .align 4                  # Align IVOR handlers on a 16 byte boundary 
    .endif

    .if __GRNHS__ 
    .section .xcptn,ax        # The "ax" generates symbols for debug
    .align 4                  # Align IVOR handlers on a 16 byte boundary 
    .endif



    .if __DIABCC__
    .section .xcptn,c         # The "c" generates symbols for debug
    .align 4                  # Align IVOR handlers on a 16 byte boundary 
    .endif

    .if __CWWRKS__
    .section .xcptn           # The "text" can only generate symbols for
#                             #  one user defined section.
#                             # Align IVOR handlers on a 16 byte boundary 
    .align 16                 # CodeWarrior requires an .align 16
#                             # Other compilers require an .align 4 for (2**4)
    .endif

    .equ  INTC_IACKR, 0xfff48010  # Interrupt Acknowledge Register address
    .equ  INTC_EOIR,  0xfff48018  # End Of Interrupt Register address

IVOR4Handler:
prolog:                           # PROLOGUE 
    stwu   r1, -0x50 (r1)         # Create stack frame and store back chain
    stw    r0, 0x24 (r1)          # Save working registers R0
    mfSRR1 r0                     # Store SRR1 (must be done before enabling EE)
    stw    r0, 0x10 (r1)
    mfSRR0 r0                     # Store SRR0 (must be done before enabling EE)
    stw    r0, 0x0C (r1)
    mfLR   r0              # Store LR (Store now since LR will be used for ISR Vector)
    stw    r0, 0x14 (r1)

    stw    r3, 0x28 (r1)          # Store a working register

    lis    r3, INTC_IACKR@h       # Store address of IACKR in r3
    ori    r3, r3, INTC_IACKR@l
    lwz    r3, 0(r3)              # Store contents of IACKR in r3 (this is vector table
#                                 # address)
    lwz    r0, 0(r3)              # Read ISR address from ISR Vector Table address

    mtLR   r0                     # Store ISR address to LR to use for branching later

#    lis    r0, 0x02008200@h
#    ori   r0,r0,0x02008200@l
#    mtmsr  r0
    wrteei 1         # Set MSR[EE]=1 (must wait a couple clocks after reading IACKR)
    stw    r12, 0x4C (r1)         # Store rest of gprs
    stw    r11, 0x48 (r1)
    stw    r10, 0x44 (r1)
    stw    r9,  0x40 (r1)
    stw    r8,  0x3C (r1)
    stw    r7,  0x38 (r1)
    stw    r6,  0x34 (r1)
    stw    r5,  0x30 (r1)
    stw    r4,  0x2c (r1)  
	
    mfCR   r0                      # Store CR
    stw    r0,  0x20 (r1)
    mfXER  r0                      # Store XER
    stw    r0,  0x1C (r1)
    mfCTR  r0                      # Store CTR
    stw    r0,  0x18 (r1)
								
    blrl                           # Branch to ISR, but return here

epilog:                            # EPILOGUE

#                                  # STEP 6 :  RESTORE CONTEXT
    mbar 0                         # Ensure interrupt flag has finished clearing
#                                  # before writing to INTC_EIOR

	
    lwz    r0, 0x14 (r1)           # Restore LR
    mtLR   r0
    lwz    r0, 0x18 (r1)           # Restore CTR
    mtCTR  r0
    lwz    r0, 0x1C (r1)           # Restore XER
    mtXER  r0
    lwz    r0, 0x20 (r1)           # Restore CR
    mtcrf  0xff, r0
    lwz    r5,  0x30 (r1)
    lwz    r6,  0x34 (r1)
    lwz    r7,  0x38 (r1)
    lwz    r8,  0x3C (r1)
    lwz    r9,  0x40 (r1)
    lwz    r10, 0x44 (r1)
    lwz    r11, 0x48 (r1)
    lwz    r12, 0x4C (r1)

    wrteei 0                # Disable interrupts

    li     r3,0		
    lis   r4, INTC_EOIR@ha      # Load upper half of EIOR address to r4
    addi  r4, r4, INTC_EOIR@l   # Load lower half of EIOR address to R4
    stw   r3, 0(r4)             # Write 0 to INTC_EOIR, address 0xFFF4 8018

    lwz    r3,  0x28 (r1)
    lwz    r4,  0x2C (r1)

    lwz    r0,  0x0C (r1)   # Restore SRR0
    mtSRR0 r0
    lwz    r0,  0x10 (r1)   # Restore SRR1
    mtSRR1 r0
    lwz    r0,  0x24 (r1)   # Restore working register
    addi   r1,  r1, 0x50    # Restore space on stack

    rfi                     # End of Interrupt - re-enables interrupts.



		.ifdef  WDT_PSR_INTH  # defined in WDT.cfg file                       
    .if __CWWRKS__ 
    .section .xcptn2          # The "text" can only generate symbols for
#                             #  one user defined section.
#                             # Align IVOR handlers on a 16 byte boundary 
    .align 16                 # CodeWarrior requires an .align 16
#                             # Other compilers require an .align 4 for (2**4)
    .endif

IVOR12Handler:
prolog1:               # PROLOGUE		 
# save context (might be meaningless here)
     stwu r1, -16(r1)  # allocate 16 bytes on stack
     stw r6, 4(r1)     # save r6 on stack so it can be used in the handler
     mfcsrr0 r6        # get CSRR0
     stw r6, 8(r1)     # save it on the stack
     mfcsrr1 r6        # get CSRR1
     stw r6, 12(r1)    # save it on the stack

#     lis  r6,WDT_ISR@ha
#     addi r6,r6,WDT_ISR@l
#     mtlr r6
#     blrl

	  bl  WDT_ISR
	 
#     lis  r6,0x4000
#     mtspr TSR,r6


epilog1:               # EPILOGUE

# restore context
     lwz r6, 12(r1)    # get CSRR1 off stack
     mtcsrr1 r6        # restore it
     lwz r6, 8(r1)     # get CSRR0 off stack
     mtcsrr0 r6        # restore it
     lwz r6, 4(r1)     # get r6 off stack
     addi r1, r1, 16   # restore stack pointer
# return from critical interrupt -
     rfci              # restores machine state, including reenabling
                       # critical interrupts MSR[CE]. 
    
    .endif #WDT_PSR_INTH      