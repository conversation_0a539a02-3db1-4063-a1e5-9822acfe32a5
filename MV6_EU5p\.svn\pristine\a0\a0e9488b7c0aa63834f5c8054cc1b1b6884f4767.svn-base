#ifndef _SPIMGM_H_
#define _SPIMGM_H_

// PUBLIC METHODS:
//
void SPIMGM_Init(void); 
void SPIMGM_Init_BOOT(void); 

void SPI_ExTxDoneChA_PCS0 (void) ;
void SPI_ExTxDoneChA_PCS1 (void) ;
void SPI_ExTxDoneChA_PCS2 (void) ;
void SPI_ExTxDoneChA_PCS3 (void) ;
void SPI_ExTxDoneChA_PCS4 (void) ;
void SPI_ExTxDoneChA_PCS5 (void) ;

void SPI_ExTxDoneChB_PCS0 (void) ;
void SPI_ExTxDoneChB_PCS1 (void) ;
void SPI_ExTxDoneChB_PCS2 (void) ;
void SPI_ExTxDoneChB_PCS3 (void) ;
void SPI_ExTxDoneChB_PCS4 (void) ;
void SPI_ExTxDoneChB_PCS5 (void) ;

void SPI_ExTxDoneChC_PCS0 (void) ;
void SPI_ExTxDoneChC_PCS1 (void) ;
void SPI_ExTxDoneChC_PCS2 (void) ;
void SPI_ExTxDoneChC_PCS3 (void) ;
void SPI_ExTxDoneChC_PCS4 (void) ;
void SPI_ExTxDoneChC_PCS5 (void) ;

void SPI_ExTxDoneChD_PCS0 (void) ;
void SPI_ExTxDoneChD_PCS1 (void) ;
void SPI_ExTxDoneChD_PCS2 (void) ;
void SPI_ExTxDoneChD_PCS3 (void) ;
void SPI_ExTxDoneChD_PCS4 (void) ;
void SPI_ExTxDoneChD_PCS5 (void) ;

#endif //_SPIMGM_H_