#ifdef _BUILD_DIAGCANMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "diagcanmgm.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08) || (ENGINE_TYPE == MV_AGUSTA_4C) || (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
const ECUcodeStrucTagID1     ECUcodeID1 =
{
    "DEFAULT   ",          /* 0x98 - TESTER CODE */
    {0x20,0x11,0x01,0x31}  /* 0x99 - DATAPROD */
};
#else
#error KWP not implemented for this target
#endif

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _BUILD_DIAGCANMGM_ */
