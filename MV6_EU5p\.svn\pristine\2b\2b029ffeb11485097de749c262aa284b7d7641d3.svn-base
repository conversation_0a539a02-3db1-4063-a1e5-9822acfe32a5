/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef  _BUILD_CAN_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include <string.h>
#include "sys.h"
#include "CAN.h"
#include "timing.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
const struct FLEXCAN2_tag *const CAN_Channel[3]= 
{ 
    (const struct FLEXCAN2_tag *)&CAN_A, 
#if (TARGET_TYPE == MPC5554)
    (const struct FLEXCAN2_tag *)&CAN_B,
#else
    (const struct FLEXCAN2_tag *)NULL,
#endif 
    (const struct FLEXCAN2_tag *)&CAN_C
};


const uint8_t CAN_ChannelMBlen[3]= 
{ 
    NUM_BUFFER_FLX_CAN_A, 
    NUM_BUFFER_FLX_CAN_B,
    NUM_BUFFER_FLX_CAN_C
};

void (*ExTxDoneChA)(void);
void (*ExRxDoneChA)(void);
void (*ExTxDoneChC)(void);
void (*ExRxDoneChC)(void);
   
bool CAN_ConfigStatus;

#if CAN_CHA_EN
t_CAN_CHA_b CAN_CHA_b; /* CCP CAN channel */ 
#endif
t_CAN_CHC_b CAN_CHC_b; /* KWP CAN channel */



uint8_T rPtrValue,wPtrValue;


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define CAN_METH_FILE

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
#define BUF_CFG(ch,buf) CAN_BufferConfig(FLEXCAN_##ch,buf,CAN_CH##ch##_BUF##buf##_DIR,CAN_CH##ch##_BUF##buf##_DLC,CAN_CH##ch##_BUF##buf##_IDE,CAN_CH##ch##_BUF##buf##_LID)


/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CAN_ConfigCh - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t CAN_ConfigCh (uint8_t ch, 
                              uint8_t brate,
                              uint32_t gmask);

/*--------------------------------------------------------------------------*
 * CAN_BufferConfig - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t CAN_BufferConfig (uint8_t ch, 
                                  uint8_t nbuf, 
                                  uint8_t dir, 
                                  uint8_t siz, 
                                  uint8_t ide, 
                                  uint32_t id);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/**************************************************************************************/
/* FUNCTION     : CAN_EX_Tx_Rx_Config                                                 */
/* PURPOSE      : This function sets CAN function exeption                            */
/* INPUTS NOTES :                                                                     */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/* RETURN NOTES :                                                                     */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/* WARNING      :                                                                     */
/*                                                                                    */
/**************************************************************************************/
void CAN_EX_Tx_Rx_Config(uint8_t channel,
                         void (*TX_Func_Ex)(void),
                         void (*RX_Func_Ex)(void))
{
    switch(channel)
    {
        case FLEXCAN_A :
        {
            ExTxDoneChA=TX_Func_Ex;
            ExRxDoneChA=RX_Func_Ex;
            break;
        }
#if (TARGET_TYPE == MPC5554)
        case FLEXCAN_B :
        {
            ExTxDoneChB=TX_Func_Ex;
            ExRxDoneChB=RX_Func_Ex;
            break;
        }
#endif
        case FLEXCAN_C :
        {
            ExTxDoneChC=TX_Func_Ex;
            ExRxDoneChC=RX_Func_Ex;
            break;
        }
        default:
        {
            break;
        }
    }
}

/**************************************************************************************/
/* FUNCTION     : CAN_config (Id_Channel Channel)                                     */
/* PURPOSE      : This function performs the main configuration of the FLEXCAN        */
/* INPUTS NOTES : FlexCan module and a pointer to structure encloses the configuration*/
/*                parameters                                                          */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/* RETURN NOTES :                                                                     */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/* WARNING      :                                                                     */
/*                                                                                    */
/**************************************************************************************/
int16_t CAN_Config (void)
{

    int16_t res=NO_ERROR;

    if(CAN_ConfigStatus==0)
    {
        CAN_ConfigStatus=1;
#if CAN_CHA_EN

        SYS_OutPinConfig(CNTXA, PRIMARY_FUNCTION, INPUT_DISABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MEDIUM_SLEW_RATE);
        SYS_InPinConfig(CNRXA, PRIMARY_FUNCTION, DISABLE_HYSTERESIS, WEAK_PULL_UP);

        CAN_ConfigCh(FLEXCAN_A,CAN_CHA_BR,CAN_CHA_GLB_MSK);

#ifdef CAN_CHA_BUF0_DIR
        BUF_CFG(A,0);
#if (CAN_CHA_BUF0_DIR==CAN_RX)
        INIT_BS(A,0)
#endif
#endif
#ifdef CAN_CHA_BUF1_DIR
        BUF_CFG(A,1);
#if (CAN_CHA_BUF1_DIR==CAN_RX)
        INIT_BS(A,1)
#endif
#endif
#ifdef CAN_CHA_BUF2_DIR
        BUF_CFG(A,2);
#if (CAN_CHA_BUF2_DIR==CAN_RX)
        INIT_BS(A,2)
#endif
#endif
#ifdef CAN_CHA_BUF3_DIR
        BUF_CFG(A,3);
#if (CAN_CHA_BUF3_DIR==CAN_RX)
        INIT_BS(A,3)
#endif
#endif
#ifdef CAN_CHA_BUF4_DIR
        BUF_CFG(A,4);
#if (CAN_CHA_BUF4_DIR==CAN_RX)
        INIT_BS(A,4)
#endif
#endif
#ifdef CAN_CHA_BUF5_DIR
        BUF_CFG(A,5);
#if (CAN_CHA_BUF5_DIR==CAN_RX)
        INIT_BS(A,5)
#endif
#endif
#ifdef CAN_CHA_BUF6_DIR
        BUF_CFG(A,6);
#if (CAN_CHA_BUF6_DIR==CAN_RX)
        INIT_BS(A,6)
#endif
#endif
#ifdef CAN_CHA_BUF7_DIR
        BUF_CFG(A,7);
#if (CAN_CHA_BUF7_DIR==CAN_RX)
        INIT_BS(A,7)
#endif
#endif
#ifdef CAN_CHA_BUF8_DIR
        BUF_CFG(A,8);
#if (CAN_CHA_BUF8_DIR==CAN_RX)
        INIT_BS(A,8)
#endif
#endif
#ifdef CAN_CHA_BUF9_DIR
        BUF_CFG(A,9);
#if (CAN_CHA_BUF9_DIR==CAN_RX)
        INIT_BS(A,9)
#endif
#endif
#ifdef CAN_CHA_BUF10_DIR
        BUF_CFG(A,10);
#if (CAN_CHA_BUF10_DIR==CAN_RX)
        INIT_BS(A,10)
#endif
#endif
#ifdef CAN_CHA_BUF11_DIR
        BUF_CFG(A,11);
#if (CAN_CHA_BUF11_DIR==CAN_RX)
        INIT_BS(A,11)
#endif
#endif
#ifdef CAN_CHA_BUF12_DIR
        BUF_CFG(A,12);
#if (CAN_CHA_BUF12_DIR==CAN_RX)
        INIT_BS(A,12)
#endif
#endif
#ifdef CAN_CHA_BUF13_DIR
        BUF_CFG(A,13);
#if (CAN_CHA_BUF13_DIR==CAN_RX)
        INIT_BS(A,13)
#endif
#endif
#ifdef CAN_CHA_BUF14_DIR
        BUF_CFG(A,14);
#if (CAN_CHA_BUF14_DIR==CAN_RX)
        INIT_BS(A,14)
#endif
#endif
#ifdef CAN_CHA_BUF15_DIR
        BUF_CFG(A,15);
#if (CAN_CHA_BUF15_DIR==CAN_RX)
        INIT_BS(A,15)
#endif
#endif
#ifdef CAN_CHA_BUF16_DIR
        BUF_CFG(A,16);
#if (CAN_CHA_BUF16_DIR==CAN_RX)
        INIT_BS(A,16)
#endif
#endif
#ifdef CAN_CHA_BUF17_DIR
        BUF_CFG(A,17);
#if (CAN_CHA_BUF17_DIR==CAN_RX)
        INIT_BS(A,17)
#endif
#endif
#ifdef CAN_CHA_BUF18_DIR
        BUF_CFG(A,18);
#if (CAN_CHA_BUF18_DIR==CAN_RX)
        INIT_BS(A,18)
#endif
#endif
#ifdef CAN_CHA_BUF19_DIR
        BUF_CFG(A,19);
#if (CAN_CHA_BUF19_DIR==CAN_RX)
        INIT_BS(A,19)
#endif
#endif
#ifdef CAN_CHA_BUF20_DIR
        BUF_CFG(A,20);
#if (CAN_CHA_BUF20_DIR==CAN_RX)
        INIT_BS(A,20)
#endif
#endif
#ifdef CAN_CHA_BUF21_DIR
        BUF_CFG(A,21);
#if (CAN_CHA_BUF21_DIR==CAN_RX)
        INIT_BS(A,21)
#endif
#endif
#ifdef CAN_CHA_BUF22_DIR
        BUF_CFG(A,22);
#if (CAN_CHA_BUF22_DIR==CAN_RX)
        INIT_BS(A,22)
#endif
#endif
#ifdef CAN_CHA_BUF23_DIR
        BUF_CFG(A,23);
#if (CAN_CHA_BUF23_DIR==CAN_RX)
        INIT_BS(A,23)
#endif
#endif
#ifdef CAN_CHA_BUF24_DIR
        BUF_CFG(A,24);
#if (CAN_CHA_BUF24_DIR==CAN_RX)
        INIT_BS(A,24)
#endif
#endif
#ifdef CAN_CHA_BUF25_DIR
        BUF_CFG(A,25);
#if (CAN_CHA_BUF25_DIR==CAN_RX)
        INIT_BS(A,25)
#endif
#endif
#ifdef CAN_CHA_BUF26_DIR
        BUF_CFG(A,26);
#if (CAN_CHA_BUF26_DIR==CAN_RX)
        INIT_BS(A,26)
#endif
#endif
#ifdef CAN_CHA_BUF27_DIR
        BUF_CFG(A,27);
#if (CAN_CHA_BUF27_DIR==CAN_RX)
        INIT_BS(A,27)
#endif
#endif
#ifdef CAN_CHA_BUF28_DIR
        BUF_CFG(A,28);
#if (CAN_CHA_BUF28_DIR==CAN_RX)
        INIT_BS(A,28)
#endif
#endif
#ifdef CAN_CHA_BUF29_DIR
        BUF_CFG(A,29);
#if (CAN_CHA_BUF29_DIR==CAN_RX)
        INIT_BS(A,29)
#endif
#endif
#ifdef CAN_CHA_BUF30_DIR
        BUF_CFG(A,30);
#if (CAN_CHA_BUF30_DIR==CAN_RX)
        INIT_BS(A,30)
#endif
#endif
#ifdef CAN_CHA_BUF31_DIR
        BUF_CFG(A,31);
#if (CAN_CHA_BUF31_DIR==CAN_RX)
        INIT_BS(A,31)
#endif
#endif
#ifdef CAN_CHA_BUF32_DIR
        BUF_CFG(A,32);
#if (CAN_CHA_BUF32_DIR==CAN_RX)
        INIT_BS(A,32)
#endif
#endif
#ifdef CAN_CHA_BUF33_DIR
        BUF_CFG(A,33);
#if (CAN_CHA_BUF33_DIR==CAN_RX)
        INIT_BS(A,33)
#endif
#endif
#ifdef CAN_CHA_BUF34_DIR
        BUF_CFG(A,34);
#if (CAN_CHA_BUF34_DIR==CAN_RX)
        INIT_BS(A,34)
#endif
#endif
#ifdef CAN_CHA_BUF35_DIR
        BUF_CFG(A,35);
#if (CAN_CHA_BUF35_DIR==CAN_RX)
        INIT_BS(A,35)
#endif
#endif
#ifdef CAN_CHA_BUF36_DIR
        BUF_CFG(A,36);
#if (CAN_CHA_BUF36_DIR==CAN_RX)
        INIT_BS(A,36)
#endif
#endif
#ifdef CAN_CHA_BUF37_DIR
        BUF_CFG(A,37);
#if (CAN_CHA_BUF37_DIR==CAN_RX)
        INIT_BS(A,37)
#endif
#endif
#ifdef CAN_CHA_BUF38_DIR
        BUF_CFG(A,38);
#if (CAN_CHA_BUF38_DIR==CAN_RX)
        INIT_BS(A,38)
#endif
#endif
#ifdef CAN_CHA_BUF39_DIR
        BUF_CFG(A,39);
#if (CAN_CHA_BUF39_DIR==CAN_RX)
        INIT_BS(A,39)
#endif
#endif
#ifdef CAN_CHA_BUF40_DIR
        BUF_CFG(A,40);
#if (CAN_CHA_BUF40_DIR==CAN_RX)
        INIT_BS(A,40)
#endif
#endif
#ifdef CAN_CHA_BUF41_DIR
        BUF_CFG(A,41);
#if (CAN_CHA_BUF41_DIR==CAN_RX)
        INIT_BS(A,41)
#endif
#endif
#ifdef CAN_CHA_BUF42_DIR
        BUF_CFG(A,42);
#if (CAN_CHA_BUF42_DIR==CAN_RX)
        INIT_BS(A,42)
#endif
#endif
#ifdef CAN_CHA_BUF43_DIR
        BUF_CFG(A,43);
#if (CAN_CHA_BUF43_DIR==CAN_RX)
        INIT_BS(A,43)
#endif
#endif
#ifdef CAN_CHA_BUF44_DIR
        BUF_CFG(A,44);
#if (CAN_CHA_BUF44_DIR==CAN_RX)
        INIT_BS(A,44)
#endif
#endif
#ifdef CAN_CHA_BUF45_DIR
        BUF_CFG(A,45);
#if (CAN_CHA_BUF45_DIR==CAN_RX)
        INIT_BS(A,45)
#endif
#endif
#ifdef CAN_CHA_BUF46_DIR
        BUF_CFG(A,46);
#if (CAN_CHA_BUF46_DIR==CAN_RX)
        INIT_BS(A,46)
#endif
#endif
#ifdef CAN_CHA_BUF47_DIR
        BUF_CFG(A,47);
#if (CAN_CHA_BUF47_DIR==CAN_RX)
        INIT_BS(A,47)
#endif
#endif
#ifdef CAN_CHA_BUF48_DIR
        BUF_CFG(A,48);
#if (CAN_CHA_BUF48_DIR==CAN_RX)
        INIT_BS(A,48)
#endif
#endif
#ifdef CAN_CHA_BUF49_DIR
        BUF_CFG(A,49);
#if (CAN_CHA_BUF49_DIR==CAN_RX)
        INIT_BS(A,49)
#endif
#endif
#ifdef CAN_CHA_BUF50_DIR
        BUF_CFG(A,50);
#if (CAN_CHA_BUF50_DIR==CAN_RX)
        INIT_BS(A,50)
#endif
#endif
#ifdef CAN_CHA_BUF51_DIR
        BUF_CFG(A,51);
#if (CAN_CHA_BUF51_DIR==CAN_RX)
        INIT_BS(A,51)
#endif
#endif
#ifdef CAN_CHA_BUF52_DIR
        BUF_CFG(A,52);
#if (CAN_CHA_BUF52_DIR==CAN_RX)
        INIT_BS(A,52)
#endif
#endif
#ifdef CAN_CHA_BUF53_DIR
        BUF_CFG(A,53);
#if (CAN_CHA_BUF53_DIR==CAN_RX)
        INIT_BS(A,53)
#endif
#endif
#ifdef CAN_CHA_BUF54_DIR
        BUF_CFG(A,54);
#if (CAN_CHA_BUF54_DIR==CAN_RX)
        INIT_BS(A,54)
#endif
#endif
#ifdef CAN_CHA_BUF55_DIR
        BUF_CFG(A,55);
#if (CAN_CHA_BUF55_DIR==CAN_RX)
        INIT_BS(A,55)
#endif
#endif
#ifdef CAN_CHA_BUF56_DIR
        BUF_CFG(A,56);
#if (CAN_CHA_BUF56_DIR==CAN_RX)
        INIT_BS(A,56)
#endif
#endif
#ifdef CAN_CHA_BUF57_DIR
        BUF_CFG(A,57);
#if (CAN_CHA_BUF57_DIR==CAN_RX)
        INIT_BS(A,57)
#endif
#endif
#ifdef CAN_CHA_BUF58_DIR
        BUF_CFG(A,58);
#if (CAN_CHA_BUF58_DIR==CAN_RX)
        INIT_BS(A,58)
#endif
#endif
#ifdef CAN_CHA_BUF59_DIR
        BUF_CFG(A,59);
#if (CAN_CHA_BUF59_DIR==CAN_RX)
        INIT_BS(A,59)
#endif
#endif
#ifdef CAN_CHA_BUF60_DIR
        BUF_CFG(A,60);
#if (CAN_CHA_BUF60_DIR==CAN_RX)
        INIT_BS(A,60)
#endif
#endif
#ifdef CAN_CHA_BUF61_DIR
        BUF_CFG(A,61);
#if (CAN_CHA_BUF61_DIR==CAN_RX)
        INIT_BS(A,61)
#endif
#endif
#ifdef CAN_CHA_BUF62_DIR
        BUF_CFG(A,62);
#if (CAN_CHA_BUF62_DIR==CAN_RX)
        INIT_BS(A,62)
#endif
#endif
#ifdef CAN_CHA_BUF63_DIR
        BUF_CFG(A,63);
#if (CAN_CHA_BUF63_DIR==CAN_RX)
        INIT_BS(A,63)
#endif
#endif

#endif

#if (TARGET_TYPE == MPC5554)

#if CAN_CHB_EN

        SYS_OutPinConfig(CNTXB, PRIMARY_FUNCTION, INPUT_DISABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MEDIUM_SLEW_RATE); /* PUSH-PULL enabled!! */
        SYS_InPinConfig(CNRXB, PRIMARY_FUNCTION, DISABLE_HYSTERESIS, WEAK_PULL_UP); /* weak-pull up enabled !! */

        CAN_ConfigCh(FLEXCAN_B,CAN_CHB_BR,CAN_CHB_GLB_MSK);

#ifdef CAN_CHB_BUF0_DIR
        BUF_CFG(B,0);
#if (CAN_CHB_BUF0_DIR==CAN_RX)
        INIT_BS(B,0)
#endif
#endif
#ifdef CAN_CHB_BUF1_DIR
        BUF_CFG(B,1);
#if (CAN_CHB_BUF1_DIR==CAN_RX)
        INIT_BS(B,1)
#endif
#endif
#ifdef CAN_CHB_BUF2_DIR
        BUF_CFG(B,2);
#if (CAN_CHB_BUF2_DIR==CAN_RX)
        INIT_BS(B,2)
#endif
#endif
#ifdef CAN_CHB_BUF3_DIR
        BUF_CFG(B,3);
#if (CAN_CHB_BUF3_DIR==CAN_RX)
        INIT_BS(B,3)
#endif
#endif
#ifdef CAN_CHB_BUF4_DIR
        BUF_CFG(B,4);
#if (CAN_CHB_BUF4_DIR==CAN_RX)
        INIT_BS(B,4)
#endif
#endif
#ifdef CAN_CHB_BUF5_DIR
        BUF_CFG(B,5);
#if (CAN_CHB_BUF5_DIR==CAN_RX)
        INIT_BS(B,5)
#endif
#endif
#ifdef CAN_CHB_BUF6_DIR
        BUF_CFG(B,6);
#if (CAN_CHB_BUF6_DIR==CAN_RX)
        INIT_BS(B,6)
#endif
#endif
#ifdef CAN_CHB_BUF7_DIR
        BUF_CFG(B,7);
#if (CAN_CHB_BUF7_DIR==CAN_RX)
        INIT_BS(B,7)
#endif
#endif
#ifdef CAN_CHB_BUF8_DIR
        BUF_CFG(B,8);
#if (CAN_CHB_BUF8_DIR==CAN_RX)
        INIT_BS(B,8)
#endif
#endif
#ifdef CAN_CHB_BUF9_DIR
        BUF_CFG(B,9);
#if (CAN_CHB_BUF9_DIR==CAN_RX)
        INIT_BS(B,9)
#endif
#endif
#ifdef CAN_CHB_BUF10_DIR
        BUF_CFG(B,10);
#if (CAN_CHB_BUF10_DIR==CAN_RX)
        INIT_BS(B,10)
#endif
#endif
#ifdef CAN_CHB_BUF11_DIR
        BUF_CFG(B,11);
#if (CAN_CHB_BUF11_DIR==CAN_RX)
        INIT_BS(B,11)
#endif
#endif
#ifdef CAN_CHB_BUF12_DIR
        BUF_CFG(B,12);
#if (CAN_CHB_BUF12_DIR==CAN_RX)
        INIT_BS(B,12)
#endif
#endif
#ifdef CAN_CHB_BUF13_DIR
        BUF_CFG(B,13);
#if (CAN_CHB_BUF13_DIR==CAN_RX)
        INIT_BS(B,13)
#endif
#endif
#ifdef CAN_CHB_BUF14_DIR
        BUF_CFG(B,14);
#if (CAN_CHB_BUF14_DIR==CAN_RX)
        INIT_BS(B,14)
#endif
#endif
#ifdef CAN_CHB_BUF15_DIR
        BUF_CFG(B,15);
#if (CAN_CHB_BUF15_DIR==CAN_RX)
        INIT_BS(B,15)
#endif
#endif
#ifdef CAN_CHB_BUF16_DIR
        BUF_CFG(B,16);
#if (CAN_CHB_BUF16_DIR==CAN_RX)
        INIT_BS(B,16)
#endif
#endif
#ifdef CAN_CHB_BUF17_DIR
        BUF_CFG(B,17);
#if (CAN_CHB_BUF17_DIR==CAN_RX)
        INIT_BS(B,17)
#endif
#endif
#ifdef CAN_CHB_BUF18_DIR
        BUF_CFG(B,18);
#if (CAN_CHB_BUF18_DIR==CAN_RX)
        INIT_BS(B,18)
#endif
#endif
#ifdef CAN_CHB_BUF19_DIR
        BUF_CFG(B,19);
#if (CAN_CHB_BUF19_DIR==CAN_RX)
        INIT_BS(B,19)
#endif
#endif
#ifdef CAN_CHB_BUF20_DIR
        BUF_CFG(B,20);
#if (CAN_CHB_BUF20_DIR==CAN_RX)
        INIT_BS(B,20)
#endif
#endif
#ifdef CAN_CHB_BUF21_DIR
        BUF_CFG(B,21);
#if (CAN_CHB_BUF21_DIR==CAN_RX)
        INIT_BS(B,21)
#endif
#endif
#ifdef CAN_CHB_BUF22_DIR
        BUF_CFG(B,22);
#if (CAN_CHB_BUF22_DIR==CAN_RX)
        INIT_BS(B,22)
#endif
#endif
#ifdef CAN_CHB_BUF23_DIR
        BUF_CFG(B,23);
#if (CAN_CHB_BUF23_DIR==CAN_RX)
        INIT_BS(B,23)
#endif
#endif
#ifdef CAN_CHB_BUF24_DIR
        BUF_CFG(B,24);
#if (CAN_CHB_BUF24_DIR==CAN_RX)
        INIT_BS(B,24)
#endif
#endif
#ifdef CAN_CHB_BUF25_DIR
        BUF_CFG(B,25);
#if (CAN_CHB_BUF25_DIR==CAN_RX)
        INIT_BS(B,25)
#endif
#endif
#ifdef CAN_CHB_BUF26_DIR
        BUF_CFG(B,26);
#if (CAN_CHB_BUF26_DIR==CAN_RX)
        INIT_BS(B,26)
#endif
#endif
#ifdef CAN_CHB_BUF27_DIR
        BUF_CFG(B,27);
#if (CAN_CHB_BUF27_DIR==CAN_RX)
        INIT_BS(B,27)
#endif
#endif
#ifdef CAN_CHB_BUF28_DIR
        BUF_CFG(B,28);
#if (CAN_CHB_BUF28_DIR==CAN_RX)
        INIT_BS(B,28)
#endif
#endif
#ifdef CAN_CHB_BUF29_DIR
        BUF_CFG(B,29);
#if (CAN_CHB_BUF29_DIR==CAN_RX)
        INIT_BS(B,29)
#endif
#endif
#ifdef CAN_CHB_BUF30_DIR
        BUF_CFG(B,30);
#if (CAN_CHB_BUF30_DIR==CAN_RX)
        INIT_BS(B,30)
#endif
#endif
#ifdef CAN_CHB_BUF31_DIR
        BUF_CFG(B,31);
#if (CAN_CHB_BUF31_DIR==CAN_RX)
        INIT_BS(B,31)
#endif
#endif
#ifdef CAN_CHB_BUF32_DIR
        BUF_CFG(B,32);
#if (CAN_CHB_BUF32_DIR==CAN_RX)
        INIT_BS(B,32)
#endif
#endif
#ifdef CAN_CHB_BUF33_DIR
        BUF_CFG(B,33);
#if (CAN_CHB_BUF33_DIR==CAN_RX)
        INIT_BS(B,33)
#endif
#endif
#ifdef CAN_CHB_BUF34_DIR
        BUF_CFG(B,34);
#if (CAN_CHB_BUF34_DIR==CAN_RX)
        INIT_BS(B,34)
#endif
#endif
#ifdef CAN_CHB_BUF35_DIR
        BUF_CFG(B,35);
#if (CAN_CHB_BUF35_DIR==CAN_RX)
        INIT_BS(B,35)
#endif
#endif
#ifdef CAN_CHB_BUF36_DIR
        BUF_CFG(B,36);
#if (CAN_CHB_BUF36_DIR==CAN_RX)
        INIT_BS(B,36)
#endif
#endif
#ifdef CAN_CHB_BUF37_DIR
        BUF_CFG(B,37);
#if (CAN_CHB_BUF37_DIR==CAN_RX)
        INIT_BS(B,37)
#endif
#endif
#ifdef CAN_CHB_BUF38_DIR
        BUF_CFG(B,38);
#if (CAN_CHB_BUF38_DIR==CAN_RX)
        INIT_BS(B,38)
#endif
#endif
#ifdef CAN_CHB_BUF39_DIR
        BUF_CFG(B,39);
#if (CAN_CHB_BUF39_DIR==CAN_RX)
        INIT_BS(B,39)
#endif
#endif
#ifdef CAN_CHB_BUF40_DIR
        BUF_CFG(B,40);
#if (CAN_CHB_BUF40_DIR==CAN_RX)
        INIT_BS(B,40)
#endif
#endif
#ifdef CAN_CHB_BUF41_DIR
        BUF_CFG(B,41);
#if (CAN_CHB_BUF41_DIR==CAN_RX)
        INIT_BS(B,41)
#endif
#endif
#ifdef CAN_CHB_BUF42_DIR
        BUF_CFG(B,42);
#if (CAN_CHB_BUF42_DIR==CAN_RX)
        INIT_BS(B,42)
#endif
#endif
#ifdef CAN_CHB_BUF43_DIR
        BUF_CFG(B,43);
#if (CAN_CHB_BUF43_DIR==CAN_RX)
        INIT_BS(B,43)
#endif
#endif
#ifdef CAN_CHB_BUF44_DIR
        BUF_CFG(B,44);
#if (CAN_CHB_BUF44_DIR==CAN_RX)
        INIT_BS(B,44)
#endif
#endif
#ifdef CAN_CHB_BUF45_DIR
        BUF_CFG(B,45);
#if (CAN_CHB_BUF45_DIR==CAN_RX)
        INIT_BS(B,45)
#endif
#endif
#ifdef CAN_CHB_BUF46_DIR
        BUF_CFG(B,46);
#if (CAN_CHB_BUF46_DIR==CAN_RX)
        INIT_BS(B,46)
#endif
#endif
#ifdef CAN_CHB_BUF47_DIR
        BUF_CFG(B,47);
#if (CAN_CHB_BUF47_DIR==CAN_RX)
        INIT_BS(B,47)
#endif
#endif
#ifdef CAN_CHB_BUF48_DIR
        BUF_CFG(B,48);
#if (CAN_CHB_BUF48_DIR==CAN_RX)
        INIT_BS(B,48)
#endif
#endif
#ifdef CAN_CHB_BUF49_DIR
        BUF_CFG(B,49);
#if (CAN_CHB_BUF49_DIR==CAN_RX)
        INIT_BS(B,49)
#endif
#endif
#ifdef CAN_CHB_BUF50_DIR
        BUF_CFG(B,50);
#if (CAN_CHB_BUF50_DIR==CAN_RX)
        INIT_BS(B,50)
#endif
#endif
#ifdef CAN_CHB_BUF51_DIR
        BUF_CFG(B,51);
#if (CAN_CHB_BUF51_DIR==CAN_RX)
        INIT_BS(B,51)
#endif
#endif
#ifdef CAN_CHB_BUF52_DIR
        BUF_CFG(B,52);
#if (CAN_CHB_BUF52_DIR==CAN_RX)
        INIT_BS(B,52)
#endif
#endif
#ifdef CAN_CHB_BUF53_DIR
        BUF_CFG(B,53);
#if (CAN_CHB_BUF53_DIR==CAN_RX)
        INIT_BS(B,53)
#endif
#endif
#ifdef CAN_CHB_BUF54_DIR
        BUF_CFG(B,54);
#if (CAN_CHB_BUF54_DIR==CAN_RX)
        INIT_BS(B,54)
#endif
#endif
#ifdef CAN_CHB_BUF55_DIR
        BUF_CFG(B,55);
#if (CAN_CHB_BUF55_DIR==CAN_RX)
        INIT_BS(B,55)
#endif
#endif
#ifdef CAN_CHB_BUF56_DIR
        BUF_CFG(B,56);
#if (CAN_CHB_BUF56_DIR==CAN_RX)
        INIT_BS(B,56)
#endif
#endif
#ifdef CAN_CHB_BUF57_DIR
        BUF_CFG(B,57);
#if (CAN_CHB_BUF57_DIR==CAN_RX)
        INIT_BS(B,57)
#endif
#endif
#ifdef CAN_CHB_BUF58_DIR
        BUF_CFG(B,58);
#if (CAN_CHB_BUF58_DIR==CAN_RX)
        INIT_BS(B,58)
#endif
#endif
#ifdef CAN_CHB_BUF59_DIR
        BUF_CFG(B,59);
#if (CAN_CHB_BUF59_DIR==CAN_RX)
        INIT_BS(B,59)
#endif
#endif
#ifdef CAN_CHB_BUF60_DIR
        BUF_CFG(B,60);
#if (CAN_CHB_BUF60_DIR==CAN_RX)
        INIT_BS(B,60)
#endif
#endif
#ifdef CAN_CHB_BUF61_DIR
        BUF_CFG(B,61);
#if (CAN_CHB_BUF61_DIR==CAN_RX)
        INIT_BS(B,61)
#endif
#endif
#ifdef CAN_CHB_BUF62_DIR
        BUF_CFG(B,62);
#if (CAN_CHB_BUF62_DIR==CAN_RX)
        INIT_BS(B,62)
#endif
#endif
#ifdef CAN_CHB_BUF63_DIR
        BUF_CFG(B,63);
#if (CAN_CHB_BUF63_DIR==CAN_RX)
        INIT_BS(B,63)
#endif
#endif

#endif

#endif /* (TARGET_TYPE == MPC5554) */

#if CAN_CHC_EN

        SYS_OutPinConfig(CNTXC, PRIMARY_FUNCTION, INPUT_DISABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MEDIUM_SLEW_RATE);
        SYS_InPinConfig(CNRXC, PRIMARY_FUNCTION, DISABLE_HYSTERESIS, WEAK_PULL_UP);

        CAN_ConfigCh(FLEXCAN_C,CAN_CHC_BR,CAN_CHC_GLB_MSK);

#ifdef CAN_CHC_BUF0_DIR
        BUF_CFG(C,0);
#if (CAN_CHC_BUF0_DIR==CAN_RX)
        INIT_BS(C,0)
#endif
#endif
#ifdef CAN_CHC_BUF1_DIR
        BUF_CFG(C,1);
#if (CAN_CHC_BUF1_DIR==CAN_RX)
        INIT_BS(C,1)
#endif
#endif
#ifdef CAN_CHC_BUF2_DIR
        BUF_CFG(C,2);
#if (CAN_CHC_BUF2_DIR==CAN_RX)
        INIT_BS(C,2)
#endif
#endif
#ifdef CAN_CHC_BUF3_DIR
        BUF_CFG(C,3);
#if (CAN_CHC_BUF3_DIR==CAN_RX)
        INIT_BS(C,3)
#endif
#endif
#ifdef CAN_CHC_BUF4_DIR
        BUF_CFG(C,4);
#if (CAN_CHC_BUF4_DIR==CAN_RX)
        INIT_BS(C,4)
#endif
#endif
#ifdef CAN_CHC_BUF5_DIR
        BUF_CFG(C,5);
#if (CAN_CHC_BUF5_DIR==CAN_RX)
        INIT_BS(C,5)
#endif
#endif
#ifdef CAN_CHC_BUF6_DIR
        BUF_CFG(C,6);
#if (CAN_CHC_BUF6_DIR==CAN_RX)
        INIT_BS(C,6)
#endif
#endif
#ifdef CAN_CHC_BUF7_DIR
        BUF_CFG(C,7);
#if (CAN_CHC_BUF7_DIR==CAN_RX)
        INIT_BS(C,7)
#endif
#endif
#ifdef CAN_CHC_BUF8_DIR
        BUF_CFG(C,8);
#if (CAN_CHC_BUF8_DIR==CAN_RX)
        INIT_BS(C,8)
#endif
#endif
#ifdef CAN_CHC_BUF9_DIR
        BUF_CFG(C,9);
#if (CAN_CHC_BUF9_DIR==CAN_RX)
        INIT_BS(C,9)
#endif
#endif
#ifdef CAN_CHC_BUF10_DIR
        BUF_CFG(C,10);
#if (CAN_CHC_BUF10_DIR==CAN_RX)
        INIT_BS(C,10)
#endif
#endif
#ifdef CAN_CHC_BUF11_DIR
        BUF_CFG(C,11);
#if (CAN_CHC_BUF11_DIR==CAN_RX)
        INIT_BS(C,11)
#endif
#endif
#ifdef CAN_CHC_BUF12_DIR
        BUF_CFG(C,12);
#if (CAN_CHC_BUF12_DIR==CAN_RX)
        INIT_BS(C,12)
#endif
#endif
#ifdef CAN_CHC_BUF13_DIR
        BUF_CFG(C,13);
#if (CAN_CHC_BUF13_DIR==CAN_RX)
        INIT_BS(C,13)
#endif
#endif
#ifdef CAN_CHC_BUF14_DIR
        BUF_CFG(C,14);
#if (CAN_CHC_BUF14_DIR==CAN_RX)
        INIT_BS(C,14)
#endif
#endif
#ifdef CAN_CHC_BUF15_DIR
        BUF_CFG(C,15);
#if (CAN_CHC_BUF15_DIR==CAN_RX)
        INIT_BS(C,15)
#endif
#endif
#ifdef CAN_CHC_BUF16_DIR
        BUF_CFG(C,16);
#if (CAN_CHC_BUF16_DIR==CAN_RX)
        INIT_BS(C,16)
#endif
#endif
#ifdef CAN_CHC_BUF17_DIR
        BUF_CFG(C,17);
#if (CAN_CHC_BUF17_DIR==CAN_RX)
        INIT_BS(C,17)
#endif
#endif
#ifdef CAN_CHC_BUF18_DIR
        BUF_CFG(C,18);
#if (CAN_CHC_BUF18_DIR==CAN_RX)
        INIT_BS(C,18)
#endif
#endif
#ifdef CAN_CHC_BUF19_DIR
        BUF_CFG(C,19);
#if (CAN_CHC_BUF19_DIR==CAN_RX)
        INIT_BS(C,19)
#endif
#endif
#ifdef CAN_CHC_BUF20_DIR
        BUF_CFG(C,20);
#if (CAN_CHC_BUF20_DIR==CAN_RX)
        INIT_BS(C,20)
#endif
#endif
#ifdef CAN_CHC_BUF21_DIR
        BUF_CFG(C,21);
#if (CAN_CHC_BUF21_DIR==CAN_RX)
        INIT_BS(C,21)
#endif
#endif
#ifdef CAN_CHC_BUF22_DIR
        BUF_CFG(C,22);
#if (CAN_CHC_BUF22_DIR==CAN_RX)
        INIT_BS(C,22)
#endif
#endif
#ifdef CAN_CHC_BUF23_DIR
        BUF_CFG(C,23);
#if (CAN_CHC_BUF23_DIR==CAN_RX)
        INIT_BS(C,23)
#endif
#endif
#ifdef CAN_CHC_BUF24_DIR
        BUF_CFG(C,24);
#if (CAN_CHC_BUF24_DIR==CAN_RX)
        INIT_BS(C,24)
#endif
#endif
#ifdef CAN_CHC_BUF25_DIR
        BUF_CFG(C,25);
#if (CAN_CHC_BUF25_DIR==CAN_RX)
        INIT_BS(C,25)
#endif
#endif
#ifdef CAN_CHC_BUF26_DIR
        BUF_CFG(C,26);
#if (CAN_CHC_BUF26_DIR==CAN_RX)
        INIT_BS(C,26)
#endif
#endif
#ifdef CAN_CHC_BUF27_DIR
        BUF_CFG(C,27);
#if (CAN_CHC_BUF27_DIR==CAN_RX)
        INIT_BS(C,27)
#endif
#endif
#ifdef CAN_CHC_BUF28_DIR
        BUF_CFG(C,28);
#if (CAN_CHC_BUF28_DIR==CAN_RX)
        INIT_BS(C,28)
#endif
#endif
#ifdef CAN_CHC_BUF29_DIR
        BUF_CFG(C,29);
#if (CAN_CHC_BUF29_DIR==CAN_RX)
        INIT_BS(C,29)
#endif
#endif
#ifdef CAN_CHC_BUF30_DIR
        BUF_CFG(C,30);
#if (CAN_CHC_BUF30_DIR==CAN_RX)
        INIT_BS(C,30)
#endif
#endif
#ifdef CAN_CHC_BUF31_DIR
        BUF_CFG(C,31);
#if (CAN_CHC_BUF31_DIR==CAN_RX)
        INIT_BS(C,31)
#endif
#endif
#ifdef CAN_CHC_BUF32_DIR
        BUF_CFG(C,32);
#if (CAN_CHC_BUF32_DIR==CAN_RX)
        INIT_BS(C,32)
#endif
#endif
#ifdef CAN_CHC_BUF33_DIR
        BUF_CFG(C,33);
#if (CAN_CHC_BUF33_DIR==CAN_RX)
        INIT_BS(C,33)
#endif
#endif
#ifdef CAN_CHC_BUF34_DIR
        BUF_CFG(C,34);
#if (CAN_CHC_BUF34_DIR==CAN_RX)
        INIT_BS(C,34)
#endif
#endif
#ifdef CAN_CHC_BUF35_DIR
        BUF_CFG(C,35);
#if (CAN_CHC_BUF35_DIR==CAN_RX)
        INIT_BS(C,35)
#endif
#endif
#ifdef CAN_CHC_BUF36_DIR
        BUF_CFG(C,36);
#if (CAN_CHC_BUF36_DIR==CAN_RX)
        INIT_BS(C,36)
#endif
#endif
#ifdef CAN_CHC_BUF37_DIR
        BUF_CFG(C,37);
#if (CAN_CHC_BUF37_DIR==CAN_RX)
        INIT_BS(C,37)
#endif
#endif
#ifdef CAN_CHC_BUF38_DIR
        BUF_CFG(C,38);
#if (CAN_CHC_BUF38_DIR==CAN_RX)
        INIT_BS(C,38)
#endif
#endif
#ifdef CAN_CHC_BUF39_DIR
        BUF_CFG(C,39);
#if (CAN_CHC_BUF39_DIR==CAN_RX)
        INIT_BS(C,39)
#endif
#endif
#ifdef CAN_CHC_BUF40_DIR
        BUF_CFG(C,40);
#if (CAN_CHC_BUF40_DIR==CAN_RX)
        INIT_BS(C,40)
#endif
#endif
#ifdef CAN_CHC_BUF41_DIR
        BUF_CFG(C,41);
#if (CAN_CHC_BUF41_DIR==CAN_RX)
        INIT_BS(C,41)
#endif
#endif
#ifdef CAN_CHC_BUF42_DIR
        BUF_CFG(C,42);
#if (CAN_CHC_BUF42_DIR==CAN_RX)
        INIT_BS(C,42)
#endif
#endif
#ifdef CAN_CHC_BUF43_DIR
        BUF_CFG(C,43);
#if (CAN_CHC_BUF43_DIR==CAN_RX)
        INIT_BS(C,43)
#endif
#endif
#ifdef CAN_CHC_BUF44_DIR
        BUF_CFG(C,44);
#if (CAN_CHC_BUF44_DIR==CAN_RX)
        INIT_BS(C,44)
#endif
#endif
#ifdef CAN_CHC_BUF45_DIR
        BUF_CFG(C,45);
#if (CAN_CHC_BUF45_DIR==CAN_RX)
        INIT_BS(C,45)
#endif
#endif
#ifdef CAN_CHC_BUF46_DIR
        BUF_CFG(C,46);
#if (CAN_CHC_BUF46_DIR==CAN_RX)
        INIT_BS(C,46)
#endif
#endif
#ifdef CAN_CHC_BUF47_DIR
        BUF_CFG(C,47);
#if (CAN_CHC_BUF47_DIR==CAN_RX)
        INIT_BS(C,47)
#endif
#endif
#ifdef CAN_CHC_BUF48_DIR
        BUF_CFG(C,48);
#if (CAN_CHC_BUF48_DIR==CAN_RX)
        INIT_BS(C,48)
#endif
#endif
#ifdef CAN_CHC_BUF49_DIR
        BUF_CFG(C,49);
#if (CAN_CHC_BUF49_DIR==CAN_RX)
        INIT_BS(C,49)
#endif
#endif
#ifdef CAN_CHC_BUF50_DIR
        BUF_CFG(C,50);
#if (CAN_CHC_BUF50_DIR==CAN_RX)
        INIT_BS(C,50)
#endif
#endif
#ifdef CAN_CHC_BUF51_DIR
        BUF_CFG(C,51);
#if (CAN_CHC_BUF51_DIR==CAN_RX)
        INIT_BS(C,51)
#endif
#endif
#ifdef CAN_CHC_BUF52_DIR
        BUF_CFG(C,52);
#if (CAN_CHC_BUF52_DIR==CAN_RX)
        INIT_BS(C,52)
#endif
#endif
#ifdef CAN_CHC_BUF53_DIR
        BUF_CFG(C,53);
#if (CAN_CHC_BUF53_DIR==CAN_RX)
        INIT_BS(C,53)
#endif
#endif
#ifdef CAN_CHC_BUF54_DIR
        BUF_CFG(C,54);
#if (CAN_CHC_BUF54_DIR==CAN_RX)
        INIT_BS(C,54)
#endif
#endif
#ifdef CAN_CHC_BUF55_DIR
        BUF_CFG(C,55);
#if (CAN_CHC_BUF55_DIR==CAN_RX)
        INIT_BS(C,55)
#endif
#endif
#ifdef CAN_CHC_BUF56_DIR
        BUF_CFG(C,56);
#if (CAN_CHC_BUF56_DIR==CAN_RX)
        INIT_BS(C,56)
#endif
#endif
#ifdef CAN_CHC_BUF57_DIR
        BUF_CFG(C,57);
#if (CAN_CHC_BUF57_DIR==CAN_RX)
        INIT_BS(C,57)
#endif
#endif
#ifdef CAN_CHC_BUF58_DIR
        BUF_CFG(C,58);
#if (CAN_CHC_BUF58_DIR==CAN_RX)
        INIT_BS(C,58)
#endif
#endif
#ifdef CAN_CHC_BUF59_DIR
        BUF_CFG(C,59);
#if (CAN_CHC_BUF59_DIR==CAN_RX)
        INIT_BS(C,59)
#endif
#endif
#ifdef CAN_CHC_BUF60_DIR
        BUF_CFG(C,60);
#if (CAN_CHC_BUF60_DIR==CAN_RX)
        INIT_BS(C,60)
#endif
#endif
#ifdef CAN_CHC_BUF61_DIR
        BUF_CFG(C,61);
#if (CAN_CHC_BUF61_DIR==CAN_RX)
        INIT_BS(C,61)
#endif
#endif
#ifdef CAN_CHC_BUF62_DIR
        BUF_CFG(C,62);
#if (CAN_CHC_BUF62_DIR==CAN_RX)
        INIT_BS(C,62)
#endif
#endif
#ifdef CAN_CHC_BUF63_DIR
        BUF_CFG(C,63);
#if (CAN_CHC_BUF63_DIR==CAN_RX)
        INIT_BS(C,63)
#endif
#endif

#endif
    }
    else
    {
        res= PERIPHERAL_ALREADY_CONFIGURED;
    }

    return res;
}

/******************************************************************************/
/* FUNCTION     : CAN_Reset (uint8_t Channel)                                 */
/* PURPOSE      : This function performs the reset of the FLEXCAN             */
/* INPUTS NOTES : Channel of the FlexCan module                               */
/*                                                                            */
/*                                                                            */
/*                                                                            */
/*                                                                            */
/* RETURN NOTES : none.                                                       */
/* WARNING      :                                                             */
/*                                                                            */
/*****************************************************************************/
int16_t CAN_Reset (uint8_t Channel) 
{
    int16_t retValue = NO_ERROR;
    timeoutHandler_t resetHandler;
    struct FLEXCAN2_tag  * CAN_ptr;
    
    uint8_t status;

    CAN_ptr = (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    CAN_ptr->MCR.B.SOFTRST=1;  /* Soft-reset of the FlexCAN module */
    TIMING_SetTimeout(CAN_TIMEOUT, &resetHandler);
    while ((CAN_ptr->MCR.B.SOFTRST==1)&&(retValue == NO_ERROR))
    {
        TIMING_GetTimeoutStatus(resetHandler, &status);
        if(status == TIMEOUT_EXPIRED)
        {
#ifdef CHECK_BIOS_FAULTS
            BIOS_Faults |= BIOS_FAILURE_CAN;
#endif /* CHECK_BIOS_FAULTS */
            retValue = PERIPHERAL_NOT_INITIALIZED;
        }
    }

    return (retValue);
}
/****************************************************************************************************/
/* FUNCTION     : CAN_ResetBuffer (Id_Channel Channel,t_CAN_bufferconfig * t_CAN_bufferconfig_pnt)  */
/* PURPOSE      : This function performs the reset the selected MB of the FLEXCAN                   */
/* INPUTS NOTES : Channel of the FlexCan module                                                     */
/*                                                                                                  */
/*                                                                                                  */
/*                                                                                                  */
/*                                                                                                  */
/* RETURN NOTES : none.                                                                             */
/* WARNING      :                                                                                   */
/*                                                                                                  */
/****************************************************************************************************/
int16_t CAN_ResetBuffer (uint8_t Channel,
                         uint8_t nbuf)
{

    struct FLEXCAN2_tag * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    CAN_ptr->BUF[nbuf].CS.R=0;
    CAN_ptr->BUF[nbuf].ID.R=0;
    memset((void*)CAN_ptr->BUF[nbuf].DATA.B,0,8);

    if (nbuf<32) 
    {
        CAN_ptr->IMRL.R &=(~(1<<nbuf));
    } 
    else
    {
        CAN_ptr->IMRH.R &=(~(1<<(nbuf-32)));
    }
    return (NO_ERROR);
}

/******************************************************************************/
/* FUNCTION     : CAN_Disable (uint8_t Channel)                               */
/* PURPOSE      : This function disables FLEXCAN Channel                      */
/* INPUTS NOTES : Channel of the FlexCan module                               */
/*                                                                            */
/*                                                                            */
/*                                                                            */
/*                                                                            */
/* RETURN NOTES : none.                                                       */
/* WARNING      :                                                             */
/*                                                                            */
/*****************************************************************************/
int16_t CAN_Disable(uint8_t Channel) 
{
    int16_t res=NO_ERROR;
    struct FLEXCAN2_tag  * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if (CAN_ptr->MCR.B.MDIS==0) 
    {
        CAN_ptr->MCR.B.MDIS=CAN_DISABLE;
        if (CAN_ptr->MCR.B.MDIS==0) 
        {
            res=NOK_MODULE_DISABLE;
        }
    }

    return res;
}

/******************************************************************************/
/* FUNCTION     : CAN_enable (Id_Channel Channel)                             */
/* PURPOSE      : This function enables FLEXCAN Channel                       */
/* INPUTS NOTES : Channel of the FlexCan module                               */
/*                                                                            */
/*                                                                            */
/*                                                                            */
/*                                                                            */
/* RETURN NOTES : none.                                                       */
/* WARNING      :                                                             */
/*                                                                            */
/*****************************************************************************/
int16_t CAN_Enable(uint8_t Channel) 
{
    int16_t res=NO_ERROR;
    struct FLEXCAN2_tag  * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    if (CAN_ptr->MCR.B.MDIS==1)
    {
        CAN_ptr->MCR.B.MDIS=CAN_ENABLE;
        if (CAN_ptr->MCR.B.MDIS==1) 
        {
            res=NOK_MODULE_ENABLE;
        }
    }

    return res;
}

/*********************************************************************************************/
/* FUNCTION     : CAN_TxData (uint8_t Channel, uint8_t* pData)                               */
/* PURPOSE      : This function performs message data transmission                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_TxData (uint8_t Channel,
                    uint16_t nbuf, 
                    uint8_t* pData) 
{

    int16_t res=NO_ERROR;
    struct FLEXCAN2_tag * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if (CAN_ptr->ESR.B.BOFFINT==0x01) 
    {
        /* If the module is in busoff, the function returns BUSOFF */
        /* (CAN_ptr->ESR.R)&=(1<<2); */
        res = CAN_BUSOFF;
    } 
    else
    {
        memcpy((void*)CAN_ptr->BUF[nbuf].DATA.B,pData,CAN_ptr->BUF[nbuf].CS.B.LENGTH);
        CAN_ptr->BUF[nbuf].CS.B.CODE=TX_BUFFER_CODE_TRANSMIT;
    }

    return res;
}

/*********************************************************************************************/
/* FUNCTION     : Can_GetTxStatus (uint8_t Channel)                                          */
/* PURPOSE      : This function performs the reset of the FLEXCAN                            */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t Can_GetTxStatus(uint8_t Channel)
{

    int16_t res=NO_ERROR;
    struct FLEXCAN2_tag * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if ( (CAN_ptr->ESR.B.IDLE==0)&& (CAN_ptr->ESR.B.TXRX==1))//&& (CAN_ptr==(struct FLEXCAN2_tag *)0xFFFC4000))
    {
        res=CAN_TX_BUSY;
    }

    return (res);
}

/*********************************************************************************************/
/* FUNCTION     : CAN_EnableReceive (uint8_t Channel, uint8_t nbuf)                          */
/* PURPOSE      : This function performs the reset of the FLEXCAN                            */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_EnableReceive (uint8_t Channel, uint8_t nbuf) 
{
    int16_t res=NO_ERROR;

    struct FLEXCAN2_tag * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if ( CAN_ptr->ESR.B.BOFFINT==0x01)
    {    
        //If the module is in busoff, the function returns BUSOFF
        //    (CAN_ptr->ESR.R)&=(1<<2);
        res = CAN_BUSOFF;
    } 
    else
    {
        if( CAN_ptr->BUF[nbuf].CS.R & (1<<24)) 
        {
            //controlla un bit per verificare sel il MB e' BUSY
            res = CAN_RX_BUSY;
        }
        else
        {
            CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_DISABLE;    //set CODE=0 for rx buffer INACTIVE
            CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_ACTIVE_EMPTY;
        }
    }

    return res;
}

/*********************************************************************************************/
/* FUNCTION     : CAN_DisableReceive (uint8_t Channel, uint8_t nbuf)                         */
/* PURPOSE      : This function performs the reset of the FLEXCAN                            */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_DisableReceive (uint8_t Channel, uint8_t nbuf) 
{
    int16_t res=NO_ERROR;

    struct FLEXCAN2_tag * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if ( CAN_ptr->ESR.B.BOFFINT==0x01) 
    {
        //If the module is in busoff, the function returns BUSOFF
        //    (CAN_ptr->ESR.R)&=(1<<2);
        res = CAN_BUSOFF;
    } 
    else
    {
        if( CAN_ptr->BUF[nbuf].CS.R & (1<<24)) 
        { //controlla un bit per verificare sel il MB e' BUSY
            res = CAN_RX_BUSY;
        } 
        else
        {
            CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_DISABLE;    //set CODE=0 for rx buffer INACTIVE
        }
    }

    return res;
}

/*********************************************************************************************/
/* FUNCTION     : CAN_BusOffRecEnable(unint8_t Channel)                                    */
/* PURPOSE      : This function performs the automatic recovering from bus off state occurs  */
/*                according to the CAN Specification 2.0B                                    */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_BusOffRecEnable(uint8_t Channel) 
{
    struct FLEXCAN2_tag  * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    CAN_ptr->CR.B.BOFFREC=0;
    return(NO_ERROR);
}


/*********************************************************************************************/
/* FUNCTION     : CAN_BusOffRecDisable(uint8_t Channel)                                   */
/* PURPOSE      : Automatic recovering from bus off state disable                            */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_BusOffRecDisable(uint8_t Channel) 
{
    struct FLEXCAN2_tag  * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    CAN_ptr->CR.B.BOFFREC=1;
    return(NO_ERROR);
}


/*********************************************************************************************/
/* FUNCTION     : CAN_BusOffIntEnable(uint8_t Channel)                                       */
/* PURPOSE      : Bus Off mask, this function enable the interrupt                           */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_BusOffIntEnable(uint8_t Channel) 
{
    struct FLEXCAN2_tag  * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    CAN_ptr->CR.B.BOFFMSK=1;
    return(NO_ERROR);
}
/*********************************************************************************************/
/* FUNCTION     : CAN_BusOffIntDisable(uint8_t Channel)                                  */
/* PURPOSE      : Bus Off mask, this function enable the interrupt                           */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/

int16_t CAN_BusOffIntDisable(uint8_t Channel)
{
    struct FLEXCAN2_tag  * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    CAN_ptr->CR.B.BOFFMSK=0;
    return(NO_ERROR);
}

/*********************************************************************************************/
/* FUNCTION     : CAN_ErrIntEnable(Id_Channel Channel)                                       */
/* PURPOSE      : Error mask, this function enable the interrupt                             */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_ErrIntEnable(uint8_t Channel) 
{
    struct FLEXCAN2_tag  * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    CAN_ptr->CR.B.ERRMSK=1;
    return(NO_ERROR);
}

/*********************************************************************************************/
/* FUNCTION     : CAN_ErrIntDisable(Id_Channel Channel)                                      */
/* PURPOSE      : Error mask, this function enable the interrupt                             */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/

int16_t CAN_ErrIntDisable(uint8_t Channel)
{
    struct FLEXCAN2_tag  * CAN_ptr;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];
    CAN_ptr->CR.B.ERRMSK=0;
    return(NO_ERROR);
}




/*********************************************************************************************/
/* FUNCTION     : CAN_ErrIntDisable(Id_Channel Channel)                                      */
/* PURPOSE      : Error mask, shis function performs message data reception                  */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_RxData(uint8_t ch, uint8_t b, struct CAN_buff ** ptr/*uint8_t** ptr*/) 
{
    int16_t res = NO_ERROR;
    uint8_t wr;
    uint8_t bs;
    uint8_t num=0;
    uint8_t* prd;
    uint8_t* pbs;
    //uint8_t* ptrout;
    struct CAN_buff * ptrout;
    struct FLEXCAN2_tag  * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[ch];

    if(CAN_ptr->ESR.B.BOFFINT==1)
    {
        //          (CAN_ptr->ESR.R)&=(1<<2);
        res=CAN_BUSOFF;
    }
    else 
    {
        switch (ch)
        {

#if CAN_CHA_EN
            case   FLEXCAN_A:
            switch (b){
#ifdef CAN_CHA_BUF0_DIR
#if (CAN_CHA_BUF0_DIR==CAN_RX)
            case  0:
            wr =  CAN_CHA_b.w_0;
            bs =    CAN_CHA_b.buff_status_0;
            pbs = &CAN_CHA_b.buff_status_0;
            prd = &CAN_CHA_b.r_0;
            ptrout = &(CAN_CHA_b.buffer_0[CAN_CHA_b.r_0]);
            num = CAN_CHA_BUF0_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF1_DIR
#if (CAN_CHA_BUF1_DIR==CAN_RX)
            case  1:
            wr =  CAN_CHA_b.w_1;
            bs =    CAN_CHA_b.buff_status_1;
            pbs = &CAN_CHA_b.buff_status_1;
            prd =  &CAN_CHA_b.r_1;
            ptrout = &(CAN_CHA_b.buffer_1[CAN_CHA_b.r_1]);
            num = CAN_CHA_BUF1_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF2_DIR
#if (CAN_CHA_BUF2_DIR==CAN_RX)
            case 2:
            wr =  CAN_CHA_b.w_2;
            bs =    CAN_CHA_b.buff_status_2;
            pbs = &CAN_CHA_b.buff_status_2;
            prd =  &CAN_CHA_b.r_2;
            ptrout = &(CAN_CHA_b.buffer_2[CAN_CHA_b.r_2]);
            num = CAN_CHA_BUF2_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF3_DIR
#if (CAN_CHA_BUF3_DIR==CAN_RX)
            case 3:
            wr =  CAN_CHA_b.w_3;
            bs =    CAN_CHA_b.buff_status_3;
            pbs = &CAN_CHA_b.buff_status_3;
            prd =  &CAN_CHA_b.r_3;
            ptrout = &(CAN_CHA_b.buffer_3[CAN_CHA_b.r_3]);
            num = CAN_CHA_BUF3_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF4_DIR
#if (CAN_CHA_BUF4_DIR==CAN_RX)
            case 4:
            wr =  CAN_CHA_b.w_4;
            bs =    CAN_CHA_b.buff_status_4;
            pbs = &CAN_CHA_b.buff_status_4;
            prd =  &CAN_CHA_b.r_4;
            ptrout = &(CAN_CHA_b.buffer_4[CAN_CHA_b.r_4]);
            num = CAN_CHA_BUF4_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF5_DIR
#if (CAN_CHA_BUF5_DIR==CAN_RX)
            case 5:
            wr =  CAN_CHA_b.w_5;
            bs =    CAN_CHA_b.buff_status_5;
            pbs = &CAN_CHA_b.buff_status_5;
            prd =  &CAN_CHA_b.r_5;
            ptrout = &(CAN_CHA_b.buffer_5[CAN_CHA_b.r_5]);
            num = CAN_CHA_BUF5_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF6_DIR
#if (CAN_CHA_BUF6_DIR==CAN_RX)
            case 6:
            wr =  CAN_CHA_b.w_6;
            bs =    CAN_CHA_b.buff_status_6;
            pbs = &CAN_CHA_b.buff_status_6;
            prd =  &CAN_CHA_b.r_6;
            ptrout = &(CAN_CHA_b.buffer_6[CAN_CHA_b.r_6]);
            num = CAN_CHA_BUF6_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF7_DIR
#if (CAN_CHA_BUF7_DIR==CAN_RX)
            case  7:
            wr =  CAN_CHA_b.w_7;
            bs =    CAN_CHA_b.buff_status_7;
            pbs = &CAN_CHA_b.buff_status_7;
            prd =  &CAN_CHA_b.r_7;
            ptrout = &(CAN_CHA_b.buffer_7[CAN_CHA_b.r_7]);
            num = CAN_CHA_BUF7_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF8_DIR
#if (CAN_CHA_BUF8_DIR==CAN_RX)
            case 8:
            wr =  CAN_CHA_b.w_8;
            bs =    CAN_CHA_b.buff_status_8;
            pbs = &CAN_CHA_b.buff_status_8;
            prd =  &CAN_CHA_b.r_8;
            ptrout = &(CAN_CHA_b.buffer_8[CAN_CHA_b.r_8]);
            num = CAN_CHA_BUF8_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF9_DIR
#if (CAN_CHA_BUF9_DIR==CAN_RX)
            case 9:
            wr =  CAN_CHA_b.w_9;
            bs =    CAN_CHA_b.buff_status_9;
            pbs = &CAN_CHA_b.buff_status_9;
            prd =  &CAN_CHA_b.r_9;
            ptrout = &(CAN_CHA_b.buffer_9[CAN_CHA_b.r_9]);
            num = CAN_CHA_BUF9_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF10_DIR
#if (CAN_CHA_BUF10_DIR==CAN_RX)
            case 10:
            wr =  CAN_CHA_b.w_10;
            bs =    CAN_CHA_b.buff_status_10;
            pbs = &CAN_CHA_b.buff_status_10;
            prd =  &CAN_CHA_b.r_10;
            ptrout = &(CAN_CHA_b.buffer_10[CAN_CHA_b.r_10]);
            num = CAN_CHA_BUF10_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF11_DIR
#if (CAN_CHA_BUF11_DIR==CAN_RX)
            case 11:
            wr =  CAN_CHA_b.w_11;
            bs =    CAN_CHA_b.buff_status_11;
            pbs = &CAN_CHA_b.buff_status_11;
            prd =  &CAN_CHA_b.r_11;
            ptrout = &(CAN_CHA_b.buffer_11[CAN_CHA_b.r_11]);
            num = CAN_CHA_BUF11_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF12_DIR
#if (CAN_CHA_BUF12_DIR==CAN_RX)
            case 12:
            wr =  CAN_CHA_b.w_12;
            bs =    CAN_CHA_b.buff_status_12;
            pbs = &CAN_CHA_b.buff_status_12;
            prd =  &CAN_CHA_b.r_12;
            ptrout = &(CAN_CHA_b.buffer_12[CAN_CHA_b.r_12]);
            num = CAN_CHA_BUF12_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF13_DIR
#if (CAN_CHA_BUF13_DIR==CAN_RX)
            case 13:
            wr =  CAN_CHA_b.w_13;
            bs =    CAN_CHA_b.buff_status_13;
            pbs = &CAN_CHA_b.buff_status_13;
            prd =  &CAN_CHA_b.r_13;
            ptrout = &(CAN_CHA_b.buffer_13[CAN_CHA_b.r_13]);
            num = CAN_CHA_BUF13_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF14_DIR
#if (CAN_CHA_BUF14_DIR==CAN_RX)
            case 14:
            wr =  CAN_CHA_b.w_14;
            bs =    CAN_CHA_b.buff_status_14;
            pbs = &CAN_CHA_b.buff_status_14;
            prd =  &CAN_CHA_b.r_14;
            ptrout = &(CAN_CHA_b.buffer_14[CAN_CHA_b.r_14]);
            num = CAN_CHA_BUF14_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF15_DIR
#if (CAN_CHA_BUF15_DIR==CAN_RX)
            case 15:
            wr =  CAN_CHA_b.w_15;
            bs =    CAN_CHA_b.buff_status_15;
            pbs = &CAN_CHA_b.buff_status_15;
            prd =  &CAN_CHA_b.r_15;
            ptrout = &(CAN_CHA_b.buffer_15[CAN_CHA_b.r_15]);
            num = CAN_CHA_BUF15_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF16_DIR
#if (CAN_CHA_BUF16_DIR==CAN_RX)
            case 16:
            wr =  CAN_CHA_b.w_16;
            bs =    CAN_CHA_b.buff_status_16;
            pbs = &CAN_CHA_b.buff_status_16;
            prd =  &CAN_CHA_b.r_16;
            ptrout = &(CAN_CHA_b.buffer_16[CAN_CHA_b.r_16]);
            num = CAN_CHA_BUF16_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF17_DIR
#if (CAN_CHA_BUF17_DIR==CAN_RX)
            case 17:
            wr =  CAN_CHA_b.w_17;
            bs =    CAN_CHA_b.buff_status_17;
            pbs = &CAN_CHA_b.buff_status_17;
            prd =  &CAN_CHA_b.r_17;
            ptrout = &(CAN_CHA_b.buffer_17[CAN_CHA_b.r_17]);
            num = CAN_CHA_BUF17_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF18_DIR
#if (CAN_CHA_BUF18_DIR==CAN_RX)
            case 18:
            wr =  CAN_CHA_b.w_18;
            bs =    CAN_CHA_b.buff_status_18;
            pbs = &CAN_CHA_b.buff_status_18;
            prd =  &CAN_CHA_b.r_18;
            ptrout = &(CAN_CHA_b.buffer_18[CAN_CHA_b.r_18]);
            num = CAN_CHA_BUF18_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF19_DIR
#if (CAN_CHA_BUF19_DIR==CAN_RX)
            case 19:
            wr =  CAN_CHA_b.w_19;
            bs =    CAN_CHA_b.buff_status_19;
            pbs = &CAN_CHA_b.buff_status_19;
            prd =  &CAN_CHA_b.r_19;
            ptrout = &(CAN_CHA_b.buffer_19[CAN_CHA_b.r_19]);
            num = CAN_CHA_BUF19_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF20_DIR
#if (CAN_CHA_BUF20_DIR==CAN_RX)
            case 20:
            wr =  CAN_CHA_b.w_20;
            bs =    CAN_CHA_b.buff_status_20;
            pbs = &CAN_CHA_b.buff_status_20;
            prd =  &CAN_CHA_b.r_20;
            ptrout = &(CAN_CHA_b.buffer_20[CAN_CHA_b.r_20]);
            num = CAN_CHA_BUF20_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF21_DIR
#if (CAN_CHA_BUF21_DIR==CAN_RX)
            case 21:
            wr =  CAN_CHA_b.w_21;
            bs =    CAN_CHA_b.buff_status_21;
            pbs = &CAN_CHA_b.buff_status_21;
            prd =  &CAN_CHA_b.r_21;
            ptrout = &(CAN_CHA_b.buffer_21[CAN_CHA_b.r_21]);
            num = CAN_CHA_BUF21_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF22_DIR
#if (CAN_CHA_BUF22_DIR==CAN_RX)
            case 22:
            wr =  CAN_CHA_b.w_22;
            bs =    CAN_CHA_b.buff_status_22;
            pbs = &CAN_CHA_b.buff_status_22;
            prd =  &CAN_CHA_b.r_22;
            ptrout = &(CAN_CHA_b.buffer_22[CAN_CHA_b.r_22]);
            num = CAN_CHA_BUF22_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF23_DIR
#if (CAN_CHA_BUF23_DIR==CAN_RX)
            case 23:
            wr =  CAN_CHA_b.w_23;
            bs =    CAN_CHA_b.buff_status_23;
            pbs = &CAN_CHA_b.buff_status_23;
            prd =  &CAN_CHA_b.r_23;
            ptrout = &(CAN_CHA_b.buffer_23[CAN_CHA_b.r_23]);
            num = CAN_CHA_BUF23_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF24_DIR
#if (CAN_CHA_BUF24_DIR==CAN_RX)
            case 24:
            wr =  CAN_CHA_b.w_24;
            bs =    CAN_CHA_b.buff_status_24;
            pbs = &CAN_CHA_b.buff_status_24;
            prd =  &CAN_CHA_b.r_24;
            ptrout = &(CAN_CHA_b.buffer_24[CAN_CHA_b.r_24]);
            num = CAN_CHA_BUF24_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF25_DIR
#if (CAN_CHA_BUF25_DIR==CAN_RX)
            case 25:
            wr =  CAN_CHA_b.w_25;
            bs =    CAN_CHA_b.buff_status_25;
            pbs = &CAN_CHA_b.buff_status_25;
            prd =  &CAN_CHA_b.r_25;
            ptrout = &(CAN_CHA_b.buffer_25[CAN_CHA_b.r_25]);
            num = CAN_CHA_BUF25_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF26_DIR
#if (CAN_CHA_BUF26_DIR==CAN_RX)
            case 26:
            wr =  CAN_CHA_b.w_26;
            bs =    CAN_CHA_b.buff_status_26;
            pbs = &CAN_CHA_b.buff_status_26;
            prd =  &CAN_CHA_b.r_26;
            ptrout = &(CAN_CHA_b.buffer_26[CAN_CHA_b.r_26]);
            num = CAN_CHA_BUF26_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF27_DIR
#if (CAN_CHA_BUF27_DIR==CAN_RX)
            case 27:
            wr =  CAN_CHA_b.w_27;
            bs =    CAN_CHA_b.buff_status_27;
            pbs = &CAN_CHA_b.buff_status_27;
            prd =  &CAN_CHA_b.r_27;
            ptrout = &(CAN_CHA_b.buffer_27[CAN_CHA_b.r_27]);
            num = CAN_CHA_BUF27_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF28_DIR
#if (CAN_CHA_BUF28_DIR==CAN_RX)
            case 28:
            wr =  CAN_CHA_b.w_28;
            bs =    CAN_CHA_b.buff_status_28;
            pbs = &CAN_CHA_b.buff_status_28;
            prd =  &CAN_CHA_b.r_28;
            ptrout = &(CAN_CHA_b.buffer_28[CAN_CHA_b.r_28]);
            num = CAN_CHA_BUF28_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF29_DIR
#if (CAN_CHA_BUF29_DIR==CAN_RX)
            case 29:
            wr =  CAN_CHA_b.w_29;
            bs =    CAN_CHA_b.buff_status_29;
            pbs = &CAN_CHA_b.buff_status_29;
            prd =  &CAN_CHA_b.r_29;
            ptrout = &(CAN_CHA_b.buffer_29[CAN_CHA_b.r_29]);
            num = CAN_CHA_BUF29_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF30_DIR
#if (CAN_CHA_BUF30_DIR==CAN_RX)
            case 30:
            wr =  CAN_CHA_b.w_30;
            bs =    CAN_CHA_b.buff_status_30;
            pbs = &CAN_CHA_b.buff_status_30;
            prd =  &CAN_CHA_b.r_30;
            ptrout = &(CAN_CHA_b.buffer_30[CAN_CHA_b.r_30]);
            num = CAN_CHA_BUF30_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF31_DIR
#if (CAN_CHA_BUF31_DIR==CAN_RX)
            case 31:
            wr =  CAN_CHA_b.w_31;
            bs =    CAN_CHA_b.buff_status_31;
            pbs = &CAN_CHA_b.buff_status_31;
            prd =  &CAN_CHA_b.r_31;
            ptrout = &(CAN_CHA_b.buffer_31[CAN_CHA_b.r_31]);
            num = CAN_CHA_BUF31_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF32_DIR
#if (CAN_CHA_BUF32_DIR==CAN_RX)
            case 32:
            wr =  CAN_CHA_b.w_32;
            bs =    CAN_CHA_b.buff_status_32;
            pbs = &CAN_CHA_b.buff_status_32;
            prd =  &CAN_CHA_b.r_32;
            ptrout = &(CAN_CHA_b.buffer_32[CAN_CHA_b.r_32]);
            num = CAN_CHA_BUF32_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF33_DIR
#if (CAN_CHA_BUF33_DIR==CAN_RX)
            case 33:
            wr =  CAN_CHA_b.w_33;
            bs =    CAN_CHA_b.buff_status_33;
            pbs = &CAN_CHA_b.buff_status_33;
            prd =  &CAN_CHA_b.r_33;
            ptrout = &(CAN_CHA_b.buffer_33[CAN_CHA_b.r_33]);
            num = CAN_CHA_BUF33_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF34_DIR
#if (CAN_CHA_BUF34_DIR==CAN_RX)
            case 34:
            wr =  CAN_CHA_b.w_34;
            bs =    CAN_CHA_b.buff_status_34;
            pbs = &CAN_CHA_b.buff_status_34;
            prd =  &CAN_CHA_b.r_34;
            ptrout = &(CAN_CHA_b.buffer_34[CAN_CHA_b.r_34]);
            num = CAN_CHA_BUF34_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF35_DIR
#if (CAN_CHA_BUF35_DIR==CAN_RX)
            case 35:
            wr =  CAN_CHA_b.w_35;
            bs =    CAN_CHA_b.buff_status_35;
            pbs = &CAN_CHA_b.buff_status_35;
            prd =  &CAN_CHA_b.r_35;
            ptrout = &(CAN_CHA_b.buffer_35[CAN_CHA_b.r_35]);
            num = CAN_CHA_BUF35_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF36_DIR
#if (CAN_CHA_BUF36_DIR==CAN_RX)
            case 36:
            wr =  CAN_CHA_b.w_36;
            bs =    CAN_CHA_b.buff_status_36;
            pbs = &CAN_CHA_b.buff_status_36;
            prd =  &CAN_CHA_b.r_36;
            ptrout = &(CAN_CHA_b.buffer_36[CAN_CHA_b.r_36]);
            num = CAN_CHA_BUF36_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF37_DIR
#if (CAN_CHA_BUF37_DIR==CAN_RX)
            case 37:
            wr =  CAN_CHA_b.w_37;
            bs =    CAN_CHA_b.buff_status_37;
            pbs = &CAN_CHA_b.buff_status_37;
            prd =  &CAN_CHA_b.r_37;
            ptrout = &(CAN_CHA_b.buffer_37[CAN_CHA_b.r_37]);
            num = CAN_CHA_BUF37_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF38_DIR
#if (CAN_CHA_BUF38_DIR==CAN_RX)
            case 38:
            wr =  CAN_CHA_b.w_38;
            bs =    CAN_CHA_b.buff_status_38;
            pbs = &CAN_CHA_b.buff_status_38;
            prd =  &CAN_CHA_b.r_38;
            ptrout = &(CAN_CHA_b.buffer_38[CAN_CHA_b.r_38]);
            num = CAN_CHA_BUF38_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF39_DIR
#if (CAN_CHA_BUF39_DIR==CAN_RX)
            case 39:
            wr =  CAN_CHA_b.w_39;
            bs =    CAN_CHA_b.buff_status_39;
            pbs = &CAN_CHA_b.buff_status_39;
            prd =  &CAN_CHA_b.r_39;
            ptrout = &(CAN_CHA_b.buffer_39[CAN_CHA_b.r_39]);
            num = CAN_CHA_BUF39_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF40_DIR
#if (CAN_CHA_BUF40_DIR==CAN_RX)
            case 40:
            wr =  CAN_CHA_b.w_40;
            bs =    CAN_CHA_b.buff_status_40;
            pbs = &CAN_CHA_b.buff_status_40;
            prd =  &CAN_CHA_b.r_40;
            ptrout = &(CAN_CHA_b.buffer_40[CAN_CHA_b.r_40]);
            num = CAN_CHA_BUF40_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF41_DIR
#if (CAN_CHA_BUF41_DIR==CAN_RX)
            case 41:
            wr =  CAN_CHA_b.w_41;
            bs =    CAN_CHA_b.buff_status_41;
            pbs = &CAN_CHA_b.buff_status_41;
            prd =  &CAN_CHA_b.r_41;
            ptrout = &(CAN_CHA_b.buffer_41[CAN_CHA_b.r_41]);
            num = CAN_CHA_BUF41_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF42_DIR
#if (CAN_CHA_BUF42_DIR==CAN_RX)
            case 42:
            wr =  CAN_CHA_b.w_42;
            bs =    CAN_CHA_b.buff_status_42;
            pbs = &CAN_CHA_b.buff_status_42;
            prd =  &CAN_CHA_b.r_42;
            ptrout = &(CAN_CHA_b.buffer_42[CAN_CHA_b.r_42]);
            num = CAN_CHA_BUF42_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF43_DIR
#if (CAN_CHA_BUF43_DIR==CAN_RX)
            case 43:
            wr =  CAN_CHA_b.w_43;
            bs =    CAN_CHA_b.buff_status_43;
            pbs = &CAN_CHA_b.buff_status_43;
            prd =  &CAN_CHA_b.r_43;
            ptrout = &(CAN_CHA_b.buffer_43[CAN_CHA_b.r_43]);
            num = CAN_CHA_BUF43_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF44_DIR
#if (CAN_CHA_BUF44_DIR==CAN_RX)
            case 44:
            wr =  CAN_CHA_b.w_44;
            bs =    CAN_CHA_b.buff_status_44;
            pbs = &CAN_CHA_b.buff_status_44;
            prd =  &CAN_CHA_b.r_44;
            ptrout = &(CAN_CHA_b.buffer_44[CAN_CHA_b.r_44]);
            num = CAN_CHA_BUF44_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF45_DIR
#if (CAN_CHA_BUF45_DIR==CAN_RX)
            case 45:
            wr =  CAN_CHA_b.w_45;
            bs =    CAN_CHA_b.buff_status_45;
            pbs = &CAN_CHA_b.buff_status_45;
            prd =  &CAN_CHA_b.r_45;
            ptrout = &(CAN_CHA_b.buffer_45[CAN_CHA_b.r_45]);
            num = CAN_CHA_BUF45_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF46_DIR
#if (CAN_CHA_BUF46_DIR==CAN_RX)
            case 46:
            wr =  CAN_CHA_b.w_46;
            bs =    CAN_CHA_b.buff_status_46;
            pbs = &CAN_CHA_b.buff_status_46;
            prd =  &CAN_CHA_b.r_46;
            ptrout = &(CAN_CHA_b.buffer_46[CAN_CHA_b.r_46]);
            num = CAN_CHA_BUF46_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF47_DIR
#if (CAN_CHA_BUF47_DIR==CAN_RX)
            case 47:
            wr =  CAN_CHA_b.w_47;
            bs =    CAN_CHA_b.buff_status_47;
            pbs = &CAN_CHA_b.buff_status_47;
            prd =  &CAN_CHA_b.r_47;
            ptrout = &(CAN_CHA_b.buffer_47[CAN_CHA_b.r_47]);
            num = CAN_CHA_BUF47_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF48_DIR
#if (CAN_CHA_BUF48_DIR==CAN_RX)
            case 48:
            wr =  CAN_CHA_b.w_48;
            bs =    CAN_CHA_b.buff_status_48;
            pbs = &CAN_CHA_b.buff_status_48;
            prd =  &CAN_CHA_b.r_48;
            ptrout = &(CAN_CHA_b.buffer_48[CAN_CHA_b.r_48]);
            num = CAN_CHA_BUF48_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF49_DIR
#if (CAN_CHA_BUF49_DIR==CAN_RX)
            case 49:
            wr =  CAN_CHA_b.w_49;
            bs =    CAN_CHA_b.buff_status_49;
            pbs = &CAN_CHA_b.buff_status_49;
            prd =  &CAN_CHA_b.r_49;
            ptrout = &(CAN_CHA_b.buffer_49[CAN_CHA_b.r_49]);
            num = CAN_CHA_BUF49_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF50_DIR
#if (CAN_CHA_BUF50_DIR==CAN_RX)
            case 50:
            wr =  CAN_CHA_b.w_50;
            bs =    CAN_CHA_b.buff_status_50;
            pbs = &CAN_CHA_b.buff_status_50;
            prd =  &CAN_CHA_b.r_50;
            ptrout = &(CAN_CHA_b.buffer_50[CAN_CHA_b.r_50]);
            num = CAN_CHA_BUF50_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF51_DIR
#if (CAN_CHA_BUF51_DIR==CAN_RX)
            case 51:
            wr =  CAN_CHA_b.w_51;
            bs =    CAN_CHA_b.buff_status_51;
            pbs = &CAN_CHA_b.buff_status_51;
            prd =  &CAN_CHA_b.r_51;
            ptrout = &(CAN_CHA_b.buffer_51[CAN_CHA_b.r_51]);
            num = CAN_CHA_BUF51_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF52_DIR
#if (CAN_CHA_BUF52_DIR==CAN_RX)
            case 52:
            wr =  CAN_CHA_b.w_52;
            bs =    CAN_CHA_b.buff_status_52;
            pbs = &CAN_CHA_b.buff_status_52;
            prd =  &CAN_CHA_b.r_52;
            ptrout = &(CAN_CHA_b.buffer_52[CAN_CHA_b.r_52]);
            num = CAN_CHA_BUF52_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF53_DIR
#if (CAN_CHA_BUF53_DIR==CAN_RX)
            case 53:
            wr =  CAN_CHA_b.w_53;
            bs =    CAN_CHA_b.buff_status_53;
            pbs = &CAN_CHA_b.buff_status_53;
            prd =  &CAN_CHA_b.r_53;
            ptrout = &(CAN_CHA_b.buffer_53[CAN_CHA_b.r_53]);
            num = CAN_CHA_BUF53_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF54_DIR
#if (CAN_CHA_BUF54_DIR==CAN_RX)
            case 54:
            wr =  CAN_CHA_b.w_54;
            bs =    CAN_CHA_b.buff_status_54;
            pbs = &CAN_CHA_b.buff_status_54;
            prd =  &CAN_CHA_b.r_54;
            ptrout = &(CAN_CHA_b.buffer_54[CAN_CHA_b.r_54]);
            num = CAN_CHA_BUF54_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF55_DIR
#if (CAN_CHA_BUF55_DIR==CAN_RX)
            case 55:
            wr =  CAN_CHA_b.w_55;
            bs =    CAN_CHA_b.buff_status_55;
            pbs = &CAN_CHA_b.buff_status_55;
            prd =  &CAN_CHA_b.r_55;
            ptrout = &(CAN_CHA_b.buffer_55[CAN_CHA_b.r_55]);
            num = CAN_CHA_BUF55_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF56_DIR
#if (CAN_CHA_BUF56_DIR==CAN_RX)
            case 56:
            wr =  CAN_CHA_b.w_56;
            bs =    CAN_CHA_b.buff_status_56;
            pbs = &CAN_CHA_b.buff_status_56;
            prd =  &CAN_CHA_b.r_56;
            ptrout = &(CAN_CHA_b.buffer_56[CAN_CHA_b.r_56]);
            num = CAN_CHA_BUF56_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF57_DIR
#if (CAN_CHA_BUF57_DIR==CAN_RX)
            case 57:
            wr =  CAN_CHA_b.w_57;
            bs =    CAN_CHA_b.buff_status_57;
            pbs = &CAN_CHA_b.buff_status_57;
            prd =  &CAN_CHA_b.r_57;
            ptrout = &(CAN_CHA_b.buffer_57[CAN_CHA_b.r_57]);
            num = CAN_CHA_BUF57_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF58_DIR
#if (CAN_CHA_BUF58_DIR==CAN_RX)
            case 58:
            wr =  CAN_CHA_b.w_58;
            bs =    CAN_CHA_b.buff_status_58;
            pbs = &CAN_CHA_b.buff_status_58;
            prd =  &CAN_CHA_b.r_58;
            ptrout = &(CAN_CHA_b.buffer_58[CAN_CHA_b.r_58]);
            num = CAN_CHA_BUF58_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF59_DIR
#if (CAN_CHA_BUF59_DIR==CAN_RX)
            case 59:
            wr =  CAN_CHA_b.w_59;
            bs =    CAN_CHA_b.buff_status_59;
            pbs = &CAN_CHA_b.buff_status_59;
            prd =  &CAN_CHA_b.r_59;
            ptrout = &(CAN_CHA_b.buffer_59[CAN_CHA_b.r_59]);
            num = CAN_CHA_BUF59_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF60_DIR
#if (CAN_CHA_BUF60_DIR==CAN_RX)
            case 60:
            wr =  CAN_CHA_b.w_60;
            bs =    CAN_CHA_b.buff_status_60;
            pbs = &CAN_CHA_b.buff_status_60;
            prd =  &CAN_CHA_b.r_60;
            ptrout = &(CAN_CHA_b.buffer_60[CAN_CHA_b.r_60]);
            num = CAN_CHA_BUF60_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF61_DIR
#if (CAN_CHA_BUF61_DIR==CAN_RX)
            case 61:
            wr =  CAN_CHA_b.w_61;
            bs =    CAN_CHA_b.buff_status_61;
            pbs = &CAN_CHA_b.buff_status_61;
            prd =  &CAN_CHA_b.r_61;
            ptrout = &(CAN_CHA_b.buffer_61[CAN_CHA_b.r_61]);
            num = CAN_CHA_BUF61_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF62_DIR
#if (CAN_CHA_BUF62_DIR==CAN_RX)
            case 62:
            wr =  CAN_CHA_b.w_62;
            bs =    CAN_CHA_b.buff_status_62;
            pbs = &CAN_CHA_b.buff_status_62;
            prd =  &CAN_CHA_b.r_62;
            ptrout = &(CAN_CHA_b.buffer_62[CAN_CHA_b.r_62]);
            num = CAN_CHA_BUF62_NUM;
            break;
#endif
#endif
#ifdef CAN_CHA_BUF63_DIR
#if (CAN_CHA_BUF63_DIR==CAN_RX)
            case 63:
            wr =  CAN_CHA_b.w_63;
            bs =    CAN_CHA_b.buff_status_63;
            pbs = &CAN_CHA_b.buff_status_63;
            prd =  &CAN_CHA_b.r_63;
            ptrout = &(CAN_CHA_b.buffer_63[CAN_CHA_b.r_63]);
            num = CAN_CHA_BUF63_NUM;
            break;
#endif
#endif
            default:
            wr=num;
            prd=&num;
            break;
            }
            break;
#endif

#if (TARGET_TYPE == MPC5554)

#if CAN_CHB_EN
            case   FLEXCAN_B:
            switch (b){
#ifdef CAN_CHB_BUF0_DIR
#if (CAN_CHB_BUF0_DIR==CAN_RX)
            case 0:
            wr =  CAN_CHB_b.w_0;
            bs =    CAN_CHB_b.buff_status_0;
            pbs = &CAN_CHB_b.buff_status_0;
            prd =  &CAN_CHB_b.r_0;
            ptrout = &(CAN_CHB_b.buffer_0[CAN_CHB_b.r_0]);
            num = CAN_CHB_BUF0_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF1_DIR
#if (CAN_CHB_BUF1_DIR==CAN_RX)
            case 1:
            wr =  CAN_CHB_b.w_1;
            bs =    CAN_CHB_b.buff_status_1;
            pbs = &CAN_CHB_b.buff_status_1;
            prd =  &CAN_CHB_b.r_1;
            ptrout = &(CAN_CHB_b.buffer_1[CAN_CHB_b.r_1]);
            num = CAN_CHB_BUF1_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF2_DIR
#if (CAN_CHB_BUF2_DIR==CAN_RX)
            case 2:
            wr =  CAN_CHB_b.w_2;
            bs =    CAN_CHB_b.buff_status_2;
            pbs = &CAN_CHB_b.buff_status_2;
            prd =  &CAN_CHB_b.r_2;
            ptrout = &(CAN_CHB_b.buffer_2[CAN_CHB_b.r_2]);
            num = CAN_CHB_BUF2_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF3_DIR
#if (CAN_CHB_BUF3_DIR==CAN_RX)
            case 3:
            wr =  CAN_CHB_b.w_3;
            bs =    CAN_CHB_b.buff_status_3;
            pbs = &CAN_CHB_b.buff_status_3;
            prd =  &CAN_CHB_b.r_3;
            ptrout = &(CAN_CHB_b.buffer_3[CAN_CHB_b.r_3]);
            num = CAN_CHB_BUF3_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF4_DIR
#if (CAN_CHB_BUF4_DIR==CAN_RX)
            case 4:
            wr =  CAN_CHB_b.w_4;
            bs =    CAN_CHB_b.buff_status_4;
            pbs = &CAN_CHB_b.buff_status_4;
            prd =  &CAN_CHB_b.r_4;
            /*ptrout = &(CAN_CHB_b.b_4[CAN_CHB_b.r_4][0]);*/
            ptrout = &(CAN_CHB_b.buffer_4[CAN_CHB_b.r_4]);
            num = CAN_CHB_BUF4_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF5_DIR
#if (CAN_CHB_BUF5_DIR==CAN_RX)
            case 5:
            wr =  CAN_CHB_b.w_5;
            bs =    CAN_CHB_b.buff_status_5;
            pbs = &CAN_CHB_b.buff_status_5;
            prd =  &CAN_CHB_b.r_5;
            ptrout = &(CAN_CHB_b.buffer_5[CAN_CHB_b.r_5]);
            num = CAN_CHB_BUF5_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF6_DIR
#if (CAN_CHB_BUF6_DIR==CAN_RX)
            case 6:
            wr =  CAN_CHB_b.w_6;
            bs =    CAN_CHB_b.buff_status_6;
            pbs = &CAN_CHB_b.buff_status_6;
            prd =  &CAN_CHB_b.r_6;
            ptrout = &(CAN_CHB_b.buffer_6[CAN_CHB_b.r_6]);
            num = CAN_CHB_BUF6_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF7_DIR
#if (CAN_CHB_BUF7_DIR==CAN_RX)
            case 7:
            wr =  CAN_CHB_b.w_7;
            bs =    CAN_CHB_b.buff_status_7;
            pbs = &CAN_CHB_b.buff_status_7;
            prd =  &CAN_CHB_b.r_7;
            ptrout = &(CAN_CHB_b.buffer_7[CAN_CHB_b.r_7]);
            num = CAN_CHB_BUF7_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF8_DIR
#if (CAN_CHB_BUF8_DIR==CAN_RX)
            case 8:
            wr =  CAN_CHB_b.w_8;
            bs =    CAN_CHB_b.buff_status_8;
            pbs = &CAN_CHB_b.buff_status_8;
            prd =  &CAN_CHB_b.r_8;
            ptrout = &(CAN_CHB_b.buffer_8[CAN_CHB_b.r_8]);
            num = CAN_CHB_BUF8_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF9_DIR
#if (CAN_CHB_BUF9_DIR==CAN_RX)
            case 9:
            wr =  CAN_CHB_b.w_9;
            bs =    CAN_CHB_b.buff_status_9;
            pbs = &CAN_CHB_b.buff_status_9;
            prd =  &CAN_CHB_b.r_9;
            ptrout = &(CAN_CHB_b.buffer_9[CAN_CHB_b.r_9]);
            num = CAN_CHB_BUF9_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF10_DIR
#if (CAN_CHB_BUF10_DIR==CAN_RX)
            case 10:
            wr =  CAN_CHB_b.w_10;
            bs =    CAN_CHB_b.buff_status_10;
            pbs = &CAN_CHB_b.buff_status_10;
            prd =  &CAN_CHB_b.r_10;
            ptrout = &(CAN_CHB_b.buffer_10[CAN_CHB_b.r_10]);
            num = CAN_CHB_BUF10_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF11_DIR
#if (CAN_CHB_BUF11_DIR==CAN_RX)
            case 11:
            wr =  CAN_CHB_b.w_11;
            bs =    CAN_CHB_b.buff_status_11;
            pbs = &CAN_CHB_b.buff_status_11;
            prd =  &CAN_CHB_b.r_11;
            ptrout = &(CAN_CHB_b.buffer_11[CAN_CHB_b.r_11]);
            num = CAN_CHB_BUF11_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF12_DIR
#if (CAN_CHB_BUF12_DIR==CAN_RX)
            case 12:
            wr =  CAN_CHB_b.w_12;
            bs =    CAN_CHB_b.buff_status_12;
            pbs = &CAN_CHB_b.buff_status_12;
            prd =  &CAN_CHB_b.r_12;
            ptrout = &(CAN_CHB_b.buffer_12[CAN_CHB_b.r_12]);
            num = CAN_CHB_BUF12_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF13_DIR
#if (CAN_CHB_BUF13_DIR==CAN_RX)
            case 13:
            wr =  CAN_CHB_b.w_13;
            bs =    CAN_CHB_b.buff_status_13;
            pbs = &CAN_CHB_b.buff_status_13;
            prd =  &CAN_CHB_b.r_13;
            ptrout = &(CAN_CHB_b.buffer_13[CAN_CHB_b.r_13]);
            num = CAN_CHB_BUF13_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF14_DIR
#if (CAN_CHB_BUF14_DIR==CAN_RX)
            case 14:
            wr =  CAN_CHB_b.w_14;
            bs =    CAN_CHB_b.buff_status_14;
            pbs = &CAN_CHB_b.buff_status_14;
            prd =  &CAN_CHB_b.r_14;
            ptrout = &(CAN_CHB_b.buffer_14[CAN_CHB_b.r_14]);
            num = CAN_CHB_BUF14_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF15_DIR
#if (CAN_CHB_BUF15_DIR==CAN_RX)
            case 15:
            wr =  CAN_CHB_b.w_15;
            bs =    CAN_CHB_b.buff_status_15;
            pbs = &CAN_CHB_b.buff_status_15;
            prd =  &CAN_CHB_b.r_15;
            ptrout = &(CAN_CHB_b.buffer_15[CAN_CHB_b.r_15]);
            num = CAN_CHB_BUF15_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF16_DIR
#if (CAN_CHB_BUF16_DIR==CAN_RX)
            case 16:
            wr =  CAN_CHB_b.w_16;
            bs =    CAN_CHB_b.buff_status_16;
            pbs = &CAN_CHB_b.buff_status_16;
            prd =  &CAN_CHB_b.r_16;
            ptrout = &(CAN_CHB_b.buffer_16[CAN_CHB_b.r_16]);
            num = CAN_CHB_BUF16_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF17_DIR
#if (CAN_CHB_BUF17_DIR==CAN_RX)
            case 17:
            wr =  CAN_CHB_b.w_17;
            bs =    CAN_CHB_b.buff_status_17;
            pbs = &CAN_CHB_b.buff_status_17;
            prd =  &CAN_CHB_b.r_17;
            ptrout = &(CAN_CHB_b.buffer_17[CAN_CHB_b.r_17]);
            num = CAN_CHB_BUF17_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF18_DIR
#if (CAN_CHB_BUF18_DIR==CAN_RX)
            case 18:
            wr =  CAN_CHB_b.w_18;
            bs =    CAN_CHB_b.buff_status_18;
            pbs = &CAN_CHB_b.buff_status_18;
            prd =  &CAN_CHB_b.r_18;
            ptrout = &(CAN_CHB_b.buffer_18[CAN_CHB_b.r_18]);
            num = CAN_CHB_BUF18_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF19_DIR
#if (CAN_CHB_BUF19_DIR==CAN_RX)
            case 19:
            wr =  CAN_CHB_b.w_19;
            bs =    CAN_CHB_b.buff_status_19;
            pbs = &CAN_CHB_b.buff_status_19;
            prd =  &CAN_CHB_b.r_19;
            ptrout = &(CAN_CHB_b.buffer_19[CAN_CHB_b.r_19]);
            num = CAN_CHB_BUF19_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF20_DIR
#if (CAN_CHB_BUF20_DIR==CAN_RX)
            case 20:
            wr =  CAN_CHB_b.w_20;
            bs =    CAN_CHB_b.buff_status_20;
            pbs = &CAN_CHB_b.buff_status_20;
            prd =  &CAN_CHB_b.r_20;
            ptrout = &(CAN_CHB_b.buffer_20[CAN_CHB_b.r_20]);
            num = CAN_CHB_BUF20_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF21_DIR
#if (CAN_CHB_BUF21_DIR==CAN_RX)
            case 21:
            wr =  CAN_CHB_b.w_21;
            bs =    CAN_CHB_b.buff_status_21;
            pbs = &CAN_CHB_b.buff_status_21;
            prd =  &CAN_CHB_b.r_21;
            ptrout = &(CAN_CHB_b.buffer_21[CAN_CHB_b.r_21]);
            num = CAN_CHB_BUF21_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF22_DIR
#if (CAN_CHB_BUF22_DIR==CAN_RX)
            case 22:
            wr =  CAN_CHB_b.w_22;
            bs =    CAN_CHB_b.buff_status_22;
            pbs = &CAN_CHB_b.buff_status_22;
            prd =  &CAN_CHB_b.r_22;
            ptrout = &(CAN_CHB_b.buffer_22[CAN_CHB_b.r_22]);
            num = CAN_CHB_BUF22_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF23_DIR
#if (CAN_CHB_BUF23_DIR==CAN_RX)
            case 23:
            wr =  CAN_CHB_b.w_23;
            bs =    CAN_CHB_b.buff_status_23;
            pbs = &CAN_CHB_b.buff_status_23;
            prd =  &CAN_CHB_b.r_23;
            ptrout = &(CAN_CHB_b.buffer_23[CAN_CHB_b.r_23]);
            num = CAN_CHB_BUF23_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF24_DIR
#if (CAN_CHB_BUF24_DIR==CAN_RX)
            case 24:
            wr =  CAN_CHB_b.w_24;
            bs =    CAN_CHB_b.buff_status_24;
            pbs = &CAN_CHB_b.buff_status_24;
            prd =  &CAN_CHB_b.r_24;
            ptrout = &(CAN_CHB_b.buffer_24[CAN_CHB_b.r_24]);
            num = CAN_CHB_BUF24_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF25_DIR
#if (CAN_CHB_BUF25_DIR==CAN_RX)
            case 25:
            wr =  CAN_CHB_b.w_25;
            bs =    CAN_CHB_b.buff_status_25;
            pbs = &CAN_CHB_b.buff_status_25;
            prd =  &CAN_CHB_b.r_25;
            ptrout = &(CAN_CHB_b.buffer_25[CAN_CHB_b.r_25]);
            num = CAN_CHB_BUF25_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF26_DIR
#if (CAN_CHB_BUF26_DIR==CAN_RX)
            case 26:
            wr =  CAN_CHB_b.w_26;
            bs =    CAN_CHB_b.buff_status_26;
            pbs = &CAN_CHB_b.buff_status_26;
            prd =  &CAN_CHB_b.r_26;
            ptrout = &(CAN_CHB_b.buffer_26[CAN_CHB_b.r_26]);
            num = CAN_CHB_BUF26_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF27_DIR
#if (CAN_CHB_BUF27_DIR==CAN_RX)
            case 27:
            wr =  CAN_CHB_b.w_27;
            bs =    CAN_CHB_b.buff_status_27;
            pbs = &CAN_CHB_b.buff_status_27;
            prd =  &CAN_CHB_b.r_27;
            ptrout = &(CAN_CHB_b.buffer_27[CAN_CHB_b.r_27]);
            num = CAN_CHB_BUF27_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF28_DIR
#if (CAN_CHB_BUF28_DIR==CAN_RX)
            case 28:
            wr =  CAN_CHB_b.w_28;
            bs =    CAN_CHB_b.buff_status_28;
            pbs = &CAN_CHB_b.buff_status_28;
            prd =  &CAN_CHB_b.r_28;
            ptrout = &(CAN_CHB_b.buffer_28[CAN_CHB_b.r_28]);
            num = CAN_CHB_BUF28_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF29_DIR
#if (CAN_CHB_BUF29_DIR==CAN_RX)
            case 29:
            wr =  CAN_CHB_b.w_29;
            bs =    CAN_CHB_b.buff_status_29;
            pbs = &CAN_CHB_b.buff_status_29;
            prd =  &CAN_CHB_b.r_29;
            ptrout = &(CAN_CHB_b.buffer_29[CAN_CHB_b.r_29]);
            num = CAN_CHB_BUF29_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF30_DIR
#if (CAN_CHB_BUF30_DIR==CAN_RX)
            case 30:
            wr =  CAN_CHB_b.w_30;
            bs =    CAN_CHB_b.buff_status_30;
            pbs = &CAN_CHB_b.buff_status_30;
            prd =  &CAN_CHB_b.r_30;
            ptrout = &(CAN_CHB_b.buffer_30[CAN_CHB_b.r_30]);
            num = CAN_CHB_BUF30_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF31_DIR
#if (CAN_CHB_BUF31_DIR==CAN_RX)
            case 31:
            wr =  CAN_CHB_b.w_31;
            bs =    CAN_CHB_b.buff_status_31;
            pbs = &CAN_CHB_b.buff_status_31;
            prd =  &CAN_CHB_b.r_31;
            ptrout = &(CAN_CHB_b.buffer_31[CAN_CHB_b.r_31]);
            num = CAN_CHB_BUF31_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF32_DIR
#if (CAN_CHB_BUF32_DIR==CAN_RX)
            case 32:
            wr =  CAN_CHB_b.w_32;
            bs =    CAN_CHB_b.buff_status_32;
            pbs = &CAN_CHB_b.buff_status_32;
            prd =  &CAN_CHB_b.r_32;
            ptrout = &(CAN_CHB_b.buffer_32[CAN_CHB_b.r_32]);
            num = CAN_CHB_BUF32_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF33_DIR
#if (CAN_CHB_BUF33_DIR==CAN_RX)
            case 33:
            wr =  CAN_CHB_b.w_33;
            bs =    CAN_CHB_b.buff_status_33;
            pbs = &CAN_CHB_b.buff_status_33;
            prd =  &CAN_CHB_b.r_33;
            ptrout = &(CAN_CHB_b.buffer_33[CAN_CHB_b.r_33]);
            num = CAN_CHB_BUF33_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF34_DIR
#if (CAN_CHB_BUF34_DIR==CAN_RX)
            case 34:
            wr =  CAN_CHB_b.w_34;
            bs =    CAN_CHB_b.buff_status_34;
            pbs = &CAN_CHB_b.buff_status_34;
            prd =  &CAN_CHB_b.r_34;
            ptrout = &(CAN_CHB_b.buffer_34[CAN_CHB_b.r_34]);
            num = CAN_CHB_BUF34_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF35_DIR
#if (CAN_CHB_BUF35_DIR==CAN_RX)
            case 35:
            wr =  CAN_CHB_b.w_35;
            bs =    CAN_CHB_b.buff_status_35;
            pbs = &CAN_CHB_b.buff_status_35;
            prd =  &CAN_CHB_b.r_35;
            ptrout = &(CAN_CHB_b.buffer_35[CAN_CHB_b.r_35]);
            num = CAN_CHB_BUF35_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF36_DIR
#if (CAN_CHB_BUF36_DIR==CAN_RX)
            case 36:
            wr =  CAN_CHB_b.w_36;
            bs =    CAN_CHB_b.buff_status_36;
            pbs = &CAN_CHB_b.buff_status_36;
            prd =  &CAN_CHB_b.r_36;
            ptrout = &(CAN_CHB_b.buffer_36[CAN_CHB_b.r_36]);
            num = CAN_CHB_BUF36_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF37_DIR
#if (CAN_CHB_BUF37_DIR==CAN_RX)
            case 37:
            wr =  CAN_CHB_b.w_37;
            bs =    CAN_CHB_b.buff_status_37;
            pbs = &CAN_CHB_b.buff_status_37;
            prd =  &CAN_CHB_b.r_37;
            ptrout = &(CAN_CHB_b.buffer_37[CAN_CHB_b.r_37]);
            num = CAN_CHB_BUF37_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF38_DIR
#if (CAN_CHB_BUF38_DIR==CAN_RX)
            case 38:
            wr =  CAN_CHB_b.w_38;
            bs =    CAN_CHB_b.buff_status_38;
            pbs = &CAN_CHB_b.buff_status_38;
            prd =  &CAN_CHB_b.r_38;
            ptrout = &(CAN_CHB_b.buffer_38[CAN_CHB_b.r_38]);
            num = CAN_CHB_BUF38_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF39_DIR
#if (CAN_CHB_BUF39_DIR==CAN_RX)
            case 39:
            wr =  CAN_CHB_b.w_39;
            bs =    CAN_CHB_b.buff_status_39;
            pbs = &CAN_CHB_b.buff_status_39;
            prd =  &CAN_CHB_b.r_39;
            ptrout = &(CAN_CHB_b.buffer_39[CAN_CHB_b.r_39]);
            num = CAN_CHB_BUF39_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF40_DIR
#if (CAN_CHB_BUF40_DIR==CAN_RX)
            case 40:
            wr =  CAN_CHB_b.w_40;
            bs =    CAN_CHB_b.buff_status_40;
            pbs = &CAN_CHB_b.buff_status_40;
            prd =  &CAN_CHB_b.r_40;
            ptrout = &(CAN_CHB_b.buffer_40[CAN_CHB_b.r_40]);
            num = CAN_CHB_BUF40_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF41_DIR
#if (CAN_CHB_BUF41_DIR==CAN_RX)
            case 41:
            wr =  CAN_CHB_b.w_41;
            bs =    CAN_CHB_b.buff_status_41;
            pbs = &CAN_CHB_b.buff_status_41;
            prd =  &CAN_CHB_b.r_41;
            ptrout = &(CAN_CHB_b.buffer_41[CAN_CHB_b.r_41]);
            num = CAN_CHB_BUF41_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF42_DIR
#if (CAN_CHB_BUF42_DIR==CAN_RX)
            case 42:
            wr =  CAN_CHB_b.w_42;
            bs =    CAN_CHB_b.buff_status_42;
            pbs = &CAN_CHB_b.buff_status_42;
            prd =  &CAN_CHB_b.r_42;
            ptrout = &(CAN_CHB_b.buffer_42[CAN_CHB_b.r_42]);
            num = CAN_CHB_BUF42_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF43_DIR
#if (CAN_CHB_BUF43_DIR==CAN_RX)
            case 43:
            wr =  CAN_CHB_b.w_43;
            bs =    CAN_CHB_b.buff_status_43;
            pbs = &CAN_CHB_b.buff_status_43;
            prd =  &CAN_CHB_b.r_43;
            ptrout = &(CAN_CHB_b.buffer_43[CAN_CHB_b.r_43]);
            num = CAN_CHB_BUF43_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF44_DIR
#if (CAN_CHB_BUF44_DIR==CAN_RX)
            case 44:
            wr =  CAN_CHB_b.w_44;
            bs =    CAN_CHB_b.buff_status_44;
            pbs = &CAN_CHB_b.buff_status_44;
            prd =  &CAN_CHB_b.r_44;
            ptrout = &(CAN_CHB_b.buffer_44[CAN_CHB_b.r_44]);
            num = CAN_CHB_BUF44_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF45_DIR
#if (CAN_CHB_BUF45_DIR==CAN_RX)
            case 45:
            wr =  CAN_CHB_b.w_45;
            bs =    CAN_CHB_b.buff_status_45;
            pbs = &CAN_CHB_b.buff_status_45;
            prd =  &CAN_CHB_b.r_45;
            ptrout = &(CAN_CHB_b.buffer_45[CAN_CHB_b.r_45]);
            num = CAN_CHB_BUF45_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF46_DIR
#if (CAN_CHB_BUF46_DIR==CAN_RX)
            case 46:
            wr =  CAN_CHB_b.w_46;
            bs =    CAN_CHB_b.buff_status_46;
            pbs = &CAN_CHB_b.buff_status_46;
            prd =  &CAN_CHB_b.r_46;
            ptrout = &(CAN_CHB_b.buffer_46[CAN_CHB_b.r_46]);
            num = CAN_CHB_BUF46_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF47_DIR
#if (CAN_CHB_BUF47_DIR==CAN_RX)
            case 47:
            wr =  CAN_CHB_b.w_47;
            bs =    CAN_CHB_b.buff_status_47;
            pbs = &CAN_CHB_b.buff_status_47;
            prd =  &CAN_CHB_b.r_47;
            ptrout = &(CAN_CHB_b.buffer_47[CAN_CHB_b.r_47]);
            num = CAN_CHB_BUF47_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF48_DIR
#if (CAN_CHB_BUF48_DIR==CAN_RX)
            case 48:
            wr =  CAN_CHB_b.w_48;
            bs =    CAN_CHB_b.buff_status_48;
            pbs = &CAN_CHB_b.buff_status_48;
            prd =  &CAN_CHB_b.r_48;
            ptrout = &(CAN_CHB_b.buffer_48[CAN_CHB_b.r_48]);
            num = CAN_CHB_BUF48_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF49_DIR
#if (CAN_CHB_BUF49_DIR==CAN_RX)
            case 49:
            wr =  CAN_CHB_b.w_49;
            bs =    CAN_CHB_b.buff_status_49;
            pbs = &CAN_CHB_b.buff_status_49;
            prd =  &CAN_CHB_b.r_49;
            ptrout = &(CAN_CHB_b.buffer_49[CAN_CHB_b.r_49]);
            num = CAN_CHB_BUF49_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF50_DIR
#if (CAN_CHB_BUF50_DIR==CAN_RX)
            case 50:
            wr =  CAN_CHB_b.w_50;
            bs =    CAN_CHB_b.buff_status_50;
            pbs = &CAN_CHB_b.buff_status_50;
            prd =  &CAN_CHB_b.r_50;
            ptrout = &(CAN_CHB_b.buffer_50[CAN_CHB_b.r_50]);
            num = CAN_CHB_BUF50_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF51_DIR
#if (CAN_CHB_BUF51_DIR==CAN_RX)
            case 51:
            wr =  CAN_CHB_b.w_51;
            bs =    CAN_CHB_b.buff_status_51;
            pbs = &CAN_CHB_b.buff_status_51;
            prd =  &CAN_CHB_b.r_51;
            ptrout = &(CAN_CHB_b.buffer_51[CAN_CHB_b.r_51]);
            num = CAN_CHB_BUF51_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF52_DIR
#if (CAN_CHB_BUF52_DIR==CAN_RX)
            case 52:
            wr =  CAN_CHB_b.w_52;
            bs =    CAN_CHB_b.buff_status_52;
            pbs = &CAN_CHB_b.buff_status_52;
            prd =  &CAN_CHB_b.r_52;
            ptrout = &(CAN_CHB_b.buffer_52[CAN_CHB_b.r_52]);
            num = CAN_CHB_BUF52_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF53_DIR
#if (CAN_CHB_BUF53_DIR==CAN_RX)
            case 53:
            wr =  CAN_CHB_b.w_53;
            bs =    CAN_CHB_b.buff_status_53;
            pbs = &CAN_CHB_b.buff_status_53;
            prd =  &CAN_CHB_b.r_53;
            ptrout = &(CAN_CHB_b.buffer_53[CAN_CHB_b.r_53]);
            num = CAN_CHB_BUF53_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF54_DIR
#if (CAN_CHB_BUF54_DIR==CAN_RX)
            case 54:
            wr =  CAN_CHB_b.w_54;
            bs =    CAN_CHB_b.buff_status_54;
            pbs = &CAN_CHB_b.buff_status_54;
            prd =  &CAN_CHB_b.r_54;
            ptrout = &(CAN_CHB_b.buffer_54[CAN_CHB_b.r_54]);
            num = CAN_CHB_BUF54_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF55_DIR
#if (CAN_CHB_BUF55_DIR==CAN_RX)
            case 55:
            wr =  CAN_CHB_b.w_55;
            bs =    CAN_CHB_b.buff_status_55;
            pbs = &CAN_CHB_b.buff_status_55;
            prd =  &CAN_CHB_b.r_55;
            ptrout = &(CAN_CHB_b.buffer_55[CAN_CHB_b.r_55]);
            num = CAN_CHB_BUF55_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF56_DIR
#if (CAN_CHB_BUF56_DIR==CAN_RX)
            case 56:
            wr =  CAN_CHB_b.w_56;
            bs =    CAN_CHB_b.buff_status_56;
            pbs = &CAN_CHB_b.buff_status_56;
            prd =  &CAN_CHB_b.r_56;
            ptrout = &(CAN_CHB_b.buffer_56[CAN_CHB_b.r_56]);
            num = CAN_CHB_BUF56_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF57_DIR
#if (CAN_CHB_BUF57_DIR==CAN_RX)
            case 57:
            wr =  CAN_CHB_b.w_57;
            bs =    CAN_CHB_b.buff_status_57;
            pbs = &CAN_CHB_b.buff_status_57;
            prd =  &CAN_CHB_b.r_57;
            ptrout = &(CAN_CHB_b.buffer_57[CAN_CHB_b.r_57]);
            num = CAN_CHB_BUF57_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF58_DIR
#if (CAN_CHB_BUF58_DIR==CAN_RX)
            case 58:
            wr =  CAN_CHB_b.w_58;
            bs =    CAN_CHB_b.buff_status_58;
            pbs = &CAN_CHB_b.buff_status_58;
            prd =  &CAN_CHB_b.r_58;
            ptrout = &(CAN_CHB_b.buffer_58[CAN_CHB_b.r_58]);
            num = CAN_CHB_BUF58_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF59_DIR
#if (CAN_CHB_BUF59_DIR==CAN_RX)
            case 59:
            wr =  CAN_CHB_b.w_59;
            bs =    CAN_CHB_b.buff_status_59;
            pbs = &CAN_CHB_b.buff_status_59;
            prd =  &CAN_CHB_b.r_59;
            ptrout = &(CAN_CHB_b.buffer_59[CAN_CHB_b.r_59]);
            num = CAN_CHB_BUF59_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF60_DIR
#if (CAN_CHB_BUF60_DIR==CAN_RX)
            case 60:
            wr =  CAN_CHB_b.w_60;
            bs =    CAN_CHB_b.buff_status_60;
            pbs = &CAN_CHB_b.buff_status_60;
            prd =  &CAN_CHB_b.r_60;
            ptrout = &(CAN_CHB_b.buffer_60[CAN_CHB_b.r_60]);
            num = CAN_CHB_BUF60_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF61_DIR
#if (CAN_CHB_BUF61_DIR==CAN_RX)
            case 61:
            wr =  CAN_CHB_b.w_61;
            bs =    CAN_CHB_b.buff_status_61;
            pbs = &CAN_CHB_b.buff_status_61;
            prd =  &CAN_CHB_b.r_61;
            ptrout = &(CAN_CHB_b.buffer_61[CAN_CHB_b.r_61]);
            num = CAN_CHB_BUF61_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF62_DIR
#if (CAN_CHB_BUF62_DIR==CAN_RX)
            case 62:
            wr =  CAN_CHB_b.w_62;
            bs =    CAN_CHB_b.buff_status_62;
            pbs = &CAN_CHB_b.buff_status_62;
            prd =  &CAN_CHB_b.r_62;
            ptrout = &(CAN_CHB_b.buffer_62[CAN_CHB_b.r_62]);
            num = CAN_CHB_BUF62_NUM;
            break;
#endif
#endif
#ifdef CAN_CHB_BUF63_DIR
#if (CAN_CHB_BUF63_DIR==CAN_RX)
            case 63:
            wr =  CAN_CHB_b.w_63;
            bs =    CAN_CHB_b.buff_status_63;
            pbs = &CAN_CHB_b.buff_status_63;
            prd =  &CAN_CHB_b.r_63;
            ptrout = &(CAN_CHB_b.buffer_63[CAN_CHB_b.r_63]);
            num = CAN_CHB_BUF63_NUM;
            break;
#endif
#endif

            default:
            wr=num;
            prd=&num;
            break;
            }
            break;
#endif

#endif /* TARGET_TYPE == MPC5554 */

#if CAN_CHC_EN
            case   FLEXCAN_C:
            switch (b){
#ifdef CAN_CHC_BUF0_DIR
#if (CAN_CHC_BUF0_DIR==CAN_RX)
            case 0:
            wr =  CAN_CHC_b.w_0;
            bs =    CAN_CHC_b.buff_status_0;
            pbs = &CAN_CHC_b.buff_status_0;
            prd =  &CAN_CHC_b.r_0;
            ptrout = &(CAN_CHC_b.buffer_0[CAN_CHC_b.r_0]);
            num = CAN_CHC_BUF0_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF1_DIR
#if (CAN_CHC_BUF1_DIR==CAN_RX)
            case 1:
            wr =  CAN_CHC_b.w_1;
            bs =    CAN_CHC_b.buff_status_1;
            pbs = &CAN_CHC_b.buff_status_1;
            prd =  &CAN_CHC_b.r_1;
            ptrout = &(CAN_CHC_b.buffer_1[CAN_CHC_b.r_1]);
            num = CAN_CHC_BUF1_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF2_DIR
#if (CAN_CHC_BUF2_DIR==CAN_RX)
            case 2:
            wr =  CAN_CHC_b.w_2;
            bs =    CAN_CHC_b.buff_status_2;
            pbs = &CAN_CHC_b.buff_status_2;
            prd =  &CAN_CHC_b.r_2;
            ptrout = &(CAN_CHC_b.buffer_2[CAN_CHC_b.r_2]);
            num = CAN_CHC_BUF2_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF3_DIR
#if (CAN_CHC_BUF3_DIR==CAN_RX)
            case 3:
            wr =  CAN_CHC_b.w_3;
            bs =    CAN_CHC_b.buff_status_3;
            pbs = &CAN_CHC_b.buff_status_3;
            prd =  &CAN_CHC_b.r_3;
            ptrout = &(CAN_CHC_b.buffer_3[CAN_CHC_b.r_3]);
            num = CAN_CHC_BUF3_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF4_DIR
#if (CAN_CHC_BUF4_DIR==CAN_RX)
            case 4:
            wr =  CAN_CHC_b.w_4;
            bs =    CAN_CHC_b.buff_status_4;
            pbs = &CAN_CHC_b.buff_status_4;
            prd =  &CAN_CHC_b.r_4;
            ptrout = &(CAN_CHC_b.buffer_4[CAN_CHC_b.r_4]);
            num = CAN_CHC_BUF4_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF5_DIR
#if (CAN_CHC_BUF5_DIR==CAN_RX)
            case 5:
            wr =  CAN_CHC_b.w_5;
            bs =    CAN_CHC_b.buff_status_5;
            pbs = &CAN_CHC_b.buff_status_5;
            prd =  &CAN_CHC_b.r_5;
            ptrout = &(CAN_CHC_b.buffer_5[CAN_CHC_b.r_5]);
            num = CAN_CHC_BUF5_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF6_DIR
#if (CAN_CHC_BUF6_DIR==CAN_RX)
            case 6:
            wr =  CAN_CHC_b.w_6;
            bs =    CAN_CHC_b.buff_status_6;
            pbs = &CAN_CHC_b.buff_status_6;
            prd =  &CAN_CHC_b.r_6;
            ptrout = &(CAN_CHC_b.buffer_6[CAN_CHC_b.r_6]);
            num = CAN_CHC_BUF6_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF7_DIR
#if (CAN_CHC_BUF7_DIR==CAN_RX)
            case 7:
            wr =  CAN_CHC_b.w_7;
            bs =    CAN_CHC_b.buff_status_7;
            pbs = &CAN_CHC_b.buff_status_7;
            prd =  &CAN_CHC_b.r_7;
            ptrout = &(CAN_CHC_b.buffer_7[CAN_CHC_b.r_7]);
            num = CAN_CHC_BUF7_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF8_DIR
#if (CAN_CHC_BUF8_DIR==CAN_RX)
            case 8:
            wr =  CAN_CHC_b.w_8;
            bs =    CAN_CHC_b.buff_status_8;
            pbs = &CAN_CHC_b.buff_status_8;
            prd =  &CAN_CHC_b.r_8;
            ptrout = &(CAN_CHC_b.buffer_8[CAN_CHC_b.r_8]);
            num = CAN_CHC_BUF8_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF9_DIR
#if (CAN_CHC_BUF9_DIR==CAN_RX)
            case 9:
            wr =  CAN_CHC_b.w_9;
            bs =    CAN_CHC_b.buff_status_9;
            pbs = &CAN_CHC_b.buff_status_9;
            prd =  &CAN_CHC_b.r_9;
            ptrout = &(CAN_CHC_b.buffer_9[CAN_CHC_b.r_9]);
            num = CAN_CHC_BUF9_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF10_DIR
#if (CAN_CHC_BUF10_DIR==CAN_RX)
            case 10:
            wr =  CAN_CHC_b.w_10;
            bs =    CAN_CHC_b.buff_status_10;
            pbs = &CAN_CHC_b.buff_status_10;
            prd =  &CAN_CHC_b.r_10;
            ptrout = &(CAN_CHC_b.buffer_10[CAN_CHC_b.r_10]);
            num = CAN_CHC_BUF10_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF11_DIR
#if (CAN_CHC_BUF11_DIR==CAN_RX)
            case 11:
            wr =  CAN_CHC_b.w_11;
            bs =    CAN_CHC_b.buff_status_11;
            pbs = &CAN_CHC_b.buff_status_11;
            prd =  &CAN_CHC_b.r_11;
            ptrout = &(CAN_CHC_b.buffer_11[CAN_CHC_b.r_11]);
            num = CAN_CHC_BUF11_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF12_DIR
#if (CAN_CHC_BUF12_DIR==CAN_RX)
            case 12:
            wr =  CAN_CHC_b.w_12;
            bs =    CAN_CHC_b.buff_status_12;
            pbs = &CAN_CHC_b.buff_status_12;
            prd =  &CAN_CHC_b.r_12;
            ptrout = &(CAN_CHC_b.buffer_12[CAN_CHC_b.r_12]);
            num = CAN_CHC_BUF12_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF13_DIR
#if (CAN_CHC_BUF13_DIR==CAN_RX)
            case 13:
            wr =  CAN_CHC_b.w_13;
            bs =    CAN_CHC_b.buff_status_13;
            pbs = &CAN_CHC_b.buff_status_13;
            prd =  &CAN_CHC_b.r_13;
            ptrout = &(CAN_CHC_b.buffer_13[CAN_CHC_b.r_13]);
            num = CAN_CHC_BUF13_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF14_DIR
#if (CAN_CHC_BUF14_DIR==CAN_RX)
            case 14:
            wr =  CAN_CHC_b.w_14;
            bs =    CAN_CHC_b.buff_status_14;
            pbs = &CAN_CHC_b.buff_status_14;
            prd =  &CAN_CHC_b.r_14;
            ptrout = &(CAN_CHC_b.buffer_14[CAN_CHC_b.r_14]);
            num = CAN_CHC_BUF14_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF15_DIR
#if (CAN_CHC_BUF15_DIR==CAN_RX)
            case 15:
            wr =  CAN_CHC_b.w_15;
            bs =    CAN_CHC_b.buff_status_15;
            pbs = &CAN_CHC_b.buff_status_15;
            prd =  &CAN_CHC_b.r_15;
            ptrout = &(CAN_CHC_b.buffer_15[CAN_CHC_b.r_15]);
            num = CAN_CHC_BUF15_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF16_DIR
#if (CAN_CHC_BUF16_DIR==CAN_RX)
            case 16:
            wr =  CAN_CHC_b.w_16;
            bs =    CAN_CHC_b.buff_status_16;
            pbs = &CAN_CHC_b.buff_status_16;
            prd =  &CAN_CHC_b.r_16;
            ptrout = &(CAN_CHC_b.buffer_16[CAN_CHC_b.r_16]);
            num = CAN_CHC_BUF16_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF17_DIR
#if (CAN_CHC_BUF17_DIR==CAN_RX)
            case 17:
            wr =  CAN_CHC_b.w_17;
            bs =    CAN_CHC_b.buff_status_17;
            pbs = &CAN_CHC_b.buff_status_17;
            prd =  &CAN_CHC_b.r_17;
            ptrout = &(CAN_CHC_b.buffer_17[CAN_CHC_b.r_17]);
            num = CAN_CHC_BUF17_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF18_DIR
#if (CAN_CHC_BUF18_DIR==CAN_RX)
            case 18:
            wr =  CAN_CHC_b.w_18;
            bs =    CAN_CHC_b.buff_status_18;
            pbs = &CAN_CHC_b.buff_status_18;
            prd =  &CAN_CHC_b.r_18;
            ptrout = &(CAN_CHC_b.buffer_18[CAN_CHC_b.r_18]);
            num = CAN_CHC_BUF18_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF19_DIR
#if (CAN_CHC_BUF19_DIR==CAN_RX)
            case 19:
            wr =  CAN_CHC_b.w_19;
            bs =    CAN_CHC_b.buff_status_19;
            pbs = &CAN_CHC_b.buff_status_19;
            prd =  &CAN_CHC_b.r_19;
            ptrout = &(CAN_CHC_b.buffer_19[CAN_CHC_b.r_19]);
            num = CAN_CHC_BUF19_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF20_DIR
#if (CAN_CHC_BUF20_DIR==CAN_RX)
            case 20:
            wr =  CAN_CHC_b.w_20;
            bs =    CAN_CHC_b.buff_status_20;
            pbs = &CAN_CHC_b.buff_status_20;
            prd =  &CAN_CHC_b.r_20;
            ptrout = &(CAN_CHC_b.buffer_20[CAN_CHC_b.r_20]);
            num = CAN_CHC_BUF20_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF21_DIR
#if (CAN_CHC_BUF21_DIR==CAN_RX)
            case 21:
            wr =  CAN_CHC_b.w_21;
            bs =    CAN_CHC_b.buff_status_21;
            pbs = &CAN_CHC_b.buff_status_21;
            prd =  &CAN_CHC_b.r_21;
            ptrout = &(CAN_CHC_b.buffer_21[CAN_CHC_b.r_21]);
            num = CAN_CHC_BUF21_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF22_DIR
#if (CAN_CHC_BUF22_DIR==CAN_RX)
            case 22:
            wr =  CAN_CHC_b.w_22;
            bs =    CAN_CHC_b.buff_status_22;
            pbs = &CAN_CHC_b.buff_status_22;
            prd =  &CAN_CHC_b.r_22;
            ptrout = &(CAN_CHC_b.buffer_22[CAN_CHC_b.r_22]);
            num = CAN_CHC_BUF22_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF23_DIR
#if (CAN_CHC_BUF23_DIR==CAN_RX)
            case 23:
            wr =  CAN_CHC_b.w_23;
            bs =    CAN_CHC_b.buff_status_23;
            pbs = &CAN_CHC_b.buff_status_23;
            prd =  &CAN_CHC_b.r_23;
            ptrout = &(CAN_CHC_b.buffer_23[CAN_CHC_b.r_23]);
            num = CAN_CHC_BUF23_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF24_DIR
#if (CAN_CHC_BUF24_DIR==CAN_RX)
            case 24:
            wr =  CAN_CHC_b.w_24;
            bs =    CAN_CHC_b.buff_status_24;
            pbs = &CAN_CHC_b.buff_status_24;
            prd =  &CAN_CHC_b.r_24;
            ptrout = &(CAN_CHC_b.buffer_24[CAN_CHC_b.r_24]);
            num = CAN_CHC_BUF24_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF25_DIR
#if (CAN_CHC_BUF25_DIR==CAN_RX)
            case 25:
            wr =  CAN_CHC_b.w_25;
            bs =    CAN_CHC_b.buff_status_25;
            pbs = &CAN_CHC_b.buff_status_25;
            prd =  &CAN_CHC_b.r_25;
            ptrout = &(CAN_CHC_b.buffer_25[CAN_CHC_b.r_25]);
            num = CAN_CHC_BUF25_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF26_DIR
#if (CAN_CHC_BUF26_DIR==CAN_RX)
            case 26:
            wr =  CAN_CHC_b.w_26;
            bs =    CAN_CHC_b.buff_status_26;
            pbs = &CAN_CHC_b.buff_status_26;
            prd =  &CAN_CHC_b.r_26;
            ptrout = &(CAN_CHC_b.buffer_26[CAN_CHC_b.r_26]);
            num = CAN_CHC_BUF26_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF27_DIR
#if (CAN_CHC_BUF27_DIR==CAN_RX)
            case 27:
            wr =  CAN_CHC_b.w_27;
            bs =    CAN_CHC_b.buff_status_27;
            pbs = &CAN_CHC_b.buff_status_27;
            prd =  &CAN_CHC_b.r_27;
            ptrout = &(CAN_CHC_b.buffer_27[CAN_CHC_b.r_27]);
            num = CAN_CHC_BUF27_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF28_DIR
#if (CAN_CHC_BUF28_DIR==CAN_RX)
            case 28:
            wr =  CAN_CHC_b.w_28;
            bs =    CAN_CHC_b.buff_status_28;
            pbs = &CAN_CHC_b.buff_status_28;
            prd =  &CAN_CHC_b.r_28;
            ptrout = &(CAN_CHC_b.buffer_28[CAN_CHC_b.r_28]);
            num = CAN_CHC_BUF28_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF29_DIR
#if (CAN_CHC_BUF29_DIR==CAN_RX)
            case 29:
            wr =  CAN_CHC_b.w_29;
            bs =    CAN_CHC_b.buff_status_29;
            pbs = &CAN_CHC_b.buff_status_29;
            prd =  &CAN_CHC_b.r_29;
            ptrout = &(CAN_CHC_b.buffer_29[CAN_CHC_b.r_29]);
            num = CAN_CHC_BUF29_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF30_DIR
#if (CAN_CHC_BUF30_DIR==CAN_RX)
            case 30:
            wr =  CAN_CHC_b.w_30;
            bs =    CAN_CHC_b.buff_status_30;
            pbs = &CAN_CHC_b.buff_status_30;
            prd =  &CAN_CHC_b.r_30;
            ptrout = &(CAN_CHC_b.buffer_30[CAN_CHC_b.r_30]);
            num = CAN_CHC_BUF30_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF31_DIR
#if (CAN_CHC_BUF31_DIR==CAN_RX)
            case 31:
            wr =  CAN_CHC_b.w_31;
            bs =    CAN_CHC_b.buff_status_31;
            pbs = &CAN_CHC_b.buff_status_31;
            prd =  &CAN_CHC_b.r_31;
            ptrout = &(CAN_CHC_b.buffer_31[CAN_CHC_b.r_31]);
            num = CAN_CHC_BUF31_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF32_DIR
#if (CAN_CHC_BUF32_DIR==CAN_RX)
            case 32:
            wr =  CAN_CHC_b.w_32;
            bs =    CAN_CHC_b.buff_status_32;
            pbs = &CAN_CHC_b.buff_status_32;
            prd =  &CAN_CHC_b.r_32;
            ptrout = &(CAN_CHC_b.buffer_32[CAN_CHC_b.r_32]);
            num = CAN_CHC_BUF32_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF33_DIR
#if (CAN_CHC_BUF33_DIR==CAN_RX)
            case 33:
            wr =  CAN_CHC_b.w_33;
            bs =    CAN_CHC_b.buff_status_33;
            pbs = &CAN_CHC_b.buff_status_33;
            prd =  &CAN_CHC_b.r_33;
            ptrout = &(CAN_CHC_b.buffer_33[CAN_CHC_b.r_33]);
            num = CAN_CHC_BUF33_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF34_DIR
#if (CAN_CHC_BUF34_DIR==CAN_RX)
            case 34:
            wr =  CAN_CHC_b.w_34;
            bs =    CAN_CHC_b.buff_status_34;
            pbs = &CAN_CHC_b.buff_status_34;
            prd =  &CAN_CHC_b.r_34;
            ptrout = &(CAN_CHC_b.buffer_34[CAN_CHC_b.r_34]);
            num = CAN_CHC_BUF34_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF35_DIR
#if (CAN_CHC_BUF35_DIR==CAN_RX)
            case 35:
            wr =  CAN_CHC_b.w_35;
            bs =    CAN_CHC_b.buff_status_35;
            pbs = &CAN_CHC_b.buff_status_35;
            prd =  &CAN_CHC_b.r_35;
            ptrout = &(CAN_CHC_b.buffer_35[CAN_CHC_b.r_35]);
            num = CAN_CHC_BUF35_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF36_DIR
#if (CAN_CHC_BUF36_DIR==CAN_RX)
            case 36:
            wr =  CAN_CHC_b.w_36;
            bs =    CAN_CHC_b.buff_status_36;
            pbs = &CAN_CHC_b.buff_status_36;
            prd =  &CAN_CHC_b.r_36;
            ptrout = &(CAN_CHC_b.buffer_36[CAN_CHC_b.r_36]);
            num = CAN_CHC_BUF36_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF37_DIR
#if (CAN_CHC_BUF37_DIR==CAN_RX)
            case 37:
            wr =  CAN_CHC_b.w_37;
            bs =    CAN_CHC_b.buff_status_37;
            pbs = &CAN_CHC_b.buff_status_37;
            prd =  &CAN_CHC_b.r_37;
            ptrout = &(CAN_CHC_b.buffer_37[CAN_CHC_b.r_37]);
            num = CAN_CHC_BUF37_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF38_DIR
#if (CAN_CHC_BUF38_DIR==CAN_RX)
            case 38:
            wr =  CAN_CHC_b.w_38;
            bs =    CAN_CHC_b.buff_status_38;
            pbs = &CAN_CHC_b.buff_status_38;
            prd =  &CAN_CHC_b.r_38;
            ptrout = &(CAN_CHC_b.buffer_38[CAN_CHC_b.r_38]);
            num = CAN_CHC_BUF38_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF39_DIR
#if (CAN_CHC_BUF39_DIR==CAN_RX)
            case 39:
            wr =  CAN_CHC_b.w_39;
            bs =    CAN_CHC_b.buff_status_39;
            pbs = &CAN_CHC_b.buff_status_39;
            prd =  &CAN_CHC_b.r_39;
            ptrout = &(CAN_CHC_b.buffer_39[CAN_CHC_b.r_39]);
            num = CAN_CHC_BUF39_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF40_DIR
#if (CAN_CHC_BUF40_DIR==CAN_RX)
            case 40:
            wr =  CAN_CHC_b.w_40;
            bs =    CAN_CHC_b.buff_status_40;
            pbs = &CAN_CHC_b.buff_status_40;
            prd =  &CAN_CHC_b.r_40;
            ptrout = &(CAN_CHC_b.buffer_40[CAN_CHC_b.r_40]);
            num = CAN_CHC_BUF40_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF41_DIR
#if (CAN_CHC_BUF41_DIR==CAN_RX)
            case 41:
            wr =  CAN_CHC_b.w_41;
            bs =    CAN_CHC_b.buff_status_41;
            pbs = &CAN_CHC_b.buff_status_41;
            prd =  &CAN_CHC_b.r_41;
            ptrout = &(CAN_CHC_b.buffer_41[CAN_CHC_b.r_41]);
            num = CAN_CHC_BUF41_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF42_DIR
#if (CAN_CHC_BUF42_DIR==CAN_RX)
            case 42:
            wr =  CAN_CHC_b.w_42;
            bs =    CAN_CHC_b.buff_status_42;
            pbs = &CAN_CHC_b.buff_status_42;
            prd =  &CAN_CHC_b.r_42;
            ptrout = &(CAN_CHC_b.buffer_42[CAN_CHC_b.r_42]);
            num = CAN_CHC_BUF42_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF43_DIR
#if (CAN_CHC_BUF43_DIR==CAN_RX)
            case 43:
            wr =  CAN_CHC_b.w_43;
            bs =    CAN_CHC_b.buff_status_43;
            pbs = &CAN_CHC_b.buff_status_43;
            prd =  &CAN_CHC_b.r_43;
            ptrout = &(CAN_CHC_b.buffer_43[CAN_CHC_b.r_43]);
            num = CAN_CHC_BUF43_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF44_DIR
#if (CAN_CHC_BUF44_DIR==CAN_RX)
            case 44:
            wr =  CAN_CHC_b.w_44;
            bs =    CAN_CHC_b.buff_status_44;
            pbs = &CAN_CHC_b.buff_status_44;
            prd =  &CAN_CHC_b.r_44;
            ptrout = &(CAN_CHC_b.buffer_44[CAN_CHC_b.r_44]);
            num = CAN_CHC_BUF44_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF45_DIR
#if (CAN_CHC_BUF45_DIR==CAN_RX)
            case 45:
            wr =  CAN_CHC_b.w_45;
            bs =    CAN_CHC_b.buff_status_45;
            pbs = &CAN_CHC_b.buff_status_45;
            prd =  &CAN_CHC_b.r_45;
            ptrout = &(CAN_CHC_b.buffer_45[CAN_CHC_b.r_45]);
            num = CAN_CHC_BUF45_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF46_DIR
#if (CAN_CHC_BUF46_DIR==CAN_RX)
            case 46:
            wr =  CAN_CHC_b.w_46;
            bs =    CAN_CHC_b.buff_status_46;
            pbs = &CAN_CHC_b.buff_status_46;
            prd =  &CAN_CHC_b.r_46;
            ptrout = &(CAN_CHC_b.buffer_46[CAN_CHC_b.r_46]);
            num = CAN_CHC_BUF46_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF47_DIR
#if (CAN_CHC_BUF47_DIR==CAN_RX)
            case 47:
            wr =  CAN_CHC_b.w_47;
            bs =    CAN_CHC_b.buff_status_47;
            pbs = &CAN_CHC_b.buff_status_47;
            prd =  &CAN_CHC_b.r_47;
            ptrout = &(CAN_CHC_b.buffer_47[CAN_CHC_b.r_47]);
            num = CAN_CHC_BUF47_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF48_DIR
#if (CAN_CHC_BUF48_DIR==CAN_RX)
            case 48:
            wr =  CAN_CHC_b.w_48;
            bs =    CAN_CHC_b.buff_status_48;
            pbs = &CAN_CHC_b.buff_status_48;
            prd =  &CAN_CHC_b.r_48;
            ptrout = &(CAN_CHC_b.buffer_48[CAN_CHC_b.r_48]);
            num = CAN_CHC_BUF48_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF49_DIR
#if (CAN_CHC_BUF49_DIR==CAN_RX)
            case 49:
            wr =  CAN_CHC_b.w_49;
            bs =    CAN_CHC_b.buff_status_49;
            pbs = &CAN_CHC_b.buff_status_49;
            prd =  &CAN_CHC_b.r_49;
            ptrout = &(CAN_CHC_b.buffer_49[CAN_CHC_b.r_49]);
            num = CAN_CHC_BUF49_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF50_DIR
#if (CAN_CHC_BUF50_DIR==CAN_RX)
            case 50:
            wr =  CAN_CHC_b.w_50;
            bs =    CAN_CHC_b.buff_status_50;
            pbs = &CAN_CHC_b.buff_status_50;
            prd =  &CAN_CHC_b.r_50;
            ptrout = &(CAN_CHC_b.buffer_50[CAN_CHC_b.r_50]);
            num = CAN_CHC_BUF50_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF51_DIR
#if (CAN_CHC_BUF51_DIR==CAN_RX)
            case 51:
            wr =  CAN_CHC_b.w_51;
            bs =    CAN_CHC_b.buff_status_51;
            pbs = &CAN_CHC_b.buff_status_51;
            prd =  &CAN_CHC_b.r_51;
            ptrout = &(CAN_CHC_b.buffer_51[CAN_CHC_b.r_51]);
            num = CAN_CHC_BUF51_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF52_DIR
#if (CAN_CHC_BUF52_DIR==CAN_RX)
            case 52:
            wr =  CAN_CHC_b.w_52;
            bs =    CAN_CHC_b.buff_status_52;
            pbs = &CAN_CHC_b.buff_status_52;
            prd =  &CAN_CHC_b.r_52;
            ptrout = &(CAN_CHC_b.buffer_52[CAN_CHC_b.r_52]);
            num = CAN_CHC_BUF52_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF53_DIR
#if (CAN_CHC_BUF53_DIR==CAN_RX)
            case 53:
            wr =  CAN_CHC_b.w_53;
            bs =    CAN_CHC_b.buff_status_53;
            pbs = &CAN_CHC_b.buff_status_53;
            prd =  &CAN_CHC_b.r_53;
            ptrout = &(CAN_CHC_b.buffer_53[CAN_CHC_b.r_53]);
            num = CAN_CHC_BUF53_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF54_DIR
#if (CAN_CHC_BUF54_DIR==CAN_RX)
            case 54:
            wr =  CAN_CHC_b.w_54;
            bs =    CAN_CHC_b.buff_status_54;
            pbs = &CAN_CHC_b.buff_status_54;
            prd =  &CAN_CHC_b.r_54;
            ptrout = &(CAN_CHC_b.buffer_54[CAN_CHC_b.r_54]);
            num = CAN_CHC_BUF54_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF55_DIR
#if (CAN_CHC_BUF55_DIR==CAN_RX)
            case 55:
            wr =  CAN_CHC_b.w_55;
            bs =    CAN_CHC_b.buff_status_55;
            pbs = &CAN_CHC_b.buff_status_55;
            prd =  &CAN_CHC_b.r_55;
            ptrout = &(CAN_CHC_b.buffer_55[CAN_CHC_b.r_55]);
            num = CAN_CHC_BUF55_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF56_DIR
#if (CAN_CHC_BUF56_DIR==CAN_RX)
            case 56:
            wr =  CAN_CHC_b.w_56;
            bs =    CAN_CHC_b.buff_status_56;
            pbs = &CAN_CHC_b.buff_status_56;
            prd =  &CAN_CHC_b.r_56;
            ptrout = &(CAN_CHC_b.buffer_56[CAN_CHC_b.r_56]);
            num = CAN_CHC_BUF56_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF57_DIR
#if (CAN_CHC_BUF57_DIR==CAN_RX)
            case 57:
            wr =  CAN_CHC_b.w_57;
            bs =    CAN_CHC_b.buff_status_57;
            pbs = &CAN_CHC_b.buff_status_57;
            prd =  &CAN_CHC_b.r_57;
            ptrout = &(CAN_CHC_b.buffer_57[CAN_CHC_b.r_57]);
            num = CAN_CHC_BUF57_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF58_DIR
#if (CAN_CHC_BUF58_DIR==CAN_RX)
            case 58:
            wr =  CAN_CHC_b.w_58;
            bs =    CAN_CHC_b.buff_status_58;
            pbs = &CAN_CHC_b.buff_status_58;
            prd =  &CAN_CHC_b.r_58;
            ptrout = &(CAN_CHC_b.buffer_58[CAN_CHC_b.r_58]);
            num = CAN_CHC_BUF58_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF59_DIR
#if (CAN_CHC_BUF59_DIR==CAN_RX)
            case 59:
            wr =  CAN_CHC_b.w_59;
            bs =    CAN_CHC_b.buff_status_59;
            pbs = &CAN_CHC_b.buff_status_59;
            prd =  &CAN_CHC_b.r_59;
            ptrout = &(CAN_CHC_b.buffer_59[CAN_CHC_b.r_59]);
            num = CAN_CHC_BUF59_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF60_DIR
#if (CAN_CHC_BUF60_DIR==CAN_RX)
            case 60:
            wr =  CAN_CHC_b.w_60;
            bs =    CAN_CHC_b.buff_status_60;
            pbs = &CAN_CHC_b.buff_status_60;
            prd =  &CAN_CHC_b.r_60;
            ptrout = &(CAN_CHC_b.buffer_60[CAN_CHC_b.r_60]);
            num = CAN_CHC_BUF60_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF61_DIR
#if (CAN_CHC_BUF61_DIR==CAN_RX)
            case 61:
            wr =  CAN_CHC_b.w_61;
            bs =    CAN_CHC_b.buff_status_61;
            pbs = &CAN_CHC_b.buff_status_61;
            prd =  &CAN_CHC_b.r_61;
            ptrout = &(CAN_CHC_b.buffer_61[CAN_CHC_b.r_61]);
            num = CAN_CHC_BUF61_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF62_DIR
#if (CAN_CHC_BUF62_DIR==CAN_RX)
            case 62:
            wr =  CAN_CHC_b.w_62;
            bs =    CAN_CHC_b.buff_status_62;
            pbs = &CAN_CHC_b.buff_status_62;
            prd =  &CAN_CHC_b.r_62;
            ptrout = &(CAN_CHC_b.buffer_62[CAN_CHC_b.r_62]);
            num = CAN_CHC_BUF62_NUM;
            break;
#endif
#endif
#ifdef CAN_CHC_BUF63_DIR
#if (CAN_CHC_BUF63_DIR==CAN_RX)
            case 63:
            wr =  CAN_CHC_b.w_63;
            bs =    CAN_CHC_b.buff_status_63;
            pbs = &CAN_CHC_b.buff_status_63;
            prd =  &CAN_CHC_b.r_63;
            ptrout = &(CAN_CHC_b.buffer_63[CAN_CHC_b.r_63]);
            num = CAN_CHC_BUF63_NUM;
            break;
#endif
#endif
            default:
            wr=num;
            prd=&num;
            break;
            }
            break;
#endif

            default:
            wr=num;
            prd=&num;
            break;
        }

        if (wr==*prd) 
        {
            if(!((bs & BUFFER_FULL)||(bs & BUFFER_OVERRUN)))
            {
                res = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                *ptr = ptrout;
                (*prd)++;
                if((*prd)==num)
                {
                    (*prd)=0;
                }

                if(bs == BUFFER_OVERRUN)
                {
                    res=CAN_RX_BUFFER_OVERRUN;
                }

                *pbs &= ~(BUFFER_OVERRUN|BUFFER_FULL);
            }
        }
        else
        {
            *ptr = ptrout;
            (*prd)++;
            if((*prd)==num) 
            {
                (*prd)=0;
            }
        }
    }

    //Dump dei puntatori 
    DUMP_R_PTR(rPtrValue,C,31)
	DUMP_W_PTR(wPtrValue,C,31)	
	
    return res;

}


/*********************************************************************************************/
/* FUNCTION     : CAN_GetStatus( Channel)                                                    */
/* PURPOSE      :                                                                            */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_GetStatus(uint8_t Channel)
{
    struct FLEXCAN2_tag  * CAN_ptr;
    int16_t res = NO_ERROR;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if(CAN_ptr->ESR.B.BOFFINT==1)
    {
        //          (CAN_ptr->ESR.R)&=(1<<2);
        res=CAN_BUSOFF;
    }
    else    // no bus-off
    {
        if (   (CAN_ptr->ECR.B.RXECNT > 127) || (CAN_ptr->ECR.B.TXECNT > 127) )
        {
            res=CAN_ERR_PASSIVE;
        }
        else // at least one counter > 127
        {
            if (   (CAN_ptr->ECR.B.RXECNT > 0) || (CAN_ptr->ECR.B.TXECNT > 0) )  // at least one counter > 0
            {
                res=CAN_ERR_ACTIVE;
            }
        }
    }

    return res;
}

/*********************************************************************************************/
/* FUNCTION     : int16_t CAN_BusOffRecovery(uint8_t Channel)                                */
/* PURPOSE      :                                                                            */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_BusOffRecovery(uint8_t Channel)
{
    struct FLEXCAN2_tag  * CAN_ptr;
    int16_t res = NO_ERROR;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if(CAN_ptr->ESR.B.BOFFINT==1)
    {
        (CAN_ptr->ESR.R)&=(1<<2);
        res=CAN_BUSOFF;
    }
    return res;
}

/*********************************************************************************************/
/* FUNCTION     : CAN_GetTxBufferStatus(uint8_t Channel, uint8_t nbuf)                       */
/* PURPOSE      :                                                                            */
/*                                                                                           */
/* INPUTS NOTES : Channel of the FlexCan module                                              */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
int16_t CAN_GetTxBufferStatus(uint8_t Channel,
                               uint8_t nbuf)
{
    struct FLEXCAN2_tag  * CAN_ptr;
    int16_t res = NO_ERROR;
    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[Channel];

    if ((CAN_ptr->ESR.B.IDLE==0) && (CAN_ptr->ESR.B.TXRX==1) && (CAN_ptr->BUF[nbuf].CS.B.CODE==TX_BUFFER_CODE_TRANSMIT))
    {
        res=CAN_TX_BUSY;
    }

    return res;
 }

/******************************************************************************/
/* FUNCTION     : CAN_ActivateKWPCAN (uint8_t ch, uint8_t nbuf)                                         */
/* PURPOSE      : This function performs KWP CAN channel activation                                     */
/* INPUTS NOTES : uint8_t ch, Channel of the FlexCan module                                              */
/*                         uint8_t nbuf, used MB                                                                             */
/*                                                                            								   */
/*                                                                            								   */
/*                                                                            								   */
/* RETURN NOTES : none.                                                                                                  */
/* WARNING      :                                                                                                              */
/*                                                                                                                                    */
/*****************************************************************************/
void CAN_ActivateKWPCAN (uint8_t ch, uint8_t nbuf)
{
	struct FLEXCAN2_tag * CAN_ptr;

	CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[ch];
	CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_ACTIVE_EMPTY;
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/**************************************************************************************/
/* FUNCTION     : CAN_ConfigCh (Id_Channel Channel)                                   */
/* PURPOSE      : This function performs the main configuration of the FLEXCAN        */
/* INPUTS NOTES : FlexCan module and a pointer to structure encloses the configuration*/
/*                parameters                                                          */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/*                                                                                    */
/* RETURN NOTES : INVALID_CAN_CHANNEL  channel not exist                              */
/*                BIT_RATE_ERROR   value not valid (change bit rate)                  */
/*                SMP_PNT_ERR      sample point value not valid                       */
/*                MODULE_OK        configuration ok                                   */
/* WARNING      :                                                                     */
/*                                                                                    */
/**************************************************************************************/
static int16_t CAN_ConfigCh (uint8_t ch, 
                             uint8_t brate, 
                             uint32_t gmask)
{

    uint8_t x;
    struct FLEXCAN2_tag  * pChan;
    timeoutHandler_t cfgHandler;
    int16_t retValue = NO_ERROR;
    
    uint8_t status;

    pChan = (struct FLEXCAN2_tag *)CAN_Channel[ch];
    pChan->MCR.B.SOFTRST = 1;                     /* Soft-reset of the FlexCAN module */
    TIMING_SetTimeout(CAN_TIMEOUT, &cfgHandler);
    while ((pChan->MCR.B.SOFTRST==1) && (retValue == NO_ERROR))
    {
        TIMING_GetTimeoutStatus(cfgHandler, &status);
        if(status == TIMEOUT_EXPIRED)
        {
#ifdef CHECK_BIOS_FAULTS
            BIOS_Faults |= BIOS_FAILURE_CAN;
#endif /* CHECK_BIOS_FAULTS */
            retValue = PERIPHERAL_NOT_INITIALIZED;
        }
    }

    if (retValue == NO_ERROR)
    {
        pChan->MCR.B.MDIS=1;
        pChan->CR.B.CLKSRC=0x1;
        pChan->MCR.B.MDIS=0;
        pChan->MCR.B.HALT=0x1;

        /* Module Configuration Register */

        switch (brate) 
        {
            case  CAN_BR_100k:   
            {
                pChan->CR.R=CR_100k;
                break;
            }
            case  CAN_BR_125k:   
            {
                pChan->CR.R=CR_125k;
                break;
            }
            case  CAN_BR_250k:   
            {
                pChan->CR.R=CR_250k;
                break;
            }
            case  CAN_BR_500k:   
            {
                pChan->CR.R=CR_500k;
                break;
            }
            case  CAN_BR_800k:   
            {
                pChan->CR.R=CR_800k;
                break;
            }
            case  CAN_BR_1000k:  
            {
                pChan->CR.R=CR_1000k;
                break;
            }
            default:
            {
                retValue = BIT_RATE_ERROR;
                break;
            }
        }

        if (retValue == NO_ERROR)
        {
            /* Inicialization of all message buffers*/

            pChan->MCR.R= (pChan->MCR.R & ~0x3F) | (CAN_ChannelMBlen[ch]) ; //sto inizializzando (0x0F) MB

            for(x = 0; (x < (CAN_ChannelMBlen[ch] + 1)); x++) 
            {
                pChan->BUF[x].CS.R=0;
                pChan->BUF[x].ID.R=0;
                memset((void*)pChan->BUF[x].DATA.B,0,8);
            }

            pChan->RXGMASK.R= gmask;      /* RX Global Mask */
            pChan->RX14MASK.R=gmask;      /* RX 14 Mask */
            pChan->RX15MASK.R=gmask;      /* RX 15 Mask */


            pChan->MCR.B.HALT=0x00;         /* Set MCR HALT bit field : exit to Freeze Mode*/
            pChan->MCR.B.FRZ=0x00;


#if CAN_CHA_EN
            memset(&CAN_CHA_b,0,sizeof(t_CAN_CHA_b));
#endif

#if CAN_CHB_EN
            memset(&CAN_CHB_b,0,sizeof(t_CAN_CHB_b));
#endif

#if CAN_CHC_EN
            memset(&CAN_CHC_b,0,sizeof(t_CAN_CHC_b));
#endif
        }
    }

    return (retValue);
}

/**************************************************************************************************************************/
/* FUNCTION     : int16_t CAN_BufferConfig (uint8_t ch, uint8_t nbuf, uint8_t dir, uint8_t siz, uint8_t ide, uint32_t id) */
/* PURPOSE      : This function performs the buffer configuration of the FLEXCAN                                          */
/* INPUTS NOTES : Channel of the FlexCan module, number of the MB and a pointer to configuration struct                   */
/*                                                                                                                        */
/*                                                                                                                        */
/*                                                                                                                        */
/*                                                                                                                        */
/* RETURN NOTES : none.                                                                                                   */
/* WARNING      :                                                                                                         */
/*                                                                                                                        */
/**************************************************************************************************************************/

static int16_t CAN_BufferConfig (uint8_t ch, 
                                 uint8_t nbuf,
                                 uint8_t dir, 
                                 uint8_t siz, 
                                 uint8_t ide, 
                                 uint32_t id) 
{
    struct FLEXCAN2_tag * CAN_ptr;

    CAN_ptr= (struct FLEXCAN2_tag *)CAN_Channel[ch];
    if (dir==CAN_TX) 
    {
        CAN_ptr->BUF[nbuf].CS.B.CODE=TX_BUFFER_CODE_INACTIVE;    //set CODE=8 for tx buffer
    } 
    else 
    {
        CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_DISABLE;    //set CODE=0 for rx buffer
    }

    CAN_ptr->BUF[nbuf].CS.B.RTR=0x0;
    if (ide==0)
    {
        CAN_ptr->BUF[nbuf].CS.B.SRR=0x0;    //set the SRR=0
        CAN_ptr->BUF[nbuf].CS.B.IDE=0x0;    //set the IDE=0
        CAN_ptr->BUF[nbuf].ID.B.STD_ID=id;
        CAN_ptr->BUF[nbuf].ID.B.EXT_ID=0;
    }
    else
    {
        CAN_ptr->BUF[nbuf].CS.B.SRR=0x1;    //set the SRR=1
        CAN_ptr->BUF[nbuf].CS.B.IDE=0x1;    //set the IDE=1
        CAN_ptr->BUF[nbuf].ID.R=id;
    }


    if (dir==CAN_RX)
    {
        if (nbuf<32)
        {
            CAN_ptr->IMRL.R &=(~(1<<nbuf));
            CAN_ptr->IMRL.R |=(1<< nbuf);
        }
        else
        {
            CAN_ptr->IMRH.R &=(~(1<<(nbuf-32)));
            CAN_ptr->IMRH.R |=(1<< (nbuf-32));
        }

        CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_DISABLE;
        /* 
		If pending KWP2000 messages arrives before tpe initialization,
	       synchronization is lost btw read and write buffers
	       this patch corrects this error
	      */
	    if (nbuf != KWP2_RXCH)
	    {
	    	CAN_ptr->BUF[nbuf].CS.B.CODE=RX_BUFFER_CODE_ACTIVE_EMPTY;
	    }
    } 
    else
    {
        CAN_ptr->BUF[nbuf].CS.B.LENGTH=siz;
    }

    return(NO_ERROR);
}



#endif
