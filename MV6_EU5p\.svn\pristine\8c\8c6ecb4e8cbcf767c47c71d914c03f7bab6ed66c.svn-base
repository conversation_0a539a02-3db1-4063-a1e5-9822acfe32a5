#ifndef _SCI_H_
#define _SCI_H_

#include "typedefs.h"
#include "os_api.h"


#define SCI_STOP_TX         0
#define SCI_STOP_RX         1
#define SCI_OVERRUN_ERROR	2

#define TX_ENABLE    1
#define TX_DISABLE   0
#define RX_ENABLE    1
#define RX_DISABLE   0

#define SCI_TX_IDLE  0
#define SCI_TX_BUSY	 1

#define SCI_RX        0 
#define SCI_TX        1 

#define MASK_T8_BIT_AND 0x0100
#define MASK_R8_BIT_AND 0x8000


/******--------------------------------------******/

#define SCI_ENABLE_MODULE		0x00

#define SCIA_LOOPS_DISABLE      0x00
#define SCIA_RSRC_DISABLE  		0x00

#define SCIA_WAKE_DISABLE		0x00
#define SCIA_ILT        		0x01
#define SCIA_PT_EVEN_PARITY     0x00

#define SCIA_TE_DISABLE         0x00
#define SCIA_RE_DISABLE         0x00
#define SCIA_TIE_DISABLE		0x00
#define SCIA_TCIE_DISABLE       0x00
#define SCIA_TCIE_ENABLE		0x01
#define SCIA_RIE_DISABLE        0x00
#define SCIA_RIE_ENABLE			0x01
#define SCIA_ILIE_DISABLE		0x00
#define SCIA_TE_ENABLE			0x01
#define SCIA_RE_ENABLE			0x01
#define SCIA_RWU_DISABLE		0x00
#define SCIA_SBK_DISABLE		0x00
#define SCIA_PE_DISABLE         0x00
#define SCIA_TDRE_CLEAR         0x01
#define SCIA_TC_CLEAR           0x01
#define SCIA_RDRF_CLEAR         0x01
#define SCIA_IDLE_CLEAR         0x01
#define SCIA_OR_CLEAR           0x01
#define SCIA_NF_CLEAR           0x01
#define SCIA_FE_CLEAR           0x01
#define SCIA_PF_CLEAR           0x01
#define SCIA_BERR_CLEAR         0x01
#define SCIA_RAF_CLEAR          0x01
#define SCIA_RXRDY_CLEAR        0x01
#define SCIA_TXRDY_CLEAR        0x01
#define SCIA_LWAKE_CLEAR        0x01
#define SCIA_STO_CLEAR          0x01
#define SCIA_PBERR_CLEAR        0x01
#define SCIA_CERR_CLEAR         0x01
#define SCIA_CKERR_CLEAR        0x01
#define SCIA_FRC_CLEAR          0x01
#define SCIA_OVFL_CLEAR         0x01



#define SCIB_LOOPS_DISABLE      0x00                
#define SCIB_RSRC_DISABLE  		0x00                

#define SCIB_WAKE_DISABLE		0x00                
#define SCIB_ILT			    0x01                
#define SCIB_PT_EVEN_PARITY	    0x00

#define SCIB_TIE_DISABLE		0x00                
#define SCIB_TCIE_ENABLE		0x01                
#define SCIB_RIE_ENABLE			0x01                
#define SCIB_ILIE_DISABLE		0x00                
#define SCIB_TE_ENABLE			0x01        
#define SCIB_RE_ENABLE			0x01        
#define SCIB_RWU_DISABLE		0x00  
#define SCIB_SBK_DISABLE		0x00  

#define SCIB_TDRE_CLEAR         0x01
#define SCIB_TC_CLEAR           0x01
#define SCIB_RDRF_CLEAR         0x01
#define SCIB_IDLE_CLEAR         0x01
#define SCIB_OR_CLEAR           0x01
#define SCIB_NF_CLEAR           0x01
#define SCIB_FE_CLEAR           0x01
#define SCIB_PF_CLEAR           0x01
#define SCIB_BERR_CLEAR         0x01
#define SCIB_RAF_CLEAR          0x01
#define SCIB_RXRDY_CLEAR        0x01
#define SCIB_TXRDY_CLEAR        0x01
#define SCIB_LWAKE_CLEAR        0x01
#define SCIB_STO_CLEAR          0x01
#define SCIB_PBERR_CLEAR        0x01
#define SCIB_CERR_CLEAR         0x01
#define SCIB_CKERR_CLEAR        0x01
#define SCIB_FRC_CLEAR          0x01
#define SCIB_OVFL_CLEAR         0x01
#define SCIB_PE_DISABLE         0x00
#define SCIB_TCIE_DISABLE       0x00
#define SCIB_TCIE_ENABLE        0x01
#define SCIB_RIE_DISABLE        0x00
#define SCIB_RIE_ENABLE         0x01



/* eSCI_A pins configured in primary function mode */
#define SCIA_SIU_PA_CONFIG   0x01  
#define SCIA_SIU_OBE_CONFIG  0x00
#define SCIA_SIU_IBE_CONFIG  0x00  
#define SCIA_SIU_ODE_CONFIG  0x00  
#define SCIA_SIU_HYS_CONFIG  0x00  
#define SCIA_SIU_SRC_CONFIG  0x00  
#define SCIA_SIU_WPE_CONFIG  0x00  
#define SCIA_SIU_WPS_CONFIG  0x00  

/* eSCI_B pins configured in primary function mode */
#define SCIB_SIU_PA_CONFIG   0x03  
#define SCIB_SIU_OBE_CONFIG  0x00
#define SCIB_SIU_IBE_CONFIG  0x00  
#define SCIB_SIU_ODE_CONFIG  0x00  
#define SCIB_SIU_HYS_CONFIG  0x00  
#define SCIB_SIU_SRC_CONFIG  0x00  
#define SCIB_SIU_WPE_CONFIG  0x00  
#define SCIB_SIU_WPS_CONFIG  0x00


/*************************************************
         Peripheral errors defines 
 *************************************************/

#define SCI_EXEC_NOT_OK             -5
#define SCI_PARAMETER_NOT_OK	    -6
#define SCI_DATA_REGISTER_FAULT     -7
#define SCI_TRANSMITTER_DISABLE     -8
#define SCI_RECEIVER_DISABLE        -9
#define SCI_PARITY_ERROR		    -10

/*************************************************
         SCI LIN definitions 
 *************************************************/
//#define NO_ERROR                                 (0)
#define SCI_LIN_FRAME_COMPLETED                  (0)
#define SCI_LIN_PERIPHERAL_ALREADY_CONFIGURED   (-1)
#define SCI_LIN_PERIPHERAL_NOT_CONFIGURED       (-2)
#define SCI_LIN_SIZE_INCORRECT                  (-3)
#define SCI_LIN_WAKEUPFNC_ERROR                 (-4)
#define SCI_LIN_SETSLEEPFNC_ERROR               (-5)
#define SCI_LIN_SETCHKSUM_ERROR                 (-6)

/* LIN exception type */
#define NO_EXCEPTION                            (0x000)
#define SCI_LIN_BIT_DETECTED_ERROR              (0x001)
#define SCI_LIN_SLAVE_TIMEOUT_ERROR             (0x002)
#define SCI_LIN_PHYSICAL_BUS_ERROR              (0x004)
#define SCI_LIN_RECEIVE_OVERFLOW_ERROR          (0x008)
#define SCI_LIN_RECEIVE_NOISE_ERROR             (0x010)
#define SCI_LIN_RECEIVE_FRAMING_ERROR           (0x020)
#define SCI_LIN_EX_CRC_ERROR                    (0x040)
#define SCI_LIN_EX_CHKSUM_ERROR                 (0x080)
#define SCI_LIN_EX_FRAME_CORRECTLY_RX           (0x100)

/* LIN exception flags */
#define SCI_LIN_EX_OVFL                         (0x00000001)
#define SCI_LIN_RECEIVE_FRAME_COMPLETED         (0x00000100)
#define SCI_LIN_RECEIVE_CHKSUM_ERROR            (0x00000200)
#define SCI_LIN_RECEIVE_CRC_ERROR               (0x00000400)
#define SCI_LIN_EX_PBERR                        (0x00000800)
#define SCI_LIN_EX_STO                          (0x00001000)
#define SCI_LIN_EX_BERR                         (0x00100000)
#define SCI_LIN_EX_FE                           (0x02000000)
#define SCI_LIN_EX_NF                           (0x04000000)

/* enabling bit eSCI_A LIN  */
#define SCIA_LIN_ENABLE                         (1)
#define SCIA_LIN_13BRK_ENABLE                   (1)
#define SCIA_LIN_LDBG_ENABLE                    (1)
#define SCIA_LIN_SBSTP_ENABLE                   (1)
#define SCIA_LIN_BESM13_ENABLE                  (1)
#define SCIA_LIN_PRTY_ENABLE                    (1)
#define SCIA_LIN_DMA_ENABLE                     (1)

#define SCIA_LIN_IRQ_ENABLE                     (1)
#define SCIA_LIN_WU_ENABLE                      (1)

#define SCIA_LIN_LDBG_DISABLE                   0x00
#define SCIA_LIN_BSTP_ENABLE                    0x01
#define SCIA_LIN_FBR_ENABLE                     0x01

/* enabling bit eSCI_B LIN */
#define SCIB_LIN_ENABLE                         (1)
#define SCIB_LIN_13BRK_ENABLE                   (1)
#define SCIB_LIN_LDBG_ENABLE                    (1)
#define SCIB_LIN_SBSTP_ENABLE                   (1)
#define SCIB_LIN_BESM13_ENABLE                  (1)
#define SCIB_LIN_PRTY_ENABLE                    (1)
#define SCIB_LIN_DMA_ENABLE                     (1)

#define SCIB_LIN_IRQ_ENABLE                     (1)
#define SCIB_LIN_WU_ENABLE                      (1)

#define SCIB_LIN_LDBG_DISABLE                   0x00
#define SCIB_LIN_BSTP_ENABLE                    0x01
#define SCIB_LIN_FBR_ENABLE                     0x01

/* 
PUSH and POP Register for the SCI 
*/
#define DSCIA_PUSHR 0xFFFB0010
#define DSCIB_PUSHR 0xFFFB4010
#define DSCIA_POPR  0xFFFB0014
#define DSCIB_POPR  0xFFFB4014
//#define DSCIA_RXFR  0xFFF9007C

/*#define eSCIA_COMBTX            (18)
#define eSCIA_COMBRX            (19)*/

#if(LIN_CH_A_ENABLED == 1)
#define DMA_ESCI_COMBRX   DMA_ESCIA_COMBRX
#define DMA_ESCI_COMBTX   DMA_ESCIA_COMBTX
#elif(LIN_CH_B_ENABLED == 1)
#define DMA_ESCI_COMBRX   DMA_ESCIB_COMBRX
#define DMA_ESCI_COMBTX   DMA_ESCIB_COMBTX
#endif


#define SCI_LIN_BUFFER_LENGTH           (12)
#define SCI_LIN_TX_BUFFER_SIZE          (12)
#define SCI_LIN_RX_BUFFER_SIZE          (12)
#define SCI_LIN_SLAVE_BUFFER_SIZE       (8)
#define SCI_LIN_HEADER_TX_BUFFER_SIZE   (3)
#define SCI_LIN_HEADER_RX_BUFFER_SIZE   (4)


#define SCI_LIN_BYTE_MASK   ((uint32_t)(0x000000FF))
#define SCI_LIN_SHL_VAL     (24)


/****** Exceptions Prototypes ******/

void SCI_ExStopTxA(void);

void SCI_ExStopRxA(void);

void SCI_ExOverrunErrorA(void);


void SCI_ExOverrunErrorB(void);

void SCI_ExStopRxB(void);

void SCI_ExStopTxB(void);


/******  Interrupt Function   ******/

void SCI_A_INT_ISR(void);
void SCI_B_INT_ISR(void);

/*
** ===================================================================
**     Method      :  SCI_Reset(uint8_t,uint8_t)
**
**     Description :
**         This method reset eSCI modules
**         Call this method in the user code to reset the module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t SCI_Reset(uint8_t chIdentifier, uint8_t line_dir);


/*
** ===================================================================
**     Method      :  SCI_Config(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to initialize the module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/

int16_t SCI_Config(void);

/*
** ===================================================================
**     Method      :  SCI_Config_Baud(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to reinitialize e set the baud of the module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t SCI_Config_Baud(uint8_t chIdentifier,uint32_t baud);


/*
** ===========================================================================
**     Method      :  SCI_TxEnable(...)
**
**     Description :
**         This method enables the transmitter of the eSCI peripherals(A and B)
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to enable the A or B transmitter
**         module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===========================================================================
*/

int16_t SCI_TxEnable(uint8_t chIdentifier, uint8_t txSwitch);

/*
** ==========================================================================
**     Method      :  SCI_RxEnable(...)
**
**     Description :
**         This method enables receiver of the eSCI peripherals(A and B)
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to enable the A or B receiver 
**         module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_RxEnable(uint8_t chIdentifier, uint8_t rxSwitch);

/*
** ==========================================================================
**     Method      :  SCI_TxData(...)
**
**     Description :
**         This method performs the data transmission through the eSCI modules
**         (A and B) according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to transmit data.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_TxData(uint8_t chIdentifier, uint16_t txData);

/*
** ==========================================================================
**     Method      :  SCI_RxData(...)
**
**     Description :
**         This method performs the data read from the Data Register of the 
**         eSCI modules (A and B) according to the Functional Specification 
**         "PAEO001_17_SCI.doc"
**         Call this method in the user code to read from the Data Register the 
**         received data.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_RxData(uint8_t chIdentifier, uint16_t *rxData);

/*
** ==========================================================================
**     Method      :  SCI_ProgramException(...)
**
**     Description :
**         This method performs the exception activation/deactivation of a 
**         particular exception of the eSCI modules (A and B) according to the
**         Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to activate/deactivate an exception.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_ProgramException(uint8_t chIdentifier, uint8_t exceptionType, uint8_t exSwitch);

/*
** ==========================================================================
**     Method      :  SCI_ReadTxStatus(...)
**
**     Description :
**         This method checks the transmission status of the eSCI modules(a and B)
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to activate/deactivate an exception.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_ReadTxStatus(uint8_t chIdentifier, uint8_t *returnStatus);
int16_t SCI_SetIsrTasks(uint8_t chIdentifier, TaskType stopTx, TaskType stopRx, TaskType overrunError);
int16_t SCI_ControlStatus(uint8_t chIdentifier);

/* Public variables */
extern uint8_t SCI_ConfigurationStatus;   
extern uint8_t SCI_LINConfigurationStatus;   

/* eSCI LIN functions */
int16_t  SCI_LINConfig(void);

int16_t  SCI_LINTransmitFrame(uint8_t * pTxBuffer, uint8_t size);
int16_t  SCI_LINTxHeaderRxBuffer(uint8_t * pTxBuffer);

int16_t  SCI_LINWakeUp(void);

int16_t  SCI_LINReadExceptionType(uint16_t *pLINException);

int16_t  SCI_LINSetSleep(uint8_t * pBuffer);

int16_t SCI_LINRxEx(void);

/* eSCI DMA Access */
void SCI_Write(uint8_t Channel,uint8_t *txbuffer,uint8_t word_number);
void SCI_Read(uint8_t Channel, uint8_t *txbuffer);

#endif /* ifndef _SCI_H_ */

