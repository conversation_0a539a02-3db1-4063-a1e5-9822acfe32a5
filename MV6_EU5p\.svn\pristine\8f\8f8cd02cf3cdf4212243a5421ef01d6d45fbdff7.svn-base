#ifndef _BUILD_OPT_H_
#define _BUILD_OPT_H_

#ifdef EM_3CYL
#define ENGINE_TYPE         MV_AGUSTA_3C_TDC_0_08 
#define _BUILD_ANGLECLOCK_
#define _BUILD_ANGLEEX_
#define _BUILD_ETPUDELAY_
#define _BUILD_ETPUPULSE_
//#define _BUILD_ETPUPHASE_
#define _BUILD_ETPUPWM_
#define _BUILD_PWM_IN_
#define _BUILD_SPARKHANDLER_
#define _BUILD_ANGTRIGGER_
//#define _BUILD_FASTLINKEDCHAN_
//#define _BUILD_ETPUUART_
//#define _BUILD_IONACQANGLE_

#endif /* endifdef EM_3CYL */

#ifdef EM_3CYL_NEW
#define ENGINE_TYPE         MV_AGUSTA_3C_TDC_0_20 
#define _BUILD_ANGLECLOCK_
#define _BUILD_ANGLEEX_
#define _BUILD_ETPUDELAY_
#define _BUILD_ETPUPULSE_
//#define _BUILD_ETPUPHASE_
#define _BUILD_ETPUPWM_
#define _BUILD_PWM_IN_
#define _BUILD_SPARKHANDLER_
#define _BUILD_ANGTRIGGER_
//#define _BUILD_FASTLINKEDCHAN_
//#define _BUILD_ETPUUART_
//#define _BUILD_IONACQANGLE_

#endif /* endifdef EM_3CYL */

#ifdef EM_3CYL_36_2
#define ENGINE_TYPE         MV_AGUSTA_3C_TDC_0_30 
#define _BUILD_ANGLECLOCK_
#define _BUILD_ANGLEEX_
#define _BUILD_ETPUDELAY_
#define _BUILD_ETPUPULSE_
//#define _BUILD_ETPUPHASE_
#define _BUILD_ETPUPWM_
#define _BUILD_PWM_IN_
#define _BUILD_SPARKHANDLER_
#define _BUILD_ANGTRIGGER_
//#define _BUILD_FASTLINKEDCHAN_
//#define _BUILD_ETPUUART_
//#define _BUILD_IONACQANGLE_

#endif /* endifdef EM_3CYL */

#ifdef EM_4CYL
#define ENGINE_TYPE         MV_AGUSTA_4C
#define _BUILD_ANGLECLOCK_
#define _BUILD_ANGLEEX_
#define _BUILD_ETPUDELAY_
#define _BUILD_ETPUPULSE_
//#define _BUILD_ETPUPHASE_
#define _BUILD_ETPUPWM_
#define _BUILD_PWM_IN_
#define _BUILD_SPARKHANDLER_
#define _BUILD_ANGTRIGGER_
//#define _BUILD_FASTLINKEDCHAN_
//#define _BUILD_ETPUUART_
//#define _BUILD_IONACQANGLE_

#endif /* endifdef EM_4CYL */

#ifdef EM_4CYL_36_2
#define ENGINE_TYPE         MV_AGUSTA_4C_TDC_0_9
#define _BUILD_ANGLECLOCK_
#define _BUILD_ANGLEEX_
#define _BUILD_ETPUDELAY_
#define _BUILD_ETPUPULSE_
//#define _BUILD_ETPUPHASE_
#define _BUILD_ETPUPWM_
#define _BUILD_PWM_IN_
#define _BUILD_SPARKHANDLER_
#define _BUILD_ANGTRIGGER_
//#define _BUILD_FASTLINKEDCHAN_
//#define _BUILD_ETPUUART_
//#define _BUILD_IONACQANGLE_

#endif /* endifdef EM_4CYL_36_2 */

#undef ETPU_12K_2_5K
#define ETPU_12K_3K
#undef ETPU_14K_2_5K
#undef ETPU_14K_3K
#ifdef ETPU_12K_2_5K
#define MAX_CODE_SIZE              0x3000   /* up to 12k  */
#define MAX_RAM_SIZE               0xA00    /* up to 2.5K */
#else
#ifdef ETPU_12K_3K
#define MAX_CODE_SIZE              0x3000   /* up to 12k  */
#define MAX_RAM_SIZE               0xC00    /* up to 3K */
#else
#ifdef ETPU_14K_2_5K
#define MAX_CODE_SIZE              0x3800   /* up to 14k  */
#define MAX_RAM_SIZE               0xA00    /* up to 2.5K */
#else
#ifdef ETPU_14K_3K
#define MAX_CODE_SIZE              0x3800   /* up to 14k  */
#define MAX_RAM_SIZE               0xC00    /* up to 3K */
#endif
#endif
#endif
#endif

#define CODE_ENTRY_SIZE            0x400
#define CODE_ADDR                  CODE_ENTRY_SIZE
#define CODE_SIZE                  (MAX_CODE_SIZE-CODE_ENTRY_SIZE)
#define GRAM_ADDR                  0x0000
#define GRAM_SIZE                  0x400
#define PRAM_START_ADDR            0x400
#define PRAM_SIZE                  (MAX_RAM_SIZE-PRAM_START_ADDR)
#define AUTO_LOCAL_ALLOC           0
#define TRAM_ADDR                  (PRAM_START_ADDR-1) /* (GRAM_ADDR+GRAM_SIZE) */
#define TRAM_SIZE                  (PRAM_START_ADDR-TRAM_ADDR)
#define FUNC_ENTRY_ADDR            0x0000

#pragma memory ROM [CODE_SIZE] @ CODE_ADDR;
#pragma memory ORG @ CODE_ADDR;

#pragma entryaddr FUNC_ENTRY_ADDR; /* This is a dummy entryaddr definition. */
#pragma memory LOCAL [AUTO_LOCAL_ALLOC] @ TRAM_ADDR;
#pragma memory RAM [GRAM_SIZE] @ GRAM_ADDR;

#endif /* _BUILD_OPT_H_ */

