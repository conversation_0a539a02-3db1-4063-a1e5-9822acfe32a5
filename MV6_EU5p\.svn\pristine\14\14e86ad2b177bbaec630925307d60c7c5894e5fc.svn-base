/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/
#ifdef _BUILD_AWHEELINGCTRL_
#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Select source front of sample for acceleration [selector]
__declspec(section ".calib") uint8_T AWACCFRSELECT =    2U;   //   2
//Select source rear of sample for acceleration [selector]
__declspec(section ".calib") uint8_T AWACCRESELECT =    2U;   //   2
//Select source front of sample for acceleration or speed [selector]
__declspec(section ".calib") uint8_T AWACCSPEEDFRSEL =    1U;   //   1
//Select source rear of sample for acceleration or speed [selector]
__declspec(section ".calib") uint8_T AWACCSPEEDRESEL =    1U;   //   1
//Baricenter angle [deg]
__declspec(section ".calib") int16_T AWALFABAR = 2880;   //( 45.000000*64)
//Delta speed per confermare wheeling [Km/h]
__declspec(section ".calib") int16_T AWCONFDVS = 2;   //(   0.1250*16)
//Delta Front speed per confermare wheeling [Km/h]
__declspec(section ".calib") int16_T AWCONFDVSFRONT = -2;   //(  -0.1250*16)
//Delta Rear speed per confermare wheeling [Km/h]
__declspec(section ".calib") int16_T AWCONFDVSREAR = 0;   //(   0.0000*16)
//DB cmi [Nm]
__declspec(section ".calib") int16_T AWDBXNCMI = 128;   //(   4.00000*32)
//Delta Aw threshold [Aw]
__declspec(section ".calib") int16_T AWDCTRL = 26;   //(   0.406250*64)
//Delta CmeEst wheeling [Nm]
__declspec(section ".calib") int16_T AWDELLCMEPW = 2400;   //( 150.0000*16)
//Delta Pitch [Deg/10ms]
__declspec(section ".calib") int16_T AWDELNPW = 11;   //( 0.171875*64)
//Delta Pitch [Deg/10ms]
__declspec(section ".calib") int16_T AWDELPW = -26;   //(-0.406250*64)
//Delta speed to enter Wait_Speed wheeling [Km/h]
__declspec(section ".calib") int16_T AWDSSW = -160;   //( -10.0000*16)
//Enable Ay [Selector]
__declspec(section ".calib") uint8_T AWENAY =  1U;   // 1
//Enable AW Control [flag]
__declspec(section ".calib") uint8_T AWENCTRL =  1U;   // 1
//Select Pre Inc Aw and Pitch [Selector]
__declspec(section ".calib") uint8_T AWENPREINC =  1U;   // 1
//Dynamics of Filter AX [m/s^2]
__declspec(section ".calib") int16_T AWFINTMAXAX = 141;   //( 2.203125*64)
//Dynamics of Filter AX [m/s^2]
__declspec(section ".calib") int16_T AWFINTMINAX = -141;   //(-2.203125*64)
//hG0 [m]
__declspec(section ".calib") int16_T AWHG0 = 48;   //(0.750000*64)
//KFilt Ax [KFilt]
__declspec(section ".calib") uint16_T AWKFILTAX = 2950U;   //(0.18005371093750*16384)
//KFilt Wx [KFilt]
__declspec(section ".calib") uint16_T AWKFILTWX = 2949U;   //(0.17999267578125*16384)
//Speed to force exit WheelingSpeed [Km/h]
__declspec(section ".calib") int16_T AWMAXRETVS = 48;   //(  3.0000*16)
//Select mean deep front of sample for acceleration or speed [selector]
__declspec(section ".calib") uint8_T AWMEANSPEEDFRSEL =    0U;   //   0
//Select mean deep rear of sample for acceleration or speed [selector]
__declspec(section ".calib") uint8_T AWMEANSPEEDRESEL =    0U;   //   0
//Min Acc Front speed per uscire dal wheeling [(Km/h)/s]
__declspec(section ".calib") int16_T AWMINACCFRSEL = 80;   //(   5.0000*16)
//Min Acc Rear speed per elaborare un possibile wheeling [(Km/h)/s]
__declspec(section ".calib") int16_T AWMINACCRESEL = -1;   //(  -0.0625*16)
//Min Cme to disable Rate [Nm]
__declspec(section ".calib") int16_T AWMINCMEERATE = 3200;   //( 200.0000*16)
//CmeEst speed wheeling [Nm]
__declspec(section ".calib") int16_T AWMINCMEEW = 4800;   //( 300.0000*16)
//CmeEst pitch wheeling [Nm]
__declspec(section ".calib") int16_T AWMINCMEPW = 10400;   //( 650.0000*16)
//CmeEst pitch wheeling [Nm]
__declspec(section ".calib") int16_T AWMINCMEVW = 10400;   //( 650.0000*16)
//Min Acc Front speed per elaborare un possibile wheeling [(Km/h)/s]
__declspec(section ".calib") int16_T AWMINDECFRSEL = 1;   //(   0.0625*16)
//Min Gas to disable Rate [%]
__declspec(section ".calib") uint16_T AWMINGASRATE = 0U;   //( 0.0000*16)
//Speed to force exit WheelingSpeed [Km/h]
__declspec(section ".calib") int16_T AWMINRETVS = -80;   //( -5.0000*16)
//Master Anti Wheeling strategy disable [flag]
__declspec(section ".calib") uint8_T AWMSTDIS =  0U;   // 0
//Delta speed per rigettare una elaborazione di wheeling [Km/h]
__declspec(section ".calib") int16_T AWNOCDVS = 16;   //(   1.0000*16)
//Delta Front speed per rigettare una elaborazione di wheeling [Km/h]
__declspec(section ".calib") int16_T AWNOCDVSFRONT = 64;   //(   4.0000*16)
//Delta Rear speed per rigettare una elaborazione di wheeling [Km/h]
__declspec(section ".calib") int16_T AWNOCDVSREAR = -48;   //(  -3.0000*16)
//Gain engine threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWPWGAIN0 = 45U;   //( 2.8125*16)
//Gain engine threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWPWGAIN1 = 45U;   //( 2.8125*16)
//Gain pitch threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWPWGAIN2 = 30U;   //( 1.8750*16)
//Max Rate I AW Control [Nm]
__declspec(section ".calib") int16_T AWRATEIMAXAW = 30;   //(  0.93750*32)
//Max Rate I Post AW Control [Nm]
__declspec(section ".calib") int16_T AWRATEIMAXPST = 166;   //(  5.18750*32)
//Dynamics of Rate AX [m/s^2]
__declspec(section ".calib") int16_T AWRATEMAXAX = 128;   //( 2.000000*64)
//Dynamics of Rate AX [m/s^2]
__declspec(section ".calib") int16_T AWRATEMINAX = -128;   //(-2.000000*64)
//Max Rate P AW Control [Nm]
__declspec(section ".calib") int16_T AWRATEPMAXAW = 30;   //(  0.93750*32)
//Max Rate in control [Nm]
__declspec(section ".calib") int16_T AWRATEPMAXCTRL = 576;   //( 18.00000*32)
//Max Rate P Post AW Control [Nm]
__declspec(section ".calib") int16_T AWRATEPMAXPST = 166;   //(  5.18750*32)
//RCPx [m]
__declspec(section ".calib") int16_T AWRCPX = 45;   //( 0.703125*64)
//RCPz [m]
__declspec(section ".calib") int16_T AWRCPZ = 55;   //( 0.859375*64)
//AWSADX [m]
__declspec(section ".calib") int16_T AWSADX = 98;   //( 1.531250*64)
//AWSADZ [m]
__declspec(section ".calib") int16_T AWSADZ = 42;   //( 0.656250*64)
//Select Aw channel [Selector]
__declspec(section ".calib") uint8_T AWSELECT =  2U;   // 2
//Diff Speed [Km/h]
__declspec(section ".calib") int16_T AWSPDIFF = 0;   //(   0.0000*16)
//Selector RearSpeed [flag]
__declspec(section ".calib") uint8_T AWSPEEDRESEL =  0U;   // 0
//SPr [m]
__declspec(section ".calib") int16_T AWSPREAR = 45;   //(0.703125*64)
//Gain speed threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWSWGAIN0 = 128U;   //( 8.0000*16)
//Gain speed threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWSWGAIN1 = 128U;   //( 8.0000*16)
//Gain speed threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWSWGAIN2 = 61U;   //( 3.8125*16)
//Aw threshold [Aw]
__declspec(section ".calib") int16_T AWTHCTRL = 0;   //(   0.000000*64)
//Threshold 0 speed wheeling [Threshold]
__declspec(section ".calib") uint16_T AWTHWHEELING0 = 30U;   //( 7.50*4)
//Threshold 1 speed wheeling [Threshold]
__declspec(section ".calib") uint16_T AWTHWHEELING1 = 3U;   //( 0.75*4)
//Threshold 2 speed wheeling [Threshold]
__declspec(section ".calib") uint16_T AWTHWHEELING2 = 30U;   //( 7.50*4)
//Time to change gain [ms]
__declspec(section ".calib") uint16_T AWTIMCHANGEW =      2U;   //     2
//Tempo per accettare un inizio pitch wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMCPW =    3U;   //   3
//Time to force exit WheelingSpeed [ms]
__declspec(section ".calib") uint8_T AWTIMEXITSW =   26U;   //  26
//Tempo per accettare una fine pitch wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMFPW =    2U;   //   2
//Tempo per rigettare una elaborazione di wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMNOSW =   10U;   //  10
//Timeout pitch relase [ms]
__declspec(section ".calib") uint8_T AWTIMOUTPW =   25U;   //  25
//Tempo per uscire da un wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMRETPW =   20U;   //  20
//Tempo per uscire da un wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMRETSW =    3U;   //   3
//Tempo per rigettare una elaborazione di wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMRETVW =    0U;   //   0
//Tempo per confermare un wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMSPEEDW =    0U;   //   0
//Tempo per uscire da un wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMSWPERM =   10U;   //  10
//Tempo per validare una elaborazione di wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMVW =    1U;   //   1
//Tempo per confermare una elaborazione di wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMWAITPW =    2U;   //   2
//Tempo per confermare una elaborazione di wheeling [ms]
__declspec(section ".calib") uint8_T AWTIMWAITSW =    0U;   //   0
//Gain vehicle threshold 0 wheeling [Gain]
__declspec(section ".calib") uint8_T AWVWGAIN0 = 133U;   //( 8.3125*16)
//Gain vehicle threshold 1 wheeling [Gain]
__declspec(section ".calib") uint8_T AWVWGAIN1 = 128U;   //( 8.0000*16)
//Gain vehicle threshold 1 wheeling [Gain]
__declspec(section ".calib") uint8_T AWVWGAIN2 = 32U;   //( 2.0000*16)
//AW breakpoint observer [Aw]
__declspec(section ".calib") int16_T BKDAW[7] = 
{
 -640, -384, -256, -192, -128, -64, 0
};
//dP breakpoint observer [Deg]
__declspec(section ".calib") int16_T BKDPITCHAW[5] = 
{
 -1920, -1280, -192, -64, 0
};
//Gain of CmiTracI [gain]
__declspec(section ".calib") uint8_T TBAWGAINCMII[7*5] = 
{
 11U, 11U, 11U, 31U, 34U,
 11U, 11U, 15U, 37U, 40U,
 11U, 11U, 21U, 43U, 46U,
 11U, 16U, 27U, 48U, 51U,
 11U, 20U, 33U, 53U, 56U,
 11U, 26U, 39U, 58U, 61U,
 11U, 30U, 43U, 61U, 64U
};
//Gain of CmiTracP [gain]
__declspec(section ".calib") uint8_T TBAWGAINCMIP[7*5] = 
{
 11U, 11U, 32U, 44U, 45U,
 11U, 14U, 38U, 50U, 51U,
 11U, 18U, 43U, 55U, 56U,
 11U, 21U, 46U, 57U, 58U,
 11U, 24U, 49U, 59U, 60U,
 11U, 27U, 52U, 61U, 62U,
 11U, 30U, 55U, 63U, 64U
};
#endif
