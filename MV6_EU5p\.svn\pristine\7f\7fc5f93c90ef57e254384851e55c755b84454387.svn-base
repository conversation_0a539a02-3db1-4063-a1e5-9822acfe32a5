#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_INJCMD_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif

#include "injcmd.h"

// Number of TDC to reset CntFirstInj
__declspec(section ".calib") uint16_T TDCRESETFIRSTINJ =  100;   // 2000

// Max number of First Injection
__declspec(section ".calib") uint16_T RPMINJCMDTH =  2000;   // 2000

// Max number of First Injection
__declspec(section ".calib") uint8_T ENINJXCRK = 0x44;

// Max number of First Injection
__declspec(section ".calib") int16_T BKMAXCNTFIRSTINJ[BKMAXCNTFIRSTINJ_dim] =  {-20, -5, 0, 15};

// Max number of First Injection
__declspec(section ".calib") uint16_T VTMAXCNTFIRSTINJ[BKMAXCNTFIRSTINJ_dim] =  {3, 3, 3, 3};   // 3

//=0 no refresh =1 INJ_PRG refresh =2 TDC refresh =3 Extra INJ [state]
__declspec(section ".calib") uint8_T INJREFRESH =  0;   // 3
// [us]
__declspec(section ".calib") int16_T INJTIMEREFRESH[4] = 
{
    0, 0, 0, 0
};
// [degree]
__declspec(section ".calib") int16_T SOIREFRESH[4] = 
{
 0, 0, 0, 0
};

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_INJCMD_
