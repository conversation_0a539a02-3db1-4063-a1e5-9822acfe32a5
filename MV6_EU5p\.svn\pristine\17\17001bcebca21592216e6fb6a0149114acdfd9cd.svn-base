#ifndef _PHASEMGM_H_
#define _PHASEMGM_H_

/** include files **/
#include "typedefs.h"

/** local definitions **/
#define SEC_2_USEC		1000000														// [s]=>[us] Conversion constant
#define K_RPM_DEGREE	((SEC_2_USEC*60*N_CYCLE)/N_TEETH_CYCLE)						//Coeff <PERSON>od [us] -> Rpm [rpm]

typedef uint8_t typPhMode;
#define PH_NORMAL_MODE  0
#define PH_RECOVERY_MODE  1

/** default settings **/

/** external functions **/

/** external data **/
extern uint8_T SparkAbsTdc;
extern volatile uint8_T  StSync;				//Sync. state
extern uint8_T FlgSyncPhased;
extern uint8_T FlgSyncReady;

/** internal functions **/

/** public data **/

/** private data **/

/** public functions **/
extern void PhaseMgm_Init(void);
extern void PhaseMgm_CamEdge(void);
				
/** private functions **/

#endif // _PHASEMGM_H_
