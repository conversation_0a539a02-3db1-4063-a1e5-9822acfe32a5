/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Device_Drivers/DBWMGM/main_trunk/DbwMgm_ert_rtw/dbw_mgm.h $ */
/* $Description:  $ */
/* $Revision: 5140 $ */
/* $Date: 2012-10-29 12:10:55 +0100 (lun, 29 ott 2012) $ */
/* $Author: lanal $ */
/*****************************************************************************************************************/
/*
 * File: dbw_mgm.h
 *
 * Real-Time Workshop code generated for Simulink model DbwMgm.
 *
 * Model version                        : 1.705
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Wed Oct 24 13:36:13 2012
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Wed Oct 24 13:36:15 2012
 */
#ifndef RTW_HEADER_dbw_mgm_h_
#define RTW_HEADER_dbw_mgm_h_
#include "rtwtypes.h"

#define DBW_ACTIVE_DIAG                5U                        /* State of control doing Active Diagnosis */
#define DBW_CLOSED_LOOP                1U                        /* State of Close Loop Control */
#define DBW_DISABLED                   2U                        /* State of Control which disables DC motor */
#define DBW_FORCE_LH                   4U                        /* State of Control which disables DC motor */
#define DBW_SELF_LEARNING              0U                        /* State of control doing Self Learning */
#define DBW_TEST_DIS_LOAD              3U                        /* State of Control which disables DC motor */

extern int16_T AbsAngThrErr;           /* Absolute throttle angle error integrated for diagnosis */
extern int16_T AngIntErr;              /* Integral term of throttle angle error */
extern int16_T AngThr;                 /* Measured throttle angle referred to limp home */
extern int16_T AngThrCorrObjF;         /* Throttle angle target filtered */
extern int32_T AngThrCorrObjFHiR;      /* Throttle angle target filtered - High resolution variable */
extern int16_T AngThrDiag;             /* Throttle angle output for diagnosis */
extern int16_T AngThrDiagErr;          /* Throttle angle Error for diagnosis */
extern int16_T AngThrErr;              /* Throttle angle error at current time step */
extern int16_T AngThrErr1;             /* Throttle angle error at time step -1 */
extern int16_T AngThrErr2;             /* Throttle angle error at time step -2 */
extern int16_T AngThrErr3;             /* Throttle angle error at time step -3 */
extern uint8_T AngThrFilReset;         /* AngThrObj filter reset flag */
extern int16_T AngThrObj;              /* Throttle angle target referred to limp home */
extern int16_T AngThrObjNoFilt;        /* Throttle angle target referred to limp home before filter and rate limiter */
extern int16_T AngThrottleTgt;         /* Absolute throttle angle target */
extern uint8_T FlgAngThrObjSat;        /* Target throttle angle saturation flag */
extern uint8_T FlgDisableDbw;          /* Disable Dbw due to Rpm flag */
extern uint8_T FlgEnHBridge;           /* Enable H-Bridge flag */
extern uint8_T FlgRpmDisable;          /* Disable Dbw controller flag */
extern uint16_T GainKPDBW;             /* PID regulator proportional gain factor */
extern int16_T KDyna;                  /* DBW dynamic constant used for diagnosis */
extern int16_T MaxAngSlope;            /* Max. throttle angle target slope */
extern int16_T MinAngErr;              /* Dead Band on throttle angle error */
extern int16_T MinAngSlope;            /* Min. throttle angle target slope */
extern uint8_T StDbwCtrl;              /* Dbw controller status */
extern int16_T VDbwFeedFwd;            /* Dbw output feedforward voltage */
extern int16_T VDbwOut;                /* Dbw output command voltage */
extern int16_T VDbwPID;                /* DBW PID regulator output voltage at current time step */
extern int16_T VDbwPID1;               /* Dbw PID regulator output voltage at time step -1 */
extern int32_T VDbwPIDNoSat;           /* Current DBW PID regulator output voltage not satured */

#endif                                 /* RTW_HEADER_dbw_mgm_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
