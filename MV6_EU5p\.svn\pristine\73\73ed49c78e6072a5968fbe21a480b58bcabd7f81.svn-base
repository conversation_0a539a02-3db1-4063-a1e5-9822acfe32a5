/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _DIGIO_H_
#define _DIGIO_H_

/* MODULE DIGIO */
#include "typedefs.h"
#include "sys.h"

/* -------------------------------------------------------------------- */
/* DIGIO Service Function */
/* -------------------------------------------------------------------- */
/*
** =======================================================================
**     Method      : DIGIO_Cfg
**
**     Description : fake function
**                   
**
**     Parameters  : None
**     Returns     : None
** =======================================================================
*/

void DIGIO_Config(void);

/*
** =======================================================================
**     Method      : DIGIO_InCfgExt
**
**     Description : fake function
**                   
**
**     Parameters  : Channel_Id, type
**     Returns     : None
** =======================================================================
*/
void DIGIO_InCfgExt(uint16_t Channel_Id, uint8_t type);

/*
** =======================================================================
**     Method      : DIGIO_InCfg
**
**     Description : This method configures the channel identifies by
**                   the input parameter index as an input pin
**
**     Parameters  : Numeric index of the input digital channel to set
**     Returns     : None
** =======================================================================
*/

void DIGIO_OutCfgExt(uint16_t Channel_Id, uint8_t ResetLvl, 
    uint8_t openDrnEna, uint8_t drvStrength, uint8_t slewRate);

/*
** ==========================================================================
**     Method      : DIGIO_InGet
**
**     Description : This method reads the status of the selected DIN
**                   channel and retrieves its status by reading this
**                   value from the pin set
**
**     Parameters  : Numeric index of the input digital channel to read
**                   current physical status of the selected channel (that
**                   would be either of SET_BIT_LOW and SET_BIT_HIGH)
**     Returns     : Error code
** ==========================================================================
*/
int16_t DIGIO_InGet(uint16_t Channel_Id,uint8_t* status);

/*
** ==========================================================================
**     Method      : DIGIO_OutSet
**
**     Description : This method sets the physical level of the hardware
**                   pin connected to the channel specified by the numeric
**                   input index parameter
**
**     Parameters  : Numeric index of the output digital channel to set
**                   physical status of the selected channel to set (that
**                   would be either of SET_BIT_LOW and SET_BIT_HIGH)
**     Returns     : Error code
** ==========================================================================
*/
int16_t DIGIO_OutSet(uint16_t Channel_Id,uint8_t status);

/*
** ==========================================================================
**     Method      :  DIGIO_OutGet
**
**     Description : This method reads the status of the selected DOUT
**                   channel and retrieves its status by reading this
**                   value from the related pin
**
**     Parameters  : Numeric index of the output digital channel to read
**                   current physical status of the selected channel (that
**                   would be either of SET_BIT_LOW and SET_BIT_HIGH)
**     Returns     : Error code
** ==========================================================================
*/
int16_t DIGIO_OutGet(uint16_t Channel_Id,uint8_t* status);

#endif /* _DIGIO_H_ */
