/*****************************************************************************************************************/
/* $HeadURL::                                                                                                $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                           */
/*****************************************************************************************************************/

#ifndef _VSPEEDMGM_H_
#define _VSPEEDMGM_H_

#include "typedefs.h"

/* public variables */
extern uint16_T VehSpeedSense;
extern uint8_T  FlgVehStop;
extern uint16_T VehSpeedRear;
extern uint16_T VehSpeedRearNc;
extern uint16_T VehSpeedRearAnDrift;
extern uint16_T VehSpeedFront;
extern uint16_T VehSpeedFrontNc;
extern uint16_T VehSpeedFrontNz;
extern int32_T  VehRbVfAxInt;
extern int16_T  AccRear;
extern int16_T  AccRear2;
extern int16_T  AccRear3;
extern int16_T  AccFront;
extern int16_T  AccFront2;
extern int16_T  AccFront3;
extern uint16_T FrontWheelRadius;
extern uint16_T FrontWheelRadiusNc;
extern uint16_T RearWheelRadius;
extern uint16_T RearWheelRadiusNc;
extern uint8_T  VehFrontSourceSel;
extern uint8_T  VehRearSourceSel;
extern int16_T  DeltaVehSpeed;
extern int16_T  AccDeltaVehSpeed;
extern uint8_T  FlgDeltaVehSpeedSup;
extern uint8_T  FlgDeltaVehSpeedInf;
extern uint8_T  StRbVf;
extern int16_T  AccRearRbVf;
extern int16_T  AccRearRbVfAvg;

/* Funzioni esportate */
void VSpeedMgm_Init(void);
void VSpeedMgm_10ms(void);
void Update_RearRollSpeed (uint16_T *tmpVSpeed, uint16_T *tmpWheelRadius, uint8_T EnRoll);
void Update_FrontRollSpeed (uint16_T *tmpVSpeed, uint16_T *tmpWheelRadius, uint8_T EnRoll);

#endif

