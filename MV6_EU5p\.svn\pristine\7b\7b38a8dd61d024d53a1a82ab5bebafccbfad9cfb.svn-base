#ifndef _PIO_H_
#define _PIO_H_

#include "typedefs.h"
#include "OS_api.h"


/*  */
/* PWM out match modes */

#ifndef PWM_MATCH_ANGLE
#define PWM_MATCH_ANGLE  0
#endif /*  PWM_MATCH_ANGLE */
#ifndef PWM_MATCH_TIME
#define PWM_MATCH_TIME   1
#endif /*  PWM_MATCH_TIME */


/* Used to select UC */
typedef uint16_t t_PIO_CHANNELS;


/*********************************/
/* Definition type for OPWFM mode */
/*********************************/
typedef vuint16_t   t_DutyCycle;     /* 1/256 precision */
typedef vuint32_t   t_Period;
typedef enum { ACTIVE_LOW, ACTIVE_HIGH } t_ActiveEdge;
typedef vuint16_t   t_EdgeCount;

/* Max value for t_DutyCycle */
#define MAX_DUTY                    (256*100)   /* 100<<8 */
/* Min value for t_DutyCycle */
#define MIN_DUTY                    (0*100)     /*   0<<8 */

/*********************************/
/* Definition type for IPWM mode */
/*********************************/
typedef uint32_t t_BusSelect;
typedef enum { SINGLE_FALLING_EDGE, SINGLE_RISING_EDGE, BOTH_EDGE } t_EdgeSelection;
typedef enum { FALLING_EDGE, RISING_EDGE } t_EdgePolarity;
typedef enum { POL_LOW, POL_HIGH } t_ActivePolarity;
typedef vuint32_t t_Distance;

/*********************************/
/* Definition type for DAOC mode */
/*********************************/
typedef enum { TIME, ANGLE } t_CompareAction;
typedef vuint32_t            t_Angle;
typedef union { 
            t_Period period;
            t_Angle  angle; 
        } t_DelayUnion;

/****************************/
/* Generic definition types */
/****************************/
typedef enum { DISABLE, ENABLE } t_State;
typedef void (*t_IsrFunc)(void);
typedef uint8_t t_IsrFuncPri;

/************************/
/*   MODULE Interface   */
/************************/

/* This method executes the initializations of the Module.
_etb parameter selects the time base source that drives counter bus[A].
0 Unified Channel 23 drives Counter bus[A]
1 STAC drives Counter bus[A].
_srv parameter selects the address of a specific STAC server to which the STAC Client
Submodule is assigned. */

/* This method executes the configuration of the Module. */
int16_t PIO_Config(void);

/* This method enables the Module */
int16_t PIO_Enable(void);

/* This method disables the Module */
int16_t PIO_Disable(void);

/* This method reads the state of the Module */
int16_t PIO_GetState(t_State *_state);


/************************/
/*    PWM Interface     */
/************************/

/* This method executes the initializations of the PWM channel, setting 
the logic level on the output pin, the period and the duty-cycle 
to the values passed as input parameters. */
int16_t PIO_PwmOutConfig(t_PIO_CHANNELS _channel, t_ActiveEdge _actedge, uint8_t matchMode, t_DutyCycle _dutycicle, t_Period _period, t_Period _startAngle);
int16_t PIO_PwmOutEdgeCountConfig(t_PIO_CHANNELS _channel, t_ActiveEdge _actedge, uint8_t matchMode, t_DutyCycle _dutycicle, t_Period _period, t_Period _startAngle, t_EdgeCount _numedge);

/* This method sets run-time the duty-cicle of the PWM signal related with PWM channel */
int16_t PIO_PwmOutSetDutyCicle(t_PIO_CHANNELS _channel, t_DutyCycle _dutycicle);

/* This method sets run-time the period of the PWM signal related with PWM channel */
int16_t PIO_PwmOutSetPeriod(t_PIO_CHANNELS _channel, t_Period _period);

int16_t PIO_PwmOutSetEdgeCount(t_PIO_CHANNELS _channel, t_EdgeCount _edgecount);

/* This method enables the output related to the PWM channel */
int16_t PIO_PwmOutEnable(t_PIO_CHANNELS _channel);

/* This method disables the output related to the PWM channel */
int16_t PIO_PwmOutDisable(t_PIO_CHANNELS _channel);

/* This method reads the state of the PWM channel */
int16_t PIO_PwmOutGetState(t_PIO_CHANNELS _channel, t_State *_state);

/* This method allows the unified channel to generate an interrupt handler */
//int16_t PIO_PwmOutSetInterruptHandler(t_PIO_CHANNELS _channel, t_IsrFunc _isrFunc, t_IsrFuncPri _priority );
int16_t PIO_PwmOutSetInterruptHandler(t_PIO_CHANNELS _channel, TaskType taskID );

/* This method enables the execution of the interrupt */
int16_t PIO_PwmOutEnableInterrupt(t_PIO_CHANNELS _channel);

/* This method disables the execution of the interrupt  */
int16_t PIO_PwmOutDisableInterrupt(t_PIO_CHANNELS _channel);


/* PWM in APIs */
int16_t PIO_PwmInConfig(t_PIO_CHANNELS _channel, t_ActiveEdge _actedge, t_Period _timeout);
int16_t PIO_PwmInEnable(t_PIO_CHANNELS _channel);
int16_t PIO_PwmInGetDutyCycle(t_PIO_CHANNELS _channel, t_DutyCycle *_dutycicle);
int16_t PIO_PwmInGetPeriod(t_PIO_CHANNELS _channel, t_Period *_period);
int16_t PIO_PwmInSetInterruptHandler(t_PIO_CHANNELS _channel, TaskType taskID);
int16_t PIO_PwmInEnableInterrupt(t_PIO_CHANNELS _channel);
int16_t PIO_PwmInDisableInterrupt(t_PIO_CHANNELS _channel);

/************************/
/*    PIN Interface     */
/************************/

/* This method configures the PIN input channel.
_bsl parameter is used to select either one of the counter buses or 
the internal counter to be used by the unified channel.
_edgesel parameter selects whether the internal counter is
triggered by both edges of a pulse or just by a 
single edge as defined by the _edgepol.
_edgepol parameter asserts which edge triggers either the 
internal counter or an input capture or a FLAG. */
int16_t PIO_PinConfig(t_PIO_CHANNELS _channel, t_BusSelect _bsl, t_EdgeSelection _edgesel, t_EdgePolarity _edgepol);

/* This method enables the input related to the PIN channel */
int16_t PIO_PinEnable(t_PIO_CHANNELS _channel);

/* This method disables the input related to the PIN channel */
int16_t PIO_PinDisable(t_PIO_CHANNELS _channel);

/* This method reads the state of the PIN channel */
int16_t PIO_PinGetState(t_PIO_CHANNELS _channel, t_State *_state);

/* This method allows the measurement of the width of a 
positive or negative pulse or allow the measurement of the 
period of an input signal by capturing two consecutive rising edges 
or two consecutive falling edges. */
int16_t PIO_PinGetDistance(t_PIO_CHANNELS _channel, t_Distance *_distance);

/* This method allows the measurement of the width of a 
positive or negative pulse or allow the measurement of the 
period of an input signal by capturing two consecutive rising edges 
or two consecutive falling edges. */
int16_t PIO_PinGetLastEdgeTime(t_PIO_CHANNELS _channel, uint32_t *_time);

/* This method allows the measurement of the width of a 
positive or negative pulse or allow the measurement of the 
period of an input signal by capturing two consecutive rising edges 
or two consecutive falling edges. */
int16_t PIO_PinGetLastEdgeAngle(t_PIO_CHANNELS _channel, uint32_t *_angle);

/* This method allows the unified channel to generate an interrupt handler */
int16_t PIO_PinSetInterruptHandler(t_PIO_CHANNELS _channel, TaskType _isrFunc);

/* This method enables the execution of the interrupt */
int16_t PIO_PinEnableInterrupt(t_PIO_CHANNELS _channel);

/* This method disables the execution of the interrupt  */
int16_t PIO_PinDisableInterrupt(t_PIO_CHANNELS _channel);

/* This method reads the last transition type and returns information about        */
/* transition overflows                                                            */
int16_t PIO_PinGetLastTransition(t_PIO_CHANNELS _channel, t_EdgePolarity *edgeType);

/************************/
/*    POUT Interface    */
/************************/

/* This method executes the initializations of the POUT channel, setting 
the logic level on the output pin, and setting the compare action type. */
int16_t PIO_PoutConfig(t_PIO_CHANNELS _channel, t_ActiveEdge _actedge, t_CompareAction _action);

/* This method sets the time or the angles references, for the POUT channel, to 
perform the required compare action programmed with the method POUT_Config. 
If <_action> = TIME the two compare value in the union structure will be 
considered as "Toff  and Ton" otherwise they will be "StartAngle and StopAngle". */
int16_t PIO_PoutSet(t_PIO_CHANNELS _channel, t_DelayUnion _firstRef, t_DelayUnion _secondRef);

/* This method enables the output related to the hardware pin 
connected to the Symbolic name of the POUT channel. */
int16_t PIO_PoutActivate(t_PIO_CHANNELS _channel);

/* This method sets the hardware pin connected to the Symbolic name 
of the POUT channel to the HIGH LEVEL after a time delay. */
int16_t PIO_PoutForceOpen(t_PIO_CHANNELS _channel, t_Period _delay);

/* This method sets the hardware pin connected to the Symbolic name 
of the POUT channel to the LOW LEVEL after a time delay. */
int16_t PIO_PoutForceClose(t_PIO_CHANNELS _channel, t_Period _delay);

/* This method enables the input related to the POUT channel */
int16_t PIO_PoutEnable(t_PIO_CHANNELS _channel);

/* This method disables the input related to the POUT channel */
int16_t PIO_PoutDisable(t_PIO_CHANNELS _channel);

/* This method reads the state of the POUT channel */
int16_t PIO_PoutGetState(t_PIO_CHANNELS _channel, t_State *_state);

/* This method allows the unified channel to generate an interrupt handler */
//int16_t PIO_PoutSetInterruptHandler(t_PIO_CHANNELS _channel, t_IsrFunc _isrFunc , t_IsrFuncPri _priority);
int16_t PIO_PoutSetInterruptHandler(t_PIO_CHANNELS _channel, TaskType taskID);

/* This method enables the execution of the interrupt */
int16_t PIO_PoutEnableInterrupt(t_PIO_CHANNELS _channel);

/* This method disables the execution of the interrupt  */
int16_t PIO_PoutDisableInterrupt(t_PIO_CHANNELS _channel);


/***************************************/
/*    FastLinked PIN/POUT Interface    */
/***************************************/
/* This method executes the initializations of the FastLinked  */
/* PIN/POUT channels                                           */
int16_t PIO_PinOutSlowConfig(t_PIO_CHANNELS _pinChannel, t_PIO_CHANNELS _poutChannel);
int16_t PIO_PinOutFastConfig(t_PIO_CHANNELS _pinChannel, t_PIO_CHANNELS _poutChannel, t_PIO_CHANNELS _dirChannel);

int16_t PIO_PinOutSlowEnable(t_PIO_CHANNELS _pinChannel, t_PIO_CHANNELS _poutChannel);
int16_t PIO_PinOutFastEnable(t_PIO_CHANNELS _pinChannel, t_PIO_CHANNELS _poutChannel, t_PIO_CHANNELS _dirChannel);

int16_t PIO_PinOutSlowDisable(t_PIO_CHANNELS _pinChannel, t_PIO_CHANNELS _poutChannel);
int16_t PIO_PinOutFastDisable(t_PIO_CHANNELS _pinChannel, t_PIO_CHANNELS _poutChannel, t_PIO_CHANNELS _dirChannel);

/* This method sets the minimum on/off time of the OUT channel */
int16_t PIO_PinOutSetMinTimes(t_PIO_CHANNELS _poutchannel, t_DelayUnion _minTimeOn, t_DelayUnion _minTimeOff);

int16_t PIO_PinoutSetMode(t_PIO_CHANNELS _poutChannel, uint8_t _mode);

/* This method gets the number of switchings of the OUT channel */
int16_t PIO_PinOutGetSwitchingTimes(t_PIO_CHANNELS _poutChannel, uint32_t *_switchingTimes);

/* This method gets the last on/off periods of the OUT channel */
int16_t PIO_PinOutGetLastPeriods(t_PIO_CHANNELS _poutChannel, uint32_t *lastOnPeriod, uint32_t *lastOffPeriod);

#endif /* _PIO_H_ */

