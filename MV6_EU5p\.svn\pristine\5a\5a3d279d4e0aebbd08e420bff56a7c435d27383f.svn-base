#ifndef __DIAGCANEE_H__
#define __DIAGCANEE_H__

#include "typedefs.h"

#ifdef _BUILD_DIAGCANMGM_

// USED EEPROM SECTION DEFINES
//#define EE_ID1   1
//#define EE_ID2   2

int16_t DIAGCANMGM_EE_UpdateRecordID1(void);
int16_t DIAGCANMGM_EE_UpdateRecordID2(void);
void DIAGCANMGM_EE_PowerOn(void);
//void DIAGCANMGM_EE_Init(void);
int16_t DIAGCANMGM_EE_Check_ID2(void);
void DIAGCANMGM_EE_InitID1(void);
void DIAGCANMGM_EE_InitID2(void);
void DIAGCANMGM_EE_SWVers_Update(void);


#endif /* _BUILD_DIAGCANMGM_ */
#endif /* __DIAGCANEE_H__ */