/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/ST_MV1_26_EXHVAL/tree/APPLIC#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1891   $                                                                                          */
/* $Date:: 2009-10-29 12:12:55 +0100 (gio, 29 ott 2009)   $                                                      */
/* $Author:: ToniS                   $                                                                       */
/*****************************************************************************************************************/

#ifdef  MATLAB_MEX_FILE
#include "typedefs.h"
#define _BUILD_SELFMGM_
#endif

#ifdef _BUILD_SELFMGM_
#include "selfmgm_ee.h"

const uint16_T VAngThrLh1 = VTHRANG_LH_DEF1;
const uint16_T VAngThrLh2 = VTHRANG_LH_DEF2;
const uint16_T VAngThrMin1 = VTHRANG_MIN_DEF1;
const uint16_T VAngThrMin2 = VTHRANG_MIN_DEF2;
const uint8_T  StDbwSelfError = 1;
const uint8_T  FlgSelfDisable = 0;

#endif // _BUILD_SELFMGM_
