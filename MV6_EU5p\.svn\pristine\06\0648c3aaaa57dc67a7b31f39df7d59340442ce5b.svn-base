/*
 * File: LaunchCtrl.h
 *
 * Code generated for Simulink model 'LaunchCtrl'.
 *
 * Model version                  : 1.234
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jun 14 15:33:33 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_LaunchCtrl_h_
#define RTW_HEADER_LaunchCtrl_h_
#include <string.h>
#ifndef LaunchCtrl_COMMON_INCLUDES_
# define LaunchCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* LaunchCtrl_COMMON_INCLUDES_ */

#include "LaunchCtrl_types.h"

/* Includes for objects with custom storage classes. */
#include "launchctrl_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKLCCMETRG_dim                 6U                        /* Referenced by:
                                                                  * '<S53>/BKLCCMETRG_dim'
                                                                  * '<S54>/BKLCCMETRG_dim'
                                                                  * '<S78>/BKLCCMETRG_dim'
                                                                  * '<S79>/BKLCCMETRG_dim'
                                                                  */

/* dim */
#define BKLCIDXCTF_dim                 3U                        /* Referenced by: '<S13>/BKLCIDXCTF_dim' */

/* dim */
#define BKRPMLCTRGWOT_dim              4U                        /* Referenced by:
                                                                  * '<S14>/BKRPMLCTRGWOT_dim'
                                                                  * '<S18>/BKRPMLCTRGWOT_dim'
                                                                  */

/* dim */
#define ID_LAUNCH_CTRL                 20703085U                 /* Referenced by: '<S2>/ID_LAUNCH_CTRL' */

/* mask */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  int32_T offLcKm;                     /* '<S43>/Enable_Lc_Km' */
  int32_T tmpOdometer;                 /* '<S43>/Enable_Lc_Km' */
  uint8_T flgRetTO;                    /* '<S4>/Chart_LaunchCtrl' */
  uint8_T enLcKm;                      /* '<S43>/Enable_Lc_Km' */
  uint8_T is_active_c3_LaunchCtrl;     /* '<S4>/Chart_LaunchCtrl' */
  uint8_T is_LC_COND_CTRL;             /* '<S4>/Chart_LaunchCtrl' */
  uint8_T is_LC_OVERRIDE_CTRL;         /* '<S4>/Chart_LaunchCtrl' */
  uint8_T cntWAMsg;                    /* '<S4>/Chart_LaunchCtrl' */
  uint8_T is_active_c1_LaunchCtrl;     /* '<S43>/Enable_Lc_Km' */
  uint8_T is_c1_LaunchCtrl;            /* '<S43>/Enable_Lc_Km' */
  boolean_T Relay_Mode;                /* '<S15>/Relay' */
} D_Work_LaunchCtrl_T;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc3_Trig_ZCE;     /* '<S1>/trig_to_fc3' */
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
} PrevZCSigStates_LaunchCtrl_T;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PreTDC;                   /* '<Root>/ev_PreTDC' */
} ExternalInputs_LaunchCtrl_T;

/* Block signals and states (default storage) */
extern D_Work_LaunchCtrl_T LaunchCtrl_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_LaunchCtrl_T LaunchCtrl_U;

/* Model entry point functions */
extern void LaunchCtrl_initialize(void);
extern void LaunchCtrl_step(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S21>/Data Type Duplicate' : Unused code path elimination
 * Block '<S59>/Data Type Duplicate' : Unused code path elimination
 * Block '<S60>/Data Type Duplicate' : Unused code path elimination
 * Block '<S61>/Data Type Duplicate' : Unused code path elimination
 * Block '<S62>/Data Type Duplicate' : Unused code path elimination
 * Block '<S58>/Data Type Duplicate' : Unused code path elimination
 * Block '<S64>/Data Type Duplicate' : Unused code path elimination
 * Block '<S69>/Data Type Duplicate' : Unused code path elimination
 * Block '<S67>/Data Type Duplicate' : Unused code path elimination
 * Block '<S70>/Data Type Duplicate' : Unused code path elimination
 * Block '<S68>/Data Type Duplicate' : Unused code path elimination
 * Block '<S75>/Data Type Duplicate' : Unused code path elimination
 * Block '<S73>/Data Type Duplicate' : Unused code path elimination
 * Block '<S76>/Data Type Duplicate' : Unused code path elimination
 * Block '<S74>/Data Type Duplicate' : Unused code path elimination
 * Block '<S83>/Data Type Duplicate' : Unused code path elimination
 * Block '<S84>/Data Type Duplicate' : Unused code path elimination
 * Block '<S85>/Data Type Duplicate' : Unused code path elimination
 * Block '<S87>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Conversion' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion' : Eliminate redundant data type conversion
 * Block '<S55>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S55>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S55>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S59>/Conversion' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S62>/Conversion' : Eliminate redundant data type conversion
 * Block '<S64>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S64>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S64>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S67>/Conversion' : Eliminate redundant data type conversion
 * Block '<S67>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S67>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S67>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S69>/Conversion' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S75>/Conversion' : Eliminate redundant data type conversion
 * Block '<S74>/Conversion' : Eliminate redundant data type conversion
 * Block '<S74>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S74>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S74>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion' : Eliminate redundant data type conversion
 * Block '<S80>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S80>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S80>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S83>/Conversion' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S84>/Conversion' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S87>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S87>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S87>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S26>/ZERO' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('LaunchCtrl_gen/LaunchCtrl')    - opens subsystem LaunchCtrl_gen/LaunchCtrl
 * hilite_system('LaunchCtrl_gen/LaunchCtrl/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'LaunchCtrl_gen'
 * '<S1>'   : 'LaunchCtrl_gen/LaunchCtrl'
 * '<S2>'   : 'LaunchCtrl_gen/LaunchCtrl/Init'
 * '<S3>'   : 'LaunchCtrl_gen/LaunchCtrl/PreTDC'
 * '<S4>'   : 'LaunchCtrl_gen/LaunchCtrl/T10ms'
 * '<S5>'   : 'LaunchCtrl_gen/LaunchCtrl/trig_to_fc1'
 * '<S6>'   : 'LaunchCtrl_gen/LaunchCtrl/trig_to_fc2'
 * '<S7>'   : 'LaunchCtrl_gen/LaunchCtrl/trig_to_fc3'
 * '<S8>'   : 'LaunchCtrl_gen/LaunchCtrl/Init/Init'
 * '<S9>'   : 'LaunchCtrl_gen/LaunchCtrl/Init/Init_Data'
 * '<S10>'  : 'LaunchCtrl_gen/LaunchCtrl/Init/Init_Scheduler'
 * '<S11>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff'
 * '<S12>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff1'
 * '<S13>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff'
 * '<S14>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget'
 * '<S15>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff/Calc_flgHys'
 * '<S16>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff/LookUp_S16_S16'
 * '<S17>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S18>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/Calc_Ratio'
 * '<S19>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LC_TO_LIM'
 * '<S20>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LookUp_IR_S16'
 * '<S21>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/RateLimiter_S2'
 * '<S22>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/Calc_Ratio/Filt_Speed'
 * '<S23>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/Calc_Ratio/PreLookUpIdSearch_U16'
 * '<S24>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S25>'  : 'LaunchCtrl_gen/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/RateLimiter_S2/Data Type Conversion Inherited1'
 * '<S26>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Ctrl_Level'
 * '<S27>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions'
 * '<S28>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Chart_LaunchCtrl'
 * '<S29>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim'
 * '<S30>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Dis'
 * '<S31>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Idle'
 * '<S32>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct'
 * '<S33>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret'
 * '<S34>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim'
 * '<S35>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Ctrl_Level/Compare To Constant'
 * '<S36>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcDis'
 * '<S37>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEn'
 * '<S38>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEnd'
 * '<S39>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLaunch'
 * '<S40>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel'
 * '<S41>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady'
 * '<S42>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcRet'
 * '<S43>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm'
 * '<S44>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcDis/Compare To Zero'
 * '<S45>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEn/Compare To Zero'
 * '<S46>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEn/Compare To Zero1'
 * '<S47>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel'
 * '<S48>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady/Reset_Idle'
 * '<S49>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady/Set_Ready'
 * '<S50>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady/Set_Ready/Compare To Zero'
 * '<S51>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km'
 * '<S52>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Assign_Lc_CtfLim'
 * '<S53>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim'
 * '<S54>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Ratio'
 * '<S55>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S1'
 * '<S56>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S16'
 * '<S57>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S2'
 * '<S58>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/RateLimiter_S16'
 * '<S59>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S60>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S61>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S62>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S63>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Ratio/Filt_Speed'
 * '<S64>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Ratio/PreLookUpIdSearch_U16'
 * '<S65>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct/Assign_Lc_Reduct'
 * '<S66>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct'
 * '<S67>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S1'
 * '<S68>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S16'
 * '<S69>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S1/Data Type Conversion Inherited1'
 * '<S70>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S71>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret/Assign_Lc_Ret'
 * '<S72>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret'
 * '<S73>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S1'
 * '<S74>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S16'
 * '<S75>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S1/Data Type Conversion Inherited1'
 * '<S76>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S77>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Assign_Lc_SatLim'
 * '<S78>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim'
 * '<S79>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Ratio'
 * '<S80>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S1'
 * '<S81>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S16'
 * '<S82>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S2'
 * '<S83>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S84>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S85>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S86>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Ratio/Filt_Speed'
 * '<S87>'  : 'LaunchCtrl_gen/LaunchCtrl/T10ms/Lc_SatLim/Calc_Ratio/PreLookUpIdSearch_U16'
 */

/*-
 * Requirements for '<Root>': LaunchCtrl
 */
#endif                                 /* RTW_HEADER_LaunchCtrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
