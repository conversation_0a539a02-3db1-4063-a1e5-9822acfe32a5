/*
 * File: C:\localmodules\local_AirMgm_PI6\slprj\ert\_sharedutils\mul_wide_su32.h
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2438
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Thu Feb 14 08:15:05 2019
 */

#ifndef SHARE_mul_wide_su32
#define SHARE_mul_wide_su32

extern void mul_wide_su32(int32_T in0, uint32_T in1, uint32_T *ptrOutBitsHi,
  uint32_T *ptrOutBitsLo);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
