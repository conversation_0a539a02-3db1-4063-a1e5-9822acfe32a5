/* Precompiler defines for SYNC module */
/****************************************************************************
     Peripheral defines 
 ****************************************************************************/

#ifndef __SYNC_CFG
#define __SYNC_CFG


  #define USE_ETPU_A 1
#if (TARGET_TYPE == MPC5554)
  #define USE_ETPU_B 1
#else
  #define USE_ETPU_B 0
#endif
  /* ETPU A */
  /* TCR1 Prescaler Control to obtain us in TCR1 */
#if (FSYS_60 == 1)
  #define ETPU_A_TCR1P  ((FSYS / 2) -1)
  /* TCR2 Prescaler Control gc */
  #define ETPU_A_TCR2P  2
  /* TCRCLK Signal Filter Control gc */
  #define ETPU_A_TCRF   1
  /* Filter Prescaler Clock Control */
  #define ETPU_A_FPSCK  7
 /* From EI-Ferrari  */
#elif (FSYS_80 == 1) /* From Ei-Ferrari @80MHz */
  #define ETPU_A_TCR1P  ((FSYS / 2) -1)
  /* TCR2 Prescaler Control gc */
  #define ETPU_A_TCR2P  2
  /* TCRCLK Signal Filter Control gc */
  #define ETPU_A_TCRF   0
  /* Filter Prescaler Clock Control */
  #define ETPU_A_FPSCK  7
#else
  #error Fsys not defined!!!
#endif
  

  /* ETPU B */
#if (FSYS_60 == 1) 
  /* TCR1 Prescaler Control to obtain us in TCR1 */
  #define ETPU_B_TCR1P  ((FSYS / 2) -1)
  /* TCR2 Prescaler Control gc */
  #define ETPU_B_TCR2P  2
  /* TCRCLK Signal Filter Control gc */
  #define ETPU_B_TCRF   0
  /* Filter Prescaler Clock Control */
  #define ETPU_B_FPSCK  7
#elif (FSYS_80 == 1) /* From Ei-Ferrari @80MHz */
  /* TCR1 Prescaler Control to obtain us in TCR1 */
  #define ETPU_B_TCR1P  ((FSYS / 2) -1)
  /* TCR2 Prescaler Control gc */
  #define ETPU_B_TCR2P  2
  /* TCRCLK Signal Filter Control gc */
  #define ETPU_B_TCRF   0
  /* Filter Prescaler Clock Control */
  #define ETPU_B_FPSCK  4
#else
  #error Fsys not defined!!!
#endif

  /*
  Channel Digital Filter Control. 
  These bits select a digital filtering mode for the channels when configured 
  as inputs for improved noise immunity. 
  The eTPU has three digital filtering modes for the channels which provide 
  programmable trade-off between signal latency and noise immunity, 
  Changing CDFC during eTPU normal input channel operation 
  is not recommended since it changes the behavior of the transition detection 
  logic while executing its operation.

  CDFC      Selected Digital Filter
  00            TPU2/3 Two Sample Mode: Using the filter clock which is the system clock divided 
          by (2, 4, 8,..., 256) as a sampling clock (selected by FPSCK field in ETPUECR), 
          comparing two consecutive samples which agree with each other sets the input signal state. 
          This is the default reset state.
  01            RESERVED
  10            eTPU Three Sample Mode: Similar to the TPU2/3 two sample mode, 
          but comparing three consecutive samples which agree with each other sets 
          the input signal state.
  11            eTPU Continuous Mode: Signal needs to be stable for the whole filter clock period. 
          This mode compares all the values at the rate of system clock divided by two, between 
          two consecutive filter clock pulses. Signal needs to be continuously stable for 
          the entire period. If all the values agree with each other, input signal state is updated.
  */
  #define ETPU_ECR_CDFC_TWO_SAMPLE_MODE     0
  #define ETPU_ECR_CDFC_THREE_SAMPLE_MODE   2
  #define ETPU_ECR_CDFC_TWO_CONTINUOUS_MODE 3



  /*
  TCR1 Clock/Gate Control
  00      selects TCRCLK as clock source for the TCR1 prescaler
  01      reserved
  10            selects system clock divided by 2 as clock source for the TCR1 prescaler
  11            TCR1CTL shuts down TCR1 clock. TCR1 can still change if STAC client.
  */
  #define ETPU_TBCR_TCR1CTL_SELECT_TCRCLK           0
  #define ETPU_TBCR_TCR1CTL_SELECT_SYSTEMCLOCK_DIV2 2
  #define ETPU_TBCR_TCR1CTL_SELECT_SHUT_TCR1CLOCK   3

  /*

  TCR2 Clock/Gate Control     
  TCR2CTL         TCR2 Clock                                                      Angle Tooth Detection
  000             Gated DIV8 clock (system clock / 8).                                                              N/A1
                  When the external TCRCLK signal is low, the DIV8 clock is         
                  blocked, preventing it from incrementing TCR2. 
                  When the external TCRCLK signal is high, TCR2 is 
                  incremented at the frequency of the system clock 
                  divided by 8.

  001             Rise transition on TCRCLK signal increments TCR2.               Rising Edge
  010             Fall transition on TCRCLK signal increments TCR2.               Falling Edge
  011             Rise or fall transition on TCRCLK signal increments TCR2.       Both
  100             DIV8 clock (system clock / 8)                                   N/A1
  101             Reserved                                                                                                                        N/A1
  110             Reserved
  111             TCR2CTL shuts down TCR2 clocking, except on Angle Mode. 
                  TCR2 can also change as STAC client.

  N/A1: These selections must not be used in Angle Mode.
  */
  #define ETPU_TBCR_TCR2CTL_GATED_DIV8          0 
  #define ETPU_TBCR_TCR2CTL_TCRCLK_RISE_TRANS   1
  #define ETPU_TBCR_TCR2CTL_TCRCLK_FALL_TRANS   2
  #define ETPU_TBCR_TCR2CTL_TCRCLK_BOTH_TRANS   3
  #define ETPU_TBCR_TCR2CTL_DIV8                4 
  #define ETPU_TBCR_TCR2CTL_TCR2_SHUT           7 


#if (VRS_ACTIVE_LEVEL == VRS_RISING_EDGE)
  #define ETPU_TBCR_TCR2CTL_TCRCLK_TRANS        ETPU_TBCR_TCR2CTL_TCRCLK_RISE_TRANS
#elif (VRS_ACTIVE_LEVEL == VRS_FALLING_EDGE)
  #define ETPU_TBCR_TCR2CTL_TCRCLK_TRANS        ETPU_TBCR_TCR2CTL_TCRCLK_FALL_TRANS
#elif (VRS_ACTIVE_LEVEL == VRS_BOTH_EDGE)
  #define ETPU_TBCR_TCR2CTL_TCRCLK_TRANS        ETPU_TBCR_TCR2CTL_TCRCLK_BOTH_TRANS
#else
  #error Errore configurazione proibita!!!
#endif

  #define ETPU_A_CHAN1_ISR_VECTOR   69



/*
  NO MORE ASSUMPTION ON CODE SIZE: USING SIZEOF(ETPUCODE)
   Assumption is made that code will not be bigger than the following size
   - ETPU_MAXCODESIZE    0x4000 
*/

  /* The entry table size */
  #define ETPU_ENTRYTABLESIZE 0x200

  /* ETPU_A/ETPU_B Channels definition */
  #define ETPU_A_CHAN_00      0
  #define ETPU_A_CHAN_01      1
  #define ETPU_A_CHAN_02      2
  #define ETPU_A_CHAN_03      3
  #define ETPU_A_CHAN_04      4
  #define ETPU_A_CHAN_05      5
  #define ETPU_A_CHAN_06      6
  #define ETPU_A_CHAN_07      7
  #define ETPU_A_CHAN_08      8
  #define ETPU_A_CHAN_09      9
  #define ETPU_A_CHAN_10      10
  #define ETPU_A_CHAN_11      11
  #define ETPU_A_CHAN_12      12
  #define ETPU_A_CHAN_13      13
  #define ETPU_A_CHAN_14      14
  #define ETPU_A_CHAN_15      15
  #define ETPU_A_CHAN_16      16
  #define ETPU_A_CHAN_17      17
  #define ETPU_A_CHAN_18      18
  #define ETPU_A_CHAN_19      19
  #define ETPU_A_CHAN_20      20
  #define ETPU_A_CHAN_21      21
  #define ETPU_A_CHAN_22      22
  #define ETPU_A_CHAN_23      23
  #define ETPU_A_CHAN_24      24
  #define ETPU_A_CHAN_25      25
  #define ETPU_A_CHAN_26      26
  #define ETPU_A_CHAN_27      27
  #define ETPU_A_CHAN_28      28
  #define ETPU_A_CHAN_29      29
  #define ETPU_A_CHAN_30      30
  #define ETPU_A_CHAN_31      31

  #define ETPU_B_CHAN_00      64
  #define ETPU_B_CHAN_01      65
  #define ETPU_B_CHAN_02      66
  #define ETPU_B_CHAN_03      67
  #define ETPU_B_CHAN_04      68
  #define ETPU_B_CHAN_05      69
  #define ETPU_B_CHAN_06      70
  #define ETPU_B_CHAN_07      71
  #define ETPU_B_CHAN_08      72
  #define ETPU_B_CHAN_09      73
  #define ETPU_B_CHAN_10      74
  #define ETPU_B_CHAN_11      75
  #define ETPU_B_CHAN_12      76
  #define ETPU_B_CHAN_13      77
  #define ETPU_B_CHAN_14      78
  #define ETPU_B_CHAN_15      79
  #define ETPU_B_CHAN_16      80
  #define ETPU_B_CHAN_17      81
  #define ETPU_B_CHAN_18      82
  #define ETPU_B_CHAN_19      83
  #define ETPU_B_CHAN_20      84
  #define ETPU_B_CHAN_21      85
  #define ETPU_B_CHAN_22      86
  #define ETPU_B_CHAN_23      87
  #define ETPU_B_CHAN_24      88
  #define ETPU_B_CHAN_25      89
  #define ETPU_B_CHAN_26      90
  #define ETPU_B_CHAN_27      91
  #define ETPU_B_CHAN_28      92
  #define ETPU_B_CHAN_29      93
  #define ETPU_B_CHAN_30      94
  #define ETPU_B_CHAN_31      95

  /* Enable or disable eTPU code copy to SCM */
  #define ETPU_CODE_ENABLED   1
  
#if (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_08 ) || (ENGINE_TYPE==MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_30) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
  /* eTPU FUNCTIONS PARAMETERS VALUES */
  #define INITIAL_STARTUPBLANKINGPERIOD (20000)   /* = 20000 [us] = 20 [ms] */
  #define INITIAL_STARTUPSKIPPEDTEETH   (3)
  #define INITIAL_RATIOSYNCLOW          (2.20*(1<<20))          
  #define INITIAL_RATIOSYNCHIGH         (7.50*(1<<20))      
  #define INITIAL_RATIOTOOTHLOW         (0.25*(1<<20))
  #define INITIAL_RATIOTOOTHHIGH        (2.10*(1<<20))
  #define INITIAL_RATIOHOLELOW          (2.20*(1<<20))  
  #define INITIAL_RATIOHOLEHIGH         (7.50*(1<<20))      
  #define INITIAL_RATIOFIRSTTOOTHLOW    (0.10*(1<<20))    
  #define INITIAL_RATIOFIRSTTOOTHHIGH   (0.50*(1<<20))          
   
  #define INITIAL_BLANKINGPERIOD        25 /* [us] (0.2* MIN_TOOTH_PERIOD)  */
  #define INITIAL_STALL_PERIOD          0x70000  /* = 0x70000 = 458752 [us] */

#else
#error WARNING: Configuration not supported!!!
#endif
                                
/****************************************************************************
     Peripheral errors defines 
 ****************************************************************************/
    #define SYNC_PERIPHERAL_NOT_ENABLED      -5
#endif /* __SYNC_CFG */

