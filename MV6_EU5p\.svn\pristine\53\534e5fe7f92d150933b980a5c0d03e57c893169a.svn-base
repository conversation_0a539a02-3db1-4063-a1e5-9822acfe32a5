/**
 ******************************************************************************
 **  Filename:      QAirTargetMgm_private.h
 **  Date:          19-Sep-2022
 **
 **  Model Version: 1.720
 ******************************************************************************
 **/

#ifndef RTW_HEADER_QAirTargetMgm_private_h_
#define RTW_HEADER_QAirTargetMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "QAirTargetMgm.h"

/* Includes for objects with custom storage classes. */
#include "trq_est.h"
#include "rpm_limiter.h"
#include "af_ctrl.h"
#include "sabasic_mgm.h"
#include "engflag.h"
#include "air_mgm.h"
#include "syncmgm.h"
#include "throttle_target.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T RTQATINC;               /* Variable: RTQATINC
                                        * Referenced by: '<S7>/RTQATINC'
                                        * Inc QAirTarget
                                        */
extern uint16_T KFILTQAIRTRG;          /* Variable: KFILTQAIRTRG
                                        * Referenced by: '<S9>/KFILTQAIRTRG'
                                        * kf
                                        */
extern uint16_T VTKFILTQAIRTRG[8];     /* Variable: VTKFILTQAIRTRG
                                        * Referenced by: '<S9>/VTKFILTQAIRTRG'
                                        * kf
                                        */
extern uint16_T THRSTSTABDYN;          /* Variable: THRSTSTABDYN
                                        * Referenced by: '<S8>/THRSTSTABDYN'
                                        * Threshold stab QAirTarget
                                        */
extern uint16_T BKKFILTQAIRTRG[8];     /* Variable: BKKFILTQAIRTRG
                                        * Referenced by: '<S9>/BKKFILTQAIRTRG'
                                        * Breakpoints of Rpm for VTKFILTQAIRTRG
                                        */
extern uint16_T TIMSTSTABDYN;          /* Variable: TIMSTSTABDYN
                                        * Referenced by: '<S8>/TIMSTSTABDYN'
                                        * Time stab QAirTarget
                                        */
extern void QAirTargetMgm_Init(void);
extern void QAirTargetMgm_T10ms(void);

#endif                                 /* RTW_HEADER_QAirTargetMgm_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
