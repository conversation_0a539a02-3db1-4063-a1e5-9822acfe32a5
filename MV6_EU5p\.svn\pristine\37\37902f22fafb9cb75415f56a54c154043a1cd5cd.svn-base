@echo off
cls
if exist "C:\pemicro\progppcnexus1_33\PrgBinaryFolder\ENGINE.CFG" goto STEPA
echo LULA 2.0 ERROR: WARNING Config Device absent, check to "C:\pemicro\progppcnexus1_33\PrgBinaryFolder\ENGINE.CFG"
echo *
goto END

:STEPA
if exist "C:\pemicro\progppcnexus1_33\algorithms\Freescale_MPC5634M_1x32x384k_LCFlash.PCP" goto STEPB
echo LULA 2.0 ERROR: WARNING Algorithm Device absent, check to"C:\pemicro\progppcnexus1_33\algorithms\Freescale_MPC5634M_1x32x384k_LCFlash.PCP"
echo *
goto END

:STEPB
if exist "C:\pemicro\progppcnexus1_33\PrgBinaryFolder\bin\Full_M3MxxR.srec" goto COMMAND
echo LULA 2.0 ERROR: WARNING Source Code absent, check to "C:\pemicro\progppcnexus1_33\PrgBinaryFolder\Full_M3MxxR.srec"
echo *
goto END

:COMMAND
if exist "C:\pemicro\progppcnexus1_33\cprogppcnexus.exe" goto PROGRAM
echo LULA 2.0 ERROR: WARNING Command path, check to correct install dir.
echo *
goto END

:PROGRAM
C:\pemicro\progHCS08sz\cproghcs08 ? Freq 8000000 Interface=usbmultilink Port=USB1 C:\pemicro\progHCS08sz\PrgBinaryFolder\ENGINE.CFG

:END
ECHO LULA 2.0: SEQUENCE TERMINATED.
echo *
pause