/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_SPIGAIN_

#include "SPIGain.h"

#pragma ghs section rodata=".calib" 

#ifdef _MCP6S9X_DEBUG_
    __declspec(section ".calib") uint8_T   GAINVARCONFIG = SPI_G_ONE_SHOT; /* Debug invio dati SPI_GAIN -> SPI_MCP6S9X. */
    __declspec(section ".calib") uint8_T   GAINVAR = 0; /* Calibrazione del gain -> SPI_MCP6S9X. */
    __declspec(section ".calib") uint8_T   GAINVARSTATUS = 0; /* Debug invio dati SPI_GAIN -> SPI_MCP6S9X. */
#endif

#endif
