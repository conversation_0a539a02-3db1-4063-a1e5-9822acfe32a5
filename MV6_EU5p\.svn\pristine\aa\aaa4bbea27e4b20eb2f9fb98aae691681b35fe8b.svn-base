/*
 * File: KnockCorr_data.c
 *
 * Real-Time Workshop code generated for Simulink model KnockCorr.
 *
 * Model version                        : 1.272
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Tue Feb 06 16:40:32 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Tue Feb 06 16:40:38 2007
 */

#include "..\KNOCKCORR_F\include\KnockCorr.h"
#include "..\KNOCKCORR_F\include\KnockCorr_private.h"

/* Invariant block signals (auto storage) */
ConstBlockIO_KnockCorr KnockCorr_ConstB = {
  384 ,                                 /* <S56>/Conversion2 */
  0U                                    /* <S56>/Conversion3 */
};

/* Constant parameters (auto storage) */
const ConstParam_KnockCorr KnockCorr_ConstP = {
  /* Computed Parameter: InitialValue
   * '<Root>/_DataStoreBlk_4'
   */
  { 6291456U, 6291456U, 6291456U, 6291456U, 6291456U, 6291456U, 6291456U,
    6291456U } ,
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_3'
   * '<Root>/_DataStoreBlk_8'
   */
  { 384U, 384U, 384U, 384U, 384U, 384U, 384U, 384U } ,
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_10'
   * '<Root>/_DataStoreBlk_11'
   * '<Root>/_DataStoreBlk_14'
   * '<Root>/_DataStoreBlk_15'
   * '<S7>/Memory1'
   */
  { 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U } ,
  /* Computed Parameter: TruthTable
   * '<S60>/Logic'
   */
  { 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0 }
};

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
