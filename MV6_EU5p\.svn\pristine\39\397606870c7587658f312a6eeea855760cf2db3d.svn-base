/** #############################################################################
**     Filename  : LIN_TL.C
**     Project   : ....
**     Processor : MPC5554
**
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 27/06/2005, 14.25
**     Abstract  :
**          This file implements the LIN TRANSPORT LAYER Communication Interface
**          module initialization according to the
**          Functional Specification "PAEO003_06_LINArchitecture.doc".
**     Settings  :
**          Device                                         : LIN
**          Communication mode                             : eSCI
**          Baud rate divisor = 4, SCI baud rate = 2000000 Bd/s
**          Data format                                    : 8 bits
**          Parity                                         : even
**          Wake up                                        : by idle line
**          RxD pin                                        : Enabled
**          TxD pin                                        : Enabled
**          Combined Interrupt
**            Interrupt                                    : INT_ESCI_A/INT_ESCI_B
**            Interrupt priority                           : 12/12
**            Transmission complete                        : Disabled
**            Receiver full                                : Disabled
** ##############################################################################*/

/* Start SCI Module */
#include "OS_api.h"
#include "tasksdefs.h"


#ifdef _BUILD_LINMGM_

#include "typedefs.h"
#include "task.h"
#include "lin_api.h"
#include "lin_tl.h"
#include "sci.h"

/* INTERFACE MANAGEMENT                                              */
/*-------------------------------------------------------------------*/
/* l_ifc_init   see paragraph 7.2.5.1 of LIN 2.1 spec. package       */
//#define LIN_BR_20K          (0x200)  //TBDef

int16_t l_sys_init(void)
{
    int16_t errorReturn = NO_ERROR;

    errorReturn = LIN_Config();

    return errorReturn;
}

/* l_ifc_goto_sleep   see paragraph 7.2.5.2 of LIN 2.1 spec. package */
int16_t l_ifc_goto_sleep(l_ifc_handle ifc_name)
{
    int16_t     errorReturn = NO_ERROR;

    if (ifc_name != LIN_NAD_ALTERNATOR)
    {
        return LIN_WRONG_NAD;
    }

    errorReturn = LIN_GoToSleep();

    return errorReturn;
}

/* l_ifc_wake_up   see paragraph 7.2.5.3 of LIN 2.1 spec. package    */
int16_t l_ifc_wake_up(l_ifc_handle ifc_name)
{
    int16_t     errorReturn = NO_ERROR;

    if (ifc_name != LIN_NAD_ALTERNATOR)
    {
        return LIN_WRONG_NAD;
    }

    errorReturn = LIN_WakeUp();

    return errorReturn;
}

/* l_ifc_read_status see paragraph 7.2.5.8 of LIN 2.1 spec. package  */
int16_t l_ifc_read_status(l_ifc_handle ifc_name)
{
    int16_t LIN_status = 0;
    uint16_t tmp;

    LIN_GetStatus(&tmp);

    LIN_status = LIN_lastPID << 8;

    if (tmp & (SCI_LIN_RECEIVE_FRAMING_ERROR|SCI_LIN_EX_CRC_ERROR|SCI_LIN_EX_CHKSUM_ERROR))
    {
        LIN_status |= 1; // error in response
    }

    if (tmp & SCI_LIN_EX_FRAME_CORRECTLY_RX)
    {
        LIN_status |= (1<<1); //successful transfer
    }

    if (tmp & SCI_LIN_RECEIVE_OVERFLOW_ERROR)
    {
        LIN_status |= (1<<2); //overrun
    }
        
    // TBD: Bus activity, event triggered frame collision, Save configuration
    
    return LIN_status;
}


/* ld_put_raw   see paragraph 7.4.3.1 of LIN 2.1 spec. package       */
void ld_put_raw(l_ifc_handle ifc_name, const l_u8 * const data)
{
    if (ifc_name != LIN_NAD_ALTERNATOR)
    {
        return;
    }

    LIN_TransmitFrame(data, LIN_MSG_8B, ifc_name);
}

/* ld_send_message see paragraph 7.4.4.1 of LIN 2.1 spec. package    */
void ld_send_message_master(l_u16 length, 
                            l_u8 NAD, 
                            const l_u8 * const data)
{
    LIN_TransmitFrame(data, length, NAD);
}

/* ld_receive_message see paragraph 7.4.4.2 of LIN 2.1 spec. package */
void ld_receive_message_master (l_u16 * const length,
                                l_u8 * const NAD, 
                                l_u8 * const data)
{
    LIN_ReceiveAnswer(*NAD);
}

#endif /* _BUILD_LINMGM_ */

