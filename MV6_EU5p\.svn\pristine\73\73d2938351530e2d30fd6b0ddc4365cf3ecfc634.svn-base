#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif
#include "diagmgm_out.h"
#ifdef _BUILD_DIAGMGM_

#pragma ghs section rodata=".calib" 


//(S1) Table to disable diagnosis [index]
__declspec(section ".calib") uint8_T TBDISDIAG[DIAG_NUMBER][TBDISDIAG_CULS] = 
{
    {255, 255, 255}, /*  0  */
    {255, 255, 255}, /*  1  */
    {255, 255, 255}, /*  2  */
    {255, 255, 255}, /*  3  */
    {255, 255, 255}, /*  4  */
    {255, 255, 255}, /*  5  */
    {255, 255, 255}, /*  6  */
    {255, 255, 255}, /*  7  */
    {255, 255, 255}, /*  8  */
    {255, 255, 255}, /*  9  */
    {255, 255, 255}, /*  10 */
    {255, 255, 255}, /*  11 */
    {255, 255, 255}, /*  12 */
    {255, 255, 255}, /*  13 */
    {255, 255, 255}, /*  14 */
    {202, 255, 255}, /*  15 */
    {255, 255, 255}, /*  16 */
    {255, 255, 255}, /*  17 */
    {255, 255, 255}, /*  18 */
    {255, 255, 255}, /*  19 */
    {255, 255, 255}, /*  20 */
    {255, 255, 255}, /*  21 */
    {255, 255, 255}, /*  22 */
    {255, 255, 255}, /*  23 */
    {255, 255, 255}, /*  24 */
    {255, 255, 255}, /*  25 */
    {255, 255, 255}, /*  26 */
    {255, 255, 255}, /*  27 */
    {255, 255, 255}, /*  28 */
    {255, 255, 255}, /*  29 */
    {255, 255, 255}, /*  30 */
    {255, 255, 255}, /*  31 */
    {255, 255, 255}, /*  32 */
    {255, 255, 255}, /*  33 */
    {255, 255, 255}, /*  34 */
    {255, 255, 255}, /*  35 */
    {255, 255, 255}, /*  36 */
    {255, 255, 255}, /*  37 */
    {255, 255, 255}, /*  38 */
    {255, 255, 255}, /*  39 */
    {255, 255, 255}, /*  40 */
    {255, 255, 255}, /*  41 */
    {255, 255, 255}, /*  42 */
    {255, 255, 255}, /*  43 */
    {255, 255, 255}, /*  44 */
    {255, 255, 255}, /*  45 */
    {255, 255, 255}, /*  46 */
    {255, 255, 255}, /*  47 */
    {255, 255, 255}, /*  48 */
    {255, 255, 255}, /*  49 */
    {255, 255, 255}, /*  50 */
    {255, 255, 255}, /*  51 */
    {255, 255, 255}, /*  52 */
    {255, 255, 255}, /*  53 */
    {255, 255, 255}, /*  54 */
    {255, 255, 255}, /*  55 */
    {255, 255, 255}, /*  56 */
    {255, 255, 255}, /*  57 */
    {255, 255, 255}, /*  58 */
    {255, 255, 255}, /*  59 */
    {255, 255, 255}, /*  60 */
    {255, 255, 255}, /*  61 */
    {255, 255, 255}, /*  62 */
    {255, 255, 255}, /*  63 */
    {255, 255, 255}, /*  64 */
    {255, 255, 255}, /*  65 */
    {255, 255, 255}, /*  66 */
    {255, 255, 255}, /*  67 */
    {255, 255, 255}, /*  68 */
    {255, 255, 255}, /*  69 */
    {255, 255, 255}, /*  70 */
    {255, 255, 255}, /*  71 */
    {255, 255, 255}, /*  72 */
    {255, 255, 255}, /*  73 */
    {255, 255, 255}, /*  74 */
    {255, 255, 255}, /*  75 */
    {255, 255, 255}, /*  76 */
    {255, 255, 255}, /*  77 */
    {255, 255, 255}, /*  78 */
    {255, 255, 255}, /*  79 */
    {255, 255, 255}, /*  80 */
    {255, 255, 255}, /*  81 */
    {255, 255, 255}, /*  82 */
    {255, 255, 255}, /*  83 */
    {255, 255, 255}, /*  84 */
    {255, 255, 255}, /*  85 */
    {255, 255, 255}, /*  86 */
    {255, 255, 255}, /*  87 */
    {255, 255, 255}, /*  88 */
    {255, 255, 255}, /*  89 */
    {255, 255, 255}, /*  90 */
    {255, 255, 255}  /*  91 */
};

//Force diagnosis fault
__declspec(section ".calib") uint8_T DIAGFOFAULTIDX = 255;
//Lower threshold on VBattery to set the flag FlgDisDiagVBat [V]
__declspec(section ".calib") uint16_T THINFVBATDDIAG = 144;   //( 9.0000*16)
//Higher threshold on VBattery to set the flag FlgDisDiagVBat [V]
__declspec(section ".calib") uint16_T THSUPVBATDDIAG = 256;   //(16.0000*16)
//Number of WUC before erasing the fault from the error memory [counter]
__declspec(section ".calib") uint8_T THRDIAGWUC = 40;
// [status]
__declspec(section ".calib") uint8_T VTDIAGENABLE[23] = 
{
    0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0
};
__declspec(section ".calib") uint8_T VTDIAGFFPRIORITY[23] = 
{
    0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0
};
__declspec(section ".calib") uint8_T VTDIAGENMONITOR[46] = 
{
    0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0
};

//Fault counter decrement step [counter]
__declspec(section ".calib") uint8_T VTSTEPDECFAULT[DIAG_NUMBER] = 
{
    1, /* 00 */
    1, /* 01 */ 
    1, /* 02 */
    1, /* 03 */
    1, /* 04 */
    1, /* 05 */
    1, /* 06 */
    1, /* 07 */
    1, /* 08 */
    1, /* 09 */
    1, /* 10 */
    1, /* 11 */
    1, /* 12 */
    1, /* 13 */
    1, /* 14 */
    1, /* 15 */
    1, /* 16 */
    1, /* 17 */
    1, /* 18 */
    1, /* 19 */
    1, /* 20 */
    1, /* 21 */
    1, /* 22 */
    1, /* 23 */
    1, /* 24 */
    1, /* 25 */
    1, /* 26 */
    1, /* 27 */
    1, /* 28 */
    1, /* 29 */
    1, /* 30 */
    1, /* 31 */
    1, /* 32 */
    1, /* 33 */
    1, /* 34 */
    1, /* 35 */
    1, /* 36 */
    1, /* 37 */
    1, /* 38 */
    1, /* 39 */
    1, /* 40 */
    1, /* 41 */
    1, /* 42 */
    1, /* 43 */
    1, /* 44 */
    1, /* 45 */
    1, /* 46 */
    1, /* 47 */
    1, /* 48 */
    1, /* 49 */
    1, /* 50 */
    1, /* 51 */
    1, /* 52 */
    1, /* 53 */
    1, /* 54 */
    1, /* 55 */
    1, /* 56 */
    1, /* 57 */
    1, /* 58 */
    1, /* 59 */
    1, /* 60 */
    1, /* 61 */
    1, /* 62 */
    1, /* 63 */
    1, /* 64 */
    1, /* 65 */
    1, /* 66 */
    1, /* 67 */
    1, /* 68 */
    1, /* 69 */
    1, /* 70 */
    1, /* 71 */
    1, /* 72 */
    1, /* 73 */
    1, /* 74 */
    1, /* 75 */
    1, /* 76 */
    1, /* 77 */
    1, /* 78 */
    1, /* 80 */
    1, /* 81 */
    1, /* 82 */
    1, /* 83 */
    1, /* 84 */
    1, /* 85 */
    1, /* 86 */
    1, /* 87 */
    1, /* 88 */
    1, /* 89 */
    1, /* 90 */
    1  /* 91 */
};
//Fault counter increment step [counter]
__declspec(section ".calib") uint8_T VTSTEPINCFAULT[DIAG_NUMBER] = 
{
    1, /* 00 */
    1, /* 01 */ 
    1, /* 02 */
    1, /* 03 */
    1, /* 04 */
    1, /* 05 */
    1, /* 06 */
    1, /* 07 */
    1, /* 08 */
    1, /* 09 */
    1, /* 10 */
    1, /* 11 */
    1, /* 12 */
    1, /* 13 */
    1, /* 14 */
    1, /* 15 */
    1, /* 16 */
    1, /* 17 */
    1, /* 18 */
    1, /* 19 */
    1, /* 20 */
    1, /* 21 */
    1, /* 22 */
    1, /* 23 */
    1, /* 24 */
    1, /* 25 */
    1, /* 26 */
    1, /* 27 */
    1, /* 28 */
    1, /* 29 */
    1, /* 30 */
    1, /* 31 */
    1, /* 32 */
    1, /* 33 */
    1, /* 34 */
    1, /* 35 */
    1, /* 36 */
    1, /* 37 */
    1, /* 38 */
    1, /* 39 */
    1, /* 40 */
    1, /* 41 */
    1, /* 42 */
    1, /* 43 */
    1, /* 44 */
    1, /* 45 */
    1, /* 46 */
    1, /* 47 */
    1, /* 48 */
    1, /* 49 */
    1, /* 50 */
    1, /* 51 */
    1, /* 52 */
    1, /* 53 */
    1, /* 54 */
    1, /* 55 */
    1, /* 56 */
    1, /* 57 */
    1, /* 58 */
    1, /* 59 */
    1, /* 60 */
    1, /* 61 */
    1, /* 62 */
    1, /* 63 */
    1, /* 64 */
    1, /* 65 */
    8, /* 66 */
    1, /* 67 */
    8, /* 68 */
    1, /* 69 */
    1, /* 70 */
    1, /* 71 */
    1, /* 72 */
    1, /* 73 */
    1, /* 74 */
    1, /* 75 */
    1, /* 76 */
    1, /* 77 */
    1, /* 78 */
    1, /* 79 */
    1, /* 80 */
    1, /* 81 */
    1, /* 82 */
    1, /* 83 */
    1, /* 84 */
    1, /* 85 */
    1, /* 86 */
    1, /* 87 */
    1, /* 88 */
    1, /* 89 */
    1, /* 90 */
    1  /* 91 */
};
// [counter]
__declspec(section ".calib") uint8_T VTTHRCONFFAULT[DIAG_NUMBER] = 
{
    255, /* 00 */
    255, /* 01 */ 
    255, /* 02 */
    255, /* 03 */
    255, /* 04 */
    255, /* 05 */
    255, /* 06 */
    255, /* 07 */
    255, /* 08 */
    255, /* 09 */
    255, /* 10 */
    255, /* 11 */
    255, /* 12 */
    255, /* 13 */
    255, /* 14 */
    255, /* 15 */
    255, /* 16 */
    255, /* 17 */
    255, /* 18 */
    255, /* 19 */
    255, /* 20 */
    255, /* 21 */
    255, /* 22 */
    255, /* 23 */
    255, /* 24 */
    255, /* 25 */
    255, /* 26 */
    255, /* 27 */
    255, /* 28 */
    255, /* 29 */
    255, /* 30 */
    255, /* 31 */
    255, /* 32 */
    255, /* 33 */
    255, /* 34 */
    255, /* 35 */
    255, /* 36 */
    255, /* 37 */
    255, /* 38 */
    255, /* 39 */
    255, /* 40 */
    255, /* 41 */
    255, /* 42 */
    255, /* 43 */
    255, /* 44 */
    255, /* 45 */
    255, /* 46 */
    255, /* 47 */
    255, /* 48 */
    255, /* 49 */
    255, /* 50 */
    255, /* 51 */
    255, /* 52 */
    255, /* 53 */
    255, /* 54 */
    255, /* 55 */
    255, /* 56 */
    255, /* 57 */
    255, /* 58 */
    255, /* 59 */
    255, /* 60 */
    255, /* 61 */
    255, /* 62 */
    255, /* 63 */
    255, /* 64 */
    255, /* 65 */
    255, /* 66 */
    255, /* 67 */
    255, /* 68 */
    255, /* 69 */
    255, /* 70 */
    255, /* 71 */
    255, /* 72 */
    255, /* 73 */
    255, /* 74 */
    255, /* 75 */
    255, /* 76 */
    255, /* 77 */
    255, /* 78 */
    255, /* 79 */
    255, /* 80 */
    255, /* 81 */
    255, /* 82 */
    255, /* 83 */
    255, /* 84 */
    255, /* 85 */
    255, /* 86 */
    255, /* 87 */
    255, /* 88 */
    255, /* 89 */
    255, /* 90 */
    255  /* 91 */
};

// [counter]
__declspec(section ".calib") uint8_T VTFOFAULT[DIAG_NUMBER] = 
{
    255, /* 00 */
    255, /* 01 */ 
    255, /* 02 */
    255, /* 03 */
    255, /* 04 */
    255, /* 05 */
    255, /* 06 */
    255, /* 07 */
    255, /* 08 */
    255, /* 09 */
    255, /* 10 */
    255, /* 11 */
    255, /* 12 */
    255, /* 13 */
    255, /* 14 */
    255, /* 15 */
    255, /* 16 */
    255, /* 17 */
    255, /* 18 */
    255, /* 19 */
    255, /* 20 */
    255, /* 21 */
    255, /* 22 */
    255, /* 23 */
    255, /* 24 */
    255, /* 25 */
    255, /* 26 */
    255, /* 27 */
    255, /* 28 */
    255, /* 29 */
    255, /* 30 */
    255, /* 31 */
    255, /* 32 */
    255, /* 33 */
    255, /* 34 */
    255, /* 35 */
    255, /* 36 */
    255, /* 37 */
    255, /* 38 */
    255, /* 39 */
    255, /* 40 */
    255, /* 41 */
    255, /* 42 */
    255, /* 43 */
    255, /* 44 */
    255, /* 45 */
    255, /* 46 */
    255, /* 47 */
    255, /* 48 */
    255, /* 49 */
    255, /* 50 */
    255, /* 51 */
    255, /* 52 */
    255, /* 53 */
    255, /* 54 */
    255, /* 55 */
    255, /* 56 */
    255, /* 57 */
    255, /* 58 */
    255, /* 59 */
    255, /* 60 */
    255, /* 61 */
    255, /* 62 */
    255, /* 63 */
    255, /* 64 */
    255, /* 65 */
    255, /* 66 */
    255, /* 67 */
    255, /* 68 */
    255, /* 69 */
    255, /* 70 */
    255, /* 71 */
    255, /* 72 */
    255, /* 73 */
    255, /* 74 */
    255, /* 75 */
    255, /* 76 */
    255, /* 77 */
    255, /* 78 */
    255, /* 79 */
    255, /* 80 */
    255, /* 81 */
    255, /* 82 */
    255, /* 83 */
    255, /* 84 */
    255, /* 85 */
    255, /* 86 */
    255, /* 87 */
    255, /* 88 */
    255, /* 89 */
    255, /* 90 */
    255  /* 91 */
};

#endif // _BUILD_DIAGMGM_

