/* Precompiler defines for ADC module */
/****************************************************************************
     Peripheral defines 
 ****************************************************************************/
/* VARIABLE_RATE Acquisition buffers size */
#define VAR_RATE_MAX_BUF_SIZE 512

/* CFIFOs TRIGGER definition */
#define CFIFO_ETSEL_TRIGGER              (0x00)
#define CFIFO_ETPU_TRIGGER               (0x01)
#define CFIFO_EMIOS_TRIGGER              (0x02)
#define CFIFO_EXTERNAL_TRIGGER           (0x03)

/* ETSEL0 sources definition */
#define ETSEL0_ETRIG0                    (0x00)
#define ETSEL0_RTI                       (0x01)
#define ETSEL0_PIT0                      (0x02)
#define ETSEL0_PIT1                      (0x03)
#define ETSEL0_PIT2                      (0x04)
#define ETSEL0_PIT3                      (0x05)
/* RESERVED                              (0x06) */
#define ETSEL0_ETRIG3                    (0x07)
#define ETSEL0_ETPUA30_PIT0              (0x08)
#define ETSEL0_ETPUA30_PIT1              (0x09)
/* RESERVED                              (0x0A) */
/* RESERVED                              (0x0B) */
#define ETSEL0_ETPUA28                   (0x0C)
#define ETSEL0_ETPUA29                   (0x0D)
#define ETSEL0_ETPUA30                   (0x0E)
#define ETSEL0_ETPUA31                   (0x0F)
/* RESERVED                              (0x10) */
/* RESERVED                              (0x11) */
/* RESERVED                              (0x12) */
/* RESERVED                              (0x13) */
#define ETSEL0_EMIOS10_PIT2              (0x14)
#define ETSEL0_EMIOS10_PIT3              (0x15)
/* RESERVED                              (0x16) */
/* RESERVED                              (0x17) */
/* RESERVED                              (0x18) */
/* RESERVED                              (0x19) */
/* RESERVED                              (0x1A) */
/* RESERVED                              (0x1B) */
/* RESERVED                              (0x1C) */
/* RESERVED                              (0x1D) */
/* RESERVED                              (0x1E) */
/* RESERVED                              (0x1F) */


/* ETSEL1 sources definition */
#define ETSEL1_ETRIG1                    (0x00)
#define ETSEL1_RTI                       (0x01)
#define ETSEL1_PIT0                      (0x02)
#define ETSEL1_PIT1                      (0x03)
#define ETSEL1_PIT2                      (0x04)
#define ETSEL1_PIT3                      (0x05)
/* RESERVED                              (0x06) */
#define ETSEL1_ETRIG2                    (0x07)
#define ETSEL1_ETPUA31_PIT0              (0x08)
#define ETSEL1_ETPUA31_PIT1              (0x09)
/* RESERVED                              (0x0A) */
/* RESERVED                              (0x0B) */
#define ETSEL1_ETPUA28                   (0x0C)
#define ETSEL1_ETPUA29                   (0x0D)
#define ETSEL1_ETPUA30                   (0x0E)
#define ETSEL1_ETPUA31                   (0x0F)
/* RESERVED                              (0x10) */
/* RESERVED                              (0x11) */
/* RESERVED                              (0x12) */
/* RESERVED                              (0x13) */
#define ETSEL1_EMIOS11_PIT2              (0x14)
#define ETSEL1_EMIOS11_PIT3              (0x15)
/* RESERVED                              (0x16) */
/* RESERVED                              (0x17) */
/* RESERVED                              (0x18) */
/* RESERVED                              (0x19) */
/* RESERVED                              (0x1A) */
/* RESERVED                              (0x1B) */
/* RESERVED                              (0x1C) */
/* RESERVED                              (0x1D) */
/* RESERVED                              (0x1E) */
/* RESERVED                              (0x1F) */


/* ETSEL2 sources definition */
#define ETSEL2_ETRIG0                    (0x00)
#define ETSEL2_RTI                       (0x01)
#define ETSEL2_PIT0                      (0x02)
#define ETSEL2_PIT1                      (0x03)
#define ETSEL2_PIT2                      (0x04)
#define ETSEL2_PIT3                      (0x05)
/* RESERVED                              (0x06) */
#define ETSEL2_ETRIG3                    (0x07)
#define ETSEL2_ETPUA30_PIT0              (0x08)
#define ETSEL2_ETPUA30_PIT1              (0x09)
/* RESERVED                              (0x0A) */
/* RESERVED                              (0x0B) */
#define ETSEL2_ETPUA28                   (0x0C)
#define ETSEL2_ETPUA29                   (0x0D)
#define ETSEL2_ETPUA30                   (0x0E)
#define ETSEL2_ETPUA31                   (0x0F)
/* RESERVED                              (0x10) */
/* RESERVED                              (0x11) */
/* RESERVED                              (0x12) */
/* RESERVED                              (0x13) */
#define ETSEL2_EMIOS10_PIT2              (0x14)
#define ETSEL2_EMIOS10_PIT3              (0x15)
/* RESERVED                              (0x16) */
/* RESERVED                              (0x17) */
/* RESERVED                              (0x18) */
/* RESERVED                              (0x19) */
/* RESERVED                              (0x1A) */
/* RESERVED                              (0x1B) */
/* RESERVED                              (0x1C) */
/* RESERVED                              (0x1D) */
/* RESERVED                              (0x1E) */
#define ETSEL2_EMIOS23                   (0x1F)


/* ETSEL3 sources definition */
#define ETSEL3_ETRIG1                    (0x00)
#define ETSEL3_RTI                       (0x01)
#define ETSEL3_PIT0                      (0x02)
#define ETSEL3_PIT1                      (0x03)
#define ETSEL3_PIT2                      (0x04)
#define ETSEL3_PIT3                      (0x05)
/* RESERVED                              (0x06) */
#define ETSEL3_ETRIG2                    (0x07)
#define ETSEL3_ETPUA31_PIT0              (0x08)
#define ETSEL3_ETPUA31_PIT1              (0x09)
/* RESERVED                              (0x0A) */
/* RESERVED                              (0x0B) */
#define ETSEL3_ETPUA28                   (0x0C)
#define ETSEL3_ETPUA29                   (0x0D)
#define ETSEL3_ETPUA30                   (0x0E)
#define ETSEL3_ETPUA31                   (0x0F)
/* RESERVED                              (0x10) */
/* RESERVED                              (0x11) */
/* RESERVED                              (0x12) */
/* RESERVED                              (0x13) */
#define ETSEL3_EMIOS11_PIT2              (0x14)
#define ETSEL3_EMIOS11_PIT3              (0x15)
/* RESERVED                              (0x16) */
/* RESERVED                              (0x17) */
/* RESERVED                              (0x18) */
/* RESERVED                              (0x19) */
/* RESERVED                              (0x1A) */
/* RESERVED                              (0x1B) */
/* RESERVED                              (0x1C) */
/* RESERVED                              (0x1D) */
/* RESERVED                              (0x1E) */
#define ETSEL3_EMIOS23                   (0x1F)


/* ETSEL4 sources definition */
#define ETSEL4_ETRIG0                    (0x00)
#define ETSEL4_RTI                       (0x01)
#define ETSEL4_PIT0                      (0x02)
#define ETSEL4_PIT1                      (0x03)
#define ETSEL4_PIT2                      (0x04)
#define ETSEL4_PIT3                      (0x05)
/* RESERVED                              (0x06) */
#define ETSEL4_ETRIG3                    (0x07)
#define ETSEL4_ETPUA30_PIT0              (0x08)
#define ETSEL4_ETPUA30_PIT1              (0x09)
/* RESERVED                              (0x0A) */
/* RESERVED                              (0x0B) */
#define ETSEL4_ETPUA28                   (0x0C)
#define ETSEL4_ETPUA29                   (0x0D)
#define ETSEL4_ETPUA30                   (0x0E)
#define ETSEL4_ETPUA31                   (0x0F)
/* RESERVED                              (0x10) */
/* RESERVED                              (0x11) */
/* RESERVED                              (0x12) */
/* RESERVED                              (0x13) */
#define ETSEL4_EMIOS10_PIT2              (0x14)
#define ETSEL4_EMIOS10_PIT3              (0x15)
/* RESERVED                              (0x16) */
/* RESERVED                              (0x17) */
/* RESERVED                              (0x18) */
/* RESERVED                              (0x19) */
/* RESERVED                              (0x1A) */
/* RESERVED                              (0x1B) */
/* RESERVED                              (0x1C) */
/* RESERVED                              (0x1D) */
/* RESERVED                              (0x1E) */
#define ETSEL4_EMIOS23                   (0x1F)


/* ETSEL5 sources definition */
#define ETSEL5_ETRIG1                    (0x00)
#define ETSEL5_RTI                       (0x01)
#define ETSEL5_PIT0                      (0x02)
#define ETSEL5_PIT1                      (0x03)
#define ETSEL5_PIT2                      (0x04)
#define ETSEL5_PIT3                      (0x05)
/* RESERVED                              (0x06) */
#define ETSEL5_ETRIG2                    (0x07)
#define ETSEL5_ETPUA31_PIT0              (0x08)
#define ETSEL5_ETPUA31_PIT1              (0x09)
/* RESERVED                              (0x0A) */
/* RESERVED                              (0x0B) */
#define ETSEL5_ETPUA28                   (0x0C)
#define ETSEL5_ETPUA29                   (0x0D)
#define ETSEL5_ETPUA30                   (0x0E)
#define ETSEL5_ETPUA31                   (0x0F)
/* RESERVED                              (0x10) */
/* RESERVED                              (0x11) */
/* RESERVED                              (0x12) */
/* RESERVED                              (0x13) */
#define ETSEL5_EMIOS11_PIT2              (0x14)
#define ETSEL5_EMIOS11_PIT3              (0x15)
/* RESERVED                              (0x16) */
/* RESERVED                              (0x17) */
/* RESERVED                              (0x18) */
/* RESERVED                              (0x19) */
/* RESERVED                              (0x1A) */
/* RESERVED                              (0x1B) */
/* RESERVED                              (0x1C) */
/* RESERVED                              (0x1D) */
/* RESERVED                              (0x1E) */
#define ETSEL5_EMIOS23                   (0x1F)



#if (TARGET_TYPE == MPC5554)
#define CFIFO_00_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_01_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_02_TRG            CFIFO_EMIOS_TRIGGER
#define CFIFO_03_TRG            CFIFO_EMIOS_TRIGGER
#define CFIFO_04_TRG            CFIFO_EMIOS_TRIGGER
#define CFIFO_05_TRG            CFIFO_EMIOS_TRIGGER
#elif (TARGET_TYPE == MPC5534)
#define CFIFO_00_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_01_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_02_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_03_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_04_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_05_TRG            CFIFO_ETPU_TRIGGER
#elif (TARGET_TYPE == MPC5633  || TARGET_TYPE == MPC5634)
#define CFIFO_00_TRG            CFIFO_ETSEL_TRIGGER
#define CFIFO_01_TRG            CFIFO_ETPU_TRIGGER
#define CFIFO_02_TRG            CFIFO_ETSEL_TRIGGER
#define CFIFO_03_TRG            CFIFO_ETSEL_TRIGGER
#define CFIFO_04_TRG            CFIFO_ETSEL_TRIGGER
#define CFIFO_05_TRG            CFIFO_EMIOS_TRIGGER
#define CFIFO_00_TRG_EXT        ETSEL0_PIT0
#define CFIFO_01_TRG_EXT        ETSEL1_ETRIG1
#define CFIFO_02_TRG_EXT        ETSEL2_PIT2
#define CFIFO_03_TRG_EXT        ETSEL3_PIT3
#define CFIFO_04_TRG_EXT        ETSEL4_ETRIG0
#define CFIFO_05_TRG_EXT        ETSEL5_ETRIG1

#else
#error ERROR: Target not supported!!
#endif



/* Internal Trigger for AD Converter */
#if (CFIFO_00_TRG == CFIFO_ETPU_TRIGGER)
#define ION_TRIGGER_A           ETPUA_UC30
#elif (CFIFO_00_TRG == CFIFO_EMIOS_TRIGGER)
#define ION_TRIGGER_A           EMIOS_UC10
#elif (CFIFO_00_TRG == CFIFO_ETSEL_TRIGGER)
#define ION_TRIGGER_A           (CFIFO_00_TRG_EXT-2) 
#endif

#if (CFIFO_01_TRG == CFIFO_ETPU_TRIGGER)
#define ANGULAR_TRIGGER         ETPUA_UC31
#elif (CFIFO_01_TRG == CFIFO_EMIOS_TRIGGER)
#define ANGULAR_TRIGGER         EMIOS_UC11
#elif (CFIFO_01_TRG == CFIFO_ETSEL_TRIGGER)
#define ANGULAR_TRIGGER         CFIFO_01_TRG_EXT
#endif

#if (CFIFO_02_TRG == CFIFO_ETPU_TRIGGER)
#define TRIGGER_450_MICROSEC    ETPUA_UC29
#elif (CFIFO_02_TRG == CFIFO_EMIOS_TRIGGER)
#define TRIGGER_450_MICROSEC    EMIOS_UC15
#elif (CFIFO_02_TRG == CFIFO_ETSEL_TRIGGER)
#define TRIGGER_450_MICROSEC    (CFIFO_02_TRG_EXT-2)
#endif

#if (CFIFO_03_TRG == CFIFO_ETPU_TRIGGER)
#define TRIGGER_2_MILLISEC      ETPUA_UC28
#elif (CFIFO_03_TRG == CFIFO_EMIOS_TRIGGER)
#define TRIGGER_2_MILLISEC      EMIOS_UC14
#elif (CFIFO_03_TRG == CFIFO_ETSEL_TRIGGER)
#define TRIGGER_2_MILLISEC      (CFIFO_03_TRG_EXT-2)
#endif

#if (CFIFO_04_TRG == CFIFO_ETPU_TRIGGER)
#define TRIGGER_10_MILLISEC     ETPUA_UC27
#elif (CFIFO_04_TRG == CFIFO_EMIOS_TRIGGER)
#define TRIGGER_10_MILLISEC     EMIOS_UC13
#elif (CFIFO_04_TRG == CFIFO_ETSEL_TRIGGER)
#define TRIGGER_10_MILLISEC    (CFIFO_04_TRG_EXT)
#endif

#if (CFIFO_05_TRG == CFIFO_ETPU_TRIGGER)
#elif (CFIFO_05_TRG == CFIFO_EMIOS_TRIGGER)
#endif

/* eQADC Channels pins */
#define AN_0    0
#define AN_1    1
#define AN_2    2
#define AN_3    3
#define AN_4    4
#define AN_5    5
#define AN_6    6
#define AN_7    7
#define AN_8    8
#define AN_9    9
#define AN_10   10
#define AN_11   11
#define AN_12   12
#define AN_13   13
#define AN_14   14
#define AN_15   15
#define AN_16   16
#define AN_17   17
#define AN_18   18
#define AN_19   19
#define AN_20   20
#define AN_21   21
#define AN_22   22
#define AN_23   23
#define AN_24   24
#define AN_25   25
#define AN_26   26
#define AN_27   27
#define AN_28   28
#define AN_29   29
#define AN_30   30
#define AN_31   31
#define AN_32   32
#define AN_33   33
#define AN_34   34
#define AN_35   35
#define AN_36   36
#define AN_37   37
#define AN_38   38
#define AN_39   39
#define AN_40   40  // VRH for AD converted
#define AN_41   41  // VRL for AD converted
#define AN_42   42  // VRH + VRL / 2
#define AN_43   43  // 75% of VRH for AD converted
#define AN_44   44  // 25% of VRH for AD converted
#define AN_45   45 // Bandgap reference voltage
#define AN_128  128 // Temp Sensor


#define ADC_TIMEOUT     50
#define ADC_TIMEOUT_SAMPLE_SWTRIG     200

/****************************************************************************
     Peripheral errors defines 
 ****************************************************************************/
#define ADC_QUEUE_FULL                   -5
#define ADC_CHANNEL_NOT_INITIALIZED      -6

