/*  File    : WatTempMgm.h
 *  Copyright 2005 Eldor Corporation
 */
#ifndef _WATTEMPMGM_H_
#define _WATTEMPMGM_H_

/** include files **/
#include "typedefs.h"

#if ((ENGINE_TYPE != MV_AGUSTA_3C_TDC_0_08) && (ENGINE_TYPE != MV_AGUSTA_4C) && (ENGINE_TYPE != MV_AGUSTA_3C_TDC_0_20) && (ENGINE_TYPE != MV_AGUSTA_3C_TDC_0_30) && (ENGINE_TYPE != MV_AGUSTA_4C_TDC_0_9))
#define   WATER_TEMP_PWM_CH       ETPUA_UC6
#endif

#define BKTWATDUTY_dim      8    // number of Twat. PWM duty cycle breakpoints
#define BKWTDIAGDUTY_dim     11    // number of Twat. PWM duty cycle breakpoints

void Init_WaterTempPWM(void);
void WaterTempPWM_Relay(void);
void WaterTempPWM_Diag(uint16_t currentDuty); 


#endif
