/*
 * File: LightOffMgm.c
 *
 * Code generated for Simulink model 'LightOffMgm'.
 *
 * Model version                  : 1.97
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jul 11 11:18:54 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Generic->32-bit Embedded Processor
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (29), Warnings (4), Error (0)
 */

#include "LightOffMgm.h"
#include "LightOffMgm_private.h"

/* Named constants for Chart: '<S4>/Control_Flow' */
#define LightOffMgm_IN_ENABLE          ((uint8_T)1U)
#define LightOffMgm_IN_LOFF_DISABLED   ((uint8_T)2U)
#define LightOffMgm_IN_LOFF_ENABLED    ((uint8_T)1U)
#define LightOffMgm_IN_LOFF_END        ((uint8_T)2U)
#define LightOffMgm_IN_LOFF_W_ENDSTART ((uint8_T)3U)
#define LightOff_IN_NO_ACTIVE_CHILD_ick ((uint8_T)0U)

/* user code (top of source file) */
/* System '<Root>/LightOffMgm' */
#ifdef _BUILD_LIGHTOFFMGM_

/* Exported data definition */

/* CCP test point */
/* Definition for custom storage class: CCP_CSC */
static CCPTEST uint16_T DeltaEffLOBase = 0U;/* '<Root>/_DataStoreBlk_9' */

/* Delta Efficiency base */
static CCPTEST uint16_T DeltaEffLOIdle = 0U;/* '<Root>/_DataStoreBlk_8' */

/* Delta Efficiency idle */
static CCPTEST uint8_T FlgStopSALOff = ((uint8_T)0U);/* '<Root>/_DataStoreBlk_6' */

/* Flg stop LOff */
static CCPTEST int16_T SALOffBase = 0; /* '<Root>/_DataStoreBlk_7' */

/* Lightoff offset on base SA */
static CCPTEST int16_T SALOffIdle = 0; /* '<Root>/_DataStoreBlk_1' */

/* Lightoff offset on idle SA */
static CCPTEST uint8_T StLightOff = ((uint8_T)0U);/* '<Root>/_DataStoreBlk_4' */

/* Light off state */

/* Block signals and states (default storage) */
D_Work_LightOffMgm_T LightOffMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_LightOffMgm_T LightOffMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_LightOffMgm_T LightOffMgm_U;

/* Definition for custom storage class: ExportToFile */
uint16_T DeltaEffLOff;

/* Delta Efficiency */
uint8_T FlgLOff;

/* EnLOff */
uint16_T GnSALoff;

/* Gain for modulating ligth off */
int16_T SALOff;

/* Lightoff offset on base SA */

/* Output and update for function-call system: '<S1>/NoSync' */
void LightOffMgm_NoSync(void)
{
  /* Chart: '<S2>/Reset_Variables' */
  /* Gateway: LightOffMgm/NoSync/Reset_Variables */
  /* During: LightOffMgm/NoSync/Reset_Variables */
  /* Entry Internal: LightOffMgm/NoSync/Reset_Variables */
  /* Transition: '<S8>:1' */
  SALOff = 0;
  DeltaEffLOff = 0U;
  FlgLOff = 0U;
  FlgStopSALOff = 1U;
  StLightOff = ((uint8_T)LOFF_NOSYNC);

  /* user code (Output function Trailer) */

  /* System '<S1>/NoSync' */
}

/* Output and update for function-call system: '<S1>/PowerOn' */
void LightOffMgm_PowerOn(void)
{
  /* Chart: '<S3>/Init_Variables' */
  /* Gateway: LightOffMgm/PowerOn/Init_Variables */
  /* During: LightOffMgm/PowerOn/Init_Variables */
  /* Entry Internal: LightOffMgm/PowerOn/Init_Variables */
  /* Transition: '<S9>:1' */
  SALOff = 0;
  SALOffBase = 0;
  SALOffIdle = 0;
  DeltaEffLOff = 0U;
  DeltaEffLOBase = 0U;
  DeltaEffLOIdle = 0U;
  FlgStopSALOff = 0U;
  FlgLOff = 0U;
  StLightOff = ((uint8_T)LOFF_DISABLED);

  /* user code (Output function Trailer) */

  /* System '<S1>/PowerOn' */
}

/* Output and update for function-call system: '<S4>/Calc_LOff_SA' */
void LightOffMgm_Calc_LOff_SA(void)
{
  /* local block i/o variables */
  int32_T rtb_RateLimiter_S32;
  int32_T rtb_RateLimiter_S32_iew;
  int16_T rtb_Look2D_S8_U16_S16;
  int16_T rtb_Look2D_S8_U16_S16_cmc;
  uint16_T rtb_Switch;
  int8_T rtb_Conversion1[5];
  int16_T rtb_LookUp_S8_S16;
  int32_T rtb_Switch1;
  int32_T i;

  /* S-Function (Look2D_S8_U16_S16): '<S20>/Look2D_S8_U16_S16' incorporates:
   *  Constant: '<S13>/BKTDCLOFF'
   *  Constant: '<S13>/BKTWATLOFF'
   *  Constant: '<S13>/TBGNSALOFF'
   *  Constant: '<S19>/BKTDCLOFF_dim'
   *  Constant: '<S19>/BKTWATLOFF_dim'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/TWaterCrk'
   */
  Look2D_S8_U16_S16( &rtb_Look2D_S8_U16_S16, &TBGNSALOFF[0], CntTdcCrk,
                    &BKTDCLOFF[0], ((uint8_T)BKTDCLOFF_dim), TWaterCrk,
                    &BKTWATLOFF[0], ((uint8_T)BKTWATLOFF_dim));

  /* DataTypeConversion: '<S19>/Data Type Conversion' */
  LightOffMgm_DWork.DataTypeConversion = rtb_Look2D_S8_U16_S16;

  /* MinMax: '<S13>/MinMax' incorporates:
   *  Constant: '<S13>/Constant'
   */
  if (LightOffMgm_DWork.DataTypeConversion > 0) {
    LightOffMgm_DWork.MinMax = (uint16_T)LightOffMgm_DWork.DataTypeConversion;
  } else {
    LightOffMgm_DWork.MinMax = 0U;
  }

  /* End of MinMax: '<S13>/MinMax' */

  /* If: '<S10>/If' incorporates:
   *  Constant: '<S12>/Constant'
   *  Constant: '<S14>/Constant'
   *  Inport: '<Root>/SelEffSABase'
   */
  if (SelEffSABase == 0) {
    /* Outputs for IfAction SubSystem: '<S10>/Calc_SALOff' incorporates:
     *  ActionPort: '<S14>/Action Port'
     */
    /* S-Function (LookUp_S8_S16): '<S22>/LookUp_S8_S16' incorporates:
     *  Constant: '<S14>/BKTWATLOFF'
     *  Constant: '<S14>/BKTWATLOFF_dim'
     *  Constant: '<S14>/VTSALOFFIDLE'
     *  Inport: '<Root>/TWaterCrk'
     */
    LookUp_S8_S16( &rtb_Look2D_S8_U16_S16_cmc, &VTSALOFFIDLE[0], TWaterCrk,
                  &BKTWATLOFF[0], ((uint8_T)BKTWATLOFF_dim));

    /* Product: '<S14>/Product1' incorporates:
     *  DataStoreWrite: '<S14>/Data Store1'
     */
    SALOffIdle = (int16_T)((rtb_Look2D_S8_U16_S16_cmc * LightOffMgm_DWork.MinMax)
      >> 19);

    /* S-Function (Look2D_S8_U16_S16): '<S24>/Look2D_S8_U16_S16' incorporates:
     *  Constant: '<S14>/BKCMELOFF'
     *  Constant: '<S14>/BKRPMLOFF'
     *  Constant: '<S14>/TBSALOFFBASE'
     *  Constant: '<S21>/BKLOADLOFF_dim'
     *  Constant: '<S21>/BKRPMLOFF_dim'
     *  Inport: '<Root>/CmeDriver'
     *  Inport: '<Root>/RpmF'
     */
    Look2D_S8_U16_S16( &rtb_Look2D_S8_U16_S16_cmc, &TBSALOFFBASE[0], RpmF,
                      &BKRPMLOFF[0], ((uint8_T)BKRPMLOFF_dim), CmeDriver,
                      &BKCMELOFF[0], ((uint8_T)BKCMELOFF_dim));

    /* Product: '<S14>/Product' incorporates:
     *  DataStoreWrite: '<S14>/Data Store4'
     *  DataTypeConversion: '<S21>/Data Type Conversion'
     */
    SALOffBase = (int16_T)((LightOffMgm_DWork.MinMax *
      (rtb_Look2D_S8_U16_S16_cmc >> 5)) >> 14);

    /* Switch: '<S14>/Switch' incorporates:
     *  Inport: '<Root>/FlgSAIdleSel'
     *  Inport: '<Root>/IdleFlg'
     *  Logic: '<S14>/Logical Operator'
     */
    if ((IdleFlg != 0) || (FlgSAIdleSel != 0)) {
      /* DataTypeConversion: '<S14>/Data Type Conversion3' incorporates:
       *  DataStoreWrite: '<S14>/Data Store1'
       */
      i = (SALOffIdle << 10);
    } else {
      /* DataTypeConversion: '<S14>/Data Type Conversion3' incorporates:
       *  DataStoreWrite: '<S14>/Data Store4'
       */
      i = (SALOffBase << 10);
    }

    /* End of Switch: '<S14>/Switch' */

    /* Memory: '<S14>/Memory' */
    rtb_Switch1 = LightOffMgm_DWork.Memory_PreviousInput_ewq;

    /* Switch: '<S14>/Switch1' incorporates:
     *  DataStoreRead: '<S14>/Data Store Read1'
     */
    if (LightOffMgm_DWork.reset != 0) {
      rtb_Switch1 = (SALOff << 10);
    }

    /* End of Switch: '<S14>/Switch1' */

    /* S-Function (RateLimiter_S32): '<S23>/RateLimiter_S32' incorporates:
     *  Constant: '<S14>/RATEMAXLOFF'
     *  Constant: '<S14>/RATEMINLOFF'
     */
    RateLimiter_S32( &rtb_RateLimiter_S32_iew, i, rtb_Switch1, RATEMINLOFF,
                    RATEMAXLOFF);

    /* DataTypeConversion: '<S14>/Data Type Conversion2' */
    LightOffMgm_DWork.Merge = (int16_T)(rtb_RateLimiter_S32_iew >> 10);

    /* Switch: '<S14>/Switch2' */
    LightOffMgm_DWork.Merge2 = (uint8_T)(rtb_RateLimiter_S32_iew != 0);
    LightOffMgm_DWork.Merge1 = 0U;

    /* Update for Memory: '<S14>/Memory' incorporates:
     *  Constant: '<S14>/Constant'
     */
    LightOffMgm_DWork.Memory_PreviousInput_ewq = rtb_RateLimiter_S32_iew;

    /* End of Outputs for SubSystem: '<S10>/Calc_SALOff' */
  } else {
    /* Outputs for IfAction SubSystem: '<S10>/Calc_DeltaEffLOBase' incorporates:
     *  ActionPort: '<S12>/Action Port'
     */
    /* S-Function (Look2D_U8_U16_S16): '<S15>/Look2D_U8_U16_S16' incorporates:
     *  Constant: '<S12>/BKCMELOFF'
     *  Constant: '<S12>/BKCMELOFF_dim'
     *  Constant: '<S12>/BKRPMLOFF'
     *  Constant: '<S12>/BKRPMLOFF_dim'
     *  Constant: '<S12>/TBDEFFLOFFBASE'
     *  Inport: '<Root>/CmeDriver'
     *  Inport: '<Root>/RpmF'
     */
    Look2D_U8_U16_S16( &rtb_Switch, &TBDEFFLOFFBASE[0], RpmF, &BKRPMLOFF[0],
                      ((uint8_T)BKRPMLOFF_dim), CmeDriver, &BKCMELOFF[0],
                      ((uint8_T)BKCMELOFF_dim));

    /* DataTypeConversion: '<S12>/Data Type Conversion' */
    rtb_LookUp_S8_S16 = (int16_T)(((uint32_T)rtb_Switch) >> 2);

    /* Product: '<S12>/Product1' incorporates:
     *  DataStoreWrite: '<S12>/Data Store1'
     */
    DeltaEffLOBase = (uint16_T)((rtb_LookUp_S8_S16 * LightOffMgm_DWork.MinMax) >>
      14);

    /* DataTypeConversion: '<S16>/Conversion1' incorporates:
     *  Constant: '<S12>/VTDEFFLOFFIDLE'
     */
    for (i = 0; i < 5; i++) {
      rtb_Conversion1[i] = (int8_T)VTDEFFLOFFIDLE[i];
    }

    /* End of DataTypeConversion: '<S16>/Conversion1' */

    /* S-Function (LookUp_S8_S16): '<S16>/LookUp_S8_S16' incorporates:
     *  Constant: '<S12>/BKTWATLOFF'
     *  Constant: '<S12>/BKTWATLOFF_dim'
     *  Inport: '<Root>/TWaterCrk'
     */
    LookUp_S8_S16( &rtb_LookUp_S8_S16, &rtb_Conversion1[0], TWaterCrk,
                  &BKTWATLOFF[0], ((uint8_T)BKTWATLOFF_dim));

    /* Product: '<S12>/Product' incorporates:
     *  DataStoreWrite: '<S12>/Data Store4'
     *  DataTypeConversion: '<S12>/Data Type Conversion1'
     */
    DeltaEffLOIdle = (uint16_T)(((rtb_LookUp_S8_S16 >> 1) *
      LightOffMgm_DWork.MinMax) >> 14);
    LightOffMgm_DWork.Merge = 0;

    /* Switch: '<S12>/Switch' incorporates:
     *  Constant: '<S12>/Constant'
     *  DataStoreWrite: '<S12>/Data Store1'
     *  DataStoreWrite: '<S12>/Data Store4'
     *  Inport: '<Root>/FlgSAIdleSel'
     *  Inport: '<Root>/IdleFlg'
     *  Logic: '<S12>/Logical Operator'
     */
    if ((IdleFlg != 0) || (FlgSAIdleSel != 0)) {
      rtb_Switch = DeltaEffLOIdle;
    } else {
      rtb_Switch = DeltaEffLOBase;
    }

    /* End of Switch: '<S12>/Switch' */

    /* DataTypeConversion: '<S12>/Data Type Conversion3' */
    i = rtb_Switch;

    /* Memory: '<S12>/Memory' */
    rtb_Switch1 = LightOffMgm_DWork.Memory_PreviousInput;

    /* Switch: '<S12>/Switch1' incorporates:
     *  DataStoreRead: '<S12>/Data Store Read1'
     */
    if (LightOffMgm_DWork.reset != 0) {
      rtb_Switch1 = DeltaEffLOff;
    }

    /* End of Switch: '<S12>/Switch1' */

    /* S-Function (RateLimiter_S32): '<S17>/RateLimiter_S32' incorporates:
     *  Constant: '<S12>/RATEMAXLOFF'
     *  Constant: '<S12>/RATEMINLOFF'
     */
    RateLimiter_S32( &rtb_RateLimiter_S32, i, rtb_Switch1, RATEMINLOFF,
                    RATEMAXLOFF);

    /* DataTypeConversion: '<S12>/Data Type Conversion2' */
    LightOffMgm_DWork.Merge1 = (uint16_T)rtb_RateLimiter_S32;

    /* Switch: '<S12>/Switch2' */
    LightOffMgm_DWork.Merge2 = (uint8_T)(rtb_RateLimiter_S32 != 0);

    /* Update for Memory: '<S12>/Memory' */
    LightOffMgm_DWork.Memory_PreviousInput = rtb_RateLimiter_S32;

    /* End of Outputs for SubSystem: '<S10>/Calc_DeltaEffLOBase' */
  }

  /* End of If: '<S10>/If' */
}

/* System initialize for atomic system: '<S4>/Control_Flow' */
void LightOffMgm_Control_Flow_Init(void)
{
  LightOffMgm_DWork.is_ENABLE = LightOff_IN_NO_ACTIVE_CHILD_ick;
  LightOffMgm_DWork.is_active_c7_LightOffMgm = 0U;
  LightOffMgm_DWork.is_c7_LightOffMgm = LightOff_IN_NO_ACTIVE_CHILD_ick;
  LightOffMgm_DWork.Merge = 0;
  LightOffMgm_DWork.Merge1 = 0U;
  LightOffMgm_DWork.Merge2 = 0U;
  LightOffMgm_DWork.reset = 0U;
}

/* Output and update for atomic system: '<S4>/Control_Flow' */
void LightOffMgm_Control_Flow(void)
{
  /* Chart: '<S4>/Control_Flow' incorporates:
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/FlgLOFEOL'
   *  Inport: '<Root>/PresAtm'
   *  Inport: '<Root>/TrqStartFlg'
   */
  /* Gateway: LightOffMgm/PreHTDC/Control_Flow */
  /* During: LightOffMgm/PreHTDC/Control_Flow */
  if (LightOffMgm_DWork.is_active_c7_LightOffMgm == 0U) {
    /* Entry: LightOffMgm/PreHTDC/Control_Flow */
    LightOffMgm_DWork.is_active_c7_LightOffMgm = 1U;

    /* Entry Internal: LightOffMgm/PreHTDC/Control_Flow */
    /* Transition: '<S11>:47' */
    LightOffMgm_DWork.is_c7_LightOffMgm = LightOffMgm_IN_LOFF_DISABLED;

    /* Entry 'LOFF_DISABLED': '<S11>:1' */
    LightOffMgm_DWork.Merge = 0;
    LightOffMgm_DWork.Merge1 = 0U;
    LightOffMgm_DWork.Merge2 = 0U;
    StLightOff = ((uint8_T)LOFF_DISABLED);
  } else if (LightOffMgm_DWork.is_c7_LightOffMgm == LightOffMgm_IN_ENABLE) {
    /* During 'ENABLE': '<S11>:48' */
    /*  Strategy Disabled */
    if ((ENLIGHTOFF == 0) || (FlgLOFEOL != 0)) {
      /* Transition: '<S11>:7' */
      /* Exit Internal 'ENABLE': '<S11>:48' */
      LightOffMgm_DWork.is_ENABLE = LightOff_IN_NO_ACTIVE_CHILD_ick;
      LightOffMgm_DWork.is_c7_LightOffMgm = LightOffMgm_IN_LOFF_DISABLED;

      /* Entry 'LOFF_DISABLED': '<S11>:1' */
      LightOffMgm_DWork.Merge = 0;
      LightOffMgm_DWork.Merge1 = 0U;
      LightOffMgm_DWork.Merge2 = 0U;
      StLightOff = ((uint8_T)LOFF_DISABLED);
    } else {
      switch (LightOffMgm_DWork.is_ENABLE) {
       case LightOffMgm_IN_LOFF_ENABLED:
        /* During 'LOFF_ENABLED': '<S11>:4' */
        if (FlgStopSALOff != 0) {
          /* Transition: '<S11>:55' */
          LightOffMgm_DWork.is_ENABLE = LightOffMgm_IN_LOFF_W_ENDSTART;

          /* Entry 'LOFF_W_ENDSTART': '<S11>:5' */
          LightOffMgm_DWork.Merge = 0;
          LightOffMgm_DWork.Merge1 = 0U;
          LightOffMgm_DWork.Merge2 = 0U;
          FlgStopSALOff = 0U;
          StLightOff = ((uint8_T)LOFF_W_ENDSTART);
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/Calc_LOff_SA' */
          /* Transition: '<S11>:16' */
          /* Event: '<S11>:44' */
          LightOffMgm_Calc_LOff_SA();

          /* End of Outputs for SubSystem: '<S4>/Calc_LOff_SA' */
          LightOffMgm_DWork.reset = 0U;
          if (LightOffMgm_DWork.DataTypeConversion <= 0) {
            /* Transition: '<S11>:13' */
            LightOffMgm_DWork.is_ENABLE = LightOffMgm_IN_LOFF_END;

            /* Entry 'LOFF_END': '<S11>:3' */
            LightOffMgm_DWork.Merge = 0;
            LightOffMgm_DWork.Merge1 = 0U;
            LightOffMgm_DWork.Merge2 = 0U;
            StLightOff = ((uint8_T)LOFF_END);
          } else {
            /* Transition: '<S11>:15' */
          }
        }
        break;

       case LightOffMgm_IN_LOFF_END:
        LightOffMgm_DWork.Merge = 0;
        LightOffMgm_DWork.Merge1 = 0U;
        LightOffMgm_DWork.Merge2 = 0U;

        /* During 'LOFF_END': '<S11>:3' */
        break;

       default:
        LightOffMgm_DWork.Merge = 0;
        LightOffMgm_DWork.Merge1 = 0U;
        LightOffMgm_DWork.Merge2 = 0U;

        /* During 'LOFF_W_ENDSTART': '<S11>:5' */
        if ((EndStartFlg != 0) && (TrqStartFlg != 0)) {
          /* Transition: '<S11>:14' */
          LightOffMgm_DWork.reset = 1U;
          LightOffMgm_DWork.is_ENABLE = LightOffMgm_IN_LOFF_ENABLED;

          /* Entry 'LOFF_ENABLED': '<S11>:4' */
          StLightOff = ((uint8_T)LOFF_ENABLED);
        }
        break;
      }
    }
  } else {
    LightOffMgm_DWork.Merge = 0;
    LightOffMgm_DWork.Merge1 = 0U;
    LightOffMgm_DWork.Merge2 = 0U;

    /* During 'LOFF_DISABLED': '<S11>:1' */
    /*  Strategy Enabled */
    if (((ENLIGHTOFF != 0) && (PresAtm > THPATMLOFF)) && (FlgLOFEOL == 0)) {
      /* Transition: '<S11>:9' */
      LightOffMgm_DWork.is_c7_LightOffMgm = LightOffMgm_IN_ENABLE;
      LightOffMgm_DWork.is_ENABLE = LightOffMgm_IN_LOFF_W_ENDSTART;

      /* Entry 'LOFF_W_ENDSTART': '<S11>:5' */
      LightOffMgm_DWork.Merge = 0;
      LightOffMgm_DWork.Merge1 = 0U;
      LightOffMgm_DWork.Merge2 = 0U;
      FlgStopSALOff = 0U;
      StLightOff = ((uint8_T)LOFF_W_ENDSTART);
    }
  }

  /* End of Chart: '<S4>/Control_Flow' */
}

/* System initialize for function-call system: '<S1>/PreHTDC' */
void LightOffMgm_PreHTDC_Init(void)
{
  /* SystemInitialize for Chart: '<S4>/Control_Flow' */
  LightOffMgm_Control_Flow_Init();
}

/* Output and update for function-call system: '<S1>/PreHTDC' */
void LightOffMgm_PreHTDC(void)
{
  /* Chart: '<S4>/Control_Flow' */
  LightOffMgm_Control_Flow();

  /* DataStoreWrite: '<S4>/Data Store1' */
  DeltaEffLOff = LightOffMgm_DWork.Merge1;

  /* DataStoreWrite: '<S4>/Data Store2' */
  SALOff = LightOffMgm_DWork.Merge;

  /* DataStoreWrite: '<S4>/Data Store3' */
  GnSALoff = LightOffMgm_DWork.MinMax;

  /* DataStoreWrite: '<S4>/Data Store4' */
  FlgLOff = LightOffMgm_DWork.Merge2;
}

/* Model step function */
void LightOffMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/LightOffMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_PowerOn' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((LightOffMgm_U.ev_PowerOn > 0) &&
      (LightOffMgm_PrevZCSigState.fc_PowerOn_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PowerOn'
     */
    LightOffMgm_PowerOn();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  LightOffMgm_PrevZCSigState.fc_PowerOn_Trig_ZCE = (ZCSigState)
    (LightOffMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/fc_PowerOn' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_NoSync' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_NoSync' */
  if ((LightOffMgm_U.ev_NoSync > 0) &&
      (LightOffMgm_PrevZCSigState.fc_NoSync_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/NoSync'
     */
    LightOffMgm_NoSync();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  LightOffMgm_PrevZCSigState.fc_NoSync_Trig_ZCE = (ZCSigState)
    (LightOffMgm_U.ev_NoSync > 0);

  /* End of Inport: '<Root>/ev_NoSync' */
  /* End of Outputs for SubSystem: '<S1>/fc_NoSync' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_PreHTDC' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PreHTDC' */
  if ((LightOffMgm_U.ev_PreTDC > 0) &&
      (LightOffMgm_PrevZCSigState.fc_PreHTDC_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PreHTDC'
     */
    LightOffMgm_PreHTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  LightOffMgm_PrevZCSigState.fc_PreHTDC_Trig_ZCE = (ZCSigState)
    (LightOffMgm_U.ev_PreTDC > 0);

  /* End of Inport: '<Root>/ev_PreHTDC' */
  /* End of Outputs for SubSystem: '<S1>/fc_PreHTDC' */

  /* End of Outputs for SubSystem: '<Root>/LightOffMgm' */
}

/* Model initialize function */
void LightOffMgm_initialize(void)
{
  /* Registration code */

  /* states (dwork) */
  (void) memset((void *)&LightOffMgm_DWork, 0,
                sizeof(D_Work_LightOffMgm_T));

  /* custom states */
  SALOff = 0;
  DeltaEffLOff = 0U;
  GnSALoff = 0U;
  FlgLOff = 0U;

  /* external inputs */
  (void)memset(&LightOffMgm_U, 0, sizeof(ExternalInputs_LightOffMgm_T));
  LightOffMgm_PrevZCSigState.fc_NoSync_Trig_ZCE = POS_ZCSIG;
  LightOffMgm_PrevZCSigState.fc_PowerOn_Trig_ZCE = POS_ZCSIG;
  LightOffMgm_PrevZCSigState.fc_PreHTDC_Trig_ZCE = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/LightOffMgm' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/fc_PreHTDC' */
  /* SystemInitialize for S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/PreHTDC'
   */
  LightOffMgm_PreHTDC_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  /* End of SystemInitialize for SubSystem: '<S1>/fc_PreHTDC' */

  /* End of SystemInitialize for SubSystem: '<Root>/LightOffMgm' */
}

/* user code (bottom of source file) */
/* System '<Root>/LightOffMgm' */
void LightOffMgm_Init(void)
{
  LightOffMgm_initialize();
  LightOffMgm_PowerOn();
}

#else                                  // _BUILD_LIGHTOFFMGM_

void LightOffMgm_Init(void);
void LightOffMgm_NoSync(void);
void LightOffMgm_PreHTDC(void);

//uscite
int16_T SALOffBase;
int16_T SALOffIdle;
void LightOffMgm_Init(void)
{
  SALOffBase= 0;
  SALOffIdle= 0;
}

void LightOffMgm_NoSync(void)
{
}

void LightOffMgm_PreHTDC(void)
{
}

#endif                                 // _BUILD_LIGHTOFFMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
