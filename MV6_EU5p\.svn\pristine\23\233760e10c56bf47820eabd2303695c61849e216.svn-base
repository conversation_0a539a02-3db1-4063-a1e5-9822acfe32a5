/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "steppercmd.h"
#include "hbridge.h"
#include "mathlib.h"
#include "diagmgm_out.h"

#ifdef  <PERSON>_MEX_FILE
#define _BUILD_STEPPERCMD_
#endif

#ifdef _BUILD_STEPPERCMD_

// Constant Parameters
#if (N_STEP == 8)
// *** STEPPER CONFIGURATIONS - HALF STEPS ****      A  D  B  C Riferito allo Stepper
const uint8_T STP_CMD_TABLE[N_STEP][N_STP_CMD] = {
                                                    {1, 0, 1, 0},  
                                                    {1, 0, 1, 1},  
                                                    {1, 0, 0, 1},  
                                                    {1, 1, 0, 1},  
                                                    {0, 1, 0, 1},  
                                                    {0, 1, 0, 0},  
                                                    {0, 1, 1, 0},  
                                                    {0, 0, 1, 0}  
                                                };
#warning STEPPER CONFIGURATIONS - HALF STEPS - (N_STEP = 8)
#elif (N_STEP == 4)
// *** STEPPER CONFIGURATIONS - FULL STEPS ****      A  D  B  C Riferito allo Stepper
const uint8_T STP_CMD_TABLE[N_STEP][N_STP_CMD] = {
                                                    {1, 0, 1, 0},  
                                                    {1, 0, 0, 1},  
                                                    {0, 1, 0, 1},  
                                                    {0, 1, 1, 0}  
                                                };
#else
#error Wrong configuration!
#endif

// Calibration Parameters
extern uint8_T  STPTASKCNT;
extern uint8_T  STPTASKCNTRST;
extern uint16_T NSTPRESET;
extern uint16_T NSTEPMAX;
extern uint16_T VBATSTP;
extern uint8_T  VBATSTPDEL;
extern uint8_T  STPINVDELAY;
   
// Output Variables
typStStepper StStepper;
uint16_T StepperPos;
uint16_T StpPosObj;
uint16_T StpPos;
uint8_T  StpAbsPos;
uint8_T  StepperCmd[N_STP_CMD];
uint8_T  FlgSteperRdy;
uint8_T  FlgStpOk;

// Local Variables
uint8_T  TaskCnt;
uint16_T StpWaitCnt;
uint32_T ResetTaskCnt;

// Functions
void fc_StpPosObj(void);
void ev_StpCmd(void);
void fc_StepperPos(void);
void fc_StepperCmd(void);
void StepperCmd_Diag(void);

void StepperCmd_Init(void)
{   
    StepperPos = 0;
    StpPosObj = 0;
    StStepper = STP_RESET;
    FlgSteperRdy = 0;
    StpPos = NSTPRESET;
    fc_StepperPos();
    StpAbsPos = StpPos % N_STEP;    
    TaskCnt = 0;
    StpWaitCnt = 0;
    ResetTaskCnt = (2 * (uint32_T)NSTPRESET) * (uint32_T)(STPTASKCNTRST + 1);
    FlgStpOk = 1;

    fc_StepperCmd();

  // Initialize HBridge
  Hbridge_Init(/*Bridge_A*/ HB_STEP_A,HB_DIGIO_MODE);
  Hbridge_Init(/*Bridge_B*/ HB_STEP_B,HB_DIGIO_MODE);
  // Enable HBridge  
  Hbridge_SetState(HB_STEP_A,1);
  Hbridge_SetState(HB_STEP_B,1);
}

void StepperCmd_NoSync(void)
{
//  StStepper = STP_RESET;
//  StpPos += N_STEP;
//  fc_StepperPos();
//  StpAbsPos = StpPos % N_STEP;
//
//  fc_StepperCmd();
}

void StepperCmd_T5ms(void)
{
    uint8_T MaxTaskCnt;
    
    TaskCnt++;
    
    if(StStepper == STP_RESET)
        MaxTaskCnt =  STPTASKCNTRST;
    else
        MaxTaskCnt =  STPTASKCNT;

    if(TaskCnt > MaxTaskCnt)    
    {
        TaskCnt = 0;
        
        // Faccio la diagnosi prima per non perdere passi
        StepperCmd_Diag();      

        // Calcolo dell'obiettivo
        fc_StpPosObj();
        
        // Automa per pilotaggio stepper
        ev_StpCmd();
        
        // Attuazione comando stepper
        fc_StepperCmd();
        
    }   
}

void fc_StpPosObj(void)
{
    StpPosObj = (uint32_T)(NSTEPMAX * StepperObj) >> 15;
}

void fc_StepperPos(void)
{
    if (StpPos < NSTEPMAX)
        StepperPos = ((((uint32_T)StpPos) <<15) / NSTEPMAX);
    else
        StepperPos = (1<<15);
}

void ev_StpCmd(void)
{
    switch (StStepper)
    {
        case STP_RESET:
            if((VBattery < VBATSTP) || !FlgStpOk)
            {
                StpWaitCnt = VBATSTPDEL;
            }
            else
            {
                if (StpWaitCnt > 0)
                {
                    StpWaitCnt--;
                }
                else
                {
                    if (StpPos == 0)
                    {
                        StStepper = STP_STOP;
                        StpWaitCnt = 0;
                    }
                    else
                    {
                        StpPos--;
                        fc_StepperPos();
                        StpAbsPos = StpPos % N_STEP;
                    }
                }               
            }
        break;
        
        case STP_STOP:
            if((VBattery < VBATSTP) || !FlgStpOk)
            {
                StpWaitCnt = VBATSTPDEL;
                StStepper = STP_PAUSE;
            }
            else 
            {
                if (StpWaitCnt == 0) 
                {
                    if (StpPosObj > StpPos)
                    {
                        StStepper= STP_UP;
                    }
                    else if (StpPosObj < StpPos)
                    {
                        StStepper= STP_DOWN;
                    }                   
                }
                else
                {
                    StpWaitCnt--;
                }
            }

        break;
        
        case STP_DOWN:
            if((VBattery < VBATSTP) || !FlgStpOk)
            {
                StpWaitCnt = VBATSTPDEL;
                StStepper = STP_PAUSE;
            }
            else if (StpPosObj >= StpPos)
            {
                StpWaitCnt = STPINVDELAY;
                StStepper = STP_STOP;
            }
            else
            {
                StpPos--;
                fc_StepperPos();
                StpAbsPos = StpPos % N_STEP;
            }
        break;
        
        case STP_UP:
            if((VBattery < VBATSTP) || !FlgStpOk)
            {
                StpWaitCnt = VBATSTPDEL;
                StStepper = STP_PAUSE;
            }
            else if (StpPosObj <= StpPos)
            {
                StpWaitCnt = STPINVDELAY;
                StStepper = STP_STOP;
            }
            else
            {
                StpPos++;
                fc_StepperPos();
                StpAbsPos = StpPos % N_STEP;
            }
        break;
        
        case STP_PAUSE:             
            if((VBattery > VBATSTP) && FlgStpOk)
            {
                StStepper = STP_STOP;
                StpWaitCnt = VBATSTPDEL;
            }
        break;
        
        default:
            StStepper = STP_RESET;
        break;
    }
    
    if(FlgSteperRdy == 0)
    {
        ResetTaskCnt--;
    
        if((ResetTaskCnt == 0) || (StStepper != STP_RESET))
            FlgSteperRdy = 1;
    }       
}

void fc_StepperCmd(void)
{
    uint8_T i;

    for(i=0;i<N_STP_CMD;i++)
    {
#ifdef STP_STOP_OFF     
        if(StStepper == STP_STOP) 
        {    
            StepperCmd[i] = 0;
        } 
        else
#endif
        {    
            StepperCmd[i] = STP_CMD_TABLE[StpAbsPos][i];
        }
    }
        
    // Attuazione del comando
    Hbridge_OutCmdSet(HB_STEP_A_IN1, StepperCmd[0]);
    Hbridge_OutCmdSet(HB_STEP_A_IN2, StepperCmd[1]);    
    Hbridge_OutCmdSet(HB_STEP_B_IN1, StepperCmd[2]);    
    Hbridge_OutCmdSet(HB_STEP_B_IN2, StepperCmd[3]);    
 }

// Stepper Motor Diagnosys
void StepperCmd_Diag(void) 
{
  uint8_T  StDiagHBridge_A = NO_FAULT;
  uint8_T  StDiagHBridge_B = NO_FAULT;
  
#ifdef DIAG_HBRIDGE_A
  DiagMgm_GetDiagState(DIAG_HBRIDGE_A, &StDiagHBridge_A);
#endif
#ifdef DIAG_HBRIDGE_B
  DiagMgm_GetDiagState(DIAG_HBRIDGE_B, &StDiagHBridge_B);
#endif  
    
  // In caso di diagnosi blocco lo stepper fino a quando non torna ok  
  if((StDiagHBridge_A == NO_FAULT) && (StDiagHBridge_B == NO_FAULT))
  {
    FlgStpOk = 1;
  } 
  else 
  {
    FlgStpOk = 0;
  }
}

#else // _BUILD_STEPPERCMD_

uint8_T  FlgSteperRdy;

void StepperCmd_Init(void)
{
  FlgSteperRdy = 1; 
}

#endif // _BUILD_STEPPERCMD_
