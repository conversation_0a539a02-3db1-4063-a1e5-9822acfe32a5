/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Application/THROTTLEMODEL/main_trunk/ThrottleModel_ert_rtw/ThrottleModel_types.h $ */
/* $Description:  $ */
/* $Revision: 5834 $ */
/* $Date: 2014-07-15 18:19:18 +0200 (mar, 15 lug 2014) $ */
/* $Author: LanaL $ */
/*****************************************************************************************************************/
/*
 * File: ThrottleModel_types.h
 *
 * Real-Time Workshop code generated for Simulink model ThrottleModel.
 *
 * Model version                        : 1.686
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Tue Jul 15 18:16:27 2014
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Tue Jul 15 18:16:28 2014
 */
#ifndef RTW_HEADER_ThrottleModel_types_h_
#define RTW_HEADER_ThrottleModel_types_h_
#include "rtwtypes.h"
#endif                                 /* RTW_HEADER_ThrottleModel_types_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
