/*
 * File: TrqDriver_data.c
 *
 * Code generated for Simulink model 'TrqDriver'.
 *
 * Model version                  : 1.2235
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Aug  5 15:33:05 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#include "TrqDriver.h"
#include "TrqDriver_private.h"

/*
 * Invariant block signals and block parameters
 * for system '<S1>/T10ms'
 */
const rtC_T10ms_TrqDriver
  TrqDriver_T10ms_C = {
  0                                    /* '<S19>/Data Type Conversion1' */
};

/*
 * Invariant block signals and block parameters
 * for system '<S1>/Init'
 */
const rtC_Init_TrqDriver
  TrqDriver_Init_C = {
  0U                                   /* '<S2>/Data Type Conversion2' */
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
