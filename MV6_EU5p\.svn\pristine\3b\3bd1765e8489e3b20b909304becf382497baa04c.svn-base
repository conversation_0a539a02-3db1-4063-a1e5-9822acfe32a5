/*
 * File: CreepLimiterMgm.c
 *
 * Code generated for Simulink model 'CreepLimiterMgm'.
 *
 * Model version                  : 1.96
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Fri Oct  4 09:09:50 2019
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Generic->32-bit Embedded Processor
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#include "CreepLimiterMgm.h"
#include "CreepLimiterMgm_private.h"
#include "mul_u32_loSR_sat.h"

/* Named constants for Chart: '<S4>/CreepEnMachine' */
#define CreepLimiterMgm_IN_ST_CREEPING ((uint8_T)1U)
#define CreepLimiterMgm_IN_ST_FREE     ((uint8_T)2U)
#define CreepLimiterMgm_IN_ST_INIT     ((uint8_T)3U)
#define CreepLimiterMgm_IN_ST_RETURN   ((uint8_T)4U)
#define CreepLimiter_IN_NO_ACTIVE_CHILD ((uint8_T)0U)

/* user code (top of source file) */
/* System '<Root>/CreepLimiterMgm' */
#ifdef _BUILD_CREEPLIMITERMGM_

extern uint8_T StDiag[];

/* Block signals and states (auto storage) */
D_Work_CreepLimiterMgm_T CreepLimiterMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_CreepLimiterM_T CreepLimiterMgm_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_CreepLimiterMg_T CreepLimiterMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T CmiCreepLimI;                  /* Cmi Creep limiter I */
int32_T CmiCreepLimIHiR;               /* Cmi Creep control */
int16_T CmiCreepLimP;                  /* Cmi Creep limiter P */
int32_T CmiCreepLimPHiR;               /* Cmi Creep control */
int32_T CreepLimDProp;                 /* Cmi Creep control */
int32_T CreepLimInt;                   /* Cmi Creep limiter Int */
int32_T CreepLimProp;                  /* Cmi Creep limiter Prop */
uint8_T FlgEnCreepLimProp;             /* En CreepLimProp */
int16_T OldCmiCreepLimI;               /* Cmi Creep limiter I */
int16_T OldCmiCreepLimP;               /* Cmi Creep limiter P */
uint8_T StCreepLim;                    /* Creep limiter status */
int16_T VehSpdCreepErr;                /* Veh speed creep error */
uint16_T VehSpdCreepTrg;               /* Veh speed creep target */

/* Output and update for function-call system: '<S1>/Tdc' */
void CreepLimiterMgm_Tdc(void)
{
  /* user code (Output function Trailer) */

  /* System '<S1>/Tdc' */
  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/Init' */
void CreepLimiterMgm_Init(void)
{
  {
    /* user code (Output function Header) */

    /* System '<S1>/Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    CreepLimiterMgm_initialize();

    /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
     *  Constant: '<S2>/ZERO'
     */
    CmiCreepLimI = 0;

    /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
     *  Constant: '<S2>/ZERO2'
     */
    OldCmiCreepLimI = 0;

    /* DataStoreWrite: '<S2>/Data Store Write3' incorporates:
     *  Constant: '<S2>/ZERO3'
     */
    OldCmiCreepLimP = 0;

    /* DataStoreWrite: '<S2>/Data Store Write8' incorporates:
     *  Constant: '<S2>/ZERO1'
     */
    CmiCreepLimP = 0;

    /* user code (Output function Trailer) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Output and update for function-call system: '<S4>/No_limiter' */
void CreepLimiterMgm_No_limiter(int16_T rtu_CmiIdleP, int16_T rtu_CmiIdleI,
  rtDW_No_limiter_CreepLimiterM_T *localDW)
{
  /* DataTypeConversion: '<S11>/Data Type Conversion' */
  localDW->DataTypeConversion = (rtu_CmiIdleP << 11);

  /* DataStoreWrite: '<S11>/Data Store Write2' */
  CmiCreepLimPHiR = localDW->DataTypeConversion;

  /* DataStoreWrite: '<S11>/Data Store Write8' */
  CmiCreepLimP = rtu_CmiIdleP;

  /* DataStoreWrite: '<S11>/Data Store Write1' */
  CmiCreepLimI = rtu_CmiIdleI;

  /* DataTypeConversion: '<S11>/Data Type Conversion1' */
  localDW->DataTypeConversion1 = (rtu_CmiIdleI << 11);

  /* DataStoreWrite: '<S11>/Data Store Write3' */
  CmiCreepLimIHiR = localDW->DataTypeConversion1;
}

/* Output and update for function-call system: '<S4>/PI_Limiter' */
void CreepLimiterMgm_PI_Limiter(uint16_T rtu_VehSpeedRear, int16_T rtu_CmiIdleP,
  uint8_T rtu_GearPos, int16_T rtu_CmiIdleI, int16_T rtu_CmiTargetPMin, int16_T
  rtu_CmfP, const uint16_T rtu_GearRatio[7], uint16_T rtu_RearWheelRadiusNc,
  rtDW_PI_Limiter_CreepLimiterM_T *localDW)
{
  int32_T minV;
  int16_T minV_0;
  int32_T tmp;
  int16_T tmp_0;
  uint32_T tmp_1;

  /* Selector: '<S15>/Selector' incorporates:
   *  Constant: '<S15>/VTMAXCMECREEPSAT'
   */
  localDW->Selector = VTMAXCMECREEPSAT[rtu_GearPos];

  /* Sum: '<S15>/Add' */
  localDW->Add = (rtu_CmfP << 11) + localDW->Selector;

  /* DataTypeConversion: '<S15>/Data Type Conversion4' */
  localDW->DataTypeConversion4 = (rtu_CmiIdleI << 11);

  /* MinMax: '<S15>/MinMax' */
  minV = localDW->Add;
  tmp = localDW->DataTypeConversion4;
  if (minV < tmp) {
  } else {
    minV = tmp;
  }

  localDW->MinMax = minV;

  /* End of MinMax: '<S15>/MinMax' */

  /* Product: '<S25>/Product' incorporates:
   *  Constant: '<S25>/RPMCREEPTRG'
   */
  localDW->Product = ((((uint32_T)RPMCREEPTRG) * rtu_RearWheelRadiusNc) >> 4);

  /* Selector: '<S25>/Selector' */
  localDW->Selector_gl0 = rtu_GearRatio[rtu_GearPos];

  /* Product: '<S25>/Product1' */
  localDW->Product1 = mul_u32_loSR_sat(localDW->Product, localDW->Selector_gl0,
    10U);

  /* Product: '<S25>/Divide' */
  tmp_1 = localDW->Product1 / 33U;
  if (tmp_1 > 65535U) {
    tmp_1 = 65535U;
  }

  localDW->Divide = (uint16_T)tmp_1;

  /* End of Product: '<S25>/Divide' */

  /* Sum: '<S17>/Add' */
  localDW->Add_ltv = (int16_T)(localDW->Divide - rtu_VehSpeedRear);

  /* DataTypeConversion: '<S24>/Data Type Conversion8' incorporates:
   *  Constant: '<S16>/BKCREEPLIMERR_dim'
   */
  localDW->DataTypeConversion8 = (uint8_T)BKCREEPLIMERR_dim;

  /* S-Function (PreLookUpIdSearch_S16): '<S24>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S16>/BKCREEPLIMERR'
   */
  PreLookUpIdSearch_S16( &localDW->PreLookUpIdSearch_S16_o1,
                        &localDW->PreLookUpIdSearch_S16_o2, localDW->Add_ltv,
                        &BKCREEPLIMERR[0], localDW->DataTypeConversion8);

  /* DataTypeConversion: '<S26>/Conversion3' incorporates:
   *  Constant: '<S18>/BKCREEPLIMERR_dim'
   */
  localDW->Conversion3 = (uint8_T)BKCREEPLIMERR_dim;

  /* S-Function (LookUp_IR_S16): '<S26>/LookUp_IR_S16' incorporates:
   *  Constant: '<S18>/VTCREEPLIMINTGAIN'
   */
  LookUp_IR_S16( &localDW->LookUp_IR_S16, &VTCREEPLIMINTGAIN[0],
                localDW->PreLookUpIdSearch_S16_o1,
                localDW->PreLookUpIdSearch_S16_o2, localDW->Conversion3);

  /* Selector: '<S16>/Selector' incorporates:
   *  Constant: '<S16>/BKCREEPLIMERR'
   *  Constant: '<S16>/BKCREEPLIMERR_dim'
   */
  localDW->Selector_bif = BKCREEPLIMERR[(int32_T)BKCREEPLIMERR_dim];

  /* MinMax: '<S16>/MinMax' */
  minV_0 = localDW->Add_ltv;
  tmp_0 = localDW->Selector_bif;
  if (minV_0 < tmp_0) {
  } else {
    minV_0 = tmp_0;
  }

  localDW->MinMax_fcp = minV_0;

  /* End of MinMax: '<S16>/MinMax' */

  /* Product: '<S18>/Product2' */
  localDW->Product2 = localDW->LookUp_IR_S16 * localDW->MinMax_fcp;

  /* DataStoreRead: '<S19>/Data Store Read1' */
  localDW->DataStoreRead1_e4y = FlgEnCreepLimProp;

  /* DataTypeConversion: '<S28>/Conversion3' incorporates:
   *  Constant: '<S19>/BKCREEPLIMERR_dim'
   */
  localDW->Conversion3_mmn = (uint8_T)BKCREEPLIMERR_dim;

  /* S-Function (LookUp_IR_S16): '<S28>/LookUp_IR_S16' incorporates:
   *  Constant: '<S19>/VTCREEPLIMPROPGAIN'
   */
  LookUp_IR_S16( &localDW->LookUp_IR_S16_ngm, &VTCREEPLIMPROPGAIN[0],
                localDW->PreLookUpIdSearch_S16_o1,
                localDW->PreLookUpIdSearch_S16_o2, localDW->Conversion3_mmn);

  /* Product: '<S19>/Product' */
  localDW->Product_ky1 = localDW->LookUp_IR_S16_ngm * localDW->MinMax_fcp;

  /* Switch: '<S19>/Switch' */
  if (localDW->DataStoreRead1_e4y != 0) {
    /* DataStoreRead: '<S19>/Data Store Read' */
    localDW->DataStoreRead_fyb = CreepLimProp;
    localDW->Switch = localDW->DataStoreRead_fyb;
  } else {
    localDW->Switch = localDW->Product_ky1;
  }

  /* End of Switch: '<S19>/Switch' */

  /* Sum: '<S19>/Sum4' */
  localDW->Sum4 = localDW->Product_ky1 - localDW->Switch;

  /* Sum: '<S14>/Sum1' */
  localDW->Sum1 = localDW->Product2 + localDW->Sum4;

  /* DataStoreRead: '<S14>/Data Store Read' */
  localDW->DataStoreRead = CmiCreepLimIHiR;

  /* Sum: '<S14>/Sum5' */
  localDW->Sum5 = localDW->Sum1 + localDW->DataStoreRead;

  /* RelationalOperator: '<S22>/LowerRelop1' */
  localDW->LowerRelop1 = (localDW->Sum5 > localDW->MinMax);

  /* Selector: '<S15>/Selector1' incorporates:
   *  Constant: '<S15>/VTMINCMECREEPSAT'
   */
  localDW->Selector1 = VTMINCMECREEPSAT[rtu_GearPos];

  /* Sum: '<S15>/Add1' */
  localDW->Add1 = (rtu_CmfP << 11) - localDW->Selector1;

  /* MinMax: '<S15>/MinMax6' incorporates:
   *  Constant: '<S15>/MINCMICREEPSAT'
   */
  minV = MINCMICREEPSAT;
  tmp = localDW->Add1;
  if (minV > tmp) {
  } else {
    minV = tmp;
  }

  localDW->MinMax6 = minV;

  /* End of MinMax: '<S15>/MinMax6' */

  /* Switch: '<S22>/Switch2' */
  if (localDW->LowerRelop1) {
    localDW->Switch2 = localDW->MinMax;
  } else {
    /* RelationalOperator: '<S22>/UpperRelop' */
    localDW->UpperRelop_icr = (localDW->Sum5 < localDW->MinMax6);

    /* Switch: '<S22>/Switch' */
    if (localDW->UpperRelop_icr) {
      localDW->Switch_bak = localDW->MinMax6;
    } else {
      localDW->Switch_bak = localDW->Sum5;
    }

    /* End of Switch: '<S22>/Switch' */
    localDW->Switch2 = localDW->Switch_bak;
  }

  /* End of Switch: '<S22>/Switch2' */

  /* DataStoreWrite: '<S12>/Data Store Write1' */
  CmiCreepLimIHiR = localDW->Switch2;

  /* DataStoreWrite: '<S12>/Data Store Write2' */
  CreepLimProp = localDW->Product_ky1;

  /* DataStoreWrite: '<S12>/Data Store Write3' */
  CreepLimInt = localDW->Product2;

  /* DataTypeConversion: '<S15>/Data Type Conversion5' */
  localDW->DataTypeConversion5 = (rtu_CmiIdleP << 11);

  /* MinMax: '<S15>/MinMax1' */
  minV = localDW->DataTypeConversion5;
  tmp = localDW->Add;
  if (minV < tmp) {
  } else {
    minV = tmp;
  }

  localDW->MinMax1 = minV;

  /* End of MinMax: '<S15>/MinMax1' */

  /* DataStoreRead: '<S14>/Data Store Read1' */
  localDW->DataStoreRead1 = CmiCreepLimPHiR;

  /* Sum: '<S14>/Sum2' */
  localDW->Sum2 = localDW->Product2 + localDW->DataStoreRead1;

  /* RelationalOperator: '<S23>/LowerRelop1' */
  localDW->LowerRelop1_mzt = (localDW->Sum2 > localDW->MinMax1);

  /* MinMax: '<S15>/MinMax4' */
  minV_0 = rtu_CmiTargetPMin;
  tmp_0 = rtu_CmfP;
  if (minV_0 > tmp_0) {
  } else {
    minV_0 = tmp_0;
  }

  localDW->MinMax4 = minV_0;

  /* End of MinMax: '<S15>/MinMax4' */

  /* Switch: '<S23>/Switch2' */
  if (localDW->LowerRelop1_mzt) {
    localDW->Switch2_aqs = localDW->MinMax1;
  } else {
    /* RelationalOperator: '<S23>/UpperRelop' */
    localDW->UpperRelop = (localDW->Sum2 < (localDW->MinMax4 << 11));

    /* Switch: '<S23>/Switch' */
    if (localDW->UpperRelop) {
      localDW->Switch_ety = (localDW->MinMax4 << 11);
    } else {
      localDW->Switch_ety = localDW->Sum2;
    }

    /* End of Switch: '<S23>/Switch' */
    localDW->Switch2_aqs = localDW->Switch_ety;
  }

  /* End of Switch: '<S23>/Switch2' */

  /* DataStoreWrite: '<S12>/Data Store Write4' */
  CmiCreepLimPHiR = localDW->Switch2_aqs;

  /* DataTypeConversion: '<S15>/Data Type Conversion3' */
  localDW->DataTypeConversion3 = (int16_T)(localDW->Add >> 11);

  /* MinMax: '<S15>/MinMax3' */
  minV_0 = rtu_CmiIdleI;
  tmp_0 = localDW->DataTypeConversion3;
  if (minV_0 < tmp_0) {
  } else {
    minV_0 = tmp_0;
  }

  localDW->MinMax3 = minV_0;

  /* End of MinMax: '<S15>/MinMax3' */

  /* DataTypeConversion: '<S15>/Data Type Conversion' */
  localDW->DataTypeConversion = (int16_T)(localDW->Sum5 >> 11);

  /* RelationalOperator: '<S20>/LowerRelop1' */
  localDW->LowerRelop1_fui = (localDW->DataTypeConversion > localDW->MinMax3);

  /* Switch: '<S20>/Switch2' */
  if (localDW->LowerRelop1_fui) {
    localDW->Switch2_f1i = localDW->MinMax3;
  } else {
    /* DataTypeConversion: '<S15>/Data Type Conversion6' */
    localDW->DataTypeConversion6 = (int16_T)(localDW->MinMax6 >> 11);

    /* MinMax: '<S15>/MinMax5' */
    minV_0 = localDW->DataTypeConversion6;
    if (minV_0 > 0) {
    } else {
      minV_0 = 0;
    }

    localDW->MinMax5 = minV_0;

    /* End of MinMax: '<S15>/MinMax5' */

    /* RelationalOperator: '<S20>/UpperRelop' */
    localDW->UpperRelop_mwq = (localDW->DataTypeConversion < localDW->MinMax5);

    /* Switch: '<S20>/Switch' */
    if (localDW->UpperRelop_mwq) {
      localDW->Switch_mek = localDW->MinMax5;
    } else {
      localDW->Switch_mek = localDW->DataTypeConversion;
    }

    /* End of Switch: '<S20>/Switch' */
    localDW->Switch2_f1i = localDW->Switch_mek;
  }

  /* End of Switch: '<S20>/Switch2' */

  /* DataStoreWrite: '<S12>/Data Store Write5' */
  CmiCreepLimI = localDW->Switch2_f1i;

  /* DataTypeConversion: '<S15>/Data Type Conversion2' */
  localDW->DataTypeConversion2 = (int16_T)(localDW->Add >> 11);

  /* MinMax: '<S15>/MinMax2' */
  minV_0 = rtu_CmiIdleP;
  tmp_0 = localDW->DataTypeConversion2;
  if (minV_0 < tmp_0) {
  } else {
    minV_0 = tmp_0;
  }

  localDW->MinMax2 = minV_0;

  /* End of MinMax: '<S15>/MinMax2' */

  /* DataTypeConversion: '<S15>/Data Type Conversion1' */
  localDW->DataTypeConversion1 = (int16_T)(localDW->Sum2 >> 11);

  /* RelationalOperator: '<S21>/LowerRelop1' */
  localDW->LowerRelop1_kq3 = (localDW->DataTypeConversion1 > localDW->MinMax2);

  /* Switch: '<S21>/Switch2' */
  if (localDW->LowerRelop1_kq3) {
    localDW->Switch2_peb = localDW->MinMax2;
  } else {
    /* RelationalOperator: '<S21>/UpperRelop' */
    localDW->UpperRelop_dbw = (localDW->DataTypeConversion1 < localDW->MinMax4);

    /* Switch: '<S21>/Switch' */
    if (localDW->UpperRelop_dbw) {
      localDW->Switch_ocf = localDW->MinMax4;
    } else {
      localDW->Switch_ocf = localDW->DataTypeConversion1;
    }

    /* End of Switch: '<S21>/Switch' */
    localDW->Switch2_peb = localDW->Switch_ocf;
  }

  /* End of Switch: '<S21>/Switch2' */

  /* DataStoreWrite: '<S12>/Data Store Write8' */
  CmiCreepLimP = localDW->Switch2_peb;

  /* DataStoreWrite: '<S14>/Data Store Write1' */
  CreepLimDProp = localDW->Sum4;

  /* DataStoreWrite: '<S16>/Data Store Write2' */
  VehSpdCreepErr = localDW->MinMax_fcp;

  /* DataStoreWrite: '<S17>/Data Store Write5' */
  VehSpdCreepTrg = localDW->Divide;
}

/* Output and update for function-call system: '<S4>/Return_limiter' */
void CreepLimiterMgm_Return_limiter(int16_T rtu_CmiIdleP, int16_T rtu_CmiIdleI,
  int16_T rtu_CmiTargetPMin, rtDW_Return_limiter_CreepLimi_T *localDW)
{
  /* DataStoreRead: '<S13>/Data Store Read1' */
  localDW->DataStoreRead1 = OldCmiCreepLimI;

  /* S-Function (RateLimiter_S16): '<S30>/RateLimiter_S16' incorporates:
   *  Constant: '<S13>/MAXRATECMICREEP'
   *  Constant: '<S13>/MINRATECMICREEP'
   */
  RateLimiter_S16( &localDW->RateLimiter_S16, rtu_CmiIdleI,
                  localDW->DataStoreRead1, MINRATECMICREEP, MAXRATECMICREEP);

  /* RelationalOperator: '<S32>/LowerRelop1' */
  localDW->LowerRelop1 = (localDW->RateLimiter_S16 > rtu_CmiIdleI);

  /* Switch: '<S32>/Switch2' */
  if (localDW->LowerRelop1) {
    localDW->Switch2 = rtu_CmiIdleI;
  } else {
    /* RelationalOperator: '<S32>/UpperRelop' */
    localDW->UpperRelop_hcn = (localDW->RateLimiter_S16 < 0);

    /* Switch: '<S32>/Switch' incorporates:
     *  Constant: '<S13>/ZERO'
     */
    if (localDW->UpperRelop_hcn) {
      localDW->Switch_dzb = 0;
    } else {
      localDW->Switch_dzb = localDW->RateLimiter_S16;
    }

    /* End of Switch: '<S32>/Switch' */
    localDW->Switch2 = localDW->Switch_dzb;
  }

  /* End of Switch: '<S32>/Switch2' */

  /* DataStoreWrite: '<S13>/Data Store Write1' */
  CmiCreepLimI = localDW->Switch2;

  /* DataStoreRead: '<S13>/Data Store Read' */
  localDW->DataStoreRead = OldCmiCreepLimP;

  /* S-Function (RateLimiter_S16): '<S31>/RateLimiter_S16' incorporates:
   *  Constant: '<S13>/MAXRATECMICREEP'
   *  Constant: '<S13>/MINRATECMICREEP'
   */
  RateLimiter_S16( &localDW->RateLimiter_S16_m4w, rtu_CmiIdleP,
                  localDW->DataStoreRead, MINRATECMICREEP, MAXRATECMICREEP);

  /* RelationalOperator: '<S33>/LowerRelop1' */
  localDW->LowerRelop1_f5y = (localDW->RateLimiter_S16_m4w > rtu_CmiIdleP);

  /* Switch: '<S33>/Switch2' */
  if (localDW->LowerRelop1_f5y) {
    localDW->Switch2_dal = rtu_CmiIdleP;
  } else {
    /* RelationalOperator: '<S33>/UpperRelop' */
    localDW->UpperRelop = (localDW->RateLimiter_S16_m4w < rtu_CmiTargetPMin);

    /* Switch: '<S33>/Switch' */
    if (localDW->UpperRelop) {
      localDW->Switch = rtu_CmiTargetPMin;
    } else {
      localDW->Switch = localDW->RateLimiter_S16_m4w;
    }

    /* End of Switch: '<S33>/Switch' */
    localDW->Switch2_dal = localDW->Switch;
  }

  /* End of Switch: '<S33>/Switch2' */

  /* DataStoreWrite: '<S13>/Data Store Write8' */
  CmiCreepLimP = localDW->Switch2_dal;
}

/* System initialize for function-call system: '<S1>/T10ms' */
void CreepLimiterMgm_T10ms_Init(void)
{
  /* SystemInitialize for Chart: '<S4>/CreepEnMachine' */
  CreepLimiterMgm_DWork.is_active_c3_CreepLimiterMgm = 0U;
  CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm = CreepLimiter_IN_NO_ACTIVE_CHILD;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void CreepLimiterMgm_T10ms(void)
{
  /* Chart: '<S4>/CreepEnMachine' incorporates:
   *  Inport: '<Root>/CmeDriver'
   *  Inport: '<Root>/CmfP'
   *  Inport: '<Root>/CmiIdleI'
   *  Inport: '<Root>/CmiIdleP'
   *  Inport: '<Root>/CmiTargetPMin'
   *  Inport: '<Root>/FlgSpringUp'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/GearRatio'
   *  Inport: '<Root>/RearWheelRadiusNc'
   *  Inport: '<Root>/StDiag'
   *  Inport: '<Root>/VehSpeedRear'
   */
  /* Gateway: CreepLimiterMgm/T10ms/CreepEnMachine */
  /* During: CreepLimiterMgm/T10ms/CreepEnMachine */
  if (CreepLimiterMgm_DWork.is_active_c3_CreepLimiterMgm == 0U) {
    /* Entry: CreepLimiterMgm/T10ms/CreepEnMachine */
    CreepLimiterMgm_DWork.is_active_c3_CreepLimiterMgm = 1U;

    /* Entry Internal: CreepLimiterMgm/T10ms/CreepEnMachine */
    /* Transition: '<S10>:4' */
    StCreepLim = ((uint8_T)ST_INIT);

    /* Outputs for Function Call SubSystem: '<S4>/No_limiter' */

    /* Event: '<S10>:42' */
    CreepLimiterMgm_No_limiter(CmiIdleP, CmiIdleI,
      &CreepLimiterMgm_DWork.No_limiter);

    /* End of Outputs for SubSystem: '<S4>/No_limiter' */
    CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm = CreepLimiterMgm_IN_ST_INIT;
  } else {
    switch (CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm) {
     case CreepLimiterMgm_IN_ST_CREEPING:
      /* During 'ST_CREEPING': '<S10>:1' */
      /* Transition: '<S10>:17' */
      if (((GearPos <= MINGPCCREEPSTART) || (StDiag[(DIAG_VEHSPEED)] == FAULT)) ||
          (StDiag[(DIAG_GEAR_SENSOR)] == FAULT)) {
        /* Transition: '<S10>:8' */
        StCreepLim = ((uint8_T)ST_RETURN);
        OldCmiCreepLimP = CmiCreepLimP;
        OldCmiCreepLimI = CmiCreepLimI;

        /* Outputs for Function Call SubSystem: '<S4>/Return_limiter' */

        /* Event: '<S10>:44' */
        CreepLimiterMgm_Return_limiter(CmiIdleP, CmiIdleI, CmiTargetPMin,
          &CreepLimiterMgm_DWork.Return_limiter);

        /* End of Outputs for SubSystem: '<S4>/Return_limiter' */
        CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm =
          CreepLimiterMgm_IN_ST_RETURN;
      } else {
        /* Transition: '<S10>:18' */
        FlgEnCreepLimProp = 1U;

        /* Outputs for Function Call SubSystem: '<S4>/PI_Limiter' */

        /* Event: '<S10>:43' */
        CreepLimiterMgm_PI_Limiter(VehSpeedRear, CmiIdleP, GearPos, CmiIdleI,
          CmiTargetPMin, CmfP, (&(GearRatio[0])), RearWheelRadiusNc,
          &CreepLimiterMgm_DWork.PI_Limiter);

        /* End of Outputs for SubSystem: '<S4>/PI_Limiter' */
      }
      break;

     case CreepLimiterMgm_IN_ST_FREE:
      /* During 'ST_FREE': '<S10>:50' */
      /* Transition: '<S10>:57' */
      if (((ENCRPUPSHIFT == 0) || (VehSpeedRear <= VEHCREEPFREE)) || (StDiag
           [(DIAG_VEHSPEED)] == FAULT)) {
        /* Transition: '<S10>:60' */
        StCreepLim = ((uint8_T)ST_INIT);

        /* Outputs for Function Call SubSystem: '<S4>/No_limiter' */

        /* Event: '<S10>:42' */
        CreepLimiterMgm_No_limiter(CmiIdleP, CmiIdleI,
          &CreepLimiterMgm_DWork.No_limiter);

        /* End of Outputs for SubSystem: '<S4>/No_limiter' */

        /* Transition: '<S10>:61' */
        CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm = CreepLimiterMgm_IN_ST_INIT;
      } else {
        /* Outputs for Function Call SubSystem: '<S4>/No_limiter' */

        /* Transition: '<S10>:58' */
        /* Event: '<S10>:42' */
        CreepLimiterMgm_No_limiter(CmiIdleP, CmiIdleI,
          &CreepLimiterMgm_DWork.No_limiter);

        /* End of Outputs for SubSystem: '<S4>/No_limiter' */
      }
      break;

     case CreepLimiterMgm_IN_ST_INIT:
      /* During 'ST_INIT': '<S10>:3' */
      /* Transition: '<S10>:62' */
      if (((((FlgSpringUp != 0) && (CmeDriver > MINCMECREEPSTART)) && (GearPos >
             MINGPCCREEPSTART)) && (StDiag[(DIAG_VEHSPEED)] != FAULT)) &&
          (StDiag[(DIAG_GEAR_SENSOR)] != FAULT)) {
        /* Transition: '<S10>:6' */
        StCreepLim = ((uint8_T)ST_CREEPING);
        FlgEnCreepLimProp = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/PI_Limiter' */

        /* Event: '<S10>:43' */
        CreepLimiterMgm_PI_Limiter(VehSpeedRear, CmiIdleP, GearPos, CmiIdleI,
          CmiTargetPMin, CmfP, (&(GearRatio[0])), RearWheelRadiusNc,
          &CreepLimiterMgm_DWork.PI_Limiter);

        /* End of Outputs for SubSystem: '<S4>/PI_Limiter' */
        CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm =
          CreepLimiterMgm_IN_ST_CREEPING;
      } else {
        /* Transition: '<S10>:16' */
        if (((((ENCRPUPSHIFT != 0) && (VehSpeedRear > VEHCREEPFREE)) && (GearPos
               <= MINGPCCREEPSTART)) && (StDiag[(DIAG_VEHSPEED)] != FAULT)) &&
            (StDiag[(DIAG_GEAR_SENSOR)] != FAULT)) {
          /* Transition: '<S10>:55' */
          StCreepLim = ((uint8_T)ST_FREE);

          /* Outputs for Function Call SubSystem: '<S4>/No_limiter' */

          /* Event: '<S10>:42' */
          CreepLimiterMgm_No_limiter(CmiIdleP, CmiIdleI,
            &CreepLimiterMgm_DWork.No_limiter);

          /* End of Outputs for SubSystem: '<S4>/No_limiter' */
          CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm =
            CreepLimiterMgm_IN_ST_FREE;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/No_limiter' */

          /* Transition: '<S10>:63' */
          /* Event: '<S10>:42' */
          CreepLimiterMgm_No_limiter(CmiIdleP, CmiIdleI,
            &CreepLimiterMgm_DWork.No_limiter);

          /* End of Outputs for SubSystem: '<S4>/No_limiter' */
        }
      }
      break;

     default:
      /* During 'ST_RETURN': '<S10>:10' */
      /* Transition: '<S10>:19' */
      if (CmiIdleP < (CmiCreepLimP + DELCMICRLIMRET)) {
        /* Transition: '<S10>:13' */
        StCreepLim = ((uint8_T)ST_FREE);

        /* Outputs for Function Call SubSystem: '<S4>/No_limiter' */

        /* Event: '<S10>:42' */
        CreepLimiterMgm_No_limiter(CmiIdleP, CmiIdleI,
          &CreepLimiterMgm_DWork.No_limiter);

        /* End of Outputs for SubSystem: '<S4>/No_limiter' */
        CreepLimiterMgm_DWork.is_c3_CreepLimiterMgm = CreepLimiterMgm_IN_ST_FREE;
      } else {
        /* Transition: '<S10>:20' */
        OldCmiCreepLimP = CmiCreepLimP;
        OldCmiCreepLimI = CmiCreepLimI;

        /* Outputs for Function Call SubSystem: '<S4>/Return_limiter' */

        /* Event: '<S10>:44' */
        CreepLimiterMgm_Return_limiter(CmiIdleP, CmiIdleI, CmiTargetPMin,
          &CreepLimiterMgm_DWork.Return_limiter);

        /* End of Outputs for SubSystem: '<S4>/Return_limiter' */
      }
      break;
    }
  }

  /* End of Chart: '<S4>/CreepEnMachine' */
  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/Off' */
void CreepLimiterMgm_Off(void)
{
  /* user code (Output function Trailer) */

  /* System '<S1>/Off' */
  /* PILOTAGGIO USCITE - OFF */
}

/* Model step function */
void CreepLimiterMgm_step(void)
{
  boolean_T zcEvent;

  /* Outputs for Atomic SubSystem: '<Root>/CreepLimiterMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  zcEvent = ((CreepLimiterMgm_U.ev_PowerOn > 0) &&
             (CreepLimiterMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG));
  if (zcEvent) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    CreepLimiterMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  /* Inport: '<Root>/ev_PowerOn' */
  CreepLimiterMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (CreepLimiterMgm_U.ev_PowerOn > 0);

  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc3' incorporates:
   *  TriggerPort: '<S9>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOff' */
  zcEvent = ((CreepLimiterMgm_U.ev_PowerOff > 0) &&
             (CreepLimiterMgm_PrevZCSigState.trig_to_fc3_Trig_ZCE != POS_ZCSIG));
  if (zcEvent) {
    /* S-Function (fcncallgen): '<S9>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Off'
     */
    CreepLimiterMgm_Off();

    /* End of Outputs for S-Function (fcncallgen): '<S9>/Function-Call Generator' */
  }

  /* Inport: '<Root>/ev_PowerOff' */
  CreepLimiterMgm_PrevZCSigState.trig_to_fc3_Trig_ZCE = (ZCSigState)
    (CreepLimiterMgm_U.ev_PowerOff > 0);

  /* End of Outputs for SubSystem: '<S1>/trig_to_fc3' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_Tdc' */
  zcEvent = ((CreepLimiterMgm_U.ev_Tdc > 0) &&
             (CreepLimiterMgm_PrevZCSigState.trig_to_fc_Trig_ZCE != POS_ZCSIG));
  if (zcEvent) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Tdc'
     */
    CreepLimiterMgm_Tdc();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  /* Inport: '<Root>/ev_Tdc' */
  CreepLimiterMgm_PrevZCSigState.trig_to_fc_Trig_ZCE = (ZCSigState)
    (CreepLimiterMgm_U.ev_Tdc > 0);

  /* End of Outputs for SubSystem: '<S1>/trig_to_fc' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S8>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  zcEvent = ((CreepLimiterMgm_U.ev_T10ms > 0) &&
             (CreepLimiterMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG));
  if (zcEvent) {
    /* S-Function (fcncallgen): '<S8>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    CreepLimiterMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S8>/Function-Call Generator' */
  }

  /* Inport: '<Root>/ev_T10ms' */
  CreepLimiterMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (CreepLimiterMgm_U.ev_T10ms > 0);

  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* End of Outputs for SubSystem: '<Root>/CreepLimiterMgm' */
}

/* Model initialize function */
void CreepLimiterMgm_initialize(void)
{
  /* Registration code */

  /* states (dwork) */
  (void) memset((void *)&CreepLimiterMgm_DWork, 0,
                sizeof(D_Work_CreepLimiterMgm_T));

  /* custom states */
  CreepLimProp = 0;
  CreepLimInt = 0;
  CmiCreepLimIHiR = 0;
  CreepLimDProp = 0;
  CmiCreepLimPHiR = 0;
  CmiCreepLimI = 0;
  CmiCreepLimP = 0;
  OldCmiCreepLimI = 0;
  OldCmiCreepLimP = 0;
  VehSpdCreepTrg = 0U;
  VehSpdCreepErr = 0;
  FlgEnCreepLimProp = 0U;
  StCreepLim = 0U;

  /* external inputs */
  (void)memset((void *)(&CreepLimiterMgm_U), 0, sizeof
               (ExternalInputs_CreepLimiterMg_T));
  CreepLimiterMgm_PrevZCSigState.trig_to_fc_Trig_ZCE = POS_ZCSIG;
  CreepLimiterMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  CreepLimiterMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
  CreepLimiterMgm_PrevZCSigState.trig_to_fc3_Trig_ZCE = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/CreepLimiterMgm' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/trig_to_fc2' */

  /* SystemInitialize for S-Function (fcncallgen): '<S8>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  CreepLimiterMgm_T10ms_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S8>/Function-Call Generator' */

  /* End of SystemInitialize for SubSystem: '<S1>/trig_to_fc2' */

  /* End of SystemInitialize for SubSystem: '<Root>/CreepLimiterMgm' */
}

/* user code (bottom of source file) */
/* System '<Root>/CreepLimiterMgm' */
#endif                                 // _BUILD_CREEPLIMITERMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
