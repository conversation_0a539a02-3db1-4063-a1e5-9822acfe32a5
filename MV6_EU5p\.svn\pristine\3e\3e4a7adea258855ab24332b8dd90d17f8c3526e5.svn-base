/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           QAirTargetMgm.c
 **  File Creation Date: 19-Sep-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         QAirTargetMgm
 **  Model Description:
 **  Model Version:      1.720
 **  Model Author:       girasoleg - Thu Sep 30 15:50:39 2004
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Mon Sep 19 13:45:54 2022
 **
 **  Last Saved Modification:   -
 **
 **
 *******************************************************************************
 **/

#include "QAirTargetMgm.h"
#include "QAirTargetMgm_private.h"
#include "div_nzp_repeat_s32_sat.h"

/*  Defines */

/*  Data Types */

/* user code (top of source file) */
/* System '<Root>/QAirTargetMgm' */
#ifdef _BUILD_QAIRTARGETMGM_

/**************************** GLOBAL DATA *************************************/
/*  Definitions */

/* Block states (default storage) */
D_Work_QAirTargetMgm QAirTargetMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_QAirTargetMgm QAirTargetMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_QAirTargetMgm QAirTargetMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T FlgStabDyn;

/* QAIRTARGETMGM.FlgStabDyn: stab flag */
int16_T GnRtQAirTarget0;

/* Gain */
uint32_T IDQAirTarget;

/* ID Version */
uint16_T QAirTarget;

/* QAIRTARGETMGM.QAirTarget: Air flow rate target (overall engine cycle) */
uint16_T QAirTarget0;

/* QAIRTARGETMGM.QAirTarget0: Air flow rate target (reference cylinder) */
uint16_T QAirTargetA;

/* QAIRTARGETMGM.QAirTarget: Air flow rate target frefiltered (overall engine cycle) */
uint16_T QAirTargetMean;

/* QAIRTARGETMGM.QAirTargetMean: Air flow rate target (mean cylinder) */

/*  Declarations  */

/***************************** FILE SCOPE DATA ********************************/

/*************************** FUNCTIONS ****************************************/

/* Output and update for function-call system: '<S1>/Init' */
void QAirTargetMgm_Init(void)
{
  /* DataTypeConversion: '<S2>/Data Type Conversion' incorporates:
   *  Constant: '<S2>/QAIR_INIT'
   *  DataStoreWrite: '<S2>/Data Store Write'
   */
  QAirTarget = (uint16_T)((uint32_T)((uint16_T)QAIR_INIT) >> 1);

  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/QAIR_INIT'
   */
  QAirTarget0 = ((uint16_T)QAIR_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Constant: '<S2>/QAIR_INIT'
   */
  QAirTargetMean = ((uint16_T)QAIR_INIT);

  /* DataTypeConversion: '<S2>/Data Type Conversion1' incorporates:
   *  Constant: '<S2>/QAIR_INIT'
   *  DataStoreWrite: '<S2>/Data Store Write3'
   */
  QAirTargetA = (uint16_T)((uint32_T)((uint16_T)QAIR_INIT) >> 1);

  /* Constant: '<S2>/ID_QAIR_TARGET' */
  IDQAirTarget = ID_QAIR_TARGET;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void QAirTargetMgm_T10ms(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_LookUp_U16_U16;
  int16_T rtb_RateLimiter_S16;
  uint8_T rtb_SteadyStateDetect_o2;
  uint16_T rtb_Product1;
  uint16_T rtb_Divide6;
  int16_T rtb_Add1_p;
  int32_T rtb_Add1;
  uint8_T rtb_SteadyStateDetect_o1;
  uint8_T rtb_Conversion3;
  uint16_T rtb_DataTypeConversion_n;
  int16_T rtb_Memory;

  /* Product: '<S6>/Divide1' incorporates:
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/EffSAbase'
   */
  rtb_Memory = (int16_T)(((uint32_T)EffSAbase * EffLambda) >> 14);

  /* Product: '<S6>/Divide2' incorporates:
   *  Inport: '<Root>/CmiTargetP'
   */
  rtb_Add1 = (CmiTargetP << 14) / rtb_Memory;
  if (rtb_Add1 > 32767) {
    rtb_Add1 = 32767;
  } else {
    if (rtb_Add1 < -32768) {
      rtb_Add1 = -32768;
    }
  }

  rtb_Add1_p = (int16_T)rtb_Add1;

  /* End of Product: '<S6>/Divide2' */

  /* Product: '<S6>/Divide4' incorporates:
   *  Constant: '<S6>/N_CYLINDER'
   *  Inport: '<Root>/CmiOffset'
   */
  rtb_Memory = (int16_T)(CmiOffset * ((uint8_T)N_CYLINDER));

  /* Sum: '<S6>/Add1' */
  rtb_Add1 = rtb_Add1_p - rtb_Memory;

  /* MinMax: '<S6>/MinMax' incorporates:
   *  Constant: '<S6>/ZERO'
   */
  if (0 > rtb_Add1) {
    rtb_Add1 = 0;
  }

  /* End of MinMax: '<S6>/MinMax' */

  /* Switch: '<S6>/Switch' incorporates:
   *  Constant: '<S6>/ZERO1'
   *  Inport: '<Root>/CmiGain'
   */
  if (CmiGain != 0) {
    rtb_DataTypeConversion_n = CmiGain;
  } else {
    rtb_DataTypeConversion_n = 1U;
  }

  /* End of Switch: '<S6>/Switch' */

  /* Product: '<S6>/Divide3' incorporates:
   *  DataStoreWrite: '<S3>/Data Store Write3'
   */
  rtb_Add1 = div_nzp_repeat_s32_sat(rtb_Add1, rtb_DataTypeConversion_n, 13U);
  if (rtb_Add1 < 0) {
    rtb_Add1 = 0;
  } else {
    if (rtb_Add1 > 65535) {
      rtb_Add1 = 65535;
    }
  }

  QAirTargetA = (uint16_T)rtb_Add1;

  /* End of Product: '<S6>/Divide3' */

  /* DataTypeConversion: '<S9>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S3>/Data Store Write3'
   */
  rtb_Add1_p = (int16_T)((uint32_T)QAirTargetA >> 1);

  /* DataTypeConversion: '<S8>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/CmiTargetP'
   */
  rtb_DataTypeConversion_n = (uint16_T)CmiTargetP;

  /* DataTypeConversion: '<S20>/Conversion4' incorporates:
   *  Constant: '<S19>/Constant'
   *  Inport: '<Root>/EndStartFlg'
   *  RelationalOperator: '<S19>/Compare'
   */
  rtb_SteadyStateDetect_o1 = (uint8_T)(EndStartFlg == 0);

  /* Memory: '<S8>/Memory' */
  rtb_Conversion3 = QAirTargetMgm_DWork.Memory_PreviousInput_n;

  /* Memory: '<S20>/Memory1' */
  rtb_Product1 = QAirTargetMgm_DWork.Memory1_PreviousInput;

  /* Memory: '<S20>/Memory' */
  rtb_Divide6 = QAirTargetMgm_DWork.Memory_PreviousInput_k;

  /* S-Function (SteadyStateDetect): '<S20>/SteadyStateDetect' incorporates:
   *  Constant: '<S8>/THRSTSTABDYN'
   *  Constant: '<S8>/TIMSTSTABDYN'
   */
  SteadyStateDetect( &rtb_SteadyStateDetect_o1, &rtb_SteadyStateDetect_o2,
                    &rtb_SteadyStateDetect_o3, &rtb_SteadyStateDetect_o4,
                    rtb_DataTypeConversion_n, rtb_SteadyStateDetect_o1,
                    THRSTSTABDYN, TIMSTSTABDYN, rtb_Conversion3, rtb_Product1,
                    rtb_Divide6);

  /* Switch: '<S9>/Switch' incorporates:
   *  Constant: '<S9>/KFILTQAIRTRG'
   */
  if (rtb_SteadyStateDetect_o1 != 0) {
    /* S-Function (LookUp_U16_U16): '<S12>/LookUp_U16_U16' incorporates:
     *  Constant: '<S9>/BKKFILTQAIRTRG'
     *  Constant: '<S9>/BKKFILTQAIRTRG_dim'
     *  Constant: '<S9>/VTKFILTQAIRTRG'
     *  Inport: '<Root>/Rpm'
     */
    LookUp_U16_U16( &rtb_LookUp_U16_U16, &VTKFILTQAIRTRG[0], Rpm,
                   &BKKFILTQAIRTRG[0], ((uint8_T)BKKFILTQAIRTRG_dim));
    rtb_Divide6 = rtb_LookUp_U16_U16;
  } else {
    rtb_Divide6 = KFILTQAIRTRG;
  }

  /* End of Switch: '<S9>/Switch' */

  /* DataTypeConversion: '<S11>/Conversion3' incorporates:
   *  Constant: '<S10>/Constant'
   *  Inport: '<Root>/EndStartFlg'
   *  RelationalOperator: '<S10>/Compare'
   */
  rtb_Conversion3 = (uint8_T)(EndStartFlg == 0);

  /* Memory: '<S9>/Memory' */
  rtb_Add1 = QAirTargetMgm_DWork.Memory_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S11>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_Add1_p, &rtb_FOF_Reset_S16_FXP_o2, rtb_Add1_p,
                    rtb_Divide6, rtb_Add1_p, rtb_Conversion3, rtb_Add1);

  /* DataTypeConversion: '<S9>/Data Type Conversion1' incorporates:
   *  DataStoreWrite: '<S3>/Data Store Write'
   */
  QAirTarget = (uint16_T)((uint16_T)rtb_Add1_p << 1);

  /* Sum: '<S6>/Add4' incorporates:
   *  Constant: '<S6>/ONE'
   *  Inport: '<Root>/TbQAirGain1'
   *  Inport: '<Root>/TbQAirGain2'
   *  Inport: '<Root>/TbQAirGain3'
   *  Sum: '<S6>/Add2'
   *  Sum: '<S6>/Add3'
   */
  rtb_Divide6 = (uint16_T)((((((((TbQAirGain1 + 32768U) >> 2) << 2) +
    TbQAirGain2) >> 2) << 2) + TbQAirGain3) >> 2);

  /* Product: '<S6>/Divide5' incorporates:
   *  DataStoreWrite: '<S3>/Data Store Write'
   */
  rtb_Product1 = (uint16_T)(((uint32_T)QAirTarget << 14) / rtb_Divide6);

  /* Switch: '<S7>/Switch' incorporates:
   *  Constant: '<S7>/ONE_QATINC'
   *  Constant: '<S7>/ZERO_QATINC'
   *  Inport: '<Root>/TrqStartFlg'
   */
  if (TrqStartFlg != 0) {
    rtb_Add1_p = ((int16_T)ONE_QATINC);
  } else {
    rtb_Add1_p = ((int16_T)ZERO_QATINC);
  }

  /* End of Switch: '<S7>/Switch' */

  /* Memory: '<S7>/Memory' */
  rtb_Memory = QAirTargetMgm_DWork.Memory_PreviousInput_f;

  /* S-Function (RateLimiter_S16): '<S15>/RateLimiter_S16' incorporates:
   *  Constant: '<S7>/ONE_QATDEC'
   *  Constant: '<S7>/RTQATINC'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16, rtb_Add1_p, rtb_Memory, ((int16_T)
    ONE_QATDEC), RTQATINC);

  /* Product: '<S7>/Product' */
  rtb_Divide6 = (uint16_T)((rtb_Product1 * rtb_RateLimiter_S16) >> 10);

  /* Sum: '<S7>/Add1' incorporates:
   *  Constant: '<S7>/ONE_QATINC1'
   */
  rtb_Add1_p = (int16_T)(((int16_T)ONE_QATINC) - rtb_RateLimiter_S16);

  /* Product: '<S7>/Product1' incorporates:
   *  Inport: '<Root>/QAirCyl0'
   */
  rtb_Product1 = (uint16_T)((QAirCyl0 * rtb_Add1_p) >> 10);

  /* Sum: '<S7>/Add' */
  rtb_DataTypeConversion_n = (uint16_T)((uint32_T)rtb_Divide6 + rtb_Product1);

  /* Switch: '<S16>/Switch2' incorporates:
   *  DataStoreWrite: '<S3>/Data Store Write1'
   *  Inport: '<Root>/QAirMaxCyl'
   *  Inport: '<Root>/QAirMinCyl'
   *  RelationalOperator: '<S16>/LowerRelop1'
   *  RelationalOperator: '<S16>/UpperRelop'
   *  Switch: '<S16>/Switch'
   */
  if (rtb_DataTypeConversion_n > QAirMaxCyl) {
    QAirTarget0 = QAirMaxCyl;
  } else if (rtb_DataTypeConversion_n < QAirMinCyl) {
    /* Switch: '<S16>/Switch' incorporates:
     *  DataStoreWrite: '<S3>/Data Store Write1'
     *  Inport: '<Root>/QAirMinCyl'
     */
    QAirTarget0 = QAirMinCyl;
  } else {
    QAirTarget0 = rtb_DataTypeConversion_n;
  }

  /* End of Switch: '<S16>/Switch2' */

  /* Product: '<S6>/Divide6' incorporates:
   *  Constant: '<S6>/N_CYLINDER'
   *  DataStoreWrite: '<S3>/Data Store Write'
   */
  rtb_Divide6 = (uint16_T)(((uint32_T)QAirTarget << 1) / ((uint8_T)N_CYLINDER));

  /* Product: '<S7>/Product2' */
  rtb_Add1 = rtb_RateLimiter_S16 * rtb_Divide6;

  /* Sum: '<S7>/Add2' */
  rtb_Product1 = (uint16_T)(((rtb_Product1 << 9) + (rtb_Add1 >> 1)) >> 9);

  /* Switch: '<S17>/Switch2' incorporates:
   *  DataStoreWrite: '<S3>/Data Store Write2'
   *  Inport: '<Root>/QAirMaxCyl'
   *  Inport: '<Root>/QAirMinCyl'
   *  RelationalOperator: '<S17>/LowerRelop1'
   *  RelationalOperator: '<S17>/UpperRelop'
   *  Switch: '<S17>/Switch'
   */
  if (rtb_Product1 > QAirMaxCyl) {
    QAirTargetMean = QAirMaxCyl;
  } else if (rtb_Product1 < QAirMinCyl) {
    /* Switch: '<S17>/Switch' incorporates:
     *  DataStoreWrite: '<S3>/Data Store Write2'
     *  Inport: '<Root>/QAirMinCyl'
     */
    QAirTargetMean = QAirMinCyl;
  } else {
    QAirTargetMean = rtb_Product1;
  }

  /* End of Switch: '<S17>/Switch2' */

  /* DataStoreWrite: '<S7>/Data Store Write' */
  GnRtQAirTarget0 = rtb_RateLimiter_S16;

  /* DataStoreWrite: '<S8>/Data Store Write3' */
  FlgStabDyn = rtb_SteadyStateDetect_o1;

  /* Update for Memory: '<S8>/Memory' */
  QAirTargetMgm_DWork.Memory_PreviousInput_n = rtb_SteadyStateDetect_o2;

  /* Update for Memory: '<S20>/Memory1' */
  QAirTargetMgm_DWork.Memory1_PreviousInput = rtb_SteadyStateDetect_o3;

  /* Update for Memory: '<S20>/Memory' */
  QAirTargetMgm_DWork.Memory_PreviousInput_k = rtb_SteadyStateDetect_o4;

  /* Update for Memory: '<S9>/Memory' */
  QAirTargetMgm_DWork.Memory_PreviousInput = rtb_FOF_Reset_S16_FXP_o2;

  /* Update for Memory: '<S7>/Memory' */
  QAirTargetMgm_DWork.Memory_PreviousInput_f = rtb_RateLimiter_S16;
}

/* Model step function */
void QAirTargetMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/QAirTargetMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_T10ms' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_10ms' */
  if ((QAirTargetMgm_U.ev_10ms > 0) &&
      (QAirTargetMgm_PrevZCSigState.fc_T10ms_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    QAirTargetMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  QAirTargetMgm_PrevZCSigState.fc_T10ms_Trig_ZCE = (ZCSigState)
    (QAirTargetMgm_U.ev_10ms > 0);

  /* End of Inport: '<Root>/ev_10ms' */
  /* End of Outputs for SubSystem: '<S1>/fc_T10ms' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_Init' incorporates:
   *  TriggerPort: '<S4>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' incorporates:
   *  Inport: '<Root>/ev_NoSync'
   */
  if (((QAirTargetMgm_U.ev_PowerOn > 0) &&
       (QAirTargetMgm_PrevZCSigState.fc_Init_Trig_ZCE[0] != POS_ZCSIG)) ||
      ((QAirTargetMgm_U.ev_NoSync > 0) &&
       (QAirTargetMgm_PrevZCSigState.fc_Init_Trig_ZCE[1] != POS_ZCSIG))) {
    /* S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    QAirTargetMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S4>/Function-Call Generator' */
  }

  QAirTargetMgm_PrevZCSigState.fc_Init_Trig_ZCE[0] = (ZCSigState)
    (QAirTargetMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */

  /* Inport: '<Root>/ev_NoSync' */
  QAirTargetMgm_PrevZCSigState.fc_Init_Trig_ZCE[1] = (ZCSigState)
    (QAirTargetMgm_U.ev_NoSync > 0);

  /* End of Outputs for SubSystem: '<S1>/fc_Init' */

  /* End of Outputs for SubSystem: '<Root>/QAirTargetMgm' */
}

/* Model initialize function */
void QAirTargetMgm_initialize(void)
{
  QAirTargetMgm_PrevZCSigState.fc_Init_Trig_ZCE[0] = POS_ZCSIG;
  QAirTargetMgm_PrevZCSigState.fc_Init_Trig_ZCE[1] = POS_ZCSIG;
  QAirTargetMgm_PrevZCSigState.fc_T10ms_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/QAirTargetMgm' */
void QAirTargetMgm_PowerOn(void)
{
  QAirTargetMgm_Init();
}

void QAirTargetMgm_NoSync(void)
{
  QAirTargetMgm_Init();
}

#else

extern uint16_T QAirCyl
  void QAirTargetMgm_PowerOn(void)
{
  QAirTarget0 = QAirCyl;
  QAirTarget = QAirCyl;
}

void QAirTargetMgm_NoSync(void)
{
  QAirTarget0 = QAirCyl;
  QAirTarget = QAirCyl;
}

void QAirTargetMgm_T10ms(void)
{
  QAirTarget0 = QAirCyl;
  QAirTarget = QAirCyl;
}

#endif                                 // _BUILD_QAIRTARGETMGM_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
