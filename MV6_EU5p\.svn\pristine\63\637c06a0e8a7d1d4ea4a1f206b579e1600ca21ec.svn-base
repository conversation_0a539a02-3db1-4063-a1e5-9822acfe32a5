/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/
#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//(CmeDriverPTmp - CmeDriverI) threshold for UP-STAB transition [Nm]
__declspec(section ".calib") int16_T CMEDRIVPDRIVITHR = 4;   //(   0.12500*32)
//Difference between not filtered and filtered to detect CmiDriverP filter stop [Nm]
__declspec(section ".calib") int16_T CMEDRIVPSTABTHR = 160;   //(   5.00000*32)
//Difference between not filtered and filtered to detect CmiDriverP filter activation in ECO DRIVE [Nm]
__declspec(section ".calib") int16_T CMEDRIVPSTEPTHR = 5;   //(   0.15625*32)
//Filter enable calibration flag [flag]
__declspec(section ".calib") uint8_T ENTRQDRIVMGM =  1U;   // 1
