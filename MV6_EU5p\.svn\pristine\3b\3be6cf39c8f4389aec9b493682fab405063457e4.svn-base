/*
 * File: TracCtrl_types.h
 *
 * Code generated for Simulink model 'TracCtrl'.
 *
 * Model version                  : 1.825
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 13:03:51 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TracCtrl_types_h_
#define RTW_HEADER_TracCtrl_types_h_
#endif                                 /* RTW_HEADER_TracCtrl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
