/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Device_Drivers/DBWMGM/main_trunk/DbwMgm_ert_rtw/DbwMgm.h $ */
/* $Description:  $ */
/* $Revision: 5140 $ */
/* $Date: 2012-10-29 12:10:55 +0100 (lun, 29 ott 2012) $ */
/* $Author: lanal $ */
/*****************************************************************************************************************/
/*
 * File: DbwMgm.h
 *
 * Real-Time Workshop code generated for Simulink model DbwMgm.
 *
 * Model version                        : 1.705
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Wed Oct 24 13:36:13 2012
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Wed Oct 24 13:36:15 2012
 */
#ifndef RTW_HEADER_DbwMgm_h_
#define RTW_HEADER_DbwMgm_h_
#ifndef DbwMgm_COMMON_INCLUDES_
# define DbwMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "diagmgm_out.h"
#include "mathlib.h"
#endif                                 /* DbwMgm_COMMON_INCLUDES_ */

#include "DbwMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "dbw_mgm.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

#define BKANGTHRERR_dim                8U                        /* Length of BKANGTHRERR */
#define BKANGTHROBJ_dim                5U                        /* Length of BKANGTHROBJ */
#define BKVBATANGSLOPE_dim             4U                        /* Length of BKVBATANGSLOPE */

/* Block signals for system '<S35>/minangerr_select' */
typedef struct {
  int16_T Switch;                      /* '<S40>/Switch' */
} rtB_minangerr_select_DbwMgm;

/* Block signals for system '<S35>/minangerr_select1' */
typedef struct {
  int16_T Switch;                      /* '<S41>/Switch' */
} rtB_minangerr_select1_DbwMgm;

/* Block signals (auto storage) */
typedef struct {
  int16_T AngThr_dq42;                 /* '<S57>/Add1' */
  int16_T AngThrObj_gm0w;              /* '<S59>/Add2' */
  rtB_minangerr_select1_DbwMgm minangerr_select2;/* '<S35>/minangerr_select2' */
  rtB_minangerr_select1_DbwMgm minangerr_select1;/* '<S35>/minangerr_select1' */
  rtB_minangerr_select_DbwMgm minangerr_select;/* '<S35>/minangerr_select' */
} BlockIO_DbwMgm;

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  uint32_T FixPtUnitDelay1_DSTATE;     /* '<S80>/FixPt Unit Delay1' */
  int32_T AngThrDiagHiR;               /* '<Root>/_DataStoreBlk_7' */
  int16_T IntegerDelay_DWORK1[2];      /* '<S30>/Integer Delay' */
  int16_T UnitDelay_DSTATE;            /* '<S30>/Unit Delay' */
  uint8_T FixPtUnitDelay2_DSTATE;      /* '<S80>/FixPt Unit Delay2' */
} D_Work_DbwMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_DbwMgm_T5ms_Trig_ZCE;  /* '<S1>/fc_DbwMgm_T5ms' */
  ZCSigState fc_DbwMgm_Init_Trig_ZCE;  /* '<S1>/fc_DbwMgm_Init' */
} PrevZCSigStates_DbwMgm;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T5ms;                     /* '<Root>/ev_T5ms' */
} ExternalInputs_DbwMgm;

/* Block signals (auto storage) */
extern BlockIO_DbwMgm DbwMgm_B;

/* Block states (auto storage) */
extern D_Work_DbwMgm DbwMgm_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_DbwMgm DbwMgm_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  RTW declares the memory for these signals
 * and exports their symbols.
 *
 */
extern uint32_T DbwMgmTimer;           /* '<Root>/DbwMgmTimer' */

/* Model entry point functions */
extern void DbwMgm_initialize(void);
extern void DbwMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('dbwmgm_gen/DbwMgm')    - opens subsystem dbwmgm_gen/DbwMgm
 * hilite_system('dbwmgm_gen/DbwMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : dbwmgm_gen
 * '<S1>'   : dbwmgm_gen/DbwMgm
 * '<S15>'  : dbwmgm_gen/DbwMgm/Init
 * '<S16>'  : dbwmgm_gen/DbwMgm/T5ms
 * '<S17>'  : dbwmgm_gen/DbwMgm/fc_DbwMgm_Init
 * '<S18>'  : dbwmgm_gen/DbwMgm/fc_DbwMgm_T5ms
 * '<S19>'  : dbwmgm_gen/DbwMgm/T5ms/ActiveDiag
 * '<S20>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop
 * '<S21>'  : dbwmgm_gen/DbwMgm/T5ms/DbwMgm
 * '<S22>'  : dbwmgm_gen/DbwMgm/T5ms/Disabled
 * '<S23>'  : dbwmgm_gen/DbwMgm/T5ms/ForceLH
 * '<S24>'  : dbwmgm_gen/DbwMgm/T5ms/HBLogicEnable
 * '<S25>'  : dbwmgm_gen/DbwMgm/T5ms/SelfLearning
 * '<S26>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond
 * '<S27>'  : dbwmgm_gen/DbwMgm/T5ms/TestDisLoads
 * '<S28>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/ErrorPositionDiagnosis
 * '<S29>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator
 * '<S30>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics
 * '<S31>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation
 * '<S32>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/ErrorPositionDiagnosis/AngIntErr_Subsystem
 * '<S33>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/ErrorPositionDiagnosis/Diagnosis_Subsystem
 * '<S34>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/ErrorPositionDiagnosis/Diagnosis_Subsystem/DiagMgm_SetDiagState
 * '<S35>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem
 * '<S36>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/Look-up 2D VTGainKPDBW
 * '<S37>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/LookUp_S16_S16
 * '<S38>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/Saturation Dynamic
 * '<S39>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/Dead Zone Dynamic
 * '<S40>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/minangerr_select
 * '<S41>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/minangerr_select1
 * '<S42>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/minangerr_select2
 * '<S43>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/minangerr_select/MinAngErr_calculation
 * '<S44>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/minangerr_select1/MinAngErr_calculation
 * '<S45>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/AngThrErr Subsystem/minangerr_select2/MinAngErr_calculation
 * '<S46>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/Look-up 2D VTGainKPDBW/LookUp_IR_U16
 * '<S47>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/Look-up 2D VTGainKPDBW/PreLookUpIdSearch_S16
 * '<S48>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/Look-up 2D VTGainKPDBW/LookUp_IR_U16/Data Type Conversion Inherited3
 * '<S49>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/PID_Regulator/LookUp_S16_S16/Data Type Conversion Inherited3
 * '<S50>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/FOF_Reset_S16_FXP
 * '<S51>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/LookUp_IR_S1
 * '<S52>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/LookUp_IR_S16
 * '<S53>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/PreLookUpIdSearch_U2
 * '<S54>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/FOF_Reset_S16_FXP/Data Type Conversion Inherited1
 * '<S55>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/LookUp_IR_S1/Data Type Conversion Inherited3
 * '<S56>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/SysDynamics/LookUp_IR_S16/Data Type Conversion Inherited3
 * '<S57>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/AngThrCorrObjF_Calculation
 * '<S58>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Dbw_Target_Saturation
 * '<S59>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter
 * '<S60>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/AngThrCorrObjF_Calculation/FOF_Reset_S16_FXP
 * '<S61>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/AngThrCorrObjF_Calculation/FOF_Reset_S16_FXP/Data Type Conversion Inherited1
 * '<S62>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Dbw_Target_Saturation/Saturation Dynamic1
 * '<S63>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S1
 * '<S64>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S16
 * '<S65>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S2
 * '<S66>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S3
 * '<S67>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/PreLookUpIdSearch_U2
 * '<S68>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/Saturation Dynamic
 * '<S69>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S1/Data Type Conversion Inherited3
 * '<S70>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S16/Data Type Conversion Inherited3
 * '<S71>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S2/Data Type Conversion Inherited3
 * '<S72>'  : dbwmgm_gen/DbwMgm/T5ms/ClosedLoop/Target_Calculation/Rate_Limiter/LookUp_IR_S3/Data Type Conversion Inherited3
 * '<S73>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/Compare To Constant1
 * '<S74>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/Compare To Zero
 * '<S75>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/Compare To Zero1
 * '<S76>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/Compare To Zero2
 * '<S77>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/Compare To Zero3
 * '<S78>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/FlgRpmDisable_Subsystem
 * '<S79>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/FlgRpmDisable_Subsystem/Compare To Zero
 * '<S80>'  : dbwmgm_gen/DbwMgm/T5ms/TestCond/FlgRpmDisable_Subsystem/Unit Delay External IC
 */

/*
 * Requirements for '<Root>' : DbwMgm
 */
#endif                                 /* RTW_HEADER_DbwMgm_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
