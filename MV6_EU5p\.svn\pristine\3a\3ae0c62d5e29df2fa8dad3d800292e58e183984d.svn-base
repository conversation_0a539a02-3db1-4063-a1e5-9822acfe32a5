/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/SAF3#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1638   $                                                                                          */
/* $Date:: 2009-09-08 09:52:41 +0200 (mar, 08 set 2009)   $                                                      */
/* $Author:: gelmettia               $                                                                       */
/*****************************************************************************************************************/

#ifndef _SAF3_H_
#define _SAF3_H_

#include "sys.h"
#include "typedefs.h"
#include "analogin.h"

#define S3_MAX_RND_NUMBER       (16)
#define S3_COMPLETE_LAP         ((uint16_t)(0xFFFF))

#define SMP_ERASE_TIME          (500)
#define SMP_ERASE_FINISHED      (0xAA)

/* MMP S3 State */
#define S3_LOAD_TEST            (0)
#define S3_INIT                 (1)
#define S3_WAIT_RND_CODE_00     (2)
#define S3_WAIT_SMP_FW          (3)
#define S3_W_START_SEQ_ACK      (4)
#define S3_TX_ANSW_RND_CODE     (5)
#define S3_W_RND_CODE           (6)
#define S3_SMP_STOP_CAUSE       (7)
#define S3_READ_AD_CH           (8)
#define S3_WAIT_KEY_OFF         (9)
#define S3_PWLATCH              (10)
#define S3_ACT_SMP              (11)
#define S3_WAIT_APPL_SMP        (12)
/* S3 SMP Boot Layer states */
#define S3_WAIT_BOOT_SMP        (13)
#define S3_MMP_WAIT_CRC_CODE    (14)
#define S3_TX_APPL_ADDRESS      (15)
#define S3_SMP_APPL_CORRECT     (16)
#define S3_TX_T3_MSG            (17)
#define S3_WAIT                 (18)
#define S3_BOOT_RSTOFF          (19)
#define S3_WAIT_SMP_ERASE       (20)
#define S3_SMP_END_OF_DOWNLOAD  (21)
#define S3_SMP_READY            (22)
#define S3_BOOT_RSTON           (23)
#ifdef _SMP_DUMMY_APPLICATION_
#define S3_SMP_DUMMY_LOOP       (24)
#endif //_SMP_DUMMY_APPLICATION_
#define S3_ERASE_SMP            (25)
#define S3_WAIT_SMP_RESET       (26)
#define S3_LOW_BAT              (27)

#define S3_REQ_AD_CH0_ACQ       0
#define S3_REQ_AD_CH1_ACQ       1

/* Load test states */
#define S3_MMP_EN_FDBK          (15)
#define S3_MMP_DIS_FDBK         (16)
#define S3_MMP_DIS_2_FDBK       (17)

#define S3_NONE                 (21)

/* Dis/En Flags */
/* AD CHANNEL READ STATES */
#define CH_0    1
#define CH_ALL  2

/* THR Steady calc */
#define SAF3_MAX_RPM 20000

/* SMP ADC rescale factor */
#define S3SADCRESC_RES        14
//#define S3SMPADC_RESCALE    31457U  //valore indicato sulle specs <<15 //Modificato nelle specs. v2.1
#if (S3SADCRESC_RES > 10)
 #if (BOARD_TYPE == BOARD_5)
//  #define S3SMPADC_RESCALE(x)      ((250<<(S3SADCRESC_RES - 10)*x)/150)    //((250*2^S3SADCRESC_RES)/(1024*150))
//  #define S3SMPADC_RESCALE      (250<<(S3SADCRESC_RES - 10))/150    //((250*2^S3SADCRESC_RES)/(1024*150))
  #define S3SMPADC_RESCALE(x)      (((uint32_T)x*(1707<<(S3SADCRESC_RES - 10)))>>10)    //((250*2^S3SADCRESC_RES)/(1024*150))
 #else
//  #define S3SMPADC_RESCALE      (1<<(S3SADCRESC_RES - 10)) /* Caso con Vkam = 5000 mV */
  #define S3SMPADC_RESCALE(x)      ((uint32_T)x<<(S3SADCRESC_RES - 10)) /* Caso con Vkam = 5000 mV */
 #endif
#else
 #error "S3SADCRESC_RES must be >= 10"
#endif

#define S3FKOSTO 60 /* n*10 [ms] */

/* Safety 23 answer defs */
#define ANSW_RIGHT      170   /* Safety 23 right answer */
#define ANSW_WRONG       0   /* Safety 23 wrong answer */

#define SAF2MOD_SET_0    0   /* Inputs set 0 for safety 23 */
#define SAF2MOD_SET_1    1   /* Inputs set 1 for safety 23 */
#define SAF2MOD_SET_2    2   /* Inputs set 2 for safety 23 */

/* Safety 3 flag value */
#define SMP_CRC_CHECK    (1)
#define NORMAL_S3_OP     (2)

/**************** MACRO ****************/
#define NO_CAUSE      (0)
#define MAX_ERR_CNT         (100) 
#define MAX_SPI_COMM_ERR    (101)

/**************** PROTOTYPES ****************/
#ifndef _SMP_DUMMY_APPLICATION_
void Saf3Mgm_ADRead(void);
void Saf3Mgm_S3INIT_State(void);
void Saf3Mgm_AnswerMgm(void);
void Saf3Mgm_S3WAITRNDCODE00_State(void);
void Saf3Mgm_S3WAITSMPFW_State(void);
void Saf3Mgm_S3WAITSTARTSEQACK_State(void);
void Saf3Mgm_S3TXANSWRNDCODE_State(void);
void Saf3Mgm_S3WAITRNDCODE_State(void);
void Saf3Mgm_WAITSMPSTOPCAUSE_State(void);
void Saf3Mgm_S3READADCH_State(void);
void Saf3Mgm_S3WAITKEYOFF_State(void);
void Saf3ADRead_REQADCH0ACQ_State(void);
void Saf3ADRead_REQADCH1ACQ_State(void);
void Saf3Mgm_AnswerMgm(void);
void Saf3Mgm_S3LOADTEST_State(void);
static void Saf3WaitSMPReset(uint8_t StopCause);
static void Check_Code(void);
void Saf3Mgm_S3WAITAPPLSMP_State(void);
#endif /* _SMP_DUMMY_APPLICATION_ */

void Saf3Mgm_S3PWLATCH_State(void);
void Saf3Mgm_S3LOWBAT_State(void);
void Saf3Mgm_S3ACTSMP_State(void);
void Saf3Mgm_S3WAITBOOTSMP_State(void);
void Saf3Mgm_S3MMPWAITCRCCODE_State(void);
void Saf3Mgm_S3ERASESMP_State(void);
void Saf3Mgm_S3SMPAPPLCORRECT_State(void);
void Saf3Mgm_S3WAITSMPERASE_State(void);
void Saf3Mgm_S3SMPREADY_State(void);
void Saf3Mgm_S3TXT3MSG_State(void);
void Saf3Mgm_S3WAIT_State(void);
void Saf3Mgm_S3SMPENDOFDOWNLOAD_State(void);
void Saf3Mgm_S3BOOTRSTOFF_State(void);
void Saf3Mgm_S3BOOTRSTON_State(void);

void Saf3Mgm_100ms(void);
void Saf3Mgm_10ms(void);
void Saf3Mgm_5ms(void);
void Saf3Mgm_MMPStateMachine(void);
void Saf3Mgm_T10ms(void);
void Saf3Mgm_T100ms(void);
void Saf3Mgm_Init(void);
void Saf3Mgm_ApplicationOn(void);
void Saf3Mgm_ReadFDB(void);
void Saf3Mgm_DISLoads(void);
void Saf3Mgm_SPI_ST_L9958Cmd(void);
int16_T Saf3Mgm_SPI_ABETLE6244X(void);

static void Saf3EngineStop(uint8_t StopCause);

/**************** STRUCT ****************/
typedef struct {
  uint16_T Memory_PreviousInput;        /* '<S29>/Memory' */
  uint16_T Memory1_PreviousInput;       /* '<S29>/Memory1' */
  uint8_T Memory_PreviousInput_e;       /* '<S17>/Memory' */
} D_Work_Saf3Mgm;

#endif /* _SAF3_H_ */
