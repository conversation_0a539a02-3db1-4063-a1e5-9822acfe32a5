/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _CAN_H_
#define _CAN_H_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define SPICAN_CH   20 
    
#define CAN_TX          1
#define CAN_RX          0
    
     
#define TX_BUFFER_CODE_INACTIVE         0x08
#define RX_BUFFER_CODE_DISABLE          0x00
#define TX_BUFFER_CODE_TRANSMIT         0x0C
#define RX_BUFFER_CODE_ACTIVE_EMPTY     0x04
    
    
    
#define CAN_DISABLE    1
#define CAN_ENABLE     0
    
    // ap: removed
    // #define NBUF_PER_CHAN          64
#define MAX_DLC_DIM            8
#define BIT_RATE_ERROR         0x02
#define NOK_MODULE_DISABLE     0x03
#define NOK_MODULE_ENABLE      0x04
#define CAN_BUSOFF             0x05
#define CAN_ERR_ACTIVE         0x09
#define CAN_ERR_PASSIVE        0x0A
#define CAN_TX_BUSY            0x06
#define CAN_RX_BUSY            0x07
#define CAN_RX_BUFFER_EMPTY    0x08
#define CAN_RX_BUFFER_OVERRUN  0x0B
#define CAN_RX_CNT_ERR         0x0E
#define CAN_RX_CRC_ERR         0x0F
#define CAN_RX_SKIP            0x0D
#define BUFFER_FULL            0x1
#define BUFFER_OVERRUN         0x2

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
#define CAN_BUF(n,x,y)\
uint8_t buff_status_##n;\
uint8_t r_##n;\
uint8_t w_##n;\
CAN_buffer buffer_##n##[x] ;
   
#define INIT_BS(ch,n)           CAN_CH##ch##_b.buff_status_##n=0;
#define UPDATE_BS(ch,n,v)       CAN_CH##ch##_b.buff_status_##n=v
#define BS_ID(ch,n)             (CAN_CH##ch##_b.buff_status_##n)
#define IS_BUFFER_OVERRUN(ch,n) (BS_ID(ch,n)& BUFFER_OVERRUN) 
#define IS_BUFFER_FULL(ch,n)    (BS_ID(ch,n)& BUFFER_FULL)  


#define READ_ID(ch,n)       (CAN_CH##ch##_b.r_##n)
#define WRITE_ID(ch,n)      (CAN_CH##ch##_b.w_##n)

///////////////////////////////////////////////////////////////
// AM - funzioni di storing dei puntatori R/W nella struttura virtuale del CAN
#define DUMP_R_PTR(value,ch,n)  value=CAN_CH##ch##_b.r_##n;
#define DUMP_W_PTR(value,ch,n)  value=CAN_CH##ch##_b.w_##n;
///////////////////////////////////////////////////////////////

#define MEM_WRITE(ch,n)    {uint8_t i;\
                            for (i=0;i<CAN_CH##ch##_BUF##n##_DLC;i++) {\
                              CAN_CH##ch##_b.buffer_##n##[CAN_CH##ch##_b.w_##n].b[i]=\
                              CAN_##ch##.BUF[n].DATA.B[i];\
                            }\
                            CAN_CH##ch##_b.buffer_##n##[CAN_CH##ch##_b.w_##n].ide=CAN_##ch##.BUF[n].CS.B.IDE;\
                            if (CAN_##ch##.BUF[n].CS.B.IDE==1){\
                            CAN_CH##ch##_b.buffer_##n##[CAN_CH##ch##_b.w_##n].id=CAN_##ch##.BUF[n].ID.R;\
                            }\
                            else{\
                            CAN_CH##ch##_b.buffer_##n##[CAN_CH##ch##_b.w_##n].id=CAN_##ch##.BUF[n].ID.B.STD_ID;\
                            }\
                            CAN_CH##ch##_b.buffer_##n##[CAN_CH##ch##_b.w_##n].dlc=CAN_##ch##.BUF[n].CS.B.LENGTH;\
                            }



#define UPDATE_WR_ID(ch,n)  WRITE_ID(ch,n) +=1; if (WRITE_ID(ch,n)==CAN_CH##ch##_BUF##n##_NUM){ WRITE_ID(ch,n)=0;}
#define UPDATE_RD_ID(ch,n)  READ_ID(ch,n) +=1; if (READ_ID(ch,n)==CAN_CH##ch##_BUF##n##_NUM) {READ_ID(ch,n)=0;}
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef  struct CAN_buff{
  uint8_t b[MAX_DLC_DIM];
  uint8_t ide;
  uint32_t id;
  uint8_t dlc;
  }CAN_buffer;

#if CAN_CHA_EN
typedef struct {
#if defined(CAN_CHA_BUF0_DIR) && (CAN_CHA_BUF0_DIR==CAN_RX)
#ifdef CAN_CHA_BUF0_NUM 
  #ifdef CAN_CHA_BUF0_DLC
    CAN_BUF(0,CAN_CHA_BUF0_NUM,CAN_CHA_BUF0_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF1_DIR==CAN_RX)
#ifdef CAN_CHA_BUF1_NUM
  #ifdef CAN_CHA_BUF1_DLC
    CAN_BUF(1,CAN_CHA_BUF1_NUM,CAN_CHA_BUF1_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF2_DIR==CAN_RX)
#ifdef CAN_CHA_BUF2_NUM
  #ifdef CAN_CHA_BUF2_DLC
    CAN_BUF(2,CAN_CHA_BUF2_NUM,CAN_CHA_BUF2_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF3_DIR==CAN_RX)
#ifdef CAN_CHA_BUF3_NUM
  #ifdef CAN_CHA_BUF3_DLC
    CAN_BUF(3,CAN_CHA_BUF3_NUM,CAN_CHA_BUF3_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF4_DIR==CAN_RX)
#ifdef CAN_CHA_BUF4_NUM
  #ifdef CAN_CHA_BUF4_DLC
    CAN_BUF(4,CAN_CHA_BUF4_NUM,CAN_CHA_BUF4_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF5_DIR==CAN_RX)
#ifdef CAN_CHA_BUF5_NUM
  #ifdef CAN_CHA_BUF5_DLC
    CAN_BUF(5,CAN_CHA_BUF5_NUM,CAN_CHA_BUF5_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF6_DIR==CAN_RX)
#ifdef CAN_CHA_BUF6_NUM
  #ifdef CAN_CHA_BUF6_DLC
    CAN_BUF(6,CAN_CHA_BUF6_NUM,CAN_CHA_BUF6_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF7_DIR==CAN_RX)
#ifdef CAN_CHA_BUF7_NUM
  #ifdef CAN_CHA_BUF7_DLC
    CAN_BUF(7,CAN_CHA_BUF7_NUM,CAN_CHA_BUF7_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF8_DIR==CAN_RX)
#ifdef CAN_CHA_BUF8_NUM
  #ifdef CAN_CHA_BUF8_DLC
    CAN_BUF(8,CAN_CHA_BUF8_NUM,CAN_CHA_BUF8_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF9_DIR==CAN_RX)
#ifdef CAN_CHA_BUF9_NUM
  #ifdef CAN_CHA_BUF9_DLC
    CAN_BUF(9,CAN_CHA_BUF9_NUM,CAN_CHA_BUF9_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF10_DIR==CAN_RX)
#ifdef CAN_CHA_BUF10_NUM
  #ifdef CAN_CHA_BUF10_DLC
    CAN_BUF(10,CAN_CHA_BUF10_NUM,CAN_CHA_BUF10_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF11_DIR==CAN_RX)
#ifdef CAN_CHA_BUF11_NUM
  #ifdef CAN_CHA_BUF11_DLC
    CAN_BUF(11,CAN_CHA_BUF11_NUM,CAN_CHA_BUF11_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF12_DIR==CAN_RX)
#ifdef CAN_CHA_BUF12_NUM
  #ifdef CAN_CHA_BUF12_DLC
    CAN_BUF(12,CAN_CHA_BUF12_NUM,CAN_CHA_BUF12_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF13_DIR==CAN_RX)
#ifdef CAN_CHA_BUF13_NUM
  #ifdef CAN_CHA_BUF13_DLC
    CAN_BUF(13,CAN_CHA_BUF13_NUM,CAN_CHA_BUF13_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF14_DIR==CAN_RX)
#ifdef CAN_CHA_BUF14_NUM
  #ifdef CAN_CHA_BUF14_DLC
    CAN_BUF(14,CAN_CHA_BUF14_NUM,CAN_CHA_BUF14_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF15_DIR==CAN_RX)
#ifdef CAN_CHA_BUF15_NUM
  #ifdef CAN_CHA_BUF15_DLC
    CAN_BUF(15,CAN_CHA_BUF15_NUM,CAN_CHA_BUF15_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF16_DIR==CAN_RX)
#ifdef CAN_CHA_BUF16_NUM
  #ifdef CAN_CHA_BUF16_DLC
    CAN_BUF(16,CAN_CHA_BUF16_NUM,CAN_CHA_BUF16_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF17_DIR==CAN_RX)
#ifdef CAN_CHA_BUF17_NUM
  #ifdef CAN_CHA_BUF17_DLC
    CAN_BUF(17,CAN_CHA_BUF17_NUM,CAN_CHA_BUF17_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF18_DIR==CAN_RX)
#ifdef CAN_CHA_BUF18_NUM
  #ifdef CAN_CHA_BUF18_DLC
    CAN_BUF(18,CAN_CHA_BUF18_NUM,CAN_CHA_BUF18_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF19_DIR==CAN_RX)
#ifdef CAN_CHA_BUF19_NUM
  #ifdef CAN_CHA_BUF19_DLC
    CAN_BUF(19,CAN_CHA_BUF19_NUM,CAN_CHA_BUF19_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF20_DIR==CAN_RX)
#ifdef CAN_CHA_BUF20_NUM
  #ifdef CAN_CHA_BUF20_DLC
    CAN_BUF(20,CAN_CHA_BUF20_NUM,CAN_CHA_BUF20_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF21_DIR==CAN_RX)
#ifdef CAN_CHA_BUF21_NUM
  #ifdef CAN_CHA_BUF21_DLC
    CAN_BUF(21,CAN_CHA_BUF21_NUM,CAN_CHA_BUF21_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF22_DIR==CAN_RX)
#ifdef CAN_CHA_BUF22_NUM
  #ifdef CAN_CHA_BUF22_DLC
    CAN_BUF(22,CAN_CHA_BUF22_NUM,CAN_CHA_BUF22_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF23_DIR==CAN_RX)
#ifdef CAN_CHA_BUF23_NUM
  #ifdef CAN_CHA_BUF23_DLC
    CAN_BUF(23,CAN_CHA_BUF23_NUM,CAN_CHA_BUF23_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF24_DIR==CAN_RX)
#ifdef CAN_CHA_BUF24_NUM
  #ifdef CAN_CHA_BUF24_DLC
    CAN_BUF(24,CAN_CHA_BUF24_NUM,CAN_CHA_BUF24_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF25_DIR==CAN_RX)
#ifdef CAN_CHA_BUF25_NUM
  #ifdef CAN_CHA_BUF25_DLC
    CAN_BUF(25,CAN_CHA_BUF25_NUM,CAN_CHA_BUF25_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF26_DIR==CAN_RX)
#ifdef CAN_CHA_BUF26_NUM
  #ifdef CAN_CHA_BUF26_DLC
    CAN_BUF(26,CAN_CHA_BUF26_NUM,CAN_CHA_BUF26_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF27_DIR==CAN_RX)
#ifdef CAN_CHA_BUF27_NUM
  #ifdef CAN_CHA_BUF27_DLC
    CAN_BUF(27,CAN_CHA_BUF27_NUM,CAN_CHA_BUF27_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF28_DIR==CAN_RX)
#ifdef CAN_CHA_BUF28_NUM
  #ifdef CAN_CHA_BUF28_DLC
    CAN_BUF(28,CAN_CHA_BUF28_NUM,CAN_CHA_BUF28_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF29_DIR==CAN_RX)
#ifdef CAN_CHA_BUF29_NUM
  #ifdef CAN_CHA_BUF29_DLC
    CAN_BUF(29,CAN_CHA_BUF29_NUM,CAN_CHA_BUF29_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF30_DIR==CAN_RX)
#ifdef CAN_CHA_BUF30_NUM
  #ifdef CAN_CHA_BUF30_DLC
    CAN_BUF(30,CAN_CHA_BUF30_NUM,CAN_CHA_BUF30_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF31_DIR==CAN_RX)
#ifdef CAN_CHA_BUF31_NUM
  #ifdef CAN_CHA_BUF31_DLC
    CAN_BUF(31,CAN_CHA_BUF31_NUM,CAN_CHA_BUF31_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF32_DIR==CAN_RX)
#ifdef CAN_CHA_BUF32_NUM
  #ifdef CAN_CHA_BUF32_DLC
    CAN_BUF(32,CAN_CHA_BUF32_NUM,CAN_CHA_BUF32_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF33_DIR==CAN_RX)
#ifdef CAN_CHA_BUF33_NUM
  #ifdef CAN_CHA_BUF33_DLC
    CAN_BUF(33,CAN_CHA_BUF33_NUM,CAN_CHA_BUF33_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF34_DIR==CAN_RX)
#ifdef CAN_CHA_BUF34_NUM
  #ifdef CAN_CHA_BUF34_DLC
    CAN_BUF(34,CAN_CHA_BUF34_NUM,CAN_CHA_BUF34_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF35_DIR==CAN_RX)
#ifdef CAN_CHA_BUF35_NUM
  #ifdef CAN_CHA_BUF35_DLC
    CAN_BUF(35,CAN_CHA_BUF35_NUM,CAN_CHA_BUF35_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF36_DIR==CAN_RX)
#ifdef CAN_CHA_BUF36_NUM
  #ifdef CAN_CHA_BUF36_DLC
    CAN_BUF(36,CAN_CHA_BUF36_NUM,CAN_CHA_BUF36_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF37_DIR==CAN_RX)
#ifdef CAN_CHA_BUF37_NUM
  #ifdef CAN_CHA_BUF37_DLC
    CAN_BUF(37,CAN_CHA_BUF37_NUM,CAN_CHA_BUF37_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF38_DIR==CAN_RX)
#ifdef CAN_CHA_BUF38_NUM
  #ifdef CAN_CHA_BUF38_DLC
    CAN_BUF(38,CAN_CHA_BUF38_NUM,CAN_CHA_BUF38_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF39_DIR==CAN_RX)
#ifdef CAN_CHA_BUF39_NUM
  #ifdef CAN_CHA_BUF39_DLC
    CAN_BUF(39,CAN_CHA_BUF39_NUM,CAN_CHA_BUF39_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF40_DIR==CAN_RX)
#ifdef CAN_CHA_BUF40_NUM
  #ifdef CAN_CHA_BUF40_DLC
    CAN_BUF(40,CAN_CHA_BUF40_NUM,CAN_CHA_BUF40_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF41_DIR==CAN_RX)
#ifdef CAN_CHA_BUF41_NUM
  #ifdef CAN_CHA_BUF41_DLC
    CAN_BUF(41,CAN_CHA_BUF41_NUM,CAN_CHA_BUF41_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF42_DIR==CAN_RX)
#ifdef CAN_CHA_BUF42_NUM
  #ifdef CAN_CHA_BUF42_DLC
    CAN_BUF(42,CAN_CHA_BUF42_NUM,CAN_CHA_BUF42_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF43_DIR==CAN_RX)
#ifdef CAN_CHA_BUF43_NUM
  #ifdef CAN_CHA_BUF43_DLC
    CAN_BUF(43,CAN_CHA_BUF43_NUM,CAN_CHA_BUF43_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF44_DIR==CAN_RX)
#ifdef CAN_CHA_BUF44_NUM
  #ifdef CAN_CHA_BUF44_DLC
    CAN_BUF(44,CAN_CHA_BUF44_NUM,CAN_CHA_BUF44_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF45_DIR==CAN_RX)
#ifdef CAN_CHA_BUF45_NUM
  #ifdef CAN_CHA_BUF45_DLC
    CAN_BUF(45,CAN_CHA_BUF45_NUM,CAN_CHA_BUF45_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF46_DIR==CAN_RX)
#ifdef CAN_CHA_BUF46_NUM
  #ifdef CAN_CHA_BUF46_DLC
    CAN_BUF(46,CAN_CHA_BUF46_NUM,CAN_CHA_BUF46_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF46_DIR==CAN_RX)
#ifdef CAN_CHA_BUF47_NUM
  #ifdef CAN_CHA_BUF47_DLC
    CAN_BUF(47,CAN_CHA_BUF47_NUM,CAN_CHA_BUF47_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF48_DIR==CAN_RX)
#ifdef CAN_CHA_BUF48_NUM
  #ifdef CAN_CHA_BUF48_DLC
    CAN_BUF(48,CAN_CHA_BUF48_NUM,CAN_CHA_BUF48_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF49_DIR==CAN_RX)
#ifdef CAN_CHA_BUF49_NUM
  #ifdef CAN_CHA_BUF49_DLC
    CAN_BUF(49,CAN_CHA_BUF49_NUM,CAN_CHA_BUF49_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF50_DIR==CAN_RX)
#ifdef CAN_CHA_BUF50_NUM
  #ifdef CAN_CHA_BUF50_DLC
    CAN_BUF(50,CAN_CHA_BUF50_NUM,CAN_CHA_BUF50_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF51_DIR==CAN_RX)
#ifdef CAN_CHA_BUF51_NUM
  #ifdef CAN_CHA_BUF51_DLC
    CAN_BUF(51,CAN_CHA_BUF51_NUM,CAN_CHA_BUF51_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF52_DIR==CAN_RX)
#ifdef CAN_CHA_BUF52_NUM
  #ifdef CAN_CHA_BUF52_DLC
    CAN_BUF(52,CAN_CHA_BUF52_NUM,CAN_CHA_BUF52_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF53_DIR==CAN_RX)
#ifdef CAN_CHA_BUF53_NUM
  #ifdef CAN_CHA_BUF53_DLC
    CAN_BUF(53,CAN_CHA_BUF53_NUM,CAN_CHA_BUF53_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF54_DIR==CAN_RX)
#ifdef CAN_CHA_BUF54_NUM
  #ifdef CAN_CHA_BUF54_DLC
    CAN_BUF(54,CAN_CHA_BUF54_NUM,CAN_CHA_BUF54_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF55_DIR==CAN_RX)
#ifdef CAN_CHA_BUF55_NUM
  #ifdef CAN_CHA_BUF55_DLC
    CAN_BUF(55,CAN_CHA_BUF55_NUM,CAN_CHA_BUF55_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF56_DIR==CAN_RX)
#ifdef CAN_CHA_BUF56_NUM
  #ifdef CAN_CHA_BUF56_DLC
    CAN_BUF(56,CAN_CHA_BUF56_NUM,CAN_CHA_BUF56_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF57_DIR==CAN_RX)
#ifdef CAN_CHA_BUF57_NUM
  #ifdef CAN_CHA_BUF57_DLC
    CAN_BUF(57,CAN_CHA_BUF57_NUM,CAN_CHA_BUF57_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF58_DIR==CAN_RX)
#ifdef CAN_CHA_BUF58_NUM
  #ifdef CAN_CHA_BUF58_DLC
    CAN_BUF(58,CAN_CHA_BUF58_NUM,CAN_CHA_BUF58_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF59_DIR==CAN_RX)
#ifdef CAN_CHA_BUF59_NUM
  #ifdef CAN_CHA_BUF59_DLC
    CAN_BUF(59,CAN_CHA_BUF59_NUM,CAN_CHA_BUF59_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF60_DIR==CAN_RX)
#ifdef CAN_CHA_BUF60_NUM
  #ifdef CAN_CHA_BUF60_DLC
    CAN_BUF(60,CAN_CHA_BUF60_NUM,CAN_CHA_BUF60_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF61_DIR==CAN_RX)
#ifdef CAN_CHA_BUF61_NUM
  #ifdef CAN_CHA_BUF61_DLC
    CAN_BUF(61,CAN_CHA_BUF61_NUM,CAN_CHA_BUF61_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF62_DIR==CAN_RX)
#ifdef CAN_CHA_BUF62_NUM
  #ifdef CAN_CHA_BUF62_DLC
    CAN_BUF(62,CAN_CHA_BUF62_NUM,CAN_CHA_BUF62_DLC)
  #endif
#endif
#endif
#if (CAN_CHA_BUF63_DIR==CAN_RX)
#ifdef CAN_CHA_BUF63_NUM
  #ifdef CAN_CHA_BUF63_DLC
    CAN_BUF(63,CAN_CHA_BUF63_NUM,CAN_CHA_BUF63_DLC)
  #endif
#endif
#endif
} t_CAN_CHA_b;
#endif


/* AM - CAN channel B not defined on this target!!! */

#if CAN_CHC_EN
typedef struct {
#if (CAN_CHC_BUF0_DIR==CAN_RX)
#ifdef CAN_CHC_BUF0_NUM
  #ifdef CAN_CHC_BUF0_DLC
    CAN_BUF(0,CAN_CHC_BUF0_NUM,CAN_CHC_BUF0_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF1_DIR==CAN_RX)
#ifdef CAN_CHC_BUF1_NUM
  #ifdef CAN_CHC_BUF1_DLC
    CAN_BUF(1,CAN_CHC_BUF1_NUM,CAN_CHC_BUF1_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF2_DIR==CAN_RX)
#ifdef CAN_CHC_BUF2_NUM
  #ifdef CAN_CHC_BUF2_DLC
    CAN_BUF(2,CAN_CHC_BUF2_NUM,CAN_CHC_BUF2_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF3_DIR==CAN_RX)
#ifdef CAN_CHC_BUF3_NUM
  #ifdef CAN_CHC_BUF3_DLC
    CAN_BUF(3,CAN_CHC_BUF3_NUM,CAN_CHC_BUF3_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF4_DIR==CAN_RX)
#ifdef CAN_CHC_BUF4_NUM
  #ifdef CAN_CHC_BUF4_DLC
    CAN_BUF(4,CAN_CHC_BUF4_NUM,CAN_CHC_BUF4_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF5_DIR==CAN_RX)
#ifdef CAN_CHC_BUF5_NUM
  #ifdef CAN_CHC_BUF5_DLC
    CAN_BUF(5,CAN_CHC_BUF5_NUM,CAN_CHC_BUF5_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF6_DIR==CAN_RX)
#ifdef CAN_CHC_BUF6_NUM
  #ifdef CAN_CHC_BUF6_DLC
    CAN_BUF(6,CAN_CHC_BUF6_NUM,CAN_CHC_BUF6_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF7_DIR==CAN_RX)
#ifdef CAN_CHC_BUF7_NUM
  #ifdef CAN_CHC_BUF7_DLC
    CAN_BUF(7,CAN_CHC_BUF7_NUM,CAN_CHC_BUF7_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF8_DIR==CAN_RX)
#ifdef CAN_CHC_BUF8_NUM
  #ifdef CAN_CHC_BUF8_DLC
    CAN_BUF(8,CAN_CHC_BUF8_NUM,CAN_CHC_BUF8_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF9_DIR==CAN_RX)
#ifdef CAN_CHC_BUF9_NUM
  #ifdef CAN_CHC_BUF9_DLC
    CAN_BUF(9,CAN_CHC_BUF9_NUM,CAN_CHC_BUF9_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF10_DIR==CAN_RX)
#ifdef CAN_CHC_BUF10_NUM
  #ifdef CAN_CHC_BUF10_DLC
    CAN_BUF(10,CAN_CHC_BUF10_NUM,CAN_CHC_BUF10_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF11_DIR==CAN_RX)
#ifdef CAN_CHC_BUF11_NUM
  #ifdef CAN_CHC_BUF11_DLC
    CAN_BUF(11,CAN_CHC_BUF11_NUM,CAN_CHC_BUF11_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF12_DIR==CAN_RX)
#ifdef CAN_CHC_BUF12_NUM
  #ifdef CAN_CHC_BUF12_DLC
    CAN_BUF(12,CAN_CHC_BUF12_NUM,CAN_CHC_BUF12_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF13_DIR==CAN_RX)
#ifdef CAN_CHC_BUF13_NUM
  #ifdef CAN_CHC_BUF13_DLC
    CAN_BUF(13,CAN_CHC_BUF13_NUM,CAN_CHC_BUF13_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF14_DIR==CAN_RX)
#ifdef CAN_CHC_BUF14_NUM
  #ifdef CAN_CHC_BUF14_DLC
    CAN_BUF(14,CAN_CHC_BUF14_NUM,CAN_CHC_BUF14_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF15_DIR==CAN_RX)
#ifdef CAN_CHC_BUF15_NUM
  #ifdef CAN_CHC_BUF15_DLC
    CAN_BUF(15,CAN_CHC_BUF15_NUM,CAN_CHC_BUF15_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF16_DIR==CAN_RX)
#ifdef CAN_CHC_BUF16_NUM
  #ifdef CAN_CHC_BUF16_DLC
    CAN_BUF(16,CAN_CHC_BUF16_NUM,CAN_CHC_BUF16_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF17_DIR==CAN_RX)
#ifdef CAN_CHC_BUF17_NUM
  #ifdef CAN_CHC_BUF17_DLC
    CAN_BUF(17,CAN_CHC_BUF17_NUM,CAN_CHC_BUF17_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF18_DIR==CAN_RX)
#ifdef CAN_CHC_BUF18_NUM
  #ifdef CAN_CHC_BUF18_DLC
    CAN_BUF(18,CAN_CHC_BUF18_NUM,CAN_CHC_BUF18_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF19_DIR==CAN_RX)
#ifdef CAN_CHC_BUF19_NUM
  #ifdef CAN_CHC_BUF19_DLC
    CAN_BUF(19,CAN_CHC_BUF19_NUM,CAN_CHC_BUF19_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF20_DIR==CAN_RX)
#ifdef CAN_CHC_BUF20_NUM
  #ifdef CAN_CHC_BUF20_DLC
    CAN_BUF(20,CAN_CHC_BUF20_NUM,CAN_CHC_BUF20_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF21_DIR==CAN_RX)
#ifdef CAN_CHC_BUF21_NUM
  #ifdef CAN_CHC_BUF21_DLC
    CAN_BUF(21,CAN_CHC_BUF21_NUM,CAN_CHC_BUF21_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF22_DIR==CAN_RX)
#ifdef CAN_CHC_BUF22_NUM
  #ifdef CAN_CHC_BUF22_DLC
    CAN_BUF(22,CAN_CHC_BUF22_NUM,CAN_CHC_BUF22_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF23_DIR==CAN_RX)
#ifdef CAN_CHC_BUF23_NUM
  #ifdef CAN_CHC_BUF23_DLC
    CAN_BUF(23,CAN_CHC_BUF23_NUM,CAN_CHC_BUF23_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF24_DIR==CAN_RX)
#ifdef CAN_CHC_BUF24_NUM
  #ifdef CAN_CHC_BUF24_DLC
    CAN_BUF(24,CAN_CHC_BUF24_NUM,CAN_CHC_BUF24_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF25_DIR==CAN_RX)
#ifdef CAN_CHC_BUF25_NUM
  #ifdef CAN_CHC_BUF25_DLC
    CAN_BUF(25,CAN_CHC_BUF25_NUM,CAN_CHC_BUF25_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF26_DIR==CAN_RX)
#ifdef CAN_CHC_BUF26_NUM
  #ifdef CAN_CHC_BUF26_DLC
    CAN_BUF(26,CAN_CHC_BUF26_NUM,CAN_CHC_BUF26_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF27_DIR==CAN_RX)
#ifdef CAN_CHC_BUF27_NUM
  #ifdef CAN_CHC_BUF27_DLC
    CAN_BUF(27,CAN_CHC_BUF27_NUM,CAN_CHC_BUF27_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF28_DIR==CAN_RX)
#ifdef CAN_CHC_BUF28_NUM
  #ifdef CAN_CHC_BUF28_DLC
    CAN_BUF(28,CAN_CHC_BUF28_NUM,CAN_CHC_BUF28_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF29_DIR==CAN_RX)
#ifdef CAN_CHC_BUF29_NUM
  #ifdef CAN_CHC_BUF29_DLC
    CAN_BUF(29,CAN_CHC_BUF29_NUM,CAN_CHC_BUF29_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF30_DIR==CAN_RX)
#ifdef CAN_CHC_BUF30_NUM
  #ifdef CAN_CHC_BUF30_DLC
    CAN_BUF(30,CAN_CHC_BUF30_NUM,CAN_CHC_BUF30_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF31_DIR==CAN_RX)
#ifdef CAN_CHC_BUF31_NUM
  #ifdef CAN_CHC_BUF31_DLC
    CAN_BUF(31,CAN_CHC_BUF31_NUM,CAN_CHC_BUF31_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF32_DIR==CAN_RX)
#ifdef CAN_CHC_BUF32_NUM
  #ifdef CAN_CHC_BUF32_DLC
    CAN_BUF(32,CAN_CHC_BUF32_NUM,CAN_CHC_BUF32_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF33_DIR==CAN_RX)
#ifdef CAN_CHC_BUF33_NUM
  #ifdef CAN_CHC_BUF33_DLC
    CAN_BUF(33,CAN_CHC_BUF33_NUM,CAN_CHC_BUF33_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF34_DIR==CAN_RX)
#ifdef CAN_CHC_BUF34_NUM
  #ifdef CAN_CHC_BUF34_DLC
    CAN_BUF(34,CAN_CHC_BUF34_NUM,CAN_CHC_BUF34_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF35_DIR==CAN_RX)
#ifdef CAN_CHC_BUF35_NUM
  #ifdef CAN_CHC_BUF35_DLC
    CAN_BUF(35,CAN_CHC_BUF35_NUM,CAN_CHC_BUF35_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF36_DIR==CAN_RX)
#ifdef CAN_CHC_BUF36_NUM
  #ifdef CAN_CHC_BUF36_DLC
    CAN_BUF(36,CAN_CHC_BUF36_NUM,CAN_CHC_BUF36_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF37_DIR==CAN_RX)
#ifdef CAN_CHC_BUF37_NUM
  #ifdef CAN_CHC_BUF37_DLC
    CAN_BUF(37,CAN_CHC_BUF37_NUM,CAN_CHC_BUF37_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF38_DIR==CAN_RX)
#ifdef CAN_CHC_BUF38_NUM
  #ifdef CAN_CHC_BUF38_DLC
    CAN_BUF(38,CAN_CHC_BUF38_NUM,CAN_CHC_BUF38_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF39_DIR==CAN_RX)
#ifdef CAN_CHC_BUF39_NUM
  #ifdef CAN_CHC_BUF39_DLC
    CAN_BUF(39,CAN_CHC_BUF39_NUM,CAN_CHC_BUF39_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF40_DIR==CAN_RX)
#ifdef CAN_CHC_BUF40_NUM
  #ifdef CAN_CHC_BUF40_DLC
    CAN_BUF(40,CAN_CHC_BUF40_NUM,CAN_CHC_BUF40_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF41_DIR==CAN_RX)
#ifdef CAN_CHC_BUF41_NUM
  #ifdef CAN_CHC_BUF41_DLC
    CAN_BUF(41,CAN_CHC_BUF41_NUM,CAN_CHC_BUF41_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF42_DIR==CAN_RX)
#ifdef CAN_CHC_BUF42_NUM
  #ifdef CAN_CHC_BUF42_DLC
    CAN_BUF(42,CAN_CHC_BUF42_NUM,CAN_CHC_BUF42_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF43_DIR==CAN_RX)
#ifdef CAN_CHC_BUF43_NUM
  #ifdef CAN_CHC_BUF43_DLC
    CAN_BUF(43,CAN_CHC_BUF43_NUM,CAN_CHC_BUF43_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF44_DIR==CAN_RX)
#ifdef CAN_CHC_BUF44_NUM
  #ifdef CAN_CHC_BUF44_DLC
    CAN_BUF(44,CAN_CHC_BUF44_NUM,CAN_CHC_BUF44_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF45_DIR==CAN_RX)
#ifdef CAN_CHC_BUF45_NUM
  #ifdef CAN_CHC_BUF45_DLC
    CAN_BUF(45,CAN_CHC_BUF45_NUM,CAN_CHC_BUF45_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF46_DIR==CAN_RX)
#ifdef CAN_CHC_BUF46_NUM
  #ifdef CAN_CHC_BUF46_DLC
    CAN_BUF(46,CAN_CHC_BUF46_NUM,CAN_CHC_BUF46_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF47_DIR==CAN_RX)
#ifdef CAN_CHC_BUF47_NUM
  #ifdef CAN_CHC_BUF47_DLC
    CAN_BUF(47,CAN_CHC_BUF47_NUM,CAN_CHC_BUF47_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF48_DIR==CAN_RX)
#ifdef CAN_CHC_BUF48_NUM
  #ifdef CAN_CHC_BUF48_DLC
    CAN_BUF(48,CAN_CHC_BUF48_NUM,CAN_CHC_BUF48_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF49_DIR==CAN_RX)
#ifdef CAN_CHC_BUF49_NUM
  #ifdef CAN_CHC_BUF49_DLC
    CAN_BUF(49,CAN_CHC_BUF49_NUM,CAN_CHC_BUF49_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF50_DIR==CAN_RX)
#ifdef CAN_CHC_BUF50_NUM
  #ifdef CAN_CHC_BUF50_DLC
    CAN_BUF(50,CAN_CHC_BUF50_NUM,CAN_CHC_BUF50_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF51_DIR==CAN_RX)
#ifdef CAN_CHC_BUF51_NUM
  #ifdef CAN_CHC_BUF51_DLC
    CAN_BUF(51,CAN_CHC_BUF51_NUM,CAN_CHC_BUF51_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF52_DIR==CAN_RX)
#ifdef CAN_CHC_BUF52_NUM
  #ifdef CAN_CHC_BUF52_DLC
    CAN_BUF(52,CAN_CHC_BUF52_NUM,CAN_CHC_BUF52_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF53_DIR==CAN_RX)
#ifdef CAN_CHC_BUF53_NUM
  #ifdef CAN_CHC_BUF53_DLC
    CAN_BUF(53,CAN_CHC_BUF53_NUM,CAN_CHC_BUF53_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF54_DIR==CAN_RX)
#ifdef CAN_CHC_BUF54_NUM
  #ifdef CAN_CHC_BUF54_DLC
    CAN_BUF(54,CAN_CHC_BUF54_NUM,CAN_CHC_BUF54_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF55_DIR==CAN_RX)
#ifdef CAN_CHC_BUF55_NUM
  #ifdef CAN_CHC_BUF55_DLC
    CAN_BUF(55,CAN_CHC_BUF55_NUM,CAN_CHC_BUF55_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF56_DIR==CAN_RX)
#ifdef CAN_CHC_BUF56_NUM
  #ifdef CAN_CHC_BUF56_DLC
    CAN_BUF(56,CAN_CHC_BUF56_NUM,CAN_CHC_BUF56_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF57_DIR==CAN_RX)
#ifdef CAN_CHC_BUF57_NUM
  #ifdef CAN_CHC_BUF57_DLC
    CAN_BUF(57,CAN_CHC_BUF57_NUM,CAN_CHC_BUF57_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF58_DIR==CAN_RX)
#ifdef CAN_CHC_BUF58_NUM
  #ifdef CAN_CHC_BUF58_DLC
    CAN_BUF(58,CAN_CHC_BUF58_NUM,CAN_CHC_BUF58_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF59_DIR==CAN_RX)
#ifdef CAN_CHC_BUF59_NUM
  #ifdef CAN_CHC_BUF59_DLC
    CAN_BUF(59,CAN_CHC_BUF59_NUM,CAN_CHC_BUF59_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF60_DIR==CAN_RX)
#ifdef CAN_CHC_BUF60_NUM
  #ifdef CAN_CHC_BUF60_DLC
    CAN_BUF(60,CAN_CHC_BUF60_NUM,CAN_CHC_BUF60_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF61_DIR==CAN_RX)
#ifdef CAN_CHC_BUF61_NUM
  #ifdef CAN_CHC_BUF61_DLC
    CAN_BUF(61,CAN_CHC_BUF61_NUM,CAN_CHC_BUF61_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF62_DIR==CAN_RX)
#ifdef CAN_CHC_BUF62_NUM
  #ifdef CAN_CHC_BUF62_DLC
    CAN_BUF(62,CAN_CHC_BUF62_NUM,CAN_CHC_BUF62_DLC)
  #endif
#endif
#endif
#if (CAN_CHC_BUF63_DIR==CAN_RX)
#ifdef CAN_CHC_BUF63_NUM
  #ifdef CAN_CHC_BUF63_DLC
    CAN_BUF(63,CAN_CHC_BUF63_NUM,CAN_CHC_BUF63_DLC)
  #endif
#endif
#endif
} t_CAN_CHC_b;
#endif





/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

#if CAN_CHA_EN
extern t_CAN_CHA_b CAN_CHA_b;
#endif
#if CAN_CHB_EN
extern t_CAN_CHB_b CAN_CHB_b;
#endif
#if CAN_CHC_EN
extern t_CAN_CHC_b CAN_CHC_b;
#endif

extern bool CAN_ConfigStatus;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CAN_EX_Tx_Rx_Config - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CAN_EX_Tx_Rx_Config(uint8_t channel,
                          void (*TX_Func_Ex)(void), 
                          void (*RX_Func_Ex)(void));

/*--------------------------------------------------------------------------*
 * CAN_Config - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_Config (void);

/*--------------------------------------------------------------------------*
 * CAN_Reset - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_Reset (uint8_t Channel) ;

/*--------------------------------------------------------------------------*
 * CAN_ResetBuffer - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_ResetBuffer (uint8_t Channel,
                          uint8_t nbuf);

/*--------------------------------------------------------------------------*
 * CAN_TxData - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_TxData (uint8_t Channel,
                     uint16_t nbuf,
                     uint8_t* pData);

/*--------------------------------------------------------------------------*
 * CAN_EnableReceive - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_EnableReceive (uint8_t Channel,
                            uint8_t nbuf);

/*--------------------------------------------------------------------------*
 * CAN_RxData - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_RxData(uint8_t ch,
                    uint8_t b,
                    struct CAN_buff ** ptr);


/*--------------------------------------------------------------------------*
 * CAN_ErrIntDisable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_ErrIntDisable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_ErrIntEnable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_ErrIntEnable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_BusOffIntDisable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_BusOffIntDisable(uint8_t Channel);


/*--------------------------------------------------------------------------*
 * CAN_Disable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_Disable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_Enable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_Enable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_DisableReceive - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_DisableReceive (uint8_t Channel, uint8_t nbuf);

/*--------------------------------------------------------------------------*
 * CAN_BusOffRecEnable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_BusOffRecEnable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_BusOffRecDisable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_BusOffRecDisable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_BusOffIntEnable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_BusOffIntEnable(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * Can_GetTxStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t Can_GetTxStatus(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_GetStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_GetStatus(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_BusOffRecovery - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_BusOffRecovery(uint8_t Channel);

/*--------------------------------------------------------------------------*
 * CAN_GetTxBufferStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t CAN_GetTxBufferStatus(uint8_t Channel,
                               uint8_t nbuf);


/*--------------------------------------------------------------------------*
 * CAN_ActivateKWPCAN - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CAN_ActivateKWPCAN (uint8_t ch, uint8_t nbuf);

#endif //_CAN_H_

