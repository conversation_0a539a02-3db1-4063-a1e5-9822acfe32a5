#ifndef __SAF2MGM_H__
#define __SAF2MGM_H__

#include "relaymgm.h"

/*  Constants */
#define IND_A    0
#define IND_B    1
#define IND_C    2
#define IND_D    3
#define IND_E    4
#define IND_F    5
#define IND_G    6
#define IND_H    7
#define IND_I    8
#define IND_L    9
#define IND_BUF  10

#define S2_EXECODE_A    0x0001 //1//1
#define S2_EXECODE_B    0x0002 //2//2
#define S2_EXECODE_C    0x0004 //4//4
#define S2_EXECODE_D    0x0008 //8
#define S2_EXECODE_E    0x0010 //16
#define S2_EXECODE_F    0x0020 //32
#define S2_EXECODE_G    0x0040 //64
#define S2_EXECODE_H    0x0080 //128
#define S2_EXECODE_I    0x0100 //256
#define S2_EXECODE_L    0x0200 //512
#define S2_EXECODE_M    0x0400 //1024
#define S2_EXECODE_BUF  0x8000 //32768

#define S2_EXECODE_ALL  0x87ff

#define S2ERR_BUFFERIN  0x0001 //1

/* S2 Module Constants */
#define CAN             0x01
#define GAS             0x00
#define SIZE_S2PUNERR   11 
#define S2_MAX_PERC     1600   /* Max gas percentage */
#define S2_DPRES_MAX    2000


#define S2_STATE_NONE          0x00
#define S2_STATE_ENG_STOPPED   0x01
#define S2_STATE_ENABLE        0x02
#define S2_STATE_WAIT_KEY_OFF  0x03
#define S2_STATE_DELAY_RESTART 0x04
#define S2_STATE_RESTART_PROG  0x05

//A Module Errors Tags
#define S2ERR_A_PRESINTAKE    0x01
/* TOLTO #define S2ERR_A_TWATCRK       0x02 */
#define S2ERR_A_TAIR          0x03
#define S2ERR_A_PRESATM       0x04
#define S2ERR_A_GASPOS        0x05

// B Module Errors Tags
#define S2ERR_B_ENERLAM     0x01
#define S2ERR_B_CUTOFF      0X02          

//C Module Errors Tags
#define S2ERR_C_CMECAN        0x01

//D Module Errors Tags
#define S2ERR_D_DRIVREQ       0x01

//E Module Errors Tags
#define S2ERR_E_CANREQ        0x01

//F Module Errors Tags
#define S2ERR_F_TOOFAST_IDLE  0x01
#define S2ERR_F_WRONG_IDLEREQ  0x02

//G Module Errors Tags
#define S2ERR_G_QAIR          0x01
#define S2ERR_G_CMIEST        0x02
#define S2ERR_G_EFFSA         0x03
#define S2ERR_G_EFFLAM        0x04
#define S2ERR_G_EFFCTF        0x05
#define S2ERR_G_SAOUT         0x06
#define S2ERR_G_SAOPT         0x07
#define S2ERR_G_LAMOBJ        0x08

//H Module Errors Tags
#define S2ERR_H_CMF           0x03
#define S2ERR_H_CMI           0x04
#define S2ERR_H_CMIDRIV       0x02
#define S2ERR_H_CMEDRIV       0x01
#define S2ERR_H_WRONG_TRQ     0x05

//I Module Errors Tags
#define S2ERR_I_TOOHIGH_TORQUE 0x01

//L Module Errors Tags
#define S2ERR_L_PEDAL_NOT_0   0x01
#define S2ERR_L_LOADCMD_NOT_0 0x02
#define S2ERR_L_VEHSPEED      0x03
#define S2ERR_L_TRQ_RED       0x04
#define S2ERR_L_RPMLIM        0x05
#define S2ERR_L_VSPEEDLIM     0x06
#define S2ERR_L_LIMPHOME		0x07

/* Macros for the NVM storage of S2 variables */
#ifdef _BUILD_S2_STORE_RESULTS_
    #define STORE_A1  0
    #define STORE_A2  1
    #define STORE_D1  2
    #define STORE_E1  3
    #define STORE_G1  4
    #define STORE_G2  5
    #define STORE_G3  6
    #define STORE_G4  7
    #define STORE_G5  8
    #define STORE_G6  9
    #define STORE_H1  10
    #define STORE_H2  11
    #define SIZE_S2STORE  12
#endif

/* Buffer di input */

typedef struct __Saf2InputBufCfg
{
    uint16_t  Rpm_10ms;                 // 1
    uint16_t  PresIntake_10ms;
    uint8_t   FlgEOL_10ms;
    uint16_t  AngThrottle_10ms;
    uint16_t  StThrRec_10ms;
    uint16_t  LamObj_10ms;
    int16_t   CmiPotEst_10ms;
    int16_t   TWater_10ms;
    uint8_t   IdleFlg_10ms;
    int16_t   CmeDriver_10ms;
    int16_t   CmeGasRpm_10ms;           // 11
    uint8_t   IdleReqFlg_10ms;        
    int16_t   CmeDriverP_10ms;
    int16_t   CmiTargetP_10ms;
    int16_t   CmiDriverP_10ms;
    int16_t   CmfP_10ms;
    int16_t   CmiEst_10ms;
    int16_t   CmiIdleP_10ms;
    uint16_t  GasPos_10ms;
    int16_t   TAir_10ms;
    uint16_t  EffCutoff_10ms;           // 21
    uint8_t   CutOffFlg_10ms;
    uint16_t  Load_10ms;
    uint16_t  PresAtm_10ms;
    uint16_t  QAirBaseAvg_10ms;
    uint16_t  EffLambda_10ms;
    int16_t   CmiSafP_10ms;
    int16_t   CmeDriverCANF_10ms;
    int16_t   SAout_10ms;
    int16_t   SAopt_10ms;
    uint16_t  EffSAReal_10ms;           // 31
    uint8_t   VtRec_NO_GAS_10ms;
    uint8_t   VtRec_ENG_OFF_10ms;
    uint8_t   VtRec_SL_T_R_10ms;
    uint8_t   VtRec_HV_T_R_10ms;
    uint8_t   VtRec_DBW_OFF_10ms;
    uint8_t   VtRec_FORCE_LH_10ms;
    uint8_t   VtRec_LIMIT_RPM_10ms;
    uint8_t   VtRec_REC_LIMIT_VEH_SPEED_10ms;
    uint8_t   FlgEnHBridge_10ms;
    uint16_t  VGasPos1_10ms;           // 41
    uint16_t  VGasPos2_10ms;
    uint16_t  VGasPos3_10ms;
    typRelayCmd LoadCmd_10ms;
    int16_t   DeltaLamCL_10ms;
    int16_t   LambdaError_10ms;
    uint32_t  InjTimePrgCyl_10ms;
    uint8_t   CtfLimiterFlg_10ms;
    uint8_t   CtfVSpeedLimFlg_10ms;
    int16_t   CmiSpeedP_10ms;
    uint16_t  RpmMaxCorr_10ms;           // 51
    int16_t   CmiReqP_10ms;
    uint16_t  VehSpeed_10ms;
    uint16_t  VehSpeedLimCAN_10ms;
    int16_t   CmiTargetPMin_10ms;
    
} Saf2InputBufCfg;

/* Public functions */
void Saf2Mgm_Init(void);
void Saf2Mgm_T10ms(void);
void Saf2Mgm_PowerOff(void);
void Saf2Mgm_CrtlFlow(void);
void Saf2Mgm_EngineStart(void);
void Saf2Task50ms(void);

/* Public variables */
extern uint8_t  S2FlgDisL2;             // 2^0
extern uint8_t  S2ForceShtDwn;
extern uint16_t S2ExeCode;
extern uint8_t  S2State;
extern uint8_t  S2FlgDisLRelay;

extern Saf2InputBufCfg S210msBuf,Saf2NegInputBuf;
extern void (*ptrS2function[SIZE_S2PUNERR])(void);

extern uint16_t  S2Result[SIZE_S2PUNERR];
extern uint16_t  S2ResultNeg[SIZE_S2PUNERR];

#endif /* __SAF2MGM_H__ */
