/*
 * File: LaunchCtrl.c
 *
 * Code generated for Simulink model 'LaunchCtrl'.
 *
 * Model version                  : 1.234
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jun 14 15:33:33 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#include "LaunchCtrl.h"
#include "LaunchCtrl_private.h"

/* Named constants for Chart: '<S43>/Enable_Lc_Km' */
#define LaunchCtrl_IN_KM               ((uint8_T)1U)
#define LaunchCtrl_IN_ONE              ((uint8_T)2U)
#define LaunchCtrl_IN_THREE            ((uint8_T)3U)
#define LaunchCtrl_IN_TWO              ((uint8_T)4U)

/* Named constants for Chart: '<S4>/Chart_LaunchCtrl' */
#define Launc_IN_LC_OVERRIDE_PRE_LAUNCH ((uint8_T)3U)
#define LaunchCtr_IN_LC_OVERRIDE_LAUNCH ((uint8_T)2U)
#define LaunchCtrl_IN_LC_DISABLE       ((uint8_T)1U)
#define LaunchCtrl_IN_LC_ENABLING      ((uint8_T)2U)
#define LaunchCtrl_IN_LC_LAUNCH        ((uint8_T)3U)
#define LaunchCtrl_IN_LC_NONE          ((uint8_T)1U)
#define LaunchCtrl_IN_LC_READY         ((uint8_T)4U)
#define LaunchCtrl_IN_LC_RET           ((uint8_T)5U)
#define LaunchCtrl_IN_LC_TO_IDLE       ((uint8_T)6U)
#define LaunchCtrl_IN_LC_TO_LIM        ((uint8_T)7U)
#define LaunchCtrl_IN_LC_WAIT_IDLE     ((uint8_T)8U)

/* user code (top of source file) */
/* System '<Root>/LaunchCtrl' */
#ifdef _BUILD_LAUNCHCTRL_

/* Block signals and states (default storage) */
D_Work_LaunchCtrl_T LaunchCtrl_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_LaunchCtrl_T LaunchCtrl_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_LaunchCtrl_T LaunchCtrl_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T AwLevel;

/* selector */
int16_T CmeLcSat;

/* CmiLcP */
int16_T CmeLcSatOffI;

/* CmiLcP */
uint8_T EnAwMaxLevel;

/* selector */
uint8_T EnLcMaxLevel;

/* selector */
uint8_T EnTcMaxLevel;

/* selector */
uint8_T FlgCtfLc;

/* flg cutoff */
uint8_T FlgEnLcKm;

/* flg Km */
uint8_T FlgLcCmiLow;

/* flag */
uint8_T FlgLcDiag;

/* flag */
uint8_T FlgLcEn;

/* flag */
uint8_T FlgLcEnd;

/* flag */
uint8_T FlgLcLaunch;

/* flag */
uint8_T FlgLcLevel;

/* flag */
uint8_T FlgLcLim;

/* flg limiter */
uint8_T FlgLcReady;

/* flag */
uint8_T FlgLcRet;

/* flag */
uint8_T FlgLcTrg;

/* flag cutoff */
uint32_T IDLaunchCtrl;

/* ID Version */
uint8_T IdxLcCutOff;

/* idx cutoff */
uint8_T LcActive;

/* LC Active */
uint8_T LcLevel;

/* selector */
int16_T LcRpmErr;

/* Err */
int8_T LcTrip;

/* counter Trip */
uint16_T RpmLcTrgCme;

/* RpmLcTrg */
int16_T RpmLcTrgCtf;

/* RpmLcTrg */
uint8_T SetTracCtrl;

/* selector */
uint8_T StLc;

/* Lc Status */
uint16_T VehLcAxIntSat;

/* VehRbVfAxInt saturation in Spring Up */

/* Output and update for function-call system: '<S1>/Init' */
void LaunchCtrl_Init(void)
{
  /* Constant: '<S2>/ID_LAUNCH_CTRL' */
  IDLaunchCtrl = ID_LAUNCH_CTRL;

  /* Chart: '<S2>/Init_Scheduler' incorporates:
   *  SubSystem: '<S2>/Init'
   */
  /* FunctionCaller: '<S8>/Init' */
  /* Gateway: LaunchCtrl/Init/Init_Scheduler */
  /* During: LaunchCtrl/Init/Init_Scheduler */
  /* Entry Internal: LaunchCtrl/Init/Init_Scheduler */
  /* Transition: '<S10>:2' */
  /* Transition: '<S10>:4' */
  /* Event: '<S10>:5' */
  LaunchCtrl_initialize();

  /* Chart: '<S2>/Init_Scheduler' incorporates:
   *  SubSystem: '<S2>/Init_Data'
   */
  /* Selector: '<S9>/Selector' incorporates:
   *  Constant: '<S9>/VTENTCMAXLEVEL'
   *  DataStoreWrite: '<S9>/Data Store Write14'
   *  Inport: '<Root>/RidingMode'
   */
  /* Event: '<S10>:6' */
  EnTcMaxLevel = VTENTCMAXLEVEL[RidingMode];

  /* DataStoreWrite: '<S9>/Data Store Write1' incorporates:
   *  Constant: '<S9>/ZERO2'
   */
  IdxLcCutOff = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write10' incorporates:
   *  Constant: '<S9>/ZERO8'
   */
  CmeLcSatOffI = 0;

  /* DataStoreWrite: '<S9>/Data Store Write11' incorporates:
   *  Constant: '<S9>/ZERO9'
   */
  RpmLcTrgCtf = 0;

  /* DataStoreWrite: '<S9>/Data Store Write12' incorporates:
   *  Constant: '<S9>/ENLCMAXLEVEL'
   */
  EnLcMaxLevel = ENLCMAXLEVEL;

  /* DataStoreWrite: '<S9>/Data Store Write13' incorporates:
   *  Constant: '<S9>/ENAWMAXLEVEL'
   */
  EnAwMaxLevel = ENAWMAXLEVEL;

  /* DataStoreWrite: '<S9>/Data Store Write15' incorporates:
   *  Constant: '<S9>/ZERO10'
   */
  VehLcAxIntSat = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write16' incorporates:
   *  Constant: '<S9>/ZERO11'
   */
  LcActive = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write2' incorporates:
   *  Constant: '<S9>/ZERO1'
   */
  CmeLcSat = 0;

  /* DataStoreWrite: '<S9>/Data Store Write3' incorporates:
   *  Constant: '<S9>/ZERO'
   */
  FlgLcTrg = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write4' incorporates:
   *  Constant: '<S9>/ZERO3'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write5' incorporates:
   *  Constant: '<S9>/ZERO4'
   */
  RpmLcTrgCme = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write6' incorporates:
   *  Constant: '<S9>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataTypeConversion: '<S9>/Data Type Conversion' incorporates:
   *  Constant: '<S9>/NUMLCTRIP'
   *  DataStoreWrite: '<S9>/Data Store Write7'
   */
  LcTrip = (int8_T)NUMLCTRIP;

  /* DataStoreWrite: '<S9>/Data Store Write8' incorporates:
   *  Constant: '<S9>/ZERO6'
   */
  AwLevel = 0U;

  /* DataStoreWrite: '<S9>/Data Store Write9' incorporates:
   *  Constant: '<S9>/ZERO7'
   */
  SetTracCtrl = 0U;
}

/* Output and update for function-call system: '<S4>/Lc_Dis' */
void LaunchCtrl_Lc_Dis(void)
{
  /* DataStoreWrite: '<S30>/Data Store Write1' incorporates:
   *  Constant: '<S30>/ZERO2'
   */
  CmeLcSatOffI = 0;

  /* DataStoreWrite: '<S30>/Data Store Write2' incorporates:
   *  Constant: '<S30>/ZERO1'
   */
  CmeLcSat = 0;

  /* DataStoreWrite: '<S30>/Data Store Write3' incorporates:
   *  Constant: '<S30>/ZERO'
   */
  FlgLcTrg = 0U;

  /* DataStoreWrite: '<S30>/Data Store Write4' incorporates:
   *  Constant: '<S30>/ZERO3'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S30>/Data Store Write5' incorporates:
   *  Constant: '<S30>/ZERO4'
   */
  RpmLcTrgCme = 0U;

  /* DataStoreWrite: '<S30>/Data Store Write6' incorporates:
   *  Constant: '<S30>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S30>/Data Store Write7' incorporates:
   *  Constant: '<S30>/ZERO6'
   */
  VehLcAxIntSat = 0U;

  /* DataStoreWrite: '<S30>/Data Store Write8' incorporates:
   *  Constant: '<S30>/ZERO7'
   */
  LcActive = 0U;
}

/* Output and update for function-call system: '<S4>/Lc_Idle' */
void LaunchCtrl_Lc_Idle(void)
{
  /* DataStoreWrite: '<S31>/Data Store Write1' incorporates:
   *  Constant: '<S31>/ZERO1'
   */
  CmeLcSatOffI = 0;

  /* DataStoreWrite: '<S31>/Data Store Write2' incorporates:
   *  Constant: '<S31>/LCCMETRGIDLE'
   */
  CmeLcSat = LCCMETRGIDLE;

  /* DataStoreWrite: '<S31>/Data Store Write3' incorporates:
   *  Constant: '<S31>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S31>/Data Store Write4' incorporates:
   *  Constant: '<S31>/ZERO2'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write5' incorporates:
   *  Constant: '<S31>/ZERO3'
   */
  RpmLcTrgCme = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write6' incorporates:
   *  Constant: '<S31>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write7' incorporates:
   *  Constant: '<S31>/ZERO6'
   */
  VehLcAxIntSat = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write8' incorporates:
   *  Constant: '<S31>/ZERO7'
   */
  LcActive = 0U;
}

/* Output and update for function-call system: '<S4>/Lc_CtfLim' */
void LaunchCtrl_Lc_CtfLim(uint16_T rtu_vehspeed_index, int32_T
  rtu_vehspeed_ratio)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o_bcr;
  uint16_T rtb_PreLookUpIdSearch_U16_o_d3t;
  int16_T rtb_LookUp_IR_S16_ib0;
  uint8_T rtb_Conversion3_bjd;
  uint16_T rtb_Switch_cjh;
  int16_T rtb_DataStoreRead2;
  int16_T rtb_Conversion1_fme[7];
  int32_T i;

  /* DataTypeConversion: '<S63>/Data Type Conversion' */
  i = rtu_vehspeed_ratio;
  if (rtu_vehspeed_ratio < 0) {
    i = 0;
  } else {
    if (rtu_vehspeed_ratio > 65535) {
      i = 65535;
    }
  }

  rtb_Switch_cjh = (uint16_T)i;

  /* End of DataTypeConversion: '<S63>/Data Type Conversion' */

  /* MinMax: '<S63>/MinMax' incorporates:
   *  Constant: '<S63>/LCVEHAXINTSAT'
   */
  if (LCVEHAXINTSAT < rtb_Switch_cjh) {
    rtb_Switch_cjh = LCVEHAXINTSAT;
  }

  /* End of MinMax: '<S63>/MinMax' */

  /* MinMax: '<S63>/MinMax1' incorporates:
   *  DataStoreRead: '<S63>/Data Store Read'
   */
  if (rtu_vehspeed_index > rtb_Switch_cjh) {
    rtb_Switch_cjh = rtu_vehspeed_index;
  }

  if (rtb_Switch_cjh > VehLcAxIntSat) {
  } else {
    rtb_Switch_cjh = VehLcAxIntSat;
  }

  /* End of MinMax: '<S63>/MinMax1' */

  /* DataStoreWrite: '<S63>/Data Store Write1' */
  VehLcAxIntSat = rtb_Switch_cjh;

  /* Switch: '<S63>/Switch' incorporates:
   *  Constant: '<S63>/LCVEHAXINTSAT'
   *  RelationalOperator: '<S63>/Relational Operator'
   */
  if (rtu_vehspeed_index >= LCVEHAXINTSAT) {
    rtb_Switch_cjh = rtu_vehspeed_index;
  }

  /* End of Switch: '<S63>/Switch' */

  /* DataTypeConversion: '<S64>/Data Type Conversion8' incorporates:
   *  Constant: '<S54>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_bjd = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S64>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S54>/BKLCCMETRG'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_bcr,
                        &rtb_PreLookUpIdSearch_U16_o_d3t, rtb_Switch_cjh,
                        &BKLCCMETRG[0], rtb_Conversion3_bjd);

  /* Outputs for Atomic SubSystem: '<S29>/Calc_Lc_CtfLim' */
  /* DataTypeConversion: '<S55>/Conversion3' incorporates:
   *  Constant: '<S53>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_bjd = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S55>/LookUp_IR_S16' incorporates:
   *  Constant: '<S53>/VTLCCMEOFFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_ib0, &VTLCCMEOFFI[0],
                rtb_PreLookUpIdSearch_U16_o_bcr, rtb_PreLookUpIdSearch_U16_o_d3t,
                rtb_Conversion3_bjd);

  /* DataStoreWrite: '<S53>/Data Store Write1' */
  CmeLcSatOffI = rtb_LookUp_IR_S16_ib0;

  /* DataTypeConversion: '<S56>/Conversion3' incorporates:
   *  Constant: '<S53>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_bjd = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S56>/LookUp_IR_S16' incorporates:
   *  Constant: '<S53>/VTLCCMETRG'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_ib0, &VTLCCMETRG[0],
                rtb_PreLookUpIdSearch_U16_o_bcr, rtb_PreLookUpIdSearch_U16_o_d3t,
                rtb_Conversion3_bjd);

  /* DataStoreRead: '<S53>/Data Store Read2' */
  rtb_DataStoreRead2 = CmeLcSat;

  /* S-Function (RateLimiter_S16): '<S58>/RateLimiter_S16' incorporates:
   *  Constant: '<S53>/LCRTCMEREADY'
   *  Constant: '<S53>/MIN'
   */
  RateLimiter_S16( &rtb_LookUp_IR_S16_ib0, rtb_LookUp_IR_S16_ib0,
                  rtb_DataStoreRead2, -32736, LCRTCMEREADY);

  /* DataStoreWrite: '<S53>/Data Store Write2' */
  CmeLcSat = rtb_LookUp_IR_S16_ib0;

  /* DataTypeConversion: '<S57>/Conversion1' incorporates:
   *  Constant: '<S53>/VTLCRPMTRG'
   */
  for (i = 0; i < 7; i++) {
    rtb_Conversion1_fme[i] = (int16_T)VTLCRPMTRG[i];
  }

  /* End of DataTypeConversion: '<S57>/Conversion1' */

  /* DataTypeConversion: '<S57>/Conversion3' incorporates:
   *  Constant: '<S53>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_bjd = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S57>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_ib0, &rtb_Conversion1_fme[0],
                rtb_PreLookUpIdSearch_U16_o_bcr, rtb_PreLookUpIdSearch_U16_o_d3t,
                rtb_Conversion3_bjd);

  /* DataTypeConversion: '<S61>/Conversion' incorporates:
   *  DataStoreWrite: '<S53>/Data Store Write3'
   */
  RpmLcTrgCme = (uint16_T)rtb_LookUp_IR_S16_ib0;

  /* End of Outputs for SubSystem: '<S29>/Calc_Lc_CtfLim' */

  /* Outputs for Atomic SubSystem: '<S29>/Assign_Lc_CtfLim' */
  /* DataStoreWrite: '<S52>/Data Store Write3' incorporates:
   *  Constant: '<S52>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S52>/Data Store Write4' incorporates:
   *  Constant: '<S52>/ZERO2'
   */
  FlgCtfLc = 1U;

  /* DataStoreWrite: '<S52>/Data Store Write6' incorporates:
   *  Constant: '<S52>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S52>/Data Store Write8' incorporates:
   *  Constant: '<S52>/ZERO7'
   */
  LcActive = 1U;

  /* End of Outputs for SubSystem: '<S29>/Assign_Lc_CtfLim' */
}

/* Output and update for function-call system: '<S4>/Lc_SatLim' */
void LaunchCtrl_Lc_SatLim(uint16_T rtu_vehspeed_index, int32_T
  rtu_vehspeed_ratio)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o_pg3;
  uint16_T rtb_PreLookUpIdSearch_U16_o_lvy;
  int16_T rtb_LookUp_IR_S16_pie;
  uint8_T rtb_Conversion3_j2l;
  uint16_T rtb_Switch_aly;
  int16_T rtb_Conversion1_eqg[7];
  int32_T i;

  /* DataTypeConversion: '<S86>/Data Type Conversion' */
  i = rtu_vehspeed_ratio;
  if (rtu_vehspeed_ratio < 0) {
    i = 0;
  } else {
    if (rtu_vehspeed_ratio > 65535) {
      i = 65535;
    }
  }

  rtb_Switch_aly = (uint16_T)i;

  /* End of DataTypeConversion: '<S86>/Data Type Conversion' */

  /* MinMax: '<S86>/MinMax' incorporates:
   *  Constant: '<S86>/LCVEHAXINTSAT'
   */
  if (LCVEHAXINTSAT < rtb_Switch_aly) {
    rtb_Switch_aly = LCVEHAXINTSAT;
  }

  /* End of MinMax: '<S86>/MinMax' */

  /* MinMax: '<S86>/MinMax1' incorporates:
   *  DataStoreRead: '<S86>/Data Store Read'
   */
  if (rtu_vehspeed_index > rtb_Switch_aly) {
    rtb_Switch_aly = rtu_vehspeed_index;
  }

  if (rtb_Switch_aly > VehLcAxIntSat) {
  } else {
    rtb_Switch_aly = VehLcAxIntSat;
  }

  /* End of MinMax: '<S86>/MinMax1' */

  /* DataStoreWrite: '<S86>/Data Store Write1' */
  VehLcAxIntSat = rtb_Switch_aly;

  /* Switch: '<S86>/Switch' incorporates:
   *  Constant: '<S86>/LCVEHAXINTSAT'
   *  RelationalOperator: '<S86>/Relational Operator'
   */
  if (rtu_vehspeed_index >= LCVEHAXINTSAT) {
    rtb_Switch_aly = rtu_vehspeed_index;
  }

  /* End of Switch: '<S86>/Switch' */

  /* DataTypeConversion: '<S87>/Data Type Conversion8' incorporates:
   *  Constant: '<S79>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j2l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S87>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S79>/BKLCCMETRG'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_pg3,
                        &rtb_PreLookUpIdSearch_U16_o_lvy, rtb_Switch_aly,
                        &BKLCCMETRG[0], rtb_Conversion3_j2l);

  /* Outputs for Atomic SubSystem: '<S34>/Calc_Lc_SatLim' */
  /* DataTypeConversion: '<S82>/Conversion1' incorporates:
   *  Constant: '<S78>/VTLCRPMTRG'
   */
  for (i = 0; i < 7; i++) {
    rtb_Conversion1_eqg[i] = (int16_T)VTLCRPMTRG[i];
  }

  /* End of DataTypeConversion: '<S82>/Conversion1' */

  /* DataTypeConversion: '<S82>/Conversion3' incorporates:
   *  Constant: '<S78>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j2l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S82>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_pie, &rtb_Conversion1_eqg[0],
                rtb_PreLookUpIdSearch_U16_o_pg3, rtb_PreLookUpIdSearch_U16_o_lvy,
                rtb_Conversion3_j2l);

  /* DataTypeConversion: '<S85>/Conversion' incorporates:
   *  DataStoreWrite: '<S78>/Data Store Write1'
   */
  RpmLcTrgCme = (uint16_T)rtb_LookUp_IR_S16_pie;

  /* DataTypeConversion: '<S81>/Conversion3' incorporates:
   *  Constant: '<S78>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j2l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S81>/LookUp_IR_S16' incorporates:
   *  Constant: '<S78>/VTLCCMETRG'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_pie, &VTLCCMETRG[0],
                rtb_PreLookUpIdSearch_U16_o_pg3, rtb_PreLookUpIdSearch_U16_o_lvy,
                rtb_Conversion3_j2l);

  /* DataStoreWrite: '<S78>/Data Store Write2' */
  CmeLcSat = rtb_LookUp_IR_S16_pie;

  /* DataTypeConversion: '<S80>/Conversion3' incorporates:
   *  Constant: '<S78>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j2l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S80>/LookUp_IR_S16' incorporates:
   *  Constant: '<S78>/VTLCCMEOFFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_pie, &VTLCCMEOFFI[0],
                rtb_PreLookUpIdSearch_U16_o_pg3, rtb_PreLookUpIdSearch_U16_o_lvy,
                rtb_Conversion3_j2l);

  /* DataStoreWrite: '<S78>/Data Store Write3' */
  CmeLcSatOffI = rtb_LookUp_IR_S16_pie;

  /* End of Outputs for SubSystem: '<S34>/Calc_Lc_SatLim' */

  /* Outputs for Atomic SubSystem: '<S34>/Assign_Lc_SatLim' */
  /* DataStoreWrite: '<S77>/Data Store Write3' incorporates:
   *  Constant: '<S77>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S77>/Data Store Write4' incorporates:
   *  Constant: '<S77>/ZERO2'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S77>/Data Store Write6' incorporates:
   *  Constant: '<S77>/ONE2'
   */
  FlgLcLim = 1U;

  /* DataStoreWrite: '<S77>/Data Store Write8' incorporates:
   *  Constant: '<S77>/ZERO7'
   */
  LcActive = 1U;

  /* End of Outputs for SubSystem: '<S34>/Assign_Lc_SatLim' */
}

/* Output and update for function-call system: '<S4>/Lc_Ret' */
void LaunchCtrl_Lc_Ret(uint8_T rtu_flgRetTO, int16_T rtu_CmeDriverP)
{
  /* local block i/o variables */
  int16_T rtb_RateLimiter_S16_hz5;
  int16_T rtb_DataStoreRead1_kl4;

  /* Outputs for Atomic SubSystem: '<S33>/Calc_Lc_Ret' */
  /* DataStoreRead: '<S72>/Data Store Read1' */
  rtb_DataStoreRead1_kl4 = CmeLcSatOffI;

  /* S-Function (RateLimiter_S16): '<S73>/RateLimiter_S16' incorporates:
   *  Constant: '<S72>/LCRTCMERET'
   *  Constant: '<S72>/MIN'
   *  Constant: '<S72>/ZERO'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_hz5, 0, rtb_DataStoreRead1_kl4, -32736,
                  LCRTCMERET);

  /* DataStoreWrite: '<S72>/Data Store Write1' */
  CmeLcSatOffI = rtb_RateLimiter_S16_hz5;

  /* DataStoreRead: '<S72>/Data Store Read2' */
  rtb_DataStoreRead1_kl4 = CmeLcSat;

  /* Switch: '<S72>/Switch' incorporates:
   *  Constant: '<S72>/LCRTCMERET'
   *  Constant: '<S72>/LCRTCMERETLOW'
   */
  if (rtu_flgRetTO != 0) {
    rtb_RateLimiter_S16_hz5 = LCRTCMERETLOW;
  } else {
    rtb_RateLimiter_S16_hz5 = LCRTCMERET;
  }

  /* End of Switch: '<S72>/Switch' */

  /* S-Function (RateLimiter_S16): '<S74>/RateLimiter_S16' incorporates:
   *  Constant: '<S72>/MIN'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_hz5, rtu_CmeDriverP,
                  rtb_DataStoreRead1_kl4, -32736, rtb_RateLimiter_S16_hz5);

  /* DataStoreWrite: '<S72>/Data Store Write2' */
  CmeLcSat = rtb_RateLimiter_S16_hz5;

  /* End of Outputs for SubSystem: '<S33>/Calc_Lc_Ret' */

  /* Outputs for Atomic SubSystem: '<S33>/Assign_Lc_Ret' */
  /* DataStoreWrite: '<S71>/Data Store Write3' incorporates:
   *  Constant: '<S71>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S71>/Data Store Write4' incorporates:
   *  Constant: '<S71>/ZERO2'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S71>/Data Store Write6' incorporates:
   *  Constant: '<S71>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S71>/Data Store Write8' incorporates:
   *  Constant: '<S71>/ZERO7'
   */
  LcActive = 0U;

  /* End of Outputs for SubSystem: '<S33>/Assign_Lc_Ret' */
}

/* Output and update for function-call system: '<S4>/Lc_Reduct' */
void LaunchCtrl_Lc_Reduct(void)
{
  /* local block i/o variables */
  int16_T rtb_RateLimiter_S16_cna;
  int16_T rtb_DataStoreRead1_et0;
  int16_T rtb_Gain;

  /* Outputs for Atomic SubSystem: '<S32>/Assign_Lc_Reduct' */
  /* DataStoreWrite: '<S65>/Data Store Write3' incorporates:
   *  Constant: '<S65>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S65>/Data Store Write4' incorporates:
   *  Constant: '<S65>/ZERO2'
   */
  FlgCtfLc = 1U;

  /* DataStoreWrite: '<S65>/Data Store Write6' incorporates:
   *  Constant: '<S65>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S65>/Data Store Write8' incorporates:
   *  Constant: '<S65>/ZERO7'
   */
  LcActive = 0U;

  /* End of Outputs for SubSystem: '<S32>/Assign_Lc_Reduct' */

  /* Outputs for Atomic SubSystem: '<S32>/Calc_Lc_Reduct' */
  /* DataStoreRead: '<S66>/Data Store Read1' */
  rtb_DataStoreRead1_et0 = CmeLcSatOffI;

  /* Gain: '<S66>/Gain' incorporates:
   *  Constant: '<S66>/LCRTCMERED'
   */
  rtb_Gain = (int16_T)(-LCRTCMERED);

  /* S-Function (RateLimiter_S16): '<S67>/RateLimiter_S16' incorporates:
   *  Constant: '<S66>/MIN'
   *  Constant: '<S66>/ZERO1'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_cna, 0, rtb_DataStoreRead1_et0, -32736,
                  rtb_Gain);

  /* DataStoreWrite: '<S66>/Data Store Write1' */
  CmeLcSatOffI = rtb_RateLimiter_S16_cna;

  /* DataStoreRead: '<S66>/Data Store Read2' */
  rtb_DataStoreRead1_et0 = CmeLcSat;

  /* S-Function (RateLimiter_S16): '<S68>/RateLimiter_S16' incorporates:
   *  Constant: '<S66>/LCCMETRGRED'
   *  Constant: '<S66>/LCRTCMERED'
   *  Constant: '<S66>/MAX'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_cna, LCCMETRGRED, rtb_DataStoreRead1_et0,
                  LCRTCMERED, 32736);

  /* DataStoreWrite: '<S66>/Data Store Write2' */
  CmeLcSat = rtb_RateLimiter_S16_cna;

  /* End of Outputs for SubSystem: '<S32>/Calc_Lc_Reduct' */
}

/* System initialize for function-call system: '<S1>/T10ms' */
void LaunchCtrl_T10ms_Init(void)
{
  /* SystemInitialize for Chart: '<S43>/Enable_Lc_Km' */
  LaunchCtrl_DWork.is_active_c1_LaunchCtrl = 0U;
  LaunchCtrl_DWork.is_c1_LaunchCtrl = 0;
  LaunchCtrl_DWork.offLcKm = 0;
  LaunchCtrl_DWork.tmpOdometer = 0;
  LaunchCtrl_DWork.enLcKm = 0U;

  /* SystemInitialize for Chart: '<S4>/Chart_LaunchCtrl' */
  LaunchCtrl_DWork.is_LC_COND_CTRL = 0;
  LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = 0;
  LaunchCtrl_DWork.is_active_c3_LaunchCtrl = 0U;
  LaunchCtrl_DWork.cntWAMsg = 0U;
  LaunchCtrl_DWork.flgRetTO = 0U;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void LaunchCtrl_T10ms(void)
{
  boolean_T rtb_LogicalOperator1_npk;
  uint8_T rtb_Merge;
  uint8_T rtb_SetTracCtrl0;
  uint8_T rtb_AwLevel0;
  int32_T rtb_DataTypeConversion_ni2;
  uint8_T rtb_LogicalOperator1_brp;
  uint32_T tmp;

  /* Logic: '<S26>/Logical Operator1' incorporates:
   *  Constant: '<S26>/ST_SAT_MAX'
   *  Inport: '<Root>/FlgYawRec'
   *  Inport: '<Root>/StSatAw'
   *  RelationalOperator: '<S26>/Relational Operator1'
   */
  rtb_LogicalOperator1_npk = ((ST_SAT_MAX == StSatAw) && (FlgYawRec != 0));

  /* Switch: '<S26>/Switch' incorporates:
   *  Constant: '<S26>/ENAWMAXLEVEL'
   *  Constant: '<S26>/REC_AW'
   *  DataStoreWrite: '<S26>/Data Store Write2'
   */
  if (rtb_LogicalOperator1_npk) {
    EnAwMaxLevel = 4U;
  } else {
    EnAwMaxLevel = ENAWMAXLEVEL;
  }

  /* End of Switch: '<S26>/Switch' */

  /* Logic: '<S36>/Logical Operator' incorporates:
   *  Constant: '<S36>/DIAG_CLUTCH'
   *  Constant: '<S36>/DIAG_GEAR_SENSOR'
   *  Constant: '<S36>/DIAG_VEHSPEED'
   *  Constant: '<S36>/DIAG_VEHSPEED_FRONT'
   *  Constant: '<S36>/FAULT'
   *  Constant: '<S36>/FAULT1'
   *  Constant: '<S36>/FAULT2'
   *  Constant: '<S36>/FAULT3'
   *  Constant: '<S44>/Constant'
   *  DataStoreWrite: '<S36>/Data Store Write1'
   *  Inport: '<Root>/FlgYawRec'
   *  Inport: '<Root>/StDiag'
   *  RelationalOperator: '<S36>/Relational Operator'
   *  RelationalOperator: '<S36>/Relational Operator1'
   *  RelationalOperator: '<S36>/Relational Operator2'
   *  RelationalOperator: '<S36>/Relational Operator3'
   *  RelationalOperator: '<S44>/Compare'
   *  Selector: '<S36>/Selector'
   *  Selector: '<S36>/Selector1'
   *  Selector: '<S36>/Selector2'
   *  Selector: '<S36>/Selector3'
   */
  FlgLcDiag = (uint8_T)(((((StDiag[(DIAG_GEAR_SENSOR)] != FAULT) && (StDiag
    [(DIAG_CLUTCH)] != FAULT)) && (StDiag[(DIAG_VEHSPEED_FRONT)] != FAULT)) &&
    (StDiag[(DIAG_VEHSPEED)] != FAULT)) && (FlgYawRec == 0));

  /* Logic: '<S37>/Logical Operator' incorporates:
   *  Constant: '<S37>/ENLCCTRL'
   *  Constant: '<S46>/Constant'
   *  DataStoreWrite: '<S36>/Data Store Write1'
   *  DataStoreWrite: '<S37>/Data Store Write1'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/VehSpeedFront'
   *  RelationalOperator: '<S45>/Compare'
   *  RelationalOperator: '<S46>/Compare'
   */
  FlgLcEn = (uint8_T)(((((ENLCCTRL != 0) && (VehSpeedFront == 0)) && (GearPos ==
    0)) && (FlgLcDiag != 0)) && (EndStartFlg != 0));

  /* MinMax: '<S40>/MinMax' incorporates:
   *  Constant: '<S40>/ENLCMAXLEVEL'
   *  Inport: '<Root>/LcLevelCAN'
   */
  if (ENLCMAXLEVEL < LcLevelCAN) {
    LcLevel = ENLCMAXLEVEL;
  } else {
    LcLevel = LcLevelCAN;
  }

  /* End of MinMax: '<S40>/MinMax' */

  /* DataTypeConversion: '<S43>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/Odometer'
   */
  rtb_DataTypeConversion_ni2 = (int32_T)Odometer;

  /* Chart: '<S43>/Enable_Lc_Km' incorporates:
   *  Inport: '<Root>/TWater'
   */
  /* Gateway: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
  /* During: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
  if (LaunchCtrl_DWork.is_active_c1_LaunchCtrl == 0U) {
    /* Entry: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
    LaunchCtrl_DWork.is_active_c1_LaunchCtrl = 1U;

    /* Entry Internal: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
    /* Transition: '<S51>:2' */
    LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_ni2;
    tmp = EELcKm;
    if (EELcKm > 2147483647U) {
      tmp = 2147483647U;
    }

    LaunchCtrl_DWork.offLcKm = (int32_T)tmp;
    LaunchCtrl_DWork.enLcKm = 0U;
    LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
  } else {
    switch (LaunchCtrl_DWork.is_c1_LaunchCtrl) {
     case LaunchCtrl_IN_KM:
      /* During 'KM': '<S51>:1' */
      /* Transition: '<S51>:5' */
      if ((LaunchCtrl_DWork.tmpOdometer >= 0) && (rtb_DataTypeConversion_ni2 <
           (LaunchCtrl_DWork.tmpOdometer - MAX_int32_T))) {
        rtb_DataTypeConversion_ni2 = MAX_int32_T;
      } else if ((LaunchCtrl_DWork.tmpOdometer < 0) &&
                 (rtb_DataTypeConversion_ni2 > (LaunchCtrl_DWork.tmpOdometer -
                   MIN_int32_T))) {
        rtb_DataTypeConversion_ni2 = MIN_int32_T;
      } else {
        rtb_DataTypeConversion_ni2 = LaunchCtrl_DWork.tmpOdometer -
          rtb_DataTypeConversion_ni2;
      }

      if ((rtb_DataTypeConversion_ni2 < 0) && (LaunchCtrl_DWork.offLcKm <
           (MIN_int32_T - rtb_DataTypeConversion_ni2))) {
        rtb_DataTypeConversion_ni2 = MIN_int32_T;
      } else if ((rtb_DataTypeConversion_ni2 > 0) && (LaunchCtrl_DWork.offLcKm >
                  (MAX_int32_T - rtb_DataTypeConversion_ni2))) {
        rtb_DataTypeConversion_ni2 = MAX_int32_T;
      } else {
        rtb_DataTypeConversion_ni2 += LaunchCtrl_DWork.offLcKm;
      }

      if (0 > rtb_DataTypeConversion_ni2) {
        rtb_DataTypeConversion_ni2 = 0;
      }

      EELcKm = (uint32_T)rtb_DataTypeConversion_ni2;
      if ((rtb_DataTypeConversion_ni2 <= 0) && (LcTrip != 0)) {
        /* Transition: '<S51>:6' */
        if (LcTrip >= NUMLCTRIP) {
          /* Transition: '<S51>:10' */
          LaunchCtrl_DWork.enLcKm = 1U;
          LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_ONE;
        } else {
          /* Transition: '<S51>:12' */
          if (LcTrip >= (NUMLCTRIP - 1)) {
            /* Transition: '<S51>:13' */
            LaunchCtrl_DWork.enLcKm = 1U;
            LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_TWO;
          } else {
            /* Transition: '<S51>:20' */
            if (LcTrip >= (NUMLCTRIP - 2)) {
              /* Transition: '<S51>:22' */
              LaunchCtrl_DWork.enLcKm = 1U;
              LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_THREE;
            } else {
              /* Transition: '<S51>:23' */
            }
          }
        }
      } else {
        /* Transition: '<S51>:7' */
      }
      break;

     case LaunchCtrl_IN_ONE:
      /* During 'ONE': '<S51>:3' */
      /* Transition: '<S51>:15' */
      if ((TWater < THRTEMPLC) && (LcTrip >= (NUMLCTRIP - 1))) {
        /* Transition: '<S51>:16' */
        LaunchCtrl_DWork.enLcKm = 1U;
        LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_TWO;
      } else {
        if (LcTrip < NUMLCTRIP) {
          /* Transition: '<S51>:17' */
          LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_ni2;
          LaunchCtrl_DWork.offLcKm = NLCKM;
          LaunchCtrl_DWork.enLcKm = 0U;
          LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
        }
      }
      break;

     case LaunchCtrl_IN_THREE:
      /* During 'THREE': '<S51>:18' */
      /* Transition: '<S51>:29' */
      if (LcTrip < (NUMLCTRIP - 2)) {
        /* Transition: '<S51>:28' */
        LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_ni2;
        LaunchCtrl_DWork.offLcKm = NLCKM;
        LaunchCtrl_DWork.enLcKm = 0U;
        LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
      } else {
        /* Transition: '<S51>:30' */
      }
      break;

     default:
      /* During 'TWO': '<S51>:8' */
      /* Transition: '<S51>:25' */
      if (LcTrip < (NUMLCTRIP - 1)) {
        /* Transition: '<S51>:26' */
        LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_ni2;
        LaunchCtrl_DWork.offLcKm = NLCKM;
        LaunchCtrl_DWork.enLcKm = 0U;
        LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
      } else {
        /* Transition: '<S51>:31' */
      }
      break;
    }
  }

  /* End of Chart: '<S43>/Enable_Lc_Km' */

  /* Logic: '<S40>/Logical Operator' incorporates:
   *  Constant: '<S40>/ZERO'
   *  DataStoreWrite: '<S40>/Data Store Write1'
   *  DataStoreWrite: '<S40>/Data Store Write3'
   *  Inport: '<Root>/EndStartFlg'
   *  RelationalOperator: '<S40>/Relational Operator'
   */
  FlgLcLevel = (uint8_T)(((LcLevel != 0) && (EndStartFlg != 0)) &&
    (LaunchCtrl_DWork.enLcKm != 0));

  /* If: '<S41>/If' incorporates:
   *  DataStoreRead: '<S41>/Data Store Read'
   */
  if (FlgLcReady != 0) {
    /* Outputs for IfAction SubSystem: '<S41>/Reset_Idle' incorporates:
     *  ActionPort: '<S48>/Action Port'
     */
    /* Logic: '<S48>/Logical Operator1' incorporates:
     *  Constant: '<S48>/LCGASIDLE'
     *  Constant: '<S48>/LCGEAREND'
     *  DataStoreWrite: '<S36>/Data Store Write1'
     *  DataStoreWrite: '<S40>/Data Store Write1'
     *  Inport: '<Root>/GasPosCC'
     *  Inport: '<Root>/GearPos'
     *  RelationalOperator: '<S48>/Relational Operator1'
     *  RelationalOperator: '<S48>/Relational Operator2'
     */
    rtb_Merge = (uint8_T)((((FlgLcDiag != 0) && (GearPos < LCGEAREND)) &&
      (GasPosCC >= LCGASIDLE)) && (FlgLcLevel != 0));

    /* End of Outputs for SubSystem: '<S41>/Reset_Idle' */
  } else {
    /* Outputs for IfAction SubSystem: '<S41>/Set_Ready' incorporates:
     *  ActionPort: '<S49>/Action Port'
     */
    /* Logic: '<S49>/Logical Operator1' incorporates:
     *  Constant: '<S49>/CLUTCH_DISENGAGED'
     *  Constant: '<S49>/GEAR_1'
     *  Constant: '<S49>/LCGASWOT'
     *  DataStoreWrite: '<S36>/Data Store Write1'
     *  DataStoreWrite: '<S40>/Data Store Write1'
     *  Inport: '<Root>/ClutchSignal'
     *  Inport: '<Root>/GasPosCC'
     *  Inport: '<Root>/GearPos'
     *  Inport: '<Root>/VehSpeedFront'
     *  RelationalOperator: '<S49>/Relational Operator'
     *  RelationalOperator: '<S49>/Relational Operator1'
     *  RelationalOperator: '<S49>/Relational Operator2'
     *  RelationalOperator: '<S50>/Compare'
     */
    rtb_Merge = (uint8_T)((((((FlgLcDiag != 0) && (GasPosCC >= LCGASWOT)) &&
      (FlgLcLevel != 0)) && (GearPos == 1)) && (VehSpeedFront == 0)) &&
                          (ClutchSignal == 0));

    /* End of Outputs for SubSystem: '<S41>/Set_Ready' */
  }

  /* End of If: '<S41>/If' */

  /* Logic: '<S39>/Logical Operator1' incorporates:
   *  DataStoreWrite: '<S36>/Data Store Write1'
   *  DataStoreWrite: '<S39>/Data Store Write2'
   *  Inport: '<Root>/FlgSpringUp'
   *  Logic: '<S39>/Logical Operator'
   */
  FlgLcLaunch = (uint8_T)((FlgLcDiag != 0) && (FlgSpringUp == 0));

  /* Logic: '<S38>/Logical Operator1' incorporates:
   *  Constant: '<S38>/LCGASIDLE'
   *  Constant: '<S38>/LCGEAREND'
   *  Constant: '<S38>/LCVEHSPDEND'
   *  DataStoreWrite: '<S36>/Data Store Write1'
   *  DataStoreWrite: '<S38>/Data Store Write2'
   *  Inport: '<Root>/GasPosCC'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/VehSpeedFront'
   *  Logic: '<S38>/Logical Operator'
   *  RelationalOperator: '<S38>/Relational Operator'
   *  RelationalOperator: '<S38>/Relational Operator1'
   *  RelationalOperator: '<S38>/Relational Operator2'
   */
  FlgLcEnd = (uint8_T)((((GasPosCC <= LCGASIDLE) || (GearPos >= LCGEAREND)) ||
                        (VehSpeedFront >= LCVEHSPDEND)) || (FlgLcDiag == 0));

  /* Logic: '<S42>/Logical Operator' incorporates:
   *  Constant: '<S42>/LCHYSCMIRET'
   *  DataStoreWrite: '<S42>/Data Store Write2'
   *  Inport: '<Root>/CmiDriverI'
   *  Inport: '<Root>/CmiDriverP'
   *  Inport: '<Root>/CmiTargetI'
   *  Inport: '<Root>/CmiTargetP'
   *  RelationalOperator: '<S42>/Relational Operator1'
   *  RelationalOperator: '<S42>/Relational Operator3'
   *  Sum: '<S42>/Add'
   *  Sum: '<S42>/Add1'
   */
  FlgLcRet = (uint8_T)((((int16_T)(CmiTargetP + LCHYSCMIRET)) >= CmiDriverP) &&
                       (((int16_T)(CmiTargetI + LCHYSCMIRET)) >= CmiDriverI));

  /* RelationalOperator: '<S42>/Relational Operator2' incorporates:
   *  Constant: '<S42>/LCGASIDLE'
   *  DataStoreWrite: '<S42>/Data Store Write1'
   *  Inport: '<Root>/GasPosCC'
   */
  FlgLcCmiLow = (uint8_T)(GasPosCC <= LCGASIDLE);

  /* Chart: '<S4>/Chart_LaunchCtrl' incorporates:
   *  DataStoreWrite: '<S37>/Data Store Write1'
   *  DataStoreWrite: '<S38>/Data Store Write2'
   *  DataStoreWrite: '<S39>/Data Store Write2'
   *  DataStoreWrite: '<S40>/Data Store Write1'
   *  DataStoreWrite: '<S40>/Data Store Write3'
   *  DataStoreWrite: '<S42>/Data Store Write1'
   *  DataStoreWrite: '<S42>/Data Store Write2'
   *  Inport: '<Root>/AwLevel'
   *  Inport: '<Root>/CmeDriverP'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/LcToExitCAN'
   *  Inport: '<Root>/SetTracCtrlCAN'
   *  Inport: '<Root>/VehRbVfAxInt'
   *  Inport: '<Root>/VehSpeedFront'
   */
  /* Gateway: LaunchCtrl/T10ms/Chart_LaunchCtrl */
  /* During: LaunchCtrl/T10ms/Chart_LaunchCtrl */
  if (LaunchCtrl_DWork.is_active_c3_LaunchCtrl == 0U) {
    /* Entry: LaunchCtrl/T10ms/Chart_LaunchCtrl */
    LaunchCtrl_DWork.is_active_c3_LaunchCtrl = 1U;

    /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
    /* Entry Internal: LaunchCtrl/T10ms/Chart_LaunchCtrl */
    /* Entry Internal 'LC_COND_CTRL': '<S28>:73' */
    /* Transition: '<S28>:18' */
    /* Event: '<S28>:128' */
    LaunchCtrl_Lc_Dis();

    /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
    StLc = ((uint8_T)LC_DISABLE);
    LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;

    /* Entry Internal 'LC_OVERRIDE_CTRL': '<S28>:74' */
    /* Transition: '<S28>:178' */
    rtb_AwLevel0 = AwLevelCAN;
    rtb_SetTracCtrl0 = SetTracCtrlCAN;
    LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
  } else {
    /* During 'LC_COND_CTRL': '<S28>:73' */
    switch (LaunchCtrl_DWork.is_LC_COND_CTRL) {
     case LaunchCtrl_IN_LC_DISABLE:
      /* During 'LC_DISABLE': '<S28>:17' */
      /* Transition: '<S28>:21' */
      if (FlgLcEn != 0) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Transition: '<S28>:23' */
        /* Event: '<S28>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
        StLc = ((uint8_T)LC_ENABLING);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_ENABLING;
      } else {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Transition: '<S28>:22' */
        /* Event: '<S28>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      }
      break;

     case LaunchCtrl_IN_LC_ENABLING:
      /* During 'LC_ENABLING': '<S28>:19' */
      /* Transition: '<S28>:26' */
      if (FlgLcEn == 0) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Transition: '<S28>:37' */
        /* Event: '<S28>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
        StLc = ((uint8_T)LC_DISABLE);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;
      } else {
        /* Transition: '<S28>:36' */
        if ((FlgLcLevel != 0) && (EndStartFlg != 0)) {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
          /* Transition: '<S28>:28' */
          /* Event: '<S28>:129' */
          LaunchCtrl_Lc_Idle();

          /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
          StLc = ((uint8_T)LC_WAIT_IDLE);
          LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_WAIT_IDLE;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
          /* Transition: '<S28>:27' */
          /* Event: '<S28>:128' */
          LaunchCtrl_Lc_Dis();

          /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
        }
      }
      break;

     case LaunchCtrl_IN_LC_LAUNCH:
      /* During 'LC_LAUNCH': '<S28>:49' */
      /* Transition: '<S28>:52' */
      if (FlgLcEnd != 0) {
        /* Transition: '<S28>:54' */
        LaunchCtrl_DWork.flgRetTO = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Ret' */
        /* Event: '<S28>:132' */
        LaunchCtrl_Lc_Ret(LaunchCtrl_DWork.flgRetTO, CmeDriverP);

        /* End of Outputs for SubSystem: '<S4>/Lc_Ret' */
        StLc = ((uint8_T)LC_RET);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_RET;
      } else {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_SatLim' */
        /* Transition: '<S28>:55' */
        /* Event: '<S28>:131' */
        LaunchCtrl_Lc_SatLim(VehSpeedFront, VehRbVfAxInt);

        /* End of Outputs for SubSystem: '<S4>/Lc_SatLim' */
      }
      break;

     case LaunchCtrl_IN_LC_READY:
      /* During 'LC_READY': '<S28>:38' */
      /* Transition: '<S28>:42' */
      if (FlgLcLaunch != 0) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_SatLim' */
        /* Transition: '<S28>:50' */
        /* Event: '<S28>:131' */
        LaunchCtrl_Lc_SatLim(VehSpeedFront, VehRbVfAxInt);

        /* End of Outputs for SubSystem: '<S4>/Lc_SatLim' */
        rtb_DataTypeConversion_ni2 = EELcTrip + 1;
        if (rtb_DataTypeConversion_ni2 > 65535) {
          rtb_DataTypeConversion_ni2 = 65535;
        }

        EELcTrip = (uint16_T)rtb_DataTypeConversion_ni2;
        StLc = ((uint8_T)LC_LAUNCH);
        if (LcTrip != 0) {
          /* Transition: '<S28>:175' */
          rtb_DataTypeConversion_ni2 = LcTrip - 1;
          if (rtb_DataTypeConversion_ni2 < -128) {
            rtb_DataTypeConversion_ni2 = -128;
          }

          LcTrip = (int8_T)rtb_DataTypeConversion_ni2;
        } else {
          /* Transition: '<S28>:176' */
        }

        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_LAUNCH;
      } else {
        /* Transition: '<S28>:67' */
        if (((rtb_Merge == 0) || (LcToExitCAN != 0)) || (EndStartFlg == 0)) {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Reduct' */
          /* Transition: '<S28>:65' */
          /* Event: '<S28>:168' */
          LaunchCtrl_Lc_Reduct();

          /* End of Outputs for SubSystem: '<S4>/Lc_Reduct' */
          StLc = ((uint8_T)LC_TO_LIM);

          /* Transition: '<S28>:135' */
          LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_TO_LIM;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_CtfLim' */
          /* Transition: '<S28>:48' */
          /* Event: '<S28>:130' */
          LaunchCtrl_Lc_CtfLim(VehSpeedFront, VehRbVfAxInt);

          /* End of Outputs for SubSystem: '<S4>/Lc_CtfLim' */
        }
      }
      break;

     case LaunchCtrl_IN_LC_RET:
      /* During 'LC_RET': '<S28>:76' */
      /* Transition: '<S28>:78' */
      if (FlgLcRet != 0) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Transition: '<S28>:79' */
        /* Event: '<S28>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
        StLc = ((uint8_T)LC_DISABLE);

        /* Transition: '<S28>:56' */
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;
      } else {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Ret' */
        /* Transition: '<S28>:80' */
        /* Event: '<S28>:132' */
        LaunchCtrl_Lc_Ret(LaunchCtrl_DWork.flgRetTO, CmeDriverP);

        /* End of Outputs for SubSystem: '<S4>/Lc_Ret' */
      }
      break;

     case LaunchCtrl_IN_LC_TO_IDLE:
      /* During 'LC_TO_IDLE': '<S28>:64' */
      /* Transition: '<S28>:221' */
      if ((FlgLcLevel == 0) || (EndStartFlg == 0)) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Transition: '<S28>:148' */
        /* Event: '<S28>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
        StLc = ((uint8_T)LC_ENABLING);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_ENABLING;
      } else {
        /* Transition: '<S28>:222' */
        if ((rtb_Merge == 0) && (LaunchCtrl_DWork.cntWAMsg >= CNTLCWAMSG)) {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
          /* Transition: '<S28>:70' */
          /* Event: '<S28>:129' */
          LaunchCtrl_Lc_Idle();

          /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
          StLc = ((uint8_T)LC_WAIT_IDLE);
          LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_WAIT_IDLE;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
          /* Transition: '<S28>:220' */
          /* Event: '<S28>:129' */
          LaunchCtrl_Lc_Idle();

          /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
          rtb_DataTypeConversion_ni2 = LaunchCtrl_DWork.cntWAMsg + 1;
          if (rtb_DataTypeConversion_ni2 > 255) {
            rtb_DataTypeConversion_ni2 = 255;
          }

          LaunchCtrl_DWork.cntWAMsg = (uint8_T)rtb_DataTypeConversion_ni2;
        }
      }
      break;

     case LaunchCtrl_IN_LC_TO_LIM:
      /* During 'LC_TO_LIM': '<S28>:133' */
      /* Transition: '<S28>:225' */
      if ((FlgLcCmiLow != 0) || (EndStartFlg == 0)) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
        /* Transition: '<S28>:143' */
        /* Event: '<S28>:129' */
        LaunchCtrl_Lc_Idle();

        /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
        LaunchCtrl_DWork.cntWAMsg = 0U;
        StLc = ((uint8_T)LC_TO_IDLE);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_TO_IDLE;
      } else {
        /* Transition: '<S28>:224' */
        if (FlgLcLaunch != 0) {
          /* Transition: '<S28>:139' */
          LaunchCtrl_DWork.flgRetTO = 1U;

          /* Outputs for Function Call SubSystem: '<S4>/Lc_Ret' */
          /* Event: '<S28>:132' */
          LaunchCtrl_Lc_Ret(LaunchCtrl_DWork.flgRetTO, CmeDriverP);

          /* End of Outputs for SubSystem: '<S4>/Lc_Ret' */
          rtb_DataTypeConversion_ni2 = EELcTrip + 1;
          if (rtb_DataTypeConversion_ni2 > 65535) {
            rtb_DataTypeConversion_ni2 = 65535;
          }

          EELcTrip = (uint16_T)rtb_DataTypeConversion_ni2;
          StLc = ((uint8_T)LC_RET);
          if (LcTrip != 0) {
            /* Transition: '<S28>:229' */
            rtb_DataTypeConversion_ni2 = LcTrip - 1;
            if (rtb_DataTypeConversion_ni2 < -128) {
              rtb_DataTypeConversion_ni2 = -128;
            }

            LcTrip = (int8_T)rtb_DataTypeConversion_ni2;
          } else {
            /* Transition: '<S28>:230' */
          }

          /* Transition: '<S28>:232' */
          /* Transition: '<S28>:233' */
          LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_RET;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Reduct' */
          /* Transition: '<S28>:144' */
          /* Event: '<S28>:168' */
          LaunchCtrl_Lc_Reduct();

          /* End of Outputs for SubSystem: '<S4>/Lc_Reduct' */
        }
      }
      break;

     default:
      /* During 'LC_WAIT_IDLE': '<S28>:24' */
      /* Transition: '<S28>:30' */
      if ((FlgLcLevel == 0) || (EndStartFlg == 0)) {
        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Transition: '<S28>:31' */
        /* Event: '<S28>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
        StLc = ((uint8_T)LC_ENABLING);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_ENABLING;
      } else {
        /* Transition: '<S28>:33' */
        if (rtb_Merge != 0) {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_CtfLim' */
          /* Transition: '<S28>:39' */
          /* Event: '<S28>:130' */
          LaunchCtrl_Lc_CtfLim(VehSpeedFront, VehRbVfAxInt);

          /* End of Outputs for SubSystem: '<S4>/Lc_CtfLim' */
          StLc = ((uint8_T)LC_READY);
          LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_READY;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
          /* Transition: '<S28>:34' */
          /* Event: '<S28>:129' */
          LaunchCtrl_Lc_Idle();

          /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
        }
      }
      break;
    }

    /* During 'LC_OVERRIDE_CTRL': '<S28>:74' */
    switch (LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL) {
     case LaunchCtrl_IN_LC_NONE:
      /* During 'LC_NONE': '<S28>:177' */
      /* Transition: '<S28>:181' */
      if (StLc == ((uint8_T)LC_WAIT_IDLE)) {
        /* Transition: '<S28>:182' */
        rtb_AwLevel0 = VTAWOVRLEV[0];
        rtb_SetTracCtrl0 = VTTCOVRLEV[0];
        LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = Launc_IN_LC_OVERRIDE_PRE_LAUNCH;
      } else {
        /* Transition: '<S28>:183' */
        rtb_AwLevel0 = AwLevelCAN;
        rtb_SetTracCtrl0 = SetTracCtrlCAN;
      }
      break;

     case LaunchCtr_IN_LC_OVERRIDE_LAUNCH:
      /* During 'LC_OVERRIDE_LAUNCH': '<S28>:196' */
      /* Transition: '<S28>:202' */
      if (StLc == ((uint8_T)LC_RET)) {
        /* Transition: '<S28>:204' */
        /* Transition: '<S28>:214' */
        rtb_AwLevel0 = AwLevelCAN;
        rtb_SetTracCtrl0 = SetTracCtrlCAN;

        /* Transition: '<S28>:205' */
        LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
      } else {
        /* Transition: '<S28>:206' */
        rtb_AwLevel0 = VTAWOVRLEV[LcLevel];
        rtb_SetTracCtrl0 = VTTCOVRLEV[LcLevel];
      }
      break;

     default:
      /* During 'LC_OVERRIDE_PRE_LAUNCH': '<S28>:179' */
      /* Transition: '<S28>:185' */
      if (StLc == ((uint8_T)LC_LAUNCH)) {
        /* Transition: '<S28>:198' */
        rtb_AwLevel0 = VTAWOVRLEV[LcLevel];
        rtb_SetTracCtrl0 = VTTCOVRLEV[LcLevel];
        LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtr_IN_LC_OVERRIDE_LAUNCH;
      } else {
        /* Transition: '<S28>:186' */
        if (StLc == ((uint8_T)LC_RET)) {
          /* Transition: '<S28>:213' */
          /* Transition: '<S28>:214' */
          rtb_AwLevel0 = AwLevelCAN;
          rtb_SetTracCtrl0 = SetTracCtrlCAN;

          /* Transition: '<S28>:205' */
          LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
        } else {
          /* Transition: '<S28>:211' */
          if ((StLc == ((uint8_T)LC_DISABLE)) || (StLc == ((uint8_T)LC_ENABLING)))
          {
            /* Transition: '<S28>:187' */
            rtb_AwLevel0 = AwLevelCAN;
            rtb_SetTracCtrl0 = SetTracCtrlCAN;
            LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
          } else {
            /* Transition: '<S28>:200' */
            rtb_AwLevel0 = VTAWOVRLEV[0];
            rtb_SetTracCtrl0 = VTTCOVRLEV[0];
          }
        }
      }
      break;
    }
  }

  /* End of Chart: '<S4>/Chart_LaunchCtrl' */

  /* Switch: '<S26>/Switch1' incorporates:
   *  Constant: '<S26>/ZERO2'
   *  DataStoreWrite: '<S26>/Data Store Write2'
   */
  if (rtb_LogicalOperator1_npk) {
    rtb_LogicalOperator1_brp = 0U;
  } else {
    rtb_LogicalOperator1_brp = EnAwMaxLevel;
  }

  /* End of Switch: '<S26>/Switch1' */

  /* MinMax: '<S26>/MinMax' */
  if (rtb_LogicalOperator1_brp < rtb_AwLevel0) {
    AwLevel = rtb_LogicalOperator1_brp;
  } else {
    AwLevel = rtb_AwLevel0;
  }

  /* End of MinMax: '<S26>/MinMax' */

  /* MinMax: '<S26>/MinMax1' */
  SetTracCtrl = rtb_SetTracCtrl0;

  /* Switch: '<S26>/Switch2' incorporates:
   *  Constant: '<S26>/REC_TC'
   *  Constant: '<S26>/VTENTCMAXLEVEL'
   *  Constant: '<S35>/Constant'
   *  DataStoreWrite: '<S26>/Data Store Write4'
   *  Inport: '<Root>/FlgYawRec'
   *  Inport: '<Root>/RidingMode'
   *  Inport: '<Root>/TcDiagRec'
   *  Logic: '<S26>/Logical Operator'
   *  RelationalOperator: '<S35>/Compare'
   *  Selector: '<S26>/Selector'
   */
  if ((TcDiagRec != 0) || (FlgYawRec == 1)) {
    EnTcMaxLevel = 9U;
  } else {
    EnTcMaxLevel = VTENTCMAXLEVEL[RidingMode];
  }

  /* End of Switch: '<S26>/Switch2' */

  /* Chart: '<S40>/Sel_EnLcMaxLevel' incorporates:
   *  Inport: '<Root>/FlgYawRec'
   */
  /* Gateway: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel */
  /* During: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel */
  /* Entry Internal: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel */
  /* Transition: '<S47>:2' */
  if ((FlgYawRec != 0) && ((((StLc == ((uint8_T)LC_DISABLE)) || (StLc ==
          ((uint8_T)LC_ENABLING))) || (StLc == ((uint8_T)LC_TO_LIM))) || (StLc ==
        ((uint8_T)LC_TO_IDLE)))) {
    /* Transition: '<S47>:4' */
    rtb_AwLevel0 = 1U;
  } else {
    /* Transition: '<S47>:5' */
    rtb_AwLevel0 = 0U;
  }

  /* End of Chart: '<S40>/Sel_EnLcMaxLevel' */

  /* Switch: '<S40>/Switch' incorporates:
   *  Constant: '<S40>/ENLCMAXLEVEL'
   *  Constant: '<S40>/ZERO2'
   *  DataStoreWrite: '<S40>/Data Store Write2'
   */
  if (rtb_AwLevel0 != 0) {
    EnLcMaxLevel = 0U;
  } else {
    EnLcMaxLevel = ENLCMAXLEVEL;
  }

  /* End of Switch: '<S40>/Switch' */

  /* DataStoreWrite: '<S41>/Data Store Write2' */
  FlgLcReady = rtb_Merge;

  /* DataStoreWrite: '<S43>/Data Store Write2' */
  FlgEnLcKm = LaunchCtrl_DWork.enLcKm;

  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/PreTDC' */
void LaunchCtrl_PreTDC(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  int16_T rtb_LookUp_IR_S16;
  int16_T rtb_RateLimiter_S16;
  int16_T rtb_DataStoreRead3;
  boolean_T rtb_LogicalOperator_ie1;
  uint16_T rtb_DataTypeConversion_gqc;
  int16_T rtb_LookUp_S16_S16;
  int16_T rtb_Conversion1[4];
  int16_T rtb_Switch_bdw[4];
  uint8_T rtb_Conversion3_ctr;
  uint8_T rtb_DataTypeConversion8;
  int32_T i;

  {
    /* user code (Output function Header) */

    /* System '<S1>/PreTDC' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */

    /* DataStoreRead: '<S3>/Data Store Read3' */
    rtb_DataStoreRead3 = RpmLcTrgCtf;

    /* If: '<S3>/If' incorporates:
     *  DataStoreRead: '<S3>/Data Store Read'
     */
    if (FlgCtfLc != 0) {
      /* Outputs for IfAction SubSystem: '<S3>/Calc_IdxLcCutOff' incorporates:
       *  ActionPort: '<S11>/Action Port'
       */
      /* Switch: '<S14>/Switch' incorporates:
       *  Constant: '<S19>/Constant'
       *  DataStoreRead: '<S3>/Data Store Read1'
       *  RelationalOperator: '<S19>/Compare'
       */
      if (StLc != ((uint8_T)LC_TO_LIM)) {
        /* DataTypeConversion: '<S20>/Conversion3' incorporates:
         *  Constant: '<S14>/BKRPMLCTRGWOT_dim'
         */
        rtb_Conversion3_ctr = (uint8_T)BKRPMLCTRGWOT_dim;

        /* DataTypeConversion: '<S23>/Data Type Conversion8' incorporates:
         *  Constant: '<S18>/BKRPMLCTRGWOT_dim'
         */
        rtb_DataTypeConversion8 = (uint8_T)BKRPMLCTRGWOT_dim;

        /* Switch: '<S22>/Switch' incorporates:
         *  Constant: '<S22>/LCVEHAXINTSAT'
         *  Inport: '<Root>/VehSpeedFront'
         *  RelationalOperator: '<S22>/Relational Operator'
         */
        if (VehSpeedFront >= LCVEHAXINTSAT) {
          rtb_DataTypeConversion_gqc = VehSpeedFront;
        } else {
          /* DataTypeConversion: '<S22>/Data Type Conversion' incorporates:
           *  Inport: '<Root>/VehRbVfAxInt'
           */
          i = VehRbVfAxInt;
          if (VehRbVfAxInt < 0) {
            i = 0;
          } else {
            if (VehRbVfAxInt > 65535) {
              i = 65535;
            }
          }

          rtb_DataTypeConversion_gqc = (uint16_T)i;

          /* End of DataTypeConversion: '<S22>/Data Type Conversion' */

          /* MinMax: '<S22>/MinMax' */
          if (LCVEHAXINTSAT < rtb_DataTypeConversion_gqc) {
            rtb_DataTypeConversion_gqc = LCVEHAXINTSAT;
          }

          /* End of MinMax: '<S22>/MinMax' */

          /* MinMax: '<S22>/MinMax1' incorporates:
           *  DataStoreRead: '<S22>/Data Store Read'
           */
          if (VehSpeedFront > rtb_DataTypeConversion_gqc) {
            rtb_DataTypeConversion_gqc = VehSpeedFront;
          }

          if (rtb_DataTypeConversion_gqc > VehLcAxIntSat) {
          } else {
            rtb_DataTypeConversion_gqc = VehLcAxIntSat;
          }

          /* End of MinMax: '<S22>/MinMax1' */
        }

        /* End of Switch: '<S22>/Switch' */

        /* S-Function (PreLookUpIdSearch_U16): '<S23>/PreLookUpIdSearch_U16' incorporates:
         *  Constant: '<S18>/BKRPMLCTRGWOT'
         */
        PreLookUpIdSearch_U16( &rtb_DataTypeConversion_gqc,
                              &rtb_PreLookUpIdSearch_U16_o2,
                              rtb_DataTypeConversion_gqc, &BKRPMLCTRGWOT[0],
                              rtb_DataTypeConversion8);

        /* S-Function (LookUp_IR_S16): '<S20>/LookUp_IR_S16' incorporates:
         *  Constant: '<S14>/VTRPMLCTRGWOT'
         */
        LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTRPMLCTRGWOT[0],
                      rtb_DataTypeConversion_gqc, rtb_PreLookUpIdSearch_U16_o2,
                      rtb_Conversion3_ctr);
        rtb_DataStoreRead3 = rtb_LookUp_IR_S16;
      } else {
        /* S-Function (RateLimiter_S16): '<S21>/RateLimiter_S16' incorporates:
         *  Constant: '<S14>/LCRPMTRGRED'
         *  Constant: '<S14>/MAXRTLCRPMTRGRED'
         *  Constant: '<S14>/RTLCRPMTRGRED'
         */
        RateLimiter_S16( &rtb_RateLimiter_S16, LCRPMTRGRED, rtb_DataStoreRead3,
                        RTLCRPMTRGRED, 1000);
        rtb_DataStoreRead3 = rtb_RateLimiter_S16;
      }

      /* End of Switch: '<S14>/Switch' */

      /* DataTypeConversion: '<S13>/Data Type Conversion1' incorporates:
       *  Inport: '<Root>/Rpm'
       */
      rtb_LookUp_S16_S16 = (int16_T)Rpm;

      /* Sum: '<S13>/Add' */
      rtb_LookUp_S16_S16 = (int16_T)(rtb_DataStoreRead3 - rtb_LookUp_S16_S16);

      /* Relay: '<S15>/Relay'
       *
       * Block description for '<S15>/Relay':
       *  max: <DTRPMLCTRGWOT>
       *  min: <HYRPMLCTRGWOT>
       */
      LaunchCtrl_DWork.Relay_Mode = ((rtb_LookUp_S16_S16 >= DTRPMLCTRGWOT) ||
        ((rtb_LookUp_S16_S16 > HYRPMLCTRGWOT) && (LaunchCtrl_DWork.Relay_Mode)));

      /* Logic: '<S15>/Logical Operator' incorporates:
       *  Constant: '<S15>/THLCAXCTF'
       *  Inport: '<Root>/AxCAN'
       *  RelationalOperator: '<S15>/Relational Operator'
       *  Relay: '<S15>/Relay'
       *
       * Block description for '<S15>/Relay':
       *  max: <DTRPMLCTRGWOT>
       *  min: <HYRPMLCTRGWOT>
       */
      rtb_LogicalOperator_ie1 = ((AxCAN > THLCAXCTF) ||
        (!LaunchCtrl_DWork.Relay_Mode));

      /* DataStoreWrite: '<S3>/Data Store Write2' incorporates:
       *  DataTypeConversion: '<S13>/Data Type Conversion3'
       */
      LcRpmErr = rtb_LookUp_S16_S16;
      for (i = 0; i < 4; i++) {
        /* DataTypeConversion: '<S16>/Conversion1' incorporates:
         *  Constant: '<S13>/VTLCIDXCTF'
         *  DataTypeConversion: '<S13>/Data Type Conversion2'
         */
        rtb_Conversion1[i] = VTLCIDXCTF[i];

        /* Switch: '<S13>/Switch' incorporates:
         *  Constant: '<S13>/BKLCIDXCTF'
         *  Constant: '<S13>/BKLCIDXCTF2'
         */
        if (rtb_LogicalOperator_ie1) {
          rtb_Switch_bdw[i] = BKLCIDXCTF2[i];
        } else {
          rtb_Switch_bdw[i] = BKLCIDXCTF[i];
        }

        /* End of Switch: '<S13>/Switch' */
      }

      /* DataTypeConversion: '<S16>/Conversion3' incorporates:
       *  Constant: '<S13>/BKLCIDXCTF_dim'
       */
      rtb_Conversion3_ctr = (uint8_T)BKLCIDXCTF_dim;

      /* S-Function (LookUp_S16_S16): '<S16>/LookUp_S16_S16' */
      LookUp_S16_S16( &rtb_LookUp_S16_S16, &rtb_Conversion1[0],
                     rtb_LookUp_S16_S16, &rtb_Switch_bdw[0], rtb_Conversion3_ctr);

      /* DataStoreWrite: '<S3>/Data Store Write1' incorporates:
       *  DataTypeConversion: '<S13>/Data Type Conversion4'
       *  DataTypeConversion: '<S17>/Conversion'
       */
      IdxLcCutOff = (uint8_T)rtb_LookUp_S16_S16;

      /* DataStoreWrite: '<S3>/Data Store Write3' incorporates:
       *  DataTypeConversion: '<S13>/Data Type Conversion5'
       */
      RpmLcTrgCtf = rtb_DataStoreRead3;

      /* End of Outputs for SubSystem: '<S3>/Calc_IdxLcCutOff' */
    } else {
      /* Outputs for IfAction SubSystem: '<S3>/Calc_IdxLcCutOff1' incorporates:
       *  ActionPort: '<S12>/Action Port'
       */
      /* DataStoreWrite: '<S3>/Data Store Write1' incorporates:
       *  Constant: '<S12>/ZERO'
       */
      IdxLcCutOff = 0U;

      /* DataStoreWrite: '<S3>/Data Store Write3' incorporates:
       *  Constant: '<S12>/ZERO1'
       */
      RpmLcTrgCtf = 0;

      /* DataStoreWrite: '<S3>/Data Store Write2' incorporates:
       *  Constant: '<S12>/ZERO2'
       */
      LcRpmErr = 0;

      /* End of Outputs for SubSystem: '<S3>/Calc_IdxLcCutOff1' */
    }

    /* End of If: '<S3>/If' */
    /* user code (Output function Trailer) */

    /* System '<S1>/PreTDC' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void LaunchCtrl_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/LaunchCtrl' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((LaunchCtrl_U.ev_PowerOn > 0) &&
      (LaunchCtrl_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    LaunchCtrl_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  LaunchCtrl_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (LaunchCtrl_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc3' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PreTDC' */
  if ((LaunchCtrl_U.ev_PreTDC > 0) &&
      (LaunchCtrl_PrevZCSigState.trig_to_fc3_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PreTDC'
     */
    LaunchCtrl_PreTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  LaunchCtrl_PrevZCSigState.trig_to_fc3_Trig_ZCE = (ZCSigState)
    (LaunchCtrl_U.ev_PreTDC > 0);

  /* End of Inport: '<Root>/ev_PreTDC' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc3' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((LaunchCtrl_U.ev_T10ms > 0) &&
      (LaunchCtrl_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    LaunchCtrl_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  LaunchCtrl_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (LaunchCtrl_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* End of Outputs for SubSystem: '<Root>/LaunchCtrl' */
}

/* Model initialize function */
void LaunchCtrl_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  IDLaunchCtrl = 0U;

  /* states (dwork) */
  (void) memset((void *)&LaunchCtrl_DWork, 0,
                sizeof(D_Work_LaunchCtrl_T));

  /* custom states */
  RpmLcTrgCtf = 0;
  LcRpmErr = 0;
  RpmLcTrgCme = 0U;
  CmeLcSatOffI = 0;
  CmeLcSat = 0;
  VehLcAxIntSat = 0U;
  LcTrip = 0;
  StLc = 0U;
  AwLevel = 0U;
  SetTracCtrl = 0U;
  FlgLcDiag = 0U;
  LcLevel = 0U;
  EnLcMaxLevel = 0U;
  EnAwMaxLevel = 0U;
  EnTcMaxLevel = 0U;
  FlgLcCmiLow = 0U;
  FlgEnLcKm = 0U;
  FlgLcEn = 0U;
  LcActive = 0U;
  FlgLcReady = 0U;
  FlgLcLaunch = 0U;
  FlgLcEnd = 0U;
  FlgLcRet = 0U;
  IdxLcCutOff = 0U;
  FlgLcTrg = 0U;
  FlgCtfLc = 0U;
  FlgLcLevel = 0U;
  FlgLcLim = 0U;

  /* external inputs */
  (void)memset(&LaunchCtrl_U, 0, sizeof(ExternalInputs_LaunchCtrl_T));
  LaunchCtrl_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  LaunchCtrl_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
  LaunchCtrl_PrevZCSigState.trig_to_fc3_Trig_ZCE = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/LaunchCtrl' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/trig_to_fc2' */
  /* SystemInitialize for S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  LaunchCtrl_T10ms_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  /* End of SystemInitialize for SubSystem: '<S1>/trig_to_fc2' */

  /* End of SystemInitialize for SubSystem: '<Root>/LaunchCtrl' */
}

/* user code (bottom of source file) */
/* System '<Root>/LaunchCtrl' */
#endif                                 // _BUILD_LAUNCHCTRL_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
