@echo off
cls
if exist "C:\pemicro\progppcnexus1_33\PrgBinaryFolder\ENGINE.CFG" goto COMMAND
echo ERROR: WARNING Config Device absent.
echo *
goto END

:COMMAND
if exist "C:\pemicro\progppcnexus1_33\cprogppcnexus.exe" goto PROGRAM
echo ERROR: WARNING Command path.
echo *
goto END

:PROGRAM
START /W C:\pemicro\progppcnexus1_33\cprogppcnexus.exe SHOWPORTS=C:\pemicro\progppcnexus1_33\PrgBinaryFolder\MYPORTS.TXT
:END
ECHO Sequence terminated.
echo *
pause