/*****************************************************************************************************************/
/* $HeadURL::                                                                                               $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _LAMP_MGM_H_
#define _LAMP_MGM_H_

/* Public types */
typedef uint8_T typLampCmd;

#define ODE_DUMMY_Lamp 255

/* Public defines */
#define LAMP_OFF    0
#define LAMP_ON     1
#define LAMP_BLINK  2

#define PIN_LAMP    0
#define CAN_LAMP    1
#define ALL_LAMP    2

#define IMMOLED_ON  1
#define IMMOLED_OFF 0

/* Physical lamps section */
#define NUMPHYSLMP  4

#define IDN_LAMP_1  0
#define IDN_LAMP_2  1
#define IDN_LAMP_3  2
#define IDN_LAMP_4  3

/* Logical lamps section */
#define NUMLOGLMP   4

/* Logical lamp 1 */
#define IDN_WRNLAMP           0           /* Logical lamp mapping */
#define LOGLAMP_1_MAP         IDN_LAMP_1  /* Psysical lamp mapping */
#define LOGLAMP_1_PERIOD      10          /* Period in 100 msec units */
#define LOGLAMP_1_DUTY        10          /* Time on in 100 msec units */

/* Logical lamp 2 */
#define IDN_SAFLAMP           1           /* Logical lamp mapping */
#define LOGLAMP_2_MAP         IDN_LAMP_1  /* Psysical lamp mapping */
#define LOGLAMP_2_PERIOD      10          /* Period in 100 msec units */
#define LOGLAMP_2_DUTY        5           /* Time on in 100 msec units */

/* Logical lamp 3 */
#define IDN_MILLAMP           2           /* Logical lamp mapping */
#define LOGLAMP_3_MAP         IDN_LAMP_1  /* Psysical lamp mapping */
#define LOGLAMP_3_PERIOD      10          /* Period in 100 msec units */
#define LOGLAMP_3_DUTY        10          /* Time on in 100 msec units */

/* Logical lamp 4 */
#define IDN_MISFLAMP          3           /* Logical lamp mapping */
#define LOGLAMP_4_MAP         IDN_LAMP_1  /* Psysical lamp mapping */
#define LOGLAMP_4_PERIOD      10          /* Period in 100 msec units */
#define LOGLAMP_4_DUTY        5           /* Time on in 100 msec units */

/* Public variables */
extern typLampCmd WarningLmpCmd;
extern typLampCmd SafetyLmpCmd;
extern typLampCmd MILLmpCmd;
extern typLampCmd MisfLmpCmd;

extern  uint8_t PhysLampPeriod[NUMPHYSLMP];
extern  uint8_t PhysLampTOn[NUMPHYSLMP];
extern  uint8_t PhysLampTimer[NUMPHYSLMP];
extern  typLampCmd PhysLampState[NUMPHYSLMP];
extern  uint8_t PhysLampOut[NUMPHYSLMP];

extern  typLampCmd LogLampState[NUMLOGLMP];

extern  uint8_T  PhysLmpTest[NUMPHYSLMP];
extern  uint8_T  StMIL;
extern  uint8_T  OdoMILTrig;
extern  uint32_T OdoMILActivePrivate;
extern  uint8_T  CntDiagWarmUpCycle;

/** public functions **/
extern void LampMgm_Init(void);
extern void LampMgm_T10ms(void);
extern void LampMgm_T100ms(void);
extern void LampMgm_KeyOn(void);
extern  typLampCmd Get_Lamp_Status (uint8_T phys_lamp);

#endif /* defined _LAMP_MGM_H_ */
