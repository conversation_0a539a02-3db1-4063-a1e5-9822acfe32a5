/*
 * File: IonLambda.h
 *
 * Real-Time Workshop code generated for Simulink model IonLambda.
 *
 * Model version                        : 1.657
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed May 23 15:23:33 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed May 23 15:23:38 2007
 */

#ifndef _RTW_HEADER_IonLambda_h_
#define _RTW_HEADER_IonLambda_h_

#ifndef _IonLambda_COMMON_INCLUDES_
# define _IonLambda_COMMON_INCLUDES_
#include <stddef.h>
#include <string.h>
#include "rtwtypes.h"
#include "rtlibsrc.h"
#include "mathlib.h"
#endif                                  /* _IonLambda_COMMON_INCLUDES_ */

#include "IonLambda_types_Y.h"

/* Macros for accessing real-time model data structure  */

#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm) ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val) ((void) 0)
#endif

#define BKFFSDSQ_dim                    4U

#define BKLAMINDEX_dim                  4U

#define BKLOADADFFS_dim                 5U

#define BKLOADLAMOD_dim                 18U

#define BKRPMADFFS_dim                  6U

#define BKRPMLAMFIL_dim                 7U

#define BKRPMLAMOD_dim                  17U

#define BKTDCCRKLAM_dim                 7U

#define BKTWATCRKLAM_dim                7U

#define D1LAM_POLY_P1                   -4347

#define D1LAM_POLY_P2                   18447

#define D1LAM_POLY_P3                   -8392172

#define D2LAM_POLY_P1                   26558

#define D2LAM_POLY_P2                   -17188

#define D2LAM_POLY_P3                   4177656

#define IONERRORSTATUS_MASK             3U

#define LEN_BKLAM                       8U

#define MONITORING                      2U

#define NO_FORCED                       0U

#define OL_FORCED                       4U

#define POST_OL_FORCED                  5U

#define RPM_2_PERC                      85U

#define WAIT_MONITORING                 1U

#define WAIT_OL_FORCED                  3U

/* Block signals for system: '<S4>/ColdCorrection' */
typedef struct {
  uint16_T MulCorrect;                  /* '<S6>/MulCorrect' */
} rtB_IonLambda_ColdCorrection;

/* Block signals for system: '<S4>/ColdCorrection1' */
typedef struct {
  uint16_T MulCorrect_b;                /* '<S7>/MulCorrect' */
} rtB_IonLambda_ColdCorrection1;

/* Block signals for system: '<S4>/Filtering' */
typedef struct {
  uint32_T Switch7;                     /* '<S94>/Switch7' */
  uint32_T Assignment3[8];              /* '<S11>/Assignment3' */
  uint32_T Assignment4[8];              /* '<S11>/Assignment4' */
  uint32_T Switch2;                     /* '<S96>/Switch2' */
  uint32_T Switch3;                     /* '<S96>/Switch3' */
  uint32_T Assignment1[8];              /* '<S11>/Assignment1' */
  uint32_T Assignment2[8];              /* '<S11>/Assignment2' */
  uint32_T Product1;                    /* '<S98>/Product1' */
  uint32_T Product1_d;                  /* '<S97>/Product1' */
  uint32_T Switch;                      /* '<S96>/Switch' */
  uint32_T Switch1;                     /* '<S96>/Switch1' */
} rtB_IonLambda_Filtering;

/* Block states (auto storage) for system: '<S4>/Filtering' */
typedef struct {
  uint16_T UnitDelay1_DSTATE;           /* '<S95>/Unit Delay1' */
} rtDW_IonLambda_Filtering;

/* Block signals for system: '<S121>/CalcLamRel' */
typedef struct {
  uint8_T Switch_g;                     /* '<S128>/Switch' */
} rtB_IonLambda_CalcLamRel;

/* Block signals for system: '<S121>/CalcLambda' */
typedef struct {
  uint16_T Conversion;                  /* '<S133>/Conversion' */
} rtB_IonLambda_CalcLambda;

/* Block signals for system: '<S121>/CalcStep' */
typedef struct {
  int16_T Switch_m;                     /* '<S134>/Switch' */
} rtB_IonLambda_CalcStep;

/* Block signals for system: '<S109>/Lam_by_x1' */
typedef struct {
  uint8_T IdLam;                        /* '<S121>/Search_lambda' */
  rtB_IonLambda_CalcStep CalcStep;      /* '<S121>/CalcStep' */
  rtB_IonLambda_CalcLambda CalcLambda; /* '<S121>/CalcLambda' */
  rtB_IonLambda_CalcLamRel CalcLamRel; /* '<S121>/CalcLamRel' */
} rtB_IonLambda_Lam_by_x1;

/* Block signals for system: '<S109>/Lam_by_x2' */
typedef struct {
  uint8_T IdLam_c;                      /* '<S122>/Search_lambda' */
  rtB_IonLambda_CalcStep CalcStep_e;    /* '<S122>/CalcStep' */
  rtB_IonLambda_CalcLambda CalcLambda_k; /* '<S122>/CalcLambda' */
  rtB_IonLambda_CalcLamRel CalcLamRel_j; /* '<S122>/CalcLamRel' */
} rtB_IonLambda_Lam_by_x2;

/* Block signals for system: '<S4>/Median' */
typedef struct {
  uint16_T median;                      /* '<S4>/Median' */
} rtB_IonLambda_Median;

/* Block states (auto storage) for system: '<S4>/Median' */
typedef struct {
  CSc3_IonLambda_ChartStruct Median;    /* '<S4>/Median' */
} rtDW_IonLambda_Median;

/* Block signals for system: '<S4>/Median1' */
typedef struct {
  uint16_T median_e;                    /* '<S4>/Median1' */
} rtB_IonLambda_Median1;

/* Block states (auto storage) for system: '<S4>/Median1' */
typedef struct {
  CSc2_IonLambda_ChartStruct Median1;   /* '<S4>/Median1' */
} rtDW_IonLambda_Median1;

/* Block signals (auto storage) */
typedef struct {
  uint16_T Selector;                    /* '<S16>/Selector' */
  uint16_T i_out;                       /* '<S77>/Chart' */
  uint16_T PreLookUpIdSearch_U16_o1;    /* '<S54>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2;    /* '<S54>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o1_c; /* '<S53>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2_m; /* '<S53>/PreLookUpIdSearch_U16' */
  uint16_T Conversion_c;                /* '<S51>/Conversion' */
  uint16_T LamEstAvg_f;                 /* '<S106>/Product' */
  uint16_T sum;                         /* '<S110>/Sum1' */
  uint16_T Product[9];                  /* '<S25>/Product' */
  uint16_T Conversion_k;                /* '<S52>/Conversion' */
  uint16_T dsq;                         /* '<S16>/Calc_indices_ad' */
  uint8_T DataStoreRead18;              /* '<S4>/Data Store Read18' */
  uint8_T DataStoreRead19;              /* '<S4>/Data Store Read19' */
  uint8_T FlgForceOL_b;                 /* '<S16>/SelfAdjFSM' */
  uint8_T indr;                         /* '<S16>/Calc_indices_ad' */
  uint8_T indc;                         /* '<S16>/Calc_indices_ad' */
  rtB_IonLambda_Median1 sf_Median1;     /* '<S4>/Median1' */
  rtB_IonLambda_Median sf_Median;       /* '<S4>/Median' */
  rtB_IonLambda_Lam_by_x2 Lam_by_x2;    /* '<S109>/Lam_by_x2' */
  rtB_IonLambda_Lam_by_x1 Lam_by_x1;    /* '<S109>/Lam_by_x1' */
  rtB_IonLambda_Filtering Filtering1;   /* '<S4>/Filtering1' */
  rtB_IonLambda_Filtering Filtering;    /* '<S4>/Filtering' */
  rtB_IonLambda_ColdCorrection1 ColdCorrection1; /* '<S4>/ColdCorrection1' */
  rtB_IonLambda_ColdCorrection ColdCorrection; /* '<S4>/ColdCorrection' */
} BlockIO_IonLambda;

/* Block states (auto storage) for system: '<Root>' */
typedef struct {
  CSc9_IonLambda_ChartStruct Calc_indices_ad; /* '<S16>/Calc_indices_ad' */
  CSc6_IonLambda_ChartStruct SelfAdjFSM; /* '<S16>/SelfAdjFSM' */
  int32_T Selector_DWORK1;              /* '<S144>/Selector' */
  int32_T Selector_DWORK2;              /* '<S144>/Selector' */
  int32_T Selector_DWORK1_m;            /* '<S145>/Selector' */
  int32_T Selector_DWORK2_e;            /* '<S145>/Selector' */
  int32_T Selector_DWORK1_g;            /* '<S148>/Selector' */
  int32_T Selector_DWORK2_p;            /* '<S148>/Selector' */
  int32_T LamEstFilt_HR[8];             /* '<Root>/_DataStoreBlk_16' */
  int32_T Memory2_PreviousInput[8];     /* '<S113>/Memory2' */
  int_T SelfAdjust_MODE[2];             /* '<S4>/SelfAdjust' */
  uint16_T Memory1_PreviousInput;       /* '<S93>/Memory1' */
  uint16_T Memory_PreviousInput;        /* '<S93>/Memory' */
  uint16_T Memory1_PreviousInput_b;     /* '<S92>/Memory1' */
  uint16_T Memory_PreviousInput_o;      /* '<S92>/Memory' */
  uint16_T Memory1_PreviousInput_g;     /* '<S85>/Memory1' */
  uint16_T Memory_PreviousInput_f;      /* '<S85>/Memory' */
  uint16_T Memory1_PreviousInput_l;     /* '<S86>/Memory1' */
  uint16_T Memory_PreviousInput_i;      /* '<S86>/Memory' */
  uint16_T LamEstFilt[8];               /* '<Root>/_DataStoreBlk_17' */
  uint16_T Memory_PreviousInput_a;      /* '<S110>/Memory' */
  uint16_T Memory1_PreviousInput_k[8]; /* '<S113>/Memory1' */
  int16_T UnitDelay_DSTATE;             /* '<S10>/Unit Delay' */
  int16_T DelayInput1_DSTATE;           /* '<S75>/Delay Input1' */
  uint8_T DelayOut3_DSTATE[8];          /* '<S108>/DelayOut3' */
  uint8_T DelayOut4_DSTATE[8];          /* '<S108>/DelayOut4' */
  uint8_T DelayOut1_DSTATE[8];          /* '<S106>/DelayOut1' */
  uint8_T UnitDelay_DSTATE_f;           /* '<S81>/Unit Delay' */
  uint8_T UnitDelay_DSTATE_j;           /* '<S80>/Unit Delay' */
  uint8_T UnitDelay_DSTATE_h;           /* '<S76>/Unit Delay' */
  uint8_T UnitDelay1_DSTATE_f;          /* '<S76>/Unit Delay1' */
  boolean_T DelayOut1_DSTATE_p[8];      /* '<S108>/DelayOut1' */
  rtDW_IonLambda_Median1 sf_Median1;    /* '<S4>/Median1' */
  rtDW_IonLambda_Median sf_Median;      /* '<S4>/Median' */
  rtDW_IonLambda_Filtering Filtering1; /* '<S4>/Filtering1' */
  rtDW_IonLambda_Filtering Filtering;   /* '<S4>/Filtering' */
} D_Work_IonLambda;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState sf_Chart_ZCE[3];           /* '<S1>/Chart' */
} PrevZCSigStates_IonLambda;
/* Invariant block signals (auto storage) */
typedef struct {
  const int32_T DataTypeConversion_d;   /* '<S3>/Data Type Conversion' */
  const uint32_T DataTypeConversion1_l; /* '<S3>/Data Type Conversion1' */
  const uint32_T DataTypeConversion2_l; /* '<S3>/Data Type Conversion2' */
  const uint8_T Conversion4_k;          /* '<S93>/Conversion4' */
  const uint8_T Conversion4_l;          /* '<S92>/Conversion4' */
} ConstBlockIO_IonLambda;

/* Constant parameters (auto storage) */
typedef struct {
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_16'
   * '<S113>/Memory2'
   */
  int32_T pooled1[8];
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_18'
   * '<Root>/_DataStoreBlk_3'
   * '<Root>/_DataStoreBlk_6'
   * '<Root>/_DataStoreBlk_9'
   */
  uint32_T pooled2[8];
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_4'
   * '<Root>/_DataStoreBlk_5'
   * '<Root>/_DataStoreBlk_7'
   * '<Root>/_DataStoreBlk_8'
   */
  uint32_T pooled3[8];
  /* Computed Parameter: InitialValue
   * '<Root>/_DataStoreBlk_2'
   */
  uint16_T _DataStoreBlk_2_Initia[42];
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_15'
   * '<Root>/_DataStoreBlk_17'
   * '<S113>/Memory1'
   */
  uint16_T pooled7[8];
  /* Computed Parameter: Value
   * Referenced by blocks:
   * '<S108>/ONES_N_CYL_MAX'
   * '<S108>/DelayOut3'
   * '<S108>/DelayOut4'
   */
  uint8_T pooled13[8];
  /* Computed Parameter: X0
   * '<S106>/DelayOut1'
   */
  uint8_T DelayOut1_X0[8];
  /* Computed Parameter: X0
   * '<S108>/DelayOut1'
   */
  boolean_T DelayOut1_X0_p[8];
} ConstParam_IonLambda;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                   /* '<Root>/ev_PowerOn' */
  uint8_T ev_TDC;                       /* '<Root>/ev_TDC' */
  uint8_T ev_NoSync;                    /* '<Root>/ev_NoSync' */
} ExternalInputs_IonLambda;

/* Real-time Model Data Structure */
struct RT_MODEL_IonLambda {

  /*
   * Timing:
   * The following substructure contains information regarding
   * the timing information for the model.
   */
  struct {
    boolean_T firstInitCondFlag;
  } Timing;
};

/* Block signals (auto storage) */
extern BlockIO_IonLambda IonLambda_B;

/* Block states (auto storage) */
extern D_Work_IonLambda IonLambda_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_IonLambda IonLambda_U;

extern ConstBlockIO_IonLambda IonLambda_ConstB; /* constant block i/o */

/* Constant parameters (auto storage) */
extern const ConstParam_IonLambda IonLambda_ConstP;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  RTW declares the memory for these signals
 * and exports their symbols.
 *
 */

extern int32_T AccumDelta;              /* '<S16>/SelfAdjFSM' */

extern uint32_T AccumFFS;               /* '<S16>/SelfAdjFSM' */

extern uint32_T AccumBkFFS;             /* '<S16>/SelfAdjFSM' */

extern uint32_T BkFFSNCOut;             /* '<S142>/BkFFSNCOut_Convert' */

extern uint32_T AccumLam;               /* '<S16>/SelfAdjFSM' */

extern uint16_T IDZoneFFSRpm;           /* '<S84>/PreLookUpIdSearch_U16' */

extern uint16_T IDZoneFFSLoad;          /* '<S83>/PreLookUpIdSearch_U16' */

extern uint16_T AvgIntIonCyl;           /* '<S4>/Data Type Conversion1' */

extern uint16_T IntIonMedianCorr;       /* '<S4>/Data Type Conversion10' */

extern uint16_T AvgIntIon[8];           /* '<S4>/Data Type Conversion4' */

extern uint16_T IntIonMedian;           /* '<S4>/Data Type Conversion7' */

extern uint16_T BkIntIon[9];            /* '<S28>/Product' */

extern uint16_T BkFFSAdj;               /* '<S87>/Conversion' */

extern uint16_T RtZoneFFSRpm;           /* '<S76>/Data Type Conversion' */

extern uint16_T RtZoneFFSLoad;          /* '<S76>/Data Type Conversion1' */

extern uint16_T FFSGainSelfAdj;         /* '<S145>/Divide2' */

extern uint16_T LamObjSelfAdj;          /* '<S146>/Add' */

extern uint16_T LamIntIonCyl;           /* '<S109>/Signal Conversion' */

extern uint16_T GnLaMod;                /* '<S120>/Multiply' */

extern uint16_T LamFFSCyl;              /* '<S109>/Signal Conversion1' */

extern uint16_T LamEstSlow[8];          /* '<S113>/Assignment1' */

extern uint16_T AvgFFSCyl;              /* '<S4>/Data Type Conversion16' */

extern uint16_T AvgFFS[8];              /* '<S4>/Data Type Conversion3' */

extern uint16_T FFSMedian;              /* '<S4>/Data Type Conversion6' */

extern uint16_T FFSMedianCorr;          /* '<S4>/Data Type Conversion9' */

extern uint16_T BkFFSRef;               /* '<S145>/Divide3' */

extern uint16_T BkFFS[9];               /* '<S25>/Product2' */

extern int16_T D1LamFil;                /* '<S78>/Data Type Conversion1' */

extern int16_T D2LamFil;                /* '<S78>/Data Type Conversion2' */

extern int16_T KFiltLamEstAvg;          /* '<S77>/Switch1' */

extern int16_T DeltaLamCLMem;           /* '<S146>/Divide3' */

extern uint16_T DSAGainFFS;             /* '<S25>/Add' */

extern uint16_T DSAGainINT;             /* '<S28>/Add' */

extern int16_T FreqNorm;                /* '<S91>/Conversion' */

extern uint16_T N0LamFil;               /* '<S78>/Data Type Conversion' */

extern uint8_T StabRpmLamTr;            /* '<S93>/SteadyStateDetect' */

extern uint8_T sstab_rpm_lam;           /* '<S93>/SteadyStateDetect' */

extern uint8_T StabLoadLamTr;           /* '<S92>/SteadyStateDetect' */

extern uint8_T sstab_load_lam;          /* '<S92>/SteadyStateDetect' */

extern uint8_T FiltParReset;            /* '<S75>/FixPt Relational Operator' */

extern uint8_T EnFilButter;             /* '<S77>/Selector2' */

extern uint8_T FStabRpmSelfAdj;         /* '<S85>/SigStab' */

extern uint8_T sstab_rpm_ffs;           /* '<S85>/SigStab' */

extern uint8_T FStabLoadSelfAdj;        /* '<S86>/SigStab' */

extern uint8_T sstab_load_ffs;          /* '<S86>/SigStab' */

extern uint8_T EnFilLamEst;             /* '<S77>/Selector3' */

extern uint8_T SelfAdjState;            /* '<S16>/SelfAdjFSM' */

extern uint8_T EnSelfAdj;               /* '<S144>/Logical Operator' */

extern uint8_T IndBkLam;                /* '<S107>/Data Type Conversion' */

extern uint8_T LambdaState[8];          /* '<S106>/Assignment2' */

extern uint8_T FlgLamIntIonRel[8];      /* '<S108>/Switch2' */

extern uint8_T FlgLamFFSRel[8];         /* '<S108>/Switch3' */

extern uint8_T FlgLamRel[8];            /* '<S108>/Switch4' */

extern uint16_T GainIntIonCrk;          /* '<S24>/Conversion' */

extern uint16_T GainFFSCrk;             /* '<S20>/Conversion' */

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  RTW declares the memory for these states
 * and exports their symbols.
 *
 */

extern uint32_T Avg_HR_FFS[8];          /* '<Root>/_DataStoreBlk_18' */

extern uint32_T Avg_HR_old_FFS[8];      /* '<Root>/_DataStoreBlk_3' */

extern uint32_T Avg_HR_old_IntIon[8];   /* '<Root>/_DataStoreBlk_6' */

extern uint32_T Avg_HR_IntIon[8];       /* '<Root>/_DataStoreBlk_9' */

extern uint32_T In_x_N0_FFS[8];         /* '<Root>/_DataStoreBlk_4' */

extern uint32_T InSum_FFS[8];           /* '<Root>/_DataStoreBlk_5' */

extern uint32_T In_x_N0_IntIon[8];      /* '<Root>/_DataStoreBlk_7' */

extern uint32_T InSum_IntIon[8];        /* '<Root>/_DataStoreBlk_8' */

extern uint16_T LamObjMem;              /* '<Root>/_DataStoreBlk_10' */

extern uint16_T LamEstAvg;              /* '<Root>/_DataStoreBlk_14' */

extern uint16_T LamEst[8];              /* '<Root>/_DataStoreBlk_15' */

extern uint8_T TbFlgSelfAdjOK[42];      /* '<Root>/_DataStoreBlk_1' */

extern uint8_T FiltLamEnable;           /* '<Root>/_DataStoreBlk_11' */

extern uint8_T FiltLamFreeze;           /* '<Root>/_DataStoreBlk_12' */

extern uint8_T FlgForceOL;              /* '<Root>/_DataStoreBlk_13' */

/* External data declarations for dependent source files */

/* Zero-crossing (trigger) state */
extern PrevZCSigStates_IonLambda IonLambda_PrevZC;

/* Model entry point functions */
extern void IonLambda_initialize(boolean_T firstTime);
extern void IonLambda_step(void);

/* Real-time Model object */
extern RT_MODEL_IonLambda *IonLambda_M;

extern uint16_T BKFFSDSQ[5];            /* IONLAMBDA.BKFFSDSQ: Normalized distance from breakpoint to adjust */

extern uint16_T BKLAM[9];               /* IONLAMBDA.BKLAM: Breakpoints of lambda */

extern uint16_T BKLAMINDEX[5];          /* IONLAMBDA.BKLAMINDEX: Breakpoints of lambda */

extern uint16_T BKLOADADFFS[6];         /* IONLAMBDA.BKLOADADFFS: Breakpoints of Load for the FFS adjustment table */

extern uint16_T BKLOADLAMOD[19];        /* IONLAMBDA.BKLOADLAMOD: Breakpoints of Load for the lambda model */

extern uint16_T BKRPMADFFS[7];          /* IONLAMBDA.BKRPMADFFS: Breakpoints of Rpm for the FFS adjustment table */

extern uint16_T BKRPMLAMFIL[8];         /* IONLAMBDA.BKRPMLAMFIL: Breakpoints of Rpm for FFS/lambda filtering */

extern uint16_T BKRPMLAMOD[18];         /* IONLAMBDA.BKRPMLAMOD: Breakpoints of Rpm for the lambda model */

extern uint16_T BKTDCCRKLAM[8];         /* IONLAMBDA.BKTDCCRKLAM: Breakpoints for CntTdcCrk - FFS cold correction */

extern int16_T BKTWATCRKLAM[8];         /* IONLAMBDA.BKTWATCRKLAM: Breakpoints for TWaterCrk - FFS cold correction */

extern uint8_T ENFFSADJ;                /* IONLAMBDA.ENFFSADJ: Enable FFS adjustment computation */

extern uint8_T ENFLGLAMREL;             /* IONLAMBDA.ENFLGLAMREL: Enable lambda reliability flag computation */

extern int16_T FFSCORRADMAX;            /* IONLAMBDA.FFSCORRADMAX: Max TbBkFFSAdj modification */

extern int16_T FFSCORRADMIN;            /* IONLAMBDA.FFSCORRADMIN: Min TbBkFFSAdj modification */

extern int16_T FREQNORMLAMTR;           /* IONLAMBDA.FREQNORMLAMTR: Normalized cutoff frequency for FFS/IntIon filtering during transient */

extern int16_T FREQRATEMAX;             /* IONLAMBDA.FREQRATEMIN: Maximum variation rate for FreqNorm */

extern int16_T FREQRATEMIN;             /* IONLAMBDA.FREQRATEMIN: Minimum variation rate for FreqNorm */

extern uint16_T GAINDELTACORR;          /* IONLAMBDA.GAINDELTACORR: Filter constant for LamEstSlow */

extern uint16_T GNFFSCORRINDAD;         /* IONLAMBDA.GNFFSCORRINDAD: FFS correction gain */

extern uint16_T IONLAMOBJTUN;           /* IONLAMBDA.IONLAMOBJTUN: Lambda obj for Lambda estimation tuning */

extern int16_T KFILTLAMESTSLOW;         /* IONLAMBDA.KFILTLAMESTSLOW: Filter constant for LamEstSlow */

extern uint16_T LAMFFSMAX;              /* IONLAMBDA.LAMFFSMAX: Max LamObj for adjustment enable */

extern uint16_T LAMFFSMIN;              /* IONLAMBDA.LAMFFSMIN: Min LamObj for adjustment enable */

extern uint16_T LAMSELFADJ;             /* IONLAMBDA.LAMSELFADJ: Forced LamObj value for adjustment phase */

extern uint8_T LENBUFMED;               /* IONLAMBDA.LENBUFMED: FFS median length */

extern uint8_T NADJMAX;                 /* IONLAMBDA.NADJMAX: Max number of adjustment cycles for a single point */

extern uint16_T NTASKDISOL;             /* IONLAMBDA.NTASKDISOL: Number of waiting tasks after disable during OL_FORCED */

extern uint16_T NTASKDISWAITOL;         /* IONLAMBDA.NTASKDISWAITOL: Number of waiting tasks after disable during WAIT_OL_FORCED */

extern uint16_T NTASKMON;               /* IONLAMBDA.NTASKMON: Number of tasks for closed-loop monitoring */

extern uint16_T NTASKPOSTOL;            /* IONLAMBDA.NTASKPOSTOL: Number of tasks for open-loop forcing */

extern uint16_T NTASKWAITMON;           /* IONLAMBDA.NTASKWAITMON: Number of waiting tasks before closed-loop monitoring */

extern uint16_T NTASKWAITOL;            /* IONLAMBDA.NTASKWAITOL: Number of waiting tasks before open-loop forcing */

extern uint16_T RPMHYSTFN;              /* IONLAMBDA.RPMHYSTFN: Rpm hysteresis for FreqNorm computation */

extern uint16_T TBBK_FFS_1[342];        /* IONLAMBDA.TBBK_FFS_1: Table of the FFS breakpoint 1 of the lambda model */

extern uint16_T TBBK_FFS_2[342];        /* IONLAMBDA.TBBK_FFS_2: Table of the FFS breakpoint 2 of the lambda model */

extern uint16_T TBBK_FFS_3[342];        /* IONLAMBDA.TBBK_FFS_3: Table of the FFS breakpoint 3 of the lambda model */

extern uint16_T TBBK_FFS_4[342];        /* IONLAMBDA.TBBK_FFS_4: Table of the FFS breakpoint 4 of the lambda model */

extern uint16_T TBBK_FFS_5[342];        /* IONLAMBDA.TBBK_FFS_5: Table of the FFS breakpoint 5 of the lambda model */

extern uint16_T TBBK_FFS_6[342];        /* IONLAMBDA.TBBK_FFS_6: Table of the FFS breakpoint 6 of the lambda model */

extern uint16_T TBBK_FFS_7[342];        /* IONLAMBDA.TBBK_FFS_7: Table of the FFS breakpoint 7 of the lambda model */

extern uint16_T TBBK_FFS_8[342];        /* IONLAMBDA.TBBK_FFS_8: Table of the FFS breakpoint 8 of the lambda model */

extern uint16_T TBBK_FFS_9[342];        /* IONLAMBDA.TBBK_FFS_9: Table of the FFS breakpoint 9 of the lambda model */

extern uint16_T TBBK_IntIon_1[342];     /* IONLAMBDA.TBBK_IntIon_1: Table of the IntIon breakpoint 1 of the lambda model */

extern uint16_T TBBK_IntIon_2[342];     /* IONLAMBDA.TBBK_IntIon_2: Table of the IntIon breakpoint 2 of the lambda model */

extern uint16_T TBBK_IntIon_3[342];     /* IONLAMBDA.TBBK_IntIon_3: Table of the IntIon breakpoint 3 of the lambda model */

extern uint16_T TBBK_IntIon_4[342];     /* IONLAMBDA.TBBK_IntIon_4: Table of the IntIon breakpoint 4 of the lambda model */

extern uint16_T TBBK_IntIon_5[342];     /* IONLAMBDA.TBBK_IntIon_5: Table of the IntIon breakpoint 5 of the lambda model */

extern uint16_T TBBK_IntIon_6[342];     /* IONLAMBDA.TBBK_IntIon_6: Table of the IntIon breakpoint 6 of the lambda model */

extern uint16_T TBBK_IntIon_7[342];     /* IONLAMBDA.TBBK_IntIon_7: Table of the IntIon breakpoint 7 of the lambda model */

extern uint16_T TBBK_IntIon_8[342];     /* IONLAMBDA.TBBK_IntIon_8: Table of the IntIon breakpoint 8 of the lambda model */

extern uint16_T TBBK_IntIon_9[342];     /* IONLAMBDA.TBBK_IntIon_9: Table of the IntIon breakpoint 9 of the lambda model */

extern uint16_T TBGAINFFSCRK[64];       /* IONLAMBDA.TBGAINFFSCRK: Tables of data for GainFFSCrk */

extern uint16_T TBGAININTCRK[64];       /* IONLAMBDA.TBGAININTCRK: Tables of data for GainFFSCrk */

extern uint16_T TBGNLAMOD[342];         /* IONLAMBDA.TBGNLAMOD: Tables of data for GnLaMod */

extern uint16_T TDCRLSTABADFFS;         /* IONLAMBDA.TDCRLSTABADFFS: Number of TDC to declare Rpm stability for FFS adjustment */

extern uint16_T TDCSTABLOADLAMTR;       /* IONLAMBDA.TDCSTABLOADLAMTR: Number of TDC to declare Rpm stability */

extern uint16_T TDCSTABRPMLAMTR;        /* IONLAMBDA.TDCSTABRPMLAMTR: Number of TDC to declare Rpm stability */

extern uint16_T THAVGFFS;               /* IONLAMBDA.THAVGFFS: Threshold for FFS out of range detection */

extern uint16_T THAVGINTION;            /* IONLAMBDA.THAVGINTION: Threshold for IntIon out of range detection */

extern uint16_T THRSTABLOADADFFS;       /* IONLAMBDA.THRSTABLOADADFFS: Stability range for Load for FFS adjustment */

extern uint16_T THRSTABLOADLAMTR;       /* IONLAMBDA.THRSTABLOADLAMTR: Stability range for Load */

extern uint16_T THRSTABRPMADFFS;        /* IONLAMBDA.THRSTABRPMADFFS: Stability range for Rpm percentage for FFS adjustment */

extern uint16_T THRSTABRPMLAMTR;        /* IONLAMBDA.THRSTABRPMLAMTR: Stability range for Rpm */

extern uint16_T VTDSAGAINFFS[18];       /* IONLAMBDA.VTDSAGAINFFS: DSA^2 coefficient vector for FFS */

extern uint16_T VTDSAGAININT[18];       /* IONLAMBDA.VTDSAGAININT: DSA^2 coefficient vector for IntIon/ChInt */

extern uint8_T VTENFILBUTTER[8];        /* IONLAMBDA.VTENFILBUTTER: Butterworth filter enable */

extern uint8_T VTENFILLAMEST[8];        /* IONLAMBDA.VTENFILLAMEST: LamEstAvg filter enable */

extern uint16_T VTFFSGNAD[5];           /* IONLAMBDA.VTFFSGNAD: Gain for FFS correction to store */

extern int16_T VTFREQNORMCUT[8];        /* IONLAMBDA.VTFREQNORMCUT: Normalized cutoff frequency for Butterworth filter */

extern int16_T VTKFILTLAMEST[8];        /* IONLAMBDA.VTKFILTLAMEST: Filter constant for LamEstAvg */

extern int16_T VTKFILTLAMESTTR[8];      /* IONLAMBDA.VTKFILTLAMESTTR: Filter constant for LamEstAvg during transient */

extern uint8_T VTLAMBDASTATE[5];        /* IONLAMBDA.VTLAMBDASTATE: Look-up table to interpolate LambdaState */

extern uint16_T VTNTASKOL[7];           /* IONLAMBDA.VTNTASKOL: Number of tasks for open-loop forcing */

extern uint16_T VTSELFADJLOADMAX[7];    /* IONLAMBDA.VTSELFADJLOADMAX: Max Load for FFS adjustment enable */

extern uint16_T VTSELFADJLOADMIN[7];    /* IONLAMBDA.VTSELFADJLOADMIN: Min Load for FFS adjustment enable */

extern uint16_T VTWEIGHLAM[5];          /* IONLAMBDA.VTWEIGHLAM: Lambda weight coefficients for GnLaMod */

extern uint16_T TbBkFFSAdj[42];

/* 
 * The generated code includes comments that allow you to trace directly 
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : IonLambda
 * '<S1>'   : IonLambda/IonLambda
 * '<S2>'   : IonLambda/IonLambda/Chart
 * '<S3>'   : IonLambda/IonLambda/IonLambda_Reset
 * '<S4>'   : IonLambda/IonLambda/IonLambda_TDC
 * '<S5>'   : IonLambda/IonLambda/IonLambda_Reset/Lam_Vector_Reset
 * '<S6>'   : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection
 * '<S7>'   : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection1
 * '<S8>'   : IonLambda/IonLambda/IonLambda_TDC/EnFilter
 * '<S9>'   : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes
 * '<S10>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc
 * '<S11>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering
 * '<S12>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering1
 * '<S13>'  : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter
 * '<S14>'  : IonLambda/IonLambda/IonLambda_TDC/Median
 * '<S15>'  : IonLambda/IonLambda/IonLambda_TDC/Median1
 * '<S16>'  : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust
 * '<S17>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection/Look2D_IR_U16
 * '<S18>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection/PreLookUpIdSearch_S16
 * '<S19>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection/PreLookUpIdSearch_U16
 * '<S20>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection/Look2D_IR_U16/Data Type Conversion Inherited1
 * '<S21>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection1/Look2D_IR_U16
 * '<S22>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection1/PreLookUpIdSearch_S16
 * '<S23>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection1/PreLookUpIdSearch_U16
 * '<S24>'  : IonLambda/IonLambda/IonLambda_TDC/ColdCorrection1/Look2D_IR_U16/Data Type Conversion Inherited1
 * '<S25>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints
 * '<S26>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/IC_Calc
 * '<S27>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Index_Ratio_Calc
 * '<S28>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints
 * '<S29>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U1
 * '<S30>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U2
 * '<S31>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U3
 * '<S32>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U4
 * '<S33>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U5
 * '<S34>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U6
 * '<S35>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U7
 * '<S36>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U8
 * '<S37>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U9
 * '<S38>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/LookUp_IR_U16
 * '<S39>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U1/Data Type Conversion Inherited1
 * '<S40>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U2/Data Type Conversion Inherited1
 * '<S41>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U3/Data Type Conversion Inherited1
 * '<S42>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U4/Data Type Conversion Inherited1
 * '<S43>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U5/Data Type Conversion Inherited1
 * '<S44>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U6/Data Type Conversion Inherited1
 * '<S45>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U7/Data Type Conversion Inherited1
 * '<S46>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U8/Data Type Conversion Inherited1
 * '<S47>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/Look2D_IR_U9/Data Type Conversion Inherited1
 * '<S48>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/FFS Breakpoints/LookUp_IR_U16/Data Type Conversion Inherited3
 * '<S49>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/IC_Calc/LookUp_U16_U1
 * '<S50>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/IC_Calc/LookUp_U16_U16
 * '<S51>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/IC_Calc/LookUp_U16_U1/Data Type Conversion Inherited3
 * '<S52>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/IC_Calc/LookUp_U16_U16/Data Type Conversion Inherited3
 * '<S53>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Index_Ratio_Calc/PreLookUpIdSearch_U1
 * '<S54>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Index_Ratio_Calc/PreLookUpIdSearch_U16
 * '<S55>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U1
 * '<S56>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U2
 * '<S57>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U3
 * '<S58>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U4
 * '<S59>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U5
 * '<S60>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U6
 * '<S61>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U7
 * '<S62>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U8
 * '<S63>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U9
 * '<S64>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/LookUp_IR_U16
 * '<S65>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U1/Data Type Conversion Inherited1
 * '<S66>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U2/Data Type Conversion Inherited1
 * '<S67>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U3/Data Type Conversion Inherited1
 * '<S68>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U4/Data Type Conversion Inherited1
 * '<S69>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U5/Data Type Conversion Inherited1
 * '<S70>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U6/Data Type Conversion Inherited1
 * '<S71>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U7/Data Type Conversion Inherited1
 * '<S72>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U8/Data Type Conversion Inherited1
 * '<S73>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/Look2D_IR_U9/Data Type Conversion Inherited1
 * '<S74>'  : IonLambda/IonLambda/IonLambda_TDC/FFS_IntIon_Talbes/Intion Breakpoints/LookUp_IR_U16/Data Type Conversion Inherited3
 * '<S75>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect Change
 * '<S76>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement
 * '<S77>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/FilterMgm
 * '<S78>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/FilterParameters
 * '<S79>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/RateLimiter_S16
 * '<S80>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/stabLoad_calc
 * '<S81>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/stabRpm_calc
 * '<S82>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement/Look2D_IR_U16
 * '<S83>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement/PreLookUpIdSearch_U1
 * '<S84>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement/PreLookUpIdSearch_U16
 * '<S85>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement/Signal_Stability
 * '<S86>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement/Signal_Stability1
 * '<S87>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/Detect_zones_for_adjustement/Look2D_IR_U16/Data Type Conversion Inherited1
 * '<S88>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/FilterMgm/Chart
 * '<S89>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/FilterMgm/PreLookUpIdSearch_U1
 * '<S90>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/FilterMgm/PreLookUpIdSearch_U16
 * '<S91>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/RateLimiter_S16/Data Type Conversion Inherited1
 * '<S92>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/stabLoad_calc/Steady_State_Detect
 * '<S93>'  : IonLambda/IonLambda/IonLambda_TDC/FiltPar_calc/stabRpm_calc/Steady_State_Detect
 * '<S94>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering/Filter
 * '<S95>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering/FilterState_Reset
 * '<S96>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering/Filter/Butterworth_2order_LOWPASS
 * '<S97>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering/Filter/ByPassValues
 * '<S98>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering/Filter/InitialCondition
 * '<S99>'  : IonLambda/IonLambda/IonLambda_TDC/Filtering/Filter/Butterworth_2order_LOWPASS/Gain_x_2_shifting
 * '<S100>' : IonLambda/IonLambda/IonLambda_TDC/Filtering1/Filter
 * '<S101>' : IonLambda/IonLambda/IonLambda_TDC/Filtering1/FilterState_Reset
 * '<S102>' : IonLambda/IonLambda/IonLambda_TDC/Filtering1/Filter/Butterworth_2order_LOWPASS
 * '<S103>' : IonLambda/IonLambda/IonLambda_TDC/Filtering1/Filter/ByPassValues
 * '<S104>' : IonLambda/IonLambda/IonLambda_TDC/Filtering1/Filter/InitialCondition
 * '<S105>' : IonLambda/IonLambda/IonLambda_TDC/Filtering1/Filter/Butterworth_2order_LOWPASS/Gain_x_2_shifting
 * '<S106>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation
 * '<S107>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lambda start index
 * '<S108>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lambda_Reliable
 * '<S109>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation
 * '<S110>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/Accumulator
 * '<S111>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/LamEst_Filtering
 * '<S112>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/PreLookUpIdSearch_U16
 * '<S113>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/Slow_lambda_estimation
 * '<S114>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/LamEst_Filtering/FOF_Reset_S16_FXP
 * '<S115>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/LamEst_Filtering/FilterReset_Mgm
 * '<S116>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/LamEst_Filtering/FOF_Reset_S16_FXP/Data Type Conversion Inherited1
 * '<S117>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/Slow_lambda_estimation/FOF_Reset_S16_FXP
 * '<S118>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lam_Estimation/Slow_lambda_estimation/FOF_Reset_S16_FXP/Data Type Conversion Inherited1
 * '<S119>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Lambda start index/PreLookUpIdSearch_U16
 * '<S120>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/GnLaMod_Subsystem
 * '<S121>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1
 * '<S122>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2
 * '<S123>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/GnLaMod_Subsystem/Look2D_IR_U16_U16_U16
 * '<S124>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/GnLaMod_Subsystem/LookUp_IR_U16
 * '<S125>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/GnLaMod_Subsystem/PreLookUpIdSearch_U16
 * '<S126>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/GnLaMod_Subsystem/Look2D_IR_U16_U16_U16/Data Type Conversion Inherited1
 * '<S127>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/GnLaMod_Subsystem/LookUp_IR_U16/Data Type Conversion Inherited3
 * '<S128>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/CalcLamRel
 * '<S129>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/CalcLambda
 * '<S130>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/CalcStep
 * '<S131>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/Search_lambda
 * '<S132>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/CalcLambda/LookUp_IR_U16
 * '<S133>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/CalcLambda/LookUp_IR_U16/Data Type Conversion Inherited3
 * '<S134>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x1/CalcStep/ratio_calc
 * '<S135>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/CalcLamRel
 * '<S136>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/CalcLambda
 * '<S137>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/CalcStep
 * '<S138>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/Search_lambda
 * '<S139>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/CalcLambda/LookUp_IR_U16
 * '<S140>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/CalcLambda/LookUp_IR_U16/Data Type Conversion Inherited3
 * '<S141>' : IonLambda/IonLambda/IonLambda_TDC/LambdaMeter/Partial_Lambda_Estimation/Lam_by_x2/CalcStep/ratio_calc
 * '<S142>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/BkFFSNCOutCalc
 * '<S143>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/Calc_indices_ad
 * '<S144>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/EnSelfAdj_Calc
 * '<S145>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/FFSGainCalc
 * '<S146>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/OLParamCalc
 * '<S147>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/SelfAdjFSM
 * '<S148>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/Update_TbFFSAdj
 * '<S149>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/BkFFSNCOutCalc/PreLookUpIdSearch_U16
 * '<S150>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/EnSelfAdj_Calc/LookUp_IR_U16_1
 * '<S151>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/EnSelfAdj_Calc/LookUp_IR_U16_2
 * '<S152>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/EnSelfAdj_Calc/LookUp_IR_U16_1/Data Type Conversion Inherited3
 * '<S153>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/EnSelfAdj_Calc/LookUp_IR_U16_2/Data Type Conversion Inherited3
 * '<S154>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/Update_TbFFSAdj/LookUp_U16_U16
 * '<S155>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/Update_TbFFSAdj/Saturation Dynamic
 * '<S156>' : IonLambda/IonLambda/IonLambda_TDC/SelfAdjust/Update_TbFFSAdj/LookUp_U16_U16/Data Type Conversion Inherited3
 */

#endif                                  /* _RTW_HEADER_IonLambda_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
