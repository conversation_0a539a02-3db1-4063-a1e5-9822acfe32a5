/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV6/tree/DD/HEATGRIPDRIVEMGM/Hea#$   */
/* $ Description:                                                                                                */
/* $Revision:: 9603   $                                                                                          */
/* $Date:: 2019-06-27 15:12:55 +0200 (gio, 27 giu 2019)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              ExhValPwm.c
 *
 * Author(s): Lana L.
 *
 *
 * Implementation notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifdef _BUILD_EXHVALPWM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ExhValPwm.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
 /// Diagnosis out
uint8_T ExVPwmDiagOut = NO_PT_FAULT;
/// Machine status
uint8_T StExVPwm = EXV_INIT;
/// Diag status
uint8_T StDiagExVPwm = 0u;
/// ExV Raw
uint32_T ExVTOnRaw = 0u;
/// ExV Period
uint32_T ExVPeriod = 0u;
/// ExV Duty Out
uint16_T ExVDuty = 0u;
/// ExV Input feedback
uint32_T ExhVDutyInFdbk = 0u;
/// ExV Output
uint16_T ExVDutyOut = 0u;
/// ExV Pos Out
int16_T AngExhValFdbk = 0;
/// ExV Pos error
int16_T AngExValErr = 0;
/// ExV Target
uint16_T AngExhTrgOut = 0u;
/// ExV Target
uint16_T ExVSBSMapOut = 0u;
/// Diag status raw
uint8_T StDiagExVPwmRaw = 0u;


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ExhValPwm_T50m (void);

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/// ExV Enable
static CCPTEST uint8_T *EnExVPwm;
/// ExV Trailing
static CCPTEST uint32_T ExVTrailing = 0u;
/// ExV Leading
static CCPTEST uint32_T ExVLeading = 0u;
/// Pwm diag in
static CCPTEST uint32_T ExhVDutyIn = 0u;
/// ExV Counter
static CCPTEST uint32_T CntExVEdge = 0u;
/// ExV self counter
static CCPTEST uint8_T  OldCntExhVMgmSelf = 0u;
/// ExV pos counter
static CCPTEST uint8_T  OldFlgExhVZeroPos = 0u;

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ExhValPwm_Init - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ExhValPwm_Init (void)
{
    t_EdgePolarity pol;
    int16_T ret = NO_ERROR;
    
    /// Init Power supply
    EnExVPwm = (uint8_T *)((uint32_T)&MainRelCmdNeg);

    /// Init Pwm
    if (EXVPWMINPOL != 0u)
    {
        pol = RISING_EDGE;
    }
    else
    {
        pol = FALLING_EDGE;
    }

    ret = PIO_PinConfig (EMIOS_UC10, (t_BusSelect)TIME, (t_EdgeSelection)BOTH_EDGE, (t_EdgePolarity)pol);

    if (ret == NO_ERROR)
    {
        #pragma ghs startnomisra
            ret = PIO_PinSetInterruptHandler(EMIOS_UC10, (TaskType)TaskExVPwmInID);
            ret = ret | PIO_PinEnableInterrupt(EMIOS_UC10);
            ret = ret | PIO_PinEnable(EMIOS_UC10);
        #pragma ghs endnomisra
        
        ExVPwmDiagOut = NO_PT_FAULT;
        EECntExVSelf++;
    }
    else { /* MISRA */ }

    if (EXVPWMOUTPOL != 0)
    {
        ExVDuty = (((uint16_T)(100.0f * 256.0f)) - CMD_EXV_VAL_MIN);
    }
    else
    {
        ExVDuty = CMD_EXV_VAL_MIN;
    }
}

/*--------------------------------------------------------------------------*
 * ExhValPwm_T10m - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ExhValPwm_T10m (void)
{
    static uint8_T cnt50ms = 0u;
    
    if (USEA2RSIGNAL == 2)
    {
        if (cnt50ms >= 4u)
        {
            ExhValPwm_T50m();
            cnt50ms = 0u;
        }
        else
        {
            cnt50ms++;
        }
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * ExhValPwm_ISR - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ExhValPwm_ISR(void)
{
    uint32_T tmpvalueCadr;
    static uint32_T oldExVTrailing = 0u;
    
    oldExVTrailing = ExVTrailing;
    ExVLeading = EMIOS.CH[EMIOS_UC10].CADR.R;
    ExVTrailing = EMIOS.CH[EMIOS_UC10].CBDR.R;

    /* TOn */
    if (ExVLeading < ExVTrailing)
    {
        tmpvalueCadr = (16777216u - ExVTrailing); // Max_uint24_T
        ExVTOnRaw = ExVLeading + tmpvalueCadr;
    }
    else
    {
        ExVTOnRaw = ExVLeading - ExVTrailing; 
    }

    /* Period */
    if (ExVTrailing < oldExVTrailing)
    {
        tmpvalueCadr = (16777216u - oldExVTrailing); // Max_uint24_T
        ExVPeriod = ExVTrailing + tmpvalueCadr;
    }
    else
    {
        ExVPeriod = ExVTrailing - oldExVTrailing; 
    }
    /* Diagnosis counter */
    CntExVEdge = 0u;
}

/*--------------------------------------------------------------------------*
 * GetEn_ExhValPwm - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_T GetEn_ExhValPwm (void)
{
    return *EnExVPwm;
}
 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ExhValPwm_T50m - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ExhValPwm_T50m (void)
{
    int16_T tmpAngExhValFdbk;
    int16_T tmpWorker;
    int32_T tmpWorker2;
    int32_T tmpWorker3;
    uint8_T stDiag;
    uint8_T ptFault;
    int16_T skDir;
    static uint8_T boot = 1u;
    static uint8_T diagEn = 0u;
    static uint8_T stBaund = 0u;
    static uint8_T stHysBaund = 0u;
    static uint8_T oldConf = 255u;
    static uint16_T cntConf = 0u;
    static uint16_T valConf = 65535u;
    static uint16_T valConfBoot = 65535u;
    static uint16_T cntExVWait = 0u;
    static uint8_T tmpConfOut = NO_PT_FAULT;
    static uint16_T oldExVDuty = CMD_EXV_VAL_MIN;
    static uint16_T oldExVDuty2 = CMD_EXV_VAL_MIN;

    ptFault = PtFault[DIAG_EXHVALVEFDBK];
    tmpWorker3 = ((ExVTOnRaw * 256u) / ExVPeriod);
    tmpWorker3 = tmpWorker3 * 100u;
    tmpWorker3 = min(tmpWorker3, (100 * 256));
    ExhVDutyIn = max(tmpWorker3, (0 * 256));

    if (EXVPWMOUTPOL != 0)
    {
        AngExhTrgOut = (((uint16_T)(100.0f * 256.0f)) - AngExhTrg);
    }
    else
    {
        AngExhTrgOut = AngExhTrg;
    }

    if ((*EnExVPwm != 0u) && (KeySignal != 0u))
    {
        if (CntExVEdge < 3u)
        {
            CntExVEdge++;
            if (ExVPeriod < ((uint32_T)(4762.0f * 80.0f))) // > 210Hz
            {
                /* EXV_SIG_NOT_PLAUSIBLE */
                StDiagExVPwmRaw = EXV_DIAG_SIG_ERROR;
                ptFault = SIG_NOT_PLAUSIBLE;
                tmpConfOut = NO_PT_FAULT;
                valConf = EXVPWMCONFON;
                valConfBoot = EXVPWMCONFOFF;
            }
            else if (ExVPeriod < ((uint32_T)(5263.0f * 80.0f))) // > 190Hz [200]
            {
                /* EXV_NORMAL */
                StDiagExVPwmRaw = EXV_DIAG_NORMAL;
                ptFault = NO_PT_FAULT;
                tmpConfOut = NO_PT_FAULT;
                valConf = 2u;
                valConfBoot = 2u;
            }
            else if (ExVPeriod < ((uint32_T)(5714.0f * 80.0f))) // > 175Hz [185]
            {
                /* EXV_OVRETEMP */
                StDiagExVPwmRaw = EXV_DIAG_POS_BLOCK;
                ptFault = OVER_CURRENT;
                tmpConfOut = NO_PT_FAULT;
                valConf = EXVPWMCONFON;
                valConfBoot = EXVPWMCONFOFF;
            }
            else if (ExVPeriod < ((uint32_T)(6060.0f * 80.0f))) // > 165Hz [170]
            {
                /* EXV_POS_BLOCK_CCW */
                StDiagExVPwmRaw = EXV_DIAG_POS_BLOCK_CCW;
                ptFault = OVER_CURRENT;
                tmpConfOut = NO_PT_FAULT;
                valConf = EXVPWMCONFON;
                valConfBoot = EXVPWMCONFOFF;
            }
            else if (ExVPeriod < ((uint32_T)(6452.0f * 80.0f))) // > 155Hz [160]
            {
                if ((ExVDuty > CMD_EXV_VAL_MAX) || (ExVDuty < CMD_EXV_VAL_MIN))
                {
                    /* EXV_NORMAL */
                    StDiagExVPwmRaw = EXV_DIAG_NORMAL;
                    ptFault = NO_PT_FAULT;
                    tmpConfOut = NO_PT_FAULT;
                    valConf = 2u;
                    valConfBoot = 2u;
                }
                else
                {
                    /* EXV_POS BLOCK */
                    StDiagExVPwmRaw = EXV_DIAG_POS_BLOCK;
                    ptFault = OVER_CURRENT;
                    tmpConfOut = NO_PT_FAULT;
                    valConf = EXVPWMCONFON;
                    valConfBoot = EXVPWMCONFOFF;
                }
            }
            else if (ExVPeriod < ((uint32_T)(6896.0f * 80.0f))) // > 145Hz [150]
            {
                /* EXV_POS_BLOCK_CW */
                StDiagExVPwmRaw = EXV_DIAG_POS_BLOCK_CW;
                ptFault = OVER_CURRENT;
                tmpConfOut = NO_PT_FAULT;
                valConf = EXVPWMCONFON;
                valConfBoot = EXVPWMCONFOFF;
            }
            else if (ExVPeriod < ((uint32_T)(7407.0f * 80.0f))) // > 135Hz [140]
            {
                /* EXV_SELF ERROR */
                StDiagExVPwmRaw = EXV_DIAG_SELF_ERROR;
                ptFault = SIGNAL_NOT_PRESENT;
                tmpConfOut = NO_PT_FAULT;
                valConf = 2u;
                valConfBoot = 2u;
            }
            else if (ExVPeriod < ((uint32_T)(8000.0f * 80.0f))) // > 125Hz [130]
            {
                /* EXV_COMPONENT_ERROR */
                StDiagExVPwmRaw = EXV_DIAG_POS_BLOCK;
                ptFault = SIG_NOT_PLAUSIBLE;
                tmpConfOut = NO_PT_FAULT;
                valConf = EXVPWMCONFON;
                valConfBoot = EXVPWMCONFOFF;
            }
            else if (ExVPeriod < ((uint32_T)(9090.0f * 80.0f))) // > 110Hz [120]
            {
                /* EXV_SELF OK */
                StDiagExVPwmRaw = EXV_DIAG_SELF_OK;
                ptFault = NO_PT_FAULT;
                tmpConfOut = NO_PT_FAULT;
                valConf = 2u;
                valConfBoot = 2u;
            }
            else
            {
                /* EXV_PWM OUT ERROR */
                StDiagExVPwmRaw = EXV_DIAG_OUT_ERROR;
                ptFault = NO_PT_FAULT;
                tmpConfOut = OPEN_CIRCUIT;
                valConf = EXVPWMCONFON;
                valConfBoot = EXVPWMCONFOFF;
            }
        }
        else
        {
            /* EXV_SIG_NOT_PLAUSIBLE */
            StDiagExVPwmRaw = EXV_DIAG_SIG_ERROR;
            ptFault = SIGNAL_STUCK;
            tmpConfOut = NO_PT_FAULT;
            valConf = EXVPWMCONFON;
            valConfBoot = EXVPWMCONFOFF;
        }
        if (EndStartFlg != 0u)
        {
            boot = 0u;
        }
        else 
        { 
            boot = 1u;
        }
        if ((oldConf == StDiagExVPwmRaw) && (StEngineStart != ENG_STARTING))
        {
            cntConf++;
        }
        else 
        {
            cntConf = 0u;
            diagEn = 0u;
        }
        oldConf = StDiagExVPwmRaw;
        if (boot != 0u)
        {
            if (cntConf > valConfBoot)
            {
                StDiagExVPwm = StDiagExVPwmRaw;
                ExVPwmDiagOut = tmpConfOut;
                cntConf = 0u;
                diagEn = 1u;
            }
            else { /* MISRA */ }
        }
        else
        {
            if (cntConf > valConf)
            {
                StDiagExVPwm = StDiagExVPwmRaw;
                ExVPwmDiagOut = tmpConfOut;
                cntConf = 0u;
                diagEn = 1u;
            }
            else { /* MISRA */ }
        }

        // Y = mX + q
        tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
        tmpWorker3 = (int32_T)(ExhVDutyIn * 100u * 256u);
        tmpWorker3 = tmpWorker3 / tmpWorker;
        tmpWorker2 = -((EEExVPercMin * 100 * 256) / tmpWorker);
        tmpWorker3 = tmpWorker3 + tmpWorker2;
        tmpWorker3 = min(tmpWorker3, (100 * 256));
        tmpAngExhValFdbk = max(tmpWorker3, (0 * 256));
        
        switch (StExVPwm)
        {
            case EXV_INIT:
            {
                if (StDiagExVPwm != EXV_DIAG_INIT)
                {
                    if (EEFlgExVPos == 0u)
                    {
                        StExVPwm = EXV_POS_END;
                        tmpAngExhValFdbk = (-1 * 256);
                        cntExVWait = 0u;
                    }
                    else if (EECntExVSelf > NUMCNTEXVSELF)
                    {
                        StExVPwm = EXV_RQ_SELF;
                        tmpAngExhValFdbk = (-1 * 256);
                        cntExVWait = 0u;
                    }
                    else if (StDiagExVPwm == EXV_DIAG_NORMAL)
                    {
                        StExVPwm = EXV_NORMAL;
                        tmpAngExhValFdbk = (-1 * 256);
                        cntExVWait = 0u;
                    }
                    else if ((StDiagExVPwm == EXV_DIAG_SIG_ERROR) || (StDiagExVPwm == EXV_DIAG_OUT_ERROR))
                    {
                        if (StDiag[DIAG_EXHVALVEFDBK] == FAULT)
                        {
                            if (EXVPWMOUTPOL != 0)
                            {
                                ExVDuty = (((uint16_T)(100.0f * 256.0f)) - CMD_EXV_POS_REC);
                                tmpAngExhValFdbk = (0 * 256);
                            }
                            else
                            {
                                ExVDuty = CMD_EXV_POS_REC;
                                tmpAngExhValFdbk = (100 * 256);
                            }
                        }
                        else { /* MISRA */ }
                    }
                    else if ((StDiagExVPwm == EXV_DIAG_POS_BLOCK) || (StDiagExVPwm == EXV_DIAG_POS_BLOCK_CW)  || (StDiagExVPwm == EXV_DIAG_POS_BLOCK_CCW))
                    {
                        StExVPwm = EXV_WAIT_OFF;
                        tmpAngExhValFdbk = (-1 * 256);
                        cntExVWait = 0u;
                    }
                    else if (StDiagExVPwm == EXV_DIAG_SELF_ERROR)
                    {
                        StExVPwm = EXV_RQ_SELF;
                        tmpAngExhValFdbk = (-1 * 256);
                        cntExVWait = 0u;
                    }
                    else { /* MISRA */ }
                }
                else { /* MISRA */ }
            }
            break;

            case EXV_RQ_SELF:
            {
                ExVDuty = CMD_EXV_RQ_SEFF;
                if (StDiagExVPwm == EXV_DIAG_SELF_OK)
                {
                    StExVPwm = EXV_SELF_OK;
                    cntExVWait = 0u;
                }
                else if (cntExVWait > 250u)
                {
                    StExVPwm = EXV_WAIT_OFF;
                    cntExVWait = 0u;
                }
                else
                {
                    cntExVWait++;
                }
            }
            break;

            case EXV_SELF_OK:
            {
                EECntExVSelf = 0u;
                if (stBaund == 0u)
                {
                    ExVDuty = CMD_EXV_TOUCH_MIN;
                    if (cntExVWait > 20u)
                    {
                        cntExVWait = 0u;
                        stBaund = 1u;
                        if (ENEXVBOUNDS != 0u)
                        {
                            EEExVPercMin = (0 * 256);
                        }
                        else
                        {
                            EEExVPercMin = ExhVDutyIn;
                        }
                    }
                    else
                    {
                        cntExVWait++;
                    }
                }
                else if (stBaund == 1u)
                {
                    ExVDuty = CMD_EXV_TOUCH_MAX;
                    if (cntExVWait > 20u)
                    {
                        cntExVWait = 0u;
                        stBaund = 2u;
                        if (ENEXVBOUNDS != 0u)
                        {
                            EEExVPercMax = (100 * 256);
                        }
                        else
                        {
                            EEExVPercMax = ExhVDutyIn;
                        }
                    }
                    else
                    {
                        cntExVWait++;
                    }
                }
                else
                {
                    EEFlgExVPos = 1u;
                    StExVPwm = EXV_NORMAL;
                    cntExVWait = 0u;
                    stBaund = 0u;
                }
            }
            break;

            case EXV_NORMAL:
            {
                if (((StDiagExVPwmRaw == EXV_DIAG_POS_BLOCK) || (StDiagExVPwmRaw == EXV_DIAG_POS_BLOCK_CW)  || (StDiagExVPwmRaw == EXV_DIAG_POS_BLOCK_CCW)) && (StEngineStart != ENG_STARTING))
                {
                    if (StDiag[DIAG_EXHVALVEFDBK] == FAULT)
                    {
                        StExVPwm = EXV_WAIT_OFF;
                        cntExVWait = 0u;
                    }
                    else
                    {
                        skDir = ((oldExVDuty > ((uint16_T)(50.0f * 256.0f))) ? -1 : 1);
                        if (cntExVWait == 0u)
                        {
                            cntExVWait = EXVPWMTIMESHAKE;
                            if (ExVDuty != oldExVDuty)
                            {
                                ExVDuty = oldExVDuty;
                            }
                            else
                            {
                                ExVDuty = oldExVDuty + (skDir * EXVPWMDELTSHAKE);
                            }
                        }
                        else
                        {
                            cntExVWait--;
                        }
                    } 
                }
                else if ((StDiagExVPwm == EXV_DIAG_SELF_ERROR) || (CntExhVMgmSelf != OldCntExhVMgmSelf))
                {
                    OldCntExhVMgmSelf = CntExhVMgmSelf;
                    StExVPwm = EXV_RQ_SELF;
                    tmpAngExhValFdbk = (-1 * 256);
                    cntExVWait = 0u;
                }
                else if (StDiagExVPwm == EXV_DIAG_NORMAL)
                {
                    // Y = mX + q
                    if (ENEXVBOUNDS != 0u)
                    {
                        if (AngExhTrgOut < CMD_EXV_VAL_MIN)
                        {
                            tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                            tmpWorker3 = CMD_EXV_TOUCH_MIN * tmpWorker;
                            if (ENEXVSHAKE != 0u)
                            {
                                stHysBaund = 1u;
                            }
                            else
                            {
                                stHysBaund = 0u;
                            }
                        }
                        else if (AngExhTrgOut > CMD_EXV_VAL_MAX)
                        {
                            tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                            tmpWorker3 = CMD_EXV_TOUCH_MAX * tmpWorker;
                            if (ENEXVSHAKE != 0u)
                            {
                                stHysBaund = 2u;
                            }
                            else
                            {
                                stHysBaund = 0u;
                            }
                        }
                        else
                        {
                            if (stHysBaund == 1u)
                            {
                                tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                                tmpWorker3 = (CMD_EXV_VAL_MIN + (7u * 256u)) * tmpWorker;
                                if (cntExVWait > 1u)
                                {
                                    stHysBaund = 0u;
                                    cntExVWait = 0u;
                                }
                                else
                                {
                                    cntExVWait++;
                                }
                            }
                            else if (stHysBaund == 2u)
                            {
                                tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                                tmpWorker3 = (CMD_EXV_VAL_MAX - (7u * 256u)) * tmpWorker;
                                if (cntExVWait > 1u)
                                {
                                    stHysBaund = 0u;
                                    cntExVWait = 0u;
                                }
                                else
                                {
                                    cntExVWait++;
                                }
                            }
                            else
                            {
                                tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                                tmpWorker3 = AngExhTrgOut * tmpWorker;
                                stHysBaund = 0u;
                            }
                        }
                        tmpWorker3 = tmpWorker3 / 100;
                        tmpWorker3 = tmpWorker3 + (EEExVPercMin << 8);
                        tmpWorker3 = min((tmpWorker3 >> 8), (100 * 256));
                        ExVDuty = max(tmpWorker3, (0 * 256));
                    }
                    else
                    {
                        if (EXVPWMOUTPOL != 0)
                        {
                            if (AngExhTrg <= EXVTOUCHMIN)
                            {
                                ExVDuty = CMD_EXV_TOUCH_MAX;
                                if (ENEXVSHAKE != 0u)
                                {
                                    stHysBaund = 1u;
                                }
                                else
                                {
                                    stHysBaund = 0u;
                                }
                            }
                            else if (AngExhTrg >= EXVTOUCHMAX)
                            {
                                ExVDuty = CMD_EXV_TOUCH_MIN;
                                if (ENEXVSHAKE != 0u)
                                {
                                    stHysBaund = 2u;
                                }
                                else
                                {
                                    stHysBaund = 0u;
                                }
                            }
                            else
                            {
                                if (stHysBaund == 1u)
                                {
                                    ExVDuty = CMD_EXV_TOUCH_MAX_EXIT; 
                                    if (cntExVWait > 1u)
                                    {
                                        stHysBaund = 0u;
                                        cntExVWait = 0u;
                                    }
                                    else
                                    {
                                        cntExVWait++;
                                    }
                                }
                                else if (stHysBaund == 2u)
                                {
                                    ExVDuty = CMD_EXV_TOUCH_MIN_EXIT; 
                                    if (cntExVWait > 1u)
                                    {
                                        stHysBaund = 0u;
                                        cntExVWait = 0u;
                                    }
                                    else
                                    {
                                        cntExVWait++;
                                    }
                                }
                                else
                                {
                                    if (ENSBSMAPPED != 0u)
                                    {
                                        LookUp_U16_U16(&ExVSBSMapOut, &VTEXVDUTY[0], AngExhTrgOut, &BKEXVDUTY[0], (BKEXVDUTY_dim - 1));
                                        oldExVDuty2 = ExVSBSMapOut;
                                        if (oldExVDuty2 <= VTEXVDUTY[0])
                                        {
                                            ExVDuty = max(CMD_EXV_VAL_MIN, VTEXVDUTY[0]);
                                        }
                                        else if (oldExVDuty2 >= VTEXVDUTY[1])
                                        {
                                            ExVDuty = min(CMD_EXV_VAL_MAX, VTEXVDUTY[1]);
                                        }
                                        else if (((oldExVDuty2 + EXVPWMSTEP) < ExVDuty) || (oldExVDuty2 > (ExVDuty + EXVPWMSTEP)))
                                        {
                                            ExVDuty = oldExVDuty2;
                                        }
                                        else { /* MISRA */ }
                                    }
                                    else 
                                    {
                                        stHysBaund = 0u;
                                        tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                                        tmpWorker3 = AngExhTrgOut * tmpWorker;
                                        tmpWorker3 = tmpWorker3 / 100;
                                        tmpWorker3 = tmpWorker3 + (EEExVPercMin << 8);
                                        tmpWorker3 = min((tmpWorker3 >> 8), CMD_EXV_VAL_MAX);
                                        oldExVDuty2 = max(tmpWorker3, CMD_EXV_VAL_MIN);
                                        if (oldExVDuty2 == CMD_EXV_VAL_MIN)
                                        {
                                            ExVDuty = CMD_EXV_VAL_MIN;
                                        }
                                        else if (oldExVDuty2 == CMD_EXV_VAL_MAX)
                                        {
                                            ExVDuty = CMD_EXV_VAL_MAX;
                                        }
                                        else if (((oldExVDuty2 + EXVPWMSTEP) < ExVDuty) || (oldExVDuty2 > (ExVDuty + EXVPWMSTEP)))
                                        {
                                            ExVDuty = oldExVDuty2;
                                        }
                                        else { /* MISRA */ }
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (AngExhTrgOut <= EXVTOUCHMIN)
                            {
                                ExVDuty = CMD_EXV_TOUCH_MIN;
                                if (ENEXVSHAKE != 0u)
                                {
                                    stHysBaund = 1u;
                                }
                                else
                                {
                                    stHysBaund = 0u;
                                }
                            }
                            else if (AngExhTrgOut >= EXVTOUCHMAX)
                            {
                                ExVDuty = CMD_EXV_TOUCH_MAX;
                                if (ENEXVSHAKE != 0u)
                                {
                                    stHysBaund = 2u;
                                }
                                else
                                {
                                    stHysBaund = 0u;
                                }
                            }
                            else
                            {
                                if (stHysBaund == 1u)
                                {
                                    ExVDuty = CMD_EXV_TOUCH_MIN_EXIT; 
                                    if (cntExVWait > 1u)
                                    {
                                        stHysBaund = 0u;
                                        cntExVWait = 0u;
                                    }
                                    else
                                    {
                                        cntExVWait++;
                                    }
                                }
                                else if (stHysBaund == 2u)
                                {
                                    ExVDuty = CMD_EXV_TOUCH_MAX_EXIT; 
                                    if (cntExVWait > 1u)
                                    {
                                        stHysBaund = 0u;
                                        cntExVWait = 0u;
                                    }
                                    else
                                    {
                                        cntExVWait++;
                                    }
                                }
                                else
                                {
                                    if (ENSBSMAPPED != 0u)
                                    {
                                        LookUp_U16_U16(&ExVSBSMapOut, &VTEXVDUTY[0], AngExhTrgOut, &BKEXVDUTY[0], (BKEXVDUTY_dim - 1));
                                        oldExVDuty2 = ExVSBSMapOut;
                                        if (oldExVDuty2 <= VTEXVDUTY[0])
                                        {
                                            ExVDuty = max(CMD_EXV_VAL_MIN, VTEXVDUTY[0]);
                                        }
                                        else if (oldExVDuty2 >= VTEXVDUTY[1])
                                        {
                                            ExVDuty = min(CMD_EXV_VAL_MAX, VTEXVDUTY[1]);
                                        }
                                        else if (((oldExVDuty2 + EXVPWMSTEP) < ExVDuty) || (oldExVDuty2 > (ExVDuty + EXVPWMSTEP)))
                                        {
                                            ExVDuty = oldExVDuty2;
                                        }
                                        else { /* MISRA */ }
                                    }
                                    else 
                                    {
                                        stHysBaund = 0u;
                                        tmpWorker = (int16_T)(EEExVPercMax - EEExVPercMin);
                                        tmpWorker3 = AngExhTrgOut * tmpWorker;
                                        tmpWorker3 = tmpWorker3 / 100;
                                        tmpWorker3 = tmpWorker3 + (EEExVPercMin << 8);
                                        tmpWorker3 = min((tmpWorker3 >> 8), CMD_EXV_VAL_MAX);
                                        oldExVDuty2 = max(tmpWorker3, CMD_EXV_VAL_MIN);
                                        if (oldExVDuty2 == CMD_EXV_VAL_MIN)
                                        {
                                            ExVDuty = CMD_EXV_VAL_MIN;
                                        }
                                        else if (oldExVDuty2 == CMD_EXV_VAL_MAX)
                                        {
                                            ExVDuty = CMD_EXV_VAL_MAX;
                                        }
                                        else if (((oldExVDuty2 + EXVPWMSTEP) < ExVDuty) || (oldExVDuty2 > (ExVDuty + EXVPWMSTEP)))
                                        {
                                            ExVDuty = oldExVDuty2;
                                        }
                                        else { /* MISRA */ }
                                    }
                                }
                            }
                        }
                    }
                    if (FlgExhVZeroPos != OldFlgExhVZeroPos)
                    {
                        OldFlgExhVZeroPos = FlgExhVZeroPos;
                        StExVPwm = EXV_RQ_POS;
                        tmpAngExhValFdbk = (-1 * 256);
                        cntExVWait = 0u;
                    }
                    else { /* MISRA */ }
                    oldExVDuty = ExVDuty;
                }
                else if ((StDiagExVPwm == EXV_DIAG_SIG_ERROR) || (StDiagExVPwm == EXV_DIAG_OUT_ERROR))
                {
                    if (StDiag[DIAG_EXHVALVEFDBK] == FAULT)
                    {
                        if (EXVPWMOUTPOL != 0)
                        {
                            ExVDuty = (((uint16_T)(100.0f * 256.0f)) - CMD_EXV_POS_REC);
                            tmpAngExhValFdbk = (0 * 256);
                        }
                        else
                        {
                            ExVDuty = CMD_EXV_POS_REC;
                            tmpAngExhValFdbk = (100 * 256);
                        }
                    }
                    else { /* MISRA */ }
                }
                else { /* MISRA */ }
            }
            break;

            case EXV_WAIT_OFF:
            {
                if (EXVPWMOUTPOL != 0)
                {
                    ExVDuty = (((uint16_T)(100.0f * 256.0f)) - CMD_EXV_POS_REC);
                }
                else
                {
                    ExVDuty = CMD_EXV_POS_REC;
                }
                if (CntExhVMgmSelf != OldCntExhVMgmSelf)
                {
                    OldCntExhVMgmSelf = CntExhVMgmSelf;
                    StExVPwm = EXV_RQ_SELF;
                    tmpAngExhValFdbk = (-1 * 256);
                    cntExVWait = 0u;
                }
                else { /* MISRA */ }
            }
            break;

            case EXV_RQ_POS:
            {
                EEFlgExVPos = 0u;
                EECntExVSelf = 0;
                EEExVPercMax = (int16_T)(87.5f * 256.0f);
                EEExVPercMin = (int16_T)(12.5f * 256.0f);
                ExVDuty = CMD_EXV_POS_EOL;
                if (cntExVWait > 50u)
                {
                    StExVPwm = EXV_POS_END;
                    cntExVWait = 0u;
                }
                else if (cntExVWait > 120u) /* Not rachable */
                {
                    StExVPwm = EXV_WAIT_OFF;
                    cntExVWait = 0u;
                }
                else
                {
                    cntExVWait++;
                }
            }
            break;

            case EXV_POS_END:
            {
                ExVDuty = CMD_EXV_POS_EOL;
                if (CntExhVMgmSelf != OldCntExhVMgmSelf)
                {
                    OldCntExhVMgmSelf = CntExhVMgmSelf;
                    StExVPwm = EXV_RQ_SELF;
                    tmpAngExhValFdbk = (-1 * 256);
                    cntExVWait = 0u;
                }
                else if (FlgExhVZeroPos != OldFlgExhVZeroPos)
                {
                    OldFlgExhVZeroPos = FlgExhVZeroPos;
                    StExVPwm = EXV_RQ_POS;
                    tmpAngExhValFdbk = (-1 * 256);
                    cntExVWait = 0u;
                }
                else { /* MISRA */ }
            }
            break;

            default:
            {
                tmpAngExhValFdbk = (-1 * 256);
            }
            break;
        }
        if (EXVPWMOUTPOL != 0)
        {
            AngExhValFdbk = (((uint16_T)(100.0f * 256.0f)) - tmpAngExhValFdbk);
        }
        else
        {
            AngExhValFdbk = tmpAngExhValFdbk;
        }
        AngExValErr = AngExhTrg - AngExhValFdbk;
    }
    else 
    {
        boot = 1u;
        diagEn = 0u;
        cntConf = 0u;
        oldConf = 255u;
        cntExVWait = 0u;
        tmpConfOut = NO_PT_FAULT;
        CntExVEdge = 0u;
        StExVPwm = EXV_INIT;
        StDiagExVPwm = EXV_DIAG_INIT;
        ExVPwmDiagOut = tmpConfOut;
    }
    
    if (FOEXVDUTYOUT < 0)
    {
        if (EnExhVMoving != 0u)
        {
            if (EXVPWMOUTPOL != 0)
            {
                ExVDuty = (((uint16_T)(100.0f * 256.0f)) - VOutExhActiveDiag);
            }
            else
            {
                ExVDuty = VOutExhActiveDiag;
            }
        }
        else
        {
            /* */
        }
    }
    else
    {
        if (EXVPWMOUTPOL != 0)
        {
            ExVDuty = (((uint16_T)(100.0f * 256.0f)) - FOEXVDUTYOUT);
        }
        else
        {
            ExVDuty = FOEXVDUTYOUT;
        }
    }
    
    if (EXVPWMOUTPOL != 0)
    {
        ExVDutyOut = (((uint16_T)(100.0f * 256.0f)) - ExVDuty);
        ExhVDutyInFdbk = (((uint16_T)(100.0f * 256.0f)) - ExhVDutyIn);
    }
    else
    {
        ExVDutyOut = ExVDuty;
        ExhVDutyInFdbk = ExhVDutyIn;
    }

    if (diagEn != 0)
    {
        DiagMgm_SetDiagState(DIAG_EXHVALVEFDBK, ptFault, &stDiag);
    }
    else { /* MISRA */}
}

#endif

/****************************************************************************
 ****************************************************************************/

