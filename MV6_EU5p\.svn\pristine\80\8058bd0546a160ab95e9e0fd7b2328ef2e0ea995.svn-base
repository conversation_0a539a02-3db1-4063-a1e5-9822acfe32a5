/** #########################################################################
**     Filename  : ccp_can_interface.C
**     Project   :
**     Processor : MPC5554
**     Version   :
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ######################################################################### */
/* MODULE ccp_can_interface */
#ifndef _CCP_CAN_INTERFACE_H_
#define _CCP_CAN_INTERFACE_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ccpTxCrmPossible - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int ccpTxCrmPossible( void );

#endif /* _CCP_CAN_INTERFACE_H_ */