#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#pragma ghs section rodata=".calib" 

//Enable injection disabling by IMMO (=1) [flag]
__declspec(section ".calib") uint8_T ENIMMOBLOCK =  0;   // 1
__declspec(section ".calib") uint8_T TIMIMMOINJSTARTEREN = 250;

#ifdef _BUILD_IMMO_

//Enable universal key detection [flag]
__declspec(section ".calib") uint8_T ENUNIVERSALKEY =  1;   // 1
//ImmoRDPhmeas nominal value [counter]
__declspec(section ".calib") uint8_T IMMOPHMEAS = 20;   //20
//ImmoRDPhmeas max value [counter]
__declspec(section ".calib") uint8_T IMMOPHMEASMAX = 31;   //31
//ImmoRDPhmeas min value [counter]
__declspec(section ".calib") uint8_T IMMOPHMEASMIN =  9;   // 9
//ImmoWRSmplc nominal value [counter]
__declspec(section ".calib") uint8_T IMMOSMPLC =  3;   // 3
// [counter]
__declspec(section ".calib") uint8_T UNIVERSALKEYVALUE[6] = 
{
  190,  250,  190,  250,  190,  250
};

#endif // _BUILD_IMMO_
