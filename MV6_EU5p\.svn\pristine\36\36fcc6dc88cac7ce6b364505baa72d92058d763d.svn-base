/*
 * File: KnockCorr.c
 *
 * Real-Time Workshop code generated for Simulink model KnockCorr.
 *
 * Model version                        : 1.272
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Tue Feb 06 16:40:32 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Tue Feb 06 16:40:38 2007
 */

#include "..\KNOCKCORR_F\include\KnockCorr.h"
#include "..\KNOCKCORR_F\include\KnockCorr_private.h"

/* Named constants for block: '<S13>/FIFO_Buf_Mgm' */
#define KnockCorr_IN_NO_ACTIVE_CHILD    (0)
#define KnockCorr_IN_Update             (2)
#define KnockCorr_IN_Reset              (1)

/* Named constants for block: '<S1>/Control_flow' */
#define KnockCorr_event_ev_PowerOn      (2U)
#define KnockCorr_event_ev_EOA          (7U)
#define KnockCorr_event_ev_NoSync       (1U)
#define KnockCorr_IN_NO_ACTIVE_CHILD_d  (0)
#define KnockCorr_IN_Disable            (1)
#define KnockCorr_IN_Enable             (2)
#define KnockCorr_b                     (1)
#define KnockCorr_c                     (7)
#define KnockCorr_d                     (2)

/* user code (top of source file) */
/* System: <Root>/KnockCorr */
#ifdef _BUILD_KNOCKCORR_

#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

uint8_T _sfEvent_KnockCorr_;

/* Exported block signals */
uint32_T statisticslow_hr_tmp[8];       /* '<S53>/Assignment2' */
uint16_T SumBufKnock[8];                /* '<S13>/FIFO_Buf_Mgm'
                                         * Sum of cylinder buffers
                                         */
uint16_T ofsr;                          /* '<S33>/Product' */
int16_T SAKCorrProtectMin;              /* '<S10>/Sum3'
                                         * Minimum value of SAKCorrProtect
                                         */
int16_T KCorrDec;                       /* '<S67>/Conversion'
                                         * Negative Delta knock correction for protection
                                         */
int16_T KCorrInc;                       /* '<S66>/Switch1'
                                         * Positive Delta knock correction for protection
                                         */
int16_T DeltaKCorrCyl;                  /* '<S61>/Switch1'
                                         * Delta knock correction for protection
                                         */
int16_T sakcorrprotect_cyl;             /* '<S61>/Sum' */
uint16_T ThKnckIntStat;                 /* '<S30>/Conversion'
                                         * Threshold on KnockInt for the statistic
                                         */
uint8_T knockstateold[8];               /* '<S7>/Memory1' */
uint8_T KCorrIncDelay;                  /* '<S62>/Parameters_choice'
                                         * Delay to increase the protective knock corr 
                                         */
uint8_T overthreshold_cyl;              /* '<S13>/Relational Operator' */
uint8_T OverThrMatrix[255];             /* '<S13>/FIFO_Buf_Mgm'
                                         * FIFO Buffer for knock statistic
                                         */
uint8_T triggeradat;                    /* '<S51>/Data Type Conversion' */
uint8_T statisticfastbit_cyl;           /* '<S50>/Relational Operator4' */
uint8_T statisticslowbit_cyl;           /* '<S50>/Dead_band' */
uint8_T TrigKnockAdat[8];               /* '<S28>/Assignement'
                                         * Trigger for knock learning
                                         */
uint8_T FStabLoadKnock;                 /* '<S22>/SigStab'
                                         * Load is stable for learning (=1)
                                         */
uint8_T FStabRpmKnock;                  /* '<S21>/SigStab'
                                         * RpmF is stable for learning (=1)
                                         */
boolean_T trigdectb;                    /* '<S13>/Relational Operator2' */
boolean_T en_res;                       /* '<S13>/Logical Operator' */
boolean_T en_hold;                      /* '<S13>/Logical Operator2' */
boolean_T FlgSteadyState;               /* '<S12>/Logical Operator2'
                                         * Both RpmF and Load are stable for learning (=1)
                                         */

/* Exported block states */
uint32_T statisticslow_hr[8];           /* '<Root>/_DataStoreBlk_4' */
uint16_T IDZoneKnockRpm;                /* '<Root>/_DataStoreBlk_1'
                                         * Rpm zone index for knock learning
                                         */
uint16_T IDZoneKnockLoad;               /* '<Root>/_DataStoreBlk_2'
                                         * Load zone index for knock learning
                                         */
uint16_T RtZoneKnockRpm;                /* '<Root>/_DataStoreBlk_12'
                                         * Rpm zone ratio for knock learning
                                         */
uint16_T RtZoneKnockLoad;               /* '<Root>/_DataStoreBlk_13'
                                         * Load zone ratio for knock learning
                                         */
int16_T SAKnockCorrAd[8];               /* '<Root>/_DataStoreBlk_16'
                                         * Adaptive knock correction on spark advance
                                         */
int16_T SAKnock[8];                     /* '<Root>/_DataStoreBlk_17'
                                         * Knock correction on Spark Advance
                                         */
int16_T SAKCorrProtect[8];              /* '<Root>/_DataStoreBlk_18'
                                         * Knock correction for engine protection
                                         */
int16_T DSAMapWrt[8];                   /* '<Root>/_DataStoreBlk_19'
                                         * Delta knock correction to be added to the table
                                         */
int16_T DSAMapFast[8];                  /* '<Root>/_DataStoreBlk_5'
                                         * Fast adaptive knock correction
                                         */
uint16_T StatisticsSlow[8];             /* '<Root>/_DataStoreBlk_3'
                                         * Slow knock statistic
                                         */
uint16_T StatisticsFast[8];             /* '<Root>/_DataStoreBlk_8'
                                         * Fast knock statistic
                                         */
uint8_T FlgKnockCtrlOff[8];             /* '<Root>/_DataStoreBlk_10'
                                         * The knock protective correction is active (=1)
                                         */
uint8_T FlgSAKnockSat[8];               /* '<Root>/_DataStoreBlk_11'
                                         * The knock correction on spark advance is saturated (=1)
                                         */
uint8_T eninc[8];                       /* '<Root>/_DataStoreBlk_14' */
uint8_T count[8];                       /* '<Root>/_DataStoreBlk_15' */
uint8_T sstab_rpm;                      /* '<Root>/_DataStoreBlk_6' */
uint8_T sstab_load;                     /* '<Root>/_DataStoreBlk_7' */

/* Block signals (auto storage) */
BlockIO_KnockCorr KnockCorr_B;

/* Block states (auto storage) */
D_Work_KnockCorr KnockCorr_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_KnockCorr KnockCorr_PrevZC;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_KnockCorr KnockCorr_U;

/* Real-time model */
RT_MODEL_KnockCorr KnockCorr_M_;
RT_MODEL_KnockCorr *KnockCorr_M = &KnockCorr_M_;

/* Output and update for function-call system: '<S1>/Reset_TbKnockAd' */
void KnockCorr_Reset_TbKnockAd(void)
{

  /* Stateflow: '<S1>/Reset_TbKnockAd' */

  {
    uint8_T i;
    uint8_T dim_i;
    uint8_T k;
    i = 0U;
    dim_i = (uint8_T)((uint8_T)(BKRPMKNOCK12_dim + 1) * N_CYLINDER);
    while(i < dim_i) {
      for(k = 0U; k <= BKLOADADKNOCK_dim; k++) {
        TbKnockAd[96 * k + i] = 0;
      }
      i++;
    }
  }
}

/* Output and update for function-call system: '<S1>/Reset_Variables' */
void KnockCorr_Reset_Variables(void)
{

  /* Stateflow: '<S1>/Reset_Variables' */

  {
    uint8_T i;
    i = 0U;
    sstab_rpm = 0U;

    sstab_load = 0U;

    while(i < N_CYLINDER) {
      eninc[(int32_T)i] = 0U;

      count[(int32_T)i] = 0U;

      SAKnockCorrAd[(int32_T)i] = 0;

      SAKnock[(int32_T)i] = 0;

      SAKCorrProtect[(int32_T)i] = 0;

      DSAMapWrt[(int32_T)i] = 0;

      FlgSAKnockSat[(int32_T)i] = 0U;

      StatisticsFast[(int32_T)i] = STAT_INIT_VALUE;

      StatisticsSlow[(int32_T)i] = STAT_INIT_VALUE;

      statisticslow_hr[(int32_T)i] = (uint32_T)STAT_INIT_VALUE << 14;

      i++;
    }
  }
}

/* Output and update for function-call system: '<S1>/Reset_Single_Variables' */
void KnockCorr_Reset_Single_Va(void)
{

  /* Stateflow: '<S1>/Reset_Single_Variables' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  eninc[(int32_T)IonAbsTdc] = 0U;

  count[(int32_T)IonAbsTdc] = 0U;

  SAKnockCorrAd[(int32_T)IonAbsTdc] = 0;

  SAKnock[(int32_T)IonAbsTdc] = 0;

  SAKCorrProtect[(int32_T)IonAbsTdc] = 0;

  DSAMapWrt[(int32_T)IonAbsTdc] = 0;

  FlgSAKnockSat[(int32_T)IonAbsTdc] = 0U;

  StatisticsFast[(int32_T)IonAbsTdc] = STAT_INIT_VALUE;

  StatisticsSlow[(int32_T)IonAbsTdc] = STAT_INIT_VALUE;

  statisticslow_hr[(int32_T)IonAbsTdc] = (uint32_T)STAT_INIT_VALUE << 14;
}

/* Disable for enable system: '<S8>/Calc_Ad_Corr' */
void KnockCorr_Calc_Ad_Corr_Disable(void)
{
  /* DisableFcn of enable SubSystem Block: <S8>/Calc_Ad_Corr */
  KnockCorr_DWork.Calc_Ad_Corr_MODE[0] = (int_T) SUBSYS_DISABLED;
}

/* Output and update for enable system: '<S8>/Calc_Ad_Corr' */
void KnockCorr_Calc_Ad_Corr(void)
{
  /* local block i/o variables*/
  int16_T rtb_Look2D_IR_S16;
  uint16_T rtb_idzoneknockfake;
  uint16_T rtb_FixPtSum1_a;
  uint16_T rtb_DataStoreRead2;
  uint16_T rtb_DataStoreRead1;
  uint16_T rtb_DataStoreRead3;

  /* switch on enable state */
  switch (rt_EnableState((boolean_T)(ENKNOCKADCORR > 0U),
    KnockCorr_DWork.Calc_Ad_Corr_MODE[0])) {
   case SUBSYS_BECOMING_ENABLED:
    /* (system initialize function is empty) */
    /* (system enable function is empty) */
    KnockCorr_DWork.Calc_Ad_Corr_MODE[0] = (int_T) SUBSYS_ENABLED;
    /*FALLTHROUGH*/
   case SUBSYS_ENABLED:

    /* Sum: '<S18>/FixPt Sum1' incorporates:
     *  Constant: '<S11>/BKRPMKNOCK12_dim'
     */
    rtb_FixPtSum1_a = (uint16_T)((uint32_T)BKRPMKNOCK12_dim + 1U);

    /* Sum: '<S11>/Add1' incorporates:
     *  DataStoreRead: '<S11>/Data Store Read'
     *  Product: '<S11>/Product'
     *  Inport: '<Root>/IonAbsTdc'
     */
    rtb_idzoneknockfake = (uint16_T)((uint32_T)IonAbsTdc *
      (uint32_T)rtb_FixPtSum1_a + (uint32_T)IDZoneKnockRpm);

    /* DataStoreRead: '<S11>/Data Store Read1' */
    rtb_DataStoreRead1 = RtZoneKnockRpm;

    /* DataStoreRead: '<S11>/Data Store Read2' */
    rtb_DataStoreRead2 = IDZoneKnockLoad;

    /* DataStoreRead: '<S11>/Data Store Read3' */
    rtb_DataStoreRead3 = RtZoneKnockLoad;

    /* S-Function (Look2D_IR_S16): '<S19>/Look2D_IR_S16' incorporates:
     *  Sum: '<S17>/FixPt Sum1'
     *  Product: '<S11>/Product1'
     *  Constant: '<S11>/N_CYL_MAX'
     *  Constant: '<S11>/BKLOADADKNOCK_dim'
     */

    Look2D_IR_S16( &rtb_Look2D_IR_S16, KnockCorr_B.tbknockad_old,
     rtb_idzoneknockfake, rtb_DataStoreRead1,
     ((uint8_T)((uint32_T)rtb_FixPtSum1_a * (uint32_T)N_CYL_MAX - 1U)),
     rtb_DataStoreRead2, rtb_DataStoreRead3, BKLOADADKNOCK_dim);

    /* Stateflow: '<S11>/Assign_SAKnockCorrAd' incorporates:
     *   Inport: '<Root>/IonAbsTdc'
     *  DataTypeConversion: '<S11>/Data Type Conversion'
     */

    SAKnockCorrAd[(int32_T)IonAbsTdc] = ((int16_T)(rtb_Look2D_IR_S16 >> 7));

    break;
   case SUBSYS_BECOMING_DISABLED:

    KnockCorr_Calc_Ad_Corr_Disable();

    /*FALLTHROUGH*/
   case SUBSYS_DISABLED:
    /* no action required while system is disabled */
    break;
   default:
    break;
  }
}

/* Output and update for function-call system: '<S33>/Write_TbKnockAd' */
void KnockCorr_Write_TbKnockAd(void)
{
  /* local block i/o variables*/
  int16_T rtb_i16_temp27;
  uint16_T rtb_DataStoreRead_o;
  uint16_T rtb_Look2D_U16_U16_U16;
  uint16_T rtb_DataStoreRead2_f;
  uint16_T rtb_DataStoreRead6;
  uint16_T rtb_DataStoreRead7;
  int16_T rtb_kcorradmin;
  int16_T rtb_Enforceupperlimit;
  int16_T rtb_Product2;
  int16_T rtb_knockadold;
  int16_T rtb_Enforcelowerlimit;
  int16_T rtb_i16_temp40;

  /* DataStoreRead: '<S37>/Data Store Read' */
  rtb_DataStoreRead_o = IDZoneKnockRpm;

  /* DataStoreRead: '<S37>/Data Store Read6' */
  rtb_DataStoreRead6 = RtZoneKnockRpm;

  /* S-Function (LookUp_IR_S16): '<S43>/LookUp_IR_S16' incorporates:
   *  Constant: '<S37>/VTKCORRMIN'
   *  Constant: '<S39>/BKRPMKNOCK12_dim'
   */

  LookUp_IR_S16( &rtb_i16_temp27, &VTKCORRMIN[0], rtb_DataStoreRead_o,
   rtb_DataStoreRead6, BKRPMKNOCK12_dim);

  /* Sum: '<S37>/Sum1' incorporates:
   *  Constant: '<S37>/DELTAKCORRMIN'
   */
  rtb_kcorradmin = (int16_T)(rtb_i16_temp27 + DELTAKCORRMIN);

  /* Product: '<S37>/Product1' */
  rtb_i16_temp27 = (int16_T)(KnockCorr_B.eta * KnockCorr_B.csi >> 6);

  /* DataStoreRead: '<S37>/Data Store Read7' */
  rtb_DataStoreRead7 = RtZoneKnockLoad;

  /* S-Function (Look2D_U16_U16_U16): '<S45>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S37>/TBGNAD'
   *  Constant: '<S37>/BKETACSIKNOCK'
   *  Constant: '<S40>/BKETACSIKNOCK_dim'
   */

  Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBGNAD[0], rtb_DataStoreRead6,
   &BKETACSIKNOCK[0], BKETACSIKNOCK_dim, rtb_DataStoreRead7, &BKETACSIKNOCK[0],
   BKETACSIKNOCK_dim);

  /* Product: '<S37>/Product2' */
  rtb_Product2 = (int16_T)(rtb_i16_temp27 * rtb_Look2D_U16_U16_U16 >> 13);

  /* Selector: '<S37>/Selector3' */
  KnockCorr_DWork.Selector3_DWORK1 = (int32_T)(KnockCorr_B.indr);

  KnockCorr_DWork.Selector3_DWORK2 = (int32_T)(KnockCorr_B.indc);

  rtb_knockadold =
    KnockCorr_B.tbknockad_old[(96*KnockCorr_DWork.Selector3_DWORK2)+KnockCorr_DWork.Selector3_DWORK1];

  {
    int16_T rtb_s16_rtmax;

    /* Sum: '<S37>/Sum' incorporates:
     *  Product: '<S37>/Product'
     */
    rtb_i16_temp40 = (int16_T)(((int16_T)(rt_MIN(MAXMAPWEIGHT, rtb_Product2) *
       KnockCorr_B.dsamapwrt_cyl >> 4)) + rtb_knockadold);

    /* MinMax: '<S42>/Enforce lower limit' */
    rtb_s16_rtmax = (int16_T)(rtb_kcorradmin << 7U);
    rtb_Enforcelowerlimit = rt_MAX(rtb_s16_rtmax, rtb_i16_temp40);

    /* DataStoreRead: '<S37>/Data Store Read2' */
    rtb_DataStoreRead2_f = IDZoneKnockLoad;
  }

  /* S-Function (Look2D_IR_S16): '<S47>/Look2D_IR_S16' incorporates:
   *  Constant: '<S37>/TBKCORRADMAX'
   *  Constant: '<S41>/BKRPMKNOCK12_dim'
   *  Constant: '<S41>/BKLOADADKNOCK_dim'
   */

  Look2D_IR_S16( &rtb_i16_temp40, &TBKCORRADMAX[0], rtb_DataStoreRead_o,
   rtb_DataStoreRead6, BKRPMKNOCK12_dim, rtb_DataStoreRead2_f,
   rtb_DataStoreRead7, BKLOADADKNOCK_dim);

  /* MinMax: '<S42>/Enforce upper limit' */
  rtb_Enforceupperlimit = rt_MIN(rtb_Enforcelowerlimit, rtb_i16_temp40);

  /* Stateflow: '<S37>/Assign_TbKnockAd' */

  TbKnockAd[96 * KnockCorr_B.indc + KnockCorr_B.indr] = rtb_Enforceupperlimit;
}

/* Initial conditions for enable system: '<S28>/CalcTable' */
void KnockCorr_CalcTable_Init(void)
{

  /*atomic Subsystem Block: <S33>/Calc_indices_ad */

  /* Initialize code for chart: '<S33>/Calc_indices_ad' */
  KnockCorr_B.indr = 0U;
  KnockCorr_B.indc = 0U;
  KnockCorr_B.eta = 0;
  KnockCorr_B.csi = 0;
}

/* Start for enable system: '<S28>/CalcTable' */
void KnockCorr_CalcTable_Start(void)
{

  KnockCorr_CalcTable_Init();
}

/* Output and update for enable system: '<S28>/CalcTable' */
void KnockCorr_CalcTable(void)
{
  if (KnockCorr_B.LogicalOperator) {

    /* Product: '<S33>/Product' incorporates:
     *  Sum: '<S36>/FixPt Sum1'
     *  Inport: '<Root>/IonAbsTdc'
     *  Constant: '<S33>/BKRPMKNOCK12_dim'
     */
    ofsr = (uint16_T)(((uint32_T)BKRPMKNOCK12_dim + 1U) * (uint32_T)IonAbsTdc);

    {

      /* Stateflow: '<S33>/Calc_indices_ad' */

      {
        uint8_T k;
        k = 1U;
        KnockCorr_B.indr = (uint8_T)(IDZoneKnockRpm + ofsr);
        KnockCorr_B.indc = (uint8_T)IDZoneKnockLoad;
        KnockCorr_B.eta = (int16_T)((65536 - RtZoneKnockRpm) >> 6);
        KnockCorr_B.csi = (int16_T)((65536 - RtZoneKnockLoad) >> 6);

        KnockCorr_Write_TbKnockAd();

        if(IDZoneKnockLoad < BKLOADADKNOCK_dim) {
          k = 2U;
          /*  indr=  IDZoneKnockRpm+ofsr; */
          KnockCorr_B.indc = (uint8_T)(IDZoneKnockLoad + 1);
          /*  eta=   (1-RtZoneKnockRpm); */
          KnockCorr_B.csi = (int16_T)(RtZoneKnockLoad >> 6);

          KnockCorr_Write_TbKnockAd();
        }
        if(IDZoneKnockRpm < BKRPMKNOCK12_dim) {
          k++;
          KnockCorr_B.indr = (uint8_T)((IDZoneKnockRpm + 1) + ofsr);
          KnockCorr_B.indc = (uint8_T)IDZoneKnockLoad;
          KnockCorr_B.eta = (int16_T)(RtZoneKnockRpm >> 6);
          KnockCorr_B.csi = (int16_T)((65536 - RtZoneKnockLoad) >> 6);

          KnockCorr_Write_TbKnockAd();

          if(k == 3) {
            /* indr=   IDZoneKnockRpm+1+ofsr; */
            KnockCorr_B.indc = (uint8_T)(IDZoneKnockLoad + 1);
            /*  eta=   RtZoneKnockRpm; */
            KnockCorr_B.csi = (int16_T)(RtZoneKnockLoad >> 6);

            KnockCorr_Write_TbKnockAd();
          }
        }
      }
    }
  }
}

/* Initial conditions for atomic system: '<S28>/Statistics' */
void KnockCorr_Statistics_Init(void)
{

  /* InitializeConditions for SubSystem: '<S34>/CheckStatistics' */

  /* Initialize code for chart: '<S50>/Dead_band' */
  statisticslowbit_cyl = 0U;

  /* end of InitializeConditions for SubSystem: '<S34>/CheckStatistics' */

  /* Initial conditions for atomic system: '<S34>/ComputeTriggerAdat' */

  /* InitializeConditions for Memory: '<S51>/Memory2' */
  KnockCorr_DWork.Memory2_PreviousInput = rtcP_pooled2;

  /* InitializeConditions for Memory: '<S60>/Memory' */
  KnockCorr_DWork.Memory_PreviousInput_k = rtcP_pooled11;
}

/* Start for atomic system: '<S28>/Statistics' */
void KnockCorr_Statistics_Start(void)
{

  /* Start for SubSystem: '<S34>/CheckStatistics' */

  /* end of Start for SubSystem: '<S34>/CheckStatistics' */
}

/* Output and update for atomic system: '<S28>/Statistics' */
void KnockCorr_Statistics(void)
{
  /* local block i/o variables*/
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int16_T rtb_Conversion5;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  uint16_T rtb_MathFunction;
  uint16_T rtb_Switch;
  uint16_T rtb_Product;
  uint16_T rtb_statisticfast_cyl;
  uint16_T rtb_Conversion;
  int8_T rtb_y;
  int8_T rtb_dsamapwrt_cyl_j;
  uint8_T rtb_IntegerDelay1;
  boolean_T rtb_RelationalOperator1_i;
  boolean_T rtb_Logic[2];
  boolean_T rtb_IntegerDelay;
  boolean_T rtb_Memory;

  /* Output and update for atomic system: '<S34>/BuildStatistics' */

  /* Product: '<S52>/Divide1' incorporates:
   *  Selector: '<S52>/Selector'
   *  Gain: '<S52>/Gain'
   *  Inport: '<Root>/IonAbsTdc'
   *  Constant: '<S52>/LENBUFKNOCKSTAT'
   */
  rtb_statisticfast_cyl = (uint16_T)(((uint32_T)SumBufKnock[(int32_T)IonAbsTdc]
    * (uint32_T)rtcP_pooled7 << 8U) / (uint32_T)LENBUFKNOCKSTAT);

  /* Stateflow: '<S52>/Assign_StatisticsFast' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  StatisticsFast[(int32_T)IonAbsTdc] = rtb_statisticfast_cyl;

  /* DataTypeConversion: '<S56>/Conversion5' */
  rtb_Conversion5 = (int16_T)rtb_statisticfast_cyl;

  /* S-Function (FOF_Reset_S16_FXP): '<S56>/FOF_Reset_S16_FXP' incorporates:
   *  DataTypeConversion: '<S56>/Conversion1'
   *  Constant: '<S53>/KFILTKNOCKSTAT'
   *  DataTypeConversion: '<S56>/Conversion4'
   *  Selector: '<S53>/Selector1'
   */

  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
   rtb_Conversion5, KFILTKNOCKSTAT, KnockCorr_ConstB.Conversion2_g,
   KnockCorr_ConstB.Conversion3,
   ((int32_T)KnockCorr_B.statisticslow_hr_old[(int32_T)IonAbsTdc]));

  {
    int32_T i1;

    /* DataTypeConversion: '<S57>/Conversion' */
    rtb_Conversion = (uint16_T)rtb_FOF_Reset_S16_FXP_o1;

    /* Stateflow: '<S53>/Assign_StatisticsSlow' incorporates:
     *   Inport: '<Root>/IonAbsTdc'
     */

    StatisticsSlow[(int32_T)IonAbsTdc] = rtb_Conversion;

    /* Assignment: '<S53>/Assignment2' incorporates:
     *  DataTypeConversion: '<S56>/Conversion7'
     *  Inport: '<Root>/IonAbsTdc'
     */
    for(i1 = 0; i1 < 8; i1++) {
      statisticslow_hr_tmp[i1] = KnockCorr_B.statisticslow_hr_old[i1];
    }
    statisticslow_hr_tmp[(int32_T)IonAbsTdc] =
      (uint32_T)rtb_FOF_Reset_S16_FXP_o2;
  }

  /* SubSystem: '<S34>/CheckStatistics' incorporates:
   *  Constant: '<S50>/SA_QUANT1'
   *  Constant: '<S50>/SA_QUANT'
   *  Constant: '<S50>/NDECSAQUANT'
   *  Constant: '<S50>/KNOCKSTATOVER'
   */

  /* Stateflow: '<S50>/Dead_band' incorporates:
   *  Constant: '<S50>/KNOCKSTATOVER'
   *  Constant: '<S50>/DBKNOCKSTATOVER'
   */

  {
    int16_T tmp;
    tmp = (int16_T)(rtb_Conversion - KNOCKSTATOVER);
    if(tmp > DBKNOCKSTATOVER) {
      rtb_y = -1;
      statisticslowbit_cyl = 1U;
    } else if(tmp < -DBKNOCKSTATOVER) {
      rtb_y = 1;
    } else {
      rtb_y = 0;
    }
  }

  /* Switch: '<S50>/Switch2' */
  if(trigdectb) {
    rtb_dsamapwrt_cyl_j = NDECSAQUANT;
  } else {
    rtb_dsamapwrt_cyl_j = rtb_y;
  }

  /* Product: '<S50>/Divide1' */
  KnockCorr_B.dsamapwrt_cyl = (int16_T)(SA_QUANT * rtb_dsamapwrt_cyl_j);

  /* Stateflow: '<S50>/Assign_DSAMapWrt' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  DSAMapWrt[(int32_T)IonAbsTdc] = KnockCorr_B.dsamapwrt_cyl;

  /* Product: '<S50>/Divide' incorporates:
   *  RelationalOperator: '<S50>/Relational Operator1'
   */
  KnockCorr_B.dsamapfast_cyl = (int16_T)((rtb_statisticfast_cyl < KNOCKSTATOVER)
    * SA_QUANT);

  /* RelationalOperator: '<S50>/Relational Operator4' incorporates:
   *  Product: '<S50>/Divide2'
   */
  statisticfastbit_cyl = rtb_statisticfast_cyl > (KNOCKSTATOVER << 1U);

  /* end of Outputs for SubSystem: '<S34>/CheckStatistics' */

  /* Output and update for atomic system: '<S34>/ComputeTriggerAdat' */

  /* S-Function (sfix_udelay): '<S51>/Integer Delay1'
   *
   * Regarding '<S51>/Integer Delay1':
   * Integer/Tapped Delay Block: '<S51>/Integer Delay1'
   */

  rtb_IntegerDelay1 = KnockCorr_DWork.IntegerDelay1_X[0];

  {
    uint32_T rtb_u32_tmp;
    uint16_T rtb_u16_tmp;

    /* Switch: '<S51>/Switch' incorporates:
     *  Sum: '<S51>/Sum'
     *  RelationalOperator: '<S51>/Relational Operator2'
     *  Logic: '<S51>/Logical Operator'
     *  Memory: '<S51>/Memory2'
     */
    if(statisticslowbit_cyl && (rtb_IntegerDelay1 == 0U) &&
     statisticfastbit_cyl) {
      rtb_Switch = 0U;
    } else {
      rtb_u32_tmp = (uint32_T)KnockCorr_DWork.Memory2_PreviousInput + 1U;
      if(rtb_u32_tmp > 65535U) {
        rtb_u16_tmp = MAX_uint16_T;
      } else {
        rtb_u16_tmp = (uint16_T)rtb_u32_tmp;
      }
      rtb_Switch = rtb_u16_tmp;
    }

    /* Product: '<S51>/Product' incorporates:
     *  Constant: '<S51>/Constant5'
     *  Constant: '<S51>/Constant6'
     *  Constant: '<S51>/Constant1'
     */
    rtb_Product = (uint16_T)((uint32_T)HOLDSA * (uint32_T)LENBUFKNOCKSTAT *
      (uint32_T)N_CYLINDER);
  }

  /* Math: '<S51>/Math Function' */
  /* Operator : rem */
  if (rtb_Product == 0U) {
    rtb_MathFunction = rtb_Switch;
  } else {
    rtb_MathFunction = rtb_Switch % rtb_Product;
  }

  /* RelationalOperator: '<S51>/Relational Operator1' */
  rtb_RelationalOperator1_i = rtb_MathFunction == 0U;

  /* S-Function (sfix_udelay): '<S51>/Integer Delay'
   *
   * Regarding '<S51>/Integer Delay':
   * Integer/Tapped Delay Block: '<S51>/Integer Delay'
   */

  rtb_IntegerDelay = KnockCorr_DWork.IntegerDelay_X[0];

  /* Memory: '<S60>/Memory' */
  rtb_Memory = KnockCorr_DWork.Memory_PreviousInput_k;

  /* CombinatorialLogic: '<S60>/Logic' */
  {
    int_T rowidx=0;
    /* Compute the truth table row index corresponding to the input */
    rowidx = (rowidx << 1) + (int_T)(rtb_RelationalOperator1_i != 0);
    rowidx = (rowidx << 1) + (int_T)(rtb_IntegerDelay != 0);
    rowidx = (rowidx << 1) + (int_T)(rtb_Memory != 0);
    /* Copy the appropriate row of the table into the block output vector */
    {
      int_T i1;

      boolean_T *y0 = rtb_Logic;

      for (i1=0; i1 < 2; i1++) {
        y0[i1] = KnockCorr_ConstP.Logic_table[rowidx + 8*i1];
      }
    }
  }

  /* DataTypeConversion: '<S51>/Data Type Conversion' */
  triggeradat = (uint8_T)rtb_Logic[0];

  /* Integer/Tapped Delay Block: '<S51>/Integer Delay1'
   */
  {
    int_T i1;

    for (i1=0; i1 < 7; i1++) {
      KnockCorr_DWork.IntegerDelay1_X[i1] =
        KnockCorr_DWork.IntegerDelay1_X[1+i1];
    }
  }
  KnockCorr_DWork.IntegerDelay1_X[7] = statisticslowbit_cyl;

  /* Update for Memory: '<S51>/Memory2' */
  KnockCorr_DWork.Memory2_PreviousInput = rtb_MathFunction;

  /* Integer/Tapped Delay Block: '<S51>/Integer Delay'
   */
  {
    int_T i1;

    for (i1=0; i1 < 7; i1++) {
      KnockCorr_DWork.IntegerDelay_X[i1] = KnockCorr_DWork.IntegerDelay_X[1+i1];
    }
  }
  KnockCorr_DWork.IntegerDelay_X[7] = rtb_RelationalOperator1_i;

  /* Update for Memory: '<S60>/Memory' */
  KnockCorr_DWork.Memory_PreviousInput_k = rtb_Logic[0];
}

/* Terminate for atomic system: '<S28>/Statistics' */
void KnockCorr_Statistics_Term(void)
{

  /* Terminate for SubSystem: '<S34>/CheckStatistics' */

  /* end of Terminate for SubSystem: '<S34>/CheckStatistics' */
}

/* Initial conditions for function-call system: '<S13>/UpAdTab' */
void KnockCorr_UpAdTab_Init(void)
{

  /*atomic Subsystem Block: <S28>/Statistics */
  KnockCorr_Statistics_Init();

  /* Initialize code for chart: '<S28>/Assignement' */

  {
    int32_T sf_i0;
    for(sf_i0 = 0; sf_i0 < 8; sf_i0++) {
      TrigKnockAdat[sf_i0] = 0U;
    }
  }
}

/* Start for function-call system: '<S13>/UpAdTab' */
void KnockCorr_UpAdTab_Start(void)
{

  /*atomic Subsystem Block: <S28>/Statistics */
  KnockCorr_Statistics_Start();

  /*enable Subsystem Block: <S28>/CalcTable */
  KnockCorr_CalcTable_Start();
}

/* Output and update for function-call system: '<S13>/UpAdTab' */
void KnockCorr_UpAdTab(void)
{

  /* SubSystem: '<S28>/Statistics' */
  KnockCorr_Statistics();

  /* Logic: '<S28>/Logical Operator' */
  KnockCorr_B.LogicalOperator = triggeradat || trigdectb;

  /* SubSystem: '<S28>/CalcTable' */
  KnockCorr_CalcTable();

  /* Stateflow: '<S28>/Assignement' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  TrigKnockAdat[(int32_T)IonAbsTdc] = (uint8_T)KnockCorr_B.LogicalOperator;
}

/* Terminate for function-call system: '<S13>/UpAdTab' */
void KnockCorr_UpAdTab_Term(void)
{

  KnockCorr_Statistics_Term();
}

/* Functions for block: '<S13>/FIFO_Buf_Mgm' */

static void KnockCorr_enter_internal_c3_Kno(void );

static void KnockCorr_enter_internal_c3_Kno(void )
{
  uint8_T i;
  /*  First entry */
  for(i = 0U; i < N_CYLINDER; i++) {
    SumBufKnock[(int32_T)i] =
      (uint16_T)div_nzp_s32((int32_T)((uint32_T)KNOCKSTATOVER *
      (uint32_T)LENBUFKNOCKSTAT), 25600);
    KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)i] = 0U;
  }
  i = 0U;
  KnockCorr_DWork.FIFO_Buf_Mgm.offset = (uint8_T)(25600 / KNOCKSTATOVER);
  KnockCorr_DWork.FIFO_Buf_Mgm.offset++;
  /*  To set the initial statistic to...
     LESS than KNOCKSTATOVER */
  while(i < LENBUFKNOCKSTAT) {
    i++;
    if((uint8_T)(i % KnockCorr_DWork.FIFO_Buf_Mgm.offset) == 0) {
      OverThrMatrix[i - 1] = 63U;
    } else {
      OverThrMatrix[i - 1] = 0U;
    }
  }
  /*  Wait enable */
  KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr = (uint8_T)KnockCorr_IN_Reset;
}

/* Shared Utility Functions */
int32_T div_nzp_s32(int32_T numerator, int32_T denominator)
{
  return (int32_T)(numerator / denominator + ((numerator < 0 != denominator < 0)
    && (numerator % denominator) ? 1 : 0));
}

/* Initial conditions for atomic system: '<S13>/FIFO_Buf_Mgm' */
void KnockCorr_FIFO_Buf_Mgm_Init(void)
{

  /* Initialize code for chart: '<S13>/FIFO_Buf_Mgm' */

  {
    int32_T sf_i0;
    KnockCorr_DWork.FIFO_Buf_Mgm.is_active_c3_KnockCorr = 0U;
    KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr = 0U;
    for(sf_i0 = 0; sf_i0 < 8; sf_i0++) {
      KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[sf_i0] = 0U;
      SumBufKnock[sf_i0] = 0U;
    }
    KnockCorr_DWork.FIFO_Buf_Mgm.offset = 0U;
    for(sf_i0 = 0; sf_i0 < 255; sf_i0++) {
      OverThrMatrix[sf_i0] = 0U;
    }
  }

  if (rtmIsFirstInitCond(KnockCorr_M)) {
    KnockCorr_UpAdTab_Init();
  }
}

/* Start for atomic system: '<S13>/FIFO_Buf_Mgm' */
void KnockCorr_FIFO_Buf_Mgm_Start(void)
{

  KnockCorr_UpAdTab_Start();
}

/* Output and update for atomic system: '<S13>/FIFO_Buf_Mgm' */
void KnockCorr_FIFO_Buf_Mgm(void)
{

  /* Stateflow: '<S13>/FIFO_Buf_Mgm' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  {
    uint8_T maskread;
    uint8_T _rlt2;
    uint8_T _rlt1;
    uint8_T i;
    if(KnockCorr_DWork.FIFO_Buf_Mgm.is_active_c3_KnockCorr == 0) {
      KnockCorr_DWork.FIFO_Buf_Mgm.is_active_c3_KnockCorr = 1U;
      KnockCorr_enter_internal_c3_Kno();
    } else {
      switch(KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr) {
       case KnockCorr_IN_Reset:
        if(en_res && en_hold) {
          if((!en_hold) || (VTFORKNOCKCYL[(int32_T)IonAbsTdc] == 1)) {
            /*  System disabled (hold) */
            KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr =
              (uint8_T)KnockCorr_IN_Update;
          } else {
            /*  System enabled */
            maskread = (uint8_T)(1 << IonAbsTdc);
            _rlt2 = KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc];
            SumBufKnock[(int32_T)IonAbsTdc] =
              (uint16_T)((SumBufKnock[(int32_T)IonAbsTdc] - ((maskread &
              OverThrMatrix[(int32_T)_rlt2]) > 0)) + overthreshold_cyl);
            OverThrMatrix[(int32_T)_rlt2] =
              (uint8_T)((OverThrMatrix[(int32_T)_rlt2] & ~maskread) |
              (uint8_T)(overthreshold_cyl << IonAbsTdc));
            KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] =
              (uint8_T)(_rlt2 + 1);
            /*  Reset buffer index */
            if(KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] >=
             LENBUFKNOCKSTAT) {
              KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] = 0U;
            }
            KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr =
              (uint8_T)KnockCorr_IN_NO_ACTIVE_CHILD;

            KnockCorr_UpAdTab();

            KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr =
              (uint8_T)KnockCorr_IN_Update;
          }
        }
        break;
       case KnockCorr_IN_Update:
        _rlt1 = VTFORKNOCKCYL[(int32_T)IonAbsTdc];
        if((!en_res) && (_rlt1 != 2)) {
          /*  Reset Buffer */
          for(i = 0U; i < N_CYLINDER; i++) {
            SumBufKnock[(int32_T)i] =
              (uint16_T)div_nzp_s32((int32_T)((uint32_T)KNOCKSTATOVER *
              (uint32_T)LENBUFKNOCKSTAT), 25600);
            KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)i] = 0U;
          }
          i = 0U;
          KnockCorr_DWork.FIFO_Buf_Mgm.offset = (uint8_T)(25600 / KNOCKSTATOVER);
          KnockCorr_DWork.FIFO_Buf_Mgm.offset++;
          /*  To set the initial statistic to...
             LESS than KNOCKSTATOVER */
          while(i < LENBUFKNOCKSTAT) {
            i++;
            if((uint8_T)(i % KnockCorr_DWork.FIFO_Buf_Mgm.offset) == 0) {
              OverThrMatrix[i - 1] = 63U;
            } else {
              OverThrMatrix[i - 1] = 0U;
            }
          }
          /*  Wait enable */
          KnockCorr_DWork.FIFO_Buf_Mgm.is_c3_KnockCorr =
            (uint8_T)KnockCorr_IN_Reset;
        } else if(_rlt1 == 2) {
          /*  System disabled (reset) */
          SumBufKnock[(int32_T)IonAbsTdc] =
            (uint16_T)div_nzp_s32((int32_T)((uint32_T)KNOCKSTATOVER *
            (uint32_T)LENBUFKNOCKSTAT), 25600);
          KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] = 0U;
          i = 0U;
          maskread = (uint8_T)(1 << IonAbsTdc);
          while(i < LENBUFKNOCKSTAT) {
            i++;
            if((uint8_T)(i % KnockCorr_DWork.FIFO_Buf_Mgm.offset) == 0) {
              OverThrMatrix[i - 1] = (uint8_T)(OverThrMatrix[i - 1] | maskread);
            } else {
              OverThrMatrix[i - 1] = (uint8_T)(OverThrMatrix[i - 1] & ~maskread);
            }
          }

          /* Output and update for function-call system: '<S13>/Reset_TbKnockAd_Cyl' */

          /* Stateflow: '<S26>/Reset_TbKnockAd' incorporates:
           *   Inport: '<Root>/IonAbsTdc'
           */

          {
            uint8_T i;
            uint8_T dim_i;
            uint8_T k;
            i = (uint8_T)((BKRPMKNOCK12_dim + 1) * IonAbsTdc);
            dim_i = (uint8_T)((i + BKRPMKNOCK12_dim) + 1);
            while(i < dim_i) {
              for(k = 0U; k <= BKLOADADKNOCK_dim; k++) {
                TbKnockAd[96 * k + i] = 0;
              }
              i++;
            }
          }
        } else if(!((!en_hold) || (_rlt1 == 1))) {
          /*  System enabled */
          maskread = (uint8_T)(1 << IonAbsTdc);
          _rlt2 = KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc];
          SumBufKnock[(int32_T)IonAbsTdc] =
            (uint16_T)((SumBufKnock[(int32_T)IonAbsTdc] - ((maskread &
            OverThrMatrix[(int32_T)_rlt2]) > 0)) + overthreshold_cyl);
          OverThrMatrix[(int32_T)_rlt2] =
            (uint8_T)((OverThrMatrix[(int32_T)_rlt2] & ~maskread) |
            (uint8_T)(overthreshold_cyl << IonAbsTdc));
          KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] =
            (uint8_T)(_rlt2 + 1);
          /*  Reset buffer index */
          if(KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] >=
           LENBUFKNOCKSTAT) {
            KnockCorr_DWork.FIFO_Buf_Mgm.IndBuf[(int32_T)IonAbsTdc] = 0U;
          }

          KnockCorr_UpAdTab();
        }
        break;
       default:
        KnockCorr_enter_internal_c3_Kno();
        break;
      }
    }
  }
}

/* Output and update for atomic system: '<S13>/Reset_statistic' */
void KnockCorr_Reset_statistic(void)
{

  /* Stateflow: '<S13>/Reset_statistic' */

  {
    uint8_T i;
    for(i = 0U; i < N_CYLINDER; i++) {
      if(en_res) {
        statisticslow_hr[(int32_T)i] = statisticslow_hr_tmp[(int32_T)i];
      } else {
        statisticslow_hr[(int32_T)i] = (uint32_T)STAT_INIT_VALUE << 14;
      }
    }
  }
}

/* Initial conditions for atomic system: '<S8>/Learning' */
void KnockCorr_Learning_Init(void)
{

  /*atomic Subsystem Block: <S13>/FIFO_Buf_Mgm */
  KnockCorr_FIFO_Buf_Mgm_Init();
}

/* Start for atomic system: '<S8>/Learning' */
void KnockCorr_Learning_Start(void)
{

  /*atomic Subsystem Block: <S13>/FIFO_Buf_Mgm */
  KnockCorr_FIFO_Buf_Mgm_Start();
}

/* Output and update for atomic system: '<S8>/Learning' */
void KnockCorr_Learning(void)
{
  /* local block i/o variables*/
  uint32_T rtb_Selector2;
  uint16_T rtb_DataStoreRead_k;
  uint16_T rtb_DataStoreRead2_l;
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_DataStoreRead1_h;
  uint16_T rtb_DataStoreRead3_j;
  uint8_T rtb_flgknockctrloff_old_cyl;

  /* Selector: '<S13>/Selector1' incorporates:
   *  Inport: '<Root>/IonAbsTdc'
   */
  rtb_flgknockctrloff_old_cyl =
    KnockCorr_B.flgknockctrloff_old[(int32_T)IonAbsTdc];

  /* RelationalOperator: '<S13>/Relational Operator2' */
  trigdectb = rtb_flgknockctrloff_old_cyl > KnockCorr_B.Compare;

  /* Logic: '<S13>/Logical Operator' incorporates:
   *  RelationalOperator: '<S13>/Relational Operator1'
   *  Inport: '<Root>/FlgNoTrqCtrSA'
   *  Inport: '<Root>/TWater'
   *  Constant: '<S13>/THWATKNOCKAD'
   *  Constant: '<S13>/ENKNOCKAD'
   */
  en_res = ENKNOCKAD && (TWater > THWATKNOCKAD) && FlgSteadyState &&
    FlgNoTrqCtrSA;

  /* Logic: '<S13>/Logical Operator2' */
  en_hold = (boolean_T)rtb_flgknockctrloff_old_cyl;

  /* Selector: '<S13>/Selector2' incorporates:
   *  Inport: '<Root>/KnockInt'
   *  Inport: '<Root>/IonAbsTdc'
   */
  rtb_Selector2 = KnockInt[(int32_T)IonAbsTdc];

  /* DataStoreRead: '<S13>/Data Store Read' */
  rtb_DataStoreRead_k = IDZoneKnockRpm;

  /* DataStoreRead: '<S13>/Data Store Read1' */
  rtb_DataStoreRead1_h = RtZoneKnockRpm;

  /* DataStoreRead: '<S13>/Data Store Read2' */
  rtb_DataStoreRead2_l = IDZoneKnockLoad;

  /* DataStoreRead: '<S13>/Data Store Read3' */
  rtb_DataStoreRead3_j = RtZoneKnockLoad;

  /* S-Function (Look2D_IR_U16): '<S29>/Look2D_IR_U16' incorporates:
   *  Constant: '<S13>/TBTHKNCKINTSTAT'
   *  Constant: '<S25>/BKRPMKNOCK12_dim'
   *  Constant: '<S25>/BKLOADADKNOCK_dim'
   */

  Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBTHKNCKINTSTAT[0], rtb_DataStoreRead_k,
   rtb_DataStoreRead1_h, BKRPMKNOCK12_dim, rtb_DataStoreRead2_l,
   rtb_DataStoreRead3_j, BKLOADADKNOCK_dim);

  /* DataTypeConversion: '<S30>/Conversion' */
  ThKnckIntStat = rtb_Look2D_IR_U16;

  /* RelationalOperator: '<S13>/Relational Operator' */
  overthreshold_cyl = rtb_Selector2 > ((uint32_T)ThKnckIntStat << 2U);

  /* Stateflow: '<S13>/FIFO_Buf_Mgm' */
  KnockCorr_FIFO_Buf_Mgm();

  /* Switch: '<S13>/Switch2' */
  if(en_hold) {
    KnockCorr_B.dsamapfastapp_cyl = KnockCorr_B.dsamapfast_cyl;
  } else {
    KnockCorr_B.dsamapfastapp_cyl = 0;
  }

  /* Stateflow: '<S13>/Assign_DSAMapFast' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  DSAMapFast[(int32_T)IonAbsTdc] = KnockCorr_B.dsamapfastapp_cyl;

  /* Stateflow: '<S13>/Reset_statistic' */
  KnockCorr_Reset_statistic();
}

/* Terminate for atomic system: '<S8>/Learning' */
void KnockCorr_Learning_Term(void)
{

  KnockCorr_UpAdTab_Term();
}

/* Initial conditions for atomic system: '<S7>/Adaptive_Corr' */
void KnockCorr_Adaptive_Corr_Init(void)
{

  /* Initial conditions for atomic system: '<S8>/Detect zones for learning' */

  /* InitializeConditions for Memory: '<S22>/Memory1' */
  KnockCorr_DWork.Memory1_PreviousInput = rtcP_pooled2;

  /* InitializeConditions for Memory: '<S22>/Memory' */
  KnockCorr_DWork.Memory_PreviousInput = rtcP_pooled2;

  /* InitializeConditions for Memory: '<S21>/Memory1' */
  KnockCorr_DWork.Memory1_PreviousInput_p = rtcP_pooled2;

  /* InitializeConditions for Memory: '<S21>/Memory' */
  KnockCorr_DWork.Memory_PreviousInput_l = rtcP_pooled2;

  /*atomic Subsystem Block: <S8>/Learning */
  KnockCorr_Learning_Init();
}

/* Disable for atomic system: '<S7>/Adaptive_Corr' */
void KnockCorr_Adaptive_Corr_Disable(void)
{

  /* enable Subsystem Block: <S8>/Calc_Ad_Corr */
  KnockCorr_Calc_Ad_Corr_Disable();
}

/* Start for atomic system: '<S7>/Adaptive_Corr' */
void KnockCorr_Adaptive_Corr_Start(void)
{

  /*atomic Subsystem Block: <S8>/Learning */
  KnockCorr_Learning_Start();
}

/* Output and update for atomic system: '<S7>/Adaptive_Corr' */
void KnockCorr_Adaptive_Corr(void)
{
  /* local block i/o variables*/
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_SigStab_o3_e;
  uint16_T rtb_SigStab_o4_o;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_i;
  uint16_T rtb_Memory_f;
  uint16_T rtb_u16_temp86;
  uint16_T rtb_u16_temp87;
  uint16_T rtb_u16_temp88;
  uint8_T rtb_state;
  uint8_T rtb_u8_temp90;

  /* DataTypeConversion: '<S15>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/RpmF'
   */
  rtb_u16_temp88 = RpmF;

  /* S-Function (PreLookUpIdSearch_U16): '<S15>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S8>/BKRPMKNOCK12'
   *  Constant: '<S8>/BKRPMKNOCK12_dim'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp88, &rtb_PreLookUpIdSearch_U16_o2,
   rtb_u16_temp88, &BKRPMKNOCK12[0], BKRPMKNOCK12_dim);

  /* DataStoreWrite: '<S8>/Data Store Write' */
  IDZoneKnockRpm = rtb_u16_temp88;

  /* S-Function (PreLookUpIdSearch_U16): '<S14>/PreLookUpIdSearch_U16' incorporates:
   *  DataTypeConversion: '<S14>/Data Type Conversion4'
   *  Constant: '<S8>/BKLOADADKNOCK'
   *  Constant: '<S8>/BKLOADADKNOCK_dim'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp88, &rtb_PreLookUpIdSearch_U16_o2_i, Load,
   &BKLOADADKNOCK[0], BKLOADADKNOCK_dim);

  /* DataStoreWrite: '<S8>/Data Store Write1' */
  IDZoneKnockLoad = rtb_u16_temp88;

  /* DataStoreWrite: '<S8>/Data Store Write2' */
  RtZoneKnockRpm = rtb_PreLookUpIdSearch_U16_o2;

  /* DataStoreWrite: '<S8>/Data Store Write3' */
  RtZoneKnockLoad = rtb_PreLookUpIdSearch_U16_o2_i;

  /* SubSystem: '<S8>/Calc_Ad_Corr' incorporates:
   *  Constant: '<S8>/ENKNOCKADCORR'
   */
  KnockCorr_Calc_Ad_Corr();

  /* Output and update for atomic system: '<S8>/Detect zones for learning' */

  /* DataStoreRead: '<S12>/Data Store Read1' */
  rtb_u8_temp90 = sstab_load;

  /* Memory: '<S22>/Memory1' */
  rtb_u16_temp86 = KnockCorr_DWork.Memory1_PreviousInput;

  /* Memory: '<S22>/Memory' */
  rtb_u16_temp87 = KnockCorr_DWork.Memory_PreviousInput;

  /* S-Function (SigStab): '<S22>/SigStab' incorporates:
   *  DataTypeConversion: '<S22>/Conversion2'
   *  Constant: '<S12>/Constant1'
   *  Constant: '<S12>/THRSTABLDKNOCK'
   *  Constant: '<S12>/TDCSTABKNOCK'
   */

  SigStab( &FStabLoadKnock, &rtb_state, &rtb_SigStab_o3, &rtb_SigStab_o4, Load,
   rtcP_pooled6, THRSTABLDKNOCK, TDCSTABKNOCK, rtb_u8_temp90, rtb_u16_temp86,
   rtb_u16_temp87);

  /* DataStoreWrite: '<S12>/Data Store Write' */
  sstab_load = rtb_state;

  /* Product: '<S12>/Product' incorporates:
   *  Inport: '<Root>/RpmF'
   *  Constant: '<S12>/MAX_RPM'
   */
  rtb_u16_temp87 = (uint16_T)(((uint32_T)RpmF << 7U) / (uint32_T)MAX_RPM * 100U);

  /* DataStoreRead: '<S12>/Data Store Read' */
  rtb_u8_temp90 = sstab_rpm;

  /* Memory: '<S21>/Memory1' */
  rtb_u16_temp86 = KnockCorr_DWork.Memory1_PreviousInput_p;

  /* Memory: '<S21>/Memory' */
  rtb_Memory_f = KnockCorr_DWork.Memory_PreviousInput_l;

  /* S-Function (SigStab): '<S21>/SigStab' incorporates:
   *  Constant: '<S12>/Constant'
   *  Constant: '<S12>/THRSTABRPMKNOCK'
   *  Constant: '<S12>/TDCSTABKNOCK'
   */

  SigStab( &FStabRpmKnock, &rtb_u8_temp90, &rtb_SigStab_o3_e, &rtb_SigStab_o4_o,
   rtb_u16_temp87, rtcP_pooled6, THRSTABRPMKNOCK, TDCSTABKNOCK, rtb_u8_temp90,
   rtb_u16_temp86, rtb_Memory_f);

  /* DataStoreWrite: '<S12>/Data Store Write1' */
  sstab_rpm = rtb_u8_temp90;

  /* Logic: '<S12>/Logical Operator2' */
  FlgSteadyState = FStabRpmKnock && FStabLoadKnock;

  /* Update for Memory: '<S22>/Memory1' */
  KnockCorr_DWork.Memory1_PreviousInput = rtb_SigStab_o3;

  /* Update for Memory: '<S22>/Memory' */
  KnockCorr_DWork.Memory_PreviousInput = rtb_SigStab_o4;

  /* Update for Memory: '<S21>/Memory1' */
  KnockCorr_DWork.Memory1_PreviousInput_p = rtb_SigStab_o3_e;

  /* Update for Memory: '<S21>/Memory' */
  KnockCorr_DWork.Memory_PreviousInput_l = rtb_SigStab_o4_o;

  /* SubSystem: '<S8>/Learning' */
  KnockCorr_Learning();
}

/* Terminate for atomic system: '<S7>/Adaptive_Corr' */
void KnockCorr_Adaptive_Corr_Term(void)
{

  KnockCorr_Learning_Term();
}

/* Initial conditions for atomic system: '<S7>/Cyl_Knock_Corr' */
void KnockCorr_Cyl_Knock_Corr_Init(void)
{

  /* InitializeConditions for SubSystem: '<S9>/Parameters for incrementing' */

  /* Initialize code for chart: '<S62>/Parameters_choice' */
  KCorrIncDelay = 0U;

  /* end of InitializeConditions for SubSystem: '<S9>/Parameters for incrementing' */
}

/* Start for atomic system: '<S7>/Cyl_Knock_Corr' */
void KnockCorr_Cyl_Knock_Corr_Start(void)
{

  /* Start for SubSystem: '<S9>/Parameters for incrementing' */

  /* end of Start for SubSystem: '<S9>/Parameters for incrementing' */
}

/* Output and update for atomic system: '<S7>/Cyl_Knock_Corr' */
void KnockCorr_Cyl_Knock_Corr(void)
{
  /* local block i/o variables*/
  int16_T rtb_LookUp_S16_S16;
  int16_T rtb_sakcorrprotect_cyl_old;
  uint8_T rtb_Product_c;
  uint8_T rtb_Product1_o;
  uint8_T rtb_countcyl;
  uint8_T rtb_eninccyl;
  uint8_T rtb_knockstatecyl_i;
  boolean_T rtb_LogicalOperator_i;
  boolean_T rtb_reseteninccyl;
  boolean_T rtb_RelationalOperator2_j;

  /* SubSystem: '<S9>/Parameters for incrementing' incorporates:
   *  Constant: '<S62>/KCORRINCN'
   *  Constant: '<S62>/KCORRINCSLOPE'
   *  Constant: '<S62>/KCORRINCSLOPE1'
   */

  /* Product: '<S62>/Product' */
  rtb_Product_c = (uint8_T)((KCORRINC << 10U) / KCORRINCSLOPEN);

  /* Product: '<S62>/Product1' */
  rtb_Product1_o = (uint8_T)((KCORRINC << 10U) / KCORRINCSLOPEI);

  /* Stateflow: '<S62>/Parameters_choice' incorporates:
   *   Inport: '<Root>/FlgNoTrqCtrSA'
   */

  if(FlgNoTrqCtrSA != 0) {
    KCorrIncDelay = rtb_Product_c;
  } else {
    KCorrIncDelay = rtb_Product1_o;
  }

  /* end of Outputs for SubSystem: '<S9>/Parameters for incrementing' */

  /* Output and update for atomic system: '<S9>/Calculate the delta correction' */

  /* Selector: '<S61>/Selector2' incorporates:
   *  DataStoreRead: '<S61>/Data Store Read'
   *  Inport: '<Root>/IonAbsTdc'
   */
  rtb_sakcorrprotect_cyl_old = SAKCorrProtect[(int32_T)IonAbsTdc];

  /* S-Function (LookUp_S16_S16): '<S65>/LookUp_S16_S16' incorporates:
   *  Constant: '<S61>/VTKCORRDEC'
   *  Selector: '<S61>/Selector1'
   *  Constant: '<S61>/BKDELTAKNOCK'
   *  Constant: '<S61>/BKDELTAKNOCK_dim'
   */

  LookUp_S16_S16( &rtb_LookUp_S16_S16, &VTKCORRDEC[0],
   DeltaKnockNPow[(int32_T)IonAbsTdc], &BKDELTAKNOCK[0], BKDELTAKNOCK_dim);

  /* DataTypeConversion: '<S67>/Conversion' */
  KCorrDec = rtb_LookUp_S16_S16;

  /* Selector: '<S61>/Selector5' incorporates:
   *  Inport: '<Root>/IonAbsTdc'
   *  Inport: '<Root>/KnockState'
   */
  rtb_knockstatecyl_i = KnockState[(int32_T)IonAbsTdc];

  /* Logic: '<S61>/Logical Operator' incorporates:
   *  RelationalOperator: '<S61>/Relational Operator'
   *  RelationalOperator: '<S61>/Relational Operator2'
   *  Constant: '<S61>/HEAVY_KNOCK'
   *  Constant: '<S61>/ACTIVE_KNOCK'
   */
  rtb_LogicalOperator_i = ((ACTIVE_KNOCK == rtb_knockstatecyl_i) ||
    (rtb_knockstatecyl_i == HEAVY_KNOCK));

  /* RelationalOperator: '<S66>/Relational Operator2' incorporates:
   *  Selector: '<S66>/Selector2'
   *  Inport: '<Root>/IonAbsTdc'
   *  Inport: '<Root>/KnockState'
   *  Constant: '<S66>/NO_KNOCK'
   */
  rtb_RelationalOperator2_j = (KnockState[(int32_T)IonAbsTdc] == NO_KNOCK);

  /* Logic: '<S66>/Logical Operator1' incorporates:
   *  Selector: '<S66>/Selector4'
   *  RelationalOperator: '<S66>/Relational Operator3'
   *  Inport: '<Root>/IonAbsTdc'
   *  Constant: '<S66>/NO_KNOCK'
   */
  rtb_reseteninccyl = (rtb_RelationalOperator2_j &&
    (knockstateold[(int32_T)IonAbsTdc] != NO_KNOCK));

  /* Switch: '<S66>/Switch2' incorporates:
   *  DataStoreRead: '<S66>/Data Store Read'
   *  Selector: '<S66>/Selector1'
   *  Sum: '<S70>/FixPt Sum1'
   *  Logic: '<S66>/Logical Operator3'
   *  DataStoreRead: '<S66>/Data Store Read3'
   *  Selector: '<S66>/Selector5'
   *  Logic: '<S66>/Logical Operator2'
   *  Inport: '<Root>/IonAbsTdc'
   */
  if((!rtb_RelationalOperator2_j) || eninc[(int32_T)IonAbsTdc]) {
    rtb_countcyl = 1U;
  } else {
    rtb_countcyl = (uint8_T)((uint32_T)count[(int32_T)IonAbsTdc] + 1U);
  }

  /* Switch: '<S66>/Switch' incorporates:
   *  RelationalOperator: '<S66>/Relational Operator'
   */
  if(rtb_reseteninccyl) {
    rtb_eninccyl = 1U;
  } else {
    rtb_eninccyl = rtb_countcyl >= KCorrIncDelay;
  }

  /* Switch: '<S66>/Switch1' incorporates:
   *  Constant: '<S66>/KCORRINCN'
   */
  if(rtb_eninccyl != 0U) {
    KCorrInc = KCORRINC;
  } else {
    KCorrInc = 0;
  }

  /* Switch: '<S61>/Switch1' */
  if(rtb_LogicalOperator_i) {
    DeltaKCorrCyl = KCorrDec;
  } else {
    DeltaKCorrCyl = KCorrInc;
  }

  /* Sum: '<S61>/Sum' incorporates:
   *  Inport: '<Root>/IonAbsTdc'
   *  Selector: '<S61>/Selector2'
   */
  sakcorrprotect_cyl = (int16_T)(rtb_sakcorrprotect_cyl_old + DeltaKCorrCyl);

  /* RelationalOperator: '<S64>/Compare' */
  KnockCorr_B.Compare = sakcorrprotect_cyl >= 0;

  /* Stateflow: '<S61>/Assign_FlgKnockCtrlOff' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  FlgKnockCtrlOff[(int32_T)IonAbsTdc] = KnockCorr_B.Compare;

  /* Stateflow: '<S66>/Assign_count' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  count[(int32_T)IonAbsTdc] = rtb_countcyl;

  /* Stateflow: '<S66>/Assign_eninccyl' incorporates:
   *   Inport: '<Root>/IonAbsTdc'
   */

  eninc[(int32_T)IonAbsTdc] = rtb_eninccyl;
}

/* Terminate for atomic system: '<S7>/Cyl_Knock_Corr' */
void KnockCorr_Cyl_Knock_Corr_Term(void)
{

  /* Terminate for SubSystem: '<S9>/Parameters for incrementing' */

  /* end of Terminate for SubSystem: '<S9>/Parameters for incrementing' */
}

/* Output and update for atomic system: '<S7>/Tot_Knock_Corr' */
void KnockCorr_Tot_Knock_Corr(void)
{
  /* local block i/o variables*/
  int16_T rtb_LookUp_IR_S16;
  uint16_T rtb_DataStoreRead4;
  uint16_T rtb_DataStoreRead5;
  int16_T rtb_Enforceupperlimit_i;
  int16_T rtb_Enforceupperlimit_l;
  int16_T rtb_saknockcorrad_cyl;
  int16_T rtb_saknock_tmp;
  boolean_T rtb_flgsaknocksat_cyl;

  /* Selector: '<S10>/Selector1' incorporates:
   *  DataStoreRead: '<S10>/Data Store Read1'
   *  Inport: '<Root>/IonAbsTdc'
   */
  rtb_saknockcorrad_cyl = SAKnockCorrAd[(int32_T)IonAbsTdc];

  /* DataStoreRead: '<S10>/Data Store Read4' */
  rtb_DataStoreRead4 = IDZoneKnockRpm;

  /* DataStoreRead: '<S10>/Data Store Read5' */
  rtb_DataStoreRead5 = RtZoneKnockRpm;

  /* S-Function (LookUp_IR_S16): '<S78>/LookUp_IR_S16' incorporates:
   *  Constant: '<S10>/VTKCORRMIN1'
   *  Constant: '<S75>/BKRPMKNOCK12_dim'
   */

  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTKCORRMIN[0], rtb_DataStoreRead4,
   rtb_DataStoreRead5, BKRPMKNOCK12_dim);

  {
    int16_T rtb_s16_rtmax;

    /* Sum: '<S10>/Sum3' */
    SAKCorrProtectMin = (int16_T)(rtb_LookUp_IR_S16 - rtb_saknockcorrad_cyl);

    /* MinMax: '<S77>/Enforce upper limit' incorporates:
     *  MinMax: '<S77>/Enforce lower limit'
     */
    rtb_s16_rtmax = rt_MAX(SAKCorrProtectMin, sakcorrprotect_cyl);
    rtb_Enforceupperlimit_i = rt_MIN(rtb_s16_rtmax, 0);

    /* Sum: '<S10>/Sum2' */
    rtb_saknock_tmp = (int16_T)((KnockCorr_B.dsamapfastapp_cyl +
      rtb_saknockcorrad_cyl) + rtb_Enforceupperlimit_i);

    /* MinMax: '<S76>/Enforce upper limit' incorporates:
     *  MinMax: '<S76>/Enforce lower limit'
     *  Constant: '<S10>/KCORRMAX'
     */
    rtb_s16_rtmax = rt_MAX(rtb_LookUp_IR_S16, rtb_saknock_tmp);
    rtb_Enforceupperlimit_l = rt_MIN(rtb_s16_rtmax, KCORRMAX);

    /* RelationalOperator: '<S10>/Relational Operator' */
    rtb_flgsaknocksat_cyl = rtb_saknock_tmp != rtb_Enforceupperlimit_l;

    /* Stateflow: '<S10>/Assign_FlgSAKnockSat' incorporates:
     *   Inport: '<Root>/IonAbsTdc'
     */

    FlgSAKnockSat[(int32_T)IonAbsTdc] = (uint8_T)rtb_flgsaknocksat_cyl;

    /* Stateflow: '<S10>/Assign_SAKCorrProtect' incorporates:
     *   Inport: '<Root>/IonAbsTdc'
     */

    SAKCorrProtect[(int32_T)IonAbsTdc] = rtb_Enforceupperlimit_i;

    /* Stateflow: '<S10>/Assign_SAKnock' incorporates:
     *   Inport: '<Root>/IonAbsTdc'
     */

    SAKnock[(int32_T)IonAbsTdc] = rtb_Enforceupperlimit_l;
  }
}

/* Initial conditions for function-call system: '<S1>/fcn_EOA' */
void KnockCorr_fcn_EOA_Init(void)
{

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* InitializeConditions for Memory: '<S7>/Memory1' */
      KnockCorr_DWork.knockstateold_j[i1] = (KnockCorr_ConstP.pooled10[i1]);
    }
  }

  /*atomic Subsystem Block: <S7>/Cyl_Knock_Corr */
  KnockCorr_Cyl_Knock_Corr_Init();

  /*atomic Subsystem Block: <S7>/Adaptive_Corr */
  KnockCorr_Adaptive_Corr_Init();
}

/* Disable for function-call system: '<S1>/fcn_EOA' */
void KnockCorr_fcn_EOA_Disable(void)
{

  KnockCorr_Adaptive_Corr_Disable();
}

/* Start for function-call system: '<S1>/fcn_EOA' */
void KnockCorr_fcn_EOA_Start(void)
{

  /*atomic Subsystem Block: <S7>/Cyl_Knock_Corr */
  KnockCorr_Cyl_Knock_Corr_Start();

  /*atomic Subsystem Block: <S7>/Adaptive_Corr */
  KnockCorr_Adaptive_Corr_Start();
}

/* Output and update for function-call system: '<S1>/fcn_EOA' */
void KnockCorr_fcn_EOA(void)
{

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* Memory: '<S7>/Memory1' */
      knockstateold[i1] = KnockCorr_DWork.knockstateold_j[i1];
    }
  }

  /* SubSystem: '<S7>/Cyl_Knock_Corr' */
  KnockCorr_Cyl_Knock_Corr();

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreRead: '<S7>/Data Store Read2' */
      KnockCorr_B.flgknockctrloff_old[i1] = FlgKnockCtrlOff[i1];
    }
    for(i1 = 0; i1 < 288; i1++) {

      /* DataStoreRead: '<S7>/Data Store Read' */
      KnockCorr_B.tbknockad_old[i1] = TbKnockAd[i1];
    }
    for(i1 = 0; i1 < 8; i1++) {

      /* DataStoreRead: '<S7>/Data Store Read1' */
      KnockCorr_B.statisticslow_hr_old[i1] = statisticslow_hr[i1];
    }
  }

  /* SubSystem: '<S7>/Adaptive_Corr' */
  KnockCorr_Adaptive_Corr();

  /* SubSystem: '<S7>/Tot_Knock_Corr' */
  KnockCorr_Tot_Knock_Corr();

  {
    int32_T i1;
    for(i1 = 0; i1 < 8; i1++) {

      /* Update for Memory: '<S7>/Memory1' */
      KnockCorr_DWork.knockstateold_j[i1] = KnockState[i1];
    }
  }
}

/* Terminate for function-call system: '<S1>/fcn_EOA' */
void KnockCorr_fcn_EOA_Term(void)
{

  KnockCorr_Cyl_Knock_Corr_Term();

  KnockCorr_Adaptive_Corr_Term();
}

/* Functions for block: '<S1>/Control_flow' */

static void KnockCorr_c7_KnockCorr(void );

static void KnockCorr_c7_KnockCorr(void )
{
  if(KnockCorr_DWork.Control_flow.is_active_c7_KnockCorr == 0) {
    KnockCorr_DWork.Control_flow.is_active_c7_KnockCorr = 1U;
    KnockCorr_DWork.Control_flow.is_active_Normal = 1U;
    /*  ev_PowerOn */

    KnockCorr_Reset_Variables();


    KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Disable;
    KnockCorr_DWork.Control_flow.is_active_Test_Forced = 1U;
  } else {
    switch(KnockCorr_DWork.Control_flow.is_Normal) {
     case KnockCorr_IN_Disable:
      /*  ev_EOA strategy enabled */
      if((_sfEvent_KnockCorr_ == KnockCorr_event_ev_EOA) && ((IonKnockEnabled !=
         0) && ENKNOCKCORR)) {
        if(VtRec[REC_KNOCKCORR_OFF_0 + IonAbsTdc] != 0) {

          KnockCorr_Reset_Single_Va();

          KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Enable;
        } else {
          /*  ev_EOA */

          KnockCorr_fcn_EOA();

          KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Enable;
        }
      } else {
        /*  ev_NoSync */
        if(_sfEvent_KnockCorr_ == KnockCorr_event_ev_NoSync) {

          KnockCorr_Reset_Variables();
        }
      }
      break;
     case KnockCorr_IN_Enable:
      /*  ev_NoSync */
      if(_sfEvent_KnockCorr_ == KnockCorr_event_ev_NoSync) {
        KnockCorr_DWork.Control_flow.is_Normal =
          (uint8_T)KnockCorr_IN_NO_ACTIVE_CHILD_d;

        KnockCorr_Reset_Variables();

        KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Disable;
      } else if(_sfEvent_KnockCorr_ == KnockCorr_event_ev_EOA) {
        /*  ev_EOA strategy disabled */
        if(!((IonKnockEnabled != 0) && ENKNOCKCORR)) {
          KnockCorr_DWork.Control_flow.is_Normal =
            (uint8_T)KnockCorr_IN_NO_ACTIVE_CHILD_d;

          KnockCorr_Reset_Variables();

          KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Disable;
        } else if(VtRec[REC_KNOCKCORR_OFF_0 + IonAbsTdc] != 0) {

          KnockCorr_Reset_Single_Va();

          KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Enable;
        } else {
          /*  ev_EOA */

          KnockCorr_fcn_EOA();

          KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Enable;
        }
      }
      break;
     default:
      /*  ev_PowerOn */

      KnockCorr_Reset_Variables();

      KnockCorr_DWork.Control_flow.is_Normal = (uint8_T)KnockCorr_IN_Disable;
      break;
    }
    if(VTFORCESAK[(int32_T)IonAbsTdc] == TRUE) {

      /* Stateflow: '<S1>/Force_SAKnock' incorporates:
       *   Inport: '<Root>/IonAbsTdc'
       */

      SAKnock[(int32_T)IonAbsTdc] = VTSAKNOCKFORCED[(int32_T)IonAbsTdc];
    }
  }
}

/* Initial conditions for trigger system: '<S1>/Control_flow' */
void KnockCorr_Control_flow_Init(void)
{

  /* Initialize code for chart: '<S1>/Control_flow' */
  KnockCorr_DWork.Control_flow.is_active_Normal = 0U;
  KnockCorr_DWork.Control_flow.is_Normal = 0U;
  KnockCorr_DWork.Control_flow.is_active_Test_Forced = 0U;
  KnockCorr_DWork.Control_flow.is_active_c7_KnockCorr = 0U;

  if (rtmIsFirstInitCond(KnockCorr_M)) {
    KnockCorr_fcn_EOA_Init();
  }
}

/* Disable for trigger system: '<S1>/Control_flow' */
void KnockCorr_Control_flow_Disable(void)
{

  /* Disable code for chart: '<S1>/Control_flow' */

  KnockCorr_fcn_EOA_Disable();
}

/* Start for trigger system: '<S1>/Control_flow' */
void KnockCorr_Control_flow_Start(void)
{

  KnockCorr_fcn_EOA_Start();
}

/* Output and update for trigger system: '<S1>/Control_flow' */
void KnockCorr_Control_flow(void)
{
  /* local block i/o variables*/
  int8_T rtb_inputevents[3];

  {
    ZCEventType trigEvent;
    ZCEventType zcEvents[3];

    /* subsystem trigger input */
    trigEvent = NO_ZCEVENT;

    zcEvents[0] = (ZCEventType) ((KnockCorr_U.ev_PowerOn > 0) &&
      (KnockCorr_PrevZC.sf_Control_flow_ZCE[0] == 0));
    KnockCorr_PrevZC.sf_Control_flow_ZCE[0] = (ZCSigState)
      KnockCorr_U.ev_PowerOn;
    trigEvent = (zcEvents[0] == NO_ZCEVENT)? trigEvent : zcEvents[0];

    zcEvents[1] = (ZCEventType) ((KnockCorr_U.ev_EOA > 0) &&
      (KnockCorr_PrevZC.sf_Control_flow_ZCE[1] == 0));
    KnockCorr_PrevZC.sf_Control_flow_ZCE[1] = (ZCSigState) KnockCorr_U.ev_EOA;
    trigEvent = (zcEvents[1] == NO_ZCEVENT)? trigEvent : zcEvents[1];

    zcEvents[2] = (ZCEventType) ((KnockCorr_U.ev_NoSync > 0) &&
      (KnockCorr_PrevZC.sf_Control_flow_ZCE[2] == 0));
    KnockCorr_PrevZC.sf_Control_flow_ZCE[2] = (ZCSigState) KnockCorr_U.ev_NoSync;
    trigEvent = (zcEvents[2] == NO_ZCEVENT)? trigEvent : zcEvents[2];
    /* conditionally execute */
    if (trigEvent != NO_ZCEVENT) {

      /* update trigger block outputs */

      rtb_inputevents[0] = (int8_T) zcEvents[0];

      rtb_inputevents[1] = (int8_T) zcEvents[1];

      rtb_inputevents[2] = (int8_T) zcEvents[2];

      /* Stateflow: '<S1>/Control_flow' incorporates:
       *   Inport: '<Root>/EERecStatus'
       *   Inport: '<Root>/IonKnockEnabled'
       *   Inport: '<Root>/IonAbsTdc'
       *   Inport: '<Root>/VtRec'
       */

      {
        uint8_T b_previousEvent;
        if(rtb_inputevents[0] == 1) {
          b_previousEvent = _sfEvent_KnockCorr_;
          _sfEvent_KnockCorr_ = KnockCorr_event_ev_PowerOn;
          KnockCorr_c7_KnockCorr();
          _sfEvent_KnockCorr_ = b_previousEvent;
        }
        if(rtb_inputevents[1] == 1) {
          b_previousEvent = _sfEvent_KnockCorr_;
          _sfEvent_KnockCorr_ = KnockCorr_event_ev_EOA;
          KnockCorr_c7_KnockCorr();
          _sfEvent_KnockCorr_ = b_previousEvent;
        }
        if(rtb_inputevents[2] == 1) {
          b_previousEvent = _sfEvent_KnockCorr_;
          _sfEvent_KnockCorr_ = KnockCorr_event_ev_NoSync;
          KnockCorr_c7_KnockCorr();
          _sfEvent_KnockCorr_ = b_previousEvent;
        }
      }
    }
  }
}

/* Model step function */
void KnockCorr_step(void)
{

  /* Output and update for atomic system: '<Root>/KnockCorr' */

  /* Stateflow: '<S1>/Control_flow' incorporates:
   *   Inport: '<Root>/ev_PowerOn'
   *   Inport: '<Root>/ev_EOA'
   *   Inport: '<Root>/ev_NoSync'
   */
  /* trigger Stateflow Block: <S1>/Control_flow */

  KnockCorr_Control_flow();

  /* set "at time zero" to false */
  if (rtmIsFirstInitCond(KnockCorr_M)) {
    rtmSetFirstInitCond(KnockCorr_M, 0);
  }
}

/* Model initialize function */

void KnockCorr_initialize(boolean_T firstTime)
{

  if (firstTime) {
    /* initialize real-time model */
    (void) memset((char_T *)KnockCorr_M,0,sizeof(RT_MODEL_KnockCorr));

    rtmSetFirstInitCond(KnockCorr_M, 1);

    {
      int32_T i1;

      /* Start for atomic system: '<Root>/KnockCorr' */

      /*trigger Subsystem Block: <S1>/Control_flow */
      KnockCorr_Control_flow_Start();

      for(i1 = 0; i1 < 8; i1++) {

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_3' */
        StatisticsSlow[i1] = (KnockCorr_ConstP.pooled5[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_4' */
        statisticslow_hr[i1] = (KnockCorr_ConstP._DataStoreBlk_4_Initia[i1]);

        /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_8' */
        StatisticsFast[i1] = (KnockCorr_ConstP.pooled5[i1]);
      }
    }
  }

  /* Zero-crossing state initialization */
  {
    int idx;
    for (idx = 0; idx < 3; idx ++) {
      KnockCorr_PrevZC.sf_Control_flow_ZCE[idx] = POS_ZCSIG;
    }
  }

  /* Initial conditions for atomic system: '<Root>/KnockCorr' */

  /*trigger Subsystem Block: <S1>/Control_flow */
  KnockCorr_Control_flow_Init();

  /* Machine initializer */
  _sfEvent_KnockCorr_ = CALL_EVENT;
}

/* user code (bottom of source file) */
/* System: <Root>/KnockCorr */
void KnockCorr_Init(void)
{
  KnockCorr_initialize(1);
  KnockCorr_U.ev_PowerOn = 0;
  KnockCorr_U.ev_EOA = 0;
  KnockCorr_U.ev_NoSync = 0;

  KnockCorr_step();

  KnockCorr_U.ev_PowerOn = 1;
  KnockCorr_U.ev_EOA = 0;
  KnockCorr_U.ev_NoSync = 0;

  KnockCorr_step();
}

void KnockCorr_NoSync(void)
{
  KnockCorr_U.ev_PowerOn = 0;
  KnockCorr_U.ev_EOA = 0;
  KnockCorr_U.ev_NoSync = 0;

  KnockCorr_step();

  KnockCorr_U.ev_PowerOn = 0;
  KnockCorr_U.ev_EOA = 0;
  KnockCorr_U.ev_NoSync = 1;

  KnockCorr_step();
}

void KnockCorr_EOA(void)
{
  KnockCorr_U.ev_PowerOn = 0;
  KnockCorr_U.ev_EOA = 0;
  KnockCorr_U.ev_NoSync = 0;

  KnockCorr_step();

  KnockCorr_U.ev_PowerOn = 0;
  KnockCorr_U.ev_EOA = 1;
  KnockCorr_U.ev_NoSync = 0;

  KnockCorr_step();
}

#else                                   // _BUILD_KNOCKCORR_                  

void KnockCorr_Init(void);
//uscite                                                                      
int16_T SAKnock[N_CYL_MAX];

void KnockCorr_Init(void)
{
  uint8_T i;
  for (i=0;i<N_CYLINDER;i++)
  SAKnock[i]=0;
}

#endif                                  // _BUILD_KNOCKCORR_                  

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
