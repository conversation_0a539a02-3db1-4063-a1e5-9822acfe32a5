/*
 * File: SpeedLimCtrl_out.h
 *
 * Code generated for Simulink model 'SpeedLimCtrl'.
 *
 * Model version                  : 1.721
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Nov 19 14:10:21 2020
 */

#ifndef RTW_HEADER_SpeedLimCtrl_out_h_
#define RTW_HEADER_SpeedLimCtrl_out_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define SPL_CTRL                       2U

/* Speed limiter Torque control */
#define SPL_DISABLE                    0U

/* Speed limiter disable */
#define SPL_SAT                        1U

/* Speed limiter Torque saturation */
#define SPL_SMOOTH                     3U

/* Speed limiter Torque returned smooth */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmiOffSpdLimI;

/* torque offset control */
extern int16_T CmiSpLimI;

/* Torque Speed limiter I */
extern int32_T CmiSpLimInt;

/* Integral torque */
extern int16_T CmiSpLimP;

/* Torque Speed limiter P */
extern int16_T CmiSpLimPSat;

/* torque staturated target */
extern int32_T CmiSpLimProp;

/* Proportional torque */
extern int32_T DeltaCmiSpLimInt;

/* Delta Integral torque */
extern int16_T ErrVsSpL;

/* Speed Limiter error */
extern int16_T GnCmiSpLimPSat;

/* Increment gain control */
extern uint16_T GnSpdLimSat;

/* Saturation gain */
extern uint32_T IDSpeedLimCtrl;

/* ID Version */
extern uint8_T StSpL;

/* Speed Limiter status */
#endif                                 /* RTW_HEADER_SpeedLimCtrl_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
