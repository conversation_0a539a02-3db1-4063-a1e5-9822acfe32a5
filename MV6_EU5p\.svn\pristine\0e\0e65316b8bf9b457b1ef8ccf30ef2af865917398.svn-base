/*
 * File: FOInjCtfMgm_private.h
 *
 * Code generated for Simulink model 'FOInjCtfMgm'.
 *
 * Model version                  : 1.360
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jan  5 15:31:29 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (30), Warnings (2), Error (0)
 */

#ifndef RTW_HEADER_FOInjCtfMgm_private_h_
#define RTW_HEADER_FOInjCtfMgm_private_h_
#include "rtwtypes.h"
#include "FOInjCtfMgm.h"

/* Includes for objects with custom storage classes. */
#include "syncmgm.h"
#include "MisfOBD2_out.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint16_T TBFOINJCTF0[24];       /* Variable: TBFOINJCTF0
                                        * Referenced by: '<S12>/TBFOINJCTF0'
                                        * Ctf selector
                                        */
extern uint16_T TBFOINJCTF1[24];       /* Variable: TBFOINJCTF1
                                        * Referenced by: '<S12>/TBFOINJCTF1'
                                        * Ctf selector
                                        */
extern uint16_T TBFOINJCTF2[24];       /* Variable: TBFOINJCTF2
                                        * Referenced by: '<S12>/TBFOINJCTF2'
                                        * Ctf selector
                                        */
extern uint16_T TBFOINJCTF3[24];       /* Variable: TBFOINJCTF3
                                        * Referenced by: '<S12>/TBFOINJCTF3'
                                        * Ctf selector
                                        */
extern uint8_T ENFOINJCUTOFF;          /* Variable: ENFOINJCUTOFF
                                        * Referenced by:
                                        *   '<S5>/ENFOINJCUTOFF'
                                        *   '<S13>/ENFOINJCUTOFF'
                                        * enable
                                        */
extern uint8_T ENFOINJCUTOFFTST;       /* Variable: ENFOINJCUTOFFTST
                                        * Referenced by: '<S22>/ENFOINJCUTOFFTST'
                                        * enable
                                        */
extern uint8_T SELIDXFOCTFTOVIEW;      /* Variable: SELIDXFOCTFTOVIEW
                                        * Referenced by: '<S11>/SELIDXFOCTFTOVIEW'
                                        * enable
                                        */
extern uint8_T SELTIMFOINJCTF;         /* Variable: SELTIMFOINJCTF
                                        * Referenced by:
                                        *   '<S14>/Player'
                                        *   '<S15>/Player'
                                        *   '<S16>/Player'
                                        *   '<S17>/Player'
                                        * enable
                                        */
extern void FOInjCtfMgm_Player_Init(uint8_T *rty_idx, rtDW_Player_FOInjCtfMgm_T *
  localDW);
extern void FOInjCtfMgm_Player(uint32_T rtu_TimFOInjCtf, uint8_T rtu_sync,
  uint16_T rtu_times, uint8_T *rty_idx, rtDW_Player_FOInjCtfMgm_T *localDW);
extern void FOInjC_Calc_SymFOInjCutoff_Init(uint8_T rty_SymFOInjCutoff[4],
  rtDW_Calc_SymFOInjCutoff_FOIn_T *localDW);
extern void FOInjCtfMgm_Calc_SymFOInjCutoff(uint8_T rtu_AbsPreTdc, const uint8_T
  rtu_VtFOInjCutoff[4], uint8_T rty_SymFOInjCutoff[4],
  rtDW_Calc_SymFOInjCutoff_FOIn_T *localDW);
extern void FOInjCtfMgm_Init(void);
extern void FOInjCtfMgm_PreTdc_Init(void);
extern void FOInjCtfMgm_PreTdc(void);
extern void FOInjCtfMgm_T10ms(void);

#endif                                 /* RTW_HEADER_FOInjCtfMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
