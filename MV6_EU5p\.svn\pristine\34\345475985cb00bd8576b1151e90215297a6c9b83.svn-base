/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_FLASHMGM_

#include "sys.h"
#include "flashmgm.h"
#include "diagmgm_out.h"

#undef _BUILD_FLASHMGM_FLASHCORRUPT_

#define TEST_APPLI_CHECKSUM_PERIOD  100     /* *100ms */
#define MAX_CHECKSUM_FAULTS           3     /* max number of faults to raise diagnosis */

/* PAY ATTENTION */
/* (CHECKSUM_BLOCK_LEN % 8) must be equal to 0 */
#define CHECKSUM_BLOCK_LEN (0x1000)
#define CHECKSUM_START_ADDR ((uint32_t)(&__APP_START))
#define CHECKSUM_STOP_ADDR (((uint32_t)(&__APP_START) + (uint32_t)(&__APP_SIZE)) - sizeof(BlockDescription))

#define CRC_BLOCK_LEN (0x200)      //512 byte
#define CRC_START_ADDR ((uint32_t)(&__CALIB_ROM_START ))
#define CRC_STOP_ADDR ((uint32_t)(&__CALIB_ROM_START ) + (uint32_t)(&__CALIB_ROM_SIZE) + (uint32_t)(&__APP_SIZE)) 


extern const uint16_t TESTCRCPERIODCNT;
extern const uint8_t WAITCNT;
extern  uint32_t    __CALIB_ROM_START;
extern  uint32_t    __CALIB_ROM_SIZE;


extern  uint32_t    __APP_START;
extern  uint32_t    __APP_SIZE;

uint8_T     FlashFaultCnt = 0;

uint8_t     FlashCorruptFlg = 0;

static  uint16_T    test_checksum_appli_callcount = 0;
uint8_T start_checksum_appli = 0;


static uint32_t checksum_appli_complete;
static uint32_t checksum = 0;
static uint32_t addr = CHECKSUM_START_ADDR;
uint8_T PtFaultFlash = NO_PT_FAULT;

uint32_t crc_appli_complete = 0;
static  uint16_t crc_tmp = 0;
static  uint32_t addrcrc = CRC_START_ADDR;
uint8_t cntUpdateCRC = 0;

static  uint16_T    test_crc_callcount = 0;
uint8_T  start_crc_appli = 0;
uint16_t crc_calib_appl = 0;
uint8_t flg_crc_complete = 0;
uint8_t FlgCVNCalc = 0;
uint16_t CVNValue = 0xffff;

uint8_T FlgCVNCalcThisTrip = 0;


#ifdef _BUILD_FLASHMGM_FLASHCORRUPT_
static void FLASHMGM_FlashCorrupt(void)
{
    BlockDescription Tag;
    uint8_t * p;
    uint8_t * pl;
    uint32_t dest;
    uint32_t size;
    uint32_t source;

    p = (uint8_t *)&Tag;
    pl = p + sizeof(BlockDescription);
    while (p<pl)
    {
        (*p) = 0;
        p++;
    }

    dest = (uint32_t)(&__APP_START) + (uint32_t)(&__APP_SIZE) - 2*sizeof(BlockDescription);
    Tag = *((BlockDescription*) dest);
    Tag.validMemoryRegion = 0;
    source = (uint32_t)&Tag;
    size = sizeof(BlockDescription);

    DisableAllInterrupts();
    FLASH_Program(dest, size, source);
    EnableAllInterrupts();
}
#endif

void FLASHMGM_FlashTestStart(void)
{
    if (test_checksum_appli_callcount >= TEST_APPLI_CHECKSUM_PERIOD)
    {
        test_checksum_appli_callcount = 0;
        start_checksum_appli = 1;
    }

    test_checksum_appli_callcount++;
}
void FLASHMGM_FlashTestCRC16Start(void)
{
    if (test_crc_callcount == 0)
    {
        test_crc_callcount = TESTCRCPERIODCNT;
        start_crc_appli = 1;
    }

    test_crc_callcount--;
}

void FLASHMGM_FlashTest(void)
{
    BlockDescription* appInfoPtr;
    uint32_t addr_next;
    uint32_t size;
    uint8_t  error = NO_ERROR;
    uint8_T  StDiagFlash = NO_FAULT;
    uint32_t checksum_block = 0 ;

    if (start_checksum_appli == 1)
    {
        appInfoPtr = (BlockDescription *) (((uint32_t)(&__APP_START) + (uint32_t)(&__APP_SIZE)) - sizeof(BlockDescription));
        checksum_appli_complete = 0;

        if (appInfoPtr->blockStatusOK == NO_ERROR)
        {
            size = CHECKSUM_BLOCK_LEN;
            addr_next = addr + size;
            if (addr_next >= CHECKSUM_STOP_ADDR)
            {
                size = (CHECKSUM_STOP_ADDR - addr);
                checksum_appli_complete = 1;
            }

            if (FLASH_CheckSum(addr, size, &checksum_block) == NO_ERROR)
            {
                checksum = checksum + checksum_block ;
            }
            addr += size;

            if (checksum_appli_complete != 0)
            {
                if (appInfoPtr->blockChecksum != checksum)
                {
                    error = 1;
                }

                start_checksum_appli = 0;
                addr = CHECKSUM_START_ADDR;
                checksum = 0;
            }
        }
        else
        {
            start_checksum_appli = 0;
            addr = CHECKSUM_START_ADDR;
            checksum = 0;
            error = 1;
        }

        if (error != NO_ERROR)
        {
            FlashFaultCnt++;
            if (FlashFaultCnt >= MAX_CHECKSUM_FAULTS)
            {
                FlashFaultCnt = MAX_CHECKSUM_FAULTS;
                PtFaultFlash = CIRCUIT_MALFUNCTION;
            }
        }
    }

    DiagMgm_SetDiagState(DIAG_FLASH, PtFaultFlash, &StDiagFlash);

#ifdef _BUILD_FLASHMGM_FLASHCORRUPT_
    if (FlashCorruptFlg != 0)
    {
        FLASHMGM_FlashCorrupt();
        FlashCorruptFlg = 0;
    }
#endif
}

void FLASHMGM_FlashTestCRC16(void)
{
    uint32_t addr_next;
    uint32_t size;
    static uint8_t flgFirstCall = 1;

    if (flgFirstCall)
    {
        crc_tmp = 0;
        flgFirstCall = 0;
    }
    crc_appli_complete = 0;
    if (start_crc_appli == 1)
    {
        if (cntUpdateCRC == 0)
        {
            size = CRC_BLOCK_LEN;
            addr_next = addrcrc + CRC_BLOCK_LEN;
            if (addr_next >= CRC_STOP_ADDR)
            {
                size = (CRC_STOP_ADDR - addrcrc);
                crc_appli_complete = 1;
            }
            crc_tmp = update_crc16(crc_tmp, (uint8_t *)addrcrc, size, UTILS_CRC_REVERSED);
            addrcrc += size;
            cntUpdateCRC = WAITCNT;
        }
        if (crc_appli_complete)
        {
            crc_calib_appl = crc_tmp;
            crc_tmp = 0;
            addrcrc = CRC_START_ADDR;
            start_crc_appli = 0;
            flg_crc_complete = 1;
            flgFirstCall = 1;
            cntUpdateCRC = 0;
            FlgCVNCalc = 1;
            FlgCVNCalcThisTrip = 1;
        }
        cntUpdateCRC--;
    }
}

int16_t FLASHMGM_GetCrc(uint16_t *crc_16)
{
    if (flg_crc_complete)
    {
        *crc_16 = crc_calib_appl;
        return(NO_ERROR);
    }
    else
    {
        return(FLASH_CRC_NOT_VALID);
    }
}

#endif /* _BUILD_FLASHMGM_ */
