/*
 * File: mul_u32_u32_u32_sr10_sat.h
 *
 * Code generated for Simulink model 'CreepLimiterMgm'.
 *
 * Model version                  : 1.89
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Thu Apr 12 13:27:52 2018
 */

#ifndef SHARE_mul_u32_u32_u32_sr10_sat
#define SHARE_mul_u32_u32_u32_sr10_sat
#include "rtwtypes.h"

extern uint32_T mul_u32_u32_u32_sr10_sat(uint32_T a, uint32_T b);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
