/*
 * File: Pby_Mgm.c
 *
 * Code generated for Simulink model 'Pby_Mgm'.
 *
 * Model version                  : 1.2249
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Fri May 16 09:49:09 2014
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA-C:2004 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (14), Warnings (3), Error (0)
 */

#include "Pby_Mgm.h"
#include "Pby_Mgm_private.h"

/* Named constants for Chart: '<S4>/StPby_Calc' */
#define Pby_Mgm_IN_Engine_running      ((uint8_T)1U)
#define Pby_Mgm_IN_NO_ACTIVE_CHILD     ((uint8_T)0U)
#define Pby_Mgm_IN_act                 ((uint8_T)1U)
#define Pby_Mgm_IN_apply               ((uint8_T)1U)
#define Pby_Mgm_IN_count               ((uint8_T)1U)
#define Pby_Mgm_IN_disabled            ((uint8_T)2U)
#define Pby_Mgm_IN_exit_act            ((uint8_T)2U)
#define Pby_Mgm_IN_init                ((uint8_T)3U)
#define Pby_Mgm_IN_wait_init           ((uint8_T)2U)
#define Pby_Mgm_IN_wait_wot            ((uint8_T)3U)
#define Pby_Mgm_IN_wc                  ((uint8_T)2U)

/* user code (top of source file) */
/* System '<Root>/Pby_Mgm' */
#ifdef _BUILD_PBYMGM_

/* Block signals (auto storage) */
BlockIO_Pby_Mgm Pby_Mgm_B;

/* Block states (auto storage) */
D_Work_Pby_Mgm Pby_Mgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_Pby_Mgm Pby_Mgm_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_Pby_Mgm Pby_Mgm_U;

/* Exported data definition */
int16_T CmeMaxPby;                     /* CME pby slow */
uint8_T CntCyclesPby;                  /* Pby activations */
uint8_T FlgStabPassBy;                 /* Speed vehicle is steady for pass by (=1) */
uint8_T StPassBy;                      /* Status of the pass by chart */

/* Output and update for function-call system: '<S4>/FlgStabPassBy_Calc' */
void Pby_Mgm_FlgStabPassBy_Calc(void)
{
  /* DataTypeConversion: '<S8>/Data Type Conversion' incorporates:
   *  Constant: '<S10>/Constant'
   *  Constant: '<S8>/DVEHSPEEDPBY'
   *  Constant: '<S8>/MAXPBYCYCLES'
   *  Constant: '<S8>/MAXPBYGEAR'
   *  Constant: '<S8>/MAXPBYKMODO'
   *  Constant: '<S8>/MINPBYGEAR'
   *  Constant: '<S8>/VEHSPEEDPBY1'
   *  Constant: '<S8>/VEHSPEEDPBY2'
   *  Constant: '<S8>/VEHSPEEDPBY3'
   *  DataStoreRead: '<S8>/Data Store Read1'
   *  Inport: '<Root>/GearPosClu'
   *  Inport: '<Root>/Odometer'
   *  Inport: '<Root>/OdometerKeyOn'
   *  Inport: '<Root>/VehSpeed'
   *  Logic: '<S11>/FixPt Logical Operator'
   *  Logic: '<S12>/FixPt Logical Operator'
   *  Logic: '<S13>/FixPt Logical Operator'
   *  Logic: '<S14>/FixPt Logical Operator'
   *  Logic: '<S8>/Logical Operator'
   *  Logic: '<S8>/Logical Operator1'
   *  Logic: '<S8>/Logical Operator2'
   *  RelationalOperator: '<S10>/Compare'
   *  RelationalOperator: '<S11>/Lower Test'
   *  RelationalOperator: '<S11>/Upper Test'
   *  RelationalOperator: '<S12>/Lower Test'
   *  RelationalOperator: '<S12>/Upper Test'
   *  RelationalOperator: '<S13>/Lower Test'
   *  RelationalOperator: '<S13>/Upper Test'
   *  RelationalOperator: '<S14>/Lower Test'
   *  RelationalOperator: '<S14>/Upper Test'
   *  RelationalOperator: '<S8>/Relational Operator'
   *  RelationalOperator: '<S8>/Relational Operator1'
   *  Sum: '<S8>/Add'
   *  Sum: '<S8>/Sub1'
   *  Sum: '<S8>/Sub2'
   *  Sum: '<S8>/Sub3'
   *  Sum: '<S8>/Sub4'
   *  Sum: '<S8>/Sub5'
   *  Sum: '<S8>/Sub6'
   */
  Pby_Mgm_B.DataTypeConversion = (uint8_T)(((((((((uint16_T)(VEHSPEEDPBY1 -
    DVEHSPEEDPBY)) <= VehSpeed) && (VehSpeed <= ((uint16_T)(((uint32_T)
    VEHSPEEDPBY1) + ((uint32_T)DVEHSPEEDPBY))))) || ((((uint16_T)(VEHSPEEDPBY2 -
    DVEHSPEEDPBY)) <= VehSpeed) && (VehSpeed <= ((uint16_T)(((uint32_T)
    VEHSPEEDPBY2) + ((uint32_T)DVEHSPEEDPBY)))))) || ((((uint16_T)(VEHSPEEDPBY3
    - DVEHSPEEDPBY)) <= VehSpeed) && (VehSpeed <= ((uint16_T)(((uint32_T)
    VEHSPEEDPBY3) + ((uint32_T)DVEHSPEEDPBY)))))) && (MAXPBYCYCLES >
    CntCyclesPby)) && ((CntCyclesPby > 0) || ((Odometer - OdometerKeyOn) <
    MAXPBYKMODO))) && ((MINPBYGEAR <= GearPosClu) && (GearPosClu <= MAXPBYGEAR)));

  /* Selector: '<S8>/Selector' incorporates:
   *  Constant: '<S8>/VTCMEMAXPBY'
   *  Inport: '<Root>/GearPosClu'
   */
  Pby_Mgm_B.Selector = VTCMEMAXPBY[GearPosClu];
}

/* Output and update for function-call system: '<S1>/T10ms' */
void Pby_Mgm_T10ms(void)
{
  int32_T tmp;

  /* Chart: '<S4>/StPby_Calc' incorporates:
   *  Inport: '<Root>/AbsCntWUC'
   *  Inport: '<Root>/CmeDriverP'
   *  Inport: '<Root>/CntAbsTdc'
   *  Inport: '<Root>/GasPosCC'
   */
  /* Gateway: Pby_Mgm/T10ms/StPby_Calc */
  /* During: Pby_Mgm/T10ms/StPby_Calc */
  if (Pby_Mgm_DWork.bitsForTID0.is_active_c7_Pby_Mgm == 0U) {
    /* Entry: Pby_Mgm/T10ms/StPby_Calc */
    Pby_Mgm_DWork.bitsForTID0.is_active_c7_Pby_Mgm = 1U;

    /* Entry Internal: Pby_Mgm/T10ms/StPby_Calc */
    /* Transition: '<S9>:15' */
    if (AbsCntWUC < THCNTWUCPBY) {
      /* Transition: '<S9>:3' */
      Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_init;

      /* Entry 'init': '<S9>:4' */
      Pby_Mgm_B.StPassBy_j = INIT_PBY;
    } else {
      /* Transition: '<S9>:6' */
      Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_disabled;

      /* Entry 'disabled': '<S9>:23' */
      Pby_Mgm_B.StPassBy_j = DISABLE_PBY;
    }
  } else {
    switch (Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm) {
     case Pby_Mgm_IN_Engine_running:
      /* During 'Engine_running': '<S9>:1' */
      if (CntAbsTdc == 1U) {
        /* Outputs for Function Call SubSystem: '<S4>/FlgStabPassBy_Calc' */

        /* Transition: '<S9>:8' */
        /* Event: '<S9>:65' */
        Pby_Mgm_FlgStabPassBy_Calc();

        /* End of Outputs for SubSystem: '<S4>/FlgStabPassBy_Calc' */
        if (Pby_Mgm_B.DataTypeConversion != 0) {
          /* Transition: '<S9>:26' */
          Pby_Mgm_DWork.t_pby = 0U;

          /* Exit Internal 'Engine_running': '<S9>:1' */
          /* Exit Internal 'apply': '<S9>:7' */
          Pby_Mgm_DWork.bitsForTID0.is_apply = Pby_Mgm_IN_NO_ACTIVE_CHILD;

          /* Exit Internal 'wc': '<S9>:5' */
          Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_Engine_running;
          Pby_Mgm_DWork.bitsForTID0.is_Engine_running = Pby_Mgm_IN_wc;
          Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_count;

          /* Entry 'count': '<S9>:17' */
          Pby_Mgm_B.StPassBy_j = COUNT_PBY;
        } else {
          /* Transition: '<S9>:35' */
          /* Exit Internal 'Engine_running': '<S9>:1' */
          /* Exit Internal 'apply': '<S9>:7' */
          Pby_Mgm_DWork.bitsForTID0.is_apply = Pby_Mgm_IN_NO_ACTIVE_CHILD;
          Pby_Mgm_DWork.bitsForTID0.is_Engine_running =
            Pby_Mgm_IN_NO_ACTIVE_CHILD;

          /* Exit Internal 'wc': '<S9>:5' */
          Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_NO_ACTIVE_CHILD;
          Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_init;

          /* Entry 'init': '<S9>:4' */
          Pby_Mgm_B.StPassBy_j = INIT_PBY;
        }
      } else if (Pby_Mgm_DWork.bitsForTID0.is_Engine_running == Pby_Mgm_IN_apply)
      {
        /* During 'apply': '<S9>:7' */
        if (GasPosCC < THGASPBY) {
          /* Transition: '<S9>:14' */
          /* Exit Internal 'apply': '<S9>:7' */
          Pby_Mgm_DWork.bitsForTID0.is_apply = Pby_Mgm_IN_NO_ACTIVE_CHILD;
          Pby_Mgm_DWork.bitsForTID0.is_Engine_running = Pby_Mgm_IN_wc;
          Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_wait_init;

          /* Entry 'wait_init': '<S9>:16' */
          Pby_Mgm_B.StPassBy_j = WAIT_INIT_PBY;
          tmp = Pby_Mgm_B.CntCyclesPby_l + 1;
          if (tmp > 255) {
            tmp = 255;
          }

          Pby_Mgm_B.CntCyclesPby_l = (uint8_T)tmp;
        } else {
          if (Pby_Mgm_DWork.bitsForTID0.is_apply == Pby_Mgm_IN_act) {
            /* During 'act': '<S9>:25' */
            if (Pby_Mgm_DWork.t_pby > TDURPBY) {
              /* Transition: '<S9>:11' */
              Pby_Mgm_DWork.bitsForTID0.is_apply = Pby_Mgm_IN_exit_act;

              /* Entry 'exit_act': '<S9>:12' */
              Pby_Mgm_B.StPassBy_j = EXIT_ACT_PBY;
            } else {
              /* Transition: '<S9>:42' */
              Pby_Mgm_DWork.t_pby++;
            }
          }
        }
      } else {
        /* During 'wc': '<S9>:5' */
        if (!(Pby_Mgm_B.DataTypeConversion != 0)) {
          /* Transition: '<S9>:2' */
          /* Exit Internal 'wc': '<S9>:5' */
          Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_NO_ACTIVE_CHILD;
          Pby_Mgm_DWork.bitsForTID0.is_Engine_running =
            Pby_Mgm_IN_NO_ACTIVE_CHILD;
          Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_init;

          /* Entry 'init': '<S9>:4' */
          Pby_Mgm_B.StPassBy_j = INIT_PBY;
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/FlgStabPassBy_Calc' */

          /* Event: '<S9>:65' */
          Pby_Mgm_FlgStabPassBy_Calc();

          /* End of Outputs for SubSystem: '<S4>/FlgStabPassBy_Calc' */
          switch (Pby_Mgm_DWork.bitsForTID0.is_wc) {
           case Pby_Mgm_IN_count:
            /* During 'count': '<S9>:17' */
            /* Transition: '<S9>:19' */
            if (Pby_Mgm_DWork.t_pby > TSTABPBY) {
              /* Transition: '<S9>:40' */
              if (CmeDriverP < CmeMaxPby) {
                /* Transition: '<S9>:33' */
                Pby_Mgm_DWork.t_pby = 0U;
                Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_wait_wot;

                /* Entry 'wait_wot': '<S9>:27' */
                Pby_Mgm_B.StPassBy_j = WAIT_WOT_PBY;
              } else {
                /* Transition: '<S9>:41' */
              }
            } else {
              /* Transition: '<S9>:20' */
              Pby_Mgm_DWork.t_pby++;
            }
            break;

           case Pby_Mgm_IN_wait_init:
            break;

           default:
            /* During 'wait_wot': '<S9>:27' */
            /* Transition: '<S9>:30' */
            Pby_Mgm_DWork.t_pby++;
            if (Pby_Mgm_DWork.t_pby > TSTABEXITPBY) {
              /* Transition: '<S9>:34' */
              Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_wait_init;

              /* Entry 'wait_init': '<S9>:16' */
              Pby_Mgm_B.StPassBy_j = WAIT_INIT_PBY;
              tmp = Pby_Mgm_B.CntCyclesPby_l + 1;
              if (tmp > 255) {
                tmp = 255;
              }

              Pby_Mgm_B.CntCyclesPby_l = (uint8_T)tmp;
            } else {
              /* Transition: '<S9>:31' */
              if (GasPosCC > THGASPBY) {
                /* Transition: '<S9>:36' */
                Pby_Mgm_DWork.t_pby = 0U;
                Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_NO_ACTIVE_CHILD;
                Pby_Mgm_DWork.bitsForTID0.is_Engine_running = Pby_Mgm_IN_apply;
                Pby_Mgm_DWork.bitsForTID0.is_apply = Pby_Mgm_IN_act;

                /* Entry 'act': '<S9>:25' */
                Pby_Mgm_B.StPassBy_j = ACT_PBY;
              } else {
                /* Transition: '<S9>:32' */
              }
            }
            break;
          }
        }
      }
      break;

     case Pby_Mgm_IN_disabled:
      break;

     default:
      /* Outputs for Function Call SubSystem: '<S4>/FlgStabPassBy_Calc' */

      /* During 'init': '<S9>:4' */
      /* Transition: '<S9>:24' */
      /* Event: '<S9>:65' */
      Pby_Mgm_FlgStabPassBy_Calc();

      /* End of Outputs for SubSystem: '<S4>/FlgStabPassBy_Calc' */
      if (Pby_Mgm_B.DataTypeConversion != 0) {
        /* Transition: '<S9>:26' */
        Pby_Mgm_DWork.t_pby = 0U;
        Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_Engine_running;
        Pby_Mgm_DWork.bitsForTID0.is_Engine_running = Pby_Mgm_IN_wc;
        Pby_Mgm_DWork.bitsForTID0.is_wc = Pby_Mgm_IN_count;

        /* Entry 'count': '<S9>:17' */
        Pby_Mgm_B.StPassBy_j = COUNT_PBY;
      } else {
        /* Transition: '<S9>:35' */
        Pby_Mgm_DWork.bitsForTID0.is_c7_Pby_Mgm = Pby_Mgm_IN_init;

        /* Entry 'init': '<S9>:4' */
        Pby_Mgm_B.StPassBy_j = INIT_PBY;
      }
      break;
    }
  }

  /* End of Chart: '<S4>/StPby_Calc' */

  /* DataStoreWrite: '<S4>/Data Store Write1' */
  CmeMaxPby = Pby_Mgm_B.Selector;

  /* DataStoreWrite: '<S4>/Data Store Write2' */
  CntCyclesPby = Pby_Mgm_B.CntCyclesPby_l;

  /* DataStoreWrite: '<S4>/Data Store Write3' */
  FlgStabPassBy = Pby_Mgm_B.DataTypeConversion;

  /* DataStoreWrite: '<S4>/Data Store Write7' */
  StPassBy = Pby_Mgm_B.StPassBy_j;

  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/Init' */
void Pby_Mgm_Init(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/ZERO2'
   */
  CmeMaxPby = 0;

  /* DataStoreWrite: '<S2>/Data Store Write11' incorporates:
   *  Constant: '<S2>/ZERO1'
   */
  CntCyclesPby = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Constant: '<S2>/ZERO3'
   */
  FlgStabPassBy = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  Constant: '<S2>/INIT_PBY'
   */
  StPassBy = INIT_PBY;

  /* user code (Output function Trailer) */

  /* System '<S1>/Init' */

  /* PILOTAGGIO USCITE - INIT */
  /* Read static variables */
  /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
  Pby_Mgm_initialize();
}

/* Output and update for function-call system: '<S1>/Off' */
void Pby_Mgm_Off(void)
{
  /* user code (Output function Trailer) */

  /* System '<S1>/Off' */
  /* PILOTAGGIO USCITE - OFF */
}

/* Model step function */
void Pby_Mgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/Pby_Mgm' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((Pby_Mgm_U.ev_PowerOn > 0) && (Pby_Mgm_PrevZCSigState.trig_to_fc1_Trig_ZCE
       != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    Pby_Mgm_Init();
  }

  Pby_Mgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = (uint8_T)((Pby_Mgm_U.ev_PowerOn >
    0) ? ((int32_T)POS_ZCSIG) : ((int32_T)ZERO_ZCSIG));

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((Pby_Mgm_U.ev_T10ms > 0) && (Pby_Mgm_PrevZCSigState.trig_to_fc_Trig_ZCE !=
       POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    Pby_Mgm_T10ms();
  }

  Pby_Mgm_PrevZCSigState.trig_to_fc_Trig_ZCE = (uint8_T)((Pby_Mgm_U.ev_T10ms > 0)
    ? ((int32_T)POS_ZCSIG) : ((int32_T)ZERO_ZCSIG));

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOff' */
  if ((Pby_Mgm_U.ev_PowerOff > 0) &&
      (Pby_Mgm_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Off'
     */
    Pby_Mgm_Off();
  }

  Pby_Mgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = (uint8_T)((Pby_Mgm_U.ev_PowerOff
    > 0) ? ((int32_T)POS_ZCSIG) : ((int32_T)ZERO_ZCSIG));

  /* End of Inport: '<Root>/ev_PowerOff' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* End of Outputs for SubSystem: '<Root>/Pby_Mgm' */
}

/* Model initialize function */
void Pby_Mgm_initialize(void)
{
  Pby_Mgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
  Pby_Mgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  Pby_Mgm_PrevZCSigState.trig_to_fc_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/Pby_Mgm' */
#endif                                 // _BUILD_PBYMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
