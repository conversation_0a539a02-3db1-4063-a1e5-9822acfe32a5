#ifdef _BUILD_ACTIVE_DIAG_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "activeDiag.h"


#pragma ghs section rodata=".calib"


/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/



__declspec(section ".calib") int16_t  TCLEANFUEL = 600; /*3s durata totale spurgo*/


__declspec(section ".calib") uint8_T ENDIAGCOIL0 = 0x1;
__declspec(section ".calib") uint8_T ENDIAGCOIL1 = 0x1;
__declspec(section ".calib") uint8_T ENDIAGCOIL2 = 0x1;
__declspec(section ".calib") uint8_T ENDIAGCOIL3 = 0x1;

__declspec(section ".calib") uint8_T ENDIAGINJ0LOW = 0x1;
__declspec(section ".calib") uint8_T ENDIAGINJ1LOW = 0x1;
__declspec(section ".calib") uint8_T ENDIAGINJ2LOW = 0x1;
__declspec(section ".calib") uint8_T ENDIAGINJ3LOW = 0x1;

__declspec(section ".calib") uint8_T ENDIAGINJ0HIGH = 0x1;
__declspec(section ".calib") uint8_T ENDIAGINJ1HIGH = 0x1;
__declspec(section ".calib") uint8_T ENDIAGINJ2HIGH = 0x1;
__declspec(section ".calib") uint8_T ENDIAGINJ3HIGH = 0x1;

__declspec(section ".calib") uint8_T ENDIAGDBWSELFLEARN = 0x1;
__declspec(section ".calib") uint8_T ENDIAGFUELPUMP = 0x1;

__declspec(section ".calib") uint8_T ENDIAGFANCOIL1 = 0x1;
__declspec(section ".calib") uint8_T ENDIAGFANCOIL2 = 0x1;

__declspec(section ".calib") uint8_T ENDIAGTSSVALVE = 0x01;
__declspec(section ".calib") int16_T TSSVALVEACTTIME = 300;
__declspec(section ".calib") uint16_T RPMMINTSSACTDIAG = 500;

__declspec(section ".calib") uint8_T ENDIAGHLAMBDA = 0x1;

__declspec(section ".calib") uint8_T ENDIAGSELFEXHVALV = 0x1;
__declspec(section ".calib") uint8_T ENDIAGEXHVALV = 0x1;

__declspec(section ".calib") uint8_T ENDIAGCLEANFUELLOW  = 0x1;
__declspec(section ".calib") uint8_T ENDIAGCLEANFUELHIGH = 0x1;
__declspec(section ".calib") uint8_T ENDIAGRESETPARAM = 0x1;
__declspec(section ".calib") uint8_T ENDIAGLOWBEAM = 0x1;
__declspec(section ".calib") uint8_T ENDIAGDRL = 0x1;
__declspec(section ".calib") uint8_T ENDIAGSTOPALL = 0x1;
__declspec(section ".calib") uint8_T ENDIAGDCMOTOR = 0x1;
__declspec(section ".calib") uint8_T ENDIAGSTARTERRELAY = 0x1;
__declspec(section.".calib") uint8_T ENDIAGRARPOSPLATE = 0x1;
__declspec(section ".calib") uint8_T ENDIAGERASESTRESSCOND = 0x1;
__declspec(section ".calib") uint8_T ENDIAGERASEHOURS = 0x1;
__declspec(section ".calib") uint8_T ENDIAGEXHVALVRES = 0x1;
__declspec(section ".calib") uint8_T ENDIAGERASELAMPONTIME = 0x1;
__declspec(section ".calib") uint8_T ENDIAGBAM = 0x1;
__declspec(section ".calib") uint8_T ENDIAGRSTVEHCONFIG = 0x1;

__declspec(section ".calib") uint8_T ENDIAGMIL = 0x1;


#endif /* _BUILD_ACTIVE_DIAG_ */
