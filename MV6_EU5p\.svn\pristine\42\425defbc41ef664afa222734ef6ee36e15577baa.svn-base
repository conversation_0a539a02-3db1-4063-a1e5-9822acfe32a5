/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _OSEK_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "tasksdefs.h"
#include "OS_errors.h"
#include "OS_alarms.h"
#include "OS_api.h"
#include "OS_tasks.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
CTRCBS   OsCounters[OSNUMCTRS]=
{ 
  {DeclareCounter(vhighRateTimer),0,{OSMAXALLOWEDVALUE_highRateTimer,OSTICKSPERBASE_highRateTimer}}    
};


const AlmCfg  OsAlmCfgTable[OSNUMALMS]=
{
    { Task5msID,   DeclareCounter(vhighRateTimer), (ptrFcn)ActivateTask},
    { Task10msID,  DeclareCounter(vhighRateTimer), (ptrFcn)ActivateTask},
    { Task100msID, DeclareCounter(vhighRateTimer), (ptrFcn)ActivateTask},
    { Task1msID,   DeclareCounter(vhighRateTimer), (ptrFcn)ActivateTask}  
};

const uint8_t  OsAlmCfgCycleTable[OSNUMALMS]=
{
    TIMING_HIGH_RATE  ,
    TIMING_MIDDLE_RATE ,
    TIMING_LOW_RATE,
    TIMING_VHIGH_RATE  ,
};


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * SetRelAlarm - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType  SetRelAlarm( AlarmType almId,
                         TickType increment, 
                         TickType cycle ) 
{
    StatusType retValue = E_OK;

    OSrtiSetServiceWatch(OSServiceId_SetRelAlarm);
    if (almId->almState == ALM_LOCKED)
    {
        OSRETERROR(E_OS_STATE,OSServiceId_SetRelAlarm,almId);
        retValue = E_OS_STATE;
    }
    else
    {
        almId->almState = ALM_LOCKED; 
        almId->delta = (OsCounters[almId->cntrId].value) + increment;
        /* rem: inserire i vari check sui parametri d'ingresso */
        almId->cycle = cycle;
        /*            */   
        OSrtiServiceWatchOnExit(OSServiceId_SetRelAlarm);
    }

    return retValue;
}

/*--------------------------------------------------------------------------*
 * SetAbsAlarm - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType  SetAbsAlarm( AlarmType almId, 
                         TickType start, 
                         TickType cycle ) 
{
    StatusType retValue = E_OK;

    OSrtiSetServiceWatch(OSServiceId_SetAbsAlarm);
    if (almId->almState == ALM_LOCKED)
    {
        OSRETERROR(E_OS_STATE,OSServiceId_SetAbsAlarm,almId);
        retValue = E_OS_STATE;
    }
    else
    {
        almId->almState = ALM_LOCKED; 
        almId->delta = start;
        /* rem: inserire i vari check sui parametri d'ingresso */
        almId->cycle = cycle;
        /*            */
        OSrtiServiceWatchOnExit(OSServiceId_SetAbsAlarm);
    }

    return retValue;
}

/*--------------------------------------------------------------------------*
 * CancelAlarm - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType  CancelAlarm( AlarmType almId )
{
    StatusType retValue = E_OK;

    OSrtiSetServiceWatch(OSServiceId_CancelAlarm);
    if (almId->almState == ALM_LOCKED)
    {
        almId->almState = ALM_FREE;
        almId->TaskId   = NULLTASK;
        almId->cntrId   = NULLCOUNTER;
        almId->delta        = 0;
        almId->cycle        = 0;
        almId->action   = NULL;
        OSrtiServiceWatchOnExit(OSServiceId_CancelAlarm);
    }
    else
    {
        OSRETERROR(E_OS_NOFUNC,OSServiceId_CancelAlarm,almId);
        retValue = E_OS_NOFUNC;
    }

    return retValue;
}


/*--------------------------------------------------------------------------*
 * SetRelAlarm - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType  GetAlarm( AlarmType almId,
                      TickRefType tick ) 
{
    StatusType retValue = E_OK;

    OSrtiSetServiceWatch(OSServiceId_GetAlarm);
    if (almId->almState != ALM_FREE)
    {
        *tick = ((almId->delta) - OsCounters[almId->cntrId].value);
        OSrtiServiceWatchOnExit(OSServiceId_GetAlarm);
    }
    else
    {
        OSRETERROR(E_OS_NOFUNC,OSServiceId_GetAlarm,almId);
        retValue = E_OS_NOFUNC;
    }

    return retValue;
}

/*--------------------------------------------------------------------------*
 * GetAlarmBase - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType  GetAlarmBase( AlarmType almId,
                          AlarmBaseRefType info )
{
    OSrtiSetServiceWatch(OSServiceId_GetAlarmBase);
    info->maxallowedvalue = OsCounters[almId->cntrId].info.maxallowedvalue;
    info->ticksperbase =    OsCounters[almId->cntrId].info.ticksperbase;
    OSrtiServiceWatchOnExit(OSServiceId_GetAlarmBase);
    return (E_OK);
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */


#endif /* _OSEK_*/
