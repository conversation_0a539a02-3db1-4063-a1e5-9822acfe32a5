#ifndef _VSRAMMGM_H_
#define _VSRAMMGM_H_

#include "typedefs.h"

extern uint32_t KWPsession;
extern uint32_t kwp_baud;


extern int8_t VsramState;
/*
** ===================================================================
**     Method      :  VSRAMMGM_Update(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/

int16_t VSRAMMGM_Update(void);

/*
** ===================================================================
**     Method      :  VSRAMMGM_Verify(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/

int16_t VSRAMMGM_Verify(void);

/*
** ===================================================================
**     Method      :  VSRAMMGM_Init(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/
int16_t VSRAMMGM_Init(uint32_t vsramInitWord);


/*
** ===================================================================
**     Method      :  VSRAMMGM_Init(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/
void VSRAMMGM_LoadFromSharedMemory(void);


/*
** ===================================================================
**     Method      :  VSRAMMGM_Init(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/
void VSRAMMGM_StoreToSharedMemory(void);


/*
** ===================================================================
**     Method      :  VSRAMMGM_LoadIvor2Data(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/
void VSRAMMGM_LoadIvor2Data(void);

/*
** ===================================================================
**     Method      :  VSRAMMGM_StoreIvor2Data(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/

void VSRAMMGM_StoreIvor2Data(void);


 
#endif	 /* _VSRAMMGM_H_ */
