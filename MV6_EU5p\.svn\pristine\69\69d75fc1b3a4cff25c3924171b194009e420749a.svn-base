/* Precompiler defines for PIO module */

/* ----------------------------------------------------------------- */
/*  PIO channels configuration                                       */
/* ----------------------------------------------------------------- */

/* eMIOS channels */
#define  EMIOS_UC0          0
#define  EMIOS_UC1          1
#define  EMIOS_UC2          2
#define  EMIOS_UC3          3
#define  EMIOS_UC4          4
#define  EMIOS_UC5          5
#define  EMIOS_UC6          6
#define  EMIOS_UC7          7
#define  EMIOS_UC8          8
#define  EMIOS_UC9          9
#define  EMIOS_UC10         10
#define  EMIOS_UC11         11
#define  EMIOS_UC12         12
#define  EMIOS_UC13         13
#define  EMIOS_UC14         14
#define  EMIOS_UC15         15
#define  EMIOS_UC16         16
#define  EMIOS_UC17         17
#define  EMIOS_UC18         18
#define  EMIOS_UC19         19
#define  EMIOS_UC20         20
#define  EMIOS_UC21         21
#define  EMIOS_UC22         22
#define  EMIOS_UC23         23

/* eTPUA channels */
#define  ETPUA_UC0         30
#define  ETPUA_UC1         (ETPUA_UC0+1)
#define  ETPUA_UC2          (ETPUA_UC0+2)
#define  ETPUA_UC3          (ETPUA_UC0+3)
#define  ETPUA_UC4          (ETPUA_UC0+4)
#define  ETPUA_UC5          (ETPUA_UC0+5)
#define  ETPUA_UC6          (ETPUA_UC0+6)
#define  ETPUA_UC7          (ETPUA_UC0+7)
#define  ETPUA_UC8          (ETPUA_UC0+8)
#define  ETPUA_UC9          (ETPUA_UC0+9)
#define  ETPUA_UC10         (ETPUA_UC0+10)
#define  ETPUA_UC11         (ETPUA_UC0+11)
#define  ETPUA_UC12         (ETPUA_UC0+12)
#define  ETPUA_UC13         (ETPUA_UC0+13)
#define  ETPUA_UC14         (ETPUA_UC0+14)
#define  ETPUA_UC15            (ETPUA_UC0+15)
#define  ETPUA_UC16         (ETPUA_UC0+16)
#define  ETPUA_UC17         (ETPUA_UC0+17)
#define  ETPUA_UC18         (ETPUA_UC0+18)
#define  ETPUA_UC19         (ETPUA_UC0+19)
#define  ETPUA_UC20         (ETPUA_UC0+20)
#define  ETPUA_UC21         (ETPUA_UC0+21)
#define  ETPUA_UC22         (ETPUA_UC0+22)
#define  ETPUA_UC23         (ETPUA_UC0+23)
#define  ETPUA_UC24         (ETPUA_UC0+24)
#define  ETPUA_UC25         (ETPUA_UC0+25)
#define  ETPUA_UC26         (ETPUA_UC0+26)
#define  ETPUA_UC27         (ETPUA_UC0+27)
#define  ETPUA_UC28         (ETPUA_UC0+28)
#define  ETPUA_UC29         (ETPUA_UC0+29)
#define  ETPUA_UC30         (ETPUA_UC0+30)
#define  ETPUA_UC31         (ETPUA_UC0+31)

/* eTPUB channels */
#define  ETPUB_UC0      80
#define  ETPUB_UC1         (ETPUB_UC0+1)
#define  ETPUB_UC2          (ETPUB_UC0+2)
#define  ETPUB_UC3          (ETPUB_UC0+3)
#define  ETPUB_UC4          (ETPUB_UC0+4)
#define  ETPUB_UC5          (ETPUB_UC0+5)
#define  ETPUB_UC6          (ETPUB_UC0+6)
#define  ETPUB_UC7          (ETPUB_UC0+7)
#define  ETPUB_UC8          (ETPUB_UC0+8)
#define  ETPUB_UC9          (ETPUB_UC0+9)
#define  ETPUB_UC10         (ETPUB_UC0+10)
#define  ETPUB_UC11         (ETPUB_UC0+11)
#define  ETPUB_UC12         (ETPUB_UC0+12)
#define  ETPUB_UC13         (ETPUB_UC0+13)
#define  ETPUB_UC14         (ETPUB_UC0+14)
#define  ETPUB_UC15         (ETPUB_UC0+15)
#define  ETPUB_UC16         (ETPUB_UC0+16)
#define  ETPUB_UC17         (ETPUB_UC0+17)
#define  ETPUB_UC18         (ETPUB_UC0+18)
#define  ETPUB_UC19         (ETPUB_UC0+19)
#define  ETPUB_UC20         (ETPUB_UC0+20)
#define  ETPUB_UC21         (ETPUB_UC0+21)
#define  ETPUB_UC22         (ETPUB_UC0+22)
#define  ETPUB_UC23         (ETPUB_UC0+23)
#define  ETPUB_UC24         (ETPUB_UC0+24)
#define  ETPUB_UC25         (ETPUB_UC0+25)
#define  ETPUB_UC26         (ETPUB_UC0+26)
#define  ETPUB_UC27         (ETPUB_UC0+27)
#define  ETPUB_UC28         (ETPUB_UC0+28)
#define  ETPUB_UC29         (ETPUB_UC0+29)
#define  ETPUB_UC30         (ETPUB_UC0+30)
#define  ETPUB_UC31         (ETPUB_UC0+31)


/****************************************************************************
     Peripheral errors defines 
 ****************************************************************************/
#define CHANNEL_NOT_CONFIGURED            -5
#define INTERRUPT_HANDLER_NOT_CONFIGURED  -6
#define PERIPHERAL_ALREADY_ENABLED        -7
#define PERIPHERAL_ALREADY_DISABLED       -8
#define PIN_NOT_ENABLED                   -9
#define POUT_NOT_ENABLED                  -10
#define PWM_NOT_ENABLED                   -11
#define PWM_MATCHMODE_NOT_SUPPORTERD      -12
#define PIN_OVERFLOW                      -13
#define PIO_CHANNEL_NOT_SUPPORTED         -14
#define MODE_NOT_AVAILABLE                -15

/*****************************************/
/* eMIOS registers Configuration Defines */
/*****************************************/

/***************************/
/* MCR eMIOS Configuration */
/***************************/

/* Puts the eMIOS in low power mode */
#define MDIS_CLK_RUN               (0x00000000UL)
#define MDIS_LOW_PWR_MOD           (0x40000000UL)

/* Enables the eMIOS to freeze the register of the UC */
#define FRZ_DISABLE                (0x00000000UL)
#define FRZ_ENABLE                 (0x20000000UL)

/* Used to export a Global Time Base Enabled from the module and control internal counter */
#define GTBE_DISABLE               (0x00000000UL)
#define GTBE_ENABLE                (0x10000000UL)

/* Select the time base source that drives counter bus[A] */
#define ETB_UC23_DRV_A             (0x00000000UL)
#define ETB_STAC_DRV_A             (0x08000000UL)

/* Enabled the prescaler counter */
#define GPREN_DISABLE              (0x00000000UL)
#define GPREN_ENABLE               (0x04000000UL)

/* Selects the address of a specific STAC server to which the STAC Client Submodule is assigned */
#define SRV_TPU_A_TCR1             (0x00000000UL)
#define SRV_TPU_B_TCR1             (0x00010000UL)
#define SRV_TPU_A_TCR2             (0x00020000UL)
#define SRV_TPU_B_TCR2             (0x00030000UL)

/************************/
/* CCR UC Configuration */
/************************/

/* If FREN enabled, freeze register value when in debug mode */
#define FREN_DISABLE               (0x00000000UL)
#define FREN_ENABLE                (0x80000000UL)

/* Allows disabling the output pin when running any of the output modes with the exception of GPIO mode */
#define ODIS_DISABLE               (0x00000000UL)
#define ODIS_ENABLE                (0x40000000UL)

/* Selects one of the four output disable input signals */
#define ODISSL_0                   (0x00000000UL)
#define ODISSL_1                   (0x10000000UL)
#define ODISSL_2                   (0x20000000UL)
#define ODISSL_3                   (0x30000000UL)

/* If UCPREN enabled, UCPRE set the clock divider value */
#define UCPRE_1                    (0x00000000UL)
#define UCPRE_2                    (0x04000000UL)
#define UCPRE_3                    (0x08000000UL)
#define UCPRE_4                    (0x0C000000UL)

/* If UCPREN enabled, enable the prescaler counter */
#define UCPREN_DISABLE             (0x00000000UL)
#define UCPREN_ENABLE              (0x02000000UL)

/* Selects if the FLAG generation will be used as an interruot or as a DMA request */               
#define DMA_IRQ                    (0x00000000UL)
#define DMA_DMA                    (0x01000000UL)

/* Controls the programmable input filter, selecting the minimum input pulse width that can pass through the filter */
#define IF_BYPASSED                (0x00000000UL)
#define IF_02                      (0x00080000UL)
#define IF_04                      (0x00100000UL)
#define IF_08                      (0x00200000UL)
#define IF_16                      (0x00400000UL)

/* Selects the clock source for the programmable input filter */
#define FCK_PRESCALER_CLOCK        (0x00000000UL)
#define FCK_MAIN_CLOCK             (0x00040000UL)

/* Allows the UC FLAG bit to generate an interrupt or a DMA request signal */
#define FEN_DISABLE                (0x00000000UL)
#define FEN_ENABLE                 (0x00020000UL)

/* For output modes, the FORCMA bit is equivalent to a successful
   comparison on comparator A (except that the FLAG bit is not set) */
#define FORCMA_NO_MATCH            (0x00000000UL)
#define FORCMA_MATCH               (0x00002000UL)

/* For output modes, the FORCMB bit is equivalent to a successful
   comparison on comparator B (except that the FLAG bit is not set) */
#define FORCMB_NO_MATCH            (0x00000000UL)
#define FORCMB_MATCH               (0x00001000UL)

/* Used to selec the counter bus or the internal counter to be used by UC */
#define BSL_A                      (0x00000000UL)
#define BSL_B_C_D                  (0x00000200UL)
#define BSL_INT_CNT                (0x00000600UL)

/* For input modes, the EDSEL bit selects whether the internal counter is
   triggered by both edges of a pulse or just by a single edge as defined by the EDPOL bit.
   When not shown in the mode of operation description, this bit has no effect.
   0 Single edge triggering defined by the EDPOL bit
   1 Both edges triggering
   
   For GPIO input mode, the EDSEL bit selects if a FLAG can be generated.
   0 A FLAG is generated as defined by the EDPOL bit
   1 No FLAG is generated
   
   For SAOC mode, the EDSEL bit selects the behavior of the output flip-flop at each match.
   0 The EDPOL value is transferred to the output flip-flop
   1 The output flip-flop is toggled */
#define EDSEL_DISABLE              (0x00000000UL)
#define EDSEL_ENABLE               (0x00000100UL)

/* For input modes (except QDEC and WPTA mode), the EDPOL bit asserts
   which edge triggers either the internal counter or an input capture or a FLAG. When not
   shown in the mode of operation description, this bit has no affect.
   0 Trigger on a falling edge
   1 Trigger on a rising edge

   For WPTA mode, the internal counter is used as a time accumulator and counts up when
   the input gating signal has the same polarity of EDPOL bit.
   0 Counting occurs when the input gating signal is low
   1 Counting occurs when the input gating signal is high

   For QDEC (MODE[6] cleared), the EDPOL bit selects the count direction according to
   direction signal (UCn input).
   0 Counts down when UCn is asserted
   1 Counts up when UCn is asserted

   NOTE: UC[n-1] EDPOL bit selects which edge clocks the internal counter of UCn
   0 Trigger on a falling edge
   1 Trigger on a rising edge

   For QDEC (MODE[6] set), the EDPOL bit selects the count direction according to the phase
   difference.
   0 Internal counter decrements if phase_A is ahead phase_B signal
   1 Internal counter increments if phase_A is ahead phase_B signal
   NOTE: In order to operate properly, EDPOL bit must contain the same value in UCn and
   UC[n-1]

   For output modes, the EDPOL bit is used to select the logic level on the output pin.
   0 A match on comparator A clears the output flip-flop, while a match on comparator B sets it
   1 A match on comparator A sets the output flip-flop, while a match on comparator B clears it */
#define EDPOL_DISABLE              (0x00000000UL)
#define EDPOL_ENABLE               (0x00000080UL)

/* Selects the mode of operation of the Input UC */
#define MODE_GPIO_IN               (0x00000000UL)
#define MODE_SAIC                  (0x00000002UL)
#define MODE_IPWM                  (0x00000004UL)
#define MODE_IPM                   (0x00000005UL)
#define MODE_PEA_C                 (0x00000008UL)
#define MODE_PEA_S                 (0x00000009UL)
#define MODE_PEC_C                 (0x0000000AUL)
#define MODE_PEC_S                 (0x0000000BUL)
#define MODE_QDEC_CNT_DIR          (0x0000000CUL)
#define MODE_QDEC_PHA_PHB          (0x0000000DUL)
#define MODE_WPTA                  (0x0000000EUL)
#define MODE_MC_UP_INT_CLK         (0x00000010UL)
#define MODE_MC_UP_EXT_CLK         (0x00000011UL)
#define MODE_MC_UP_DW_INT_CLK      (0x00000014UL)
#define MODE_MC_UP_DW_EXT_CLK      (0x00000015UL)
#define MODE_MC_UP_DW_INT_CLK_FLG  (0x00000016UL)
#define MODE_MC_UP_DW_EXT_CLK_FLG  (0x00000017UL)

/* Selects the mode of operation of the Output UC */
#define MODE_GPIO_OUT                     (0x00000001UL)
#define MODE_SAOC                         (0x00000003UL)
#define MODE_DAOC_SNG_MACTH               (0x00000006UL)
#define MODE_DAOC_BTH_MATCH               (0x00000007UL)
#define MODE_OPWFM_INT_CNT_CMP_B_IMM_UP   (0x00000018UL)
#define MODE_OPWFM_INT_CNT_CMP_B_NXT_UP   (0x00000019UL)
#define MODE_OPWFM_INT_CNT_CMP_AB_IMM_UP  (0x0000001AUL)
#define MODE_OPWFM_INT_CNT_CMP_AB_NXT_UP  (0x0000001BUL)
#define MODE_OPWMC_TRL_EDGE_TRL_DT        (0x0000001CUL)
#define MODE_OPWMC_TRL_EDGE_LED_DT        (0x0000001DUL)
#define MODE_OPWMC_BOTH_EDGE_TRL_DT       (0x0000001EUL)
#define MODE_OPWMC_BOTH_EDGE_LED_DT       (0x0000001FUL)
#define MODE_OPWM_INT_CNT_CMP_B_IMM_UP    (0x00000020UL)
#define MODE_OPWM_INT_CNT_CMP_B_NXT_UP    (0x00000021UL)
#define MODE_OPWM_INT_CNT_CMP_AB_IMM_UP   (0x00000022UL)
#define MODE_OPWM_INT_CNT_CMP_AB_NXT_UP   (0x00000023UL)

#define MODE_OPWFMB_INT_CNT_CMP_B_IMM_UP  (0x00000058UL)
#define MODE_OPWMB_BUS_CNT_CMP_AB_IMM_UP  (0x00000062UL)
#define MODE_MCB_UP_INT_CLK               (0x00000050UL)
#define MODE_MCB_UP_EXT_CLK               (0x00000051UL) 


/**********************************************/
/* CSR (Channel Satus Register) Configuration */
/**********************************************/
/* Overrun. Indicates that FLAG generation occurred when the FLAG bit was already set. */
#define EMIOS_CSR_OVR_MASK           (0x80000000UL)

/* Overflow. Indicates that an overflow has occurred in the internal counter. */
#define EMIOS_CSR_OVFL_MASK          (0x00008000UL)

/* Unified channel input pin. Reflects the input pin state after being filtered and synchronized. */
#define EMIOS_CSR_UCIN_MASK          (0x00000004UL)

/* Unified channel output pin. The UCOUT bit reflects the output pin state. */
#define EMIOS_CSR_UCOUT_MASK         (0x00000002UL)

/* FLAG. Set when an input capture or a match event in the comparators occurred. */
#define EMIOS_CSR_FLAG_MASK          (0x00000001UL)


/****************************************/
/* eMIOS registers Configuration Values */
/****************************************/

/* Select the time base source that drives counter bus[A] */
#define EXTERNAL_TIME_BASE         ETB_UC23_DRV_A
/* Selects the address of a specific STAC server to which the STAC Client Submodule is assigned */
#define SERVER_TIME_SLOT           SRV_TPU_B_TCR2
