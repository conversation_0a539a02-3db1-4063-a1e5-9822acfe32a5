/*
 * File: cmefilter_mgm.h
 *
 * Code generated for Simulink model 'CmeFilterMgm'.
 *
 * Model version                  : 1.2319
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Feb 10 08:50:24 2023
 */

#ifndef RTW_HEADER_cmefilter_mgm_h_
#define RTW_HEADER_cmefilter_mgm_h_
#include "rtwtypes.h"

#define CME_I_CL                       ((uint8_T) 2U)            /* Closed_Loop */
#define CME_I_INIT                     ((uint8_T) 0U)            /* Init */
#define CME_I_OL                       ((uint8_T) 1U)            /* Open_Loop */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmeDriverCIPostF;

/* CmiDriverI post filtered */
extern int16_T CmeDriverCLOff;

/* CmeDriverCLOff */
extern int16_T CmeDriverI;

/* CME fast */
extern int16_T CmeDriverICLOffOut;

/* CmeDriverICLOffOut */
extern int16_T CmeDriverP;

/* CME slow */
extern int16_T CmeDriverPMaxRSat;

/* CmeDriverPMaxRSat */
extern int16_T CmeDriverPOffMaxRSat;

/* CmeDriverPMaxRSat */
extern int16_T CmeDriverPRate;

/* CmeDriverP post rate */
extern int16_T CmeDriverPTmpFilt;

/* CmiDriverPTmp filtered (before rate limiter) */
extern int16_T CmeIBuffCLOff[17];

/* CmeDriverI shifted */
extern int32_T CmeRateMax;

/* CmiDriverP max variation in filtering phase */
extern int32_T CmeRateMin;

/* CmiDriverP min variation in filtering phase */
extern uint16_T CntCDICLOff;

/* CntCDICLOff */
extern uint8_T FlgCmeDrPMaxRSat;

/* FlgCmeDrPMaxRSat */
extern uint8_T FlgCmeDriverP;

/* FlgCmeDriverP */
extern uint8_T FlgCmeDriverPHiRpm;

/* FlgCmeDriverPHiRpm */
extern uint8_T FlgKfCmeDGResp;

/* Kf Filt CmeDriver Gas resp enable */
extern uint8_T FlgPhGain;

/* FlgPhGain */
extern uint8_T FlgResCmeDriverI;

/* FlgResCmeDriverI */
extern uint16_T GRGainLim;

/* Gain */
extern uint32_T IDCmeFilterMgm;

/* ID Version */
extern uint16_T KFiltCmeDriverI;

/* CmiDriverP filter constant in filtering phase */
extern uint16_T KFiltCmeDriverP;

/* CmiDriverP filter constant in filtering phase */
extern uint8_T StCmeICL;

/* StCmeICL */
extern uint8_T StCmeITrCL;

/* StCmeITrCL */
extern uint8_T TrLimGCTrig;

/* Trigger to reset Limiter to filter */
#endif                                 /* RTW_HEADER_cmefilter_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
