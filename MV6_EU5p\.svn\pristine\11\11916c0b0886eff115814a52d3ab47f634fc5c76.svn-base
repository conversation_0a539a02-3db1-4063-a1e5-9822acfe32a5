/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "typedefs.h"
#include "digio.h"
#include "adc.h"
#include "hbridge.h"
#include "pio.h"
#include "analogin.h"
#include "spi.h"
#include "OS_resources.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#include "sys.h"
#include "utils.h"

#ifdef  MATLAB_MEX_FILE
#define _BUILD_HBRIDGE_
#endif

#ifdef _BUILD_HBRIDGE_
/* tuning variables */
extern uint16_T HB1PERIOD;
extern uint16_T HB2PERIOD;
extern uint8_T  USEA2RSIGNAL;

uint8_T     HBridgeMode_A;
uint8_T     HBridgeMode_B;
int16_T     HBVOut_A;
int16_T     HBVOut_B;
uint16_T    DbwOutDuty1;
uint16_T    DbwOutDuty2;
uint16_T    HB_B_Duty1, HB_B_Duty2;
//uint8_t     Hbridge_status = 0; /* VARIABILE INTERNA PER VERIFICARE LA CORRETTA INIZIALIZZAZIONE DEL DBW */

int16_T Hbridge_LogicEnable(uint8_t ena)
/* set the logical enable state of the H-BRIDGE to a value
    'TRUE' == ENABLE
    'FALSE == DISABLE
*/    
{
    FlgEnHBridge = ena;
    return NO_ERROR;
}

int16_T Hbridge_GetLogicEnable(uint8_t *pEnaState)
/* get the logical enable state of the H-BRIDGE:
    'TRUE' == ENABLE
    'FALSE == DISABLE
*/    
{
    *pEnaState = FlgEnHBridge;
    return NO_ERROR;
}

/*******************************************************************/
int16_t Hbridge_SetState (uint8_t hbridge_Id, uint8_t state)
{
  int16_t returnCode=NO_ERROR;
  
  switch (hbridge_Id)
  {
#if defined(Bridge_A) && defined(ODE_DBW1_ENABLE)  
    case Bridge_A: 
        DIGIO_OutSet(ODE_DBW1_ENABLE, ((state)?(HB_ENABLE):(HB_DISABLE)));
        break; 
#endif
#if defined(Bridge_B) && defined(ODE_DBW2_ENABLE)  
    case Bridge_B: 
        DIGIO_OutSet(ODE_DBW2_ENABLE, ((state)?(HB_ENABLE):(HB_DISABLE)));
        break;
#endif
    default:
        returnCode = HB_ERROR;
        break;
  }
  
  return returnCode;
}
/************************************************************************/

/*******************************************************************/
int16_t Hbridge_Init (uint8_t hbridge_Id,uint8_t hbridge_Mode, uint16_T duty1, uint16_T duty2, uint16_T period) 
{
    int16_t returnCode=NO_ERROR;
#if defined(Bridge_A_FB) || defined(Bridge_B_FB) 
    int16_T adcerr = NO_ERROR;
#endif

    switch (hbridge_Id)  
    {
#ifdef Bridge_A  
        case  Bridge_A: 
            HBridgeMode_A = hbridge_Mode;
            Hbridge_Diag_Init();
            DIGIO_OutCfgExt(ODE_DBW1_ENABLE, HB_DISABLE, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
#ifdef Bridge_A_FS                     
            DIGIO_InCfgExt(Bridge_A_FS, DISABLE_PULL );   
#endif
            switch(hbridge_Mode) 
            {    
                case  HB_DIGIO_MODE:      
                    DIGIO_OutCfgExt(Bridge_A_IN1, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    DIGIO_OutCfgExt(Bridge_A_IN2, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    break;
                case  HB_DIGIO_PWM_MODE:  
                    DIGIO_OutCfgExt(Bridge_A_IN1, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    ETPU_OutCfgExt(Bridge_A_IN2, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    /* Configuro pin Bridge_A_IN1 come DIGIO */
                    DIGIO_OutCfgExt(Bridge_A_IN1, (duty1 == MAX_DUTY), OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    /* Configuro pin Bridge_A_IN2 come PWM */
                    PIO_PwmOutConfig(Bridge_A_IN2_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty2, period, 0);
                    PIO_PwmOutEnable(Bridge_A_IN2_PWM);
                    break;
                case  HB_PWM_DIGIO_MODE:  
                    ETPU_OutCfgExt(Bridge_A_IN1, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    DIGIO_OutCfgExt(Bridge_A_IN2, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    /* Configuro pin Bridge_A_IN1 come PWM */
                    PIO_PwmOutConfig(Bridge_A_IN1_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty1, period, 0);
                    PIO_PwmOutEnable(Bridge_A_IN1_PWM);
                    /* Configuro pin Bridge_A_IN2 come DIGIO */
                    DIGIO_OutCfgExt(Bridge_A_IN2, (duty2 == MAX_DUTY), OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    break;
                case  HB_PWM_MODE:
                    ETPU_OutCfgExt(Bridge_A_IN1, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    ETPU_OutCfgExt(Bridge_A_IN2, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    /* Configuro pin Bridge_A_IN1 come PWM */
                    PIO_PwmOutConfig(Bridge_A_IN1_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty1, period, 0);
                    PIO_PwmOutEnable(Bridge_A_IN1_PWM);    
                    /* Configuro pin Bridge_A_IN2 come PWM */
                    PIO_PwmOutConfig(Bridge_A_IN2_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty2, period, 0);
                    PIO_PwmOutEnable(Bridge_A_IN2_PWM);    
                    break;
                default:    
                    returnCode = HB_MODE_ERROR;
                    break;
            }
            if (returnCode == NO_ERROR)
            {
#ifdef Bridge_A_FB
                adcerr = ADC_Init(Bridge_A_FB, NULL, NULL, 0, SOFTWARE_TRIGGERED);
                if (adcerr < 0)
                {
                    SETBIT(EE_BiosErr,ADC_IDX);
                    debugADC_Error_2 = 51;
                    debugADC_ErrorCnt_2++;
                    returnCode = HB_ERROR;
                }
#endif
            }
            break;  
#endif // Bridge_A
#ifdef Bridge_B  
        case  Bridge_B: 
            HBridgeMode_B = hbridge_Mode;
            DIGIO_OutCfgExt(ODE_DBW2_ENABLE, HB_DISABLE, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
#ifdef Bridge_B_FS                                           
            DIGIO_InCfgExt(Bridge_B_FS, DISABLE_PULL );   
#endif
            switch(hbridge_Mode) 
            {    
                case  HB_DIGIO_MODE:
                    DIGIO_OutCfgExt(Bridge_B_IN1, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    DIGIO_OutCfgExt(Bridge_B_IN2, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    break;
                case  HB_DIGIO_PWM_MODE:
                    DIGIO_OutCfgExt(Bridge_B_IN1, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    ETPU_OutCfgExt(Bridge_B_IN2, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    /* Configuro pin Bridge_A_IN1 come DIGIO */
                    DIGIO_OutCfgExt(Bridge_B_IN1, (duty1 == MAX_DUTY), OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    /* Configuro pin Bridge_A_IN2 come PWM */
                    PIO_PwmOutConfig(Bridge_B_IN2_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty2, period, 0);
                    PIO_PwmOutEnable(Bridge_B_IN2_PWM);
                    break;
                case  HB_PWM_DIGIO_MODE: 
                    ETPU_OutCfgExt(Bridge_B_IN1, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    DIGIO_OutCfgExt(Bridge_B_IN2, 0, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    /* Configuro pin Bridge_A_IN1 come PWM */
                    PIO_PwmOutConfig(Bridge_B_IN1_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty1, period, 0);
                    PIO_PwmOutEnable(Bridge_B_IN1_PWM);
                    /* Configuro pin Bridge_A_IN2 come DIGIO */
                    DIGIO_OutCfgExt(Bridge_B_IN2, (duty2 == MAX_DUTY), OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
                    break;
                case  HB_PWM_MODE:
                    ETPU_OutCfgExt(Bridge_B_IN1, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    ETPU_OutCfgExt(Bridge_B_IN2, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
                    /* Configuro pin Bridge_B_IN1 come PWM */
                    PIO_PwmOutConfig(Bridge_B_IN1_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty1, period, 0);
                    PIO_PwmOutEnable(Bridge_B_IN1_PWM);
                    /* Configuro pin Bridge_B_IN2 come PWM */
                    PIO_PwmOutConfig(Bridge_B_IN2_PWM, ACTIVE_HIGH, PWM_MATCH_TIME, duty2, period, 0);
                    PIO_PwmOutEnable(Bridge_B_IN2_PWM);
                    break;
                default:    
                    returnCode = HB_MODE_ERROR;
                    break;
            }
            if (returnCode == NO_ERROR)
            {
#ifdef Bridge_B_FB
                adcerr = ADC_Init(Bridge_B_FB, NULL, NULL, 0, SOFTWARE_TRIGGERED);
                if (adcerr < 0)
                {
                    SETBIT(EE_BiosErr,ADC_IDX);
                    debugADC_Error_2 = 52;
                    debugADC_ErrorCnt_2++;
                    returnCode = HB_ERROR;
                }
#endif
            }    
            break;
#endif // Bridge_B
        default:    
            returnCode = HB_ERROR;
            break;
    }
#if defined(Bridge_A_FB) || defined(Bridge_B_FB)   
    adcerr = ADC_Enable(SOFTWARE_TRIGGERED);
    if (adcerr < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 53;
        debugADC_ErrorCnt_2++;
        returnCode = HB_ERROR;
    }
#endif
    return returnCode;
}

/************************************************************************/

/***********************************************************************/
int16_t Hbridge_SetDutyCicle(uint16_t hbridge_Input, vuint16_t duty)
{
  int16_t returnCode=NO_ERROR;
  returnCode=PIO_PwmOutSetDutyCicle(hbridge_Input, duty);
  return returnCode;
}
/************************************************************************/

/************************************************************************/
int16_t Hbridge_PwmDisable(uint16_t hbridge_Input)
{
  int16_t returnCode=NO_ERROR;
  returnCode=PIO_PwmOutDisable(hbridge_Input);
  return returnCode;
}
/************************************************************************/

/***************************************************************************/
int16_t Hbridge_SetOut (uint8_t hbridge_Id, uint8_t  hbridgeOut)
{
    int16_t returnCode=NO_ERROR;
    uint8_t hbridgeCmd[HB_IN];
    uint8_t i;
    
    if(hbridgeOut>(HB_OUT -1))
    {
        returnCode=HB_STATUS_ERROR ;
    }
    if (returnCode == NO_ERROR)
    {
        for(i=0;i<HB_IN;i++)
        {
            hbridgeCmd[i] = HBRIDGE_STATE_TABLE[hbridgeOut][i];
        }
        switch (hbridge_Id)  
        {
#ifdef Bridge_A 
            case  Bridge_A: 
                Hbridge_OutCmdSet(Bridge_A_IN1, hbridgeCmd[0]);
                Hbridge_OutCmdSet(Bridge_A_IN2, hbridgeCmd[1]);    
                break;  
#endif // Bridge_A 
#ifdef Bridge_B 
            case  Bridge_B: 
                Hbridge_OutCmdSet(Bridge_B_IN1, hbridgeCmd[0]);
                Hbridge_OutCmdSet(Bridge_B_IN2, hbridgeCmd[1]);  
                break;
#endif // Bridge_B 
            default:    
                returnCode=HB_ERROR;
                break;
        }
    }    
    return returnCode;
}

/***************************************************************************/
int16_t Hbridge_GetOut(uint8_t hbridge_Id, uint8_t *hbridgeOut) 
{
  int16_t returnCode = NO_ERROR;
  uint8_T tmpHbridgeOut = 0;
  uint8_T cmd1;
  uint8_T cmd2;
  
  switch (hbridge_Id)  
  {
#if defined(Bridge_A) 
    case Bridge_A: 
        switch(HBridgeMode_A) 
        {    
            case  HB_DIGIO_MODE:      
                DIGIO_OutGet(Bridge_A_IN1, &cmd1);   
                DIGIO_OutGet(Bridge_A_IN2, &cmd2);      
                tmpHbridgeOut = ((cmd1 + cmd2) == 1);
                break;
            case  HB_DIGIO_PWM_MODE:  
                tmpHbridgeOut = (HBVOut_A != 0); 
                break;
            case  HB_PWM_DIGIO_MODE:  
                tmpHbridgeOut = (HBVOut_A != 0); 
                break;
            case  HB_PWM_MODE:        
                tmpHbridgeOut = (HBVOut_A != 0); 
                break;
            default:    
                returnCode = HB_MODE_ERROR;
                break;
        }
        break; 
#endif
#if defined(Bridge_B)    
    case Bridge_B: 
        switch(HBridgeMode_B) 
        {    
            case  HB_DIGIO_MODE:      
                DIGIO_OutGet(Bridge_B_IN1, &cmd1);   
                DIGIO_OutGet(Bridge_B_IN2, &cmd2);      
                tmpHbridgeOut = ((cmd1 + cmd2) == 1);
                break;
            case  HB_DIGIO_PWM_MODE:  
                tmpHbridgeOut = (HBVOut_B != 0); 
                break;
            case  HB_PWM_DIGIO_MODE:  
                tmpHbridgeOut = (HBVOut_B != 0); 
                break;
            case  HB_PWM_MODE:        
                tmpHbridgeOut = (HBVOut_B != 0); 
                break;
            default:    
                returnCode = HB_MODE_ERROR;
                break;
        }
        break;
#endif
    default:
        returnCode = HB_ERROR; 
        break;
  }
    
  *hbridgeOut = tmpHbridgeOut;
  return(returnCode);
}   /* end Hbridge_GetOut(.) */

int16_t Hbridge_GetOutThr(uint8_t hbridge_Id, uint8_t *hbridgeOut, int16_t hbThrVout) 
{
    int16_t returnCode = NO_ERROR;
    uint8_T tmpHbridgeOut = 0;
    uint8_T cmd1;
    uint8_T cmd2;

    switch (hbridge_Id)  
    {
#if defined(Bridge_A)    
        case Bridge_A: 
            switch(HBridgeMode_A) 
            {    
                case  HB_DIGIO_MODE:      
                    DIGIO_OutGet(Bridge_A_IN1, &cmd1);   
                    DIGIO_OutGet(Bridge_A_IN2, &cmd2);      
                    tmpHbridgeOut = ((cmd1 + cmd2) == 1);
                    break;
                case  HB_DIGIO_PWM_MODE:  
                    tmpHbridgeOut = (HBVOut_A < -hbThrVout) || (HBVOut_A > hbThrVout); 
                    break;
                case  HB_PWM_DIGIO_MODE:  
                    tmpHbridgeOut = (HBVOut_A < -hbThrVout) || (HBVOut_A > hbThrVout); 
                    break;
                case  HB_PWM_MODE:        
                    tmpHbridgeOut = (HBVOut_A < -hbThrVout) || (HBVOut_A > hbThrVout); 
                    break;
                default:    
                    returnCode = HB_MODE_ERROR;                
                    break;
            }
            break; 
#endif
        default:
            returnCode = HB_ERROR;                
            break;
    }
    *hbridgeOut = tmpHbridgeOut;
    return(returnCode);
}   /* end Hbridge_GetOutThr(.) */

/***************************************************************************/
int16_t Hbridge_SetVOut(uint8_t hbridge_Id, int16_t  hbridgeVOut) 
{
  int16_t returnCode = NO_ERROR;

  switch (hbridge_Id)
  {
#if defined(Bridge_A)
    case  Bridge_A: 
      HBVOut_A = hbridgeVOut;
    break; 
#endif // Bridge_A 
#if defined(Bridge_B)
    case  Bridge_B: 
      HBVOut_B = hbridgeVOut;
    break;  
#endif // Bridge_B          
    default: 
      returnCode = HB_ERROR;
      break;
  }
  return(returnCode);
}

/***************************************************************************/
#if defined(Bridge_A_FS) || defined(Bridge_B_FS)  
int16_t Hbridge_FSGetStatus (uint8_t hbridge_Id, uint8_t * hbridgeFSStatus){
  
  int16_t returnCode=NO_ERROR;
  
  switch (hbridge_Id)
  {
#if defined(Bridge_A) && defined(Bridge_A_FS)
    case  Bridge_A: 
        DIGIO_InGet(Bridge_A_FS, hbridgeFSStatus);
        break; 
#endif 
#if defined(Bridge_B) && defined(Bridge_B_FS)  
    case  Bridge_B: 
        DIGIO_InGet(Bridge_B_FS, hbridgeFSStatus);
        break;  
#endif 
    default:    
        *hbridgeFSStatus = 0;
        returnCode = HB_ERROR;
        break;
  }
  return returnCode; 
}
#endif
/**************************************************************************/

/*******************************************************************/
int16_t Hbridge_OutCmdSet (uint8_t pin, uint8_t lvl){

  int16_t returnCode=NO_ERROR;
  
  returnCode=DIGIO_OutSet(pin, lvl);
  
  return returnCode;
}
/*******************************************************************/

#endif /* _BUILD_HBRIDGE_ */

