/*
 * File: AnalogQS.c
 *
 * Code generated for Simulink model 'AnalogQS'.
 *
 * Model version                  : 1.655
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Nov 16 11:29:39 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "AnalogQS.h"
#include "AnalogQS_private.h"

/* Named constants for Chart: '<S16>/Chart' */
#define AnalogQS_IN_CUTOFF             ((uint8_T)1U)
#define AnalogQS_IN_PRE_TRQ            ((uint8_T)2U)
#define AnalogQS_IN_RETURN             ((uint8_T)3U)
#define AnalogQS_IN_SLEEP              ((uint8_T)4U)

/* Named constants for Chart: '<S7>/QS_Manager' */
#define AnalogQS_IN_ST_QS_CHANGE_DOWN  ((uint8_T)1U)
#define AnalogQS_IN_ST_QS_CHANGE_UP    ((uint8_T)2U)
#define AnalogQS_IN_ST_QS_FAULT_GND    ((uint8_T)1U)
#define AnalogQS_IN_ST_QS_FAULT_PLAUS  ((uint8_T)2U)
#define AnalogQS_IN_ST_QS_FAULT_VCC    ((uint8_T)3U)
#define AnalogQS_IN_ST_QS_FIND_DOWN    ((uint8_T)3U)
#define AnalogQS_IN_ST_QS_FIND_UP      ((uint8_T)4U)
#define AnalogQS_IN_ST_QS_IDLE         ((uint8_T)5U)
#define AnalogQS_IN_ST_QS_NORMAL       ((uint8_T)4U)

/* user code (top of source file) */
/* System '<Root>/AnalogQS' */
#ifdef _BUILD_ANALOGQS_

/* Block signals (default storage) */
BlockIO_AnalogQS AnalogQS_B;

/* Block states (default storage) */
D_Work_AnalogQS AnalogQS_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_AnalogQS AnalogQS_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_AnalogQS AnalogQS_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint32_T CntVQSFault;

/* Time fault QS */
uint32_T CntVQSFind;

/* Time find QS Up or Down */
uint8_T GearDownSignal;

/* Down shift flag */
uint8_T GearNSignal;

/* Neutral shift flag */
uint8_T GearShiftWait;

/* QS Status CAN */
uint8_T GearUpSignal;

/* Up shift flag */
uint32_T IDAnalogQs;

/* ID Version */
uint8_T RecQS;

/* Recovery QS */
uint8_T RelAQSCmd;

/* Find exit gear change threshold */
uint8_T StVQuickShift;

/* QS Status */
int16_T Thvqshigh;

/* Up threshold */
int16_T Thvqshighrel;

/* Up threshold */
int16_T Thvqslow;

/* Down threshold */
int16_T Thvqslowrel;

/* Down threshold */
uint8_T VQSPtFault;

/* diag fault */

/* Output and update for function-call system: '<S13>/DiagQS' */
void AnalogQS_DiagQS(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;

  /* S-Function (DiagMgm_SetDiagState): '<S17>/DiagMgm_SetDiagState' */
  DiagMgm_SetDiagState( AnalogQS_B.diagId, AnalogQS_B.ptFault,
                       &rtb_DiagMgm_SetDiagState);
}

/* Output and update for function-call system: '<S7>/DiagQS' */
void AnalogQS_DiagQS_o(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState_m;
  uint8_T rtb_DataStoreRead7;

  /* DataStoreRead: '<S9>/Data Store Read7' */
  rtb_DataStoreRead7 = VQSPtFault;

  /* S-Function (DiagMgm_SetDiagState): '<S12>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S9>/DIAG_QSHIFT_ANALOG'
   */
  DiagMgm_SetDiagState( DIAG_QSHIFT_ANALOG, rtb_DataStoreRead7,
                       &rtb_DiagMgm_SetDiagState_m);
}

/* Output and update for function-call system: '<S1>/T5ms' */
void AnalogQS_T5ms(void)
{
  int32_T TimAQSMatch;
  uint8_T rtb_BitwiseOperator;
  uint8_T rtb_BitwiseOperator1;
  uint8_T rtb_BitwiseOperator2;

  /* If: '<S3>/If' incorporates:
   *  Inport: '<Root>/EECANNode4En'
   */
  if (EECANNode4En != 0) {
    /* Outputs for IfAction SubSystem: '<S3>/Digital_Chart' incorporates:
     *  ActionPort: '<S8>/if'
     */
    /* Chart: '<S16>/Chart' incorporates:
     *  Constant: '<S16>/FOGSFLAGS'
     *  Inport: '<Root>/GearShiftWaitCAN'
     *  Memory: '<S16>/Memory3'
     *  RelationalOperator: '<S16>/Relational Operator3'
     */
    /* Gateway: AnalogQS/T5ms/Digital_Chart/Subsystem/FO_Input/Chart */
    /* During: AnalogQS/T5ms/Digital_Chart/Subsystem/FO_Input/Chart */
    if (AnalogQS_DWork.bitsForTID0.is_active_c4_AnalogQS == 0U) {
      /* Entry: AnalogQS/T5ms/Digital_Chart/Subsystem/FO_Input/Chart */
      AnalogQS_DWork.bitsForTID0.is_active_c4_AnalogQS = 1U;

      /* Entry Internal: AnalogQS/T5ms/Digital_Chart/Subsystem/FO_Input/Chart */
      /* Transition: '<S18>:2' */
      AnalogQS_B.out = GearShiftWaitCAN;
      AnalogQS_DWork.bitsForTID0.is_c4_AnalogQS = AnalogQS_IN_SLEEP;
    } else {
      switch (AnalogQS_DWork.bitsForTID0.is_c4_AnalogQS) {
       case AnalogQS_IN_CUTOFF:
        /* During 'CUTOFF': '<S18>:3' */
        /* Transition: '<S18>:9' */
        if (AnalogQS_DWork.cnt >= 8) {
          /* Transition: '<S18>:16' */
          AnalogQS_DWork.cnt = 0U;
          AnalogQS_B.out = ((uint8_T)GEAR_SHIFT_WAIT);
          AnalogQS_DWork.bitsForTID0.is_c4_AnalogQS = AnalogQS_IN_PRE_TRQ;
        } else {
          /* Transition: '<S18>:10' */
          TimAQSMatch = AnalogQS_DWork.cnt + 1;
          if (TimAQSMatch > 255) {
            TimAQSMatch = 255;
          }

          AnalogQS_DWork.cnt = (uint8_T)TimAQSMatch;
        }
        break;

       case AnalogQS_IN_PRE_TRQ:
        /* During 'PRE_TRQ': '<S18>:12' */
        /* Transition: '<S18>:15' */
        if (AnalogQS_DWork.cnt >= 6) {
          /* Transition: '<S18>:21' */
          AnalogQS_DWork.cnt = 0U;
          AnalogQS_B.out = ((uint8_T)GEAR_SHIFT_RETURN);
          AnalogQS_DWork.bitsForTID0.is_c4_AnalogQS = AnalogQS_IN_RETURN;
        } else {
          /* Transition: '<S18>:13' */
          TimAQSMatch = AnalogQS_DWork.cnt + 1;
          if (TimAQSMatch > 255) {
            TimAQSMatch = 255;
          }

          AnalogQS_DWork.cnt = (uint8_T)TimAQSMatch;
        }
        break;

       case AnalogQS_IN_RETURN:
        /* During 'RETURN': '<S18>:17' */
        /* Transition: '<S18>:20' */
        if (AnalogQS_DWork.cnt >= 10) {
          /* Transition: '<S18>:19' */
          AnalogQS_DWork.cnt = 0U;
          AnalogQS_B.out = ((uint8_T)GEAR_SHIFT_IDLE);

          /* Transition: '<S18>:24' */
          AnalogQS_DWork.bitsForTID0.is_c4_AnalogQS = AnalogQS_IN_SLEEP;
        } else {
          /* Transition: '<S18>:22' */
        }
        break;

       default:
        /* During 'SLEEP': '<S18>:1' */
        /* Transition: '<S18>:5' */
        AnalogQS_B.out = GearShiftWaitCAN;
        if (AnalogQS_DWork.Memory3_PreviousInput != FOGSFLAGS) {
          /* Transition: '<S18>:6' */
          AnalogQS_DWork.cnt = 0U;
          AnalogQS_B.out = ((uint8_T)GEAR_SHIFT_MOVE);
          AnalogQS_DWork.bitsForTID0.is_c4_AnalogQS = AnalogQS_IN_CUTOFF;
        } else {
          /* Transition: '<S18>:7' */
        }
        break;
      }
    }

    /* End of Chart: '<S16>/Chart' */

    /* S-Function (sfix_bitop): '<S16>/Bitwise Operator' incorporates:
     *  Constant: '<S16>/FOGSFLAGS'
     */
    rtb_BitwiseOperator = (uint8_T)(FOGSFLAGS & 1);

    /* S-Function (sfix_bitop): '<S16>/Bitwise Operator1' incorporates:
     *  Constant: '<S16>/FOGSFLAGS'
     */
    rtb_BitwiseOperator1 = (uint8_T)(FOGSFLAGS & 2);

    /* S-Function (sfix_bitop): '<S16>/Bitwise Operator2' incorporates:
     *  Constant: '<S16>/FOGSFLAGS'
     */
    rtb_BitwiseOperator2 = (uint8_T)(FOGSFLAGS & 4);

    /* Chart: '<S13>/Chart' incorporates:
     *  Inport: '<Root>/DownShiftReqCAN'
     *  Inport: '<Root>/NeutralReqCAN'
     *  Inport: '<Root>/UpShiftReqCAN'
     *  Logic: '<S16>/Logical Operator1'
     *  Logic: '<S16>/Logical Operator2'
     *  Logic: '<S16>/Logical Operator3'
     *  Memory: '<S16>/Memory'
     *  Memory: '<S16>/Memory1'
     *  Memory: '<S16>/Memory2'
     *  RelationalOperator: '<S16>/Relational Operator'
     *  RelationalOperator: '<S16>/Relational Operator1'
     *  RelationalOperator: '<S16>/Relational Operator2'
     */
    /* Gateway: AnalogQS/T5ms/Digital_Chart/Subsystem/Chart */
    /* During: AnalogQS/T5ms/Digital_Chart/Subsystem/Chart */
    if (AnalogQS_DWork.bitsForTID0.is_active_c3_AnalogQS == 0U) {
      /* Entry: AnalogQS/T5ms/Digital_Chart/Subsystem/Chart */
      AnalogQS_DWork.bitsForTID0.is_active_c3_AnalogQS = 1U;

      /* Entry Internal: AnalogQS/T5ms/Digital_Chart/Subsystem/Chart */
    } else {
      /* During 'GEAR_SHIFT_WAIT': '<S14>:36' */
      /* Transition: '<S14>:196' */
      /* Transition: '<S14>:167' */
      AnalogQS_B.GearShiftWait_g = AnalogQS_B.out;
      AnalogQS_B.GearDownSignal_k = (uint8_T)
        ((AnalogQS_DWork.Memory1_PreviousInput != rtb_BitwiseOperator1) ||
         (DownShiftReqCAN != 0));
      AnalogQS_B.GearUpSignal_j = (uint8_T)
        ((AnalogQS_DWork.Memory2_PreviousInput != rtb_BitwiseOperator2) ||
         (UpShiftReqCAN != 0));
      AnalogQS_B.GearNSignal_c = (uint8_T)((AnalogQS_DWork.Memory_PreviousInput
        != rtb_BitwiseOperator) || (NeutralReqCAN != 0));

      /* During 'DIAG': '<S14>:37' */
      /* Transition: '<S14>:189' */
      if (AnalogQS_B.GearShiftWait_g == ((uint8_T)GEAR_SHIFT_ERROR)) {
        /* Transition: '<S14>:188' */
        AnalogQS_B.ptFault = SIG_NOT_PLAUSIBLE;
        RecQS = ((uint8_T)QS_SLIGHT_TRQ_LIM);

        /* Transition: '<S14>:193' */
        /* Transition: '<S14>:194' */
      } else {
        /* Transition: '<S14>:185' */
        if (AnalogQS_B.GearShiftWait_g >= ((uint8_T)GEAR_SHIFT_BLOCKED)) {
          /* Transition: '<S14>:186' */
          AnalogQS_B.ptFault = CIRCUIT_MALFUNCTION;
          RecQS = ((uint8_T)QS_FORCE_IDLE);

          /* Transition: '<S14>:194' */
        } else {
          /* Transition: '<S14>:190' */
          AnalogQS_B.ptFault = NO_PT_FAULT;
        }
      }

      /* Transition: '<S14>:40' */
      AnalogQS_B.diagId = DIAG_QSHIFT_ANALOG;

      /* Outputs for Function Call SubSystem: '<S13>/DiagQS' */
      /* Event: '<S14>:154' */
      AnalogQS_DiagQS();

      /* End of Outputs for SubSystem: '<S13>/DiagQS' */
    }

    /* End of Chart: '<S13>/Chart' */

    /* DataStoreWrite: '<S3>/GearDownSignal' incorporates:
     *  SignalConversion generated from: '<S8>/GearDownSignal'
     */
    GearDownSignal = AnalogQS_B.GearDownSignal_k;

    /* DataStoreWrite: '<S3>/GearUpSignal1' incorporates:
     *  SignalConversion generated from: '<S8>/GearNSignal'
     */
    GearNSignal = AnalogQS_B.GearNSignal_c;

    /* DataStoreWrite: '<S3>/GearDownSignal1' incorporates:
     *  SignalConversion generated from: '<S8>/GearShiftWait'
     */
    GearShiftWait = AnalogQS_B.GearShiftWait_g;

    /* DataStoreWrite: '<S3>/GearUpSignal' incorporates:
     *  SignalConversion generated from: '<S8>/GearUpSignal'
     */
    GearUpSignal = AnalogQS_B.GearUpSignal_j;

    /* Update for Memory: '<S16>/Memory3' incorporates:
     *  Constant: '<S16>/FOGSFLAGS'
     */
    AnalogQS_DWork.Memory3_PreviousInput = FOGSFLAGS;

    /* Update for Memory: '<S16>/Memory' */
    AnalogQS_DWork.Memory_PreviousInput = rtb_BitwiseOperator;

    /* Update for Memory: '<S16>/Memory1' */
    AnalogQS_DWork.Memory1_PreviousInput = rtb_BitwiseOperator1;

    /* Update for Memory: '<S16>/Memory2' */
    AnalogQS_DWork.Memory2_PreviousInput = rtb_BitwiseOperator2;

    /* End of Outputs for SubSystem: '<S3>/Digital_Chart' */
  } else {
    /* Outputs for IfAction SubSystem: '<S3>/Analog_Chart' incorporates:
     *  ActionPort: '<S7>/else'
     */
    /* Chart: '<S7>/QS_Manager' incorporates:
     *  Constant: '<S11>/THVQSFAULTHIGH'
     *  Constant: '<S11>/THVQSFAULTLOW'
     *  Constant: '<S11>/THVQSHIGH'
     *  Constant: '<S11>/THVQSHIGHREL'
     *  Constant: '<S11>/THVQSLOW'
     *  Constant: '<S11>/THVQSLOWREL'
     *  Inport: '<Root>/FlgEnQs'
     *  Inport: '<Root>/RpmLim'
     *  Inport: '<Root>/VGearShift'
     */
    /* Gateway: AnalogQS/T5ms/Analog_Chart/QS_Manager */
    /* During: AnalogQS/T5ms/Analog_Chart/QS_Manager */
    if (AnalogQS_DWork.bitsForTID0.is_active_c1_AnalogQS == 0U) {
      /* Entry: AnalogQS/T5ms/Analog_Chart/QS_Manager */
      AnalogQS_DWork.bitsForTID0.is_active_c1_AnalogQS = 1U;

      /* Entry Internal: AnalogQS/T5ms/Analog_Chart/QS_Manager */
      /* Entry Internal 'ST_QS_FIND': '<S10>:172' */
      /* Transition: '<S10>:202' */
      AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_IDLE;

      /* Entry 'ST_QS_IDLE': '<S10>:1' */
      AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_IDLE);
      CntVQSFind = 0U;
      AnalogQS_B.gearUpSignal = 0U;
      AnalogQS_B.gearDownSignal = 0U;
      VQSPtFault = NO_PT_FAULT;

      /* Entry Internal 'ST_QS_DIAG': '<S10>:218' */
      /* Transition: '<S10>:219' */
      CntVQSFault = 0U;
      AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG = AnalogQS_IN_ST_QS_NORMAL;
    } else {
      /* During 'ST_QS_FIND': '<S10>:172' */
      switch (AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND) {
       case AnalogQS_IN_ST_QS_CHANGE_DOWN:
        /* During 'ST_QS_CHANGE_DOWN': '<S10>:38' */
        /* Transition: '<S10>:327' */
        if (CntVQSFind >= TIMVQSBLANKDN) {
          /* Transition: '<S10>:244' */
          if (RpmLim == 0) {
            /* Transition: '<S10>:332' */
            TimAQSMatch = TIMAQSMATCH;
          } else {
            /* Transition: '<S10>:331' */
            TimAQSMatch = TIMAQSMATCH1;
          }

          if ((THVQSLOWREL < VGearShift) && (VGearShift < THVQSHIGHREL)) {
            /* Transition: '<S10>:315' */
            AnalogQS_DWork.cntThMatch++;
          } else {
            /* Transition: '<S10>:247' */
            AnalogQS_DWork.cntThMatch = 0U;
          }

          if (AnalogQS_DWork.cntThMatch >= (uint32_T)TimAQSMatch) {
            /* Transition: '<S10>:318' */
            AnalogQS_B.relAQSCmd = 1U;
          } else {
            /* Transition: '<S10>:317' */
          }

          if ((AnalogQS_B.relAQSCmd != 0) || (FlgEnQs == 0)) {
            /* Transition: '<S10>:43' */
            /* Transition: '<S10>:289' */
            /* Transition: '<S10>:293' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_IDLE;

            /* Entry 'ST_QS_IDLE': '<S10>:1' */
            AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_IDLE);
            CntVQSFind = 0U;
            AnalogQS_B.gearUpSignal = 0U;
            AnalogQS_B.gearDownSignal = 0U;
            VQSPtFault = NO_PT_FAULT;
          } else {
            /* Transition: '<S10>:42' */
          }
        } else {
          /* Transition: '<S10>:328' */
          CntVQSFind = CntVQSFind + ((uint8_T)TIM_QS_STEP_INC);
        }
        break;

       case AnalogQS_IN_ST_QS_CHANGE_UP:
        /* During 'ST_QS_CHANGE_UP': '<S10>:18' */
        /* Transition: '<S10>:325' */
        if (CntVQSFind >= TIMVQSBLANKUP) {
          /* Transition: '<S10>:234' */
          if (RpmLim == 0) {
            /* Transition: '<S10>:335' */
            TimAQSMatch = TIMAQSMATCH;
          } else {
            /* Transition: '<S10>:334' */
            TimAQSMatch = TIMAQSMATCH1;
          }

          if ((THVQSLOWREL < VGearShift) && (VGearShift < THVQSHIGHREL)) {
            /* Transition: '<S10>:311' */
            AnalogQS_DWork.cntThMatch++;
          } else {
            /* Transition: '<S10>:238' */
            AnalogQS_DWork.cntThMatch = 0U;
          }

          if (AnalogQS_DWork.cntThMatch >= (uint32_T)TimAQSMatch) {
            /* Transition: '<S10>:312' */
            AnalogQS_B.relAQSCmd = 1U;
          } else {
            /* Transition: '<S10>:313' */
          }

          if ((AnalogQS_B.relAQSCmd != 0) || (FlgEnQs == 0)) {
            /* Transition: '<S10>:37' */
            /* Transition: '<S10>:361' */
            /* Transition: '<S10>:289' */
            /* Transition: '<S10>:293' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_IDLE;

            /* Entry 'ST_QS_IDLE': '<S10>:1' */
            AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_IDLE);
            CntVQSFind = 0U;
            AnalogQS_B.gearUpSignal = 0U;
            AnalogQS_B.gearDownSignal = 0U;
            VQSPtFault = NO_PT_FAULT;
          } else {
            /* Transition: '<S10>:32' */
          }
        } else {
          /* Transition: '<S10>:324' */
          CntVQSFind = CntVQSFind + ((uint8_T)TIM_QS_STEP_INC);
        }
        break;

       case AnalogQS_IN_ST_QS_FIND_DOWN:
        /* During 'ST_QS_FIND_DOWN': '<S10>:46' */
        /* Transition: '<S10>:48' */
        if (CntVQSFind >= TIMVQSFINDDOWN) {
          /* Transition: '<S10>:49' */
          AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND =
            AnalogQS_IN_ST_QS_CHANGE_DOWN;

          /* Entry 'ST_QS_CHANGE_DOWN': '<S10>:38' */
          AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_CHANGE_DOWN);
          AnalogQS_B.gearDownSignal = 1U;
          CntVQSFind = 0U;
          AnalogQS_DWork.cntThMatch = 0U;
          AnalogQS_B.relAQSCmd = 0U;
        } else {
          /* Transition: '<S10>:51' */
          if (((THVQSLOW < VGearShift) && (VGearShift < THVQSHIGH)) || (FlgEnQs ==
               0)) {
            /* Transition: '<S10>:57' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_IDLE;

            /* Entry 'ST_QS_IDLE': '<S10>:1' */
            AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_IDLE);
            CntVQSFind = 0U;
            AnalogQS_B.gearUpSignal = 0U;
            AnalogQS_B.gearDownSignal = 0U;
            VQSPtFault = NO_PT_FAULT;
          } else {
            /* Transition: '<S10>:53' */
            CntVQSFind = CntVQSFind + ((uint8_T)TIM_QS_STEP_INC);
          }
        }
        break;

       case AnalogQS_IN_ST_QS_FIND_UP:
        /* During 'ST_QS_FIND_UP': '<S10>:7' */
        /* Transition: '<S10>:17' */
        if (CntVQSFind >= TIMVQSFINDUP) {
          /* Transition: '<S10>:20' */
          AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_CHANGE_UP;

          /* Entry 'ST_QS_CHANGE_UP': '<S10>:18' */
          AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_CHANGE_UP);
          AnalogQS_B.gearUpSignal = 1U;
          CntVQSFind = 0U;
          AnalogQS_DWork.cntThMatch = 0U;
          AnalogQS_B.relAQSCmd = 0U;
        } else {
          /* Transition: '<S10>:343' */
          if (((THVQSLOW < VGearShift) && (VGearShift < THVQSHIGH)) || (FlgEnQs ==
               0)) {
            /* Transition: '<S10>:273' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_IDLE;

            /* Entry 'ST_QS_IDLE': '<S10>:1' */
            AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_IDLE);
            CntVQSFind = 0U;
            AnalogQS_B.gearUpSignal = 0U;
            AnalogQS_B.gearDownSignal = 0U;
            VQSPtFault = NO_PT_FAULT;
          } else {
            /* Transition: '<S10>:344' */
            CntVQSFind = CntVQSFind + ((uint8_T)TIM_QS_STEP_INC);
          }
        }
        break;

       default:
        /* During 'ST_QS_IDLE': '<S10>:1' */
        /* Transition: '<S10>:342' */
        if (((VQSUPLOW != 0) && ((VGearShift < THVQSLOW) && (FlgEnQs != 0))) ||
            ((VQSUPLOW == 0) && ((VGearShift > THVQSHIGH) && (FlgEnQs != 0)))) {
          /* Transition: '<S10>:6' */
          /* Transition: '<S10>:255' */
          /* Transition: '<S10>:259' */
          /* Transition: '<S10>:260' */
          AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND = AnalogQS_IN_ST_QS_FIND_UP;

          /* Entry 'ST_QS_FIND_UP': '<S10>:7' */
          AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_FIND_UP);
        } else {
          /* Transition: '<S10>:10' */
          /* Transition: '<S10>:261' */
          /* Transition: '<S10>:262' */
          /* Transition: '<S10>:263' */
          if (((VQSUPLOW != 0) && ((VGearShift > THVQSHIGH) && (FlgEnQs != 0))) ||
              ((VQSUPLOW == 0) && ((VGearShift < THVQSLOW) && (FlgEnQs != 0))))
          {
            /* Transition: '<S10>:295' */
            /* Transition: '<S10>:12' */
            /* Transition: '<S10>:300' */
            /* Transition: '<S10>:307' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_FIND =
              AnalogQS_IN_ST_QS_FIND_DOWN;

            /* Entry 'ST_QS_FIND_DOWN': '<S10>:46' */
            AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_FIND_DOWN);
          } else {
            /* Transition: '<S10>:297' */
            /* Transition: '<S10>:298' */
            /* Transition: '<S10>:302' */
            /* Transition: '<S10>:303' */
            /* Transition: '<S10>:306' */
          }
        }
        break;
      }

      /* During 'ST_QS_DIAG': '<S10>:218' */
      switch (AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG) {
       case AnalogQS_IN_ST_QS_FAULT_GND:
        /* During 'ST_QS_FAULT_GND': '<S10>:351' */
        /* Transition: '<S10>:363' */
        AnalogQS_B.gearUpSignal = 0U;
        AnalogQS_B.gearDownSignal = 0U;
        if ((VGearShift >= THVQSFAULTLOW) || (FlgEnQs == 0)) {
          /* Transition: '<S10>:365' */
          AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG = AnalogQS_IN_ST_QS_NORMAL;
        } else {
          /* Transition: '<S10>:364' */
          VQSPtFault = CC_TO_GND;

          /* Outputs for Function Call SubSystem: '<S7>/DiagQS' */
          /* Event: '<S10>:118' */
          AnalogQS_DiagQS_o();

          /* End of Outputs for SubSystem: '<S7>/DiagQS' */
        }
        break;

       case AnalogQS_IN_ST_QS_FAULT_PLAUS:
        /* During 'ST_QS_FAULT_PLAUS': '<S10>:62' */
        /* Transition: '<S10>:80' */
        AnalogQS_B.gearUpSignal = 0U;
        AnalogQS_B.gearDownSignal = 0U;
        if (((THVQSLOWREL < VGearShift) && (VGearShift < THVQSHIGHREL)) ||
            (FlgEnQs == 0)) {
          /* Transition: '<S10>:86' */
          CntVQSFault = CntVQSFault + ((uint8_T)TIM_QS_STEP_INC);
          if (CntVQSFault >= TIMVQSVALIDF) {
            /* Transition: '<S10>:175' */
            CntVQSFault = 0U;

            /* Transition: '<S10>:350' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG = AnalogQS_IN_ST_QS_NORMAL;
          } else {
            /* Transition: '<S10>:348' */
          }
        } else {
          /* Transition: '<S10>:89' */
          CntVQSFault = 0U;
          VQSPtFault = SIG_NOT_PLAUSIBLE;

          /* Outputs for Function Call SubSystem: '<S7>/DiagQS' */
          /* Event: '<S10>:118' */
          AnalogQS_DiagQS_o();

          /* End of Outputs for SubSystem: '<S7>/DiagQS' */
        }
        break;

       case AnalogQS_IN_ST_QS_FAULT_VCC:
        /* During 'ST_QS_FAULT_VCC': '<S10>:359' */
        /* Transition: '<S10>:368' */
        AnalogQS_B.gearUpSignal = 0U;
        AnalogQS_B.gearDownSignal = 0U;
        if ((VGearShift <= THVQSFAULTHIGH) || (FlgEnQs == 0)) {
          /* Transition: '<S10>:367' */
          AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG = AnalogQS_IN_ST_QS_NORMAL;
        } else {
          /* Transition: '<S10>:369' */
          VQSPtFault = CC_TO_VCC;

          /* Outputs for Function Call SubSystem: '<S7>/DiagQS' */
          /* Event: '<S10>:118' */
          AnalogQS_DiagQS_o();

          /* End of Outputs for SubSystem: '<S7>/DiagQS' */
        }
        break;

       default:
        /* During 'ST_QS_NORMAL': '<S10>:211' */
        /* Transition: '<S10>:346' */
        if (((THVQSLOWREL < VGearShift) && (VGearShift < THVQSHIGHREL)) ||
            (FlgEnQs == 0)) {
          /* Transition: '<S10>:181' */
          CntVQSFault = 0U;
          VQSPtFault = NO_PT_FAULT;

          /* Outputs for Function Call SubSystem: '<S7>/DiagQS' */
          /* Event: '<S10>:118' */
          AnalogQS_DiagQS_o();

          /* End of Outputs for SubSystem: '<S7>/DiagQS' */
        } else {
          /* Transition: '<S10>:182' */
          if (VGearShift < THVQSFAULTLOW) {
            /* Transition: '<S10>:354' */
            AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG =
              AnalogQS_IN_ST_QS_FAULT_GND;

            /* Entry 'ST_QS_FAULT_GND': '<S10>:351' */
            AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_FAULT);
          } else {
            /* Transition: '<S10>:353' */
            if (VGearShift > THVQSFAULTHIGH) {
              /* Transition: '<S10>:358' */
              AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG =
                AnalogQS_IN_ST_QS_FAULT_VCC;

              /* Entry 'ST_QS_FAULT_VCC': '<S10>:359' */
              AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_FAULT);
            } else {
              /* Transition: '<S10>:357' */
              if (CntVQSFault > TIMVQSFAULT) {
                /* Transition: '<S10>:174' */
                CntVQSFault = 0U;
                AnalogQS_DWork.bitsForTID0.is_ST_QS_DIAG =
                  AnalogQS_IN_ST_QS_FAULT_PLAUS;

                /* Entry 'ST_QS_FAULT_PLAUS': '<S10>:62' */
                AnalogQS_B.stVQuickShift = ((uint8_T)ST_QS_FAULT);
              } else {
                /* Transition: '<S10>:345' */
                CntVQSFault = CntVQSFault + 1U;
              }
            }
          }
        }
        break;
      }
    }

    /* End of Chart: '<S7>/QS_Manager' */

    /* DataStoreWrite: '<S7>/RelAQSCmd' */
    RelAQSCmd = AnalogQS_B.relAQSCmd;

    /* DataStoreWrite: '<S7>/StVQuickShift' */
    StVQuickShift = AnalogQS_B.stVQuickShift;

    /* DataStoreWrite: '<S7>/Thvqshigh' incorporates:
     *  Constant: '<S11>/THVQSHIGH'
     */
    Thvqshigh = THVQSHIGH;

    /* DataStoreWrite: '<S7>/Thvqshighrel' incorporates:
     *  Constant: '<S11>/THVQSHIGHREL'
     */
    Thvqshighrel = THVQSHIGHREL;

    /* DataStoreWrite: '<S7>/Thvqslow' incorporates:
     *  Constant: '<S11>/THVQSLOW'
     */
    Thvqslow = THVQSLOW;

    /* DataStoreWrite: '<S7>/Thvqslowrel' incorporates:
     *  Constant: '<S11>/THVQSLOWREL'
     */
    Thvqslowrel = THVQSLOWREL;

    /* DataStoreWrite: '<S3>/GearDownSignal' incorporates:
     *  SignalConversion generated from: '<S7>/GearDownSignal'
     */
    GearDownSignal = AnalogQS_B.gearDownSignal;

    /* DataStoreWrite: '<S3>/GearUpSignal' incorporates:
     *  SignalConversion generated from: '<S7>/GearUpSignal'
     */
    GearUpSignal = AnalogQS_B.gearUpSignal;

    /* DataStoreWrite: '<S3>/GearUpSignal1' incorporates:
     *  Constant: '<S7>/Constant'
     *  SignalConversion generated from: '<S7>/GearNSignal'
     */
    GearNSignal = 0U;

    /* DataStoreWrite: '<S3>/GearDownSignal1' incorporates:
     *  Constant: '<S7>/Constant1'
     *  SignalConversion generated from: '<S7>/GearShiftWait'
     */
    GearShiftWait = ((uint8_T)GEAR_SHIFT_IDLE);

    /* End of Outputs for SubSystem: '<S3>/Analog_Chart' */
  }

  /* End of If: '<S3>/If' */
}

/* Output and update for function-call system: '<S1>/Reset' */
void AnalogQS_Reset(void)
{
  /* Constant: '<S2>/ID_ANALOG_QS' */
  IDAnalogQs = ID_ANALOG_QS;

  /* Chart: '<S2>/fc_Reset' */
  /* Gateway: AnalogQS/Reset/fc_Reset */
  /* During: AnalogQS/Reset/fc_Reset */
  /* Entry Internal: AnalogQS/Reset/fc_Reset */
  /* Transition: '<S6>:1' */
  GearUpSignal = 0U;
  GearDownSignal = 0U;
  GearNSignal = 0U;
  GearShiftWait = ((uint8_T)GEAR_SHIFT_IDLE);
  RecQS = 0U;

  /* Transition: '<S6>:32' */
  /*  Exit */
}

/* Model step function */
void AnalogQS_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/AnalogQS' */

  /* Outputs for Triggered SubSystem: '<S1>/Trig_2_fc1' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((AnalogQS_U.ev_PowerOn > 0) &&
      (AnalogQS_PrevZCSigState.Trig_2_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Reset'
     */
    AnalogQS_Reset();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  AnalogQS_PrevZCSigState.Trig_2_fc1_Trig_ZCE = (ZCSigState)
    (AnalogQS_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/Trig_2_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/Trig_2_fc' incorporates:
   *  TriggerPort: '<S4>/Trigger'
   */
  /* Inport: '<Root>/ev_5ms' */
  if ((AnalogQS_U.ev_5ms > 0) && (AnalogQS_PrevZCSigState.Trig_2_fc_Trig_ZCE !=
       POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T5ms'
     */
    AnalogQS_T5ms();

    /* End of Outputs for S-Function (fcncallgen): '<S4>/Function-Call Generator' */
  }

  AnalogQS_PrevZCSigState.Trig_2_fc_Trig_ZCE = (ZCSigState)(AnalogQS_U.ev_5ms >
    0);

  /* End of Inport: '<Root>/ev_5ms' */
  /* End of Outputs for SubSystem: '<S1>/Trig_2_fc' */

  /* End of Outputs for SubSystem: '<Root>/AnalogQS' */
}

/* Model initialize function */
void AnalogQS_initialize(void)
{
  AnalogQS_PrevZCSigState.Trig_2_fc_Trig_ZCE = POS_ZCSIG;
  AnalogQS_PrevZCSigState.Trig_2_fc1_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/AnalogQS' */
void AnalogQS_Init(void)
{
  AnalogQS_initialize();               /* Init statechart */
  AnalogQS_Reset();
}

#else
#include "digitalin.h"

uint8_T GearUpSignal;
uint8_T GearDOwnSignal;
void AnalogQS_stub(void);
void AnalogQS_Init(void)
{
  AnalogQS_stub();
}

void AnalogQS_T5ms(void)
{
  AnalogQS_stub();
}

void AnalogQS_stub(void)
{
  GearUpSignal = 0;
  GearDownSignal = 0;
}

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
