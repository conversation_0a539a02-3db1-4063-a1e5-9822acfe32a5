/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_FOINJCTFMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//enable [flag]
CALQUAL uint8_T ENFOINJCUTOFF =    0u;   //   0
//enable [flag]
CALQUAL uint8_T ENFOINJCUTOFFTST =    0u;   //   0
//enable [flag]
CALQUAL uint8_T SELIDXFOCTFTOVIEW =  0u;   // 0
//enable [flag]
CALQUAL uint8_T SELTIMFOINJCTF =  0u;   // 0
//Ctf selector [flag]
CALQUAL uint16_T TBFOINJCTF0[12*2] = 
{
      1u,      1u,
      0u,      3u,
      0u,    100u,
      1u,      3u,
      0u,      1u,
      1u,      3u,
      1u,      3u,
      0u,    200u,
      1u,      5u,
      0u,      2u,
      1u,      4u,
      0u,    300u
};
//Ctf selector [flag]
CALQUAL uint16_T TBFOINJCTF1[12*2] = 
{
      1u,      1u,
      0u,      3u,
      0u,    100u,
      1u,      3u,
      0u,      1u,
      1u,      3u,
      1u,      3u,
      0u,    200u,
      1u,      5u,
      0u,      2u,
      1u,      4u,
      0u,    300u
};
//Ctf selector [flag]
CALQUAL uint16_T TBFOINJCTF2[12*2] = 
{
      1u,      1u,
      0u,      3u,
      0u,    100u,
      1u,      3u,
      0u,      1u,
      1u,      3u,
      1u,      3u,
      0u,    200u,
      1u,      5u,
      0u,      2u,
      1u,      4u,
      0u,    300u
};
//Ctf selector [flag]
CALQUAL uint16_T TBFOINJCTF3[12*2] = 
{
      1u,      1u,
      0u,      3u,
      0u,    100u,
      1u,      3u,
      0u,      1u,
      1u,      3u,
      1u,      3u,
      0u,    200u,
      1u,      5u,
      0u,      2u,
      1u,      4u,
      0u,    300u
};

#endif /* _BUILD_FOINJCTFMGM_ */

