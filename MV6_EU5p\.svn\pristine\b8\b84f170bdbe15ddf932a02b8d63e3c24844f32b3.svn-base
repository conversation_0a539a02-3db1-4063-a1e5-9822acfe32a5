/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_FUELMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//FUELMGM.AF0: Basic AF Ratio [ratio]
CALQUAL uint16_T AF0 = 14920u;   //(14.5703125000*1024)
//FUELMGM.BKANGQFSPLIT: AngThrottle breakpoint vector for fuel split [%]
CALQUAL uint16_T BKANGQFSPLIT[6] = 
{
 160u, 640u, 768u, 960u, 1120u, 1280u
};
//FUELMGM.BKBATINJT: Vbattery Breakpoint vector for BKBATINJT [V]
CALQUAL uint16_T BKBATINJT[16] = 
{
 112u, 128u, 136u, 144u, 160u, 168u, 184u, 192u, 200u, 208u, 216u, 224u, 232u, 240u, 248u, 384u
};
//FUELMGM.BKDPRES: Breakpoint vector for VTGAININJT [mbar]
CALQUAL uint16_T BKDPRES[12] = 
{
  3200u,  3400u,  3500u,  3600u,  3700u,  3800u,  3900u,  4000u,  4100u,  4200u,  4300u,  4400u
};
//FUELMGM.BKPRESFUEL: PresIntake breakpoint vector for fuel film [mbar]
CALQUAL uint16_T BKPRESFUEL[4] = 
{
   270u,   450u,   650u,  1024u
};
//FUELMGM.BKRPMFUEL: Rpm breakpoint vector for fuel film [rpm]
CALQUAL uint16_T BKRPMFUEL[8] = 
{
   1500u,   2500u,   3200u,   4500u,   6000u,   7500u,   9500u,  12000u
};
//FUELMGM.BKRPMQFSPLIT: Rpm breakpoint vector for TBSOIBASE [rpm]
CALQUAL uint16_T BKRPMQFSPLIT[3] = 
{
   4000u,   7000u,  10000u
};
//FUELMGM.BKSOIBASELOAD: Load Breakpoint vector for TBSOIBASE [%]
CALQUAL uint16_T BKSOIBASELOAD[6] = 
{
 3200u, 3840u, 5120u, 6400u, 7680u, 10240u
};
//FUELMGM.BKSOIBASERPM: Rpm breakpoint vector for TBSOIBASE [%]
CALQUAL uint16_T BKSOIBASERPM[6] = 
{
   3000u,   4500u,   6500u,   9600u,  10800u,  14600u
};
//FUELMGM.BKTDCFUEL: CntAbsTdc Breakpoint vector for fuel film [%]
CALQUAL uint16_T BKTDCFUEL[4] = 
{
    200u,    900u,   2300u,   4200u
};
//FUELMGM.BKTWFUEL: TWater breakpoint vector for fuel [C]
CALQUAL int16_T BKTWFUEL[4] = 
{
 -160, 80, 640, 1360
};
//FUELMGM.DPRESINJ0: delta pressure to force Film Enable [mbar]
CALQUAL int16_T DPRESFLEN =   1000;   //  1000
//FUELMGM.DPRESINJ0: Fuel accumulator pressure [mbar]
CALQUAL uint16_T DPRESINJ0 =   3800u;   //  3800
//FUELMGM.ENFUELFILM: Fuel Film Correction Enable Flag [boolean]
CALQUAL uint8_T ENFUELFILM =  1u;   // 1
//FUELMGM.ENQFOBJBASE: Enable use of QFObjBase [boolean]
CALQUAL uint8_T ENQFOBJBASE =  0u;   // 0
//FUELMGM.EXTRAFORCEDINJT: Forced value for Extra InjTime (only in development phase) [us]
CALQUAL uint16_T EXTRAFORCEDINJT =      0u;   //     0
//FUELMGM.EXTRAINJGAIN: Extra Inj Gain threshold [gain]
CALQUAL uint16_T EXTRAINJGAIN = 1126u;   //( 1.0996093750*1024)
//FUELMGM.EXTRAINJTMIN: Minimum Extra Injection Time in order to perform it [us]
CALQUAL uint16_T EXTRAINJTMIN =    790u;   //   790
//FUELMGM.EXTRASOI: Extra Inj Start of injection angle [deg]
CALQUAL uint16_T EXTRASOI = 5760u;   //(360.0000*16)
//FUELMGM.FILMENSTEP: Fuel Film Correction Enable Step [gain]
CALQUAL uint16_T FILMENSTEP = 2048u;   //(0.125********000*16384)
//FUELMGM.FILMENSTEPH: Fuel Film Correction Enable Step [gain]
CALQUAL uint16_T FILMENSTEPH = 2048u;   //(0.125********000*16384)
//FUELMGM.FIRSTSOI: First Start of injection angle [deg]
CALQUAL uint16_T FIRSTSOI = 12800u;   //(800.0000*16)
//FUELMGM.FORCEDKF: Manual Fuel gain (only in development phase) [gain]
CALQUAL uint16_T FORCEDKF = 16384u;   //(1.********000000*16384)
//FUELMGM.FUELFILMPRES: Fuel Film interpolating pressure selection (0:PresIntake, 1:PresIntk0, 2:PresObj, 3:PresObjF) [counter]
CALQUAL uint8_T FUELFILMPRES =  2u;   // 2
//FUELMGM.GAININJTH: Injection Time Gain [us/mgcc]
CALQUAL uint16_T GAININJTH = 15539u;   //( 242.796875*64)
//FUELMGM.INJTMIN: Minimum Injector Time [us]
CALQUAL uint16_T INJTMIN =    800u;   //   800
//FUELMGM.MAXQFILM: Maximum Fuel Film Mass [mgcc]
CALQUAL uint16_T MAXQFILM = 25600u;   //(100.*********256)
//FUELMGM.MAXQFSPLITBOTH: Max split value for both injector banks actuation [us]
CALQUAL int16_T MAXQFSPLITBOTH = 14254;   //(0.***************16384)
//FUELMGM.MINQFSPLITBOTH: Min split value for both injector banks actuation [us]
CALQUAL int16_T MINQFSPLITBOTH = 1638;   //(0.***************16384)
//FUELMGM.OFFINJTH: Injection Time Offset [us]
CALQUAL int16_T OFFINJTH =    498;   //   498
//FUELMGM.QFSPLITHYST: Split value hystheresis [us]
CALQUAL int16_T QFSPLITHYST = 492;   //(0.***************16384)
//Selector [g/l]
CALQUAL uint16_T QFUELDENS = 7500u;   //( 750.****************0000*10)
//Selector [flag]
CALQUAL uint8_T SELQFLTH =  0u;   // 0
//FUELMGM.TBQFUELSPLIT: TBQFUELSPLIT [deg]
CALQUAL uint8_T TBQFUELSPLIT[6*6] = 
{
 128u, 128u, 128u, 128u, 128u, 128u,
 128u, 128u, 128u, 128u, 128u, 128u,
 128u, 128u, 128u, 128u, 128u, 128u,
 128u, 128u, 128u, 128u, 128u, 128u,
 128u, 128u, 128u, 128u, 128u, 128u,
 128u, 128u, 128u, 128u, 128u, 128u
};
//FUELMGM.TBSOIBASE: Injection Time Offset [deg]
CALQUAL uint8_T TBSOIBASE[6*6] = 
{
 188u, 188u, 188u, 163u, 150u, 150u,
 188u, 188u, 175u, 144u, 138u, 138u,
 163u, 155u, 140u, 133u, 130u, 130u,
 150u, 150u, 150u, 163u, 180u, 180u,
 150u, 156u, 169u, 200u, 202u, 202u,
 150u, 163u, 188u, 202u, 202u, 202u
};
//FUELMGM.TBSOIBASEH: Injection Time Offset [deg]
CALQUAL uint8_T TBSOIBASEH[6*6] = 
{
 100u, 100u, 100u, 100u, 100u, 100u,
 120u, 120u, 120u, 138u, 138u, 138u,
 120u, 120u, 120u, 120u, 120u, 120u,
 181u, 181u, 181u, 181u, 181u, 181u,
 183u, 183u, 183u, 183u, 183u, 193u,
 175u, 175u, 175u, 175u, 175u, 175u
};
//FUELMGM.TBTAUFILMACC: Fuel Film Model Acceleration Time Constant Factor [gain]
CALQUAL uint8_T TBTAUFILMACC[4*8] = 
{
    4u,    4u,    5u,    7u,   10u,   12u,   14u,   14u,
    8u,   12u,   16u,   20u,   18u,   16u,   14u,   14u,
    9u,   13u,   17u,   22u,   19u,   16u,   15u,   14u,
    5u,   11u,   15u,   22u,   19u,   17u,   16u,   15u
};
//FUELMGM.TBTAUFILMDEC: Fuel Film Model Deceleration Time Constant Factor [gain]
CALQUAL uint8_T TBTAUFILMDEC[4*8] = 
{
    2u,    2u,    3u,    4u,    4u,    4u,    4u,    3u,
    3u,    3u,    4u,    5u,    5u,    5u,    4u,    3u,
    5u,    5u,    5u,    6u,    6u,    6u,    5u,    4u,
    7u,    7u,    7u,    8u,    8u,    8u,    6u,    6u
};
//FUELMGM.TBTWTAUFILM: Fuel Film Model Time Constant Factor Temperature and Starting Correction [gain]
CALQUAL uint8_T TBTWTAUFILM[4*4] = 
{
 33u, 31u, 26u, 19u,
 30u, 29u, 25u, 19u,
 27u, 27u, 24u, 18u,
 25u, 24u, 22u, 16u
};
//FUELMGM.TBTWXFILM: Fuel Film Model X Factor Temperature and Starting Correction [gain]
CALQUAL uint8_T TBTWXFILM[4*4] = 
{
 51u, 48u, 42u, 25u,
 48u, 46u, 42u, 25u,
 45u, 42u, 24u, 17u,
 43u, 39u, 20u, 16u
};
//FUELMGM.TBXFILMACC: Fuel Film Model Acceleration X Factor [gain]
CALQUAL uint8_T TBXFILMACC[4*8] = 
{
 22u, 21u, 21u, 21u, 20u, 18u, 13u, 12u,
 31u, 30u, 29u, 28u, 26u, 22u, 18u, 15u,
 39u, 38u, 37u, 35u, 30u, 25u, 20u, 18u,
 27u, 30u, 31u, 33u, 33u, 32u, 26u, 20u
};
//FUELMGM.THFOMINFLEN: Min threshold to force Film Enable [gain]
CALQUAL uint16_T THFOMINFLEN = 16384u;   //(1.********000000*16384)
//FUELMGM.THRQFACC: DQFObjCyl threshold for accelerate detection [gain]
CALQUAL int16_T THRQFACC = 13107;   //(0.399993896484375*32768)
//FUELMGM.THRQFDEC: DQFObjCyl threshold for decelerate detection [gain]
CALQUAL int16_T THRQFDEC = -7209;   //(-0.220001220703125*32768)
//FUELMGM.THRQFSTABACC: Max. DQFilmCyl threshold for stabilization detection [gain]
CALQUAL int16_T THRQFSTABACC = 328;   //(0.010009765625000*32768)
//FUELMGM.THRQFSTABDEC: Min. DQFilmCyl threshold for stabilization detection [gain]
CALQUAL int16_T THRQFSTABDEC = -164;   //(-0.005004882812500*32768)
//FUELMGM.USEQAIR4QFOBJ: QAir selection for QFObj calculation (0:QAir, 1:QAirCyl0*TbQAirGain, 2:QAirTarget0*TbQAirGain) [counter]
CALQUAL uint8_T USEQAIR4QFOBJ =  0u;   // 0
//FUELMGM.VTBATINJT: Injector Time Offset (Vbattery) [us]
CALQUAL int16_T VTBATINJT[16] = 
{
   2000,   1300,    900,    660,    450,    370,    250,    200,    140,     80,     30,      0,    -10,    -20,    -30,   -250
};
//FUELMGM.VTBATINJTH: Injector Time Offset (Vbattery) [us]
CALQUAL int16_T VTBATINJTH[16] = 
{
   2000,   1300,    900,    660,    450,    370,    250,    200,    140,     80,     30,      0,    -10,    -20,    -30,   -250
};
//FUELMGM.VTFIRSTINJTIME: First Injection time [us]
CALQUAL uint16_T VTFIRSTINJTIME[4] = 
{
  40000u,  14000u,   7500u,   5000u
};
//FUELMGM.VTFORCEDINJT: Forced value for InjTime (only in development phase) [us]
CALQUAL uint16_T VTFORCEDINJT[8] = 
{
      0u,      0u,      0u,      0u,      0u,      0u,      0u,      0u
};
//FUELMGM.VTFORCEINJT: Force InjTime (only in development phase) [boolean]
CALQUAL uint8_T VTFORCEINJT[8] = 
{
  0u,  0u,  0u,  0u,  0u,  0u,  0u,  0u
};
//FUELMGM.VTGAININJT: Injection Time Gain [us/mgcc]
CALQUAL uint16_T VTGAININJT[12] = 
{
 20864u, 20864u, 20858u, 20550u, 20221u, 20083u, 19784u, 19633u, 19293u, 19044u, 18962u, 18760u
};
//FUELMGM.VTOFFINJT: Injection Time Offset [us]
CALQUAL int16_T VTOFFINJT[12] = 
{
    768,    761,    810,    813,    825,    799,    831,    815,    838,    846,    817,    805
};
//FUELMGM.VTQFSPLITRATEMAX: Max rate limiter value for QFuelSplitFrac [us]
CALQUAL int16_T VTQFSPLITRATEMAX[3] = 
{
 82, 66, 49
};
//FUELMGM.VTQFSPLITRATEMIN: Min rate limiter value for QFuelSplitFrac [us]
CALQUAL int16_T VTQFSPLITRATEMIN[3] = 
{
 -66, -49, -33
};
//FUELMGM.VTTAUFILMACC: Fuel Film Model Acceleration Time Constant Factor [gain]
CALQUAL uint8_T VTTAUFILMACC[8] = 
{
   57u,   57u,   57u,   50u,   42u,   35u,   30u,   25u
};
//FUELMGM.VTTAUFILMDEC: Fuel Film Model Deceleration Time Constant Factor [gain]
CALQUAL uint8_T VTTAUFILMDEC[8] = 
{
   23u,   22u,   21u,   20u,   18u,   17u,   15u,   15u
};
//FUELMGM.VTXFILMACC: Fuel Film Model Acceleration X Factor [gain]
CALQUAL uint8_T VTXFILMACC[8] = 
{
 38u, 38u, 38u, 38u, 40u, 42u, 43u, 44u
};

#endif /* _BUILD_FUELMGM_ */

