/**
 ******************************************************************************
 **  Filename:      AFCtrl_private.h
 **  Date:          16-Oct-2020
 **
 **  Model Version: 1.850
 ******************************************************************************
 **/

#ifndef RTW_HEADER_AFCtrl_private_h_
#define RTW_HEADER_AFCtrl_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "AFCtrl.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "null.h"
#include "recmgm.h"
#include "lambda_mgm.h"
#include "analogin.h"
#include "Syncmgm.h"
#include "thrposmgm.h"
#include "engflag.h"
#include "DiagCanMgm.h"
#include "injcmd.h"
#include "ion_lambda.h"
#include "airdiag_mgm.h"
#include "trq_driver.h"
#include "idxctfctrl_out.h"
#include "fuel_mgm.h"
#include "canmgm.h"
#include "rpm_limiter.h"
#include "loadmgm.h"
#include "temp_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int32_T DELTALAMCLMAX;          /* Variable: DELTALAMCLMAX
                                        * Referenced by: '<S100>/KFMAX'
                                        * Maximum value for lambda control variable
                                        */
extern int32_T DELTALAMCLMIN;          /* Variable: DELTALAMCLMIN
                                        * Referenced by: '<S100>/KFMIN'
                                        * Minimum value for lambda control variable
                                        */
extern int32_T KLAMSLOWMAX;            /* Variable: KLAMSLOWMAX
                                        * Referenced by: '<S122>/KLAMSLOWMAX'
                                        * Max value for DeltaLamCyl0
                                        */
extern int32_T KLAMSLOWMIN;            /* Variable: KLAMSLOWMIN
                                        * Referenced by: '<S122>/KLAMSLOWMIN'
                                        * Min value for DeltaLamCyl0
                                        */
extern int16_T ERRLAMEACHDBAND;        /* Variable: ERRLAMEACHDBAND
                                        * Referenced by: '<S121>/ERRLAMEACHDBAND'
                                        * AF cyl cl control dead band
                                        */
extern int16_T LAMERPOOR;              /* Variable: LAMERPOOR
                                        * Referenced by: '<S68>/Calc_DeltaLamCLDiff'
                                        * PROBE ON-OFF: Lambda error in poor state
                                        */
extern int16_T LAMERRICH;              /* Variable: LAMERRICH
                                        * Referenced by: '<S68>/Calc_DeltaLamCLDiff'
                                        * PROBE ON-OFF: Lambda error in rich state
                                        */
extern int16_T STDECDLAMTRANS;         /* Variable: STDECDLAMTRANS
                                        * Referenced by: '<S191>/Transient_Lambda_Offset'
                                        * Dec step to slew DLamTrans to 0
                                        */
extern int16_T KFSTEP;                 /* Variable: KFSTEP
                                        * Referenced by: '<S41>/TDC_OL_s'
                                        * Step for lambda control variables
                                        */
extern int16_T LAMCORRADMAX;           /* Variable: LAMCORRADMAX
                                        * Referenced by: '<S150>/LAMCORRADMAX'
                                        * Maximum adaptive lambda correction
                                        */
extern int16_T LAMCORRADMIN;           /* Variable: LAMCORRADMIN
                                        * Referenced by: '<S150>/LAMCORRADMIN'
                                        * Minimum adaptive lambda correction
                                        */
extern int16_T THDLAMADINF;            /* Variable: THDLAMADINF
                                        * Referenced by: '<S45>/THDLAMADINF'
                                        * Lower threshold on DeltaLamCLFilt for lambda learning
                                        */
extern int16_T THDLAMADSUP;            /* Variable: THDLAMADSUP
                                        * Referenced by: '<S45>/THDLAMADSUP'
                                        * Upper threshold on DeltaLamCLFilt to enable learning
                                        */
extern int16_T DANGTHDISTRANS;         /* Variable: DANGTHDISTRANS
                                        * Referenced by: '<S191>/Transient_Lambda_Offset'
                                        * Delta throttle angle to disable transient lambda corr
                                        */
extern int16_T DANGTHMINTRANS;         /* Variable: DANGTHMINTRANS
                                        * Referenced by: '<S191>/Transient_Lambda_Offset'
                                        * Min delta throttle angle to enable transient lambda corr
                                        */
extern int16_T THAIRLAMADMAX;          /* Variable: THAIRLAMADMAX
                                        * Referenced by: '<S45>/THAIRLAMADMAX'
                                        * Threshold on the air temperature to enable lambda learning
                                        */
extern int16_T THAIRLAMADMIN;          /* Variable: THAIRLAMADMIN
                                        * Referenced by: '<S45>/THAIRLAMADMIN'
                                        * Threshold on the air temperature to enable lambda learning
                                        */
extern int16_T THWATLAMADMAX;          /* Variable: THWATLAMADMAX
                                        * Referenced by: '<S45>/THWATLAMADMAX'
                                        * Threshold on the coolant temperature to enable lambda learning
                                        */
extern int16_T THWATLAMADMIN;          /* Variable: THWATLAMADMIN
                                        * Referenced by: '<S45>/THWATLAMADMIN'
                                        * Threshold on the coolant temperature to enable lambda learning
                                        */
extern int16_T TWAFMIN;                /* Variable: TWAFMIN
                                        * Referenced by: '<S47>/TWAFMIN'
                                        * AF Control Enable min TWater Threshold
                                        */
extern int16_T TWENABLEWOT;            /* Variable: TWENABLEWOT
                                        * Referenced by: '<S183>/Target_Choice_automaton'
                                        * Wot TWater Threshold enable
                                        */
extern uint16_T DLAMTRANSINIT;         /* Variable: DLAMTRANSINIT
                                        * Referenced by: '<S191>/Transient_Lambda_Offset'
                                        * Init value of DLamTrans
                                        */
extern uint16_T FORCEDLAM;             /* Variable: FORCEDLAM
                                        * Referenced by: '<S182>/FORCEDLAM'
                                        * Manual target Lambda
                                        */
extern uint16_T FRACDLAMCLDIFF;        /* Variable: FRACDLAMCLDIFF
                                        * Referenced by: '<S105>/FRACDLAMCLDIFF'
                                        * Fraction of DeltaLamCLDiff applied in probe commutation
                                        */
extern uint16_T GAINKLAMINT;           /* Variable: GAINKLAMINT
                                        * Referenced by: '<S101>/GAINKLAMINT'
                                        * Gain applied to integral term in very poor-rich
                                        */
extern uint16_T LAMMAXCL;              /* Variable: LAMMAXCL
                                        * Referenced by: '<S48>/LAMMAXCL'
                                        * Max Lambda for CL
                                        */
extern uint16_T LAMMINCL;              /* Variable: LAMMINCL
                                        * Referenced by:
                                        *   '<S48>/LAMMINCL'
                                        *   '<S183>/Target_Choice_automaton'
                                        * Min Lambda for CL
                                        */
extern uint16_T THFREQOSCLAMAD;        /* Variable: THFREQOSCLAMAD
                                        * Referenced by: '<S45>/THFREQOSCLAMAD'
                                        * Threshold on FreqOscLambda to enable learning
                                        */
extern uint16_T BKLAMDSQ[4];           /* Variable: BKLAMDSQ
                                        * Referenced by: '<S150>/BKLAMDSQ'
                                        * Vector of breakpoints for the square distance
                                        */
extern uint16_T GNLAMCORRINDAD;        /* Variable: GNLAMCORRINDAD
                                        * Referenced by: '<S150>/GNKCORRINDAD'
                                        * Gain for lambda learning
                                        */
extern uint16_T KFENERLAMFILT;         /* Variable: KFENERLAMFILT
                                        * Referenced by: '<S39>/KFENERLAMFILT'
                                        * Filter constant for EnerLam
                                        */
extern uint16_T KFILTLAMERR;           /* Variable: KFILTLAMERR
                                        * Referenced by: '<S44>/KFILTLAMERR'
                                        * Filter constant for the lambda error
                                        */
extern uint16_T KFILTLAMOBJAVG;        /* Variable: KFILTLAMOBJAVG
                                        * Referenced by: '<S44>/KFILTLAMOBJAVG'
                                        * Filter constant for the common avg lambda
                                        */
extern uint16_T KFLFILT;               /* Variable: KFLFILT
                                        * Referenced by: '<S100>/KFLFILT'
                                        * Filter constant for DeltaLamCLFilt
                                        */
extern uint16_T KLAMSLOWKINT;          /* Variable: KLAMSLOWKINT
                                        * Referenced by: '<S120>/KLAMSLOWKINT'
                                        * AF cyl cl control integral gain
                                        */
extern uint16_T VTLAMGNAD[4];          /* Variable: VTLAMGNAD
                                        * Referenced by: '<S150>/VTLAMGNAD'
                                        * Vector of gains for adaptive coefficients spreading
                                        */
extern uint16_T ANGTHMINTRANS;         /* Variable: ANGTHMINTRANS
                                        * Referenced by: '<S191>/Transient_Lambda_Offset'
                                        * Min throttle angle to enable transient lambda corr
                                        */
extern uint16_T WOTANGHYST;            /* Variable: WOTANGHYST
                                        * Referenced by: '<S179>/WOTANGHYST'
                                        * Gas position hysteresis for WotFlg
                                        */
extern uint16_T BKLOADADLAM[6];        /* Variable: BKLOADADLAM
                                        * Referenced by: '<S43>/BKLOADADLAM'
                                        * Breakpoints of load for lambda learning
                                        */
extern uint16_T THRSTABDLAMADLAM;      /* Variable: THRSTABDLAMADLAM
                                        * Referenced by: '<S143>/THRSTABDLAMADLAM'
                                        * Threshold to detect stability for DeltaLamCFilt
                                        */
extern uint16_T THRSTABLOADADLAM;      /* Variable: THRSTABLOADADLAM
                                        * Referenced by: '<S43>/THRSTABLDADLAM'
                                        * Threshold to detect stability for Load
                                        */
extern uint16_T THRSTABRPMADLAM;       /* Variable: THRSTABRPMADLAM
                                        * Referenced by: '<S43>/THRSTABRPMADLAM'
                                        * Threshold to detect stability for Rpm
                                        */
extern uint16_T BKKFTDCCRANK[5];       /* Variable: BKKFTDCCRANK
                                        * Referenced by: '<S163>/BKKFTDCCRANK'
                                        * CntTdcCrk Breakpoint vector for TBKFCRANK
                                        */
extern uint16_T BKKFTDCSTARTER[4];     /* Variable: BKKFTDCSTARTER
                                        * Referenced by: '<S163>/BKKFTDCSTARTER'
                                        * CntTdcCrk Breakpoint vector for TBKFSTARTER
                                        */
extern uint16_T BKRPMADLAM[7];         /* Variable: BKRPMADLAM
                                        * Referenced by: '<S43>/BKRPMADLAM'
                                        * Rpm breakpoints for lambda learning
                                        */
extern uint16_T DELAYAFCLCTOFF;        /* Variable: DELAYAFCLCTOFF
                                        * Referenced by: '<S46>/Cutoff_condition'
                                        * Delay for CL after cutoff exit
                                        */
extern uint16_T DELAYCLCRKOFF;         /* Variable: DELAYCLCRKOFF
                                        * Referenced by: '<S56>/RPMAFMAX1'
                                        * Threshold of TdcCrk Offset
                                        */
extern uint16_T DELTDCCRKOFF;          /* Variable: DELTDCCRKOFF
                                        * Referenced by: '<S163>/Chart'
                                        * Delta TdcCrk Offset
                                        */
extern uint16_T RPMAFMAX;              /* Variable: RPMAFMAX
                                        * Referenced by: '<S52>/RPMAFMAX'
                                        * AF Control Enable Max Rpm Threshold
                                        */
extern uint16_T RPMAFMIN;              /* Variable: RPMAFMIN
                                        * Referenced by: '<S52>/RPMAFMIN'
                                        * AF Control Enable Min Rpm Threshold
                                        */
extern uint16_T TDCDLAMSTABADLAM;      /* Variable: TDCDLAMSTABADLAM
                                        * Referenced by: '<S143>/TDCDLAMSTABADLAM'
                                        * Number of TDCs to detect stability of DeltaLamFilt for Lambda learning
                                        */
extern uint16_T TDCRLSTABADLAM;        /* Variable: TDCRLSTABADLAM
                                        * Referenced by: '<S43>/TDCSTABADLAM'
                                        * Number of TDCs to detect stability of Rpm and Load for Lambda learning
                                        */
extern uint16_T THTDCCRKOFF;           /* Variable: THTDCCRKOFF
                                        * Referenced by: '<S163>/Chart'
                                        * Threshold of TdcCrk Offset
                                        */
extern uint16_T VTSTRCNTTDCCRK[8];     /* Variable: VTSTRCNTTDCCRK
                                        * Referenced by: '<S163>/VTSTRCNTTDCCRK'
                                        * Start of CntTdcCrk in lost sync
                                        */
extern int8_T TBDELAYRICHPOOR[24];     /* Variable: TBDELAYRICHPOOR
                                        * Referenced by: '<S68>/TBDELAYRICHPOOR'
                                        * P2R trans delay (>0); R2P trans delay (<0)
                                        */
extern int8_T BKKFSTAIR[8];            /* Variable: BKKFSTAIR
                                        * Referenced by: '<S163>/BKKFSTAIR'
                                        * TAirCrk Breakpoint vector
                                        */
extern int8_T BKKFSTWATER[8];          /* Variable: BKKFSTWATER
                                        * Referenced by:
                                        *   '<S163>/BKKFSTWATER'
                                        *   '<S163>/BKKFSTWATER1'
                                        * TWaterCrk Breakpoint vector for TBDLTKFSTART
                                        */
extern int8_T TBGNPROPCORR[24];        /* Variable: TBGNPROPCORR
                                        * Referenced by: '<S68>/TBGNPROPCORR'
                                        * P2R trans corr (>0); R2P trans corr (<0)
                                        */
extern uint8_T BKAFRPM[12];            /* Variable: BKAFRPM
                                        * Referenced by: '<S180>/BKAFRPM'
                                        * Rpm breakpoints
                                        */
extern uint8_T BKLAMCTRLRPM[6];        /* Variable: BKLAMCTRLRPM
                                        * Referenced by:
                                        *   '<S95>/BKLAMCTRLRPM'
                                        *   '<S68>/BKLAMCTRLRPM'
                                        *   '<S52>/BKLAMCTRLRPM'
                                        * Rpm breakpoints
                                        */
extern uint8_T BKWOTANGRPM[10];        /* Variable: BKWOTANGRPM
                                        * Referenced by: '<S177>/BKWOTANGRPM'
                                        * Rpm breakpoints for WotFlg
                                        */
extern uint8_T VTDELAYCLCRK[8];        /* Variable: VTDELAYCLCRK
                                        * Referenced by: '<S56>/RPMAFMAX8'
                                        * Vector of delay for CL after crank
                                        */
extern uint8_T VTDELAYAFWOT[10];       /* Variable: VTDELAYAFWOT
                                        * Referenced by: '<S198>/ VTDELAYAFWOT'
                                        * Threshold for wot lambda delay
                                        */
extern uint8_T BKAFLOAD[8];            /* Variable: BKAFLOAD
                                        * Referenced by: '<S180>/BKAFLOAD'
                                        * Load breakpoints
                                        */
extern uint8_T BKLAMCTRLLOAD[4];       /* Variable: BKLAMCTRLLOAD
                                        * Referenced by:
                                        *   '<S95>/BKLAMCTRLLOAD'
                                        *   '<S68>/BKLAMCTRLLOAD'
                                        * Load breakpoints
                                        */
extern uint8_T VTLOADLAMMAX[6];        /* Variable: VTLOADLAMMAX
                                        * Referenced by: '<S52>/RPMAFMAX5'
                                        * AF Control Enable Max Load Threshold
                                        */
extern uint8_T VTLOADLAMMIN[6];        /* Variable: VTLOADLAMMIN
                                        * Referenced by: '<S52>/RPMAFMAX3'
                                        * AF Control Enable Min Load Threshold
                                        */
extern uint8_T VTWOTANG[10];           /* Variable: VTWOTANG
                                        * Referenced by: '<S179>/VTWOTANG'
                                        * Gas position threshold for WotFlg
                                        */
extern uint8_T TBKLAMINT[24];          /* Variable: TBKLAMINT
                                        * Referenced by: '<S101>/TBKLAMINT'
                                        * AF Control integral gain
                                        */
extern uint8_T TBKLAMPRO[24];          /* Variable: TBKLAMPRO
                                        * Referenced by: '<S105>/TBKLAMPRO'
                                        * AF Control proportional gain
                                        */
extern uint8_T BKLAMEFF[8];            /* Variable: BKLAMEFF
                                        * Referenced by:
                                        *   '<S31>/BKLAMEFF'
                                        *   '<S178>/BKLAMEFF'
                                        * Breakpoints vector of lambda for VTEFFLAM
                                        */
extern uint8_T TBCRKLAMPIRED[40];      /* Variable: TBCRKLAMPIRED
                                        * Referenced by: '<S95>/TBCRKLAMPIRED'
                                        * PI reduction gain during crank
                                        */
extern uint8_T TBKFAIRCRANK[40];       /* Variable: TBKFAIRCRANK
                                        * Referenced by: '<S178>/TBKFAIRCRANK'
                                        * Cranking gain for fuel enrichment
                                        */
extern uint8_T TBKFCRANK[40];          /* Variable: TBKFCRANK
                                        * Referenced by: '<S178>/TBKFCRANK'
                                        * Cranking gain for fuel enrichment
                                        */
extern uint8_T TBECONLAMBASE[96];      /* Variable: TBECONLAMBASE
                                        * Referenced by: '<S180>/TBECONLAMBASE'
                                        * Base target lambda in Economy Mode
                                        */
extern uint8_T TBKFSTARTER[32];        /* Variable: TBKFSTARTER
                                        * Referenced by: '<S163>/TBKFSTARTER'
                                        * Starter gain for fuel enrichment
                                        */
extern uint8_T TBLAMBASE[96];          /* Variable: TBLAMBASE
                                        * Referenced by: '<S180>/TBLAMBASE'
                                        * Base target lambda
                                        */
extern uint8_T VTEFFLAM[8];            /* Variable: VTEFFLAM
                                        * Referenced by: '<S31>/VTEFFLAM '
                                        * (SR) Vector of lambda efficiency
                                        */
extern uint8_T VTKFLAMCOMP[8];         /* Variable: VTKFLAMCOMP
                                        * Referenced by: '<S178>/VTKFLAMCOMP'
                                        * Vector of gains for unburned fuel
                                        */
extern uint8_T VTLAMWOT[12];           /* Variable: VTLAMWOT
                                        * Referenced by: '<S182>/VTLAMWOT '
                                        * Target Lambda in WOT
                                        */
extern uint8_T VTLAMWOTRED[12];        /* Variable: VTLAMWOTRED
                                        * Referenced by: '<S182>/VTLAMWOTRED'
                                        * Reduced target Lambda in WOT
                                        */
extern uint8_T ENAFCTRL;               /* Variable: ENAFCTRL
                                        * Referenced by: '<S46>/ENAFCTRL'
                                        * If 1 then AF closed loop control is enabled
                                        */
extern uint8_T ENAFOBJIN;              /* Variable: ENAFOBJIN
                                        * Referenced by: '<S44>/ENAFOBJIN'
                                        * Use LamEstAvg (=0), AFObjIn (=1), LamEstAvg_ANN (=2), LamCAN1 (=3), LamCAN2 (=4), LamCAN3 (=5), LamCAN4 (=6)
                                        */
extern uint8_T ENAFONOFF;              /* Variable: ENAFONOFF
                                        * Referenced by: '<S44>/ENAFONOFF'
                                        * PROBE ON-OFF: calibration flag enable
                                        */
extern uint8_T ENCTRLAMCYL;            /* Variable: ENCTRLAMCYL
                                        * Referenced by:
                                        *   '<S40>/ENCTRLAMCYL'
                                        *   '<S100>/KFLFILT1'
                                        * AF cyl cl control enable flag
                                        */
extern uint8_T ENLAMAD;                /* Variable: ENLAMAD
                                        * Referenced by: '<S45>/ENLAMAD'
                                        * Enables the lambda learning (=1)
                                        */
extern uint8_T ENLAMADCORR;            /* Variable: ENLAMADCORR
                                        * Referenced by: '<S39>/ENLAMADCORR'
                                        * Enables the adaptive correction for lambda (=1)
                                        */
extern uint8_T FLGENAECONWOT;          /* Variable: FLGENAECONWOT
                                        * Referenced by: '<S183>/Target_Choice_automaton'
                                        * enables WOT management in ECONOMY mode
                                        */
extern uint8_T LAMCANCYLSEL;           /* Variable: LAMCANCYLSEL
                                        * Referenced by:
                                        *   '<S39>/LAMCANCYLSEL'
                                        *   '<S100>/KFLFILT2'
                                        *   '<S120>/LAMCANCYLSEL'
                                        *   '<S120>/LAMCANCYLSEL1'
                                        *   '<S121>/LAMCANCYLSEL'
                                        * Enable LAMCANCYLSEL
                                        */
extern uint8_T STDECWOTCOUNT;          /* Variable: STDECWOTCOUNT
                                        * Referenced by: '<S183>/Target_Choice_automaton'
                                        * Dec step for wot lambda delay
                                        */
extern uint8_T STINCWOTCOUNT;          /* Variable: STINCWOTCOUNT
                                        * Referenced by: '<S183>/Target_Choice_automaton'
                                        * Inc step for wot lambda delay
                                        */
extern void AFCtrl_Reset(void);
extern void AFCtrl_T10ms_Init(void);
extern void AFCtrl_T10ms(void);
extern void AFCtrl_TDC_OL(void);
extern void AFCtrl_PreTDC_Out(void);
extern void AFCtrl_PreTDC_Condition_Init(void);
extern void AFCtrl_PreTDC_Condition_Reset(void);
extern void AFCtrl_PreTDC_Condition(void);
extern void AFCtrl_Update_TbLamAd(void);
extern void AFCtrl_TDC_CL(void);
extern void AFCtrl_Control_flow_Init(void);
extern void AFCtrl_Control_flow_Enable(void);
extern void AFCtrl_Control_flow(void);
extern void AFCtrl_PreTDC_Init(void);
extern void AFCtrl_PreTDC_Enable(void);
extern void AFCtrl_PreTDC(void);
extern void AFCtrl_NoSync(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern int16_T TbLamAd[42];            /* '<Root>/_DataStoreBlk_35' */

/* Lambda adaptive table */
#endif                                 /* RTW_HEADER_AFCtrl_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
