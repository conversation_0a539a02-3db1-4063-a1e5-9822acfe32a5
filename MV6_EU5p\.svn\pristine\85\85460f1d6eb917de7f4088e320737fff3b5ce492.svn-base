/**
 ******************************************************************************
 **  Filename:      QAirTargetMgm.h
 **  Date:          19-Sep-2022
 **
 **  Model Version: 1.720
 ******************************************************************************
 **/

#ifndef RTW_HEADER_QAirTargetMgm_h_
#define RTW_HEADER_QAirTargetMgm_h_
#ifndef QAirTargetMgm_COMMON_INCLUDES_
# define QAirTargetMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* QAirTargetMgm_COMMON_INCLUDES_ */

#include "QAirTargetMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "ETPU_EngineDefs.h"
#include "qair_target.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKKFILTQAIRTRG_dim             7U                        /* Referenced by: '<S9>/BKKFILTQAIRTRG_dim' */

/* BKKFILTQAIRTRG dimension */
#define ID_QAIR_TARGET                 28856812U                 /* Referenced by: '<S2>/ID_QAIR_TARGET' */

/* mask */
#define ONE_QATDEC                     -1024                     /* Referenced by: '<S7>/ONE_QATDEC' */
#define ONE_QATINC                     1024                      /* Referenced by:
                                                                  * '<S7>/ONE_QATINC'
                                                                  * '<S7>/ONE_QATINC1'
                                                                  */
#define QAIR_INIT                      0U                        /* Referenced by: '<S2>/QAIR_INIT' */
#define ZERO_QATINC                    0                         /* Referenced by: '<S7>/ZERO_QATINC' */

/* Block states (default storage) for system '<Root>' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S9>/Memory' */
  uint16_T Memory1_PreviousInput;      /* '<S20>/Memory1' */
  uint16_T Memory_PreviousInput_k;     /* '<S20>/Memory' */
  int16_T Memory_PreviousInput_f;      /* '<S7>/Memory' */
  uint8_T Memory_PreviousInput_n;      /* '<S8>/Memory' */
} D_Work_QAirTargetMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_T10ms_Trig_ZCE;        /* '<S1>/fc_T10ms' */
  ZCSigState fc_Init_Trig_ZCE[2];      /* '<S1>/fc_Init' */
} PrevZCSigStates_QAirTargetMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
} ExternalInputs_QAirTargetMgm;

/* Block states (default storage) */
extern D_Work_QAirTargetMgm QAirTargetMgm_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_QAirTargetMgm QAirTargetMgm_U;

/* Model entry point functions */
extern void QAirTargetMgm_initialize(void);
extern void QAirTargetMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('QAirTargetMgm_gen/QAirTargetMgm')    - opens subsystem QAirTargetMgm_gen/QAirTargetMgm
 * hilite_system('QAirTargetMgm_gen/QAirTargetMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'QAirTargetMgm_gen'
 * '<S1>'   : 'QAirTargetMgm_gen/QAirTargetMgm'
 * '<S2>'   : 'QAirTargetMgm_gen/QAirTargetMgm/Init'
 * '<S3>'   : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms'
 * '<S4>'   : 'QAirTargetMgm_gen/QAirTargetMgm/fc_Init'
 * '<S5>'   : 'QAirTargetMgm_gen/QAirTargetMgm/fc_T10ms'
 * '<S6>'   : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget'
 * '<S7>'   : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Sel_QAirTarget'
 * '<S8>'   : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Stab_Dyn'
 * '<S9>'   : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget/Calc_QAirTarget'
 * '<S10>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget/Calc_QAirTarget/Compare To Zero'
 * '<S11>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget/Calc_QAirTarget/FOF_Reset_S16_FXP'
 * '<S12>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget/Calc_QAirTarget/LookUp_U16_U16_1'
 * '<S13>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget/Calc_QAirTarget/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S14>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Calc_QAirTarget/Calc_QAirTarget/LookUp_U16_U16_1/Data Type Conversion Inherited3'
 * '<S15>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Sel_QAirTarget/RateLimiter_S16'
 * '<S16>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Sel_QAirTarget/Saturation Dynamic'
 * '<S17>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Sel_QAirTarget/Saturation Dynamic1'
 * '<S18>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Sel_QAirTarget/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S19>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Stab_Dyn/Compare To Zero'
 * '<S20>'  : 'QAirTargetMgm_gen/QAirTargetMgm/T10ms/Stab_Dyn/Steady_State_Detect'
 */
#endif                                 /* RTW_HEADER_QAirTargetMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
