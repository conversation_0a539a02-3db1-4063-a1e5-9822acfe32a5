/*
 * File: idle_mgm.h
 *
 * Code generated for Simulink model 'IdleMgm'.
 *
 * Model version                  : 1.854
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar 22 16:31:41 2023
 */

#ifndef RTW_HEADER_idle_mgm_h_
#define RTW_HEADER_idle_mgm_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmiIdleI;

/* CMI fast after Idle speed control */
extern int16_T CmiIdleP;

/* CMI slow after Idle speed control */
extern uint8_T EnEconFlg;

/* Rpm Idle in Economy Mode applied */
extern uint8_T FlagResetIdle;

/* flag to reset IdleFlg */
extern uint8_T FlgEconVbat;

/* enable for Vbattery */
extern uint32_T IDIdleMgm;

/* ID Version */
extern int16_T IdlRpmErr;

/* Rpm Error for Idle speed control */
extern int16_T IdlTrqOffstp;

/* Offset */
extern uint8_T IdleAdpFlg;

/* Idle speed control Torque Adaptation flag */
extern uint8_T IdleFlg;

/* Idle Speed Controller Active */
extern int16_T IdlePropGain;

/* Idle gain prop */
extern int16_T IdleTrqF;

/* Filtered idle integral speed control torque output */
extern int16_T IdleTrqI;

/* Instantaneous idle speed control torque output */
extern int32_T IdleTrqIHiR;

/* Instantaneous idle speed control torque output */
extern int16_T IdleTrqNoSat;

/* Idle speed control torque output (not saturated) */
extern int16_T IdleTrqP;

/* Predicted idle speed control torque output */
extern int32_T IdleTrqPHiR;

/* Slow idle speed control torque output */
extern int16_T PiIdleTrqInt;

/* Integral idle speed governor torque output */
extern int32_T PiIdleTrqIntHiR;

/* Idle integral term - high resolution */
extern int16_T PiIdleTrqProp;

/* Proportional idle speed governor torque output */
extern uint16_T RpmIdleGasOff;

/* Idle Rpm Target */
extern uint16_T RpmIdleObj;

/* Idle Rpm Target */
extern uint16_T RpmIdleObj0;

/* Idle Rpm Static Target */
extern uint16_T RpmIdleObj0Sw;

/* Idle Rpm Static Target */
extern uint32_T RpmIdleObjHr;

/* Idle Rpm Target */
extern uint8_T StIdle;

/* Idle status */
extern int32_T VBatErrInt;

/* Integral of VBattery error */
extern int32_T idletrqf_hires;

/** public functions **/
void IdleMgm_Init(void);
void IdleMgm_T10ms(void);
void IdleMgm_NoSync(void);

/* idletrqf_hires */
#endif                                 /* RTW_HEADER_idle_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
