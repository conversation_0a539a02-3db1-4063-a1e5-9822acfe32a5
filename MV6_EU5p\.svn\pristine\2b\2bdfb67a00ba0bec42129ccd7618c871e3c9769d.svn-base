/*******************************************************************
 *
 *    DESCRIPTION: Math Lib 1.16    16/07/2007 
 *
 *    AUTHOR: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _MATHLIB_H_
#define _MATHLIB_H_
/** include files **/
#include "typedefs.h"

/** local definitions **/
typedef enum { 
    INT8_TYPE, 
    INT16_TYPE, 
    INT32_TYPE, 
    INT64_TYPE,
    UINT8_TYPE,    
    UINT16_TYPE,    
    UINT32_TYPE,    
    UINT64_TYPE    
} ENUM_TYPE;

/*********************************************************************
 *********************************************************************
 *************** Librerie matematiche scritte a mano   ***************
 *********************************************************************
 *********************************************************************/
#ifndef min
#define min(a,b)    (((a)>(b))?(b):(a))
#endif
#ifndef max
#define max(a,b)    (((a)<(b))?(b):(a))
#endif
#ifndef abs
#define abs(x)      (((x)>0)?(x):(-(x)))
#endif
#ifndef sign
#define sign(x)     (((x)>0)?(1):(((x)<0)?(-1):(0)))
#endif
#ifndef abs_delta
#define abs_delta(a,b) (((a)<(b))?(b-a):(a-b))
#endif
#ifndef Calc_Period_SAT
#define Calc_Period_SAT(start,stop,saturation) ((stop >= start)?(stop - start):(stop + (saturation - start)))
#endif

#define BITVAL(i)   (1U<<i)
#define TSTBIT(a,i) (!(!(a & BITVAL(i))))
#define SETBIT(a,i) (a |= BITVAL(i))
#define RESBIT(a,i) (a &= ~BITVAL(i))
#define SETBITATY(a,i,y)  a = (a & (~BITVAL(i))) | ((y>0)<<i)

/* Definizione scalature fixed point  */
#define FIX_00  1           /* = 2^0  */
#define FIX_01  2           /* = 2^1  */
#define FIX_02  4           /* = 2^2  */
#define FIX_03  8           /* = 2^3  */
#define FIX_04  16          /* = 2^4  */
#define FIX_05  32          /* = 2^5  */
#define FIX_06  64          /* = 2^6  */
#define FIX_07  128         /* = 2^7  */
#define FIX_08  256         /* = 2^8  */
#define FIX_09  512         /* = 2^9  */
#define FIX_10  1024        /* = 2^10 */
#define FIX_11  2048        /* = 2^11 */
#define FIX_12  4096        /* = 2^12 */
#define FIX_13  8192        /* = 2^13 */
#define FIX_14  16384       /* = 2^14 */
#define FIX_15  32768       /* = 2^15 */
#define FIX_16  65536       /* = 2^16 */
#define FIX_17  131072      /* = 2^17 */
#define FIX_18  262144      /* = 2^18 */
#define FIX_19  524288      /* = 2^19 */
#define FIX_20  1048576     /* = 2^20 */
#define FIX_21  2097152     /* = 2^21 */
#define FIX_22  4194304     /* = 2^22 */
#define FIX_23  8388608     /* = 2^23 */
#define FIX_24  16777216    /* = 2^24 */
#define FIX_25  33554432    /* = 2^25 */
#define FIX_26  67108864    /* = 2^26 */
#define FIX_27  134217728   /* = 2^27 */
#define FIX_28  268435456   /* = 2^28 */
#define FIX_29  536870912   /* = 2^29 */
#define FIX_30  1073741824  /* = 2^30 */
#define FIX_31  2147483648  /* = 2^31 */
#define FIX_32  4294967296  /* = 2^32 */

/* SteadyStateDetect */
#define FIRST_TIME  0
#define INIT        1
#define COUNT       2
#define WAIT_RESET  3

/* CRC algo type */
#define UTILS_CRC_FORWARD   0x0U
#define UTILS_CRC_REVERSED  0x1U
#ifdef FAST_CRC16
uint16_t    update_crc16( uint16_t crc, uint8_t* c ,uint32_t block_size, uint8_t CRC_Type);
#else
uint16_T update_crc16_2(uint16_T crc,uint8_T* c,uint32_T block_size);
#endif

#ifdef  INT64_T
int64_T     GenAbs64(int64_T x, ENUM_TYPE xtype);
#endif
int32_T     GenAbs(int32_T x, ENUM_TYPE xtype);
uint32_T    sqrtU32(uint32_T a);
 
/*********************************************************************
 *********************************************************************
 *************** Librerie matematiche generate da RTW  ***************
 *********************************************************************
 *********************************************************************/

/*********************************************************************
 * LSRu
 * Logical Shift Right for unsigned integers.
 */
#define LSRu(nBits,C) (((uint32_T)(C))>>(nBits))

/* end macro LSRu
 *********************************************************************/

/*********************************************************************
 * LSL_U32
 * Shift Left for unsigned integers.
 * Note there is no differenct between logical shift left and
 * arithmetic shift left.
 */
#define LSL_U32(nBits,C) (((unsigned long)(C))<<(nBits))

/* end macro LSL_U32
 *********************************************************************/

/*********************************************************************
 * Fixed Point Multiplication Utility MUL_U32_U32_U32_SR31
 *   Values
 *      Vc = Va * Vb
 *   Stored Integer Formula
 *      C = A * B * 2^-31
 *
 * overflow is possible
 *   HiProd = 2^33 - 2^2 + 2^-31
 *          = (2^32-1) * (2^32-1) * (2^-31)
 *   HiOut  = 2^32 - 1
 *   LoProd = 0
 *   LoOut  = 0
 * overflows will WRAP (modulo 2)
 *    no code specific to overflow management is included
 *
 * round to FLOOR
 *    no code specific to rounding is required
 */
#define MUL_U32_U32_U32_SR31(C,A,B) \
  { \
    unsigned long pHi, pLo; \
    \
    /* Multiply Integers with result stored in two ulongs */ \
    DMUL_2xU32_U32_U32(&(pHi),&(pLo),A,B); \
    \
    pLo = LSL_U32(1,pHi) | LSRu(31,pLo); \
    \
    C = (uint32_T)(pLo); \
  } \

/* end macro MUL_U32_U32_U32_SR31
 *********************************************************************/
 
 /*********************************************************************
 * ASR
 * Arithmetic Shift Right for signed integers.
 * Note: the C standard does not specify whether shift right >> 
 * on signed integers is Logical, Arithmetic, or even garbage.
 * This macro uses the implementation dependent behavior to
 * get desired Arithmetic Shift Right behavior.  This macro is
 * NOT portable.
 */
#define ASR(nBits,C) ( (C)>>(nBits) )

/* end macro ASR
 *********************************************************************/
 
/*********************************************************************
 * Fixed Point Division Utility DIV_MACRO_u32
 */
#define DIV_MACRO_u32(quotient,numerator,denominator) \
  (quotient) = ((denominator)==0) ? (uint32_T)(MAX_uint32_T) : (uint32_T)((numerator) / (denominator)) \

/* end macro DIV_MACRO_u32
 *********************************************************************/

#define DIV_U16_U16_U16_SL8(C,A,B) \
  { \
    /* Handle division by zero */ \
    if ( (B) == 0) \
    { \
      /* saturate to maximum */ \
      (C) = (uint16_T)((0xFFFFU)); \
    } \
    else \
    { \
      /* compute quotient */ \
      /* output precision is greater than natural precision, but \
       * shifts left in uint before division gives desired results \
       */ \
      (C) = (uint16_T)( ( ((unsigned)(A)) << 8 ) / ((unsigned)(B)) ); \
    } \
  } \

/*********************************************************************
 * LSL_S32
 * Shift Left for signed integers.
 * Note there is no differenct between logical shift left and
 * arithmetic shift left.
 */
#define LSL_S32(nBits,C) (((long)(C))<<(nBits))

/* end macro LSL_S32
 *********************************************************************/
 
/*********************************************************************
 * Fixed Point Multiplication Utility MUL_U32_U16_U8
 *   Values
 *      Vc = Va * Vb
 *   Stored Integer Formula
 *      C = A * B * 2^0
 *
 * overflow is impossible
 *   HiProd = 2^24 - 2^16 - 2^8 + 2^0
 *          = (2^16-1) * (2^8-1) * (2^0)
 *   HiOut  = 2^32 - 1
 *   LoProd = 0
 *   LoOut  = 0
 * so SATURATE verses WRAP is irrelevant
 *    no code specific to overflow management is required
 *
 * rounding irrelevant  2^0  NO shifts right
 *    no code specific to rounding is required
 */
#define MUL_U32_U16_U8(C,A,B) \
  { \
    \
    C = (((uint32_T)(A)) * ((uint32_T)(B))); \
  } \

/* end macro MUL_U32_U16_U8
 *********************************************************************/

/** public functions **/

/** SPE Functions **/
int32_T SPE_MultiplyAndShiftRight_32_32(int32_T x, int32_T y, int32_T shift);

void DIV_RepeatSub_U32_U32_U32(uint32_T *C, unsigned long A, unsigned long B, uint32_T nRepeatSub);
void DIV_S16_S16_U16_SL8_FLOOR( int16_T *C, int16_T A, uint16_T B); 
uint32_T div_repeat_u32(uint32_T numerator, uint32_T denominator, uint32_T nRepeatSub);
uint32_T div_repeat_us32_floor(int32_T numerator, int32_T denominator, uint32_T nRepeatSub);
uint32_T div_repeat_u32_ceiling(uint32_T numerator, uint32_T denominator, uint32_T nRepeatSub);

void DMUL_2xU32_U32_U32( unsigned long *Chi, unsigned long *Clo, uint32_T A, uint32_T B);

/* Nuove funzioni Matlab R14 */ 
void BINARYSEARCH_U16( uint32_T *piLeft, uint32_T *piRght, uint16_T u, const uint16_T *pData, uint8_T iHi);
void BINARYSEARCH_S16( uint32_T *piLeft, uint32_T *piRght, int16_T u, const int16_T *pData, uint8_T iHi);
void BINARYSEARCH_U16_Near_iL( uint32_T *piLeft, uint16_T u, const uint16_T *pData, uint32_T iHi);
void BINARYSEARCH_U32( uint32_T *piLeft, uint32_T *piRght, uint32_T u, const uint32_T *pData, uint32_T iHi);
void BINARYSEARCH_U8( uint32_T *piLeft, uint32_T *piRght, uint8_T u, const uint8_T *pData, uint8_T iHi);
void BINARYSEARCH_S8( uint32_T *piLeft, uint32_T *piRght, int8_T u, const int8_T *pData, uint8_T iHi);

void INTERPOLATE_U8_U16( uint16_T *pY, uint8_T yL, uint8_T yR, uint16_T x, uint16_T xL, uint16_T xR);
void INTERPOLATE_U8_S16( uint8_T *pY, uint8_T yL, uint8_T yR, int16_T x, int16_T xL, int16_T xR);
void INTERPOLATE_S8_S16( int8_T *pY, int8_T yL, int8_T yR, int16_T x, int16_T xL, int16_T xR);
void INTERPOLATE_S8_U16( int8_T *pY, int8_T yL, int8_T yR, uint16_T x, uint16_T xL, uint16_T xR);
void INTERPOLATE_U16_U16(uint16_T *pY, uint16_T yL, uint16_T yR, uint16_T x, uint16_T xL, uint16_T xR);
void INTERPOLATE_U16_S16(uint16_T *pY, uint16_T yL, uint16_T yR, int16_T x, int16_T xL, int16_T xR);
void INTERPOLATE_S16_U16( int16_T *pY, int16_T yL, int16_T yR, uint16_T x, uint16_T xL, uint16_T xR);
void INTERPOLATE_S16_S16( int16_T *pY, int16_T yL, int16_T yR, int16_T x, int16_T xL, int16_T xR);

void Look2D_U8_U16_U16( uint16_T *pY, const uint8_T *pYData, uint16_T u0, const uint16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_U8_U16_S16( uint16_T *pY, const uint8_T *pYData, uint16_T u0, const  uint16_T *pU0Data, uint8_T iHiU0, int16_T u1, const int16_T *pU1Data, uint8_T iHiU1);
void Look2D_U8_S16_S16( uint16_T *pY, const uint8_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, int16_T u1, const int16_T *pU1Data, uint8_T iHiU1);
void Look2D_U8_S16_U16( uint16_T *pY, const uint8_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data,uint8_T iHiU1);
void Look2D_S8_U16_U16( int16_T *pY, const int8_T *pYData, uint16_T u0, const uint16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_S8_U16_S16( int16_T *pY, const int8_T *pYData, uint16_T u0, const uint16_T *pU0Data, uint8_T iHiU0, int16_T u1, const int16_T *pU1Data, uint8_T iHiU1);
void Look2D_S8_S16_S16( int16_T *pY, const int8_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, int16_T u1, const int16_T *pU1Data, uint8_T iHiU1);
void Look2D_S8_S16_U16( int16_T *pY, const int8_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_U16_U16_U16(uint16_T *pY, const uint16_T *pYData, uint16_T u0, const uint16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_U16_S16_S16(uint16_T *pY, const uint16_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, int16_T u1, const int16_T *pU1Data, uint8_T iHiU1);
void Look2D_S16_S16_U16(  int16_T *pY, const int16_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_U16_S16_U16( uint16_T *pY, const uint16_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_S16_U16_U16( int16_T *pY, const int16_T *pYData, uint16_T u0, const uint16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);
void Look2D_S16_S16_S16( int16_T *pY, const int16_T *pYData, int16_T u0, const int16_T *pU0Data, uint8_T iHiU0, int16_T u1, const int16_T *pU1Data, uint8_T iHiU1);
void Look2D_U32_U16_U16(uint32_T *pY, const uint32_T *pYData, uint16_T u0, const uint16_T *pU0Data, uint8_T iHiU0, uint16_T u1, const uint16_T *pU1Data, uint8_T iHiU1);

void LookUp_U8_U16( uint16_T *pY, const uint8_T *pYData, uint16_T u, const uint16_T *pUData, uint8_T iHi);
void LookUp_U8_S16( uint16_T *pY, const uint8_T *pYData, int16_T u, const int16_T *pUData, uint8_T iHi);
void LookUp_S8_U16( int16_T *pY, const int8_T *pYData, uint16_T u, const uint16_T *pUData, uint8_T iHi);
void LookUp_S8_S16( int16_T *pY, const int8_T *pYData, int16_T u, const int16_T *pUData, uint8_T iHi);

void LookUp_U8_U8( uint16_T *pY, const uint8_T *pYData, uint16_T u, const uint8_T *pUData, uint8_T DeltaScaling, uint8_T iHi);
void LookUp_U8_S8( uint16_T *pY, const uint8_T *pYData, int16_T u, const int8_T *pUData, uint8_T DeltaScaling, uint8_T iHi);
void LookUp_S8_U8( int16_T *pY, const int8_T *pYData, uint16_T u, const uint8_T *pUData, uint8_T DeltaScaling, uint8_T iHi);
void LookUp_S8_S8( int16_T *pY, const int8_T *pYData, int16_T u, const int8_T *pUData, uint8_T DeltaScaling, uint8_T iHi);

void LookUp_U16_U16( uint16_T *pY, const uint16_T *pYData, uint16_T u, const uint16_T *pUData, uint8_T iHi);
void LookUp_U16_S16( uint16_T *pY, const uint16_T *pYData, int16_T u, const int16_T *pUData, uint8_T iHi);
void LookUp_S16_U16( int16_T *pY, const int16_T *pYData, uint16_T u, const uint16_T *pUData, uint8_T iHi);
void LookUp_S16_S16( int16_T *pY, const int16_T *pYData, int16_T u, const int16_T *pUData, uint8_T iHi);
void LookUp_U32_U32( uint32_T *pY, const uint32_T *pYData, uint32_T u, const uint32_T *pUData, uint32_T iHi);
void LookUp_U32_S16( uint32_T *pY, const uint32_T *pYData, int16_T u, const int16_T *pUData, uint8_T iHi);

void PreLookUpIdSearch_U16(uint16_T *pIndex, uint16_T *pRatio, uint16_T inX, const uint16_T *pdataX, const uint8_T dimX);
void PreLookUpIdSearch_S16(uint16_T *pIndex, uint16_T *pRatio,  int16_T inX,  const int16_T *pdataX, const uint8_T dimX);
void PreLookUpIdSearch_U8(uint16_T *pIndex, uint16_T *pRatio, uint16_T inX, const uint8_T *pdataX, uint8_T DeltaScaling, const uint8_T dimX);
void PreLookUpIdSearch_S8(uint16_T *pIndex, uint16_T *pRatio, int16_T inX, const int8_T *pdataX, uint8_T DeltaScaling, const uint8_T dimX);

void INTERP_IR_U8(uint8_T *pOutY, uint8_T yL, uint8_T yR, uint16_T RatioX);
void INTERP_IR_S8(int8_T *pOutY, int8_T yL, int8_T yR, uint16_T RatioX);
void INTERP_IR_U16(uint16_T *pOutY, uint16_T yL, uint16_T yR, uint16_T RatioX);
void INTERP_IR_S16( int16_T *pOutY,  int16_T yL,  int16_T yR, uint16_T RatioX);

void LookUp_IR_U8(uint16_T *pOutZ, const uint8_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX);
void LookUp_IR_S8(int16_T *pOutZ, const int8_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX);
void LookUp_IR_U16(uint16_T *pOutZ, const uint16_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX);
void LookUp_IR_S16( int16_T *pOutZ, const  int16_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX);

void Look2D_IR_U8(uint16_T *pOutZ, const uint8_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX, uint16_T IdY, uint16_T RatioY, uint8_T DimY);
void Look2D_IR_S8(int16_T *pOutZ, const int8_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX, uint16_T IdY, uint16_T RatioY, uint8_T DimY);
void Look2D_IR_U16(uint16_T *pOutZ, const uint16_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX, uint16_T IdY, uint16_T RatioY, uint8_T DimY);
void Look2D_IR_S16( int16_T *pOutZ, const  int16_T *TbZ, uint16_T IdX, uint16_T RatioX, uint8_T DimX, uint16_T IdY, uint16_T RatioY, uint8_T DimY);

void FOF_Reset_S16_FXP(int16_T * Y, int32_T * Y_hires, int16_T U, uint16_T KFILT, int16_T RESETVALUE, uint8_T EVRESET, int32_T Y_hires_old);
void SigStab(uint8_T *OutFlagStab, uint8_T *State, uint16_T *Signal_init, uint16_T *Timer, uint16_T Signal, uint8_T Reset, uint16_T DSigThr, uint16_T NCStab, uint8_T State_old, uint16_T Signal_init_old, uint16_T Timer_old);
void RateLimiter_S16(int16_T *output, int16_T input, int16_T output_old, int16_T min_rate, int16_T max_rate);
void RateLimiter_S32(int32_T *output, int32_T input, int32_T output_old, int32_T min_rate, int32_T max_rate);
void RatioCalcU16(uint16_T * ratio, uint16_T num, uint16_T den, uint16_T max);
void RatioCalcS16(int16_T * ratio, int16_T num, int16_T den, int16_T max);
void MovAvgFilter_S16(int16_T *output, int32_T *out_hr, int16_T *buffer, uint8_T *index, int16_T input, uint8_T num, uint8_T num_max, uint8_T reset, int32_T out_hr_old, int16_T *buffer_old, uint8_T index_old);
void MovAvgFilter_U16(uint16_T *output, int32_T *out_hr, uint16_T *buffer, uint8_T *index, uint16_T input, uint8_T num, uint8_T num_max, uint8_T reset, int32_T out_hr_old, uint16_T *buffer_old, uint8_T index_old);
void SteadyStateDetect(uint8_T *OutFlagStab, uint8_T *State, uint16_T *Signal_init, uint16_T *Timer, uint16_T Signal, uint8_T Reset, uint16_T DSigThr, uint16_T NCStab, uint8_T State_old, uint16_T Signal_init_old, uint16_T Timer_old);
void InsertionSort(uint16_t *buffer, uint8_t arraySize);

#endif  /* _MATHLIB_H_ */
