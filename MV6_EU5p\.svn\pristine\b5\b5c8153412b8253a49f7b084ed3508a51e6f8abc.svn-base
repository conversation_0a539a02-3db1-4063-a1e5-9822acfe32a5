/*
 * File: lightoffmgm_out.h
 *
 * Code generated for Simulink model 'LightOffMgm'.
 *
 * Model version                  : 1.92
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed May  3 14:06:57 2023
 */

#ifndef RTW_HEADER_lightoffmgm_out_h_
#define RTW_HEADER_lightoffmgm_out_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint16_T DeltaEffLOff;

/* Delta Efficiency */
extern uint8_T FlgLOff;

/* EnLOff */
extern uint16_T GnSALoff;

/* Gain for modulating ligth off */
extern int16_T SALOff;

/* Lightoff offset on base SA */
#endif                                 /* RTW_HEADER_lightoffmgm_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
