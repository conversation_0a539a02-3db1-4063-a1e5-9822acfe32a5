/*
 * File: TrqDrivMgm_private.h
 *
 * Code generated for Simulink model 'TrqDrivMgm'.
 *
 * Model version                  : 1.2254
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Mon May 07 08:46:29 2018
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA-C:2004 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (13), Warnings (4), Error (0)
 */

#ifndef RTW_HEADER_TrqDrivMgm_private_h_
#define RTW_HEADER_TrqDrivMgm_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "pby_mgm.h"
#include "engflag.h"
#include "trq_driver.h"
#include "cmefilter_mgm.h"
#include "syncmgm.h"
#include "mathlib.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error "Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compiler's limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, which will disable the preprocessor word size checks."
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error "Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Imported (extern) block parameters */
extern int16_T CMEDRIVPDRIVITHR;       /* Variable: CMEDRIVPDRIVITHR
                                        * Referenced by: '<S2>/FiltState'
                                        * (CmeDriverPTmp - CmeDriverI) threshold for UP-STAB transition
                                        */
extern int16_T CMEDRIVPSTABTHR;        /* Variable: CMEDRIVPSTABTHR
                                        * Referenced by: '<S2>/FiltState'
                                        * Difference between not filtered and filtered to detect CmiDriverP filter stop
                                        */
extern int16_T CMEDRIVPSTEPTHR;        /* Variable: CMEDRIVPSTEPTHR
                                        * Referenced by: '<S2>/FiltState'
                                        * Difference between not filtered and filtered to detect CmiDriverP filter activation in ECO DRIVE
                                        */
extern uint8_T ENTRQDRIVMGM;           /* Variable: ENTRQDRIVMGM
                                        * Referenced by: '<S2>/FiltState'
                                        * Filter enable calibration flag
                                        */
extern void TrqDrivMgm_Calc(void);
extern void TrqDrivMgm_Init(void);

#endif                                 /* RTW_HEADER_TrqDrivMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
