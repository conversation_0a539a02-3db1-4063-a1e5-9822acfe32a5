/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Application/THROTTLEMODEL/main_trunk/ThrottleModel_ert_rtw/ThrottleModel.c $ */
/* $Description:  $ */
/* $Revision: 5834 $ */
/* $Date: 2014-07-15 18:19:18 +0200 (mar, 15 lug 2014) $ */
/* $Author: LanaL $ */
/*****************************************************************************************************************/
/*
 * File: ThrottleModel.c
 *
 * Real-Time Workshop code generated for Simulink model ThrottleModel.
 *
 * Model version                        : 1.686
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Tue Jul 15 18:16:27 2014
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Tue Jul 15 18:16:28 2014
 */
#include "ThrottleModel.h"
#include "ThrottleModel_private.h"

/* user code (top of source file) */
/* System '<Root>/ThrottleModel' */
#ifdef _BUILD_THROTTLEMODEL_

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_ThrottleModel ThrottleModel_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_ThrottleModel ThrottleModel_U;
uint16_T AngThrModelTarg;              /* Throttle angle target */

/* Output and update for function-call system: '<S1>/T10ms' */
void ThrottleModel_T10ms(void)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_U16_U16_U16;

  /* Outputs for atomic SubSystem: '<S3>/Sec2Ang_Calc' */

  /* Product: '<S6>/Product3' incorporates:
   *  Inport: '<Root>/PresAtmRatio'
   *  Inport: '<Root>/PresObj'
   */
  rtb_Look2D_U16_U16_U16 = (uint16_T)((uint32_T)(PresObj << 15) / (uint32_T)
    PresAtmRatio);

  /* S-Function (Look2D_U16_U16_U16): '<S7>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S6>/TBANGTHRTARG'
   *  Constant: '<S6>/BKPRESANGTHRTARG'
   *  Constant: '<S6>/BKPRESANGTHRTARG_dim'
   *   Inport: '<Root>/RpmF'
   *  Constant: '<S6>/BKRPMANGTHRTARG'
   *  Constant: '<S6>/BKRPMANGTHRTARG_dim'
   */
  Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBANGTHRTARG[0],
                     rtb_Look2D_U16_U16_U16, &BKPRESANGTHRTARG[0],
                     BKPRESANGTHRTARG_dim, RpmF, &BKRPMANGTHRTARG[0],
                     BKRPMANGTHRTARG_dim);

  /* DataStoreWrite: '<S6>/Data Store Write' */
  AngThrModelTarg = rtb_Look2D_U16_U16_U16;

  /* end of Outputs for SubSystem: '<S3>/Sec2Ang_Calc' */
}

/* Output and update for function-call system: '<S1>/Init' */
void ThrottleModel_Init(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write4' incorporates:
   *  Constant: '<S2>/ZERO2'
   */
  AngThrModelTarg = 0U;
}

/* Model step function */
void ThrottleModel_step(void)
{
  {
    uint8_T tmp[2];
    boolean_T tmp_0;
    int32_T tmp_1;

    /* Outputs for atomic SubSystem: '<Root>/ThrottleModel' */

    /* Outputs for trigger SubSystem: '<S1>/fc_ThrottleModel_Init' incorporates:
     *  Inport: '<Root>/ev_NoSync'
     *  Inport: '<Root>/ev_PowerOn'
     *  TriggerPort: '<S5>/Trigger'
     */
    tmp[0] = ThrottleModel_U.ev_PowerOn;
    tmp[1] = ThrottleModel_U.ev_NoSync;
    tmp_0 = false;
    for (tmp_1 = 0; tmp_1 < 2; tmp_1++) {
      tmp_0 = (tmp_0 || ((tmp[tmp_1] > 0) &&
                         (ThrottleModel_PrevZCSigState.fc_ThrottleModel_Init_Trig_ZCE
                          [tmp_1] != POS_ZCSIG)));
    }

    if (tmp_0) {
      /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
       *  SubSystem: '<S1>/Init'
       */
      ThrottleModel_Init();
    }

    ThrottleModel_PrevZCSigState.fc_ThrottleModel_Init_Trig_ZCE[0] =
      ThrottleModel_U.ev_PowerOn > 0 ? POS_ZCSIG : ZERO_ZCSIG;
    ThrottleModel_PrevZCSigState.fc_ThrottleModel_Init_Trig_ZCE[1] =
      ThrottleModel_U.ev_NoSync > 0 ? POS_ZCSIG : ZERO_ZCSIG;

    /* end of Outputs for SubSystem: '<S1>/fc_ThrottleModel_Init' */

    /* Outputs for trigger SubSystem: '<S1>/fc_ThrottleModel_Calc' incorporates:
     *  Inport: '<Root>/ev_10ms'
     *  TriggerPort: '<S4>/Trigger'
     */
    if ((ThrottleModel_U.ev_10ms > 0) &&
        (ThrottleModel_PrevZCSigState.fc_ThrottleModel_Calc_Trig_ZCE !=
         POS_ZCSIG)) {
      /* S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
       *  SubSystem: '<S1>/T10ms'
       */
      ThrottleModel_T10ms();
    }

    ThrottleModel_PrevZCSigState.fc_ThrottleModel_Calc_Trig_ZCE =
      ThrottleModel_U.ev_10ms > 0 ? POS_ZCSIG : ZERO_ZCSIG;

    /* end of Outputs for SubSystem: '<S1>/fc_ThrottleModel_Calc' */

    /* end of Outputs for SubSystem: '<Root>/ThrottleModel' */
  }
}

/* Model initialize function */
void ThrottleModel_initialize(void)
{
  {
    int_T idx;
    for (idx = 0; idx < 2; idx ++) {
      ThrottleModel_PrevZCSigState.fc_ThrottleModel_Init_Trig_ZCE[idx] =
        POS_ZCSIG;
    }
  }

  ThrottleModel_PrevZCSigState.fc_ThrottleModel_Calc_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/ThrottleModel' */
#endif                                 // _BUILD_THROTTLEMODEL_

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
