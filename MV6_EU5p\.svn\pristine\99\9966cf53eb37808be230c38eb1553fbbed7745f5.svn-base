/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _PWRMGM_H_
#define _PWRMGM_H_



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "lamheater_mgm.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

#define REGON               ODE_RegOn
#define ECU_ON              0
#define ENGINE_RUN          1
#define ENGINE_OFF          2
#define WAIT_POWER_LATCH    3
#define POWER_LATCH         4
#define ECU_OFF             5

#define BKTWATPWRLATCH_dim 4


/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

typedef uint8_t typStEcu;

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

extern uint16_T Rpm;
extern uint8_T  KeySignal;
extern int16_T  TWater;

extern typStEcu    StEcu;


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
 extern const uint16_T THRPWRLATCH;


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * PWRMGM_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void PWRMGM_Init(void);

/*--------------------------------------------------------------------------*
 * PWRMGM_T10ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void PWRMGM_T10ms(void);

/*--------------------------------------------------------------------------*
 * PWRMGM_T100ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void PWRMGM_T100ms(void);

/*--------------------------------------------------------------------------*
 * PWRMGM_PowerOff - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void PWRMGM_PowerOff(void);


#endif  // _PWRMGM_H_ 

