#ifndef _IVOR_H_
#define _IVOR_H_


extern  uint8_t     IvorIndex;
extern  uint32_t    SRR0_Value;
extern  uint32_t    SRR1_Value;
extern  uint32_t    CSRR0_Value;
extern  uint32_t    CSRR1_Value;
extern  uint32_t    SPR_ESRValue;
extern  uint32_t    SPR_DEARValue;
extern  uint32_t    SPR_MCSRValue;

extern  void (*IVOR10_UserFunction) (void);
extern  void (*IVOR_Common_ManagerUserFunction) (void);
extern  void (*IVOR2_UserFunction) (void);

void IVOR_Config(void);

/*
** =========================================================================
**     Method      :  InitIVPR
**
**     Description : This method intializes the IVPR register
** =========================================================================
*/
void InitIVPR(void);


/*
** =========================================================================
**     Method      :  InitIVOR0Handler
**
**     Description : This method intializes the IVOR0 register 
** =========================================================================
*/
void InitIVOR0Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR1Handler
**
**     Description : This method intializes the IVOR1 register 
** =========================================================================
*/
void InitIVOR1Handler(void);

/*
** =========================================================================
**     Method      :  InitIVOR2Handler
**
**     Description : This method intializes the IVOR2 register 
** =========================================================================
*/
void InitIVOR2Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR3Handler
**
**     Description : This method intializes the IVOR3 register 
** =========================================================================
*/
void InitIVOR3Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR4Handler
**
**     Description : This method intializes the IVOR4 register 
** =========================================================================
*/
void InitIVOR4Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR5Handler
**
**     Description : This method intializes the IVOR5 register 
** =========================================================================
*/
void InitIVOR5Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR6Handler
**
**     Description : This method intializes the IVOR6 register 
** =========================================================================
*/
void InitIVOR6Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR7Handler
**
**     Description : This method intializes the IVOR7 register 
** =========================================================================
*/
void InitIVOR7Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR8Handler
**
**     Description : This method intializes the IVOR8 register 
** =========================================================================
*/
void InitIVOR8Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR9Handler
**
**     Description : This method intializes the IVOR9 register 
** =========================================================================
*/
void InitIVOR9Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR10Handler
**
**     Description : This method intializes the IVOR10 register 
** =========================================================================
*/
void InitIVOR10Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR11Handler
**
**     Description : This method intializes the IVOR11 register 
** =========================================================================
*/
void InitIVOR11Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR12Handler
**
**     Description : This method intializes the IVOR12 register 
** =========================================================================
*/
void InitIVOR12Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR13Handler
**
**     Description : This method intializes the IVOR13 register 
** =========================================================================
*/
void InitIVOR13Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR14Handler
**
**     Description : This method intializes the IVOR14 register 
** =========================================================================
*/
void InitIVOR14Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR15Handler
**
**     Description : This method intializes the IVOR15 register 
** =========================================================================
*/
void InitIVOR15Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR32Handler
**
**     Description : This method intializes the IVOR32 register 
** =========================================================================
*/
void InitIVOR32Handler(void);



/*
** =========================================================================
**     Method      :  InitIVOR33Handler
**
**     Description : This method intializes the IVOR33 register 
** =========================================================================
*/
void InitIVOR33Handler(void);


/*
** =========================================================================
**     Method      :  InitIVOR34Handler
**
**     Description : This method intializes the IVOR34 register 
** =========================================================================
*/
void InitIVOR34Handler(void);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetESRExceptionType 
**
**    \par Description :
**     Discriminate exceptions that can generate the same interrupt type.
**	   Note: only IVOR2,3,5,6,13,32,33,34 change ESR register
**         
**    \param uint8_t IvorType
**    \param uint32_t* ExcepType
**    \return error code
** ===================================================================
*/
int16_t SYS_GetESRExceptionType(uint8_t IvorType,uint32_t* ExcepType);


/*===========================================================================*/
/**
**    \par Method
**    SYS_GetMCSRExceptionType 
**
**    \par Description :
**     diffentiate among machine check conditions; also indicates whether the
**	   source of a machine check condition is recoverable.
**	   Note: MCSR register is IVOR2 dedicated
**         
**    \param uint32_t* ExcepType
**    \return error code
** ===================================================================
*/
int16_t SYS_GetMCSRExceptionType(uint32_t* ExcepType);


#endif /* _IVOR_H_ */
