/***********************************************************************************/
/* PIT Module                                                                      */
/***********************************************************************************/
/* File:      PIT.h                                                                */
/* Author:    <PERSON><PERSON><PERSON><PERSON> (A<PERSON>)                                            */
/* Last Rev.: 18/06/2009                                                           */ 
/*                                                                                 */
/* Contains Periodic Interrupt Timer API function definitions                      */
/*                                                                                 */
/***********************************************************************************/
#ifndef _PIT_H_
#define _PIT_H_

#include "typedefs.h"
#include "OS_api.h"
#include "TasksDefs.h"

/****************************************************************************
     PIT Peripheral defines 
****************************************************************************/
#define PIT_TMR_0      (0x00)
#define PIT_TMR_1      (0x01)
#define PIT_TMR_2      (0x02)
#define PIT_TMR_3      (0x03)
#define PIT_RTI        (0x04)
/***********************************************************************************/
/* Module types declaration:                                                       */
/***********************************************************************************/


/***********************************************************************************/
/* DEFINITION OF TIMER GROUPS                                                      */
/***********************************************************************************/
typedef enum
{
    PIT_ALL_TIMERS  = 0U,
    PIT_PIT_TIMERS  = 1U,
    PIT_RTI_TIMER   = 2U
    
} t_PIT_TimerGroup;


/***********************************************************************************/
/* DEFINITION OF CHANNELS                                                          */
/***********************************************************************************/
#define     PIT_NUM_CHANNELS    5U

typedef enum
{
    PIT_TIMER_0 = (uint8_t)PIT_TMR_0 ,
    PIT_TIMER_1 = (uint8_t)PIT_TMR_1 ,
    PIT_TIMER_2 = (uint8_t)PIT_TMR_2 ,
    PIT_TIMER_3 = (uint8_t)PIT_TMR_3 ,
    RTI         = (uint8_t)PIT_RTI 
} t_PIT_Timer;


/***********************************************************************************/
/* DEFINITION OF BURSTS                                                            */
/***********************************************************************************/
#define     PIT_NUM_BURSTS      2U

typedef enum
{
    PIT_BURST1  =   0U,
    PIT_BURST2  =   1U
    
} t_PIT_Burst;


/***********************************************************************************/
/* DEFINITION OF TIMER STATES                                                      */
/***********************************************************************************/
typedef enum
{
    PIT_TIMER_STOPPED       =   0U,
    PIT_TIMER_RUNNING       =   1U
    
} t_PIT_TimerStatus;


/***********************************************************************************/
/* DEFINITION OF TIMER ACTIONS                                                     */
/***********************************************************************************/
typedef enum
{
    PIT_TIMER_SET           =   0U,
    PIT_TIMER_SET_AND_START =   1U

} t_PIT_TimerAction;


/***********************************************************************************/
/* DEFINITION OF BURST ACTIONS                                                     */
/***********************************************************************************/
typedef enum
{
    PIT_BURST_SET           =   0U,
    PIT_BURST_SET_AND_START =   1U

} t_PIT_BurstAction;


/***********************************************************************************/
/* DEFINITION OF INTERRUPT TYPES                                                   */
/***********************************************************************************/
typedef enum
{
    PIT_NO_ISR              =   0U,
    PIT_TIMER_ISR           =   1U,
    PIT_BURST_ISR           =   2U
    
} t_PIT_ISRType;


/***********************************************************************************/
/* DEFINITION OF PIT TIMERS/RTI/BURST EQADC TRIGGER SOURCES (eTSELx[0-4] values)   */
/***********************************************************************************/
#define PIT_RTI_EQADC_TRIGGER        1U
#define PIT_PIT0_EQADC_TRIGGER       2U
#define PIT_PIT1_EQADC_TRIGGER       3U
#define PIT_PIT2_EQADC_TRIGGER       4U
#define PIT_PIT3_EQADC_TRIGGER       5U

#define PIT_BURST1_EQADC_TRIGGER     PIT_PIT1_EQADC_TRIGGER
#define PIT_BURST2_EQADC_TRIGGER     PIT_PIT3_EQADC_TRIGGER


/***********************************************************************************/
/* PIT Status structure                                                            */
/* This structure describes the status of the PIT peripheral                       */
/***********************************************************************************/
typedef struct
{

    struct {
        uint8_t         ENABLED:1;
        uint8_t         CONFIGURED:1;
        uint32_t        PERIOD_TIMER; 
        t_PIT_ISRType   INTERRUPT_TYPE;
        TaskType        CONFIGURED_TASK;
    } TIMER [5];
    
    struct {
        uint8_t         ENABLED:1;
        uint8_t         CONFIGURED:1;
        uint32_t        PERIOD_TIMER; 
        uint32_t        BURST_TIMER;
    } BURST[2];

}PIT_STATUS_STRUCT;


/***********************************************************************************/
/* PIT API                                                                         */
/***********************************************************************************/
int16_t PIT_Config(void);
int16_t PIT_Enable( t_PIT_TimerGroup _TimerGroup );
int16_t PIT_Disable( t_PIT_TimerGroup _TimerGroup );
int16_t PIT_EnableTimer( t_PIT_Timer _WhichTimer );
int16_t PIT_DisableTimer( t_PIT_Timer _WhichTimer);
int16_t PIT_SetTimer ( t_PIT_Timer _WhichTimer, uint32_t _USLoadVal, t_PIT_TimerAction _Action, TaskType _ISRTaskID );
int16_t PIT_GetTimer ( t_PIT_Timer  _WhichTimer, uint32_t * _CVal, t_PIT_TimerStatus * _Status);
int16_t PIT_SetBurst ( t_PIT_Burst _BurstID, uint32_t _PulseNumber, uint32_t _USLoadVal, t_PIT_BurstAction _Action);
int16_t PIT_Burst( t_PIT_Burst _BurstID);
int16_t PIT_UnSetBurst( t_PIT_Burst _BurstID );


/***********************************************************************************/
/* Interrupt manager functions                                                     */
/***********************************************************************************/
void PIT_ISR_Mngr_PIT0(void);
void PIT_ISR_Mngr_PIT1(void);
void PIT_ISR_Mngr_PIT2(void);
void PIT_ISR_Mngr_PIT3(void);
void PIT_ISR_Mngr_RTI(void); 


/***********************************************************************************/
/*  End of PIT.h                                                                   */
/***********************************************************************************/
#endif  /* _PIT_H_ */
