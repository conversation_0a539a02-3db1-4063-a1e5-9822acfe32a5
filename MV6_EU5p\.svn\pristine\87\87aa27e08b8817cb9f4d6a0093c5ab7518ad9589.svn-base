/*
 * File: div_s32.c
 *
 * Code generated for Simulink model 'PresTarget'.
 *
 * Model version                  : 1.2279
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Sep 12 14:33:56 2022
 */

#include "rtwtypes.h"
#include "div_s32.h"

int32_T div_s32(int32_T numerator, int32_T denominator)
{
  int32_T quotient;
  uint32_T tempAbsQuotient;
  if (denominator == 0) {
    quotient = (numerator >= 0) ? MAX_int32_T : MIN_int32_T;

    /* Divide by zero handler */
  } else {
    tempAbsQuotient = ((numerator < 0) ? ((~((uint32_T)numerator)) + 1U) :
                       ((uint32_T)numerator)) / ((denominator < 0) ?
      ((~((uint32_T)denominator)) + 1U) : ((uint32_T)denominator));
    quotient = ((numerator < 0) != (denominator < 0)) ? (-((int32_T)
      tempAbsQuotient)) : ((int32_T)tempAbsQuotient);
  }

  return quotient;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
