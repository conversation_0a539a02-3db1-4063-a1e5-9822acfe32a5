/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_EXTIRQ_

#include "typedefs.h"
#include "os_api.h"
#include "extirq.h"
#include "digio.h"
#include "sys.h"

/**************************************************************/
#define NUM_OF_EXTIRQ   16

/**************************************************************/
TaskType ExtIRQ_IsrFuncs[NUM_OF_EXTIRQ] = { -1 };

const uint8_T extirq_pin[NUM_OF_EXTIRQ] =
{
    IRQ0_PCR, 
    IRQ1_PCR,
    IRQ2_PCR,
    IRQ3_PCR,

    IRQ4_PCR,
    IRQ5_PCR, 
    IRQ6_PCR,
    IRQ7_PCR,
    
    IRQ8_PCR, 
    IRQ9_PCR,
    IRQ10_PCR,
    IRQ11_PCR,

    IRQ12_PCR,
    IRQ13_PCR, 
    IRQ14_PCR,
    IRQ15_PCR,
};

/**************************************************************/
void EXTIRQ00_Isr(void);
void EXTIRQ01_Isr(void);
void EXTIRQ02_Isr(void);
void EXTIRQ03_Isr(void);
void EXTIRQ04_15_Isr(void);

/**************************************************************/
int16_t EXTIRQ_Config(void)
{
  SIU.IDFR.B.DFL = EXTIRQ_FILTER_LENGTH;
  SIU.DIRSR.R = 0;
  SIU.EIISR.R = 0;
  
  return NO_ERROR;
}
int16_t EXTIRQ_Init(uint8_t extIrqNum)
{
    if((extIrqNum >= NUM_OF_EXTIRQ)||(extirq_pin[extIrqNum] == 0)
        return EXTIRQ_WRONG_PARAMETER;

    SYS_InPinConfig(extirq_pin[extIrqNum], ALTERNATE_FUNCTION, ENABLE_HYSTERESIS, WEAK_PULL_UP);

  return NO_ERROR;
}

int16_t EXTIRQ_GetLevel(uint8_t extIrqNum, uint8_t *status)
{
    if((extIrqNum >= NUM_OF_EXTIRQ)||(extirq_pin[extIrqNum] == 0)
        return EXTIRQ_WRONG_PARAMETER;

    DIGIO_InGet(extirq_pin[extIrqNum], status);

  
  return NO_ERROR;
}

int16_t EXTIRQ_SetEnableState(uint8_t extIrqNum, uint8_t status)
{
  if((extIrqNum >= NUM_OF_EXTIRQ)||(extirq_pin[extIrqNum] == 0)
    return EXTIRQ_WRONG_PARAMETER;
  
  if(status)
    SIU.DIRER.R |= ((uint32_t)0x1)<<extIrqNum;
  else
    SIU.DIRER.R &= ~(((uint32_t)0x1)<<extIrqNum);
  
  return NO_ERROR;
}

int16_t EXTIRQ_SetInterruptHandler(uint8_t extIrqNum, uint8_t condition, TaskType isrFunc)
{
  if((extIrqNum >= NUM_OF_EXTIRQ)||(extirq_pin[extIrqNum] == 0)
    return EXTIRQ_WRONG_PARAMETER;
  
  switch(condition)
  {
    case EXTIRQ_RISING_EDGE:
    SIU.IREER.R |= ((uint32_t)0x1)<<extIrqNum;
    SIU.IFEER.R &= ~(((uint32_t)0x1)<<extIrqNum);
    break;
    case EXTIRQ_FALLING_EDGE:
    SIU.IREER.R &= ~(((uint32_t)0x1)<<extIrqNum);
    SIU.IFEER.R |= ((uint32_t)0x1)<<extIrqNum;
    break;
    case EXTIRQ_BOTH_EDGES:
    SIU.IREER.R |= ((uint32_t)0x1)<<extIrqNum;
    SIU.IFEER.R |= ((uint32_t)0x1)<<extIrqNum;
    break;
    default:
    break;
  }
  
  ExtIRQ_IsrFuncs[extIrqNum] = isrFunc;
  
  return NO_ERROR;
}

void EXTIRQ00_Isr(void)
{
  SIU.EISR.R = ((uint32_t)0x1);
  ActivateTask(ExtIRQ_IsrFuncs[0]);
}

void EXTIRQ01_Isr(void)
{
  SIU.EISR.R = ((uint32_t)0x1)<<1;
  ActivateTask(ExtIRQ_IsrFuncs[1]);
}

void EXTIRQ02_Isr(void)
{
  SIU.EISR.R = ((uint32_t)0x1)<<2;
  ActivateTask(ExtIRQ_IsrFuncs[2]);
}

void EXTIRQ03_Isr(void)
{
  SIU.EISR.R = ((uint32_t)0x1)<<3;
  ActivateTask(ExtIRQ_IsrFuncs[3]);
}

void EXTIRQ04_15_Isr(void)
{
  uint8_t i;
  for(i=4;i<NUM_OF_EXTIRQ;i++)
    if(SIU.EISR.R & ((uint32_t)0x1)<<i)
    {
      SIU.EISR.R = ((uint32_t)0x1)<<i;
      ActivateTask(ExtIRQ_IsrFuncs[i]);
    }
}



#endif /* _BUILD_EXTIRQ_ */
