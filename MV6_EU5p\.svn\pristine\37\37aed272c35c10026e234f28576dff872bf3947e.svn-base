/*
 * File: Pby_Mgm.h
 *
 * Code generated for Simulink model 'Pby_Mgm'.
 *
 * Model version                  : 1.2249
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Fri May 16 09:49:09 2014
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA-C:2004 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (14), Warnings (3), Error (0)
 */

#ifndef RTW_HEADER_Pby_Mgm_h_
#define RTW_HEADER_Pby_Mgm_h_
#ifndef Pby_Mgm_COMMON_INCLUDES_
# define Pby_Mgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* Pby_Mgm_COMMON_INCLUDES_ */

#include "Pby_Mgm_types.h"

/* Includes for objects with custom storage classes. */
#include "pby_mgm.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

/* Exported data define */
#define ACT_PBY                        ((uint8_T) 4U)
#define COUNT_PBY                      ((uint8_T) 2U)
#define DISABLE_PBY                    ((uint8_T) 0U)
#define EXIT_ACT_PBY                   ((uint8_T) 6U)
#define INIT_PBY                       ((uint8_T) 1U)
#define WAIT_INIT_PBY                  ((uint8_T) 5U)
#define WAIT_WOT_PBY                   ((uint8_T) 3U)

/* Block signals (auto storage) */
typedef struct {
  int16_T Selector;                    /* '<S8>/Selector' */
  uint8_T StPassBy_j;                  /* '<S4>/StPby_Calc' */
  uint8_T CntCyclesPby_l;              /* '<S4>/StPby_Calc' */
  uint8_T DataTypeConversion;          /* '<S8>/Data Type Conversion' */
} BlockIO_Pby_Mgm;

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  uint32_T t_pby;                      /* '<S4>/StPby_Calc' */
  struct {
    uint_T is_c7_Pby_Mgm:2;            /* '<S4>/StPby_Calc' */
    uint_T is_Engine_running:2;        /* '<S4>/StPby_Calc' */
    uint_T is_wc:2;                    /* '<S4>/StPby_Calc' */
    uint_T is_apply:2;                 /* '<S4>/StPby_Calc' */
    uint_T is_active_c7_Pby_Mgm:1;     /* '<S4>/StPby_Calc' */
  } bitsForTID0;
} D_Work_Pby_Mgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
  ZCSigState trig_to_fc_Trig_ZCE;      /* '<S1>/trig_to_fc' */
} PrevZCSigStates_Pby_Mgm;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PowerOff;                 /* '<Root>/ev_PowerOff' */
} ExternalInputs_Pby_Mgm;

/* Block signals (auto storage) */
extern BlockIO_Pby_Mgm Pby_Mgm_B;

/* Block states (auto storage) */
extern D_Work_Pby_Mgm Pby_Mgm_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_Pby_Mgm Pby_Mgm_U;

/* Model entry point functions */
extern void Pby_Mgm_initialize(void);
extern void Pby_Mgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm')    - opens subsystem PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm
 * hilite_system('PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0'
 * '<S1>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm'
 * '<S2>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/Init'
 * '<S3>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/Off'
 * '<S4>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms'
 * '<S5>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/trig_to_fc'
 * '<S6>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/trig_to_fc1'
 * '<S7>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/trig_to_fc2'
 * '<S8>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/FlgStabPassBy_Calc'
 * '<S9>'   : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/StPby_Calc'
 * '<S10>'  : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/FlgStabPassBy_Calc/Compare To Zero'
 * '<S11>'  : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/FlgStabPassBy_Calc/Interval Test Dynamic'
 * '<S12>'  : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/FlgStabPassBy_Calc/Interval Test Dynamic1'
 * '<S13>'  : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/FlgStabPassBy_Calc/Interval Test Dynamic2'
 * '<S14>'  : 'PbyMgm_fxp/PbyMgm/PbyMgm_1_0/Pby_Mgm/T10ms/FlgStabPassBy_Calc/Interval Test Dynamic3'
 */

/*-
 * Requirements for '<Root>': Pby_Mgm
 */
#endif                                 /* RTW_HEADER_Pby_Mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
