/*
 * File: mul_s32_loSR_sat.c
 *
 * Code generated for Simulink model 'CmiSatMgm'.
 *
 * Model version                  : 1.199
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Tue Sep 10 09:05:22 2019
 */

#include "rtwtypes.h"
#include "mul_wide_s32.h"
#include "mul_s32_loSR_sat.h"

int32_T mul_s32_loSR_sat(int32_T a, int32_T b, uint32_T aShift)
{
  int32_T result;
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_s32(a, b, &u32_chi, &u32_clo);
  u32_clo = (u32_chi << (32U - aShift)) | (u32_clo >> aShift);
  u32_chi = (uint32_T)(((int32_T)u32_chi) >> aShift);
  if ((((int32_T)u32_chi) > 0) || ((u32_chi == 0U) && (u32_clo >= 2147483648U)))
  {
    result = MAX_int32_T;
  } else if ((((int32_T)u32_chi) < -1) || ((((int32_T)u32_chi) == -1) &&
              (u32_clo < 2147483648U))) {
    result = MIN_int32_T;
  } else {
    result = (int32_T)u32_clo;
  }

  return result;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
