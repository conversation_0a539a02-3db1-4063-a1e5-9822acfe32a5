/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL$ */
/* $Description:  $ */
/* $Revision$ */
/* $Date$ */
/* $Author$ */
/*****************************************************************************************************************/
/*
 * File: throttle_model.h
 *
 * Real-Time Workshop code generated for Simulink model ThrottleModel.
 *
 * Model version                        : 1.686
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Tue Jul 15 18:16:27 2014
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Tue Jul 15 18:16:28 2014
 */
#ifndef RTW_HEADER_throttle_model_h_
#define RTW_HEADER_throttle_model_h_
#include "rtwtypes.h"

extern uint16_T AngThrModelTarg;       /* Throttle angle target */

#endif                                 /* RTW_HEADER_throttle_model_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
