/*
 * File: ThrottleAdapt.c
 *
 * Code generated for Simulink model 'ThrottleAdapt'.
 *
 * Model version                  : 1.736
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun  8 17:18:28 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "ThrottleAdapt.h"
#include "ThrottleAdapt_private.h"

/* Named constants for Chart: '<S9>/ThrottleAdapt_Sched' */
#define ThrottleAdapt_event_ev_10ms    (2)
#define ThrottleAdapt_event_ev_NoSync  (1)
#define ThrottleAdapt_event_ev_PowerOn (0)

/* user code (top of source file) */
/* System '<Root>/ThrottleAdapt' */
#ifdef _BUILD_THROTTLEADAPT_

/* Block signals (default storage) */
BlockIO_ThrottleAdapt ThrottleAdapt_B;

/* Block states (default storage) */
D_Work_ThrottleAdapt ThrottleAdapt_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_ThrottleAdapt ThrottleAdapt_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_ThrottleAdapt ThrottleAdapt_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T DAngThr;

/* - */
int16_T DAngThrAdp;

/* - */
int16_T DAngThrFilt;

/* - */
int32_T DAngThrFiltHiR;

/* - */
int32_T DAngThrHiR;

/* - */
int32_T DAngThrTmp;

/* - */
int16_T DeltaDAng;

/* - */
uint8_T FlgStabAng;

/* - */
uint8_T FlgStabDAng;

/* - */
uint32_T IDThrottleAdapt;

/* ID Version */
uint16_T IndexAngThr;

/* - */
uint8_T PresCtrlActive;

/* - */
int16_T PresError;

/* - */
int16_T PresErrorOld;

/* - */
int16_T PresErrorTmp;

/* - */
uint8_T PresFilReset;

/* - */
uint16_T PresObjF;

/* - */
uint32_T PresObjFHR;

/* - */
uint16_T RatioAngThr;

/* - */
uint8_T TrigDAngAdapt;

/* - */

/* Forward declaration for local functions */
static void Thro_chartstep_c1_ThrottleAdapt(const int32_T *sfEvent);

/* Output and update for function-call system: '<S9>/ThrottleAdapt_Init' */
void ThrottleAdap_ThrottleAdapt_Init(void)
{
  /* DataStoreWrite: '<S12>/Data Store Write' incorporates:
   *  Constant: '<S12>/PRES_INIT'
   */
  PresObjF = ((uint16_T)PRES_INIT);

  /* DataStoreWrite: '<S12>/Data Store Write1' incorporates:
   *  Constant: '<S12>/PRES_INIT_HR'
   */
  PresObjFHR = PRES_INIT_HR;

  /* DataStoreWrite: '<S12>/Data Store Write2' incorporates:
   *  Constant: '<S12>/ERROR_INIT'
   */
  PresErrorTmp = ((int16_T)ERROR_INIT);

  /* DataStoreWrite: '<S12>/Data Store Write3' incorporates:
   *  Constant: '<S12>/ERROR_INIT1'
   */
  PresError = ((int16_T)ERROR_INIT);

  /* DataStoreWrite: '<S12>/Data Store Write4' incorporates:
   *  Constant: '<S12>/ZERO'
   */
  DAngThrTmp = 0;

  /* DataStoreWrite: '<S12>/Data Store Write5' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  DAngThr = 0;

  /* DataStoreWrite: '<S12>/Data Store Write6' incorporates:
   *  Constant: '<S12>/ONE'
   */
  PresFilReset = 1U;

  /* DataStoreWrite: '<S12>/Data Store Write7' incorporates:
   *  Constant: '<S12>/ZERO2'
   */
  DAngThrHiR = 0;

  /* DataStoreWrite: '<S12>/Data Store Write8' incorporates:
   *  Constant: '<S12>/ERROR_INIT2'
   */
  PresErrorOld = ((int16_T)ERROR_INIT);

  /* Constant: '<S12>/ID_THROTTLEADAPT' */
  IDThrottleAdapt = ID_THROTTLEADAPT;
}

/* Output and update for function-call system: '<S15>/Update_VtDAngThr' */
void ThrottleAdapt_Update_VtDAngThr(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_U16_U16;
  int16_T rtb_Selector;
  int16_T rtb_Product;
  uint16_T rtb_Conversion1[5];
  uint8_T rtb_Conversion3;
  int32_T i;

  /* Selector: '<S19>/Selector' incorporates:
   *  DataStoreRead: '<S19>/Data Store Read1'
   */
  rtb_Selector = VtDAngThrEE[ThrottleAdapt_B.idxOut];

  /* DataTypeConversion: '<S22>/Conversion1' incorporates:
   *  Constant: '<S19>/VTDANGGNAD'
   */
  for (i = 0; i < 5; i++) {
    rtb_Conversion1[i] = (uint16_T)VTDANGGNAD[i];
  }

  /* End of DataTypeConversion: '<S22>/Conversion1' */

  /* DataTypeConversion: '<S22>/Conversion3' incorporates:
   *  Constant: '<S19>/BKDANGDSQ_dim'
   */
  rtb_Conversion3 = (uint8_T)((uint16_T)BKDANGDSQ_dim);

  /* S-Function (LookUp_U16_U16): '<S22>/LookUp_U16_U16' incorporates:
   *  Constant: '<S19>/BKDANGDSQ'
   */
  LookUp_U16_U16( &rtb_LookUp_U16_U16, &rtb_Conversion1[0], ThrottleAdapt_B.ds,
                 &BKDANGDSQ[0], rtb_Conversion3);

  /* Product: '<S19>/Product4' incorporates:
   *  Constant: '<S19>/GNDANGCORRINDAD'
   *  DataStoreRead: '<S19>/Data Store Read1'
   *  DataStoreRead: '<S19>/Data Store Read5'
   *  Selector: '<S19>/Selector'
   *  Sum: '<S19>/Add'
   *  Switch: '<S24>/Switch2'
   */
  VtDAngThrEE[ThrottleAdapt_B.idxOut] = (int16_T)(((int16_T)((int16_T)
    (DAngThrFilt - VtDAngThrEE[ThrottleAdapt_B.idxOut]) << 4) * GNDANGCORRINDAD)
    >> 8);

  /* Product: '<S19>/Product' incorporates:
   *  DataTypeConversion: '<S25>/Conversion'
   *  Switch: '<S24>/Switch2'
   */
  rtb_Product = (int16_T)(((int16_T)rtb_LookUp_U16_U16 *
    VtDAngThrEE[ThrottleAdapt_B.idxOut]) >> 10);

  /* Switch: '<S23>/Switch2' incorporates:
   *  Constant: '<S19>/DANGCORRADMAX'
   *  Constant: '<S19>/DANGCORRADMIN'
   *  RelationalOperator: '<S23>/LowerRelop1'
   *  RelationalOperator: '<S23>/UpperRelop'
   *  Switch: '<S23>/Switch'
   */
  if (rtb_Product > DANGCORRADMAX) {
    DeltaDAng = DANGCORRADMAX;
  } else if (rtb_Product < DANGCORRADMIN) {
    /* Switch: '<S23>/Switch' incorporates:
     *  Constant: '<S19>/DANGCORRADMIN'
     */
    DeltaDAng = DANGCORRADMIN;
  } else {
    DeltaDAng = rtb_Product;
  }

  /* End of Switch: '<S23>/Switch2' */

  /* Sum: '<S19>/Add1' incorporates:
   *  Switch: '<S24>/Switch2'
   */
  VtDAngThrEE[ThrottleAdapt_B.idxOut] = (int16_T)((rtb_Selector << 6) +
    DeltaDAng);

  /* DataTypeConversion: '<S19>/Data Type Conversion' incorporates:
   *  Switch: '<S24>/Switch2'
   */
  rtb_Selector = (int16_T)(VtDAngThrEE[ThrottleAdapt_B.idxOut] >> 6);

  /* Switch: '<S24>/Switch2' incorporates:
   *  Constant: '<S19>/MAXDANGTHRADP'
   *  Constant: '<S19>/MINDANGTHRADP'
   *  RelationalOperator: '<S24>/LowerRelop1'
   *  RelationalOperator: '<S24>/UpperRelop'
   *  Switch: '<S24>/Switch'
   */
  if (rtb_Selector > MAXDANGTHRADP) {
    VtDAngThrEE[ThrottleAdapt_B.idxOut] = MAXDANGTHRADP;
  } else if (rtb_Selector < MINDANGTHRADP) {
    /* Switch: '<S24>/Switch' incorporates:
     *  Constant: '<S19>/MINDANGTHRADP'
     */
    VtDAngThrEE[ThrottleAdapt_B.idxOut] = MINDANGTHRADP;
  } else {
    VtDAngThrEE[ThrottleAdapt_B.idxOut] = rtb_Selector;
  }
}

/* Function for Chart: '<S9>/ThrottleAdapt_Sched' */
static void Thro_chartstep_c1_ThrottleAdapt(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_SigStab_o3_d;
  uint16_T rtb_SigStab_o4_p;
  uint16_T rtb_Conversion1_g;
  int16_T rtb_LookUp_IR_S16;
  uint8_T rtb_SigStab_o2;
  uint8_T rtb_SigStab_o2_i;
  int32_T rtb_FOF_Reset_S16_FXP_o2_j;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  uint16_T rtb_DataTypeConversion_m;
  int16_T rtb_Add_i;
  int16_T rtb_DataStoreRead1_b[7];
  uint8_T rtb_Switch_ev;
  uint16_T rtb_Memory;
  uint16_T rtb_Memory1;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint8_T rtb_DataStoreRead10;
  int32_T i;

  /* Chart: '<S9>/ThrottleAdapt_Sched' incorporates:
   *  Constant: '<S14>/BKANGTHRTARG_dim'
   *  Constant: '<S14>/ONE'
   *  Constant: '<S9>/ENDANGTHRADAPT'
   *  Constant: '<S9>/ENDANGTHRCALC'
   *  Inport: '<Root>/VtRec'
   *  Sum: '<S14>/Add'
   */
  /* During: ThrottleAdapt/ThrottleAdapt_Sched */
  /* Entry Internal: ThrottleAdapt/ThrottleAdapt_Sched */
  /* Transition: '<S13>:1' */
  if (*sfEvent == ThrottleAdapt_event_ev_PowerOn) {
    /* Outputs for Function Call SubSystem: '<S9>/ThrottleAdapt_Init' */
    /* Transition: '<S13>:2' */
    /* Event: '<S13>:26' */
    ThrottleAdap_ThrottleAdapt_Init();

    /* End of Outputs for SubSystem: '<S9>/ThrottleAdapt_Init' */
  } else if (*sfEvent == ThrottleAdapt_event_ev_NoSync) {
    /* Outputs for Function Call SubSystem: '<S9>/ThrottleAdapt_Init' */
    /* Transition: '<S13>:7' */
    /* Event: '<S13>:26' */
    ThrottleAdap_ThrottleAdapt_Init();

    /* End of Outputs for SubSystem: '<S9>/ThrottleAdapt_Init' */
  } else {
    if (*sfEvent == ThrottleAdapt_event_ev_10ms) {
      /* Transition: '<S13>:3' */
      if ((VtRec[REC_DBW_OFF] != 0) || (VtRec[REC_ENG_OFF] != 0) ||
          (VtRec[REC_NO_DBW_SELF] != 0) || (VtRec[REC_ALFA_N] != 0) ||
          (VtRec[REC_FORCE_LH] != 0)) {
        /* Outputs for Function Call SubSystem: '<S9>/ThrottleAdapt_Init' */
        /* Transition: '<S13>:8' */
        /* Event: '<S13>:26' */
        ThrottleAdap_ThrottleAdapt_Init();

        /* End of Outputs for SubSystem: '<S9>/ThrottleAdapt_Init' */

        /* Outputs for Function Call SubSystem: '<S9>/VtDAngThr_Reset' */
        /* Outputs for Iterator SubSystem: '<S14>/For Iterator Subsystem' incorporates:
         *  ForIterator: '<S44>/For Iterator'
         */
        /* Event: '<S13>:30' */
        rtb_Memory1 = (uint16_T)(((uint16_T)BKANGTHRTARG_dim) + 1U);
        if (rtb_Memory1 > 32767) {
          rtb_Memory1 = 32767U;
        }

        rtb_Add_i = (int16_T)rtb_Memory1;
        for (rtb_FOF_Reset_S16_FXP_o1 = 0; rtb_FOF_Reset_S16_FXP_o1 < rtb_Add_i;
             rtb_FOF_Reset_S16_FXP_o1++) {
          /* Assignment: '<S44>/Assignment' incorporates:
           *  Constant: '<S14>/ZERO'
           *  DataStoreWrite: '<S44>/Data Store Write4'
           */
          VtDAngThrEE[rtb_FOF_Reset_S16_FXP_o1] = 0;
        }

        /* End of Outputs for SubSystem: '<S14>/For Iterator Subsystem' */
        /* End of Outputs for SubSystem: '<S9>/VtDAngThr_Reset' */
      } else {
        /* Transition: '<S13>:9' */
        if (ENDANGTHRCALC != 0) {
          /* Outputs for Function Call SubSystem: '<S9>/DAngThr_Calc' */
          /* DataTypeConversion: '<S39>/Conversion5' incorporates:
           *  Inport: '<Root>/PresObj'
           */
          /* Transition: '<S13>:5' */
          /* Event: '<S13>:31' */
          rtb_Add_i = (int16_T)PresObj;

          /* DataTypeConversion: '<S39>/Conversion2' incorporates:
           *  Inport: '<Root>/PresObj'
           */
          rtb_FOF_Reset_S16_FXP_o1 = (int16_T)PresObj;

          /* DataStoreRead: '<S32>/Data Store Read10' */
          rtb_DataStoreRead10 = PresFilReset;

          /* DataTypeConversion: '<S39>/Conversion4' incorporates:
           *  DataStoreRead: '<S32>/Data Store Read9'
           */
          rtb_FOF_Reset_S16_FXP_o2 = (int32_T)PresObjFHR;

          /* S-Function (FOF_Reset_S16_FXP): '<S39>/FOF_Reset_S16_FXP' incorporates:
           *  Constant: '<S32>/KFILTPRESOBJ'
           */
          FOF_Reset_S16_FXP( &rtb_Add_i, &rtb_FOF_Reset_S16_FXP_o2, rtb_Add_i,
                            KFILTPRESOBJ, rtb_FOF_Reset_S16_FXP_o1,
                            rtb_DataStoreRead10, rtb_FOF_Reset_S16_FXP_o2);

          /* DataTypeConversion: '<S41>/Conversion' incorporates:
           *  DataStoreWrite: '<S32>/Data Store Write'
           */
          PresObjF = (uint16_T)rtb_Add_i;

          /* Sum: '<S32>/pressure error' incorporates:
           *  DataStoreWrite: '<S32>/Data Store Write'
           *  DataStoreWrite: '<S32>/Data Store Write2'
           *  Inport: '<Root>/PresIntake'
           */
          PresErrorTmp = (int16_T)((int16_T)PresObjF - (int16_T)PresIntake);

          /* MinMax: '<S32>/MinMax1' incorporates:
           *  Constant: '<S32>/MINPRESERR'
           *  DataStoreWrite: '<S32>/Data Store Write2'
           */
          if (PresErrorTmp > MINPRESERR) {
            rtb_FOF_Reset_S16_FXP_o1 = PresErrorTmp;
          } else {
            rtb_FOF_Reset_S16_FXP_o1 = MINPRESERR;
          }

          /* End of MinMax: '<S32>/MinMax1' */

          /* MinMax: '<S32>/MinMax' incorporates:
           *  Constant: '<S32>/MAXPRESERR'
           */
          if (MAXPRESERR < rtb_FOF_Reset_S16_FXP_o1) {
            rtb_FOF_Reset_S16_FXP_o1 = MAXPRESERR;
          }

          /* End of MinMax: '<S32>/MinMax' */

          /* DataTypeConversion: '<S40>/Data Type Conversion4' */
          rtb_FOF_Reset_S16_FXP_o2_j = rtb_FOF_Reset_S16_FXP_o1;

          /* S-Function (GenAbs): '<S40>/GenAbs' incorporates:
           *  Constant: '<S32>/INT16_TYPE'
           */
          rtb_FOF_Reset_S16_FXP_o2_j = GenAbs( rtb_FOF_Reset_S16_FXP_o2_j,
            INT16_TYPE);

          /* DataTypeConversion: '<S42>/Conversion' */
          rtb_Add_i = (int16_T)rtb_FOF_Reset_S16_FXP_o2_j;

          /* Switch: '<S32>/Switch' incorporates:
           *  Constant: '<S32>/PRESERRDB'
           *  Constant: '<S32>/ZERO'
           *  DataStoreWrite: '<S11>/Data Store Write3'
           *  RelationalOperator: '<S32>/Relational Operator'
           */
          if (rtb_Add_i > PRESERRDB) {
            PresError = rtb_FOF_Reset_S16_FXP_o1;
          } else {
            PresError = 0;
          }

          /* End of Switch: '<S32>/Switch' */

          /* Logic: '<S33>/Logical Operator' incorporates:
           *  Inport: '<Root>/FlgAngThrObjSat'
           *  Inport: '<Root>/FlgMLStr'
           *  Inport: '<Root>/TbQAirSelect'
           *  Inport: '<Root>/TrqStartFlg'
           *  Logic: '<S33>/Logical Operator1'
           *  Logic: '<S33>/Logical Operator2'
           *  RelationalOperator: '<S43>/Compare'
           */
          PresCtrlActive = (uint8_T)((FlgAngThrObjSat == 0) && (TrqStartFlg != 0)
            && (FlgMLStr == 0) && (TbQAirSelect == 16384));

          /* Switch: '<S33>/Switch' incorporates:
           *  DataStoreRead: '<S33>/Data Store Read11'
           *  Switch: '<S33>/Switch1'
           */
          if (PresCtrlActive != 0) {
            /* Sum: '<S33>/Sum2' incorporates:
             *  Constant: '<S33>/KIDANGTHR'
             *  DataStoreWrite: '<S11>/Data Store Write3'
             *  Product: '<S33>/Product1'
             */
            rtb_FOF_Reset_S16_FXP_o2_j = KIDANGTHR * PresError;

            /* Sum: '<S33>/Sum1' incorporates:
             *  DataStoreRead: '<S33>/Data Store Read8'
             *  DataStoreWrite: '<S11>/Data Store Write3'
             */
            i = PresError - PresErrorOld;
            if (i > 32767) {
              i = 32767;
            } else {
              if (i < -32768) {
                i = -32768;
              }
            }

            /* Sum: '<S33>/Sum2' incorporates:
             *  Constant: '<S33>/KPDANGTHR'
             *  Product: '<S33>/Product2'
             *  Sum: '<S33>/Sum1'
             */
            i *= KPDANGTHR;
            if ((rtb_FOF_Reset_S16_FXP_o2_j < 0) && (i < MIN_int32_T
                 - rtb_FOF_Reset_S16_FXP_o2_j)) {
              rtb_FOF_Reset_S16_FXP_o2_j = MIN_int32_T;
            } else if ((rtb_FOF_Reset_S16_FXP_o2_j > 0) && (i > MAX_int32_T
                        - rtb_FOF_Reset_S16_FXP_o2_j)) {
              rtb_FOF_Reset_S16_FXP_o2_j = MAX_int32_T;
            } else {
              rtb_FOF_Reset_S16_FXP_o2_j += i;
            }

            /* Sum: '<S33>/Sum4' incorporates:
             *  DataStoreRead: '<S33>/Data Store Read11'
             *  DataStoreWrite: '<S11>/Data Store Write4'
             *  Sum: '<S33>/Sum2'
             */
            if ((rtb_FOF_Reset_S16_FXP_o2_j < 0) && (DAngThrHiR < MIN_int32_T
                 - rtb_FOF_Reset_S16_FXP_o2_j)) {
              DAngThrTmp = MIN_int32_T;
            } else if ((rtb_FOF_Reset_S16_FXP_o2_j > 0) && (DAngThrHiR >
                        MAX_int32_T - rtb_FOF_Reset_S16_FXP_o2_j)) {
              DAngThrTmp = MAX_int32_T;
            } else {
              DAngThrTmp = rtb_FOF_Reset_S16_FXP_o2_j + DAngThrHiR;
            }

            /* End of Sum: '<S33>/Sum4' */
          } else if (DAngThrHiR >= rtCP_Switch1_Threshold) {
            /* Sum: '<S33>/Sum3' incorporates:
             *  Constant: '<S33>/DANGTHRSTEP'
             *  DataStoreRead: '<S33>/Data Store Read11'
             *  DataTypeConversion: '<S33>/Data Type Conversion'
             *  Switch: '<S33>/Switch1'
             */
            i = DANGTHRSTEP << 12;
            if (DAngThrHiR < i + MIN_int32_T) {
              rtb_FOF_Reset_S16_FXP_o2_j = MIN_int32_T;
            } else {
              rtb_FOF_Reset_S16_FXP_o2_j = DAngThrHiR - i;
            }

            /* End of Sum: '<S33>/Sum3' */

            /* MinMax: '<S33>/MinMax1' incorporates:
             *  Switch: '<S33>/Switch1'
             */
            if (rtb_FOF_Reset_S16_FXP_o2_j > 0) {
              /* DataStoreWrite: '<S11>/Data Store Write4' */
              DAngThrTmp = rtb_FOF_Reset_S16_FXP_o2_j;
            } else {
              /* DataStoreWrite: '<S11>/Data Store Write4' */
              DAngThrTmp = 0;
            }

            /* End of MinMax: '<S33>/MinMax1' */
          } else {
            /* Sum: '<S33>/Sum5' incorporates:
             *  Constant: '<S33>/DANGTHRSTEP'
             *  DataStoreRead: '<S33>/Data Store Read11'
             *  DataTypeConversion: '<S33>/Data Type Conversion'
             *  Switch: '<S33>/Switch1'
             */
            rtb_FOF_Reset_S16_FXP_o2_j = DANGTHRSTEP << 12;
            if (DAngThrHiR > MAX_int32_T - rtb_FOF_Reset_S16_FXP_o2_j) {
              rtb_FOF_Reset_S16_FXP_o2_j = MAX_int32_T;
            } else {
              rtb_FOF_Reset_S16_FXP_o2_j += DAngThrHiR;
            }

            /* End of Sum: '<S33>/Sum5' */

            /* MinMax: '<S33>/MinMax' incorporates:
             *  Switch: '<S33>/Switch1'
             */
            if (0 < rtb_FOF_Reset_S16_FXP_o2_j) {
              /* DataStoreWrite: '<S11>/Data Store Write4' */
              DAngThrTmp = 0;
            } else {
              /* DataStoreWrite: '<S11>/Data Store Write4' */
              DAngThrTmp = rtb_FOF_Reset_S16_FXP_o2_j;
            }

            /* End of MinMax: '<S33>/MinMax' */
          }

          /* End of Switch: '<S33>/Switch' */

          /* MinMax: '<S31>/MinMax3' incorporates:
           *  Constant: '<S31>/MINDANGTHR'
           *  DataStoreWrite: '<S11>/Data Store Write4'
           */
          i = MINDANGTHR << 12;
          if (DAngThrTmp > i) {
            rtb_FOF_Reset_S16_FXP_o2_j = DAngThrTmp;
          } else {
            rtb_FOF_Reset_S16_FXP_o2_j = i;
          }

          /* End of MinMax: '<S31>/MinMax3' */

          /* MinMax: '<S31>/MinMax2' incorporates:
           *  Constant: '<S31>/MAXDANGTHR'
           *  DataStoreWrite: '<S31>/Data Store Write7'
           */
          i = MAXDANGTHR << 12;
          if (i < rtb_FOF_Reset_S16_FXP_o2_j) {
            DAngThrHiR = i;
          } else {
            DAngThrHiR = rtb_FOF_Reset_S16_FXP_o2_j;
          }

          /* End of MinMax: '<S31>/MinMax2' */

          /* DataTypeConversion: '<S31>/Data Type Conversion' incorporates:
           *  DataStoreWrite: '<S31>/Data Store Write7'
           */
          rtb_FOF_Reset_S16_FXP_o1 = (int16_T)(DAngThrHiR >> 12);

          /* DataStoreRead: '<S31>/Data Store Read1' */
          for (i = 0; i < 7; i++) {
            rtb_DataStoreRead1_b[i] = VtDAngThrEE[i];
          }

          /* End of DataStoreRead: '<S31>/Data Store Read1' */

          /* DataTypeConversion: '<S36>/Data Type Conversion8' incorporates:
           *  Constant: '<S31>/BKANGTHRTARG_dim'
           */
          rtb_DataStoreRead10 = (uint8_T)((uint16_T)BKANGTHRTARG_dim);

          /* S-Function (PreLookUpIdSearch_U16): '<S36>/PreLookUpIdSearch_U16' incorporates:
           *  Constant: '<S31>/BKANGTHRTARG'
           *  Inport: '<Root>/AngThrModelTarg'
           */
          PreLookUpIdSearch_U16( (&(IndexAngThr)), &rtb_Conversion1_g,
                                AngThrModelTarg, &BKANGTHRTARG[0],
                                rtb_DataStoreRead10);

          /* DataTypeConversion: '<S36>/Data Type Conversion1' */
          RatioAngThr = rtb_Conversion1_g;

          /* DataTypeConversion: '<S35>/Conversion3' incorporates:
           *  Constant: '<S31>/BKANGTHRTARG_dim'
           */
          rtb_DataStoreRead10 = (uint8_T)((uint16_T)BKANGTHRTARG_dim);

          /* S-Function (LookUp_IR_S16): '<S35>/LookUp_IR_S16' */
          LookUp_IR_S16( &rtb_Add_i, &rtb_DataStoreRead1_b[0], IndexAngThr,
                        RatioAngThr, rtb_DataStoreRead10);

          /* DataTypeConversion: '<S38>/Conversion' */
          DAngThrAdp = rtb_Add_i;

          /* Sum: '<S31>/Add' */
          rtb_Add_i = (int16_T)(rtb_FOF_Reset_S16_FXP_o1 + DAngThrAdp);

          /* DataTypeConversion: '<S34>/Conversion1' incorporates:
           *  Constant: '<S31>/KFILTDANGTHR'
           */
          rtb_Conversion1_g = (uint16_T)KFILTDANGTHR;

          /* DataStoreRead: '<S31>/Data Store Read8' */
          i = DAngThrFiltHiR;

          /* S-Function (FOF_Reset_S16_FXP): '<S34>/FOF_Reset_S16_FXP' incorporates:
           *  Constant: '<S31>/RESET_EVENT'
           *  Constant: '<S31>/RESET_VAL'
           */
          FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1,
                            &rtb_FOF_Reset_S16_FXP_o2_j, rtb_Add_i,
                            rtb_Conversion1_g, rtCP_RESET_VAL_Value,
                            rtCP_RESET_EVENT_Value, i);

          /* DataStoreWrite: '<S31>/Data Store Write1' */
          DAngThrFilt = rtb_FOF_Reset_S16_FXP_o1;

          /* DataStoreWrite: '<S31>/Data Store Write2' */
          DAngThrFiltHiR = rtb_FOF_Reset_S16_FXP_o2_j;

          /* DataStoreWrite: '<S31>/Data Store Write5' */
          DAngThr = rtb_Add_i;

          /* DataTypeConversion: '<S39>/Conversion7' incorporates:
           *  DataStoreWrite: '<S32>/Data Store Write1'
           */
          PresObjFHR = (uint32_T)rtb_FOF_Reset_S16_FXP_o2;

          /* Memory: '<S33>/Memory' incorporates:
           *  DataStoreWrite: '<S33>/Data Store Write1'
           */
          PresErrorOld = ThrottleAdapt_DWork.Memory_PreviousInput;

          /* DataStoreWrite: '<S11>/Data Store Write6' incorporates:
           *  Constant: '<S11>/ZERO'
           */
          PresFilReset = 0U;

          /* Update for Memory: '<S33>/Memory' incorporates:
           *  DataStoreWrite: '<S11>/Data Store Write3'
           */
          ThrottleAdapt_DWork.Memory_PreviousInput = PresError;

          /* End of Outputs for SubSystem: '<S9>/DAngThr_Calc' */
          if (ENDANGTHRADAPT != 0) {
            /* Outputs for Function Call SubSystem: '<S9>/DAngThr_Adapt' */
            /* DataTypeConversion: '<S29>/Data Type Conversion4' incorporates:
             *  DataStoreRead: '<S16>/Data Store Read5'
             */
            /* Transition: '<S13>:4' */
            /* Event: '<S13>:32' */
            rtb_FOF_Reset_S16_FXP_o2 = DAngThr;

            /* S-Function (GenAbs): '<S29>/GenAbs' incorporates:
             *  Constant: '<S16>/INT16_TYPE'
             */
            rtb_FOF_Reset_S16_FXP_o2 = GenAbs( rtb_FOF_Reset_S16_FXP_o2,
              INT16_TYPE);

            /* DataTypeConversion: '<S30>/Conversion' */
            rtb_Add_i = (int16_T)rtb_FOF_Reset_S16_FXP_o2;

            /* RelationalOperator: '<S16>/Relational Operator' incorporates:
             *  Constant: '<S16>/MINPOSDANGADP'
             */
            rtb_DataStoreRead10 = (uint8_T)(rtb_Add_i >= MINPOSDANGADP);

            /* Logic: '<S16>/Logical Operator1' incorporates:
             *  Inport: '<Root>/TbQAirSelect'
             *  Logic: '<S16>/Logical Operator2'
             *  RelationalOperator: '<S27>/Compare'
             *  UnitDelay: '<S16>/Unit Delay2'
             */
            rtb_Switch_ev = (uint8_T)((rtb_DataStoreRead10 != 0) &&
              (PresCtrlActive != 0) && (TbQAirSelect == 16384) &&
              (ThrottleAdapt_DWork.UnitDelay2_DSTATE == 0));

            /* Switch: '<S16>/Switch1' incorporates:
             *  Constant: '<S16>/FIRST_TIME'
             *  UnitDelay: '<S16>/Unit Delay'
             */
            if (rtb_Switch_ev != 0) {
              rtb_DataStoreRead10 = ThrottleAdapt_DWork.UnitDelay_DSTATE;
            } else {
              rtb_DataStoreRead10 = 0U;
            }

            /* End of Switch: '<S16>/Switch1' */

            /* Memory: '<S26>/Memory1' */
            rtb_Memory1 = ThrottleAdapt_DWork.Memory1_PreviousInput;

            /* Memory: '<S26>/Memory' */
            rtb_DataTypeConversion_m =
              ThrottleAdapt_DWork.Memory_PreviousInput_j;

            /* S-Function (SigStab): '<S26>/SigStab' incorporates:
             *  Constant: '<S16>/NTASKANGTHRSTAB'
             *  Constant: '<S16>/THANGTHRMODEL'
             *  Constant: '<S16>/ZERO'
             *  Inport: '<Root>/AngThrModelTarg'
             */
            SigStab( (&(FlgStabAng)), &rtb_SigStab_o2, &rtb_SigStab_o3,
                    &rtb_SigStab_o4, AngThrModelTarg, rtCP_ZERO_Value_i,
                    THANGTHRMODEL, NTASKANGTHRSTAB, rtb_DataStoreRead10,
                    rtb_Memory1, rtb_DataTypeConversion_m);

            /* DataTypeConversion: '<S16>/Data Type Conversion' */
            rtb_DataTypeConversion_m = (uint16_T)rtb_Add_i;

            /* Switch: '<S16>/Switch' incorporates:
             *  UnitDelay: '<S16>/Unit Delay1'
             */
            if (rtb_Switch_ev != 0) {
              rtb_Switch_ev = ThrottleAdapt_DWork.UnitDelay1_DSTATE;
            }

            /* End of Switch: '<S16>/Switch' */

            /* Memory: '<S28>/Memory1' */
            rtb_Memory1 = ThrottleAdapt_DWork.Memory1_PreviousInput_g;

            /* Memory: '<S28>/Memory' */
            rtb_Memory = ThrottleAdapt_DWork.Memory_PreviousInput_e;

            /* S-Function (SigStab): '<S28>/SigStab' incorporates:
             *  Constant: '<S16>/NTASKDANGTHRSTAB'
             *  Constant: '<S16>/THDANGTHR'
             *  Constant: '<S16>/ZERO'
             */
            SigStab( (&(FlgStabDAng)), &rtb_SigStab_o2_i, &rtb_SigStab_o3_d,
                    &rtb_SigStab_o4_p, rtb_DataTypeConversion_m,
                    rtCP_ZERO_Value_i, THDANGTHR, NTASKDANGTHRSTAB,
                    rtb_Switch_ev, rtb_Memory1, rtb_Memory);

            /* Logic: '<S16>/Logical Operator' */
            TrigDAngAdapt = (uint8_T)((FlgStabAng != 0) && (FlgStabDAng != 0));

            /* If: '<S10>/If' incorporates:
             *  Constant: '<S15>/BKANGTHRTARG_dim'
             */
            if (TrigDAngAdapt != 0) {
              /* Outputs for IfAction SubSystem: '<S10>/Adaptivity' incorporates:
               *  ActionPort: '<S15>/Action Port'
               */
              /* DataTypeConversion: '<S15>/Data Type Conversion' */
              rtb_Memory1 = (uint16_T)((uint32_T)RatioAngThr >> 1);

              /* Gateway: ThrottleAdapt/DAngThr_Adapt/Adaptivity/Calc_indices_ad */
              /* During: ThrottleAdapt/DAngThr_Adapt/Adaptivity/Calc_indices_ad */
              /* Entry Internal: ThrottleAdapt/DAngThr_Adapt/Adaptivity/Calc_indices_ad */
              /* Transition: '<S17>:1' */
              ThrottleAdapt_B.idxOut = IndexAngThr;
              ThrottleAdapt_B.ds = rtb_Memory1;

              /* Chart: '<S15>/Calc_indices_ad' incorporates:
               *  SubSystem: '<S15>/Update_VtDAngThr'
               */
              /* Event: '<S17>:13' */
              ThrottleAdapt_Update_VtDAngThr();
              if (IndexAngThr < ((uint16_T)BKANGTHRTARG_dim)) {
                /* Transition: '<S17>:2' */
                ThrottleAdapt_B.idxOut = (uint16_T)(IndexAngThr + 1);
                ThrottleAdapt_B.ds = (uint16_T)(32768 - rtb_Memory1);

                /* Outputs for Function Call SubSystem: '<S15>/Update_VtDAngThr' */
                /* Event: '<S17>:13' */
                ThrottleAdapt_Update_VtDAngThr();

                /* End of Outputs for SubSystem: '<S15>/Update_VtDAngThr' */
              } else {
                /* Transition: '<S17>:3' */
              }

              /* Chart: '<S15>/Calc_indices_ad' incorporates:
               *  SubSystem: '<S15>/ControlReset'
               */
              /* Transition: '<S17>:4' */
              /* Event: '<S17>:14' */
              for (i = 0; i < 7; i++) {
                /* DataStoreRead: '<S18>/Data Store Read1' */
                rtb_DataStoreRead1_b[i] = VtDAngThrEE[i];
              }

              /* DataTypeConversion: '<S20>/Conversion3' incorporates:
               *  Constant: '<S15>/BKANGTHRTARG_dim'
               *  Constant: '<S18>/BKANGTHRTARG_dim'
               */
              rtb_DataStoreRead10 = (uint8_T)((uint16_T)BKANGTHRTARG_dim);

              /* S-Function (LookUp_IR_S16): '<S20>/LookUp_IR_S16' */
              LookUp_IR_S16( &rtb_LookUp_IR_S16, &rtb_DataStoreRead1_b[0],
                            IndexAngThr, RatioAngThr, rtb_DataStoreRead10);

              /* DataStoreWrite: '<S18>/Data Store Write3' incorporates:
               *  DataStoreRead: '<S18>/Data Store Read2'
               *  DataTypeConversion: '<S18>/Data Type Conversion'
               *  Sum: '<S18>/Add'
               *  Sum: '<S18>/Add1'
               */
              DAngThrHiR = ((int16_T)(DAngThrAdp - rtb_LookUp_IR_S16) << 12) +
                DAngThrHiR;

              /* End of Outputs for SubSystem: '<S10>/Adaptivity' */
            }

            /* End of If: '<S10>/If' */

            /* Update for UnitDelay: '<S16>/Unit Delay' */
            ThrottleAdapt_DWork.UnitDelay_DSTATE = rtb_SigStab_o2;

            /* Update for UnitDelay: '<S16>/Unit Delay2' */
            ThrottleAdapt_DWork.UnitDelay2_DSTATE = TrigDAngAdapt;

            /* Update for Memory: '<S26>/Memory1' */
            ThrottleAdapt_DWork.Memory1_PreviousInput = rtb_SigStab_o3;

            /* Update for Memory: '<S26>/Memory' */
            ThrottleAdapt_DWork.Memory_PreviousInput_j = rtb_SigStab_o4;

            /* Update for UnitDelay: '<S16>/Unit Delay1' */
            ThrottleAdapt_DWork.UnitDelay1_DSTATE = rtb_SigStab_o2_i;

            /* Update for Memory: '<S28>/Memory1' */
            ThrottleAdapt_DWork.Memory1_PreviousInput_g = rtb_SigStab_o3_d;

            /* Update for Memory: '<S28>/Memory' */
            ThrottleAdapt_DWork.Memory_PreviousInput_e = rtb_SigStab_o4_p;

            /* End of Outputs for SubSystem: '<S9>/DAngThr_Adapt' */
          } else {
            /* Transition: '<S13>:10' */
          }
        } else {
          /* Transition: '<S13>:6' */
        }
      }
    }
  }

  /* End of Chart: '<S9>/ThrottleAdapt_Sched' */
}

/* Model step function */
void ThrottleAdapt_step(void)
{
  boolean_T zcEvent[3];
  int8_T rtb_inputevents[3];
  int32_T i;
  boolean_T zcEvent_0;

  /* Outputs for Atomic SubSystem: '<Root>/ThrottleAdapt' */

  /* Chart: '<S9>/ThrottleAdapt_Sched' incorporates:
   *  TriggerPort: '<S13>/ input events '
   */
  /* Inport: '<Root>/ev_PowerOn' */
  zcEvent[0] = ((ThrottleAdapt_U.ev_PowerOn > 0) &&
                (ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[0] !=
                 POS_ZCSIG));

  /* Inport: '<Root>/ev_NoSync' */
  zcEvent[1] = ((ThrottleAdapt_U.ev_NoSync > 0) &&
                (ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[1] !=
                 POS_ZCSIG));

  /* Inport: '<Root>/ev_10ms' */
  zcEvent[2] = ((ThrottleAdapt_U.ev_10ms > 0) &&
                (ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[2] !=
                 POS_ZCSIG));
  zcEvent_0 = false;
  for (i = 0; i < 3; i++) {
    zcEvent_0 = (zcEvent_0 || zcEvent[i]);
  }

  if (zcEvent_0) {
    for (i = 0; i < 3; i++) {
      rtb_inputevents[i] = (int8_T)zcEvent[i];
    }

    /* Gateway: ThrottleAdapt/ThrottleAdapt_Sched */
    if (rtb_inputevents[0U] == 1) {
      /* Event: '<S13>:27' */
      i = ThrottleAdapt_event_ev_PowerOn;
      Thro_chartstep_c1_ThrottleAdapt(&i);
    }

    if (rtb_inputevents[1U] == 1) {
      /* Event: '<S13>:28' */
      i = ThrottleAdapt_event_ev_NoSync;
      Thro_chartstep_c1_ThrottleAdapt(&i);
    }

    if (rtb_inputevents[2U] == 1) {
      /* Event: '<S13>:29' */
      i = ThrottleAdapt_event_ev_10ms;
      Thro_chartstep_c1_ThrottleAdapt(&i);
    }
  }

  /* Inport: '<Root>/ev_PowerOn' */
  ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[0] = (ZCSigState)
    (ThrottleAdapt_U.ev_PowerOn > 0);

  /* Inport: '<Root>/ev_NoSync' */
  ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[1] = (ZCSigState)
    (ThrottleAdapt_U.ev_NoSync > 0);

  /* Inport: '<Root>/ev_10ms' */
  ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[2] = (ZCSigState)
    (ThrottleAdapt_U.ev_10ms > 0);

  /* End of Outputs for SubSystem: '<Root>/ThrottleAdapt' */
}

/* Model initialize function */
void ThrottleAdapt_initialize(void)
{
  {
    int32_T i;
    for (i = 0; i < 3; i++) {
      ThrottleAdapt_PrevZCSigState.ThrottleAdapt_Sched_Trig_ZCE[i] = POS_ZCSIG;
    }
  }
}

/* user code (bottom of source file) */
/* System '<Root>/ThrottleAdapt' */
void ThrottleAdapt_Init(void)
{
  ThrottleAdapt_U.ev_PowerOn = 0;
  ThrottleAdapt_U.ev_NoSync = 0;
  ThrottleAdapt_U.ev_10ms = 0;
  ThrottleAdapt_step();
  ThrottleAdapt_U.ev_PowerOn = 1;
  ThrottleAdapt_U.ev_NoSync = 0;
  ThrottleAdapt_U.ev_10ms = 0;
  ThrottleAdapt_step();
}

void ThrottleAdapt_10ms(void)
{
  ThrottleAdapt_U.ev_PowerOn = 0;
  ThrottleAdapt_U.ev_NoSync = 0;
  ThrottleAdapt_U.ev_10ms = 0;
  ThrottleAdapt_step();
  ThrottleAdapt_U.ev_PowerOn = 0;
  ThrottleAdapt_U.ev_NoSync = 0;
  ThrottleAdapt_U.ev_10ms = 1;
  ThrottleAdapt_step();
}

void ThrottleAdapt_NoSync(void)
{
  ThrottleAdapt_U.ev_PowerOn = 0;
  ThrottleAdapt_U.ev_NoSync = 0;
  ThrottleAdapt_U.ev_10ms = 0;
  ThrottleAdapt_step();
  ThrottleAdapt_U.ev_PowerOn = 0;
  ThrottleAdapt_U.ev_NoSync = 1;
  ThrottleAdapt_U.ev_10ms = 0;
  ThrottleAdapt_step();
}

#else

int16_T DAngThr = 0;

#endif                                 //_BUILD_THROTTLEADAPT_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
