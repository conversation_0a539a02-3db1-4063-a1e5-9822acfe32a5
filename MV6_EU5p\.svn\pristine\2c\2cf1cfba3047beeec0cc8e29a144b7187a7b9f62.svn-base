#-------------------------------------------------------------------------------
# Name:        modulo1
# Purpose:
#
# Author:      SalimbeniT
#
# Created:     28/05/2013
# Copyright:   (c) SalimbeniT 2013
# Licence:     <your licence>
#-------------------------------------------------------------------------------
import csv
def main():
    pass

if __name__ == '__main__':
    main()
LenArray=9800;


"""                PARSE CSV                      """


s = input('CSV input file name: ')

with open('data/'+s+'.csv', 'r') as csvfile:
    spamreader = csv.reader(csvfile)
    rowcount=0
    for row in spamreader:
        rowcount +=1

    csvfile.seek(0)
    SignalNameTot=next(spamreader)


    SignalTaskTot=[]
    csvfile.seek(0)
    spamreader.__next__()
    row=spamreader.__next__()
    for i in row:
        SignalTaskTot.append(int(i))

    MexName=[]
    mexname=[]
    Signal=[]
    SignalName=[]
    SignalTask=[]
    Index=[]


    for Nome in SignalNameTot:
		##### Vehicle CAN - E-Shock IMU #####
        if Nome=="AxCAN":    # nome che appare nel CSV
            Signal.append(Nome)     # nome del segnale cos come lo si trova nel dbc
            mexname.append("Axis1")   # la variabile di tipo messaggio da dichiarare nel CAPL
            MexName.append("AxisPosition1")   # nome del messaggio can
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="AyCAN":
            Signal.append(Nome)
            mexname.append("Axis1")
            MexName.append("AxisPosition1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="AzCAN":
            Signal.append(Nome)
            mexname.append("Axis1")
            MexName.append("AxisPosition1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="GxCAN":
            Signal.append("WxCAN")
            mexname.append("Axis2")
            MexName.append("AxisPosition2")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="GyCAN":
            Signal.append("WyCAN")
            mexname.append("Axis2")
            MexName.append("AxisPosition2")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="GzCAN":
            Signal.append("WzCAN")
            mexname.append("Axis2")
            MexName.append("AxisPosition2")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="PitchCAN":
            Signal.append(Nome)
            mexname.append("Axis3")
            MexName.append("AxisPosition3")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="RollCAN":
            Signal.append(Nome)
            mexname.append("Axis3")
            MexName.append("AxisPosition3")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])
		
		##### Analog External Sensor #####
        if Nome=="SelectorCAN":
            Signal.append("TractionControlStatus")
            mexname.append("Dash")
            MexName.append("DashBoard")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="FlagBrakeCAN":
            Signal.append("EngineBrake")
            mexname.append("Dash")
            MexName.append("DashBoard")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="VRearCAN":
            Signal.append("VehSpeedRearCAN")
            mexname.append("ABS_1")
            MexName.append("ABS1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="VFrontCAN":
            Signal.append("VehSpeedFrontCAN")
            mexname.append("ABS_1")
            MexName.append("ABS1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        ##### ECU Data simulated #####
        if Nome=="CMEDriverCAN":
            Signal.append("CMEDriverCAN_sim")
            mexname.append("EngSim")
            MexName.append("Engine_sim")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="CMEEstMinCAN":
            Signal.append("CMEEstMinCAN")
            mexname.append("EngSim")
            MexName.append("Engine_sim")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="ClutchCAN":
            Signal.append("ClutchCAN_sim")
            mexname.append("EngSim")
            MexName.append("Engine_sim")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="GasPosCAN":
            Signal.append("GasPosCAN_sim")
            mexname.append("EngSim")
            MexName.append("Engine_sim")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="GearPosCAN":
            Signal.append("GearPosCAN_sim")
            mexname.append("EngSim")
            MexName.append("Engine_sim")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="rpmCAN":
            Signal.append("RpmCAN_sim")
            mexname.append("EngSim")
            MexName.append("Engine_sim")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="Longitude":
            Signal.append("Longitude_sim")
            mexname.append("EngSim1")
            MexName.append("Engine_sim1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="Latitude":
            Signal.append("Latitude_sim")
            mexname.append("EngSim1")
            MexName.append("Engine_sim1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="Satellites":
            Signal.append("Satellites_sim")
            mexname.append("EngSim1")
            MexName.append("Engine_sim1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="Second":
            Signal.append("Second_sim")
            mexname.append("EngSim1")
            MexName.append("Engine_sim1")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])
			
		
    segnale1=[0] * (rowcount+LenArray)
    segnale2=[0] * (rowcount+LenArray)
    segnale3=[0] * (rowcount+LenArray)
    segnale4=[0] * (rowcount+LenArray)
    segnale5=[0] * (rowcount+LenArray)
    segnale6=[0] * (rowcount+LenArray)

    SignalMatrix= [[0] * len(SignalName)] * (rowcount+LenArray)


    csvfile.seek(0)
    spamreader.__next__()
    row=spamreader.__next__()
    riga=[]

    for i in range (2,rowcount):
        row=spamreader.__next__()
        riga=[]
        for j in range (0,len(Index)):
            riga.append(row[Index[j]])

        SignalMatrix[i-2]=riga

"""interfaccia utente"""

Tempo=(rowcount-2)*int(min(SignalTask))/1000
minuti=int(Tempo/60)
secondi=Tempo-(minuti*60)
print("\nDurata attesa del test: %s secondi (%s:%s minuti)"%(Tempo, minuti,int(secondi)))
print("Numero di segnali processati: %s\n"%len(SignalName))

s = input('Specificare nome file CAPL? (y/n): ')
if s=="y":
    r = input('Nome File: ')
    out_file = open(r+".can","w")
else:
    out_file = open("CAPL.can","w")


"""                PARSE CAPL                      """

"""                VARIABLES                      """

out_file.write("/*@@var:*/\n")
out_file.write("variables\n")
out_file.write("{\n")
Task=[]
for i in SignalTask:
    if i not in Task:
        Task.append(i)
for i in Task:
    out_file.write("msTimer t%sms;\n"%i)
    out_file.write("word Cnt%sms=0;\n"%i)
    out_file.write("word CountArray%sms=0;\n"%i)

mex=[]
Mex=[]
for i in range (0,len(mexname)):
    if mexname[i] not in mex:
        mex.append(mexname[i])
        Mex.append(MexName[i])
for i in range(0,len(mex)):
    out_file.write("message %s %s;\n"%(Mex[i],mex[i]))



out_file.write("word LenArray=%s;\n" % LenArray)

NumOfArray=[]

for i in Task:
    LenInput=(rowcount-2)*int(min(SignalTask))/int(i);
    tmp=int(LenInput/LenArray)+1
    NumOfArray.append(tmp)
    out_file.write("word LenInput%sms=%s;\n" % (i,LenInput))
    out_file.write("word NumOfArray%sms=%s;\n" % (i,NumOfArray[len(NumOfArray)-1]))


#ARRAY SEGNALI

for i in range(0,len(SignalName)):
    ind=Task.index(SignalTask[i])
    for j in range (0,NumOfArray[ind]):
        out_file.write("float %s_%s[%s]="%(SignalName[i],j+1,LenArray))
        out_file.write("{")
        for k in range (j*LenArray,(j+1)*LenArray-1):
            out_file.write("%s,"%SignalMatrix[k][i])
        out_file.write("%s};\n"%SignalMatrix[(j+1)*LenArray-1][i])




out_file.write("}\n")
out_file.write("/*@@end*/\n")


"""                START                      """
out_file.write("/*@@startStart:Start:*/\n")
out_file.write("on start\n")
out_file.write("{\n")
out_file.write("\n")
for i in Task:
#    if ((i==2)|(i==4)):
#        out_file.write("t%sms.set(0);\n"%i)
#    else:
        out_file.write("t%sms.set(%s);\n"%(i,i))

#out_file.write("ECU_Engine_Data.ECU_MUX_Engine_Data.phys=2;")
out_file.write("\n")
out_file.write("}\n")
out_file.write("/*@@end*/\n")


"""                TASK                       """
for i in range (0,len(Task)):
    out_file.write("/*@@timer:t%sms:*/\n"%Task[i])
    out_file.write("on timer t%sms\n"%Task[i])
    out_file.write("{\n")
    out_file.write("t%sms.set(%s);\n"%(Task[i],Task[i]))
    out_file.write("if(Cnt%sms>=LenArray)\n"%Task[i])
    out_file.write("{\n")
    out_file.write("Cnt%sms=0;\n"%Task[i])
    out_file.write("CountArray%sms++;\n"%Task[i])
    out_file.write("}\n\n")
    out=[]

    for j in range (0,NumOfArray[i]):

        out_file.write("if(CountArray%sms==%s)\n"%(Task[i],(j)))
        out_file.write("{\n")
        for k in range (0,len(SignalName)):
            if SignalTask[k]==Task[i]:
                out_file.write("%s.%s.phys=%s_%s[Cnt%sms];\n"%(mexname[k],Signal[k],SignalName[k],(j+1),Task[i]))
                message=mexname[k]
                if message not in out:
                    out.append(message)
        for h in out:
            out_file.write("output(%s);\n"%h)

        out_file.write("Cnt%sms++;\n"%Task[i])
        out_file.write("}\n")
    out_file.write("}\n")
    out_file.write("/*@@end*/\n")


out_file.close()

"""    UTILS  """


minimo=[]
massimo=[]


for i in range (0, len(SignalName)):
    minimo.append(100000)
    massimo.append(-100000)
    if ((mexname[i]=="dac_fast") | (mexname[i]=="dac")):
        minimo.append(100000)
        massimo.append(-100000)
        for j in range(0,int(rowcount*min(SignalTask)/SignalTask[i])+1):
            if float(SignalMatrix[j][i])<float(minimo[i]):
                minimo[i]= float(SignalMatrix[j][i])
            if float(SignalMatrix[j][i])>float(massimo[i]):
                massimo[i]= float(SignalMatrix[j][i])


gainCan=[]
offsetCan=[]
gainSw=[]
roundmin=[]
roundmax=[]


for i in range (0, len(SignalName)):
    if minimo[i]!=100000:
        if minimo[i]>=0:
            m=minimo[i]-(minimo[i]*0.1)      #10% di margine
        else:
            m=minimo[i]+(minimo[i]*0.1)      #10% di margine

        if massimo[i]>=0:
            M=massimo[i]+(massimo[i]*0.1)  #10% di margine
        else:
            M=massimo[i]-(massimo[i]*0.1)  #10% di margine

        roundmin.append(m)
        roundmax.append(M)
        g=(M-m)/(4096)
        o=(m)
        gainCan.append(g)
        offsetCan.append(o)
        g=g*(4096)/5
        gainSw.append(g)



out = open("Scalature Analogici.txt","w")


out.write("A={")
for i in range (0,len(SignalName)-1):

    out.write("'%s',"%SignalName[i])

out.write("'%s';"%SignalName[len(SignalName)-1])

for i in range (0,len(SignalTask)-1):

    out.write("'%s',"%SignalTask[i])

out.write("'%s'}"%SignalTask[len(SignalTask)-1])

out.write("\n")
out.write("\n")
out.write("\n")
cnt=0
for i in range (0,len(SignalName)):
    if ((mexname[i]=="dac_fast") | (mexname[i]=="dac")):
        out.write("\n")
        out.write("SEGNALE: %s\n\n"%SignalName[i])
        out.write("Massimo:        %s (%s)\n"%(massimo[i],int(roundmax[cnt])))
        out.write("Minimo:         %s (%s)\n"%(minimo[i],int(roundmin[cnt])))
        out.write("Gain per DBC:   %s\n"%gainCan[cnt])
        out.write("offset:         %s\n"%offsetCan[cnt])
        out.write("Gain per Sw:    %s\n"%gainSw[cnt])
        out.write("\n")
        cnt=cnt+1



out.close()




