#ifndef _STUB_H_
#define _STUB_H_

#include "typedefs.h"

extern uint8_T idxUint8;
extern int16_T idxInt16;
extern uint16_T idxUInt16;
extern int16_T BkIdxInt16[3];
extern int16_T BkIdx2Int16[7];
extern uint8_T BkIdxUInt8[6];

extern int16_T VOutExh;
extern int32_T AngExhValPerc;
extern uint8_T FlgExhValHBEna;

extern uint8_T QSSetupChange;
extern int8_T  IonAbsTdcDT;
extern uint16_T CtlystMonitorCompletionCntsBk1_stub;
extern uint16_T CtlystMonitorConditionsEncCntsBk1_stub;
extern uint16_T CtlystMonitorCompletionCntsBk2_stub;
extern uint16_T CtlystMonitorConditionsEncCntsBk2_stub;
extern uint16_T O2SensMonitorCompletionCntsBk2_stub;
extern uint16_T O2SensMonitorConditionsEncCntsBk2_stub;
extern uint16_T EgrVvtMonitorCompletionCondCnts_stub;
extern uint16_T EgrVvtMonitorConditionsEncCnts_stub;
extern uint16_T AirMonitorCompletionCondCnts_stub;
extern uint16_T AirMonitorConditionsEncCounts_stub;
extern uint16_T EvapMonitorCompletionCondCnts_stub;
extern uint16_T EvapMonitorConditionsEncCounts_stub;
extern uint16_T SecOxySenMonitorComplCntsBk1_stub;
extern uint16_T SecOxySenMonitorCondiEncCntsBk1_stub;
extern uint16_T SecOxySenMonitorComplCntsBk2_stub;
extern uint16_T SecOxySenMonitorCondiEncCntsBk2_stub;
extern uint8_T TempUnlockCntDown_stub;

void Stub_Init (void);

#endif
