/*
 * File: TrqDriver.c
 *
 * Code generated for Simulink model 'TrqDriver'.
 *
 * Model version                  : 1.2235
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Aug  5 15:33:05 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#include "TrqDriver.h"
#include "TrqDriver_private.h"

/* Named constants for Chart: '<S3>/CmeDriver_Management' */
#define TrqDriver_IN_CME_CAN           ((uint8_T)1U)
#define TrqDriver_IN_CME_CAN_FREEZE    ((uint8_T)1U)
#define TrqDriver_IN_CME_CAN_OK        ((uint8_T)2U)
#define TrqDriver_IN_CME_GAS_RPM       ((uint8_T)2U)
#define TrqDriver_IN_CME_REC_ACTIVE    ((uint8_T)3U)
#define TrqDriver_IN_DISABLED          ((uint8_T)1U)
#define TrqDriver_IN_NORMAL            ((uint8_T)2U)
#define TrqDriver_IN_NORMAL_CL         ((uint8_T)1U)
#define TrqDriver_IN_NORMAL_CL_CTF     ((uint8_T)2U)
#define TrqDriver_IN_NORMAL_WAIT       ((uint8_T)3U)
#define TrqDriver_IN_NO_ACTIVE_CHILD   ((uint8_T)0U)
#define TrqDriver_IN_PAUSED            ((uint8_T)4U)
#define TrqDriver_IN_UNUSED            ((uint8_T)1U)
#define TrqDriver_IN_USED              ((uint8_T)2U)

/* user code (top of source file) */
/* System '<Root>/TrqDriver' */
#ifdef _BUILD_TRQDRIVER_

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_TrqDriver TrqDriver_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_TrqDriver TrqDriver_U;

/* Real-time model */
RT_MODEL_TrqDriver TrqDriver_M_;
RT_MODEL_TrqDriver *const TrqDriver_M = &TrqDriver_M_;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T CmeDriver;

/* Final CME driver/CAN request */
int16_T CmeDriverCANF;

/* CMI target for WOT strategy */
int16_T CmeDriverF;

/* Final CME driver/CAN request */
int16_T CmeDriverTmp;

/* CME driver/CAN request (no sat) */
int16_T CmeGasRpm;

/* CME pedal request */
int16_T CmeGasRpmX0;

/* CME pedal request */
int16_T CmeTargetWot;

/* CME target for WOT strategy */
int16_T CmiTargetWot;

/* CMI target for WOT strategy */
int16_T DVehSpeedSetUp;

/* VehSpeedLim - VehSpeedSetUp */
int16_T DeltaCmeDriverF;

/* Final CME driver/CAN request */
uint8_T FlgCmeDriverCANOK;

/* Plausibility test for CME from CAN */
uint8_T FlgGasPosCCFilt;

/* Flag to indicate GasPosCC filter while disabling/pausing */
uint8_T FlgVSCActive;

/* Flag to indicate control active */
uint8_T FlgVSCDisable;

/* Flag to disable vehicle speed control */
uint8_T FlgVSCPause;

/* Flag to pause vehicle speed control */
uint16_T GasPosCC;

/* GasPos corrected by vehicle speed control */
int32_T GasPosCCFiltHiR;

/* GasPosCC filtered */
int32_T GasPosCCIntHiR;

/* VSC integral term */
uint32_T IDTrqDriver;

/* ID Version */
uint8_T StCmeCAN;

/* CAN torque management state */
uint8_T StVehSpeedCtrl;

/* Vehicle speed control status */
uint16_T TDrivGasIndex;

/* TDrivRpmIndex */
uint16_T TDrivGasRatio;

/* TDrivRpmRatio */
uint16_T TDrivRpmIndex;

/* TDrivRpmIndex */
uint16_T TDrivRpmRatio;

/* TDrivRpmRatio */
int16_T VSCError;

/* Cruise control error */
int16_T VSCGasPosCCInt;

/* Vehicle speed control - integral term */
int16_T VSCGasPosCCProp;

/* Vehicle speed control - proportional term */
uint16_T VSCKFilt;

/* VehSpeedSetupCAN filter gain */
uint8_T VSCUseFilt;

/* Flag to indicate VSC is using the filter */
uint16_T VehSpeedSetUp;

/* Vehicle speed setup */
uint32_T VehSpeedSetUpHiR;

/* Vehicle speed setup - high resolution var */
uint16_T VehSpeedSetUpInit;

/* Last VehSpeedLim value */
int32_T VehSpeedSetUpRL;

/* Last VehSpeedSetUpCANRL value */
uint8_T VehSpeedSetUpRateCnt;

/* Counter from new VehSpeedLim value */

/* Declare variables for internal data of system '<S1>/T10ms' */
rtB_T10ms_TrqDriver TrqDriver_T10ms_B;
rtDW_T10ms_TrqDriver TrqDriver_T10ms_DW;

/* Output and update for function-call system: '<S3>/CmeDriverCAN_Diagnosis' */
void TrqDrive_CmeDriverCAN_Diagnosis(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T rtb_Switch4;

  /* Switch: '<S20>/Switch' incorporates:
   *  Constant: '<S20>/CMEGASRPMOFF'
   *  Constant: '<S20>/GASPOSMINTHR'
   *  Constant: '<S20>/OK'
   *  DataStoreRead: '<S20>/Data Store Read1'
   *  DataStoreWrite: '<S20>/Data Store Write4'
   *  Inport: '<Root>/CmeDriverCAN'
   *  Inport: '<Root>/GasPos'
   *  RelationalOperator: '<S20>/Relational Operator2'
   *  RelationalOperator: '<S20>/Relational Operator3'
   *  Sum: '<S20>/Add'
   */
  if (GasPos < GASPOSMINTHR) {
    FlgCmeDriverCANOK = (uint8_T)(CmeDriverCAN < ((int16_T)(CmeGasRpm +
      CMEGASRPMOFF)));
  } else {
    FlgCmeDriverCANOK = 1U;
  }

  /* End of Switch: '<S20>/Switch' */

  /* Switch: '<S7>/Switch4' incorporates:
   *  Constant: '<S7>/ENTORQUELAW'
   *  Constant: '<S7>/NO_PT_FAULT '
   *  Constant: '<S7>/USECMECAN'
   *  Logic: '<S7>/Logical Operator2'
   */
  if ((USECMECAN != 0) && (ENTORQUELAW != 0)) {
    /* Switch: '<S7>/Switch3' incorporates:
     *  Constant: '<S7>/BITMASK_BIT0'
     *  Constant: '<S7>/BITMASK_BIT1'
     *  Constant: '<S7>/BITMASK_BIT2'
     *  Constant: '<S7>/SIGNAL_NOT_PRESENT'
     *  Constant: '<S7>/SIGNAL_NOT_VALID'
     *  DataStoreWrite: '<S20>/Data Store Write4'
     *  Inport: '<Root>/CmeCanFault'
     *  Logic: '<S7>/Logical Operator1'
     *  S-Function (sfix_bitop): '<S7>/Bitwise Operator'
     *  S-Function (sfix_bitop): '<S7>/Bitwise Operator1'
     *  S-Function (sfix_bitop): '<S7>/Bitwise Operator2'
     *  Switch: '<S7>/Switch2'
     */
    if ((((CmeCanFault & ((uint8_T)BITMASK_BIT0)) != 0) || ((CmeCanFault &
           ((uint8_T)BITMASK_BIT2)) != 0)) || ((CmeCanFault & ((uint8_T)
           BITMASK_BIT1)) != 0)) {
      rtb_Switch4 = ((uint8_T)SIGNAL_NOT_VALID);
    } else if (FlgCmeDriverCANOK != 0) {
      /* Switch: '<S7>/Switch2' incorporates:
       *  Constant: '<S7>/NO_PT_FAULT'
       */
      rtb_Switch4 = ((uint8_T)NO_PT_FAULT);
    } else {
      rtb_Switch4 = ((uint8_T)SIGNAL_NOT_PRESENT);
    }

    /* End of Switch: '<S7>/Switch3' */
  } else {
    rtb_Switch4 = ((uint8_T)NO_PT_FAULT);
  }

  /* End of Switch: '<S7>/Switch4' */

  /* RelationalOperator: '<S7>/Relational Operator' incorporates:
   *  Constant: '<S7>/NO_PT_FAULT '
   */
  TrqDriver_T10ms_B.RelationalOperator_p = (uint8_T)(rtb_Switch4 != ((uint8_T)
    NO_PT_FAULT));

  /* S-Function (DiagMgm_SetDiagState): '<S21>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S7>/DIAG_CMEDRIVERCAN'
   */
  DiagMgm_SetDiagState( ((uint8_T)DIAG_CMEDRIVERCAN), rtb_Switch4,
                       &rtb_DiagMgm_SetDiagState);
}

/* Output and update for function-call system: '<S3>/CmeDriverCAN_Selection' */
void TrqDrive_CmeDriverCAN_Selection(void)
{
  /* DataStoreWrite: '<S8>/Data Store Write1' incorporates:
   *  Inport: '<Root>/CmeDriverCAN'
   */
  CmeDriverTmp = CmeDriverCAN;

  /* DataStoreWrite: '<S8>/Data Store Write2' incorporates:
   *  Inport: '<Root>/CmeDriverCAN'
   */
  CmeDriver = CmeDriverCAN;

  /* DataStoreWrite: '<S8>/Data Store Write3' incorporates:
   *  Inport: '<Root>/CmeDriverCAN'
   */
  CmeDriverCANF = CmeDriverCAN;
}

/* Output and update for function-call system: '<S3>/CmeGasRpm_Selection' */
void TrqDriver_CmeGasRpm_Selection(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int16_T rtb_Memory4;
  int16_T rtb_Memory1;
  int16_T rtb_Memory2;

  /* DataStoreRead: '<S12>/Data Store Read' incorporates:
   *  DataStoreWrite: '<S12>/Data Store Write1'
   */
  CmeDriverTmp = CmeGasRpm;

  /* Switch: '<S12>/Switch' incorporates:
   *  Constant: '<S12>/ENTORQUELAW'
   *  DataStoreWrite: '<S12>/Data Store Write1'
   *  Inport: '<Root>/CmeMecTarget'
   */
  if (ENTORQUELAW != 0) {
    rtb_Memory4 = CmeDriverTmp;
  } else {
    rtb_Memory4 = CmeMecTarget;
  }

  /* End of Switch: '<S12>/Switch' */

  /* DataStoreWrite: '<S12>/Data Store Write2' */
  CmeDriver = rtb_Memory4;

  /* DataStoreWrite: '<S12>/Data Store Write3' */
  CmeDriverCANF = rtb_Memory4;

  /* S-Function (FOF_Reset_S16_FXP): '<S44>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S12>/KFCMEDRIVER'
   *  Constant: '<S12>/ZERO'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_Memory4, KFCMEDRIVER, rtb_Memory4, ((uint8_T)0U),
                    TrqDriver_T10ms_DW.Memory_PreviousInput);

  /* DataStoreWrite: '<S12>/Data Store Write4' */
  CmeDriverF = rtb_FOF_Reset_S16_FXP_o1;

  /* Memory: '<S43>/Memory4' */
  rtb_Memory4 = TrqDriver_T10ms_DW.Memory4_PreviousInput;

  /* Sum: '<S43>/Add' incorporates:
   *  DataStoreWrite: '<S43>/Data Store Write5'
   */
  DeltaCmeDriverF = (int16_T)(rtb_FOF_Reset_S16_FXP_o1 - rtb_Memory4);

  /* Memory: '<S43>/Memory' */
  rtb_Memory4 = TrqDriver_T10ms_DW.Memory_PreviousInput_c;

  /* Memory: '<S43>/Memory1' */
  rtb_Memory1 = TrqDriver_T10ms_DW.Memory1_PreviousInput;

  /* Memory: '<S43>/Memory2' */
  rtb_Memory2 = TrqDriver_T10ms_DW.Memory2_PreviousInput;

  /* Update for Memory: '<S12>/Memory' */
  TrqDriver_T10ms_DW.Memory_PreviousInput = rtb_FOF_Reset_S16_FXP_o2;

  /* Update for Memory: '<S43>/Memory4' incorporates:
   *  Memory: '<S43>/Memory3'
   */
  TrqDriver_T10ms_DW.Memory4_PreviousInput =
    TrqDriver_T10ms_DW.Memory3_PreviousInput;

  /* Update for Memory: '<S43>/Memory' */
  TrqDriver_T10ms_DW.Memory_PreviousInput_c = rtb_FOF_Reset_S16_FXP_o1;

  /* Update for Memory: '<S43>/Memory1' */
  TrqDriver_T10ms_DW.Memory1_PreviousInput = rtb_Memory4;

  /* Update for Memory: '<S43>/Memory2' */
  TrqDriver_T10ms_DW.Memory2_PreviousInput = rtb_Memory1;

  /* Update for Memory: '<S43>/Memory3' */
  TrqDriver_T10ms_DW.Memory3_PreviousInput = rtb_Memory2;
}

/* Output and update for function-call system: '<S3>/CmeGasRpm_Calc' */
void TrqDriver_CmeGasRpm_Calc(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_b;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_l;
  uint16_T rtb_LookUp_IR_U8;
  int16_T rtb_Look2D_IR_S16;
  uint8_T rtb_Compare_m;
  uint16_T rtb_LookUp_IR_U8_f;
  uint16_T rtb_DataStoreRead_g;
  uint16_T rtb_Switch8[10];
  uint8_T rtb_Switch6[16];
  int32_T i;

  /* Switch: '<S36>/Switch8' incorporates:
   *  Constant: '<S41>/Constant'
   *  Constant: '<S42>/Constant'
   *  Inport: '<Root>/FlgVehStop'
   *  Inport: '<Root>/GearPosClu'
   *  Logic: '<S36>/Logical Operator1'
   *  RelationalOperator: '<S41>/Compare'
   *  RelationalOperator: '<S42>/Compare'
   */
  if ((GearPosClu != 0) || (FlgVehStop != 0)) {
    /* Switch: '<S36>/Switch4' incorporates:
     *  Constant: '<S36>/BKGASDRIV'
     *  Constant: '<S39>/Constant'
     *  Inport: '<Root>/AccSens'
     *  RelationalOperator: '<S39>/Compare'
     */
    if (AccSens == 0) {
      for (i = 0; i < 10; i++) {
        rtb_Switch8[i] = BKGASDRIV[i];
      }
    } else {
      /* RelationalOperator: '<S40>/Compare' incorporates:
       *  Constant: '<S40>/Constant'
       */
      rtb_Compare_m = (uint8_T)(AccSens == 1);
      for (i = 0; i < 10; i++) {
        /* Switch: '<S36>/Switch5' incorporates:
         *  Constant: '<S36>/BKGASDRIV1'
         *  Constant: '<S36>/BKGASDRIV2'
         */
        if (rtb_Compare_m != 0) {
          rtb_Switch8[i] = BKGASDRIV1[i];
        } else {
          rtb_Switch8[i] = BKGASDRIV2[i];
        }

        /* End of Switch: '<S36>/Switch5' */
      }
    }

    /* End of Switch: '<S36>/Switch4' */
  } else {
    /* MultiPortSwitch: '<S36>/Multiport Switch' incorporates:
     *  Constant: '<S36>/BKGASDRIV'
     *  Constant: '<S36>/BKGASDRIV1'
     *  Constant: '<S36>/BKGASDRIV2'
     *  Constant: '<S36>/SELBKGASDRIV'
     */
    switch (SELBKGASDRIV) {
     case 0:
      for (i = 0; i < 10; i++) {
        rtb_Switch8[i] = BKGASDRIV[i];
      }
      break;

     case 1:
      for (i = 0; i < 10; i++) {
        rtb_Switch8[i] = BKGASDRIV1[i];
      }
      break;

     default:
      for (i = 0; i < 10; i++) {
        rtb_Switch8[i] = BKGASDRIV2[i];
      }
      break;
    }

    /* End of MultiPortSwitch: '<S36>/Multiport Switch' */
  }

  /* End of Switch: '<S36>/Switch8' */

  /* Switch: '<S27>/Switch2' incorporates:
   *  Constant: '<S27>/RPMFSEL'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/RpmF'
   */
  if (RPMFSEL != 0) {
    rtb_LookUp_IR_U8_f = RpmF;
  } else {
    rtb_LookUp_IR_U8_f = Rpm;
  }

  /* End of Switch: '<S27>/Switch2' */

  /* S-Function (PreLookUpIdSearch_U16): '<S38>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S27>/BKRPMDRIV'
   *  Constant: '<S27>/BKRPMDRIV_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, rtb_LookUp_IR_U8_f,
                        &BKRPMDRIV[0], ((uint8_T)BKRPMDRIV_dim));

  /* S-Function (LookUp_IR_U8): '<S32>/LookUp_IR_U8' incorporates:
   *  Constant: '<S25>/VTCMETARGETWOT'
   *  Constant: '<S27>/BKRPMDRIV_dim'
   */
  LookUp_IR_U8( &rtb_LookUp_IR_U8_f, &VTCMETARGETWOT[0],
               rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
               ((uint8_T)BKRPMDRIV_dim));

  /* DataStoreRead: '<S27>/Data Store Read' */
  rtb_DataStoreRead_g = GasPosCC;

  /* S-Function (PreLookUpIdSearch_U16): '<S37>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S27>/BKGASDRIV_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_b,
                        &rtb_PreLookUpIdSearch_U16_o2_l, rtb_DataStoreRead_g,
                        &rtb_Switch8[0], ((uint8_T)BKGASDRIV_dim));

  /* S-Function (Look2D_IR_S16): '<S28>/Look2D_IR_S16' incorporates:
   *  Constant: '<S24>/TBCMEDRIV'
   *  Constant: '<S27>/BKGASDRIV_dim'
   *  Constant: '<S27>/BKRPMDRIV_dim'
   */
  Look2D_IR_S16( &rtb_Look2D_IR_S16, &TBCMEDRIV[0], rtb_PreLookUpIdSearch_U16_o1,
                rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMDRIV_dim),
                rtb_PreLookUpIdSearch_U16_o1_b, rtb_PreLookUpIdSearch_U16_o2_l,
                ((uint8_T)BKGASDRIV_dim));

  /* Switch: '<S26>/Switch7' incorporates:
   *  Constant: '<S26>/CME_MAX'
   *  Constant: '<S33>/Constant'
   *  DataTypeConversion: '<S26>/Data Type Conversion'
   *  Inport: '<Root>/MaxTrq'
   *  RelationalOperator: '<S33>/Compare'
   */
  if (MaxTrq == 1) {
    /* RelationalOperator: '<S34>/Compare' incorporates:
     *  Constant: '<S34>/Constant'
     *  Inport: '<Root>/RidingMode'
     */
    rtb_Compare_m = (uint8_T)(RidingMode != 1);

    /* Switch: '<S26>/Switch6' incorporates:
     *  Constant: '<S26>/VTMAXCMEMTRQ'
     *  Constant: '<S26>/VTMAXCMERAIN'
     */
    for (i = 0; i < 16; i++) {
      if (rtb_Compare_m != 0) {
        rtb_Switch6[i] = VTMAXCMEMTRQ[i];
      } else {
        rtb_Switch6[i] = VTMAXCMERAIN[i];
      }
    }

    /* End of Switch: '<S26>/Switch6' */

    /* S-Function (LookUp_IR_U8): '<S35>/LookUp_IR_U8' incorporates:
     *  Constant: '<S27>/BKRPMDRIV_dim'
     */
    LookUp_IR_U8( &rtb_LookUp_IR_U8, &rtb_Switch6[0],
                 rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                 ((uint8_T)BKRPMDRIV_dim));
    CmeGasRpmX0 = (int16_T)(((uint32_T)rtb_LookUp_IR_U8) >> 3);
  } else {
    CmeGasRpmX0 = ((int16_T)CME_MAX);
  }

  /* End of Switch: '<S26>/Switch7' */

  /* MinMax: '<S11>/MinMax' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write3'
   */
  if (rtb_Look2D_IR_S16 < CmeGasRpmX0) {
    CmeGasRpm = rtb_Look2D_IR_S16;
  } else {
    CmeGasRpm = CmeGasRpmX0;
  }

  /* End of MinMax: '<S11>/MinMax' */

  /* S-Function (Look2D_IR_S16): '<S29>/Look2D_IR_S16' incorporates:
   *  Constant: '<S24>/Constant'
   *  Constant: '<S24>/Constant1'
   *  Constant: '<S24>/TBCMEDRIV'
   *  Constant: '<S27>/BKGASDRIV_dim'
   *  Constant: '<S27>/BKRPMDRIV_dim'
   */
  Look2D_IR_S16( &rtb_Look2D_IR_S16, &TBCMEDRIV[0], rtb_PreLookUpIdSearch_U16_o1,
                rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMDRIV_dim),
                ((uint16_T)0U), ((uint16_T)0U), ((uint8_T)BKGASDRIV_dim));

  /* MinMax: '<S11>/MinMax1' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write4'
   */
  if (rtb_Look2D_IR_S16 < CmeGasRpmX0) {
    CmeGasRpmX0 = rtb_Look2D_IR_S16;
  }

  /* End of MinMax: '<S11>/MinMax1' */

  /* DataStoreWrite: '<S27>/Data Store Write2' */
  TDrivGasIndex = rtb_PreLookUpIdSearch_U16_o1_b;

  /* DataStoreWrite: '<S27>/Data Store Write4' */
  TDrivGasRatio = rtb_PreLookUpIdSearch_U16_o2_l;

  /* DataTypeConversion: '<S25>/Data Type Conversion1' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write1'
   */
  CmeTargetWot = (int16_T)(((uint32_T)rtb_LookUp_IR_U8_f) >> 3);

  /* Sum: '<S25>/Add' incorporates:
   *  DataStoreWrite: '<S11>/Data Store Write1'
   *  DataStoreWrite: '<S11>/Data Store Write2'
   *  Inport: '<Root>/CmfP'
   */
  CmiTargetWot = (int16_T)(CmeTargetWot + CmfP);

  /* DataStoreWrite: '<S27>/Data Store Write1' */
  TDrivRpmRatio = rtb_PreLookUpIdSearch_U16_o2;

  /* DataStoreWrite: '<S27>/Data Store Write3' */
  TDrivRpmIndex = rtb_PreLookUpIdSearch_U16_o1;
}

/* Output and update for function-call system: '<S3>/VehCtrl_CL' */
void TrqDriver_VehCtrl_CL(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  int16_T rtb_LookUp_IR_S16;
  int32_T rtb_idlrpmerrint_nosat;
  int32_T rtb_Sum1;
  int32_T rtb_DataTypeConversion1_a;
  int16_T rtb_DataStoreRead1_p;

  /* Outputs for Atomic SubSystem: '<S16>/PI_term_calc' */
  /* Sum: '<S72>/Sum2' incorporates:
   *  Constant: '<S72>/ONE'
   *  Inport: '<Root>/GearPosClu'
   */
  rtb_DataTypeConversion1_a = GearPosClu - 1;
  if (rtb_DataTypeConversion1_a < 0) {
    rtb_DataTypeConversion1_a = 0;
  }

  /* DataStoreRead: '<S72>/Data Store Read1' */
  rtb_DataStoreRead1_p = VSCError;

  /* S-Function (PreLookUpIdSearch_S16): '<S78>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S72>/BKVSCERROR'
   *  Constant: '<S72>/BKVSCERROR_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, rtb_DataStoreRead1_p,
                        &BKVSCERROR[0], ((uint8_T)BKVSCERROR_dim));

  /* S-Function (LookUp_IR_S16): '<S76>/LookUp_IR_S16' incorporates:
   *  Constant: '<S72>/BKVSCERROR_dim'
   *  Constant: '<S72>/VTVSCINT'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTVSCINT[0], rtb_PreLookUpIdSearch_S16_o1,
                rtb_PreLookUpIdSearch_S16_o2, ((uint8_T)BKVSCERROR_dim));

  /* Product: '<S72>/Product' incorporates:
   *  Constant: '<S72>/VTVSCGAINGEAR'
   *  Selector: '<S72>/Selector3'
   *  Sum: '<S72>/Sum2'
   */
  rtb_idlrpmerrint_nosat = rtb_LookUp_IR_S16 * VTVSCGAINGEAR[(uint8_T)
    rtb_DataTypeConversion1_a];
  VSCGasPosCCInt = (int16_T)((((rtb_idlrpmerrint_nosat < 0) ? 127 : 0) +
    rtb_idlrpmerrint_nosat) >> 7);

  /* S-Function (LookUp_IR_S16): '<S77>/LookUp_IR_S16' incorporates:
   *  Constant: '<S72>/BKVSCERROR_dim'
   *  Constant: '<S72>/VTVSCPROP'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTVSCPROP[0], rtb_PreLookUpIdSearch_S16_o1,
                rtb_PreLookUpIdSearch_S16_o2, ((uint8_T)BKVSCERROR_dim));

  /* Product: '<S72>/Product1' incorporates:
   *  Constant: '<S72>/VTVSCGAINGEAR'
   *  Selector: '<S72>/Selector3'
   *  Sum: '<S72>/Sum2'
   */
  rtb_idlrpmerrint_nosat = rtb_LookUp_IR_S16 * VTVSCGAINGEAR[(uint8_T)
    rtb_DataTypeConversion1_a];
  VSCGasPosCCProp = (int16_T)((((rtb_idlrpmerrint_nosat < 0) ? 127 : 0) +
    rtb_idlrpmerrint_nosat) >> 7);

  /* End of Outputs for SubSystem: '<S16>/PI_term_calc' */

  /* Outputs for Atomic SubSystem: '<S16>/tmpGasPosCC_calc' */
  /* Sum: '<S73>/Sum4' incorporates:
   *  DataStoreRead: '<S73>/Data Store Read2'
   */
  rtb_idlrpmerrint_nosat = GasPosCCIntHiR + VSCGasPosCCInt;

  /* Outputs for Atomic SubSystem: '<S16>/PI_term_calc' */
  /* DataTypeConversion: '<S73>/Data Type Conversion' incorporates:
   *  Constant: '<S73>/VTMAXGASPOSCCINT'
   *  Selector: '<S73>/Selector1'
   *  Sum: '<S72>/Sum2'
   */
  rtb_Sum1 = (VTMAXGASPOSCCINT[(uint8_T)rtb_DataTypeConversion1_a] << 12);

  /* End of Outputs for SubSystem: '<S16>/PI_term_calc' */

  /* Switch: '<S81>/Switch2' incorporates:
   *  RelationalOperator: '<S81>/LowerRelop1'
   */
  if (rtb_idlrpmerrint_nosat > rtb_Sum1) {
    rtb_idlrpmerrint_nosat = rtb_Sum1;
  } else {
    /* Outputs for Atomic SubSystem: '<S16>/PI_term_calc' */
    /* DataTypeConversion: '<S73>/Data Type Conversion1' incorporates:
     *  Constant: '<S73>/VTMINGASPOSCCINT'
     *  Selector: '<S73>/Selector2'
     *  Sum: '<S72>/Sum2'
     */
    rtb_DataTypeConversion1_a = (VTMINGASPOSCCINT[(uint8_T)
      rtb_DataTypeConversion1_a] << 12);

    /* End of Outputs for SubSystem: '<S16>/PI_term_calc' */

    /* Switch: '<S81>/Switch' incorporates:
     *  RelationalOperator: '<S81>/UpperRelop'
     */
    if (rtb_idlrpmerrint_nosat < rtb_DataTypeConversion1_a) {
      rtb_idlrpmerrint_nosat = rtb_DataTypeConversion1_a;
    }

    /* End of Switch: '<S81>/Switch' */
  }

  /* End of Switch: '<S81>/Switch2' */

  /* DataTypeConversion: '<S73>/Data Type Conversion2' incorporates:
   *  Sum: '<S73>/Sum1'
   */
  rtb_DataTypeConversion1_a = (((VSCGasPosCCProp << 8) + rtb_idlrpmerrint_nosat)
    >> 8);
  if (rtb_DataTypeConversion1_a < 0) {
    rtb_DataTypeConversion1_a = 0;
  }

  /* Switch: '<S82>/Switch2' incorporates:
   *  Constant: '<S73>/MAX_GASPOS_CC'
   *  Constant: '<S73>/MIN_GASPOS_CC'
   *  DataTypeConversion: '<S73>/Data Type Conversion2'
   *  RelationalOperator: '<S82>/LowerRelop1'
   *  RelationalOperator: '<S82>/UpperRelop'
   *  Switch: '<S82>/Switch'
   */
  if (((uint16_T)rtb_DataTypeConversion1_a) > ((uint16_T)MAX_GASPOS_CC)) {
    GasPosCC = ((uint16_T)MAX_GASPOS_CC);
  } else if (((uint16_T)rtb_DataTypeConversion1_a) < ((uint16_T)MIN_GASPOS_CC))
  {
    /* Switch: '<S82>/Switch' incorporates:
     *  Constant: '<S73>/MIN_GASPOS_CC'
     */
    GasPosCC = ((uint16_T)MIN_GASPOS_CC);
  } else {
    GasPosCC = (uint16_T)rtb_DataTypeConversion1_a;
  }

  /* End of Switch: '<S82>/Switch2' */
  /* End of Outputs for SubSystem: '<S16>/tmpGasPosCC_calc' */

  /* Outputs for Atomic SubSystem: '<S16>/GasPosCC_calc' */
  /* If: '<S71>/If' incorporates:
   *  Inport: '<Root>/GasPos'
   *  RelationalOperator: '<S71>/Relational Operator'
   */
  if (GasPos > GasPosCC) {
    /* Outputs for IfAction SubSystem: '<S71>/GasPos_overwrite' incorporates:
     *  ActionPort: '<S75>/Action Port'
     */
    /* DataStoreWrite: '<S75>/Data Store Write14' incorporates:
     *  Constant: '<S75>/ZERO'
     */
    FlgGasPosCCFilt = 0U;

    /* DataTypeConversion: '<S75>/Data Type Conversion2' incorporates:
     *  DataStoreWrite: '<S75>/Data Store Write4'
     */
    GasPosCCFiltHiR = (GasPos << 14);

    /* DataStoreWrite: '<S75>/Data Store Write2' incorporates:
     *  Constant: '<S75>/ONE'
     */
    FlgVSCActive = 1U;

    /* DataStoreWrite: '<S75>/Data Store Write1' */
    GasPosCC = GasPos;

    /* End of Outputs for SubSystem: '<S71>/GasPos_overwrite' */
  } else {
    /* Outputs for IfAction SubSystem: '<S71>/Closed_Loop_Active' incorporates:
     *  ActionPort: '<S74>/Action Port'
     */
    /* DataStoreWrite: '<S74>/Data Store Write14' incorporates:
     *  Constant: '<S74>/ONE'
     */
    FlgGasPosCCFilt = 1U;

    /* DataStoreWrite: '<S74>/Data Store Write2' incorporates:
     *  Constant: '<S74>/ONE'
     */
    FlgVSCActive = 1U;

    /* DataTypeConversion: '<S74>/Data Type Conversion2' incorporates:
     *  DataStoreWrite: '<S74>/Data Store Write4'
     */
    GasPosCCFiltHiR = (GasPosCC << 14);

    /* DataStoreWrite: '<S74>/Data Store Write3' */
    GasPosCCIntHiR = rtb_idlrpmerrint_nosat;

    /* End of Outputs for SubSystem: '<S71>/Closed_Loop_Active' */
  }

  /* End of If: '<S71>/If' */
  /* End of Outputs for SubSystem: '<S16>/GasPosCC_calc' */
}

/* Output and update for function-call system: '<S3>/VehCtrl_Reset' */
void TrqDriver_VehCtrl_Reset(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2_p;
  uint8_T rtb_Compare_lm;
  int16_T rtb_FOF_Reset_S16_FXP_o1_f;

  /* DataTypeConversion: '<S19>/Data Type Conversion2' incorporates:
   *  Inport: '<Root>/GasPos'
   */
  rtb_FOF_Reset_S16_FXP_o1_f = (int16_T)GasPos;

  /* Logic: '<S19>/Logical Operator2' incorporates:
   *  Constant: '<S85>/Constant'
   *  Constant: '<S87>/Constant'
   *  DataStoreRead: '<S19>/Data Store Read2'
   *  DataStoreRead: '<S19>/Data Store Read3'
   *  DataStoreWrite: '<S19>/Data Store Write14'
   *  Inport: '<Root>/GasPos'
   *  Inport: '<Root>/GearPosClu'
   *  RelationalOperator: '<S19>/Relational Operator'
   *  RelationalOperator: '<S85>/Compare'
   *  RelationalOperator: '<S87>/Compare'
   */
  FlgGasPosCCFilt = (uint8_T)(((GasPosCC > GasPos) && (FlgGasPosCCFilt != 0)) &&
    (GearPosClu != 0));

  /* RelationalOperator: '<S86>/Compare' incorporates:
   *  Constant: '<S86>/Constant'
   *  DataStoreWrite: '<S19>/Data Store Write14'
   */
  rtb_Compare_lm = (uint8_T)(FlgGasPosCCFilt == 0);

  /* S-Function (FOF_Reset_S16_FXP): '<S88>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S19>/GASPOSCCFILT'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1_f, &rtb_FOF_Reset_S16_FXP_o2_p,
                    rtb_FOF_Reset_S16_FXP_o1_f, GASPOSCCFILT,
                    rtb_FOF_Reset_S16_FXP_o1_f, rtb_Compare_lm, GasPosCCFiltHiR);

  /* DataStoreWrite: '<S19>/Data Store Write1' */
  GasPosCCFiltHiR = rtb_FOF_Reset_S16_FXP_o2_p;

  /* DataTypeConversion: '<S19>/Data Type Conversion3' incorporates:
   *  DataStoreWrite: '<S19>/Data Store Write4'
   */
  GasPosCC = (uint16_T)rtb_FOF_Reset_S16_FXP_o1_f;

  /* DataStoreWrite: '<S19>/Data Store Write12' */
  VSCError = TrqDriver_T10ms_C.DataTypeConversion1;

  /* DataStoreWrite: '<S19>/Data Store Write5' incorporates:
   *  Constant: '<S19>/ZERO'
   */
  FlgVSCActive = 0U;

  /* DataTypeConversion: '<S19>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S19>/Data Store Write13'
   *  Inport: '<Root>/GasPos'
   */
  GasPosCCIntHiR = (GasPos << 8);
}

/* System initialize for function-call system: '<S3>/FlgVSCFilt_Calc' */
void TrqDriver_FlgVSCFilt_Calc_Init(void)
{
  /* SystemInitialize for IfAction SubSystem: '<S14>/VehSpeedSetUpRateLim' */
  /* SystemInitialize for Outport: '<S51>/VehSpeedSetUpRateLim' */
  TrqDriver_T10ms_B.Conversion4 = 16U;

  /* End of SystemInitialize for SubSystem: '<S14>/VehSpeedSetUpRateLim' */
}

/* Output and update for function-call system: '<S3>/FlgVSCFilt_Calc' */
void TrqDriver_FlgVSCFilt_Calc(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_S8_o2;
  uint16_T rtb_LookUp_IR_U16;
  uint32_T tmp;
  uint8_T rtb_RelationalOperator2_b;
  boolean_T rtb_LogicalOperator;
  int32_T rtb_Conversion2;
  uint32_T rtb_Switch1;
  int16_T rtb_Conversion2_g;
  int16_T rtb_FOF_Reset_S16_FXP_o1_b;
  uint16_T rtb_DataStoreRead4;
  int32_T rtb_RateLimiter_S32;
  int32_T rtb_Conversion2_i;

  /* RelationalOperator: '<S14>/Relational Operator2' incorporates:
   *  DataStoreRead: '<S14>/Data Store Read2'
   */
  rtb_RelationalOperator2_b = (uint8_T)(VehSpeedSetUpInit !=
    TrqDriver_T10ms_B.Switch1);

  /* Outputs for Atomic SubSystem: '<S14>/VEHSPEEDSTEP_Cnt' */
  /* Switch: '<S49>/Switch' incorporates:
   *  Constant: '<S49>/Constant1'
   *  DataStoreWrite: '<S49>/Data Store Write3'
   *  Sum: '<S49>/Sum'
   */
  if (rtb_RelationalOperator2_b != 0) {
    VehSpeedSetUpRateCnt = 0U;
  } else {
    /* Sum: '<S49>/Sum' incorporates:
     *  Constant: '<S49>/Constant'
     *  DataStoreRead: '<S49>/Data Store Read3'
     */
    rtb_Conversion2 = (int32_T)(VehSpeedSetUpRateCnt + 1U);
    if (((uint32_T)rtb_Conversion2) > 255U) {
      rtb_Conversion2 = 255;
    }

    VehSpeedSetUpRateCnt = (uint8_T)rtb_Conversion2;
  }

  /* End of Switch: '<S49>/Switch' */

  /* RelationalOperator: '<S49>/Relational Operator' incorporates:
   *  Constant: '<S49>/VEHSPEEDSETUPFILTDELAY'
   *  DataStoreWrite: '<S49>/Data Store Write3'
   */
  VSCUseFilt = (uint8_T)(VehSpeedSetUpRateCnt >= VEHSPEEDSETUPFILTDELAY);

  /* Outputs for Enabled SubSystem: '<S49>/ResetRateLimiter' incorporates:
   *  EnablePort: '<S54>/Enable'
   */
  if (rtb_RelationalOperator2_b > 0) {
    /* DataTypeConversion: '<S54>/Conversion3' incorporates:
     *  DataStoreRead: '<S54>/Data Store Read1'
     *  DataStoreWrite: '<S54>/Data Store Write4'
     */
    VehSpeedSetUpRL = (VehSpeedSetUp << 5);

    /* DataStoreWrite: '<S54>/Data Store Write1' */
    VehSpeedSetUpInit = TrqDriver_T10ms_B.Switch1;
  }

  /* End of Outputs for SubSystem: '<S49>/ResetRateLimiter' */
  /* End of Outputs for SubSystem: '<S14>/VEHSPEEDSTEP_Cnt' */

  /* DataTypeConversion: '<S14>/Conversion2' */
  rtb_Conversion2 = (TrqDriver_T10ms_B.Switch1 << 9);

  /* If: '<S14>/If1' */
  if (VSCUseFilt == 0) {
    /* Outputs for IfAction SubSystem: '<S14>/VehSpeedSetUpRateLim' incorporates:
     *  ActionPort: '<S51>/Action Port'
     */
    /* DataTypeConversion: '<S51>/Conversion2' incorporates:
     *  Constant: '<S51>/VEHSPEEDSETUPRATE'
     */
    rtb_Conversion2_i = VEHSPEEDSETUPRATE;

    /* Gain: '<S51>/Gain' */
    rtb_RateLimiter_S32 = -rtb_Conversion2_i;

    /* S-Function (RateLimiter_S32): '<S62>/RateLimiter_S32' */
    RateLimiter_S32( &rtb_RateLimiter_S32, rtb_Conversion2, VehSpeedSetUpRL,
                    rtb_RateLimiter_S32, rtb_Conversion2_i);

    /* DataStoreWrite: '<S51>/Data Store Write4' */
    VehSpeedSetUpRL = rtb_RateLimiter_S32;

    /* DataTypeConversion: '<S51>/Conversion4' */
    TrqDriver_T10ms_B.Conversion4 = (uint16_T)(rtb_RateLimiter_S32 >> 5);

    /* End of Outputs for SubSystem: '<S14>/VehSpeedSetUpRateLim' */
  } else {
    /* Outputs for IfAction SubSystem: '<S14>/VehSpeedSetUpFOF' incorporates:
     *  ActionPort: '<S50>/Action Port'
     */
    /* Logic: '<S50>/Logical Operator' incorporates:
     *  Constant: '<S50>/VEHSPEEDSETUPFILTDELAY'
     *  Constant: '<S56>/Constant'
     *  DataStoreRead: '<S50>/Data Store Read1'
     *  DataStoreRead: '<S50>/Data Store Read5'
     *  RelationalOperator: '<S50>/Relational Operator'
     *  RelationalOperator: '<S56>/Compare'
     */
    rtb_LogicalOperator = ((VehSpeedSetUpRateCnt == VEHSPEEDSETUPFILTDELAY) ||
      (FlgVSCActive == 0));

    /* If: '<S50>/If' */
    if (rtb_LogicalOperator) {
      /* Outputs for IfAction SubSystem: '<S50>/Calc_KFilt' incorporates:
       *  ActionPort: '<S55>/Action Port'
       */
      /* Sum: '<S55>/Subtract' incorporates:
       *  Inport: '<Root>/VehSpeedCC'
       */
      DVehSpeedSetUp = (int16_T)(((TrqDriver_T10ms_B.Switch1 << 4) - VehSpeedCC)
        >> 4);

      /* S-Function (PreLookUpIdSearch_S8): '<S59>/PreLookUpIdSearch_S8' incorporates:
       *  Constant: '<S55>/BKDVEHSPEEDSETUP'
       *  Constant: '<S55>/BKDVEHSPEEDSETUP_dim'
       *  Constant: '<S55>/ZERO'
       */
      PreLookUpIdSearch_S8( &rtb_LookUp_IR_U16, &rtb_PreLookUpIdSearch_S8_o2,
                           DVehSpeedSetUp, &BKDVEHSPEEDSETUP[0], ((uint8_T)0U),
                           ((uint8_T)BKDVEHSPEEDSETUP_dim));

      /* S-Function (LookUp_IR_U16): '<S58>/LookUp_IR_U16' incorporates:
       *  Constant: '<S55>/BKDVEHSPEEDSETUP_dim'
       *  Constant: '<S55>/VTVSCKFILT'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTVSCKFILT[0], rtb_LookUp_IR_U16,
                    rtb_PreLookUpIdSearch_S8_o2, ((uint8_T)BKDVEHSPEEDSETUP_dim));

      /* DataStoreWrite: '<S55>/Data Store Write1' */
      VSCKFilt = rtb_LookUp_IR_U16;

      /* End of Outputs for SubSystem: '<S50>/Calc_KFilt' */
    }

    /* End of If: '<S50>/If' */

    /* DataStoreRead: '<S50>/Data Store Read4' */
    rtb_DataStoreRead4 = VSCKFilt;

    /* DataTypeConversion: '<S57>/Conversion2' incorporates:
     *  DataStoreRead: '<S50>/Data Store Read2'
     */
    rtb_Conversion2_g = (int16_T)VehSpeedSetUp;

    /* DataTypeConversion: '<S57>/Conversion3' */
    rtb_RelationalOperator2_b = rtb_LogicalOperator;

    /* DataTypeConversion: '<S57>/Conversion4' incorporates:
     *  DataStoreRead: '<S50>/Data Store Read3'
     */
    rtb_Conversion2 = (int32_T)VehSpeedSetUpHiR;

    /* DataTypeConversion: '<S57>/Conversion5' incorporates:
     *  DataTypeConversion: '<S50>/Conversion2'
     */
    rtb_FOF_Reset_S16_FXP_o1_b = (int16_T)(TrqDriver_T10ms_B.Switch1 << 4);

    /* S-Function (FOF_Reset_S16_FXP): '<S57>/FOF_Reset_S16_FXP' */
    FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1_b, &rtb_Conversion2,
                      rtb_FOF_Reset_S16_FXP_o1_b, rtb_DataStoreRead4,
                      rtb_Conversion2_g, rtb_RelationalOperator2_b,
                      rtb_Conversion2);

    /* DataTypeConversion: '<S57>/Conversion7' */
    TrqDriver_T10ms_B.Conversion7 = (uint32_T)rtb_Conversion2;

    /* DataTypeConversion: '<S61>/Conversion' */
    TrqDriver_T10ms_B.Conversion = (uint16_T)rtb_FOF_Reset_S16_FXP_o1_b;

    /* End of Outputs for SubSystem: '<S14>/VehSpeedSetUpFOF' */
  }

  /* End of If: '<S14>/If1' */

  /* Switch: '<S14>/Switch1' incorporates:
   *  Constant: '<S48>/Constant'
   *  DataTypeConversion: '<S14>/Conversion1'
   *  RelationalOperator: '<S48>/Compare'
   *  Switch: '<S14>/Switch'
   */
  if (VSCUseFilt == 0) {
    rtb_Switch1 = (((uint32_T)TrqDriver_T10ms_B.Conversion4) << 14);
    VehSpeedSetUp = TrqDriver_T10ms_B.Conversion4;
  } else {
    rtb_Switch1 = TrqDriver_T10ms_B.Conversion7;
    VehSpeedSetUp = TrqDriver_T10ms_B.Conversion;
  }

  /* End of Switch: '<S14>/Switch1' */

  /* If: '<S14>/If' incorporates:
   *  Constant: '<S14>/ST_VSC_NORMAL_CL_CTF'
   *  DataStoreRead: '<S14>/Data Store Read1'
   *  RelationalOperator: '<S14>/Relational Operator1'
   */
  if (StVehSpeedCtrl != ((uint8_T)ST_VSC_NORMAL_CL_CTF)) {
    /* Outputs for IfAction SubSystem: '<S14>/VehSpeedSetUp_nosat' incorporates:
     *  ActionPort: '<S52>/Action Port'
     */
    /* DataStoreWrite: '<S52>/Data Store Write3' incorporates:
     *  Constant: '<S52>/ONE'
     */
    FlgVSCActive = 1U;

    /* Sum: '<S52>/Add' incorporates:
     *  DataStoreWrite: '<S52>/Data Store Write4'
     *  Inport: '<Root>/VehSpeedCC'
     */
    VSCError = (int16_T)(VehSpeedSetUp - VehSpeedCC);

    /* MinMax: '<S53>/MinMax1' incorporates:
     *  DataStoreWrite: '<S52>/Data Store Write1'
     */
    VehSpeedSetUpHiR = rtb_Switch1;

    /* End of Outputs for SubSystem: '<S14>/VehSpeedSetUp_nosat' */
  } else {
    /* Outputs for IfAction SubSystem: '<S14>/VehSpeedSetUp_sat' incorporates:
     *  ActionPort: '<S53>/Action Port'
     */
    /* MinMax: '<S53>/MinMax' incorporates:
     *  Constant: '<S53>/THVSCSETUPCTF'
     *  Inport: '<Root>/VehSpeedCC'
     *  Sum: '<S53>/Add1'
     */
    tmp = (((uint32_T)((uint16_T)(((uint32_T)TrqDriver_T10ms_B.Switch1) +
              THVSCSETUPCTF))) << 4);
    if (VehSpeedCC < tmp) {
      /* MinMax: '<S53>/MinMax1' */
      VehSpeedSetUpHiR = (((uint32_T)VehSpeedCC) << 14);
    } else {
      /* MinMax: '<S53>/MinMax1' */
      VehSpeedSetUpHiR = (tmp << 14);
    }

    /* End of MinMax: '<S53>/MinMax' */

    /* MinMax: '<S53>/MinMax1' incorporates:
     *  DataStoreWrite: '<S53>/Data Store Write1'
     */
    if (rtb_Switch1 > VehSpeedSetUpHiR) {
      VehSpeedSetUpHiR = rtb_Switch1;
    }

    /* DataTypeConversion: '<S53>/Conversion1' incorporates:
     *  DataStoreWrite: '<S53>/Data Store Write1'
     *  DataStoreWrite: '<S53>/Data Store Write2'
     */
    VehSpeedSetUp = (uint16_T)(VehSpeedSetUpHiR >> 14);

    /* Sum: '<S53>/Add' incorporates:
     *  DataStoreWrite: '<S53>/Data Store Write2'
     *  DataStoreWrite: '<S53>/Data Store Write4'
     *  Inport: '<Root>/VehSpeedCC'
     */
    VSCError = (int16_T)(VehSpeedSetUp - VehSpeedCC);

    /* DataTypeConversion: '<S53>/Conversion2' incorporates:
     *  DataStoreWrite: '<S53>/Data Store Write1'
     *  DataStoreWrite: '<S53>/Data Store Write3'
     */
    VehSpeedSetUpRL = (int32_T)(VehSpeedSetUpHiR >> 9);

    /* End of Outputs for SubSystem: '<S14>/VehSpeedSetUp_sat' */
  }

  /* End of If: '<S14>/If' */
}

/* Output and update for function-call system: '<S3>/VehCtrl_CL_Init' */
void TrqDriver_VehCtrl_CL_Init_k(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_U16_U16;
  int32_T tmp;

  /* S-Function (LookUp_U16_U16): '<S83>/LookUp_U16_U16' incorporates:
   *  Constant: '<S17>/BKVEHSPSETUPCAN'
   *  Constant: '<S17>/BKVEHSPSETUPCAN_dim'
   *  Constant: '<S17>/VTGASPOSCCINIT'
   */
  LookUp_U16_U16( &rtb_LookUp_U16_U16, &VTGASPOSCCINIT[0],
                 TrqDriver_T10ms_B.Switch1, &BKVEHSPSETUPCAN[0], ((uint8_T)
    BKVEHSPSETUPCAN_dim));

  /* MinMax: '<S17>/MinMax' incorporates:
   *  DataStoreWrite: '<S17>/Data Store Write1'
   *  Inport: '<Root>/GasPos'
   */
  if (rtb_LookUp_U16_U16 < GasPos) {
    GasPosCC = rtb_LookUp_U16_U16;
  } else {
    GasPosCC = GasPos;
  }

  /* End of MinMax: '<S17>/MinMax' */

  /* DataTypeConversion: '<S17>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S17>/Data Store Write1'
   *  DataStoreWrite: '<S17>/Data Store Write13'
   */
  GasPosCCIntHiR = (GasPosCC << 8);

  /* Sum: '<S17>/Sum' incorporates:
   *  Constant: '<S17>/Constant'
   *  Constant: '<S17>/VEHSPEEDSETUPFILTDELAY'
   *  DataStoreWrite: '<S17>/Data Store Write7'
   */
  tmp = VEHSPEEDSETUPFILTDELAY - 1;
  if (tmp < 0) {
    tmp = 0;
  }

  VehSpeedSetUpRateCnt = (uint8_T)tmp;

  /* End of Sum: '<S17>/Sum' */

  /* DataStoreWrite: '<S17>/Data Store Write2' incorporates:
   *  Constant: '<S17>/ZERO'
   */
  FlgVSCActive = 0U;

  /* DataTypeConversion: '<S17>/Data Type Conversion1' incorporates:
   *  DataStoreWrite: '<S17>/Data Store Write6'
   *  Inport: '<Root>/VehSpeedCC'
   */
  VehSpeedSetUpHiR = (((uint32_T)VehSpeedCC) << 14);

  /* DataStoreWrite: '<S17>/Data Store Write4' */
  VehSpeedSetUpInit = TrqDriver_T10ms_B.Switch1;

  /* DataStoreWrite: '<S17>/Data Store Write5' incorporates:
   *  Inport: '<Root>/VehSpeedCC'
   */
  VehSpeedSetUp = VehSpeedCC;
}

/* Output and update for function-call system: '<S3>/VehCtrl_CL_Suspend' */
void TrqDriver_VehCtrl_CL_Suspend(void)
{
  /* MinMax: '<S18>/MinMax1' incorporates:
   *  DataStoreWrite: '<S18>/Data Store Write1'
   *  Inport: '<Root>/GasPos'
   */
  if (GasPos > 0U) {
    GasPosCC = GasPos;
  } else {
    GasPosCC = 0U;
  }

  /* End of MinMax: '<S18>/MinMax1' */

  /* DataTypeConversion: '<S18>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S18>/Data Store Write1'
   *  DataStoreWrite: '<S18>/Data Store Write13'
   */
  GasPosCCIntHiR = (GasPosCC << 8);
}

/* Output and update for function-call system: '<S3>/CmeDriverCAN_Smooth' */
void TrqDriver_CmeDriverCAN_Smooth(void)
{
  /* local block i/o variables */
  int16_T rtb_RateLimiter_S16;
  int16_T rtb_DataStoreRead1_d;
  int16_T rtb_Gain;

  /* DataStoreRead: '<S9>/Data Store Read1' */
  rtb_DataStoreRead1_d = CmeGasRpm;

  /* Gain: '<S9>/Gain' incorporates:
   *  Constant: '<S9>/CMECANDELTA'
   */
  rtb_Gain = (int16_T)(-CMECANDELTA);

  /* S-Function (RateLimiter_S16): '<S22>/RateLimiter_S16' incorporates:
   *  Constant: '<S9>/CMECANDELTA'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16, rtb_DataStoreRead1_d, CmeDriver,
                  rtb_Gain, CMECANDELTA);

  /* DataStoreWrite: '<S9>/Data Store Write1' */
  CmeDriver = rtb_RateLimiter_S16;

  /* DataStoreWrite: '<S9>/Data Store Write3' */
  CmeDriverCANF = rtb_RateLimiter_S16;

  /* RelationalOperator: '<S9>/Relational Operator' */
  TrqDriver_T10ms_B.RelationalOperator = (uint8_T)(rtb_DataStoreRead1_d ==
    rtb_RateLimiter_S16);
}

/* Output and update for function-call system: '<S3>/FlgVSCDisable_Calc' */
void TrqDriver_FlgVSCDisable_Calc(void)
{
  uint8_T tmp;

  /* Switch: '<S13>/Switch1' incorporates:
   *  Constant: '<S13>/SECOND_GEAR'
   *  Constant: '<S13>/VSCMINGEAR'
   *  Inport: '<Root>/EnTC2WZeroCC'
   */
  if (EnTC2WZeroCC != 0) {
    tmp = 2U;
  } else {
    tmp = VSCMINGEAR;
  }

  /* End of Switch: '<S13>/Switch1' */

  /* Logic: '<S13>/Logical Operator1' incorporates:
   *  Constant: '<S13>/BRAKE_INVALID'
   *  Constant: '<S13>/REC_NO_VSC'
   *  Constant: '<S46>/Constant'
   *  Constant: '<S47>/Constant'
   *  Inport: '<Root>/BrakeSignalCAN'
   *  Inport: '<Root>/GearPosClu'
   *  Inport: '<Root>/InjEnable'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S13>/Logical Operator2'
   *  RelationalOperator: '<S13>/Relational Operator'
   *  RelationalOperator: '<S13>/Relational Operator1'
   *  RelationalOperator: '<S46>/Compare'
   *  RelationalOperator: '<S47>/Compare'
   *  Selector: '<S13>/Selector_Outold2'
   */
  TrqDriver_T10ms_B.LogicalOperator1 = (uint8_T)(((((GearPosClu != 0) &&
    (GearPosClu < tmp)) || (InjEnable == 0)) || (BrakeSignalCAN == ((uint8_T)
    BRAKE_INVALID))) || (VtRec[(((uint8_T)REC_NO_VSC))] != 0));

  /* DataStoreWrite: '<S13>/Data Store Write4' */
  FlgVSCDisable = TrqDriver_T10ms_B.LogicalOperator1;
}

/* Output and update for function-call system: '<S3>/FlgVSCPause_Calc' */
void TrqDriver_FlgVSCPause_Calc(void)
{
  int16_T tmp;
  uint8_T tmp_0;

  /* Abs: '<S15>/Abs' incorporates:
   *  Inport: '<Root>/RollCAN'
   */
  if (RollCAN < 0) {
    tmp = (int16_T)(-RollCAN);
  } else {
    tmp = RollCAN;
  }

  /* End of Abs: '<S15>/Abs' */

  /* Switch: '<S15>/Switch1' incorporates:
   *  Constant: '<S15>/VEHSPEEDSETUPMIN'
   *  Constant: '<S15>/VEHSPEEDTC2WZMIN'
   *  Inport: '<Root>/EnTC2WZeroCC'
   */
  if (EnTC2WZeroCC != 0) {
    tmp_0 = VEHSPEEDTC2WZMIN;
  } else {
    tmp_0 = VEHSPEEDSETUPMIN;
  }

  /* End of Switch: '<S15>/Switch1' */

  /* Logic: '<S15>/Logical Operator' incorporates:
   *  Constant: '<S15>/BRAKE_ON'
   *  Constant: '<S15>/MAXROLLVEHCTRL'
   *  Constant: '<S15>/VEHSPEEDSETUPMAX'
   *  Constant: '<S64>/Constant'
   *  Constant: '<S65>/Constant'
   *  Constant: '<S66>/Constant'
   *  Constant: '<S67>/Constant'
   *  Constant: '<S68>/Constant'
   *  Constant: '<S69>/Constant'
   *  Constant: '<S70>/Constant'
   *  DataStoreRead: '<S15>/Data Store Read'
   *  Inport: '<Root>/BrakeSignal'
   *  Inport: '<Root>/BrakeSignalCAN'
   *  Inport: '<Root>/FlgGasNeg'
   *  Inport: '<Root>/FlgYawRec'
   *  Inport: '<Root>/GearDownSignal'
   *  Inport: '<Root>/GearPosClu'
   *  Inport: '<Root>/GearUpSignal'
   *  Inport: '<Root>/VehSpeedCC'
   *  Logic: '<S15>/Logical Operator1'
   *  Logic: '<S15>/Logical Operator2'
   *  Memory: '<S15>/Memory'
   *  RelationalOperator: '<S15>/Relational Operator'
   *  RelationalOperator: '<S15>/Relational Operator1'
   *  RelationalOperator: '<S15>/Relational Operator2'
   *  RelationalOperator: '<S15>/Relational Operator3'
   *  RelationalOperator: '<S15>/Relational Operator4'
   *  RelationalOperator: '<S64>/Compare'
   *  RelationalOperator: '<S65>/Compare'
   *  RelationalOperator: '<S66>/Compare'
   *  RelationalOperator: '<S67>/Compare'
   *  RelationalOperator: '<S68>/Compare'
   *  RelationalOperator: '<S69>/Compare'
   *  RelationalOperator: '<S70>/Compare'
   */
  TrqDriver_T10ms_B.LogicalOperator = (uint8_T)((((((((((GearPosClu == 0) ||
    (GearPosClu != TrqDriver_T10ms_DW.Memory_PreviousInput_d)) ||
    (BrakeSignalCAN == ((uint8_T)BRAKE_ON))) || (BrakeSignal != 0)) || (tmp >=
    MAXROLLVEHCTRL)) || (GearUpSignal != 0)) || (FlgYawRec == 1)) ||
    (GearDownSignal != 0)) || (FlgGasNeg != 0)) || ((FlgVSCPause != 0) &&
    (((((uint32_T)tmp_0) << 4) > VehSpeedCC) || (VehSpeedCC > (((uint32_T)
    VEHSPEEDSETUPMAX) << 4)))));

  /* DataStoreWrite: '<S15>/Data Store Write4' */
  FlgVSCPause = TrqDriver_T10ms_B.LogicalOperator;

  /* Update for Memory: '<S15>/Memory' incorporates:
   *  Inport: '<Root>/GearPosClu'
   */
  TrqDriver_T10ms_DW.Memory_PreviousInput_d = GearPosClu;
}

/* System initialize for function-call system: '<S1>/T10ms' */
void TrqDriver_T10ms_Init(void)
{
  TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_NO_ACTIVE_CHILD;
  TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_NO_ACTIVE_CHILD;
  TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_NO_ACTIVE_CHILD;
  TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_NO_ACTIVE_CHILD;
  TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NO_ACTIVE_CHILD;
  TrqDriver_T10ms_DW.is_active_c1_TrqDriver = 0U;
  TrqDriver_T10ms_DW.vehSpeedSetUpCANOld = 0U;

  /* SystemInitialize for Chart: '<S3>/CmeDriver_Management' incorporates:
   *  SubSystem: '<S3>/FlgVSCFilt_Calc'
   */
  TrqDriver_FlgVSCFilt_Calc_Init();
}

/* Output and update for function-call system: '<S1>/T10ms' */
void TrqDriver_T10ms(void)
{
  uint8_T rtb_Switch_g;

  /* Switch: '<S6>/Switch' incorporates:
   *  Constant: '<S6>/VEH_SPEED_CTRL_STS_CRUISE'
   *  DataTypeConversion: '<S6>/Data Type Conversion'
   *  Inport: '<Root>/EnTC2WZeroCC'
   *  Inport: '<Root>/VehSpeedCtrlCAN'
   *  Inport: '<Root>/VehSpeedLim'
   *  Inport: '<Root>/VehSpeedTC2WZero'
   *  Switch: '<S6>/Switch1'
   */
  if (EnTC2WZeroCC != 0) {
    rtb_Switch_g = ((uint8_T)VEH_SPEED_CTRL_STS_CRUISE);
    TrqDriver_T10ms_B.Switch1 = (uint16_T)(((uint32_T)VehSpeedTC2WZero) >> 4);
  } else {
    rtb_Switch_g = VehSpeedCtrlCAN;
    TrqDriver_T10ms_B.Switch1 = VehSpeedLim;
  }

  /* End of Switch: '<S6>/Switch' */

  /* Chart: '<S3>/CmeDriver_Management' incorporates:
   *  Constant: '<S3>/USECMECAN'
   *  Inport: '<Root>/FlgEOL'
   *  Inport: '<Root>/VehSpeedCC'
   *  Inport: '<Root>/VtRec'
   */
  /* Gateway: TrqDriver/T10ms/CmeDriver_Management */
  /* During: TrqDriver/T10ms/CmeDriver_Management */
  if (TrqDriver_T10ms_DW.is_active_c1_TrqDriver == 0U) {
    /* Entry: TrqDriver/T10ms/CmeDriver_Management */
    TrqDriver_T10ms_DW.is_active_c1_TrqDriver = 1U;

    /* Outputs for Function Call SubSystem: '<S3>/FlgVSCPause_Calc' */
    /* Entry Internal: TrqDriver/T10ms/CmeDriver_Management */
    /* Entry Internal 'VehicleSpeed_Conditions': '<S10>:81' */
    /* Transition: '<S10>:119' */
    /* Event: '<S10>:114' */
    TrqDriver_FlgVSCPause_Calc();

    /* End of Outputs for SubSystem: '<S3>/FlgVSCPause_Calc' */

    /* Outputs for Function Call SubSystem: '<S3>/FlgVSCDisable_Calc' */
    /* Event: '<S10>:113' */
    TrqDriver_FlgVSCDisable_Calc();

    /* End of Outputs for SubSystem: '<S3>/FlgVSCDisable_Calc' */
    /* Entry Internal 'VehicleSpeed_Control': '<S10>:62' */
    /* Transition: '<S10>:63' */
    if (ENVEHSPEEDCTRL == 0) {
      /* Transition: '<S10>:69' */
      TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_UNUSED;

      /* Entry 'UNUSED': '<S10>:68' */
      StVehSpeedCtrl = ((uint8_T)ST_VSC_UNUSED);

      /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
      /* Event: '<S10>:117' */
      TrqDriver_VehCtrl_Reset();

      /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
    } else {
      /* Transition: '<S10>:70' */
      if (TrqDriver_T10ms_B.LogicalOperator1 != 0) {
        /* Transition: '<S10>:77' */
        TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_USED;
        TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_DISABLED;

        /* Entry 'DISABLED': '<S10>:65' */
        StVehSpeedCtrl = ((uint8_T)ST_VSC_DISABLED);

        /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
        /* Event: '<S10>:117' */
        TrqDriver_VehCtrl_Reset();

        /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
      } else {
        /* Transition: '<S10>:85' */
        TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_USED;
        TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_NORMAL;

        /* Entry Internal 'NORMAL': '<S10>:80' */
        /* Transition: '<S10>:125' */
        TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_WAIT;

        /* Entry 'NORMAL_WAIT': '<S10>:124' */
        StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_WAIT);

        /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
        /* Event: '<S10>:117' */
        TrqDriver_VehCtrl_Reset();

        /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
      }
    }

    /* Outputs for Function Call SubSystem: '<S3>/CmeGasRpm_Calc' */
    /* Entry Internal 'CmeGasRpm_Calculation': '<S10>:6' */
    /* Transition: '<S10>:120' */
    /* Event: '<S10>:46' */
    TrqDriver_CmeGasRpm_Calc();

    /* End of Outputs for SubSystem: '<S3>/CmeGasRpm_Calc' */
    /* Entry Internal 'CMECAN_Calculation': '<S10>:5' */
    /* Transition: '<S10>:11' */
    if (((USECMECAN != 0) && (FlgEOL == 0)) && (VtRec[(((uint8_T)REC_NO_GAS))] ==
         0)) {
      /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Selection' */
      /* Transition: '<S10>:14' */
      /* Event: '<S10>:44' */
      TrqDrive_CmeDriverCAN_Selection();

      /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Selection' */

      /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
      /* Event: '<S10>:43' */
      TrqDrive_CmeDriverCAN_Diagnosis();

      /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
      StCmeCAN = ((uint8_T)CME_CAN_OK);
      TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_CAN;
      TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_OK;
    } else {
      /* Outputs for Function Call SubSystem: '<S3>/CmeGasRpm_Selection' */
      /* Transition: '<S10>:13' */
      /* Event: '<S10>:45' */
      TrqDriver_CmeGasRpm_Selection();

      /* End of Outputs for SubSystem: '<S3>/CmeGasRpm_Selection' */
      StCmeCAN = ((uint8_T)CME_GAS_RPM);
      TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_GAS_RPM;
    }
  } else {
    /* Outputs for Function Call SubSystem: '<S3>/FlgVSCPause_Calc' */
    /* During 'VehicleSpeed_Conditions': '<S10>:81' */
    /* During 'CALC': '<S10>:118' */
    /* Transition: '<S10>:83' */
    /* Event: '<S10>:114' */
    TrqDriver_FlgVSCPause_Calc();

    /* End of Outputs for SubSystem: '<S3>/FlgVSCPause_Calc' */

    /* Outputs for Function Call SubSystem: '<S3>/FlgVSCDisable_Calc' */
    /* Event: '<S10>:113' */
    TrqDriver_FlgVSCDisable_Calc();

    /* End of Outputs for SubSystem: '<S3>/FlgVSCDisable_Calc' */
    /* During 'VehicleSpeed_Control': '<S10>:62' */
    if (TrqDriver_T10ms_DW.is_VehicleSpeed_Control == TrqDriver_IN_UNUSED) {
      /* During 'UNUSED': '<S10>:68' */
      if (ENVEHSPEEDCTRL != 0) {
        /* Transition: '<S10>:67' */
        if (TrqDriver_T10ms_B.LogicalOperator1 != 0) {
          /* Transition: '<S10>:77' */
          TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_USED;
          TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_DISABLED;

          /* Entry 'DISABLED': '<S10>:65' */
          StVehSpeedCtrl = ((uint8_T)ST_VSC_DISABLED);

          /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
          /* Event: '<S10>:117' */
          TrqDriver_VehCtrl_Reset();

          /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
        } else {
          /* Transition: '<S10>:85' */
          TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_USED;
          TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_NORMAL;

          /* Entry Internal 'NORMAL': '<S10>:80' */
          /* Transition: '<S10>:125' */
          TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_WAIT;

          /* Entry 'NORMAL_WAIT': '<S10>:124' */
          StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_WAIT);

          /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
          /* Event: '<S10>:117' */
          TrqDriver_VehCtrl_Reset();

          /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
        }
      } else {
        /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
        /* Event: '<S10>:117' */
        TrqDriver_VehCtrl_Reset();

        /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
      }
    } else {
      /* During 'USED': '<S10>:71' */
      if (ENVEHSPEEDCTRL == 0) {
        /* Transition: '<S10>:73' */
        /* Exit Internal 'USED': '<S10>:71' */
        /* Exit Internal 'NORMAL': '<S10>:80' */
        TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NO_ACTIVE_CHILD;
        TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_NO_ACTIVE_CHILD;
        TrqDriver_T10ms_DW.is_VehicleSpeed_Control = TrqDriver_IN_UNUSED;

        /* Entry 'UNUSED': '<S10>:68' */
        StVehSpeedCtrl = ((uint8_T)ST_VSC_UNUSED);

        /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
        /* Event: '<S10>:117' */
        TrqDriver_VehCtrl_Reset();

        /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
      } else if (TrqDriver_T10ms_B.LogicalOperator1 != 0) {
        /* Transition: '<S10>:89' */
        /* Exit Internal 'USED': '<S10>:71' */
        /* Exit Internal 'NORMAL': '<S10>:80' */
        TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NO_ACTIVE_CHILD;
        TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_DISABLED;

        /* Entry 'DISABLED': '<S10>:65' */
        StVehSpeedCtrl = ((uint8_T)ST_VSC_DISABLED);

        /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
        /* Event: '<S10>:117' */
        TrqDriver_VehCtrl_Reset();

        /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
      } else if (TrqDriver_T10ms_DW.is_USED == TrqDriver_IN_DISABLED) {
        /* During 'DISABLED': '<S10>:65' */
        if (TrqDriver_T10ms_B.LogicalOperator1 == 0) {
          /* Transition: '<S10>:90' */
          TrqDriver_T10ms_DW.is_USED = TrqDriver_IN_NORMAL;

          /* Entry Internal 'NORMAL': '<S10>:80' */
          /* Transition: '<S10>:125' */
          TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_WAIT;

          /* Entry 'NORMAL_WAIT': '<S10>:124' */
          StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_WAIT);

          /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
          /* Event: '<S10>:117' */
          TrqDriver_VehCtrl_Reset();

          /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
        } else {
          /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
          /* Event: '<S10>:117' */
          TrqDriver_VehCtrl_Reset();

          /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
        }
      } else {
        /* During 'NORMAL': '<S10>:80' */
        if (TrqDriver_T10ms_B.LogicalOperator != 0) {
          /* Transition: '<S10>:149' */
          /* Exit Internal 'NORMAL': '<S10>:80' */
          TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_PAUSED;

          /* Entry 'PAUSED': '<S10>:147' */
          StVehSpeedCtrl = ((uint8_T)ST_VSC_PAUSED);

          /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
          /* Event: '<S10>:117' */
          TrqDriver_VehCtrl_Reset();

          /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
        } else {
          switch (TrqDriver_T10ms_DW.is_NORMAL) {
           case TrqDriver_IN_NORMAL_CL:
            /* During 'NORMAL_CL': '<S10>:126' */
            if (rtb_Switch_g != ((uint8_T)VEH_SPEED_CTRL_STS_CRUISE)) {
              /* Transition: '<S10>:132' */
              TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_WAIT;

              /* Entry 'NORMAL_WAIT': '<S10>:124' */
              StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_WAIT);

              /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
              /* Event: '<S10>:117' */
              TrqDriver_VehCtrl_Reset();

              /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
            } else if ((GasPosCC == 0) && (VehSpeedCC > VehSpeedSetUp)) {
              /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_CL_Suspend' */
              /* Transition: '<S10>:153' */
              /* Event: '<S10>:155' */
              TrqDriver_VehCtrl_CL_Suspend();

              /* End of Outputs for SubSystem: '<S3>/VehCtrl_CL_Suspend' */

              /* Outputs for Function Call SubSystem: '<S3>/FlgVSCFilt_Calc' */
              /* Event: '<S10>:133' */
              TrqDriver_FlgVSCFilt_Calc();

              /* End of Outputs for SubSystem: '<S3>/FlgVSCFilt_Calc' */
              TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_CL_CTF;

              /* Entry 'NORMAL_CL_CTF': '<S10>:151' */
              StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_CL_CTF);
            } else {
              /* Transition: '<S10>:163' */
              if (((TrqDriver_T10ms_B.Switch1 -
                    TrqDriver_T10ms_DW.vehSpeedSetUpCANOld) > MAXVEHSPEEDSTEP) ||
                  ((TrqDriver_T10ms_DW.vehSpeedSetUpCANOld -
                    TrqDriver_T10ms_B.Switch1) > MAXVEHSPEEDSTEP)) {
                /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_CL_Init' */
                /* Transition: '<S10>:165' */
                /* Event: '<S10>:134' */
                TrqDriver_VehCtrl_CL_Init_k();

                /* End of Outputs for SubSystem: '<S3>/VehCtrl_CL_Init' */
              } else {
                /* Transition: '<S10>:166' */
              }

              /* Outputs for Function Call SubSystem: '<S3>/FlgVSCFilt_Calc' */
              /* Transition: '<S10>:167' */
              /* Event: '<S10>:133' */
              TrqDriver_FlgVSCFilt_Calc();

              /* End of Outputs for SubSystem: '<S3>/FlgVSCFilt_Calc' */

              /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_CL' */
              /* Event: '<S10>:116' */
              TrqDriver_VehCtrl_CL();

              /* End of Outputs for SubSystem: '<S3>/VehCtrl_CL' */
              TrqDriver_T10ms_DW.vehSpeedSetUpCANOld = TrqDriver_T10ms_B.Switch1;
            }
            break;

           case TrqDriver_IN_NORMAL_CL_CTF:
            /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_CL_Suspend' */
            /* During 'NORMAL_CL_CTF': '<S10>:151' */
            /* Transition: '<S10>:160' */
            /* Event: '<S10>:155' */
            TrqDriver_VehCtrl_CL_Suspend();

            /* End of Outputs for SubSystem: '<S3>/VehCtrl_CL_Suspend' */

            /* Outputs for Function Call SubSystem: '<S3>/FlgVSCFilt_Calc' */
            /* Event: '<S10>:133' */
            TrqDriver_FlgVSCFilt_Calc();

            /* End of Outputs for SubSystem: '<S3>/FlgVSCFilt_Calc' */
            if (VehSpeedCC < VehSpeedSetUp) {
              /* Transition: '<S10>:154' */
              FlgVSCActive = 0U;
              TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_CL;

              /* Entry 'NORMAL_CL': '<S10>:126' */
              StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_CL);
              TrqDriver_T10ms_DW.vehSpeedSetUpCANOld = TrqDriver_T10ms_B.Switch1;
            } else {
              /* Transition: '<S10>:161' */
            }
            break;

           case TrqDriver_IN_NORMAL_WAIT:
            /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
            /* During 'NORMAL_WAIT': '<S10>:124' */
            /* Event: '<S10>:117' */
            TrqDriver_VehCtrl_Reset();

            /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
            /* Transition: '<S10>:128' */
            if (rtb_Switch_g == ((uint8_T)VEH_SPEED_CTRL_STS_CRUISE)) {
              /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_CL_Init' */
              /* Transition: '<S10>:129' */
              /* Event: '<S10>:134' */
              TrqDriver_VehCtrl_CL_Init_k();

              /* End of Outputs for SubSystem: '<S3>/VehCtrl_CL_Init' */

              /* Outputs for Function Call SubSystem: '<S3>/FlgVSCFilt_Calc' */
              /* Event: '<S10>:133' */
              TrqDriver_FlgVSCFilt_Calc();

              /* End of Outputs for SubSystem: '<S3>/FlgVSCFilt_Calc' */
              TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_CL;

              /* Entry 'NORMAL_CL': '<S10>:126' */
              StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_CL);
              TrqDriver_T10ms_DW.vehSpeedSetUpCANOld = TrqDriver_T10ms_B.Switch1;
            } else {
              /* Transition: '<S10>:131' */
            }
            break;

           default:
            /* During 'PAUSED': '<S10>:147' */
            if ((rtb_Switch_g != ((uint8_T)VEH_SPEED_CTRL_STS_CRUISE)) &&
                (TrqDriver_T10ms_B.LogicalOperator == 0)) {
              /* Transition: '<S10>:150' */
              TrqDriver_T10ms_DW.is_NORMAL = TrqDriver_IN_NORMAL_WAIT;

              /* Entry 'NORMAL_WAIT': '<S10>:124' */
              StVehSpeedCtrl = ((uint8_T)ST_VSC_NORMAL_WAIT);

              /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
              /* Event: '<S10>:117' */
              TrqDriver_VehCtrl_Reset();

              /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
            } else {
              /* Outputs for Function Call SubSystem: '<S3>/VehCtrl_Reset' */
              /* Event: '<S10>:117' */
              TrqDriver_VehCtrl_Reset();

              /* End of Outputs for SubSystem: '<S3>/VehCtrl_Reset' */
            }
            break;
          }
        }
      }
    }

    /* Outputs for Function Call SubSystem: '<S3>/CmeGasRpm_Calc' */
    /* During 'CmeGasRpm_Calculation': '<S10>:6' */
    /* During 'CALC': '<S10>:121' */
    /* Transition: '<S10>:122' */
    /* Event: '<S10>:46' */
    TrqDriver_CmeGasRpm_Calc();

    /* End of Outputs for SubSystem: '<S3>/CmeGasRpm_Calc' */
    /* During 'CMECAN_Calculation': '<S10>:5' */
    switch (TrqDriver_T10ms_DW.is_CMECAN_Calculation) {
     case TrqDriver_IN_CME_CAN:
      /* During 'CME_CAN': '<S10>:10' */
      if (((USECMECAN == 0) || (FlgEOL != 0)) && (VtRec[(((uint8_T)REC_NO_GAS))]
           == 0)) {
        /* Transition: '<S10>:51' */
        if (((USECMECAN != 0) && (FlgEOL == 0)) && (VtRec[(((uint8_T)REC_NO_GAS))]
             == 0)) {
          /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Selection' */
          /* Transition: '<S10>:14' */
          /* Event: '<S10>:44' */
          TrqDrive_CmeDriverCAN_Selection();

          /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Selection' */

          /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
          /* Event: '<S10>:43' */
          TrqDrive_CmeDriverCAN_Diagnosis();

          /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
          StCmeCAN = ((uint8_T)CME_CAN_OK);

          /* Exit Internal 'CME_CAN': '<S10>:10' */
          TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_CAN;
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_OK;
        } else {
          /* Outputs for Function Call SubSystem: '<S3>/CmeGasRpm_Selection' */
          /* Transition: '<S10>:13' */
          /* Event: '<S10>:45' */
          TrqDriver_CmeGasRpm_Selection();

          /* End of Outputs for SubSystem: '<S3>/CmeGasRpm_Selection' */
          StCmeCAN = ((uint8_T)CME_GAS_RPM);

          /* Exit Internal 'CME_CAN': '<S10>:10' */
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_NO_ACTIVE_CHILD;
          TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_GAS_RPM;
        }
      } else if (VtRec[(((uint8_T)REC_NO_GAS))] == 1) {
        /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Smooth' */
        /* Transition: '<S10>:22' */
        /* Event: '<S10>:47' */
        TrqDriver_CmeDriverCAN_Smooth();

        /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Smooth' */
        StCmeCAN = ((uint8_T)CME_REC_ACTIVE);

        /* Exit Internal 'CME_CAN': '<S10>:10' */
        TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_NO_ACTIVE_CHILD;
        TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_REC_ACTIVE;
      } else if (TrqDriver_T10ms_DW.is_CME_CAN == TrqDriver_IN_CME_CAN_FREEZE) {
        /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
        /* During 'CME_CAN_FREEZE': '<S10>:3' */
        /* Transition: '<S10>:25' */
        /* Event: '<S10>:43' */
        TrqDrive_CmeDriverCAN_Diagnosis();

        /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
        if (TrqDriver_T10ms_B.RelationalOperator_p == 0) {
          /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Selection' */
          /* Transition: '<S10>:19' */
          /* Event: '<S10>:44' */
          TrqDrive_CmeDriverCAN_Selection();

          /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Selection' */
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_OK;
        } else {
          /* Transition: '<S10>:12' */
          StCmeCAN = ((uint8_T)CME_CAN_FREEZE);
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_FREEZE;
        }
      } else {
        /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
        /* During 'CME_CAN_OK': '<S10>:2' */
        /* Transition: '<S10>:16' */
        /* Event: '<S10>:43' */
        TrqDrive_CmeDriverCAN_Diagnosis();

        /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
        if (TrqDriver_T10ms_B.RelationalOperator_p == 0) {
          /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Selection' */
          /* Transition: '<S10>:19' */
          /* Event: '<S10>:44' */
          TrqDrive_CmeDriverCAN_Selection();

          /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Selection' */
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_OK;
        } else {
          /* Transition: '<S10>:12' */
          StCmeCAN = ((uint8_T)CME_CAN_FREEZE);
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_FREEZE;
        }
      }
      break;

     case TrqDriver_IN_CME_GAS_RPM:
      /* During 'CME_GAS_RPM': '<S10>:1' */
      /* Transition: '<S10>:15' */
      if (((USECMECAN != 0) && (FlgEOL == 0)) && (VtRec[(((uint8_T)REC_NO_GAS))]
           == 0)) {
        /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Selection' */
        /* Transition: '<S10>:14' */
        /* Event: '<S10>:44' */
        TrqDrive_CmeDriverCAN_Selection();

        /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Selection' */

        /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
        /* Event: '<S10>:43' */
        TrqDrive_CmeDriverCAN_Diagnosis();

        /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
        StCmeCAN = ((uint8_T)CME_CAN_OK);
        TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_CAN;
        TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_OK;
      } else {
        /* Outputs for Function Call SubSystem: '<S3>/CmeGasRpm_Selection' */
        /* Transition: '<S10>:13' */
        /* Event: '<S10>:45' */
        TrqDriver_CmeGasRpm_Selection();

        /* End of Outputs for SubSystem: '<S3>/CmeGasRpm_Selection' */
        StCmeCAN = ((uint8_T)CME_GAS_RPM);
        TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_GAS_RPM;
      }
      break;

     default:
      /* During 'CME_REC_ACTIVE': '<S10>:9' */
      /* Transition: '<S10>:21' */
      if (TrqDriver_T10ms_B.RelationalOperator != 0) {
        /* Transition: '<S10>:20' */
        if (((USECMECAN != 0) && (FlgEOL == 0)) && (VtRec[(((uint8_T)REC_NO_GAS))]
             == 0)) {
          /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Selection' */
          /* Transition: '<S10>:14' */
          /* Event: '<S10>:44' */
          TrqDrive_CmeDriverCAN_Selection();

          /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Selection' */

          /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
          /* Event: '<S10>:43' */
          TrqDrive_CmeDriverCAN_Diagnosis();

          /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Diagnosis' */
          StCmeCAN = ((uint8_T)CME_CAN_OK);
          TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_CAN;
          TrqDriver_T10ms_DW.is_CME_CAN = TrqDriver_IN_CME_CAN_OK;
        } else {
          /* Outputs for Function Call SubSystem: '<S3>/CmeGasRpm_Selection' */
          /* Transition: '<S10>:13' */
          /* Event: '<S10>:45' */
          TrqDriver_CmeGasRpm_Selection();

          /* End of Outputs for SubSystem: '<S3>/CmeGasRpm_Selection' */
          StCmeCAN = ((uint8_T)CME_GAS_RPM);
          TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_GAS_RPM;
        }
      } else {
        /* Outputs for Function Call SubSystem: '<S3>/CmeDriverCAN_Smooth' */
        /* Transition: '<S10>:18' */
        /* Event: '<S10>:47' */
        TrqDriver_CmeDriverCAN_Smooth();

        /* End of Outputs for SubSystem: '<S3>/CmeDriverCAN_Smooth' */
        TrqDriver_T10ms_DW.is_CMECAN_Calculation = TrqDriver_IN_CME_REC_ACTIVE;
      }
      break;
    }
  }

  /* End of Chart: '<S3>/CmeDriver_Management' */
}

/* Output and update for function-call system: '<S1>/Init' */
void TrqDriver_Init(void)
{
  /* DataTypeConversion: '<S2>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S2>/Data Store Write13'
   *  Inport: '<Root>/GasPos'
   */
  GasPosCCIntHiR = (GasPos << 8);

  /* DataStoreWrite: '<S2>/Data Store Write' incorporates:
   *  Constant: '<S2>/NOT_OK'
   */
  FlgCmeDriverCANOK = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write10' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  FlgVSCPause = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write14' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  FlgGasPosCCFilt = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write18' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  VehSpeedSetUpRateCnt = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write5' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  FlgVSCActive = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write9' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  FlgVSCDisable = 0U;

  /* DataTypeConversion: '<S2>/Data Type Conversion1' incorporates:
   *  DataStoreWrite: '<S2>/Data Store Write15'
   *  Inport: '<Root>/GasPos'
   */
  GasPosCCFiltHiR = (GasPos << 14);

  /* DataStoreWrite: '<S2>/Data Store Write11' incorporates:
   *  Constant: '<S2>/ZERO2'
   */
  VehSpeedSetUpHiR = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write12' incorporates:
   *  Constant: '<S2>/ZERO1'
   */
  VSCError = 0;

  /* DataStoreWrite: '<S2>/Data Store Write17' */
  VehSpeedSetUp = TrqDriver_Init_C.DataTypeConversion2;

  /* DataStoreWrite: '<S2>/Data Store Write16' incorporates:
   *  Constant: '<S2>/ZERO3'
   */
  VehSpeedSetUpInit = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeDriverCANF = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write19' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeGasRpmX0 = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeDriver = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write20' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeDriverF = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write21' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  DeltaCmeDriverF = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write3' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeGasRpm = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write6' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeDriverTmp = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmeTargetWot = ((int16_T)CME_INIT);

  /* DataStoreWrite: '<S2>/Data Store Write8' incorporates:
   *  Constant: '<S2>/CME_INIT'
   */
  CmiTargetWot = ((int16_T)CME_INIT);

  /* Constant: '<S2>/ID_TRQ_DRIVER' */
  IDTrqDriver = ID_TRQ_DRIVER;

  /* DataStoreWrite: '<S2>/Data Store Write4' incorporates:
   *  Inport: '<Root>/GasPos'
   */
  GasPosCC = GasPos;
}

/* Model step function */
void TrqDriver_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/TrqDriver' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_TrqDriver_Reset' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' incorporates:
   *  Inport: '<Root>/ev_NoSync'
   */
  if (((TrqDriver_U.ev_PowerOn > 0) &&
       (TrqDriver_PrevZCSigState.fc_TrqDriver_Reset_Trig_ZCE[0] != POS_ZCSIG)) ||
      ((TrqDriver_U.ev_NoSync > 0) &&
       (TrqDriver_PrevZCSigState.fc_TrqDriver_Reset_Trig_ZCE[1] != POS_ZCSIG)))
  {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    TrqDriver_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  TrqDriver_PrevZCSigState.fc_TrqDriver_Reset_Trig_ZCE[0] = (ZCSigState)
    (TrqDriver_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */

  /* Inport: '<Root>/ev_NoSync' */
  TrqDriver_PrevZCSigState.fc_TrqDriver_Reset_Trig_ZCE[1] = (ZCSigState)
    (TrqDriver_U.ev_NoSync > 0);

  /* End of Outputs for SubSystem: '<S1>/fc_TrqDriver_Reset' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_TrqDriver_Calc' incorporates:
   *  TriggerPort: '<S4>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((TrqDriver_U.ev_T10ms > 0) &&
      (TrqDriver_PrevZCSigState.fc_TrqDriver_Calc_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    TrqDriver_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S4>/Function-Call Generator' */
  }

  TrqDriver_PrevZCSigState.fc_TrqDriver_Calc_Trig_ZCE = (ZCSigState)
    (TrqDriver_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/fc_TrqDriver_Calc' */

  /* End of Outputs for SubSystem: '<Root>/TrqDriver' */
}

/* Initialize for function-call system: '<S1>/T10ms' */
void TrqDriver_T10ms_initialize(void)
{
  (void) memset((void *) &TrqDriver_T10ms_B, 0,
                sizeof(rtB_T10ms_TrqDriver));

  /* custom signals */
  VSCGasPosCCProp = 0;
  VSCGasPosCCInt = 0;
  DVehSpeedSetUp = 0;
  VSCUseFilt = 0U;
  (void) memset((void *)&TrqDriver_T10ms_DW, 0,
                sizeof(rtDW_T10ms_TrqDriver));
}

/* Initialize for function-call system: '<S1>/Init' */
void TrqDriver_Init_initialize(void)
{
  /* custom signals */
  IDTrqDriver = 0U;
}

/* Model initialize function */
void TrqDriver_initialize(void)
{
  /* Registration code */

  /* initialize error status */
  rtmSetErrorStatus(TrqDriver_M, (NULL));

  /* states (dwork) */

  /* custom states */
  GasPosCCFiltHiR = 0;
  VehSpeedSetUpRL = 0;
  VehSpeedSetUpHiR = 0U;
  GasPosCCIntHiR = 0;
  VehSpeedSetUpInit = 0U;
  TDrivRpmIndex = 0U;
  TDrivGasIndex = 0U;
  VehSpeedSetUp = 0U;
  GasPosCC = 0U;
  CmeDriver = 0;
  CmeGasRpmX0 = 0;
  CmeDriverF = 0;
  DeltaCmeDriverF = 0;
  CmeGasRpm = 0;
  CmeDriverTmp = 0;
  CmeTargetWot = 0;
  CmeDriverCANF = 0;
  CmiTargetWot = 0;
  VSCKFilt = 0U;
  TDrivRpmRatio = 0U;
  TDrivGasRatio = 0U;
  VSCError = 0;
  FlgCmeDriverCANOK = 0U;
  VehSpeedSetUpRateCnt = 0U;
  StCmeCAN = 0U;
  FlgVSCPause = 0U;
  FlgVSCDisable = 0U;
  StVehSpeedCtrl = 0U;
  FlgVSCActive = 0U;
  FlgGasPosCCFilt = 0U;

  /* external inputs */
  (void)memset(&TrqDriver_U, 0, sizeof(ExternalInputs_TrqDriver));

  /* Initialize subsystem data */
  TrqDriver_T10ms_initialize();
  TrqDriver_Init_initialize();
  TrqDriver_PrevZCSigState.fc_TrqDriver_Calc_Trig_ZCE = POS_ZCSIG;
  TrqDriver_PrevZCSigState.fc_TrqDriver_Reset_Trig_ZCE[0] = POS_ZCSIG;
  TrqDriver_PrevZCSigState.fc_TrqDriver_Reset_Trig_ZCE[1] = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/TrqDriver' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/fc_TrqDriver_Calc' */
  /* SystemInitialize for S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  TrqDriver_T10ms_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S4>/Function-Call Generator' */
  /* End of SystemInitialize for SubSystem: '<S1>/fc_TrqDriver_Calc' */

  /* End of SystemInitialize for SubSystem: '<Root>/TrqDriver' */
}

/* user code (bottom of source file) */
/* System '<Root>/TrqDriver' */
#endif                                 //_BUILD_TRQDRIVER_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
