#include "typedefs.h"
#include "sci.h"

#if (TARGET_TYPE == MPC5554)
#include "mpc5554.h"
#elif (TARGET_TYPE == MPC5534)
#include "mpc5534.h"
#elif (TARGET_TYPE == MPC5633  || TARGET_TYPE == MPC5634)
#include "mpc563m.h"
#else
#error ERROR: Target not supported
#endif


#ifdef _BUILD_LINMGM_


#define NUM_OF_LINISR   2

/**************************************************************/
TaskType LinIRQ_IsrFuncs[NUM_OF_LINISR] = { (TaskType)(-1) };


int16_t LINSCI_SetInterrupt(uint8_t linIrqNum, TaskType isrFunc)
{
    LinIRQ_IsrFuncs[linIrqNum] = isrFunc;
    
    return NO_ERROR;

}

/* l_ifc_rx   see paragraph 7.2.5.5 of LIN 2.1 spec. package         */
void l_ifc_rx_master(void)
{

    EDMA.CIRQR.R = DMA_ESCI_COMBRX; 

    if (LinIRQ_IsrFuncs[0] != (TaskType)(-1))
    {
        ActivateTask(LinIRQ_IsrFuncs[0]);
    }
}

/* l_ifc_tx   see paragraph 7.2.5.6 of LIN 2.1 spec. package         */
void l_ifc_tx_master(void)
{
    EDMA.CIRQR.R = DMA_ESCI_COMBTX;

    if (LinIRQ_IsrFuncs[1] != (TaskType)(-1))
    {
        ActivateTask(LinIRQ_IsrFuncs[1]);
    }
}



#endif





