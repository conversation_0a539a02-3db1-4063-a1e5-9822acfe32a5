/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/MCU/appl_calib/trunk/tree/DD/COMMON/HeatGripDriveMg#$   */
/* $ Description:                                                                                                */
/* $Revision:: 6369   $                                                                                          */
/* $Date:: 2014-03-25 09:13:52 +0100 (mar, 25 mar 2014)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              GasPosFiltMgm.h
 *
 * Author(s): Lana L.
 * 
 * 
 * Description:
 * 
 *
 * Usage notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifndef _GASPOSFILTMGM_H_
#define _GASPOSFILTMGM_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "mathlib.h"
#include "gaspos_mgm.h"
#include "Digitalin.h"
#include "gasposfilt_mgm.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define GASPOSFILT_DEEP 40

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern uint8_T GASPOSFILTDEEP;
extern uint8_T GASPOSFILTDEEPSHORT;
extern uint8_T GASPOSFILTDECIM;
extern uint8_T TIMGASSUPHILL;
extern uint16_T THRGASATTACH;
extern uint16_T MINGASATTACH;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
 /*--------------------------------------------------------------------------*
 * GasPosFiltMgm_T5m - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
/* None */

#endif 

/****************************************************************************
 ****************************************************************************/
 

