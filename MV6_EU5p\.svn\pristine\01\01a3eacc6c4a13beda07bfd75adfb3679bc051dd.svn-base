/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _OSEK_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "OS_hooks.h"
#include "task.h"
#include "mpc5500_spr_macros.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* None */
/* Example:
uint16_T templatePublicVar;
*/

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/

#define FILLED_LIST             -1
#define EMPTY_LIST              -2


/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/

typedef struct
{
    TaskType buffer[FIFO_HANDLER_DIM];
    uint8_t   r_index;
    uint8_t   w_index;
} OSBuff;


/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * OSGetLinkRegister - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void OSGetLinkRegister(void (*pFunc)(void));

/*--------------------------------------------------------------------------*
 * OSTASK_TaskHandlingFunction - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t OSTASK_TaskHandlingFunction(TaskType taskId ,
                                            uint8_t index);

/*--------------------------------------------------------------------------*
 * OSSetOnHandlerManager - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t OSSetOnHandlerManager(TaskType taskId,
                                      uint8_t index);

/*--------------------------------------------------------------------------*
 * OSGetFromHandlerManager - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t OSGetFromHandlerManager(uint8_t index,
                                        TaskType *tmptaskId);

/*--------------------------------------------------------------------------*
 * OSSetTaskID - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static StatusType OSSetTaskID( TaskType  TaskId);

/*--------------------------------------------------------------------------*
 * _blr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * AM - MISRA 2004 Rule 2.1: in-line assembly must be embedded in dedicated 
 *      assembly procedures only
 *--------------------------------------------------------------------------*/
 static inline void _blr(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static OSBuff  OSHandler_Manager[SOFT_SET_INTERRUPT_NUM];  

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ActivateTask - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType ActivateTask( TaskType taskId)
{
    uint8_t index;
    int16_t retFcnValue;
    StatusType retValue = E_OK;

    OSrtiSetServiceWatch(OSServiceId_ActivateTask);
    index = OSTASKPRI(taskId);
    OSTASKSTATUS(taskId) = READY;

    //   OSrtiSetOldServiceID(OSServiceId_ActivateTask);
    retFcnValue=OSTASK_TaskHandlingFunction(taskId,index);
    if (retFcnValue==NO_ERROR) 
    {
        //      OSrtiResetIdOnExit();
        OSrtiServiceWatchOnExit(OSServiceId_ActivateTask);
    }
    else
    {
        OSRETERROR( E_OS_LIMIT, OSServiceId_ActivateTask, taskId );
        retValue = E_OS_LIMIT;
    }
    return retValue;
}

/*--------------------------------------------------------------------------*
 * GetTaskID - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType GetTaskID( TaskRefType  ptrTaskId)
{
    OSrtiSetServiceWatch(OSServiceId_GetTaskID);
    *ptrTaskId = runningTaskId;
    OSrtiServiceWatchOnExit(OSServiceId_GetTaskID);
    return (E_OK);    
}



/*--------------------------------------------------------------------------*
 * Schedule - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType Schedule(void)
{
    register TaskType taskId;
    TaskType currentTaskId;

    OSrtiSetServiceWatch(OSServiceId_Schedule);
    OSrtiSetOldServiceID(OSServiceId_Schedule);
    if(OSGetFromHandlerManager(FIFO_QUEUE_ID(), &currentTaskId)==0)
    /* retrieve the user defined   */
    /* ISR from the associate FIFO */
    /* list                        */
    {
        /* check sul current task e deallocazione delle sue risorse */

        taskId = currentTaskId;

        GetTaskID(&currentTaskId);

        OSTASKSTATUS(taskId) = RUNNING;
        OSSetTaskID(taskId);
        {
            DisableAllInterrupts();
            PreTaskHook();
            OSTASKSTACKPTR(taskId) = currentTaskStkPtr;
            EnableAllInterrupts();
        }

        OSGetLinkRegister(OSTASKENTRY(taskId));

        {
            DisableAllInterrupts();
            currentTaskStkPtr = OSTASKSTACKPTR(runningTaskId);
            PostTaskHook();
            EnableAllInterrupts();
        }
    }

    DisableAllInterrupts();
    OSSetTaskID(currentTaskId);
    EnableAllInterrupts();
    OSrtiResetIdOnExit()    /* without ; for MISRA 14.3 */

    return (E_OK);
}                                                                                                                  
                                                                                    
/*--------------------------------------------------------------------------*
 * TerminateTask - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType TerminateTask(void)
{
    OSrtiSetServiceWatch(OSServiceId_TerminateTask);
    OSTASKSTATUS(runningTaskId) = SUSPENDED;
    OSrtiServiceWatchOnExit(OSServiceId_TerminateTask);
    __MTSPR(SPR_LR,terminationAddress);
    _blr();

    /* never return from this point */
    return(E_OS_STATE);
}

/*--------------------------------------------------------------------------*
* OSInitHandlerManager - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
void OSInitHandlerManager(void)
{
    uint8_t elem_index,buf_index;

    for (buf_index=0;buf_index<SOFT_SET_INTERRUPT_NUM;buf_index++)
    {
        for (elem_index=0;elem_index<FIFO_HANDLER_DIM;elem_index++)
        {
            OSHandler_Manager[buf_index].buffer[elem_index]=0;
        }
        OSHandler_Manager[buf_index].r_index=0;
        OSHandler_Manager[buf_index].w_index=0;
    }
}


/*--------------------------------------------------------------------------*
* GetTaskState - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
StatusType GetTaskState( TaskType TaskId,
                         TaskStateRefType  ptrState)
{
    OSrtiSetServiceWatch(OSServiceId_GetTaskState);
    *ptrState = OSTASKSTATUS(TaskId);
    OSrtiServiceWatchOnExit(OSServiceId_GetTaskState);

    return (E_OK);    
}


/*--------------------------------------------------------------------------*
* ChainTask - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
StatusType ChainTask( TaskType TaskId)
{
    uint8_t index;
    TaskType currentTaskId;
    StatusType retValue = E_OS_STATE;

    OSrtiSetServiceWatch(OSServiceId_ChainTask);
    GetTaskID(&currentTaskId);

    if (currentTaskId == TaskId)
    {
        /* rem: overrun di task identici */
        OSRETERROR(E_OS_LIMIT,OSServiceId_ChainTask,TaskId);
        retValue = E_OS_LIMIT;
    }
    else
    {
        OSTASKSTATUS(runningTaskId) = SUSPENDED;

        index = OSTASKPRI(TaskId);
        OSTASKSTATUS(TaskId) = READY;

        if (OSTASK_TaskHandlingFunction(TaskId,index))
        {
            OSRETERROR(E_OS_LIMIT,OSServiceId_ChainTask,TaskId);
            retValue = E_OS_LIMIT;
        }
        else
        {
            OSrtiServiceWatchOnExit(OSServiceId_ChainTask);
            __MTSPR(SPR_LR,terminationAddress);
            _blr();
        }
    }
    /* never return from this point in case of correct execution */
    return(retValue);
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
 
/*--------------------------------------------------------------------------*
* OSSetTaskID - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
static StatusType OSSetTaskID( TaskType  TaskId)
{
    runningTaskId = TaskId;    
    return (E_OK);    
}

/*--------------------------------------------------------------------------*
* OSSetOnHandlerManager - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
static int16_t OSSetOnHandlerManager(TaskType taskId,uint8_t index)
{
    int16_t retValue = NO_ERROR;

    DisableAllInterrupts();
    if (OSHandler_Manager[index].w_index+1 == OSHandler_Manager[index].r_index || \
    ((OSHandler_Manager[index].w_index+1 == FIFO_HANDLER_DIM) && (! \
    (OSHandler_Manager[index].r_index))
    )
    )
    {
        EnableAllInterrupts();
        retValue = FILLED_LIST;
    }
    else
    {
        OSHandler_Manager[index].buffer[OSHandler_Manager[index].w_index] = taskId;
        OSHandler_Manager[index].w_index++;
        if (OSHandler_Manager[index].w_index == FIFO_HANDLER_DIM)
        {
            OSHandler_Manager[index].w_index = 0;
        }
        EnableAllInterrupts();
    }

    return (retValue);
}

/*--------------------------------------------------------------------------*
* OSGetFromHandlerManager - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
static int16_t OSGetFromHandlerManager(uint8_t index,
                                       TaskType *tmptaskId)
{
    int16_t retValue = NO_ERROR;

    DisableAllInterrupts();
    if(OSHandler_Manager[index].r_index == OSHandler_Manager[index].w_index)
    {
        CLR_SSCIR(index)=SET_BIT_ENABLED; /* check for others entry on a FIFO */
        EnableAllInterrupts(); 
        retValue = EMPTY_LIST;
    }
    else
    {
        *tmptaskId = OSHandler_Manager[index].buffer[(OSHandler_Manager[index].r_index)];
        OSHandler_Manager[index].r_index++;

        if (OSHandler_Manager[index].r_index == FIFO_HANDLER_DIM)
        {
            OSHandler_Manager[index].r_index = 0;
        }

        if (OSHandler_Manager[index].r_index == OSHandler_Manager[index].w_index)
        {
            CLR_SSCIR(index)=SET_BIT_ENABLED; 
        }
        EnableAllInterrupts(); 
    }

    return (retValue);
}

/*--------------------------------------------------------------------------*
* OSTASK_TaskHandlingFunction - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
static int16_t OSTASK_TaskHandlingFunction(TaskType taskId,
                                           uint8_t index)
{
    int16_t retValue = NO_ERROR;

    if(index > S_IRQ7)
    {   
        retValue = SOFTWARE_INTERRUPT_REQUEST_ERROR;
    }
    else
    {   
        if (!OSSetOnHandlerManager(taskId,index))
        {
            SET_SSCIR(index) = SET_BIT_ENABLED;
        }
        else
        {   
            retValue = TASK_NOT_ENABLED_DUE_FILLED_LIST;
        }
    }

    return (retValue);

}


/*--------------------------------------------------------------------------*
* OSGetLinkRegister - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
static void OSGetLinkRegister(void (*pFunc)(void))
{
    terminationAddress = getSpecReg32SPR_LR();
    pFunc();
}

/*--------------------------------------------------------------------------*
* __blr - Function description
*
* Implementation notes:
* None
*--------------------------------------------------------------------------*/
static __asm inline void _blr(void)
{
    blr
}


#endif /* _OSEK_ */
