/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/



/*     calib region                             backup region
**   --------------------- 0x30000            --------------------- 0xA0000
**   |     CALIB_ROM     |                    |     CALIB_ROM     |
**   --------------------- 0x34000            --------------------- 0xA4000
**   |    CALIB_UPDATE   |                    |    CALIB_UPDATE   |
**   --------------------- 0x38000            --------------------- 0xA8000
**
**
**
**                           ---------- 0x4000B000
**           VCALIB_CACHE    | page1  | 
**                           ---------- 0x4000C000
**
**   ---------- 0x34000                            ---------- 0xA4000
**   | page1  |                                    | page1  | 
**   ---------- 0x35000                            ---------- 0xA5000
**   | page2  |                                    | page2  | 
**   ---------- 0x36000                            ---------- 0xA6000
**   | page3  |                                    | page3  | 
**   ---------- 0x37000                            ---------- 0xA7000
**   | page4  |                                    | page4  | 
**   ---------- 0x38000                            ---------- 0xA8000
** 
**
*/

#ifdef _BUILD_VCALIB_

#include "flash_api.h"	 /* needed by the 'PSSD_CONFIG' type */

#include "../../CONFIG/C/vcalib.cfg"
#include <string.h>
#ifdef  _OSEK_ 
#include "task.h"
#endif /* _OSEK_ */

#include "tasksdefs.h"

#include "sys.h"
#include "mmu.h"
#include "vcalib.h"
#include "mpc5500_spr_macros.h"

#ifndef _BUILD_MMU_
#error VCALIB module cannot be built without MMU module
#endif

#if (VCALIB_CACHE_SIZE == VCALIB_CACHE_SIZE_4K)
#define VCALIB_PAGE_SHL      12
#define VCALIB_ADDRESS_SHL   14
#define VCALIB_PAGES         4
#define VCALIB_PAGE_SIZE     0x1000
#elif (VCALIB_CACHE_SIZE == VCALIB_CACHE_SIZE_16K)
#define VCALIB_PAGE_SHL      14
#define VCALIB_ADDRESS_SHL   16
#define VCALIB_PAGES         4
#define VCALIB_PAGE_SIZE     0x4000
#else
#error VCALIB module cannot be built using this page size
#endif

#ifndef VCALIB_TLB_0
#error VCALIB_TLB_0 MUST be defined
#endif
#ifndef VCALIB_TLB_1
#error VCALIB_TLB_1 MUST be defined
#endif 
#ifndef VCALIB_TLB_2
#error VCALIB_TLB_2 MUST be defined
#endif 
#ifndef VCALIB_TLB_3
#error VCALIB_TLB_3 MUST be defined
#endif

/* define it in order to create a CALIB_RAM tag, updated on changes */
// #define KEEP_TAG_REGION_UPDATED

static const uint8_t vcalib_TlbList[] = {
    VCALIB_TLB_0,
    VCALIB_TLB_1,
    VCALIB_TLB_2,
    VCALIB_TLB_3
};

/* 
 * available memory space for each page
 * reserve space in the last page for memory tagging
 */
static const uint32_t vcalib_page_space[] = {
    VCALIB_PAGE_SIZE,
    VCALIB_PAGE_SIZE,
    VCALIB_PAGE_SIZE,
    (VCALIB_PAGE_SIZE - sizeof(BlockDescription))
};

#define VCALIB_RAM_EMPTY (-1)
#define VCALIB_PAGE_EMPTY (0)
#define VCALIB_FULL_COPIED (0xF)
#define vcalib_getPageToRAM() (vcalib_PageToRAM)
#define vcalib_setPageToRAM(page) (vcalib_PageToRAM = (page))
#define vcalib_getPageStatus(page) (vcalib_PageStatus & (1 << (page)))
#define vcalib_setPageStatus(page) (vcalib_PageStatus |= (1 << (page)))

extern uint32_t VCALIB_callbackCount;
extern void VCALIB_EraseCallback(void);

/* internal variables */

#ifdef KEEP_TAG_REGION_UPDATED
static BlockDescription vcalib_BlockDescriptor;
#endif

static uint8_t  vcalib_PageStatus;
static uint32_t vcalib_AddressMask;
static uint32_t vcalib_PageMask;
static int8_t  vcalib_PageToRAM;
static uint32_t vcalib_nTLBs;
static uint32_t vcalib_valid_region; // points to the last validated region
static uint32_t vcalib_working_region; // points to the backup region
static uint32_t vcalib_valid_base_addr; // points to the last valid vcalib entry point memory
static uint32_t vcalib_working_base_addr; // points to the working vcalib entry point memory

/* internal methods */

/* internal flash program & verify */
#define PROGRAM_STRIDE (64)
static uint32_t vcalib_flash_program(uint32_t dest, uint32_t size, uint32_t source)
{
    uint32_t error = NO_ERROR;
    uint32_t ret;
    uint32_t i;

    for (i=0; i<(size/PROGRAM_STRIDE); i++)
    {
        DisableAllInterrupts();
        ret = FLASH_Program(dest+i*PROGRAM_STRIDE, PROGRAM_STRIDE, source+i*PROGRAM_STRIDE);
        EnableAllInterrupts();
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }
    }
    ret = FLASH_ProgramVerify(dest, size, source);
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }

    return error;
}

/*
 * Check if address fall in virtual calib range
 */
static uint8_t vcalib_isVirtual(uint32_t addr)
{
    uint8_t res = FALSE;
    uint32_t vcalib_page;

    vcalib_page = ((uint32_t) (& __VCALIB_START)) >> VCALIB_ADDRESS_SHL;

    if ((addr >> VCALIB_ADDRESS_SHL) == vcalib_page)
    {
        res = TRUE;  
    }

    return res;
}

/*
 * Retrieve page number for current address
 */
static uint8_t vcalib_getPageNum(uint32_t addr)
{
    uint8_t res;

    res = (addr & vcalib_PageMask) >> VCALIB_PAGE_SHL;

    return res; 
}

/*
 * Config
 */
int32_t VCALIB_Config(void)
{
    uint32_t f_address;
    uint32_t t_address;
    uint32_t size;
    int32_t error = NO_ERROR;

    f_address = (uint32_t) (& __CALIB_ROM_START);
    t_address = (uint32_t) (& __BACKUP_START);
    size = (uint32_t) (& __CALIB_ROM_SIZE);

    vcalib_valid_region = f_address;
    vcalib_working_region = t_address;

    /*
     * following starting addresses are used for storing temporary
     * calibration changes. Just after calibration region
    */
    vcalib_valid_base_addr = vcalib_valid_region + size;
    vcalib_working_base_addr = vcalib_working_region + size;

    vcalib_AddressMask = (0xffffffff<<VCALIB_ADDRESS_SHL);
    vcalib_PageMask = ((0xffffffff)<<VCALIB_PAGE_SHL) & ~vcalib_AddressMask;
    vcalib_nTLBs = 1 << (VCALIB_ADDRESS_SHL - VCALIB_PAGE_SHL);

    VCALIB_callbackCount = 0;

    return error;
}

/*
 * Init
 * initialize vcalib driver and flash mamory
 * 0x30000 to 0x34000: starting calibration. never change
 * 0x34000 to 0x38000: will contain a working copy of starting calibration.
 *                     it will be changed as calibration values change
 * 0xA0000 to 0xA4000: starting calibration. never change 
 * 0xA4000 to 0xA8000: as for 0x34000 to 0x38000 memory space
 */
int32_t VCALIB_Init(void)
{
    uint32_t v_address;
    uint32_t vaddr;
    uint32_t faddr;
    uint32_t dest;
    uint32_t size;
    uint32_t source;
    uint32_t i;
    uint32_t ret = NO_ERROR;
    int32_t error = NO_ERROR;

    size = (uint32_t) (& __VCALIB_SIZE);

    /*
     * working memory shall be erased at startup
     * memory space required: 2*__CALIB_ROM_SIZE
     */
    size = (uint32_t)(& __CALIB_ROM_SIZE);
    size *= 2; 
    if (FLASH_BlankCheck(vcalib_working_region, size))
    {
        DisableAllInterrupts();
        ret = FLASH_Erase(vcalib_working_region, size, (void(*)(void))VCALIB_EraseCallback);
        EnableAllInterrupts();
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }
    }

    /* initialize backup memory region */
    dest = vcalib_working_region;
    size = (uint32_t)(& __CALIB_ROM_SIZE);
    source = vcalib_valid_region;
    DisableAllInterrupts();
    ret = FLASH_Program(dest, size, source);
    EnableAllInterrupts();
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }
    ret = FLASH_ProgramVerify(dest, size, source);
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }

    /*
     * valid region should have only calibration and the remainder empty
     * at startup. If it is not, memory will be reinitialized
    */
    size = (uint32_t)(& __CALIB_ROM_SIZE);
    if (FLASH_BlankCheck(vcalib_valid_base_addr, size))
    {
        /* clear calibration */
        DisableAllInterrupts();
        ret = FLASH_Erase(vcalib_valid_region, size, (void(*)(void))VCALIB_EraseCallback);
        EnableAllInterrupts();
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }

        /* reinit calibration */
        dest = vcalib_valid_region;
        source = vcalib_working_region;

        ret = vcalib_flash_program(dest, size, source);
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }
    }

    /* initialize working calibration with starting calibration
     * region tag not included
     */
    dest = vcalib_valid_base_addr;
    size = (uint32_t)(& __CALIB_ROM_SIZE) - sizeof(BlockDescription);
    source = vcalib_valid_region;

    ret = vcalib_flash_program(dest, size, source);
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }

    v_address = (uint32_t) (& __VCALIB_START);

    /*
     * Initialize TLBs entries
     * set all TLBs entries to calib region
     * It is safer to take as long as possible valid calibration not in ram cache
     */
    vaddr = v_address;
    faddr = vcalib_valid_base_addr;
    for (i=0; i<vcalib_nTLBs; i++)
    {
        MMU_switch_Tlb(vcalib_TlbList[i], vaddr, faddr, VCALIB_CACHE_SIZE);
        vaddr += VCALIB_PAGE_SIZE;
        faddr += VCALIB_PAGE_SIZE;
    }

    vcalib_setPageToRAM(VCALIB_RAM_EMPTY);
    vcalib_PageStatus = 0;

#if 0
    vcalib_unit_test();
#endif

    return error;
}

/*
 * flush ram cache content into working region
 */
static int32_t vcalib_ram_flush(void)
{
    uint32_t v_address;
    uint32_t r_address;
    int8_t page;
    uint32_t dest;
    uint32_t size;
    uint32_t source;
    uint32_t offset;
    uint32_t ret = NO_ERROR;
    int32_t error = NO_ERROR;

    page = vcalib_getPageToRAM();

    if (page != VCALIB_RAM_EMPTY)
    {
        v_address = (uint32_t) (& __VCALIB_START);
        r_address = (uint32_t) (& __VCALIB_CACHE_START);

        /* Copy the ram content to the working region */
        offset = page*VCALIB_PAGE_SIZE;
        dest = vcalib_working_base_addr + offset;
        size = vcalib_page_space[page];
        source = r_address;

        ret = vcalib_flash_program(dest, size, source);
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }

        /* Configure TLB pointing to working region */
        MMU_switch_Tlb(vcalib_TlbList[page], (v_address + offset), dest, VCALIB_CACHE_SIZE);

        /* Update status */
        vcalib_setPageToRAM(VCALIB_RAM_EMPTY);
        vcalib_setPageStatus(page);
    }

    return error;
}

/*
 * Create a new valid region.
 * Program to flash memory the working region
 */
static int32_t vcalib_update_region(void)
{
    uint32_t v_address;
    uint32_t tmp_address;
    uint32_t dest;
    uint32_t size;
    uint32_t source;
    uint32_t offset;
    uint32_t i;
    uint32_t ret = NO_ERROR;
    int32_t error = NO_ERROR;

    v_address = (uint32_t) (& __VCALIB_START);

#ifdef KEEP_TAG_REGION_UPDATED
    /* Make a backup for block descriptor. later used for tagging region */
    dest = (uint32_t)&vcalib_BlockDescriptor;
    size = sizeof(BlockDescription);
    source = vcalib_valid_base_addr + (uint32_t) (& __CALIB_ROM_SIZE) - sizeof(BlockDescription);
    memcpy((void *)dest, (const void *)source, size);
#endif

    /* Fill empty pages of the working region and configure TLBs */
    for (i=0; i<vcalib_nTLBs; i++)
    {
        /*
         * Empty pages are filled (that is unmodified pages)
         * with the content of the corresponding valid region pages
         */
        if (vcalib_getPageStatus(i) == VCALIB_PAGE_EMPTY)
        {
            offset = i*VCALIB_PAGE_SIZE;
            dest = vcalib_working_base_addr + offset;
            size = vcalib_page_space[i];
            source = vcalib_valid_base_addr + offset;

            ret = vcalib_flash_program(dest, size, source);
            if (ret != NO_ERROR)
            {
                error |= VCALIB_ERROR_FLASH;
            }

            /* Configure TLB */
            MMU_switch_Tlb(vcalib_TlbList[i], (v_address + offset), dest, VCALIB_CACHE_SIZE);
        }
    }

#ifdef KEEP_TAG_REGION_UPDATED
    /* Update tag of the working region */
    dest = (uint32_t)&(vcalib_BlockDescriptor.blockChecksum);
    size = (uint32_t)(& __CALIB_ROM_SIZE) - sizeof(BlockDescription);
    source = vcalib_working_base_addr;
    ret = FLASH_CheckSum(source, size, (uint32_t *)dest);

    dest = vcalib_working_base_addr + (uint32_t)(& __CALIB_ROM_SIZE) - sizeof(BlockDescription);
    size = sizeof(BlockDescription);
    source = (uint32_t)&vcalib_BlockDescriptor;

    DisableAllInterrupts();
    ret = FLASH_Program(dest, size, source);
    EnableAllInterrupts();
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }
    ret = FLASH_ProgramVerify(dest, size, source);
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }
#endif

    /* Swap regions */
    tmp_address = vcalib_valid_region;
    vcalib_valid_region = vcalib_working_region;
    vcalib_working_region = tmp_address;

    size = (uint32_t)(& __CALIB_ROM_SIZE);
    vcalib_valid_base_addr = vcalib_valid_region + size;
    vcalib_working_base_addr = vcalib_working_region + size;

    /* Erase working region for future changes */
    size = (uint32_t) (& __CALIB_ROM_SIZE);
    DisableAllInterrupts();
    ret = FLASH_Erase(vcalib_working_region, size, (void(*)(void))VCALIB_EraseCallback);
    EnableAllInterrupts();
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }

    vcalib_PageStatus = 0;

    /*
     * initialize working memory region with original calibration values
    */
    dest = vcalib_working_region;
    size = (uint32_t) (& __CALIB_ROM_SIZE);
    source = vcalib_valid_region;

    ret = vcalib_flash_program(dest, size, source);
    if (ret != NO_ERROR)
    {
        error |= VCALIB_ERROR_FLASH;
    }

    return error;
}

/*
 * Flush cache and update memory region
 * Make valid the working region
 * blocking!!!
 */
int32_t VCALIB_Flush(void)
{
    uint8_t page_to_ram;
    int32_t error = NO_ERROR;

    /* Get current page cached into ram */
    page_to_ram = vcalib_getPageToRAM();

    /*
     * Current page in ram has to be flushed
     * it is necessary to check for flush completation before
     * if (vcalib_getPageStatus(page_to_ram) != VCALIB_PAGE_EMPTY)
     */
    if (vcalib_getPageStatus(page_to_ram) != VCALIB_PAGE_EMPTY)
    {
        return VCALIB_ERROR_FLASH_BUSY;
    }

    /* safety, flush ram content into working region */
    error = vcalib_ram_flush();

    if (vcalib_PageStatus != 0)
    {
        /*
         * Synchronize region. Meanwhile we cannot
         * continue working writing in ram cache.
         * Instead of ActivateTask(TaskVCALIB_update_regionID)
         * call directly the body of the TaskVCALIB_update_region task
         */
        error |= vcalib_update_region();
    }

    return error;
}

/*
 * Flush cache and update memory region
 * Make valid the working region
 * no  blocking
 */
int32_t VCALIB_Flush_no_blocking(void)
{
    uint8_t page_to_ram;
    int32_t error = NO_ERROR;

    /* Get current page cached into ram */
    page_to_ram = vcalib_getPageToRAM();

    /*
     * Current page in ram has to be flushed
     * !!! check for flush completation before.
     * if (vcalib_getPageStatus(page_to_ram) != VCALIB_PAGE_EMPTY)
     * cannot wait for sync, cannot block task
     */
    if (vcalib_getPageStatus(page_to_ram) != VCALIB_PAGE_EMPTY)
    {
        return VCALIB_ERROR_FLASH_BUSY;
    }

    /* Safety, flush ram content into working region */
    error = vcalib_ram_flush();

    if (vcalib_PageStatus != 0)
    {
        /*
         * Synchronize region. meanwhile we can
         * continue working writing in ram cache
         */
        ActivateTask(TaskVCALIB_update_regionID);
    }

    return error;
}


/*
 * Check for ram cache miss.
 * If page is not cached ram is flushed and the new
 * page is loaded in cache
 * no blocking function
 * NB: can be pretty moved into an TLB miss interrupt handler: IVOR14
 */
int32_t VCALIB_Switch_no_blocking(uint32_t addr)
{
    uint32_t v_address;
    uint32_t r_address;
    uint8_t page_to_ram;
    uint8_t page;
    uint32_t dest;
    uint32_t size;
    uint32_t source;
    uint32_t offset;
    int32_t error = NO_ERROR;

    if (vcalib_isVirtual(addr))
    {
        /* Get page number for current address */
        page = vcalib_getPageNum(addr);

        /* Get current page cached into ram */
        page_to_ram = vcalib_getPageToRAM();

        /* Go on if page is alrady in ram, */
        /* otherwise manage page switch */
        if (page != page_to_ram)
        {
            /*
             * Current page in ram has to be flushed
             * !!! check for flush completation before
             * if (vcalib_getPageStatus(page_to_ram) != VCALIB_PAGE_EMPTY)
             * cannot wait for sync, cannot block task
             */
            if (vcalib_getPageStatus(page_to_ram) != VCALIB_PAGE_EMPTY)
            {
                return VCALIB_ERROR_FLASH_BUSY;
            }

            /* Safety, flush ram content into working region */
            error = vcalib_ram_flush();

            /*
             * Check for hits of the new page in working region
             * if page is already present in working region means that
             * we will be unable to flush ram content after...
             * Working region have to be syncrhonized, but meanwhile we can
             * continue working writing in ram cache
             */
            if (vcalib_getPageStatus(page) != VCALIB_PAGE_EMPTY)
            {
                ActivateTask(TaskVCALIB_update_regionID);

                /*
                 * If page is not empty the cache RAM will be filled with value from
                 * the working region
                 */
                source = vcalib_working_base_addr;
            }
            else
            {
                /*
                 * If page is still empty the cache RAM will be filled with value from
                 * the valid region
                 */
                source = vcalib_valid_base_addr;
            }

            v_address = (uint32_t) (& __VCALIB_START);
            r_address = (uint32_t) (& __VCALIB_CACHE_START);

            offset = page*VCALIB_PAGE_SIZE;
            dest = r_address;
            size = vcalib_page_space[page];
            source += offset;
            memcpy((void *)dest, (const void *)source, size);

            /* Configure TLB pointing to ram cache */
            MMU_switch_Tlb(vcalib_TlbList[page], (v_address + offset), dest, VCALIB_CACHE_SIZE);

            vcalib_setPageToRAM(page);
        }
    }

    return error;
}

#if 0
/*
 * Synchronize the working calibration region
 * Update calibration copy with the valid region
 * no blocking
 */
int32_t VCALIB_Sync(void)
{
    uint32_t v_address;
    uint32_t f_address;
    uint32_t tmp_address;
    uint32_t dest;
    uint32_t size;
    uint32_t source;
    uint32_t offset;
    uint8_t i;
    uint32_t ret = NO_ERROR;
    int32_t error = NO_ERROR;

    v_address = (uint32_t) (& __VCALIB_START);
    f_address = (uint32_t) (& __CALIB_ROM_START);

    error = VCALIB_Flush();

    if (vcalib_valid_region != f_address)
    {
        /*
         * Calibration region is already initialized
         * just copy the actual calibration content from backup region
        */
        dest = vcalib_working_base_addr;
        size = (uint32_t)(& __CALIB_ROM_SIZE);
        source = vcalib_valid_base_addr;

        ret = vcalib_flash_program(dest, size, source);
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }

        /* Reconfigure TLBs: switch to working region */
        for (i=0; i<vcalib_nTLBs; i++)
        {
            offset = i*VCALIB_PAGE_SIZE;
            dest = vcalib_working_base_addr + offset;

            DisableAllInterrupts();
            MMU_switch_Tlb(vcalib_TlbList[i], (v_address + offset), dest, VCALIB_CACHE_SIZE);
            EnableAllInterrupts();
        }

        /* Swap regions */
        tmp_address = vcalib_valid_region;
        vcalib_valid_region = vcalib_working_region;
        vcalib_working_region = tmp_address;
        size = (uint32_t)(& __CALIB_ROM_SIZE);
        vcalib_valid_base_addr = vcalib_valid_region + size;
        vcalib_working_base_addr = vcalib_working_region + size;

        /* Erase working region for future changes */
        size = (uint32_t)(& __CALIB_ROM_SIZE);
        DisableAllInterrupts();
        ret = FLASH_Erase(vcalib_working_region, size, (void(*)(void))VCALIB_EraseCallback);
        EnableAllInterrupts();
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }

        /* initialize working memory region */
        dest = vcalib_working_region;
        size = (uint32_t)(& __CALIB_ROM_SIZE);
        source = vcalib_valid_region;

        ret = vcalib_flash_program(dest, size, source);
        if (ret != NO_ERROR)
        {
            error |= VCALIB_ERROR_FLASH;
        }
    }

    return error;
}

/*
 * Exit
 */
int32_t VCALIB_Exit(void)
{
    int32_t error = NO_ERROR;

    error = VCALIB_Sync();

    return error;
}
#endif


/*
 * Task for update calibration
 */
void FuncTaskVCALIB_update_region(void)
{
    vcalib_update_region();

    TerminateTask();
}

#if 0
/*
 * unit test for vcalib module
 */
static void vcalib_unit_test()
{
    uint32_t v_address;
    uint32_t t_address;
    uint32_t f_address;
    uint32_t r_address;
    uint32_t i;
    uint8_t *raddr;
    uint8_t *waddr;

    v_address = (uint32_t) (& __VCALIB_START);
    t_address = (uint32_t) (& __BACKUP_START);
    f_address = (uint32_t) (& __CALIB_ROM_START);
    r_address = (uint32_t) (& __vCALIB_RAM_START);

    raddr = (uint8_t *)f_address;
    waddr = (uint8_t *)v_address;
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }

    raddr = (uint8_t *)f_address;
    waddr = (uint8_t *)v_address;
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }
    for (i=0; i<VCALIB_PAGE_SIZE; i++)
    {
        VCALIB_Switch((uint32_t)waddr);
        (*waddr++) = (*raddr++);
    }

    VCALIB_Sync();
}

#endif

#endif /* _BUILD_VCALIB_ */
