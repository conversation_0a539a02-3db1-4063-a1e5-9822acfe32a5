/*
 * File: mul_s32_s32_s32_sr5_sat.h
 *
 * Code generated for Simulink model 'CmiSatMgm'.
 *
 * Model version                  : 1.159
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Thu Jun 28 10:29:27 2018
 */

#ifndef SHARE_mul_s32_s32_s32_sr5_sat
#define SHARE_mul_s32_s32_s32_sr5_sat
#include "rtwtypes.h"

extern int32_T mul_s32_s32_s32_sr5_sat(int32_T a, int32_T b);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
