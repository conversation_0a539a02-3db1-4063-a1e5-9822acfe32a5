/************************************************************************
 *                                                                       *
 *                   Standard Software C90LC Driver for MPC563xM         *
 *                                                                       *
 * FILE NAME     : ProgramVerify.c                                         *
 * DATE          :                                                       *
 *                                                                       *
 * AUTHOR        :                                                       *
 * E-mail        :                                                       *
 *                                                                       *
 *************************************************************************/

 /******************************* CHANGES *********************************
 0.0a    01-24-2008  Sindhu R01         Initial Version
 0.1     02-08-2008  <PERSON> He            Updated for final release.
 0.2     03-18-2008  Chen He            Add FlashECCLogicCheck
 1.0     04-10-2008  Sindhu R01         Updated for final Release
 1.1     06-12-2008  <PERSON><PERSON>      Updates to JDP SW template.
 1.2     08-20-2008  A<PERSON><PERSON>     Updated after changes in SSD to support 
                                        MPC5668x.
 1.3     01-05-2009  Sindhu R01         Added Return codes related to FlashECCLogicCheck  
                                        and FlashArrayIntegrityCheck in Section 2.5.
                                        Modified Table 5.
 0.2.0   01-20-2009  Sindhu R01         Added API FactoryMarginReadCheck
 0.3.0   02-13-2009  Arvind Awasthi     Added eccValue parameter to the prototype of 
                                        FlashECCLogicCheck API.
 0.4.0   11-25-2009  Leonardo Colombo   Updated after changes in SSD to support all xPC56xx.
 0.5.0   06-15-2010  Cosimo Stornaiuolo Modified Test_FlashArrayUntegrityCheck.c to 
                                        support EmbAlgo 3.7
                                        Added support for DFO
                                        Tets Suite Updated for xPC56xx.
 *************************************************************************/

/******************************* AKHELA **********************************
 1.0.0  2012.06.06       Mocci A.       C90LC v1.0.1 porting
 *************************************************************************/ 

#ifdef  _BUILD_FLASH_
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/

#include    "ssd_c90fl.h"

 /*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* C90LC Driver v1.0.1 */

const unsigned long ProgramVerify_C[] = 
{
      0x7C0802A6, 0x9421FFC0, 0xBEA10014, 0x90010044, 0x7C781B78, 0x7C992378, 0x7CBA2B78
    , 0x7CDB3378, 0x7CFC3B78, 0x7D1D4378, 0x7D3E4B78, 0x7D5F5378, 0x3AE00000, 0x7F2AD378
    , 0x81780020, 0x7D8A5B96, 0x7D8C59D6, 0x7D8C5051, 0x4082000C, 0x576C07BF, 0x4182000C
    , 0x3AE00100, 0x48000104, 0x8178000C, 0x81980010, 0x7D4B6214, 0x81780004, 0x81980008
    , 0x7D6B6214, 0x7ED9D214, 0x8198000C, 0x7C196040, 0x41800020, 0x7C195040, 0x40800018
    , 0x81980010, 0x7C1A6040, 0x4181000C, 0x7C165040, 0x40810034, 0x81980004, 0x7C196040
    , 0x41800020, 0x7C195840, 0x40800018, 0x81980008, 0x7C1A6040, 0x4181000C, 0x7C165840
    , 0x4081000C, 0x3AE00200, 0x48000090, 0x3AC00000, 0x3AA00000, 0x48000078, 0x81390000
    , 0x81590004, 0x817B0000, 0x819B0004, 0x7C095840, 0x4082000C, 0x7C0A6040, 0x41820030
    , 0x3AE00700, 0x933C0000, 0x81790000, 0x81990004, 0x917D0000, 0x919D0004, 0x817B0000
    , 0x819B0004, 0x917E0000, 0x919E0004, 0x48000038, 0x3B390008, 0x3B7B0008, 0x2C1FFFFF
    , 0x41820018, 0x7C15B040, 0x40820010, 0x7FE903A6, 0x4E800421, 0x3AD60050, 0x3AB50001
    , 0x574CE8FE, 0x7C156040, 0x4180FF84, 0x81980024, 0x2C0C0000, 0x41820008, 0x44000002
    , 0x7EECBB78, 0x7D836378, 0xBAA10014, 0x80010044, 0x38210040, 0x7C0803A6, 0x4E800020
    , 0x30393530, 0x35464646
    
}; /* Total Size = 100 words */

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_t functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ProgramVerify - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/  
uint32_t ProgramVerify ( PSSD_CONFIG pSSDConfig,
                       uint32_t dest,
                       uint32_t size,
                       uint32_t source,
                       uint32_t *pFailAddress,
                       uint64_t *pFailData,
                       uint64_t *pFailSource,
                       void (*CallBack)(void)
                       )
{
  FlashFunctionLoader( (unsigned long*)ProgramVerify_C, sizeof(ProgramVerify_C)/4);
  return ((pPROGRAMVERIFY)FlashFunctionPointer)(pSSDConfig, dest, size, source,
                                                pFailAddress, pFailData, pFailSource,
                                                CallBack);
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
#endif /* _BUILD_FLASH_ */
