/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/LL_MV6_19_RT2/tree/DD/COMMON#$   */
/* $ Description:                                                                                                */
/* $Revision:: 12684  $                                                                                          */
/* $Date:: 2020-05-18 15:19:15 +0200 (lun, 18 mag 2020)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

#ifndef _IUPR_OUT_H_
#define _IUPR_OUT_H_

#include "Rtwtypes.h"

extern uint16_T EEIUPRGenDenominator;
extern uint16_T EEIUPRO2Denominator;
extern uint16_T EEIUPRO2Numerator;
extern uint16_T EEIUPRO22Denominator;
extern uint16_T EEIUPRO22Numerator;
extern uint16_T EEIUPRCATDenominator;
extern uint16_T EEIUPRCATNumerator;

extern uint16_T EEIUPRIgnitions;

void IUPR_T10ms(void);

#endif

