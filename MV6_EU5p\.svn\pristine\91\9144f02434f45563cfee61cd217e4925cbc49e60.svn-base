/**
 ******************************************************************************
 **  Filename:      TrqEst_private.h
 **  Date:          10-Mar-2023
 **
 **  Model Version: 1.1961
 ******************************************************************************
 **/

#ifndef RTW_HEADER_TrqEst_private_h_
#define RTW_HEADER_TrqEst_private_h_
#include "rtwtypes.h"
#include "TrqEst.h"

/* Includes for objects with custom storage classes. */
#include "syncmgm.h"
#include "air_mgm.h"
#include "digitalin.h"
#include "sparkmgm.h"
#include "af_ctrl.h"
#include "sabasic_mgm.h"
#include "Gear_mgm.h"
#include "PTrain_Diag.h"
#include "loadmgm.h"
#include "patm_model.h"
#include "Prestarget_mgm.h"
#include "Throttle_adapt.h"
#include "trac_ctrl.h"
#include "temp_mgm.h"
#include "throttle_target.h"
#include "canmgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKCMFPRES[10];          /* Variable: BKCMFPRES
                                        * Referenced by: '<S14>/BKCMFPRES'
                                        * PresIntake breakpoints
                                        */
extern int16_T BKCMFTWAT[8];           /* Variable: BKCMFTWAT
                                        * Referenced by: '<S14>/BKCMFTWAT'
                                        * TWater breakpoints
                                        */
extern int16_T VTCMFVEH[7];            /* Variable: VTCMFVEH
                                        * Referenced by: '<S33>/VTCMFVEH'
                                        * (SR) Air flow offset for CMI computation
                                        */
extern uint16_T KFCMEESTWHEEL;         /* Variable: KFCMEESTWHEEL
                                        * Referenced by: '<S12>/KFCMEESTWHEEL'
                                        * filter
                                        */
extern uint16_T KFDPATMPRES;           /* Variable: KFDPATMPRES
                                        * Referenced by: '<S27>/KFDPATMPRES'
                                        * Kf DPAtmPres
                                        */
extern uint16_T BKCMFVEH[7];           /* Variable: BKCMFVEH
                                        * Referenced by: '<S33>/BKCMFVEH'
                                        * Vehspeed breakpoints
                                        */
extern uint16_T QAIRREFPERC;           /* Variable: QAIRREFPERC
                                        * Referenced by: '<S26>/QAIRREFPERC'
                                        * QAir percent factor
                                        */
extern uint16_T TBCMI[150];            /* Variable: TBCMI
                                        * Referenced by: '<S26>/TBCMI'
                                        * Torque table
                                        */
extern uint16_T BKCMLOAD[10];          /* Variable: BKCMLOAD
                                        * Referenced by: '<S34>/BKCMLOAD'
                                        * TBCMIGAIN breakpoint vector
                                        */
extern uint16_T BKCMRPMF[15];          /* Variable: BKCMRPMF
                                        * Referenced by: '<S34>/BKCMRPMF'
                                        * RpmF breakpoints
                                        */
extern uint16_T BKTDCCRKCMF[8];        /* Variable: BKTDCCRKCMF
                                        * Referenced by: '<S14>/BKTDCCRKCMF'
                                        * CntTdcCrk breakpoints
                                        */
extern int8_T TBCMFTW[120];            /* Variable: TBCMFTW
                                        * Referenced by: '<S14>/TBCMFTW'
                                        * (SR) Cold start friction torque table
                                        */
extern uint8_T TBCMF[150];             /* Variable: TBCMF
                                        * Referenced by: '<S14>/TBCMF'
                                        * (SR) Friction torque table
                                        */
extern uint8_T TBCRKCMF[64];           /* Variable: TBCRKCMF
                                        * Referenced by: '<S14>/TBCRKCMF'
                                        * Cmf offset during crank
                                        */
extern uint8_T DISTECLUTCH;            /* Variable: DISTECLUTCH
                                        * Referenced by: '<S12>/DISTECLUTCH'
                                        * Clutch deactive
                                        */
extern uint8_T PRESTESEL;              /* Variable: PRESTESEL
                                        * Referenced by: '<S27>/PRESTESEL'
                                        * Pressure input selector
                                        */
extern uint8_T SELQAIRREF;             /* Variable: SELQAIRREF
                                        * Referenced by: '<S26>/SELQAIRREF'
                                        * Select QAIRREF
                                        */
extern void TrqEst_cmi_formula_cyl(uint16_T rtu_qair, uint16_T rtu_effSA,
  uint16_T rtu_effLam, rtB_cmi_formula_cyl_TrqEst *localB);
extern void TrqEst_cmi_formula_cycle1(uint16_T rtu_qair_cycle, uint16_T
  rtu_effSA, uint16_T rtu_effLam, uint16_T rtu_effCutoff,
  rtB_cmi_formula_cycle1_TrqEst *localB);
extern void TrqEst_cmi_formula_cycle2(uint16_T rtu_qair_cycle, uint16_T
  rtu_effSA, uint16_T rtu_effLam, uint16_T rtu_effCutoff,
  rtB_cmi_formula_cycle2_TrqEst *localB);
extern void TrqEst_Reset(void);
extern void TrqEst_PreHTDC(void);
extern void TrqEst_PreTDC(void);
extern void TrqEst_T10ms(void);

#endif                                 /* RTW_HEADER_TrqEst_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
