//#include "thrposmgm.h"
#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif
#include "analogin.h"

#ifdef _BUILD_THRPOSMGM_

#ifdef __MWERKS__
#pragma force_active on
#pragma section RW ".calib" ".calib"
#else
#pragma ghs section rodata=".calib"
#endif

#if defined(IDN_ANG_THROTTLE) && !defined(IDN_ANG_THROTTLE2)
__declspec(section ".calib") int16_T    ANGTHRGAIN = (0.02002*2048);  
__declspec(section ".calib") int16_T    ANGTHROFFSET = ((371*1024)/5000);  
#elif defined(IDN_ANG_THROTTLE) && defined(IDN_ANG_THROTTLE2)  
#if (ENGINE_TYPE == PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_08) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_30)
__declspec(section ".calib") int16_T    ANGTHRGAIN =  (0.0250*2048);  
__declspec(section ".calib") int16_T    ANGTHRGAIN2 = (0.0250*2048);  
#elif (ENGINE_TYPE == MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
__declspec(section ".calib") int16_T    ANGTHRGAIN =  (0.0250*2048);  
__declspec(section ".calib") int16_T    ANGTHRGAIN2 = (0.0250*2048);  
#elif (ENGINE_TYPE == FE_4300_8C_32V_TDN)
__declspec(section ".calib") int16_T    ANGTHRGAIN = (0.0210*2048);  
__declspec(section ".calib") int16_T    ANGTHRGAIN2 = (-0.0210*2048);  
#else
#error ERROR: Target not supported!!
#endif
#endif   	

//THRPOSMGM.FORCEANGTHROTTLE: Force VAngThrottle (=0 VAngThrottle1 =1 VAngThrottle1 =2 VAngThrottle2) [flag]
__declspec(section ".calib") uint8_T  FORCEANGTHROTTLE = 0;
//THRPOSMGM.VANGTHRCOH: Maximum value of VAngThrottle coherence diagnosis [mV]
__declspec(section ".calib") uint16_T VANGTHRCOH = (5000*0.2048);
//THRPOSMGM.VANGTHRMAX: Maximum value of VAngThrottle for diagnosis [mV]
__declspec(section ".calib") uint16_T VANGTHRMAX = (5000*0.2048);
//THRPOSMGM.VANGTHRMIN: Minimum value of VAngThrottle for diagnosis [mV]
__declspec(section ".calib") uint16_T VANGTHRMIN = (0*0.2048);

#ifdef __MWERKS__
#pragma force_active off
#endif

#endif // _BUILD_THRPOSMGM_
