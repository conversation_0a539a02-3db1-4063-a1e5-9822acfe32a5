/*
 * File: af_ctrl.h
 *
 * Code generated for Simulink model 'AFCtrl'.
 *
 * Model version                  : 1.850
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Oct 16 10:25:53 2020
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_af_ctrl_h_
#define RTW_HEADER_af_ctrl_h_
#include "rtwtypes.h"

/* Exported data define */

/* Definition for custom storage class: Define */
#define AF_CL                          1U                        /* Referenced by: '<S29>/Control_flow' */

/* Closed Loop lambda control */
#define AF_HCL                         2U                        /* Referenced by: '<S29>/Control_flow' */

/* Closed Loop lambda control */
#define AF_OL                          0U                        /* Referenced by:
                                                                  * '<S28>/Reset_Variables_s'
                                                                  * '<S29>/Control_flow'
                                                                  * '<S30>/Reset_Variables_s'
                                                                  */

/* Open Loop lambda control */

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern uint8_T AFDelayFlg;             /* '<S68>/Calc_DeltaLamCLDiff' */

/* P2R delay is active (=1), R2P delay is active (=2) */
extern uint32_T CntTdcCrkOff;          /* '<Root>/_DataStoreBlk_24' */

/* Offset applied to CntTdcCrk for cranking correction calculation */
extern uint16_T CntTdcCrkTot;          /* '<S163>/Add' */

/* CntTdcCrk Tot applied */
extern int16_T DAngThrTrans;           /* '<S191>/Subtract1' */

/* Delta AngThrottle for transient corr */
extern int16_T DLamErrDead[4];         /* '<Root>/_DataStoreBlk_33' */

/* Delta Individual cylinder lambda error */
extern uint16_T DLamTrans;             /* '<S191>/Transient_Lambda_Offset' */

/* Delta Lambda for transient corr */
extern int16_T DeltaLamCL;             /* '<Root>/_DataStoreBlk_37' */

/* Delta compensated common lambda */
extern int16_T DeltaLamCLDiff;         /* '<S68>/Data Type Conversion' */

/* DeltaLamCL difference between commutations */
extern int16_T DeltaLamCLFilt;         /* '<Root>/_DataStoreBlk_4' */

/* Filtered Delta compensated common lambda */
extern int16_T DeltaLamCorrAd;         /* '<S91>/Data Type Conversion' */

/* Adaptive common delta lambda */
extern int16_T DeltaLamCyl0[4];        /* '<Root>/_DataStoreBlk_31' */

/* Adaptive delta lambda */
extern uint16_T EffLambda;             /* '<S165>/Data Type Conversion' */

/* Lambda combustion efficiency */
extern int16_T EnerLam[4];             /* '<Root>/_DataStoreBlk_39' */

/* Energy of the lambda control */
extern uint8_T FStabDeltaLamCL;        /* '<S146>/SigStab' */

/* The DeltaLamCLFilt is stable for learning (=1) */
extern uint8_T FStabLoadAdLam;         /* '<S66>/SigStab' */

/* The Load is stable for learning (=1) */
extern uint8_T FStabRpmAdLam;          /* '<S65>/SigStab' */

/* The Rpm is stable for learning (=1) */
extern uint8_T FlgAdLam;               /* '<S45>/Logical Operator' */

/* Lambda learning is enabled (=1) */
extern uint8_T FlgAfLamRich;           /* '<S183>/Target_Choice_automaton' */

/* Lambda base rich */
extern uint8_T FlgClosedLoop;          /* '<Root>/_DataStoreBlk_17' */

/* AF Closed Loop Flag */
extern uint8_T FlgClosedLoopOnOff;     /* '<S48>/Logical Operator' */

/* AF Closed Loop Flag for ON OFF sensor */
extern uint8_T FlgHalfClosedLoop;      /* '<S48>/Logical Operator6' */

/* AF Half-Closed Loop Flag for ON OFF sensor */
extern uint8_T FlgTrigAdLam;           /* '<S145>/FixPt Relational Operator' */

/* Lambda learning is triggered (=1) */
extern uint16_T GainIntLamCtrl;        /* '<S101>/Divide' */

/* Integral gain */
extern uint16_T GainIntLamLin;         /* '<S101>/Product1' */

/* Proportional gain integral lambda lin */
extern uint16_T GainPropLamCtrl;       /* '<S105>/Product4' */

/* Proportional gain */
extern uint16_T GainPropLamLin;        /* '<S105>/Product2' */

/* Proportional gain prop lambda lin */
extern uint16_T GnPropCorr;            /* '<S68>/Data Type Conversion2' */

/* Proportional gain correction to offset lambda */
extern uint32_T IDAFCtrl;              /* '<S30>/ID_VER_AF_CTRL' */

/* ID Version */
extern uint16_T IDZoneLamLoad;         /* '<S63>/PreLookUpIdSearch_U16' */

/* Load zone index for Lambda learning */
extern uint16_T IDZoneLamRpm;          /* '<S64>/PreLookUpIdSearch_U16' */

/* Rpm zone index for lambda learning */
extern uint16_T KFComp;                /* '<Root>/_DataStoreBlk_20' */

/* Unmburned fuel compensation gain */
extern uint8_T KFSatFlg;               /* '<Root>/_DataStoreBlk_12' */

/* AF governor saturated flag */
extern uint16_T KFStarter;             /* '<S167>/Data Type Conversion' */

/* Fuel starter enrichment gain */
extern int32_T KLamInt;                /* '<S101>/Switch' */

/* Integral delta lambda */
extern int32_T KLamProp;               /* '<S105>/Switch' */

/* Proportional delta lambda */
extern uint16_T LamComp[4];            /* '<Root>/_DataStoreBlk_34' */

/* Compensated lambda */
extern uint16_T LamCompCommon;         /* '<S39>/Data Type Conversion1' */

/* Compensated common lambda */
extern int16_T LamErrDead[4];          /* '<Root>/_DataStoreBlk_15' */

/* Individual cylinder lambda error */
extern uint16_T LamObj;                /* '<Root>/_DataStoreBlk_22' */

/* Target lambda */
extern uint16_T LamObjAvgFilt;         /* '<S79>/Conversion' */

/* Filtered average target lambda */
extern uint16_T LamObjSel;             /* '<S182>/Multiport Switch' */

/* Target lambda Selected */
extern int16_T LambdaError;            /* '<Root>/_DataStoreBlk_6' */

/* Lambda error */
extern int16_T LambdaErrorFilt;        /* '<Root>/_DataStoreBlk_7' */

/* Lambda error */
extern int8_T LongTermFuelTrim;        /* '<Root>/_DataStoreBlk_29' */

/* LongTermFuelTrim */
extern uint16_T RtZoneLamLoad;         /* '<S43>/Data Type Conversion1' */

/* Load zone ratio for Lambda learning */
extern uint16_T RtZoneLamRpm;          /* '<S43>/Data Type Conversion' */

/* Rpm zone ratio for lambda learning */
extern int8_T ShortTermFuelTrim;       /* '<Root>/_DataStoreBlk_28' */

/* ShortTermFuelTrim */
extern uint8_T StAFCtrl;               /* '<Root>/_DataStoreBlk_36' */

/* AF Control State */
extern uint8_T StChooseLam;            /* '<S183>/Target_Choice_automaton' */

/* State of the lambda reduction */
extern uint8_T StFuelSys;              /* '<Root>/_DataStoreBlk_30' */

/* StFuelSys */
extern uint16_T StrCntTdcCrkOff;       /* '<S176>/Conversion' */

/* Start of Offset applied to CntTdcCrk for cranking correction calculation */
extern uint16_T ThDelayCLCrk;          /* '<Root>/_DataStoreBlk_21' */

/* Output of VTDELAYCLCRK */
extern uint8_T WotFlg;                 /* '<Root>/_DataStoreBlk_16' */

/* Full power request flag */
#endif                                 /* RTW_HEADER_af_ctrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
