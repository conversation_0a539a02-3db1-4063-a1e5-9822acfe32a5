/*
 * File: AirDiagMgm.h
 *
 * Real-Time Workshop code generated for Simulink model AirDiagMgm.
 *
 * Model version                        : 1.655
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Wed Jun 16 18:21:44 2010
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Wed Jun 16 18:21:45 2010
 */

#ifndef RTW_HEADER_AirDiagMgm_h_
#define RTW_HEADER_AirDiagMgm_h_
#ifndef AirDiagMgm_COMMON_INCLUDES_
# define AirDiagMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#include "rtw_shared_utils.h"
#endif                                 /* AirDiagMgm_COMMON_INCLUDES_ */

#include "AirDiagMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "airdiag_mgm.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

/* Block signals for system '<S15>/Coherence_Analysis' */
typedef struct {
  int16_T Add;                         /* '<S17>/Add' */
  uint8_T Switch1;                     /* '<S17>/Switch1' */
} rtB_Coherence_Analysis_AirDiagM;

/* Block signals (auto storage) */
typedef struct {
  uint8_T ptfaultsenspres;             /* '<S19>/Truth Table' */
  uint8_T ptfaultholeman;              /* '<S19>/Truth Table' */
  uint8_T stdiagpresfun;               /* '<S33>/DiagMgm_SetDiagState1' */
  uint8_T stdiagholeman;               /* '<S32>/DiagMgm_SetDiagState1' */
  rtB_Coherence_Analysis_AirDiagM Coherence_Analysis1;/* '<S15>/Coherence_Analysis1' */
  rtB_Coherence_Analysis_AirDiagM Coherence_Analysis;/* '<S15>/Coherence_Analysis' */
} BlockIO_AirDiagMgm;

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  int_T Enabled_MODE;                  /* '<S9>/Enabled' */
  uint16_T Memory1_PreviousInput;      /* '<S34>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S34>/Memory' */
  uint8_T Memory_PreviousInput_gyuc;   /* '<S20>/Memory' */
  uint8_T Memory_PreviousInput_nyam;   /* '<S15>/Memory' */
} D_Work_AirDiagMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_AirDiagMgm_Reset_Trig_ZCE[2];/* '<S1>/fc_AirDiagMgm_Reset' */
  ZCSigState fc_AirDiagMgm_Calc_Trig_ZCE;/* '<S1>/fc_AirDiagMgm_Calc' */
} PrevZCSigStates_AirDiagMgm;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_TDC;                      /* '<Root>/ev_TDC' */
} ExternalInputs_AirDiagMgm;

/* Block signals (auto storage) */
extern BlockIO_AirDiagMgm AirDiagMgm_B;

/* Block states (auto storage) */
extern D_Work_AirDiagMgm AirDiagMgm_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_AirDiagMgm AirDiagMgm_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  RTW declares the memory for these signals
 * and exports their symbols.
 *
 */
extern int16_T PresErrThr;             /* '<S20>/Switch2'
                                        * Pressure error threshold for air diagnosis
                                        */
extern uint8_T EnPresTest;             /* '<S9>/Logical Operator1'
                                        * Air Diagnosis Enable Flag
                                        */
extern uint8_T FlgPresStable;          /* '<S34>/SteadyStateDetect'
                                        * Air Diagnosis Pressure stability Flag
                                        */
extern uint8_T state;                  /* '<S34>/SteadyStateDetect' */
extern uint8_T FlgFreezePresDiag;      /* '<S20>/Logical Operator2'
                                        * Flag to freeze the coherence analysis (=1)
                                        */
extern uint8_T StChoiceAirDiag;        /* '<S19>/Truth Table'
                                        * Choice in the truth table for air diag
                                        */
extern uint8_T FlgADiagOpenThr;        /* '<S19>/Control_flags'
                                        * Pressure control is opening the throttle
                                        */
extern uint8_T FlgADiagCloseThr;       /* '<S19>/Control_flags'
                                        * Pressure control is closing the throttle
                                        */

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  RTW declares the memory for these states
 * and exports their symbols.
 *
 */
extern int16_T PresErr1;               /* '<Root>/_DataStoreBlk_1'
                                        * Pressure error calculated for throttle 1
                                        */
extern int16_T PresErr2;               /* '<Root>/_DataStoreBlk_3'
                                        * Pressure error calculated for throttle 2
                                        */
extern uint8_T FlgUnexpPresFault;      /* '<Root>/_DataStoreBlk_2'
                                        * Unexpected error in pressure diagnosis
                                        */

/* Model entry point functions */
extern void AirDiagMgm_initialize(void);
extern void AirDiagMgm_step(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S22>/Data Type Duplicate' : Eliminated upon user request
 * Block '<S21>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Duplicate' : Eliminated upon user request
 * Block '<S23>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Duplicate' : Eliminated upon user request
 * Block '<S26>/Data Type Propagation' : Eliminated upon user request
 * Block '<S32>/Data Type Duplicate' : Eliminated upon user request
 * Block '<S33>/Data Type Duplicate' : Eliminated upon user request
 * Block '<S34>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Duplicate' : Eliminated upon user request
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('airdiagmgm_gen/AirDiagMgm')    - opens subsystem airdiagmgm_gen/AirDiagMgm
 * hilite_system('airdiagmgm_gen/AirDiagMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : airdiagmgm_gen
 * '<S1>'   : airdiagmgm_gen/AirDiagMgm
 * '<S8>'   : airdiagmgm_gen/AirDiagMgm/Initialization
 * '<S9>'   : airdiagmgm_gen/AirDiagMgm/TDC
 * '<S10>'  : airdiagmgm_gen/AirDiagMgm/fc_AirDiagMgm_Calc
 * '<S11>'  : airdiagmgm_gen/AirDiagMgm/fc_AirDiagMgm_Reset
 * '<S12>'  : airdiagmgm_gen/AirDiagMgm/Initialization/Init
 * '<S13>'  : airdiagmgm_gen/AirDiagMgm/TDC/Compare To Constant
 * '<S14>'  : airdiagmgm_gen/AirDiagMgm/TDC/Disabled
 * '<S15>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled
 * '<S16>'  : airdiagmgm_gen/AirDiagMgm/TDC/Disabled/Init
 * '<S17>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Coherence_Analysis
 * '<S18>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Coherence_Analysis1
 * '<S19>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis
 * '<S20>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Enable_Threshold_Calc
 * '<S21>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Coherence_Analysis/GenAbs
 * '<S22>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Coherence_Analysis/GenAbs/Data Type Conversion Inherited
 * '<S23>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Coherence_Analysis1/GenAbs
 * '<S24>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Coherence_Analysis1/GenAbs/Data Type Conversion Inherited
 * '<S25>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/Control_flags
 * '<S26>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/RTW_MEAN
 * '<S27>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/Truth Table
 * '<S28>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/call_holeman
 * '<S29>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/call_senspres
 * '<S30>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/RTW_MEAN/Chart
 * '<S31>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/RTW_MEAN/Data Type Conversion Inherited
 * '<S32>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/call_holeman/DiagMgm_SetDiagState1
 * '<S33>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Diagnosis/call_senspres/DiagMgm_SetDiagState
 * '<S34>'  : airdiagmgm_gen/AirDiagMgm/TDC/Enabled/Enable_Threshold_Calc/Steady_State_Detect
 */

/*
 * Requirements for '<Root>' : AirDiagMgm
 */
#endif                                 /* RTW_HEADER_AirDiagMgm_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
