#ifndef _EMIOS_H_
#define _EMIOS_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/****************************************************************/
/*    eMIOS conversion macros                                   */
/****************************************************************/
/* Total number of eMios channels */
#define EMIOS_NUM_OF_CHANNELS 24

#define TICK_IN_CENT_GRAD            (10)


#ifdef _DEBUG_PIO_
#define FRZ_STATUS                   (FRZ_ENABLE)
#define FREN_STATUS                  (FREN_ENABLE)
#else
#define FRZ_STATUS                   (FRZ_DISABLE)
#define FREN_STATUS                  (FREN_DISABLE)
#endif

/* eMIOS Channel Counter Register */
#define EMIOS_CCNTR_MASK             (0x00FFFFFFUL)
#define EMIOS_CCNTR_BITNUM           (0x00000000UL)

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/

#define EMIOS_US_TO_TICK(us)         ((us)*FSYS)
#define EMIOS_TICK_TO_US(tick)       ((tick)/FSYS)
#define EMIOS_GRAD_DEC_TO_TICK(grad) ((grad)*TICK_IN_CENT_GRAD)
#define EMIOS_TICK_TO_GRAD_DEC(tick) ((tick)/TICK_IN_CENT_GRAD)

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

typedef vuint32_t t_CCNTR;
typedef struct 
{
    t_DelayUnion     firstRef;
    t_DelayUnion     secondRef;
} t_POUT_Set;
typedef struct 
{
    t_CompareAction  action;
    uint32_t          edpol;
    uint32_t            bsl;
} t_POUT_Config;

/* eMIOS Channel A Data Register and eMIOS Channel B Data Register */
typedef vuint32_t t_CADR;
typedef vuint32_t t_CBDR;



/* MPC563xm EMIOS channel type groups */
typedef enum {SMALL, MEDIUM, BIG} t_CHTYPE;

/************************/
/* CSR UC Configuration */
/************************/
/* Indicates that FLAG generation occurred when the FLAG bit was already set. This
   bit can be cleared by writing a 1 to it or by clearing the FLAG bit. */
typedef enum { OVR_NOT_OCCURRED, OVR_OCCURRED } t_OVR;
/* Indicates that an overflow has occurred in the internal counter. OVFL is cleared
   by writing a 1 to it. */
typedef enum { OVFL_NOT_OCCURRED, OVFL_OCCURRED } t_OVFL;
/* Set when an input capture or a match event in the comparators occurred. This bit is
   cleared by writing a 1 to it */
typedef enum { FLAG_NOT_OCCURRED, FLAG_OCCURRED } t_FLAG;


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
 extern uint32_t EMIOS_ChInitDone;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/****************************************************************/
/*    eMIOS API                                                 */
/****************************************************************/
int16_t EMIOS_Init(void);
int16_t EMIOS_ModuleConfig(uint32_t _etb, uint32_t _srv);
int16_t EMIOS_ModuleEnable(void);
int16_t EMIOS_ModuleDisable(void);
int16_t EMIOS_ModuleGetState(t_State *_state);
void  EMIOS_InCfg(uint8_T pid);
void  EMIOS_OutCfg(uint8_T pid);

/****************************************************************/
/*    eMIOS Channels generic API                                */
/****************************************************************/
/* This method enables the input related to the specified channel */
int16_t EMIOS_ChEnable(t_PIO_CHANNELS _channel);
/* This method disables the input related to the specified channel */
int16_t EMIOS_ChDisable(t_PIO_CHANNELS _channel);
/* This method reads the state of the specified channel */
int16_t EMIOS_ChGetState(t_PIO_CHANNELS _channel, t_State *_state);

/****************************************************************/
/*    PWM-OUT                                                   */
/****************************************************************/
/* This method executes the initializations of the Module. */
int16_t EMIOS_PWM_Init(void);
/* This method executes the initializations of the PWM channel, setting 
the logic level on the output pin, the period and the duty-cycle 
to the values passed as input parameters. */
int16_t EMIOS_PWM_Config(t_PIO_CHANNELS _channel, t_ActiveEdge _actedge, t_DutyCycle _dutycycle, t_Period _period);
/* This method sets run-time the duty-cicle of the PWM signal related with PWM channel */
int16_t EMIOS_PWM_SetDutyCycle(t_PIO_CHANNELS _channel, t_DutyCycle _dutycycle);
/* This method sets run-time the period of the PWM signal related with PWM channel */
int16_t EMIOS_PWM_SetPeriod(t_PIO_CHANNELS _channel, t_Period _period);

/****************************************************************/
/*    PIN                                                       */
/****************************************************************/
/* This method executes the initializations of the Pulse IN channel */ 
int16_t EMIOS_PIN_Config(t_PIO_CHANNELS _channel, t_BusSelect _bsl, t_EdgeSelection _edgesel, t_EdgePolarity _edgepol);
/* This method allows the measurement of the width of a 
positive or negative pulse or allow the measurement of the 
period of an input signal by capturing two consecutive rising edges 
or two consecutive falling edges. */
int16_t EMIOS_PIN_GetDistance(t_PIO_CHANNELS _channel, t_Distance *_distance);

/****************************************************************/
/*    POUT                                                      */
/****************************************************************/
/* This method executes the initializations of the POUT channel, setting 
the logic level on the output pin, and setting the compare action type. */
int16_t EMIOS_POUT_Config(t_PIO_CHANNELS _channel, t_ActiveEdge _actedge, t_CompareAction _action);
/* This method sets the time or the angles references, for the POUT channel, to 
perform the required compare action programmed with the method POUT_Config. 
If <_action> = TIME the two compare value in the union structure will be 
considered as "Toff  and Ton" otherwise they will be "StartAngle and StopAngle". */
int16_t EMIOS_POUT_Set(t_PIO_CHANNELS _channel, t_DelayUnion _firstRef, t_DelayUnion _secondRef);
/* This method enables the output related to the hardware pin 
connected to the Symbolic name of the POUT channel. */
int16_t EMIOS_POUT_Activate(t_PIO_CHANNELS _channel);
/* This method sets the hardware pin connected to the Symbolic name 
of the POUT channel to the selected leel after a time delay. */
int16_t EMIOS_POUT_ForceAction(t_PIO_CHANNELS _channel, t_Period _delay, t_State _action);

/****************************************************************/
/*    eMIOS Channels ISR management API                         */
/****************************************************************/
int16_t EMIOS_SetInterruptHandlerUC(t_PIO_CHANNELS _channel, TaskType taskID);
int16_t EMIOS_EnableInterruptUC(t_PIO_CHANNELS _channel);
int16_t EMIOS_DisableInterruptUC(t_PIO_CHANNELS _channel);

/****************************************************************/
/*    eMIOS Channels ISR functions                              */
/****************************************************************/
void IsrFunc_UC0(void);
void IsrFunc_UC1(void);
void IsrFunc_UC2(void);
void IsrFunc_UC3(void);
void IsrFunc_UC4(void);
void IsrFunc_UC5(void);
void IsrFunc_UC6(void);
void IsrFunc_UC7(void);
void IsrFunc_UC8(void);
void IsrFunc_UC9(void);
void IsrFunc_UC10(void);
void IsrFunc_UC11(void);
void IsrFunc_UC12(void);
void IsrFunc_UC13(void);
void IsrFunc_UC14(void);
void IsrFunc_UC15(void);
void IsrFunc_UC16(void);
void IsrFunc_UC17(void);
void IsrFunc_UC18(void);
void IsrFunc_UC19(void);
void IsrFunc_UC20(void);
void IsrFunc_UC21(void);
void IsrFunc_UC22(void);
void IsrFunc_UC23(void);

#endif  /* _EMIOS_H_ */
