/****************************************************************************/
/*                        Peripheral CONFIGURATION                          */
/*      The User should change only the following parameters for the        */
/*      correct peripheral configuration                                    */
/****************************************************************************/

/* SPI Channel Configuration */
#define SPI_CH_A_EN 0 /* canale non presente nel 5533 */
#define SPI_CH_B_EN 1 
#define SPI_CH_C_EN 1
#define SPI_CH_D_EN 0

#define SPI_CH_A_EXTERNAL 0  //Enable External Pins for the SPI CHannel A
#define SPI_CH_B_EXTERNAL 1  //Enable External Pins for the SPI CHannel B
#define SPI_CH_C_EXTERNAL 1  //Enable External Pins for the SPI CHannel C
#define SPI_CH_D_EXTERNAL 0  //Enable External Pins for the SPI CHannel D

#define SPI_CH_A_MODE       SPI_MASTER
#define SPI_CH_A_IRQ_MASK   0x2 // EOQ IRQ
#define SPI_CH_A_CNTS_SCK   0  /*Continuous Clock */
#define SPI_CH_A_PULL_MODE  DISABLE_PULL 
#define SPI_CH_A_DRVSTREN   DRVSTR_10PF       
#define SPI_CH_A_PCS_SRC    MAX_SLEWRATE      /* A CHANNEL SLEW RATE CONTROL */         

#define SPI_CH_B_MODE       SPI_MASTER
#define SPI_CH_B_IRQ_MASK   0x2   // EOQ IRQ
#define SPI_CH_B_CNTS_SCK   0    /*Continuous Clock */
#define SPI_CH_B_PULL_MODE  DISABLE_PULL
#define SPI_CH_B_DRVSTREN   DRVSTR_10PF       
#define SPI_CH_B_PCS_SRC    MED_SLEWRATE      /* B CHANNEL SLEW RATE CONTROL */

#define SPI_CH_C_MODE       SPI_MASTER
#define SPI_CH_C_IRQ_MASK   0x2 // EOQ IRQ
#define SPI_CH_C_CNTS_SCK   0    /*Continuous Clock */
#define SPI_CH_C_PULL_MODE  DISABLE_PULL    
#define SPI_CH_C_DRVSTREN   DRVSTR_10PF       
#define SPI_CH_C_PCS_SRC    MED_SLEWRATE      /* C CHANNEL SLEW RATE CONTROL */

#define SPI_CH_D_MODE       SPI_MASTER
#define SPI_CH_D_IRQ_MASK   0x2 // EOQ IRQ
#define SPI_CH_D_CNTS_SCK   0    /*Continuous Clock */
#define SPI_CH_D_PULL_MODE  DISABLE_PULL    
#define SPI_CH_D_DRVSTREN   DRVSTR_10PF       
#define SPI_CH_D_PCS_SRC    MED_SLEWRATE      /* D CHANNEL SLEW RATE CONTROL */

#define SPI_INTERNAL_CONNECTION 0x00000000 /* No internal Connection */


/* SPI PINOUT CONFIGURATION - SIU PINS */

// Enable CS.
#define PCSA0_ENABLE  0             /* (1) Enable the SPI PCSA0 Chip Select Attenzione Conflitto con CAN B*/
#define PCSA1_ENABLE  0             /* (1) Enable the SPI PCSA1 Chip Select */
#define PCSA2_ENABLE  0             /* (1) Enable the SPI PCSA2 Chip Select */
#define PCSA3_ENABLE  0             /* (1) Enable the SPI PCSA3 Chip Select */
#define PCSA4_ENABLE  0             /* (1) Enable the SPI PCSA4 Chip Select */
#define PCSA5_ENABLE  0             /* (1) Enable the SPI PCSA5 Chip Select */

#define PCSB0_ENABLE  1             /* (1) Enable the SPI PCSB0 Chip Select */
#define PCSB1_ENABLE  0             /* (1) Enable the SPI PCSB1 Chip Select */
#define PCSB2_ENABLE  0             /* (1) Enable the SPI PCSB2 Chip Select */
#define PCSB3_ENABLE  0             /* (1) Enable the SPI PCSB3 Chip Select */
#define PCSB4_ENABLE  0             /* (1) Enable the SPI PCSB4 Chip Select */
#define PCSB5_ENABLE  0             /* (1) Enable the SPI PCSB5 Chip Select */

#define PCSC0_ENABLE  1             /* (1) Enable the SPI PCSC0 Chip Select */
#define PCSC1_ENABLE  1             /* (1) Enable the SPI PCSC1 Chip Select */
#define PCSC2_ENABLE  1             /* (1) Enable the SPI PCSC2 Chip Select */
#define PCSC3_ENABLE  1             /* (1) Enable the SPI PCSC3 Chip Select */
#define PCSC4_ENABLE  0             /* (1) Enable the SPI PCSC4 Chip Select */
#define PCSC5_ENABLE  0             /* (1) Enable the SPI PCSC5 Chip Select */

#define PCSD0_ENABLE  0             /* (1) Enable the SPI PCSD0 Chip Select */
#define PCSD1_ENABLE  0             /* (1) Enable the SPI PCSD1 Chip Select */
#define PCSD2_ENABLE  0             /* (1) Enable the SPI PCSD2 Chip Select */
#define PCSD3_ENABLE  0             /* (1) Enable the SPI PCSD3 Chip Select */
#define PCSD4_ENABLE  0             /* (1) Enable the SPI PCSD4 Chip Select */
#define PCSD5_ENABLE  0             /* (1) Enable the SPI PCSD5 Chip Select */


// Stato CS.
#define PCSA0_ACT_STATE     PCS_ACTIVE_LOW                  
#define PCSA1_ACT_STATE     PCS_ACTIVE_LOW             
#define PCSA2_ACT_STATE     PCS_ACTIVE_LOW                       
#define PCSA3_ACT_STATE     PCS_ACTIVE_HIGH                       
#define PCSA4_ACT_STATE     PCS_ACTIVE_HIGH                          
#define PCSA5_ACT_STATE     PCS_ACTIVE_HIGH             

#define PCSB0_ACT_STATE     PCS_ACTIVE_LOW              
#define PCSB1_ACT_STATE     PCS_ACTIVE_HIGH                          
#define PCSB2_ACT_STATE     PCS_ACTIVE_HIGH                          
#define PCSB3_ACT_STATE     PCS_ACTIVE_HIGH                                       
#define PCSB4_ACT_STATE     PCS_ACTIVE_HIGH            
#define PCSB5_ACT_STATE     PCS_ACTIVE_HIGH            

#define PCSC0_ACT_STATE     PCS_ACTIVE_LOW              
#define PCSC1_ACT_STATE     PCS_ACTIVE_LOW                           
#define PCSC2_ACT_STATE     PCS_ACTIVE_LOW              
#define PCSC3_ACT_STATE     PCS_ACTIVE_LOW             
#define PCSC4_ACT_STATE     PCS_ACTIVE_HIGH              
#define PCSC5_ACT_STATE     PCS_ACTIVE_HIGH              

#define PCSD0_ACT_STATE     PCS_ACTIVE_LOW              
#define PCSD1_ACT_STATE     PCS_ACTIVE_HIGH              
#define PCSD2_ACT_STATE     PCS_ACTIVE_HIGH              
#define PCSD3_ACT_STATE     PCS_ACTIVE_HIGH              
#define PCSD4_ACT_STATE     PCS_ACTIVE_HIGH              
#define PCSD5_ACT_STATE     PCS_ACTIVE_HIGH              

// PAD Field Select.
#define PCS_PAD_01          0x1
#define PCS_PAD_02          0x2
#define PCS_PAD_03          0x4
#define PCS_PAD_04          0x8
   
// CKL Continuous.
#define PCSA0_CONT_EN     0   /*Continuous PCS Enable/Disable */          
#define PCSA1_CONT_EN     0   /*Continuous PCS Enable/Disable */       
#define PCSA2_CONT_EN     0   /*Continuous PCS Enable/Disable */                    
#define PCSA3_CONT_EN     0   /*Continuous PCS Enable/Disable */                    
#define PCSA4_CONT_EN     0   /*Continuous PCS Enable/Disable */                       
#define PCSA5_CONT_EN     0   /*Continuous PCS Enable/Disable */          

#define PCSB0_CONT_EN     0   /*Continuous PCS Enable/Disable */          
#define PCSB1_CONT_EN     0   /*Continuous PCS Enable/Disable */                      
#define PCSB2_CONT_EN     0   /*Continuous PCS Enable/Disable */                      
#define PCSB3_CONT_EN     0   /*Continuous PCS Enable/Disable */                                    
#define PCSB4_CONT_EN     0   /*Continuous PCS Enable/Disable */         
#define PCSB5_CONT_EN     0   /*Continuous PCS Enable/Disable */         

#define PCSC0_CONT_EN     0   /*Continuous PCS Enable/Disable */          
#define PCSC1_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSC2_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSC3_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSC4_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSC5_CONT_EN     0   /*Continuous PCS Enable/Disable */           

#define PCSD0_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSD1_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSD2_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSD3_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSD4_CONT_EN     0   /*Continuous PCS Enable/Disable */           
#define PCSD5_CONT_EN     0   /*Continuous PCS Enable/Disable */           


#if PCSA0_ENABLE

#define      SPI_CH_A0_DBR          DBR_VAL_1M
#define      SPI_CH_A0_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_A0_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_A0_FMSZ         FRAMESIZE_8
#define      SPI_CH_A0_CPOL         SPI_CPOL_LOW
#define      SPI_CH_A0_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_A0_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                               
#define      SPI_CH_A0_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_A0_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_A0_LSBFE        SPI_LSB_FIRST 
#define      PCSA0_PAD_SEL          PCS_PAD_01

#endif

#if PCSA1_ENABLE

#define      SPI_CH_A1_DBR          DBR_VAL_1M
#define      SPI_CH_A1_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_A1_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_A1_FMSZ         FRAMESIZE_8
#define      SPI_CH_A1_CPOL         SPI_CPOL_LOW
#define      SPI_CH_A1_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_A1_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_A1_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_A1_ASC          4    /* After  SCK delay     SCALER */ 
#define      SPI_CH_A1_LSBFE        SPI_MSB_FIRST
#define      PCSA1_PAD_SEL          PCS_PAD_01

#endif

#if PCSA2_ENABLE

#define      SPI_CH_A2_DBR          DBR_VAL_1M
#define      SPI_CH_A2_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_A2_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_A2_FMSZ         FRAMESIZE_8
#define      SPI_CH_A2_CPOL         SPI_CPOL_LOW
#define      SPI_CH_A2_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_A2_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_A2_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_A2_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_A2_LSBFE        SPI_MSB_FIRST
#define      PCSA2_PAD_SEL          PCS_PAD_01

#endif

#if PCSA3_ENABLE

#define      SPI_CH_A3_DBR          DBR_VAL_1M
#define      SPI_CH_A3_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_A3_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_A3_FMSZ         FRAMESIZE_8
#define      SPI_CH_A3_CPOL         SPI_CPOL_LOW
#define      SPI_CH_A3_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_A3_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_A3_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_A3_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_A3_LSBFE        SPI_MSB_FIRST
#define      PCSA3_PAD_SEL          PCS_PAD_01

#endif

#if PCSA4_ENABLE

#define      SPI_CH_A4_DBR          DBR_VAL_1M
#define      SPI_CH_A4_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_A4_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_A4_FMSZ         FRAMESIZE_8
#define      SPI_CH_A4_CPOL         SPI_CPOL_LOW
#define      SPI_CH_A4_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_A4_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_A4_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_A4_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_A4_LSBFE        SPI_MSB_FIRST
#define      PCSA4_PAD_SEL          PCS_PAD_01

#endif


#if PCSA5_ENABLE

#define      SPI_CH_A5_DBR          DBR_VAL_1M
#define      SPI_CH_A5_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_A5_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_A5_FMSZ         FRAMESIZE_16
#define      SPI_CH_A5_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_A5_CPHA         SPI_CPHA_LOW 
#define      SPI_CH_A5_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_A5_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_A5_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_A5_LSBFE        SPI_LSB_FIRST 
#define      PCSA5_PAD_SEL          PCS_PAD_01

#endif


#if PCSB0_ENABLE
#if (FSYS == 60)
/* SMP */
#define      SPI_CH_B0_DBR          DBR_VAL_187K
#define      SPI_CH_B0_BRP          SPI_BRP_187K                   /*Channel Baud Rate */ 
#define      SPI_CH_B0_BR           SPI_BR_187K                    /*Channel Baud Rate */ 
#define      SPI_CH_B0_FMSZ         FRAMESIZE_8
#define      SPI_CH_B0_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_B0_CPHA         SPI_CPHA_LOW 
#define      SPI_CH_B0_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_B0_DT           13    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B0_ASC          3    /* After SCK delay to PCS off SCALER Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/  
#define      SPI_CH_B0_LSBFE        SPI_MSB_FIRST
#define      PCSB0_PAD_SEL          PCS_PAD_01
#else /* FSYS == 80MHz */
#define      SPI_CH_B0_DBR          DBR_VAL_178K
#define      SPI_CH_B0_BRP          SPI_BRP_178K                   /*Channel Baud Rate */ 
#define      SPI_CH_B0_BR           SPI_BR_178K                    /*Channel Baud Rate */ 
#define      SPI_CH_B0_FMSZ         FRAMESIZE_8
#define      SPI_CH_B0_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_B0_CPHA         SPI_CPHA_LOW 
#define      SPI_CH_B0_CSSCK        7    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_B0_DT           13    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B0_ASC          7    /* After SCK delay to PCS off SCALER Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/  
#define      SPI_CH_B0_LSBFE        SPI_MSB_FIRST
#define      PCSB0_PAD_SEL          PCS_PAD_01
#endif
#endif

#if PCSB1_ENABLE

#define      SPI_CH_B1_DBR          DBR_VAL_1M
#define      SPI_CH_B1_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_B1_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_B1_FMSZ         FRAMESIZE_8
#define      SPI_CH_B1_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_B1_CPHA         SPI_CPHA_HIGH 
#define      SPI_CH_B1_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                     
#define      SPI_CH_B1_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B1_ASC          4    /* After SCK delay to PCS off SCALER Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/ 
#define      SPI_CH_B1_LSBFE        SPI_MSB_FIRST
#define      PCSB1_PAD_SEL          PCS_PAD_02

#endif

#if PCSB2_ENABLE

#define      SPI_CH_B2_DBR          DBR_VAL_1M
#define      SPI_CH_B2_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_B2_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_B2_FMSZ         FRAMESIZE_8
#define      SPI_CH_B2_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_B2_CPHA         SPI_CPHA_HIGH 
#define      SPI_CH_B2_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                        
#define      SPI_CH_B2_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B2_ASC          4    /* After SCK delay to PCS off SCALER Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/  
#define      SPI_CH_B2_LSBFE        SPI_MSB_FIRST
#define      PCSB2_PAD_SEL          PCS_PAD_02

#endif

#if PCSB3_ENABLE

#define      SPI_CH_B3_DBR          DBR_VAL_1M
#define      SPI_CH_B3_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_B3_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_B3_FMSZ         FRAMESIZE_8
#define      SPI_CH_B3_CPOL         SPI_CPOL_LOW
#define      SPI_CH_B3_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_B3_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_B3_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B3_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_B3_LSBFE        SPI_MSB_FIRST
#define      PCSB3_PAD_SEL          PCS_PAD_02

#endif

#if PCSB4_ENABLE

#define      SPI_CH_B4_DBR          DBR_VAL_1M
#define      SPI_CH_B4_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_B4_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_B4_FMSZ         FRAMESIZE_8
#define      SPI_CH_B4_CPOL         SPI_CPOL_LOW
#define      SPI_CH_B4_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_B4_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_B4_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B4_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_B4_LSBFE        SPI_MSB_FIRST
#define      PCSB4_PAD_SEL          PCS_PAD_02

#endif


#if PCSB5_ENABLE

#define      SPI_CH_B5_DBR          DBR_VAL_1M
#define      SPI_CH_B5_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_B5_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_B5_FMSZ         FRAMESIZE_8
#define      SPI_CH_B5_CPOL         SPI_CPOL_LOW
#define      SPI_CH_B5_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_B5_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_B5_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_B5_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_B5_LSBFE        SPI_MSB_FIRST
#define      PCSB5_PAD_SEL          PCS_PAD_02

#endif



#if PCSC0_ENABLE

/* TLE6244X */
#define      SPI_CH_C0_DBR          DBR_VAL_2M
#define      SPI_CH_C0_BRP          SPI_BRP_2M                   /*Channel Baud Rate */ 
#define      SPI_CH_C0_BR           SPI_BR_2M                    /*Channel Baud Rate */ 
#define      SPI_CH_C0_FMSZ         FRAMESIZE_16
#define      SPI_CH_C0_CPOL         SPI_CPOL_HIGH           // clock active high
#define      SPI_CH_C0_CPHA         SPI_CPHA_HIGH           // sample SDO on falling edge
#define      SPI_CH_C0_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_C0_DT           5    /* Delay After Transfer SCALER */  
#define      SPI_CH_C0_ASC          4    /* After  SCK delay     SCALER */  
#define      SPI_CH_C0_LSBFE        SPI_MSB_FIRST   
#define      PCSC0_PAD_SEL          PCS_PAD_02

#endif

#if PCSC1_ENABLE

/* MCP6S9X */
#define      SPI_CH_C1_DBR          DBR_VAL_2M
#define      SPI_CH_C1_BRP          SPI_BRP_2M                   /*Channel Baud Rate */ 
#define      SPI_CH_C1_BR           SPI_BR_2M                    /*Channel Baud Rate */ 
#define      SPI_CH_C1_FMSZ         FRAMESIZE_16
#define      SPI_CH_C1_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_C1_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_C1_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_C1_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_C1_ASC          4    /* After  SCK delay     SCALER */ 
#define      SPI_CH_C1_LSBFE        SPI_MSB_FIRST
#define      PCSC1_PAD_SEL          PCS_PAD_02

#endif

#if PCSC2_ENABLE

/* HB_STL9958 A */
#define      SPI_CH_C2_DBR          DBR_VAL_2M
#define      SPI_CH_C2_BRP          SPI_BRP_2M                   /*Channel Baud Rate */ 
#define      SPI_CH_C2_BR           SPI_BR_2M                    /*Channel Baud Rate */ 
#define      SPI_CH_C2_FMSZ         FRAMESIZE_16
#define      SPI_CH_C2_CPOL         SPI_CPOL_HIGH      // clock active high
#define      SPI_CH_C2_CPHA         SPI_CPHA_HIGH       // sample SDO on falling edge   
#define      SPI_CH_C2_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_C2_DT           5    /* Delay After Transfer SCALER */  
#define      SPI_CH_C2_ASC          4    /* After  SCK delay     SCALER */  
#define      SPI_CH_C2_LSBFE        SPI_LSB_FIRST
#define      PCSC2_PAD_SEL          PCS_PAD_02

#endif

#if PCSC3_ENABLE

/* HB_STL9958 B */
#define      SPI_CH_C3_DBR          DBR_VAL_2M
#define      SPI_CH_C3_BRP          SPI_BRP_2M                   /*Channel Baud Rate */ 
#define      SPI_CH_C3_BR           SPI_BR_2M                    /*Channel Baud Rate */ 
#define      SPI_CH_C3_FMSZ         FRAMESIZE_16
#define      SPI_CH_C3_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_C3_CPHA         SPI_CPHA_HIGH  
#define      SPI_CH_C3_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_C3_DT           5    /* Delay After Transfer SCALER */  
#define      SPI_CH_C3_ASC          4    /* After  SCK delay     SCALER */  
#define      SPI_CH_C3_LSBFE        SPI_LSB_FIRST
#define      PCSC3_PAD_SEL          PCS_PAD_02

#endif

#if PCSC4_ENABLE

#define      SPI_CH_C4_DBR          DBR_VAL_1M
#define      SPI_CH_C4_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_C4_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_C4_FMSZ         FRAMESIZE_8
#define      SPI_CH_C4_CPOL         SPI_CPOL_LOW
#define      SPI_CH_C4_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_C4_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_C4_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_C4_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_C4_LSBFE        SPI_MSB_FIRST
#define      PCSC4_PAD_SEL          PCS_PAD_02

#endif


#if PCSC5_ENABLE

#define      SPI_CH_C5_DBR          DBR_VAL_1M
#define      SPI_CH_C5_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_C5_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_C5_FMSZ         FRAMESIZE_8
#define      SPI_CH_C5_CPOL         SPI_CPOL_LOW
#define      SPI_CH_C5_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_C5_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_C5_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_C5_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_C5_LSBFE        SPI_MSB_FIRST
#define      PCSC5_PAD_SEL          PCS_PAD_02

#endif


#if PCSD0_ENABLE

#define      SPI_CH_D0_DBR          DBR_VAL_125K
#define      SPI_CH_D0_BRP          SPI_BRP_125K                   /*Channel Baud Rate */ 
#define      SPI_CH_D0_BR           SPI_BR_125K                  /*Channel Baud Rate */ 
#define      SPI_CH_D0_FMSZ         FRAMESIZE_8
#define      SPI_CH_D0_CPOL         SPI_CPOL_HIGH
#define      SPI_CH_D0_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_D0_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D0_DT           13   /* Delay After Transfer SCALER */  
#define      SPI_CH_D0_ASC          3    /* After  SCK delay     SCALER */  
#define      SPI_CH_D0_LSBFE        SPI_MSB_FIRST  
#define      PCSD0_PAD_SEL          PCS_PAD_02

#endif

#if PCSD1_ENABLE

#define      SPI_CH_D1_DBR          DBR_VAL_1M
#define      SPI_CH_D1_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_D1_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_D1_FMSZ         FRAMESIZE_8
#define      SPI_CH_D1_CPOL         SPI_CPOL_LOW
#define      SPI_CH_D1_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_D1_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D1_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_D1_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_D1_LSBFE        SPI_MSB_FIRST
#define      PCSD1_PAD_SEL          PCS_PAD_02

#endif

#if PCSD2_ENABLE

#define      SPI_CH_D2_DBR          DBR_VAL_1M
#define      SPI_CH_D2_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_D2_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_D2_FMSZ         FRAMESIZE_8
#define      SPI_CH_D2_CPOL         SPI_CPOL_LOW
#define      SPI_CH_D2_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_D2_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D2_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_D2_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_D2_LSBFE        SPI_MSB_FIRST
#define      PCSD2_PAD_SEL          PCS_PAD_02

#endif

#if PCSD3_ENABLE

#define      SPI_CH_D3_DBR          DBR_VAL_1M
#define      SPI_CH_D3_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_D3_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_D3_FMSZ         FRAMESIZE_8
#define      SPI_CH_D3_CPOL         SPI_CPOL_LOW
#define      SPI_CH_D3_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_D3_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D3_DT           5    /* Delay After Transfer SCALER */  
#define      SPI_CH_D3_ASC          2    /* After  SCK delay     SCALER */  
#define      SPI_CH_D3_LSBFE        SPI_MSB_FIRST
#define      PCSD3_PAD_SEL          PCS_PAD_02

#endif

#if PCSD4_ENABLE

#define      SPI_CH_D4_DBR          DBR_VAL_1M
#define      SPI_CH_D4_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_D4_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_D4_FMSZ         FRAMESIZE_8
#define      SPI_CH_D4_CPOL         SPI_CPOL_LOW
#define      SPI_CH_D4_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_D4_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D4_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_D4_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_D4_LSBFE        SPI_MSB_FIRST
#define      PCSD4_PAD_SEL          PCS_PAD_02

#endif


#if PCSD5_ENABLE

#define      SPI_CH_D5_DBR          DBR_VAL_1M
#define      SPI_CH_D5_BRP          SPI_BRP_1M                   /*Channel Baud Rate */ 
#define      SPI_CH_D5_BR           SPI_BR_1M                    /*Channel Baud Rate */ 
#define      SPI_CH_D5_FMSZ         FRAMESIZE_8
#define      SPI_CH_D5_CPOL         SPI_CPOL_LOW
#define      SPI_CH_D5_CPHA         SPI_CPHA_LOW  
#define      SPI_CH_D5_CSSCK        4    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                         
#define      SPI_CH_D5_DT           5    /* Delay After Transfer SCALER */ 
#define      SPI_CH_D5_ASC          2    /* After  SCK delay     SCALER */ 
#define      SPI_CH_D5_LSBFE        SPI_MSB_FIRST
#define      PCSD5_PAD_SEL          PCS_PAD_02

#endif
