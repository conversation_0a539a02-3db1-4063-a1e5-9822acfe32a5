/**
 ******************************************************************************
 **  Filename:      TrqEst.h
 **  Date:          10-Mar-2023
 **
 **  Model Version: 1.1961
 ******************************************************************************
 **/

#ifndef RTW_HEADER_TrqEst_h_
#define RTW_HEADER_TrqEst_h_
#ifndef TrqEst_COMMON_INCLUDES_
# define TrqEst_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* TrqEst_COMMON_INCLUDES_ */

#include "TrqEst_types.h"

/* Includes for objects with custom storage classes. */
#include "ETPU_EngineDefs.h"
#include "trac_ctrl.h"
#include "trq_est.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKCMFPRES_dim                  9U                        /* Referenced by:
                                                                  * '<S14>/BKCMFPRES_dim'
                                                                  * '<S29>/BKCMFPRES_dim'
                                                                  */
#define BKCMFTWAT_dim                  7U                        /* Referenced by:
                                                                  * '<S14>/BKCMFTWAT_dim'
                                                                  * '<S30>/BKCMFTWAT_dim'
                                                                  * '<S31>/BKCMFTWAT_dim'
                                                                  */
#define BKCMFVEH_dim                   6U                        /* Referenced by:
                                                                  * '<S38>/BKCMFVEH_dim'
                                                                  * '<S39>/BKCMFVEH_dim'
                                                                  */
#define BKCMLOAD_dim                   9U                        /* Referenced by: '<S34>/BKCMLOAD_dim' */
#define BKCMRPMF_dim                   14U                       /* Referenced by:
                                                                  * '<S29>/BKCMRPMF_dim'
                                                                  * '<S30>/BKCMRPMF_dim'
                                                                  * '<S34>/BKCMRPMF_dim'
                                                                  */
#define BKTDCCRKCMF_dim                7U                        /* Referenced by: '<S31>/BKTDCCRKCMF_dim' */
#define CM_INIT                        0                         /* Referenced by:
                                                                  * '<S5>/CM_INIT'
                                                                  * '<S12>/CM_INIT'
                                                                  */
#define ID_TRQ_EST                     20067228U                 /* Referenced by: '<S5>/ID_TRQ_EST' */

/* mask */
#define MAXEFF                         16384U                    /* Referenced by:
                                                                  * '<S3>/MAXEFF'
                                                                  * '<S4>/MAXEFF'
                                                                  * '<S13>/MAXEFF'
                                                                  */

/* Block signals for system '<S3>/cmi_formula_cyl' */
typedef struct {
  int16_T Divide4;                     /* '<S8>/Divide4' */
} rtB_cmi_formula_cyl_TrqEst;

/* Block signals for system '<S18>/cmi_formula_cycle1' */
typedef struct {
  int16_T Divide4;                     /* '<S20>/Divide4' */
} rtB_cmi_formula_cycle1_TrqEst;

/* Block signals for system '<S18>/cmi_formula_cycle2' */
typedef struct {
  int16_T Divide4;                     /* '<S21>/Divide4' */
} rtB_cmi_formula_cycle2_TrqEst;

/* Block signals (default storage) */
typedef struct {
  rtB_cmi_formula_cycle1_TrqEst cmi_formula_cycle6;/* '<S18>/cmi_formula_cycle6' */
  rtB_cmi_formula_cycle1_TrqEst cmi_formula_cycle5;/* '<S18>/cmi_formula_cycle5' */
  rtB_cmi_formula_cycle2_TrqEst cmi_formula_cycle4;/* '<S18>/cmi_formula_cycle4' */
  rtB_cmi_formula_cycle2_TrqEst cmi_formula_cycle3;/* '<S18>/cmi_formula_cycle3' */
  rtB_cmi_formula_cycle2_TrqEst cmi_formula_cycle2;/* '<S18>/cmi_formula_cycle2' */
  rtB_cmi_formula_cycle1_TrqEst cmi_formula_cycle1;/* '<S18>/cmi_formula_cycle1' */
  rtB_cmi_formula_cyl_TrqEst cmi_formula_cyl_n;/* '<S4>/cmi_formula_cyl' */
  rtB_cmi_formula_cyl_TrqEst cmi_formula_cyl;/* '<S3>/cmi_formula_cyl' */
} BlockIO_TrqEst;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S12>/Memory' */
  int32_T Memory1_PreviousInput;       /* '<S27>/Memory1' */
  int16_T Memory4_PreviousInput;       /* '<S15>/Memory4' */
  int16_T Memory_PreviousInput_m;      /* '<S15>/Memory' */
  int16_T Memory1_PreviousInput_o;     /* '<S15>/Memory1' */
  int16_T Memory2_PreviousInput;       /* '<S15>/Memory2' */
  int16_T Memory3_PreviousInput;       /* '<S15>/Memory3' */
} D_Work_TrqEst;

/* Block signals (default storage) */
extern BlockIO_TrqEst TrqEst_B;

/* Block states (default storage) */
extern D_Work_TrqEst TrqEst_DWork;

/* Model entry point functions */
extern void TrqEst_initialize(void);

/* Exported entry point function */
extern void Trig_TrqEst_ev_NoSync(void);

/* Exported entry point function */
extern void Trig_TrqEst_ev_PowerOn(void);

/* Exported entry point function */
extern void Trig_TrqEst_ev_PreHTDC(void);

/* Exported entry point function */
extern void Trig_TrqEst_ev_PreTDC(void);

/* Exported entry point function */
extern void Trig_TrqEst_ev_T10ms(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Propagation' : Unused code path elimination
 * Block '<S41>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S46>/Data Type Duplicate' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate' : Unused code path elimination
 * Block '<S47>/Data Type Duplicate' : Unused code path elimination
 * Block '<S47>/Data Type Propagation' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S49>/Constant' : Unused code path elimination
 * Block '<S49>/Data Type Duplicate' : Unused code path elimination
 * Block '<S49>/Data Type Propagation' : Unused code path elimination
 * Block '<S50>/Data Type Propagation' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S51>/Data Type Propagation' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion' : Eliminate redundant data type conversion
 * Block '<S44>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S44>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S44>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S44>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S45>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S45>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S45>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S45>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion' : Eliminate redundant data type conversion
 * Block '<S36>/Reshape' : Reshape block reduction
 * Block '<S47>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S49>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S49>/Reshape' : Reshape block reduction
 * Block '<S50>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S50>/Reshape' : Reshape block reduction
 * Block '<S51>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S51>/Reshape' : Reshape block reduction
 * Block '<S32>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TrqEst'
 * '<S1>'   : 'TrqEst/Model Info'
 * '<S2>'   : 'TrqEst/TrqEst'
 * '<S3>'   : 'TrqEst/TrqEst/PreHTDC'
 * '<S4>'   : 'TrqEst/TrqEst/PreTDC'
 * '<S5>'   : 'TrqEst/TrqEst/Reset'
 * '<S6>'   : 'TrqEst/TrqEst/T10ms'
 * '<S7>'   : 'TrqEst/TrqEst/PreHTDC/Assign_CmiPotEst'
 * '<S8>'   : 'TrqEst/TrqEst/PreHTDC/cmi_formula_cyl'
 * '<S9>'   : 'TrqEst/TrqEst/PreTDC/Assign_CmiPotEst'
 * '<S10>'  : 'TrqEst/TrqEst/PreTDC/cmi_formula_cyl'
 * '<S11>'  : 'TrqEst/TrqEst/Reset/Assign_CmiPotEst'
 * '<S12>'  : 'TrqEst/TrqEst/T10ms/Cmi2Cme'
 * '<S13>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation'
 * '<S14>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation'
 * '<S15>'  : 'TrqEst/TrqEst/T10ms/Cmi2Cme/DeltaCmeEstWheelF'
 * '<S16>'  : 'TrqEst/TrqEst/T10ms/Cmi2Cme/FOF_Reset_S16_FXP'
 * '<S17>'  : 'TrqEst/TrqEst/T10ms/Cmi2Cme/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S18>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi'
 * '<S19>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle'
 * '<S20>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle1'
 * '<S21>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle2'
 * '<S22>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle3'
 * '<S23>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle4'
 * '<S24>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle5'
 * '<S25>'  : 'TrqEst/TrqEst/T10ms/Cmi_Calculation/Cmi/cmi_formula_cycle6'
 * '<S26>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain'
 * '<S27>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_DPAtmPresF'
 * '<S28>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/DPAtmPres_IndexRatio'
 * '<S29>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Look2D_TBCMF'
 * '<S30>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Look2D_TBCMFTW'
 * '<S31>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Look2D_TBCRKCMF'
 * '<S32>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/TWater_IndexRatio'
 * '<S33>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel'
 * '<S34>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_Ratio'
 * '<S35>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/If_QAirRef'
 * '<S36>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Look2D_IR_U1'
 * '<S37>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/else_QAIRREFPERC'
 * '<S38>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel/LookUp_VTCMFVEH'
 * '<S39>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel/LookUp_VTCMFVEH1'
 * '<S40>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel/LookUp_VTCMFVEH/LookUp_S16_U16'
 * '<S41>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel/LookUp_VTCMFVEH/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S42>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel/LookUp_VTCMFVEH1/LookUp_S16_U16'
 * '<S43>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_CmfWheel/LookUp_VTCMFVEH1/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S44>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_Ratio/PreLookUpIdSearch_U16'
 * '<S45>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Calc_Ratio/RpmF_IndexRatio'
 * '<S46>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_CmiGain/Look2D_IR_U1/Data Type Conversion Inherited1'
 * '<S47>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_DPAtmPresF/FOF_Reset_S16_FXP'
 * '<S48>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Calc_DPAtmPresF/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S49>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Look2D_TBCMF/Look2D_IR_U8'
 * '<S50>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Look2D_TBCMFTW/Look2D_IR_S8'
 * '<S51>'  : 'TrqEst/TrqEst/T10ms/Friction_Interpolation/Look2D_TBCRKCMF/Look2D_U8_U16_S16'
 */

/*-
 * Requirements for '<Root>': TrqEst
 */
#endif                                 /* RTW_HEADER_TrqEst_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
