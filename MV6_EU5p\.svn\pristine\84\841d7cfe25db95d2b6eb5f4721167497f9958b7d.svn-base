/*
 * File: AirDiagMgm.c
 *
 * Real-Time Workshop code generated for Simulink model AirDiagMgm.
 *
 * Model version                        : 1.655
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Wed Jun 16 18:21:44 2010
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Wed Jun 16 18:21:45 2010
 */

#include "AirDiagMgm.h"
#include "AirDiagMgm_private.h"

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_AIRDIAGMGM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/* Exported block signals */
int16_T PresErrThr;                    /* '<S20>/Switch2'
                                        * Pressure error threshold for air diagnosis
                                        */
uint8_T EnPresTest;                    /* '<S9>/Logical Operator1'
                                        * Air Diagnosis Enable Flag
                                        */
uint8_T FlgPresStable;                 /* '<S34>/SteadyStateDetect'
                                        * Air Diagnosis Pressure stability Flag
                                        */
uint8_T state;                         /* '<S34>/SteadyStateDetect' */
uint8_T FlgFreezePresDiag;             /* '<S20>/Logical Operator2'
                                        * Flag to freeze the coherence analysis (=1)
                                        */
uint8_T StChoiceAirDiag;               /* '<S19>/Truth Table'
                                        * Choice in the truth table for air diag
                                        */
uint8_T FlgADiagOpenThr;               /* '<S19>/Control_flags'
                                        * Pressure control is opening the throttle
                                        */
uint8_T FlgADiagCloseThr;              /* '<S19>/Control_flags'
                                        * Pressure control is closing the throttle
                                        */

/* Exported block states */
int16_T PresErr1;                      /* '<Root>/_DataStoreBlk_1'
                                        * Pressure error calculated for throttle 1
                                        */
int16_T PresErr2;                      /* '<Root>/_DataStoreBlk_3'
                                        * Pressure error calculated for throttle 2
                                        */
uint8_T FlgUnexpPresFault;             /* '<Root>/_DataStoreBlk_2'
                                        * Unexpected error in pressure diagnosis
                                        */

/* Block signals (auto storage) */
BlockIO_AirDiagMgm AirDiagMgm_B;

/* Block states (auto storage) */
D_Work_AirDiagMgm AirDiagMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_AirDiagMgm AirDiagMgm_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_AirDiagMgm AirDiagMgm_U;
int16_T AvgEnerLam;                    /* Mean energy of the lambda control */
uint8_T FlgPresCoh1;                   /* Coherence test result for throttle 1 */
uint8_T FlgPresCoh2;                   /* Coherence test result for throttle 2 */

/* Output and update for atomic system:
 *   '<S14>/Init'
 *   '<S8>/Init'
 */
void AirDiagMgm_Init(void)
{
  /* Stateflow: '<S14>/Init' */
  /* Gateway: AirDiagMgm/TDC/Disabled/Init */
  /* During: AirDiagMgm/TDC/Disabled/Init */
  /* Transition: '<S16>:1' */
  FlgPresCoh1 = 1U;
  FlgPresCoh2 = 1U;
  PresErr1 = 0;
  PresErr2 = 0;
  FlgUnexpPresFault = 0U;
  AvgEnerLam = 0;
}

/* Start for atomic system:
 *   '<S15>/Coherence_Analysis'
 *   '<S15>/Coherence_Analysis1'
 */
void AirDia_Coherence_Analysis_Start(void)
{
  /* DataTypeConversion Block: '<S21>/Data Type Conversion1'
   *
   * Regarding '<S21>/Data Type Conversion1':
   *   Eliminate redundant data type conversion
   */
}

/* Output and update for atomic system:
 *   '<S15>/Coherence_Analysis'
 *   '<S15>/Coherence_Analysis1'
 */
void AirDiagMgm_Coherence_Analysis(uint16_T rtu_MapSignal, uint16_T
  rtu_PresIntkF, int16_T rtu_PresErrThr, uint8_T rtu_flgdiagval,
  rtB_Coherence_Analysis_AirDiagM *localB)
{
  /* local block i/o variables */
  int32_T rtb_GenAbs;

  {
    int16_T rtb_preserr_abs;
    uint8_T rtb_Switch4;

    /* Sum: '<S17>/Add' */
    localB->Add = (int16_T)(rtu_MapSignal - rtu_PresIntkF);

    /* DataTypeConversion: '<S21>/Data Type Conversion4' */
    rtb_GenAbs = (int32_T)localB->Add;

    /* S-Function (GenAbs): '<S21>/GenAbs' incorporates:
     *  Constant: '<S17>/INT16_TYPE'
     */
    rtb_GenAbs = GenAbs( rtb_GenAbs, INT16_TYPE);

    /* DataTypeConversion: '<S22>/Conversion' */
    rtb_preserr_abs = (int16_T)rtb_GenAbs;

    /* Switch: '<S17>/Switch2' incorporates:
     *  Constant: '<S17>/FALSE1'
     *  Constant: '<S17>/THRPRESCANC'
     *  Constant: '<S17>/TRUE'
     *  RelationalOperator: '<S17>/Relational Operator2'
     */
    if (rtb_preserr_abs < THRPRESCANC) {
      rtb_Switch4 = 1U;
    } else {
      rtb_Switch4 = 0U;
    }

    /* Switch: '<S17>/Switch4' incorporates:
     *  Constant: '<S17>/TRUE1'
     */
    if (rtu_flgdiagval != 0) {
    } else {
      rtb_Switch4 = 1U;
    }

    /* Switch: '<S17>/Switch1' incorporates:
     *  Constant: '<S17>/FALSE'
     *  RelationalOperator: '<S17>/Relational Operator'
     */
    if (rtb_preserr_abs > rtu_PresErrThr) {
      localB->Switch1 = 0U;
    } else {
      localB->Switch1 = rtb_Switch4;
    }
  }
}

/* Disable for function-call system: '<S1>/TDC' */
void AirDiagMgm_TDC_Disable(void)
{
  /* Disable for enable SubSystem: '<S9>/Enabled' */
  AirDiagMgm_DWork.Enabled_MODE = SUBSYS_DISABLED;

  /* end of Disable for SubSystem: '<S9>/Enabled' */
}

/* Start for function-call system: '<S1>/TDC' */
void AirDiagMgm_TDC_Start(void)
{
  /* Start for enable SubSystem: '<S9>/Enabled' */

  /* Start for atomic SubSystem: '<S15>/Coherence_Analysis' */
  AirDia_Coherence_Analysis_Start();

  /* end of Start for SubSystem: '<S15>/Coherence_Analysis' */

  /* Start for atomic SubSystem: '<S15>/Coherence_Analysis1' */
  AirDia_Coherence_Analysis_Start();

  /* end of Start for SubSystem: '<S15>/Coherence_Analysis1' */

  /* Start for Truth Table: '<S19>/Truth Table' incorporates:
   *  Start for SubSystem: '<S19>/call_holeman'
   *  Start for SubSystem: '<S19>/call_senspres'
   */

  /* InitializeConditions for Memory: '<S20>/Memory' */
  AirDiagMgm_DWork.Memory_PreviousInput_gyuc = 0U;

  /* InitializeConditions for Memory: '<S34>/Memory1' */
  AirDiagMgm_DWork.Memory1_PreviousInput = 0U;

  /* InitializeConditions for Memory: '<S34>/Memory' */
  AirDiagMgm_DWork.Memory_PreviousInput = 0U;

  /* InitializeConditions for Memory: '<S15>/Memory' */
  AirDiagMgm_DWork.Memory_PreviousInput_nyam = 0U;

  /* end of Start for SubSystem: '<S9>/Enabled' */
}

/* Output and update for function-call system: '<S1>/TDC' */
void AirDiagMgm_TDC(void)
{
  /* local block i/o variables */
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_Memory1;
  uint16_T rtb_Memory;
  uint8_T rtb_Memory_atse;
  uint8_T rtb_Memory_cn4z;

  {
    uint16_T sf_i;
    uint8_T eml_ptfaultsenspres;
    uint8_T eml_ptfaultholeman;
    boolean_T eml_aVarTruthTableCondition;
    boolean_T eml_aVarTruthTableCondition_0;
    boolean_T eml_aVarTruthTableCondition_1;
    boolean_T eml_aVarTruthTableCondition_2;
    boolean_T eml_aVarTruthTableCondition_3;
    boolean_T eml_aVarTruthTableCondition_4;
    boolean_T eml_aVarTruthTableCondition_5;
    boolean_T eml_aVarTruthTableCondition_6;
    boolean_T eml_aVarTruthTableCondition_7;
    boolean_T eml_aVarTruthTableCondition_8;
    boolean_T eml_aVarTruthTableCondition_9;
    boolean_T eml_b;
    uint8_T rtb_vtrec_13;
    int32_T rtb_sum;
    int16_T rtb_AvgEnerLam;

    /* Logic: '<S9>/Logical Operator1' incorporates:
     *  Constant: '<S13>/Constant'
     *  Constant: '<S9>/ENAIRDIAG'
     *  Constant: '<S9>/MINANGTHRAIRDIAG'
     *  Constant: '<S9>/MINRPMAIRDIAG'
     *  Constant: '<S9>/MINTDCAIRDIAG'
     *  Inport: '<Root>/AngThrottle'
     *  Inport: '<Root>/CntTdcCrk'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/StThrRec'
     *  Inport: '<Root>/TrqStartFlg'
     *  RelationalOperator: '<S13>/Compare'
     *  RelationalOperator: '<S9>/Relational Operator2'
     *  RelationalOperator: '<S9>/Relational Operator3'
     *  RelationalOperator: '<S9>/Relational Operator4'
     */
    EnPresTest = ((ENAIRDIAG != 0) && (TrqStartFlg != 0) && (Rpm > MINRPMAIRDIAG)
                  && (AngThrottle > MINANGTHRAIRDIAG) && (CntTdcCrk >
      MINTDCAIRDIAG) && ((StThrRec <= 1) != 0));

    /* Outputs for enable SubSystem: '<S9>/Disabled' incorporates:
     *  EnablePort: '<S14>/Enable'
     *  Logic: '<S9>/Logical Operator'
     */
    if (!(EnPresTest != 0)) {
      AirDiagMgm_Init();
    }

    /* end of Outputs for SubSystem: '<S9>/Disabled' */

    /* Outputs for enable SubSystem: '<S9>/Enabled' incorporates:
     *  EnablePort: '<S15>/Enable'
     */
    if (EnPresTest > 0) {
      if (AirDiagMgm_DWork.Enabled_MODE == SUBSYS_DISABLED) {
        /* InitializeConditions for Memory: '<S20>/Memory' */
        AirDiagMgm_DWork.Memory_PreviousInput_gyuc = 0U;

        /* InitializeConditions for Memory: '<S34>/Memory1' */
        AirDiagMgm_DWork.Memory1_PreviousInput = 0U;

        /* InitializeConditions for Memory: '<S34>/Memory' */
        AirDiagMgm_DWork.Memory_PreviousInput = 0U;

        /* InitializeConditions for Memory: '<S15>/Memory' */
        AirDiagMgm_DWork.Memory_PreviousInput_nyam = 0U;
        AirDiagMgm_DWork.Enabled_MODE = SUBSYS_ENABLED;
      }

      /* Memory: '<S20>/Memory' */
      rtb_Memory_cn4z = AirDiagMgm_DWork.Memory_PreviousInput_gyuc;

      /* Memory: '<S34>/Memory1' */
      rtb_Memory1 = AirDiagMgm_DWork.Memory1_PreviousInput;

      /* Memory: '<S34>/Memory' */
      rtb_Memory = AirDiagMgm_DWork.Memory_PreviousInput;

      /* S-Function (SteadyStateDetect): '<S34>/SteadyStateDetect' incorporates:
       *   Inport: '<Root>/PresIntk0'
       *  Constant: '<S20>/no_reset'
       *  Constant: '<S20>/THRSTABPRES'
       *  Constant: '<S20>/NCSTABPRES'
       */
      SteadyStateDetect( &FlgPresStable, &state, &rtb_SteadyStateDetect_o3,
                        &rtb_SteadyStateDetect_o4, PresIntk0, ((uint8_T)0U),
                        THRSTABPRES, NCSTABPRES, rtb_Memory_cn4z, rtb_Memory1,
                        rtb_Memory);

      /* Switch: '<S20>/Switch2' incorporates:
       *  Constant: '<S20>/THRPRESHIGH'
       *  Constant: '<S20>/THRPRESLOW'
       */
      if (FlgPresStable != 0) {
        PresErrThr = THRPRESLOW;
      } else {
        PresErrThr = THRPRESHIGH;
      }

      /* Memory: '<S15>/Memory' */
      rtb_Memory_atse = AirDiagMgm_DWork.Memory_PreviousInput_nyam;

      /* Outputs for atomic SubSystem: '<S15>/Coherence_Analysis' */
      AirDiagMgm_Coherence_Analysis(MapSignal, PresIntkF1, PresErrThr,
        rtb_Memory_atse, &AirDiagMgm_B.Coherence_Analysis);

      /* end of Outputs for SubSystem: '<S15>/Coherence_Analysis' */

      /* DataStoreWrite: '<S15>/Data Store Write1' */
      PresErr1 = AirDiagMgm_B.Coherence_Analysis.Add;

      /* DataStoreWrite: '<S15>/Data Store Write3' */
      FlgPresCoh1 = AirDiagMgm_B.Coherence_Analysis.Switch1;

      /* Outputs for atomic SubSystem: '<S15>/Coherence_Analysis1' */
      AirDiagMgm_Coherence_Analysis(MapSignal, PresIntkF2, PresErrThr,
        rtb_Memory_atse, &AirDiagMgm_B.Coherence_Analysis1);

      /* end of Outputs for SubSystem: '<S15>/Coherence_Analysis1' */

      /* DataStoreWrite: '<S15>/Data Store Write5' */
      PresErr2 = AirDiagMgm_B.Coherence_Analysis1.Add;

      /* DataStoreWrite: '<S15>/Data Store Write7' */
      FlgPresCoh2 = AirDiagMgm_B.Coherence_Analysis1.Switch1;

      /* Stateflow: '<S26>/Chart' incorporates:
       *  Constant: '<S26>/Constant1'
       *  Inport: '<Root>/EnerLam'
       */
      /* Gateway: RTW_MEAN/Chart */
      /* During: RTW_MEAN/Chart */
      /* Transition: '<S30>:1' */
      rtb_sum = 0;
      for (sf_i = 0U; sf_i < N_CYLINDER; sf_i++) {
        /* Transition: '<S30>:2' */
        rtb_sum += EnerLam[sf_i];
      }

      /* Transition: '<S30>:3' */

      /* DataTypeConversion: '<S31>/Conversion' incorporates:
       *  Constant: '<S26>/Constant1'
       *  Product: '<S26>/Divide'
       */
      rtb_AvgEnerLam = (int16_T)div_nzp_s32_floor(rtb_sum, (int32_T)N_CYLINDER);

      /* DataStoreWrite: '<S19>/Data Store Write1' */
      AvgEnerLam = rtb_AvgEnerLam;

      /* Stateflow: '<S19>/Control_flags' incorporates:
       *  Inport: '<Root>/DAngThrFilt'
       *  Inport: '<Root>/PresCtrlActive'
       */
      /* Gateway: AirDiagMgm/TDC/Enabled/Diagnosis/Control_flags */
      /* During: AirDiagMgm/TDC/Enabled/Diagnosis/Control_flags */
      /* Transition: '<S25>:1' */
      if (PresCtrlActive == 1) {
        /* Transition: '<S25>:2' */
        FlgADiagOpenThr = (DAngThrFilt > THPRESDANG);
        FlgADiagCloseThr = (DAngThrFilt < -THPRESDANG);
      } else {
        /* Transition: '<S25>:3' */
        FlgADiagOpenThr = 0U;
        FlgADiagCloseThr = 0U;
      }

      /* Logic: '<S20>/Logical Operator2' incorporates:
       *  Constant: '<S20>/THPRESFHIGH'
       *  Constant: '<S20>/THPRESFLOW'
       *  Inport: '<Root>/PresIntkF1'
       *  RelationalOperator: '<S20>/Relational Operator3'
       *  RelationalOperator: '<S20>/Relational Operator4'
       */
      FlgFreezePresDiag = ((PresIntkF1 > THPRESFHIGH) || (PresIntkF1 <
        THPRESFLOW));

      /* Truth Table: '<S19>/Truth Table' incorporates:
       *  Constant: '<S19>/REC_ALFA_N'
       *  Inport: '<Root>/StAFCtrl'
       *  Inport: '<Root>/VtRec'
       *  Selector: '<S19>/Selector'
       *  SubSystem: '<S19>/call_holeman'
       *  SubSystem: '<S19>/call_senspres'
       */
      /* Truth Table Function 'AirDiagMgm/TDC/Enabled/Diagnosis/Truth Table': '<S27>:1' */
      /*  Steady state */
      /* Condition '#1': '<S27>:1:18' */
      eml_aVarTruthTableCondition = (FlgPresStable == 1);

      /*  Pressure is too high or too low */
      /* Condition '#2': '<S27>:1:21' */
      eml_aVarTruthTableCondition_0 = (FlgFreezePresDiag == 1);

      /*  Pressure not coherent */
      if ((AirDiagMgm_B.Coherence_Analysis.Switch1 == 0) &&
          (AirDiagMgm_B.Coherence_Analysis1.Switch1 == 0)) {
        /* Condition '#3': '<S27>:1:24' */
        eml_b = true;
      } else {
        /* Condition '#3': '<S27>:1:24' */
        eml_b = false;
      }

      /*  Pressure is lower than the estimated one */
      /* Condition '#4': '<S27>:1:27' */
      eml_aVarTruthTableCondition_1 = (AirDiagMgm_B.Coherence_Analysis.Add < 0);

      /*  Pressure is greater than the estimated one */
      /* Condition '#5': '<S27>:1:30' */
      eml_aVarTruthTableCondition_2 = (AirDiagMgm_B.Coherence_Analysis.Add > 0);

      /*  Recovery alfa-n attiva */
      /* Condition '#6': '<S27>:1:33' */
      eml_aVarTruthTableCondition_3 = (VtRec[(int32_T)REC_ALFA_N] == 1);

      /*  Diagnosi pressione funzionale attiva */
      /* Condition '#7': '<S27>:1:36' */
      eml_aVarTruthTableCondition_4 = (AirDiagMgm_B.stdiagpresfun == FAULT);

      /*  The lambda control is in closed loop */
      /* Condition '#8': '<S27>:1:39' */
      eml_aVarTruthTableCondition_5 = (StAFCtrl == AF_CL);

      /*  Lam control decreases fuel */
      /* Condition '#9': '<S27>:1:42' */
      eml_aVarTruthTableCondition_6 = (rtb_AvgEnerLam > THPRESENERLAM);

      /*  Lam ambda control increases fuel */
      /* Condition '#10': '<S27>:1:45' */
      eml_aVarTruthTableCondition_7 = (rtb_AvgEnerLam < (int16_T)(-THPRESENERLAM));

      /*  The pressure control opens the throttle */
      /* Condition '#11': '<S27>:1:48' */
      eml_aVarTruthTableCondition_8 = (FlgADiagOpenThr != 0);

      /*  The pressure control closes the throttle */
      /* Condition '#12': '<S27>:1:51' */
      eml_aVarTruthTableCondition_9 = (FlgADiagCloseThr != 0);
      if (eml_aVarTruthTableCondition && (!eml_aVarTruthTableCondition_0) &&
          (!eml_b)) {
        /* Decision 'D1': '<S27>:1:53' */
        /*  No pressure fault */
        rtb_vtrec_13 = 1U;

        /* Action '1': '<S27>:1:98' */
        eml_ptfaultsenspres = NO_FAULT;

        /* Action '1': '<S27>:1:98' */
        eml_ptfaultholeman = NO_FAULT;

        /* Action '1': '<S27>:1:99' */
        FlgUnexpPresFault = 0U;

        /* Action '1': '<S27>:1:100' */

        /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
         *  Constant: '<S29>/DIAG_PRES_NOT_COH'
         */
        DiagMgm_SetDiagState( DIAG_PRES_NOT_COH, AirDiagMgm_B.ptfaultsenspres,
                             &AirDiagMgm_B.stdiagpresfun);

        /* Action '1': '<S27>:1:100' */

        /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
         *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
         */
        DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD, AirDiagMgm_B.ptfaultholeman,
                             &AirDiagMgm_B.stdiagholeman);
      } else {
        /* Decision 'D2': '<S27>:1:53' */
        if ((!eml_aVarTruthTableCondition) && (!eml_aVarTruthTableCondition_0) &&
            (!eml_b)) {
          /* Decision 'D2': '<S27>:1:55' */
          /*  No pressure fault */
          rtb_vtrec_13 = 2U;

          /* Action '2': '<S27>:1:107' */
          eml_ptfaultsenspres = NO_FAULT;

          /* Action '2': '<S27>:1:107' */
          eml_ptfaultholeman = NO_FAULT;
        } else {
          /* Decision 'D3': '<S27>:1:55' */
          if (eml_aVarTruthTableCondition_0 && (!eml_b)) {
            /* Decision 'D3': '<S27>:1:57' */
            /*  No pressure fault */
            rtb_vtrec_13 = 2U;

            /* Action '2': '<S27>:1:107' */
            eml_ptfaultsenspres = NO_FAULT;

            /* Action '2': '<S27>:1:107' */
            eml_ptfaultholeman = NO_FAULT;
          } else if (eml_b && eml_aVarTruthTableCondition_1 &&
                     (!eml_aVarTruthTableCondition_2) &&
                     eml_aVarTruthTableCondition_5 &&
                     (!eml_aVarTruthTableCondition_6) &&
                     (!eml_aVarTruthTableCondition_7)) {
            /* Condition '#3': '<S27>:1:24' */
            /* Decision 'D4': '<S27>:1:59' */
            /*  Unexpected fault */
            rtb_vtrec_13 = 3U;

            /* Action '3': '<S27>:1:114' */
            eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

            /* Action '3': '<S27>:1:114' */
            eml_ptfaultholeman = NO_FAULT;

            /* Action '3': '<S27>:1:115' */
            FlgUnexpPresFault = 1U;

            /* Action '3': '<S27>:1:116' */

            /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
             *  Constant: '<S29>/DIAG_PRES_NOT_COH'
             */
            DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                 AirDiagMgm_B.ptfaultsenspres,
                                 &AirDiagMgm_B.stdiagpresfun);

            /* Action '3': '<S27>:1:116' */

            /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
             *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
             */
            DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                 AirDiagMgm_B.ptfaultholeman,
                                 &AirDiagMgm_B.stdiagholeman);
          } else {
            /* Decision 'D5': '<S27>:1:59' */
            if (eml_b && eml_aVarTruthTableCondition_1 &&
                (!eml_aVarTruthTableCondition_2) &&
                (!eml_aVarTruthTableCondition_5)) {
              /* Condition '#3': '<S27>:1:24' */
              /* Decision 'D5': '<S27>:1:61' */
              /*  Unexpected fault */
              rtb_vtrec_13 = 3U;

              /* Action '3': '<S27>:1:114' */
              eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

              /* Action '3': '<S27>:1:114' */
              eml_ptfaultholeman = NO_FAULT;

              /* Action '3': '<S27>:1:115' */
              FlgUnexpPresFault = 1U;

              /* Action '3': '<S27>:1:116' */

              /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
               *  Constant: '<S29>/DIAG_PRES_NOT_COH'
               */
              DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                   AirDiagMgm_B.ptfaultsenspres,
                                   &AirDiagMgm_B.stdiagpresfun);

              /* Action '3': '<S27>:1:116' */

              /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
               *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
               */
              DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                   AirDiagMgm_B.ptfaultholeman,
                                   &AirDiagMgm_B.stdiagholeman);
            } else {
              /* Decision 'D6': '<S27>:1:61' */
              if (eml_b && eml_aVarTruthTableCondition_1 &&
                  (!eml_aVarTruthTableCondition_2) &&
                  eml_aVarTruthTableCondition_5 && eml_aVarTruthTableCondition_6
                  && (!eml_aVarTruthTableCondition_7)) {
                /* Condition '#3': '<S27>:1:24' */
                /* Decision 'D6': '<S27>:1:63' */
                /*  Unexpected fault */
                rtb_vtrec_13 = 3U;

                /* Action '3': '<S27>:1:114' */
                eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                /* Action '3': '<S27>:1:114' */
                eml_ptfaultholeman = NO_FAULT;

                /* Action '3': '<S27>:1:115' */
                FlgUnexpPresFault = 1U;

                /* Action '3': '<S27>:1:116' */

                /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                 *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                 */
                DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                     AirDiagMgm_B.ptfaultsenspres,
                                     &AirDiagMgm_B.stdiagpresfun);

                /* Action '3': '<S27>:1:116' */

                /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                 *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                 */
                DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                     AirDiagMgm_B.ptfaultholeman,
                                     &AirDiagMgm_B.stdiagholeman);
              } else {
                /* Decision 'D7': '<S27>:1:63' */
                if (eml_b && eml_aVarTruthTableCondition_1 &&
                    (!eml_aVarTruthTableCondition_2) &&
                    eml_aVarTruthTableCondition_5 &&
                    (!eml_aVarTruthTableCondition_6) &&
                    eml_aVarTruthTableCondition_7) {
                  /* Condition '#3': '<S27>:1:24' */
                  /* Decision 'D7': '<S27>:1:65' */
                  /*  Faulty pressure sensor */
                  rtb_vtrec_13 = 4U;

                  /* Action '4': '<S27>:1:123' */
                  eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                  /* Action '4': '<S27>:1:123' */
                  eml_ptfaultholeman = NO_FAULT;

                  /* Action '4': '<S27>:1:124' */
                  FlgUnexpPresFault = 0U;

                  /* Action '4': '<S27>:1:125' */

                  /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                   *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                   */
                  DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                       AirDiagMgm_B.ptfaultsenspres,
                                       &AirDiagMgm_B.stdiagpresfun);

                  /* Action '4': '<S27>:1:125' */

                  /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                   *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                   */
                  DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                       AirDiagMgm_B.ptfaultholeman,
                                       &AirDiagMgm_B.stdiagholeman);
                } else {
                  /* Decision 'D8': '<S27>:1:65' */
                  if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                      eml_aVarTruthTableCondition_2 &&
                      eml_aVarTruthTableCondition_3 &&
                      eml_aVarTruthTableCondition_5 &&
                      (!eml_aVarTruthTableCondition_6) &&
                      (!eml_aVarTruthTableCondition_7)) {
                    /* Condition '#3': '<S27>:1:24' */
                    /* Decision 'D8': '<S27>:1:67' */
                    /*  Faulty pressure sensor */
                    rtb_vtrec_13 = 4U;

                    /* Action '4': '<S27>:1:123' */
                    eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                    /* Action '4': '<S27>:1:123' */
                    eml_ptfaultholeman = NO_FAULT;

                    /* Action '4': '<S27>:1:124' */
                    FlgUnexpPresFault = 0U;

                    /* Action '4': '<S27>:1:125' */

                    /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                     *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                     */
                    DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                         AirDiagMgm_B.ptfaultsenspres,
                                         &AirDiagMgm_B.stdiagpresfun);

                    /* Action '4': '<S27>:1:125' */

                    /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                     *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                     */
                    DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                         AirDiagMgm_B.ptfaultholeman,
                                         &AirDiagMgm_B.stdiagholeman);
                  } else {
                    /* Decision 'D9': '<S27>:1:67' */
                    if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                        eml_aVarTruthTableCondition_2 &&
                        (!eml_aVarTruthTableCondition_3) &&
                        eml_aVarTruthTableCondition_5 &&
                        (!eml_aVarTruthTableCondition_6) &&
                        (!eml_aVarTruthTableCondition_7) &&
                        (!eml_aVarTruthTableCondition_8) &&
                        (!eml_aVarTruthTableCondition_9)) {
                      /* Condition '#3': '<S27>:1:24' */
                      /* Decision 'D9': '<S27>:1:69' */
                      /*  Pressure too high (ex.: hole) */
                      rtb_vtrec_13 = 5U;

                      /* Action '5': '<S27>:1:132' */
                      eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                      /* Action '5': '<S27>:1:132' */
                      eml_ptfaultholeman = SIG_NOT_PLAUSIBLE;

                      /* Action '5': '<S27>:1:133' */
                      FlgUnexpPresFault = 0U;

                      /* Action '5': '<S27>:1:134' */

                      /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                       *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                       */
                      DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                           AirDiagMgm_B.ptfaultsenspres,
                                           &AirDiagMgm_B.stdiagpresfun);

                      /* Action '5': '<S27>:1:134' */

                      /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                       *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                       */
                      DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                           AirDiagMgm_B.ptfaultholeman,
                                           &AirDiagMgm_B.stdiagholeman);
                    } else {
                      /* Decision 'D10': '<S27>:1:69' */
                      if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                          eml_aVarTruthTableCondition_2 &&
                          eml_aVarTruthTableCondition_4 &&
                          (!eml_aVarTruthTableCondition_5)) {
                        /* Condition '#3': '<S27>:1:24' */
                        /* Decision 'D10': '<S27>:1:71' */
                        /*  No pressure fault */
                        rtb_vtrec_13 = 2U;

                        /* Action '2': '<S27>:1:107' */
                        eml_ptfaultsenspres = NO_FAULT;

                        /* Action '2': '<S27>:1:107' */
                        eml_ptfaultholeman = NO_FAULT;
                      } else {
                        /* Decision 'D11': '<S27>:1:71' */
                        if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                            eml_aVarTruthTableCondition_2 &&
                            (!eml_aVarTruthTableCondition_4) &&
                            (!eml_aVarTruthTableCondition_5) &&
                            (!eml_aVarTruthTableCondition_8)) {
                          /* Condition '#3': '<S27>:1:24' */
                          /* Decision 'D11': '<S27>:1:73' */
                          /*  Pressure too high (ex.: hole) */
                          rtb_vtrec_13 = 6U;

                          /* Action '6': '<S27>:1:141' */
                          eml_ptfaultsenspres = NO_FAULT;

                          /* Action '6': '<S27>:1:141' */
                          eml_ptfaultholeman = SIG_NOT_PLAUSIBLE;

                          /* Action '6': '<S27>:1:142' */
                          FlgUnexpPresFault = 0U;

                          /* Action '6': '<S27>:1:143' */

                          /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                           *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                           */
                          DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                               AirDiagMgm_B.ptfaultholeman,
                                               &AirDiagMgm_B.stdiagholeman);
                        } else {
                          /* Decision 'D12': '<S27>:1:73' */
                          if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                              eml_aVarTruthTableCondition_2 &&
                              eml_aVarTruthTableCondition_5 &&
                              (!eml_aVarTruthTableCondition_6) &&
                              (!eml_aVarTruthTableCondition_7) &&
                              eml_aVarTruthTableCondition_8 &&
                              (!eml_aVarTruthTableCondition_9)) {
                            /* Condition '#3': '<S27>:1:24' */
                            /* Decision 'D12': '<S27>:1:75' */
                            /*  Unexpected fault */
                            rtb_vtrec_13 = 3U;

                            /* Action '3': '<S27>:1:114' */
                            eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                            /* Action '3': '<S27>:1:114' */
                            eml_ptfaultholeman = NO_FAULT;

                            /* Action '3': '<S27>:1:115' */
                            FlgUnexpPresFault = 1U;

                            /* Action '3': '<S27>:1:116' */

                            /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                             *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                             */
                            DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                                 AirDiagMgm_B.ptfaultsenspres,
                                                 &AirDiagMgm_B.stdiagpresfun);

                            /* Action '3': '<S27>:1:116' */

                            /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                             *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                             */
                            DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                                 AirDiagMgm_B.ptfaultholeman,
                                                 &AirDiagMgm_B.stdiagholeman);
                          } else {
                            /* Decision 'D13': '<S27>:1:75' */
                            if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                                eml_aVarTruthTableCondition_2 &&
                                (!eml_aVarTruthTableCondition_4) &&
                                (!eml_aVarTruthTableCondition_5) &&
                                eml_aVarTruthTableCondition_8 &&
                                (!eml_aVarTruthTableCondition_9)) {
                              /* Condition '#3': '<S27>:1:24' */
                              /* Decision 'D13': '<S27>:1:77' */
                              /*  Unexpected fault */
                              rtb_vtrec_13 = 3U;

                              /* Action '3': '<S27>:1:114' */
                              eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                              /* Action '3': '<S27>:1:114' */
                              eml_ptfaultholeman = NO_FAULT;

                              /* Action '3': '<S27>:1:115' */
                              FlgUnexpPresFault = 1U;

                              /* Action '3': '<S27>:1:116' */

                              /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                               *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                               */
                              DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                                   AirDiagMgm_B.ptfaultsenspres,
                                                   &AirDiagMgm_B.stdiagpresfun);

                              /* Action '3': '<S27>:1:116' */

                              /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                               *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                               */
                              DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                                   AirDiagMgm_B.ptfaultholeman,
                                                   &AirDiagMgm_B.stdiagholeman);
                            } else {
                              /* Decision 'D14': '<S27>:1:77' */
                              if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                                  eml_aVarTruthTableCondition_2 &&
                                  (!eml_aVarTruthTableCondition_3) &&
                                  eml_aVarTruthTableCondition_5 &&
                                  (!eml_aVarTruthTableCondition_6) &&
                                  (!eml_aVarTruthTableCondition_7) &&
                                  (!eml_aVarTruthTableCondition_8) &&
                                  eml_aVarTruthTableCondition_9) {
                                /* Condition '#3': '<S27>:1:24' */
                                /* Decision 'D14': '<S27>:1:79' */
                                /*  Pressure too high (ex.: hole) */
                                rtb_vtrec_13 = 6U;

                                /* Action '6': '<S27>:1:141' */
                                eml_ptfaultsenspres = NO_FAULT;

                                /* Action '6': '<S27>:1:141' */
                                eml_ptfaultholeman = SIG_NOT_PLAUSIBLE;

                                /* Action '6': '<S27>:1:142' */
                                FlgUnexpPresFault = 0U;

                                /* Action '6': '<S27>:1:143' */

                                /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                                 *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                                 */
                                DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                                     AirDiagMgm_B.ptfaultholeman,
                                                     &AirDiagMgm_B.stdiagholeman);
                              } else {
                                /* Decision 'D15': '<S27>:1:79' */
                                if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                                    eml_aVarTruthTableCondition_2 &&
                                    eml_aVarTruthTableCondition_3 &&
                                    eml_aVarTruthTableCondition_5 &&
                                    eml_aVarTruthTableCondition_6 &&
                                    (!eml_aVarTruthTableCondition_7)) {
                                  /* Condition '#3': '<S27>:1:24' */
                                  /* Decision 'D15': '<S27>:1:81' */
                                  /*  Unexpected fault */
                                  rtb_vtrec_13 = 3U;

                                  /* Action '3': '<S27>:1:114' */
                                  eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                                  /* Action '3': '<S27>:1:114' */
                                  eml_ptfaultholeman = NO_FAULT;

                                  /* Action '3': '<S27>:1:115' */
                                  FlgUnexpPresFault = 1U;

                                  /* Action '3': '<S27>:1:116' */

                                  /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                                   *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                                   */
                                  DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                                       AirDiagMgm_B.ptfaultsenspres,
                                                       &AirDiagMgm_B.stdiagpresfun);

                                  /* Action '3': '<S27>:1:116' */

                                  /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                                   *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                                   */
                                  DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                                       AirDiagMgm_B.ptfaultholeman,
                                                       &AirDiagMgm_B.stdiagholeman);
                                } else {
                                  /* Decision 'D16': '<S27>:1:81' */
                                  if (eml_b && (!eml_aVarTruthTableCondition_1) &&
                                      eml_aVarTruthTableCondition_2 &&
                                      (!eml_aVarTruthTableCondition_3) &&
                                      eml_aVarTruthTableCondition_5 &&
                                      eml_aVarTruthTableCondition_6 &&
                                      (!eml_aVarTruthTableCondition_7)) {
                                    /* Condition '#3': '<S27>:1:24' */
                                    /* Decision 'D16': '<S27>:1:83' */
                                    /*  Faulty pressure sensor */
                                    rtb_vtrec_13 = 4U;

                                    /* Action '4': '<S27>:1:123' */
                                    eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                                    /* Action '4': '<S27>:1:123' */
                                    eml_ptfaultholeman = NO_FAULT;

                                    /* Action '4': '<S27>:1:124' */
                                    FlgUnexpPresFault = 0U;

                                    /* Action '4': '<S27>:1:125' */

                                    /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                                     *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                                     */
                                    DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                                         AirDiagMgm_B.ptfaultsenspres,
                                                         &AirDiagMgm_B.stdiagpresfun);

                                    /* Action '4': '<S27>:1:125' */

                                    /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                                     *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                                     */
                                    DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                                         AirDiagMgm_B.ptfaultholeman,
                                                         &AirDiagMgm_B.stdiagholeman);
                                  } else {
                                    /* Decision 'D17': '<S27>:1:83' */
                                    if (eml_b && (!eml_aVarTruthTableCondition_1)
                                        && eml_aVarTruthTableCondition_2 &&
                                        eml_aVarTruthTableCondition_3 &&
                                        eml_aVarTruthTableCondition_5 &&
                                        (!eml_aVarTruthTableCondition_6) &&
                                        eml_aVarTruthTableCondition_7) {
                                      /* Condition '#3': '<S27>:1:24' */
                                      /* Decision 'D17': '<S27>:1:85' */
                                      /*  Pressure too high (ex.: hole) */
                                      rtb_vtrec_13 = 6U;

                                      /* Action '6': '<S27>:1:141' */
                                      eml_ptfaultsenspres = NO_FAULT;

                                      /* Action '6': '<S27>:1:141' */
                                      eml_ptfaultholeman = SIG_NOT_PLAUSIBLE;

                                      /* Action '6': '<S27>:1:142' */
                                      FlgUnexpPresFault = 0U;

                                      /* Action '6': '<S27>:1:143' */

                                      /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                                       *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                                       */
                                      DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                                           AirDiagMgm_B.ptfaultholeman,
                                                           &AirDiagMgm_B.stdiagholeman);
                                    } else {
                                      /* Decision 'D18': '<S27>:1:85' */
                                      if (eml_b &&
                                          (!eml_aVarTruthTableCondition_1) &&
                                          eml_aVarTruthTableCondition_2 &&
                                          (!eml_aVarTruthTableCondition_3) &&
                                          eml_aVarTruthTableCondition_5 &&
                                          (!eml_aVarTruthTableCondition_6) &&
                                          eml_aVarTruthTableCondition_7) {
                                        /* Condition '#3': '<S27>:1:24' */
                                        /* Decision 'D18': '<S27>:1:87' */
                                        /*  Unexpected fault */
                                        rtb_vtrec_13 = 3U;

                                        /* Action '3': '<S27>:1:114' */
                                        eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                                        /* Action '3': '<S27>:1:114' */
                                        eml_ptfaultholeman = NO_FAULT;

                                        /* Action '3': '<S27>:1:115' */
                                        FlgUnexpPresFault = 1U;

                                        /* Action '3': '<S27>:1:116' */

                                        /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                                         *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                                         */
                                        DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                          AirDiagMgm_B.ptfaultsenspres,
                                          &AirDiagMgm_B.stdiagpresfun);

                                        /* Action '3': '<S27>:1:116' */

                                        /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                                         *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                                         */
                                        DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                          AirDiagMgm_B.ptfaultholeman,
                                          &AirDiagMgm_B.stdiagholeman);
                                      } else {
                                        /* Decision 'D19': '<S27>:1:87' */
                                        /* Decision 'D19': '<S27>:1:89' */
                                        /*  Default */
                                        /*  Unexpected fault */
                                        rtb_vtrec_13 = 3U;

                                        /* Action '3': '<S27>:1:114' */
                                        eml_ptfaultsenspres = SIG_NOT_PLAUSIBLE;

                                        /* Action '3': '<S27>:1:114' */
                                        eml_ptfaultholeman = NO_FAULT;

                                        /* Action '3': '<S27>:1:115' */
                                        FlgUnexpPresFault = 1U;

                                        /* Action '3': '<S27>:1:116' */

                                        /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState1' incorporates:
                                         *  Constant: '<S29>/DIAG_PRES_NOT_COH'
                                         */
                                        DiagMgm_SetDiagState( DIAG_PRES_NOT_COH,
                                          AirDiagMgm_B.ptfaultsenspres,
                                          &AirDiagMgm_B.stdiagpresfun);

                                        /* Action '3': '<S27>:1:116' */

                                        /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState1' incorporates:
                                         *  Constant: '<S28>/DIAG_HOLE_MANIFOLD'
                                         */
                                        DiagMgm_SetDiagState( DIAG_HOLE_MANIFOLD,
                                          AirDiagMgm_B.ptfaultholeman,
                                          &AirDiagMgm_B.stdiagholeman);
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      AirDiagMgm_B.ptfaultsenspres = eml_ptfaultsenspres;
      AirDiagMgm_B.ptfaultholeman = eml_ptfaultholeman;
      StChoiceAirDiag = rtb_vtrec_13;

      /* Update for Memory: '<S20>/Memory' */
      AirDiagMgm_DWork.Memory_PreviousInput_gyuc = state;

      /* Update for Memory: '<S34>/Memory1' */
      AirDiagMgm_DWork.Memory1_PreviousInput = rtb_SteadyStateDetect_o3;

      /* Update for Memory: '<S34>/Memory' */
      AirDiagMgm_DWork.Memory_PreviousInput = rtb_SteadyStateDetect_o4;

      /* Update for Memory: '<S15>/Memory' incorporates:
       *  Constant: '<S19>/FAULT'
       *  Logic: '<S19>/Logical Operator'
       *  RelationalOperator: '<S19>/Relational Operator'
       *  RelationalOperator: '<S19>/Relational Operator1'
       */
      AirDiagMgm_DWork.Memory_PreviousInput_nyam = ((AirDiagMgm_B.stdiagpresfun ==
        FAULT) || (FAULT == AirDiagMgm_B.stdiagholeman));
    } else {
      if (AirDiagMgm_DWork.Enabled_MODE == SUBSYS_ENABLED) {
        AirDiagMgm_DWork.Enabled_MODE = SUBSYS_DISABLED;
      }
    }

    /* end of Outputs for SubSystem: '<S9>/Enabled' */
  }
}

/* Model step function */
void AirDiagMgm_step(void)
{
  {
    uint8_T tmp[2];
    boolean_T tmp_0;
    int32_T tmp_1;

    /* Outputs for trigger SubSystem: '<S1>/fc_AirDiagMgm_Calc' incorporates:
     *  Inport: '<Root>/ev_TDC'
     *  TriggerPort: '<S10>/Trigger'
     */
    if ((AirDiagMgm_U.ev_TDC > 0) &&
        (AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Calc_Trig_ZCE != POS_ZCSIG)) {
      /* S-Function (fcncallgen): '<S10>/Function-Call Generator' incorporates:
       *  SubSystem: '<S1>/TDC'
       */
      AirDiagMgm_TDC();
    }

    AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Calc_Trig_ZCE = AirDiagMgm_U.ev_TDC >
      0 ? POS_ZCSIG : ZERO_ZCSIG;

    /* end of Outputs for SubSystem: '<S1>/fc_AirDiagMgm_Calc' */

    /* Outputs for trigger SubSystem: '<S1>/fc_AirDiagMgm_Reset' incorporates:
     *  Inport: '<Root>/ev_NoSync'
     *  Inport: '<Root>/ev_PowerOn'
     *  TriggerPort: '<S11>/Trigger'
     */
    tmp[0] = AirDiagMgm_U.ev_PowerOn;
    tmp[1] = AirDiagMgm_U.ev_NoSync;
    tmp_0 = false;
    for (tmp_1 = 0; tmp_1 < 2; tmp_1++) {
      tmp_0 = (tmp_0 || ((tmp[tmp_1] > 0) &&
                         (AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Reset_Trig_ZCE[
                          tmp_1] != POS_ZCSIG)));
    }

    if (tmp_0) {
      /* S-Function (fcncallgen): '<S11>/Function-Call Generator' incorporates:
       *  SubSystem: '<S1>/Initialization'
       */
      AirDiagMgm_Init();
    }

    AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Reset_Trig_ZCE[0] =
      AirDiagMgm_U.ev_PowerOn > 0 ? POS_ZCSIG : ZERO_ZCSIG;
    AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Reset_Trig_ZCE[1] =
      AirDiagMgm_U.ev_NoSync > 0 ? POS_ZCSIG : ZERO_ZCSIG;

    /* end of Outputs for SubSystem: '<S1>/fc_AirDiagMgm_Reset' */
  }
}

/* Model initialize function */
void AirDiagMgm_initialize(void)
{
  /* Start for trigger SubSystem: '<S1>/fc_AirDiagMgm_Calc' */

  /* Start for S-Function (fcncallgen): '<S10>/Function-Call Generator' incorporates:
   *  Start for SubSystem: '<S1>/TDC'
   */
  AirDiagMgm_TDC_Start();

  /* end of Start for SubSystem: '<S1>/fc_AirDiagMgm_Calc' */
  {
    int_T idx;
    for (idx = 0; idx < 2; idx ++) {
      AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Reset_Trig_ZCE[idx] = POS_ZCSIG;
    }
  }

  AirDiagMgm_PrevZCSigState.fc_AirDiagMgm_Calc_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T FlgPresCoh1;
uint8_T FlgPresCoh2;
void AirDiagMgm_stub(void);
void AirDiagMgm_Init(void)
{
  AirDiagMgm_stub();
}

void AirDiagMgm_NoSync(void)
{
  AirDiagMgm_stub();
}

void AirDiagMgm_TDC(void)
{
  AirDiagMgm_stub();
}

void AirDiagMgm_stub(void)
{
  FlgPresCoh1 = 1U;                    // 2^0
  FlgPresCoh2 = 1U;                    // 2^0
}

#endif

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
