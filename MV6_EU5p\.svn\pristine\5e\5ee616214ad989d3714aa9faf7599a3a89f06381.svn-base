/*
 * File: IonMisf.c
 *
 * Real-Time Workshop code generated for Simulink model IonMisf.
 *
 * Model version                        : 1.613
 * Real-Time Workshop file version      : 7.0  (R2007b)  02-Aug-2007
 * Real-Time Workshop file generated on : Thu Feb 28 11:29:28 2008
 * TLC version                          : 7.0 (Jul 26 2007)
 * C source code generated on           : Thu Feb 28 11:29:29 2008
 */

#include "..\include\IonMisf.h"
#include "..\include\IonMisf_private.h"

/* Named constants for block: '<S8>/State_Mgm' */
#define IonMisf_IN_MisfDetection       (1)

/* Named constants for block: '<S1>/Control_flow' */
#define IonMisf_event_ev_PowerOn       (0U)
#define IonMisf_event_ev_EOA           (4U)
#define IonMisf_event_ev_NoSync        (2U)
#define IonMisf_IN_NO_ACTIVE_CHILD     (0)
#define IonMisf_IN_Disable             (1)
#define IonMisf_IN_Enable              (2)

/* user code (top of source file) */
/* System '<Root>/IonMisf' */
#ifdef _BUILD_IONMISF_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

uint8_T LookIndideTheCode = 0; /* LL: HAZARD Look to the body */
uint16_T TbMisfTht;


uint8_T _sfEvent_IonMisf_;

/* Exported block signals */
uint16_T MisfThr;                      /* '<S8>/Product3'
                                        * Total misfire threshold
                                        */
uint16_T BadCombInt;                   /* '<S8>/Product'
                                        * Bad Combustion ThInt Threshold
                                        */
uint16_T ParMisfInt;                   /* '<S8>/Product1'
                                        * Partial Misfire ThInt Threshold
                                        */
uint16_T MisfInt;                      /* '<S8>/Product2'
                                        * Total Misfire ThInt Threshold
                                        */

/* Exported block states */
uint16_T MisfAbsCnt[8];                /* '<Root>/_DataStoreBlk_3'
                                        * Total Misfire Cylinder Counter
                                        */
uint16_T ParMisfAbsCnt[8];             /* '<Root>/_DataStoreBlk_4'
                                        * Partial Misfire Cylinder Counter
                                        */
uint16_T BadCombAbsCnt[8];             /* '<Root>/_DataStoreBlk_5'
                                        * Bad Combustion Cylinder Counter
                                        */

/* Block states (auto storage) */
D_Work_IonMisf IonMisf_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_IonMisf IonMisf_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_IonMisf IonMisf_U;
uint16_T FaultCnt[8];                  /* Cylinder Misfire Counter Vector */
uint8_T StMisf[8];                     /* Cylinder Misfire Status Vector */
static void IonMisf_chartstep_c7_IonMisf(void);

/* Output and update for function-call system: '<S1>/Reset_Variables' */
void IonMisf_Reset_Variables(void)
{
  /* Stateflow: '<S7>/Init' */
  {
    uint8_T sf_i;
    for (sf_i = 0U; sf_i < N_CYLINDER; sf_i++) {
      StMisf[(int32_T)sf_i] = NO_MISF;
      FaultCnt[(int32_T)sf_i] = 0U;
      MisfAbsCnt[(int32_T)sf_i] = 0U;
      ParMisfAbsCnt[(int32_T)sf_i] = 0U;
      BadCombAbsCnt[(int32_T)sf_i] = 0U;
    }
  }
}

/* Initial conditions for function-call system: '<S1>/fcn_EOA' */
void IonMisf_fcn_EOA_Init(void)
{
  /* Initialize code for chart: '<S8>/State_Mgm' */
  IonMisf_DWork.is_active_c1_IonMisf = 0U;
  IonMisf_DWork.is_c1_IonMisf = 0U;
}

/* Output and update for function-call system: '<S1>/fcn_EOA' */
void IonMisf_fcn_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_U8_U16_U16;
  uint16_T rtb_Look2D_U16_S16_S16;
  uint16_T gain_LookUp_U16_S16;

  {
    uint32_T tmp;
    uint16_T tmp_0;

    /* S-Function (Look2D_U8_U16_U16): '<S15>/Look2D_U8_U16_U16' incorporates:
     *  Constant: '<S8>/TBMISFTHR'
     *   Inport: '<Root>/Load'
     *  Constant: '<S8>/BKMISFLOAD'
     *  Constant: '<S11>/BKMISFLOAD_dim'
     *   Inport: '<Root>/Rpm'
     *  Constant: '<S8>/BKMISFRPM'
     *  Constant: '<S11>/BKMISFRPM_dim'
     */
    Look2D_U16_U16_U16( &rtb_Look2D_U8_U16_U16, &TBMISFTHR[0], Load, &BKMISFLOAD
                      [0], BKMISFLOAD_dim, Rpm, &BKMISFRPM[0], BKMISFRPM_dim);

    /* LL Insert embedded Code, remenber to Modelling this part */
    TbMisfTht = rtb_Look2D_U8_U16_U16;
    LookUp_U16_S16(&gain_LookUp_U16_S16, &VTMISFTHRDEFF[0], DEffSAPerc, &BKMISFTHRDEFF[0], 4);

    tmp = (uint32_T)rtb_Look2D_U8_U16_U16 * (uint32_T)gain_LookUp_U16_S16 >>
      11;
    if (tmp > 65535U) {
      rtb_Look2D_U8_U16_U16 = MAX_uint16_T;
    } else {
      rtb_Look2D_U8_U16_U16 = (uint16_T)tmp;
    }
    LookIndideTheCode = 0; /* Remenbre this part of code */
    /* LL End embedded Code, remenber to Modelling this part */

    /* S-Function (Look2D_U16_S16_S16): '<S13>/Look2D_U16_S16_S16' incorporates:
     *  Constant: '<S8>/TBCORRTHRMISF'
     *   Inport: '<Root>/TWater'
     *  Constant: '<S8>/BKTWATIONMISF'
     *  Constant: '<S10>/BKTWATIONMISF_dim'
     *   Inport: '<Root>/TAir'
     *  Constant: '<S8>/BKTAIRIONMISF'
     *  Constant: '<S10>/BKTAIRIONMISF_dim'
     */
    Look2D_U16_S16_S16( &rtb_Look2D_U16_S16_S16, &TBCORRTHRMISF[0], TWater,
                       &BKTWATIONMISF[0], BKTWATIONMISF_dim, TAir,
                       &BKTAIRIONMISF[0], BKTAIRIONMISF_dim);

    /* Product: '<S8>/Product3' */
    tmp = (uint32_T)rtb_Look2D_U8_U16_U16 * (uint32_T)rtb_Look2D_U16_S16_S16 >>
      11;
    if (tmp > 65535U) {
      tmp_0 = MAX_uint16_T;
    } else {
      tmp_0 = (uint16_T)tmp;
    }

    MisfThr = tmp_0;

    /* Product: '<S8>/Product' incorporates:
     *  Constant: '<S8>/BADCOMBPER'
     */
    BadCombInt = (uint16_T)((uint32_T)MisfThr * (uint32_T)BADCOMBPER >> 10);

    /* Product: '<S8>/Product1' incorporates:
     *  Constant: '<S8>/PARMISFPER'
     */
    ParMisfInt = (uint16_T)((uint32_T)MisfThr * (uint32_T)PARMISFPER >> 10);

    /* Product: '<S8>/Product2' incorporates:
     *  Constant: '<S8>/MISFPER'
     */
    MisfInt = (uint16_T)((uint32_T)MisfThr * (uint32_T)MISFPER >> 10);

    /* Stateflow: '<S8>/State_Mgm' incorporates:
     *   Inport: '<Root>/IonAbsTdc'
     *   Inport: '<Root>/CutoffFlg'
     *   Inport: '<Root>/IntIon'
     */
    {
      boolean_T sf_guard1 = false;
      if (IonMisf_DWork.is_active_c1_IonMisf == 0) {
        IonMisf_DWork.is_active_c1_IonMisf = 1U;
        IonMisf_DWork.is_c1_IonMisf = (uint8_T)IonMisf_IN_MisfDetection;
      } else {
        sf_guard1 = false;
        if (MISFRESET == 1) {
          BadCombAbsCnt[(int32_T)IonAbsTdc] = 0U;
          ParMisfAbsCnt[(int32_T)IonAbsTdc] = 0U;
          MisfAbsCnt[(int32_T)IonAbsTdc] = 0U;
        } else if (CutoffFlg == 0) {
          if (IntIon[(int32_T)IonAbsTdc] <= MisfInt) {
            StMisf[(int32_T)IonAbsTdc] = MISF;
            MisfAbsCnt[(int32_T)IonAbsTdc] = (uint16_T)(MisfAbsCnt[IonAbsTdc] +
              1);
            FaultCnt[(int32_T)IonAbsTdc] = (uint16_T)(FaultCnt[IonAbsTdc] +
              MISF_INC);
            sf_guard1 = true;
          } else if ((IntIon[(int32_T)IonAbsTdc] <= ParMisfInt) && (IntIon
                      [(int32_T)IonAbsTdc] > MisfInt)) {
            StMisf[(int32_T)IonAbsTdc] = PAR_MISF;
            ParMisfAbsCnt[(int32_T)IonAbsTdc] = (uint16_T)
              (ParMisfAbsCnt[IonAbsTdc] + 1);
            FaultCnt[(int32_T)IonAbsTdc] = (uint16_T)(FaultCnt[IonAbsTdc] +
              PAR_MISF_INC);
            sf_guard1 = true;
          } else if ((IntIon[(int32_T)IonAbsTdc] <= BadCombInt) && (IntIon
                      [(int32_T)IonAbsTdc] > ParMisfInt)) {
            StMisf[(int32_T)IonAbsTdc] = BAD_COMB;
            BadCombAbsCnt[(int32_T)IonAbsTdc] = (uint16_T)
              (BadCombAbsCnt[IonAbsTdc] + 1);
            FaultCnt[(int32_T)IonAbsTdc] = (uint16_T)(FaultCnt[IonAbsTdc] +
              BAD_COMB_INC);
            sf_guard1 = true;
          } else if (FaultCnt[IonAbsTdc] > 0) {
            FaultCnt[(int32_T)IonAbsTdc] = (uint16_T)(FaultCnt[IonAbsTdc] - 1);
          } else {
            StMisf[(int32_T)IonAbsTdc] = NO_MISF;
          }
        }
        else /* Toni - MISRA */
        {
        }

        if (sf_guard1 == true) {
          if (FaultCnt[IonAbsTdc] >= MAX_MISF) {
            FaultCnt[(int32_T)IonAbsTdc] = (uint16_T)MAX_MISF;
          }
        }
      }
    }
  }
}

/* Functions for block: '<S1>/Control_flow' */
static void IonMisf_chartstep_c7_IonMisf(void)
{
  if (IonMisf_DWork.is_active_c7_IonMisf == 0) {
    IonMisf_DWork.is_active_c7_IonMisf = 1U;

    /*  ev_PowerOn */
    IonMisf_Reset_Variables();
    IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Disable;
  } else {
    switch (IonMisf_DWork.is_c7_IonMisf) {
     case IonMisf_IN_Disable:
      /*  ev_EOA strategy enabled */
      if ((_sfEvent_IonMisf_ == IonMisf_event_ev_EOA) && (ENIONMISF != 0)) {
        IonMisf_fcn_EOA();
        IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Enable;
      } else {
        /*  ev_NoSync */
        if (_sfEvent_IonMisf_ == IonMisf_event_ev_NoSync) {
          IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_NO_ACTIVE_CHILD;
          IonMisf_Reset_Variables();
          IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Disable;
        }
      }
      break;

     case IonMisf_IN_Enable:
      /*  ev_NoSync */
      if (_sfEvent_IonMisf_ == IonMisf_event_ev_NoSync) {
        IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_NO_ACTIVE_CHILD;
        IonMisf_Reset_Variables();
        IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Disable;
      } else if (_sfEvent_IonMisf_ == IonMisf_event_ev_EOA) {
        /*  ev_EOA strategy disabled */
        if (!(ENIONMISF != 0)) {
          IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_NO_ACTIVE_CHILD;
          IonMisf_Reset_Variables();
          IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Disable;
        } else {
          IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_NO_ACTIVE_CHILD;

          /*  ev_EOA */
          IonMisf_fcn_EOA();
          IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Enable;
        }
      }
    else /* Toni - MISRA */
    {
    }
      break;

     default:
      /*  ev_PowerOn */
      IonMisf_Reset_Variables();
      IonMisf_DWork.is_c7_IonMisf = (uint8_T)IonMisf_IN_Disable;
      break;
    }
  }
}

/* Initial conditions for trigger system: '<S1>/Control_flow' */
void IonMisf_Control_flow_Init(void)
{
  /* Initialize code for chart: '<S1>/Control_flow' */
  IonMisf_DWork.is_active_c7_IonMisf = 0U;
  IonMisf_DWork.is_c7_IonMisf = 0U;
  IonMisf_fcn_EOA_Init();
}

/* Output and update for trigger system: '<S1>/Control_flow' */
void IonMisf_Control_flow(void)
{
  /* local block i/o variables */
  int8_T rtb_inputevents[3];

  {
    uint8_T cg_in[3];
    boolean_T zcEvent[3];
    int32_T i;
    boolean_T tmp;
    cg_in[0] = IonMisf_U.ev_PowerOn;
    cg_in[1] = IonMisf_U.ev_EOA;
    cg_in[2] = IonMisf_U.ev_NoSync;
    for (i = 0; i < 3; i++) {
      zcEvent[i] = ((cg_in[i] > 0) &&
                    (IonMisf_PrevZCSigState.Control_flow_Trig_ZCE[i] !=
                     POS_ZCSIG));
    }

    tmp = false;
    for (i = 0; i < 3; i++) {
      tmp = (tmp || zcEvent[i]);
    }

    if (tmp) {
      for (i = 0; i < 3; i++) {
        rtb_inputevents[i] = (int8_T)zcEvent[i];
      }

      /* Stateflow: '<S1>/Control_flow' */
      {
        uint8_T sf_previousEvent;
        if (rtb_inputevents[0] == 1) {
          sf_previousEvent = _sfEvent_IonMisf_;
          _sfEvent_IonMisf_ = IonMisf_event_ev_PowerOn;
          IonMisf_chartstep_c7_IonMisf();
          _sfEvent_IonMisf_ = sf_previousEvent;
        }

        if (rtb_inputevents[1] == 1) {
          sf_previousEvent = _sfEvent_IonMisf_;
          _sfEvent_IonMisf_ = IonMisf_event_ev_EOA;
          IonMisf_chartstep_c7_IonMisf();
          _sfEvent_IonMisf_ = sf_previousEvent;
        }

        if (rtb_inputevents[2] == 1) {
          sf_previousEvent = _sfEvent_IonMisf_;
          _sfEvent_IonMisf_ = IonMisf_event_ev_NoSync;
          IonMisf_chartstep_c7_IonMisf();
          _sfEvent_IonMisf_ = sf_previousEvent;
        }
      }
    }

    for (i = 0; i < 3; i++) {
      IonMisf_PrevZCSigState.Control_flow_Trig_ZCE[i] = cg_in[i] > 0 ? POS_ZCSIG
        : ZERO_ZCSIG;
    }
  }
}

/* Model step function */
void IonMisf_step(void)
{
  /* Outputs for atomic SubSystem: '<Root>/IonMisf' */
  IonMisf_Control_flow();

  /* end of Outputs for SubSystem: '<Root>/IonMisf' */
}

/* Model initialize function */
void IonMisf_initialize(void)
{
  /* Start for atomic SubSystem: '<Root>/IonMisf' */

  /* end of Start for SubSystem: '<Root>/IonMisf' */
  {
    int idx;
    for (idx = 0; idx < 3; idx ++) {
      IonMisf_PrevZCSigState.Control_flow_Trig_ZCE[idx] = POS_ZCSIG;
    }
  }

  /* Machine initializer */
  _sfEvent_IonMisf_ = CALL_EVENT;

  /* InitializeConditions for atomic SubSystem: '<Root>/IonMisf' */
  IonMisf_Control_flow_Init();

  /* end of InitializeConditions for SubSystem: '<Root>/IonMisf' */
}

/* user code (bottom of source file) */
/* System '<Root>/IonMisf' */
void IonMisf_Init(void)
{
  IonMisf_initialize();
  IonMisf_U.ev_PowerOn = 0;
  IonMisf_U.ev_EOA = 0;
  IonMisf_U.ev_NoSync = 0;
  IonMisf_step();
  IonMisf_U.ev_PowerOn = 1;
  IonMisf_U.ev_EOA = 0;
  IonMisf_U.ev_NoSync = 0;
  IonMisf_step();
}

void IonMisf_NoSync(void)
{
  IonMisf_U.ev_PowerOn = 0;
  IonMisf_U.ev_EOA = 0;
  IonMisf_U.ev_NoSync = 0;
  IonMisf_step();
  IonMisf_U.ev_PowerOn = 0;
  IonMisf_U.ev_EOA = 0;
  IonMisf_U.ev_NoSync = 1;
  IonMisf_step();
}

void IonMisf_EOA(void)
{
  IonMisf_U.ev_PowerOn = 0;
  IonMisf_U.ev_EOA = 0;
  IonMisf_U.ev_NoSync = 0;
  IonMisf_step();
  IonMisf_U.ev_PowerOn = 0;
  IonMisf_U.ev_EOA = 1;
  IonMisf_U.ev_NoSync = 0;
  IonMisf_step();
}

#else                                  // _BUILD_IonMisf_

void IonMisf_Init(void);
void IonMisf_NoSync(void);
void IonMisf_EOA(void);

//uscite
uint8_T StMisf[N_CYL_MAX];
void IonMisf_Init(void)
{
  uint8_T i;
  for (i=0;i<N_CYL_MAX;i++)
  {
    StMisf[i] = 0;
  }  
}

#endif                                 // _BUILD_IonMisf_

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
