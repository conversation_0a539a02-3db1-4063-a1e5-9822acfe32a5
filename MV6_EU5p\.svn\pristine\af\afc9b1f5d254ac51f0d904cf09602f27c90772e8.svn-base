#ifndef _RECOVERY_TEST_PRIVATE_H_
#define _RECOVERY_TEST_PRIVATE_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/

extern const uint32_T CENSORSHIPRECOVERY1;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

#endif /* _RECOVERY_TEST_PRIVATE_H_*/
