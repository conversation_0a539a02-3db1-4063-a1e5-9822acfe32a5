/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  _BUILD_SYNC_

#include <stddef.h>

#include "ETPU_Shared.h"
#include "ETPU_EngineDefs.h"
#include "ETPU_VrsDefs.h"

#include "etpu_util.h"

#include "..\..\sys\include\ETPU_HostInterface.h"

#include "..\sys\auto\etpu_angle_clock_func_auto.h"
#include "..\sys\auto\etpu_angle_clock_auto.h"
#include "..\sys\auto\etpu_angleExGenerator_auto.h"

#if (ENGINE_TYPE == PI_500_1C_4V) 
#include "..\sys\auto\etpu_piaggio_generated_image.h"
#elif (ENGINE_TYPE == FE_6300_12C_48V) 
#include "..\sys\auto\etpu_ferrari_generated_image.h"
#elif (ENGINE_TYPE == FE_4300_8C_32V)
#include "..\sys\auto\etpu_ferrari_v8_generated_image.h"
#elif (ENGINE_TYPE == FE_4300_8C_32V_TDN)
#include "..\sys\auto\etpu_ferrari_v8_tdn_generated_image.h"
#elif (ENGINE_TYPE == FE_4300_8C_32V_GT2)
#include "..\sys\auto\etpu_ferrari_v8_gt2_generated_image.h"    
#elif (ENGINE_TYPE == YP_250_G) 
#include "..\sys\auto\etpu_yamaha_generated_image.h"
#elif (ENGINE_TYPE == MA_MC12_12C) 
#include "..\sys\auto\etpu_maserati_generated_image.h"
#elif (ENGINE_TYPE == VW_1400_4C_16V)
#include "..\sys\auto\etpu_vw_generated_image.h"
#elif (ENGINE_TYPE == EM_VW_1400_4C_16V)
#include "..\sys\auto\etpu_em_4cyl_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_4C)
#include "..\sys\auto\etpu_em_4cyl_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
#include "..\sys\auto\etpu_em_3cyl_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
#include "..\sys\auto\etpu_em_3cyl_new_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
#include "..\sys\auto\etpu_em_3cyl_36_2_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_4C_TDC_0_9)
#include "..\sys\auto\etpu_em_4cyl_36_2_generated_image.h"
#elif (ENGINE_TYPE == PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID)
#include "..\sys\auto\etpu_piaggio_generated_image.h"
#else
#error WRONG CONFIGURATION!!!
#endif

#include "sync.h"
#include "sys.h"
#include "digio.h"

uint16_t syncInternalState;
uint8_t syncEnablingState;

/* 
  This function configure the eTPU registers.
  It also configure SPRAM memory and copy globals and functions parameters

*/
int16_t SYNC_Config(void)
{
  int16_t errorCode = NO_ERROR;

  syncEnablingState = SE_STATE_DISABLED;

#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      errorCode = PERIPHERAL_FAILURE;
  }
  else
#endif
  if((syncInternalState == SI_STATE_PRE_CONFIGURATION)) 
  {
    syncInternalState = SI_STATE_CONFIGURED;
  }
  else
  {
    errorCode = PERIPHERAL_ALREADY_CONFIGURED;
  }
  return (errorCode) ;
}


int16_t SYNC_Init(void)
{
  uint32_t ParameterBaseAddress;
  int16_t errorCode = NO_ERROR;
  uint32_t polarityTmp;

#ifdef CHECK_BIOS_FAULTS
  if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
  {
      errorCode = PERIPHERAL_FAILURE;
  }
#endif
  if (errorCode == NO_ERROR)
  {
      switch(syncInternalState)
      {
        case SI_STATE_PRE_CONFIGURATION:
            errorCode = PERIPHERAL_NOT_CONFIGURED;
        break;
        case SI_STATE_INITIALIZED:
            errorCode = PERIPHERAL_ALREADY_INITIALIZED;
        break;
        case SI_STATE_CONFIGURED:
          /* Initialize channel CRANKANGLE_CHANNEL */
    //      ParameterBaseAddress = ETPU_ChannelInitRam(CRANKANGLE_CHANNEL, ETPU_ANGLE_CLOCK_NUM_PARMS);
            errorCode = ETPU_ChannelInitRam(CRANKANGLE_CHANNEL, ETPU_ANGLE_CLOCK_NUM_PARMS, &ParameterBaseAddress );
            if (errorCode != NO_ERROR)
            {
#ifdef CHECK_BIOS_FAULTS        
              BIOS_Faults |= (BIOS_FAILURE_SYNC | BIOS_FAILURE_ETPU);
#endif /* CHECK_BIOS_FAULTS */
              errorCode = PERIPHERAL_FAILURE;
            }
            else
            {
            /* Initialize channel 0 to manage crank signal */
                errorCode = ETPU_ChannelInit(CRANKANGLE_CHANNEL, FS_ETPU_PRIORITY_HIGH, ETPU_COUNT_MODE_CONT, 
                                           ETPU_ANGLE_CLOCK_FUNCTION_NUMBER,
                                           ETPU_ANGLE_CLOCK_TABLE_SELECT, ParameterBaseAddress );

                if(errorCode == NO_ERROR)
                {
                    polarityTmp = read_angleClockChanFlags(CRANKANGLE_CHANNEL) & ~(POLARITY_MASK);
                    write_angleClockChanFlags(polarityTmp, CRANKANGLE_CHANNEL);

                    write_TeethPerRev          (TEETH_PER_REV);
                    write_NumberMissing        (NUMBER_MISSING);
                    write_TicksPerTooth        (TICKS_PER_TOOTH);
                    write_NumRevPerCycle       (N_REV_PER_CYCLE);

#if(ETPU_TBCR_TCR2CTL_TCRCLK_TRANS == ETPU_TBCR_TCR2CTL_TCRCLK_RISE_TRANS)
                    write_polarity(TRANS_LOW_HIGH, CRANKANGLE_CHANNEL);
#elif(ETPU_TBCR_TCR2CTL_TCRCLK_TRANS == ETPU_TBCR_TCR2CTL_TCRCLK_FALL_TRANS)
                    write_polarity(TRANS_HIGH_LOW, CRANKANGLE_CHANNEL);
#elif(ETPU_TBCR_TCR2CTL_TCRCLK_TRANS == ETPU_TBCR_TCR2CTL_TCRCLK_BOTH_TRANS)
                    write_polarity(TRANS_BOTH, CRANKANGLE_CHANNEL);
#else
#warning Wrong polarity!!
#endif
                    /* Initialize channel CRANKEXGEN_CHANNEL */
                    //      ParameterBaseAddress = ETPU_ChannelInitRam(CRANKEXGEN_CHANNEL, ANGLE_EX_GEN_NUM_PARMS);
                    errorCode = ETPU_ChannelInitRam(CRANKEXGEN_CHANNEL, ANGLE_EX_GEN_NUM_PARMS, &ParameterBaseAddress );
                    if(errorCode == NO_ERROR)
                    {
                        /* Initialize channel CRANKEXGEN_CHANNEL to generate programmed angle exceptions*/
                        errorCode = ETPU_ChannelInit(CRANKEXGEN_CHANNEL, FS_ETPU_PRIORITY_MIDDLE, ETPU_COUNT_MODE_SINGLE, 
                                                  ANGLE_EX_GEN_FUNC, ETPU_ANGLE_CLOCK_TABLE_SELECT, ParameterBaseAddress );

                        if(errorCode == NO_ERROR) 
                        {
                            polarityTmp = read_angleExGeneratorChanFlags(CRANKEXGEN_CHANNEL) & ~(POLARITY_MASK);
                            write_angleExGeneratorChanFlags(polarityTmp, CRANKEXGEN_CHANNEL);

                            ETPU_SetAngleModeBothETPU();
                        }
                    }
                }
          }
          break;
          default:
          break;
      } //end switch
      if (errorCode == NO_ERROR)
      {
         // change module internal state 
         syncInternalState = SI_STATE_INITIALIZED;
         ETPU_StartTCRx ();
      }
  }        
  return (errorCode) ;
}

int16_t SYNC_GetToothCycleCnt(uint8_t *toothNumber)
{
  int16_t errorCode = NO_ERROR;
  uint8_t CrankStatus;

#ifdef CHECK_BIOS_FAULTS
  if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
  {
      errorCode = PERIPHERAL_FAILURE;
  }
#endif
  if (errorCode == NO_ERROR)
  {
      CrankStatus = (uint8_t)read_CrankStatus();
       
      if((syncInternalState == SI_STATE_INITIALIZED) 
       & (syncEnablingState == SE_STATE_ENABLED))
      {
        if((CrankStatus==HALF_SYNC) || (CrankStatus==FULL_SYNC))
        {
            *toothNumber = (uint8_t)read_ToothCount();
        }
        else
        {
            *toothNumber = 0;
        }
      }
      else
      {
        *toothNumber = 0;
        errorCode = PERIPHERAL_NOT_INITIALIZED;
      }  
  }
  return (errorCode) ;
}

int16_t SYNC_GetSkippingEdges(uint8_t *SkippingEdges)
{
  int16_t errorCode = NO_ERROR;
  uint8_t Last_State;
   
#ifdef CHECK_BIOS_FAULTS
  if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
  {
     errorCode = PERIPHERAL_FAILURE;
  }
#endif
  if (errorCode == NO_ERROR)
  {
      if((syncInternalState == SI_STATE_INITIALIZED) 
       & (syncEnablingState == SE_STATE_ENABLED)) 
      { 
        Last_State = (uint8_t)read_Last_State();

        if( (Last_State == AS_FIRST_EDGE) ||
            (Last_State == AS_SKIPPING_EDGES))
        {
          *SkippingEdges = 1;
        }
        else
        {
          *SkippingEdges = 0;
        }
      }
      else
      {
        *SkippingEdges = 0;
        errorCode = PERIPHERAL_NOT_INITIALIZED;
      }  
  }
  return (errorCode) ;
}

int16_t SYNC_GetCURRENT_Angle(uint32_t *ticksPerDegree16 )
{
    int16_t errorCode = NO_ERROR;
    uint32_t tmp;
 #ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {   
        tmp = ETPU.TB2R_A.R;
        *ticksPerDegree16 = (TICKS2DEGREE16 * tmp);
    }
    return errorCode;
    
}

int16_t SYNC_GetCURRENT_Time(uint32_t *timetime )
{
    int16_t errorCode = NO_ERROR;
   
 #ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {   
       
        *timetime = ETPU.TB1R_A.R;
    }
    return errorCode;
    
}


int16_t SYNC_GetCrankAngle(uint16_t *ticksPerDegree16 )
{
    int16_t errorCode = NO_ERROR;
    uint8_t CrankStatus = (uint8_t)read_CrankStatus();
    uint32_t tmp;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if((syncEnablingState == SE_STATE_ENABLED))
        {
            if((CrankStatus==HALF_SYNC) || (CrankStatus==FULL_SYNC))
            {
#if USE_ETPU_A    
                tmp = ETPU.TB2R_A.R;
                *ticksPerDegree16 = (uint16_t)(TICKS2DEGREE16 * tmp);
#endif
            } 
            else
            {
#if USE_ETPU_A    
                *ticksPerDegree16 = 0;
#endif
            } 
        } 
        else
        {
            *ticksPerDegree16 = 0;
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        } 
    }
    return errorCode; 
}


int16_t SYNC_GetToothPeriod(uint32_t *toothPeriod)
{
    int16_t errorCode = NO_ERROR;
    uint8_t toothCount = read_ToothCount();
    uint8_t CrankStatus = (uint8_t)read_CrankStatus();

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if((syncEnablingState == SE_STATE_ENABLED)) 
        {
            *toothPeriod = read_ToothPeriod();
        }
        else
        {
            *toothPeriod = MAX_TOOTH_PERIOD;
            errorCode = SYNC_PERIPHERAL_NOT_ENABLED;
        }
    }
    return (errorCode); 
}


int16_t SYNC_GetEngineTime(uint32_t *EngineTime)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if((syncInternalState == SI_STATE_INITIALIZED)&
           (syncEnablingState == SE_STATE_ENABLED))
        {
            *EngineTime = (TCR1TICKS2US * read_captureRegB());
        }
        else
        {
            errorCode = SYNC_PERIPHERAL_NOT_ENABLED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetToothLevel(uint8_t *toothLevel)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if((syncInternalState == SI_STATE_INITIALIZED)& 
           (syncEnablingState == SE_STATE_ENABLED))
        {
            DIGIO_InGet(CTRCLK_CHANNEL_PIN,toothLevel);
        }
        else
        {
            errorCode = SYNC_PERIPHERAL_NOT_ENABLED;
        }
    }
    return (errorCode); 
}

int16_t  SYNC_SetResync(void)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if((syncInternalState == SI_STATE_INITIALIZED)& 
           (syncEnablingState == SE_STATE_ENABLED))
        {
#if USE_ETPU_A    
            errorCode = ETPU_set_hsr( CRANKANGLE_CHANNEL, HSR_RESYNCH_ANGLE_CLOCK_VAL);
#endif
        } 
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return errorCode;
}


int16_t SYNC_SetState(uint8_t state)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            if (state)
            {
                syncEnablingState = SE_STATE_ENABLED;
                errorCode = ETPU_set_hsr( CRANKANGLE_CHANNEL, HSR_INIT_ANGLE_CLOCK_VAL);

                if(errorCode == NO_ERROR)
                {
                    ETPU.CHAN[CRANKANGLE_CHANNEL].SCR.B.CIS = 1;
                    ETPU.CHAN[CRANKANGLE_CHANNEL].CR.B.CIE = 1;

                    ETPU.CHAN[CRANKEXGEN_CHANNEL].SCR.B.CIS = 1;
                    ETPU.CHAN[CRANKEXGEN_CHANNEL].CR.B.CIE = 1;
                }
            }
            else
            {
                syncEnablingState = SE_STATE_DISABLED;
                ETPU.CHAN[CRANKANGLE_CHANNEL].CR.B.CIE = 0;
                ETPU.CHAN[CRANKEXGEN_CHANNEL].CR.B.CIE = 0;
                errorCode = ETPU_set_hsr( CRANKANGLE_CHANNEL, HSR_SHUTDOWN_ANGLE_CLOCK_VAL);
                errorCode = ETPU_set_hsr( CRANKEXGEN_CHANNEL, HSR_DISABLE_ANGLE_EX_GENERATOR_VAL);
            }
        } 
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return errorCode;
}


int16_t SYNC_GetState(uint8_t *state)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
#if USE_ETPU_A    
            *state =  (uint8_t) ETPU.CHAN[CRANKANGLE_CHANNEL].CR.B.CIE;
#endif
        } 
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetSyncState(uint8_t *SyncState)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            uint8_t CrankStatus = (uint8_t)read_CrankStatus();
            switch (CrankStatus)
            {
                case WAITING_GAP:
                    *SyncState = WAIT_SYNCH;
                break;
                case HALF_SYNC:
                case FULL_SYNC:
                    *SyncState = SYNCH;
                break;
                default:
                    *SyncState = NO_SYNCH;
                break;
            }
        } 
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetSyncError(uint8_t *SyncError)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED)
        {    
            *SyncError = (uint8_t)read_CrankStatus();
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetNTeethDeleted(uint32_t *NTeethDeleted)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED)
        {    
            *NTeethDeleted = (uint32_t)read_FalseTeeth();
//            LastDelToothPeriod = (uint32_t)read_LastFalseToothPeriod(); /* toni */
//            LastDelToothLowTime = (uint32_t)read_LastFalseToothLowTime(); /* toni */
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetLastFalseTooth(uint8_t *LastFalseTooth)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED)
        {    
            *LastFalseTooth = (uint8_t)read_LastFalseTooth();
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetLastDelToothPeriod(uint32_t *period) /* toni */
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED)
        {    
            *period = (uint32_t)read_LastFalseToothPeriod();
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_GetLastDelToothLowTime(uint32_t *interval) /* toni */
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED)
        {    
            *interval = (uint32_t)read_LastFalseToothLowTime();
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t  SYNC_SetNextTooth(uint8_t NextTooth)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif        
    if (errorCode == NO_ERROR)
    {
        if((syncInternalState == SI_STATE_INITIALIZED)& 
           (syncEnablingState == SE_STATE_ENABLED)) 
        {
            write_exToothNum(NextTooth);
            write_angleClockChanFlags(EXCEPTIONS_ENABLED, CRANKANGLE_CHANNEL);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}


int16_t  SYNC_SetNextAngle(uint16_t NextAngle)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if((syncInternalState == SI_STATE_INITIALIZED)& 
           (syncEnablingState == SE_STATE_ENABLED))
        {
            write_exAngleNum((NextAngle / TCR2_TICK_PRESCALER));
            write_angleExGeneratorChanFlags(EXCEPTIONS_ENABLED, CRANKEXGEN_CHANNEL);
            errorCode = ETPU_set_hsr(CRANKEXGEN_CHANNEL, HSR_INIT_ANGLE_EX_GENERATOR_VAL);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetStartupBlankingPeriod(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_StartUpBlankingPeriod(parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetStartupSkippedTeeth(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_StartUpSkippedTeeth(parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioSyncLow(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioSyncLow(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}
 
int16_t SYNC_SetRatioSyncHigh(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioSyncHigh(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioToothLow(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioToothLow(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioToothHigh(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioToothHigh(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioHoleLow(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioHoleLow(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioHoleHigh(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioHoleHigh(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioFirstToothLow(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioFirstToothLow(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetRatioFirstToothHigh(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_RatioFirstToothHigh(0x00FFFFFF & parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetBlankingPeriod(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_BlankingPeriod(parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}

int16_t SYNC_SetStallPeriod(uint32_t parameter)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        if(syncInternalState == SI_STATE_INITIALIZED) 
        {
            write_StallPeriod(parameter);
        }
        else
        {
            errorCode = PERIPHERAL_NOT_INITIALIZED;
        }
    }
    return (errorCode); 
}


int16_t SYNC_SetRePhase(uint32_t toothCounter)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {

        write_toothCounter(toothCounter,CRANKANGLE_CHANNEL);

    }
    return ETPU_set_hsr(CRANKANGLE_CHANNEL,HSR_REPHASE_ANGLE_CLOCK_VAL);
}

int16_t SYNC_GetCrankStatus(uint8_t *status)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        *status = (uint8_t)read_CrankStatus();
    }
    return (errorCode); 
}

int16_t SYNC_GetLastState(uint8_t *status)
{
    int16_t errorCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        *status = (uint8_t)read_Last_State();
    }
    return (errorCode); 
}

#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08) || (ENGINE_TYPE == MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
int16_t SYNC_SetGainAngleClock(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain(gain);
    }
    return errorCode;
}

int16_t SYNC_SetTdcGainAngleClock(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_Tdc(gain);
    }
    return errorCode;
}

int16_t SYNC_SetTdcGain2AngleClock(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_m15d(gain);
    }
    return errorCode;
}

int16_t SYNC_SetGainTooth15d(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_15d(gain);
    }
    return errorCode;
}

int16_t SYNC_SetGainTooth30d(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_30d(gain);
    }
    return errorCode;
}

int16_t SYNC_EventDivider(uint32_t event_divider)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_event_divider(event_divider);
    }
    return errorCode;
}

#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
int16_t SYNC_SetGainHoleTdc(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain(gain);
    }
    return errorCode;
}

int16_t SYNC_SetGainToothAft2Tdc(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_m15d(gain);
    }
    return errorCode;
}

int16_t SYNC_SetGainToothAftTdc(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_Tdc(gain);
    }
    return errorCode;
}
int16_t SYNC_SetGainToothBefTdc(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_15d(gain);
    }
    return errorCode;
}

int16_t SYNC_SetGainToothBef2Tdc(uint32_t gain)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_angleClockGain_30d(gain);
    }
    return errorCode;
}

int16_t SYNC_SetEventDivider(uint32_t event_divider)
{
    int16_t errorCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if((BIOS_Faults & BIOS_FAILURE_ETPU)||(BIOS_Faults & BIOS_FAILURE_SYNC))
    {
        errorCode = PERIPHERAL_FAILURE;
    }
#endif
    if (errorCode == NO_ERROR)
    {
        write_event_divider(event_divider);
    }
    return errorCode;
}
#endif

#endif //_BUILD_SYNC_
