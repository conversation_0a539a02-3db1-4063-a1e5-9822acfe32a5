/*
 * File: ExhValMgm.h
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1699
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Aug  9 12:27:20 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (28), Warnings (4), Error (0)
 */

#ifndef RTW_HEADER_ExhValMgm_h_
#define RTW_HEADER_ExhValMgm_h_
#include <string.h>
#ifndef ExhValMgm_COMMON_INCLUDES_
# define ExhValMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#endif                                 /* ExhValMgm_COMMON_INCLUDES_ */

#include "ExhValMgm_SBS_types.h"

/* Includes for objects with custom storage classes. */
#include "exhval_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKEXHVALCME_dim                4U                        /* Referenced by: '<S23>/BKEXHVALCME_dim' */

/* Dim of BKEXHVALCME */
#define BKEXHVALRPM_dim                5U                        /* Referenced by: '<S23>/BKEXHVALRPM_dim' */

/* Dim of BKEXHVALRPM */
#define BKEXHVALTIME_dim               3U                        /* Referenced by:
                                                                  * '<S18>/BKEXHVALTIME_dim'
                                                                  * '<S24>/BKEXHVALTIME_dim'
                                                                  */

/* Dim of BKEXHVALTIME */
#define ID_EXHVALVE_MGM                19627665U                 /* Referenced by: '<S3>/ID_EXHVALVE_MGM' */

/* mask */

/* Block signals and states (default storage) for system '<S9>/Diag_Stab' */
typedef struct {
  uint16_T Memory_PreviousInput;       /* '<S45>/Memory' */
  uint16_T Memory1_PreviousInput;      /* '<S45>/Memory1' */
  uint8_T Memory_PreviousInput_mvd;    /* '<S41>/Memory' */
} DW_Diag_Stab_ExhValMgm_T;

/* Block signals and states (default storage) for system '<S9>/fc_diag_calc' */
typedef struct {
  uint8_T Memory_PreviousInput;        /* '<S44>/Memory' */
} DW_fc_diag_calc_ExhValMgm_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  DW_fc_diag_calc_ExhValMgm_T fc_diag_calc;/* '<S9>/fc_diag_calc' */
  DW_Diag_Stab_ExhValMgm_T Diag_Stab;  /* '<S9>/Diag_Stab' */
  uint32_T Memory_PreviousInput;       /* '<S26>/Memory' */
  uint32_T Memory_PreviousInput_do4;   /* '<S24>/Memory' */
  uint32_T diff;                       /* '<S9>/ExhV_Diag_Func' */
  int16_T UnitDelay1_DSTATE;           /* '<S22>/Unit Delay1' */
  int16_T UnitDelay3_DSTATE;           /* '<S22>/Unit Delay3' */
  int16_T Memory2_PreviousInput;       /* '<S31>/Memory2' */
  int16_T Memory1_PreviousInput;       /* '<S31>/Memory1' */
  int16_T Memory_PreviousInput_f5m;    /* '<S31>/Memory' */
  uint16_T Memory_PreviousInput_pjn;   /* '<S54>/Memory' */
  uint16_T oldExVDutyOut;              /* '<S9>/ExhV_Diag_Func' */
  uint8_T FlgExVManPos;                /* '<S51>/Calc_StFoExVSL' */
  uint8_T FlgExVManSelf;               /* '<S51>/Calc_StFoExVSL' */
  uint8_T ptFault;                     /* '<S9>/ExhV_Diag_Func' */
  uint8_T DiagMgm_SetDiagState;        /* '<S48>/DiagMgm_SetDiagState' */
  uint8_T Add;                         /* '<S44>/Add' */
  uint8_T flgPbyRate;                  /* '<S8>/PbyRateSel' */
  uint8_T flgZeroTarget;               /* '<S8>/PbyRateSel' */
  uint8_T FlgExVSelfWLamp;             /* '<S13>/Chart' */
  uint8_T FlgExhVZeroPos_jtz;          /* '<S13>/Chart' */
  uint8_T Memory1_PreviousInput_pyc;   /* '<S12>/Memory1' */
  uint8_T is_active_c8_ExhValMgm;      /* '<S51>/Calc_StFoExVSL' */
  uint8_T is_c8_ExhValMgm;             /* '<S51>/Calc_StFoExVSL' */
  uint8_T is_active_c3_ExhValMgm;      /* '<S9>/ExhV_Diag_Func' */
  uint8_T is_c3_ExhValMgm;             /* '<S9>/ExhV_Diag_Func' */
  uint8_T is_active_c9_ExhValMgm;      /* '<S9>/Calc_FlgExhVDiagOn' */
  uint8_T is_c9_ExhValMgm;             /* '<S9>/Calc_FlgExhVDiagOn' */
  uint8_T oldCntDiagCall;              /* '<S9>/Calc_FlgExhVDiagOn' */
  uint8_T oldDrivingCycle;             /* '<S9>/Calc_FlgExhVDiagOn' */
  uint8_T is_active_c7_ExhValMgm;      /* '<S8>/PbyRateSel' */
  uint8_T is_c7_ExhValMgm;             /* '<S8>/PbyRateSel' */
  uint8_T is_active_c1_ExhValMgm;      /* '<S13>/Chart' */
  uint8_T is_c1_ExhValMgm;             /* '<S13>/Chart' */
  uint8_T oldCntReqExhSelf;            /* '<S13>/Chart' */
  uint8_T cnt;                         /* '<S13>/Chart' */
  boolean_T Memory_PreviousInput_jyx;  /* '<S12>/Memory' */
} DW_ExhValMgm_T;

/* Invariant block signals (default storage) */
typedef struct {
  const uint8_T Constant;              /* '<S60>/Constant' */
} ConstB_ExhValMgm_T;

/* External outputs (root outports fed by signals with default storage) */
typedef struct {
  uint8_T BUS_TP;                      /* '<Root>/BUS_TP' */
} ExtY_ExhValMgm_T;

/* Block signals and states (default storage) */
extern DW_ExhValMgm_T ExhValMgm_DW;

/* External outputs (root outports fed by signals with default storage) */
extern ExtY_ExhValMgm_T ExhValMgm_Y;
extern const ConstB_ExhValMgm_T ExhValMgm_ConstB;/* constant block i/o */

/* Model entry point functions */
extern void ExhValMgm_initialize(void);

/* Exported entry point function */
extern void Trig_ExhValMgm_Init(void);

/* Exported entry point function */
extern void Trig_ExhValMgm_T10ms(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ExhValMgm'
 * '<S1>'   : 'ExhValMgm/ExhValMgm'
 * '<S2>'   : 'ExhValMgm/Model Info'
 * '<S3>'   : 'ExhValMgm/ExhValMgm/Init'
 * '<S4>'   : 'ExhValMgm/ExhValMgm/Merge'
 * '<S5>'   : 'ExhValMgm/ExhValMgm/T10ms'
 * '<S6>'   : 'ExhValMgm/ExhValMgm/TP'
 * '<S7>'   : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self'
 * '<S8>'   : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target'
 * '<S9>'   : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position'
 * '<S10>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag'
 * '<S11>'  : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve'
 * '<S12>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/FOEHXSELF'
 * '<S13>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/Self'
 * '<S14>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/Signal_Correct_End_Procedure'
 * '<S15>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/FOEHXSELF/Compare To Zero'
 * '<S16>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/Self/Chart'
 * '<S17>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/Self/Common_Comd'
 * '<S18>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory'
 * '<S19>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Force_Target'
 * '<S20>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Idle_Switch'
 * '<S21>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/PbyRateSel'
 * '<S22>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target'
 * '<S23>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target'
 * '<S24>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/Calc_ExhvalTime'
 * '<S25>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/Compare To Constant'
 * '<S26>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/ExhVal_TimingModule'
 * '<S27>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/LookUp_S16_U16'
 * '<S28>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S29>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Idle_Switch/Compare To Constant'
 * '<S30>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Idle_Switch/Compare To Constant1'
 * '<S31>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/Filter_Target'
 * '<S32>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_1'
 * '<S33>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_2'
 * '<S34>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_1/Data Type Conversion Inherited1'
 * '<S35>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_2/Data Type Conversion Inherited1'
 * '<S36>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/Look2D_S16_S16_U16_1'
 * '<S37>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/LookUp_S16_U16_1'
 * '<S38>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/Look2D_S16_S16_U16_1/Data Type Conversion Inherited1'
 * '<S39>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S40>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn'
 * '<S41>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Stab'
 * '<S42>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/ExhV_Diag_Func'
 * '<S43>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Subsystem'
 * '<S44>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/fc_diag_calc'
 * '<S45>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Stab/Signal_Stability_1'
 * '<S46>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Subsystem/EXV_DIAG_NORMAL'
 * '<S47>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Subsystem/EXV_DIAG_OUT_ERROR'
 * '<S48>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/fc_diag_calc/SetDiagState'
 * '<S49>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure'
 * '<S50>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Else'
 * '<S51>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF'
 * '<S52>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/Calc_StFoExVSL'
 * '<S53>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/Compare To Constant'
 * '<S54>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/En_Manual_Cond'
 * '<S55>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/En_Manual_Cond/Compare To Constant1'
 * '<S56>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/En_Manual_Cond/Compare To Constant2'
 * '<S57>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/En_Manual_Cond/Compare To Constant3'
 * '<S58>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/En_Manual_Cond/Compare To Constant4'
 * '<S59>'  : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Diag_Vehicle'
 * '<S60>'  : 'ExhValMgm/ExhValMgm/TP/TP_OUT'
 */

/*-
 * Requirements for '<Root>': ExhValMgm
 */
#endif                                 /* RTW_HEADER_ExhValMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
