/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/MCU/appl_calib/trunk/tree/DD/COMMON/HeatGripDriveMg#$   */
/* $ Description:                                                                                                */
/* $Revision:: 6369   $                                                                                          */
/* $Date:: 2014-03-25 09:13:52 +0100 (mar, 25 mar 2014)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              TransportLock_out.h
 *
 * Author(s): Lana L.
 * 
 * 
 * Description:
 * 
 *
 * Usage notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifndef _TRSLOCK_OUT_H_
#define _TRSLOCK_OUT_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "Rtwtypes.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T TransportLockEcu;
extern uint8_T TempUnlockCntDown;
extern uint8_T TransportLockAck;
extern uint32_T TransportLockEE;
extern uint32_T TempUnlockCntDownEE;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * TransportLock_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void TransportLock_Init (void);

/*--------------------------------------------------------------------------*
 * TransportLock_T100m - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void TransportLock_T100m (void);

#endif 

/****************************************************************************
 ****************************************************************************/

