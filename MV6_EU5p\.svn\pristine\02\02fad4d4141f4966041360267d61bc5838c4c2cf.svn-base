/*
 * File: ThrottleAdapt_private.h
 *
 * Code generated for Simulink model 'ThrottleAdapt'.
 *
 * Model version                  : 1.736
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun  8 17:18:28 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ThrottleAdapt_private_h_
#define RTW_HEADER_ThrottleAdapt_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "ThrottleAdapt.h"

/* Includes for objects with custom storage classes. */
#include "mathlib.h"
#include "null.h"
#include "recmgm.h"
#include "throttle_model.h"
#include "Dbw_mgm.h"
#include "Throttle_target.h"
#include "air_mgm.h"
#include "Prestarget_mgm.h"
#include "Air_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Computed Parameter: rtCP_Switch1_Threshold
 * Referenced by: '<S33>/Switch1'
 */
#define rtCP_Switch1_Threshold         (0)

/* Computed Parameter: rtCP_RESET_VAL_Value
 * Referenced by: '<S31>/RESET_VAL'
 */
#define rtCP_RESET_VAL_Value           (0)

/* Computed Parameter: rtCP_RESET_EVENT_Value
 * Referenced by: '<S31>/RESET_EVENT'
 */
#define rtCP_RESET_EVENT_Value         (((uint8_T)0U))

/* Computed Parameter: rtCP_ZERO_Value_i
 * Referenced by: '<S16>/ZERO'
 */
#define rtCP_ZERO_Value_i              (((uint8_T)0U))

/* Imported (extern) block parameters */
extern int16_T MAXPRESERR;             /* Variable: MAXPRESERR
                                        * Referenced by: '<S32>/MAXPRESERR'
                                        * THROTTLEADAPT.MINPRESERR: Max pressure error
                                        */
extern int16_T MINPRESERR;             /* Variable: MINPRESERR
                                        * Referenced by: '<S32>/MINPRESERR'
                                        * THROTTLEADAPT.MINPRESERR: Min pressure error
                                        */
extern int16_T PRESERRDB;              /* Variable: PRESERRDB
                                        * Referenced by: '<S32>/PRESERRDB'
                                        * THROTTLEADAPT.PRESERRDB: Dead band on pressure error
                                        */
extern int16_T DANGCORRADMAX;          /* Variable: DANGCORRADMAX
                                        * Referenced by: '<S19>/DANGCORRADMAX'
                                        * THROTTLEADAPT.DANGCORRADMAX: Max VtDAngThr modification
                                        */
extern int16_T DANGCORRADMIN;          /* Variable: DANGCORRADMIN
                                        * Referenced by: '<S19>/DANGCORRADMIN'
                                        * THROTTLEADAPT.DANGCORRADMIN: Min VtDAngThr modification
                                        */
extern int16_T VTDANGGNAD[5];          /* Variable: VTDANGGNAD
                                        * Referenced by: '<S19>/VTDANGGNAD'
                                        * THROTTLEADAPT.VTDANGGNAD: Gain for VtDAngThr correction to store
                                        */
extern int16_T KFILTDANGTHR;           /* Variable: KFILTDANGTHR
                                        * Referenced by: '<S31>/KFILTDANGTHR'
                                        * THROTTLEADAPT.KFILTDANGTHR: Filter constant for DAngThr
                                        */
extern int16_T KIDANGTHR;              /* Variable: KIDANGTHR
                                        * Referenced by: '<S33>/KIDANGTHR'
                                        * THROTTLEADAPT.KIDANGTHR: PI integral gain
                                        */
extern int16_T KPDANGTHR;              /* Variable: KPDANGTHR
                                        * Referenced by: '<S33>/KPDANGTHR'
                                        * THROTTLEADAPT.KIDANGTHR: PI integral gain
                                        */
extern int16_T MAXDANGTHR;             /* Variable: MAXDANGTHR
                                        * Referenced by: '<S31>/MAXDANGTHR'
                                        * THROTTLEADAPT.MAXDANGTHR: Max throttle angle correction
                                        */
extern int16_T MAXDANGTHRADP;          /* Variable: MAXDANGTHRADP
                                        * Referenced by: '<S19>/MAXDANGTHRADP'
                                        * THROTTLEADAPT.MAXDANGTHRADP: Max adaptive throttle angle correction
                                        */
extern int16_T MINDANGTHR;             /* Variable: MINDANGTHR
                                        * Referenced by: '<S31>/MINDANGTHR'
                                        * THROTTLEADAPT.KIDANGTHR: PI integral gain
                                        */
extern int16_T MINDANGTHRADP;          /* Variable: MINDANGTHRADP
                                        * Referenced by: '<S19>/MINDANGTHRADP'
                                        * THROTTLEADAPT.MINDANGTHRADP: Min adaptive throttle angle correction
                                        */
extern uint16_T GNDANGCORRINDAD;       /* Variable: GNDANGCORRINDAD
                                        * Referenced by: '<S19>/GNDANGCORRINDAD'
                                        * THROTTLEADAPT.GNDANGCORRINDAD: Adaptive correction gain
                                        */
extern uint16_T KFILTPRESOBJ;          /* Variable: KFILTPRESOBJ
                                        * Referenced by: '<S32>/KFILTPRESOBJ'
                                        * THROTTLEADAPT.KFILTPRESOBJ: Filter constant for PresObj
                                        */
extern uint16_T BKDANGDSQ[5];          /* Variable: BKDANGDSQ
                                        * Referenced by: '<S19>/BKDANGDSQ'
                                        * THROTTLEADAPT.BKDANGDSQ: Normalized distance from breakpoint to adjust
                                        */
extern uint16_T BKANGTHRTARG[7];       /* Variable: BKANGTHRTARG
                                        * Referenced by: '<S31>/BKANGTHRTARG'
                                        * THROTTLEADAPT.BKANGTHRTARG: AngThrModelTarg breakpoint vector
                                        */
extern uint16_T DANGTHRSTEP;           /* Variable: DANGTHRSTEP
                                        * Referenced by: '<S33>/DANGTHRSTEP'
                                        * THROTTLEADAPT.DANGTHRSTEP: Max variation for AngThrModelTarg stability
                                        */
extern uint16_T MINPOSDANGADP;         /* Variable: MINPOSDANGADP
                                        * Referenced by: '<S16>/MINPOSDANGADP'
                                        * THROTTLEADAPT.MINPOSDANGADP: Min throttle angle correction for adaptivity
                                        */
extern uint16_T THANGTHRMODEL;         /* Variable: THANGTHRMODEL
                                        * Referenced by: '<S16>/THANGTHRMODEL'
                                        * THROTTLEADAPT.THANGTHRMODEL: Max variation for AngThrModelTarg stability
                                        */
extern uint16_T THDANGTHR;             /* Variable: THDANGTHR
                                        * Referenced by: '<S16>/THDANGTHR'
                                        * THROTTLEADAPT.THDANGTHR: Max variation for DAngThr stability
                                        */
extern uint16_T NTASKANGTHRSTAB;       /* Variable: NTASKANGTHRSTAB
                                        * Referenced by: '<S16>/NTASKANGTHRSTAB'
                                        * THROTTLEADAPT.NTASKANGTHRSTAB: Number of task for AngThrModelTarg stability
                                        */
extern uint16_T NTASKDANGTHRSTAB;      /* Variable: NTASKDANGTHRSTAB
                                        * Referenced by: '<S16>/NTASKDANGTHRSTAB'
                                        * THROTTLEADAPT.NTASKDANGTHRSTAB: Number of task for DAngThr stability
                                        */
extern uint8_T ENDANGTHRADAPT;         /* Variable: ENDANGTHRADAPT
                                        * Referenced by: '<S9>/ENDANGTHRADAPT'
                                        * THROTTLEADAPT.ENDANGTHRADAPT: Enable flag for DAngThr adaptivity calculation
                                        */
extern uint8_T ENDANGTHRCALC;          /* Variable: ENDANGTHRCALC
                                        * Referenced by: '<S9>/ENDANGTHRCALC'
                                        * THROTTLEADAPT.ENDANGTHRCALC: Enable flag for DAngThr calculation
                                        */
extern void ThrottleAdap_ThrottleAdapt_Init(void);
extern void ThrottleAdapt_Update_VtDAngThr(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern int16_T VtDAngThrEE[7];         /* '<Root>/_DataStoreBlk_3' */

/* VtDAngThr */
#endif                                 /* RTW_HEADER_ThrottleAdapt_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
