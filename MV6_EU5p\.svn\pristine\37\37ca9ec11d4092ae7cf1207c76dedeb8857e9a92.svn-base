/*
 * File: FOInjCtfMgm.h
 *
 * Code generated for Simulink model 'FOInjCtfMgm'.
 *
 * Model version                  : 1.360
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jan  5 15:31:29 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (30), Warnings (2), Error (0)
 */

#ifndef RTW_HEADER_FOInjCtfMgm_h_
#define RTW_HEADER_FOInjCtfMgm_h_
#include <string.h>
#ifndef FOInjCtfMgm_COMMON_INCLUDES_
# define FOInjCtfMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* FOInjCtfMgm_COMMON_INCLUDES_ */

#include "FOInjCtfMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "FOInjCtfMgm_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define ID_FOINJ_CTF_MGM               22817014U                 /* Referenced by: '<S3>/ID_FOINJ_CTF_MGM' */

/* mask */

/* Block signals and states (default storage) for system '<S14>/Player' */
typedef struct {
  uint32_T absLocalCnt;                /* '<S14>/Player' */
  uint16_T localCnt;                   /* '<S14>/Player' */
} rtDW_Player_FOInjCtfMgm_T;

/* Block signals and states (default storage) for system '<S25>/Calc_SymFOInjCutoff' */
typedef struct {
  uint8_T x[4];                        /* '<S25>/Calc_SymFOInjCutoff' */
} rtDW_Calc_SymFOInjCutoff_FOIn_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  rtDW_Calc_SymFOInjCutoff_FOIn_T sf_Calc_SymFOInjCutoff_c3u;/* '<S24>/Calc_SymFOInjCutoff' */
  rtDW_Calc_SymFOInjCutoff_FOIn_T sf_Calc_SymFOInjCutoff;/* '<S25>/Calc_SymFOInjCutoff' */
  rtDW_Player_FOInjCtfMgm_T sf_Player_afc;/* '<S17>/Player' */
  rtDW_Player_FOInjCtfMgm_T sf_Player_nzn;/* '<S16>/Player' */
  rtDW_Player_FOInjCtfMgm_T sf_Player_jao;/* '<S15>/Player' */
  rtDW_Player_FOInjCtfMgm_T sf_Player; /* '<S14>/Player' */
  uint32_T Add;                        /* '<S6>/Add' */
  uint16_T MultiportSwitch;            /* '<S19>/Multiport Switch' */
  uint8_T SymFOInjCutoff_khl[4];       /* '<S24>/Calc_SymFOInjCutoff' */
  uint8_T SymFOInjCutoff_d0v[4];       /* '<S25>/Calc_SymFOInjCutoff' */
  uint8_T VtFOInjCutoff_gmp[4];        /* '<S13>/Chart' */
  uint8_T enSync[4];                   /* '<S13>/Chart' */
  uint8_T MultiportSwitch1;            /* '<S11>/Multiport Switch1' */
  uint8_T idx;                         /* '<S17>/Player' */
  uint8_T idx_otj;                     /* '<S16>/Player' */
  uint8_T idx_lah;                     /* '<S15>/Player' */
  uint8_T idx_jwk;                     /* '<S14>/Player' */
  uint8_T Memory_PreviousInput_k2b;    /* '<S11>/Memory' */
  uint8_T Memory1_PreviousInput;       /* '<S11>/Memory1' */
  uint8_T Memory2_PreviousInput;       /* '<S11>/Memory2' */
  uint8_T Memory3_PreviousInput;       /* '<S11>/Memory3' */
} D_Work_FOInjCtfMgm_T;

/* Block signals and states (default storage) */
extern D_Work_FOInjCtfMgm_T FOInjCtfMgm_DWork;

/* Model entry point functions */
extern void FOInjCtfMgm_initialize(void);

/* Exported entry point function */
extern void Trig_FOInjCtfMgm_Init(void);

/* Exported entry point function */
extern void Trig_FOInjCtfMgm_PreTdc(void);

/* Exported entry point function */
extern void Trig_FOInjCtfMgm_T10ms(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'FOInjCtfMgm'
 * '<S1>'   : 'FOInjCtfMgm/FOInjCtfMgm'
 * '<S2>'   : 'FOInjCtfMgm/Model Info'
 * '<S3>'   : 'FOInjCtfMgm/FOInjCtfMgm/Init'
 * '<S4>'   : 'FOInjCtfMgm/FOInjCtfMgm/Merger'
 * '<S5>'   : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc'
 * '<S6>'   : 'FOInjCtfMgm/FOInjCtfMgm/T10ms'
 * '<S7>'   : 'FOInjCtfMgm/FOInjCtfMgm/TP'
 * '<S8>'   : 'FOInjCtfMgm/FOInjCtfMgm/Init/Dummy_Export'
 * '<S9>'   : 'FOInjCtfMgm/FOInjCtfMgm/Init/Dummy_Export/Dummy_Chart'
 * '<S10>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/If Action Subsystem1'
 * '<S11>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1'
 * '<S12>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Subsystem'
 * '<S13>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff'
 * '<S14>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_1'
 * '<S15>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_2'
 * '<S16>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_3'
 * '<S17>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_4'
 * '<S18>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Subsystem/Calc_CutOff'
 * '<S19>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Subsystem/Calc_Time'
 * '<S20>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Subsystem/Trigger_Cylinder'
 * '<S21>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/Chart'
 * '<S22>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT'
 * '<S23>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/else'
 * '<S24>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/elseif'
 * '<S25>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/if'
 * '<S26>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/elseif/Calc_SymFOInjCutoff'
 * '<S27>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/Sync_cutoff/VtFOInjCutoff_OUT/if/Calc_SymFOInjCutoff'
 * '<S28>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_1/Player'
 * '<S29>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_2/Player'
 * '<S30>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_3/Player'
 * '<S31>'  : 'FOInjCtfMgm/FOInjCtfMgm/PreTdc/Subsystem1/fc_4/Player'
 */

/*-
 * Requirements for '<Root>': FOInjCtfMgm
 */
#endif                                 /* RTW_HEADER_FOInjCtfMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
