/********************************************************************************/
/* FILE NAME: sparkHandler.c                     COPYRIGHT (c) Akhela srl       */
/* VERSION:  0.1.1                                    All Rights Reserved       */
/*                                                                              */
/********************************************************************************/

/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function FastLinkedChan_OUT, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

#include "..\..\common\ETPU_Shared.h"
#include "..\..\common\ETPU_SharedTypes.h"
#include "..\include\FastLinkedChan.h"
#include "..\include\etpuc.h"
#include "..\include\eTPUC_common.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

#pragma library;
#pragma option +l;  // List the library
#pragma option v;


/********************************************************************************
* FUNCTION: FastLinkedChan_OUT                                                  *
* PURPOSE:  This function drive the output of fastlinked channels               *
*                                                                               *
* INPUTS NOTES:                                                                 *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
void FastLinkedChan_OUT(unsigned int minOnTime, unsigned int minOffTime, unsigned int FL_OUT_chanFlags, unsigned int lastTransitionTime,
                        unsigned int lastOnTime, unsigned int lastOffTime, unsigned int switchingTimes, unsigned int directionChannel,
                        unsigned int switchingMode)
{
#ifdef _BUILD_FASTLINKEDCHAN_

    unsigned int currentTransitionTime;

    if (HSR_INIT_FLCHAN_OUT)   // Required to initialize
    {
        FL_OUT_chanFlags = 1;
        switchingTimes = 0;
        lastOnTime = 0;
        lastOffTime = 0;
        lastTransitionTime = 0;

        SetPinLow();
        SetChannelMode(sm_st);
        OnMatchAPinToggle();

//        SetupMatch_A(tcr1+minOffTime, Mtcr1_Ctcr1_ge, NoChange);
    }
    else if(HSR_CLOSE_FLCHAN_OUT) /* stop channel operations */
    {
        DisableMatchAndTransitionEventHandling();
        SetPinLow();
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
    }
    else if (IsLinkServiceRequestEvent())
    {
//      Linking is detected
        ClearLSRLatch();

        SetPinPerPacA();

        currentTransitionTime = tcr1;

        if(FL_OUT_chanFlags)
        {
            if(switchingMode)
                LinkToChannel(directionChannel);

            if(FLCHAN_IN_level == 0)
            {
                lastOnTime = currentTransitionTime - lastTransitionTime;
                SetupMatch_A(currentTransitionTime+minOffTime, Mtcr1_Ctcr1_ge, NoChange);
            }
            else
            {
                lastOffTime = currentTransitionTime - lastTransitionTime;
                SetupMatch_A(currentTransitionTime+minOnTime, Mtcr1_Ctcr1_ge, NoChange);
            }
            lastTransitionTime = currentTransitionTime;
            FL_OUT_chanFlags = 0;
            switchingTimes++;
        }
        else
        {
            if(FLCHAN_IN_level == 1)
            {
                SetupMatch_A(lastTransitionTime+minOffTime, Mtcr1_Ctcr1_ge, PinToggle);
            }
            else
            {
                SetupMatch_A(lastTransitionTime+minOnTime, Mtcr1_Ctcr1_ge, PinToggle);
            }
            FL_OUT_chanFlags = 1;
        }

    }
    else if (IsMatchAOrTransitionBEvent())
    {
//      MatchA condition is detected
        ClearMatchALatch();

        if(FL_OUT_chanFlags)
        {
            if(switchingMode)
                LinkToChannel(directionChannel);

            currentTransitionTime = GetCapRegB();
            if(FLCHAN_IN_level == 0)
            {
                lastOnTime = currentTransitionTime - lastTransitionTime;
                SetupMatch_A(currentTransitionTime+minOffTime, Mtcr1_Ctcr1_ge, NoChange);
            }
            else
            {
                lastOffTime = currentTransitionTime - lastTransitionTime;
                SetupMatch_A(currentTransitionTime+minOnTime, Mtcr1_Ctcr1_ge, NoChange);
            }
            lastTransitionTime = currentTransitionTime;
            FL_OUT_chanFlags = 0;
            switchingTimes++;
        }
        else
        {
            FL_OUT_chanFlags = 1;
            OnMatchAPinToggle();
        }
    }
    else
    {
        //This else statement is used to catch all unspecified entry table conditions
        // Clear all possible event sources
        // And set the unexpected event error indicator
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
    }
#endif /* _BUILD_FASTLINKEDCHAN_ */
}

#pragma endlibrary;

