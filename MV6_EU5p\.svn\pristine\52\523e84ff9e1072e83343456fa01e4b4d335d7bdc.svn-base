/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _OSEK_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "OS_hooks.h"
     
/*################ nedeed for StartupHook ################*/
//#include "digio.h"
#include "sys.h"
#include "mathlib.h"
#include "intsrcmgm.h"
#include "eemgm.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint32_t currentTaskStkPtr;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * PreTaskHook - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void PreTaskHook(void)
{
#pragma asm
    stwu sp,-80(sp)
    stmw r12,0(sp)

    lis  r3,currentTaskStkPtr@ha    
    addi r3,r3,currentTaskStkPtr@l
    stw  sp,0(r3)
    blr
#pragma endasm
}

/*--------------------------------------------------------------------------*
 * PostTaskHook - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void PostTaskHook(void)
{
#pragma asm
    lis  r3, currentTaskStkPtr@h
    ori  r3, r3, currentTaskStkPtr@l
    lwz  sp,0(r3)                              

    lmw  r12,0(sp)
    addi sp,sp,80
    blr
#pragma endasm
}

/*--------------------------------------------------------------------------*
 * PostTaskHook - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void StartupHook(void)
{
    /*cfg_MMU();*/ /*MI MMU configured in boot */
    ((void (*)(void))(GetApplicationStartup()))();
}


/*--------------------------------------------------------------------------*
 * PostTaskHook - Function description
 *
 * Implementation notes:
 * Modified after enabling MISRA 16.2 - avoid recursive function
 *--------------------------------------------------------------------------*/
void ShutdownHook( StatusType error)
{

    /* saving in EEPROM the appropriate data */
    if (error != E_OS_TIMEDOUT_KEY_OFF)
    {
        SYS_ProgDelayedShutdown();
    }

    ShutdownOSerrorHandler(error);
     
}

/*--------------------------------------------------------------------------*
 * PostTaskHook - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ErrorHook(StatusType error) 
{
    if (error == E_OS_STATE)
    {
        tempOsService = OsService;
        tempOsObjId = OsObjId;
    }
#ifdef CHECK_BIOS_FAULTS
    SETBIT(BIOS_Faults, OSEK_IDX);
#endif /* CHECK_BIOS_FAULTS */
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _OSEK_  */
