/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _OSEK_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "OS_errors.h"
#include "OS_resources.h"
#include "OS_api.h"
#include "mpc5500_spr_macros.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

/* Resource Configuration table         */
const ResCfg  OsResCfgTable[OSNUMRES]=
{
    { RES_1ID, RES_1_PRI },
    { RES_2ID, RES_2_PRI },
    { RES_3ID, RES_3_PRI },
    { RES_4ID, RES_4_PRI },
    { RES_5ID, RES_5_PRI },
    { RES_6ID, RES_6_PRI }
    
};


ResCBS   OsResTable[OSNUMRES];           /* Resources table */


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * _blr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * AM - MISRA 2004 Rule 2.1: in-line assembly must be embedded in dedicated 
 *      assembly procedures only; use of asm("mbar 0"); is not allowed
 *--------------------------------------------------------------------------*/
 static inline void _mbar(void);

 /*--------------------------------------------------------------------------*
 * _blr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * AM - MISRA 2004 Rule 2.1: in-line assembly must be embedded in dedicated 
 *      assembly procedures only; use of asm("isync"); is not allowed
 *--------------------------------------------------------------------------*/
 static inline void _isync(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/


/*--------------------------------------------------------------------------*
 * GetResource - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType GetResource(ResourceType ResID)
{
    TaskType currentTaskId;
    uint32_t MSR_EE_Status;

    OSrtiSetServiceWatch(OSServiceId_GetResource);
    MSR_EE_Status=(getSpecReg32MSR())&(0x8000);
    if (MSR_EE_Status)
    {
        DisableAllInterrupts();
    }
    OSRESCURRPRI(ResID) = INTC_CUR_PRI();
    if ((uint8_t)INTC_CUR_PRI() < OSRESPRI(ResID))
    {
        _mbar();
        _isync();
        INTC_CUR_PRI() = OSRESPRI(ResID); 
    }
    if (MSR_EE_Status)
    {
        EnableAllInterrupts();
    }
    OSRESSTATE(ResID)   = RES_LOCKED;
    GetTaskID(&currentTaskId);
    OSRESTASKID(ResID)  = currentTaskId;
    OSrtiServiceWatchOnExit(OSServiceId_GetResource);

    return( E_OK );
}

/*--------------------------------------------------------------------------*
 * ReleaseResource - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
StatusType ReleaseResource(ResourceType ResID)
{
        uint32_t MSR_EE_Status;

    OSrtiSetServiceWatch(OSServiceId_ReleaseResource);
    MSR_EE_Status=(getSpecReg32MSR())&(0x8000);
    if (MSR_EE_Status)
    {
        DisableAllInterrupts();
    }

    _mbar();
    INTC_CUR_PRI() = OSRESCURRPRI(ResID);
    if (MSR_EE_Status) 
    {
        EnableAllInterrupts();
    }
    OSRESSTATE(ResID)  = RES_FREE;
    OSRESTASKID(ResID) = NULLTASK;    
    OSRESCURRPRI(ResID)= DEFAULT_CURR_PRI;
    OSrtiServiceWatchOnExit(OSServiceId_ReleaseResource);
        
    return( E_OK );
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * _mbar - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static __asm  inline void _mbar(void)
{
    mbar 0;
}

/*--------------------------------------------------------------------------*
 * _isync - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static __asm  inline void _isync(void)
{
    isync;
}
#endif /* _OSEK_ */
