/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "sys.h"
#include "..\include\checkVersion.h"
#include <string.h>

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * compute_CheckSum - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_t compute_CheckSum(uint32_t dest, 
                                  uint32_t size,
                                  uint32_t *sum);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
int16_t app_start(void)
{
    BlockDescription* InfoPtr;
    uint32_t source;
    uint32_t size;
    int16_t error = NO_ERROR;
    uint32_t *p;
    uint32_t i;

    /* check application compatibility */
    error = app_CheckVersion();
    if (error != NO_ERROR)
    {
        error = -1;
    }
    else
    {
#ifdef _BUILD_CHECKSUMVALIDATION_
        /* verify calibration checksum */
        {
            uint32_t checksum;

            InfoPtr = (BlockDescription *) (((uint32_t)(&__CALIB_ROM_START)+(uint32_t)(&__CALIB_ROM_SIZE))-sizeof(BlockDescription));
            source = (uint32_t)(&__CALIB_ROM_START);
            size = (uint32_t)(&__CALIB_ROM_SIZE) - sizeof(BlockDescription);

            checksum = 0;
            if (compute_CheckSum(source, size, &checksum) == NO_ERROR)
            {
                if (InfoPtr->blockChecksum != checksum)
                {
                    error = -1;
                }
            }
        }
#endif
        if (error == NO_ERROR)
        {
            /* check calibration compatibility */
            error = calib_CheckVersion();
            if (error != NO_ERROR)
            {
                error = -1;
            }
            else
            {
                /* clear sram memory before jumping to the application */
                /* NO VSRAM WILL BE CLEARED */
                p = (uint32_t *)((uint32_t)&__SRAM_START_ADDR_1);
                for (i=0; i<((uint32_t)&__SRAM_SIZE_1); i++)
                {
                    (*p) = 0;
                    p++;
                }

#ifndef _BUILD_VCALIB_
    #ifdef _BUILD_DEVELOPMENT_
                /* if the virtual calibration module is not defined we need to *
                 * initialize the calibration RAM area                         */
                memcpy(&__VCALIB_START, &__CALIB_ROM_START, (size_t)&__VCALIB_SIZE);
    #endif /* _BUILD_DEVELOPMENT_ */
#endif /* _BUILD_VCALIB_ */
                
                StartOS(OSAPPMODE);
            }
        }
    }
    return error;
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * compute_CheckSum - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t compute_CheckSum(uint32_t dest,
                                 uint32_t size, 
                                 uint32_t *sum)
{
    uint32_t i;
    uint32_t local_sum = 0;
    uint32_t value;

    for (i=0; i<(size/sizeof(uint32_t)); i++)
    {
        value  =  *(uint8_t *)(dest  );
        value |= (*(uint8_t *)(dest+1))<<8;
        value |= (*(uint8_t *)(dest+2))<<16;
        value |= (*(uint8_t *)(dest+3))<<24;

        local_sum += value;
        dest += sizeof(uint32_t);
    }

    *sum += local_sum;

    return NO_ERROR;
}

