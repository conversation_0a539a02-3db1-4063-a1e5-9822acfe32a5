/*
 * File: TracCtrl_private.h
 *
 * Code generated for Simulink model 'TracCtrl'.
 *
 * Model version                  : 1.825
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 13:03:51 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TracCtrl_private_h_
#define RTW_HEADER_TracCtrl_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "TracCtrl.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "gearshift_mgm.h"
#include "vspeed_mgm.h"
#include "digitalin.h"
#include "trq_est.h"
#include "trq_drivmgm.h"
#include "cmisatmgm_out.h"
#include "trq_driver.h"
#include "diagflags_out.h"
#include "idlectf_mgm.h"
#include "canmgm.h"
#include "Gear_mgm.h"
#include "ptrain_diag.h"
#include "engflag.h"
#include "syncmgm.h"
#include "launchctrl_out.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint32_T FOTCREC;               /* Variable: FOTCREC
                                        * Referenced by: '<S10>/FOTCREC'
                                        * Enable Force TC Recovery
                                        */
extern int16_T BKTCLEVIDX[3];          /* Variable: BKTCLEVIDX
                                        * Referenced by: '<S81>/BKTCLEVIDX'
                                        * bk
                                        */
extern int16_T KITRACCTRL;             /* Variable: KITRACCTRL
                                        * Referenced by: '<S66>/KITRACCTRL'
                                        * TC PI integral gain
                                        */
extern int16_T BKSMINTGAIN[4];         /* Variable: BKSMINTGAIN
                                        * Referenced by: '<S80>/BKSMINTGAIN'
                                        * Delta vehSpeed error breakpoints for traction control
                                        */
extern int16_T BKACCDVS[7];            /* Variable: BKACCDVS
                                        * Referenced by: '<S67>/BKACCDVS'
                                        * Delta Acc vehSpeed error breakpoints for traction control
                                        */
extern int16_T BKCMEESTWHEEL[10];      /* Variable: BKCMEESTWHEEL
                                        * Referenced by: '<S24>/BKCMEESTWHEEL'
                                        * CmeEstWheel breakpoints for traction control
                                        */
extern int16_T BKDCMEESTWHEEL[6];      /* Variable: BKDCMEESTWHEEL
                                        * Referenced by: '<S24>/BKDCMEESTWHEEL'
                                        * Delta CmeEstWheel breakpoints for traction control
                                        */
extern int16_T BKDVSERR[7];            /* Variable: BKDVSERR
                                        * Referenced by:
                                        *   '<S67>/BKDVSERR'
                                        *   '<S81>/BKDVSERR'
                                        * Delta vehSpeed error breakpoints for traction control
                                        */
extern int16_T BKDVSERRCUTOFF[5];      /* Variable: BKDVSERRCUTOFF
                                        * Referenced by: '<S23>/BKDVSERRCUTOFF'
                                        * DVSCtrlErr breakpoints for CutOff
                                        */
extern int16_T MAXDVSPDSAT;            /* Variable: MAXDVSPDSAT
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Delta vehspeed max value
                                        */
extern int16_T TBDVSTARG[56];          /* Variable: TBDVSTARG
                                        * Referenced by: '<S28>/TBDVSTARG'
                                        * Delat VehSpeed in closed-loop
                                        */
extern int16_T TBTHRDVSPD[70];         /* Variable: TBTHRDVSPD
                                        * Referenced by: '<S29>/TBTHRDVSPD'
                                        * Delta VehSpeed in closed-loop
                                        */
extern int16_T TBTHRDVSPDWET[70];      /* Variable: TBTHRDVSPDWET
                                        * Referenced by: '<S29>/TBTHRDVSPDWET'
                                        * Delta VehSpeed in closed-loop
                                        */
extern int16_T THRNEGDVS;              /* Variable: THRNEGDVS
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Delta vehspeed negative threshold
                                        */
extern int16_T VTMINRTACCDVSFM[3];     /* Variable: VTMINRTACCDVSFM
                                        * Referenced by: '<S46>/VTMINRTACCDVSFM'
                                        * Rate
                                        */
extern int16_T CMISMOOTHRATREC;        /* Variable: CMISMOOTHRATREC
                                        * Referenced by: '<S19>/CMISMOOTHRATREC'
                                        * rate return recovery
                                        */
extern int16_T OFFCMIGRUFIL;           /* Variable: OFFCMIGRUFIL
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Offset applied to CmiTracP
                                        */
extern int16_T OFFCMITRACP;            /* Variable: OFFCMITRACP
                                        * Referenced by: '<S27>/OFFCMITRACP'
                                        * Offset applied to CmiTracP
                                        */
extern int16_T OFFCMITRACP2;           /* Variable: OFFCMITRACP2
                                        * Referenced by: '<S27>/OFFCMITRACP2'
                                        * Offset applied to CmiTracP
                                        */
extern int16_T OFFCMITRACP3;           /* Variable: OFFCMITRACP3
                                        * Referenced by: '<S27>/OFFCMITRACP3'
                                        * Offset applied to CmiTracP
                                        */
extern int16_T TBOFFCMITRACI[9];       /* Variable: TBOFFCMITRACI
                                        * Referenced by: '<S27>/TBOFFCMITRACI'
                                        * Cmi offset
                                        */
extern int16_T VTGNSMOOTLEVEL[3];      /* Variable: VTGNSMOOTLEVEL
                                        * Referenced by: '<S26>/VTGNSMOOTLEVEL'
                                        * offset model
                                        */
extern int16_T DVSTARGRATEMAX;         /* Variable: DVSTARGRATEMAX
                                        * Referenced by: '<S28>/DVSTARGRATEMAX'
                                        * Rate limiter for RollCAN correction
                                        */
extern int16_T DVSTARGRATEMIN;         /* Variable: DVSTARGRATEMIN
                                        * Referenced by: '<S28>/DVSTARGRATEMIN'
                                        * Rate limiter for RollCAN correction
                                        */
extern int16_T TBGAINCMITRACDVS[49];   /* Variable: TBGAINCMITRACDVS
                                        * Referenced by: '<S67>/TBGAINCMITRACDVS'
                                        * Torque correction as function of DeltaAccF and AccRearInt
                                        */
extern int16_T TBKPTCGAINI[21];        /* Variable: TBKPTCGAINI
                                        * Referenced by: '<S64>/TBKPTCGAINI'
                                        * KP I
                                        */
extern int16_T TBKPTCGAINP[21];        /* Variable: TBKPTCGAINP
                                        * Referenced by: '<S65>/TBKPTCGAINP'
                                        * KP P
                                        */
extern int16_T TBTHRDVSPDGAIN[18];     /* Variable: TBTHRDVSPDGAIN
                                        * Referenced by: '<S29>/TBTHRDVSPDGAIN'
                                        * Model gain
                                        */
extern int16_T VTGAINTCGEAR[7];        /* Variable: VTGAINTCGEAR
                                        * Referenced by: '<S63>/VTGAINTCGEAR'
                                        * gain smooth
                                        */
extern int16_T VTSMINTGAIN[4];         /* Variable: VTSMINTGAIN
                                        * Referenced by: '<S80>/VTSMINTGAIN'
                                        * Smooth gain
                                        */
extern int16_T BKROLLANGLETARGCORR[7]; /* Variable: BKROLLANGLETARGCORR
                                        * Referenced by:
                                        *   '<S24>/BKROLLANGLETARGCORR'
                                        *   '<S81>/BKROLLANGLETARGCORR'
                                        * Roll Angle bkp
                                        */
extern int16_T BKROLLTCINIT[3];        /* Variable: BKROLLTCINIT
                                        * Referenced by: '<S24>/BKROLLTCINIT'
                                        * Roll breakpoint
                                        */
extern uint16_T KFFOTCREC1;            /* Variable: KFFOTCREC1
                                        * Referenced by: '<S92>/KFFOTCREC1'
                                        * Kf
                                        */
extern uint16_T KFFOTCREC2;            /* Variable: KFFOTCREC2
                                        * Referenced by: '<S92>/KFFOTCREC2'
                                        * Kf
                                        */
extern uint16_T KFILTACCDVS;           /* Variable: KFILTACCDVS
                                        * Referenced by: '<S45>/KFILTACCDVS'
                                        * AccRear filter constant
                                        */
extern uint16_T BKVEHSPEEDTRAC[7];     /* Variable: BKVEHSPEEDTRAC
                                        * Referenced by: '<S24>/BKVEHSPEEDTRAC'
                                        * VehSpeed breakpoints for AccRearInt target
                                        */
extern uint16_T DELTAACCDVSMAX;        /* Variable: DELTAACCDVSMAX
                                        * Referenced by: '<S45>/DELTAACCDVSMAX'
                                        * Max absolute value for DeltaAcc - PI control
                                        */
extern uint16_T DELTAVSSPRINGUP;       /* Variable: DELTAVSSPRINGUP
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Delta VehSpeed in SPRING_UP
                                        */
extern uint16_T THVSNOCHANGE;          /* Variable: THVSNOCHANGE
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Threshold VehSpeed to enter in SPRING_UP
                                        */
extern uint16_T THVSPRINGUP;           /* Variable: THVSPRINGUP
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Threshold VehSpeed to enter in SPRING_UP
                                        */
extern uint16_T TBDVSTARGCORR[56];     /* Variable: TBDVSTARGCORR
                                        * Referenced by: '<S28>/TBDVSTARGCORR'
                                        * Delata VehSpeed correction for Roll angle
                                        */
extern uint16_T BKTCRPMCUTOFF[3];      /* Variable: BKTCRPMCUTOFF
                                        * Referenced by: '<S23>/BKTCRPMCUTOFF'
                                        * Rpm breakpoints for CutOff
                                        */
extern uint16_T NUMWAITERR;            /* Variable: NUMWAITERR
                                        * Referenced by: '<S87>/Chart'
                                        * Time Force TC Recovery
                                        */
extern uint16_T TIMSPEXIT;             /* Variable: TIMSPEXIT
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Timeout to exit by SPRING_UP in right conditions
                                        */
extern int8_T ENTCINTTR;               /* Variable: ENTCINTTR
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Enable TC Int mem transition
                                        */
extern uint8_T CMIIDBTCACC;            /* Variable: CMIIDBTCACC
                                        * Referenced by: '<S17>/CMIIDBTCACC'
                                        * Dead-band on CmiDriverI to exit TC_ACC
                                        */
extern uint8_T CMIIDBTCSMOOTH;         /* Variable: CMIIDBTCSMOOTH
                                        * Referenced by: '<S78>/CMIIDBTCSMOOTH'
                                        * Dead-band on CmiDriverI to exit TC_SMOOTH
                                        */
extern uint8_T CMIPDBTCACC;            /* Variable: CMIPDBTCACC
                                        * Referenced by: '<S17>/CMIPDBTCACC'
                                        * Dead-band on CmiDriverI to exit TC_ACC
                                        */
extern uint8_T CMIPDBTCSMOOTH;         /* Variable: CMIPDBTCSMOOTH
                                        * Referenced by: '<S78>/CMIPDBTCSMOOTH'
                                        * Dead-band on CmiDriverP to exit TC_SMOOTH
                                        */
extern uint8_T VTDELTATCSMOOTHDVS[7];  /* Variable: VTDELTATCSMOOTHDVS
                                        * Referenced by: '<S81>/VTDELTATCSMOOTHDVS'
                                        * Smooth step
                                        */
extern uint8_T TBSMOOTHGAIN[21];       /* Variable: TBSMOOTHGAIN
                                        * Referenced by: '<S81>/TBSMOOTHGAIN'
                                        * Torque correction during smooth as function of RollAngle and AccRearInt
                                        */
extern uint8_T CNTDISTRAC;             /* Variable: CNTDISTRAC
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Task to disable traction control
                                        */
extern uint8_T CNTDISTRAC2;            /* Variable: CNTDISTRAC2
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Task to disable traction control 2
                                        */
extern uint8_T DISTCCLUTCH;            /* Variable: DISTCCLUTCH
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Clutch deactive function
                                        */
extern uint8_T ENTRACCTRL;             /* Variable: ENTRACCTRL
                                        * Referenced by:
                                        *   '<S3>/fc_Reset'
                                        *   '<S9>/Calc_TracCtrl_DVS'
                                        * Traction control active
                                        */
extern uint8_T KPTRACLEVIDX;           /* Variable: KPTRACLEVIDX
                                        * Referenced by:
                                        *   '<S64>/KPTRACLEVIDX'
                                        *   '<S65>/KPTRACLEVIDX'
                                        * set level
                                        */
extern uint8_T SELACCDVSPDFRZ;         /* Variable: SELACCDVSPDFRZ
                                        * Referenced by: '<S44>/SELACCDVSPDFRZ'
                                        * Select rate mode
                                        */
extern uint8_T SELCNTDISTRAC;          /* Variable: SELCNTDISTRAC
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Select blanking time by TC Level
                                        */
extern uint8_T SELTCDELTCME;           /* Variable: SELTCDELTCME
                                        * Referenced by: '<S11>/SELTCDELTCME'
                                        * Delta selector
                                        */
extern uint8_T SELTHSMOOTHEXIT;        /* Variable: SELTHSMOOTHEXIT
                                        * Referenced by: '<S20>/SELTHSMOOTHEXIT'
                                        * Select Exit smooth threshold
                                        */
extern uint8_T SETTRACCTRL1;           /* Variable: SETTRACCTRL1
                                        * Referenced by: '<S22>/SETTRACCTRL1'
                                        * If SetTracCtrl value is from 1 to SETTRACCTRL1 select the 1st row of TBACCWHEELDB
                                        */
extern uint8_T SETTRACCTRL2;           /* Variable: SETTRACCTRL2
                                        * Referenced by: '<S22>/SETTRACCTRL2'
                                        * If SetTracCtrl value is from (SETTRACCTRL1+1) to SETTRACCTRL2 select the 2nd row of TBACCWHEELDB if greater than SETTRACCTRL2 selects the 3rd row
                                        */
extern uint8_T TBIDXTCDVSCUTOFF[15];   /* Variable: TBIDXTCDVSCUTOFF
                                        * Referenced by: '<S23>/TBIDXTCDVSCUTOFF'
                                        * Index to select Tc CutOff pattern
                                        */
extern uint8_T TBIDXTCDVSCUTOFFWET[15];/* Variable: TBIDXTCDVSCUTOFFWET
                                        * Referenced by: '<S23>/TBIDXTCDVSCUTOFFWET'
                                        * Index to select Tc CutOff pattern
                                        */
extern uint8_T TIMSPRINGUP;            /* Variable: TIMSPRINGUP
                                        * Referenced by: '<S9>/Calc_TracCtrl_DVS'
                                        * Time to observe and enable TrqCtrl in SPRING_UP
                                        */
extern uint8_T VTIDXDVSMODEL[3];       /* Variable: VTIDXDVSMODEL
                                        * Referenced by: '<S29>/VTIDXDVSMODEL'
                                        * Enable
                                        */
extern uint8_T VTIDXOFFCMITRACP[3];    /* Variable: VTIDXOFFCMITRACP
                                        * Referenced by: '<S27>/VTIDXOFFCMITRACP'
                                        * Enable
                                        */
extern uint8_T VTIDXTCCUTOFFSEL[3];    /* Variable: VTIDXTCCUTOFFSEL
                                        * Referenced by: '<S23>/VTIDXTCCUTOFFSEL'
                                        * selector
                                        */
extern void TracCtrl_fc_TC_AccWheelCalc(int16_T rtu_CmeEstWheelF, int16_T
  rtu_AccDeltaVehSpeed, uint8_T rtu_SetTracCtrl, int16_T rtu_RollCAN, uint16_T
  rtu_Rpm, uint16_T rtu_VehSpeed, int16_T rtu_DeltaVehSpeed, int16_T
  rtu_DeltaCmeEstWheelF, uint8_T rtu_FlgYawRec, uint8_T rtu_flg_reset_filt,
  rtB_fc_TC_AccWheelCalc_TracCtrl *localB, const rtC_fc_TC_AccWheelCalc_TracCtrl
  *localC, rtDW_fc_TC_AccWheelCalc_TracCtr *localDW, uint16_T *rtd_index_RollCAN,
  uint16_T *rtd_ratio_RollCAN);
extern void TracCtrl_Prop_Acc_To_Smooth(int32_T rtu_TCPropPTerm_in,
  rtB_Prop_Acc_To_Smooth_TracCtrl *localB, rtDW_Prop_Acc_To_Smooth_TracCtr
  *localDW);
extern void TracCtrl_fc_TC_CmiControl(int16_T rtu_AccDVSpdCtrlFMem, int16_T
  rtu_DVSCtrlErr, uint8_T rtu_localgear, uint16_T rtu_IdTracCtrlRid,
  rtB_fc_TC_CmiControl_TracCtrl *localB, rtDW_fc_TC_CmiControl_TracCtrl *localDW,
  uint16_T *rtd_index_RollCAN, uint16_T *rtd_ratio_RollCAN);
extern void TracCtrl_FunctionCallSubsystem(uint8_T rtu_reset, int16_T
  rtu_DeltaVehSpeed, int16_T rtu_AccDeltaVehSpeed,
  rtB_FunctionCallSubsystem_TracC *localB, rtDW_FunctionCallSubsystem_Trac
  *localDW);
extern void TracCtrl_fc_TC_Assign(void);
extern void TracCtrl_fc_TC_Limit(void);
extern void TracCtrl_fc_TC_NoLimit(void);
extern void TracCtrl_fc_TC_Smooth(void);
extern void TracCtrl_fc_TC_GrUp_Filt_Assign(void);
extern void TracCtrl_T10ms(void);
extern void TracCtrl_Reset(void);
extern void TracCtrl_PreTDC(void);

#endif                                 /* RTW_HEADER_TracCtrl_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
