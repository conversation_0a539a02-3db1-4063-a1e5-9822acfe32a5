/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _DIGITALIN_H_
#define _DIGITALIN_H_

/** include files **/
#include "typedefs.h"
#include "analog_QS.h"

/** local definitions **/
#if ((BOARD_TYPE == BOARD_5) || (BOARD_TYPE == BOARD_M1) || (BOARD_TYPE == BOARD_M2) || (BOARD_TYPE == BOARD_M3))
#undef USE_KEYSIGNAL2
#else
#error Board not supported!!
#endif

#if (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_08) || (ENGINE_TYPE==MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_30) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
#define IDN_FBK_REAR_POS IDE_Rear_stop
#define IDN_KEYSENSE    IDE_KeySense_D
#define T_VAL_KEYOFF    20
#define KEYSNS_PULL     DISABLE_PULL
#define CLUTCH_ENGAGED      1
#define CLUTCH_DISENGAGED   0

#elif (ENGINE_TYPE==PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID)

#define IDN_KEYSENSE    KeySense_D
#define T_VAL_KEYOFF    20
#define KEYSNS_PULL     DISABLE_PULL
//#define IDN_CRASHSW     IN_CrashSW_1
//#define   IDN_STARTSW       IN_StartSW_1
#define     IDN_STOPSW          IN_StopSW_1   
//#define   IDN_TRESTLE       IN_TrestleSW_1
//#define   IDN_BRAKE           IN_BrakePos_1
//#define IDN_CLUTCH          IN_ClutchPos_1
#define IDN_TACH_CONS   OPE_TachCons

#else
#error ATTENZIONE: Combinazione proibita!!!
#endif

#define ANDIGLOTH    287  /* 1024/5000 * 1400 */
#define ANDIGHITH    758  /* 1024/5000 * 3700 */

#define ANDIGLOTHLC4 614  /* 1024/5000 * 3000 */
#define ANDIGHITHLC4 922  /* 1024/5000 * 4500 */

#define ANDIGLOTHRB3 614  /* 1024/5000 * 3000 */
#define ANDIGHITHRB3 922  /* 1024/5000 * 4500 */

#define ANDIAGLOW    41   /* 1024/5000 * 200 */
#define ANDIAGHIGH   983  /* 1024/5000 * 4800 */

#define ANDIGLOTH_IGN   717  /* 1024/5000 * 3500 */
#define ANDIGHITH_IGN   717  /* 1024/5000 * 3500 */

#define ANDIAGLOW_BRAKELAMP    0
#define ANDIAGHIGH_BRAKELAMP   1024

#define ANDIAGLOW_GEARSHIFT    0
#define ANDIAGHIGH_GEARSHIFT   1024

#define ANDIAGLOW_RUNSTOP    0
#define ANDIAGHIGH_RUNSTOP   1024

#define ANDIAGLOW_SIDESTAND    0
#define ANDIAGHIGH_SIDESTAND   1024

#define ANDIAGLOW_SIDESTAND    0
#define ANDIAGHIGH_SIDESTAND   1024

#define ANDIAGLOW_STARTSW    0
#define ANDIAGHIGH_STARTSW   1024

#define ANDIAGLOW_CLUTCH    0
#define ANDIAGHIGH_CLUTCH   1024

#define ANDIAGLOW_IGN1    0
#define ANDIAGHIGH_IGN1   1024

#define ANDIAGLOW_IGN2    0
#define ANDIAGHIGH_IGN2   1024

#define ANDIAGLOW_IGN3    0
#define ANDIAGHIGH_IGN3   1024

#define ANDIAGLOW_IGN4    0
#define ANDIAGHIGH_IGN4   1024

#define ANDIAGLOW_BRAKESW    0
#define ANDIAGHIGH_BRAKESW   1024


/* EEFlgsID7 Mask */
#define FLG_ID_7_RES_QS_PRES 0xFFFFFFFC
#define FLG_ID_7_SET_QS_PRES 0x00000001
#define FLG_ID_7_DIS_QS_PRES 0x00000002
#define FLG_ID_7_GET_QS_PRES 0x00000003

#define FLG_ID_7_RES_DUMMY_1 0xFFFFFFFB
#define FLG_ID_7_SET_DUMMY_1 0x00000004

#define FLG_ID_7_RES_DUMMY_2 0xFFFFFFF7
#define FLG_ID_7_SET_DUMMY_2 0x00000008

// And so on....
/* End EEFlgsID7 Mask */


/** default settings **/

/** external functions **/

/** external data **/
extern uint8_T  KeySignal2;
extern uint8_T  KeySignal1;
extern uint8_T  KeySignal;
extern uint8_T  KeySig4Coil;
extern uint8_T  KeyEnDiagSignal;
extern uint8_T  BrakeSignal;
extern uint8_T  OilPresSignal;
extern uint8_T  DiagBrakeLamp;
extern uint8_T  ClutchSignal;
extern uint8_T  ClutchSignal2;
extern uint8_T  CrashSignal;
extern uint8_T  StartSignalIn;
extern uint8_T  StartSwitch;
extern uint8_T  StopSignal;
extern uint8_T  TrestleSignal;
extern uint8_T  GearUpSignal;
extern uint8_T  GearDownSignal;
extern uint16_T VCrashSignal;
extern uint8_T  ParkBrakeSignal;
extern uint8_T  RiderPresence;
extern int32_T  EEFlgsID7;
extern uint8_T  FlgEnQs;

/** public data **/

/** public functions **/
void    Reset_DigInSignals(void);
void    DigitalIn_Init(void);
void    DigitalIn_T5ms(void);
void    DigitalIn_T10ms(void);
void    DigitalIn_CoilDiag(void);

/** private functions **/

#endif
