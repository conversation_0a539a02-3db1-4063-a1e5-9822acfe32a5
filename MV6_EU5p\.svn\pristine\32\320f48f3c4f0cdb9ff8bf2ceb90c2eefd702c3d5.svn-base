/*
 * File: ExhValMgm.h
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1587
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 18 16:31:59 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#ifndef RTW_HEADER_ExhValMgm_h_
#define RTW_HEADER_ExhValMgm_h_
#include <string.h>
#ifndef ExhValMgm_COMMON_INCLUDES_
# define ExhValMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#endif                                 /* ExhValMgm_COMMON_INCLUDES_ */

#include "ExhValMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "exhval_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKEXHVALCME_dim                4U                        /* Referenced by:
                                                                  * '<S71>/BKEXHVALCME_dim'
                                                                  * '<S21>/BKEXHVALCME_dim'
                                                                  */

/* Dim of BKEXHVALCME */
#define BKEXHVALRPM_dim                5U                        /* Referenced by:
                                                                  * '<S71>/BKEXHVALRPM_dim'
                                                                  * '<S21>/BKEXHVALRPM_dim'
                                                                  */

/* Dim of BKEXHVALRPM */
#define BKEXHVALTIME_dim               3U                        /* Referenced by:
                                                                  * '<S66>/BKEXHVALTIME_dim'
                                                                  * '<S72>/BKEXHVALTIME_dim'
                                                                  */

/* Dim of BKEXHVALTIME */
#define BKMAXVEXHOUTOPEN_dim           2U                        /* Referenced by:
                                                                  * '<S122>/BKMAXVEXHOUTOPEN_dim'
                                                                  * '<S20>/BKMAXVEXHOUTOPEN_dim'
                                                                  */

/* Dim of BKMAXVEXHOUTOPEN */
#define ID_EXHVALVE_MGM                19439760U                 /* Referenced by: '<S3>/ID_EXHVALVE_MGM' */

/* mask */
#define SELF_HOLD_CLOSED               4U                        /* Referenced by: '<S8>/Self_Machine' */

/* status of self-learning of LMS hold */
#define SELF_HOLD_OPEN                 6U                        /* Referenced by: '<S8>/Self_Machine' */

/* status of self-learning UMS hold */
#define SELF_IDLE                      1U                        /* Referenced by: '<S8>/Self_Machine' */

/* idle status of self-learning */
#define SELF_INIT                      0U                        /* Referenced by: '<S8>/Self_Machine' */

/* init status of self-learning */
#define SELF_PEAK_CLOSED               3U                        /* Referenced by: '<S8>/Self_Machine' */

/* status of self-learning of LMS peak */
#define SELF_PEAK_OPEN                 5U                        /* Referenced by: '<S8>/Self_Machine' */

/* status of self-learning UMS peak */
#define SELF_WAIT_MOVE                 2U                        /* Referenced by: '<S8>/Self_Machine' */

/* wait status of self-learning */
#define TH_ADFB_VPIDINACTIVE           0U                        /* Referenced by: '<S11>/Dirive_ZeroPosition' */

/* Voltage of VPID to validate zero position */
#define TIM_LMS_EN                     4U                        /* Referenced by: '<S8>/Self_Machine' */

/* wait Enable of LMS */
#define TIM_UMS_EN                     4U                        /* Referenced by: '<S8>/Self_Machine' */

/* wait Enable of UMS */
#define V_EXHV_WAIT                    102                       /* Referenced by: '<S8>/Self_Machine' */

/* Voltage low wait */

/* Block signals and states (default storage) for system '<S18>/fc_Calc_MinMax' */
typedef struct {
  int16_T MinMax;                      /* '<S24>/MinMax' */
  int16_T MinMax1;                     /* '<S24>/MinMax1' */
  int16_T MinMax_k30;                  /* '<S25>/MinMax' */
  int16_T MinMax1_a11;                 /* '<S25>/MinMax1' */
  int16_T Memory_PreviousInput;        /* '<S24>/Memory' */
  int16_T Memory1_PreviousInput;       /* '<S24>/Memory1' */
  int16_T Memory_PreviousInput_kjb;    /* '<S25>/Memory' */
  int16_T Memory1_PreviousInput_puf;   /* '<S25>/Memory1' */
} DW_fc_Calc_MinMax_ExhValMgm_T;

/* Block signals and states (default storage) for system '<S31>/fc_ExhVSelfSteadySt' */
typedef struct {
  uint16_T Memory_PreviousInput;       /* '<S64>/Memory' */
  uint16_T Memory1_PreviousInput;      /* '<S64>/Memory1' */
  uint8_T Memory_PreviousInput_h5f;    /* '<S34>/Memory' */
} DW_fc_ExhVSelfSteadySt_ExhVal_T;

/* Block signals and states (default storage) for system '<S31>/fc_EvaluateSelf' */
typedef struct {
  uint32_T Memory_PreviousInput;       /* '<S42>/Memory' */
  boolean_T Memory_PreviousInput_gan;  /* '<S58>/Memory' */
  boolean_T Memory_PreviousInput_jbu;  /* '<S46>/Memory' */
  boolean_T Memory_PreviousInput_poe;  /* '<S59>/Memory' */
  boolean_T Memory_PreviousInput_lvt;  /* '<S47>/Memory' */
  boolean_T Memory2_PreviousInput;     /* '<S43>/Memory2' */
  boolean_T Memory1_PreviousInput;     /* '<S43>/Memory1' */
} DW_fc_EvaluateSelf_ExhValMgm_T;

/* Block signals and states (default storage) for system '<S10>/fc_diag_calc' */
typedef struct {
  uint8_T Memory_PreviousInput;        /* '<S90>/Memory' */
} DW_fc_diag_calc_ExhValMgm_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  DW_fc_diag_calc_ExhValMgm_T fc_diag_calc;/* '<S10>/fc_diag_calc' */
  DW_fc_EvaluateSelf_ExhValMgm_T fc_EvaluateSelf;/* '<S31>/fc_EvaluateSelf' */
  DW_fc_ExhVSelfSteadySt_ExhVal_T fc_ExhVSelfSteadySt;/* '<S31>/fc_ExhVSelfSteadySt' */
  DW_fc_Calc_MinMax_ExhValMgm_T fc_Calc_MinMax;/* '<S18>/fc_Calc_MinMax' */
  int32_T UnitDelay3_DSTATE;           /* '<S16>/Unit Delay3' */
  int32_T Mem_PreviousInput;           /* '<S121>/Mem' */
  uint32_T Memory_PreviousInput;       /* '<S74>/Memory' */
  uint32_T Memory_PreviousInput_do4;   /* '<S72>/Memory' */
  int16_T VOutSelf;                    /* '<S8>/Self_Machine' */
  int16_T UnitDelay1_DSTATE;           /* '<S70>/Unit Delay1' */
  int16_T UnitDelay3_DSTATE_oui;       /* '<S70>/Unit Delay3' */
  int16_T Memory2_PreviousInput;       /* '<S79>/Memory2' */
  int16_T Memory1_PreviousInput;       /* '<S79>/Memory1' */
  int16_T Memory_PreviousInput_f5m;    /* '<S79>/Memory' */
  int16_T Mem_PreviousInput_ixw;       /* '<S123>/Mem' */
  uint16_T Memory_PreviousInput_pjn;   /* '<S98>/Memory' */
  uint16_T cntExhVSafePos;             /* '<S135>/SafePosition' */
  uint16_T cntDisExhV;                 /* '<S135>/RecExhValve' */
  uint16_T cntEnExhVMotion;            /* '<S135>/RecExhValve' */
  uint16_T cntZeroFind;                /* '<S11>/Dirive_ZeroPosition' */
  uint16_T cntZeroPosTO;               /* '<S11>/Dirive_ZeroPosition' */
  uint16_T cntDrvEn;                   /* '<S8>/Self_Machine' */
  uint8_T enableExhVFdbk;              /* '<S135>/SafePosition' */
  uint8_T enableEhxV;                  /* '<S135>/RecExhValve' */
  uint8_T FlgExVManPos;                /* '<S94>/Calc_StFoExVSL' */
  uint8_T FlgExVManSelf;               /* '<S94>/Calc_StFoExVSL' */
  uint8_T FlgExVPWLamp_fgf;            /* '<S11>/Dirive_ZeroPosition' */
  uint8_T FlgExhVZeroPos_gpr;          /* '<S11>/Dirive_ZeroPosition' */
  uint8_T ptFault;                     /* '<S10>/Diag_Position' */
  uint8_T DiagMgm_SetDiagState;        /* '<S91>/DiagMgm_SetDiagState' */
  uint8_T Add;                         /* '<S90>/Add' */
  uint8_T flgPbyRate;                  /* '<S9>/PbyRateSel' */
  uint8_T flgZeroTarget;               /* '<S9>/PbyRateSel' */
  uint8_T resetStab;                   /* '<S8>/Self_Machine' */
  uint8_T MinMax;                      /* '<S41>/MinMax' */
  uint8_T MinMax1;                     /* '<S41>/MinMax1' */
  uint8_T UnitDelay_DSTATE;            /* '<S111>/Unit Delay' */
  uint8_T Memory1_PreviousInput_pyc;   /* '<S28>/Memory1' */
  uint8_T is_active_c2_ExhValMgm;      /* '<S135>/RecExhValve' */
  uint8_T is_c2_ExhValMgm;             /* '<S135>/RecExhValve' */
  uint8_T is_active_c8_ExhValMgm;      /* '<S94>/Calc_StFoExVSL' */
  uint8_T is_c8_ExhValMgm;             /* '<S94>/Calc_StFoExVSL' */
  uint8_T is_active_c5_ExhValMgm;      /* '<S11>/Dirive_ZeroPosition' */
  uint8_T is_c5_ExhValMgm;             /* '<S11>/Dirive_ZeroPosition' */
  uint8_T is_active_c10_ExhValMgm;     /* '<S10>/Diag_Position' */
  uint8_T is_c10_ExhValMgm;            /* '<S10>/Diag_Position' */
  uint8_T is_active_c9_ExhValMgm;      /* '<S10>/Calc_FlgExhVDiagOn' */
  uint8_T is_c9_ExhValMgm;             /* '<S10>/Calc_FlgExhVDiagOn' */
  uint8_T oldCntDiagCall;              /* '<S10>/Calc_FlgExhVDiagOn' */
  uint8_T oldDrivingCycle;             /* '<S10>/Calc_FlgExhVDiagOn' */
  uint8_T is_active_c7_ExhValMgm;      /* '<S9>/PbyRateSel' */
  uint8_T is_c7_ExhValMgm;             /* '<S9>/PbyRateSel' */
  uint8_T is_active_c3_ExhValMgm;      /* '<S8>/Self_Machine' */
  uint8_T is_c3_ExhValMgm;             /* '<S8>/Self_Machine' */
  uint8_T is_SELF_TO_CLOSED;           /* '<S8>/Self_Machine' */
  uint8_T is_SELF_TO_OPEN;             /* '<S8>/Self_Machine' */
  uint8_T oldCntReqExhSelf;            /* '<S8>/Self_Machine' */
  uint8_T is_active_c6_ExhValMgm;      /* '<S18>/Manage_MinMax_Trg' */
  uint8_T oldTrigExhVMinMaxTrg;        /* '<S18>/Manage_MinMax_Trg' */
  boolean_T LogicalOperator_hoi;       /* '<S43>/Logical Operator' */
  boolean_T Memory_PreviousInput_jyx;  /* '<S28>/Memory' */
} DW_ExhValMgm_T;

/* Invariant block signals (default storage) */
typedef struct {
  const uint8_T Constant;              /* '<S139>/Constant' */
} ConstB_ExhValMgm_T;

/* External outputs (root outports fed by signals with default storage) */
typedef struct {
  uint8_T BUS_TP;                      /* '<Root>/BUS_TP' */
} ExtY_ExhValMgm_T;

/* Block signals and states (default storage) */
extern DW_ExhValMgm_T ExhValMgm_DW;

/* External outputs (root outports fed by signals with default storage) */
extern ExtY_ExhValMgm_T ExhValMgm_Y;
extern const ConstB_ExhValMgm_T ExhValMgm_ConstB;/* constant block i/o */

/* Model entry point functions */
extern void ExhValMgm_initialize(void);

/* Exported entry point function */
extern void Trig_ExhValMgm_NoSync(void);

/* Exported entry point function */
extern void Trig_ExhValMgm_Init(void);

/* Exported entry point function */
extern void Trig_ExhValMgm_T10ms(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ExhValMgm'
 * '<S1>'   : 'ExhValMgm/ExhValMgm'
 * '<S2>'   : 'ExhValMgm/Model Info'
 * '<S3>'   : 'ExhValMgm/ExhValMgm/Init'
 * '<S4>'   : 'ExhValMgm/ExhValMgm/Merge'
 * '<S5>'   : 'ExhValMgm/ExhValMgm/T10ms'
 * '<S6>'   : 'ExhValMgm/ExhValMgm/TP'
 * '<S7>'   : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position'
 * '<S8>'   : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self'
 * '<S9>'   : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target'
 * '<S10>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position'
 * '<S11>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag'
 * '<S12>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB'
 * '<S13>'  : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator'
 * '<S14>'  : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve'
 * '<S15>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle'
 * '<S16>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/VAngExhValvFilter'
 * '<S17>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Calc_Perc'
 * '<S18>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator'
 * '<S19>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Manage_MinMax_Trg'
 * '<S20>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Subsystem'
 * '<S21>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/fc_Calc_MinMax'
 * '<S22>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Subsystem/Saturation Dynamic'
 * '<S23>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/fc_Calc_MinMax/TBEXHVALANGTGT_Min_Max'
 * '<S24>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/fc_Calc_MinMax/VTEXHVANGTGTIDLE_Min_Max'
 * '<S25>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/fc_Calc_MinMax/TBEXHVALANGTGT_Min_Max/Subsystem'
 * '<S26>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/VAngExhValvFilter/FOF_Reset_S16_FXP_1'
 * '<S27>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Position/VAngExhValvFilter/FOF_Reset_S16_FXP_1/Data Type Conversion Inherited1'
 * '<S28>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/FOEHXSELF'
 * '<S29>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/Self_Machine'
 * '<S30>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/Signal_Correct_End_Procedure'
 * '<S31>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal'
 * '<S32>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/FOEHXSELF/Compare To Zero'
 * '<S33>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf'
 * '<S34>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_ExhVSelfSteadySt'
 * '<S35>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_IncCntExhVTrip'
 * '<S36>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_ResetCntExhVTrip'
 * '<S37>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_ResetLMSOnce'
 * '<S38>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_ResetUMSOnce'
 * '<S39>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_WriteExhVClose'
 * '<S40>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_WriteExhVOpen'
 * '<S41>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition'
 * '<S42>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Common_Comd'
 * '<S43>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once'
 * '<S44>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip'
 * '<S45>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Common_Comd/Compare To Zero1'
 * '<S46>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_LMSOnce'
 * '<S47>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_UMSOnce'
 * '<S48>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Compare To Constant'
 * '<S49>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Compare To Constant1'
 * '<S50>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_LMSOnce/Compare To Zero1'
 * '<S51>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_LMSOnce/Compare To Zero2'
 * '<S52>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_LMSOnce/Compare To Zero3'
 * '<S53>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_LMSOnce/Compare To Zero6'
 * '<S54>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_UMSOnce/Compare To Zero1'
 * '<S55>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_UMSOnce/Compare To Zero2'
 * '<S56>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_UMSOnce/Compare To Zero3'
 * '<S57>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Once/Calc_UMSOnce/Compare To Zero7'
 * '<S58>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip/Calc_LMSTrip'
 * '<S59>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip/Calc_UMSTrip'
 * '<S60>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip/Calc_LMSTrip/Compare To Zero'
 * '<S61>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip/Calc_LMSTrip/Compare To Zero2'
 * '<S62>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip/Calc_UMSTrip/Compare To Zero3'
 * '<S63>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_EvaluateSelf/Self_Condition/Self_Trip/Calc_UMSTrip/Compare To Zero4'
 * '<S64>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Self/fc_Cal/fc_ExhVSelfSteadySt/Signal_Stability'
 * '<S65>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/ActiveDiag_ZeroPosition_Trg'
 * '<S66>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory'
 * '<S67>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Force_Target'
 * '<S68>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Idle_Switch'
 * '<S69>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/PbyRateSel'
 * '<S70>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target'
 * '<S71>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target'
 * '<S72>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/Calc_ExhvalTime'
 * '<S73>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/Compare To Constant'
 * '<S74>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/ExhVal_TimingModule'
 * '<S75>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/LookUp_S16_U16'
 * '<S76>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Calc_TimeHistory/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S77>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Idle_Switch/Compare To Constant'
 * '<S78>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Idle_Switch/Compare To Constant1'
 * '<S79>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/Filter_Target'
 * '<S80>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_1'
 * '<S81>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_2'
 * '<S82>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_1/Data Type Conversion Inherited1'
 * '<S83>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Rate_Target/RateLimiter_S16_2/Data Type Conversion Inherited1'
 * '<S84>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/Look2D_S16_S16_U16_1'
 * '<S85>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/LookUp_S16_U16_1'
 * '<S86>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/Look2D_S16_S16_U16_1/Data Type Conversion Inherited1'
 * '<S87>'  : 'ExhValMgm/ExhValMgm/T10ms/Calc_Target/Selec_Target/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S88>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn'
 * '<S89>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Position'
 * '<S90>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/fc_diag_calc'
 * '<S91>'  : 'ExhValMgm/ExhValMgm/T10ms/Diag_ExhValve_Position/fc_diag_calc/SetDiagState'
 * '<S92>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Active_Moving'
 * '<S93>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Dirive_ZeroPosition'
 * '<S94>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure'
 * '<S95>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Active_Moving/Compare To Zero'
 * '<S96>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Calc_StFoExVSL'
 * '<S97>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Compare To Constant'
 * '<S98>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/En_Manual_Cond'
 * '<S99>'  : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/En_Manual_Cond/Compare To Constant1'
 * '<S100>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/En_Manual_Cond/Compare To Constant2'
 * '<S101>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/En_Manual_Cond/Compare To Constant3'
 * '<S102>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/En_Diag'
 * '<S103>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver'
 * '<S104>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/En_Diag/Compare To Zero5'
 * '<S105>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver/Compare To Zero1'
 * '<S106>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver/Compare To Zero2'
 * '<S107>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver/Compare To Zero5'
 * '<S108>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver/else'
 * '<S109>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver/if'
 * '<S110>' : 'ExhValMgm/ExhValMgm/T10ms/Drive_HB/Enable_Driver/if/Compare To Zero5'
 * '<S111>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Calc_Error'
 * '<S112>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI'
 * '<S113>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI'
 * '<S114>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/Compare To Zero2'
 * '<S115>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/Compare To Zero3'
 * '<S116>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/else'
 * '<S117>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/if'
 * '<S118>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/else/Compare To Constant'
 * '<S119>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/if/Compare To Constant'
 * '<S120>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/Enable_Reset_PI/if/Compare To Zero5'
 * '<S121>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Reset_PI'
 * '<S122>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator'
 * '<S123>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Subsystem'
 * '<S124>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Reset_PI/Compare To Zero'
 * '<S125>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator/LookUp_U16_S16_1'
 * '<S126>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator/LookUp_U16_S16_2'
 * '<S127>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator/Subsystem'
 * '<S128>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator/LookUp_U16_S16_1/Data Type Conversion Inherited3'
 * '<S129>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator/LookUp_U16_S16_2/Data Type Conversion Inherited3'
 * '<S130>' : 'ExhValMgm/ExhValMgm/T10ms/PI_Regulator/PI/Saturator/Subsystem/Saturation Dynamic'
 * '<S131>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Compare To Constant'
 * '<S132>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Compare To Zero1'
 * '<S133>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Compare To Zero2'
 * '<S134>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Compare To Zero3'
 * '<S135>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve'
 * '<S136>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Diag_Vehicle'
 * '<S137>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/RecExhValve'
 * '<S138>' : 'ExhValMgm/ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/SafePosition'
 * '<S139>' : 'ExhValMgm/ExhValMgm/TP/TP_OUT'
 */

/*-
 * Requirements for '<Root>': ExhValMgm
 */
#endif                                 /* RTW_HEADER_ExhValMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
