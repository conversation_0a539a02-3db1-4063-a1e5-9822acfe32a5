#ifndef __SHAREDDEF_H
#define __SHAREDDEF_H

/*----------------------------------------------------------------+
|                       Constants Definition                      |
+-----------------------------------------------------------------*/
/* Function Mode Definitions */
#define ETPU_COUNT_MODE_SINGLE      1
#define ETPU_COUNT_MODE_CONT        0
#define ETPU_COUNT_MODE_LINK       (1 << 1)

#define N_IGNORED_TEETH 3

/* other defs*/
#define ETPU_uint unsigned int
#define ETPU_int int
#define TCR1TICKS2US            1       //TCR1 tick duration in us

/*----------------------------------------------------------------*/


/*                flags array                  */
#define EXCEPTIONS_ENABLED   0x00800000     // exception mask
#define POLARITY_MASK        0x00400000     // polarity mask
#define INIT_STATUS_MASK     0x00000020     // init status mask
#define LINK_MASK            0x00100000     // Linking status mask

//#define PIN_SPARK_ON_MASK    0x00200000     // spark on mask
//#define PIN_SPARK_OFF_MASK   0x00100000     // spark off mask




/******************************************************/
/******************************************************/
/**               ETPU Functions defs                **/
/******************************************************/
/******************************************************/

/******************************************************/
/*                angleClock function                 */
/******************************************************/
/* transitions defines */
#define TRANS_HIGH_LOW      0
#define TRANS_LOW_HIGH      1
#define TRANS_BOTH          2

/* exTypes Values */
#define EX_NONE             0x00        // No exception
#define EX_SYNC             0x01        // Synchronization exception
#define EX_NO_SYNC          0x02        // No Synchronization exception
#define EX_TOOTH            0x04        // Tooth exception


/* AngleStates Values */
#define AS_INVALID                          0  /* 0  as_INVALID */
#define AS_INITIALIZE_CRANK                 1  /* 1  InitializeCrank */
#define AS_FIRST_EDGE                       2  /* 2  First_edge */
#define AS_SKIPPING_EDGES                   3  /* 3  Skipping first edges */
#define AS_FIRST_PERIOD                     4  /* 4  FirstPeriod */
#define AS_TESTING_POSSIBLE_GAP             5  /* 5  Apparent_Gap */
#define AS_GAP_MISSED                       6  /* 6  PossibleGapVerifying */
#define AS_GAP_VERIFIED                     7  /* 7  GapVerified */
#define AS_COUNTING                         8  /* 8  Counting */
#define AS_NEW_REV                          9  /* 9  NewRev */
#define AS_GAPPING                          10 /* 10 Gapping */
#define AS_ADJUST_ANGLE                     11 /* 11 AdjustAngle */
#define AS_CAM_DETECTED                     12 /* 12 Cam_Detected */
#define AS_ERROR_INVALID_STATE              13 /* 13 as_ERROR_INVALID_STATE */
#define AS_RECOVERY_MODE                    14 /* 14 Recovery Mode */

/* Crank Status Values */
#define STALL             0     // Stall
#define WAITING_GAP     1       // Waiting_Gap
#define HALF_SYNC         2     // Half_Sync
#define FULL_SYNC         3     // Full_Sync
#define LOST_SYNC     4     // Lost sync
#define ERROR               5       // Error

/* Host Service Requests */
#define HSR_INIT_ANGLE_CLOCK_VAL        1   // hsrInitAngleClockVal
#define HSR_ADJUST_ANGLE_VAL              2     // hsrAdjustAngleClockVal
#define HSR_RESYNCH_ANGLE_CLOCK_VAL   3     // hsrResynchAngleClockVal
#define HSR_GET_CRANK_ANGLE_VAL         4       //
#define HSR_SHUTDOWN_ANGLE_CLOCK_VAL    5   //
#define HSR_REPHASE_ANGLE_CLOCK_VAL   6

#define HSR_INIT_ANGLE_CLOCK        (hsr == HSR_INIT_ANGLE_CLOCK_VAL)
#define HSR_ADJUST_ANGLE_CLOCK      (hsr == HSR_ADJUST_ANGLE_VAL)
#define HSR_RESYNCH_ANGLE_CLOCK     (hsr == HSR_RESYNCH_ANGLE_CLOCK_VAL)
#define HSR_GET_CRANK_ANGLE         (hsr == HSR_GET_CRANK_ANGLE_VAL)
#define HSR_SHUTDOWN_ANGLE_CLOCK    (hsr == HSR_SHUTDOWN_ANGLE_CLOCK_VAL)
#define HSR_REPHASE_ANGLE_CLOCK     (hsr == HSR_REPHASE_ANGLE_CLOCK_VAL)

/******************************************************/
/*            angleExGenerator function               */
/******************************************************/
/* exTypes Values */
#define EX_ANGLE            0x08   // Angle exception
/* Host Service Requests */
#define HSR_INIT_ANGLE_EX_GENERATOR_VAL     1
#define HSR_DISABLE_ANGLE_EX_GENERATOR_VAL     2

#define HSR_INIT_ANGLE_EX_GENERATOR    (hsr == HSR_INIT_ANGLE_EX_GENERATOR_VAL)
#define HSR_DISABLE_ANGLE_EX_GENERATOR (hsr == HSR_DISABLE_ANGLE_EX_GENERATOR_VAL)

/******************************************************/
/*              SparkHandler function                 */
/******************************************************/
/* exSparkType Values */
#define EX_SPARK_ON         0x01
#define EX_SPARK_OFF        0x02

/* Host Service Requests */
#define HSR_INIT_SPARK_HANDLER_VAL      1       // hsrInitAngleClockVal

#define HSR_INIT_SPARK_HANDLER      (hsr == HSR_INIT_SPARK_HANDLER_VAL)       // hsrInitAngleClockVal

/******************************************************/
/*              PWM function                 */
/******************************************************/
/* PWM Values */

/* Host Service Requests */
#define HSR_INIT_PWM_VAL        1       //
#define HSR_DISABLE_PWM_VAL     2       //

#define HSR_INIT_PWM        (hsr == HSR_INIT_PWM_VAL)
#define HSR_DISABLE_PWM     (hsr == HSR_DISABLE_PWM_VAL)


/* Host Service Requests */
#define HSR_INIT_ADCTRIGGER_VAL        1       //
#define HSR_DISABLE_ADCTRIGGER_VAL     2       //

#define HSR_INIT_ADCTRIGGER        (hsr == HSR_INIT_ADCTRIGGER_VAL)
#define HSR_DISABLE_ADCTRIGGER     (hsr == HSR_DISABLE_ADCTRIGGER_VAL)


/* Host Service Requests */
#define HSR_INIT_IONTRIGGER_VAL        1       //
#define HSR_DISABLE_IONTRIGGER_VAL     2       //

#define HSR_INIT_IONTRIGGER        (hsr == HSR_INIT_IONTRIGGER_VAL)
#define HSR_DISABLE_IONTRIGGER     (hsr == HSR_DISABLE_IONTRIGGER_VAL)


/* Host Service Requests */
#define HSR_INIT_PWM_IN_VAL        1       //
#define HSR_DISABLE_PWM_IN_VAL     2       //

#define HSR_INIT_PWM_IN        (hsr == HSR_INIT_PWM_IN_VAL)
#define HSR_DISABLE_PWM_IN     (hsr == HSR_DISABLE_PWM_IN_VAL)



/******************************************************/
/*              INJ function                 */
/******************************************************/
/* INJ Values */

#define OPEN_INJ_EXCEPTION      (0x0001)
#define CLOSE_INJ_EXCEPTION     (0x0002)

/* Host Service Requests */
#define HSR_INIT_INJ_VAL        1       //

#define HSR_INIT_INJ        (hsr == HSR_INIT_INJ_VAL)

#define HSR_ADJUST_INJ_VAL      2

#define HSR_ADJUST_INJ      (hsr == HSR_ADJUST_INJ_VAL)

#define HSR_FORCE_CLOSE_INJ_VAL     3

#define HSR_FORCE_CLOSE_INJ         (hsr == HSR_FORCE_CLOSE_INJ_VAL)


/******************************************************/
/*              IGN function                 */
/******************************************************/
/* IGN Values */

#define OPEN_IGN_EXCEPTION      (0x0001)
#define CLOSE_IGN_EXCEPTION     (0x0002)

/* Host Service Requests */
#define HSR_INIT_IGN_VAL        1       //

#define HSR_INIT_IGN        (hsr == HSR_INIT_IGN_VAL)

#define HSR_ADJUST_IGN_VAL      2

#define HSR_ADJUST_IGN      (hsr == HSR_ADJUST_IGN_VAL)

#define HSR_FORCE_CLOSE_IGN_VAL     3

#define HSR_FORCE_CLOSE_IGN         (hsr == HSR_FORCE_CLOSE_IGN_VAL)

/******************************************************/
/*              PULSE function                 */
/******************************************************/
/* PULSE Values */

#define OPEN_PULSE_EXCEPTION      (0x0001)
#define CLOSE_PULSE_EXCEPTION     (0x0002)

/* Host Service Requests */
#define HSR_INIT_PULSE_VAL        1       //

#define HSR_INIT_PULSE        (hsr == HSR_INIT_PULSE_VAL)

#define HSR_ADJUST_PULSE_VAL      2

#define HSR_ADJUST_PULSE      (hsr == HSR_ADJUST_PULSE_VAL)

#define HSR_FORCE_CLOSE_PULSE_VAL     3

#define HSR_FORCE_CLOSE_PULSE         (hsr == HSR_FORCE_CLOSE_PULSE_VAL)

#define HSR_FORCE_INIT_PULSE_VAL     4

#define HSR_FORCE_INIT_PULSE         (hsr == HSR_FORCE_INIT_PULSE_VAL)

/******************************************************/
/*              UART function                 */
/******************************************************/

/* Macros to make the code easier to understand. */
/* These are specific to this UART function */
/* Host service requests */
#define UART_TX_INIT   4
#define UART_RX_INIT   7
/*Function Modes */
#define UART_NO_PARITY     0   /* used with FM1 */
#define UART_USE_PARITY    2   /* used when parity is passed to CPU */
#define UART_EVEN_PARITY   0   /* used with FM0 */
#define UART_ODD_PARITY    1   /* used with FM0 */





/******************************************************/
/*              DELAY function                 */
/******************************************************/
/* DELAY Values */

//#define OPEN_IGN_EXCEPTION        (0x0001)
//#define CLOSE_IGN_EXCEPTION       (0x0002)

/* Host Service Requests */
#define HSR_INIT_DELAY_VAL      1       //

#define HSR_INIT_DELAY      (hsr == HSR_INIT_DELAY_VAL)


/******************************************************/
/*              PHASE function                        */
/******************************************************/
/* PHASE Values */
#define PHASE_NORMAL_MODE       (0x00000000)
#define PHASE_RECOVERY_MODE     (0x00000100)
/* exceptions */
#define PHASE_EX_LOCKED         (0x00000200)
#define PHASE_EX_LOST           (0x00000400)
#define PHASE_EX_EDGE           (0x00000800)
#define PHASE_EX_ERROR_LEARNING (0x00001000)

/* pin transition symbols */
#define TRANSITION_LOW_HIGH     (1)
#define TRANSITION_HIGH_LOW     (2)

/* Host Service Requests */
#define HSR_INIT_PHASE_VAL          1
#define HSR_INIT_PHASE          (hsr == HSR_INIT_PHASE_VAL)

#define HSR_RECOVERY_PHASE_VAL      2
#define HSR_RECOVERY_PHASE       (hsr == HSR_RECOVERY_PHASE_VAL)

#define HSR_SET_CURR_EDGE_PHASE_VAL 3
#define HSR_SET_CURR_EDGE_PHASE  (hsr == HSR_SET_CURR_EDGE_PHASE_VAL)


/******************************************************/
/*           FastLinked channel functions             */
/******************************************************/
/* SwitchingMode definitions */
#define SWITCHINGMODE_SLOW     0
#define SWITCHINGMODE_FAST     1

/* Host Service Requests for output channel */
#define HSR_INIT_FLCHAN_OUT_VAL      1
#define HSR_CLOSE_FLCHAN_OUT_VAL     2
#define HSR_INIT_FLCHAN_OUT          (hsr == HSR_INIT_FLCHAN_OUT_VAL)
#define HSR_CLOSE_FLCHAN_OUT          (hsr == HSR_CLOSE_FLCHAN_OUT_VAL)


/* Host Service Requests for input channel */
#define HSR_INIT_FLCHAN_IN_VAL       1
#define HSR_CLOSE_FLCHAN_IN_VAL      2
#define HSR_INIT_FLCHAN_IN           (hsr == HSR_INIT_FLCHAN_IN_VAL)
#define HSR_CLOSE_FLCHAN_IN          (hsr == HSR_CLOSE_FLCHAN_IN_VAL)


/* Host Service Requests for direction channel */
#define HSR_INIT_FLCHAN_DIR_VAL       1
#define HSR_CLOSE_FLCHAN_DIR_VAL      2
#define HSR_INIT_FLCHAN_DIR           (hsr == HSR_INIT_FLCHAN_DIR_VAL)
#define HSR_CLOSE_FLCHAN_DIR          (hsr == HSR_CLOSE_FLCHAN_DIR_VAL)


/******************************************************/
/*              ANGLE Trigger function                */
/******************************************************/
/* Host Service Requests */
#define HSR_INIT_ANGTRIG_VAL        1       //
#define HSR_DISABLE_ANGTRIG_VAL     2       //

#define HSR_INIT_ANGTRIG        (hsr == HSR_INIT_ANGTRIG_VAL)
#define HSR_DISABLE_ANGTRIG     (hsr == HSR_DISABLE_ANGTRIG_VAL)

/* Angular Acquisition buffer size */
#define ANG_ACQ_BUF_SIZE 48



#endif  /* __SHAREDDEF_H */





