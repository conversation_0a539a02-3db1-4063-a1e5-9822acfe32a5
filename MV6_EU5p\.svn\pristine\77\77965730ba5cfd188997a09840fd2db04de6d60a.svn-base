/** #########################################################################
**     Filename  : Saf2Mgm_eep.c
**     Project   : ELDOR ECU
**     Processor : SPC5533
**     Version   : 1.0
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 06/07/2007
**     Abstract  : Safety 2 NVM variables to help with calibration
**     (c) Copyright 2007 Eldor Corporation
** ######################################################################### */
/* MODULE Saf2Mgm_eep */

//#include "saf2mgm.h"

#ifdef _BUILD_SAF2MGM_

#include "saf2mgm.h"

#ifdef _BUILD_S2_STORE_RESULTS_

    const uint16_t   EES2AEstPresIntake = 0;
    const uint16_t   EEPresIntake_10ms = 0;

    const uint16_t   EEGasPos_10ms = 0;
    const uint16_t   EES2AEstGasPos = 0;

    const int16_t   EES2DEstCmeGasRpm = 0;
    const int16_t   EECmeGasRpm_10ms = 0;

    const int16_t   EES2EEstCmeTargCAN = 0;
    const int16_t   EECmeDriverCANF_10ms = 0;

    const uint16_t  EES2GEstQAirAvg = 0;
    const uint16_t  EEQAirAvg_10ms = 0;

    const int16_t   EESAopt_10ms = 0;
    const int16_t   EES2GEstSAopt = 0;

    const uint16_t  EES2GEstEffSAreal = 0;
    const uint16_t  EEEffSAReal_10ms = 0;

    const uint16_t  EEEffLambda_10ms = 0;
    const uint16_t  EES2GEstEffLambda = 0;

    const uint16_t  EEEffCutoff_10ms = 0;
    const uint16_t  EES2GEstEffCutoff = 0;

    const int16_t EES2GEstCmiEst = 0;
    const int16_t EECmiEst_10ms = 0;

    const int16_t EECmiDriverP_10ms = 0;
    const int16_t EES2HEstCmiDriverP = 0;

    const int16_t EECmfP_10ms = 0;
    const int16_t EES2HEstCmfP = 0;

    const uint16_t EES2FrzNEvents[SIZE_S2STORE] = 
    {
      0, 0, 0, 0,
      0, 0, 0, 0,
      0, 0, 0, 0
    };

    const uint16_t EES2FrzRpm[SIZE_S2STORE] =
      {
        0, 0, 0, 0,
        0, 0, 0, 0,
        0, 0, 0, 0
      };
    const uint16_t EES2FrzLoad[SIZE_S2STORE] =
      {
        0, 0, 0, 0,
        0, 0, 0, 0,
        0, 0, 0, 0
      };
    const uint16_t EES2FrzPresIntake[SIZE_S2STORE] =
      {
        0, 0, 0, 0,
        0, 0, 0, 0,
        0, 0, 0, 0
      };
    const uint16_t EES2FrzGasPos[SIZE_S2STORE] =
      {
        0, 0, 0, 0,
        0, 0, 0, 0,
        0, 0, 0, 0
      };
    const uint16_t EES2FrzAngThrottle[SIZE_S2STORE] =
      {
        0, 0, 0, 0,
        0, 0, 0, 0,
        0, 0, 0, 0
      };
    const int16_t EES2FrzTWater[SIZE_S2STORE] =
      {
        0, 0, 0, 0,
        0, 0, 0, 0,
        0, 0, 0, 0
      };
      
#endif

const uint8_T S2PtFault = 0;


#endif /* _BUILD_SAF2MGM_ */
