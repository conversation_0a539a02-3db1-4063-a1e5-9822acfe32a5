/*
 * File: IdxCtfCtrl_types.h
 *
 * Code generated for Simulink model 'IdxCtfCtrl'.
 *
 * Model version                  : 1.480
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Feb 17 15:53:24 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (29), Warnings (3), Error (0)
 */

#ifndef RTW_HEADER_IdxCtfCtrl_types_h_
#define RTW_HEADER_IdxCtfCtrl_types_h_
#endif                                 /* RTW_HEADER_IdxCtfCtrl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
