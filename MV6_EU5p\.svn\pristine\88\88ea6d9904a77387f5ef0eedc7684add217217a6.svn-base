/*
 * File: fuel_mgm.h
 *
 * Code generated for Simulink model 'FuelMgm'.
 *
 * Model version                  : 1.994
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Apr 28 08:34:10 2021
 */

#ifndef RTW_HEADER_fuel_mgm_h_
#define RTW_HEADER_fuel_mgm_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T CntQFuelSplit;

/* FUELMGM.CntQFuelSplit: Counter to enable low bank switching */
extern uint16_T DPresInj;

/* FUELMGM.DPresInj: Injection pressure drop */
extern int16_T DQFObjCyl;

/* FUELMGM.DQFObjCyl: Injection Target Fuel mass delta */
extern int16_T DQFObjCylH;

/* FUELMGM.DQFObjCylH: Injection Target Fuel mass delta - HIGH INJECTOR BANK */
extern int16_T DQFilmCyl;

/* FUELMGM.DQFilmCyl: Fuel Film mass delta */
extern int16_T DQFilmCylH;

/* FUELMGM.DQFilmCylH: Fuel Film mass delta - HIGH INJECTOR BANK */
extern uint16_T FilmEnab;

/* FUELMGM.FilmEnab: Fuel Film Correction Enable Gain */
extern uint16_T FilmEnabH;

/* FUELMGM.FilmEnabH: Fuel Film Correction Enable Gain */
extern uint16_T FirstInjTime;

/* FUELMGM.FirstInjTime: Fuel Injection time */
extern uint16_T FirstSOI;

/* FUELMGM.FirstSOI: Start of injection angle */
extern uint8_T FlgInjTMin[8];

/* FUELMGM.FlgInjTMin: Minimum injection time saturation flag */
extern uint16_T FuelLt;

/* QFuel tot integral */
extern uint8_T FuelMgmcyl4calc;

/* FUELMGM.FuelMgmcyl4calc: FuelMgmcyl4calc */
extern uint8_T FuelMgmcyl4calcH;

/* FUELMGM.FuelMgmcyl4calcH: FuelMgmcyl4calcH */
extern uint8_T FuelMgmflagHTDC;

/* FUELMGM.FuelMgmflagHTDC: FuelMgmflagHTDC */
extern uint8_T FuelMgmflagTDC;

/* FUELMGM.FuelMgmflagTDC: FuelMgmflagTDC */
extern uint16_T GainFilm[4];

/* FUELMGM.GainFilm: Fuel Film Model Gain */
extern uint16_T GainFilmH;

/* FUELMGM.GainFilmH: Fuel Film Model Gain */
extern uint16_T GainInjT;

/* FUELMGM.GainInjT: Gain Inj Time */
extern uint32_T IDFuelMgm;

/* ID Version */
extern uint8_T InjEnable;

/* FUELMGM.InjEnable: Injection enable condition */
extern uint8_T InjEnableEMD;

/* FUELMGM.InjEnableEMD: Enable flag for EMD */
extern int16_T InjTVBat;

/* FUELMGM.InjTVBat: Injector time offset f(Vbattery) */
extern int16_T InjTVBatH;

/* FUELMGM.InjTVBatH: Injector time offset f(Vbattery) */
extern uint16_T InjTime[16];

/* FUELMGM.InjTime: Fuel Injection time */
extern uint16_T KFFilm[4];

/* FUELMGM.KFFilm: Fuel Film Model Filter Gain */
extern uint16_T KFFilmH;

/* FUELMGM.KFFilmH: Fuel Film Model Filter Gain */
extern int16_T OffInjT;

/* FUELMGM.OffInjT: Offset Inj Time */
extern uint16_T QFObj[8];

/* FUELMGM.QFObj: Injection Target Fuel mass */
extern uint16_T QFObjBase[8];

/* FUELMGM.QFObjBase: Injection Target Fuel mass based on QAir */
extern uint16_T QFObjCylH;

/* FUELMGM.QFObjCylH: Injection Target Fuel mass - HIGH INJECTOR BANK */
extern uint16_T QFObjOld[4];

/* FUELMGM.QFObjOld: Injection Target Fuel mass - old TDC value */
extern uint16_T QFilm[8];

/* FUELMGM.QFilm: Fuel Film mass */
extern uint16_T QFuel[8];

/* FUELMGM.QFuel: Injection fuel mass */
extern uint16_T QFuelAvg;

/* FUELMGM.QFuelAvg: Mean Injection Target Fuel mass */
extern uint16_T QFuelCyl;

/* FUELMGM.QFuelCyl: Injection fuel mass */
extern uint16_T QFuelCylH;

/* FUELMGM.QFuelCylH: Injection fuel mass - HIGH INJECTOR BANK */
extern uint16_T QFuelExtra;

/* QFuel extra */
extern uint32_T QFuelIntExtTot;

/* QFuel tot */
extern uint32_T QFuelIntLth;

/* QFuel tot integral */
extern uint32_T QFuelIntTot;

/* QFuel tot */
extern uint16_T QFuelLam;

/* FUELMGM.QFuelLam: Fuel mass calculated from lambda */
extern uint16_T QFuelLth;

/* FUELMGM.QFuelLth: Injected fuel volume */
extern int16_T QFuelSplitFrac;

/* FUELMGM.QFuelSplitFrac: Fuel mass fraction for low bank */
extern int16_T QFuelSplitFracH;

/* FUELMGM.QFuelSplitFracH: Fuel mass fraction for high bank */
extern int16_T QFuelSplitFracRL;

/* FUELMGM.QFuelSplitFracRL: Fuel mass fraction for low bank - RATE LIMITER OUT */
extern int16_T QFuelSplitFracTB;

/* FUELMGM.QFuelSplitFracTB: Fuel mass fraction for low bank - TABLE VALUE CONVERTED */
extern uint16_T QFuelTot;

/* FUELMGM.QFuelTot: Fuel mass calculated from lambda */
extern uint16_T SOI[16];

/* FUELMGM.SOI: Start of injection angle */
extern int8_T StQFAcc[4];

/* FUELMGM.StQFAcc: Fuel film transient state */
extern int8_T StQFAccH;

/* FUELMGM.StQFAccH: Fuel film transient state - HIGH INJECTOR BANK */
extern uint8_T StQFuelSplit;

/* FUELMGM.StQFuelSplit: Fuel film transient state */
extern uint16_T XFilm[4];

/* FUELMGM.XFilm: Fuel Film Model X Factor */
extern uint16_T XFilmH;

/* FUELMGM.XFilmH: Fuel Film Model X Factor */
#endif                                 /* RTW_HEADER_fuel_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
