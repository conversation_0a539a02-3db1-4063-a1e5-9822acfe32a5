/* Precompiler defines for UART module */

#define MAIN_SYSTEM_CLOCK   (FSYS*1000000)

#define UART_TIMEBASE_FREQ (MAIN_SYSTEM_CLOCK / (ETPU_TBCR_TCR1CTL_SELECT_SYSTEMCLOCK_DIV2 * (ETPU_A_TCR1P+1)))

//#define UART_BAUD_RATE_9600 9600
#define UART_BAUD_RATE_12K 12000
#define UART_BAUD_RATE_7800 7800
#define UART_BAUD_RATE_4800 4800
#define UART_BAUD_RATE_20K  20000

#define BITS_PER_DATA_WORD_8  8

/* UART Channels */

#if((BOARD_TYPE == BOARD_3)||(BOARD_TYPE == BOARD_4)||(BOARD_TYPE == BOARD_5))
#define ETPU_UART_RX 11
#define ETPU_UART_TX 10

#define     IN_UART_RX            LinImmo_RXD /*DIGIO.cfg */
#define     OUT_UART_TX           LinImmo_TXD /*DIGIO.cfg */

#elif (BOARD_TYPE == BOARD_M1) || (BOARD_TYPE==BOARD_M2) || (BOARD_TYPE == BOARD_M3)
#define ETPU_UART_RX 11
#define ETPU_UART_TX 10

#define     IN_UART_RX            LinImmo_RXD /*DIGIO.cfg */
#define     OUT_UART_TX           LinImmo_TXD /*DIGIO.cfg */
#else
 #error Board not supported!
#endif

/****************************************************************************
     Peripheral errors defines 
 ****************************************************************************/
#define UART_RX_CHANNEL_NOT_CONFIGURED                -5
#define UART_TX_CHANNEL_NOT_CONFIGURED                -6
#define UART_INTERRUPT_HANDLER_NOT_CONFIGURED         -7
#define UART_PERIPHERAL_ALREADY_ENABLED               -8
#define UART_PERIPHERAL_ALREADY_DISABLED              -9
#define UART_ETPU_INIT_ERROR                          -14
#define UART_POUT_NOT_ENABLED                         -10
#define UART_ETPU_ERROR_FREQ                          -11
#define UART_INVALID_IOCTRL_OPTION                    -12
#define UART_NO_EX_ENABLED                            -13

