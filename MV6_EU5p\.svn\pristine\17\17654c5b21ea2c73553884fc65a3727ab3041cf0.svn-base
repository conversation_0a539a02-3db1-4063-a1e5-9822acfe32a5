/*
 * File: gearshift_mgm.h
 *
 * Code generated for Simulink model 'GearShiftMgm'.
 *
 * Model version                  : 1.1788
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Feb 27 12:20:48 2024
 */

#ifndef RTW_HEADER_gearshift_mgm_h_
#define RTW_HEADER_gearshift_mgm_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define QSHIFT_BLIP_REQUEST            3U

/* Quick down shift request state */
#define QSHIFT_BLIP_WAIT               4U

/* Quick down shift wait state */
#define QSHIFT_BLP_REDUCED             6U

/* Quick down shift wait state */
#define QSHIFT_CTF_DOUBLE              7U

/* Quick up shift request state */
#define QSHIFT_CTF_REDUCED             5U

/* Quick down shift wait state */
#define QSHIFT_CTF_REQUEST             1U

/* Quick up shift request state */
#define QSHIFT_CTF_WAIT                2U

/* Quick up shift wait state */
#define QSHIFT_DISABLE                 0U

/* Quick shift disabled state */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmeDriverQShift;

/* CmeDriver freezed at gearshift request */
extern int32_T CmeQsBlpIFiltHr;

/* CmeI quick shift blip High resolution filter output */
extern int32_T CmeQsBlpPFiltHr;

/* CmeP quick shift blip High resolution filter output */
extern int32_T CmeQsCtfIFiltHr;

/* CmeI quick shift ctf High resolution filter output */
extern int16_T CmeQsI;

/* CmeI quickShift */
extern int16_T CmeQsIFilt;

/* CmeI quickShift filtered */
extern uint8_T CmeQsIPeriod;

/* CmeQsI period of application */
extern int16_T CmeQsP;

/* CmeP quickShift */
extern int16_T CmeQsPFilt;

/* CmeP quickShift filtered */
extern uint8_T CtfGearShift;

/* Cut off Gear Shift Flag */
extern uint8_T FlgEnQuickShift;

/* Flag Enable Quick shift */
extern uint8_T FlgEnQuickShiftDn;

/* Flag Enable down Quick shift */
extern uint8_T FlgEnQuickShiftN;

/* Enable Neutral */
extern uint8_T FlgEnQuickShiftUp;

/* Flag Enable up Quick shift */
extern uint8_T FlgQSLow;

/* FlgQSLow */
extern uint8_T GearPosQShift;

/* Gear position at time of gear shift request */
extern uint16_T GearShiftBlpDnFOFK;

/* Gear shift Blip FOF K parameter */
extern uint16_T GearShiftBlpUpFOFK;

/* Gear shift Blip FOF K parameter */
extern uint16_T GearShiftCtfDnFOFK;

/* Gear shift Ctf FOF K parameter */
extern uint16_T GearShiftCtfUpFOFK;

/* Gear shift Ctf FOF K parameter */
extern uint32_T IDGearShiftMgm;

/* ID Version */
extern uint8_T QSBlpTime;

/* Blip Quick shift Cme increment Duration */
extern uint8_T QSBlpToTime;

/* Blip Quick shift Cme increment Duration */
extern uint8_T QSCntDlbTime;

/* QSCntDlbTime */
extern uint8_T QSCntDlbToTime;

/* QSCntDlbToTime */
extern uint8_T QSCtfPeriod;

/* Gear shift Cut Off Period withought offset applied */
extern uint8_T QSCtfTime;

/* Ctf Quick shift Cme increment Duration */
extern uint8_T QSGearDnSignal;

/* Quick Shift conditioned Down Signal */
extern uint8_T QSGearUpSignal;

/* Quick Shift conditioned Up Signal */
extern uint8_T QShiftCnt;

/* Count number of QGearShift Calculations (10ms) */
extern uint8_T QuickGearShiftBlp;

/* Flag blip Quick shift running */
extern uint8_T QuickGearShiftBlpOn;

/* Flag blip Quick shift running internal sig */
extern uint8_T QuickGearShiftCtf;

/* Flag ctf Quick shift running */
extern uint8_T QuickGearShiftCtfOn;

/* Flag ctf Quick shift running internal sig */
extern uint8_T QuickShiftPresence;

/* Quick Shift Strategy Enabled */
extern uint16_T RpmQShift;

/* Rpm at the beguinning of Quick Shift request */
extern uint8_T StQShift;

/* Quick Gear Shift status */

/** public functions **/
extern void GearShiftMgm_initialize(void);
extern void GesrShiftMgm_10ms(void);
extern void GearShiftMgm_NoSync(void); 

#endif                                 /* RTW_HEADER_gearshift_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
