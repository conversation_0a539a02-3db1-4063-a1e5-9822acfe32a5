/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "PTrainDiag.h"

#ifdef _BUILD_PTRAINDIAG_

#pragma ghs section rodata=".calib" 


/* Dual Clutch flag */
__declspec(section ".calib") int16_T BKKFRPMF2[BKKFRPMF2_dim] =
{
    0,
    5,
    10,
    20,
    50
};
__declspec(section ".calib") int16_T BKKFDISTRF2[BKKFDISTRF2_dim] =
{
    0,
    10,
    50,
};
__declspec(section ".calib") int16_T TBKFRPMF2[BKKFRPMF2_dim][BKKFDISTRF2_dim] =
{
    {(int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f)},
    {(int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f)},
    {(int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f)},
    {(int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f)},
    {(int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f), (int16_T)(1.0f * 16384.0f)},
};
/* Dual Clutch flag */
__declspec(section ".calib") uint8_T RPMFFILTDEEP = 13;
/* Flag to force EMS CAN message vehicle speed [flag] */
__declspec(section ".calib") uint8_T USEEMSVEHSPEED =  0; /* 0 */
/* Force the vehicle speed [Km/h] */
__declspec(section ".calib") int16_T VEHSPEEDFORCED =  -1*16;
/* Flag to force wheel speed to read: 0 = NO_FORCED, 1 = FRONT, 2 = REAR */
__declspec(section ".calib") uint8_T FORCEWHEELSPEED =  2; /* 0 */
/* Number of sequence to confirm cloutch fault. [x] */
__declspec(section ".calib") uint8_T NPTFOBSCS =  5; /* 5 sequence */
/* Number of consecutive sample to deconfirm Clutch diag. [x/2] */
__declspec(section ".calib") uint8_T NSAMPOBSCS =  20; /* 10 */
/* Time step of Clutch Wrong condition. [x*10ms] */
__declspec(section ".calib") uint16_T TIMCSCNDDISENGAGE =  12000; /* 120000ms */
/* Time step of Clutch Wrong condition. [x*10ms] */
__declspec(section ".calib") uint16_T TIMCSCNDENGAGE =  12000; /* 120000ms */
/* Threshold of minimum rpm to start clutch disengage diagnosis. [x*1Rpm] */
__declspec(section ".calib") uint16_T THRPMCED =  3500; /* 3500Rpm */
/* Threshold of minimum torque to start clutch disengage diagnosis. [x*1Rpm] */
__declspec(section ".calib") uint16_T THTORQUECED =  50*32; /* 50Nm */
/* Time Speed Limiter ratio. [x*10ms/(Km/h)] */
__declspec(section ".calib") uint8_T TIMSLRATIO =  30; /* 300ms/(Km/h) */
/* VehSpeed Torque threshold. [x/32] */
__declspec(section ".calib") int16_T THTORQUEVS =  3200; /* 100Nm*/
/* VehSpeed Rpm threshold. [x*1Rpm] */
__declspec(section ".calib") uint16_T THRPMVS =  2000; /* 2000Rpm*/
/* Delta max VehSpeed plausible. [x/16Km/h] */
__declspec(section ".calib") uint16_T DELTAMAXSPEED =  320; /* 20Km/h */
/* Number of consecutive sample to confirm VehSpeed diag. [x*10ms] */
__declspec(section ".calib") uint16_T NSAMPOBSVS =  1000; /* 10000ms */
/* Increment step of delta VehSpeed Wrong condition. [x*10ms] */
__declspec(section ".calib") uint8_T INCVSCNDWRONG =  100; /* 1000ms */
/* Threshold for detecting vehicle stop [Km/h] */
__declspec(section ".calib") uint16_T THVRUN1 = 8*16;
/* Threshold for detecting vehicle running [Km/h] */
__declspec(section ".calib") uint16_T THVRUN2 = 15*16;
/* Threshold for detecting vehicle running [Km/h] */
__declspec(section ".calib") uint16_T THVRUN3 = 35*16;
/* Timeout for detecting vehicle running after vehspeed > THVRUN1 [ms] */
__declspec(section ".calib") uint16_T TVRUN = 6000;
/* VehSpeedRear breakpoints for RearVehSpeed [Km/h] */
__declspec(section ".calib") uint16_T BKCOMPREARSPEED[BKCOMPREARSPEED_dim] = 
{
 8*16, 26*16, 74*16, 123*16, 197*16, 242*16
};
/* VehSpeed drift compensation. */
__declspec(section ".calib") int16_T VTVEHSPEEDDELTAREAR[BKCOMPREARSPEED_dim] =
{
 -5.35*16, 1.33*16, 4.72*16, 7.9*16, 11.17*16, 12.54*16
};
/* Filter K. */
__declspec(section ".calib") int16_T KFILTDELTAVHESPEED = 0.2*16384;

/* Km/h. */
__declspec(section ".calib") int16_T DVEHSPSPRINGUP = -0.2*16;

/* VehSpeedCCOld brakepoints for VehSpeedCC median length */
__declspec(section ".calib") uint16_T BKVEHSPMEDIANLEN[BKVEHSPMEDIANLEN_dim] = 
{
 0, 20*16, 80*16, 150*16
};
/* VehSpeedCC median length */
__declspec(section ".calib") uint8_T VTMEDIANLEN[BKVEHSPMEDIANLEN_dim] = 
{
 1, 3, 5, 7
};

#else

#endif

