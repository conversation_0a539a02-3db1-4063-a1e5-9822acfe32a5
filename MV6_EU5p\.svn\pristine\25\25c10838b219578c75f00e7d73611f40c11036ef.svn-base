/**
 ******************************************************************************
 **  Filename:      SABasicMgm_private.h
 **  Date:          10-Oct-2023
 **
 **  Model Version: 1.2290
 ******************************************************************************
 **/

#ifndef RTW_HEADER_SABasicMgm_private_h_
#define RTW_HEADER_SABasicMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SABasicMgm.h"

/* Includes for objects with custom storage classes. */
#include "sparkmgm.h"
#include "null.h"
#include "syncmgm.h"
#include "Rpm_limiter.h"
#include "Syncmgm.h"
#include "lightoffmgm_out.h"
#include "engflag.h"
#include "Vspeed_mgm.h"
#include "GearPosClu_mgm.h"
#include "idle_mgm.h"
#include "loadmgm.h"
#include "temp_mgm.h"
#include "throttle_target.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKEFFSA[12];            /* Variable: BKEFFSA
                                        * Referenced by:
                                        *   '<S17>/BKEFFSA'
                                        *   '<S47>/BKEFFSA'
                                        * SA Breakpoint for VTEFFSA
                                        */
extern int16_T SASTEPNORM;             /* Variable: SASTEPNORM
                                        * Referenced by: '<S85>/SASTEPNORM'
                                        * (SR) Rate limit on Spark Advance
                                        */
extern int16_T SASTEPSTART;            /* Variable: SASTEPSTART
                                        * Referenced by: '<S85>/SASTEPSTART'
                                        * Rate limit on Spark Advance during start
                                        */
extern int16_T BKCMIPEFFSA[10];        /* Variable: BKCMIPEFFSA
                                        * Referenced by: '<S66>/BKCMIPEFFSA'
                                        * CmiTargetP Breakpoint
                                        */
extern int16_T BKCMIPEFFSAIDLE[8];     /* Variable: BKCMIPEFFSAIDLE
                                        * Referenced by: '<S66>/BKCMIPEFFSAIDLE'
                                        * CmiTargetP Breakpoint
                                        */
extern uint16_T VTEFFSA[12];           /* Variable: VTEFFSA
                                        * Referenced by:
                                        *   '<S17>/VTEFFSA'
                                        *   '<S47>/VTEFFSA'
                                        * Target SA Efficiency
                                        */
extern uint16_T BKRPMEFFSA[20];        /* Variable: BKRPMEFFSA
                                        * Referenced by: '<S66>/BKRPMEFFSA'
                                        * Rpm Breakpoint
                                        */
extern uint16_T BKRPMEFFSAIDLE[12];    /* Variable: BKRPMEFFSAIDLE
                                        * Referenced by: '<S66>/BKRPMEFFSAIDLE'
                                        * Rpm Breakpoint
                                        */
extern uint16_T BKSAIDLERPM[12];       /* Variable: BKSAIDLERPM
                                        * Referenced by: '<S12>/BKSAIDLERPM'
                                        * Rpm Breakpoint vector for TBSIDLE
                                        */
extern uint16_T BKSARPM[20];           /* Variable: BKSARPM
                                        * Referenced by: '<S12>/BKSARPM'
                                        * Rpm Breakpoint vector for TBSAMIN - TBSAOPT
                                        */
extern uint16_T BKSATDCCRK[4];         /* Variable: BKSATDCCRK
                                        * Referenced by:
                                        *   '<S6>/BKSATDCCRK'
                                        *   '<S36>/BKSATDCCRK'
                                        * CntTdcCrk Breakpoint vector for TBSASTART
                                        */
extern uint16_T THRPMUPDATESA;         /* Variable: THRPMUPDATESA
                                        * Referenced by: '<S1>/Chart'
                                        * Threshold to enable SA update at HTDC
                                        */
extern int8_T BKSATAIR[10];            /* Variable: BKSATAIR
                                        * Referenced by: '<S36>/BKSATAIR'
                                        * Tair Breakpoint vector for TBSATEMP
                                        */
extern int8_T BKSATWATER[10];          /* Variable: BKSATWATER
                                        * Referenced by:
                                        *   '<S6>/BKSATWATER'
                                        *   '<S36>/BKSATWATER'
                                        *   '<S36>/BKSATWATER1'
                                        * TWater Breakpoint vector for TBSATEMP
                                        */
extern int8_T TBSATEMP[100];           /* Variable: TBSATEMP
                                        * Referenced by: '<S36>/TBSATEMP'
                                        * SA Temperature Correction Table
                                        */
extern int8_T TBECONSABASE[200];       /* Variable: TBECONSABASE
                                        * Referenced by: '<S12>/TBECONSABASE'
                                        * (SR) Basic torque SA Table EconMode
                                        */
extern int8_T TBSABASE[200];           /* Variable: TBSABASE
                                        * Referenced by: '<S12>/TBSABASE'
                                        * (SR) Basic torque SA Table
                                        */
extern int8_T TBSAIDLE[96];            /* Variable: TBSAIDLE
                                        * Referenced by: '<S12>/TBSAIDLE'
                                        * (SR) Idle torque SA Table
                                        */
extern int8_T TBSAMAX[200];            /* Variable: TBSAMAX
                                        * Referenced by: '<S12>/TBSAMAX'
                                        * (SR) Increased basic torque SA Table
                                        */
extern int8_T TBSAMIN[200];            /* Variable: TBSAMIN
                                        * Referenced by: '<S12>/TBSAMIN'
                                        * Minimum SA Table
                                        */
extern int8_T TBSAOPT[200];            /* Variable: TBSAOPT
                                        * Referenced by: '<S12>/TBSAOPT'
                                        * (SR) Maximum torque SA Table
                                        */
extern int8_T TBSASTART[40];           /* Variable: TBSASTART
                                        * Referenced by:
                                        *   '<S6>/TBSASTART'
                                        *   '<S36>/TBSASTART'
                                        * (SR) Basic torque SA during crank
                                        */
extern int8_T VTSAIDLEOFF[12];         /* Variable: VTSAIDLEOFF
                                        * Referenced by: '<S12>/VTSAIDLEOFF'
                                        * SAIdleOff table
                                        */
extern uint8_T BKSACOMPRPM[8];         /* Variable: BKSACOMPRPM
                                        * Referenced by: '<S18>/BKSACOMPRPM'
                                        * Rpm Breakpoint vector for TBSACOMPLAMRPM
                                        */
extern uint8_T BKSAIDLELOAD[8];        /* Variable: BKSAIDLELOAD
                                        * Referenced by: '<S12>/BKSAIDLELOAD'
                                        * Load Breakpoint vector for TBSIDLE
                                        */
extern uint8_T BKSALOAD[10];           /* Variable: BKSALOAD
                                        * Referenced by:
                                        *   '<S12>/BKSALOAD'
                                        *   '<S18>/BKSALOAD'
                                        * Load Breakpoint vector for TBSAMIN - TBSAOPT
                                        */
extern uint8_T SASTEPGAIN;             /* Variable: SASTEPGAIN
                                        * Referenced by: '<S14>/SASTEPGAIN'
                                        * Gain on negative SA step
                                        */
extern uint8_T VTKFILSABASIC[10];      /* Variable: VTKFILSABASIC
                                        * Referenced by: '<S18>/VTKFILSABASIC'
                                        * K Filter
                                        */
extern uint8_T VTKFILTEFFSA[8];        /* Variable: VTKFILTEFFSA
                                        * Referenced by: '<S18>/VTKFILTEFFSA'
                                        * Filter on Basic SA Efficiency after rate limiter
                                        */
extern uint8_T TBEFFSA[200];           /* Variable: TBEFFSA
                                        * Referenced by: '<S66>/TBEFFSA'
                                        * Eff SA
                                        */
extern uint8_T TBEFFSAIDLE[96];        /* Variable: TBEFFSAIDLE
                                        * Referenced by: '<S66>/TBEFFSAIDLE'
                                        * Eff SA
                                        */
extern uint8_T VTLOTECOMP[10];         /* Variable: VTLOTECOMP
                                        * Referenced by: '<S36>/VTLOTECOMP'
                                        * Load based gain for temperature compensation
                                        */
extern uint8_T VTMAXEFFSASTEP[8];      /* Variable: VTMAXEFFSASTEP
                                        * Referenced by: '<S18>/VTMAXEFFSASTEP'
                                        * Rate limit on Basic SA Efficiency
                                        */
extern uint8_T VTMAXEFFSASTNT[8];      /* Variable: VTMAXEFFSASTNT
                                        * Referenced by: '<S18>/VTMAXEFFSASTNT'
                                        * Rate limit on Basic SA Efficiency in Neutral
                                        */
extern uint8_T VTRPMTECOMP[20];        /* Variable: VTRPMTECOMP
                                        * Referenced by: '<S36>/VTRPMTECOMP'
                                        * Rpm based gain for temperature compensation
                                        */
extern uint8_T ENSAIDLCUTOFF;          /* Variable: ENSAIDLCUTOFF
                                        * Referenced by: '<S13>/ENSAIDLCUTOFF'
                                        * Enable of selecting SAIdle table in CutOff
                                        */
extern uint8_T ENSAIDLVEHSTOP;         /* Variable: ENSAIDLVEHSTOP
                                        * Referenced by: '<S13>/ENSAIDLVEHSTOP'
                                        * Enable selecting SAIdle table when vehicle stopped
                                        */
extern uint8_T SELEFFSABASE;           /* Variable: SELEFFSABASE
                                        * Referenced by:
                                        *   '<S6>/SELEFFSABASE'
                                        *   '<S7>/SELEFFSABASE'
                                        * Select EffSABase
                                        */
extern uint8_T SELLOADSABASE;          /* Variable: SELLOADSABASE
                                        * Referenced by: '<S12>/SELLOADSABASE'
                                        * Select Load (=0) or LoadObj (=1) for SA tables interpolation
                                        */
extern void SABasicMgm_fcn_Init(void);
extern void SABasicMgm_fcn_calc(void);

#endif                                 /* RTW_HEADER_SABasicMgm_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
