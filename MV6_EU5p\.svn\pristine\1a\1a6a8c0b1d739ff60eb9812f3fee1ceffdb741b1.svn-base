/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIGI#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

/* ------------------------------------------------------------------ */
/*    DIGIO pin configuration                                           */
/* ------------------------------------------------------------------ */
/* ------------------------------------------------------------------ */
/*  Common module defines                                             */
/* ------------------------------------------------------------------ */
#define     SET_BIT_LOW      0x00            /* Low level bit status  */ 
#define     SET_BIT_HIGH     0x01             /* High level bit status */
 
/* Costanti per tipo weak pull up-down */
#define     DISABLE_PULL        0x00
#define     EN_PULL_DOWN        0x02
#define     EN_PULL_UP          0x03

#define     DRVSTR_10PF         0x0        /* 10pF drive strength */
#define     DRVSTR_20PF         0x1        /* 20pF drive strength */
#define     DRVSTR_30PF         0x2        /* 30pF drive strength */
#define     DRVSTR_50PF         0x3        /* 50pF drive strength */

/* SPI pin's slew rate      */
#define     MIN_SLEWRATE        0x0        /* slowest slew rate    */
#define     MED_SLEWRATE        0x1        /* medium    slew rate  */
#define     MAX_SLEWRATE        0x3        /* fastest slew rate    */

#define     OUT_PUSHPULL        0x0        /* push-pull enable */
#define     OUT_OPENDRAIN       0x1        /* open-drain enable */

#define     EN_INPUT_BUF        0x1        /* enable input buffer */
#define     DIS_INPUT_BUF       0x0        /* disable input buffer */


