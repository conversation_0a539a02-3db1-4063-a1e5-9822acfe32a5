/*
 * File: AirMgm_data.c
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2341
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Sep 21 14:33:03 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifdef _BUILD_AIRMGM_

#include "AirMgm.h"
#include "AirMgm_private.h"

/*
 * Invariant block signals and block parameters
 * for system '<S1>/TDC'
 */
const rtC_TDC_AirMgm
  AirMgm_TDC_C = {
  /* Start of '<S243>/QAir_Model' */
  {
    0                                  /* '<S250>/Data Type Conversion4' */
  }
  ,

  /* End of '<S243>/QAir_Model' */

  /* Start of '<S246>/QAir_Model1' */
  {
    0                                  /* '<S262>/Data Type Conversion4' */
  }
  ,

  /* End of '<S246>/QAir_Model1' */

  /* Start of '<S244>/QAir_Model1' */
  {
    0                                  /* '<S254>/Data Type Conversion4' */
  }
  ,

  /* End of '<S244>/QAir_Model1' */

  /* Start of '<S242>/QAir_Model' */
  {
    0                                  /* '<S247>/Data Type Conversion4' */
  }
  /* End of '<S242>/QAir_Model' */
};

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */

