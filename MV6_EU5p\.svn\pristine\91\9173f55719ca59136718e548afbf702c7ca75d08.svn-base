#include "secure.h"
#include "..\include\secure_private.h"

#ifdef _BUILD_SECURE_

#ifdef __MWERKS__
#pragma force_active on
#pragma section RW ".calib" ".calib"
#else
#pragma ghs section rodata=".calib"
#endif

__declspec(section ".calib") uint32_T NEXUSSECURED = NEXUS_ENABLE_PASSWORD;
__declspec(section ".calib") uint32_T CCPSECURED = CCP_ENABLE_PASSWORD;
__declspec(section ".calib") uint32_T CCPCALIBSECURED = CCPCALIB_ENABLE_PASSWORD;

#ifdef __MWERKS__
#pragma force_active off
#endif


#endif /* _BUILD_SECURE_ */
