/**
 ******************************************************************************
 **  Filename:      RpmLimiter_private.h
 **  Date:          01-Mar-2023
 **
 **  Model Version: 1.2476
 ******************************************************************************
 **/

#ifndef RTW_HEADER_RpmLimiter_private_h_
#define RTW_HEADER_RpmLimiter_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "RpmLimiter.h"

/* Includes for objects with custom storage classes. */
#include "engflag.h"
#include "trq_est.h"
#include "SpeedLimCtrl_out.h"
#include "Idlectf_mgm.h"
#include "launchctrl_out.h"
#include "PTrain_Diag.h"
#include "trq_driver.h"
#include "gear_mgm.h"
#include "GearPosClu_mgm.h"
#include "syncmgm.h"
#include "recmgm.h"
#include "mathlib.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKDELTARPMGAIN[5];      /* Variable: BKDELTARPMGAIN
                                        * Referenced by: '<S53>/BKDELTARPMGAIN'
                                        * Bk: VTDELTARPMGAIN
                                        */
extern int16_T BKERRRPMLCTFIDX[5];     /* Variable: BKERRRPMLCTFIDX
                                        * Referenced by:
                                        *   '<S32>/BKERRRPMLCTFIDX'
                                        *   '<S55>/BKERRRPMLCTFIDX'
                                        * RpmLimErr breakpoints
                                        */
extern int16_T BKRPMLIMERR[5];         /* Variable: BKRPMLIMERR
                                        * Referenced by:
                                        *   '<S34>/BKRPMLIMERR'
                                        *   '<S35>/BKRPMLIMERR'
                                        * RpmLimErr breakpoints
                                        */
extern int16_T BKRPMLIMERRTRSTOP[9];   /* Variable: BKRPMLIMERRTRSTOP
                                        * Referenced by: '<S10>/BKRPMLIMERRTRSTOP'
                                        * RpmLimErrTrStop breakpoints
                                        */
extern int16_T RPMERRCTFREDUCT;        /* Variable: RPMERRCTFREDUCT
                                        * Referenced by: '<S32>/Calc_FlgRpmLIReduct'
                                        * threshold to confirm end of reduction Integral term
                                        */
extern int16_T RPMLIMERRCTF;           /* Variable: RPMLIMERRCTF
                                        * Referenced by: '<S55>/RPMLIMERRCTF'
                                        * rpm err threshold to enable cutoff for rpm limiter
                                        */
extern int16_T RPMLIMERRCTFNL;         /* Variable: RPMLIMERRCTFNL
                                        * Referenced by: '<S55>/RPMLIMERRCTFNL'
                                        * rpm err threshold to enable cutoff for rpm limiter - no load
                                        */
extern int16_T RPMLIMERRCTFNLDB;       /* Variable: RPMLIMERRCTFNLDB
                                        * Referenced by: '<S55>/RPMLIMERRCTFNLDB'
                                        * rpm err threshold to enable cutoff for rpm limiter - no load
                                        */
extern int16_T RPMLIMERRMAX;           /* Variable: RPMLIMERRMAX
                                        * Referenced by: '<S14>/RPMLIMERRMAX'
                                        * Max rpm err for rpm limiter
                                        */
extern int16_T DCMIRPMLIMCTF0;         /* Variable: DCMIRPMLIMCTF0
                                        * Referenced by: '<S55>/DCMIRPMLIMCTF0'
                                        * Decremental step for Cmi in cutoff
                                        */
extern int16_T DCMIRPMLIMCTF1;         /* Variable: DCMIRPMLIMCTF1
                                        * Referenced by: '<S55>/DCMIRPMLIMCTF1'
                                        * Decremental step for Cmi in cutoff
                                        */
extern int16_T DCMIRPMLIMCTFNL;        /* Variable: DCMIRPMLIMCTFNL
                                        * Referenced by: '<S55>/DCMIRPMLIMCTFNL'
                                        * Decremental step for Cmi in cutoff - neutral
                                        */
extern int16_T RELRATEGAINPROP;        /* Variable: RELRATEGAINPROP
                                        * Referenced by: '<S43>/RELRATEGAINPROP'
                                        * Rate gain relase Proportional
                                        */
extern int16_T THRCMIENDFILT;          /* Variable: THRCMIENDFILT
                                        * Referenced by: '<S9>/THRCMIENDFILT'
                                        * Delta cmi to stop filter
                                        */
extern int16_T THRCMIENDFILTDB;        /* Variable: THRCMIENDFILTDB
                                        * Referenced by: '<S9>/THRCMIENDFILTDB'
                                        * Delta cmi to stop filter
                                        */
extern int16_T THRCMIRPMLIM0;          /* Variable: THRCMIRPMLIM0
                                        * Referenced by: '<S14>/THRCMIRPMLIM0'
                                        * Smoothing step for CmiRpmLimiter
                                        */
extern int16_T THRCMIRPMLIM1;          /* Variable: THRCMIRPMLIM1
                                        * Referenced by: '<S14>/THRCMIRPMLIM1'
                                        * Smoothing step for CmiRpmLimiter
                                        */
extern int16_T THRCMIRPMLIMNT;         /* Variable: THRCMIRPMLIMNT
                                        * Referenced by: '<S14>/THRCMIRPMLIMNT'
                                        * Smoothing step for CmiRpmLimiter neutral
                                        */
extern int16_T VTCMIPBBPROT[6];        /* Variable: VTCMIPBBPROT
                                        * Referenced by: '<S20>/VTCMIPBBPROT'
                                        * BlowBy protection saturation vector
                                        */
extern int16_T VTDCMIMAXTRSTOPDB[9];   /* Variable: VTDCMIMAXTRSTOPDB
                                        * Referenced by: '<S10>/VTDCMIMAXTRSTOPDB'
                                        * Offset applied to CmfP to saturate CmiRpmLimiter
                                        */
extern uint16_T GNRPMLCTFIREDUCT;      /* Variable: GNRPMLCTFIREDUCT
                                        * Referenced by: '<S34>/GNRPMLCTFIREDUCT'
                                        * Gain Integral term in cutoff
                                        */
extern uint16_T VTKFILTCMIRPMLIM[7];   /* Variable: VTKFILTCMIRPMLIM
                                        * Referenced by: '<S9>/VTKFILTCMIRPMLIM'
                                        * Filter constant
                                        */
extern uint16_T VTLIMINTGAIN[5];       /* Variable: VTLIMINTGAIN
                                        * Referenced by: '<S34>/VTLIMINTGAIN'
                                        * Integral gain for rpm limiter
                                        */
extern uint16_T VTLIMPROPGAIN[5];      /* Variable: VTLIMPROPGAIN
                                        * Referenced by: '<S35>/VTLIMPROPGAIN'
                                        * Proportional gain for rpm limiter
                                        */
extern uint16_T BKGASPOSREC[6];        /* Variable: BKGASPOSREC
                                        * Referenced by: '<S54>/BKGASPOSREC'
                                        * GasPosCC breakpoints for rpm limit recovery
                                        */
extern uint16_T VTDELTARPMGAIN[5];     /* Variable: VTDELTARPMGAIN
                                        * Referenced by: '<S53>/VTDELTARPMGAIN'
                                        * DeltaRpm gain
                                        */
extern uint16_T BKCMIPBBPROT[6];       /* Variable: BKCMIPBBPROT
                                        * Referenced by: '<S20>/BKCMIPBBPROT'
                                        * BlowBy protection saturation breakpoint
                                        */
extern uint16_T RPMINLIMITNL;          /* Variable: RPMINLIMITNL
                                        * Referenced by: '<S54>/RPMINLIMITNL'
                                        * Threshold to activate the rpm limiter - no load
                                        */
extern uint16_T RPMLIMHYST;            /* Variable: RPMLIMHYST
                                        * Referenced by: '<S3>/Calc_Lim'
                                        * Hysteresis to suspend the rpm limiter
                                        */
extern uint16_T RPMLIMHYSTDB;          /* Variable: RPMLIMHYSTDB
                                        * Referenced by: '<S3>/Calc_Lim'
                                        * Hysteresis to suspend the rpm limiter 2
                                        */
extern uint16_T RPMLIMHYSTFIL;         /* Variable: RPMLIMHYSTFIL
                                        * Referenced by: '<S3>/Calc_Lim'
                                        * Hysteresis to suspend the rpm limiter filter
                                        */
extern uint16_T TBRPMINLIMIT[21];      /* Variable: TBRPMINLIMIT
                                        * Referenced by: '<S54>/TBRPMINLIMIT'
                                        * Threshold to activate the rpm limiter
                                        */
extern uint16_T VTRPMMAXREC[6];        /* Variable: VTRPMMAXREC
                                        * Referenced by: '<S54>/VTRPMMAXREC'
                                        * Max Rpm for rpm limit recovery
                                        */
extern uint8_T THRDELTARPMDB;          /* Variable: THRDELTARPMDB
                                        * Referenced by: '<S53>/THRDELTARPMDB'
                                        * Rpm threshold 2 to activate Cmi filtering
                                        */
extern uint8_T VTDELTARPM00[7];        /* Variable: VTDELTARPM00
                                        * Referenced by: '<S53>/VTDELTARPM00'
                                        * Rpm threshold to activate Cmi filtering
                                        */
extern uint8_T VTDELTARPM01[7];        /* Variable: VTDELTARPM01
                                        * Referenced by: '<S53>/VTDELTARPM01'
                                        * Rpm threshold to activate Cmi filtering
                                        */
extern uint8_T VTDELTARPM10[7];        /* Variable: VTDELTARPM10
                                        * Referenced by: '<S53>/VTDELTARPM10'
                                        * Rpm threshold to activate Cmi filtering
                                        */
extern uint8_T VTDELTARPM11[7];        /* Variable: VTDELTARPM11
                                        * Referenced by: '<S53>/VTDELTARPM11'
                                        * Rpm threshold to activate Cmi filtering
                                        */
extern uint8_T VTDELTARPM20[7];        /* Variable: VTDELTARPM20
                                        * Referenced by: '<S53>/VTDELTARPM20'
                                        * Rpm threshold to activate Cmi filtering
                                        */
extern uint8_T VTDELTARPM21[7];        /* Variable: VTDELTARPM21
                                        * Referenced by: '<S53>/VTDELTARPM21'
                                        * Rpm threshold to activate Cmi filtering
                                        */
extern uint8_T DRPMLIMERRGAIN;         /* Variable: DRPMLIMERRGAIN
                                        * Referenced by: '<S35>/DRPMLIMERRGAIN'
                                        * Gain applied to the proportional term
                                        */
extern uint8_T DRPMLIMERRGAINNL;       /* Variable: DRPMLIMERRGAINNL
                                        * Referenced by: '<S35>/DRPMLIMERRGAINNL'
                                        * Gain applied to the proportional term - neutral
                                        */
extern uint8_T DRPMLIMERRGAINNLDB;     /* Variable: DRPMLIMERRGAINNLDB
                                        * Referenced by: '<S35>/DRPMLIMERRGAINNLDB'
                                        * Gain applied to the proportional term - neutral 2
                                        */
extern uint8_T DISCTFRPMLIMTPROP;      /* Variable: DISCTFRPMLIMTPROP
                                        * Referenced by: '<S43>/DISCTFRPMLIMTPROP'
                                        * Rpm limiter disable proportional in CutOff
                                        */
extern uint8_T ENLIMOVSH;              /* Variable: ENLIMOVSH
                                        * Referenced by: '<S3>/Calc_Lim'
                                        * Enable rpm limiting after power on
                                        */
extern uint8_T ENRPMLIMITER;           /* Variable: ENRPMLIMITER
                                        * Referenced by: '<S3>/Calc_Lim'
                                        * Rpm limiter is active (=1)
                                        */
extern uint8_T SELRPMLIM;              /* Variable: SELRPMLIM
                                        * Referenced by: '<S3>/SELRPMLIM'
                                        * Select Rpm source
                                        */
extern uint8_T TBRPMLCTFIDX[15];       /* Variable: TBRPMLCTFIDX
                                        * Referenced by: '<S32>/TBRPMLCTFIDX'
                                        * Cutoff index selector
                                        */
extern uint8_T TIMRPMCTFREDUCT;        /* Variable: TIMRPMCTFREDUCT
                                        * Referenced by: '<S32>/Calc_FlgRpmLIReduct'
                                        * Time to confirm end of reduction Integral term
                                        */
extern void RpmLimiter_CmiRpmFilter(uint8_T rtu_GearPosFilt, uint8_T
  rtu_GearPosClu, int16_T rtu_CmfWheel, const uint16_T rtu_GearRatio[7], int16_T
  rtu_CmiTargetPMin, int16_T rtu_CmfP, rtB_CmiRpmFilter_RpmLimiter *localB,
  uint8_T *rtd_mem_CtfLimiterFlg);
extern void RpmLimiter_PI_regulator(void);
extern void RpmLimiter_CmiNoLimit(void);
extern void RpmLimiter_CmiSaturation(void);
extern void RpmLimiter_CmiSmoothing(void);
extern void RpmLimiter_T10ms(void);
extern void RpmLimiter_Reset(void);

#endif                                 /* RTW_HEADER_RpmLimiter_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
