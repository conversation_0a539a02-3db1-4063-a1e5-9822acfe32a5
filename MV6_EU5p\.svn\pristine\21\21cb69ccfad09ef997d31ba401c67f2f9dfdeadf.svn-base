/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#include "sys.h"
#include "../COMMON/mmu.h"
#include "mpc5500_spr_macros.h"


#ifdef _BUILD_MMU_

#define PAGE_NUMBER_MASK 0xFFFFF000
//Definitions for MAS0 (MMU Assist Register 0) 
//bit reference: MAS0AVAL ((tlbsel<<28)|(eselcam<<16))

// Fields for MAS0 (MMU Assist Register 0 ) 
// Field for TLBSEL  (TLB Select, bits [2:3]) 
#define TLB_SELECT  0x10000000   // Always 0x01 for MPC5554

//*************************************************************
// Definitions for MAS1 (MMU Assist Register 1) 
// bit reference: MAS1AVAL ((valid<<31)| (iprot<<30)|
//                      (tid<<16)|(ts<<12)|(tsize<<8))

// Fields for MAS1 (MMU Assist Register 1 ) 
// Field for V (Valid, bit [0])
#define TLB_ENTRY_INVALID   0x00000000  // 0x0   
#define TLB_ENTRY_VALID     0x80000000  // 0x1   
// Field for IPROT (Invalidation Protection, bit [1]) 
#define ENTRY_NOT_PROTECTED 0x00000000  // 0x0 # From Invalidation  
#define ENTRY_PROTECTED     0x40000000  // 0x1 # From Invalidation  
// Field for TID (Process ID, bits [8:15])
#define GLOBAL_MATCH        0x00000000  // 0x0 # Match all process IDs  
// Field for TS (Translation address space, bit [19])
#define TS_IS_COMPARE       0x00000000  // 0x0 # Match entry to instruction space  
#define TS_DS_COMPARE       0x00001000  // 0x1 # Match entry to data space 

//*************************************************************
// Definitions for MAS2 (MMU Assist Register 2) 
// bit reference: MAS2AVAL((epn<<12)|(w<<4)|(i << 3)|
//                         (m << 2)|(g << 1)|(e)) 

// Fields for MAS2 (MMU Assist Register 2 ) 
// The EPN field is defined above with global address space 
// EPN (effective page number, bits [0:19]) 
// Field for W (Write-through required, bit [27]) 
#define CACHE_WRITE_BACK 0x00000000  // 0x0 # Stores write-back to cache 
#define CACHE_WRITE_THRU 0x00000010  // 0x1 # Stores write through to memory    
#define CACHE_ACTIVE     0x00000000  // 0x0 # Cache is active for TLB entry 
// Field for I (Cache Inhibited, bit [28])
#define CACHE_INHIBIT    0x00000008  // 0x1 # Cache is inhibited for TLB entry 
// Field for M (Memory Coherence required, bit [29])
#define MEM_NO_COHERENCE 0x00000000  // 0x0 # Only valid setting for MPC5554 
#define MEM_COHERENCE    0x00000004  // 0x1 # Not valid--ignored on MPC5554 
// Field for G (Page Guarded, bit [30])
#define PAGE_NOT_GUARDED 0x00000000  // 0x0 # Cache page not guarded  
#define PAGE_GUARDED     0x00000002  // 0x1 # Cache page guarded   
// Field for E (Endianess, bit [31])
#define PAGE_BIG_ENDIAN  0x00000000  // 0x0 # Big endian byte order 
#define PAGE_LTL_ENDIAN  0x00000001  // 0x1 # True little endian byte order  

//*************************************************************
// Definitions for MAS3 (MMU Assist Register 3) 
// bit reference: MAS3AVAL ((rpn<<12)|(permissions))

// Fields for MAS3 (MMU Assist Register 3 ) 
// The RPN field is defined above with global address space 
// RPN == real page number 

// Field for U0-U3 (USR bits, bits [22:25])
#define MAS3_USR0      0x00000000  // 0x0  # User bit value =0000 
// Field for UX (User Execute Access, bit [26])   
#define USR_NO_EXECUTE 0x00000000  // 0x0  # User cannot execute code 
#define USR_EXECUTE    0x00000020  // 0x1  # User executable permission 
// Field for SX (Supervisor Execute Access, bit [27])
#define SUP_NO_EXECUTE 0x00000000  // 0x0  # Supervisor cannot execute code 
#define SUP_EXECUTE    0x00000010  // 0x1  # Supervisor executable permission
// Field for UW (User Write Access, bit [28]) 
#define USR_NO_WRITE   0x00000000  // 0x0  # User cannot write code 
#define USR_WRITE      0x00000008  // 0x1  # User write permission 
// Field for SW (Supervisor Write Access, bit [29])
#define SUP_NO_WRITE   0x00000000  // 0x0  # Supervisor cannot write code 
#define SUP_WRITE      0x00000004  // 0x1  # Supervisor write permission 
// Field for UR (User Read Access, bit [30])
#define USR_NO_READ    0x00000000  // 0x0  # User cannot read code 
#define USR_READ       0x00000002  // 0x1  # User read permission 
// Field for SR (Supervisor Read Access, bit [31])
#define SUP_NO_READ    0x00000000  // 0x0  # Supervisor cannot read code 
#define SUP_READ       0x00000001  // 0x1  # Supervisor read permission 

// extern  void setSpecReg32SPR_MAS0(uint32_t register data);
// extern  void setSpecReg32SPR_MAS1(uint32_t register data);
// extern  void setSpecReg32SPR_MAS2(uint32_t register data);
// extern  void setSpecReg32SPR_MAS3(uint32_t register data);

void MMU_switch_Tlb(uint8_t tlb_entry, uint32_t virtual_addr, uint32_t physic_addr, uint32_t tlb_page_size)
{
    uint32_t register value_MAS0 = 0;
    uint32_t register value_MAS1 = 0;
    uint32_t register value_MAS2 = 0;
    uint32_t register value_MAS3 = 0;
    uint32_t EPN;
    uint32_t RPN;
    uint32_t tlb_entry_32;

    tlb_entry_32 = tlb_entry;
    tlb_entry_32 <<= 16;

    EPN = virtual_addr & PAGE_NUMBER_MASK;
    RPN = physic_addr & PAGE_NUMBER_MASK;

    value_MAS0 = (uint32_t)(tlb_entry_32 | TLB_SELECT);
    value_MAS1 = TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | tlb_page_size;
    value_MAS2 = EPN | CACHE_WRITE_THRU | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_GUARDED |PAGE_BIG_ENDIAN;
    value_MAS3 = RPN | MAS3_USR0 | USR_EXECUTE | SUP_EXECUTE | USR_WRITE | SUP_WRITE | USR_READ | SUP_READ;
  
    setSpecReg32SPR_MAS0(value_MAS0);
    setSpecReg32SPR_MAS1(value_MAS1);
    setSpecReg32SPR_MAS2(value_MAS2);
    setSpecReg32SPR_MAS3(value_MAS3);

    asm("wrteei 0");
    asm("mbar");
    asm("tlbwe");
    asm("isync");
    asm("wrteei 1");
} 

#endif /* _BUILD_MMU_ */
