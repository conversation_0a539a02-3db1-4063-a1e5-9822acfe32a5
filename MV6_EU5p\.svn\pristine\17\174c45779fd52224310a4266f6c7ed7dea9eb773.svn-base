#!gbuild
[Program]
	-I ..\tree\CONFIG\asm
	-I ..\tree\BIOS\COMMON
	-I ..\tree\BIOS\FLASH\include
	-I ..\tree\AK_OSEK\include
	-I ..\tree\CONFIG\C
	-I ..\tree\DD\COMMON
	-I ..\tree\EEPCOM
	:binDirRelative=.\bin\M3U36R
	-Mn
	-no_include_once
	-include M3U36R_config.h
	--no_trace_includes
	--register_definition_file=MPC56xx.grd
	-Olimit=peephole,pipeline
	-discard_zero_initializers
	-strict_overlap_check
	--no_commons
	--no_preprocess_linker_directive
	-o M3U36R.elf
	-I ..\tree\APPLICATION\COMMON
	-I ..\tree\CONFIG\asm\mpc5534
	-I ..\common
	-list
	-object_dir=obj\M3U36R
	:postexecShell='bin\\BatchRunnerPostLinker.bat M3U36R'
	-delete
	-full_macro_debug_info
	-full_debug_info
	--asm_silent
	--scan_source
	-passsource
	-g
	-dwarf2
	-inline_prologue
	-noentry
	-Ospeed
	-cpu=ppc563xm
DD_MV_F3.gpj
AK_OSEK.gpj
BIOS.gpj
DD_COMMON.gpj
Application.gpj
Appl_MV_F3.gpj
main.c
lcf\MPC5633.ld
CONFIG\C\M3U36R_config.h
