general {
	version = 1
#	byte_endian = "default"
	bit_0_is_msb = true
#	multi_command += "eval $MULTI_PPC_IMMR_BASE = 0x00000000;"
}

#enum {
#	"enable_disable_enum" {
#		auto_value_desc = false
#		"enabled" {value = 0}
#		"disabled" {value = 1}
#	}
#	"disable_enable_enum" {
#		auto_value_desc = false
#		"disabled" {value = 0}
#		"enabled" {value = 1}
#	}
#}

#%include "C:\GHS\ppc423\defaults\registers\ppc_5554_ebi_fmpll_ecsm_esci.grd"
#%include "C:\GHS\ppc423\defaults\registers\ppc_5554_emios_etpu_flash_intc.grd"
#%include "C:\GHS\ppc423\defaults\registers\ppc_5554_dspi_eqadc_can.grd"
#%include "C:\GHS\ppc423\defaults\registers\ppc_5554_siu_xbar_pbridge.grd"
#%include "C:\GHS\ppc423\defaults\registers\ppc_5554_edma.grd"

group {
	on_chip_peripherals {
		sn = "On-Chip Peripherals"
		ln = "On-Chip Peripherals"
		group = { "can", "dspi", "ebi", "ecsm", "emios", "eqadc" }
		group += { "esci", "etpu", "flash", "fmpll", "intc", "edma" }
		group += { "pbridge", "siux", "xbar" }
# had to name siux vs. siu, otherwise siu showed up in special group

	}
}

