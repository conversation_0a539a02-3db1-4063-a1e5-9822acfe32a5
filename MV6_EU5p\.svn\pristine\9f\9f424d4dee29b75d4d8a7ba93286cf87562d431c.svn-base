/*
 * File: AnalogQS_private.h
 *
 * Code generated for Simulink model 'AnalogQS'.
 *
 * Model version                  : 1.655
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Nov 16 11:29:39 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_AnalogQS_private_h_
#define RTW_HEADER_AnalogQS_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "AnalogQS.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "canmgm.h"
#include "digitalin.h"
#include "syncmgm.h"
#include "Engflag.h"
#include "analogin.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint32_T TIMVQSFAULT;           /* Variable: TIMVQSFAULT
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Fault time QS
                                        */
extern int16_T THVQSFAULTHIGH;         /* Variable: THVQSFAULTHIGH
                                        * Referenced by: '<S11>/THVQSFAULTHIGH'
                                        * faultt threshold
                                        */
extern int16_T THVQSFAULTLOW;          /* Variable: THVQSFAULTLOW
                                        * Referenced by: '<S11>/THVQSFAULTLOW'
                                        * faultt threshold
                                        */
extern int16_T THVQSHIGH;              /* Variable: THVQSHIGH
                                        * Referenced by: '<S11>/THVQSHIGH'
                                        * UP Shift threshold
                                        */
extern int16_T THVQSHIGHREL;           /* Variable: THVQSHIGHREL
                                        * Referenced by: '<S11>/THVQSHIGHREL'
                                        * threshold rel
                                        */
extern int16_T THVQSLOW;               /* Variable: THVQSLOW
                                        * Referenced by: '<S11>/THVQSLOW'
                                        * Down Shift threshold
                                        */
extern int16_T THVQSLOWREL;            /* Variable: THVQSLOWREL
                                        * Referenced by: '<S11>/THVQSLOWREL'
                                        * threshold rel
                                        */
extern uint16_T TIMVQSBLANKDN;         /* Variable: TIMVQSBLANKDN
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Blanking time QS
                                        */
extern uint16_T TIMVQSBLANKUP;         /* Variable: TIMVQSBLANKUP
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Blanking time QS
                                        */
extern uint16_T TIMVQSFINDDOWN;        /* Variable: TIMVQSFINDDOWN
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Time to find Down shift
                                        */
extern uint16_T TIMVQSFINDUP;          /* Variable: TIMVQSFINDUP
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Time to find Up shift
                                        */
extern uint16_T TIMVQSVALIDF;          /* Variable: TIMVQSVALIDF
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Time of validate fault
                                        */
extern uint8_T FOGSFLAGS;              /* Variable: FOGSFLAGS
                                        * Referenced by: '<S16>/FOGSFLAGS'
                                        * Flag forced
                                        */
extern uint8_T TIMAQSMATCH;            /* Variable: TIMAQSMATCH
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Time match threshold
                                        */
extern uint8_T TIMAQSMATCH1;           /* Variable: TIMAQSMATCH1
                                        * Referenced by: '<S7>/QS_Manager'
                                        * Time match threshold
                                        */
extern uint8_T VQSUPLOW;               /* Variable: VQSUPLOW
                                        * Referenced by: '<S7>/QS_Manager'
                                        * AQS Sensor polarity
                                        */
extern void AnalogQS_DiagQS(void);
extern void AnalogQS_DiagQS_o(void);
extern void AnalogQS_T5ms(void);
extern void AnalogQS_Reset(void);

#endif                                 /* RTW_HEADER_AnalogQS_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
