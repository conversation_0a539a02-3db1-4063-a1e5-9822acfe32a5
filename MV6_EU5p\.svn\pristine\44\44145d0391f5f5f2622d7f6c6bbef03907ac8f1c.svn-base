/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/MCU/appl_calib/trunk/tree/DD/COMMON/HeatGripDriveMg#$   */
/* $ Description:                                                                                                */
/* $Revision:: 6369   $                                                                                          */
/* $Date:: 2014-03-25 09:13:52 +0100 (mar, 25 mar 2014)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              TransportLock.h
 *
 * Author(s): Lana L.
 * 
 * 
 * Description:
 * 
 *
 * Usage notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifndef _TRPLOCK_H_
#define _TRPLOCK_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "Digitalin.h"
#include "Syncmgm.h"
#include "TransportLock_out.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define TRS_LOCK_NOT      0u
#define TRS_LOCK_LOCK     1u
#define TRS_LOCK_UNLOCK   2u
#define TRS_LOCK_T_UNLOCK 4u

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern uint8_T TRSLOCKTIME;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * TransportLock_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/

#endif 

/****************************************************************************
 ****************************************************************************/
 
