/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1394   $                                                                                          */
/* $Date:: 2009-06-24 17:47:22 +0200 (mer, 24 giu 2009)   $                                                      */
/* $Author:: GelmettiA               $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_RLI_

#ifndef _BUILD_DIAGCANMGM_
#error RLI module enabled without _BUILD_DIAGCANMGM_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "diagcanmgm.h"
#include "immo_app.h"
#include "pwrmgm.h"
#include "relaymgm.h"
#include "fuel_mgm.h"
#include "exhval_mgm.h"
#include "selfmgm.h"
#include "recmgm.h"
#include "pio.h"
#include "ign.h"
#include "inj.h"
#include "sync.h"
#include "TransportLock_out.h"
#include "thrposmgm.h"
#include "gaspos_mgm.h"
#include "lamp_mgm.h"
#include "Vspeed_mgm.h"
#include "watpumpmgm.h"
#include "activediag.h"
#include "af_ctrl.h"
#include "Ionmgm.h"
#include "lambda_mgm.h"
#include "Rli.h"
#include "mathlib.h"
#include "loadmgm.h"
#include "temp_mgm.h"
#include "analogin.h"
#include "air_mgm.h"
#include "ionacq.h"
#include "injcmd.h"
#include "idle_mgm.h"
#include "digitalin.h"
#include "syncmgm.h"
#include "throttle_adapt.h"
#include "immo_app.h"
#include "trq_est.h"
#include "ion_lambda.h"
#include "canmgm.h"
#include "Saf2Mgm_eep.h"
#include "Saf3Mgm_eep.h"
#include "saf3_mgm.h"
#include "thrposmgm.h"
#include "vspeed_mgm.h"
#include "Engflag.h"
#include "Syncmgm.h"
#include "vsrammgm.h"
#include "Analogin.h"
#include "HeatGripDriveMgm.h"
#include "trq_driver.h"
#include "launchctrl_out.h"
#include "Trc2wzero_out.h"
#include "antiTampering_out.h"
#include "ion_misf.h"
#include "stub.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

/* External from GearMgm.c */
int8_T FlgGearShift;        /* Stub, variable removed */

/* Externals from LampMgm.c */
extern uint8_T LOGLAMPOUT[NUMLOGLMP];

/* Externals from SelfMgm.c*/
extern uint8_T  FlgSelfDisable;



/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/

/*--------------------------------------------------------------------------*
 * rliConversion - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_t rliConversion(uint32_t value,
                               int8_t type_data);
/*--------------------------------------------------------------------------*
 * convOnOff - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_t convOnOff(uint32_t value,
                           int8_t type_data);

/*--------------------------------------------------------------------------*
 * convSession - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_t convSession(uint32_t value, 
                             int8_t type_data);

/*--------------------------------------------------------------------------*
 * convInjection - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_t convInjection(uint32_t value,
                               int8_t type_data);

/*--------------------------------------------------------------------------*
 * convGearShift - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_T convGearShift(uint32_t value, 
                               int8_t type_data);


/*--------------------------------------------------------------------------*
 * convIMMO - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 //static uint32_t convIMMO(uint32_t value, int8_t type_data);

/*--------------------------------------------------------------------------*
 * convWarningLamp - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convWarningLamp(uint32_t value, int8_t type_data);

/*--------------------------------------------------------------------------*
 * rliMaskConv - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 //static uint32_t rliMaskConv(uint32_t value, int8_t type_data);

/*--------------------------------------------------------------------------*
 * convAntiTamp - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convAntiTamp(uint32_t value, int8_t type_data);

/*--------------------------------------------------------------------------*
 * convStMisf - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_T convStMisf(uint32_T value, int8_T type_data);

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/

static uint32_t rli_unmapped = 0;
static uint8_T rli_FutureNowFF = 0xFF;

static uint32_t (* const Rli_function[NUM_OF_RLIFUNC])(uint32_t value, int8_t type_data) =
{
    rliConversion,      /* FUNC_RLICONV   */
    convOnOff,          /* FUNC_CONVONOFF */
    convSession,        /* FUNC_SESSIONCONV  */
    convInjection,      /* FUNC_INJCONV */
    convGearShift,      /* FUNC_GEAR_SHIFT*/
    convWarningLamp,    /* FUNC_WRNLAMPST */
    convAntiTamp,       /* FUNC_ANTITAMPER */
    convStMisf          /* FUNC_CONVSTMISF */
};


static const rliDataStruct rliData[RLI_LENGTH] = {

//----------------------------------------------------------------------------------------//
//         rli  | w | sz|   pointer data               | k | a | b | c | d | e | f |.. .. .. .  conv factor    //
//----------------------------------------------------------------------------------------//
//-----------------------------------------------------------------------------------------//
//     ------------
//   | RAM data |
//-----------------------------------------------------------------------------------
/*0x01*/{   2  ,  (uint32_t )&EngStressWarm                ,0,   FUNC_RLICONV      }, 
/*0x02*/{   2  ,  (uint32_t )&TrackUseTime                 ,0,   FUNC_RLICONV      }, 
/*0x03*/{   4  ,  (uint32_t )&Odometer                     ,0,   FUNC_RLICONV      }, 
/*0x04*/{   2  ,  (uint32_t )&EOHours                      ,0,   FUNC_RLICONV      }, 
/*0x05*/{   1  ,  (uint32_t )&CrashEventCnt                ,0,   FUNC_RLICONV      }, 
/*0x06*/{   1  ,  (uint32_t )&StressCondResetCnt           ,0,   FUNC_RLICONV      }, 
/*0x07*/{   1  ,  (uint32_t )&HourResetCnt                 ,0,   FUNC_RLICONV      }, 
/*0x08*/{   1  ,  (uint32_t )&EngOverCnt                   ,0,   FUNC_RLICONV      }, 
/*0x09*/{   1  ,  (uint32_t )&EngOverDur                   ,0,   FUNC_RLICONV      }, 
/*0x0A*/{   1  ,  (uint32_t )&MaxRpmOver                   ,0,   FUNC_RLICONV      }, 
/*0x0B*/{   1  ,  (uint32_t )&EcuFlashingCnt               ,0,   FUNC_RLICONV      }, 
/*0x0C*/{   2  ,  (uint32_t )&OdometerLastFlash            ,0,   FUNC_RLICONV      }, 
/*0x0D*/{   2  ,  (uint32_t )&WlampOnTime                  ,0,   FUNC_RLICONV      }, 
/*0x0E*/{   2  ,  (uint32_t )&SafLampOnTime                ,0,   FUNC_RLICONV      }, 
/*0x0F*/{   2  ,  (uint32_t )&EngStressCold                ,0,   FUNC_RLICONV      }, 
/*0x10*/{   1  ,  (uint32_t )&rli_unmapped                 ,2,   FUNC_CONVONOFF    }, /* Segnale DRL */
#ifdef _BUILD_SAF2MGM_
/*0x1A*/{   1  ,  (uint32_t )&S2PtFault                    ,0,   FUNC_RLICONV      }, /* */
#else
/*0x1A*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* */
#endif
#ifdef _BUILD_SAF3MGM_
/*0x1B*/{   1  ,  (uint32_t )&S3StopCause                  ,0,   FUNC_RLICONV      }, /* Errore di safety3*/
#else
/*0x1B*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* Errore di safety3*/
#endif
/*0x1C*/{   2  ,  (uint32_t )&VLC4Raw                      ,0,   FUNC_RLICONV      }, /* Tensione su L-C4 del connettore */
/*0x1D*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* USELA3SIGNAL */ 
/*0x1F*/{   1  ,  (uint32_t )&KWPsession                   ,0,   FUNC_SESSIONCONV  }, /* Stato diagnosi attiva*/
/*0x21*/{   2  ,  (uint32_t )&OdoMILActive                 ,0,   FUNC_RLICONV      }, /* Km with MIL active*/
/*0x2A*/{   4  ,  (uint32_t )&EEECULoadMax                 ,0,   FUNC_RLICONV      }, /* <RESERVED 1> */
/*0x2B*/{   4  ,  (uint32_t )&EEStoreInc                   ,0,   FUNC_RLICONV      }, /* <RESERVED 2> */
/*0x30*/{   2  ,  (uint32_t )&Rpm                          ,0,   FUNC_RLICONV      }, /* Giri Motore*/
/*0x31*/{   2  ,  (uint32_t )&Load                         ,0,   FUNC_RLICONV      }, /* Carico Motore*/
/*0x32*/{   2  ,  (uint32_t )&TWater                       ,0,   FUNC_RLICONV      }, /* Temperatura Acqua*/
/*0x33*/{   2  ,  (uint32_t )&TWaterModel                  ,0,   FUNC_RLICONV      }, /* Temperatura Acqua Modellata*/
/*0x34*/{   2  ,  (uint32_t )&VTWater                      ,0,   FUNC_RLICONV      }, /* Tensione Sensore temperatura acqua*/
/*0x35*/{   2  ,  (uint32_t )&TAir                         ,0,   FUNC_RLICONV      }, /* Temperatura Aria*/
/*0x36*/{   2  ,  (uint32_t )&VTAir                        ,0,   FUNC_RLICONV      }, /* Tensione Sensore temperatura aria*/
/*0x37*/{   2  ,  (uint32_t )&GasPosCC                     ,0,   FUNC_RLICONV      }, /* Posizione Gas*/
/*0x38*/{   1  ,  (uint32_t )&IndUsedSens                  ,2,   FUNC_RLICONV      }, /* Potenziometro Gas in uso*/
/*0x39*/{   1  ,  (uint32_t )&StIdleSwitch                 ,3,   FUNC_RLICONV      }, /* Stato switch Gas*/
/*0x3A*/{   2  ,  (uint32_t )&VGasPos1                     ,0,   FUNC_RLICONV      }, /* Tensione Sensore angolo gas 1*/
/*0x3C*/{   2  ,  (uint32_t )&VGasPos2                     ,0,   FUNC_RLICONV      }, /* Tensione Sensore angolo gas 2*/
/*0x3D*/{   2  ,  (uint32_t )&AngThrottle                  ,0,   FUNC_RLICONV      }, /* Posizione Farfalla*/
/*0x3E*/{   1  ,  (uint32_t )&StThrActive                  ,2,   FUNC_RLICONV      }, /* Potenziometro Farfalla in uso*/
/*0x3F*/{   2  ,  (uint32_t )&VAngThrottle1                ,0,   FUNC_RLICONV      }, /* Tensione Sensore angolo farfalla 1*/
/*0x40*/{   2  ,  (uint32_t )&VAngThrottle2                ,0,   FUNC_RLICONV      }, /* Tensione Sensore angolo farfalla 2*/
/*0x41*/{   2  ,  (uint32_t )&VAngThrLh1                   ,0,   FUNC_RLICONV      }, /* Posizione Limp Home potenziometro 1*/
/*0x42*/{   2  ,  (uint32_t )&VAngThrLh2                   ,0,   FUNC_RLICONV      }, /* Posizione Limp Home potenziometro 2*/
/*0x43*/{   2  ,  (uint32_t )&VAngThrMin1                  ,0,   FUNC_RLICONV      }, /* Posizione Meccanica onferiore pot. 1*/
/*0x44*/{   2  ,  (uint32_t )&VAngThrMin2                  ,0,   FUNC_RLICONV      }, /* Posizione Meccanica onferiore pot. 2*/
/*0x45*/{   1  ,  (uint32_t )&StDbwSelf                    ,8,   FUNC_RLICONV      }, /* Stato Self Learning farfalla*/
/*0x46*/{   1  ,  (uint32_t )&StDbwSelfError               ,0,   FUNC_RLICONV      }, /* stato errore self learning*/
/*0x47*/{   2  ,  (uint32_t )&DAngThr                      ,0,   FUNC_RLICONV      }, /* Correzione Angolo farfalla*/
/*0x48*/{   1  ,  (uint32_t )&GearPos                      ,0,   FUNC_RLICONV      }, /* Posizione Marce */
/*0x49*/{   2  ,  (uint32_t )&VGearPos                     ,0,   FUNC_RLICONV      }, /* Tensione sensore Posizione marce */
/*0x4A*/{   2  ,  (uint32_t )&VAngExhVal                   ,0,   FUNC_RLICONV      }, /*  */
/*0x4B*/{   4  ,  (uint32_t )&AngExhValPerc                ,0,   FUNC_RLICONV      }, /*  */
/*0x4C*/{   2  ,  (uint32_t )&AngExhTrg                    ,0,   FUNC_RLICONV      }, /*  */
/*0x4D*/{   1  ,  (uint32_t )&ClutchSignal                 ,0,   FUNC_CONVONOFF    }, /* Clutch state */
/*0x50*/{   2  ,  (uint32_t )&VGasIDLSw                    ,0,   FUNC_RLICONV      }, /*  */
/*0x51*/{   2  ,  (uint32_t )&VehSpeedFront                ,0,   FUNC_RLICONV      }, /* FUTURE - VSPEEDFRONT_ID  */
/*0x52*/{   2  ,  (uint32_t )&VehSpeedRearAnDrift          ,0,   FUNC_RLICONV      }, /*  */
/*0x53*/{   2  ,  (uint32_t )&PresIntake                   ,0,   FUNC_RLICONV      }, /* Pressione Aria collettore di aspirazione*/
/*0x54*/{   2  ,  (uint32_t )&VMapSignal                   ,0,   FUNC_RLICONV      }, /* Tensione Sensore pressione collettore*/
/*0x55*/{   2  ,  (uint32_t )&PresAtm                      ,0,   FUNC_RLICONV      }, /*  */
/*0x56*/{   2  ,  (uint32_t )&PresIntk0                    ,0,   FUNC_RLICONV      }, /* Pressione Aria collettore di aspirazione di recovery*/
/*0x57*/{   2  ,  (uint32_t )&VBattery                     ,0,   FUNC_RLICONV      }, /* Tensione Batteria linearizzata*/
/*0x58*/{   2  ,  (uint32_t )&SAout                        ,0,   FUNC_RLICONV      }, /* Anticipo Attuato*/
/*0x59*/{   1  ,  (uint32_t )&StEcu                        ,6,   FUNC_RLICONV      }, /* Stato Ecu*/
/*0x5A*/{   2  ,  (uint32_t )&VGearPos0                    ,0,   FUNC_RLICONV      }, /* VGearPos0*/
/*0x5B*/{   2  ,  (uint32_t )&VLamP                        ,0,   FUNC_RLICONV      }, /* VLamP*/
/*0x5C*/{   2  ,  (uint32_t )&VSparkPeakCyl                ,0,   FUNC_RLICONV      }, /* VSparkPeakCyl*/
/*0x5D*/{   2  ,  (uint32_t )&VCrashSignal                 ,0,   FUNC_RLICONV      }, /* VCrashSignal*/
/*0x5E*/{   1  ,  (uint32_t )&DiagBrakeLamp                ,0,   FUNC_RLICONV      }, /* DiagBrakeLamp*/
/*0x5F*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* USELB3SIGNAL */ 
/*0x60*/{   4  ,  (uint32_t )&EffDwellTime[0]              ,0,   FUNC_RLICONV      }, /* Tempo di carica bobina1 &EffDwellTime1 */
/*0x61*/{   4  ,  (uint32_t )&EffDwellTime[1]              ,0,   FUNC_RLICONV      }, /*  */
/*0x62*/{   4  ,  (uint32_t )&EffDwellTime[2]              ,0,   FUNC_RLICONV      }, /*  */
#if (N_CYLINDER == 4)
/*0x63*/{   4  ,  (uint32_t )&EffDwellTime[3]              ,0,   FUNC_RLICONV      }, /* FUTURE - EFFDWELLTIME4_ID */
#else
/*0x63*/{   4,    (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* FUTURE - INJTIMR4_ID */
#endif
/*0x64*/{   4  ,  (uint32_t )&InjTimePrg[0]                ,0,   FUNC_INJCONV/*FUNC_RLICONV*/      }, /*InjTime_00 Tempo di iniezione programmato*/
/*0x65*/{   4  ,  (uint32_t )&InjTimePrg[1]                ,0,   FUNC_INJCONV/*FUNC_RLICONV*/      }, /*InjTime_01 Tempo di iniezione programmato*/
/*0x66*/{   4  ,  (uint32_t )&InjTimePrg[2]                ,0,   FUNC_INJCONV/*FUNC_RLICONV*/      }, /*InjTime_02 Tempo  di iniezione programmato*/
#if (N_CYLINDER == 4)
/*0x67*/{   4  ,  (uint32_t )&InjTimePrg[3]                ,0,   FUNC_INJCONV      }, /* InjTime_03 Tempo  di iniezione programmato */
#else
/*0x67*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* UNMAPPED */
#endif
#ifdef N_INJECTOR_HB
/*0x68*/{   4  ,  (uint32_t )&InjTimePrg[4]                ,0,   FUNC_INJCONV/*FUNC_RLICONV*/      }, /*InjTime_04 Tempo di iniezione programmato*/
/*0x69*/{   4  ,  (uint32_t )&InjTimePrg[5]                ,0,   FUNC_INJCONV/*FUNC_RLICONV*/      }, /*InjTime_05 Tempo di iniezione programmato*/
/*0x6A*/{   4  ,  (uint32_t )&InjTimePrg[6]                ,0,   FUNC_INJCONV/*FUNC_RLICONV*/      }, /*InjTime_06 Tempo  di iniezione programmato*/
#if (N_CYLINDER == 4)
/*0x6B*/{   4  ,  (uint32_t )&InjTimePrg[7]                ,0,   FUNC_INJCONV      }, /* InjTime_07 Tempo  di iniezione programmato */
#else
/*0x6B*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* UNMAPPED */
#endif
#else
/*0x68*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* Tempo di iniezione programmato*/
/*0x69*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* Tempo di iniezione programmato*/
/*0x6A*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* Tempo  di iniezione programmato*/
/*0x6B*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* FUTURE - INJTIMR8_ID */
#endif
/*0x6C*/{   1  ,  (uint32_t )&GearDownSignal               ,0,   FUNC_RLICONV      }, /* Down shift flag */
/*0x6D*/{   1  ,  (uint32_t )&GearUpSignal                 ,0,   FUNC_RLICONV      }, /* Up shift flag */
/*0x6E*/{   1  ,  (uint32_t )&RearPosDrv                   ,0,   FUNC_CONVONOFF    }, /* Comando luci posizione */
/*0x6F*/{   2  ,  (uint32_t )&HeatGripOut                  ,0,   FUNC_RLICONV      }, /* PWM del riscaldatore manopole */
/*0x70*/{   2  ,  (uint32_t )&RpmIdleObj0                  ,0,   FUNC_RLICONV      }, /* Regime di minimo obiettivo*/
/*0x71*/{   2  ,  (uint32_t )&IdleTrqI                     ,0,   FUNC_RLICONV      }, /* Instantaneous idle speed control torque output */
/*0x72*/{   2  ,  (uint32_t )&CmfP                         ,0,   FUNC_RLICONV      }, /* Coppia di friction*/
/*0x73*/{   2  ,  (uint32_t )&QAirCyl                      ,0,   FUNC_RLICONV      }, /* Portata aria aspirata*/
/*0x74*/{   2  ,  (uint32_t )&QFuelCyl                     ,0,   FUNC_RLICONV      }, /* Portata Benzina*/
/*0x75*/{   2  ,  (uint32_t )&VLambda                      ,0,   FUNC_RLICONV      }, /* Tensione sonda Lambda*/
/*0x76*/{   1  ,  (uint32_t )&VLambdaState                 ,3,   FUNC_RLICONV      }, /* Stato Sonda Lambda */
/*0x77*/{   1  ,  (uint32_t )&StAFCtrl                     ,0,   FUNC_CONVONOFF    }, /* Stato Controllo Titolo*/
/*0x78*/{   2  ,  (uint32_t )&DeltaLamCL                   ,0,   FUNC_RLICONV      }, /* Correzione Controllo titolo*/
/*0x79*/{   2  ,  (uint32_t )&DeltaLamCorrAd               ,0,   FUNC_RLICONV      }, /* Correzione Controllo titolo adattativa*/
/*0x7A*/{   2  ,  (uint32_t )&TWater2                      ,0,   FUNC_RLICONV      }, /* Engine coolant temperature 2 */
/*0x7B*/{   2  ,  (uint32_t )&VTWater2                     ,0,   FUNC_RLICONV      }, /* Engine coolant temperature sensor 2 */
/*0x7C*/{   2  ,  (uint32_t )&VSens1                       ,0,   FUNC_RLICONV      }, /* Sensor supply voltage 1 */
/*0x7D*/{   2  ,  (uint32_t )&VSens2                       ,0,   FUNC_RLICONV      }, /* Sensor supply voltage 2 */
/*0x7E*/{   1  ,  (uint32_t )&BrakeSignal                  ,2,   FUNC_CONVONOFF    }, /* Segnale freno */
/*0x7F*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* USELC4SIGNAL */ 
/*0x80*/{   1  ,  (uint32_t )&InjEnable                    ,2,   FUNC_CONVONOFF    }, /* Stato abilitazione Iniezione*/
/*0x81*/{   1  ,  (uint32_t )&KeySignal                    ,2,   FUNC_CONVONOFF    }, /* Stato sincronizzazione ruota fonica*/
/*0x82*/{   1  ,  (uint32_t )&StopSignal                   ,1,   FUNC_CONVONOFF    }, /* Stato pulsante Run-Stop*/
/*0x83*/{   1  ,  (uint32_t )&CrashSignal                  ,1,   FUNC_CONVONOFF    }, /* Stato Crash Switch*/
/*0x84*/{   1  ,  (uint32_t )&TrestleSignal                ,1,   FUNC_CONVONOFF    }, /* Stato cavalletto*/
/*0x85*/{   1  ,  (uint32_t )&S2FlgDisLRelay               ,1,   FUNC_CONVONOFF    }, /*  */
/*0x86*/{   1  ,  (uint32_t )&S3FlgAllowStart              ,2,   FUNC_CONVONOFF    }, /*  */
/*0x87*/{   1  ,  (uint32_t )&S3FlgDisLRelay               ,1,   FUNC_CONVONOFF    }, /*  */
/*0x88*/{   1  ,  (uint32_t )&rli_FutureNowFF              ,0,   FUNC_RLICONV      }, /* FUTURE */
/*0x89*/{   1  ,  (uint32_t )&VtRec[REC_ENG_OFF]           ,1,   FUNC_CONVONOFF    }, /* VtRec_11   */
/*0x8A*/{   1  ,  (uint32_t )&FanCoilCmd                   ,0,   FUNC_CONVONOFF    }, /* Stato Comando Elettroventole*/
/*0x8B*/{   1  ,  (uint32_t )&rli_FutureNowFF              ,0,   FUNC_RLICONV      }, /* FANCOILCMD2_ID */
/*0x8C*/{   1  ,  (uint32_t )&StartSignal                  ,0,   FUNC_CONVONOFF    }, /* Stato  */
/*0x8D*/{   1  ,  (uint32_t )&EngineStartCmd               ,0,   FUNC_CONVONOFF    }, /* Stato Comando motorino avviamento*/
/*0x8E*/{   1  ,  (uint32_t )&LoadCmd                      ,2,   FUNC_CONVONOFF    }, /* Stato Comando relay carichi*/
/*0x8F*/{   1  ,  (uint32_t )&PhysLampState[0]             ,0,   FUNC_WRNLAMPST    }, /*    */
/*0x90*/{   1  ,  (uint32_t )&LowBeamCmd                   ,0,   FUNC_CONVONOFF    }, /*    */
/*0x91*/{   1  ,  (uint32_t )&rli_FutureNowFF              ,0,   FUNC_RLICONV      }, /* FUTURE */
/*0x92*/{   1  ,  (uint32_t )&RidingMode                   ,4,   FUNC_RLICONV      }, /*    */
/*0x93*/{   1  ,  (uint32_t )&SetTracCtrl                  ,0,   FUNC_RLICONV      }, /*    */
/*0x94*/{   1  ,  (uint32_t )&StSync                       ,3,   FUNC_RLICONV      }, /* Stato sincronizzazione ruota fonica*/
/*0x95*/{   1  ,  (uint32_t )&FlgSyncPhased                ,0,   FUNC_CONVONOFF    }, /* Stato fasatura motore*/
/*0x96*/{   1  ,  (uint32_t )&FlgVBatResync                ,0,   FUNC_CONVONOFF    }, /* Stato fasatura motore*/
/*0x97*/{   1  ,  (uint32_t )&StPurgeFuelLine              ,0,   FUNC_RLICONV      }, /* stato procedura spurgo*/
/*0x98*/{   4  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      },
/*0x99*/{   1  ,  (uint32_t )&RecThrReq                    ,3,   FUNC_RLICONV      }, /* Stato recovery diagnosi farfalla*/
/*0x9A*/{   1  ,  (uint32_t )&RecLimTorqueGas              ,4,   FUNC_RLICONV      }, /* stato recovery diagnosi potenziometro*/
/*0x9B*/{   1  ,  (uint32_t )&VehOptConfig                 ,0,   FUNC_RLICONV      }, /* Optional accessories configuration */   
/*0x9C*/{   2  ,  (uint32_t )&RollCAN                      ,0,   FUNC_RLICONV      }, /* Vehicle Roll Angle Value */
/*0x9D*/{   2  ,  (uint32_t )&VGearShift                   ,0,   FUNC_RLICONV      }, /* Vehicle Roll Angle Value */
/*0x9E*/{   2  ,  (uint32_t )&PitchCAN                     ,0,   FUNC_RLICONV      }, /* Vehicle Pitch Angle Value */
/*0x9F*/{   2  ,  (uint32_t )&VLB3Raw                      ,0,   FUNC_RLICONV      }, /* Tensione su L-B3 del connettore */
/*0xA0*/{INJECTION_LENGHT    ,  (uint32_t )NULL            ,0,   FUNC_RLICONV      }, /* Injection "Snapshot" */
/*0xA1*/{STRESSCONTROL_LENGHT,  (uint32_t )NULL            ,0,   FUNC_RLICONV      }, /* Stress Control "Snapshot" */
/*0xA2*/{   1  ,  (uint32_T )&StPhase[0]                   ,0,   FUNC_RLICONV      }, /* Ion State (cyl0) */
/*0xA3*/{   1  ,  (uint32_T )&StPhase[1]                   ,0,   FUNC_RLICONV      }, /* Ion State (cyl1) */
/*0xA4*/{   1  ,  (uint32_T )&StPhase[2]                   ,0,   FUNC_RLICONV      }, /* Ion State (cyl2) */
/*0xA5*/{   1  ,  (uint32_T )&StPhase[3]                   ,0,   FUNC_RLICONV      }, /* Ion State (cyl3) */
/*0xA6*/{   2  ,  (uint32_T )&IntIon[0]                    ,0,   FUNC_RLICONV      }, /* Integral of the ion current signal (cyl0) */
/*0xA7*/{   2  ,  (uint32_T )&IntIon[1]                    ,0,   FUNC_RLICONV      }, /* Integral of the ion current signal (cyl1) */
/*0xA8*/{   2  ,  (uint32_T )&IntIon[2]                    ,0,   FUNC_RLICONV      }, /* Integral of the ion current signal (cyl2) */
/*0xA9*/{   2  ,  (uint32_T )&IntIon[3]                    ,0,   FUNC_RLICONV      }, /* Integral of the ion current signal (cyl3) */
/*0xB3*/{   2  ,  (uint32_t )&CntCrankingEE                ,0,   FUNC_RLICONV      }, /* Contatore cranking */
/*0xB4*/{   1  ,  (uint32_t )&PhysLampState[1]             ,1,   FUNC_WRNLAMPST    }, /* Physical lamp state */
/*0xB5*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_CONVONOFF    }, /* Riding Mode switch select */
/*0xB6*/{   1  ,  (uint32_t )&rli_unmapped                 ,0,   FUNC_RLICONV      }, /* USELA3SIGNAL */ 
/*0xB7*/{   1  ,  (uint32_t )&FlgSelfDisable               ,0,   FUNC_RLICONV      }, /* DBW self learning disable flag */
/*0xB8*/{   1  ,  (uint32_t )&ATEnable                     ,0,   FUNC_CONVONOFF    }, /* Anti-Tampering Enable Status */
/*0xB9*/{   1  ,  (uint32_t )&ATRoutineEnable              ,0,   FUNC_ANTITAMPER   }, /* Anti-Tampering Routine Enable Status */
/* 
REQ_MV_EM2.0_AT_19:
ATroutineFlag (Msg Dashboard, ID:0x20, start bit 35; lenght 1) signal shall be linked to Anti-Tampering Routine Result Status (RLI 0xBA)
*/
/*0xBA*/{   1  ,  (uint32_t )&ATroutineFlagCAN             ,0,   FUNC_ANTITAMPER   }, /* Anti-Tampering Routine Result Status Flag*/
/*0xBB*/{   1  ,  (uint32_t )&InjVINEnable                 ,0,   FUNC_RLICONV      }, /* Injection VIN Enable Status */
/*0xBC*/{   2  ,  (uint32_t )&CntInjVINEE                  ,0,   FUNC_RLICONV      }, /* Injection VIN Failure Counter */
/*0xBD*/{   VIN_LENGTH ,  (uint32_t )NULL                  ,0,   FUNC_RLICONV      }, /* VIN Dashboard */
/*0xBE*/{   VIN_LENGTH ,  (uint32_t )NULL                  ,0,   FUNC_RLICONV      }, /* VIN ECU */
/*0xBF*/{   1  ,  (uint32_t )&MainRelCmd                   ,1,   FUNC_CONVONOFF    }, /* Main relay */ 
/*0xC1*/{   2  ,  (uint32_t )&CntRpmLimStressRLI           ,0,   FUNC_RLICONV      }, /* Rpm lim stress */ 
/*0xC2*/{   2  ,  (uint32_t )&EEGnVehSpeedFront            ,0,   FUNC_RLICONV      }, /* VehSpeedFront gain */ 
/*0xC3*/{   1  ,  (uint32_t )&QuickShiftEnCAN              ,2,   FUNC_CONVONOFF    }, /* Quick enable */ 
/*0xC4*/{   1  ,  (uint32_t )&QuickShiftTypeCAN            ,2,   FUNC_CONVONOFF    }, /* Quick type */ 
/*0xC5*/{   2  ,  (uint32_t )&EEGnVehSpeedFront            ,0,   FUNC_RLICONV      }, /* Tc zero */ 
/*0xC6*/{   4  ,  (uint32_t )&EEIDVersion                  ,0,   FUNC_RLICONV      }, /* ID Version */
/*0xC7*/{   8  ,  (uint32_t )&StMisf[0]                    ,0,   FUNC_CONVSTMISF   }, /* Misfire State */
/*0xC8*/{   8  ,  (uint32_t )&IonSelect[0]                 ,0,   FUNC_RLICONV      }, /* Select Circuit on Ion acquisition */
/*0xC9*/{  16  ,  (uint32_t )&Start_Ch[0]                  ,0,   FUNC_RLICONV      }, /* Start of Chemical phase */
/*0xCA*/{  16  ,  (uint32_t )&Start_Th[0]                  ,0,   FUNC_RLICONV      }, /* Start of Thermal phase */
/*0xCB*/{  16  ,  (uint32_t )&ThPeak[0]                    ,0,   FUNC_RLICONV      }, /* Peak of Thermal phase */
/*0xCD*/{   1  ,  (uint32_t )&IonAbsTdcDT                  ,0,   FUNC_RLICONV      }, /* Cylinder for ion acquisition selection */ 
/*0xCE*/{   IUMPR_LENGTH ,  (uint32_t )NULL                ,0,   FUNC_RLICONV      }, /* In-Use Performance monitoring Ratio */
/*0xCF*/{   1  ,  (uint32_t )&DRLCmd                       ,1,   FUNC_CONVONOFF    }, /* DRL */
/*0xD0*/{   2  ,  (uint32_T )&CntTdcCrk                    ,0,   FUNC_RLICONV      }, /* EndStartFlg TDC counter */
/*0xDE*/{   1  ,  (uint32_t )&LampOnTimeResetCnt           ,0,   FUNC_RLICONV      },
/*0xE2*/{   1  ,  (uint32_t )&TempUnlockCntDown            ,0,   FUNC_RLICONV      },
/*0xE3*/{   1  ,  (uint32_t )&TransportLockEcu             ,0,   FUNC_RLICONV      },
/*0xE4*/{   1  ,  (uint32_t )&CrankEnable                  ,0,   FUNC_RLICONV      },
/*0xE5*/{   2  ,  (uint32_t )&EELcTrip                     ,0,   FUNC_RLICONV      },
/*0xE6*/{   2  ,  (uint32_t )&VLambda2                     ,0,   FUNC_RLICONV      }, // Lambda 2 Sensor Voltage
/*0xE7*/{   4  ,  (uint32_t )&RatioCatDiag                 ,0,   FUNC_RLICONV      }, // Ratio CAT
/*0xE8*/{   2  ,  (uint32_t )&DCLamHeater2                 ,0,   FUNC_RLICONV      }, // Sensor supply voltage 1
/*0xEF*/{   1  ,  (uint32_t )&FlgEOL                       ,2,   FUNC_RLICONV      }
};

static const uint8_T RliKey[RLI_LENGTH] = 
{
/*0x01*/    ENGSTREEWARM_ID    ,
/*0x02*/    TRACKUSETIME_ID    ,
/*0x03*/    ODOMETER_ID        ,
/*0x04*/    OPERATINGHOURS_ID  ,
/*0x05*/    CRASHEVENTCNT_ID   ,
/*0x06*/    STRESSCONDEVTCNT_ID,
/*0x07*/    OPERGHOURSRSTCNT_ID,
/*0x08*/    OVERDRIVECNT_ID    ,
/*0x09*/    LENGOVERDRIVEDUR_ID,
/*0x0A*/    MAXENGOVERDRIVE_ID ,
/*0x0B*/    ECUFLSHCNT_ID      ,
/*0x0C*/    LASTECUFLSHODOM_ID ,
/*0x0D*/    WARLAMPONTIME_ID   ,
/*0x0E*/    SAFONTIME_ID       ,
/*0x0F*/    ENGINESTREESCOLD_ID,
/*0x10*/    DRLSIGNAL_ID       ,
/*0x1A*/    S2FAULT_ID         ,
/*0x1B*/    S3STOPCAUSE_ID     ,
/*0x1C*/    VLC4RAW_ID         ,
/*0x1D*/    BUTTONMAP_ID       ,
/*0x1F*/    KWPSESSION_ID      ,
/*0x21*/    ODOMILACTIVE_ID    ,
/*0x2A*/    RESERVED_1_ID      ,
/*0x2B*/    RESERVED_2_ID      ,
/*0x30*/    RPM_ID             ,
/*0x31*/    LOAD_ID            ,
/*0x32*/    TWATER_ID          ,
/*0x33*/    TWATERMODEL_ID     ,
/*0x34*/    VTWATER_ID         ,
/*0x35*/    TAIR_ID            ,
/*0x36*/    VTAIR_ID           ,
/*0x37*/    GASPOS_ID          ,
/*0x38*/    INDUSEDSENS_ID     ,
/*0x39*/    GASSWITCHSTATUS_ID ,
/*0x3A*/    VGASPOS1_ID        ,
/*0x3C*/    VGASPOS2_ID        ,
/*0x3D*/    ANGTHROTTLE_ID     ,
/*0x3E*/    STTHRACTIVE_ID     ,
/*0x3F*/    VANGTHROTTLE1_ID   ,
/*0x40*/    VANGTHROTTLE2_ID   ,
/*0x41*/    VANGTHRLH1_ID      ,
/*0x42*/    VANGTHRLH2_ID      ,
/*0x43*/    VANGTHRMIN1_ID     ,
/*0x44*/    VANGTHRMIN2_ID     ,
/*0x45*/    STDBWSELF_ID       ,
/*0x46*/    STDBWSELFERROR_ID  ,
/*0x47*/    DANGTHR_ID         ,
/*0x48*/    GEARPOS_ID         ,
/*0x49*/    VGEARPOS_ID        ,
/*0x4A*/    EXVALVSENSV_ID     ,
/*0x4B*/    EXVALVPOS_ID       ,
/*0x4C*/    EXVALVTARGPOS_ID   ,
/*0x4D*/    CLUTCHSTATE_ID     ,
/*0x50*/    GASSWITCH_ID       ,
/*0x51*/    VSPEEDFRONT_ID     ,
/*0x52*/    VSPEEDREAR_ID      ,
/*0x53*/    PRESINTAKE_ID      ,
/*0x54*/    VMAPSIGNAL_ID      ,
/*0x55*/    PRESATM_ID         ,
/*0x56*/    PRESINTK0_ID       ,
/*0x57*/    VBATTERY_ID        ,
/*0x58*/    SAOUT_ID           ,
/*0x59*/    STECU_ID           ,
/*0x5A*/    VGEARPOS0_ID       ,
/*0x5B*/    VLAMP_ID           ,
/*0x5C*/    VSPARKPEAKCYL_ID   ,
/*0x5D*/    VCRASHSIGNAL_ID    ,
/*0x5E*/    DIAGBRAKELAMP_ID   ,
/*0x5F*/    USELB3SIGNAL_ID    ,
/*0x60*/    EFFDWELLTIME1_ID   ,
/*0x61*/    EFFDWELLTIME2_ID   ,
/*0x62*/    EFFDWELLTIME3_ID   ,
/*0x63*/    EFFDWELLTIME4_ID   ,
/*0x64*/    INJTIME1_ID        ,
/*0x65*/    INJTIME2_ID        ,
/*0x66*/    INJTIME3_ID        ,
/*0x67*/    INJTIMR4_ID        ,
/*0x68*/    INJTIME5_ID        ,
/*0x69*/    INJTIME6_ID        ,
/*0x6A*/    INJTIME7_ID        ,
/*0x6B*/    INJTIMR8_ID        ,
/*0x6C*/    GEARDOWNSIGNAL_ID  ,
/*0x6D*/    GEARUPSIGNAL_ID    ,
/*0x6E*/    REARPOS_ID         ,
/*0x6F*/    HEATEDGRIP_ID      ,
/*0x70*/    RPMIDLEOBJ_ID      ,
/*0x71*/    IDLETRQ_ID         ,
/*0x72*/    CMFP_ID            ,
/*0x73*/    QAIRCYL_ID         ,
/*0x74*/    QFUELCY_ID         ,
/*0x75*/    VLAMBDA_ID         ,
/*0x76*/    VLAMBDASTATE_ID    ,
/*0x77*/    STAFCTRL_ID        ,
/*0x78*/    DELTALAMCL_ID      ,
/*0x79*/    DELTALAMCORRAD_ID  ,
/*0x7A*/    ENGCOOLTEMP2_ID    ,
/*0x7B*/    ENGCOOLTEMPSENS2_ID,
/*0x7C*/    VSENSSUPPLY1_ID    ,
/*0x7D*/    VSENSSUPPLY2_ID    ,
/*0x7E*/    BRAKESIGNAL_ID     ,
/*0x7F*/    USELC4SIGNAL_ID    ,
/*0x80*/    INJENABLE_ID       ,
/*0x81*/    KEYSIGNAL_ID       ,
/*0x82*/    STOPSIGNAL_ID      ,
/*0x83*/    CRASHSIGNAL_ID     ,
/*0x84*/    TRESTLESIGNAL_ID   ,
/*0x85*/    S2FLGDISRELAY_ID   ,
/*0x86*/    S3FLGALLOWSTART_ID ,
/*0x87*/    S3FLGDISRELAY_ID   ,
/*0x88*/    INJENABLECAN_ID    ,
/*0x89*/    VTREC11_ID         ,
/*0x8A*/    FANCOILCMD_ID      ,
/*0x8B*/    FANCOILCMD2_ID     ,
/*0x8C*/    STARTSIGNAL_ID     ,
/*0x8D*/    ENGINESTARTCMD_ID  ,
/*0x8E*/    LOADCMD_ID         ,
/*0x8F*/    LAMCAMCMD_ID       ,
/*0x90*/    LOWBEAMCMD_ID      ,
/*0x91*/    REARLIGHTSTATUS_ID ,
/*0x92*/    RIDINGMODE_ID      ,
/*0x93*/    SETTRACCTRL_ID     ,
/*0x94*/    STSYNC_ID          ,
/*0x95*/    FLGSYNCPHASED_ID   ,
/*0x96*/    FLGVBATRESSYNC_ID  ,
/*0x97*/    STPURGEFUELLINE_ID ,
/*0x98*/    GEARSHIFT_ID       ,
/*0x99*/    RECTHRREQ_ID       ,
/*0x9A*/    RECLIMTORQUEGAS_ID ,
/*0x9B*/    VEHOPTCONFIG_ID    ,
/*0x9C*/    ROLLCAN_ID         ,
/*0x9D*/    VGEARSHIFTUP_ID    ,
/*0x9E*/    PITCHCAN_ID        ,
/*0x9F*/    VLB3RAW_ID         ,
/*0xA0*/    INJECTION_ID       , /* managed in KWP2000_ll - getRLI is not called */
/*0xA1*/    STRESS_CONTROL_ID  , /* managed in KWP2000_ll - getRLI is not called */
/*0xA2*/    ION_STATE_CYL0_ID  ,
/*0xA3*/    ION_STATE_CYL1_ID  ,
/*0xA4*/    ION_STATE_CYL2_ID  ,
/*0xA5*/    ION_STATE_CYL3_ID  ,
/*0xA6*/    INT_ION_CYL0_ID    ,
/*0xA7*/    INT_ION_CYL1_ID    ,
/*0xA8*/    INT_ION_CYL2_ID    ,
/*0xA9*/    INT_ION_CYL3_ID    ,
/*0xB3*/    CNTCRANK_ID        ,
/*0xB4*/    LAMMILCMD_ID       ,
/*0xB5*/    BUTTONMAPSIGNAL_ID ,
/*0xB6*/    USELA3SIGNAL_ID    ,
/*0xB7*/    FLG_SELF_DISABLE_ID,
/*0xB8*/    ANTITAMPEN_ID      ,
/*0xB9*/    ANTITAMPREN_ID     ,
/*0xBA*/    ANTITAMPRRES_ID    ,
/*0xBB*/    INJVINEN_ID        ,
/*0xBC*/    INJVINFAILCNT_ID   ,
/*0xBD*/    VINDASH_ID         , /* managed in KWP2000_ll - getRLI is not called */
/*0xBE*/    VINECU_ID          , /* managed in KWP2000_ll - getRLI is not called */
/*0xBF*/    MAINRELAY_ID       ,
/*0xC1*/    RPMLIMSTRESS_ID    ,
/*0xC2*/    VSFOFFSET_ID       ,
/*0xC3*/    QSHIFTEN_ID        ,
/*0xC4*/    QSHIFTTYPE_ID      ,
/*0xC5*/    TCZERO_ID          ,
/*0xC6*/    IDVERSION_ID       ,
/*0xC7*/    MISFIRE_STATE_ID   ,
/*0xC8*/    CIR_SEL_ION_ACQ_ID ,
/*0xC9*/    START_CH_ID        ,
/*0xCA*/    START_TH_ID        ,
/*0xCB*/    THPEAK_ID          ,
/*0xCD*/    IONABSTDC_ID       ,
/*0xCE*/    IUMPR_ID           , /* managed in KWP2000_ll - getRLI is not called */
/*0xCF*/    DRL_ID             ,
/*0xD0*/    CNTTDCCRK_ID       ,
/*0xDE*/    LAMPONTIMERSTCNT_ID,
/*0xE2*/    TEMPUNLOCKCNTDOWN_ID,
/*0xE3*/    TRANSPORTLOCKST_ID ,
/*0xE4*/    CRANKSTREN_ID      ,
/*0xE5*/    EELCTRIP_ID        ,
/*0xE6*/    VLAMBDA_2_ID       ,
/*0xE7*/    CAT_ID             ,
/*0xE8*/    DCLAMHEATER2_ID    ,
/*0xEF*/    FLGEOL_ID
};


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * getRLI - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void getRLI(uint8_t * data,
            uint8_t * dataLength,
            uint8_t rli)
{
    uint32_t temp_rli = 0;
    uint8_t dLength = *dataLength;
    uint32_T i ;
    uint32_T iRight;
    
    BINARYSEARCH_U8( &i, &iRight, rli, RliKey, RLI_LENGTH-1);

    if((RliKey[i] == rli)&&(rliData[i].size>0)&&(rliData[i].p_data)!=NULL)
    {
        *dataLength = dLength + rliData[i].size ;
        if(rliData[i].size == 16)
        {
            {
                uint16_t l_temp_pdata0;
                uint16_t l_temp_pdata1;
                uint16_t l_temp_pdata;
                uint8_t k;
                uint8_t l_maxLoop = rliData[i].size >> 1 ;
                           
                for(k=0; k < l_maxLoop; k++)
                {   
                    /*Ricavo il dato da elaborare*/
                    l_temp_pdata0 =  *((uint8_t *)(rliData[i].p_data + 1 + (k<<1)));
                    l_temp_pdata1 =  *((uint8_t *)(rliData[i].p_data + (k<<1)));
                    l_temp_pdata = (l_temp_pdata1<<8) | l_temp_pdata0;
                    /* Scelgo la funzione da usare */
                    temp_rli = (uint16_t) Rli_function[rliData[i].function_index](
                        (uint32_t)l_temp_pdata, (int8_t) rliData[i].type_data);
#ifdef USE_REVERSE_BYTE
                    /* Scrivo sul buffer di uscita in ordine invertito */
                    *( (uint16_t *)(data + dLength + ((l_maxLoop - (k+1))<<1)) ) = (uint16_t)(temp_rli & (0xffff));
#else
                    /* Scrivo sul buffer di uscita nello stesso ordine del dato */
                    *( (uint16_t *)(data + dLength + (k<<1)) ) = (uint16_t)(temp_rli & (0xffff));
#endif
                }
            }
        }
        else if(rliData[i].size == 8)
        {
            {
                uint8_t l_temp_pdata;
                uint8_t k;
                uint8_t l_maxLoop = rliData[i].size;
                           
                for(k=0; k < l_maxLoop; k++)
                {   
                    l_temp_pdata = (uint8_t) (*(uint8_t*)(rliData[i].p_data + k));
                    temp_rli = Rli_function[rliData[i].function_index]((uint32_t)l_temp_pdata, (int8_t) rliData[i].type_data);
#ifdef USE_REVERSE_BYTE
                    /* Scrivo sul buffer di uscita in ordine invertito */
                    *(data + dLength + (l_maxLoop - (k+1))) = ((uint8_t) temp_rli) & (0xff);
#else
                    /* Scrivo sul buffer di uscita nello stesso ordine del dato */
                    *(data + dLength + k) = ((uint8_t) temp_rli) & (0xff);
#endif
                }
            }
        }
        else if(rliData[i].size == 4)
        {
            temp_rli = Rli_function[rliData[i].function_index]((*(uint32_t *)(rliData[i].p_data)),(rliData[i].type_data));
            *(data + dLength ) = (uint8_t) ((temp_rli)>>24)&(0xff);
            *(data + dLength + 1) =(uint8_t) ((temp_rli)>>16)&(0xff);
            *(data + dLength + 2) =(uint8_t) ((temp_rli)>>8)&(0xff);
            *(data + dLength + 3) =(uint8_t) (temp_rli)&(0xff);
        }
        else if(rliData[i].size == 2)
        {
            
            temp_rli = Rli_function[rliData[i].function_index]((*(uint16_t *)(rliData[i].p_data)),(rliData[i].type_data));
            *(data + dLength  ) =(uint8_t) (((temp_rli)>>8)&(0xff));
            *(data + dLength + 1) =(uint8_t) ((temp_rli)&(0xff));
        }
        else if(rliData[i].size == 1)
        {
            temp_rli = Rli_function[rliData[i].function_index]((*(uint8_t *)(rliData[i].p_data)),(rliData[i].type_data));
            *(data + dLength ) =(uint8_t) (temp_rli)&(0xff);
        } 
        else
        {
            *dataLength = dLength;
        }    
    }
}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * rliConversion - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t rliConversion(uint32_t value, int8_t type_data)
{
    uint32_t newValue ;

    if(type_data==0)
    {
        newValue = value;
    }
    else
    {
        newValue = RESET_RLISTATUS;
        if( ( value+1 <= type_data) && (value < 8)) 
        {
            newValue = BITVAL(value);
        }    
        
        
    }  
    return newValue; 
}


/*--------------------------------------------------------------------------*
 * rliConversion - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convOnOff(uint32_t value, 
                          int8_t type_data)
{
    uint32_t newValue=0; 

    if(type_data)
        value=!value;

    if(value > 1)
    {
        if(type_data==3)
        {
            newValue= RLISTATUS_BIT1;
        }
        else
        {
            newValue = RESET_RLISTATUS;
        }
    }
    else
    {  
        if(type_data==2)
        {
            if(value)
                newValue= RLISTATUS_BIT1;
            else
                newValue= RLISTATUS_BIT0;  
            }
        else
        {
            if(value)
                newValue= RLISTATUS_BIT0;
            else
                newValue= RLISTATUS_BIT1;  
        }  
    }

    return newValue;
}

/*--------------------------------------------------------------------------*
 * convSession - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convSession(uint32_t value, 
                            int8_t type_data)
{
    uint32_t newValue;
    newValue = (uint8_t)(KWPsession>>1);

    return newValue;
}

/*--------------------------------------------------------------------------*
 * convInjection - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convInjection(uint32_t value, 
                              int8_t type_data)
{
    return (uint32_t) (value>>16);
}

/*--------------------------------------------------------------------------*
 * convGearShift - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_T convGearShift(uint32_t value, 
                              int8_t type_data)
{
    return (uint32_T) BITVAL(value+1);
}

/*--------------------------------------------------------------------------*
 * convWarningLamp - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convWarningLamp(uint32_t value, int8_t type_data)
{
    uint32_t newValue;
    switch(Get_Lamp_Status(type_data))
    {     
        case LAMP_ON:
            newValue = RLISTATUS_BIT0;
            break;
        case LAMP_OFF:
            newValue = RLISTATUS_BIT1;
            break;
        case LAMP_BLINK:
            newValue = RLISTATUS_BIT2;
            break;
        default:
            newValue = RESET_RLISTATUS;
            break; 
    }
    return newValue;
}




/*--------------------------------------------------------------------------*
 * rliConversion - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convAntiTamp(uint32_t value, int8_t type_data)
{
    uint32_t newValue=0; 

    if (ATEnable == 1)
    {
        if(type_data)
        value=!value;

        if(value > 1)
        {
            if(type_data==3)
            {
                newValue= RLISTATUS_BIT1;
            }
            else
            {
                newValue = RESET_RLISTATUS;
            }
        }
        else
        {  
            if(type_data==2)
            {
                if(value)
                    newValue= RLISTATUS_BIT1;
                else
                    newValue= RLISTATUS_BIT0;  
                }
            else
            {
                if(value)
                    newValue= RLISTATUS_BIT0;
                else
                    newValue= RLISTATUS_BIT1;  
            }  
        }
    }
    else
    {
        newValue = 0xFF;
    }

    return newValue;
}

/*--------------------------------------------------------------------------*
 * convStMisf - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_T convStMisf(uint32_T value, int8_T type_data)
{
uint32_t newValue = 0u;

    switch (value)
    {
        case NO_MISF: // 0 Actual value --> 0x01 Data value for the tool
            {
                newValue = 0x01u;
                break;
            }
        case BAD_COMB: // 1 Actual value --> 0x04 Data value for the tool
            {
                newValue = 0x04u;
                break;
            }
        case PAR_MISF: // 2 Actual value --> 0x02 Data value for the tool
            {
                newValue = 0x02u;
                break;
            }
        case MISF: // 3 Actual value --> 0x10 Data value for the tool
            {
                newValue = 0x10u;
                break;
            }
        default:
            break;
    }
    return (newValue);
}

#if 0
/*--------------------------------------------------------------------------*
 * convIMMO - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t convIMMO(uint32_t value, int8_t type_data)
{
   uint32_t newValue;
   newValue = IMMO_GetDiagState();

  return newValue;
}

/*--------------------------------------------------------------------------*
 * rliMaskConv - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t rliMaskConv(uint32_t value, int8_t type_data)
{
    uint32_t newValue ;
    /* type_data represents here the mask to be applied to value */
    newValue = (value & type_data); 
    return newValue; 
}
#endif




#else
/*stubs section*/
#include "typedefs.h"


void getRLI(uint8_t * data,uint8_t * dataLength ,uint8_t rli)
{
    *dataLength = 2;
}


#endif  //_BUILD_RLI_

