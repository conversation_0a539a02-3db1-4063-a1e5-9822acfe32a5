/*
 * File: TrqDrivMgm.c
 *
 * Code generated for Simulink model 'TrqDrivMgm'.
 *
 * Model version                  : 1.2254
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Mon May 07 08:46:29 2018
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA-C:2004 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (13), Warnings (4), Error (0)
 */

#include "TrqDrivMgm.h"
#include "TrqDrivMgm_private.h"

/* Named constants for Chart: '<S2>/FiltState' */
#define TrqDrivMgm_IN_DRIVE            ((uint8_T)1U)
#define TrqDrivMgm_IN_DRIVECHANGE      ((uint8_T)2U)
#define TrqDrivMgm_IN_DRIVEDOWN        ((uint8_T)1U)
#define TrqDrivMgm_IN_DRIVESTAB        ((uint8_T)2U)
#define TrqDrivMgm_IN_DRIVEUP          ((uint8_T)3U)
#define TrqDrivMgm_IN_EngineRunning    ((uint8_T)1U)
#define TrqDrivMgm_IN_INIT_ST          ((uint8_T)2U)
#define TrqDrivMgm_IN_NO_ACTIVE_CHILD  ((uint8_T)0U)
#define TrqDrivMgm_IN_NoPassBy         ((uint8_T)1U)
#define TrqDrivMgm_IN_PBYACTIVE        ((uint8_T)2U)

/* user code (top of source file) */
/* System '<Root>/TrqDrivMgm' */
#ifdef _BUILD_TRQDRIVMGM_

/* Block signals (auto storage) */
BlockIO_TrqDrivMgm TrqDrivMgm_B;

/* Block states (auto storage) */
D_Work_TrqDrivMgm TrqDrivMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_TrqDrivMgm TrqDrivMgm_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_TrqDrivMgm TrqDrivMgm_U;

/* Exported data definition */
int16_T CmeDriverIDiffAbs;             /* CmiDriverI absolute variation */
int16_T CmeDriverPDiff;                /* CmiDriverP variation */
int16_T CmeDriverPDiffAbs;             /* CmiDriverP absolute variation */
int16_T CmeDriverPDownDiff;            /* CmeDriverPStab - CmeDriverPTmp */
int16_T CmeDriverPStab;                /* CmiDriverP during DRIVESTAB */
int16_T CmeDriverPTmp;                 /* CME slow not filtered */
int16_T CmeDriverPUpDiff;              /* CmeDriverPTmp - CmeDriverPStab */
uint8_T FlgCmeFilt;                    /* CmiDriverP filter enable flag */
uint8_T FlgFromPby;                    /* FlgFromPby */
uint8_T StTrqDriv;                     /* Cmi variation detection state */
uint8_T TrqDrivEconMode;               /* TrqDrivEconMode */

/* Forward declaration for local functions */
static void TrqDrivMgm_ST_TRQ_DRIV(void);

/* Function for Chart: '<S2>/FiltState' */
static void TrqDrivMgm_ST_TRQ_DRIV(void)
{
  /* During 'ST_TRQ_DRIV': '<S7>:346' */
  if (TrqDrivMgm_DWork.bitsForTID0.is_ST_TRQ_DRIV == TrqDrivMgm_IN_EngineRunning)
  {
    /* During 'EngineRunning': '<S7>:358' */
    /* Transition: '<S7>:354' */
    if (!(ENTRQDRIVMGM != 0)) {
      /* Transition: '<S7>:351' */
      /* Exit Internal 'EngineRunning': '<S7>:358' */
      /* Exit Internal 'NoPassBy': '<S7>:368' */
      /* Exit Internal 'DRIVE': '<S7>:383' */
      TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_NO_ACTIVE_CHILD;
      TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_NO_ACTIVE_CHILD;
      TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning =
        TrqDrivMgm_IN_NO_ACTIVE_CHILD;
      TrqDrivMgm_DWork.bitsForTID0.is_ST_TRQ_DRIV = TrqDrivMgm_IN_INIT_ST;

      /* Entry 'INIT_ST': '<S7>:352' */
      TrqDrivMgm_B.FlgCmeFilt_h = 0U;
      TrqDrivMgm_B.StTrqDriv_l = INIT_ST;
      TrqDrivMgm_B.FlgFromPby_o = 0U;
    } else {
      /* Transition: '<S7>:355' */
      if ((CntAbsTdc == 1U) && (TrqDrivMgm_DWork.lastCntAbsTdc > CntAbsTdc)) {
        /* Transition: '<S7>:357' */
        /* Exit Internal 'EngineRunning': '<S7>:358' */
        /* Exit Internal 'NoPassBy': '<S7>:368' */
        /* Exit Internal 'DRIVE': '<S7>:383' */
        TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_NO_ACTIVE_CHILD;
        TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_NO_ACTIVE_CHILD;
        TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning =
          TrqDrivMgm_IN_NO_ACTIVE_CHILD;
        TrqDrivMgm_DWork.bitsForTID0.is_ST_TRQ_DRIV = TrqDrivMgm_IN_INIT_ST;

        /* Entry 'INIT_ST': '<S7>:352' */
        TrqDrivMgm_B.FlgCmeFilt_h = 0U;
        TrqDrivMgm_B.StTrqDriv_l = INIT_ST;
        TrqDrivMgm_B.FlgFromPby_o = 0U;
      } else if (TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning ==
                 TrqDrivMgm_IN_NoPassBy) {
        /* During 'NoPassBy': '<S7>:368' */
        if (StPassBy == WAIT_WOT_PBY) {
          /* Transition: '<S7>:364' */
          /* Exit Internal 'NoPassBy': '<S7>:368' */
          /* Exit Internal 'DRIVE': '<S7>:383' */
          TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_NO_ACTIVE_CHILD;
          TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy =
            TrqDrivMgm_IN_NO_ACTIVE_CHILD;
          TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning =
            TrqDrivMgm_IN_PBYACTIVE;

          /* Entry 'PBYACTIVE': '<S7>:367' */
          TrqDrivMgm_B.FlgCmeFilt_h = 1U;
          TrqDrivMgm_B.StTrqDriv_l = PBYACTIVE;
        } else if (TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy ==
                   TrqDrivMgm_IN_DRIVE) {
          /* During 'DRIVE': '<S7>:383' */
          if (TrqDrivMgm_B.Add != TrqDrivMgm_B.DataStoreRead2) {
            /* Transition: '<S7>:378' */
            /* Exit Internal 'DRIVE': '<S7>:383' */
            TrqDrivMgm_DWork.bitsForTID0.is_DRIVE =
              TrqDrivMgm_IN_NO_ACTIVE_CHILD;
            TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_DRIVECHANGE;

            /* Entry 'DRIVECHANGE': '<S7>:379' */
            TrqDrivMgm_B.FlgCmeFilt_h = 1U;
            TrqDrivMgm_B.StTrqDriv_l = DRIVECHANGE;
          } else {
            switch (TrqDrivMgm_DWork.bitsForTID0.is_DRIVE) {
             case TrqDrivMgm_IN_DRIVEDOWN:
              /* During 'DRIVEDOWN': '<S7>:402' */
              if (TrqDrivMgm_B.Sub > CMEDRIVPSTEPTHR) {
                /* Transition: '<S7>:404' */
                TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVEUP;

                /* Entry 'DRIVEUP': '<S7>:401' */
                TrqDrivMgm_B.FlgCmeFilt_h = 1U;
                TrqDrivMgm_B.StTrqDriv_l = DRIVEUP;
                CmeDriverPUpDiff = (int16_T)(TrqDrivMgm_B.Switch2 -
                  CmeDriverPStab);
              } else if ((TrqDrivMgm_B.Conversion_n < CMEDRIVPSTABTHR) &&
                         (TrqDrivMgm_B.Conversion < CMEDRIVPSTABTHR)) {
                /* Transition: '<S7>:407' */
                TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVESTAB;

                /* Entry 'DRIVESTAB': '<S7>:400' */
                TrqDrivMgm_B.FlgCmeFilt_h = 0U;
                TrqDrivMgm_B.StTrqDriv_l = DRIVESTAB;
                CmeDriverPDownDiff = 0;
                CmeDriverPUpDiff = 0;
                CmeDriverPStab = CmeDriverP;
              } else {
                CmeDriverPDownDiff = (int16_T)(CmeDriverPStab -
                  TrqDrivMgm_B.Switch2);
              }
              break;

             case TrqDrivMgm_IN_DRIVESTAB:
              /* During 'DRIVESTAB': '<S7>:400' */
              if (TrqDrivMgm_B.Sub > CMEDRIVPSTEPTHR) {
                /* Transition: '<S7>:403' */
                TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVEUP;

                /* Entry 'DRIVEUP': '<S7>:401' */
                TrqDrivMgm_B.FlgCmeFilt_h = 1U;
                TrqDrivMgm_B.StTrqDriv_l = DRIVEUP;
                CmeDriverPUpDiff = (int16_T)(TrqDrivMgm_B.Switch2 -
                  CmeDriverPStab);
              } else if (TrqDrivMgm_B.Sub < (-CMEDRIVPSTEPTHR)) {
                /* Transition: '<S7>:406' */
                TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVEDOWN;

                /* Entry 'DRIVEDOWN': '<S7>:402' */
                TrqDrivMgm_B.FlgCmeFilt_h = 1U;
                TrqDrivMgm_B.StTrqDriv_l = DRIVEDOWN;
                CmeDriverPDownDiff = (int16_T)(CmeDriverPStab -
                  TrqDrivMgm_B.Switch2);
              } else {
                CmeDriverPStab = CmeDriverP;
              }
              break;

             default:
              /* During 'DRIVEUP': '<S7>:401' */
              if ((TrqDrivMgm_B.Sub < (-CMEDRIVPSTEPTHR)) &&
                  ((TrqDrivMgm_B.Switch2 - CmeDriverI) < CMEDRIVPDRIVITHR)) {
                /* Transition: '<S7>:405' */
                TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVEDOWN;

                /* Entry 'DRIVEDOWN': '<S7>:402' */
                TrqDrivMgm_B.FlgCmeFilt_h = 1U;
                TrqDrivMgm_B.StTrqDriv_l = DRIVEDOWN;
                CmeDriverPDownDiff = (int16_T)(CmeDriverPStab -
                  TrqDrivMgm_B.Switch2);
              } else if ((TrqDrivMgm_B.Conversion_n < CMEDRIVPSTABTHR) &&
                         ((TrqDrivMgm_B.Switch2 - CmeDriverI) < CMEDRIVPDRIVITHR))
              {
                /* Transition: '<S7>:408' */
                TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVESTAB;

                /* Entry 'DRIVESTAB': '<S7>:400' */
                TrqDrivMgm_B.FlgCmeFilt_h = 0U;
                TrqDrivMgm_B.StTrqDriv_l = DRIVESTAB;
                CmeDriverPDownDiff = 0;
                CmeDriverPUpDiff = 0;
                CmeDriverPStab = CmeDriverP;
              } else {
                CmeDriverPUpDiff = (int16_T)(TrqDrivMgm_B.Switch2 -
                  CmeDriverPStab);
              }
              break;
            }
          }
        } else {
          /* During 'DRIVECHANGE': '<S7>:379' */
          /* Transition: '<S7>:381' */
          if ((TrqDrivMgm_B.Conversion_n < CMEDRIVPSTABTHR) && (TrqDrivMgm_B.Add
               == TrqDrivMgm_B.DataStoreRead2)) {
            /* Transition: '<S7>:374' */
            TrqDrivMgm_B.FlgFromPby_o = 0U;

            /* Transition: '<S7>:375' */
            TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_DRIVE;

            /* Entry Internal 'DRIVE': '<S7>:383' */
            /* Transition: '<S7>:388' */
            TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVESTAB;

            /* Entry 'DRIVESTAB': '<S7>:400' */
            TrqDrivMgm_B.FlgCmeFilt_h = 0U;
            TrqDrivMgm_B.StTrqDriv_l = DRIVESTAB;
            CmeDriverPDownDiff = 0;
            CmeDriverPUpDiff = 0;
            CmeDriverPStab = CmeDriverP;
          } else {
            /* Transition: '<S7>:382' */
          }
        }
      } else {
        /* During 'PBYACTIVE': '<S7>:367' */
        if ((StPassBy != WAIT_WOT_PBY) && (StPassBy != ACT_PBY)) {
          /* Transition: '<S7>:366' */
          TrqDrivMgm_B.FlgFromPby_o = 1U;
          TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning = TrqDrivMgm_IN_NoPassBy;
          TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_DRIVECHANGE;

          /* Entry 'DRIVECHANGE': '<S7>:379' */
          TrqDrivMgm_B.FlgCmeFilt_h = 1U;
          TrqDrivMgm_B.StTrqDriv_l = DRIVECHANGE;
        }
      }
    }
  } else {
    /* During 'INIT_ST': '<S7>:352' */
    if (CntAbsTdc > TrqDrivMgm_DWork.lastCntAbsTdc) {
      /* Transition: '<S7>:347' */
      if (ENTRQDRIVMGM != 0) {
        /* Transition: '<S7>:348' */
        TrqDrivMgm_DWork.bitsForTID0.is_ST_TRQ_DRIV =
          TrqDrivMgm_IN_EngineRunning;

        /* Entry Internal 'EngineRunning': '<S7>:358' */
        /* Transition: '<S7>:360' */
        if (StPassBy == WAIT_WOT_PBY) {
          /* Transition: '<S7>:361' */
          TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning =
            TrqDrivMgm_IN_PBYACTIVE;

          /* Entry 'PBYACTIVE': '<S7>:367' */
          TrqDrivMgm_B.FlgCmeFilt_h = 1U;
          TrqDrivMgm_B.StTrqDriv_l = PBYACTIVE;
        } else {
          /* Transition: '<S7>:363' */
          if ((TrqDrivMgm_B.Conversion_n < CMEDRIVPSTABTHR) || (TrqDrivMgm_B.Add
               == TrqDrivMgm_B.DataStoreRead2)) {
            /* Transition: '<S7>:372' */
            /* Transition: '<S7>:375' */
            TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning =
              TrqDrivMgm_IN_NoPassBy;
            TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_DRIVE;

            /* Entry Internal 'DRIVE': '<S7>:383' */
            /* Transition: '<S7>:388' */
            TrqDrivMgm_DWork.bitsForTID0.is_DRIVE = TrqDrivMgm_IN_DRIVESTAB;

            /* Entry 'DRIVESTAB': '<S7>:400' */
            TrqDrivMgm_B.FlgCmeFilt_h = 0U;
            TrqDrivMgm_B.StTrqDriv_l = DRIVESTAB;
            CmeDriverPDownDiff = 0;
            CmeDriverPUpDiff = 0;
            CmeDriverPStab = CmeDriverP;
          } else {
            /* Transition: '<S7>:373' */
            TrqDrivMgm_DWork.bitsForTID0.is_EngineRunning =
              TrqDrivMgm_IN_NoPassBy;
            TrqDrivMgm_DWork.bitsForTID0.is_NoPassBy = TrqDrivMgm_IN_DRIVECHANGE;

            /* Entry 'DRIVECHANGE': '<S7>:379' */
            TrqDrivMgm_B.FlgCmeFilt_h = 1U;
            TrqDrivMgm_B.StTrqDriv_l = DRIVECHANGE;
          }
        }
      } else {
        /* Transition: '<S7>:350' */
        TrqDrivMgm_DWork.bitsForTID0.is_ST_TRQ_DRIV = TrqDrivMgm_IN_INIT_ST;

        /* Entry 'INIT_ST': '<S7>:352' */
        TrqDrivMgm_B.FlgCmeFilt_h = 0U;
        TrqDrivMgm_B.StTrqDriv_l = INIT_ST;
        TrqDrivMgm_B.FlgFromPby_o = 0U;
      }
    }
  }
}

/* Output and update for function-call system: '<S1>/Calc' */
void TrqDrivMgm_Calc(void)
{
  /* local block i/o variables */
  int32_T rtb_GenAbs;
  boolean_T rtb_RelationalOperator2;
  int32_T tmp;

  /* Outputs for Atomic SubSystem: '<S2>/localMode_Calc' */
  /* DataStoreRead: '<S8>/Data Store Read2' */
  TrqDrivMgm_B.DataStoreRead2 = TrqDrivMgm_DWork.localMode;

  /* DataStoreWrite: '<S8>/Data Store Write1' */
  TrqDrivMgm_DWork.lastlocalMode = TrqDrivMgm_B.DataStoreRead2;

  /* RelationalOperator: '<S8>/Relational Operator2' incorporates:
   *  Constant: '<S8>/ONE1'
   *  Inport: '<Root>/EngResp'
   */
  rtb_RelationalOperator2 = (EngResp == 1);

  /* Sum: '<S8>/Add' incorporates:
   *  Inport: '<Root>/AccSens'
   *  Inport: '<Root>/MaxTrq'
   *  RelationalOperator: '<S8>/Relational Operator3'
   */
  TrqDrivMgm_B.Add = (uint8_T)((((uint32_T)rtb_RelationalOperator2) + ((uint32_T)
    AccSens)) + ((uint32_T)MaxTrq));

  /* End of Outputs for SubSystem: '<S2>/localMode_Calc' */

  /* DataStoreWrite: '<S2>/Data Store Write1' */
  TrqDrivMgm_DWork.localMode = TrqDrivMgm_B.Add;

  /* Outputs for Atomic SubSystem: '<S2>/CmeDriver_Split' */
  /* Switch: '<S6>/Switch' incorporates:
   *  Constant: '<S6>/ACT_PBY'
   *  Constant: '<S6>/CME_MAX'
   *  Inport: '<Root>/CmeMaxPby'
   *  Inport: '<Root>/StPassBy'
   *  RelationalOperator: '<S6>/Relational Operator'
   */
  if (StPassBy == ACT_PBY) {
    TrqDrivMgm_B.Switch2 = CmeMaxPby;
  } else {
    TrqDrivMgm_B.Switch2 = CME_MAX;
  }

  /* End of Switch: '<S6>/Switch' */

  /* Switch: '<S11>/Switch2' incorporates:
   *  Inport: '<Root>/CmeDriver'
   *  RelationalOperator: '<S11>/LowerRelop1'
   */
  if (!(CmeDriver > TrqDrivMgm_B.Switch2)) {
    /* Switch: '<S11>/Switch' incorporates:
     *  Constant: '<S6>/CME_MIN'
     *  RelationalOperator: '<S11>/UpperRelop'
     */
    if (CmeDriver < CME_MIN) {
      TrqDrivMgm_B.Switch2 = CME_MIN;
    } else {
      TrqDrivMgm_B.Switch2 = CmeDriver;
    }

    /* End of Switch: '<S11>/Switch' */
  }

  /* End of Switch: '<S11>/Switch2' */

  /* Sum: '<S6>/Sub1' incorporates:
   *  Inport: '<Root>/CmeDriverI'
   */
  rtb_GenAbs = TrqDrivMgm_B.Switch2 - CmeDriverI;
  if (rtb_GenAbs > 32767) {
    rtb_GenAbs = 32767;
  } else {
    if (rtb_GenAbs < -32768) {
      rtb_GenAbs = -32768;
    }
  }

  /* S-Function (GenAbs): '<S10>/GenAbs' incorporates:
   *  Constant: '<S6>/INT16_TYPE'
   */
  rtb_GenAbs = GenAbs( (int32_T)rtb_GenAbs, (uint8_T)INT16_TYPE);

  /* DataTypeConversion: '<S13>/Conversion' */
  TrqDrivMgm_B.Conversion = (int16_T)rtb_GenAbs;

  /* DataStoreWrite: '<S6>/Data Store Write1' */
  CmeDriverIDiffAbs = TrqDrivMgm_B.Conversion;

  /* DataStoreWrite: '<S6>/Data Store Write3' */
  CmeDriverPTmp = TrqDrivMgm_B.Switch2;

  /* Sum: '<S6>/Sub' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  tmp = TrqDrivMgm_B.Switch2 - CmeDriverP;
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  TrqDrivMgm_B.Sub = (int16_T)tmp;

  /* End of Sum: '<S6>/Sub' */

  /* Sum: '<S6>/Sub1' incorporates:
   *  DataTypeConversion: '<S9>/Data Type Conversion4'
   */
  rtb_GenAbs = TrqDrivMgm_B.Sub;

  /* S-Function (GenAbs): '<S9>/GenAbs' incorporates:
   *  Constant: '<S6>/INT16_TYPE'
   */
  rtb_GenAbs = GenAbs( (int32_T)rtb_GenAbs, (uint8_T)INT16_TYPE);

  /* DataTypeConversion: '<S12>/Conversion' */
  TrqDrivMgm_B.Conversion_n = (int16_T)rtb_GenAbs;

  /* DataStoreWrite: '<S6>/Data Store Write4' */
  CmeDriverPDiffAbs = TrqDrivMgm_B.Conversion_n;

  /* DataStoreWrite: '<S6>/Data Store Write5' */
  CmeDriverPDiff = TrqDrivMgm_B.Sub;

  /* End of Outputs for SubSystem: '<S2>/CmeDriver_Split' */

  /* Chart: '<S2>/FiltState' incorporates:
   *  Inport: '<Root>/CntAbsTdc'
   */
  /* Gateway: TrqDrivMgm/Calc/FiltState */
  /* During: TrqDrivMgm/Calc/FiltState */
  if (TrqDrivMgm_DWork.bitsForTID0.is_active_c2_TrqDrivMgm == 0U) {
    /* Entry: TrqDrivMgm/Calc/FiltState */
    TrqDrivMgm_DWork.bitsForTID0.is_active_c2_TrqDrivMgm = 1U;

    /* Entry Internal: TrqDrivMgm/Calc/FiltState */
    TrqDrivMgm_DWork.bitsForTID0.is_active_ST_TRQ_DRIV = 1U;

    /* Entry Internal 'ST_TRQ_DRIV': '<S7>:346' */
    /* Transition: '<S7>:396' */
    TrqDrivMgm_DWork.bitsForTID0.is_ST_TRQ_DRIV = TrqDrivMgm_IN_INIT_ST;

    /* Entry 'INIT_ST': '<S7>:352' */
    TrqDrivMgm_B.FlgCmeFilt_h = 0U;
    TrqDrivMgm_B.StTrqDriv_l = INIT_ST;
    TrqDrivMgm_B.FlgFromPby_o = 0U;
    TrqDrivMgm_DWork.bitsForTID0.is_active_UPDATE_OLD_VAR = 1U;
  } else {
    TrqDrivMgm_ST_TRQ_DRIV();

    /* During 'UPDATE_OLD_VAR': '<S7>:26' */
    /* Transition: '<S7>:60' */
    TrqDrivMgm_DWork.lastCntAbsTdc = CntAbsTdc;
  }

  /* End of Chart: '<S2>/FiltState' */

  /* DataStoreWrite: '<S2>/Data Store Write6' */
  StTrqDriv = TrqDrivMgm_B.StTrqDriv_l;

  /* Outputs for Atomic SubSystem: '<S2>/localMode_Calc' */
  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  DataTypeConversion: '<S8>/Data Type Conversion'
   */
  TrqDrivEconMode = (uint8_T)rtb_RelationalOperator2;

  /* End of Outputs for SubSystem: '<S2>/localMode_Calc' */

  /* DataStoreWrite: '<S2>/Data Store Write8' */
  FlgCmeFilt = TrqDrivMgm_B.FlgCmeFilt_h;

  /* DataStoreWrite: '<S2>/Data Store Write9' */
  FlgFromPby = TrqDrivMgm_B.FlgFromPby_o;
}

/* Output and update for function-call system: '<S1>/Init' */
void TrqDrivMgm_Init(void)
{
  /* DataStoreWrite: '<S3>/Data Store Write1' incorporates:
   *  Constant: '<S3>/ZERO4'
   */
  FlgFromPby = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write10' incorporates:
   *  Constant: '<S3>/ZERO1'
   */
  CmeDriverPDiffAbs = 0;

  /* DataStoreWrite: '<S3>/Data Store Write5' incorporates:
   *  Constant: '<S3>/ZERO1'
   */
  CmeDriverPStab = 0;

  /* DataStoreWrite: '<S3>/Data Store Write6' incorporates:
   *  Constant: '<S3>/ZERO1'
   */
  CmeDriverPDownDiff = 0;

  /* DataStoreWrite: '<S3>/Data Store Write7' incorporates:
   *  Constant: '<S3>/ZERO1'
   */
  CmeDriverPDiff = 0;

  /* DataStoreWrite: '<S3>/Data Store Write8' incorporates:
   *  Constant: '<S3>/ZERO1'
   */
  CmeDriverPUpDiff = 0;

  /* DataStoreWrite: '<S3>/Data Store Write9' incorporates:
   *  Constant: '<S3>/ZERO1'
   */
  CmeDriverPTmp = 0;

  /* DataStoreWrite: '<S3>/Data Store Write2' incorporates:
   *  Constant: '<S3>/INIT_ST'
   */
  StTrqDriv = INIT_ST;

  /* DataStoreWrite: '<S3>/Data Store Write3' incorporates:
   *  Constant: '<S3>/ZERO'
   */
  FlgCmeFilt = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write4' incorporates:
   *  Constant: '<S3>/ZERO5'
   */
  TrqDrivEconMode = 0U;
}

/* Model step function */
void TrqDrivMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/TrqDrivMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_TrqDrivMgm_Init' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' incorporates:
   *  Inport: '<Root>/ev_NoSync'
   */
  if (((TrqDrivMgm_U.ev_PowerOn > 0) &&
       (TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Init_Trig_ZCE[0] != POS_ZCSIG)) ||
      ((TrqDrivMgm_U.ev_NoSync > 0) &&
       (TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Init_Trig_ZCE[1] != POS_ZCSIG)))
  {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    TrqDrivMgm_Init();
  }

  TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Init_Trig_ZCE[0] = (uint8_T)
    ((TrqDrivMgm_U.ev_PowerOn > 0) ? ((int32_T)POS_ZCSIG) : ((int32_T)ZERO_ZCSIG));

  /* End of Inport: '<Root>/ev_PowerOn' */

  /* Inport: '<Root>/ev_NoSync' */
  TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Init_Trig_ZCE[1] = (uint8_T)
    ((TrqDrivMgm_U.ev_NoSync > 0) ? ((int32_T)POS_ZCSIG) : ((int32_T)ZERO_ZCSIG));

  /* End of Outputs for SubSystem: '<S1>/fc_TrqDrivMgm_Init' */

  /* Outputs for Triggered SubSystem: '<S1>/fc_TrqDrivMgm_Calc' incorporates:
   *  TriggerPort: '<S4>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((TrqDrivMgm_U.ev_T10ms > 0) &&
      (TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Calc_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S4>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Calc'
     */
    TrqDrivMgm_Calc();
  }

  TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Calc_Trig_ZCE = (uint8_T)
    ((TrqDrivMgm_U.ev_T10ms > 0) ? ((int32_T)POS_ZCSIG) : ((int32_T)ZERO_ZCSIG));

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/fc_TrqDrivMgm_Calc' */

  /* End of Outputs for SubSystem: '<Root>/TrqDrivMgm' */
}

/* Model initialize function */
void TrqDrivMgm_initialize(void)
{
  TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Init_Trig_ZCE[0] = POS_ZCSIG;
  TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Init_Trig_ZCE[1] = POS_ZCSIG;
  TrqDrivMgm_PrevZCSigState.fc_TrqDrivMgm_Calc_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/TrqDrivMgm' */
void TrqDrivMgm_10ms(void)
{
  TrqDrivMgm_Calc();
}

void TrqDrivMgm_NoSync(void)
{
  TrqDrivMgm_Init();
}

#endif                                 // _BUILD_TRQDRIVMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
