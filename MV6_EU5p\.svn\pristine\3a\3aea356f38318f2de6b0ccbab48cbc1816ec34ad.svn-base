#ifndef  _HBRIDGE_H_
#define _HBRIDGE_H_

/* Definizione tipi per BRIDGE_MODE */
#define SLOW_DECAY        0x00
#define FAST_DECAY        0x01
#define PWM_DIR_SLOW      0x02  // PWM/DIR  per SLOW_DECAY
#define PWM_DIR_FAST      0x03  // PWM/DIR  per FAST_DECAY

// BRIDGE TYPE
#define HB_UNKNOWN        0
#define HB_MC33887        1
#define HB_TLE7209        2
#define HB_TLE6209        3
#define HB_MC33926        4
#define HB_STL6205        5
#define HB_STL9958        6

#ifdef  MATLAB_MEX_FILE
#include "hbridge.h"
#define MAX_DUTY 25600
#define MIN_DUTY 0
#undef  USE_DBW_BRIDGE_A
#undef  USE_DBW_BRIDGE_B
#elif (BOARD_TYPE == BOARD_1)
#define USE_DBW_BRIDGE_A
#undef  USE_DBW_BRIDGE_B
#elif (BOARD_TYPE == BOARD_2) || (BOARD_TYPE == BOARD_S4)   
#define USE_DBW_BRIDGE_A
#undef  USE_DBW_BRIDGE_B
#elif (BOARD_TYPE == BOARD_5)
#define USE_DBW_BRIDGE_A
#undef  USE_DBW_BRIDGE_B
#elif (BOARD_TYPE == BOARD_M1) || (BOARD_TYPE == BOARD_M2) || (BOARD_TYPE == BOARD_M3)
#define USE_DBW_BRIDGE_A
//#define USE_DBW_BRIDGE_B
//#define USE_EXHVAL_BRIDGE_A
#define USE_EXHVAL_BRIDGE_B
#else
#error Wrong Configuration!
#endif

#if (BOARD_TYPE == BOARD_M1) || (BOARD_TYPE == BOARD_M2) || (BOARD_TYPE == BOARD_M3)
// HBRIDGE n.1 == STL9958
#define Bridge_A            1
#define Bridge_A_IN1        OTE_Bridge_A1 /* DIR */
#define Bridge_A_IN2        OTE_Bridge_B1 /* PWM */
#define Bridge_A_IN1_PWM    EMIOS_UC2        
#define Bridge_A_IN2_PWM    EMIOS_UC12       
#define ODE_DBW1_ENABLE     ODE_EN_L9958_1  
#define Bridge_A_TYPE       HB_STL9958
#define BRIDGE_A_MODE       PWM_DIR_SLOW // PWM/DIR in SLOW DECAY mode

// HBRIDGE n.2 == STL9958
#define Bridge_B            2
#define Bridge_B_IN1        OTE_Bridge_A2 /* DIR */
#define Bridge_B_IN2        OTE_Bridge_B2 /* PWM */
#define Bridge_B_IN1_PWM    EMIOS_UC4    
#define Bridge_B_IN2_PWM    EMIOS_UC14    
#define ODE_DBW2_ENABLE     ODE_EN_L9958_2
#define Bridge_B_TYPE       HB_STL9958
#define BRIDGE_B_MODE       PWM_DIR_SLOW // PWM/DIR in SLOW DECAY mode

#define HB_DISABLE          1
#define HB_ENABLE           0

#else
#error Board not supported!!
#endif

#define NO_HB_ERR 0      // no error detected 
#define SHCIR_VBAT 1  // short circuit to battery
#define SHCIR_GND 2   // short circuit to ground
#define SHCIR_OVL 3   // short circuit overload 
#define OPENLOAD 4    // open load
#define UV_VS 5       // under voltage on pin Vs
#define NO_DIAG 6     // no diagnosis made

#define HB_IN             2
#define HB_OUT            4

//#define HB_NOT_INIT       0
#define HB_DIGIO_MODE     1      // IN1 and IN2 are in  DGIO mode
#define HB_DIGIO_PWM_MODE   2        // IN1 is in DGIO mode and IN2 is in PWM mode  
#define HB_PWM_DIGIO_MODE   3        // IN1 is in PWM mode and IN2 is in DGIO mode
#define HB_PWM_MODE             4        // IN1 and IN2 are in PWM mode

/* ERROR CODES: */
#define HB_ERROR          -5
#define HB_STATUS_ERROR   -6
#define HB_MODE_ERROR     -7
#define HB_MODE_NOT_SUPPORTED -8

int16_T DbwHB_Config(uint8_T hbridge_id, int16_T vout, uint8_T mode, uint8_T flgHBena, uint16_T duty1, uint16_T duty2, uint16_T period);

int16_T    DbwCalcDuty(uint16_T *pDuty1, uint16_T *pDuty2, int16_T vout, uint8_T mode);

void    DbwActDuty(uint8_T hbridge_id, uint16_T duty1, uint16_T duty2, uint8_T flgHBEnable);

int16_t Hbridge_SetState (uint8_t hbridge_Id, uint8_t state);

int16_t Hbridge_SetDutyCicle(uint16_t hbridge_Input, vuint16_t duty);

int16_t Hbridge_SetPeriod(uint16_t hbridge_Input, vuint32_t period);

int16_t Hbridge_PwmDisable(uint16_t hbridge_Input);

int16_t Hbridge_OutCmdSet (uint8_t pin, uint8_t lvl);

int16_t Hbridge_Init (uint8_t hbridge_Id,uint8_t hbridge_Mode, uint16_T duty1, uint16_T duty2, uint16_T period) ;

int16_t Hbridge_SetOut (uint8_t hbridge_Id, uint8_t  hbridgeOut);

int16_t Hbridge_GetOut(uint8_t hbridge_Id, uint8_t *hbridgeOut); 

int16_t Hbridge_GetOutThr(uint8_t hbridge_Id, uint8_t *hbridgeOut, int16_t hbThrVout);

int16_t Hbridge_SetVOut(uint8_t hbridge_Id, int16_t  hbridgeVOut); 

int16_t Hbridge_FSGetStatus (uint8_t hbridge_Id, uint8_t * hbridgeFSStatus);

void    Hbridge_T5ms(void);

void    Hbridge_A_SPI_ExTxDone(void) ;

int16_T Hbridge_LogicEnable(uint8_t ena);

int16_T Hbridge_GetLogicEnable(uint8_t *pEnaState);

extern const uint8_t HBRIDGE_STATE_TABLE[HB_OUT][HB_IN];
extern uint16_T     DbwOutDuty1;
extern uint16_T     DbwOutDuty2;
extern uint8_t      Hbridge_status;
extern uint8_T      FlgEnHBridge;
extern uint16_T     HB_B_Duty1;
extern uint16_T     HB_B_Duty2;

/* Var Esterne. */
extern uint8_T SpiSTL9958OFFH1;
extern uint8_T SpiSTL9958OFFH2;

/* Interfaccie esterne. */
void SPIST_L9958Cmd_Init (void);
void Hbridge_Diagnosis(uint8_T bridge);
void Hbridge_Config(void);

#endif
