/*
 * File: Ion<PERSON><PERSON>bda_types.h
 *
 * Real-Time Workshop code generated for Simulink model IonLambda.
 *
 * Model version                        : 1.657
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed May 23 15:23:33 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed May 23 15:23:38 2007
 */

#ifndef _RTW_HEADER_IonLambda_types_h_
#define _RTW_HEADER_IonLambda_types_h_

#ifndef _CSC3_IONLAMBDA_CHARTSTRUCT_
#define _CSC3_IONLAMBDA_CHARTSTRUCT_

typedef struct {
  uint16_T Buffer[8][7];
  uint16_T VettSort[7];
  uint8_T Index[8];
  uint_T is_c3_IonLambda : 2;
  uint_T is_active_c3_IonLambda : 1;
} CSc3_IonLambda_ChartStruct;

#endif                                  /* _CSC3_IONLAMBDA_CHARTSTRUCT_ */
#ifndef _CSC2_IONLAMBDA_CHARTSTRUCT_
#define _CSC2_IONLAMBDA_CHARTSTRUCT_

typedef struct {
  uint16_T Buffer[8][7];
  uint16_T VettSort[7];
  uint8_T Index[8];
  uint_T is_c2_IonLambda : 2;
  uint_T is_active_c2_IonLambda : 1;
} CSc2_IonLambda_ChartStruct;

#endif                                  /* _CSC2_IONLAMBDA_CHARTSTRUCT_ */
#ifndef _CSC9_IONLAMBDA_CHARTSTRUCT_
#define _CSC9_IONLAMBDA_CHARTSTRUCT_

typedef struct {
  uint16_T rtc2_b;
} CSc9_IonLambda_ChartStruct;

#endif                                  /* _CSC9_IONLAMBDA_CHARTSTRUCT_ */
#ifndef _CSC6_IONLAMBDA_CHARTSTRUCT_
#define _CSC6_IONLAMBDA_CHARTSTRUCT_

typedef struct {
  uint16_T cntTask;
  uint16_T cnttaskthr;
  uint_T is_c6_IonLambda : 3;
  uint_T is_active_c6_IonLambda : 1;
} CSc6_IonLambda_ChartStruct;

#endif                                  /* _CSC6_IONLAMBDA_CHARTSTRUCT_ */

/* Forward declaration for rtModel */
typedef struct RT_MODEL_IonLambda RT_MODEL_IonLambda;

#endif                                  /* _RTW_HEADER_IonLambda_types_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
