/*
 * File: WatPumpCtrl.c
 *
 * Real-Time Workshop code generated for Simulink model WatPumpCtrl.
 *
 * Model version                        : 1.701
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed Dec 19 09:43:59 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed Dec 19 09:43:59 2007
 */

#include "..\include\WatPumpCtrl.h"
#include "..\include\WatPumpCtrl_private.h"

/* Named constants for block: '<S1>/WatPumpCtrl_Sched' */
#define WatPumpCtrl_event_ev_PowerOn    (0U)
#define WatPumpCtrl_event_ev_100ms      (3U)
#define WatPumpCtrl_b                   (3)
#define WatPumpCtrl_c                   (0)

/* user code (top of source file) */
/* System: <Root>/WatPumpCtrl */
#ifdef _BUILD_WATPUMPCTRL_

uint8_T _sfEvent_WatPumpCtrl_;

/* Exported block signals */
uint16_T WatPumpDutyMax;                /* '<S11>/Conversion' */
uint16_T WatPumpDutyMin;                /* '<S12>/Conversion' */
int16_T TWaterErr;                      /* '<S2>/Sum' */

/* Exported block states */
int32_T WatPumpPWMDutyHiR;              /* '<Root>/_DataStoreBlk_3' */
uint16_T WatPumpPWMDuty;                /* '<Root>/_DataStoreBlk_1' */
uint8_T FlgWatPumpCtrlOn;               /* '<Root>/_DataStoreBlk_2' */

/* Block signals (auto storage) */
BlockIO_WatPumpCtrl WatPumpCtrl_B;

/* Block states (auto storage) */
D_Work_WatPumpCtrl WatPumpCtrl_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_WatPumpCtrl WatPumpCtrl_PrevZC;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_WatPumpCtrl WatPumpCtrl_U;

int32_T mul_s32_s32_s32_sr31(int32_T a, int32_T b)
{
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_s32(a, b, &u32_chi, &u32_clo);
  u32_clo = u32_chi << 1 | u32_clo >> 31;
  return (int32_T)u32_clo;
}

/* Output and update for function-call system: '<S1>/WatPumpCtrl_Reset' */
void WatPumpCtrl_WatPumpCtrl_R(void)
{

  /* DataStoreWrite: '<S3>/Data Store Write' */
  FlgWatPumpCtrlOn = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write1' */
  WatPumpPWMDuty = 0U;

  /* DataStoreWrite: '<S3>/Data Store Write2' */
  WatPumpPWMDutyHiR = 0;
}

/* Output and update for function-call system: '<S1>/WatPumpCtrl_DutyCalc' */
void WatPumpCtrl_WatPumpCtrl_D(uint16_T rtu_0, int16_T rtu_1, int16_T
 rtu_TWaterErr_old)
{
  /* local block i/o variables*/
  int32_T rtb_DataTypeConversion1_g;
  int32_T rtb_MinMax1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_u16_temp16;

  /* DataStoreWrite: '<S2>/Data Store Write' */
  FlgWatPumpCtrlOn = 1U;

  /* DataTypeConversion: '<S10>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_u16_temp16 = rtu_0;

  /* S-Function (PreLookUpIdSearch_U16): '<S10>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S2>/BKRPMWPUMP'
   *  Constant: '<S2>/BKRPMWPUMP_dim'
   */

  PreLookUpIdSearch_U16( &rtb_u16_temp16, &rtb_PreLookUpIdSearch_U16_o2,
   rtb_u16_temp16, &(BKRPMWPUMP[0]), BKRPMWPUMP_dim);

  /* S-Function (LookUp_IR_U16): '<S7>/LookUp_IR_U16' incorporates:
   *  Constant: '<S2>/VTWPUMPDUTYMAX'
   *  Constant: '<S2>/BKRPMWPUMP_dim'
   */

  LookUp_IR_U16( &rtb_LookUp_IR_U16, &(VTWPUMPDUTYMAX[0]), rtb_u16_temp16,
   rtb_PreLookUpIdSearch_U16_o2, BKRPMWPUMP_dim);

  /* DataTypeConversion: '<S11>/Conversion' */
  WatPumpDutyMax = rtb_LookUp_IR_U16;

  /* S-Function (LookUp_IR_U16): '<S8>/LookUp_IR_U16' incorporates:
   *  Constant: '<S2>/VTWPUMPDUTYMIN'
   *  Constant: '<S2>/BKRPMWPUMP_dim'
   */

  LookUp_IR_U16( &rtb_u16_temp16, &(VTWPUMPDUTYMIN[0]), rtb_u16_temp16,
   rtb_PreLookUpIdSearch_U16_o2, BKRPMWPUMP_dim);

  {
    uint16_T rtb_u16_rtmin;
    int32_T rtb_s32_rtmax;

    /* DataTypeConversion: '<S12>/Conversion' */
    WatPumpDutyMin = rtb_u16_temp16;

    /* Sum: '<S2>/Sum' incorporates:
     *  Constant: '<S2>/TWATERTARGET'
     *  Inport: '<Root>/TWater'
     */
    TWaterErr = (int16_T)(TWATERTARGET - rtu_1);

    /* If: '<S2>/If' incorporates:
     *  RelationalOperator: '<S2>/Relational Operator'
     *  Gain: '<S2>/Gain'
     *  RelationalOperator: '<S2>/Relational Operator1'
     *  Constant: '<S6>/WPUMPDUTYDOWN'
     *  Constant: '<S5>/WPUMPDUTYUP'
     *  Constant: '<S9>/WPUMPCTRLKI'
     *  Constant: '<S9>/WPUMPCTRLKP'
     *  Constant: '<S2>/TWATERERRDBDOWN'
     *  Constant: '<S2>/TWATERERRDBUP'
     */
    if(TWaterErr >= TWATERERRDBDOWN != 0U) {

      /* MinMax: '<S6>/MinMax1' incorporates:
       *  MinMax: '<S6>/MinMax4'
       */
      rtb_u16_rtmin = rt_MIN(WPUMPDUTYDOWN, WatPumpDutyMax);
      rtb_u16_rtmin = rt_MAX(rtb_u16_rtmin, WatPumpDutyMin);

      /* DataStoreWrite: '<S6>/Data Store Write' */
      WatPumpPWMDuty = rtb_u16_rtmin;

      /* DataStoreWrite: '<S6>/Data Store Write1' incorporates:
       *  DataTypeConversion: '<S6>/Data Type Conversion1'
       */
      WatPumpPWMDutyHiR = rtb_u16_rtmin << 5U;
    } else if((TWaterErr << 15U) <= TWATERERRDBUP * (rtcP_Gain_Gain_h) != 0U) {

      /* MinMax: '<S5>/MinMax1' incorporates:
       *  MinMax: '<S5>/MinMax4'
       */
      rtb_u16_rtmin = rt_MIN(WPUMPDUTYUP, WatPumpDutyMax);
      rtb_u16_rtmin = rt_MAX(rtb_u16_rtmin, WatPumpDutyMin);

      /* DataStoreWrite: '<S5>/Data Store Write' */
      WatPumpPWMDuty = rtb_u16_rtmin;

      /* DataStoreWrite: '<S5>/Data Store Write1' incorporates:
       *  DataTypeConversion: '<S5>/Data Type Conversion1'
       */
      WatPumpPWMDutyHiR = rtb_u16_rtmin << 5U;
    } else {

      /* DataTypeConversion: '<S9>/Data Type Conversion' */
      rtb_MinMax1 = WatPumpDutyMax << 5U;

      /* MinMax: '<S9>/MinMax4' */
      rtb_s32_rtmax = mul_s32_s32_s32_sr31(((TWaterErr - rtu_TWaterErr_old) *
        WPUMPCTRLKP >> 1) + (WPUMPCTRLKI * TWaterErr >> 1), (rtcP_Gain_Gain)) +
      WatPumpPWMDutyHiR;

      /* DataTypeConversion: '<S9>/Data Type Conversion1' */
      rtb_DataTypeConversion1_g = WatPumpDutyMin << 5U;

      /* MinMax: '<S9>/MinMax1' incorporates:
       *  Product: '<S9>/Product'
       *  Sum: '<S9>/Sum1'
       *  Product: '<S9>/Product1'
       *  Sum: '<S9>/Sum2'
       *  Gain: '<S9>/Gain'
       *  DataStoreRead: '<S9>/Data Store Read'
       *  Sum: '<S9>/Sum3'
       *  MinMax: '<S9>/MinMax4'
       */
      rtb_s32_rtmax = rt_MIN(rtb_s32_rtmax, rtb_MinMax1);
      rtb_MinMax1 = rt_MAX(rtb_s32_rtmax, rtb_DataTypeConversion1_g);

      /* DataStoreWrite: '<S9>/Data Store Write' incorporates:
       *  DataTypeConversion: '<S9>/Data Type Conversion2'
       */
      WatPumpPWMDuty = (uint16_T)(rtb_MinMax1 >> 5);

      /* DataStoreWrite: '<S9>/Data Store Write1' */
      WatPumpPWMDutyHiR = rtb_MinMax1;
    }
  }
}

/* Model step function */
void WatPumpCtrl_step(void)
{

  /* local block i/o variables*/
  int8_T rtb_inputevents[2];

  /* Output and update for atomic system: '<Root>/WatPumpCtrl' */

  /* Memory: '<S1>/Memory' */
  WatPumpCtrl_B.Memory = WatPumpCtrl_DWork.Memory_PreviousInput;

  /* trigger Stateflow Block: <S1>/WatPumpCtrl_Sched */

  {
    ZCEventType trigEvent;
    ZCEventType zcEvents[2];

    /* subsystem trigger input */
    trigEvent = NO_ZCEVENT;

    zcEvents[0] = (ZCEventType) ((WatPumpCtrl_U.ev_PowerOn > 0) &&
      (WatPumpCtrl_PrevZC.sf_WatPumpCtrl_Sched_ZCE[0] == 0));
    WatPumpCtrl_PrevZC.sf_WatPumpCtrl_Sched_ZCE[0] = (ZCSigState)
      WatPumpCtrl_U.ev_PowerOn;
    trigEvent = (zcEvents[0] == NO_ZCEVENT)? trigEvent : zcEvents[0];

    zcEvents[1] = (ZCEventType) ((WatPumpCtrl_U.ev_100ms > 0) &&
      (WatPumpCtrl_PrevZC.sf_WatPumpCtrl_Sched_ZCE[1] == 0));
    WatPumpCtrl_PrevZC.sf_WatPumpCtrl_Sched_ZCE[1] = (ZCSigState)
      WatPumpCtrl_U.ev_100ms;
    trigEvent = (zcEvents[1] == NO_ZCEVENT)? trigEvent : zcEvents[1];
    /* conditionally execute */
    if (trigEvent != NO_ZCEVENT) {

      /* update trigger block outputs */

      rtb_inputevents[0] = (int8_T) zcEvents[0];

      rtb_inputevents[1] = (int8_T) zcEvents[1];

      /* Stateflow: '<S1>/WatPumpCtrl_Sched' incorporates:
       *  Constant: '<S1>/ENWPUMPCTRL'
       *   Inport: '<Root>/Rpm'
       *  Constant: '<S1>/RPMTHRWPUMPON'
       *   Inport: '<Root>/CntTdcCrk'
       *  Constant: '<S1>/THRTDCWATPUMP'
       */

      {
        uint8_T b_previousEvent;
        if(rtb_inputevents[0] == 1) {
          b_previousEvent = _sfEvent_WatPumpCtrl_;
          _sfEvent_WatPumpCtrl_ = WatPumpCtrl_event_ev_PowerOn;
          if(_sfEvent_WatPumpCtrl_ == WatPumpCtrl_event_ev_PowerOn) {

            WatPumpCtrl_WatPumpCtrl_R();
          } else if(_sfEvent_WatPumpCtrl_ == WatPumpCtrl_event_ev_100ms) {
            if((ENWPUMPCTRL != 0) && (Rpm > RPMTHRWPUMPON) && (CntTdcCrk >
              THRTDCWATPUMP)) {

              WatPumpCtrl_WatPumpCtrl_D(Rpm, TWater, WatPumpCtrl_B.Memory);
            } else {

              WatPumpCtrl_WatPumpCtrl_R();
            }
          }
          _sfEvent_WatPumpCtrl_ = b_previousEvent;
        }
        if(rtb_inputevents[1] == 1) {
          b_previousEvent = _sfEvent_WatPumpCtrl_;
          _sfEvent_WatPumpCtrl_ = WatPumpCtrl_event_ev_100ms;
          if(_sfEvent_WatPumpCtrl_ == WatPumpCtrl_event_ev_PowerOn) {

            WatPumpCtrl_WatPumpCtrl_R();
          } else if(_sfEvent_WatPumpCtrl_ == WatPumpCtrl_event_ev_100ms) {
            if((ENWPUMPCTRL != 0) && (Rpm > RPMTHRWPUMPON) && (CntTdcCrk >
              THRTDCWATPUMP)) {

              WatPumpCtrl_WatPumpCtrl_D(Rpm, TWater, WatPumpCtrl_B.Memory);
            } else {

              WatPumpCtrl_WatPumpCtrl_R();
            }
          }
          _sfEvent_WatPumpCtrl_ = b_previousEvent;
        }
      }
    }
  }

  /* Update for Memory: '<S1>/Memory' */
  WatPumpCtrl_DWork.Memory_PreviousInput = TWaterErr;
}

/* Model initialize function */

void WatPumpCtrl_initialize(boolean_T firstTime)
{

  /* Zero-crossing state initialization */
  {
    int idx;
    for (idx = 0; idx < 2; idx ++) {
      WatPumpCtrl_PrevZC.sf_WatPumpCtrl_Sched_ZCE[idx] = POS_ZCSIG;
    }
  }

  /* Initial conditions for atomic system: '<Root>/WatPumpCtrl' */

  /* InitializeConditions for Memory: '<S1>/Memory' */
  WatPumpCtrl_DWork.Memory_PreviousInput = rtcP_Memory_X0;

  /* Machine initializer */
  _sfEvent_WatPumpCtrl_ = CALL_EVENT;
}

/* user code (bottom of source file) */
/* System: <Root>/WatPumpCtrl */
void WatPumpCtrl_Init(void)

{

  WatPumpCtrl_U.ev_PowerOn = 0;

  WatPumpCtrl_U.ev_100ms = 0;

  WatPumpCtrl_step();

  WatPumpCtrl_U.ev_PowerOn = 1;

  WatPumpCtrl_U.ev_100ms = 0;

  WatPumpCtrl_step();
}

void WatPumpCtrl_100ms(void)

{

  WatPumpCtrl_U.ev_PowerOn = 0;

  WatPumpCtrl_U.ev_100ms = 0;

  WatPumpCtrl_step();

  WatPumpCtrl_U.ev_PowerOn = 0;

  WatPumpCtrl_U.ev_100ms = 1;

  WatPumpCtrl_step();
}

#else

/* Stub variabili di uscita */
uint8_T FlgWatPumpCtrlOn = 0;

uint16_T WatPumpPWMDuty= 0;

#endif                                  // _BUILD_WATPUMPCTRL_                                                                                                                                                                                                                                                                                                                                                                                         

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
