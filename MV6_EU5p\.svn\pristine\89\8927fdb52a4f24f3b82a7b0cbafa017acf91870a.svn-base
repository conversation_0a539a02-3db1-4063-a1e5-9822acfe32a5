;
; Example script for programming internal flash of JPC563x derivatives:
; - SPC563M54 (768 kB flash)
; - MPC5632M (768 kB flash)
; - SPC563M60 (1 MB flash)
; - MPC5633M (1 MB flash)
; - SPC563M64 (1.5 MB flash)
; - MPC5634M (1.5 MB flash)
;
; Flash register base address is 0xc3f88000
;
; NOTES:
; 
;   Flash register base address has to be 0xc3f88000, because target
;   program does currently not support another base address.
;
; $Author: wdoering $
; $Date: 2009-06-02 14:54:40 +0200 (Di, 02 Jun 2009) $
; $Rev: 542 $
;

&flashbase=0x00000000
&shadowbase=0x00ffc000
&rambase=0x40000000

; Optimize flash programming time by switching on PLL
&optimize=0

;========================================================================
; CPU setup

SYStem.RESet
SYStem.BdmClock 4.0MHz
SYStem.CPU MPC55XX
SYStem.DETECT CPU
SYStem.Option.WATCHDOG OFF
SYStem.Up

;check processor revision
&NPC_DID=data.long(DBG:0x80)
if (&NPC_DID&0x0FFFF000)==0x08200000
(
  print "Processor revision 0 detected, calling jpc563xm_rev0.cmm..."
  DO ~~~~/jpc563xm_rev0.cmm
  ENDDO
)

if &optimize==1
(
  ; TBD
  ; setup PLL
  ; Data.Set ASD:0xC3F80000 %LONG 0x06000000
  ; set JTAG clock to 25 MHz
  ; SYStem.BdmClock 25MHz
)

; initialize internal SRAM
Data.Set EA:0x40000000--0x4000bfff %quad 0

; setup MMU for flash, RAM and register access
MMU.TLB1.SET 0x1 0xC0000500 0xFFF0000A 0xFFF0003F
MMU.TLB1.SET 0x2 0xC0000700 0x20000000 0x2000003F
MMU.TLB1.SET 0x3 0xC0000400 0x40000008 0x4000003F
MMU.TLB1.SET 0x4 0xC0000500 0xC3F00008 0xC3F0003F
MMU.TLB1.SET 0x5 0xC0000700 0x00000000 0x0000003F

;enable SPE
Register.Set SPE 1

;========================================================================
; Flash declaration

FLASH.RESet

FLASH.Create 1. (&flashbase+0x00000000)++0x03fff TARGET Quad 0. ; L0
FLASH.Create 1. (&flashbase+0x00004000)++0x03fff TARGET Quad 1. ; L1a 
FLASH.Create 1. (&flashbase+0x00008000)++0x07fff TARGET Quad 2. ; L1b 
FLASH.Create 1. (&flashbase+0x00010000)++0x07fff TARGET Quad 3. ; L2a 
FLASH.Create 1. (&flashbase+0x00018000)++0x03fff TARGET Quad 4. ; L2b 
FLASH.Create 1. (&flashbase+0x0001c000)++0x03fff TARGET Quad 5. ; L3
FLASH.Create 1. (&flashbase+0x00020000)++0x0ffff TARGET Quad 6. ; L4
FLASH.Create 1. (&flashbase+0x00030000)++0x0ffff TARGET Quad 7. ; L5

if (CPU()=="MPC5633M")||(CPU()=="MPC5634M")||(CPU()=="SPC563M60")||(CPU()=="SPC563M64")
(
  FLASH.Create 2. (&flashbase+0x00040000)++0x1ffff TARGET Quad 0. ; M0
  FLASH.Create 2. (&flashbase+0x00060000)++0x1ffff TARGET Quad 1. ; M1
)

&flashaddr=&flashbase+0x00080000
&Hx=0.
while &flashaddr<(&flashbase+0x100000)
(
  FLASH.Create 3. &flashaddr++0x1ffff TARGET Quad &Hx ; H0..H3
  &flashaddr=&flashaddr+0x20000
  &Hx=&Hx+1.
)

if (CPU()=="MPC5634M")||(CPU()=="SPC563M64")
(
  &Hx=0.
  while &flashaddr<(&flashbase+0x180000)
  (
    FLASH.Create 4. &flashaddr++0x1ffff TARGET Quad &Hx ; H0..H3
    &flashaddr=&flashaddr+0x20000
    &Hx=&Hx+1.
  )
)

; Shadow row
FLASH.Create 5. (&shadowbase+0x00000000)++0x3fff NOP Quad

FLASH.TARGET E:&rambase E:&rambase+0x2000 0x1000 ~~/demo/powerpc/flash/quad/c90fl563xm.bin /STACKSIZE 0x0200

;========================================================================
; Flash programming example
;
; Flash programming speed is about three times faster when memory class E:
; is used for data buffer (DUALPORT memory access). For DUALPORT access it
; is required to setup MemAccess NEXUS for both, NEXUS and JTAG debugger. 

DIALOG.YESNO "Flash programming prepared. Program flash memory now?"
entry &progflash

if &progflash 
(
  ;FLASH.Erase ALL
  ;FLASH.Auto ALL
  FLASH.ERASE 0x30000--0x15FFFF
  FLASH.Auto 0x30000--0x15FFFF

  
  Data.LOAD.binary ..\GHS\bin\M3M46D\M3M46D-appl.bin 0x30000--0x15FFFF

  FLASH.Auto OFF
)
else
(
  FLASH.LIST
)

  Data.LOAD.elf   ..\GHS\bin\M3M46D\M3M46D.elf 0x30000--0x15FFFF /GHILLS /NOCODE
  

;set JTAG clock back to default
SYStem.BdmClock 4MHz

;attach debugger to cores ====================================================
; PPC
sys.down
sys.bdmclock 4.M
;sys.o.DTM off ;no datatrace
;sys.o stall off  

sys.o NEXUS MDO4  

;PPC
sys.u
do .\sram_init_5633.cmm
do .\errata_e_5200.cmm
do .\win

ENDDO


























