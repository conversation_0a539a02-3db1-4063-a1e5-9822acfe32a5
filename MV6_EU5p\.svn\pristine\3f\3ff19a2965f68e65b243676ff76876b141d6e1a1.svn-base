/*
 * File: IdxCtfCtrl.h
 *
 * Code generated for Simulink model 'IdxCtfCtrl'.
 *
 * Model version                  : 1.480
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Feb 17 15:53:24 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (29), Warnings (3), Error (0)
 */

#ifndef RTW_HEADER_IdxCtfCtrl_h_
#define RTW_HEADER_IdxCtfCtrl_h_
#include <string.h>
#ifndef IdxCtfCtrl_COMMON_INCLUDES_
# define IdxCtfCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* IdxCtfCtrl_COMMON_INCLUDES_ */

#include "IdxCtfCtrl_types.h"

/* Includes for objects with custom storage classes. */
#include "idxctfctrl_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKANGTHCTFSPARE_dim            8U                        /* Referenced by: '<S22>/BKANGTHCTFSPARE_dim' */

/* dim */
#define BKRPMCTFSPARE_dim              8U                        /* Referenced by: '<S22>/BKRPMCTFSPARE_dim' */

/* dim */
#define ID_IDX_CTFCTRL                 24830280U                 /* Referenced by: '<S8>/ID_IDX_CTFCTRL' */

/* mask */
#define N_TYPE_CUTOFF                  9U                        /* Referenced by: '<S15>/N_TYPE_CUTOFF' */

/* dim */

/* Block signals and states (default storage) for system '<S1>/PreTDC' */
typedef struct {
  uint8_T VtIdxCtfFlgBuff_pie[4];      /* '<S5>/Calc_VtIdxCtfFlgBuff' */
  uint8_T Memory_PreviousInput_o4d[4]; /* '<S10>/Memory' */
  uint8_T Memory1_PreviousInput[4];    /* '<S10>/Memory1' */
  uint8_T x[4];                        /* '<S5>/Calc_VtIdxCtfFlgBuff' */
  uint8_T MinMax;                      /* '<S19>/MinMax' */
  uint8_T oldidxCutOff;                /* '<S5>/playIdxCutOff' */
} rtDW_PreTDC_IdxCtfCtrl_T;

/* Block signals and states (default storage) for system '<S1>/T10ms' */
typedef struct {
  int16_T Memory_PreviousInput;        /* '<S25>/Memory' */
  uint8_T Merge;                       /* '<S6>/Merge' */
  uint8_T mem;                         /* '<S21>/Select_StPlasObjB' */
  uint8_T is_active_c6_IdxCtfCtrl;     /* '<S21>/Select_StPlasObjB' */
} rtDW_T10ms_IdxCtfCtrl_T;

/* Extern declarations of internal data for system '<S1>/PreTDC' */
extern rtDW_PreTDC_IdxCtfCtrl_T IdxCtfCtrl_PreTDC_DW;

/* Extern declarations of internal data for system '<S1>/T10ms' */
extern rtDW_T10ms_IdxCtfCtrl_T IdxCtfCtrl_T10ms_DW;

/* Model entry point functions */
extern void IdxCtfCtrl_initialize(void);

/* Exported entry point function */
extern void Trig_IdxCtfCtrl_Init(void);

/* Exported entry point function */
extern void Trig_IdxCtfCtrl_PreTdc(void);

/* Exported entry point function */
extern void Trig_IdxCtfCtrl_T10ms(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S29>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S21>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Constant' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Propagation' : Unused code path elimination
 * Block '<S24>/Constant' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Propagation' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S29>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S29>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S29>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S30>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S30>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S30>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S23>/Reshape' : Reshape block reduction
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S24>/Reshape' : Reshape block reduction
 * Block '<S31>/Conversion' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IdxCtfCtrl'
 * '<S1>'   : 'IdxCtfCtrl/IdxCtfCtrl'
 * '<S2>'   : 'IdxCtfCtrl/Model Info'
 * '<S3>'   : 'IdxCtfCtrl/IdxCtfCtrl/Init'
 * '<S4>'   : 'IdxCtfCtrl/IdxCtfCtrl/Merge'
 * '<S5>'   : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC'
 * '<S6>'   : 'IdxCtfCtrl/IdxCtfCtrl/T10ms'
 * '<S7>'   : 'IdxCtfCtrl/IdxCtfCtrl/TP'
 * '<S8>'   : 'IdxCtfCtrl/IdxCtfCtrl/Init/Init_Data'
 * '<S9>'   : 'IdxCtfCtrl/IdxCtfCtrl/Init/Init_Scheduler'
 * '<S10>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Assign_Ctf'
 * '<S11>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Calc_CtfPattern'
 * '<S12>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Calc_IdxCtfFlg'
 * '<S13>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Calc_VtIdxCtfFlgBuff'
 * '<S14>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/IdxCtfCtrl_CntPreTDC'
 * '<S15>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Sel_IdxCutoff'
 * '<S16>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/playIdxCutOff'
 * '<S17>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Assign_Ctf/foCtf'
 * '<S18>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Sel_IdxCutoff/Cutoff_tot'
 * '<S19>'  : 'IdxCtfCtrl/IdxCtfCtrl/PreTDC/Sel_IdxCutoff/For Iterator Subsystem'
 * '<S20>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/If Action Subsystem1'
 * '<S21>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem'
 * '<S22>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Calc_Ratio'
 * '<S23>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Look2D_IR_U1'
 * '<S24>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Look2D_IR_U8'
 * '<S25>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Rate_Cutoff'
 * '<S26>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Select_StPlasObjB'
 * '<S27>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Calc_Ratio/PreLookUpIdSearch_U16_1'
 * '<S28>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Calc_Ratio/PreLookUpIdSearch_U16_2'
 * '<S29>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Calc_Ratio/PreLookUpIdSearch_U16_3'
 * '<S30>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Calc_Ratio/PreLookUpIdSearch_U16_4'
 * '<S31>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Rate_Cutoff/RateLimiter_S16'
 * '<S32>'  : 'IdxCtfCtrl/IdxCtfCtrl/T10ms/Subsystem/Rate_Cutoff/RateLimiter_S16/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': IdxCtfCtrl
 */
#endif                                 /* RTW_HEADER_IdxCtfCtrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
