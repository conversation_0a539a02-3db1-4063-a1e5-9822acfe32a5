/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _DIAGMGM_H_
#define _DIAGMGM_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "mathlib.h"
#include "recmgm.h"
#include "diagmgm_def.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define TBDISDIAG_CULS      3  /* Colonne TBDisDiag */

#define MIL_LAMP_GLOBAL_ON  (1)
#define MIL_LAMP_GLOBAL_OFF (0)

/* Commands for MIL_driveLamp(p_plusOrMinus,...) function */
#define MIL_MINUS_MINUS_CMD (0)
#define MIL_PLUS_PLUS_CMD   (1)
#define MIL_RESET_STS_CMD   (2)

/* MIL status */
typedef enum { 
    MIL_OFF = 0x0u, // 000
    MIL_ON1 = 0x1u, // 001
    MIL_ON3 = 0x3u  // 011 not used in MV SCANTOOL project
}milSts;

#define CLEAR_MIL_BIT_STATUS (0x7F)

/* Abilitazione diagnosi*/
#ifdef _BUILD_DIAGMGM_
#define  EN_DIAG_CAMLEVEL
#define  EN_DIAG_FLGBANKSEL
#define  EN_DIAG_ION_CH_A
//#define  EN_DIAG_ION_CH_B
#define  EN_DIAG_ION
#define  EN_DIAG_VANGTHR_1
#define  EN_DIAG_VANGTHR_2
#define  EN_DIAG_COH_VANGTHR
#define  EN_DIAG_DBW_CONTROL
#define  EN_DIAG_RPM_SENS
#endif

/* Valori di StDiag */
#define  NO_FAULT             0
#define  FAULT_FILTERING      1
#define  FAULT                2

/* Valori di VTDIAGENABLE */
#define  DISABLED             0
#define  ENABLE_KEEP_PWON     1
#define  ENABLE_RESET_PWON    2

/* Valori di VTDIAGENMONITOR */
#define  OBD_DISABLED       0
#define  OBD_FUEL_SYSTEM    1
#define  OBD_C_COMPONENT    2
#define  OBD_MISFIRE        3
#define  OBD_O2H            4
#define  OBD_LAMBDA         5
#define  OBD_CAT            6

/* Bit OBDMonitorStatus */
#define  BIT_MISFIRE        0
#define  BIT_FUEL_SYSTEM    1
#define  BIT_C_COMPONENT    2
#define  BIT_LAM_FUNC       3
#define  BIT_O2H            4
#define  BIT_CATALYST       5

/* Numero di righe di TBDISDIAG*/
#define  TBDISDIAG_ROWS       3

/* Numero iniziale per i flag di disabilitazione diagnosi */
#define FICT_DIAG_NUMBER    200

/* Nessuna diagnosi e' memorizzata */
#define NO_STORED_DIAG      255

#define DIAG_OFF    1
#define DIAG_ON     2

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/// Valori di StDiag
typedef uint8_T typStDiag;

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

extern uint16_T AbsCntWUC;                      // eeprom
extern uint8_T EEID3Flags10ms; 
extern uint8_T PtFault[DIAG_NUMBER];
extern uint8_T MIL_LampStatus;
extern uint8_T StDiag[DIAG_NUMBER];
extern uint8_T StoredDiag[DIAG_FAULT_LENGTH];
extern DiagDataFaultStruct DiagDataFault[DIAG_FAULT_LENGTH];


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern const uint8_T  VTTHRCONFFAULT[DIAG_NUMBER];
extern const uint8_T  VTSTEPINCFAULT[DIAG_NUMBER];
extern const uint8_T  VTSTEPDECFAULT[DIAG_NUMBER];
extern const uint8_T  VTFOFAULT[DIAG_NUMBER];
extern const uint8_T  VTDIAGENABLE[23];
extern const uint8_T  VTDIAGFFPRIORITY[23];
extern const uint8_T  VTDIAGENMONITOR[46];
extern const uint8_T  TBDISDIAG[DIAG_NUMBER][TBDISDIAG_ROWS];
extern const uint16_T THINFVBATDDIAG;
extern const uint16_T THSUPVBATDDIAG;
extern const uint8_T  THRDIAGWUC;
extern const uint8_T  DIAGFOFAULTIDX;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * DiagMgm_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DiagMgm_Init(void);

/*--------------------------------------------------------------------------*
 * DiagMgm_10ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DiagMgm_T10ms(void);

/*--------------------------------------------------------------------------*
 * DiagMgm_T100ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DiagMgm_T100ms(void);

/*--------------------------------------------------------------------------*
 * isValidDiag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 bool isValidDiag(uint16_t dtc);

/*--------------------------------------------------------------------------*
 * DiagMgm_SetDiagState - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DiagMgm_SetDiagState(uint8_T id,
                              uint8_T fault, 
                              uint8_T *state);

/*--------------------------------------------------------------------------*
 * DiagMgm_RangeCheck_U16 - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DiagMgm_RangeCheck_U16(uint8_T *fault,
                             uint16_T input, 
                             uint16_T min_input, 
                             uint16_T max_input,
                             uint8_T min_error,
                             uint8_T max_error);

/*--------------------------------------------------------------------------*
 * DiagMgm_GetDiagState - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DiagMgm_GetDiagState(uint8_T id, 
                           uint8_T *state);

/*--------------------------------------------------------------------------*
 * DiagMgm_SetDiagNode2Validity - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void DiagMgm_SetDiagNode2Validity(void);

/*--------------------------------------------------------------------------*
 * DiagMgm_ClearMilMgmData - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void DiagMgm_ClearMilMgmData(void);

/*--------------------------------------------------------------------------*
 * DRVC_WUC_Mgm_10ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void DRVC_WUC_Mgm_10ms(void);

/*--------------------------------------------------------------------------*
 * MIL_GetCalibStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 milSts MIL_GetCalibStatus(uint8_T p_diag_Id);

#endif  /* _DIAGMGM_H_*/

