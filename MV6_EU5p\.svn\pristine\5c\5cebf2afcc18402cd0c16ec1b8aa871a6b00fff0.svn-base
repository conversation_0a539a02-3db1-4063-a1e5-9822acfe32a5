/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "OS_api.h"
#include "tasksdefs.h"

#include "spi.h"

/* dummy word to clean the warning cases */
extern uint8_t dummy8;
#ifdef _BUILD_SPI_
void SPI_ConfigSiuChA (bool SPI_Mode) {
#if SPI_CH_A_EN   
    /***************************************************/
    /************************* DSPIA *******************/
    /**** SCKA ****/
    SIU.PCR[SCKA].B.PA = 0x3; /* Initialize pin assignment to primary, SCKA */
    SIU.PCR[SCKA].B.OBE = SPI_Mode; /* Enable output buffer (SCK OBE =1 when Master and 0 when Slave) */
    SIU.PCR[SCKA].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SCKA].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SCKA].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/

    /**** SINA ****/
    SIU.PCR[SINA].B.PA = 0x3; /* Initialize pin assignment to primary, SINA */
    SIU.PCR[SINA].B.OBE = 0; /* Disable output buffer as SINA is an input */
    SIU.PCR[SINA].B.IBE = 1; /* Enable input buffer so pin can be monitored
    (can be set to 0 to reduce power consumption)*/
    SIU.PCR[SINA].B.WPE = (SPI_CH_A_PULL_MODE & 0x2)>>1;
    SIU.PCR[SINA].B.WPS = (SPI_CH_A_PULL_MODE & 0x1);

    /**** SOUTA ****/
    SIU.PCR[SOUTA].B.PA = 0x3; /* Initialize pin assignment to secondary, SOUTA */
    SIU.PCR[SOUTA].B.OBE = 1; /* Enable output buffer as SOUTA is an output */
    SIU.PCR[SOUTA].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SOUTA].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SOUTA].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/

    /**** PCSA0 ****/
    #if PCSA0_ENABLE
    SIU.PCR[PCSA0].B.PA = PCSA0_PAD_SEL; /* Initialize pin assignment to primary, PCSA0 */
    SIU.PCR[PCSA0].B.OBE = SPI_Mode; /* Enable output buffer as PCSA0 is an output */
    SIU.PCR[PCSA0].B.IBE = 1 /*0*/; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSA0].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSA0].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/
    #endif
    /**** PCSA1 ****/
    #if PCSA1_ENABLE
    SIU.PCR[PCSA1].B.PA = PCSA1_PAD_SEL; /* Initialize pin assignment to primary, PCSA1 */
    SIU.PCR[PCSA1].B.OBE = 1; /* Enable output buffer as PCSA1 is an output */
    SIU.PCR[PCSA1].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSA1].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSA1].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/
    #endif

    /**** PCSA2 ****/
    #if PCSA2_ENABLE
    SIU.PCR[SCKD_PCSA2].B.PA = PCSA2_PAD_SEL; /* Initialize pin assignment to primary, PCSA2 */
    SIU.PCR[SCKD_PCSA2].B.OBE = 1; /* Enable output buffer as PCSA2 is an output */
    SIU.PCR[SCKD_PCSA2].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SCKD_PCSA2].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SCKD_PCSA2].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/
    #endif
    /**** PCSA3 ****/
    #if PCSA3_ENABLE
    SIU.PCR[SIND_PCSA3].B.PA = PCSA3_PAD_SEL; /* Initialize pin assignment to primary, PCSA3 */
    SIU.PCR[SIND_PCSA3].B.OBE = 1; /* Enable output buffer as PCSA3 is an output */
    SIU.PCR[SIND_PCSA3].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SIND_PCSA3].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SIND_PCSA3].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/
    #endif
    /**** PCSA4 ****/
    #if PCSA4_ENABLE
    SIU.PCR[PCSA4].B.PA = PCSA4_PAD_SEL; /* Initialize pin assignment to primary, PCSA4 */
    SIU.PCR[PCSA4].B.OBE = 1; /* Enable output buffer as PCSA4 is an output */
    SIU.PCR[PCSA4].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSA4].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSA4].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/
    #endif
    /**** PCSA5 ****/
    #if PCSA5_ENABLE
    SIU.PCR[PCSA5].B.PA = PCSA5_PAD_SEL; /* Initialize pin assignment to primary, PCSA5 */
    SIU.PCR[PCSA5].B.OBE = 1; /* Enable output buffer as PCSA5 is an output */
    SIU.PCR[PCSA5].B.IBE = 1;  /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSA5].B.DSC = SPI_CH_A_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSA5].B.SRC = SPI_CH_A_PCS_SRC ; /*  Slew Rate*/
    #endif
#else
    dummy8 = SPI_Mode;
#endif // SPI_CH_A_EN
}


void SPI_ConfigSiuChB (bool SPI_Mode) {
#if SPI_CH_B_EN    
    /****************************************************/
    /************************* DSPIB  *******************/
    /**** SCKB ****/
    SIU.PCR[SCKB].B.PA = 0x3; /* Initialize pin assignment to primary, SCKA */
    SIU.PCR[SCKB].B.OBE = SPI_Mode; /* Enable output buffer (SCK OBE =1 when Master and 0 when Slave) */
    SIU.PCR[SCKB].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SCKB].B.DSC = SPI_CH_B_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SCKB].B.SRC = SPI_CH_B_PCS_SRC ; /* slew rate */

    /**** SINB ****/
    SIU.PCR[SINB].B.PA = 0x3; /* Initialize pin assignment to primary, SINA */
    SIU.PCR[SINB].B.OBE = 0; /* Disable output buffer as SINA is an input */
    SIU.PCR[SINB].B.IBE = 1; /* Enable input buffer so pin can be monitored
    (can be set to 0 to reduce power consumption)*/
    SIU.PCR[SINB].B.WPE = (SPI_CH_B_PULL_MODE & 0x2)>>1;
    SIU.PCR[SINB].B.WPS = (SPI_CH_B_PULL_MODE & 0x1);
     

    /**** SOUTB ****/
    SIU.PCR[SOUTB].B.PA = 0x1; /* Initialize pin assignment to secondary, SOUTA */
    SIU.PCR[SOUTB].B.OBE = 1; /* Enable output buffer as SOUTA is an output */
    SIU.PCR[SOUTB].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SOUTB].B.DSC = SPI_CH_B_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SOUTB].B.SRC = SPI_CH_B_PCS_SRC ; /* slew rate */

    /**** PCSB0 ****/
    #if PCSB0_ENABLE
    SIU.PCR[PCSB0].B.PA = PCSB0_PAD_SEL; /* Initialize pin assignment to primary, PCSB0 */
    SIU.PCR[PCSB0].B.OBE = SPI_Mode; /* Enable output buffer as PCSB0 is an output */
    SIU.PCR[PCSB0].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSB0].B.DSC = SPI_CH_B_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSB0].B.SRC = SPI_CH_B_PCS_SRC ; /* slew rate */
    #endif
  
    /**** PCSB1 ****/
    #if PCSB1_ENABLE
    SIU.PCR[PCSB1].B.PA = PCSB1_PAD_SEL; /* Initialize pin assignment to primary, PCSB1 */
    SIU.PCR[PCSB1].B.OBE = 1; /* Enable output buffer as PCSB1 is an output */
    SIU.PCR[PCSB1].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSB1].B.DSC = SPI_CH_B_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSB1].B.SRC = SPI_CH_B_PCS_SRC ; /* slew rate */ 
    #endif

    /**** PCSB2 ****/
    #if PCSB2_ENABLE
    SIU.PCR[PCSB2].B.PA = PCSB2_PAD_SEL; /* Initialize pin assignment to secondary, PCSB2 */
    SIU.PCR[PCSB2].B.OBE = 1; /* Enable output buffer as PCSB2 is an output */
    SIU.PCR[PCSB2].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSB2].B.DSC = SPI_CH_B_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSB2].B.SRC = SPI_CH_B_PCS_SRC ;   /* slew rate */
    #endif

/**** PCSB3 ****/
    #if PCSB3_ENABLE
    SIU.PCR[PCSB3].B.PA = PCSB3_PAD_SEL; /* Initialize pin assignment to secondary, PCSB3 */
    SIU.PCR[PCSB3].B.OBE = 1; /* Enable output buffer as PCSB3 is an output */
    SIU.PCR[PCSB3].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSB3].B.DSC = SPI_CH_B_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSB3].B.SRC = SPI_CH_B_PCS_SRC ;   /* slew rate */
    #endif

    /**** PCSB4 ****/
    #if PCSB4_ENABLE
    SIU.PCR[PCSB4].B.PA = PCSB4_PAD_SEL; /* Initialize pin assignment to secondary, PCSB4 */
    SIU.PCR[PCSB4].B.OBE = 1; /* Enable output buffer as PCSB4 is an output */
    SIU.PCR[PCSB4].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSB4].B.DSC = SPI_CH_B_DRVSTREN ;  /*  drive strength*/
    SIU.PCR[PCSB4].B.SRC = SPI_CH_B_PCS_SRC ;   /* slew rate */
    #endif

    /**** PCSB5 ****/
    #if PCSB5_ENABLE
    SIU.PCR[PCSB5].B.PA = PCSB5_PAD_SEL; /* Initialize pin assignment to secondary, PCSB5 */
    SIU.PCR[PCSB5].B.OBE = 1; /* Enable output buffer as PCSB5 is an output */
    SIU.PCR[PCSB5].B.IBE = 1;  /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSB5].B.DSC = SPI_CH_B_DRVSTREN ;  /*  drive strength*/
    SIU.PCR[PCSB5].B.SRC = SPI_CH_B_PCS_SRC ;   /* slew rate */
    #endif
#else
    dummy8 = SPI_Mode;    
#endif // SPI_CH_B_EN 
}

void SPI_ConfigSiuChC (bool SPI_Mode) {
#if SPI_CH_C_EN   
    /***************************************************************/
    /************************* DSPIC (SPI Slave) *******************/
    /**** SCKC ****/
    SIU.PCR[SCKC].B.PA = 0x2; /* Initialize pin assignment to seconary, SCKC */
    SIU.PCR[SCKC].B.OBE = SPI_Mode; /* Enable output buffer (SCK OBE =1 when Master and 0 when Slave) */
    SIU.PCR[SCKC].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SCKC].B.DSC = SPI_CH_C_DRVSTREN ;   /*  drive strength*/
    SIU.PCR[SCKC].B.SRC = SPI_CH_C_PCS_SRC ;    /* slew rate */
    SIU.PCR[SCKC].B.ODE = 0;   /* open drain disabled *** Toni */

    /**** SINC ****/
    SIU.PCR[SINC].B.PA = 0x2; /* Initialize pin assignment to secondary, SINC */
    SIU.PCR[SINC].B.OBE = 0; /* Disable output buffer as SINA is an input */
    SIU.PCR[SINC].B.IBE = 1; /* Enable input buffer so pin can be monitored
    (can be set to 0 to reduce power consumption)*/
    SIU.PCR[SINC].B.WPE = (SPI_CH_C_PULL_MODE & 0x2)>>1;
    SIU.PCR[SINC].B.WPS = (SPI_CH_C_PULL_MODE & 0x1);

    /**** SOUTC ****/
    SIU.PCR[SOUTC].B.PA = 0x2; /* Initialize pin assignment to secondary, SOUTC */
    SIU.PCR[SOUTC].B.OBE = 1; /* Enable output buffer as SOUTA is an output */
    SIU.PCR[SOUTC].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SOUTC].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SOUTC].B.SRC = SPI_CH_C_PCS_SRC ; /* slew rate */
    SIU.PCR[SOUTC].B.ODE = 0;             /* open drain disabled *** Toni */

    /**** PCSC0 ****/
    #if PCSC0_ENABLE
    SIU.PCR[PCSC0].B.PA = PCSC0_PAD_SEL; /* Initialize pin assignment to secondary, PCSC0*/
    SIU.PCR[PCSC0].B.OBE = SPI_Mode; /* Disable output buffer as SSC0 is an output */
    SIU.PCR[PCSC0].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSC0].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSC0].B.SRC = SPI_CH_C_PCS_SRC ; /*  slew rate */
    #endif

    /**** PCSC1 ****/
    #if PCSC1_ENABLE
    SIU.PCR[PCSC1].B.PA = PCSC1_PAD_SEL; /* Initialize pin assignment to secondary, PCSC1*/
    SIU.PCR[PCSC1].B.OBE = 1; /* Enable output buffer as PCSC1 is an output */
    SIU.PCR[PCSC1].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSC1].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSC1].B.SRC = SPI_CH_C_PCS_SRC ; /* slew rate */
    #endif
    /**** PCSC2 ****/
    #if PCSC2_ENABLE
    SIU.PCR[PCSC2].B.PA = PCSC2_PAD_SEL; /* Initialize pin assignment to secondary, PCSC2*/
    SIU.PCR[PCSC2].B.OBE = 1; /* Enable output buffer as PCSC2 is an output */
    SIU.PCR[PCSC2].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSC2].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSC2].B.SRC = SPI_CH_C_PCS_SRC ; /* slew rate */
    #endif
    /**** PCSC3 ****/
    #if PCSC3_ENABLE
    SIU.PCR[PCSC3].B.PA = PCSC3_PAD_SEL; /* Initialize pin assignment to secondary, PCSC3*/
    SIU.PCR[PCSC3].B.OBE = 1; /* Enable output buffer as PCSC3 is an output */
    SIU.PCR[PCSC3].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSC3].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSC3].B.SRC = SPI_CH_C_PCS_SRC ; /* slew rate */
    #endif

    /**** PCSC4 ****/
    #if PCSC4_ENABLE
    SIU.PCR[PCSC4].B.PA = PCSC4_PAD_SEL; /* Initialize pin assignment to secondary, PCSC4*/
    SIU.PCR[PCSC4].B.OBE = 1; /* Enable output buffer as PCSC4 is an output */
    SIU.PCR[PCSC4].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSC4].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSC4].B.SRC = SPI_CH_C_PCS_SRC ; /* slew rate */
    #endif

    /**** PCSC5 ****/
    #if PCSC5_ENABLE
    SIU.PCR[PCSC5].B.PA = PCSC5_PAD_SEL; /* Initialize pin assignment to secondary, PCSC5*/
    SIU.PCR[PCSC5].B.OBE = 1; /* Enable output buffer as PCSC5 is an output */
    SIU.PCR[PCSC5].B.IBE = 1;  /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSC5].B.DSC = SPI_CH_C_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSC5].B.SRC = SPI_CH_C_PCS_SRC ; /* slew rate */
    #endif
#else
    dummy8 = SPI_Mode;
#endif // SPI_CH_C_EN
}

void SPI_ConfigSiuChD (bool SPI_Mode) {
#if SPI_CH_D_EN   
    /**********************************************************************/
    /******************** DSPID (SIU CONFIGURATION ) **********************/
    /**** SCKD ****/
    SIU.PCR[SCKD].B.PA = 0x2; /* Initialize pin assignment to secondary, SCKD */
    SIU.PCR[SCKD].B.OBE = SPI_Mode; /* disable output buffer (SCK OBE =1 when Master and 0 when Slave) */
    SIU.PCR[SCKD].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SCKD].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SCKD].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    /**** SIND ****/
    SIU.PCR[SIND].B.PA = 0x2; /* Initialize pin assignment to secondary, SIND */
    SIU.PCR[SIND].B.OBE = 0; /* Disable output buffer as SINA is an input */
    SIU.PCR[SIND].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SIND].B.WPE = (SPI_CH_D_PULL_MODE & 0x2)>>1;
    SIU.PCR[SIND].B.WPS = (SPI_CH_D_PULL_MODE & 0x1);
    /**** SOUTD ****/
    SIU.PCR[SOUTD].B.PA = 0x2; /* Initialize pin assignment to secondary, SOUTD */
    SIU.PCR[SOUTD].B.OBE = 1; /* Enable output buffer as SOUTD is an output */
    SIU.PCR[SOUTD].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[SOUTD].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[SOUTD].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    /**** PCSD0 ****/
    #if PCSD0_ENABLE
    SIU.PCR[PCSD0].B.PA = PCSD0_PAD_SEL; /* Initialize pin assignment to secondary, PCSD0 */
    SIU.PCR[PCSD0].B.OBE = SPI_Mode; /* Disable output buffer as PCSD0 is an input when SS */
    SIU.PCR[PCSD0].B.IBE = 1; /* Enable input buffer so pin can be SS */
    SIU.PCR[PCSD0].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSD0].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    #endif

    /**** PCSD1 ****/
    #if PCSD1_ENABLE
    SIU.PCR[PCSD1].B.PA = PCSD1_PAD_SEL; /* Initialize pin assignment to secondary, PCSD1 */
    SIU.PCR[PCSD1].B.OBE = 1; /* Enable output buffer as PCSD1 is an output */
    SIU.PCR[PCSD1].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSD1].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSD1].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    #endif

    /**** PCSD2 ****/
    #if PCSD2_ENABLE
    SIU.PCR[PCSD2].B.PA = PCSD2_PAD_SEL; /* Initialize pin assignment to secondary, PCSD2 */
    SIU.PCR[PCSD2].B.OBE = 1; /* Enable output buffer as PCSD2 is an output */
    SIU.PCR[PCSD2].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSD2].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSD2].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    #endif

    /**** PCSD3 ****/
    #if PCSD3_ENABLE
    SIU.PCR[PCSD3].B.PA = PCSD3_PAD_SEL; /* Initialize pin assignment to secondary, PCSD3 */
    SIU.PCR[PCSD3].B.OBE = 1; /* Enable output buffer as PCSA3 is an output */
    SIU.PCR[PCSD3].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSD3].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSD3].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    #endif

    /**** PCSD4 ****/
    #if PCSD4_ENABLE
    SIU.PCR[PCSD4].B.PA = PCSD4_PAD_SEL; /* Initialize pin assignment to secondary, PCSD4 */
    SIU.PCR[PCSD4].B.OBE = 1; /* Enable output buffer as PCSD4 is an output */
    SIU.PCR[PCSD4].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSD4].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSD4].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    #endif

    /**** PCSD5 ****/
    #if PCSD5_ENABLE
    SIU.PCR[PCSD5].B.PA = PCSD5_PAD_SEL; /* Initialize pin assignment to secondary, PCSD5 */
    SIU.PCR[PCSD5].B.OBE = 1; /* Enable output buffer as PCSA0 is an output */
    SIU.PCR[PCSD5].B.IBE = 1; /* Enable input buffer so pin can be monitored (can be set to 0 to reduce
    power consumption)*/
    SIU.PCR[PCSD5].B.DSC = SPI_CH_D_DRVSTREN ; /*  drive strength*/
    SIU.PCR[PCSD5].B.SRC = SPI_CH_D_PCS_SRC ; /*  Slew Rate*/
    #endif
#else
    dummy8 = SPI_Mode;
#endif // SPI_CH_D_EN   
}

#endif
