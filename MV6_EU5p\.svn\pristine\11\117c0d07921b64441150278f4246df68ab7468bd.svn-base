/*
 * File: PAtmModel.c
 *
 * Code generated for Simulink model 'PAtmModel'.
 *
 * Model version                  : 1.773
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 24 16:04:22 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "PAtmModel.h"
#include "PAtmModel_private.h"
#include "div_su32.h"

/* Named constants for Chart: '<S5>/Running_Chart' */
#define PAtmModel_IN_NO_ACTIVE_CHILD   ((uint8_T)0U)
#define PAtmModel_IN_RELIABLE          ((uint8_T)1U)
#define PAtmModel_IN_RUNNINGBUFFER     ((uint8_T)1U)
#define PAtmModel_IN_RUNNINGFAILED     ((uint8_T)2U)
#define PAtmModel_IN_RUNNINGSTOP       ((uint8_T)3U)
#define PAtmModel_IN_RUNNINGWAIT       ((uint8_T)4U)
#define PAtmModel_IN_SENSOR            ((uint8_T)2U)
#define PAtmModel_IN_UNRELIABLERUN     ((uint8_T)3U)

/* Named constants for Chart: '<S4>/PowerOn_Chart' */
#define PAtmModel_IN_BUFFER            ((uint8_T)1U)
#define PAtmModel_IN_FAILED            ((uint8_T)2U)
#define PAtmModel_IN_NO_ACTIVE_CHILD_j ((uint8_T)0U)
#define PAtmModel_IN_RELIABLE_m        ((uint8_T)1U)
#define PAtmModel_IN_SENSOR_p          ((uint8_T)2U)
#define PAtmModel_IN_STOP              ((uint8_T)3U)
#define PAtmModel_IN_UNRELIABLE        ((uint8_T)3U)
#define PAtmModel_IN_WAIT              ((uint8_T)4U)

/* user code (top of source file) */
/* System '<Root>/PAtmModel' */
#ifdef _BUILD_PATMMODEL_

/* Block signals (default storage) */
BlockIO_PAtmModel PAtmModel_B;

/* Block states (default storage) */
D_Work_PAtmModel PAtmModel_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_PAtmModel PAtmModel_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_PAtmModel PAtmModel_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint16_T AngThrPAtmMax;

/* PATMMODEL.AngThrPAtmMax: Max AngThrottle value for atmospheric pressure estimation */
uint16_T AngThrPAtmMin;

/* PATMMODEL.AngThrPAtmMin: Min AngThrottle value for atmospheric pressure estimation */
uint8_T FlgPAtmEst;

/* PATMMODEL.FlgPAtmEst: Estimation correctly terminated */
uint16_T PAtmBuffer;

/* PATMMODEL.PAtmBuffer: Buffer for atmospheric pressure estimation */
uint16_T PAtmBufferRun;

/* PATMMODEL.PAtmBuffer: Buffer for atmospheric pressure estimation */
uint16_T PresAtmTmp;

/* PATMMODEL.PresAtmTmp: Atmospheric pressure estimated (not saturated) */
uint8_T StPAtmRun;

/* PATMMODEL.StPAtmRun: Running estimation state */
uint8_T StPAtmStart;

/* PATMMODEL.StPAtmStart: PowerOn estimation state */

/* Output and update for function-call system: '<S1>/T100ms' */
void PAtmModel_T100ms(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T rtb_DiagMgm_RangeCheck_U16;
  uint16_T y;

  /* Outputs for Enabled SubSystem: '<S3>/Sensor' incorporates:
   *  EnablePort: '<S14>/Enable'
   */
  /* Constant: '<S3>/USEPATMSENSOR' */
  if (USEPATMSENSOR > 0) {
    /* Sum: '<S14>/Sum' incorporates:
     *  Constant: '<S14>/PRESATMGAIN'
     *  Constant: '<S14>/PRESATMOFFSET'
     *  DataStoreWrite: '<S14>/Data Store Write'
     *  Inport: '<Root>/VPresAtm'
     *  Product: '<S14>/Product'
     */
    PresAtmTmp = (uint16_T)(((uint16_T)(((uint16_T)(((uint32_T)VPresAtm *
      PRESATMGAIN) >> 2) * 625U) >> 9) + ((uint32_T)PRESATMOFFSET << 6)) >> 6);

    /* MinMax: '<S14>/MinMax1' incorporates:
     *  Constant: '<S14>/PRESATMMIN'
     *  DataStoreWrite: '<S14>/Data Store Write'
     */
    if (PresAtmTmp > PRESATMMIN) {
      y = PresAtmTmp;
    } else {
      y = PRESATMMIN;
    }

    /* End of MinMax: '<S14>/MinMax1' */

    /* MinMax: '<S14>/MinMax4' incorporates:
     *  Constant: '<S14>/PRESATMMAX'
     */
    if (y < PRESATMMAX) {
      PresAtm = y;
    } else {
      PresAtm = PRESATMMAX;
    }

    /* End of MinMax: '<S14>/MinMax4' */

    /* S-Function (DiagMgm_RangeCheck_U16): '<S15>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S14>/CC_TO_GND'
     *  Constant: '<S14>/CC_TO_VCC'
     *  Constant: '<S14>/VPRESATMMAX'
     *  Constant: '<S14>/VPRESATMMIN'
     *  Inport: '<Root>/VPresAtm'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16, VPresAtm, VPRESATMMIN,
      VPRESATMMAX, ((uint8_T)CC_TO_GND), ((uint8_T)CC_TO_VCC));

    /* S-Function (DiagMgm_SetDiagState): '<S16>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S14>/DIAG_PRESATM'
     */
    DiagMgm_SetDiagState( ((uint8_T)DIAG_PRESATM), rtb_DiagMgm_RangeCheck_U16,
                         &rtb_DiagMgm_SetDiagState);
  }

  /* End of Constant: '<S3>/USEPATMSENSOR' */
  /* End of Outputs for SubSystem: '<S3>/Sensor' */
}

/*
 * Output and update for function-call system:
 *    '<S5>/PresAtm_Saturation'
 *    '<S4>/PresAtm_Saturation'
 */
void PAtmModel_PresAtm_Saturation(void)
{
  uint16_T y;

  /* MinMax: '<S21>/MinMax1' incorporates:
   *  Constant: '<S21>/PRESATMMIN'
   *  DataStoreRead: '<S21>/Data Store Read2'
   */
  if (PresAtmTmp > PRESATMMIN) {
    y = PresAtmTmp;
  } else {
    y = PRESATMMIN;
  }

  /* End of MinMax: '<S21>/MinMax1' */

  /* MinMax: '<S21>/MinMax4' incorporates:
   *  Constant: '<S21>/PRESATMMAX'
   */
  if (y < PRESATMMAX) {
    PresAtm = y;
  } else {
    PresAtm = PRESATMMAX;
  }

  /* End of MinMax: '<S21>/MinMax4' */
}

/* Output and update for function-call system: '<S5>/AngThrPAtm_Calc' */
void PAtmModel_AngThrPAtm_Calc(uint16_T rtu_Rpm, uint16_T rtu_AngThrottle,
  int16_T rtu_DAngThr, rtB_AngThrPAtm_Calc_PAtmModel *localB)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_g;
  uint8_T rtb_Conversion3;
  int16_T tmp;

  /* DataTypeConversion: '<S26>/Data Type Conversion8' incorporates:
   *  Constant: '<S19>/BKRPMANGPATM_dim'
   */
  rtb_Conversion3 = (uint8_T)BKRPMANGPATM_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S26>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S19>/BKRPMANGPATM'
   */
  PreLookUpIdSearch_U16( &rtb_LookUp_IR_U16_g, &rtb_PreLookUpIdSearch_U16_o2,
                        rtu_Rpm, &BKRPMANGPATM[0], rtb_Conversion3);

  /* DataTypeConversion: '<S24>/Conversion3' incorporates:
   *  Constant: '<S19>/BKRPMANGPATM_dim'
   */
  rtb_Conversion3 = (uint8_T)BKRPMANGPATM_dim;

  /* S-Function (LookUp_IR_U16): '<S24>/LookUp_IR_U16' incorporates:
   *  Constant: '<S19>/VTANGTHRPATMMAX'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTANGTHRPATMMAX[0], rtb_LookUp_IR_U16_g,
                rtb_PreLookUpIdSearch_U16_o2, rtb_Conversion3);

  /* DataStoreWrite: '<S19>/Data Store Write' */
  AngThrPAtmMax = rtb_LookUp_IR_U16;

  /* DataTypeConversion: '<S25>/Conversion3' incorporates:
   *  Constant: '<S19>/BKRPMANGPATM_dim'
   */
  rtb_Conversion3 = (uint8_T)BKRPMANGPATM_dim;

  /* S-Function (LookUp_IR_U16): '<S25>/LookUp_IR_U16' incorporates:
   *  Constant: '<S19>/VTANGTHRPATMMIN'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16_g, &VTANGTHRPATMMIN[0], rtb_LookUp_IR_U16_g,
                rtb_PreLookUpIdSearch_U16_o2, rtb_Conversion3);

  /* DataStoreWrite: '<S19>/Data Store Write1' */
  AngThrPAtmMin = rtb_LookUp_IR_U16_g;

  /* DataTypeConversion: '<S23>/Data Type Conversion2' incorporates:
   *  DataTypeConversion: '<S23>/Data Type Conversion1'
   *  Sum: '<S23>/Add'
   */
  tmp = (int16_T)((int16_T)rtu_AngThrottle - rtu_DAngThr);
  if (tmp < 0) {
    tmp = 0;
  }

  localB->DataTypeConversion2 = (uint16_T)tmp;

  /* End of DataTypeConversion: '<S23>/Data Type Conversion2' */
}

/* Output and update for function-call system: '<S1>/TDC' */
void PAtmModel_TDC(void)
{
  /* Chart: '<S5>/Running_Chart' incorporates:
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/CntAbsTdc'
   *  Inport: '<Root>/DAngThr'
   *  Inport: '<Root>/MapSignalPAtm'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/VtRec'
   */
  /* Gateway: PAtmModel/TDC/Running_Chart */
  /* During: PAtmModel/TDC/Running_Chart */
  if (PAtmModel_DWork.bitsForTID0.is_active_c2_PAtmModel == 0U) {
    /* Entry: PAtmModel/TDC/Running_Chart */
    PAtmModel_DWork.bitsForTID0.is_active_c2_PAtmModel = 1U;

    /* Entry Internal: PAtmModel/TDC/Running_Chart */
    /* Transition: '<S22>:207' */
    if (USEPATMSENSOR != 0) {
      /* Transition: '<S22>:210' */
      StPAtmRun = ((uint8_T)SENSOR);
      PAtmModel_DWork.bitsForTID0.is_c2_PAtmModel = PAtmModel_IN_SENSOR;
    } else {
      /* Transition: '<S22>:214' */
      /* Transition: '<S22>:196' */
      StPAtmRun = ((uint8_T)RUNNINGWAIT);
      PAtmModel_DWork.TaskCounter_m = 0U;
      PAtmModel_DWork.bitsForTID0.is_c2_PAtmModel = PAtmModel_IN_RELIABLE;
      PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_RUNNINGWAIT;
    }
  } else {
    switch (PAtmModel_DWork.bitsForTID0.is_c2_PAtmModel) {
     case PAtmModel_IN_RELIABLE:
      /* During 'RELIABLE': '<S22>:152' */
      if (((VtRec[((uint8_T)REC_AF_OL)] != 0) && (VtRec[((uint8_T)REC_LIMIT_RPM)]
            != 0)) || (VtRec[((uint8_T)REC_ALFA_N)] != 0) || (VtRec[((uint8_T)
            REC_FORCE_LH)] != 0)) {
        /* Transition: '<S22>:73' */
        PresAtm = PRESATMNOM;
        FlgPAtmEst = 0U;
        StPAtmRun = ((uint8_T)UNRELIABLERUN);

        /* Exit Internal 'RELIABLE': '<S22>:152' */
        PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_NO_ACTIVE_CHILD;
        PAtmModel_DWork.bitsForTID0.is_c2_PAtmModel = PAtmModel_IN_UNRELIABLERUN;
      } else if (CntAbsTdc == 1U) {
        /* Transition: '<S22>:194' */
        /* Transition: '<S22>:196' */
        StPAtmRun = ((uint8_T)RUNNINGWAIT);
        PAtmModel_DWork.TaskCounter_m = 0U;

        /* Exit Internal 'RELIABLE': '<S22>:152' */
        PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_RUNNINGWAIT;
      } else {
        switch (PAtmModel_DWork.bitsForTID0.is_RELIABLE_p) {
         case PAtmModel_IN_RUNNINGBUFFER:
          /* Outputs for Function Call SubSystem: '<S5>/AngThrPAtm_Calc' */
          /* During 'RUNNINGBUFFER': '<S22>:187' */
          /* Transition: '<S22>:178' */
          /* Event: '<S22>:147' */
          PAtmModel_AngThrPAtm_Calc(Rpm, AngThrottle, DAngThr,
            &PAtmModel_B.AngThrPAtm_Calc);

          /* End of Outputs for SubSystem: '<S5>/AngThrPAtm_Calc' */
          PAtmBufferRun = (uint16_T)(PAtmBufferRun + MapSignalPAtm);
          PAtmModel_DWork.IdxBuffer_a++;
          if ((PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 < AngThrPAtmMin) ||
              (PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 > AngThrPAtmMax))
          {
            /* Transition: '<S22>:180' */
            StPAtmRun = ((uint8_T)RUNNINGFAILED);
            PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
              PAtmModel_IN_RUNNINGFAILED;
          } else {
            /* Transition: '<S22>:181' */
            if (PAtmModel_DWork.IdxBuffer_a < PATMBUFFSIZE) {
              /* Transition: '<S22>:177' */
              PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
                PAtmModel_IN_RUNNINGBUFFER;
            } else {
              /* Transition: '<S22>:182' */
              FlgPAtmEst = 1U;
              StPAtmRun = ((uint8_T)RUNNINGSTOP);

              /* Outputs for Function Call SubSystem: '<S5>/PresAtm_Adapt' */
              /* Sum: '<S20>/Add1' incorporates:
               *  Constant: '<S20>/PATMADAPTGAIN'
               *  DataStoreRead: '<S20>/Data Store Read1'
               *  DataStoreWrite: '<S20>/Data Store Write'
               *  Product: '<S20>/Mul'
               *  Sum: '<S20>/Add'
               */
              /* Event: '<S22>:148' */
              PresAtmTmp = (uint16_T)((int16_T)(((int16_T)((int16_T)div_su32
                (PAtmBufferRun, PATMBUFFSIZE) - (int16_T)PresAtm) *
                PATMADAPTGAIN) >> 15) + PresAtm);

              /* End of Outputs for SubSystem: '<S5>/PresAtm_Adapt' */

              /* Outputs for Function Call SubSystem: '<S5>/PresAtm_Saturation' */
              /* Event: '<S22>:149' */
              PAtmModel_PresAtm_Saturation();

              /* End of Outputs for SubSystem: '<S5>/PresAtm_Saturation' */
              PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
                PAtmModel_IN_RUNNINGSTOP;
            }
          }
          break;

         case PAtmModel_IN_RUNNINGFAILED:
          /* Outputs for Function Call SubSystem: '<S5>/AngThrPAtm_Calc' */
          /* During 'RUNNINGFAILED': '<S22>:188' */
          /* Transition: '<S22>:176' */
          /* Event: '<S22>:147' */
          PAtmModel_AngThrPAtm_Calc(Rpm, AngThrottle, DAngThr,
            &PAtmModel_B.AngThrPAtm_Calc);

          /* End of Outputs for SubSystem: '<S5>/AngThrPAtm_Calc' */
          if ((PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 >= AngThrPAtmMin)
              && (PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 <=
                  AngThrPAtmMax)) {
            /* Transition: '<S22>:167' */
            StPAtmRun = ((uint8_T)RUNNINGWAIT);
            PAtmModel_DWork.TaskCounter_m = 0U;
            PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_RUNNINGWAIT;
          } else {
            /* Transition: '<S22>:175' */
            PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
              PAtmModel_IN_RUNNINGFAILED;
          }
          break;

         case PAtmModel_IN_RUNNINGSTOP:
          /* Outputs for Function Call SubSystem: '<S5>/AngThrPAtm_Calc' */
          /* During 'RUNNINGSTOP': '<S22>:189' */
          /* Transition: '<S22>:185' */
          /* Event: '<S22>:147' */
          PAtmModel_AngThrPAtm_Calc(Rpm, AngThrottle, DAngThr,
            &PAtmModel_B.AngThrPAtm_Calc);

          /* End of Outputs for SubSystem: '<S5>/AngThrPAtm_Calc' */
          if ((PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 >= AngThrPAtmMin)
              && (PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 <=
                  AngThrPAtmMax)) {
            /* Transition: '<S22>:184' */
            StPAtmRun = ((uint8_T)RUNNINGWAIT);
            PAtmModel_DWork.TaskCounter_m = 0U;
            PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_RUNNINGWAIT;
          } else {
            /* Transition: '<S22>:183' */
            PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_RUNNINGSTOP;
          }
          break;

         default:
          /* Outputs for Function Call SubSystem: '<S5>/AngThrPAtm_Calc' */
          /* During 'RUNNINGWAIT': '<S22>:186' */
          /* Transition: '<S22>:170' */
          /* Event: '<S22>:147' */
          PAtmModel_AngThrPAtm_Calc(Rpm, AngThrottle, DAngThr,
            &PAtmModel_B.AngThrPAtm_Calc);

          /* End of Outputs for SubSystem: '<S5>/AngThrPAtm_Calc' */
          PAtmModel_DWork.TaskCounter_m++;
          if ((PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 < AngThrPAtmMin) ||
              (PAtmModel_B.AngThrPAtm_Calc.DataTypeConversion2 > AngThrPAtmMax))
          {
            /* Transition: '<S22>:171' */
            StPAtmRun = ((uint8_T)RUNNINGFAILED);
            PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
              PAtmModel_IN_RUNNINGFAILED;
          } else {
            /* Transition: '<S22>:172' */
            if (PAtmModel_DWork.TaskCounter_m < NRUNWAITPATM) {
              /* Transition: '<S22>:169' */
              PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
                PAtmModel_IN_RUNNINGWAIT;
            } else {
              /* Transition: '<S22>:174' */
              PAtmModel_DWork.IdxBuffer_a = 0U;
              StPAtmRun = ((uint8_T)RUNNINGBUFFER);
              PAtmBufferRun = 0U;
              PAtmModel_DWork.bitsForTID0.is_RELIABLE_p =
                PAtmModel_IN_RUNNINGBUFFER;
            }
          }
          break;
        }
      }
      break;

     case PAtmModel_IN_SENSOR:
      /* During 'SENSOR': '<S22>:208' */
      break;

     default:
      /* During 'UNRELIABLERUN': '<S22>:1' */
      if (((VtRec[((uint8_T)REC_AF_OL)] == 0) || (VtRec[((uint8_T)REC_LIMIT_RPM)]
            == 0)) && (VtRec[((uint8_T)REC_ALFA_N)] == 0) && (VtRec[((uint8_T)
            REC_FORCE_LH)] == 0)) {
        /* Transition: '<S22>:14' */
        /* Transition: '<S22>:196' */
        StPAtmRun = ((uint8_T)RUNNINGWAIT);
        PAtmModel_DWork.TaskCounter_m = 0U;
        PAtmModel_DWork.bitsForTID0.is_c2_PAtmModel = PAtmModel_IN_RELIABLE;
        PAtmModel_DWork.bitsForTID0.is_RELIABLE_p = PAtmModel_IN_RUNNINGWAIT;
      }
      break;
    }
  }

  /* End of Chart: '<S5>/Running_Chart' */
}

/* Output and update for function-call system: '<S1>/Init' */
void PAtmModel_Init(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState_b;
  uint8_T rtb_DiagMgm_RangeCheck_U16_c;
  uint16_T y;

  /* If: '<S2>/If' incorporates:
   *  Constant: '<S2>/USEPATMSENSOR'
   */
  if (USEPATMSENSOR > 0) {
    /* Outputs for IfAction SubSystem: '<S2>/SENSOR' incorporates:
     *  ActionPort: '<S11>/Action Port'
     */
    /* Sum: '<S11>/Sum' incorporates:
     *  Constant: '<S11>/PRESATMGAIN'
     *  Constant: '<S11>/PRESATMOFFSET'
     *  DataStoreWrite: '<S11>/Data Store Write'
     *  Inport: '<Root>/VPresAtm'
     *  Product: '<S11>/Product'
     */
    PresAtmTmp = (uint16_T)(((uint16_T)(((uint16_T)(((uint32_T)VPresAtm *
      PRESATMGAIN) >> 2) * 625U) >> 9) + ((uint32_T)PRESATMOFFSET << 6)) >> 6);

    /* S-Function (DiagMgm_RangeCheck_U16): '<S12>/DiagMgm_RangeCheck_U16' incorporates:
     *  Constant: '<S11>/CC_TO_GND'
     *  Constant: '<S11>/CC_TO_VCC'
     *  Constant: '<S11>/VPRESATMMAX'
     *  Constant: '<S11>/VPRESATMMIN'
     *  Inport: '<Root>/VPresAtm'
     */
    DiagMgm_RangeCheck_U16( &rtb_DiagMgm_RangeCheck_U16_c, VPresAtm, VPRESATMMIN,
      VPRESATMMAX, ((uint8_T)CC_TO_GND), ((uint8_T)CC_TO_VCC));

    /* Switch: '<S11>/Switch' incorporates:
     *  Constant: '<S11>/NO_PT_FAULT'
     *  Constant: '<S11>/PRESATMNOM'
     *  DataStoreWrite: '<S11>/Data Store Write1'
     *  RelationalOperator: '<S11>/Relational Operator'
     */
    if (rtb_DiagMgm_RangeCheck_U16_c == ((uint8_T)NO_PT_FAULT)) {
      /* MinMax: '<S11>/MinMax1' incorporates:
       *  Constant: '<S11>/PRESATMMIN'
       *  DataStoreWrite: '<S11>/Data Store Write'
       */
      if (PresAtmTmp > PRESATMMIN) {
        y = PresAtmTmp;
      } else {
        y = PRESATMMIN;
      }

      /* End of MinMax: '<S11>/MinMax1' */

      /* MinMax: '<S11>/MinMax4' incorporates:
       *  Constant: '<S11>/PRESATMMAX'
       */
      if (y < PRESATMMAX) {
        PresAtm = y;
      } else {
        PresAtm = PRESATMMAX;
      }

      /* End of MinMax: '<S11>/MinMax4' */
    } else {
      PresAtm = PRESATMNOM;
    }

    /* End of Switch: '<S11>/Switch' */

    /* S-Function (DiagMgm_SetDiagState): '<S13>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S11>/DIAG_PRESATM'
     */
    DiagMgm_SetDiagState( ((uint8_T)DIAG_PRESATM), rtb_DiagMgm_RangeCheck_U16_c,
                         &rtb_DiagMgm_SetDiagState_b);

    /* End of Outputs for SubSystem: '<S2>/SENSOR' */
  } else {
    /* Outputs for IfAction SubSystem: '<S2>/NO_SENSOR' incorporates:
     *  ActionPort: '<S10>/Action Port'
     */
    /* DataStoreWrite: '<S10>/Data Store Write1' incorporates:
     *  Constant: '<S10>/ZERO'
     */
    FlgPAtmEst = 0U;

    /* End of Outputs for SubSystem: '<S2>/NO_SENSOR' */
  }

  /* End of If: '<S2>/If' */
}

/* Output and update for function-call system: '<S1>/T5ms' */
void PAtmModel_T5ms(void)
{
  /* Chart: '<S4>/PowerOn_Chart' incorporates:
   *  Inport: '<Root>/FlgSyncReady'
   *  Inport: '<Root>/MapSignalPAtm'
   *  Inport: '<Root>/StSync'
   *  Inport: '<Root>/VtRec'
   */
  /* Gateway: PAtmModel/T5ms/PowerOn_Chart */
  /* During: PAtmModel/T5ms/PowerOn_Chart */
  if (PAtmModel_DWork.bitsForTID0.is_active_c1_PAtmModel == 0U) {
    /* Entry: PAtmModel/T5ms/PowerOn_Chart */
    PAtmModel_DWork.bitsForTID0.is_active_c1_PAtmModel = 1U;

    /* Entry Internal: PAtmModel/T5ms/PowerOn_Chart */
    /* Transition: '<S17>:161' */
    if (USEPATMSENSOR != 0) {
      /* Transition: '<S17>:179' */
      StPAtmStart = ((uint8_T)SENSOR);
      PAtmModel_DWork.bitsForTID0.is_c1_PAtmModel = PAtmModel_IN_SENSOR_p;
    } else {
      /* Transition: '<S17>:17' */
      /* Transition: '<S17>:175' */
      PAtmModel_DWork.TaskCounter = 0U;
      FlgPAtmEst = 0U;
      StPAtmStart = ((uint8_T)WAIT);
      PAtmModel_DWork.bitsForTID0.is_c1_PAtmModel = PAtmModel_IN_RELIABLE_m;
      PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_WAIT;
    }
  } else {
    switch (PAtmModel_DWork.bitsForTID0.is_c1_PAtmModel) {
     case PAtmModel_IN_RELIABLE_m:
      /* During 'RELIABLE': '<S17>:11' */
      if (((VtRec[((uint8_T)REC_AF_OL)] != 0) && (VtRec[((uint8_T)REC_LIMIT_RPM)]
            != 0)) || (VtRec[((uint8_T)REC_ALFA_N)] != 0) || (VtRec[((uint8_T)
            REC_FORCE_LH)] != 0)) {
        /* Transition: '<S17>:73' */
        PresAtm = PRESATMNOM;
        FlgPAtmEst = 0U;
        StPAtmStart = ((uint8_T)UNRELIABLE);

        /* Exit Internal 'RELIABLE': '<S17>:11' */
        PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_NO_ACTIVE_CHILD_j;
        PAtmModel_DWork.bitsForTID0.is_c1_PAtmModel = PAtmModel_IN_UNRELIABLE;
      } else {
        switch (PAtmModel_DWork.bitsForTID0.is_RELIABLE) {
         case PAtmModel_IN_BUFFER:
          /* During 'BUFFER': '<S17>:3' */
          /* Transition: '<S17>:23' */
          PAtmBuffer = (uint16_T)(PAtmBuffer + MapSignalPAtm);
          PAtmModel_DWork.IdxBuffer++;
          if ((FlgSyncReady == 0) || (StSync != ((uint8_T)NO_SYNCH))) {
            /* Transition: '<S17>:28' */
            StPAtmStart = ((uint8_T)FAILED);
            PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_FAILED;
          } else {
            /* Transition: '<S17>:22' */
            if (PAtmModel_DWork.IdxBuffer < PATMBUFFSIZE) {
              /* Transition: '<S17>:27' */
              PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_BUFFER;
            } else {
              /* Transition: '<S17>:24' */
              FlgPAtmEst = 1U;
              StPAtmStart = ((uint8_T)STOP);
              PresAtmTmp = (uint16_T)div_su32(PAtmBuffer, PATMBUFFSIZE);

              /* Outputs for Function Call SubSystem: '<S4>/PresAtm_Saturation' */
              /* Event: '<S17>:149' */
              PAtmModel_PresAtm_Saturation();

              /* End of Outputs for SubSystem: '<S4>/PresAtm_Saturation' */
              PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_STOP;
            }
          }
          break;

         case PAtmModel_IN_FAILED:
          /* During 'FAILED': '<S17>:5' */
          /* Transition: '<S17>:163' */
          if ((FlgSyncReady == 1) && (StSync == ((uint8_T)NO_SYNCH))) {
            /* Transition: '<S17>:166' */
            StPAtmStart = ((uint8_T)WAIT);
            PAtmModel_DWork.TaskCounter = 0U;
            PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_WAIT;
          }
          break;

         case PAtmModel_IN_STOP:
          /* During 'STOP': '<S17>:4' */
          /* Transition: '<S17>:165' */
          if ((FlgSyncReady == 1) && (StSync == ((uint8_T)NO_SYNCH))) {
            /* Transition: '<S17>:166' */
            StPAtmStart = ((uint8_T)WAIT);
            PAtmModel_DWork.TaskCounter = 0U;
            PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_WAIT;
          }
          break;

         default:
          /* During 'WAIT': '<S17>:2' */
          /* Transition: '<S17>:19' */
          PAtmModel_DWork.TaskCounter++;
          if ((FlgSyncReady == 0) || (StSync != ((uint8_T)NO_SYNCH))) {
            /* Transition: '<S17>:25' */
            StPAtmStart = ((uint8_T)FAILED);
            PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_FAILED;
          } else {
            /* Transition: '<S17>:26' */
            if (PAtmModel_DWork.TaskCounter < NWAITPATM) {
              /* Transition: '<S17>:21' */
              PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_WAIT;
            } else {
              /* Transition: '<S17>:20' */
              PAtmModel_DWork.IdxBuffer = 0U;
              StPAtmStart = ((uint8_T)BUFFER);
              PAtmBuffer = 0U;
              PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_BUFFER;
            }
          }
          break;
        }
      }
      break;

     case PAtmModel_IN_SENSOR_p:
      /* During 'SENSOR': '<S17>:177' */
      break;

     default:
      /* During 'UNRELIABLE': '<S17>:1' */
      if (((VtRec[((uint8_T)REC_AF_OL)] == 0) || (VtRec[((uint8_T)REC_LIMIT_RPM)]
            == 0)) && (VtRec[((uint8_T)REC_ALFA_N)] == 0) && (VtRec[((uint8_T)
            REC_FORCE_LH)] == 0)) {
        /* Transition: '<S17>:14' */
        /* Transition: '<S17>:175' */
        PAtmModel_DWork.TaskCounter = 0U;
        FlgPAtmEst = 0U;
        StPAtmStart = ((uint8_T)WAIT);
        PAtmModel_DWork.bitsForTID0.is_c1_PAtmModel = PAtmModel_IN_RELIABLE_m;
        PAtmModel_DWork.bitsForTID0.is_RELIABLE = PAtmModel_IN_WAIT;
      }
      break;
    }
  }

  /* End of Chart: '<S4>/PowerOn_Chart' */
}

/* Model step function */
void PAtmModel_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/PAtmModel' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem4' incorporates:
   *  TriggerPort: '<S9>/Trigger'
   */
  /* Inport: '<Root>/ev_5ms' */
  if ((PAtmModel_U.ev_5ms > 0) &&
      (PAtmModel_PrevZCSigState.TriggeredSubsystem4_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S9>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T5ms'
     */
    PAtmModel_T5ms();

    /* End of Outputs for S-Function (fcncallgen): '<S9>/Function-Call Generator' */
  }

  PAtmModel_PrevZCSigState.TriggeredSubsystem4_Trig_ZCE = (ZCSigState)
    (PAtmModel_U.ev_5ms > 0);

  /* End of Inport: '<Root>/ev_5ms' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem4' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem2' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_TDC' */
  if ((PAtmModel_U.ev_TDC > 0) &&
      (PAtmModel_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/TDC'
     */
    PAtmModel_TDC();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  PAtmModel_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE = (ZCSigState)
    (PAtmModel_U.ev_TDC > 0);

  /* End of Inport: '<Root>/ev_TDC' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem2' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem3' incorporates:
   *  TriggerPort: '<S8>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((PAtmModel_U.ev_PowerOn > 0) &&
      (PAtmModel_PrevZCSigState.TriggeredSubsystem3_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S8>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    PAtmModel_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S8>/Function-Call Generator' */
  }

  PAtmModel_PrevZCSigState.TriggeredSubsystem3_Trig_ZCE = (ZCSigState)
    (PAtmModel_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem3' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem1' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_100ms' */
  if ((PAtmModel_U.ev_100ms > 0) &&
      (PAtmModel_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T100ms'
     */
    PAtmModel_T100ms();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  PAtmModel_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE = (ZCSigState)
    (PAtmModel_U.ev_100ms > 0);

  /* End of Inport: '<Root>/ev_100ms' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem1' */

  /* End of Outputs for SubSystem: '<Root>/PAtmModel' */
}

/* Model initialize function */
void PAtmModel_initialize(void)
{
  PAtmModel_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE = POS_ZCSIG;
  PAtmModel_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE = POS_ZCSIG;
  PAtmModel_PrevZCSigState.TriggeredSubsystem3_Trig_ZCE = POS_ZCSIG;
  PAtmModel_PrevZCSigState.TriggeredSubsystem4_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/PAtmModel' */
void PAtmModel_PowerOn(void)
{
  PAtmModel_initialize();
  PAtmModel_Init();                    /* Inizializzazione macchine a stati */
}

#endif                                 // _BUILD_PATMMODEL_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
