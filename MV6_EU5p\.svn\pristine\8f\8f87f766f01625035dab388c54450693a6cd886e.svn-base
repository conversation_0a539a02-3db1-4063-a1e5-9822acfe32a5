/*
 * File: AirMgm_types.h
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2341
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Sep 21 14:33:03 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_AirMgm_types_h_
#define RTW_HEADER_AirMgm_types_h_

/* Includes for objects with custom storage classes. */
#include "air_mgm.h"

/*
 * Check that imported macros with storage class "ImportedDefine" are defined
 */
#ifndef N_QA_DEL_BUF
#error The variable for the parameter "N_QA_DEL_BUF" is not defined
#endif

/*
 * Registered constraints for dimension variants
 */
/* Constraint 'N_QA_DEL_BUF == 7' registered by:
 * '<S69>/Assignment'
 * '<S69>/Selector'
 * '<S284>/Assignment'
 * '<S284>/Selector'
 * '<S72>/buffer_old'
 * '<S184>/Assignment'
 * '<S184>/Selector'
 * '<S269>/Assignment'
 * '<S269>/Selector'
 * '<S292>/buffer_old'
 * '<S187>/buffer_old'
 * '<S277>/buffer_old'
 */
#if N_QA_DEL_BUF != 7
# error "The preprocessor definition 'N_QA_DEL_BUF' must be equal to '7'"
#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_AirMgm RT_MODEL_AirMgm;

#endif                                 /* RTW_HEADER_AirMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
