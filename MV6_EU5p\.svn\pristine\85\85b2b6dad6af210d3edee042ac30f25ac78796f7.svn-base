/*
 * File: mul_u32_loSR.c
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2232
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Oct 28 14:23:17 2020
 */

#include "rtwtypes.h"
#include "mul_wide_u32.h"
#include "mul_u32_loSR.h"

uint32_T mul_u32_loSR(uint32_T a, uint32_T b, uint32_T aShift)
{
  uint32_T result;
  uint32_T u32_chi;
  mul_wide_u32(a, b, &u32_chi, &result);
  return (u32_chi << /*MW:OvBitwiseOk*/ (32U - aShift)) | (result >> aShift);
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
