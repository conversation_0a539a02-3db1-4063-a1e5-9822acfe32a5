/*
 * File: ThrottleAdapt_types.h
 *
 * Code generated for Simulink model 'ThrottleAdapt'.
 *
 * Model version                  : 1.736
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun  8 17:18:28 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ThrottleAdapt_types_h_
#define RTW_HEADER_ThrottleAdapt_types_h_

#if 0
/*
 * Registered constraints for dimension variants
 */
/* Constraint 'REC_DBW_OFF > 0' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_DBW_OFF <= 0
# error "The preprocessor definition 'REC_DBW_OFF' must be greater than '0'"
#endif

/* Constraint 'REC_ENG_OFF > 0' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_ENG_OFF <= 0
# error "The preprocessor definition 'REC_ENG_OFF' must be greater than '0'"
#endif

/* Constraint 'REC_NO_DBW_SELF > 0' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_NO_DBW_SELF <= 0
# error "The preprocessor definition 'REC_NO_DBW_SELF' must be greater than '0'"
#endif

/* Constraint 'REC_ALFA_N > 0' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_ALFA_N <= 0
# error "The preprocessor definition 'REC_ALFA_N' must be greater than '0'"
#endif

/* Constraint 'REC_FORCE_LH > 0' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_FORCE_LH <= 0
# error "The preprocessor definition 'REC_FORCE_LH' must be greater than '0'"
#endif

/* Constraint 'REC_DBW_OFF < 2147483647' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_DBW_OFF >= 2147483647
# error "The preprocessor definition 'REC_DBW_OFF' must be less than '2147483647'"
#endif

/* Constraint 'REC_ENG_OFF < 2147483647' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_ENG_OFF >= 2147483647
# error "The preprocessor definition 'REC_ENG_OFF' must be less than '2147483647'"
#endif

/* Constraint 'REC_NO_DBW_SELF < 2147483647' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_NO_DBW_SELF >= 2147483647
# error "The preprocessor definition 'REC_NO_DBW_SELF' must be less than '2147483647'"
#endif

/* Constraint 'REC_ALFA_N < 2147483647' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_ALFA_N >= 2147483647
# error "The preprocessor definition 'REC_ALFA_N' must be less than '2147483647'"
#endif

/* Constraint 'REC_FORCE_LH < 2147483647' registered by:
 * '<S9>/ThrottleAdapt_Sched'
 */
#if REC_FORCE_LH >= 2147483647
# error "The preprocessor definition 'REC_FORCE_LH' must be less than '2147483647'"
#endif
#endif
#endif                                 /* RTW_HEADER_ThrottleAdapt_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
