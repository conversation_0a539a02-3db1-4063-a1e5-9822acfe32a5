/** ###################################################################
**     Filename  : INTC.cfg
**     Project   :
**     Processor : MPC5554
**     Version   : 
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 28/02/2005
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ###################################################################*/

/* ------------------------------------------------------------------ */
/*     Common errors defines                                            */
/* ------------------------------------------------------------------ */

#define TASK_NOT_ENABLED_DUE_FILLED_LIST  -2
#define SOFTWARE_INTERRUPT_REQUEST_ERROR -3

/* ------------------------------------------------------------------ */
/* Interrupts Priority symbols                                        */
/* ------------------------------------------------------------------ */
#define PRI_0    0x0 /* --> lower priority */
#define PRI_1    0x1
#define PRI_2    0x2
#define PRI_3    0x3
#define PRI_4    0x4
#define PRI_5    0x5
#define PRI_6    0x6
#define PRI_7    0x7
#define PRI_8    0x8 /* --> medium priority */
#define PRI_9    0x9
#define PRI_10   0xA
#define PRI_11   0xB
#define PRI_12   0xC
#define PRI_13   0xD
#define PRI_14   0xE
#define PRI_15   0xF /* --> high priority       */


/* ------------------------------------------------------------------ */
/* Interrupts request symbols                                         */
/* ------------------------------------------------------------------ */
#ifndef _OSEK_

#ifdef _BUILD_TASK_
#define INTC_SSCIR0_ISR                 RoutineISR0     /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_ISR                 RoutineISR1     /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_ISR                 RoutineISR2     /* Interrupt no. 2  - ivINT_SSCIR2_CLR2  */
#define INTC_SSCIR3_ISR                 RoutineISR3     /* Interrupt no. 3  - ivINT_SSCIR3_CLR3  */
#define INTC_SSCIR4_ISR                 RoutineISR4     /* Interrupt no. 4  - ivINT_SSCIR4_CLR4  */
#define INTC_SSCIR5_ISR                 RoutineISR5     /* Interrupt no. 5  - ivINT_SSCIR5_CLR5  */
#define INTC_SSCIR6_ISR                 RoutineISR6     /* Interrupt no. 6  - ivINT_SSCIR6_CLR6  */
#define INTC_SSCIR7_ISR                 RoutineISR7     /* Interrupt no. 7  - ivINT_SSCIR7_CLR7  */
#else
#define INTC_SSCIR0_ISR                 DUMMY_FUNC      /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_ISR                 DUMMY_FUNC      /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_ISR                 DUMMY_FUNC      /* Interrupt no. 2  - ivINT_SSCIR2_CLR2  */
#define INTC_SSCIR3_ISR                 DUMMY_FUNC      /* Interrupt no. 3  - ivINT_SSCIR3_CLR3  */
#define INTC_SSCIR4_ISR                 DUMMY_FUNC      /* Interrupt no. 4  - ivINT_SSCIR4_CLR4  */
#define INTC_SSCIR5_ISR                 DUMMY_FUNC      /* Interrupt no. 5  - ivINT_SSCIR5_CLR5  */
#define INTC_SSCIR6_ISR                 DUMMY_FUNC      /* Interrupt no. 6  - ivINT_SSCIR6_CLR6  */
#define INTC_SSCIR7_ISR                 DUMMY_FUNC      /* Interrupt no. 7  - ivINT_SSCIR7_CLR7  */
#endif /* _BUILD_TASK_ */

#else /* _OSEK_ */

#define INTC_SSCIR0_ISR                 Schedule        /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_ISR                 Schedule        /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_ISR                 Schedule        /* Interrupt no. 2  - ivINT_SSCIR2_CLR2  */
#define INTC_SSCIR3_ISR                 Schedule        /* Interrupt no. 3  - ivINT_SSCIR3_CLR3  */
#define INTC_SSCIR4_ISR                 Schedule        /* Interrupt no. 4  - ivINT_SSCIR4_CLR4  */
#define INTC_SSCIR5_ISR                 Schedule        /* Interrupt no. 5  - ivINT_SSCIR5_CLR5  */
#define INTC_SSCIR6_ISR                 Schedule        /* Interrupt no. 6  - ivINT_SSCIR6_CLR6  */
#define INTC_SSCIR7_ISR                 Schedule        /* Interrupt no. 7  - ivINT_SSCIR7_CLR7  */

#endif /* _OSEK_ */
#define ECSM_SWTIR_SWTIC_ISR              DUMMY_FUNC      /* Interrupt no. 8  - ivECSM_SWTIR_SWTIC  */
#define ECSM_ESR_ERROR_ISR              DUMMY_FUNC      /* Interrupt no. 9  - ivECSM_ESR_ERROR  */
#define EDMA_ERL_ERR31_ERR0_ISR         DUMMY_FUNC      /* Interrupt no. 10 - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_ISR            DUMMY_FUNC      /* Interrupt no. 11 - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_ISR            DUMMY_FUNC      /* Interrupt no. 12 - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_ISR            DUMMY_FUNC      /* Interrupt no. 13 - ivINT_EDMA_IRQRL_INT2  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT03_ISR            EndOfAngAcq     /* Interrupt no. 14 - ivINT_EDMA_IRQRL_INT3  */
#else
#define EDMA_IRQRL_INT03_ISR            DUMMY_FUNC      /* Interrupt no. 14 - ivINT_EDMA_IRQRL_INT3  */
#endif
#define EDMA_IRQRL_INT04_ISR            DUMMY_FUNC      /* Interrupt no. 15 - ivINT_EDMA_IRQRL_INT4  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT05_ISR            EndOfAcquisition_450u_Isr  /* Interrupt no. 16 - ivINT_EDMA_IRQRL_INT5  */
#else
#define EDMA_IRQRL_INT05_ISR            DUMMY_FUNC      /* Interrupt no. 16 - ivINT_EDMA_IRQRL_INT5  */
#endif
#define EDMA_IRQRL_INT06_ISR            DUMMY_FUNC      /* Interrupt no. 17 - ivINT_EDMA_IRQRL_INT6  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT07_ISR            EndOfAcquisition_2m_Isr    /* Interrupt no. 18 - ivINT_EDMA_IRQRL_INT7  */
#else
#define EDMA_IRQRL_INT07_ISR            DUMMY_FUNC      /* Interrupt no. 18 - ivINT_EDMA_IRQRL_INT7  */
#endif
#define EDMA_IRQRL_INT08_ISR            DUMMY_FUNC      /* Interrupt no. 19 - ivINT_EDMA_IRQRL_INT8  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT09_ISR            EndOfAcquisition_10m_Isr   /* Interrupt no. 20 - ivINT_EDMA_IRQRL_INT9  */
#else
#define EDMA_IRQRL_INT09_ISR            DUMMY_FUNC      /* Interrupt no. 20 - ivINT_EDMA_IRQRL_INT9  */
#endif

#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT10_ISR            EndOfAcq_A      /* Interrupt no. 21 - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_ISR            DUMMY_FUNC /* EndOfAcq_B */  /* Interrupt no. 22 - ivINT_EDMA_IRQRL_INT11  */
#else
#define EDMA_IRQRL_INT10_ISR            DUMMY_FUNC      /* Interrupt no. 21 - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_ISR            DUMMY_FUNC      /* Interrupt no. 22 - ivINT_EDMA_IRQRL_INT11  */
#endif /* _BUILD_ADC_ */

#define EDMA_IRQRL_INT12_ISR            DUMMY_FUNC      /* Interrupt no. 23 - ivINT_EDMA_IRQRL_INT12  */
#ifdef _BUILD_SPI_
#if (SPI_CH_B_EN)
#define EDMA_IRQRL_INT13_ISR            SPI_B_EOQ_ISR      /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
#else
#define EDMA_IRQRL_INT13_ISR            DUMMY_FUNC      /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
#endif 
#else
#define EDMA_IRQRL_INT13_ISR            DUMMY_FUNC      /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
#endif
#define EDMA_IRQRL_INT14_ISR            DUMMY_FUNC      /* Interrupt no. 25 - ivINT_EDMA_IRQRL_INT14  */
#ifdef _BUILD_SPI_
#if (SPI_CH_C_EN)
#define EDMA_IRQRL_INT15_ISR            SPI_C_EOQ_ISR      /* Interrupt no. 26 - ivINT_EDMA_IRQRL_INT15  */
#else
#define EDMA_IRQRL_INT15_ISR            DUMMY_FUNC      /* Interrupt no. 26 - ivINT_EDMA_IRQRL_INT15  */
#endif
#else
#define EDMA_IRQRL_INT15_ISR            DUMMY_FUNC      /* Interrupt no. 26 - ivINT_EDMA_IRQRL_INT15  */
#endif

#define EDMA_IRQRL_INT16_ISR            DUMMY_FUNC      /* Interrupt no. 27 - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_ISR            DUMMY_FUNC      /* Interrupt no. 28 - ivINT_EDMA_IRQRL_INT17  */

#ifdef _BUILD_LINMGM_
#define EDMA_IRQRL_INT18_ISR            l_ifc_tx_master /* Interrupt no. 29 - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_ISR            l_ifc_rx_master /* Interrupt no. 30 - ivINT_EDMA_IRQRL_INT19  */
#else
#define EDMA_IRQRL_INT18_ISR            DUMMY_FUNC	    /* Interrupt no. 29 - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_ISR            DUMMY_FUNC	    /* Interrupt no. 30 - ivINT_EDMA_IRQRL_INT19  */
#endif

#define EDMA_IRQRL_INT20_ISR            DUMMY_FUNC      /* Interrupt no. 31 - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_ISR            DUMMY_FUNC      /* Interrupt no. 32 - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_ISR            DUMMY_FUNC      /* Interrupt no. 33 - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_ISR            DUMMY_FUNC      /* Interrupt no. 34 - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_ISR            DUMMY_FUNC      /* Interrupt no. 35 - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_ISR            DUMMY_FUNC      /* Interrupt no. 36 - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_ISR            DUMMY_FUNC      /* Interrupt no. 37 - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_ISR            DUMMY_FUNC      /* Interrupt no. 38 - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_ISR            DUMMY_FUNC      /* Interrupt no. 39 - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_ISR            DUMMY_FUNC      /* Interrupt no. 40 - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_ISR            DUMMY_FUNC      /* Interrupt no. 41 - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_ISR            DUMMY_FUNC      /* Interrupt no. 42 - ivINT_EDMA_IRQRL_INT31  */
#define FMPLL_SYNSR_LOCF_ISR            DUMMY_FUNC      /* Interrupt no. 43 - ivFMPLL_SYNSR_LOCF  */
#define FMPLL_SYNSR_LOLF_ISR            DUMMY_FUNC      /* Interrupt no. 44 - ivFMPLL_SYNSR_LOLF  */
#define SIU_OSR_OVF15_OVF0_ISR          DUMMY_FUNC      /* Interrupt no. 45 - ivINT_SIU_OSR_OVF15_0  */

#ifdef _BUILD_EXTIRQ_
#define SIU_EISR_EIF0_ISR               EXTIRQ00_Isr      /* Interrupt no. 46 - ivINT_SIU_EISR_EIF0  */
#define SIU_EISR_EIF1_ISR               EXTIRQ01_Isr      /* Interrupt no. 47 - ivINT_SIU_EISR_EIF1  */
#define SIU_EISR_EIF2_ISR               EXTIRQ02_Isr        /* Interrupt no. 48 - ivINT_SIU_EISR_EIF2  */
#define SIU_EISR_EIF3_ISR               EXTIRQ03_Isr        /* Interrupt no. 49 - ivINT_SIU_EISR_EIF3  */
#define SIU_EISR_EIF15_EIF4_ISR         EXTIRQ04_15_Isr   /* Interrupt no. 50 - ivINT_SIU_EISR_EIF15_4  */
#else
#define SIU_EISR_EIF0_ISR               DUMMY_FUNC      /* Interrupt no. 46 - ivINT_SIU_EISR_EIF0  */
#define SIU_EISR_EIF1_ISR               DUMMY_FUNC      /* Interrupt no. 47 - ivINT_SIU_EISR_EIF1  */
#define SIU_EISR_EIF2_ISR               DUMMY_FUNC      /* Interrupt no. 48 - ivINT_SIU_EISR_EIF2  */
#define SIU_EISR_EIF3_ISR               DUMMY_FUNC      /* Interrupt no. 49 - ivINT_SIU_EISR_EIF3  */
#define SIU_EISR_EIF15_EIF4_ISR         DUMMY_FUNC      /* Interrupt no. 50 - ivINT_SIU_EISR_EIF15_4  */
#endif



#define EMIOS_GFR_F0_ISR                IsrFunc_UC0     /* Interrupt no. 51 - ivINT_EMIOS_GFR_F0  */
#define EMIOS_GFR_F1_ISR                IsrFunc_UC1     /* Interrupt no. 52 - ivINT_EMIOS_GFR_F1  */
#if defined(_BUILD_TIMING_) && defined(_BUILD_EMIOS_) 
#define EMIOS_GFR_F2_ISR                IsrFunc_UC2     /* Interrupt no. 53 - ivINT_EMIOS_GFR_F2  */
#else
#define EMIOS_GFR_F2_ISR                DUMMY_FUNC     /* Interrupt no. 53 - ivINT_EMIOS_GFR_F2  */
#endif
#define EMIOS_GFR_F3_ISR                IsrFunc_UC3     /* Interrupt no. 54 - ivINT_EMIOS_GFR_F3  */
#if defined(_BUILD_TIMING_) && defined(_BUILD_EMIOS_) 
#define EMIOS_GFR_F4_ISR                IsrFunc_UC4     /* Interrupt no. 55 - ivINT_EMIOS_GFR_F4  */
#define EMIOS_GFR_F5_ISR                IsrFunc_UC5     /* Interrupt no. 56 - ivINT_EMIOS_GFR_F5  */
#define EMIOS_GFR_F6_ISR                IsrFunc_UC6     /* Interrupt no. 57 - ivINT_EMIOS_GFR_F6  */
#else
#define EMIOS_GFR_F4_ISR                DUMMY_FUNC     /* Interrupt no. 55 - ivINT_EMIOS_GFR_F4  */
#define EMIOS_GFR_F5_ISR                DUMMY_FUNC     /* Interrupt no. 56 - ivINT_EMIOS_GFR_F5  */
#define EMIOS_GFR_F6_ISR                DUMMY_FUNC     /* Interrupt no. 57 - ivINT_EMIOS_GFR_F6  */
#endif
#define EMIOS_GFR_F7_ISR                IsrFunc_UC7     /* Interrupt no. 58 - ivINT_EMIOS_GFR_F7  */
#define EMIOS_GFR_F8_ISR                IsrFunc_UC8     /* Interrupt no. 59 - ivINT_EMIOS_GFR_F8  */
#define EMIOS_GFR_F9_ISR                IsrFunc_UC9     /* Interrupt no. 60 - ivINT_EMIOS_GFR_F9  */
#define EMIOS_GFR_F10_ISR               IsrFunc_UC10        /* Interrupt no. 61 - ivINT_EMIOS_GFR_F10  */
#define EMIOS_GFR_F11_ISR               IsrFunc_UC11        /* Interrupt no. 62 - ivINT_EMIOS_GFR_F11  */
#define EMIOS_GFR_F12_ISR               IsrFunc_UC12        /* Interrupt no. 63 - ivINT_EMIOS_GFR_F12  */
#define EMIOS_GFR_F13_ISR               IsrFunc_UC13        /* Interrupt no. 64 - ivINT_EMIOS_GFR_F13  */
#define EMIOS_GFR_F14_ISR               IsrFunc_UC14        /* Interrupt no. 65 - ivINT_EMIOS_GFR_F14  */
#define EMIOS_GFR_F15_ISR               IsrFunc_UC15        /* Interrupt no. 66 - ivINT_EMIOS_GFR_F15  */
#define ETPU_MCR_MGE_ILF_SCMMISF_ISR    DUMMY_FUNC      /* Interrupt no. 67 - ivINT_ETPU_MCR_MGE_ILF_SCMMISF  */



//#if((BOARD_TYPE == BOARD_4)||(BOARD_TYPE == BOARD_5))

//#else

#ifdef _BUILD_SYNC_
    #define ETPU_CISR_A_CIS0_ISR          _ETPU_A_Channel0Interrupt_Handler     /* Interrupt no. 68 - ivINT_ETPU_CISR_A_CIS0  */
    #define ETPU_CISR_A_CIS1_ISR          _ETPU_A_Channel1Interrupt_Handler    /* Interrupt no. 69 - ivINT_ETPU_CISR_A_CIS1  */
#else
    #define ETPU_CISR_A_CIS0_ISR          DUMMY_FUNC        /* Interrupt no. 68 - ivINT_ETPU_CISR_A_CIS0  */
    #define ETPU_CISR_A_CIS1_ISR          DUMMY_FUNC        /* Interrupt no. 69 - ivINT_ETPU_CISR_A_CIS1  */
#endif /*_BUILD_SYNC_*/

#ifdef _BUILD_SYS_
    #define ETPU_CISR_A_CIS2_ISR          _ETPU_A_Channel2Interrupt_Handler     /* Interrupt no. 70 - ivINT_ETPU_CISR_A_CIS2  */
    #define ETPU_CISR_A_CIS3_ISR          _ETPU_A_Channel3Interrupt_Handler     /* Interrupt no. 71 - ivINT_ETPU_CISR_A_CIS3  */
    #define ETPU_CISR_A_CIS4_ISR          _ETPU_A_Channel4Interrupt_Handler     /* Interrupt no. 72 - ivINT_ETPU_CISR_A_CIS4  */
    #define ETPU_CISR_A_CIS5_ISR          _ETPU_A_Channel5Interrupt_Handler     /* Interrupt no. 73 - ivINT_ETPU_CISR_A_CIS5  */
    #define ETPU_CISR_A_CIS6_ISR          _ETPU_A_Channel6Interrupt_Handler     /* Interrupt no. 74 - ivINT_ETPU_CISR_A_CIS6  */
    #define ETPU_CISR_A_CIS7_ISR          _ETPU_A_Channel7Interrupt_Handler     /* Interrupt no. 75 - ivINT_ETPU_CISR_A_CIS7  */
    #define ETPU_CISR_A_CIS8_ISR          _ETPU_A_Channel8Interrupt_Handler     /* Interrupt no. 76 - ivINT_ETPU_CISR_A_CIS8  */
    #define ETPU_CISR_A_CIS9_ISR          _ETPU_A_Channel9Interrupt_Handler     /* Interrupt no. 77 - ivINT_ETPU_CISR_A_CIS9  */
    #define ETPU_CISR_A_CIS10_ISR         _ETPU_A_Channel10Interrupt_Handler    /* Interrupt no. 78 - ivINT_ETPU_CISR_A_CIS10  */
    #define ETPU_CISR_A_CIS11_ISR         _ETPU_A_Channel11Interrupt_Handler    /* Interrupt no. 79 - ivINT_ETPU_CISR_A_CIS11  */
    #define ETPU_CISR_A_CIS12_ISR         _ETPU_A_Channel12Interrupt_Handler    /* Interrupt no. 80 - ivINT_ETPU_CISR_A_CIS12  */
    #define ETPU_CISR_A_CIS13_ISR         _ETPU_A_Channel13Interrupt_Handler    /* Interrupt no. 81 - ivINT_ETPU_CISR_A_CIS13  */
#else
    #define ETPU_CISR_A_CIS2_ISR            DUMMY_FUNC      /* Interrupt no. 70 - ivINT_ETPU_CISR_A_CIS2  */
    #define ETPU_CISR_A_CIS3_ISR            DUMMY_FUNC      /* Interrupt no. 71 - ivINT_ETPU_CISR_A_CIS3  */
    #define ETPU_CISR_A_CIS4_ISR            DUMMY_FUNC      /* Interrupt no. 72 - ivINT_ETPU_CISR_A_CIS4  */
    #define ETPU_CISR_A_CIS5_ISR            DUMMY_FUNC      /* Interrupt no. 73 - ivINT_ETPU_CISR_A_CIS5  */
    #define ETPU_CISR_A_CIS6_ISR            DUMMY_FUNC      /* Interrupt no. 74 - ivINT_ETPU_CISR_A_CIS6  */
    #define ETPU_CISR_A_CIS7_ISR            DUMMY_FUNC      /* Interrupt no. 75 - ivINT_ETPU_CISR_A_CIS7  */
    #define ETPU_CISR_A_CIS8_ISR            DUMMY_FUNC      /* Interrupt no. 76 - ivINT_ETPU_CISR_A_CIS8  */
    #define ETPU_CISR_A_CIS9_ISR            DUMMY_FUNC      /* Interrupt no. 77 - ivINT_ETPU_CISR_A_CIS9  */
    #define ETPU_CISR_A_CIS10_ISR           DUMMY_FUNC      /* Interrupt no. 78 - ivINT_ETPU_CISR_A_CIS10  */
    #define ETPU_CISR_A_CIS11_ISR           DUMMY_FUNC      /* Interrupt no. 79 - ivINT_ETPU_CISR_A_CIS11  */
    #define ETPU_CISR_A_CIS12_ISR           DUMMY_FUNC      /* Interrupt no. 80 - ivINT_ETPU_CISR_A_CIS12  */
    #define ETPU_CISR_A_CIS13_ISR           DUMMY_FUNC      /* Interrupt no. 81 - ivINT_ETPU_CISR_A_CIS13  */
#endif  /*_BUILD_SYS_*/

   #define ETPU_CISR_A_CIS14_ISR           _ETPU_A_Channel14Interrupt_Handler       /* Interrupt no. 82 - ivINT_ETPU_CISR_A_CIS14  */
   #define ETPU_CISR_A_CIS15_ISR           _ETPU_A_Channel15Interrupt_Handler       /* Interrupt no. 83 - ivINT_ETPU_CISR_A_CIS15  */
   #define ETPU_CISR_A_CIS16_ISR           _ETPU_A_Channel16Interrupt_Handler       /* Interrupt no. 84 - ivINT_ETPU_CISR_A_CIS16  */
   #define ETPU_CISR_A_CIS17_ISR           _ETPU_A_Channel17Interrupt_Handler       /* Interrupt no. 85 - ivINT_ETPU_CISR_A_CIS17  */
   #define ETPU_CISR_A_CIS18_ISR           _ETPU_A_Channel18Interrupt_Handler       /* Interrupt no. 86 - ivINT_ETPU_CISR_A_CIS18  */
   #define ETPU_CISR_A_CIS19_ISR           _ETPU_A_Channel19Interrupt_Handler       /* Interrupt no. 87 - ivINT_ETPU_CISR_A_CIS19  */
   #define ETPU_CISR_A_CIS20_ISR           _ETPU_A_Channel20Interrupt_Handler       /* Interrupt no. 88 - ivINT_ETPU_CISR_A_CIS20  */
   #define ETPU_CISR_A_CIS21_ISR           _ETPU_A_Channel21Interrupt_Handler       /* Interrupt no. 89 - ivINT_ETPU_CISR_A_CIS21  */
   #define ETPU_CISR_A_CIS22_ISR           _ETPU_A_Channel22Interrupt_Handler       /* Interrupt no. 90 - ivINT_ETPU_CISR_A_CIS22  */
   #define ETPU_CISR_A_CIS23_ISR           _ETPU_A_Channel23Interrupt_Handler       /* Interrupt no. 91 - ivINT_ETPU_CISR_A_CIS23  */
   #define ETPU_CISR_A_CIS24_ISR           _ETPU_A_Channel24Interrupt_Handler       /* Interrupt no. 92 - ivINT_ETPU_CISR_A_CIS24  */
   #define ETPU_CISR_A_CIS25_ISR           _ETPU_A_Channel25Interrupt_Handler       /* Interrupt no. 93 - ivINT_ETPU_CISR_A_CIS25  */
   #define ETPU_CISR_A_CIS26_ISR           _ETPU_A_Channel26Interrupt_Handler       /* Interrupt no. 94 - ivINT_ETPU_CISR_A_CIS26  */

   #define ETPU_CISR_A_CIS27_ISR           DUMMY_FUNC       /* Interrupt no. 95 - ivINT_ETPU_CISR_A_CIS27  */
   #define ETPU_CISR_A_CIS28_ISR           DUMMY_FUNC       /* Interrupt no. 96 - ivINT_ETPU_CISR_A_CIS28  */
   #define ETPU_CISR_A_CIS29_ISR           DUMMY_FUNC       /* Interrupt no. 97 - ivINT_ETPU_CISR_A_CIS29  */
   #define ETPU_CISR_A_CIS30_ISR           DUMMY_FUNC       /* Interrupt no. 98 - ivINT_ETPU_CISR_A_CIS30  */
   #define ETPU_CISR_A_CIS31_ISR           DUMMY_FUNC       /* Interrupt no. 99 - ivINT_ETPU_CISR_A_CIS31  */
//#endif /* end */


#ifdef _BUILD_ADC_
#define EQADC_FISR_TORF_RFOF_CFUF_ISR   ALLFIFOFault_Isr /* Interrupt no. 100 - ivINT_EQADC_FISR_TORF_RFOF_CFUF  */
#else
#define EQADC_FISR_TORF_RFOF_CFUF_ISR   DUMMY_FUNC      /* Interrupt no. 100 - ivINT_EQADC_FISR_TORF_RFOF_CFUF  */
#endif /* _BUILD_ADC_ */

#define EQADC_FISR0_NCF_ISR             DUMMY_FUNC      /* Interrupt no. 101 - ivINT_EQADC_FISR0_NCF  */
#define EQADC_FISR0_PF_ISR              DUMMY_FUNC      /* Interrupt no. 102 - ivINT_EQADC_FISR0_PF  */
#define EQADC_FISR0_EOQF_ISR            DUMMY_FUNC      /* Interrupt no. 103 - ivINT_EQADC_FISR0_EOQF  */
#define EQADC_FISR0_CFFF_ISR            DUMMY_FUNC      /* Interrupt no. 104 - ivINT_EQADC_FISR0_CFFF  */
#define EQADC_FISR0_RFDF_ISR            DUMMY_FUNC      /* Interrupt no. 105 - ivINT_EQADC_FISR0_RFDF  */
#define EQADC_FISR1_NCF_ISR             DUMMY_FUNC      /* Interrupt no. 106 - ivINT_EQADC_FISR1_NCF  */
#define EQADC_FISR1_PF_ISR              DUMMY_FUNC      /* Interrupt no. 107 - ivINT_EQADC_FISR1_PF  */
#define EQADC_FISR1_EOQF_ISR            DUMMY_FUNC      /* Interrupt no. 108 - ivINT_EQADC_FISR1_EOQF  */
#define EQADC_FISR1_CFFF_ISR            DUMMY_FUNC      /* Interrupt no. 109 - ivINT_EQADC_FISR1_CFFF  */
#define EQADC_FISR1_RFDF_ISR            DUMMY_FUNC      /* Interrupt no. 110 - ivINT_EQADC_FISR1_RFDF  */
#define EQADC_FISR2_NCF_ISR             DUMMY_FUNC      /* Interrupt no. 111 - ivINT_EQADC_FISR2_NCF  */
#define EQADC_FISR2_PF_ISR              DUMMY_FUNC      /* Interrupt no. 112 - ivINT_EQADC_FISR2_PF  */
#define EQADC_FISR2_EOQF_ISR            DUMMY_FUNC      /* Interrupt no. 113 - ivINT_EQADC_FISR2_EOQF  */
#define EQADC_FISR2_CFFF_ISR            DUMMY_FUNC      /* Interrupt no. 114 - ivINT_EQADC_FISR2_CFFF  */
#define EQADC_FISR2_RFDF_ISR            DUMMY_FUNC      /* Interrupt no. 115 - ivINT_EQADC_FISR2_RFDF  */
#define EQADC_FISR3_NCF_ISR             DUMMY_FUNC      /* Interrupt no. 116 - ivINT_EQADC_FISR3_NCF  */
#define EQADC_FISR3_PF_ISR              DUMMY_FUNC      /* Interrupt no. 117 - ivINT_EQADC_FISR3_PF  */
#define EQADC_FISR3_EOQF_ISR            DUMMY_FUNC      /* Interrupt no. 118 - ivINT_EQADC_FISR3_EOQF  */
#define EQADC_FISR3_CFFF_ISR            DUMMY_FUNC      /* Interrupt no. 119 - ivINT_EQADC_FISR3_CFFF  */
#define EQADC_FISR3_RFDF_ISR            DUMMY_FUNC      /* Interrupt no. 120 - ivINT_EQADC_FISR3_RFDF  */
#define EQADC_FISR4_NCF_ISR             DUMMY_FUNC      /* Interrupt no. 121 - ivINT_EQADC_FISR4_NCF  */
#define EQADC_FISR4_PF_ISR              DUMMY_FUNC      /* Interrupt no. 122 - ivINT_EQADC_FISR4_PF  */
#define EQADC_FISR4_EOQF_ISR            DUMMY_FUNC      /* Interrupt no. 123 - ivINT_EQADC_FISR4_EOQF  */
#define EQADC_FISR4_CFFF_ISR            DUMMY_FUNC      /* Interrupt no. 124 - ivINT_EQADC_FISR4_CFFF  */
#define EQADC_FISR4_RFDF_ISR            DUMMY_FUNC      /* Interrupt no. 125 - ivINT_EQADC_FISR4_RFDF  */
#define EQADC_FISR5_NCF_ISR             DUMMY_FUNC      /* Interrupt no. 126 - ivINT_EQADC_FISR5_NCF  */
#define EQADC_FISR5_PF_ISR              DUMMY_FUNC      /* Interrupt no. 127 - ivINT_EQADC_FISR5_PF  */
#define EQADC_FISR5_EOQF_ISR            DUMMY_FUNC      /* Interrupt no. 128 - ivINT_EQADC_FISR5_EOQF  */
#define EQADC_FISR5_CFFF_ISR            DUMMY_FUNC      /* Interrupt no. 129 - ivINT_EQADC_FISR5_CFFF  */
#define EQADC_FISR5_RFDF_ISR            DUMMY_FUNC      /* Interrupt no. 130 - ivINT_EQADC_FISR5_RFDF  */
#define DSPI_B_ISR_TFUF_RFOF_ISR        DUMMY_FUNC      /* Interrupt no. 131 - ivINT_DSPI_B_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#if (SPI_CH_B_EN)
#define DSPI_B_ISR_EOQF_ISR             DUMMY_FUNC   /* Interrupt no. 132 - ivINT_DSPI_B_ISR_EOQF  */
#else
#define DSPI_B_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 132 - ivINT_DSPI_B_ISR_EOQF  */
#endif  
#else
#define DSPI_B_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 132 - ivINT_DSPI_B_ISR_EOQF  */
#endif /*_BUILD_SPI_*/

#define DSPI_B_ISR_TFFF_ISR             DUMMY_FUNC      /* Interrupt no. 133 - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_B_ISR_TCF_ISR              DUMMY_FUNC      /* Interrupt no. 134 - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_B_ISR_RFDF_ISR             DUMMY_FUNC      /* Interrupt no. 135 - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_C_ISR_TFUF_RFOF_ISR        DUMMY_FUNC      /* Interrupt no. 136 - ivINT_DSPI_C_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#define DSPI_C_ISR_EOQF_ISR             DUMMY_FUNC     /* Interrupt no. 137 - ivINT_DSPI_C_ISR_EOQF  */
#else
#define DSPI_C_ISR_EOQF_ISR             DUMMY_FUNC        /* Interrupt no. 137 - ivINT_DSPI_C_ISR_EOQF  */
#endif  /*_BUILD_SPI_*/

#define DSPI_C_ISR_TFFF_ISR             DUMMY_FUNC      /* Interrupt no. 138 - ivINT_DSPI_C_ISR_TFFF  */
#define DSPI_C_ISR_TCF_ISR              DUMMY_FUNC      /* Interrupt no. 139 - ivINT_DSPI_C_ISR_TCF  */
#define DSPI_C_ISR_RFDF_ISR             DUMMY_FUNC      /* Interrupt no. 140 - ivINT_DSPI_C_ISR_RFDF  */
#define DSPI_D_ISR_TFUF_RFOF_ISR        DUMMY_FUNC      /* Interrupt no. 141 - ivINT_DSPI_D_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#if (TARGET_TYPE == MPC5633  || TARGET_TYPE == MPC5634)
#define DSPI_D_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
#else
#define DSPI_D_ISR_EOQF_ISR             SPI_D_EOQ_ISR     /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
#endif
#else
#define DSPI_D_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
#endif  /*_BUILD_SPI_*/

#define DSPI_D_ISR_TFFF_ISR             DUMMY_FUNC      /* Interrupt no. 143 - ivINT_DSPI_D_ISR_TFFF  */
#define DSPI_D_ISR_TCF_ISR              DUMMY_FUNC      /* Interrupt no. 144 - ivINT_DSPI_D_ISR_TCF  */
#define DSPI_D_ISR_RFDF_ISR             DUMMY_FUNC      /* Interrupt no. 145 - ivINT_DSPI_D_ISR_RFDF  */

#ifdef _BUILD_SCI_
#if (SCI_CH_A_ENABLED)
  #define ESCI_A_ISR                    SCI_A_INT_ISR   /* Interrupt no. 146 - ivINT_ESCI_A  */
#else
  #define ESCI_A_ISR                    DUMMY_FUNC      /* Interrupt no. 146 - ivINT_ESCI_A  */
#endif
#if(SCI_CH_B_ENABLED)
  #define ESCI_B_ISR                    SCI_B_INT_ISR   /* Interrupt no. 149 - ivINT_ESCI_B  */
#else
  #define ESCI_B_ISR                    DUMMY_FUNC      /* Interrupt no. 149 - ivINT_ESCI_B  */
#endif
#else
  #define ESCI_A_ISR                    DUMMY_FUNC      /* Interrupt no. 146 - ivINT_ESCI_A  */
  #define ESCI_B_ISR                    DUMMY_FUNC      /* Interrupt no. 149 - ivINT_ESCI_B  */
#endif

#ifdef _BUILD_CAN_
#define CAN_A_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 152 - ivINT_CAN_A_ESR_BOFF_INT  */
#define CAN_A_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 153 - ivINT_CAN_A_ESR_ERR_INT  */
#define CAN_A_IFRL_BUF0I_ISR            CAN_CHA_MB0     /* Interrupt no. 155 - ivINT_CAN_A_IFRL_BUF0  */
#define CAN_A_IFRL_BUF1I_ISR            CAN_CHA_MB1     /* Interrupt no. 156 - ivINT_CAN_A_IFRL_BUF1  */
#define CAN_A_IFRL_BUF2I_ISR            CAN_CHA_MB2     /* Interrupt no. 157 - ivINT_CAN_A_IFRL_BUF2  */
#define CAN_A_IFRL_BUF3I_ISR            CAN_CHA_MB3     /* Interrupt no. 158 - ivINT_CAN_A_IFRL_BUF3  */
#define CAN_A_IFRL_BUF4I_ISR            CAN_CHA_MB4     /* Interrupt no. 159 - ivINT_CAN_A_IFRL_BUF4  */
#define CAN_A_IFRL_BUF5I_ISR            CAN_CHA_MB5     /* Interrupt no. 160 - ivINT_CAN_A_IFRL_BUF5  */
#define CAN_A_IFRL_BUF6I_ISR            CAN_CHA_MB6     /* Interrupt no. 161 - ivINT_CAN_A_IFRL_BUF6  */
#define CAN_A_IFRL_BUF7I_ISR            CAN_CHA_MB7     /* Interrupt no. 162 - ivINT_CAN_A_IFRL_BUF7  */
#define CAN_A_IFRL_BUF8I_ISR            CAN_CHA_MB8     /* Interrupt no. 163 - ivINT_CAN_A_IFRL_BUF8  */
#define CAN_A_IFRL_BUF9I_ISR            CAN_CHA_MB9     /* Interrupt no. 164 - ivINT_CAN_A_IFRL_BUF9  */
#define CAN_A_IFRL_BUF10I_ISR           CAN_CHA_MB10    /* Interrupt no. 165 - ivINT_CAN_A_IFRL_BUF10  */
#define CAN_A_IFRL_BUF11I_ISR           CAN_CHA_MB11    /* Interrupt no. 166 - ivINT_CAN_A_IFRL_BUF11  */
#define CAN_A_IFRL_BUF12I_ISR           CAN_CHA_MB12    /* Interrupt no. 167 - ivINT_CAN_A_IFRL_BUF12  */
#define CAN_A_IFRL_BUF13I_ISR           CAN_CHA_MB13    /* Interrupt no. 168 - ivINT_CAN_A_IFRL_BUF13  */
#define CAN_A_IFRL_BUF14I_ISR           CAN_CHA_MB14    /* Interrupt no. 169 - ivINT_CAN_A_IFRL_BUF14  */
#define CAN_A_IFRL_BUF15I_ISR           CAN_CHA_MB15    /* Interrupt no. 170 - ivINT_CAN_A_IFRL_BUF15  */
#define CAN_A_IFRL_BUF31I_BUF16I_ISR    CAN_CHA_MB16_31 /* Interrupt no. 171 - ivINT_CAN_A_IFRL_BUF31_16  */
#define CAN_A_IFRH_BUF63I_BUF32I_ISR    CAN_CHA_MB32_63 /* Interrupt no. 172 - ivINT_CAN_A_IFRL_BUF63_32  */
#define CAN_C_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 173 - ivINT_CAN_C_ESR_BOFF_INT  */
#define CAN_C_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 174 - ivINT_CAN_C_ESR_ERR_INT  */
#define CAN_C_IFRL_BUF0I_ISR            CAN_CHC_MB0     /* Interrupt no. 176 - ivINT_CAN_C_IFRL_BUF0  */
#define CAN_C_IFRL_BUF1I_ISR            CAN_CHC_MB1     /* Interrupt no. 177 - ivINT_CAN_C_IFRL_BUF1  */
#define CAN_C_IFRL_BUF2I_ISR            CAN_CHC_MB2     /* Interrupt no. 178 - ivINT_CAN_C_IFRL_BUF2  */
#define CAN_C_IFRL_BUF3I_ISR            CAN_CHC_MB3     /* Interrupt no. 179 - ivINT_CAN_C_IFRL_BUF3  */
#define CAN_C_IFRL_BUF4I_ISR            CAN_CHC_MB4     /* Interrupt no. 180 - ivINT_CAN_C_IFRL_BUF4  */
#define CAN_C_IFRL_BUF5I_ISR            CAN_CHC_MB5     /* Interrupt no. 181 - ivINT_CAN_C_IFRL_BUF5  */
#define CAN_C_IFRL_BUF6I_ISR            CAN_CHC_MB6     /* Interrupt no. 182 - ivINT_CAN_C_IFRL_BUF6  */
#define CAN_C_IFRL_BUF7I_ISR            CAN_CHC_MB7     /* Interrupt no. 183 - ivINT_CAN_C_IFRL_BUF7  */
#define CAN_C_IFRL_BUF8I_ISR            CAN_CHC_MB8     /* Interrupt no. 184 - ivINT_CAN_C_IFRL_BUF8  */
#define CAN_C_IFRL_BUF9I_ISR            CAN_CHC_MB9     /* Interrupt no. 185 - ivINT_CAN_C_IFRL_BUF9  */
#define CAN_C_IFRL_BUF10I_ISR           CAN_CHC_MB10    /* Interrupt no. 186 - ivINT_CAN_C_IFRL_BUF10  */
#define CAN_C_IFRL_BUF11I_ISR           CAN_CHC_MB11    /* Interrupt no. 187 - ivINT_CAN_C_IFRL_BUF11  */
#define CAN_C_IFRL_BUF12I_ISR           CAN_CHC_MB12    /* Interrupt no. 188 - ivINT_CAN_C_IFRL_BUF12  */
#define CAN_C_IFRL_BUF13I_ISR           CAN_CHC_MB13    /* Interrupt no. 189 - ivINT_CAN_C_IFRL_BUF13  */
#define CAN_C_IFRL_BUF14I_ISR           CAN_CHC_MB14    /* Interrupt no. 190 - ivINT_CAN_C_IFRL_BUF14  */
#define CAN_C_IFRL_BUF15I_ISR           CAN_CHC_MB15    /* Interrupt no. 191 - ivINT_CAN_C_IFRL_BUF15  */
#define CAN_C_IFRL_BUF31I_BUF16I_ISR    CAN_CHC_MB16_31 /* Interrupt no. 192 - ivINT_CAN_C_IFRL_BUF31_16  */
#define CAN_C_IFRH_BUF63I_BUF32I_ISR    DUMMY_FUNC      /* Interrupt no. 193 - ivINT_CAN_C_IFRL_BUF63_32  */

#else
#define CAN_A_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 152 - ivINT_CAN_A_ESR_BOFF_INT  */
#define CAN_A_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 153 - ivINT_CAN_A_ESR_ERR_INT  */
#define CAN_A_IFRL_BUF0I_ISR            DUMMY_FUNC      /* Interrupt no. 155 - ivINT_CAN_A_IFRL_BUF0  */
#define CAN_A_IFRL_BUF1I_ISR            DUMMY_FUNC      /* Interrupt no. 156 - ivINT_CAN_A_IFRL_BUF1  */
#define CAN_A_IFRL_BUF2I_ISR            DUMMY_FUNC      /* Interrupt no. 157 - ivINT_CAN_A_IFRL_BUF2  */
#define CAN_A_IFRL_BUF3I_ISR            DUMMY_FUNC      /* Interrupt no. 158 - ivINT_CAN_A_IFRL_BUF3  */
#define CAN_A_IFRL_BUF4I_ISR            DUMMY_FUNC      /* Interrupt no. 159 - ivINT_CAN_A_IFRL_BUF4  */
#define CAN_A_IFRL_BUF5I_ISR            DUMMY_FUNC      /* Interrupt no. 160 - ivINT_CAN_A_IFRL_BUF5  */
#define CAN_A_IFRL_BUF6I_ISR            DUMMY_FUNC      /* Interrupt no. 161 - ivINT_CAN_A_IFRL_BUF6  */
#define CAN_A_IFRL_BUF7I_ISR            DUMMY_FUNC      /* Interrupt no. 162 - ivINT_CAN_A_IFRL_BUF7  */
#define CAN_A_IFRL_BUF8I_ISR            DUMMY_FUNC      /* Interrupt no. 163 - ivINT_CAN_A_IFRL_BUF8  */
#define CAN_A_IFRL_BUF9I_ISR            DUMMY_FUNC      /* Interrupt no. 164 - ivINT_CAN_A_IFRL_BUF9  */
#define CAN_A_IFRL_BUF10I_ISR           DUMMY_FUNC      /* Interrupt no. 165 - ivINT_CAN_A_IFRL_BUF10  */
#define CAN_A_IFRL_BUF11I_ISR           DUMMY_FUNC      /* Interrupt no. 166 - ivINT_CAN_A_IFRL_BUF11  */
#define CAN_A_IFRL_BUF12I_ISR           DUMMY_FUNC      /* Interrupt no. 167 - ivINT_CAN_A_IFRL_BUF12  */
#define CAN_A_IFRL_BUF13I_ISR           DUMMY_FUNC      /* Interrupt no. 168 - ivINT_CAN_A_IFRL_BUF13  */
#define CAN_A_IFRL_BUF14I_ISR           DUMMY_FUNC      /* Interrupt no. 169 - ivINT_CAN_A_IFRL_BUF14  */
#define CAN_A_IFRL_BUF15I_ISR           DUMMY_FUNC      /* Interrupt no. 170 - ivINT_CAN_A_IFRL_BUF15  */
#define CAN_A_IFRL_BUF31I_BUF16I_ISR    DUMMY_FUNC      /* Interrupt no. 171 - ivINT_CAN_A_IFRL_BUF31_16  */
#define CAN_A_IFRH_BUF63I_BUF32I_ISR    DUMMY_FUNC      /* Interrupt no. 172 - ivINT_CAN_A_IFRL_BUF63_32  */
#define CAN_C_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 173 - ivINT_CAN_C_ESR_BOFF_INT  */
#define CAN_C_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 174 - ivINT_CAN_C_ESR_ERR_INT  */
#define CAN_C_IFRL_BUF0I_ISR            DUMMY_FUNC      /* Interrupt no. 176 - ivINT_CAN_C_IFRL_BUF0  */
#define CAN_C_IFRL_BUF1I_ISR            DUMMY_FUNC      /* Interrupt no. 177 - ivINT_CAN_C_IFRL_BUF1  */
#define CAN_C_IFRL_BUF2I_ISR            DUMMY_FUNC      /* Interrupt no. 178 - ivINT_CAN_C_IFRL_BUF2  */
#define CAN_C_IFRL_BUF3I_ISR            DUMMY_FUNC      /* Interrupt no. 179 - ivINT_CAN_C_IFRL_BUF3  */
#define CAN_C_IFRL_BUF4I_ISR            DUMMY_FUNC      /* Interrupt no. 180 - ivINT_CAN_C_IFRL_BUF4  */
#define CAN_C_IFRL_BUF5I_ISR            DUMMY_FUNC      /* Interrupt no. 181 - ivINT_CAN_C_IFRL_BUF5  */
#define CAN_C_IFRL_BUF6I_ISR            DUMMY_FUNC      /* Interrupt no. 182 - ivINT_CAN_C_IFRL_BUF6  */
#define CAN_C_IFRL_BUF7I_ISR            DUMMY_FUNC      /* Interrupt no. 183 - ivINT_CAN_C_IFRL_BUF7  */
#define CAN_C_IFRL_BUF8I_ISR            DUMMY_FUNC      /* Interrupt no. 184 - ivINT_CAN_C_IFRL_BUF8  */
#define CAN_C_IFRL_BUF9I_ISR            DUMMY_FUNC      /* Interrupt no. 185 - ivINT_CAN_C_IFRL_BUF9  */
#define CAN_C_IFRL_BUF10I_ISR           DUMMY_FUNC      /* Interrupt no. 186 - ivINT_CAN_C_IFRL_BUF10  */
#define CAN_C_IFRL_BUF11I_ISR           DUMMY_FUNC      /* Interrupt no. 187 - ivINT_CAN_C_IFRL_BUF11  */
#define CAN_C_IFRL_BUF12I_ISR           DUMMY_FUNC      /* Interrupt no. 188 - ivINT_CAN_C_IFRL_BUF12  */
#define CAN_C_IFRL_BUF13I_ISR           DUMMY_FUNC      /* Interrupt no. 189 - ivINT_CAN_C_IFRL_BUF13  */
#define CAN_C_IFRL_BUF14I_ISR           DUMMY_FUNC      /* Interrupt no. 190 - ivINT_CAN_C_IFRL_BUF14  */
#define CAN_C_IFRL_BUF15I_ISR           DUMMY_FUNC      /* Interrupt no. 191 - ivINT_CAN_C_IFRL_BUF15  */
#define CAN_C_IFRL_BUF31I_BUF16I_ISR    DUMMY_FUNC      /* Interrupt no. 192 - ivINT_CAN_C_IFRL_BUF31_16  */
#define CAN_C_IFRH_BUF63I_BUF32I_ISR    DUMMY_FUNC      /* Interrupt no. 193 - ivINT_CAN_C_IFRL_BUF63_32  */
#endif  /*_BUILD_CAN_*/

#define EMIOS_GFR_F16_ISR               IsrFunc_UC16      /* Interrupt no. 202 - ivINT_EMIOS_GFR_F16  */
#define EMIOS_GFR_F17_ISR               IsrFunc_UC17      /* Interrupt no. 203 - ivINT_EMIOS_GFR_F17  */
#define EMIOS_GFR_F18_ISR               IsrFunc_UC18      /* Interrupt no. 204 - ivINT_EMIOS_GFR_F18  */
#define EMIOS_GFR_F19_ISR               IsrFunc_UC19      /* Interrupt no. 205 - ivINT_EMIOS_GFR_F19  */

#if defined(_BUILD_TIMING_) && defined(_BUILD_EMIOS_) 
#define EMIOS_GFR_F20_ISR               IsrFunc_UC20        /* Interrupt no. 206 - ivINT_EMIOS_GFR_F20  */
#define EMIOS_GFR_F21_ISR               IsrFunc_UC21        /* Interrupt no. 207 - ivINT_EMIOS_GFR_F21  */
#define EMIOS_GFR_F22_ISR               IsrFunc_UC22        /* Interrupt no. 208 - ivINT_EMIOS_GFR_F22  */
#else
#define EMIOS_GFR_F20_ISR               DUMMY_FUNC      /* Interrupt no. 206 - ivINT_EMIOS_GFR_F20  */
#define EMIOS_GFR_F21_ISR               DUMMY_FUNC      /* Interrupt no. 207 - ivINT_EMIOS_GFR_F21  */
#define EMIOS_GFR_F22_ISR               DUMMY_FUNC      /* Interrupt no. 208 - ivINT_EMIOS_GFR_F22  */
#endif /* _BUILD_TIMING_ */

#define EMIOS_GFR_F23_ISR               IsrFunc_UC23      /* Interrupt no. 209 - ivINT_EMIOS_GFR_F23  */
#define EDMA_ERRH_ERR63_ERR32_ISR       DUMMY_FUNC      /* Interrupt no. 210 - ivINT_EDMA_ERRH_ERR63_32  */
#define EDMA_IRQRH_INT32_ISR            DUMMY_FUNC      /* Interrupt no. 211 - ivINT_EDMA_IRQRH_INT32  */
#define EDMA_IRQRH_INT33_ISR            DUMMY_FUNC      /* Interrupt no. 212 - ivINT_EDMA_IRQRH_INT33  */
#define EDMA_IRQRH_INT34_ISR            DUMMY_FUNC      /* Interrupt no. 213 - ivINT_EDMA_IRQRH_INT34  */
#define EDMA_IRQRH_INT35_ISR            DUMMY_FUNC      /* Interrupt no. 214 - ivINT_EDMA_IRQRH_INT35  */
#define EDMA_IRQRH_INT36_ISR            DUMMY_FUNC      /* Interrupt no. 215 - ivINT_EDMA_IRQRH_INT36  */
#define EDMA_IRQRH_INT37_ISR            DUMMY_FUNC      /* Interrupt no. 216 - ivINT_EDMA_IRQRH_INT37  */
#define EDMA_IRQRH_INT38_ISR            DUMMY_FUNC      /* Interrupt no. 217 - ivINT_EDMA_IRQRH_INT38  */
#define EDMA_IRQRH_INT39_ISR            DUMMY_FUNC      /* Interrupt no. 218 - ivINT_EDMA_IRQRH_INT39  */
#define EDMA_IRQRH_INT40_ISR            DUMMY_FUNC      /* Interrupt no. 219 - ivINT_EDMA_IRQRH_INT40  */
#define EDMA_IRQRH_INT41_ISR            DUMMY_FUNC      /* Interrupt no. 220 - ivINT_EDMA_IRQRH_INT41  */
#define EDMA_IRQRH_INT42_ISR            DUMMY_FUNC      /* Interrupt no. 221 - ivINT_EDMA_IRQRH_INT42  */
#define EDMA_IRQRH_INT43_ISR            DUMMY_FUNC      /* Interrupt no. 222 - ivINT_EDMA_IRQRH_INT43  */
#define EDMA_IRQRH_INT44_ISR            DUMMY_FUNC      /* Interrupt no. 223 - ivINT_EDMA_IRQRH_INT44  */
#define EDMA_IRQRH_INT45_ISR            DUMMY_FUNC      /* Interrupt no. 224 - ivINT_EDMA_IRQRH_INT45  */
#define EDMA_IRQRH_INT46_ISR            DUMMY_FUNC      /* Interrupt no. 225 - ivINT_EDMA_IRQRH_INT46  */
#define EDMA_IRQRH_INT47_ISR            DUMMY_FUNC      /* Interrupt no. 226 - ivINT_EDMA_IRQRH_INT47  */
#define EDMA_IRQRH_INT48_ISR            DUMMY_FUNC      /* Interrupt no. 227 - ivINT_EDMA_IRQRH_INT48  */
#define EDMA_IRQRH_INT49_ISR            DUMMY_FUNC      /* Interrupt no. 228 - ivINT_EDMA_IRQRH_INT49  */
#define EDMA_IRQRH_INT50_ISR            DUMMY_FUNC      /* Interrupt no. 229 - ivINT_EDMA_IRQRH_INT50  */
#define EDMA_IRQRH_INT51_ISR            DUMMY_FUNC      /* Interrupt no. 230 - ivINT_EDMA_IRQRH_INT51  */
#define EDMA_IRQRH_INT52_ISR            DUMMY_FUNC      /* Interrupt no. 231 - ivINT_EDMA_IRQRH_INT52  */
#define EDMA_IRQRH_INT53_ISR            DUMMY_FUNC      /* Interrupt no. 232 - ivINT_EDMA_IRQRH_INT53  */
#define EDMA_IRQRH_INT54_ISR            DUMMY_FUNC      /* Interrupt no. 233 - ivINT_EDMA_IRQRH_INT54  */
#define EDMA_IRQRH_INT55_ISR            DUMMY_FUNC      /* Interrupt no. 234 - ivINT_EDMA_IRQRH_INT55  */
#define EDMA_IRQRH_INT56_ISR            DUMMY_FUNC      /* Interrupt no. 235 - ivINT_EDMA_IRQRH_INT56  */
#define EDMA_IRQRH_INT57_ISR            DUMMY_FUNC      /* Interrupt no. 236 - ivINT_EDMA_IRQRH_INT57  */
#define EDMA_IRQRH_INT58_ISR            DUMMY_FUNC      /* Interrupt no. 237 - ivINT_EDMA_IRQRH_INT58  */
#define EDMA_IRQRH_INT59_ISR            DUMMY_FUNC      /* Interrupt no. 238 - ivINT_EDMA_IRQRH_INT59  */
#define EDMA_IRQRH_INT60_ISR            DUMMY_FUNC      /* Interrupt no. 239 - ivINT_EDMA_IRQRH_INT60  */
#define EDMA_IRQRH_INT61_ISR            DUMMY_FUNC      /* Interrupt no. 240 - ivINT_EDMA_IRQRH_INT61  */
#define EDMA_IRQRH_INT62_ISR            DUMMY_FUNC      /* Interrupt no. 241 - ivINT_EDMA_IRQRH_INT62  */
#define EDMA_IRQRH_INT63_ISR            DUMMY_FUNC      /* Interrupt no. 242 - ivINT_EDMA_IRQRH_INT63  */


#if (USE_ETPU_B == 1)
#define ETPU_CISR_B_CIS0_ISR            _ETPU_B_Channel0Interrupt_Handler       /* Interrupt no. 243 - ivINT_ETPU_CISR_B_CIS0  */
#define ETPU_CISR_B_CIS1_ISR            _ETPU_B_Channel1Interrupt_Handler       /* Interrupt no. 244 - ivINT_ETPU_CISR_B_CIS1  */
#define ETPU_CISR_B_CIS2_ISR            _ETPU_B_Channel2Interrupt_Handler       /* Interrupt no. 245 - ivINT_ETPU_CISR_B_CIS2  */
#define ETPU_CISR_B_CIS3_ISR            _ETPU_B_Channel3Interrupt_Handler       /* Interrupt no. 246 - ivINT_ETPU_CISR_B_CIS3  */
#define ETPU_CISR_B_CIS4_ISR            _ETPU_B_Channel4Interrupt_Handler       /* Interrupt no. 247 - ivINT_ETPU_CISR_B_CIS4  */
#define ETPU_CISR_B_CIS5_ISR            _ETPU_B_Channel5Interrupt_Handler       /* Interrupt no. 248 - ivINT_ETPU_CISR_B_CIS5  */
#define ETPU_CISR_B_CIS6_ISR            _ETPU_B_Channel6Interrupt_Handler       /* Interrupt no. 249 - ivINT_ETPU_CISR_B_CIS6  */
#define ETPU_CISR_B_CIS7_ISR            _ETPU_B_Channel7Interrupt_Handler       /* Interrupt no. 250 - ivINT_ETPU_CISR_B_CIS7  */
#define ETPU_CISR_B_CIS8_ISR            _ETPU_B_Channel8Interrupt_Handler       /* Interrupt no. 251 - ivINT_ETPU_CISR_B_CIS8  */
#define ETPU_CISR_B_CIS9_ISR            _ETPU_B_Channel9Interrupt_Handler       /* Interrupt no. 252 - ivINT_ETPU_CISR_B_CIS9  */
#define ETPU_CISR_B_CIS10_ISR           _ETPU_B_Channel10Interrupt_Handler      /* Interrupt no. 253 - ivINT_ETPU_CISR_B_CIS10  */
#define ETPU_CISR_B_CIS11_ISR           _ETPU_B_Channel11Interrupt_Handler      /* Interrupt no. 254 - ivINT_ETPU_CISR_B_CIS11  */
#define ETPU_CISR_B_CIS12_ISR           _ETPU_B_Channel12Interrupt_Handler      /* Interrupt no. 255 - ivINT_ETPU_CISR_B_CIS12  */
#define ETPU_CISR_B_CIS13_ISR           _ETPU_B_Channel13Interrupt_Handler      /* Interrupt no. 256 - ivINT_ETPU_CISR_B_CIS13  */
#define ETPU_CISR_B_CIS14_ISR           _ETPU_B_Channel14Interrupt_Handler      /* Interrupt no. 257 - ivINT_ETPU_CISR_B_CIS14  */
#define ETPU_CISR_B_CIS15_ISR           _ETPU_B_Channel15Interrupt_Handler      /* Interrupt no. 258 - ivINT_ETPU_CISR_B_CIS15  */
#define ETPU_CISR_B_CIS16_ISR           _ETPU_B_Channel16Interrupt_Handler      /* Interrupt no. 259 - ivINT_ETPU_CISR_B_CIS16  */
#define ETPU_CISR_B_CIS17_ISR           _ETPU_B_Channel17Interrupt_Handler      /* Interrupt no. 260 - ivINT_ETPU_CISR_B_CIS17  */
#define ETPU_CISR_B_CIS18_ISR           _ETPU_B_Channel18Interrupt_Handler      /* Interrupt no. 261 - ivINT_ETPU_CISR_B_CIS18  */
#define ETPU_CISR_B_CIS19_ISR           _ETPU_B_Channel19Interrupt_Handler      /* Interrupt no. 262 - ivINT_ETPU_CISR_B_CIS19  */
#define ETPU_CISR_B_CIS20_ISR           _ETPU_B_Channel20Interrupt_Handler      /* Interrupt no. 263 - ivINT_ETPU_CISR_B_CIS20  */
#define ETPU_CISR_B_CIS21_ISR           _ETPU_B_Channel21Interrupt_Handler      /* Interrupt no. 264 - ivINT_ETPU_CISR_B_CIS21  */
#define ETPU_CISR_B_CIS22_ISR           _ETPU_B_Channel22Interrupt_Handler      /* Interrupt no. 265 - ivINT_ETPU_CISR_B_CIS22  */
#define ETPU_CISR_B_CIS23_ISR           _ETPU_B_Channel23Interrupt_Handler      /* Interrupt no. 266 - ivINT_ETPU_CISR_B_CIS23  */
#define ETPU_CISR_B_CIS24_ISR           _ETPU_B_Channel24Interrupt_Handler      /* Interrupt no. 267 - ivINT_ETPU_CISR_B_CIS24  */
#define ETPU_CISR_B_CIS25_ISR           _ETPU_B_Channel25Interrupt_Handler      /* Interrupt no. 268 - ivINT_ETPU_CISR_B_CIS25  */
#define ETPU_CISR_B_CIS26_ISR           _ETPU_B_Channel26Interrupt_Handler      /* Interrupt no. 269 - ivINT_ETPU_CISR_B_CIS26  */
#define ETPU_CISR_B_CIS27_ISR           _ETPU_B_Channel27Interrupt_Handler      /* Interrupt no. 270 - ivINT_ETPU_CISR_B_CIS27  */
#define ETPU_CISR_B_CIS28_ISR           _ETPU_B_Channel28Interrupt_Handler      /* Interrupt no. 271 - ivINT_ETPU_CISR_B_CIS28  */
#define ETPU_CISR_B_CIS29_ISR           _ETPU_B_Channel29Interrupt_Handler      /* Interrupt no. 272 - ivINT_ETPU_CISR_B_CIS29  */
#define ETPU_CISR_B_CIS30_ISR           _ETPU_B_Channel30Interrupt_Handler      /* Interrupt no. 273 - ivINT_ETPU_CISR_B_CIS30  */
#define ETPU_CISR_B_CIS31_ISR           _ETPU_B_Channel31Interrupt_Handler      /* Interrupt no. 274 - ivINT_ETPU_CISR_B_CIS31  */
#else
#define ETPU_CISR_B_CIS0_ISR            DUMMY_FUNC      /* Interrupt no. 243 - ivINT_ETPU_CISR_B_CIS0  */
#define ETPU_CISR_B_CIS1_ISR            DUMMY_FUNC      /* Interrupt no. 244 - ivINT_ETPU_CISR_B_CIS1  */
#define ETPU_CISR_B_CIS2_ISR            DUMMY_FUNC      /* Interrupt no. 245 - ivINT_ETPU_CISR_B_CIS2  */
#define ETPU_CISR_B_CIS3_ISR            DUMMY_FUNC      /* Interrupt no. 246 - ivINT_ETPU_CISR_B_CIS3  */
#define ETPU_CISR_B_CIS4_ISR            DUMMY_FUNC      /* Interrupt no. 247 - ivINT_ETPU_CISR_B_CIS4  */
#define ETPU_CISR_B_CIS5_ISR            DUMMY_FUNC      /* Interrupt no. 248 - ivINT_ETPU_CISR_B_CIS5  */
#define ETPU_CISR_B_CIS6_ISR            DUMMY_FUNC      /* Interrupt no. 249 - ivINT_ETPU_CISR_B_CIS6  */
#define ETPU_CISR_B_CIS7_ISR            DUMMY_FUNC      /* Interrupt no. 250 - ivINT_ETPU_CISR_B_CIS7  */
#define ETPU_CISR_B_CIS8_ISR            DUMMY_FUNC      /* Interrupt no. 251 - ivINT_ETPU_CISR_B_CIS8  */
#define ETPU_CISR_B_CIS9_ISR            DUMMY_FUNC      /* Interrupt no. 252 - ivINT_ETPU_CISR_B_CIS9  */
#define ETPU_CISR_B_CIS10_ISR           DUMMY_FUNC      /* Interrupt no. 253 - ivINT_ETPU_CISR_B_CIS10  */
#define ETPU_CISR_B_CIS11_ISR           DUMMY_FUNC      /* Interrupt no. 254 - ivINT_ETPU_CISR_B_CIS11  */
#define ETPU_CISR_B_CIS12_ISR           DUMMY_FUNC      /* Interrupt no. 255 - ivINT_ETPU_CISR_B_CIS12  */
#define ETPU_CISR_B_CIS13_ISR           DUMMY_FUNC      /* Interrupt no. 256 - ivINT_ETPU_CISR_B_CIS13  */
#define ETPU_CISR_B_CIS14_ISR           DUMMY_FUNC      /* Interrupt no. 257 - ivINT_ETPU_CISR_B_CIS14  */
#define ETPU_CISR_B_CIS15_ISR           DUMMY_FUNC      /* Interrupt no. 258 - ivINT_ETPU_CISR_B_CIS15  */
#define ETPU_CISR_B_CIS16_ISR           DUMMY_FUNC      /* Interrupt no. 259 - ivINT_ETPU_CISR_B_CIS16  */
#define ETPU_CISR_B_CIS17_ISR           DUMMY_FUNC      /* Interrupt no. 260 - ivINT_ETPU_CISR_B_CIS17  */
#define ETPU_CISR_B_CIS18_ISR           DUMMY_FUNC      /* Interrupt no. 261 - ivINT_ETPU_CISR_B_CIS18  */
#define ETPU_CISR_B_CIS19_ISR           DUMMY_FUNC      /* Interrupt no. 262 - ivINT_ETPU_CISR_B_CIS19  */
#define ETPU_CISR_B_CIS20_ISR           DUMMY_FUNC      /* Interrupt no. 263 - ivINT_ETPU_CISR_B_CIS20  */
#define ETPU_CISR_B_CIS21_ISR           DUMMY_FUNC      /* Interrupt no. 264 - ivINT_ETPU_CISR_B_CIS21  */
#define ETPU_CISR_B_CIS22_ISR           DUMMY_FUNC      /* Interrupt no. 265 - ivINT_ETPU_CISR_B_CIS22  */
#define ETPU_CISR_B_CIS23_ISR           DUMMY_FUNC      /* Interrupt no. 266 - ivINT_ETPU_CISR_B_CIS23  */
#define ETPU_CISR_B_CIS24_ISR           DUMMY_FUNC      /* Interrupt no. 267 - ivINT_ETPU_CISR_B_CIS24  */
#define ETPU_CISR_B_CIS25_ISR           DUMMY_FUNC      /* Interrupt no. 268 - ivINT_ETPU_CISR_B_CIS25  */
#define ETPU_CISR_B_CIS26_ISR           DUMMY_FUNC      /* Interrupt no. 269 - ivINT_ETPU_CISR_B_CIS26  */
#define ETPU_CISR_B_CIS27_ISR           DUMMY_FUNC      /* Interrupt no. 270 - ivINT_ETPU_CISR_B_CIS27  */
#define ETPU_CISR_B_CIS28_ISR           DUMMY_FUNC      /* Interrupt no. 271 - ivINT_ETPU_CISR_B_CIS28  */
#define ETPU_CISR_B_CIS29_ISR           DUMMY_FUNC      /* Interrupt no. 272 - ivINT_ETPU_CISR_B_CIS29  */
#define ETPU_CISR_B_CIS30_ISR           DUMMY_FUNC      /* Interrupt no. 273 - ivINT_ETPU_CISR_B_CIS30  */
#define ETPU_CISR_B_CIS31_ISR           DUMMY_FUNC      /* Interrupt no. 274 - ivINT_ETPU_CISR_B_CIS31  */
#endif

#define DSPI_A_ISR_TFUF_RFOF_ISR        DUMMY_FUNC      /* Interrupt no. 275 - ivINT_DSPI_A_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#if (TARGET_TYPE == MPC5554)
#define DSPI_A_ISR_EOQF_ISR             SPI_A_EOQ_ISR     /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
#elif (TARGET_TYPE == MPC5534)
#define DSPI_A_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
#elif (TARGET_TYPE == MPC5633  || TARGET_TYPE == MPC5634)
#define DSPI_A_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
#endif
#else
#define DSPI_A_ISR_EOQF_ISR             DUMMY_FUNC      /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
#endif  /*_BUILD_SPI_*/

#define DSPI_A_ISR_TFFF_ISR             DUMMY_FUNC      /* Interrupt no. 277 - ivINT_DSPI_A_ISR_TFFF  */
#define DSPI_A_ISR_TCF_ISR              DUMMY_FUNC      /* Interrupt no. 278 - ivINT_DSPI_A_ISR_TCF  */
#define DSPI_A_ISR_RFDF_ISR             DUMMY_FUNC      /* Interrupt no. 279 - ivINT_DSPI_A_ISR_RFDF  */

#ifdef _BUILD_CAN_
#if (CAN_CHB_EN)
#define CAN_B_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 280 - ivINT_CAN_B_ESR_BOFF_INT  */
#define CAN_B_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 281 - ivINT_CAN_B_ESR_ERR_INT  */
#define CAN_B_IFRL_BUF0I_ISR            CAN_CHB_MB0     /* Interrupt no. 283 - ivINT_CAN_B_IFRL_BUF0  */
#define CAN_B_IFRL_BUF1I_ISR            CAN_CHB_MB1     /* Interrupt no. 284 - ivINT_CAN_B_IFRL_BUF1  */
#define CAN_B_IFRL_BUF2I_ISR            CAN_CHB_MB2     /* Interrupt no. 285 - ivINT_CAN_B_IFRL_BUF2  */
#define CAN_B_IFRL_BUF3I_ISR            CAN_CHB_MB3     /* Interrupt no. 286 - ivINT_CAN_B_IFRL_BUF3  */
#define CAN_B_IFRL_BUF4I_ISR            CAN_CHB_MB4     /* Interrupt no. 287 - ivINT_CAN_B_IFRL_BUF4  */
#define CAN_B_IFRL_BUF5I_ISR            CAN_CHB_MB5     /* Interrupt no. 288 - ivINT_CAN_B_IFRL_BUF5  */
#define CAN_B_IFRL_BUF6I_ISR            CAN_CHB_MB6     /* Interrupt no. 289 - ivINT_CAN_B_IFRL_BUF6  */
#define CAN_B_IFRL_BUF7I_ISR            CAN_CHB_MB7     /* Interrupt no. 290 - ivINT_CAN_B_IFRL_BUF7  */
#define CAN_B_IFRL_BUF8I_ISR            CAN_CHB_MB8     /* Interrupt no. 291 - ivINT_CAN_B_IFRL_BUF8  */
#define CAN_B_IFRL_BUF9I_ISR            CAN_CHB_MB9     /* Interrupt no. 292 - ivINT_CAN_B_IFRL_BUF9  */
#define CAN_B_IFRL_BUF10I_ISR           CAN_CHB_MB10        /* Interrupt no. 293 - ivINT_CAN_B_IFRL_BUF10  */
#define CAN_B_IFRL_BUF11I_ISR           CAN_CHB_MB11        /* Interrupt no. 294 - ivINT_CAN_B_IFRL_BUF11  */
#define CAN_B_IFRL_BUF12I_ISR           CAN_CHB_MB12        /* Interrupt no. 295 - ivINT_CAN_B_IFRL_BUF12  */
#define CAN_B_IFRL_BUF13I_ISR           CAN_CHB_MB13        /* Interrupt no. 296 - ivINT_CAN_B_IFRL_BUF13  */
#define CAN_B_IFRL_BUF14I_ISR           CAN_CHB_MB14        /* Interrupt no. 297 - ivINT_CAN_B_IFRL_BUF14  */
#define CAN_B_IFRL_BUF15I_ISR           CAN_CHB_MB15        /* Interrupt no. 298 - ivINT_CAN_B_IFRL_BUF15  */
#define CAN_B_IFRL_BUF31I_BUF16I_ISR    CAN_CHB_MB16_31     /* Interrupt no. 299 - ivINT_CAN_B_IFRL_BUF31_16  */
#define CAN_B_IFRH_BUF63I_BUF32I_ISR    DUMMY_FUNC      /* Interrupt no. 300 - ivINT_CAN_B_IFRL_BUF63_32  */
#else
#define CAN_B_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 280 - ivINT_CAN_B_ESR_BOFF_INT  */
#define CAN_B_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 281 - ivINT_CAN_B_ESR_ERR_INT  */
#define CAN_B_IFRL_BUF0I_ISR            DUMMY_FUNC      /* Interrupt no. 283 - ivINT_CAN_B_IFRL_BUF0  */
#define CAN_B_IFRL_BUF1I_ISR            DUMMY_FUNC      /* Interrupt no. 284 - ivINT_CAN_B_IFRL_BUF1  */
#define CAN_B_IFRL_BUF2I_ISR            DUMMY_FUNC      /* Interrupt no. 285 - ivINT_CAN_B_IFRL_BUF2  */
#define CAN_B_IFRL_BUF3I_ISR            DUMMY_FUNC      /* Interrupt no. 286 - ivINT_CAN_B_IFRL_BUF3  */
#define CAN_B_IFRL_BUF4I_ISR            DUMMY_FUNC      /* Interrupt no. 287 - ivINT_CAN_B_IFRL_BUF4  */
#define CAN_B_IFRL_BUF5I_ISR            DUMMY_FUNC      /* Interrupt no. 288 - ivINT_CAN_B_IFRL_BUF5  */
#define CAN_B_IFRL_BUF6I_ISR            DUMMY_FUNC      /* Interrupt no. 289 - ivINT_CAN_B_IFRL_BUF6  */
#define CAN_B_IFRL_BUF7I_ISR            DUMMY_FUNC      /* Interrupt no. 290 - ivINT_CAN_B_IFRL_BUF7  */
#define CAN_B_IFRL_BUF8I_ISR            DUMMY_FUNC      /* Interrupt no. 291 - ivINT_CAN_B_IFRL_BUF8  */
#define CAN_B_IFRL_BUF9I_ISR            DUMMY_FUNC      /* Interrupt no. 292 - ivINT_CAN_B_IFRL_BUF9  */
#define CAN_B_IFRL_BUF10I_ISR           DUMMY_FUNC      /* Interrupt no. 293 - ivINT_CAN_B_IFRL_BUF10  */
#define CAN_B_IFRL_BUF11I_ISR           DUMMY_FUNC      /* Interrupt no. 294 - ivINT_CAN_B_IFRL_BUF11  */
#define CAN_B_IFRL_BUF12I_ISR           DUMMY_FUNC      /* Interrupt no. 295 - ivINT_CAN_B_IFRL_BUF12  */
#define CAN_B_IFRL_BUF13I_ISR           DUMMY_FUNC      /* Interrupt no. 296 - ivINT_CAN_B_IFRL_BUF13  */
#define CAN_B_IFRL_BUF14I_ISR           DUMMY_FUNC      /* Interrupt no. 297 - ivINT_CAN_B_IFRL_BUF14  */
#define CAN_B_IFRL_BUF15I_ISR           DUMMY_FUNC      /* Interrupt no. 298 - ivINT_CAN_B_IFRL_BUF15  */
#define CAN_B_IFRL_BUF31I_BUF16I_ISR    DUMMY_FUNC      /* Interrupt no. 299 - ivINT_CAN_B_IFRL_BUF31_16  */
#define CAN_B_IFRH_BUF63I_BUF32I_ISR    DUMMY_FUNC      /* Interrupt no. 300 - ivINT_CAN_B_IFRL_BUF63_32  */
#endif
#else
#define CAN_B_ESR_BOFF_INT_ISR          DUMMY_FUNC      /* Interrupt no. 280 - ivINT_CAN_B_ESR_BOFF_INT  */
#define CAN_B_ESR_ERR_INT_ISR           DUMMY_FUNC      /* Interrupt no. 281 - ivINT_CAN_B_ESR_ERR_INT  */
#define CAN_B_IFRL_BUF0I_ISR            DUMMY_FUNC      /* Interrupt no. 283 - ivINT_CAN_B_IFRL_BUF0  */
#define CAN_B_IFRL_BUF1I_ISR            DUMMY_FUNC      /* Interrupt no. 284 - ivINT_CAN_B_IFRL_BUF1  */
#define CAN_B_IFRL_BUF2I_ISR            DUMMY_FUNC      /* Interrupt no. 285 - ivINT_CAN_B_IFRL_BUF2  */
#define CAN_B_IFRL_BUF3I_ISR            DUMMY_FUNC      /* Interrupt no. 286 - ivINT_CAN_B_IFRL_BUF3  */
#define CAN_B_IFRL_BUF4I_ISR            DUMMY_FUNC      /* Interrupt no. 287 - ivINT_CAN_B_IFRL_BUF4  */
#define CAN_B_IFRL_BUF5I_ISR            DUMMY_FUNC      /* Interrupt no. 288 - ivINT_CAN_B_IFRL_BUF5  */
#define CAN_B_IFRL_BUF6I_ISR            DUMMY_FUNC      /* Interrupt no. 289 - ivINT_CAN_B_IFRL_BUF6  */
#define CAN_B_IFRL_BUF7I_ISR            DUMMY_FUNC      /* Interrupt no. 290 - ivINT_CAN_B_IFRL_BUF7  */
#define CAN_B_IFRL_BUF8I_ISR            DUMMY_FUNC      /* Interrupt no. 291 - ivINT_CAN_B_IFRL_BUF8  */
#define CAN_B_IFRL_BUF9I_ISR            DUMMY_FUNC      /* Interrupt no. 292 - ivINT_CAN_B_IFRL_BUF9  */
#define CAN_B_IFRL_BUF10I_ISR           DUMMY_FUNC      /* Interrupt no. 293 - ivINT_CAN_B_IFRL_BUF10  */
#define CAN_B_IFRL_BUF11I_ISR           DUMMY_FUNC      /* Interrupt no. 294 - ivINT_CAN_B_IFRL_BUF11  */
#define CAN_B_IFRL_BUF12I_ISR           DUMMY_FUNC      /* Interrupt no. 295 - ivINT_CAN_B_IFRL_BUF12  */
#define CAN_B_IFRL_BUF13I_ISR           DUMMY_FUNC      /* Interrupt no. 296 - ivINT_CAN_B_IFRL_BUF13  */
#define CAN_B_IFRL_BUF14I_ISR           DUMMY_FUNC      /* Interrupt no. 297 - ivINT_CAN_B_IFRL_BUF14  */
#define CAN_B_IFRL_BUF15I_ISR           DUMMY_FUNC      /* Interrupt no. 298 - ivINT_CAN_B_IFRL_BUF15  */
#define CAN_B_IFRL_BUF31I_BUF16I_ISR    DUMMY_FUNC      /* Interrupt no. 299 - ivINT_CAN_B_IFRL_BUF31_16  */
#define CAN_B_IFRH_BUF63I_BUF32I_ISR    DUMMY_FUNC      /* Interrupt no. 300 - ivINT_CAN_B_IFRL_BUF63_32  */
#endif  /*_BUILD_CAN_*/

#ifdef _BUILD_PIT_
#define PIT_INT_PIT0_ISR                PIT_ISR_Mngr_PIT0   /* Interrupt no. 301 - PIT0 */
#define PIT_INT_PIT1_ISR                PIT_ISR_Mngr_PIT1   /* Interrupt no. 302 - PIT1 */
#define PIT_INT_PIT2_ISR                PIT_ISR_Mngr_PIT2   /* Interrupt no. 303 - PIT2 */
#define PIT_INT_PIT3_ISR                PIT_ISR_Mngr_PIT3   /* Interrupt no. 304 - PIT3 */
#define PIT_INT_RTI_ISR                 PIT_ISR_Mngr_RTI    /* Interrupt no. 305 - RTI  */
#else
#define PIT_INT_PIT0_ISR                DUMMY_FUNC      /* Interrupt no. 301 - PIT0 */
#define PIT_INT_PIT1_ISR                DUMMY_FUNC      /* Interrupt no. 302 - PIT1 */
#define PIT_INT_PIT2_ISR                DUMMY_FUNC      /* Interrupt no. 303 - PIT2 */
#define PIT_INT_PIT3_ISR                DUMMY_FUNC      /* Interrupt no. 304 - PIT3 */
#define PIT_INT_RTI_ISR                 DUMMY_FUNC      /* Interrupt no. 305 - RTI  */
#endif /*_BUILD_PIT_*/


/* ------------------------------------------------------------------ */
/* Interrupts level priority definition                                         */
/* ------------------------------------------------------------------ */
#ifdef _BUILD_TASK_
#define INTC_SSCIR0_LVL                 PRI_1           /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_LVL                 PRI_2       /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_LVL                 PRI_3         /* Interrupt no. 2  - ivINT_SSCIR2_CLR2  */
#define INTC_SSCIR3_LVL                 PRI_4       /* Interrupt no. 3  - ivINT_SSCIR3_CLR3  */
#define INTC_SSCIR4_LVL                 PRI_5       /* Interrupt no. 4  - ivINT_SSCIR4_CLR4  */
#define INTC_SSCIR5_LVL                 PRI_6       /* Interrupt no. 5  - ivINT_SSCIR5_CLR5  */
#define INTC_SSCIR6_LVL                 PRI_7       /* Interrupt no. 6  - ivINT_SSCIR6_CLR6  */
#define INTC_SSCIR7_LVL                 PRI_8       /* Interrupt no. 7  - ivINT_SSCIR7_CLR7  */
#else
#define INTC_SSCIR0_LVL                 PRI_0           /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_LVL                 PRI_0       /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_LVL                 PRI_0         /* Interrupt no. 2  - ivINT_SSCIR2_CLR2  */
#define INTC_SSCIR3_LVL                 PRI_0       /* Interrupt no. 3  - ivINT_SSCIR3_CLR3  */
#define INTC_SSCIR4_LVL                 PRI_0       /* Interrupt no. 4  - ivINT_SSCIR4_CLR4  */
#define INTC_SSCIR5_LVL                 PRI_0       /* Interrupt no. 5  - ivINT_SSCIR5_CLR5  */
#define INTC_SSCIR6_LVL                 PRI_0       /* Interrupt no. 6  - ivINT_SSCIR6_CLR6  */
#define INTC_SSCIR7_LVL                 PRI_0       /* Interrupt no. 7  - ivINT_SSCIR7_CLR7  */
#endif /* _BUILD_TASK_ */

#define ECSM_SWTIR_SWTIC_LVL              PRI_0         /* Interrupt no. 8  - ivECSM_SWTIR_SWTIC  */
#define ECSM_ESR_ERROR_LVL              PRI_0       /* Interrupt no. 9  - ivECSM_ESR_ERROR  */
#define EDMA_ERL_ERR31_ERR0_LVL         PRI_0       /* Interrupt no. 10 - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_LVL            PRI_0       /* Interrupt no. 11 - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_LVL            PRI_0       /* Interrupt no. 12 - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_LVL            PRI_0       /* Interrupt no. 13 - ivINT_EDMA_IRQRL_INT2  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT03_LVL            PRI_11       /* Interrupt no. 14 - ivINT_EDMA_IRQRL_INT3  */
#else
#define EDMA_IRQRL_INT03_LVL            PRI_0       /* Interrupt no. 14 - ivINT_EDMA_IRQRL_INT3  */
#endif
#define EDMA_IRQRL_INT04_LVL            PRI_0       /* Interrupt no. 15 - ivINT_EDMA_IRQRL_INT4  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT05_LVL            PRI_15      /* Interrupt no. 16 - ivINT_EDMA_IRQRL_INT5  */
#else
#define EDMA_IRQRL_INT05_LVL            PRI_0       /* Interrupt no. 16 - ivINT_EDMA_IRQRL_INT5  */
#endif
#define EDMA_IRQRL_INT06_LVL            PRI_0       /* Interrupt no. 17 - ivINT_EDMA_IRQRL_INT6  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT07_LVL            PRI_15      /* Interrupt no. 18 - ivINT_EDMA_IRQRL_INT7  */
#else
#define EDMA_IRQRL_INT07_LVL            PRI_0       /* Interrupt no. 18 - ivINT_EDMA_IRQRL_INT7  */
#endif
#define EDMA_IRQRL_INT08_LVL            PRI_0       /* Interrupt no. 19 - ivINT_EDMA_IRQRL_INT8  */
#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT09_LVL            PRI_15      /* Interrupt no. 20 - ivINT_EDMA_IRQRL_INT9  */
#else
#define EDMA_IRQRL_INT09_LVL            PRI_0       /* Interrupt no. 20 - ivINT_EDMA_IRQRL_INT9  */
#endif

#ifdef _BUILD_ADC_
#define EDMA_IRQRL_INT10_LVL            PRI_13          /* Interrupt no. 21 - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_LVL            PRI_13          /* Interrupt no. 22 - ivINT_EDMA_IRQRL_INT11  */
#else
#define EDMA_IRQRL_INT10_LVL            PRI_0       /* Interrupt no. 21 - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_LVL            PRI_0       /* Interrupt no. 22 - ivINT_EDMA_IRQRL_INT11  */
#endif /* _BUILD_ADC_ */

#define EDMA_IRQRL_INT12_LVL            PRI_0       /* Interrupt no. 23 - ivINT_EDMA_IRQRL_INT12  */
#ifdef _BUILD_SPI_
#define EDMA_IRQRL_INT13_LVL            PRI_11       /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
#else
#define EDMA_IRQRL_INT13_LVL            PRI_0       /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
#endif
#define EDMA_IRQRL_INT14_LVL            PRI_0       /* Interrupt no. 25 - ivINT_EDMA_IRQRL_INT14  */
#ifdef _BUILD_SPI_
#define EDMA_IRQRL_INT15_LVL            PRI_11       /* Interrupt no. 26 - ivINT_EDMA_IRQRL_INT15  */
#else
#define EDMA_IRQRL_INT15_LVL            PRI_0       /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
#endif 
#define EDMA_IRQRL_INT16_LVL            PRI_0       /* Interrupt no. 27 - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_LVL            PRI_0       /* Interrupt no. 28 - ivINT_EDMA_IRQRL_INT17  */

#ifdef _BUILD_LINMGM_
#define EDMA_IRQRL_INT18_LVL            PRI_9       /* Interrupt no. 29 - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_LVL            PRI_9       /* Interrupt no. 30 - ivINT_EDMA_IRQRL_INT19  */
#else
#define EDMA_IRQRL_INT18_LVL            PRI_0       /* Interrupt no. 29 - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_LVL            PRI_0       /* Interrupt no. 30 - ivINT_EDMA_IRQRL_INT19  */
#endif

#define EDMA_IRQRL_INT20_LVL            PRI_0       /* Interrupt no. 31 - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_LVL            PRI_0       /* Interrupt no. 32 - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_LVL            PRI_0       /* Interrupt no. 33 - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_LVL            PRI_0       /* Interrupt no. 34 - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_LVL              PRI_0         /* Interrupt no. 35 - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_LVL              PRI_0         /* Interrupt no. 36 - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_LVL              PRI_0         /* Interrupt no. 37 - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_LVL              PRI_0         /* Interrupt no. 38 - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_LVL              PRI_0         /* Interrupt no. 39 - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_LVL              PRI_0         /* Interrupt no. 40 - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_LVL              PRI_0         /* Interrupt no. 41 - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_LVL              PRI_0         /* Interrupt no. 42 - ivINT_EDMA_IRQRL_INT31  */
#define FMPLL_SYNSR_LOCF_LVL              PRI_0         /* Interrupt no. 43 - ivFMPLL_SYNSR_LOCF  */
#define FMPLL_SYNSR_LOLF_LVL              PRI_0         /* Interrupt no. 44 - ivFMPLL_SYNSR_LOLF  */
#define SIU_OSR_OVF15_OVF0_LVL          PRI_0       /* Interrupt no. 45 - ivINT_SIU_OSR_OVF15_0  */

#ifdef _BUILD_EXTIRQ_
#define SIU_EISR_EIF0_LVL               PRI_15      /* Interrupt no. 46 - ivINT_SIU_EISR_EIF0  */
#define SIU_EISR_EIF1_LVL               PRI_15      /* Interrupt no. 47 - ivINT_SIU_EISR_EIF1  */
#define SIU_EISR_EIF2_LVL               PRI_15      /* Interrupt no. 48 - ivINT_SIU_EISR_EIF2  */
#define SIU_EISR_EIF3_LVL               PRI_15      /* Interrupt no. 49 - ivINT_SIU_EISR_EIF3  */
#define SIU_EISR_EIF15_EIF4_LVL         PRI_15      /* Interrupt no. 50 - ivINT_SIU_EISR_EIF15_4  */
#else
#define SIU_EISR_EIF0_LVL               PRI_0       /* Interrupt no. 46 - ivINT_SIU_EISR_EIF0  */
#define SIU_EISR_EIF1_LVL               PRI_0       /* Interrupt no. 47 - ivINT_SIU_EISR_EIF1  */
#define SIU_EISR_EIF2_LVL               PRI_0           /* Interrupt no. 48 - ivINT_SIU_EISR_EIF2  */
#define SIU_EISR_EIF3_LVL               PRI_0           /* Interrupt no. 49 - ivINT_SIU_EISR_EIF3  */
#define SIU_EISR_EIF15_EIF4_LVL         PRI_0           /* Interrupt no. 50 - ivINT_SIU_EISR_EIF15_4  */
#endif

#define EMIOS_GFR_F0_LVL                PRI_12          /* Interrupt no. 51 - ivINT_EMIOS_GFR_F0  */
#define EMIOS_GFR_F1_LVL                PRI_12          /* Interrupt no. 52 - ivINT_EMIOS_GFR_F1  */
#define EMIOS_GFR_F2_LVL                PRI_12          /* Interrupt no. 53 - ivINT_EMIOS_GFR_F2  */
#define EMIOS_GFR_F3_LVL                PRI_12          /* Interrupt no. 54 - ivINT_EMIOS_GFR_F3  */
#define EMIOS_GFR_F4_LVL                PRI_12          /* Interrupt no. 55 - ivINT_EMIOS_GFR_F4  */
#define EMIOS_GFR_F5_LVL                PRI_12          /* Interrupt no. 56 - ivINT_EMIOS_GFR_F5  */
#define EMIOS_GFR_F6_LVL                PRI_12          /* Interrupt no. 57 - ivINT_EMIOS_GFR_F6  */
#define EMIOS_GFR_F7_LVL                PRI_12          /* Interrupt no. 58 - ivINT_EMIOS_GFR_F7  */
#define EMIOS_GFR_F8_LVL                PRI_12          /* Interrupt no. 59 - ivINT_EMIOS_GFR_F8  */
#define EMIOS_GFR_F9_LVL                PRI_12          /* Interrupt no. 60 - ivINT_EMIOS_GFR_F9  */
#define EMIOS_GFR_F10_LVL               PRI_12          /* Interrupt no. 61 - ivINT_EMIOS_GFR_F10  */
#define EMIOS_GFR_F11_LVL               PRI_12          /* Interrupt no. 62 - ivINT_EMIOS_GFR_F11  */
#define EMIOS_GFR_F12_LVL               PRI_12          /* Interrupt no. 63 - ivINT_EMIOS_GFR_F12  */
#define EMIOS_GFR_F13_LVL               PRI_12          /* Interrupt no. 64 - ivINT_EMIOS_GFR_F13  */
#define EMIOS_GFR_F14_LVL               PRI_12          /* Interrupt no. 65 - ivINT_EMIOS_GFR_F14  */
#define EMIOS_GFR_F15_LVL               PRI_12          /* Interrupt no. 66 - ivINT_EMIOS_GFR_F15  */
#define ETPU_MCR_MGE_ILF_SCMMISF_LVL    PRI_0           /* Interrupt no. 67 - ivINT_ETPU_MCR_MGE_ILF_SCMMISF  */


#if((BOARD_TYPE == BOARD_4)||(BOARD_TYPE == BOARD_5))

    #define ETPU_CISR_A_CIS0_LVL            PRI_14      /* Interrupt no. 68 - ivINT_ETPU_CISR_A_CIS0  */
    #define ETPU_CISR_A_CIS1_LVL            PRI_14      /* Interrupt no. 69 - ivINT_ETPU_CISR_A_CIS1  */
    #define ETPU_CISR_A_CIS2_LVL            PRI_12      /* Interrupt no. 70 - ivINT_ETPU_CISR_A_CIS2  */
    #define ETPU_CISR_A_CIS3_LVL            PRI_12      /* Interrupt no. 71 - ivINT_ETPU_CISR_A_CIS3  */
    #define ETPU_CISR_A_CIS4_LVL            PRI_12      /* Interrupt no. 72 - ivINT_ETPU_CISR_A_CIS4  */
    #define ETPU_CISR_A_CIS5_LVL            PRI_12      /* Interrupt no. 73 - ivINT_ETPU_CISR_A_CIS5  */
    #define ETPU_CISR_A_CIS6_LVL            PRI_0       /* Interrupt no. 74 - ivINT_ETPU_CISR_A_CIS6  */
    #define ETPU_CISR_A_CIS7_LVL            PRI_0       /* Interrupt no. 75 - ivINT_ETPU_CISR_A_CIS7  */
    #define ETPU_CISR_A_CIS8_LVL            PRI_12      /* Interrupt no. 76 - ivINT_ETPU_CISR_A_CIS8  */
    #define ETPU_CISR_A_CIS9_LVL            PRI_0       /* Interrupt no. 77 - ivINT_ETPU_CISR_A_CIS9  */
    #define ETPU_CISR_A_CIS10_LVL           PRI_12      /* Interrupt no. 78 - ivINT_ETPU_CISR_A_CIS10  */
    #define ETPU_CISR_A_CIS11_LVL           PRI_12      /* Interrupt no. 79 - ivINT_ETPU_CISR_A_CIS11  */
    #define ETPU_CISR_A_CIS12_LVL           PRI_0       /* Interrupt no. 80 - ivINT_ETPU_CISR_A_CIS12  */
    #define ETPU_CISR_A_CIS13_LVL           PRI_0       /* Interrupt no. 81 - ivINT_ETPU_CISR_A_CIS13  */
    #define ETPU_CISR_A_CIS14_LVL           PRI_0       /* Interrupt no. 82 - ivINT_ETPU_CISR_A_CIS14  */
    #define ETPU_CISR_A_CIS15_LVL           PRI_0       /* Interrupt no. 83 - ivINT_ETPU_CISR_A_CIS15  */
    #define ETPU_CISR_A_CIS16_LVL           PRI_0       /* Interrupt no. 84 - ivINT_ETPU_CISR_A_CIS16  */
    #define ETPU_CISR_A_CIS17_LVL           PRI_0       /* Interrupt no. 85 - ivINT_ETPU_CISR_A_CIS17  */
    #define ETPU_CISR_A_CIS18_LVL           PRI_12      /* Interrupt no. 86 - ivINT_ETPU_CISR_A_CIS18  */
    #define ETPU_CISR_A_CIS19_LVL           PRI_0       /* Interrupt no. 87 - ivINT_ETPU_CISR_A_CIS19  */
    #define ETPU_CISR_A_CIS20_LVL           PRI_0       /* Interrupt no. 88 - ivINT_ETPU_CISR_A_CIS20  */
    #define ETPU_CISR_A_CIS21_LVL           PRI_0       /* Interrupt no. 89 - ivINT_ETPU_CISR_A_CIS21  */
    #define ETPU_CISR_A_CIS22_LVL           PRI_0       /* Interrupt no. 90 - ivINT_ETPU_CISR_A_CIS22  */
    #define ETPU_CISR_A_CIS23_LVL           PRI_0       /* Interrupt no. 91 - ivINT_ETPU_CISR_A_CIS23  */
    #define ETPU_CISR_A_CIS24_LVL           PRI_0       /* Interrupt no. 92 - ivINT_ETPU_CISR_A_CIS24  */
    #define ETPU_CISR_A_CIS25_LVL           PRI_0       /* Interrupt no. 93 - ivINT_ETPU_CISR_A_CIS25  */
    #define ETPU_CISR_A_CIS26_LVL           PRI_14      /* Interrupt no. 94 - ivINT_ETPU_CISR_A_CIS26  */
    #define ETPU_CISR_A_CIS27_LVL           PRI_0       /* Interrupt no. 95 - ivINT_ETPU_CISR_A_CIS27  */
    #define ETPU_CISR_A_CIS28_LVL           PRI_0       /* Interrupt no. 96 - ivINT_ETPU_CISR_A_CIS28  */
    #define ETPU_CISR_A_CIS29_LVL           PRI_0       /* Interrupt no. 97 - ivINT_ETPU_CISR_A_CIS29  */
    #define ETPU_CISR_A_CIS30_LVL           PRI_0       /* Interrupt no. 98 - ivINT_ETPU_CISR_A_CIS30  */
    #define ETPU_CISR_A_CIS31_LVL           PRI_0       /* Interrupt no. 99 - ivINT_ETPU_CISR_A_CIS31  */



#else

#ifdef _BUILD_SYNC_
    #define ETPU_CISR_A_CIS0_LVL            PRI_14          /* Interrupt no. 68 - ivINT_ETPU_CISR_A_CIS0  */
    #define ETPU_CISR_A_CIS1_LVL            PRI_14          /* Interrupt no. 69 - ivINT_ETPU_CISR_A_CIS0  */
#else
    #define ETPU_CISR_A_CIS0_LVL            PRI_0           /* Interrupt no. 68 - ivINT_ETPU_CISR_A_CIS0  */
    #define ETPU_CISR_A_CIS1_LVL            PRI_0           /* Interrupt no. 69 - ivINT_ETPU_CISR_A_CIS1  */
#endif /*_BUILD_SYNC_*/

#ifdef _BUILD_SYS_
    #define ETPU_CISR_A_CIS2_LVL            PRI_12          /* Interrupt no. 70 - ivINT_ETPU_CISR_A_CIS2  */
    #define ETPU_CISR_A_CIS3_LVL            PRI_12          /* Interrupt no. 71 - ivINT_ETPU_CISR_A_CIS3  */
    #define ETPU_CISR_A_CIS4_LVL            PRI_12          /* Interrupt no. 72 - ivINT_ETPU_CISR_A_CIS4  */
    #define ETPU_CISR_A_CIS5_LVL            PRI_12          /* Interrupt no. 73 - ivINT_ETPU_CISR_A_CIS5  */
    #define ETPU_CISR_A_CIS6_LVL            PRI_12          /* Interrupt no. 74 - ivINT_ETPU_CISR_A_CIS6  */
    #define ETPU_CISR_A_CIS7_LVL            PRI_12          /* Interrupt no. 75 - ivINT_ETPU_CISR_A_CIS7  */
    #define ETPU_CISR_A_CIS8_LVL            PRI_12          /* Interrupt no. 76 - ivINT_ETPU_CISR_A_CIS8  */
    #define ETPU_CISR_A_CIS9_LVL            PRI_12          /* Interrupt no. 77 - ivINT_ETPU_CISR_A_CIS9  */
    #define ETPU_CISR_A_CIS10_LVL           PRI_12          /* Interrupt no. 78 - ivINT_ETPU_CISR_A_CIS10  */
    #define ETPU_CISR_A_CIS11_LVL           PRI_12          /* Interrupt no. 79 - ivINT_ETPU_CISR_A_CIS11  */
    #define ETPU_CISR_A_CIS12_LVL           PRI_12          /* Interrupt no. 80 - ivINT_ETPU_CISR_A_CIS12  */
    #define ETPU_CISR_A_CIS13_LVL           PRI_12          /* Interrupt no. 81 - ivINT_ETPU_CISR_A_CIS13  */
#else
    #define ETPU_CISR_A_CIS2_LVL            PRI_0           /* Interrupt no. 70 - ivINT_ETPU_CISR_A_CIS2  */
    #define ETPU_CISR_A_CIS3_LVL            PRI_0           /* Interrupt no. 71 - ivINT_ETPU_CISR_A_CIS3  */
    #define ETPU_CISR_A_CIS4_LVL            PRI_0           /* Interrupt no. 72 - ivINT_ETPU_CISR_A_CIS4  */
    #define ETPU_CISR_A_CIS5_LVL            PRI_0           /* Interrupt no. 73 - ivINT_ETPU_CISR_A_CIS5  */
    #define ETPU_CISR_A_CIS6_LVL            PRI_0           /* Interrupt no. 74 - ivINT_ETPU_CISR_A_CIS6  */
    #define ETPU_CISR_A_CIS7_LVL            PRI_0           /* Interrupt no. 75 - ivINT_ETPU_CISR_A_CIS7  */
    #define ETPU_CISR_A_CIS8_LVL            PRI_0           /* Interrupt no. 76 - ivINT_ETPU_CISR_A_CIS8  */
    #define ETPU_CISR_A_CIS9_LVL            PRI_0           /* Interrupt no. 77 - ivINT_ETPU_CISR_A_CIS9  */
    #define ETPU_CISR_A_CIS10_LVL           PRI_0           /* Interrupt no. 78 - ivINT_ETPU_CISR_A_CIS10  */
    #define ETPU_CISR_A_CIS11_LVL           PRI_0           /* Interrupt no. 79 - ivINT_ETPU_CISR_A_CIS11  */
    #define ETPU_CISR_A_CIS12_LVL           PRI_0           /* Interrupt no. 80 - ivINT_ETPU_CISR_A_CIS12  */
    #define ETPU_CISR_A_CIS13_LVL           PRI_0           /* Interrupt no. 81 - ivINT_ETPU_CISR_A_CIS13  */
#endif /* _BUILD_SYS_ */

   #define ETPU_CISR_A_CIS14_LVL           PRI_12           /* Interrupt no. 82 - ivINT_ETPU_CISR_A_CIS14  */
   #define ETPU_CISR_A_CIS15_LVL           PRI_12           /* Interrupt no. 83 - ivINT_ETPU_CISR_A_CIS15  */
   #define ETPU_CISR_A_CIS16_LVL           PRI_12           /* Interrupt no. 84 - ivINT_ETPU_CISR_A_CIS16  */
   #define ETPU_CISR_A_CIS17_LVL           PRI_12           /* Interrupt no. 85 - ivINT_ETPU_CISR_A_CIS17  */
   #define ETPU_CISR_A_CIS18_LVL           PRI_12           /* Interrupt no. 86 - ivINT_ETPU_CISR_A_CIS18  */
   #define ETPU_CISR_A_CIS19_LVL           PRI_12           /* Interrupt no. 87 - ivINT_ETPU_CISR_A_CIS19  */
   #define ETPU_CISR_A_CIS20_LVL           PRI_12           /* Interrupt no. 88 - ivINT_ETPU_CISR_A_CIS20  */
   #define ETPU_CISR_A_CIS21_LVL           PRI_12           /* Interrupt no. 89 - ivINT_ETPU_CISR_A_CIS21  */
   #define ETPU_CISR_A_CIS22_LVL           PRI_12           /* Interrupt no. 90 - ivINT_ETPU_CISR_A_CIS22  */
   #define ETPU_CISR_A_CIS23_LVL           PRI_12           /* Interrupt no. 91 - ivINT_ETPU_CISR_A_CIS23  */
   #define ETPU_CISR_A_CIS24_LVL           PRI_12           /* Interrupt no. 92 - ivINT_ETPU_CISR_A_CIS24  */
   #define ETPU_CISR_A_CIS25_LVL           PRI_12           /* Interrupt no. 93 - ivINT_ETPU_CISR_A_CIS25  */
   #define ETPU_CISR_A_CIS26_LVL           PRI_12 /* PRI_14 */     /* Interrupt no. 99 - ivINT_ETPU_CISR_A_CIS31  */

   #define ETPU_CISR_A_CIS27_LVL           PRI_0            /* Interrupt no. 95 - ivINT_ETPU_CISR_A_CIS27  */
   #define ETPU_CISR_A_CIS28_LVL           PRI_0            /* Interrupt no. 96 - ivINT_ETPU_CISR_A_CIS28  */
   #define ETPU_CISR_A_CIS29_LVL           PRI_0            /* Interrupt no. 97 - ivINT_ETPU_CISR_A_CIS29  */
   #define ETPU_CISR_A_CIS30_LVL           PRI_0            /* Interrupt no. 98 - ivINT_ETPU_CISR_A_CIS30  */
   #define ETPU_CISR_A_CIS31_LVL           PRI_0            /* Interrupt no. 99 - ivINT_ETPU_CISR_A_CIS31  */
#endif

#ifdef _BUILD_ADC_
#define EQADC_FISR_TORF_RFOF_CFUF_LVL   PRI_15          /* Interrupt no. 100 - ivINT_EQADC_FISR_TORF_RFOF_CFUF  */
#else
#define EQADC_FISR_TORF_RFOF_CFUF_LVL   PRI_0           /* Interrupt no. 100 - ivINT_EQADC_FISR_TORF_RFOF_CFUF  */
#endif /* _BUILD_ADC_ */

#define EQADC_FISR0_NCF_LVL             PRI_0           /* Interrupt no. 101 - ivINT_EQADC_FISR0_NCF  */
#define EQADC_FISR0_PF_LVL              PRI_0           /* Interrupt no. 102 - ivINT_EQADC_FISR0_PF  */
#define EQADC_FISR0_EOQF_LVL            PRI_0           /* Interrupt no. 103 - ivINT_EQADC_FISR0_EOQF  */
#define EQADC_FISR0_CFFF_LVL            PRI_0           /* Interrupt no. 104 - ivINT_EQADC_FISR0_CFFF  */
#define EQADC_FISR0_RFDF_LVL            PRI_0           /* Interrupt no. 105 - ivINT_EQADC_FISR0_RFDF  */
#define EQADC_FISR1_NCF_LVL             PRI_0           /* Interrupt no. 106 - ivINT_EQADC_FISR1_NCF  */
#define EQADC_FISR1_PF_LVL              PRI_0           /* Interrupt no. 107 - ivINT_EQADC_FISR1_PF  */
#define EQADC_FISR1_EOQF_LVL            PRI_0           /* Interrupt no. 108 - ivINT_EQADC_FISR1_EOQF  */
#define EQADC_FISR1_CFFF_LVL            PRI_0           /* Interrupt no. 109 - ivINT_EQADC_FISR1_CFFF  */
#define EQADC_FISR1_RFDF_LVL            PRI_0           /* Interrupt no. 110 - ivINT_EQADC_FISR1_RFDF  */
#define EQADC_FISR2_NCF_LVL             PRI_0           /* Interrupt no. 111 - ivINT_EQADC_FISR2_NCF  */
#define EQADC_FISR2_PF_LVL              PRI_0           /* Interrupt no. 112 - ivINT_EQADC_FISR2_PF  */
#define EQADC_FISR2_EOQF_LVL            PRI_0           /* Interrupt no. 113 - ivINT_EQADC_FISR2_EOQF  */
#define EQADC_FISR2_CFFF_LVL            PRI_0           /* Interrupt no. 114 - ivINT_EQADC_FISR2_CFFF  */
#define EQADC_FISR2_RFDF_LVL            PRI_0           /* Interrupt no. 115 - ivINT_EQADC_FISR2_RFDF  */
#define EQADC_FISR3_NCF_LVL             PRI_0           /* Interrupt no. 116 - ivINT_EQADC_FISR3_NCF  */
#define EQADC_FISR3_PF_LVL              PRI_0           /* Interrupt no. 117 - ivINT_EQADC_FISR3_PF  */
#define EQADC_FISR3_EOQF_LVL            PRI_0           /* Interrupt no. 118 - ivINT_EQADC_FISR3_EOQF  */
#define EQADC_FISR3_CFFF_LVL            PRI_0           /* Interrupt no. 119 - ivINT_EQADC_FISR3_CFFF  */
#define EQADC_FISR3_RFDF_LVL            PRI_0           /* Interrupt no. 120 - ivINT_EQADC_FISR3_RFDF  */
#define EQADC_FISR4_NCF_LVL             PRI_0           /* Interrupt no. 121 - ivINT_EQADC_FISR4_NCF  */
#define EQADC_FISR4_PF_LVL              PRI_0           /* Interrupt no. 122 - ivINT_EQADC_FISR4_PF  */
#define EQADC_FISR4_EOQF_LVL            PRI_0           /* Interrupt no. 123 - ivINT_EQADC_FISR4_EOQF  */
#define EQADC_FISR4_CFFF_LVL            PRI_0           /* Interrupt no. 124 - ivINT_EQADC_FISR4_CFFF  */
#define EQADC_FISR4_RFDF_LVL            PRI_0           /* Interrupt no. 125 - ivINT_EQADC_FISR4_RFDF  */
#define EQADC_FISR5_NCF_LVL             PRI_0           /* Interrupt no. 126 - ivINT_EQADC_FISR5_NCF  */
#define EQADC_FISR5_PF_LVL              PRI_0           /* Interrupt no. 127 - ivINT_EQADC_FISR5_PF  */
#define EQADC_FISR5_EOQF_LVL            PRI_0           /* Interrupt no. 128 - ivINT_EQADC_FISR5_EOQF  */
#define EQADC_FISR5_CFFF_LVL            PRI_0           /* Interrupt no. 129 - ivINT_EQADC_FISR5_CFFF  */
#define EQADC_FISR5_RFDF_LVL            PRI_0           /* Interrupt no. 130 - ivINT_EQADC_FISR5_RFDF  */
#define DSPI_B_ISR_TFUF_RFOF_LVL        PRI_0           /* Interrupt no. 131 - ivINT_DSPI_B_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#define DSPI_B_ISR_EOQF_LVL             PRI_0          /* Interrupt no. 132 - ivINT_DSPI_B_ISR_EOQF  */
#else
#define DSPI_B_ISR_EOQF_LVL             PRI_0       /* Interrupt no. 132 - ivINT_DSPI_B_ISR_EOQF  */
#endif /* _BUILD_SPI_ */

#define DSPI_B_ISR_TFFF_LVL             PRI_0       /* Interrupt no. 133 - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_B_ISR_TCF_LVL              PRI_0       /* Interrupt no. 134 - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_B_ISR_RFDF_LVL             PRI_0       /* Interrupt no. 135 - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_C_ISR_TFUF_RFOF_LVL        PRI_0       /* Interrupt no. 136 - ivINT_DSPI_C_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#define DSPI_C_ISR_EOQF_LVL             PRI_0          /* Interrupt no. 137 - ivINT_DSPI_C_ISR_EOQF  */
#else
#define DSPI_C_ISR_EOQF_LVL             PRI_0       /* Interrupt no. 137 - ivINT_DSPI_C_ISR_EOQF  */
#endif /* _BUILD_SPI_ */

#define DSPI_C_ISR_TFFF_LVL             PRI_0       /* Interrupt no. 138 - ivINT_DSPI_C_ISR_TFFF  */
#define DSPI_C_ISR_TCF_LVL              PRI_0       /* Interrupt no. 139 - ivINT_DSPI_C_ISR_TCF  */
#define DSPI_C_ISR_RFDF_LVL             PRI_0       /* Interrupt no. 140 - ivINT_DSPI_C_ISR_RFDF  */
#define DSPI_D_ISR_TFUF_RFOF_LVL        PRI_0       /* Interrupt no. 141 - ivINT_DSPI_D_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#if (TARGET_TYPE == MPC5633  || TARGET_TYPE == MPC5634)
#define DSPI_D_ISR_EOQF_LVL             PRI_0       /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
#else
#define DSPI_D_ISR_EOQF_LVL             PRI_10          /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
#endif
#else
#define DSPI_D_ISR_EOQF_LVL             PRI_0       /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
#endif /* _BUILD_SPI_ */

#define DSPI_D_ISR_TFFF_LVL             PRI_0       /* Interrupt no. 143 - ivINT_DSPI_D_ISR_TFFF  */
#define DSPI_D_ISR_TCF_LVL              PRI_0       /* Interrupt no. 144 - ivINT_DSPI_D_ISR_TCF  */
#define DSPI_D_ISR_RFDF_LVL             PRI_0       /* Interrupt no. 145 - ivINT_DSPI_D_ISR_RFDF  */
#define ESCI_A_LVL                      PRI_12      /* Interrupt no. 146 - ivINT_ESCI_A  */
#define ESCI_B_LVL                      PRI_12      /* Interrupt no. 149 - ivINT_ESCI_B  */


#ifdef _BUILD_CAN_
#define CAN_A_ESR_BOFF_INT_LVL          PRI_0       /* Interrupt no. 152 - ivINT_CAN_A_ESR_BOFF_INT  */
#define CAN_A_ESR_ERR_INT_LVL           PRI_0       /* Interrupt no. 153 - ivINT_CAN_A_ESR_ERR_INT  */
#define CAN_A_IFRL_BUF0I_LVL            PRI_10          /* Interrupt no. 155 - ivINT_CAN_A_IFRL_BUF0  */
#define CAN_A_IFRL_BUF1I_LVL            PRI_10          /* Interrupt no. 156 - ivINT_CAN_A_IFRL_BUF1  */
#define CAN_A_IFRL_BUF2I_LVL            PRI_10          /* Interrupt no. 157 - ivINT_CAN_A_IFRL_BUF2  */
#define CAN_A_IFRL_BUF3I_LVL            PRI_10          /* Interrupt no. 158 - ivINT_CAN_A_IFRL_BUF3  */
#define CAN_A_IFRL_BUF4I_LVL            PRI_10          /* Interrupt no. 159 - ivINT_CAN_A_IFRL_BUF4  */
#define CAN_A_IFRL_BUF5I_LVL            PRI_10          /* Interrupt no. 160 - ivINT_CAN_A_IFRL_BUF5  */
#define CAN_A_IFRL_BUF6I_LVL            PRI_10          /* Interrupt no. 161 - ivINT_CAN_A_IFRL_BUF6  */
#define CAN_A_IFRL_BUF7I_LVL            PRI_10          /* Interrupt no. 162 - ivINT_CAN_A_IFRL_BUF7  */
#define CAN_A_IFRL_BUF8I_LVL            PRI_10      /* Interrupt no. 163 - ivINT_CAN_A_IFRL_BUF8  */
#define CAN_A_IFRL_BUF9I_LVL            PRI_10          /* Interrupt no. 164 - ivINT_CAN_A_IFRL_BUF9  */
#define CAN_A_IFRL_BUF10I_LVL           PRI_10          /* Interrupt no. 165 - ivINT_CAN_A_IFRL_BUF10  */
#define CAN_A_IFRL_BUF11I_LVL           PRI_10          /* Interrupt no. 166 - ivINT_CAN_A_IFRL_BUF11  */
#define CAN_A_IFRL_BUF12I_LVL           PRI_10          /* Interrupt no. 167 - ivINT_CAN_A_IFRL_BUF12  */
#define CAN_A_IFRL_BUF13I_LVL           PRI_10          /* Interrupt no. 168 - ivINT_CAN_A_IFRL_BUF13  */
#define CAN_A_IFRL_BUF14I_LVL           PRI_10          /* Interrupt no. 169 - ivINT_CAN_A_IFRL_BUF14  */
#define CAN_A_IFRL_BUF15I_LVL           PRI_10          /* Interrupt no. 170 - ivINT_CAN_A_IFRL_BUF15  */
#define CAN_A_IFRL_BUF31I_BUF16I_LVL    PRI_10          /* Interrupt no. 171 - ivINT_CAN_A_IFRL_BUF31_16  */
#define CAN_A_IFRH_BUF63I_BUF32I_LVL    PRI_10          /* Interrupt no. 172 - ivINT_CAN_A_IFRL_BUF63_32  */
#define CAN_C_ESR_BOFF_INT_LVL          PRI_0       /* Interrupt no. 173 - ivINT_CAN_C_ESR_BOFF_INT  */
#define CAN_C_ESR_ERR_INT_LVL           PRI_0       /* Interrupt no. 174 - ivINT_CAN_C_ESR_ERR_INT  */
#define CAN_C_IFRL_BUF0I_LVL            PRI_10          /* Interrupt no. 176 - ivINT_CAN_C_IFRL_BUF0  */
#define CAN_C_IFRL_BUF1I_LVL            PRI_10          /* Interrupt no. 177 - ivINT_CAN_C_IFRL_BUF1  */
#define CAN_C_IFRL_BUF2I_LVL            PRI_10          /* Interrupt no. 178 - ivINT_CAN_C_IFRL_BUF2  */
#define CAN_C_IFRL_BUF3I_LVL            PRI_10          /* Interrupt no. 179 - ivINT_CAN_C_IFRL_BUF3  */
#define CAN_C_IFRL_BUF4I_LVL            PRI_10          /* Interrupt no. 180 - ivINT_CAN_C_IFRL_BUF4  */
#define CAN_C_IFRL_BUF5I_LVL            PRI_10          /* Interrupt no. 181 - ivINT_CAN_C_IFRL_BUF5  */
#define CAN_C_IFRL_BUF6I_LVL            PRI_10          /* Interrupt no. 182 - ivINT_CAN_C_IFRL_BUF6  */
#define CAN_C_IFRL_BUF7I_LVL            PRI_10          /* Interrupt no. 183 - ivINT_CAN_C_IFRL_BUF7  */
#define CAN_C_IFRL_BUF8I_LVL            PRI_10          /* Interrupt no. 184 - ivINT_CAN_C_IFRL_BUF8  */
#define CAN_C_IFRL_BUF9I_LVL            PRI_10          /* Interrupt no. 185 - ivINT_CAN_C_IFRL_BUF9  */
#define CAN_C_IFRL_BUF10I_LVL           PRI_10          /* Interrupt no. 186 - ivINT_CAN_C_IFRL_BUF10  */
#define CAN_C_IFRL_BUF11I_LVL           PRI_10          /* Interrupt no. 187 - ivINT_CAN_C_IFRL_BUF11  */
#define CAN_C_IFRL_BUF12I_LVL           PRI_10          /* Interrupt no. 188 - ivINT_CAN_C_IFRL_BUF12  */
#define CAN_C_IFRL_BUF13I_LVL           PRI_10          /* Interrupt no. 189 - ivINT_CAN_C_IFRL_BUF13  */
#define CAN_C_IFRL_BUF14I_LVL           PRI_10          /* Interrupt no. 190 - ivINT_CAN_C_IFRL_BUF14  */
#define CAN_C_IFRL_BUF15I_LVL           PRI_10          /* Interrupt no. 191 - ivINT_CAN_C_IFRL_BUF15  */
#define CAN_C_IFRL_BUF31I_BUF16I_LVL    PRI_10          /* Interrupt no. 192 - ivINT_CAN_C_IFRL_BUF31_16  */
#define CAN_C_IFRH_BUF63I_BUF32I_LVL    PRI_0           /* Interrupt no. 193 - ivINT_CAN_C_IFRL_BUF63_32  */
#else
#define CAN_A_ESR_BOFF_INT_LVL          PRI_0       /* Interrupt no. 152 - ivINT_CAN_A_ESR_BOFF_INT  */
#define CAN_A_ESR_ERR_INT_LVL           PRI_0       /* Interrupt no. 153 - ivINT_CAN_A_ESR_ERR_INT  */
#define CAN_A_IFRL_BUF0I_LVL            PRI_0       /* Interrupt no. 155 - ivINT_CAN_A_IFRL_BUF0  */
#define CAN_A_IFRL_BUF1I_LVL            PRI_0       /* Interrupt no. 156 - ivINT_CAN_A_IFRL_BUF1  */
#define CAN_A_IFRL_BUF2I_LVL            PRI_0       /* Interrupt no. 157 - ivINT_CAN_A_IFRL_BUF2  */
#define CAN_A_IFRL_BUF3I_LVL            PRI_0       /* Interrupt no. 158 - ivINT_CAN_A_IFRL_BUF3  */
#define CAN_A_IFRL_BUF4I_LVL            PRI_0       /* Interrupt no. 159 - ivINT_CAN_A_IFRL_BUF4  */
#define CAN_A_IFRL_BUF5I_LVL            PRI_0       /* Interrupt no. 160 - ivINT_CAN_A_IFRL_BUF5  */
#define CAN_A_IFRL_BUF6I_LVL            PRI_0       /* Interrupt no. 161 - ivINT_CAN_A_IFRL_BUF6  */
#define CAN_A_IFRL_BUF7I_LVL            PRI_0       /* Interrupt no. 162 - ivINT_CAN_A_IFRL_BUF7  */
#define CAN_A_IFRL_BUF8I_LVL            PRI_0       /* Interrupt no. 163 - ivINT_CAN_A_IFRL_BUF8  */
#define CAN_A_IFRL_BUF9I_LVL            PRI_0       /* Interrupt no. 164 - ivINT_CAN_A_IFRL_BUF9  */
#define CAN_A_IFRL_BUF10I_LVL           PRI_0       /* Interrupt no. 165 - ivINT_CAN_A_IFRL_BUF10  */
#define CAN_A_IFRL_BUF11I_LVL           PRI_0       /* Interrupt no. 166 - ivINT_CAN_A_IFRL_BUF11  */
#define CAN_A_IFRL_BUF12I_LVL           PRI_0       /* Interrupt no. 167 - ivINT_CAN_A_IFRL_BUF12  */
#define CAN_A_IFRL_BUF13I_LVL           PRI_0       /* Interrupt no. 168 - ivINT_CAN_A_IFRL_BUF13  */
#define CAN_A_IFRL_BUF14I_LVL           PRI_0       /* Interrupt no. 169 - ivINT_CAN_A_IFRL_BUF14  */
#define CAN_A_IFRL_BUF15I_LVL           PRI_0       /* Interrupt no. 170 - ivINT_CAN_A_IFRL_BUF15  */
#define CAN_A_IFRL_BUF31I_BUF16I_LVL    PRI_0       /* Interrupt no. 171 - ivINT_CAN_A_IFRL_BUF31_16  */
#define CAN_A_IFRH_BUF63I_BUF32I_LVL    PRI_0       /* Interrupt no. 172 - ivINT_CAN_A_IFRL_BUF63_32  */
#define CAN_C_ESR_BOFF_INT_LVL          PRI_0       /* Interrupt no. 173 - ivINT_CAN_C_ESR_BOFF_INT  */
#define CAN_C_ESR_ERR_INT_LVL           PRI_0       /* Interrupt no. 174 - ivINT_CAN_C_ESR_ERR_INT  */
#define CAN_C_IFRL_BUF0I_LVL            PRI_0       /* Interrupt no. 176 - ivINT_CAN_C_IFRL_BUF0  */
#define CAN_C_IFRL_BUF1I_LVL            PRI_0       /* Interrupt no. 177 - ivINT_CAN_C_IFRL_BUF1  */
#define CAN_C_IFRL_BUF2I_LVL            PRI_0       /* Interrupt no. 178 - ivINT_CAN_C_IFRL_BUF2  */
#define CAN_C_IFRL_BUF3I_LVL            PRI_0       /* Interrupt no. 179 - ivINT_CAN_C_IFRL_BUF3  */
#define CAN_C_IFRL_BUF4I_LVL            PRI_0           /* Interrupt no. 180 - ivINT_CAN_C_IFRL_BUF4  */
#define CAN_C_IFRL_BUF5I_LVL            PRI_0           /* Interrupt no. 181 - ivINT_CAN_C_IFRL_BUF5  */
#define CAN_C_IFRL_BUF6I_LVL            PRI_0           /* Interrupt no. 182 - ivINT_CAN_C_IFRL_BUF6  */
#define CAN_C_IFRL_BUF7I_LVL            PRI_0           /* Interrupt no. 183 - ivINT_CAN_C_IFRL_BUF7  */
#define CAN_C_IFRL_BUF8I_LVL            PRI_0           /* Interrupt no. 184 - ivINT_CAN_C_IFRL_BUF8  */
#define CAN_C_IFRL_BUF9I_LVL            PRI_0           /* Interrupt no. 185 - ivINT_CAN_C_IFRL_BUF9  */
#define CAN_C_IFRL_BUF10I_LVL           PRI_0           /* Interrupt no. 186 - ivINT_CAN_C_IFRL_BUF10  */
#define CAN_C_IFRL_BUF11I_LVL           PRI_0           /* Interrupt no. 187 - ivINT_CAN_C_IFRL_BUF11  */
#define CAN_C_IFRL_BUF12I_LVL           PRI_0           /* Interrupt no. 188 - ivINT_CAN_C_IFRL_BUF12  */
#define CAN_C_IFRL_BUF13I_LVL           PRI_0           /* Interrupt no. 189 - ivINT_CAN_C_IFRL_BUF13  */
#define CAN_C_IFRL_BUF14I_LVL           PRI_0           /* Interrupt no. 190 - ivINT_CAN_C_IFRL_BUF14  */
#define CAN_C_IFRL_BUF15I_LVL           PRI_0           /* Interrupt no. 191 - ivINT_CAN_C_IFRL_BUF15  */
#define CAN_C_IFRL_BUF31I_BUF16I_LVL    PRI_0           /* Interrupt no. 192 - ivINT_CAN_C_IFRL_BUF31_16  */
#define CAN_C_IFRH_BUF63I_BUF32I_LVL    PRI_0           /* Interrupt no. 193 - ivINT_CAN_C_IFRL_BUF63_32  */
#endif /* _BUILD_CAN_ */


#define EMIOS_GFR_F16_LVL               PRI_0           /* Interrupt no. 202 - ivINT_EMIOS_GFR_F16  */
#define EMIOS_GFR_F17_LVL               PRI_0           /* Interrupt no. 203 - ivINT_EMIOS_GFR_F17  */
#define EMIOS_GFR_F18_LVL               PRI_0           /* Interrupt no. 204 - ivINT_EMIOS_GFR_F18  */
#define EMIOS_GFR_F19_LVL               PRI_0           /* Interrupt no. 205 - ivINT_EMIOS_GFR_F19  */

#ifdef _BUILD_TIMING_
#define EMIOS_GFR_F20_LVL               PRI_11          /* Interrupt no. 206 - ivINT_EMIOS_GFR_F20  */
#define EMIOS_GFR_F21_LVL               PRI_11          /* Interrupt no. 207 - ivINT_EMIOS_GFR_F21  */
#define EMIOS_GFR_F22_LVL               PRI_11          /* Interrupt no. 208 - ivINT_EMIOS_GFR_F22  */
#else
#define EMIOS_GFR_F20_LVL               PRI_0           /* Interrupt no. 206 - ivINT_EMIOS_GFR_F20  */
#define EMIOS_GFR_F21_LVL               PRI_0           /* Interrupt no. 207 - ivINT_EMIOS_GFR_F21  */
#define EMIOS_GFR_F22_LVL               PRI_0           /* Interrupt no. 208 - ivINT_EMIOS_GFR_F22  */
#endif /* _BUILD_TIMING_ */

#define EMIOS_GFR_F23_LVL               PRI_12           /* Interrupt no. 209 - ivINT_EMIOS_GFR_F23  */
#define EDMA_ERRH_ERR63_ERR32_LVL       PRI_0           /* Interrupt no. 210 - ivINT_EDMA_ERRH_ERR63_32  */
#define EDMA_IRQRH_INT32_LVL            PRI_0           /* Interrupt no. 211 - ivINT_EDMA_IRQRH_INT32  */
#define EDMA_IRQRH_INT33_LVL            PRI_0           /* Interrupt no. 212 - ivINT_EDMA_IRQRH_INT33  */
#define EDMA_IRQRH_INT34_LVL            PRI_0           /* Interrupt no. 213 - ivINT_EDMA_IRQRH_INT34  */
#define EDMA_IRQRH_INT35_LVL            PRI_0           /* Interrupt no. 214 - ivINT_EDMA_IRQRH_INT35  */
#define EDMA_IRQRH_INT36_LVL            PRI_0           /* Interrupt no. 215 - ivINT_EDMA_IRQRH_INT36  */
#define EDMA_IRQRH_INT37_LVL            PRI_0           /* Interrupt no. 216 - ivINT_EDMA_IRQRH_INT37  */
#define EDMA_IRQRH_INT38_LVL            PRI_0           /* Interrupt no. 217 - ivINT_EDMA_IRQRH_INT38  */
#define EDMA_IRQRH_INT39_LVL            PRI_0           /* Interrupt no. 218 - ivINT_EDMA_IRQRH_INT39  */
#define EDMA_IRQRH_INT40_LVL            PRI_0           /* Interrupt no. 219 - ivINT_EDMA_IRQRH_INT40  */
#define EDMA_IRQRH_INT41_LVL            PRI_0           /* Interrupt no. 220 - ivINT_EDMA_IRQRH_INT41  */
#define EDMA_IRQRH_INT42_LVL            PRI_0           /* Interrupt no. 221 - ivINT_EDMA_IRQRH_INT42  */
#define EDMA_IRQRH_INT43_LVL            PRI_0           /* Interrupt no. 222 - ivINT_EDMA_IRQRH_INT43  */
#define EDMA_IRQRH_INT44_LVL            PRI_0           /* Interrupt no. 223 - ivINT_EDMA_IRQRH_INT44  */
#define EDMA_IRQRH_INT45_LVL            PRI_0           /* Interrupt no. 224 - ivINT_EDMA_IRQRH_INT45  */
#define EDMA_IRQRH_INT46_LVL            PRI_0           /* Interrupt no. 225 - ivINT_EDMA_IRQRH_INT46  */
#define EDMA_IRQRH_INT47_LVL            PRI_0           /* Interrupt no. 226 - ivINT_EDMA_IRQRH_INT47  */
#define EDMA_IRQRH_INT48_LVL            PRI_0           /* Interrupt no. 227 - ivINT_EDMA_IRQRH_INT48  */
#define EDMA_IRQRH_INT49_LVL            PRI_0           /* Interrupt no. 228 - ivINT_EDMA_IRQRH_INT49  */
#define EDMA_IRQRH_INT50_LVL            PRI_0           /* Interrupt no. 229 - ivINT_EDMA_IRQRH_INT50  */
#define EDMA_IRQRH_INT51_LVL            PRI_0           /* Interrupt no. 230 - ivINT_EDMA_IRQRH_INT51  */
#define EDMA_IRQRH_INT52_LVL            PRI_0           /* Interrupt no. 231 - ivINT_EDMA_IRQRH_INT52  */
#define EDMA_IRQRH_INT53_LVL            PRI_0           /* Interrupt no. 232 - ivINT_EDMA_IRQRH_INT53  */
#define EDMA_IRQRH_INT54_LVL            PRI_0           /* Interrupt no. 233 - ivINT_EDMA_IRQRH_INT54  */
#define EDMA_IRQRH_INT55_LVL            PRI_0           /* Interrupt no. 234 - ivINT_EDMA_IRQRH_INT55  */
#define EDMA_IRQRH_INT56_LVL            PRI_0           /* Interrupt no. 235 - ivINT_EDMA_IRQRH_INT56  */
#define EDMA_IRQRH_INT57_LVL            PRI_0           /* Interrupt no. 236 - ivINT_EDMA_IRQRH_INT57  */
#define EDMA_IRQRH_INT58_LVL            PRI_0           /* Interrupt no. 237 - ivINT_EDMA_IRQRH_INT58  */
#define EDMA_IRQRH_INT59_LVL            PRI_0           /* Interrupt no. 238 - ivINT_EDMA_IRQRH_INT59  */
#define EDMA_IRQRH_INT60_LVL            PRI_0           /* Interrupt no. 239 - ivINT_EDMA_IRQRH_INT60  */
#define EDMA_IRQRH_INT61_LVL            PRI_0           /* Interrupt no. 240 - ivINT_EDMA_IRQRH_INT61  */
#define EDMA_IRQRH_INT62_LVL            PRI_0           /* Interrupt no. 241 - ivINT_EDMA_IRQRH_INT62  */
#define EDMA_IRQRH_INT63_LVL            PRI_0           /* Interrupt no. 242 - ivINT_EDMA_IRQRH_INT63  */

#if (USE_ETPU_B == 1)
#define ETPU_CISR_B_CIS0_LVL            PRI_12          /* Interrupt no. 243 - ivINT_ETPU_CISR_B_CIS0  */
#define ETPU_CISR_B_CIS1_LVL            PRI_12          /* Interrupt no. 244 - ivINT_ETPU_CISR_B_CIS1  */
#define ETPU_CISR_B_CIS2_LVL            PRI_12          /* Interrupt no. 245 - ivINT_ETPU_CISR_B_CIS2  */
#define ETPU_CISR_B_CIS3_LVL            PRI_12          /* Interrupt no. 246 - ivINT_ETPU_CISR_B_CIS3  */
#define ETPU_CISR_B_CIS4_LVL            PRI_12          /* Interrupt no. 247 - ivINT_ETPU_CISR_B_CIS4  */
#define ETPU_CISR_B_CIS5_LVL            PRI_12          /* Interrupt no. 248 - ivINT_ETPU_CISR_B_CIS5  */
#define ETPU_CISR_B_CIS6_LVL            PRI_12          /* Interrupt no. 249 - ivINT_ETPU_CISR_B_CIS6  */
#define ETPU_CISR_B_CIS7_LVL            PRI_12          /* Interrupt no. 250 - ivINT_ETPU_CISR_B_CIS7  */
#define ETPU_CISR_B_CIS8_LVL            PRI_12          /* Interrupt no. 251 - ivINT_ETPU_CISR_B_CIS8  */
#define ETPU_CISR_B_CIS9_LVL            PRI_12          /* Interrupt no. 252 - ivINT_ETPU_CISR_B_CIS9  */
#define ETPU_CISR_B_CIS10_LVL           PRI_12          /* Interrupt no. 253 - ivINT_ETPU_CISR_B_CIS10  */
#define ETPU_CISR_B_CIS11_LVL           PRI_12          /* Interrupt no. 254 - ivINT_ETPU_CISR_B_CIS11  */
#define ETPU_CISR_B_CIS12_LVL           PRI_12          /* Interrupt no. 255 - ivINT_ETPU_CISR_B_CIS12  */
#define ETPU_CISR_B_CIS13_LVL           PRI_12          /* Interrupt no. 256 - ivINT_ETPU_CISR_B_CIS13  */
#define ETPU_CISR_B_CIS14_LVL           PRI_12          /* Interrupt no. 257 - ivINT_ETPU_CISR_B_CIS14  */
#define ETPU_CISR_B_CIS15_LVL           PRI_12          /* Interrupt no. 258 - ivINT_ETPU_CISR_B_CIS15  */
#define ETPU_CISR_B_CIS16_LVL           PRI_12          /* Interrupt no. 259 - ivINT_ETPU_CISR_B_CIS16  */
#define ETPU_CISR_B_CIS17_LVL           PRI_12          /* Interrupt no. 260 - ivINT_ETPU_CISR_B_CIS17  */
#define ETPU_CISR_B_CIS18_LVL           PRI_12          /* Interrupt no. 261 - ivINT_ETPU_CISR_B_CIS18  */
#define ETPU_CISR_B_CIS19_LVL           PRI_12          /* Interrupt no. 262 - ivINT_ETPU_CISR_B_CIS19  */
#define ETPU_CISR_B_CIS20_LVL           PRI_12          /* Interrupt no. 263 - ivINT_ETPU_CISR_B_CIS20  */
#define ETPU_CISR_B_CIS21_LVL           PRI_12          /* Interrupt no. 264 - ivINT_ETPU_CISR_B_CIS21  */
#define ETPU_CISR_B_CIS22_LVL           PRI_12          /* Interrupt no. 265 - ivINT_ETPU_CISR_B_CIS22  */
#define ETPU_CISR_B_CIS23_LVL           PRI_12          /* Interrupt no. 266 - ivINT_ETPU_CISR_B_CIS23  */
#define ETPU_CISR_B_CIS24_LVL           PRI_12          /* Interrupt no. 267 - ivINT_ETPU_CISR_B_CIS24  */
#define ETPU_CISR_B_CIS25_LVL           PRI_12          /* Interrupt no. 268 - ivINT_ETPU_CISR_B_CIS25  */
#define ETPU_CISR_B_CIS26_LVL           PRI_12          /* Interrupt no. 269 - ivINT_ETPU_CISR_B_CIS26  */
#define ETPU_CISR_B_CIS27_LVL           PRI_12          /* Interrupt no. 270 - ivINT_ETPU_CISR_B_CIS27  */
#define ETPU_CISR_B_CIS28_LVL           PRI_12          /* Interrupt no. 271 - ivINT_ETPU_CISR_B_CIS28  */
#define ETPU_CISR_B_CIS29_LVL           PRI_12          /* Interrupt no. 272 - ivINT_ETPU_CISR_B_CIS29  */
#define ETPU_CISR_B_CIS30_LVL           PRI_12          /* Interrupt no. 273 - ivINT_ETPU_CISR_B_CIS30  */
#define ETPU_CISR_B_CIS31_LVL           PRI_12          /* Interrupt no. 274 - ivINT_ETPU_CISR_B_CIS31  */
#else
#define ETPU_CISR_B_CIS0_LVL            PRI_0           /* Interrupt no. 243 - ivINT_ETPU_CISR_B_CIS0  */
#define ETPU_CISR_B_CIS1_LVL            PRI_0           /* Interrupt no. 244 - ivINT_ETPU_CISR_B_CIS1  */
#define ETPU_CISR_B_CIS2_LVL            PRI_0           /* Interrupt no. 245 - ivINT_ETPU_CISR_B_CIS2  */
#define ETPU_CISR_B_CIS3_LVL            PRI_0           /* Interrupt no. 246 - ivINT_ETPU_CISR_B_CIS3  */
#define ETPU_CISR_B_CIS4_LVL            PRI_0           /* Interrupt no. 247 - ivINT_ETPU_CISR_B_CIS4  */
#define ETPU_CISR_B_CIS5_LVL            PRI_0           /* Interrupt no. 248 - ivINT_ETPU_CISR_B_CIS5  */
#define ETPU_CISR_B_CIS6_LVL            PRI_0           /* Interrupt no. 249 - ivINT_ETPU_CISR_B_CIS6  */
#define ETPU_CISR_B_CIS7_LVL            PRI_0           /* Interrupt no. 250 - ivINT_ETPU_CISR_B_CIS7  */
#define ETPU_CISR_B_CIS8_LVL            PRI_0           /* Interrupt no. 251 - ivINT_ETPU_CISR_B_CIS8  */
#define ETPU_CISR_B_CIS9_LVL            PRI_0           /* Interrupt no. 252 - ivINT_ETPU_CISR_B_CIS9  */
#define ETPU_CISR_B_CIS10_LVL           PRI_0           /* Interrupt no. 253 - ivINT_ETPU_CISR_B_CIS10  */
#define ETPU_CISR_B_CIS11_LVL           PRI_0           /* Interrupt no. 254 - ivINT_ETPU_CISR_B_CIS11  */
#define ETPU_CISR_B_CIS12_LVL           PRI_0           /* Interrupt no. 255 - ivINT_ETPU_CISR_B_CIS12  */
#define ETPU_CISR_B_CIS13_LVL           PRI_0           /* Interrupt no. 256 - ivINT_ETPU_CISR_B_CIS13  */
#define ETPU_CISR_B_CIS14_LVL           PRI_0           /* Interrupt no. 257 - ivINT_ETPU_CISR_B_CIS14  */
#define ETPU_CISR_B_CIS15_LVL           PRI_0           /* Interrupt no. 258 - ivINT_ETPU_CISR_B_CIS15  */
#define ETPU_CISR_B_CIS16_LVL           PRI_0           /* Interrupt no. 259 - ivINT_ETPU_CISR_B_CIS16  */
#define ETPU_CISR_B_CIS17_LVL           PRI_0           /* Interrupt no. 260 - ivINT_ETPU_CISR_B_CIS17  */
#define ETPU_CISR_B_CIS18_LVL           PRI_0           /* Interrupt no. 261 - ivINT_ETPU_CISR_B_CIS18  */
#define ETPU_CISR_B_CIS19_LVL           PRI_0           /* Interrupt no. 262 - ivINT_ETPU_CISR_B_CIS19  */
#define ETPU_CISR_B_CIS20_LVL           PRI_0           /* Interrupt no. 263 - ivINT_ETPU_CISR_B_CIS20  */
#define ETPU_CISR_B_CIS21_LVL           PRI_0           /* Interrupt no. 264 - ivINT_ETPU_CISR_B_CIS21  */
#define ETPU_CISR_B_CIS22_LVL           PRI_0           /* Interrupt no. 265 - ivINT_ETPU_CISR_B_CIS22  */
#define ETPU_CISR_B_CIS23_LVL           PRI_0           /* Interrupt no. 266 - ivINT_ETPU_CISR_B_CIS23  */
#define ETPU_CISR_B_CIS24_LVL           PRI_0           /* Interrupt no. 267 - ivINT_ETPU_CISR_B_CIS24  */
#define ETPU_CISR_B_CIS25_LVL           PRI_0           /* Interrupt no. 268 - ivINT_ETPU_CISR_B_CIS25  */
#define ETPU_CISR_B_CIS26_LVL           PRI_0           /* Interrupt no. 269 - ivINT_ETPU_CISR_B_CIS26  */
#define ETPU_CISR_B_CIS27_LVL           PRI_0           /* Interrupt no. 270 - ivINT_ETPU_CISR_B_CIS27  */
#define ETPU_CISR_B_CIS28_LVL           PRI_0           /* Interrupt no. 271 - ivINT_ETPU_CISR_B_CIS28  */
#define ETPU_CISR_B_CIS29_LVL           PRI_0           /* Interrupt no. 272 - ivINT_ETPU_CISR_B_CIS29  */
#define ETPU_CISR_B_CIS30_LVL           PRI_0           /* Interrupt no. 273 - ivINT_ETPU_CISR_B_CIS30  */
#define ETPU_CISR_B_CIS31_LVL           PRI_0           /* Interrupt no. 274 - ivINT_ETPU_CISR_B_CIS31  */
#endif
#define DSPI_A_ISR_TFUF_RFOF_LVL        PRI_0           /* Interrupt no. 275 - ivINT_DSPI_A_ISR_TFUF_RFOF  */

#ifdef _BUILD_SPI_
#define DSPI_A_ISR_EOQF_LVL             PRI_10          /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
#else
#define DSPI_A_ISR_EOQF_LVL             PRI_0           /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
#endif /* _BUILD_SPI_ */

#define DSPI_A_ISR_TFFF_LVL             PRI_0           /* Interrupt no. 277 - ivINT_DSPI_A_ISR_TFFF  */
#define DSPI_A_ISR_TCF_LVL              PRI_0           /* Interrupt no. 278 - ivINT_DSPI_A_ISR_TCF  */
#define DSPI_A_ISR_RFDF_LVL             PRI_0           /* Interrupt no. 279 - ivINT_DSPI_A_ISR_RFDF  */

#ifdef _BUILD_CAN_
#define CAN_B_ESR_BOFF_INT_LVL          PRI_0           /* Interrupt no. 280 - ivINT_CAN_B_ESR_BOFF_INT  */
#define CAN_B_ESR_ERR_INT_LVL           PRI_0           /* Interrupt no. 281 - ivINT_CAN_B_ESR_ERR_INT  */
#define CAN_B_IFRL_BUF0I_LVL            PRI_10          /* Interrupt no. 283 - ivINT_CAN_B_IFRL_BUF0  */
#define CAN_B_IFRL_BUF1I_LVL            PRI_10          /* Interrupt no. 284 - ivINT_CAN_B_IFRL_BUF1  */
#define CAN_B_IFRL_BUF2I_LVL            PRI_10          /* Interrupt no. 285 - ivINT_CAN_B_IFRL_BUF2  */
#define CAN_B_IFRL_BUF3I_LVL            PRI_10          /* Interrupt no. 286 - ivINT_CAN_B_IFRL_BUF3  */
#define CAN_B_IFRL_BUF4I_LVL            PRI_10          /* Interrupt no. 287 - ivINT_CAN_B_IFRL_BUF4  */
#define CAN_B_IFRL_BUF5I_LVL            PRI_10          /* Interrupt no. 288 - ivINT_CAN_B_IFRL_BUF5  */
#define CAN_B_IFRL_BUF6I_LVL            PRI_10          /* Interrupt no. 289 - ivINT_CAN_B_IFRL_BUF6  */
#define CAN_B_IFRL_BUF7I_LVL            PRI_10          /* Interrupt no. 290 - ivINT_CAN_B_IFRL_BUF7  */
#define CAN_B_IFRL_BUF8I_LVL            PRI_10          /* Interrupt no. 291 - ivINT_CAN_B_IFRL_BUF8  */
#define CAN_B_IFRL_BUF9I_LVL            PRI_10          /* Interrupt no. 292 - ivINT_CAN_B_IFRL_BUF9  */
#define CAN_B_IFRL_BUF10I_LVL           PRI_10          /* Interrupt no. 293 - ivINT_CAN_B_IFRL_BUF10  */
#define CAN_B_IFRL_BUF11I_LVL           PRI_10          /* Interrupt no. 294 - ivINT_CAN_B_IFRL_BUF11  */
#define CAN_B_IFRL_BUF12I_LVL           PRI_10          /* Interrupt no. 295 - ivINT_CAN_B_IFRL_BUF12  */
#define CAN_B_IFRL_BUF13I_LVL           PRI_10          /* Interrupt no. 296 - ivINT_CAN_B_IFRL_BUF13  */
#define CAN_B_IFRL_BUF14I_LVL           PRI_10          /* Interrupt no. 297 - ivINT_CAN_B_IFRL_BUF14  */
#define CAN_B_IFRL_BUF15I_LVL           PRI_10          /* Interrupt no. 298 - ivINT_CAN_B_IFRL_BUF15  */
#define CAN_B_IFRL_BUF31I_BUF16I_LVL    PRI_10          /* Interrupt no. 299 - ivINT_CAN_B_IFRL_BUF31_16  */
#define CAN_B_IFRH_BUF63I_BUF32I_LVL    PRI_0           /* Interrupt no. 300 - ivINT_CAN_B_IFRL_BUF63_32  */
#else
#define CAN_B_ESR_BOFF_INT_LVL          PRI_0           /* Interrupt no. 280 - ivINT_CAN_B_ESR_BOFF_INT  */
#define CAN_B_ESR_ERR_INT_LVL           PRI_0           /* Interrupt no. 281 - ivINT_CAN_B_ESR_ERR_INT  */
#define CAN_B_IFRL_BUF0I_LVL            PRI_0           /* Interrupt no. 283 - ivINT_CAN_B_IFRL_BUF0  */
#define CAN_B_IFRL_BUF1I_LVL            PRI_0           /* Interrupt no. 284 - ivINT_CAN_B_IFRL_BUF1  */
#define CAN_B_IFRL_BUF2I_LVL            PRI_0           /* Interrupt no. 285 - ivINT_CAN_B_IFRL_BUF2  */
#define CAN_B_IFRL_BUF3I_LVL            PRI_0           /* Interrupt no. 286 - ivINT_CAN_B_IFRL_BUF3  */
#define CAN_B_IFRL_BUF4I_LVL            PRI_0           /* Interrupt no. 287 - ivINT_CAN_B_IFRL_BUF4  */
#define CAN_B_IFRL_BUF5I_LVL            PRI_0           /* Interrupt no. 288 - ivINT_CAN_B_IFRL_BUF5  */
#define CAN_B_IFRL_BUF6I_LVL            PRI_0           /* Interrupt no. 289 - ivINT_CAN_B_IFRL_BUF6  */
#define CAN_B_IFRL_BUF7I_LVL            PRI_0           /* Interrupt no. 290 - ivINT_CAN_B_IFRL_BUF7  */
#define CAN_B_IFRL_BUF8I_LVL            PRI_0           /* Interrupt no. 291 - ivINT_CAN_B_IFRL_BUF8  */
#define CAN_B_IFRL_BUF9I_LVL            PRI_0           /* Interrupt no. 292 - ivINT_CAN_B_IFRL_BUF9  */
#define CAN_B_IFRL_BUF10I_LVL           PRI_0           /* Interrupt no. 293 - ivINT_CAN_B_IFRL_BUF10  */
#define CAN_B_IFRL_BUF11I_LVL           PRI_0               /* Interrupt no. 294 - ivINT_CAN_B_IFRL_BUF11  */
#define CAN_B_IFRL_BUF12I_LVL           PRI_0               /* Interrupt no. 295 - ivINT_CAN_B_IFRL_BUF12  */
#define CAN_B_IFRL_BUF13I_LVL           PRI_0               /* Interrupt no. 296 - ivINT_CAN_B_IFRL_BUF13  */
#define CAN_B_IFRL_BUF14I_LVL           PRI_0               /* Interrupt no. 297 - ivINT_CAN_B_IFRL_BUF14  */
#define CAN_B_IFRL_BUF15I_LVL           PRI_0               /* Interrupt no. 298 - ivINT_CAN_B_IFRL_BUF15  */
#define CAN_B_IFRL_BUF31I_BUF16I_LVL    PRI_0               /* Interrupt no. 299 - ivINT_CAN_B_IFRL_BUF31_16  */
#define CAN_B_IFRH_BUF63I_BUF32I_LVL    PRI_0               /* Interrupt no. 300 - ivINT_CAN_B_IFRL_BUF63_32  */                        
#endif /* _BUILD_CAN_ */

#ifdef _BUILD_PIT_
#define PIT_INT_PIT0_LVL                PRI_12          /* Interrupt no. 301 - PIT0 */
#define PIT_INT_PIT1_LVL                PRI_15          /* Interrupt no. 302 - PIT1 */
#define PIT_INT_PIT2_LVL                PRI_12          /* Interrupt no. 303 - PIT2 */
#define PIT_INT_PIT3_LVL                PRI_12          /* Interrupt no. 304 - PIT3 */
#define PIT_INT_RTI_LVL                 PRI_12          /* Interrupt no. 305 - RTI  */
#else
#define PIT_INT_PIT0_LVL                PRI_0           /* Interrupt no. 301 - PIT0 */
#define PIT_INT_PIT1_LVL                PRI_0           /* Interrupt no. 302 - PIT1 */
#define PIT_INT_PIT2_LVL                PRI_0           /* Interrupt no. 303 - PIT2 */
#define PIT_INT_PIT3_LVL                PRI_0           /* Interrupt no. 304 - PIT3 */
#define PIT_INT_RTI_LVL                 PRI_0           /* Interrupt no. 305 - RTI  */
#endif /*_BUILD_PIT_*/


/* ------------------------------------------------------------------ */
/*     Common defines                                                   */
/* ------------------------------------------------------------------ */
#define SET_BIT_ENABLED    0x01
#define SET_BIT_DISABLED   0x00


#if (TARGET_TYPE == MPC5554)
  #if (ION_TRG_SOURCE == ION_TRG_EXTERN)
  #define TASKDELAY_CHANNEL   (ETPUA_UC25-ETPUA_UC0) /* Sovrapposto OUT_IgnDrv_4 */
  #else
  #define TASKDELAY_CHANNEL   (ETPUA_UC3-ETPUA_UC0)  /* Sovrapposto IN_IgnCmd_2  */
  #endif

#elif (TARGET_TYPE == MPC5534)
  #define TASKDELAY_CHANNEL   (ETPUA_UC18-ETPUA_UC0)

#elif (TARGET_TYPE == MPC5633 || TARGET_TYPE == MPC5634)
  #define TASKDELAY_CHANNEL   (ETPUA_UC18-ETPUA_UC0)

#define TASKDELAY_0_EMIOS_CHANNEL EMIOS_UC2
#define TASKDELAY_1_EMIOS_CHANNEL EMIOS_UC4
#define TASKDELAY_2_EMIOS_CHANNEL EMIOS_UC5
#define TASKDELAY_3_EMIOS_CHANNEL EMIOS_UC6
#define TASKDELAY_4_EMIOS_CHANNEL EMIOS_UC11

#else
  #error ERROR: Target not supported
#endif

