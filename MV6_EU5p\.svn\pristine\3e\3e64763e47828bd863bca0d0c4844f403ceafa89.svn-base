/*
 * File: SpeedLimCtrl_types.h
 *
 * Code generated for Simulink model 'SpeedLimCtrl'.
 *
 * Model version                  : 1.725
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Feb 17 15:04:15 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (30), Warnings (3), Error (0)
 */

#ifndef RTW_HEADER_SpeedLimCtrl_types_h_
#define RTW_HEADER_SpeedLimCtrl_types_h_
#endif                                 /* RTW_HEADER_SpeedLimCtrl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
