/*
 * File: KnockCorr_types.h
 *
 * Real-Time Workshop code generated for Simulink model KnockCorr.
 *
 * Model version                        : 1.272
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Tue Feb 06 16:40:32 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Tue Feb 06 16:40:38 2007
 */

#ifndef _RTW_HEADER_KnockCorr_types_h_
#define _RTW_HEADER_KnockCorr_types_h_

#ifndef _CSC3_KNOCKCORR_CHARTSTRUCT_
#define _CSC3_KNOCKCORR_CHARTSTRUCT_

typedef struct {
  uint8_T IndBuf[8];
  uint8_T offset;
  uint_T is_c3_KnockCorr : 2;
  uint_T is_active_c3_KnockCorr : 1;
} CSc3_KnockCorr_ChartStruct;

#endif                                  /* _CSC3_KNOCKCORR_CHARTSTRUCT_ */
#ifndef _CSC7_KNOCKCORR_CHARTSTRUCT_
#define _CSC7_KNOCKCORR_CHARTSTRUCT_

typedef struct {
  uint_T is_Normal : 2;
  uint_T is_active_Normal : 1;
  uint_T is_active_Test_Forced : 1;
  uint_T is_active_c7_KnockCorr : 1;
} CSc7_KnockCorr_ChartStruct;

#endif                                  /* _CSC7_KNOCKCORR_CHARTSTRUCT_ */

/* Forward declaration for rtModel */
typedef struct RT_MODEL_KnockCorr RT_MODEL_KnockCorr;

#endif                                  /* _RTW_HEADER_KnockCorr_types_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
