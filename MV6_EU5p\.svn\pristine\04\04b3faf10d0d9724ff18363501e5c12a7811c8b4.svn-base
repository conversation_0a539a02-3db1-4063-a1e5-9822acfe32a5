#include "mathlib.h"

#define MAX_DIFF_DATA_NUMBER    3       // massima differenza contatori (Eng1MsgCnt - Eng1MsgCntReply)
#define N_MSG_LAM               4       // Lam tx

/* E-LEAN */
#define AX_OFFSET (-16380)
#define AX_GAIN         2  /* 1/100 */
#define AX_MIN    (-16380)
#define AX_MAX      16380

#define AY_OFFSET (-16380)
#define AY_GAIN         2  /* 1/100 */
#define AY_MIN    (-16380)
#define AY_MAX      16380

#define AZ_OFFSET (-16380)
#define AZ_GAIN         2  /* 1/100 */
#define AZ_MIN    (-16380)
#define AZ_MAX      16380

#define PITCH_OFFSET (-16380)
#define PITCH_GAIN         2  /* 1/100 */
#define PITCH_MIN    (-16380)
#define PITCH_MAX      16380

#define ROLL_OFFSET  (-16380)
#define ROLL_GAIN          2  /* 1/100 */
#define ROLL_MIN     (-16380)
#define ROLL_MAX       16380

#define WX_OFFSET  (-16380)
#define WX_GAIN          2  /* 1/100 */
#define WX_MIN     (-16380)
#define WX_MAX       16380

#define WY_OFFSET  (-16380)
#define WY_GAIN          2  /* 1/100 */
#define WY_MIN     (-16380)
#define WY_MAX       16380

#define WZ_OFFSET  (-16380)
#define WZ_GAIN          2  /* 1/100 */
#define WZ_MIN     (-16380)
#define WZ_MAX       16380

#define SPEEDCAN_OFFSET     0
#define SPEEDCAN_GAIN       2 /* 2^-4 */
#define SPEEDCAN_MIN        0
#define SPEEDCAN_MAX    16383

#define TEMPEXTCAN_OFFSET (-1600)
#define TEMPEXTCAN_GAIN        4 /* 2^-4 */
#define TEMPEXTCAN_MIN    (-1600)
#define TEMPEXTCAN_MAX      2480

#define TEMPINTCAN_OFFSET (-1600)
#define TEMPINTCAN_GAIN        4 /* 2^-4 */
#define TEMPINTCAN_MIN    (-1600)
#define TEMPINTCAN_MAX      2480

#define TAIRCAN_OFFSET     (-40)
#define TAIRCAN_DIVIDER      (0)
#define TAIRCAN_MIN        (-40)
#define TAIRCAN_MAX        (127)
/********/

/* GPS */
#define ALT_GPS_GAIN (1) // 1/10
#define ALT_GPS_OFFSET (int32_T)(0.0f * 10.0f) // 0 m
#define ALT_GPS_MIN (int32_T)(0.0f * 10.0f) // 0 m
#define ALT_GPS_MAX (int32_T)(6553.5f * 10.0f) // 6553.5 m

#define SPEED_GPS_GAIN (4) // 2^-0 -> 2^-4
#define SPEED_GPS_OFFSET (int32_T)(0.0f * 16.0f) // 0 Km/h
#define SPEED_GPS_MIN (int32_T)(0.0f * 16.0f) // 0 Km/h
#define SPEED_GPS_MAX (int32_T)(511.0f * 16.0f) // 511 Km/h
/********/

/* E-SHARK */
#define SPEEDGPS_OFFSET_ES     0
#define SPEEDGPS_GAIN_ES      10 /* 1/100 */
#define SPEEDGPS_MIN_ES        0
#define SPEEDGPS_MAX_ES    65535

#define ALTITUDECAN_OFFSET_ES  (-32767)
#define ALTITUDECAN_GAIN_ES          1 /* 1/10 */
#define ALTITUDECAN_MIN_ES     (-32767)
#define ALTITUDECAN_MAX_ES       32767

#define HEADINGCAN_OFFSET_ES        0
#define HEADINGCAN_GAIN_ES         10 /* 1/100 */
#define HEADINGCAN_MIN_ES           0
#define HEADINGCAN_MAX_ES       65535
/********/

/* LMK005 */
#define LMK005_PITCH_OFFSET      (0)
#define LMK005_PITCH_GAIN        -1  /* 1/100 */
#define LMK005_PITCH_MIN    (-16380)
#define LMK005_PITCH_MAX      16380

#define LMK005_ROLL_OFFSET      (0)
#define LMK005_ROLL_GAIN         1  /* 1/100 */
#define LMK005_ROLL_MIN    (-16380)
#define LMK005_ROLL_MAX      16380
/********/

/************************/
/********* GPS_02 *******/
/************************/
typedef struct GPS02_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Altitude0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Altitude1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Satellites2   :6;
            vuint8_T  Dummy2_0   :2;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  SW_revision3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :5;
            vuint8_T  Speed6   :3;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Speed7   :6;
            vuint8_T  Dummy7_0   :2;
        } B;
    } Byte7;
} GPS02_T;

// Function prototypes
extern void CANMGM_Init(void);
extern void CANMGM_CanSend10ms(void);
extern void CANMGM_CanSend20ms(void);
extern void CANMGM_CanSend100ms(void);
extern void CANMGM_CanRecv20ms(void);
extern void CANMGM_CanRecv100ms(void);
extern void CANMGM_VehicCanDiag(void);

void CANMGM_CanRecvLAM(uint8_T can_ch, uint8_T lam_buf, uint8_T lam_buf_quelen, uint8_T ind_lam);
void CANMGM_VehicCanDiagBusOff(void);
void CANMGM_VehicCanDiagNST1Absent(void);

/* CN DB message protoypes */
void CANMGM_DBCAN_5_1(void);
void CANMGM_DBCAN_10_1(void);
void CANMGM_DBCAN_10_2(void);
void CANMGM_DBCAN_10_3(void);
void CANMGM_DBCAN_100_1(void);

