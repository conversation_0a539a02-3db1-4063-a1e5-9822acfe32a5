#ifndef _VSRAM_SHARED_CONTENT_H
#define _VSRAM_SHARED_CONTENT_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "recovery.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/


#define VSRAM_SHARED_MEMORY_SIZE (IVOR2_DATA_POS + FLASH_FAULT_STRUCT_LEN)

#define COMM_PROTOCOL_LEN                       (4)
#define KWP_TIMING_TEMPP1MIN_LEN                (4)
#define KWP_TIMING_TEMPP2MIN_LEN                (4)
#define KWP_TIMING_TEMPP2MAX_LEN                (4)
#define KWP_TIMING_TEMPP3MIN_LEN                (4)
#define KWP_TIMING_TEMPP3MAX_LEN                (4)
#define KWP_TIMING_TEMPP4MIN_LEN                (4)
#define KWPSESSION_LEN                          (4)
#define KWPDOWNLOADSTRUCT_LEN                   (4)
#define FLASHERASEPARAMS_STARTINGADDRESS_LEN    (4)
#define FLASHERASEPARAMS_SIZE_LEN               (4)
#define FLASHERASEPARAMS_CALLBACK_LEN           (4)
#define CHECKSUMPARAMS_CHECKSUMSTARTADDRESS_LEN (4)
#define CHECKSUMPARAMS_CHECKSUMENDADDRESS_LEN   (4)
#define CHECKSUMPARAMS_CHECKSUM_LEN             (4)
#define CHECKSUMPARAMS_INIT_CRC_LEN             (4)
#define CHECKSUMPARAMS_DATA_PTR_LEN             (4)
#define CHECKSUMPARAMS_DATA_SIZE_LEN            (4)
#define KWP_BAUD_LEN                            (4)
#define IVOR2_DATA_LEN                          (FLASH_FAULT_STRUCT_LEN)


#define COMM_PROTOCOL_POS                        (0)
#define KWP_TIMING_TEMPP1MIN_POS                 (COMM_PROTOCOL_POS + COMM_PROTOCOL_LEN)
#define KWP_TIMING_TEMPP2MIN_POS                 (KWP_TIMING_TEMPP1MIN_POS + KWP_TIMING_TEMPP1MIN_LEN)
#define KWP_TIMING_TEMPP2MAX_POS                 (KWP_TIMING_TEMPP2MIN_POS + KWP_TIMING_TEMPP2MIN_LEN)
#define KWP_TIMING_TEMPP3MIN_POS                 (KWP_TIMING_TEMPP2MAX_POS + KWP_TIMING_TEMPP2MAX_LEN)
#define KWP_TIMING_TEMPP3MAX_POS                 (KWP_TIMING_TEMPP3MIN_POS + KWP_TIMING_TEMPP3MIN_LEN)
#define KWP_TIMING_TEMPP4MIN_POS                 (KWP_TIMING_TEMPP3MAX_POS + KWP_TIMING_TEMPP3MAX_LEN)
#define KWPSESSION_POS                           (KWP_TIMING_TEMPP4MIN_POS + KWP_TIMING_TEMPP4MIN_LEN)
#define KWPDOWNLOADSTRUCT_POS                    (KWPSESSION_POS + KWPSESSION_LEN)
#define FLASHERASEPARAMS_STARTINGADDRESS_POS     (KWPDOWNLOADSTRUCT_POS + KWPDOWNLOADSTRUCT_LEN)
#define FLASHERASEPARAMS_SIZE_POS                (FLASHERASEPARAMS_STARTINGADDRESS_POS + FLASHERASEPARAMS_STARTINGADDRESS_LEN)
#define FLASHERASEPARAMS_CALLBACK_POS            (FLASHERASEPARAMS_SIZE_POS + FLASHERASEPARAMS_SIZE_LEN)
#define CHECKSUMPARAMS_CHECKSUMSTARTADDRESS_POS  (FLASHERASEPARAMS_CALLBACK_POS + FLASHERASEPARAMS_CALLBACK_LEN)
#define CHECKSUMPARAMS_CHECKSUMENDADDRESS_POS    (CHECKSUMPARAMS_CHECKSUMSTARTADDRESS_POS + CHECKSUMPARAMS_CHECKSUMSTARTADDRESS_LEN)
#define CHECKSUMPARAMS_CHECKSUM_POS              (CHECKSUMPARAMS_CHECKSUMENDADDRESS_POS + CHECKSUMPARAMS_CHECKSUMENDADDRESS_LEN)
#define CHECKSUMPARAMS_INIT_CRC_POS              (CHECKSUMPARAMS_CHECKSUM_POS + CHECKSUMPARAMS_CHECKSUM_LEN)
#define CHECKSUMPARAMS_DATA_PTR_POS              (CHECKSUMPARAMS_INIT_CRC_POS + CHECKSUMPARAMS_INIT_CRC_LEN)
#define CHECKSUMPARAMS_DATA_SIZE_POS             (CHECKSUMPARAMS_DATA_PTR_POS + CHECKSUMPARAMS_DATA_PTR_LEN)
#define KWP_BAUD_POS                             (CHECKSUMPARAMS_DATA_SIZE_POS + CHECKSUMPARAMS_DATA_SIZE_LEN)
#define IVOR2_DATA_POS                           (KWP_BAUD_POS + KWP_BAUD_LEN)

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
 extern uint8_t vsram_shared_memory[VSRAM_SHARED_MEMORY_SIZE];


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */
#endif /* _VSRAM_SHARED_CONTENT_H */
