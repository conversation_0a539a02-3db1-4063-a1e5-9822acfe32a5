#ifndef _EE_H_
#define _EE_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/    
#define EE_ID_FOUND                 (0)
#define NO_EE_BLOCK                (-1)
#define BLOCK_NOT_OK               (-2)
#define EE_BLANK                   (-3)
#define ERROR_CHECKSUM             (-4)
#define EE_ERROR                   (-5)
#define EE_SIZE_NOT_ALIGNED        (-6)
#define EE_ID_UNAVAILABLE          (-7)
#define EE_WRONG_DATA_RECORD_SIZE  (-8)
#define EE_FIRST_POWER_ON          (-9)
#define EE_CHECK_VERSION_FAULT     (-10)
#define EE_ERASE_NOT_CORRECT       (-11)
#define EE_SCANNING_COMPLETED      (-12)
#define EE_RECOVERY_ERROR_BLK0     (-13) /*AM from FE4 */
#define EE_RECOVERY_ERROR_BLK1     (-14) /*AM from FE4 */
#define EE_RECOVERY_ERROR_ALL_BLK  (-15) /*AM from FE4 */
/* error status for EE_flashErrorOnBlock_X */
#define EE_IVOR2_ERROR             (-16) /* page is declared corrupted and NOT READABLE*/
#define EE_IVOR2_RUNTIME_READ      (-17) /* Ivor2 not in startup controlled conditions*/
#define EE_DO_COPY_FROM_OTHER_PAGE (-18) /* Page erased by IVOR2 (EE reading error)
                                              * is setted on checkFaultyAddrFromLastIvor2() at startup
                                              * and become NO_ERROR or EE_IVOR2_ERROR in EE_corruptedSetup
                                              * if copy has been performed without errors or not 
                                              */


/* EE task table definitions */
#define EE_NO_EVENT                (0)
#define EE_END_OF_POWERLATCH       (1)
#define EE_ECU_OFF_NO_PWLATCH      (2)
#define EE_BEFORE_SAF23_PWROFF     (3)
#define EE_BEFORE_SW_RESET_IVOR    (4)
#define EE_BEFORE_SW_RESET_SAF2    (5)
#define EE_AFTER_KWP2000_SW_DWLOAD (6)
#define EE_INVALIDATE_ADPT_PARAMS  (7)
#define EE_WRITE_ECU_DATA          (8)
#define EE_WRITE_SUPPL_DATA        (9)
#define EE_INVALIDATE_DIAG         (10)
#define EE_PWON_AFTER_RESET_CORE   (11)
#define EE_WAITING_FOR_SMP_RESET   (12)
#define EE_WRITE_CVN               (13)

    
    
#define EE_ID0   0
#define EE_ID1   1
#define EE_ID2   2
#define EE_ID3   3
#define EE_ID4   4
#define EE_ID5   5
#define EE_ID6   6
#define EE_ID7   7
#define EE_ID8   8
#define EE_ID9   9
#define EE_ID10 10
    
#define EE_PAGE_0   (0)
#define EE_PAGE_1   (1)
    
#define FLASH_NOT_BLOCKING  (0)
#define FLASH_BLOCKING      (1)

#define EE_DO_NOT_CHECK_VERSION 0x00
#define EE_CHECK_VERSION        0x01

#define EE_ERASE_RETRY            (3)

#define EE_SYNCH_WORD           (0x0F0F0F0F)
#define EE_PADDING_WORDS        (1)

#define EE_RECOVERY_BLK0  (1) /*AM from FE4 */
#define EE_RECOVERY_BLK1  (2) /*AM from FE4 */
#define EE_ERASE_ALL      (3) /*AM from FE4 */


/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
#define EEPROM_START() ((uint32_t)&__EEPROM_START)
#define EEPROM_END()   ((uint32_t)&__EEPROM_END)
#define EEPROM_SIZE()   (EEPROM_END()-EEPROM_START())
        
#define EEPROM_BLK0_START()       ((uint32_t)&__EEPROM_START)
#define EEPROM_BLK0_END()         ((uint32_t)(&__EEPROM_START) + (((uint32_t)(&__EEPROM_SIZE)) >>1) )
#define EEPROM_BLK1_START()       ((uint32_t)(&__EEPROM_START) + (((uint32_t)(&__EEPROM_SIZE)) >>1) )
#define EEPROM_BLK1_HALF_BLOCK()  (EEPROM_BLK1_START() + (((uint32_t)(&__EEPROM_SIZE)) >>2) )
#define EEPROM_BLK1_END()         ((uint32_t)&__EEPROM_END)


/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef uint32_t EE_checksum_t;




typedef struct EEBlockDescription
{
  uint32_t       EEsynchWord;
  uint32_t       EEid;
  uint32_t       EEsize;
  uint32_t       padding[EE_PADDING_WORDS];
  char           EEcheckVersion[sizeof(uint32_t)];
  EE_checksum_t  EEheaderChecksum;
  uint32_t       EElast;
  EE_checksum_t  EEdataChecksum;
 }t_EEBlockDescriptionVar __attribute__ ((aligned(32)));

extern int16_t (*EE_SetupToDo)(void);

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_t EE_TaskToExec;
extern bool EE_ConfigStatus;

extern int8_t EE_flashErrorOnBlock0;
extern int8_t EE_flashErrorOnBlock1;
extern uint8_t EE_errorOnBlock0;
extern uint8_t EE_errorOnBlock1;




/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * EE_Setup - This method initializes flash simulated EEPROM at system startup
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t EE_Setup(void);


/*--------------------------------------------------------------------------*
 * EE_CorruptedSetup - error detection by IVOR2 (EE reading error)
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t EE_CorruptedSetup(void); 

/*--------------------------------------------------------------------------*
 * EE_ReadRecord - This method reads the content of EEPROM ID
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t EE_ReadRecord(uint8_t *dataRecord, 
                      uint16_t dataRecordSize, 
                      uint16_t ID, 
                      uint8_t checkVersion);


/*--------------------------------------------------------------------------*
 * EE_UpdateRecord - This method writes the content of EEPROM ID
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t EE_UpdateRecord(uint8_t *dataRecord,  
                        uint16_t dataRecordSize,
                        uint16_t ID);


/*--------------------------------------------------------------------------*
 * EE_InvalidateRecord - This method invalidates the content of EEPROM ID
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t EE_InvalidateRecord(uint16_t ID);

/*--------------------------------------------------------------------------*
 * EE_Clean - This method make a full erase operation of EEPROM 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t EE_Clean(void); 


/*--------------------------------------------------------------------------*
 * EE_Flush - This method writes the content of EEPROM ID
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t EE_Flush(uint8_t EEPROM_Page,uint8_t blockingStatus); /*AM from FE4 */

#endif  /*  _EE_H_ */
