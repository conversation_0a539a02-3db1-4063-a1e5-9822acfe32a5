/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

/*! \mainpage Flash

\section intro Introduction
\brief   FLASH Sw module provides an interface to manage freescale's flash drivers

 */
#ifdef  _BUILD_FLASH_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "Flash.h"
#include "ssd_types.h"

#pragma ghs startnomisra
#pragma ghs nowarning 550 /* warning #550-D: variable  "source_on_BK1A2" was set but never used */

/*!
\defgroup PublicVariables Public Variables 
\sgroup
 */
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// Global variable for configuration status
uint16_T FLASH_ConfigStatus;


#if (CODE_FLASH_BANK_ARRAY > 1)

static int8_T used_bank0_array0 = 0;
static int8_T used_bank1_array1 = 0;
static int8_T used_bank1_array2 = 0;

static uint32_T source_on_BK0A0 __attribute__ ((aligned(32)));
static uint32_T source_on_BK1A1 __attribute__ ((aligned(32)));
static uint32_T source_on_BK1A2 __attribute__ ((aligned(32)));

#endif

/*!\egroup*/

/*!
\defgroup PrivateVariables Private Variables 
\sgroup
 */
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/
/// Global variable for code flash bank configuration
#if (USE_BANK0_ARRAY0==1)
SSD_CONFIG ssdConfig_BK0A0  = {
        C90FL_REG_BASE_BK0A0,       /* control register base */
        MAIN_ARRAY_BASE_BK0A0,      /* base of main array */
        0,                          /* size of main array */
        SHADOW_ROW_BASE_BK0A0,      /* base of shadow row */
        SHADOW_ROW_SIZE_BK0A0,      /* size of shadow row */
        0,                          /* block number in low address space */
        0,                          /* block number in middle address space */
        0,                          /* block number in high address space */
        FLASH_PAGE_SIZE_BK0A0,
        FALSE                       /* debug mode selection */
};
#endif


#if (CODE_FLASH_BANK_ARRAY > 1)

#if (USE_BANK0_ARRAY0==1)
SSD_CFG_ADD ssdCfg_addBK0A0 = {
        MAIN_ARRAY_BASE_BK0A0,           /* control register base */
        MAIN_ARRAY_BASE_BK1A1,
        0
};
#endif

#if (USE_BANK1_ARRAY1==1)
SSD_CONFIG ssdConfig_BK1A1 = {
        C90FL_REG_BASE_BK1A1,       /* control register base */
        MAIN_ARRAY_BASE_BK1A1,      /* base of main array */
        0,                          /* size of main array */
        SHADOW_ROW_BASE_BK1A1,      /* base of shadow row */
        SHADOW_ROW_SIZE_BK1A1,      /* size of shadow row */
        0,                          /* block number in low address space */
        0,                          /* block number in middle address space */
        0,                          /* block number in high address space */
        FLASH_PAGE_SIZE_BK1A1,
        FALSE                       /* debug mode selection */
};

SSD_CFG_ADD ssdCfg_addBK1A1 = {
        MAIN_ARRAY_BASE_BK1A1,           /* H7F control register base */
        MAIN_ARRAY_BASE_BK1A2,
        0
};
#endif

#if (USE_BANK1_ARRAY2==1)
SSD_CONFIG ssdConfig_BK1A2 = {
        C90FL_REG_BASE_BK1A2,       /* control register base */
        MAIN_ARRAY_BASE_BK1A2,      /* base of main array */
        0,                          /* size of main array */
        SHADOW_ROW_BASE_BK1A2,      /* base of shadow row */
        SHADOW_ROW_SIZE_BK1A2,      /* size of shadow row */
        0,                          /* block number in low address space */
        0,                          /* block number in middle address space */
        0,                          /* block number in high address space */
        FLASH_PAGE_SIZE_BK1A2,
        FALSE                       /* debug mode selection */
};

SSD_CFG_ADD ssdCfg_addBK1A2 = {
        MAIN_ARRAY_BASE_BK1A2,           /* control register base */
        (OFFSET_HIGH_BLOCK7+HIGH_BLOCK7_SIZE),
        0
};
#endif

#endif


#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
/// Global variable for data flash bank configuration
static SSD_CONFIG ssdConfigDataFlash  = 
{
        DATA_C90FL_REG_BASE,        /* control register base */
        DATA_MAIN_ARRAY_BASE,       /* base of main array */
        0,                          /* size of main array */
        0,                          /* base of shadow row */
        0,                          /* size of shadow row */
        0,                          /* block number in low address space */
        0,                          /* block number in middle address space */
        0,                          /* block number in high address space */
        DATA_FLASH_PAGE_SIZE,
        FALSE                       /* debug mode selection */
};
#endif
#endif
/*!\egroup*/


/*!
\defgroup PublicFunctions Public Functions 
\sgroup
 */
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   FLASH_Config
//
//   Description:    
/*! \brief Function for configuration of FLASH
 */
//
//  Parameters and Returns:
/*! 
\returns  int16_T error code
 */
//  Notes:        
/*!
It uses the configuration parameters indicated into the file FLASH.cfg
 */
/**************************************************************************/
int16_T FLASH_Config (void) 
{
    register uint32_T returnCode = C90FL_OK ;     /* return code */  

    if(FLASH_ConfigStatus==0) 
    {
        FLASH_ConfigStatus=1;

#if (USE_BANK0_ARRAY0==1)
        returnCode = FlashInit( &ssdConfig_BK0A0 );
        if ( (C90FL_OK != returnCode))
        {
            return returnCode;
        }

        /*==================== Lock to Protect Shadow Row ====================*/
        returnCode = SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_PRIMARY, 1, FLASH_LMLR_PASSWORD );
        if ( C90FL_OK != returnCode )
        {
            return returnCode;
        }

        returnCode = SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_SECONDARY, 1, FLASH_SLMLR_PASSWORD );
        if ( C90FL_OK != returnCode )
        {
            return returnCode;
        }  
#endif
#if (FLASH_TYPE==C90LC)
#if (CODE_FLASH_BANK_ARRAY == 3)
#if (USE_BANK1_ARRAY1==1)
        returnCode = FlashInit( &ssdConfig_BK1A1 );
        if ( (C90FL_OK != returnCode))
        {
            return returnCode;
        }
#endif
#if (USE_BANK1_ARRAY2==1)
        returnCode = FlashInit( &ssdConfig_BK1A2 );
        if ( (C90FL_OK != returnCode))
        {
            return returnCode;
        }
#endif     
#endif
#ifdef DATA_FLASH_BANK
        /* configure data FLASH too */
        returnCode = FlashInit( &ssdConfigDataFlash );
        if ( (C90FL_OK != returnCode))
        {
            return returnCode;
        }
#endif
#endif
    } 
    else
    {
        returnCode = (uint32_T)PERIPHERAL_ALREADY_CONFIGURED; 
    }

    return (int16_T)returnCode;
}


/***************************************************************************/
//   Function    :   FLASH_Erase
//
//   Description:    
/*! \brief This function erases the memory blocks which contain the range address from "address" to  "address" + "size"
 */
//
//  Parameters and Returns:
/*! 
\param _address  start address of the range to be erased 
\param _size    size of the range to be erased 
\param callback  address of void callback function pointer
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_Erase ( uint32_T _address, uint32_T _size, void(*callback)(void))
{
    uint32_T blkslctLow, blkslctMiddle, blkslctHigh;
    uint32_T returnCode = NO_ERROR;
    uint8_T flashBank;
    PSSD_CONFIG pSSDConfig;

    returnCode = FlashCheckFlashBank(_address, _size, &flashBank, &pSSDConfig);

    if (returnCode == NO_ERROR)
    {
        switch (flashBank)
        {
        case FLASH_CODE_BANK:
            FlashCheckMemIndex(_address, _size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
#if (CODE_FLASH_BANK_ARRAY > 1)
            returnCode = Flash_CheckBankArray(_address, _size, 0 , blkslctLow, blkslctMiddle, blkslctHigh);
            if (returnCode!= NO_ERROR){
                return returnCode;
            }
#endif
            break;
#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
        case FLASH_DATA_BANK:
            FlashDataCheckMemIndex(_address, _size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
            break;
#endif
#endif                
        default:
            break;
        }

#if (CODE_FLASH_BANK_ARRAY > 1)    

#if (USE_BANK0_ARRAY0==1)
        if(used_bank0_array0)
        {
            /* unlock blocks that are going to be erased */
            returnCode = FlashUnlockBlocks(&ssdConfig_BK0A0, blkslctLow, blkslctMiddle, 0);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            returnCode = FlashErase( &ssdConfig_BK0A0, 0, blkslctLow, blkslctMiddle, 0, callback );
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }
            /* relock all unlocked blocks */
            returnCode = FlashRelockBlocks(&ssdConfig_BK0A0, blkslctLow, blkslctMiddle, 0);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

        }
#endif    
#if (USE_BANK1_ARRAY1==1)    
        if(used_bank1_array1)
        {
            /* unlock blocks that are going to be erased */
            returnCode = FlashUnlockBlocks(&ssdConfig_BK1A1, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* start erasing... */
            returnCode = FlashErase( &ssdConfig_BK1A1, 0, 0, 0, blkslctHigh, callback );
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* relock all unlocked blocks */
            returnCode = FlashRelockBlocks(&ssdConfig_BK1A1, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

        }
#endif    
#if (USE_BANK1_ARRAY2==1)     
        if(used_bank1_array2)
        {
            /* unlock blocks that are going to be erased */
            returnCode = FlashUnlockBlocks(&ssdConfig_BK1A2, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* start erasing... */
            returnCode = FlashErase( &ssdConfig_BK1A2, 0, 0, 0, (blkslctHigh>>4), callback );
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* relock all unlocked blocks */
            returnCode = FlashRelockBlocks(&ssdConfig_BK1A2, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

        }
#endif 
#else
        /* unlock blocks that are going to be erased */
        returnCode = FlashUnlockBlocks(pSSDConfig, blkslctLow, blkslctMiddle, blkslctHigh);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }

        /* start erasing... */
        returnCode = FlashErase( pSSDConfig, 0, blkslctLow, blkslctMiddle, blkslctHigh, callback );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }

        /* relock all unlocked blocks */
        returnCode = FlashRelockBlocks(pSSDConfig, blkslctLow, blkslctMiddle, blkslctHigh);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
#endif
    }
    return returnCode;
}


/***************************************************************************/
//   Function    :   FLASH_BlankCheck
//
//   Description:    
/*! \brief This function checks if the range memory from "_address" to  "_address" + "_size" is blank
 */
//
//  Parameters and Returns:
/*! 
\param _address  start address of the range to be checked 
\param _size    size of the range to be checked 
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_BlankCheck ( uint32_T _address, uint32_T _size )
{
    uint32_T returnCode = C90FL_OK;
    uint32_T failAddress;          /* save the failed address in flash */
    uint64_T failData;             /* save the failed data in flash */
    uint8_T flashBank;
    PSSD_CONFIG pSSDConfig;
    uint32_T blkslctLow, blkslctMiddle, blkslctHigh;    
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFF;

    returnCode = FlashCheckFlashBank(_address, _size, &flashBank, &pSSDConfig);

    if (returnCode == NO_ERROR)
    {
        switch (flashBank)
        {
        case FLASH_CODE_BANK:
            FlashCheckMemIndex(_address, _size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
#if (CODE_FLASH_BANK_ARRAY > 1)
            returnCode = Flash_CheckBankArray(_address, _size, 0, blkslctLow, blkslctMiddle, blkslctHigh);
            if (returnCode!= NO_ERROR){
                return returnCode;
            }
#endif
            break;
#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
        case FLASH_DATA_BANK:
            FlashDataCheckMemIndex(_address, _size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
            break;
#endif
#endif                
        default:
            break;
        }

#if (CODE_FLASH_BANK_ARRAY > 1)
#if (USE_BANK0_ARRAY0==1)
        if(used_bank0_array0)
        {
            returnCode = BlankCheck( &ssdConfig_BK0A0,ssdCfg_addBK0A0.start_Add_Block, ssdCfg_addBK0A0.size_Block, &failAddress, &failData, callback /* (void (*)(void))NULL_CALLBACK*/ );
        }
#endif
#if (USE_BANK1_ARRAY1==1)    
        if(used_bank1_array1)
        {
            returnCode |= BlankCheck( &ssdConfig_BK1A1,ssdCfg_addBK1A1.start_Add_Block, ssdCfg_addBK1A1.size_Block, &failAddress, &failData, callback /* (void (*)(void))NULL_CALLBACK*/ );
        }
#endif
#if (USE_BANK1_ARRAY2==1)     
        if(used_bank1_array2)
        {
            returnCode |= BlankCheck( &ssdConfig_BK1A2,ssdCfg_addBK1A2.start_Add_Block, ssdCfg_addBK1A2.size_Block, &failAddress, &failData, callback /* (void (*)(void))NULL_CALLBACK*/ );
        }
#endif
#else
        returnCode = BlankCheck( pSSDConfig, _address, _size, &failAddress, &failData, callback /* (void (*)(void))NULL_CALLBACK*/ );
#endif


    }

    return (int16_T)returnCode;
}


/***************************************************************************/
//   Function    :   FLASH_Program
//
//   Description:    
/*! \brief This function programs the specified area with the provided source data
 */
//
//  Parameters and Returns:
/*! 
\param  dest  destination address to be programmed 
\param  size    size of the flash region to be programmed
\param  source    source program buffer address
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_Program ( uint32_T dest, uint32_T size, uint32_T source)
{
    uint32_T blkslctLow, blkslctMiddle, blkslctHigh;
    uint32_T returnCode = NO_ERROR;
    uint8_T flashBank;
    PSSD_CONFIG pSSDConfig;
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFF;

    returnCode = FlashCheckFlashBank(dest, size, &flashBank, &pSSDConfig);

    if (returnCode == NO_ERROR)
    {
        switch (flashBank)
        {
        case FLASH_CODE_BANK:
            FlashCheckMemIndex(dest, size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
#if (CODE_FLASH_BANK_ARRAY > 1)
            returnCode = Flash_CheckBankArray(dest, size, source, blkslctLow, blkslctMiddle, blkslctHigh);
            if (returnCode!= NO_ERROR){
                return returnCode;
            }
#endif
            break;
#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
        case FLASH_DATA_BANK:
            FlashDataCheckMemIndex(dest, size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
            break;
#endif
#endif                
        default:
            break;
        }

#if (CODE_FLASH_BANK_ARRAY > 1)
#if (USE_BANK0_ARRAY0==1)
        if(used_bank0_array0)
        {
            /* unlock blocks that are going to be erased */
            returnCode = FlashUnlockBlocks(&ssdConfig_BK0A0, blkslctLow, blkslctMiddle, 0);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }
            /* start FLASH programming... */
            returnCode = FlashProgram( &ssdConfig_BK0A0, ssdCfg_addBK0A0.start_Add_Block , ssdCfg_addBK0A0.size_Block , source_on_BK0A0, callback /* (void (*)(void))NULL_CALLBACK*/ );
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* relock all unlocked blocks */
            returnCode = FlashRelockBlocks(&ssdConfig_BK0A0, blkslctLow, blkslctMiddle, 0);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

        }
#endif
#if (USE_BANK1_ARRAY1==1)
        if(used_bank1_array1)
        {
            /* unlock blocks that are going to be erased*/
            returnCode = FlashUnlockBlocks(&ssdConfig_BK1A1, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* start FLASH programming... */
            returnCode = FlashProgram( &ssdConfig_BK1A1,  ssdCfg_addBK1A1.start_Add_Block, ssdCfg_addBK1A1.size_Block, source_on_BK1A1, callback /* (void (*)(void))NULL_CALLBACK*/ );
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* relock all unlocked blocks */
            returnCode = FlashRelockBlocks(&ssdConfig_BK1A1, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

        }
#endif
#if (USE_BANK1_ARRAY2==1)    
        if(used_bank1_array2)
        {
            /* unlock blocks that are going to be erased */
            returnCode = FlashUnlockBlocks(&ssdConfig_BK1A2, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* start FLASH programming... */
            returnCode = FlashProgram( &ssdConfig_BK1A2, ssdCfg_addBK1A2.start_Add_Block, ssdCfg_addBK1A2.size_Block, source_on_BK1A2, callback /* (void (*)(void))NULL_CALLBACK*/);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }

            /* relock all unlocked blocks */
            returnCode = FlashRelockBlocks(&ssdConfig_BK1A2, 0, 0, blkslctHigh);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }
        }
#endif

#else
        /* unlock blocks that are going to be erased */
        returnCode = FlashUnlockBlocks(pSSDConfig, blkslctLow, blkslctMiddle, blkslctHigh);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }

        /* start FLASH programming... */
        returnCode = FlashProgram( pSSDConfig, dest, size, source, callback /* (void (*)(void))NULL_CALLBACK*/ );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }

        /* relock all unlocked blocks */
        returnCode = FlashRelockBlocks(pSSDConfig, blkslctLow, blkslctMiddle, blkslctHigh);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
#endif
    }

    return returnCode;
}

/***************************************************************************/
//   Function    :   FLASH_ProgramVerify
//
//   Description:    
/*! \brief This function programs checks if a programmed flash range matches the corresponding source data buffer
 */
//
//  Parameters and Returns:
/*! 
\param  dest  destination address to be verified in flash memory 
\param  size    size of the flash region to be verify
\param  source    verify source buffer address
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_ProgramVerify ( uint32_T dest, uint32_T size, uint32_T source)
{

    int16_T returnCode = C90FL_OK;
    uint32_T failAddress;          /* save the failed address in flash */
    uint64_T failData;             /* save the failed data in flash */
    uint64_T failSource;           /* save the failed data in source buffer */
    uint8_T flashBank;
    PSSD_CONFIG pSSDConfig;    
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFF;
    uint32_T blkslctLow, blkslctMiddle, blkslctHigh;

    returnCode = FlashCheckFlashBank(dest, size, &flashBank, &pSSDConfig);

    if (returnCode == NO_ERROR)
    {
        switch (flashBank)
        {
        case FLASH_CODE_BANK:
            FlashCheckMemIndex(dest, size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
#if (CODE_FLASH_BANK_ARRAY > 1)
            returnCode = Flash_CheckBankArray(dest, size, source, blkslctLow, blkslctMiddle, blkslctHigh);
            if (returnCode!= NO_ERROR){
                return returnCode;
            }
#endif
            break;
#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
        case FLASH_DATA_BANK:
            FlashDataCheckMemIndex(dest, size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
            break;
#endif
#endif                
        default:
            break;
        }

#if (CODE_FLASH_BANK_ARRAY > 1)
#if (USE_BANK0_ARRAY0==1)
        if(used_bank0_array0)
        {
            returnCode = ProgramVerify( &ssdConfig_BK0A0,ssdCfg_addBK0A0.start_Add_Block, ssdCfg_addBK0A0.size_Block, source_on_BK0A0, &failAddress, &failData, &failSource, callback /* (void (*)(void))NULL_CALLBACK*/);
        }
#endif
#if (USE_BANK1_ARRAY1==1)
        if(used_bank1_array1)
        {
            returnCode |= ProgramVerify( &ssdConfig_BK1A1,ssdCfg_addBK1A1.start_Add_Block, ssdCfg_addBK1A1.size_Block, source_on_BK1A1, &failAddress, &failData, &failSource, callback /* (void (*)(void))NULL_CALLBACK*/);
        }
#endif
#if (USE_BANK1_ARRAY2==1)    
        if(used_bank1_array2)
        {
            returnCode |= ProgramVerify( &ssdConfig_BK1A2,ssdCfg_addBK1A2.start_Add_Block, ssdCfg_addBK1A2.size_Block, source_on_BK1A2, &failAddress, &failData, &failSource, callback /* (void (*)(void))NULL_CALLBACK*/);
        }
#endif              
#else
        returnCode = ProgramVerify( pSSDConfig, dest, size, source, &failAddress, &failData, &failSource, callback /* (void (*)(void))NULL_CALLBACK*/);
#endif
    }

    return returnCode;
}

/***************************************************************************/
//   Function    :   FLASH_CheckSum
//
//   Description:    
/*! \brief This function performs a 32-bit sum over the specified flash memory range without carry
 */
//
//  Parameters and Returns:
/*! 
\param  dest  destination address to be summed in flash memory 
\param  size    size of the flash region to check sum
\param  sum    return sum value
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_CheckSum ( uint32_T dest, uint32_T size, uint32_T *sum)
{
    uint32_T returnCode = C90FL_OK;
    uint8_T flashBank;
    PSSD_CONFIG pSSDConfig;    
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFF;
    uint32_T blkslctLow, blkslctMiddle, blkslctHigh;
    uint32_T sum_b0a0=0;
    uint32_T sum_b1a1=0;
    uint32_T sum_b1a2=0;


    returnCode = FlashCheckFlashBank(dest, size, &flashBank, &pSSDConfig);

    if (returnCode == NO_ERROR)
    {
        switch (flashBank)
        {
        case FLASH_CODE_BANK:
            FlashCheckMemIndex(dest, size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
#if (CODE_FLASH_BANK_ARRAY > 1)
            returnCode = Flash_CheckBankArray(dest, size, 0, blkslctLow, blkslctMiddle, blkslctHigh);
            if (returnCode!= NO_ERROR){
                return returnCode;
            }
#endif
            break;
#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
        case FLASH_DATA_BANK:
            FlashDataCheckMemIndex(dest, size, &blkslctLow,&blkslctMiddle,&blkslctHigh);
            break;
#endif
#endif                
        default:
            break;
        }

#if (CODE_FLASH_BANK_ARRAY > 1)
#if (USE_BANK0_ARRAY0==1)
        if(used_bank0_array0)
        {
            returnCode = CheckSum( &ssdConfig_BK0A0,ssdCfg_addBK0A0.start_Add_Block, ssdCfg_addBK0A0.size_Block, &sum_b0a0, callback /* (void (*)(void))NULL_CALLBACK*/);
        }
#endif
#if (USE_BANK1_ARRAY1==1)
        if(used_bank1_array1)
        {
            returnCode |= CheckSum( &ssdConfig_BK1A1,ssdCfg_addBK1A1.start_Add_Block, ssdCfg_addBK1A1.size_Block, &sum_b1a1, callback /* (void (*)(void))NULL_CALLBACK*/);
        }
#endif
#if (USE_BANK1_ARRAY2==1)    
        if(used_bank1_array2)
        {
            returnCode |= CheckSum( &ssdConfig_BK1A2,ssdCfg_addBK1A2.start_Add_Block, ssdCfg_addBK1A2.size_Block, &sum_b1a2, callback /* (void (*)(void))NULL_CALLBACK*/);
        }
#endif
        *sum = (sum_b0a0 + sum_b1a1 + sum_b1a2);
#else
        returnCode = CheckSum( pSSDConfig, dest, size, sum, callback /* (void (*)(void))NULL_CALLBACK*/);
#endif        


    }

    return (int16_T)returnCode;
}

/***************************************************************************/
//   Function    :   FLASH_Suspend
//
//   Description:    
/*! \brief This function performs a 32-bit sum over the specified flash memory range without carry
 */
//
//  Parameters and Returns:
/*! 
\param  pSSDConfig  pointer to the SSD Configuration Structure 
\param  suspendState0    the suspend state after the function being called
\param  suspendFlag0    return whether the suspended operation, if there is any, is suspended by this call
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_Suspend ( PSSD_CONFIG pSSDConfig, uint8_T *suspendState0, uint8_T *suspendFlag0)
{
    uint32_T returnCode = C90FL_OK;

    returnCode = FlashSuspend (pSSDConfig, suspendState0, suspendFlag0);

    return (int16_T)returnCode;
}

/***************************************************************************/
//   Function    :   FLASH_Resume
//
//   Description:    
/*! \brief This function checks if there is any suspended erase or program operation and will resume the suspended operation if there is any
 */
//
//  Parameters and Returns:
/*! 
\param  pSSDConfig  pointer to the SSD Configuration Structure 
\param  resumeState0    the resume state after the function being called
\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T FLASH_Resume ( PSSD_CONFIG pSSDConfig, uint8_T *resumeState0 )
{
    uint32_T returnCode = C90FL_OK;

    returnCode = FlashResume (pSSDConfig, resumeState0);

    return (int16_T)returnCode;
}

/*!\egroup*/



/*!
\defgroup PrivateFunctions Private Functions 
\sgroup
 */
/*==================================================================================================
                                       PRIVATE FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   FlashCheckFlashBank
//
//   Description:    
/*! \brief This function Check if address range is in code or data FLASH
 */
//
//  Parameters and Returns:
/*!  
\param  start start address to be checked
\param  size  size of the flash region to be checked
\param  flashBank   the flash bank which contains the range address
\param  pSSDConfig  pointer to the SSD Configuration Structure of flash bank which contains the range address

\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
static int16_T FlashCheckFlashBank(uint32_T start, uint32_T size, uint8_T *flashBank, PSSD_CONFIG *pSSDConfig)
{
    uint32_T end = start + size;
    int16_T returnCode = NO_ERROR;


    if  ((start <= (LAST_CODE_FLASH_ADDRESS)) && (end <= (LAST_CODE_FLASH_ADDRESS)))
    {
        /* code FLASH */
        *flashBank = FLASH_CODE_BANK;
        *pSSDConfig = &ssdConfig_BK0A0;
    }
#if (FLASH_TYPE==C90LC)
#ifdef DATA_FLASH_BANK
    else if ((start >= DATA_OFFSET_LOW_BLOCK0) && (end <= LAST_DATA_FLASH_ADDRESS))
    {
        /* data FLASH */
        *flashBank = FLASH_DATA_BANK;
        *pSSDConfig = &ssdConfigDataFlash;
    }
#endif
#endif
    else
    {
        /* error, out of range */
        returnCode = ARG_ERROR;
        *pSSDConfig = NULL;
    }

    return returnCode;
}

/***************************************************************************/
//   Function    :   FlashDataCheckMemIndex
//
//   Description:    
/*! \brief This function get Data FLASH blocks from address/size
 */
//
//  Parameters and Returns:
/*!  
\param  start start address to be checked
\param  size  size of the flash region to be checked
\param  blkslctLow    bit-mapped value to indicate the array blocks in low address space belonging to the range address
\param  blkslctMiddle bit-mapped value to indicate the array blocks in middle address space belonging to the range address
\param  blkslctHigh   bit-mapped value to indicate the array blocks in high address space belonging to the range address

\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
#if (CODE_FLASH_BANK_ARRAY > 1)
static int16_T Flash_CheckBankArray(uint32_T start, uint32_T size, uint32_T source, uint32_T blkslctLow,uint32_T blkslctMiddle,uint32_T blkslctHigh)
{
    int16_T returnCode = NO_ERROR ;     /* return code of low level primitives */
    uint32_T end = start + size;

    uint8_T blkslctHigh_l = (uint8_T)blkslctHigh;
    uint8_T blkslctHigh_l_low ;
    uint8_T blkslctHigh_l_hi ;
    uint8_T AddressSpaceMask = 0;

    used_bank0_array0 = 0 ;
    used_bank1_array1 = 0 ;
    used_bank1_array2 = 0 ;

    source_on_BK0A0 = 0 ;
    source_on_BK1A1 = 0 ;
    source_on_BK1A2 = 0 ;

    if((blkslctLow!=0)||(blkslctMiddle!=0))
    {
        used_bank0_array0 = 1;
    }

    blkslctHigh_l_low = (blkslctHigh_l<<4) ;
    if((blkslctHigh_l_low)!=0)
    {
        used_bank1_array1 = 1 ;
    }

    blkslctHigh_l_hi = (blkslctHigh_l>>4) ;
    if((blkslctHigh_l_hi)!=0)
    {
        used_bank1_array2 = 1 ;
    }

    AddressSpaceMask = (used_bank1_array2<<2)|(used_bank1_array1<<1)|(used_bank0_array0);

    switch(AddressSpaceMask)
    {
#if (USE_BANK0_ARRAY0==1)
    case 1 : /* Bank0Array0 */
        ssdCfg_addBK0A0.start_Add_Block = start;
        ssdCfg_addBK0A0.size_Block = size;
        source_on_BK0A0 = source ;
        break;
#endif
#if (USE_BANK1_ARRAY1==1)
    case 2 : /* Bank1Array1 */
        ssdCfg_addBK1A1.start_Add_Block = start;
        ssdCfg_addBK1A1.size_Block = size;
        source_on_BK1A1 = source ;
        break ;
#endif
#if (USE_BANK1_ARRAY2==1)
    case 4:   /* Bank1Array2 */
        ssdCfg_addBK1A2.start_Add_Block = start;
        ssdCfg_addBK1A2.size_Block = size;
        source_on_BK1A2 = source ;
        break ;
#endif
#if (USE_BANK0_ARRAY0==1)&&(USE_BANK1_ARRAY1==1)
    case 3 :  /* Bank0Array0 , Bank1Array1 */
        ssdCfg_addBK0A0.start_Add_Block =  start;
        ssdCfg_addBK0A0.size_Block      = (OFFSET_HIGH_BLOCK0-start);
        source_on_BK0A0 = source;
        ssdCfg_addBK1A1.start_Add_Block = OFFSET_HIGH_BLOCK0 ;
        ssdCfg_addBK1A1.size_Block      = (end - OFFSET_HIGH_BLOCK0)  ;
        source_on_BK1A1 = (source+( OFFSET_HIGH_BLOCK0-start)) ;
        break;
#endif
#if (USE_BANK1_ARRAY1==1)&&(USE_BANK1_ARRAY2==1)        
    case 6 :   /* Bank1Array1 , Bank1Array2  */
        ssdCfg_addBK1A1.start_Add_Block =  start;
        ssdCfg_addBK1A1.size_Block      = (OFFSET_HIGH_BLOCK4-start);
        source_on_BK1A1 = source ;
        ssdCfg_addBK1A2.start_Add_Block =  OFFSET_HIGH_BLOCK4 ;
        ssdCfg_addBK1A2.size_Block      = (end - OFFSET_HIGH_BLOCK4)  ;
        source_on_BK1A2 = (source+( OFFSET_HIGH_BLOCK4-start));
        break;
#endif
#if (USE_BANK0_ARRAY0==1)&&(USE_BANK1_ARRAY1==1)&&(USE_BANK1_ARRAY2==1)        
    case 7 :  /* Bank0Array0 , Bank1Array1, Bank1Array2  */
        ssdCfg_addBK0A0.start_Add_Block =  start;
        ssdCfg_addBK0A0.size_Block      =  (OFFSET_HIGH_BLOCK0-start);
        source_on_BK0A0 = source ;
        ssdCfg_addBK1A1.start_Add_Block =  OFFSET_HIGH_BLOCK0;
        ssdCfg_addBK1A1.size_Block      =  (OFFSET_HIGH_BLOCK4-OFFSET_HIGH_BLOCK0);
        source_on_BK1A1 = (source+( OFFSET_HIGH_BLOCK0-start));
        ssdCfg_addBK1A2.start_Add_Block =  OFFSET_HIGH_BLOCK4 ;
        ssdCfg_addBK1A2.size_Block      =  (end - OFFSET_HIGH_BLOCK4)  ;
        source_on_BK1A2 = (source+OFFSET_HIGH_BLOCK4-start) ;
        break;
#endif        
    default:
        returnCode = ERROR_BAD_ADDRESS ;
        break;

    }

    return returnCode;
}

#endif
/***************************************************************************/
//   Function    :   FlashDataCheckMemIndex
//
//   Description:    
/*! \brief This function get Data FLASH blocks from address/size
 */
//
//  Parameters and Returns:
/*!  
\param  start start address to be checked
\param  size  size of the flash region to be checked
\param  blkslctLow    bit-mapped value to indicate the array blocks in low address space belonging to the range address
\param  blkslctMiddle bit-mapped value to indicate the array blocks in middle address space belonging to the range address
\param  blkslctHigh   bit-mapped value to indicate the array blocks in high address space belonging to the range address

\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
#if (FLASH_TYPE==C90LC) 
#ifdef DATA_FLASH_BANK
static void FlashDataCheckMemIndex(uint32_T start, uint32_T size, uint32_T* blkslctLow, uint32_T* blkslctMiddle, uint32_T* blkslctHigh)
{
    uint32_T end = start + size;

    *blkslctLow = 0;
    *blkslctMiddle = 0;
    *blkslctHigh = 0;

    /* calculate *blkslctLow mask */
    if (start < (DATA_OFFSET_LOW_BLOCK3+DATA_LOW_BLOCK3_SIZE))
    {
        if ((start<DATA_OFFSET_LOW_BLOCK1))
        {
            *blkslctLow |= 0x1;
            start = DATA_OFFSET_LOW_BLOCK1 ;
        }

        if (((start>=DATA_OFFSET_LOW_BLOCK1) && (start<DATA_OFFSET_LOW_BLOCK2)) && (start < end))
        {
            *blkslctLow |= 0x2;
            start = DATA_OFFSET_LOW_BLOCK2 ;
        }

        if (((start>=DATA_OFFSET_LOW_BLOCK2) && (start<DATA_OFFSET_LOW_BLOCK3)) && (start < end))
        {
            *blkslctLow |= 0x4;
            start = DATA_OFFSET_LOW_BLOCK3 ;
        }

        if ((start>=DATA_OFFSET_LOW_BLOCK3) && (start < end))
        {
            *blkslctLow |= 0x8;
        }
    }
}
#endif
#endif

/***************************************************************************/
//   Function    :   FlashCheckMemIndex
//
//   Description:    
/*! \brief This function get Code FLASH blocks from address/size
 */
//
//  Parameters and Returns:
/*!  
\param  start start address to be checked
\param  size  size of the flash region to be checked
\param  blkslctLow    bit-mapped value to indicate the array blocks in low address space belonging to the range address
\param  blkslctMiddle bit-mapped value to indicate the array blocks in middle address space belonging to the range address
\param  blkslctHigh   bit-mapped value to indicate the array blocks in high address space belonging to the range address

\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
static void FlashCheckMemIndex(uint32_T start, uint32_T size, uint32_T* blkslctLow, uint32_T* blkslctMiddle, uint32_T* blkslctHigh)
{
    uint32_T end = start + size;

    *blkslctLow = 0;
    *blkslctMiddle = 0;
    *blkslctHigh = 0;

#if (USE_LOW_ADDR_SPACE == 1)
    /* calculate *blkslctLow mask */
    if (start <= LAST_LOW_ADDRESS)
    {
        if ((start<LAST_LOW_BLOCK0_ADDR))
        {
            *blkslctLow |= 0x1;
            start = LAST_LOW_BLOCK0_ADDR ;
        }
#ifdef OFFSET_LOW_BLOCK1
        if (((start>=OFFSET_LOW_BLOCK1) && (start<LAST_LOW_BLOCK1_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x2;
            start = LAST_LOW_BLOCK1_ADDR ;
        }
#endif
#ifdef OFFSET_LOW_BLOCK2
        if (((start>=OFFSET_LOW_BLOCK2) && (start<LAST_LOW_BLOCK2_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x4;
            start = LAST_LOW_BLOCK2_ADDR ;
        }
#endif
#ifdef OFFSET_LOW_BLOCK3
        if (((start>=OFFSET_LOW_BLOCK3) && (start<LAST_LOW_BLOCK3_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x8;
            start = LAST_LOW_BLOCK3_ADDR ;
        }
#endif
#ifdef OFFSET_LOW_BLOCK4
        if (((start>=OFFSET_LOW_BLOCK4) && (start<LAST_LOW_BLOCK4_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x10;
            start = LAST_LOW_BLOCK4_ADDR ;
        }
#endif
#ifdef OFFSET_LOW_BLOCK5
        if (((start>=OFFSET_LOW_BLOCK5) && (start<LAST_LOW_BLOCK5_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x20;
            start = LAST_LOW_BLOCK5_ADDR ;
        }
#endif
#ifdef OFFSET_LOW_BLOCK6        
        if (((start>=OFFSET_LOW_BLOCK6) && (start<LAST_LOW_BLOCK6_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x40;
            start = LAST_LOW_BLOCK6_ADDR ;
        }
#endif
#ifdef OFFSET_LOW_BLOCK7         
        if (((start>=OFFSET_LOW_BLOCK7) && (start<LAST_LOW_BLOCK7_ADDR)) && (start < end))
        {
            *blkslctLow |= 0x80;
        }        
#endif
        if(end <= (LAST_LOW_ADDRESS+1))
            return ;
        else
            start = LAST_LOW_ADDRESS+1;
    }
#endif

#if (USE_MID_ADDR_SPACE == 1)

    if ((start>=OFFSET_MID_BLOCK0)&&(start<=LAST_MID_ADDRESS))
    {
        if ((start<LAST_MID_BLOCK0_ADDR))
        {  
            *blkslctMiddle = 0x1 ;
            start = LAST_MID_BLOCK0_ADDR ;
        }
#ifdef OFFSET_MID_BLOCK1
        if (((start>=OFFSET_MID_BLOCK1) && (start<LAST_MID_BLOCK1_ADDR)) && (start < end))
        {
            *blkslctMiddle |= 0x2;
        }
#endif        
        if(end <= (LAST_MID_ADDRESS+1))
            return ;
        else
            start = LAST_MID_ADDRESS+1;
    }

#endif

#if (USE_HIGH_ADDR_SPACE == 1)

    if ((start>=OFFSET_HIGH_BLOCK0)&&(start<=LAST_HIGH_ADDRESS))
    {
        if ((start<LAST_HIGH_BLOCK0_ADDR))
        {
            *blkslctHigh |= 0x1;
            start = LAST_HIGH_BLOCK0_ADDR ;
        }
#ifdef OFFSET_HIGH_BLOCK1
        if (((start>=OFFSET_HIGH_BLOCK1) && (start<LAST_HIGH_BLOCK1_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x2;
            start = LAST_HIGH_BLOCK1_ADDR ;
        }
#endif
#ifdef OFFSET_HIGH_BLOCK2
        if (((start>=OFFSET_HIGH_BLOCK2) && (start<LAST_HIGH_BLOCK2_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x4;
            start = LAST_HIGH_BLOCK2_ADDR ;
        }
#endif
#ifdef OFFSET_HIGH_BLOCK3
        if (((start>=OFFSET_HIGH_BLOCK3) && (start<LAST_HIGH_BLOCK3_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x8;
            start = LAST_HIGH_BLOCK3_ADDR ;
        }
#endif
#ifdef OFFSET_HIGH_BLOCK4
        if (((start>=OFFSET_HIGH_BLOCK4) && (start<LAST_HIGH_BLOCK4_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x10;
            start = LAST_HIGH_BLOCK4_ADDR ;
        }
#endif
#ifdef OFFSET_HIGH_BLOCK5
        if (((start>=OFFSET_HIGH_BLOCK5) && (start<LAST_HIGH_BLOCK5_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x20;
            start = LAST_HIGH_BLOCK5_ADDR ;
        }
#endif
#ifdef OFFSET_HIGH_BLOCK6        
        if (((start>=OFFSET_HIGH_BLOCK6) && (start<LAST_HIGH_BLOCK6_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x40;
            start = LAST_HIGH_BLOCK6_ADDR ;
        }
#endif
#ifdef OFFSET_HIGH_BLOCK7         
        if (((start>=OFFSET_HIGH_BLOCK7) && (start<LAST_HIGH_BLOCK7_ADDR)) && (start < end))
        {
            *blkslctHigh |= 0x80;
        }        
#endif


    }
#endif
}

/***************************************************************************/
//   Function    :   FlashUnlockBlocks
//
//   Description:    
/*! \brief This function Unlock FLASH blocks for programming/erase
 */
//
//  Parameters and Returns:
/*!  
\param  pSSDConfig  pointer to the SSD Configuration Structure of flash bank which contains the blocks
\param  blkslctLow    bit-mapped value to indicate the array blocks in low address space belonging to the range address
\param  blkslctMiddle bit-mapped value to indicate the array blocks in middle address space belonging to the range address
\param  blkslctHigh   bit-mapped value to indicate the array blocks in high address space belonging to the range address

\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
static int16_T FlashUnlockBlocks (PSSD_CONFIG pSSDConfig, const uint32_T blkslctLow, const uint32_T blkslctMiddle, const uint32_T blkslctHigh)
{
    uint32_T maskblkslctLow;
    uint32_T maskblkslctMiddle;
    uint32_T maskblkslctHigh;
    boolean_T  blk_Lock_Enabled;         /* block lock enabled state */
    uint32_T blk_Lock_State;        /* block lock status to be retrieved */
#if (CODE_FLASH_BANK_ARRAY > 1) 

    uint8_T blkslctHigh_low = (blkslctHigh & 0x0F) ;
    uint8_T blkslctHigh_hi  = ((blkslctHigh & 0xF0)>>4) ;
#endif
    uint32_T returnCode = NO_ERROR;

#if (USE_HIGH_ADDR_SPACE == 1)
    if (blkslctHigh!=0){
#if (CODE_FLASH_BANK_ARRAY > 1)    

        if (blkslctHigh!=0)
        {   

            if(used_bank1_array1)
            {
                maskblkslctHigh= ~blkslctHigh_low;
                returnCode=GetLock( pSSDConfig, LOCK_HIGH, &blk_Lock_Enabled, &blk_Lock_State );
                if ( C90FL_OK != returnCode )
                {
                    return returnCode;
                }
                if ( (blkslctHigh == (blk_Lock_State & blkslctHigh)) || !(blk_Lock_State & 0x00008000))
                {
                    returnCode=SetLock( pSSDConfig, LOCK_HIGH, ((blk_Lock_State & maskblkslctHigh) | 0x00008000), FLASH_HLR_PASSWORD);
                }
                if ( C90FL_OK != returnCode )
                {
                    return returnCode;
                }
            }

            if(used_bank1_array2)
            {
                maskblkslctHigh= ~blkslctHigh_hi;
                returnCode=GetLock( pSSDConfig, LOCK_HIGH, &blk_Lock_Enabled, &blk_Lock_State );
                if ( C90FL_OK != returnCode )
                {
                    return returnCode;
                }
                if ( (blkslctHigh == (blk_Lock_State & blkslctHigh)) || !(blk_Lock_State & 0x00008000))
                {
                    returnCode=SetLock( pSSDConfig, LOCK_HIGH, ((blk_Lock_State & (maskblkslctHigh)) | 0x00008000), FLASH_HLR_PASSWORD);
                }
                if ( C90FL_OK != returnCode )
                {
                    return returnCode;
                }
            }
        }

#else
        maskblkslctHigh= ~blkslctHigh;
        returnCode=GetLock( pSSDConfig, LOCK_HIGH, &blk_Lock_Enabled, &blk_Lock_State );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        if ( (blkslctHigh == (blk_Lock_State & blkslctHigh)) || !(blk_Lock_State & 0x00008000)){
            returnCode=SetLock( pSSDConfig, LOCK_HIGH, ((blk_Lock_State & maskblkslctHigh) | 0x00008000), FLASH_HLR_PASSWORD);
        }
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
#endif
    }
#endif
#if (USE_MID_ADDR_SPACE == 1)
    if (blkslctMiddle!=0){
        maskblkslctMiddle= ~blkslctMiddle;
        returnCode=GetLock( pSSDConfig, LOCK_MID_PRIMARY, &blk_Lock_Enabled, &blk_Lock_State );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        if ( (blkslctMiddle == (blk_Lock_State & blkslctMiddle)) || !(blk_Lock_State & 0x00008000)){        
            returnCode=SetLock( pSSDConfig, LOCK_MID_PRIMARY, ((blk_Lock_State & maskblkslctMiddle) | 0x00008000), FLASH_LMLR_PASSWORD);
        }
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        returnCode=GetLock( pSSDConfig, LOCK_MID_SECONDARY, &blk_Lock_Enabled, &blk_Lock_State );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        if ( (blkslctMiddle == (blk_Lock_State & blkslctMiddle)) || !(blk_Lock_State & 0x00008000)){
            returnCode=SetLock( pSSDConfig, LOCK_MID_SECONDARY, ((blk_Lock_State & maskblkslctMiddle) | 0x00008000), FLASH_SLMLR_PASSWORD);
        }
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
    }
#endif

#if (USE_LOW_ADDR_SPACE == 1)
    if (blkslctLow!=0){
        maskblkslctLow= ~blkslctLow;
        returnCode=GetLock( pSSDConfig, LOCK_LOW_PRIMARY, &blk_Lock_Enabled, &blk_Lock_State );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        if ( (blkslctLow == (blk_Lock_State & blkslctLow)) || !(blk_Lock_State & 0x00008000)){      
            returnCode=SetLock( pSSDConfig, LOCK_LOW_PRIMARY, ((blk_Lock_State & maskblkslctLow) | 0x00008000), FLASH_LMLR_PASSWORD);
        }
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        returnCode=GetLock( pSSDConfig, LOCK_LOW_SECONDARY, &blk_Lock_Enabled, &blk_Lock_State );
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        if ( (blkslctLow == (blk_Lock_State & blkslctLow)) || !(blk_Lock_State & 0x00008000)){
            returnCode=SetLock( pSSDConfig, LOCK_LOW_SECONDARY, ((blk_Lock_State & maskblkslctLow) | 0x00008000), FLASH_SLMLR_PASSWORD);
        }
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
    }
#endif

    return (int16_T)returnCode;
}

/***************************************************************************/
//   Function    :   FlashRelockBlocks
//
//   Description:    
/*! \brief This function Relock FLASH blocks for programming/erase
 */
//
//  Parameters and Returns:
/*!  
\param  pSSDConfig  pointer to the SSD Configuration Structure of flash bank which contains the blocks
\param  blkslctLow    bit-mapped value to indicate the array blocks in low address space belonging to the range address
\param  blkslctMiddle bit-mapped value to indicate the array blocks in middle address space belonging to the range address
\param  blkslctHigh   bit-mapped value to indicate the array blocks in high address space belonging to the range address

\returns  int16_T error code
 */
//  Notes:        
/*!

 */
/**************************************************************************/
static int16_T FlashRelockBlocks(PSSD_CONFIG pSSDConfig, const uint32_T blkslctLow, const uint32_T blkslctMiddle, const uint32_T blkslctHigh)
{
    uint32_T returnCode = NO_ERROR;

#if (USE_HIGH_ADDR_SPACE == 1)
    /* relock all touched arrays */
    if (blkslctHigh!=0){
#if (CODE_FLASH_BANK_ARRAY > 1)    
        if(used_bank1_array1)
        {
            returnCode=SetLock( pSSDConfig, LOCK_HIGH, 0xFFFF, FLASH_HLR_PASSWORD);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }
        }
        if(used_bank1_array2)
        {
            returnCode=SetLock( pSSDConfig, LOCK_HIGH, 0xFFFF, FLASH_HLR_PASSWORD);
            if ( C90FL_OK != returnCode ){
                return returnCode;
            }
        }
#else
        returnCode=SetLock( pSSDConfig, LOCK_HIGH, 0xFFFF, FLASH_HLR_PASSWORD);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
#endif
    }
#endif

#if (USE_MID_ADDR_SPACE == 1)
    if (blkslctMiddle!=0){
        returnCode=SetLock( pSSDConfig, LOCK_MID_PRIMARY,0xFFFF , FLASH_LMLR_PASSWORD);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        returnCode=SetLock( pSSDConfig, LOCK_MID_SECONDARY,0xFFFF, FLASH_SLMLR_PASSWORD);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
    }
#endif
#if (USE_LOW_ADDR_SPACE == 1)
    if (blkslctLow!=0){
        returnCode=SetLock( pSSDConfig, LOCK_LOW_PRIMARY,0xFFFF, FLASH_LMLR_PASSWORD);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
        returnCode=SetLock( pSSDConfig, LOCK_LOW_SECONDARY,0xFFFF, FLASH_SLMLR_PASSWORD);
        if ( C90FL_OK != returnCode ){
            return returnCode;
        }
    }
#endif

    return (int16_T)returnCode;
}
/*!\egroup*/

#pragma ghs endnomisra

#endif //  _BUILD_FLASH_
