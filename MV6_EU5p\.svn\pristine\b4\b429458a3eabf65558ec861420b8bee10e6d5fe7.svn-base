/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include <string.h>
#include "typedefs.h"
#include "sys.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

extern const char calib_buildCode[16];

const char app_buildCode[sizeof(uint32_t)] = 
{
    0x00,0x00,0x00,0x00
};

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/

#define CALIB_APPLIINFO_LABEL_POS (0)     // blockVersionLabel position in tag
#define CALIB_APPLIINFO_LABEL_LEN (11)
#define CALIB_APPLIINFO_CHECKSUM_POS (12) // blockChecksum position in tag
#define CALIB_APPLIINFO_CHECKSUM_LEN (4)

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * app_CheckVersion - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t app_CheckVersion(void)
{
    BlockDescription* InfoPtr;
    int16_t error;

    error = -1;
    InfoPtr = (BlockDescription *) (((uint32_t)(&__CALIB_ROM_START)+(uint32_t)(&__CALIB_ROM_SIZE))-sizeof(BlockDescription));
    if ((InfoPtr->blockStatusOK == NO_ERROR) &&
        (CHECK_VALID_REGION(InfoPtr) == NO_ERROR))
    {
        error = NO_ERROR;
    }

    return error;
}

/*--------------------------------------------------------------------------*
 * calib_CheckVersion - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t calib_CheckVersion(void)           //!!PD  Da spostare nel boot
{
    BlockDescription *appliTag;
    uint8_t *calibAppInfo;
    uint8_t *s1, *s2;
    uint32_t i;
    int16_t retValue = NO_ERROR;

    appliTag = (BlockDescription *) (((uint32_t)(&__APP_START)+(uint32_t)(&__APP_SIZE))-sizeof(BlockDescription));

    calibAppInfo = (uint8_t *)calib_buildCode + CALIB_APPLIINFO_LABEL_POS;
    s1 = (uint8_t *)&appliTag->blockVersionLabel;
    s2 = (uint8_t *)calibAppInfo;
    for (i=0; (i<CALIB_APPLIINFO_LABEL_LEN) && (retValue == NO_ERROR); i++)
    {
        if (s1[i] != s2[i])
        {
            retValue = -1;
        }
    }
    if (retValue == NO_ERROR)
    {
        calibAppInfo = (uint8_t *)calib_buildCode + CALIB_APPLIINFO_CHECKSUM_POS;
        s1 = (uint8_t *)&appliTag->blockChecksum;
        s2 = (uint8_t *)calibAppInfo;
        for (i=0; (i<CALIB_APPLIINFO_CHECKSUM_LEN) && (retValue == NO_ERROR); i++)
        {
            if (s1[i] != s2[i])
            {
                retValue = -1;
            }
        }
    }
    return retValue;
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
