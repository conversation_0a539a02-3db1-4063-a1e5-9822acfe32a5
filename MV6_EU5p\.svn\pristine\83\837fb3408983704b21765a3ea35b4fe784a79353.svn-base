/*
 * File: WatPumpCtrl.h
 *
 * Real-Time Workshop code generated for Simulink model WatPumpCtrl.
 *
 * Model version                        : 1.701
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed Dec 19 09:43:59 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed Dec 19 09:43:59 2007
 */

#ifndef _RTW_HEADER_WatPumpCtrl_h_
#define _RTW_HEADER_WatPumpCtrl_h_

#ifndef _WatPumpCtrl_COMMON_INCLUDES_
# define _WatPumpCtrl_COMMON_INCLUDES_
#include <stddef.h>
#include "rtwtypes.h"
#include "rtlibsrc.h"
#include "mathlib.h"
#endif                                  /* _WatPumpCtrl_COMMON_INCLUDES_ */

#include "WatPumpCtrl_types.h"

/* Macros for accessing real-time model data structure  */

#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm) ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val) ((void) 0)
#endif

#define BKRPMWPUMP_dim                  7U

/* Block signals (auto storage) */
typedef struct {
  int16_T Memory;                       /* '<S1>/Memory' */
} BlockIO_WatPumpCtrl;

/* Block states (auto storage) for system: '<Root>' */
typedef struct {
  int16_T Memory_PreviousInput;         /* '<S1>/Memory' */
} D_Work_WatPumpCtrl;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState sf_WatPumpCtrl_Sched_ZCE[2]; /* '<S1>/WatPumpCtrl_Sched' */
} PrevZCSigStates_WatPumpCtrl;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint16_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint16_T ev_100ms;                    /* '<Root>/ev_100ms' */
} ExternalInputs_WatPumpCtrl;

/* Block signals (auto storage) */
extern BlockIO_WatPumpCtrl WatPumpCtrl_B;

/* Block states (auto storage) */
extern D_Work_WatPumpCtrl WatPumpCtrl_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_WatPumpCtrl WatPumpCtrl_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  RTW declares the memory for these signals
 * and exports their symbols.
 *
 */

extern uint16_T WatPumpDutyMax;         /* '<S11>/Conversion' */

extern uint16_T WatPumpDutyMin;         /* '<S12>/Conversion' */

extern int16_T TWaterErr;               /* '<S2>/Sum' */

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  RTW declares the memory for these states
 * and exports their symbols.
 *
 */

extern int32_T WatPumpPWMDutyHiR;       /* '<Root>/_DataStoreBlk_3' */

extern uint16_T WatPumpPWMDuty;         /* '<Root>/_DataStoreBlk_1' */

extern uint8_T FlgWatPumpCtrlOn;        /* '<Root>/_DataStoreBlk_2' */

/* External data declarations for dependent source files */

/* Zero-crossing (trigger) state */
extern PrevZCSigStates_WatPumpCtrl WatPumpCtrl_PrevZC;

/* Model entry point functions */
extern void WatPumpCtrl_initialize(boolean_T firstTime);
extern void WatPumpCtrl_step(void);

extern uint16_T BKRPMWPUMP[8];          /* WATPUMPCTRL.BKRPMWPUMP: Rpm breakpoints for duty max calculation */

extern uint8_T ENWPUMPCTRL;             /* WATPUMPCTRL.ENWPUMPCTRL: Water pump control enable flag */

extern uint16_T RPMTHRWPUMPON;          /* WATPUMPCTRL.RPMTHRWPUMPON: Rpm threshold for water control pump enable */

extern uint16_T THRTDCWATPUMP;          /* WATPUMPCTRL.THRTDCWATPUMP: CntTdcCrk threshold for water control pump enable */

extern int16_T TWATERERRDBDOWN;         /* WATPUMPCTRL.TWATERERRDBDOWN: Dead band amplitude below target */

extern int16_T TWATERERRDBUP;           /* WATPUMPCTRL.TWATERERRDBUP: Dead band amplitude above target */

extern int16_T TWATERTARGET;            /* WATPUMPCTRL.TWATERTARGET: Water temperature target */

extern uint16_T VTWPUMPDUTYMAX[8];      /* WATPUMPCTRL.VTWPUMPDUTYMAX: Duty max vector */

extern uint16_T VTWPUMPDUTYMIN[8];      /* WATPUMPCTRL.VTWPUMPDUTYMIN: Duty min vector */

extern uint16_T WPUMPCTRLKI;            /* WATPUMPCTRL.WPUMPCTRLKI: Water pump control PI integral gain */

extern uint16_T WPUMPCTRLKP;            /* WATPUMPCTRL.WPUMPCTRLKP: Water pump control PI proportional gain */

extern uint16_T WPUMPDUTYDOWN;          /* WATPUMPCTRL.WPUMPDUTYDOWN: Duty cycle below dead band */

extern uint16_T WPUMPDUTYUP;            /* WATPUMPCTRL.WPUMPDUTYUP: Duty cycle above dead band */

/* 
 * The generated code includes comments that allow you to trace directly 
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : WatPumpCtrl
 * '<S1>'   : WatPumpCtrl/WatPumpCtrl
 * '<S2>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc
 * '<S3>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_Reset
 * '<S4>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_Sched
 * '<S5>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/Above_DeadBand
 * '<S6>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/Below_DeadBand
 * '<S7>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/LookUp_IR_U16_1
 * '<S8>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/LookUp_IR_U16_2
 * '<S9>'   : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/Near_Target
 * '<S10>'  : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/PreLookUpIdSearch_U16
 * '<S11>'  : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/LookUp_IR_U16_1/Data Type Conversion Inherited3
 * '<S12>'  : WatPumpCtrl/WatPumpCtrl/WatPumpCtrl_DutyCalc/LookUp_IR_U16_2/Data Type Conversion Inherited3
 */

#endif                                  /* _RTW_HEADER_WatPumpCtrl_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
