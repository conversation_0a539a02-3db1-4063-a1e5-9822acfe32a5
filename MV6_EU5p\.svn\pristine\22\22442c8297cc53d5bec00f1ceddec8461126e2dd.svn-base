#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_SAF2MGM_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 

//(S2) Delay before sw restart [ms]
__declspec(section ".calib") uint16_T S2DELAYRESTART =     50;   //    50
//(S2) Enable safety 2 bit mask [flag]
__declspec(section ".calib") uint16_T S2ENSAF2 =   2047;   //  2047
//(S2) Increment step of S2RestartCnt [counter]
__declspec(section ".calib") uint16_T S2INCRESTARTCNT =   5000;   //  5000
//(S2) Thr on S2RestartCnt to do a safety stop engine [counter]
__declspec(section ".calib") uint16_T S2MAXRESTART =  25000;   // 25000
//(S2) Disable S2 reset (=1) [flag]
__declspec(section ".calib") uint8_T S2PERFENABLE =  1;   // 0
//(S2) Rpm threshold to start safety 2 [rpm]
__declspec(section ".calib") uint16_T S2RPMSTARTS2 =   3800;   //  3800
//(S2) Rpm threshold to start safety 2 [rpm]
__declspec(section ".calib") uint16_T S2RPMSTOPS2 =   3700;   //  3700
//(S2) Delete the saf2 EE data [flag]
__declspec(section ".calib") uint8_T S2STOREDEL =  0;   // 0
//(S2) Threshold on S2 error counter to restart [counter]
__declspec(section ".calib") uint8_T S2THS2ERRCNT =   10;   //  10

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_SAF2MGM_
