// T32TPU20001 Tue Mar 30 16:17:13 2010

 B::
 
 TOOLBAR ON
 STATUSBAR ON
 WINPAGE.RESET
 
 WINCLEAR
 WINPOS 0.5 22.125 66. 1. 0. 0. W010
 Var.View %HEX %E %SPOTLIGHT SPISTL9958DEBFRAMEH1
 
 WINPOS 0.375 29.875 66. 1. 0. 0. W012
 Var.View %HEX %E %SPOTLIGHT SPISTL9958DEBFRAMEH2
 
 WINPOS 64.0 11.375 21. 5. 0. 0. W007
 Var.View %HEX %E %SPOTLIGHT S3SMPData
 
 WINPOS 0.5 0.1875 60. 1. 0. 0. W004
 Var.View %HEX %E %SPOTLIGHT SPITLE6244XDEBFRAME
 
 WINPOS 35.25 33.875 31. 1. 0. 0. W016
 Var.View %HEX %E %SPOTLIGHT spiSTL9958DataH2
 
 WINPOS 35.375 26.0 31. 1. 0. 0. W002
 Var.View %HEX %E %SPOTLIGHT spiSTL9958D<PERSON>H1
 
 WINPOS 112.63 0.1875 43. 1. 0. 0. W015
 Var.View %HEX %E %SPOTLIGHT spiMCP6S9XGain
 
 WINPOS 0.5 26.0 31. 1. 0. 0. W015
 Var.View %HEX %E %SPOTLIGHT spiSTL9958CmdH1
 
 WINPOS 0.375 33.875 31. 1. 0. 0. W014
 Var.View %HEX %E %SPOTLIGHT spiSTL9958CmdH2
 
 WINPOS 64.125 0.1875 21. 8. 0. 0. W006
 Var.View %HEX %E %SPOTLIGHT Saf3_RxBuffer
 
 WINPOS 88.875 0.1875 20. 35. 0. 0. W005
 Var.View %HEX %E %SPOTLIGHT Saf3_TxBuffer
 
 WINPOS 32.25 4.25 28. 15. 0. 0. W002
 Var.View %HEX %E %SPOTLIGHT spi_TLE6244X_DataBuff_RX
 
 WINPOS 0.5 4.25 28. 15. 0. 0. W003
 Var.View %HEX %E %SPOTLIGHT spi_TLE6244X_DataBuff_TX
 
 WINPOS 112.75 4.25 43. 29. 0. 0. W000
 Var.Watch
 
 VAR.ADDWATCH %E %SPOTLIGHT GAINVARCONFIG
 VAR.ADDWATCH dbCntMF_Gain
 VAR.ADDWATCH dbCntMF_HB1
 VAR.ADDWATCH dbCntMF_HB2
 VAR.ADDWATCH dbCntMF_TLE
 VAR.ADDWATCH SPITLE6244XFAN2
 VAR.ADDWATCH IGNREFRESH
 VAR.ADDWATCH Rpm
 VAR.ADDWATCH ResetType
 VAR.ADDWATCH VMapSignal
 VAR.ADDWATCH VTWater
 VAR.ADDWATCH VTAir
 VAR.ADDWATCH CFIFO_Commands
 VAR.ADDWATCH CFIFO_writePos
 VAR.ADDWATCH RFIFOs_Results
 VAR.ADDWATCH CntSpiHandshakeHB2
 VAR.ADDWATCH CntSpiHandshakeHB1
 VAR.ADDWATCH spi_MCP6S9XGain_HandShake
 VAR.ADDWATCH spi_TLE6244X_HandShake
 VAR.ADDWATCH InjEnable
 VAR.ADDWATCH CrashSignal
 VAR.ADDWATCH StopSignal
 VAR.ADDWATCH TrestleSignal
 VAR.ADDWATCH S2FlgDisLRelay
 VAR.ADDWATCH S3FlgDisLRelay
 VAR.ADDWATCH InjEnableIMMO
 VAR.ADDWATCH S3FlgAllowStart
 VAR.ADDWATCH FORCEDSA
 VAR.ADDWATCH FORCESA
 VAR.ADDWATCH CntSPIBadION
 VAR.ADDWATCH VTIONWINDOW
 VAR.ADDWATCH NSampleMax
 VAR.ADDWATCH BKIONDTRPM
 VAR.ADDWATCH EffDwellTime
 VAR.ADDWATCH VTIONDT
 VAR.ADDWATCH NSparkDeleted
 VAR.ADDWATCH IonWindow
 VAR.ADDWATCH DISABLEINT
 VAR.ADDWATCH MAXCPULOADCNT0
 VAR.ADDWATCH MAXCPULOADTIME0
 VAR.ADDWATCH MaxCntCpuLoad
 VAR.ADDWATCH CpuLoadPerc
 VAR.ADDWATCH CntCpuLoad
 VAR.ADDWATCH CntCpuLoadMax
 VAR.ADDWATCH CntCpuLoadMin
 VAR.ADDWATCH CntDisableCPUInit
 
 WINPAGE.SELECT P000     
 
 ENDDO

