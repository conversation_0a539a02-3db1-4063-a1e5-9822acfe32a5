/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

#include "Testio.h"
#include "Injcmd.h"

#ifdef  _BUILD_TESTIO_

#undef  _DEBUG_IO_ /* if defined, it allows to test signals not used at present */

#ifdef _DEBUG_IO_
uint8_T TestOutEnable = 0;
uint16_T VMapSignal2;
uint8_T TestONOFF;
uint16_T TestDuty;
uint32_T TestPeriod;
#endif

void TestIO_Init(void)
{
#ifdef _DEBUG_IO_
    TestOutEnable = 0;
    TestONOFF = TEST_OFF;
    TestDuty = 0;
    TestPeriod = 100000;
#endif

    /* Segnali non usati */
    SYS_OutPinConfig(OTE_VVT_Valve, VVT_PWM_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);

#ifdef ODE_LedImmo
    DIGIO_OutCfgExt(ODE_LedImmo, IMMOLED_OFF, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
#endif

#ifdef ODE_Warning_Lamp
    DIGIO_OutCfgExt(ODE_Warning_Lamp, WLAMP_OFF, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
#endif    
        
/* Segnali ancora da implementare, se implementati levarli da questo blocco */
#ifndef IDN_INJECTOR_3
#ifndef IDN_INJ_TEST
    SYS_OutPinConfig(OUT_InjDrv_4, 0x400, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(INJDRV_4_PWM, INJDRV_4_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0*256, 100000,0);
    PIO_PwmOutEnable(INJDRV_4_PWM);
#endif
#endif
#ifndef IDN_INJECTOR_4
    SYS_OutPinConfig(OUT_InjDrv_5, 0x400, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(INJDRV_5_PWM, INJDRV_5_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0*256, 100000,0);
    PIO_PwmOutEnable(INJDRV_5_PWM);
#endif
#ifndef IDN_INJECTOR_5
    SYS_OutPinConfig(OUT_InjDrv_6, 0x400, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(INJDRV_6_PWM, INJDRV_6_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0*256, 100000,0);
    PIO_PwmOutEnable(INJDRV_6_PWM);
#endif
#ifndef IDN_INJECTOR_6
    SYS_OutPinConfig(OUT_InjDrv_7, 0x400, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(INJDRV_7_PWM, INJDRV_7_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0*256, 100000,0);
    PIO_PwmOutEnable(INJDRV_7_PWM);
#endif
#ifndef IDN_INJECTOR_7
#ifndef IDN_INJ_TEST_HB
    SYS_OutPinConfig(OUT_InjDrv_8, 0x400, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(INJDRV_8_PWM, INJDRV_8_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0*256, 100000,0);
    PIO_PwmOutEnable(INJDRV_8_PWM);
#endif
#endif

#ifndef LAMHEAT_2_OUT
  SYS_OutPinConfig(ODE_H_Lambda_2, LAMHEAT_2_PWM_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
  PIO_PwmOutConfig(Lam_Heat_2_PWM, LAMHEAT_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0*256, 33333,0);
  PIO_PwmOutEnable(Lam_Heat_2_PWM);
#endif

#ifndef OUT_TSS
  DIGIO_OutCfgExt(ODE_TSS, ODE_TSS_OFF, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
#endif

}

void TestIO_T100ms(void)
{
#ifdef _DEBUG_IO_
    switch (TestOutEnable)
    {
        case TEST_IDLE:
        {
            /* Non fare niente. */    
        }
        break;
        /* Segnali non usati */
        case TEST_VVT:
        {
            PIO_PwmOutSetPeriod(VVT_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(VVT_PWM, TestDuty);    
        }
        break;
        case TEST_AIRCUT:
        {
            PIO_PwmOutSetPeriod(AIRCUT_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(AIRCUT_PWM, TestDuty);    
        }
        break;
        case TEST_CANISTER:
        {
            PIO_PwmOutSetPeriod(CANISTER_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(CANISTER_PWM, TestDuty);    
        }
        break;
        case TEST_LEDIMMO:
        {
            #ifdef ODE_LedImmo
            DIGIO_OutSet(ODE_LedImmo, TestONOFF);  
            #endif            
        }
        break;
        //case TEST_WLAMP:
        //{
        //    #ifdef ODE_Warning_Lamp
        //    DIGIO_OutSet(ODE_Warning_Lamp, TestONOFF);
        //    #endif
        //}
        break;
        /* Segnali ancora da implementare */
        #ifndef IDN_INJECTOR_3
        case TEST_INJA1:
        {
            PIO_PwmOutSetPeriod(INJDRV_4_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(INJDRV_4_PWM, TestDuty);    
        }
        break;
        #endif

        #ifndef IDN_INJECTOR_4
        case TEST_INJA1:
        {
            PIO_PwmOutSetPeriod(INJDRV_5_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(INJDRV_5_PWM, TestDuty);    
        }
        break;
        #endif
        #ifndef IDN_INJECTOR_5
        case TEST_INJA2:
        {
            PIO_PwmOutSetPeriod(INJDRV_6_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(INJDRV_6_PWM, TestDuty);    
        }
        break;
        #endif
        #ifndef IDN_INJECTOR_6
        case TEST_INJA3:
        {
            PIO_PwmOutSetPeriod(INJDRV_7_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(INJDRV_7_PWM, TestDuty);    
        }
        break;
        #endif
        #ifndef IDN_INJECTOR_7
        case TEST_INJA4:
        {
            PIO_PwmOutSetPeriod(INJDRV_8_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(INJDRV_8_PWM, TestDuty);    
        }
        break;
        #endif
        #ifndef OUT_TSS
        case TEST_TSS:
        {
            DIGIO_OutSet(ODE_TSS, (~TestONOFF) & 0x01);
        }
        break;
        #endif
        case TEST_LAMH2:
        {
            PIO_PwmOutSetPeriod(Lam_Heat_2_PWM, TestPeriod);
            PIO_PwmOutSetDutyCicle(Lam_Heat_2_PWM, TestDuty);    
        }
        break;
        #ifdef IDN_REAR_STOP
        case TEST_REARSTOP:
        {
            DIGIO_OutSet(ODE_Rear_Stop, TestONOFF);  
        }
        break;
        #endif
        default:
        {
            /* Non fare niente. */
        }
        break;
    }
#ifdef IA_MAP_SIGNAL2
//    ADC_GetSampleRes(IA_MAP_SIGNAL2, &(VMapSignal2), 10);
#endif
#ifdef _DEBUG_IO_
//    ADC_GetSampleRes(IA_TSS_FBK, (uint16_T *)&debug02, 10);
// #ifdef _SALA_PROVA_
//    ADC_GetSampleRes(IA_EXH_FBK, (uint16_T *)&debug03, 10);
// #endif
 #if _TEST_SPD_REARW_PWM_
    PIO_PwmOutSetDutyCicle(SPD_REARW_PWM, debug04);
 #endif
 #if _TEST_SPD_FRONTW_PWM_
    PIO_PwmOutSetDutyCicle(SPD_FRONTW_PWM, debug05);
 #endif
#endif
#endif
}

#endif

