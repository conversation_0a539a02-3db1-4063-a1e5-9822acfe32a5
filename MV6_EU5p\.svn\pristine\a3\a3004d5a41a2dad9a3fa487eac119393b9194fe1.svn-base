/**
 ******************************************************************************
 **  Filename:      GearShiftMgm_private.h
 **  Date:          28-Feb-2024
 **
 **  Model Version: 1.1792
 ******************************************************************************
 **/

#ifndef RTW_HEADER_GearShiftMgm_private_h_
#define RTW_HEADER_GearShiftMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "GearShiftMgm.h"

/* Includes for objects with custom storage classes. */
#include "digitalin.h"
#include "Trq_driver.h"
#include "cmefilter_mgm.h"
#include "Trq_est.h"
#include "sparkmgm.h"
#include "diagflags_out.h"
#include "Canmgm.h"
#include "Idlectf_mgm.h"
#include "gear_mgm.h"
#include "analog_qs.h"
#include "analogin.h"
#include "idle_mgm.h"
#include "syncmgm.h"
#include "PTrain_Diag.h"
#include "recmgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKCMEGEARSHIFT[8];      /* Variable: BKCMEGEARSHIFT
                                        * Referenced by:
                                        *   '<S8>/BKCMEGEARSHIFT'
                                        *   '<S89>/BKCMEGEARSHIFT'
                                        * CmeDriver breakpoint vector for CutOff Gain management
                                        */
extern int16_T QSDNCMEDRIVERMAX;       /* Variable: QSDNCMEDRIVERMAX
                                        * Referenced by: '<S6>/Chart'
                                        * Cme threshold for down
                                        */
extern int16_T QSUPCMEDRIVERMIN;       /* Variable: QSUPCMEDRIVERMIN
                                        * Referenced by: '<S6>/Chart'
                                        * Cme threshold for up
                                        */
extern int16_T VTCMEQSBLPDNI[8];       /* Variable: VTCMEQSBLPDNI
                                        * Referenced by:
                                        *   '<S63>/VTCMEQSBLPDNI'
                                        *   '<S153>/VTCMEQSBLPDNI'
                                        * CmeI for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPDNICTF[8];    /* Variable: VTCMEQSBLPDNICTF
                                        * Referenced by:
                                        *   '<S63>/VTCMEQSBLPDNICTF'
                                        *   '<S153>/VTCMEQSBLPDNICTF'
                                        * CmeI for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPDNP[8];       /* Variable: VTCMEQSBLPDNP
                                        * Referenced by:
                                        *   '<S63>/VTCMEQSBLPDNP'
                                        *   '<S153>/VTCMEQSBLPDNP'
                                        * CmeP for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPDNPCTF[8];    /* Variable: VTCMEQSBLPDNPCTF
                                        * Referenced by:
                                        *   '<S63>/VTCMEQSBLPDNPCTF'
                                        *   '<S153>/VTCMEQSBLPDNPCTF'
                                        * CmeP for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPUPI[8];       /* Variable: VTCMEQSBLPUPI
                                        * Referenced by:
                                        *   '<S76>/VTCMEQSBLPUPI'
                                        *   '<S166>/VTCMEQSBLPUPI'
                                        * CmeI for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPUPICTF[8];    /* Variable: VTCMEQSBLPUPICTF
                                        * Referenced by:
                                        *   '<S76>/VTCMEQSBLPUPICTF'
                                        *   '<S166>/VTCMEQSBLPUPICTF'
                                        * CmeI for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPUPP[8];       /* Variable: VTCMEQSBLPUPP
                                        * Referenced by:
                                        *   '<S76>/VTCMEQSBLPUPP'
                                        *   '<S166>/VTCMEQSBLPUPP'
                                        * CmeP for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSBLPUPPCTF[8];    /* Variable: VTCMEQSBLPUPPCTF
                                        * Referenced by:
                                        *   '<S76>/VTCMEQSBLPUPPCTF'
                                        *   '<S166>/VTCMEQSBLPUPPCTF'
                                        * CmeP for quick Blip gear shifting
                                        */
extern int16_T VTCMEQSCTFI[8];         /* Variable: VTCMEQSCTFI
                                        * Referenced by:
                                        *   '<S69>/VTCMEQSCTFI'
                                        *   '<S82>/VTCMEQSCTFI'
                                        *   '<S159>/VTCMEQSCTFI'
                                        *   '<S172>/VTCMEQSCTFI'
                                        * CmeI for quick Up gear shifting
                                        */
extern int16_T VTFLGQSLOW[5];          /* Variable: VTFLGQSLOW
                                        * Referenced by:
                                        *   '<S10>/VTFLGQSLOW'
                                        *   '<S91>/VTFLGQSLOW'
                                        * FlgQSLow
                                        */
extern int16_T VTGNCMEQSBLPDN[7];      /* Variable: VTGNCMEQSBLPDN
                                        * Referenced by: '<S63>/VTGNCMEQSBLPDN'
                                        * Gsin CmeQs
                                        */
extern int16_T VTGNCMEQSBLPUP[7];      /* Variable: VTGNCMEQSBLPUP
                                        * Referenced by: '<S76>/VTGNCMEQSBLPUP'
                                        * Gsin CmeQs
                                        */
extern uint16_T VTQSBLPDNFOFK[8];      /* Variable: VTQSBLPDNFOFK
                                        * Referenced by:
                                        *   '<S35>/VTQSBLPDNFOFK'
                                        *   '<S125>/VTQSBLPDNFOFK'
                                        * GearShift Gain Down FOF constant
                                        */
extern uint16_T VTQSBLPUPFOFK[8];      /* Variable: VTQSBLPUPFOFK
                                        * Referenced by:
                                        *   '<S35>/VTQSBLPUPFOFK'
                                        *   '<S125>/VTQSBLPUPFOFK'
                                        * GearShift Gain Down FOF constant
                                        */
extern uint16_T VTQSCTFDNFOFK[8];      /* Variable: VTQSCTFDNFOFK
                                        * Referenced by:
                                        *   '<S36>/VTQSCTFDNFOFK'
                                        *   '<S126>/VTQSCTFDNFOFK'
                                        * GearShift Gain UP FOF constant
                                        */
extern uint16_T VTQSCTFUPFOFK[8];      /* Variable: VTQSCTFUPFOFK
                                        * Referenced by:
                                        *   '<S36>/VTQSCTFUPFOFK'
                                        *   '<S126>/VTQSCTFUPFOFK'
                                        * GearShift Gain UP FOF constant
                                        */
extern uint16_T BKRPMQSGASPOS[2];      /* Variable: BKRPMQSGASPOS
                                        * Referenced by:
                                        *   '<S8>/BKRPMQSGASPOS'
                                        *   '<S89>/BKRPMQSGASPOS'
                                        * GasPosCC brakepoint
                                        */
extern uint16_T CMEQSIDIFF;            /* Variable: CMEQSIDIFF
                                        * Referenced by:
                                        *   '<S48>/CMEQSIDIFF'
                                        *   '<S138>/CMEQSIDIFF'
                                        * Absolute difference between CmeDriverI and CmeQsIFilt to exit shifting Cme
                                        */
extern uint16_T CMEQSPDIFF;            /* Variable: CMEQSPDIFF
                                        * Referenced by:
                                        *   '<S48>/CMEQSPDIFF'
                                        *   '<S138>/CMEQSPDIFF'
                                        * Absolute difference between CmeDriverP and CmeQsDnPFilt to exit shifting Cme
                                        */
extern uint16_T QSGNFILTTOTIME;        /* Variable: QSGNFILTTOTIME
                                        * Referenced by: '<S50>/QSGNFILTTOTIME'
                                        * K Filter gain
                                        */
extern uint16_T BKFLGQSLOW[5];         /* Variable: BKFLGQSLOW
                                        * Referenced by:
                                        *   '<S10>/BKFLGQSLOW'
                                        *   '<S91>/BKFLGQSLOW'
                                        * BK: VTFLGQSLOW
                                        */
extern uint16_T BKRPMGEARSHIFT[8];     /* Variable: BKRPMGEARSHIFT
                                        * Referenced by:
                                        *   '<S8>/BKRPMGEARSHIFT'
                                        *   '<S89>/BKRPMGEARSHIFT'
                                        * Rpm breakpoint vector for CutOff Gain management
                                        */
extern uint16_T BKRPMQSCTFOFFSET[5];   /* Variable: BKRPMQSCTFOFFSET
                                        * Referenced by:
                                        *   '<S8>/BKRPMQSCTFOFFSET'
                                        *   '<S89>/BKRPMQSCTFOFFSET'
                                        * QuickShift cutoff offset brakepoint
                                        */
extern uint16_T QSRPMMAXHYS;           /* Variable: QSRPMMAXHYS
                                        * Referenced by:
                                        *   '<S27>/QSRPMMAXHYS'
                                        *   '<S28>/QSRPMMAXHYS'
                                        *   '<S110>/QSRPMMAXHYS'
                                        *   '<S112>/QSRPMMAXHYS'
                                        * Rpm Hysteresis
                                        */
extern uint16_T TBRPMQSHIFTDNMAX[14];  /* Variable: TBRPMQSHIFTDNMAX
                                        * Referenced by:
                                        *   '<S27>/TBRPMQSHIFTDNMAX'
                                        *   '<S110>/TBRPMQSHIFTDNMAX'
                                        * Rpm threshold
                                        */
extern uint16_T VTRPMQSHIFTUPMIN[7];   /* Variable: VTRPMQSHIFTUPMIN
                                        * Referenced by:
                                        *   '<S28>/VTRPMQSHIFTUPMIN'
                                        *   '<S112>/VTRPMQSHIFTUPMIN'
                                        * Minimum Rpm value to enable quick up shifting
                                        */
extern int8_T TBQSCTFDNOFFSET[35];     /* Variable: TBQSCTFDNOFFSET
                                        * Referenced by:
                                        *   '<S70>/TBQSCTFDNOFFSET'
                                        *   '<S160>/TBQSCTFDNOFFSET'
                                        * Quick shift Cut Off period offset
                                        */
extern int8_T TBQSCTFUPOFFSET[35];     /* Variable: TBQSCTFUPOFFSET
                                        * Referenced by:
                                        *   '<S83>/TBQSCTFUPOFFSET'
                                        *   '<S173>/TBQSCTFUPOFFSET'
                                        * Quick shift Cut Off period offset
                                        */
extern uint8_T ENQUICKSHIFT;           /* Variable: ENQUICKSHIFT
                                        * Referenced by:
                                        *   '<S24>/ENQUICKSHIFT'
                                        *   '<S106>/ENQUICKSHIFT'
                                        * If 1 Quick shifting is enabled
                                        */
extern uint8_T MINTIMEQSHIFT;          /* Variable: MINTIMEQSHIFT
                                        * Referenced by:
                                        *   '<S28>/MINTIMEQSHIFT'
                                        *   '<S112>/MINTIMEQSHIFT'
                                        * Quick shift maximum duration
                                        */
extern uint8_T NUMQSDBLCTF;            /* Variable: NUMQSDBLCTF
                                        * Referenced by: '<S6>/Chart'
                                        * counter
                                        */
extern uint8_T QSMINGEARPOS;           /* Variable: QSMINGEARPOS
                                        * Referenced by:
                                        *   '<S27>/QSMINGEARPOS'
                                        *   '<S110>/QSMINGEARPOS'
                                        * Min gear QS
                                        */
extern uint8_T SELQS2;                 /* Variable: SELQS2
                                        * Referenced by: '<S6>/Chart'
                                        * QS ver 2
                                        */
extern uint8_T TBCMEQSDNIPERIOD[56];   /* Variable: TBCMEQSDNIPERIOD
                                        * Referenced by:
                                        *   '<S70>/TBCMEQSDNIPERIOD'
                                        *   '<S160>/TBCMEQSDNIPERIOD'
                                        * Quick shift CmeQsI application period
                                        */
extern uint8_T TBCMEQSUPIPERIOD[56];   /* Variable: TBCMEQSUPIPERIOD
                                        * Referenced by:
                                        *   '<S83>/TBCMEQSUPIPERIOD'
                                        *   '<S173>/TBCMEQSUPIPERIOD'
                                        * Quick shift CmeQsI application period
                                        */
extern uint8_T TBQSCTFDNPERIOD[56];    /* Variable: TBQSCTFDNPERIOD
                                        * Referenced by:
                                        *   '<S70>/TBQSCTFDNPERIOD'
                                        *   '<S160>/TBQSCTFDNPERIOD'
                                        * Quick shift Cut Off period
                                        */
extern uint8_T TBQSCTFUPPERIOD[56];    /* Variable: TBQSCTFUPPERIOD
                                        * Referenced by:
                                        *   '<S83>/TBQSCTFUPPERIOD'
                                        *   '<S173>/TBQSCTFUPPERIOD'
                                        * Quick shift Cut Off period
                                        */
extern uint8_T VTQSBLPDNTIME[7];       /* Variable: VTQSBLPDNTIME
                                        * Referenced by:
                                        *   '<S64>/VTQSBLPDNTIME'
                                        *   '<S154>/VTQSBLPDNTIME'
                                        * Quick shift Cme increment duration
                                        */
extern uint8_T VTQSBLPDNTIMECTF[7];    /* Variable: VTQSBLPDNTIMECTF
                                        * Referenced by:
                                        *   '<S64>/VTQSBLPDNTIMECTF'
                                        *   '<S154>/VTQSBLPDNTIMECTF'
                                        * Quick shift Cme increment duration
                                        */
extern uint8_T VTQSBLPDNTOTIME[7];     /* Variable: VTQSBLPDNTOTIME
                                        * Referenced by: '<S64>/VTQSBLPDNTOTIME'
                                        * Quick shift Cme increment duration
                                        */
extern uint8_T VTQSBLPUPTIME[7];       /* Variable: VTQSBLPUPTIME
                                        * Referenced by:
                                        *   '<S77>/VTQSBLPUPTIME'
                                        *   '<S167>/VTQSBLPUPTIME'
                                        * Quick shift Cme increment duration
                                        */
extern uint8_T VTQSBLPUPTIMECTF[7];    /* Variable: VTQSBLPUPTIMECTF
                                        * Referenced by:
                                        *   '<S77>/VTQSBLPUPTIMECTF'
                                        *   '<S167>/VTQSBLPUPTIMECTF'
                                        * Quick shift Cme increment duration
                                        */
extern uint8_T VTQSBLPUPTOTIME[7];     /* Variable: VTQSBLPUPTOTIME
                                        * Referenced by: '<S77>/VTQSBLPUPTOTIME'
                                        * Quick shift Cme increment duration
                                        */
extern uint8_T VTQSDNDBLCTFTIME[7];    /* Variable: VTQSDNDBLCTFTIME
                                        * Referenced by: '<S70>/VTQSDNDBLCTFTIME'
                                        * Time
                                        */
extern uint8_T VTQSDNDBLCTFTOTIME[7];  /* Variable: VTQSDNDBLCTFTOTIME
                                        * Referenced by: '<S70>/VTQSDNDBLCTFTOTIME'
                                        * Time
                                        */
extern uint8_T VTQSUPDBLCTFTIME[7];    /* Variable: VTQSUPDBLCTFTIME
                                        * Referenced by: '<S83>/VTQSUPDBLCTFTIME'
                                        * Time
                                        */
extern uint8_T VTQSUPDBLCTFTOTIME[7];  /* Variable: VTQSUPDBLCTFTOTIME
                                        * Referenced by: '<S83>/VTQSUPDBLCTFTOTIME'
                                        * Time
                                        */
extern void GearShiftMgm_Calc_Ratio(int16_T rtu_outBus, uint16_T rtu_outBus_d4ae,
  int16_T rtu_inBus, uint16_T rtu_inBus_nye2, rtB_Calc_Ratio_GearShiftMgm
  *localB);
extern void GearShiftMgm_Hys__x(uint16_T rtu_inSig, uint16_T rtu_cal, uint16_T
  rtu_hys, rtB_Hys__x_GearShiftMgm *localB, rtDW_Hys__x_GearShiftMgm *localDW);
extern void GearShiftMgm_Hys__x_iuw3(uint16_T rtu_inSig, uint16_T rtu_cal,
  uint16_T rtu_hys, rtB_Hys__x_GearShiftMgm_hazx *localB,
  rtDW_Hys__x_GearShiftMgm_knoo *localDW);
extern void GearShiftMgm_Calc_KFilt_Ctf(uint32_T rtu_ratioBus, uint16_T
  rtu_ratioBus_exde, uint16_T rtu_ratioBus_imf2, uint8_T rtu_outBus,
  rtB_Calc_KFilt_Ctf_GearShiftMgm *localB);
extern void GearShiftMgm_Calc_KFilt_Blp(uint32_T rtu_ratioBus, uint16_T
  rtu_ratioBus_pjxr, uint16_T rtu_ratioBus_lolj, uint8_T rtu_outBus,
  rtB_Calc_KFilt_Blp_GearShiftMgm *localB);
extern void GearShiftMgm_EnQs_lgkn(uint8_T rtu_outBus, uint8_T rtu_inBus,
  uint8_T rtu_inBus_pt3c, const uint8_T rtu_inBus_bm5j[22], uint8_T
  rtu_inBus_bvaj, uint8_T rtu_inBus_o5cv, uint8_T rtu_inBus_kg1o, uint8_T
  rtu_inBus_cf2k, uint8_T rtu_inBus_nqsl, uint8_T rtu_inBus_je2r, uint8_T
  rtu_inBus_ie1h, uint8_T rtu_inBus_ezdu, uint16_T rtu_inBus_ahd1, uint32_T
  rtu_ratioBus, uint16_T rtu_ratioBus_ozbd, uint16_T rtu_ratioBus_knrk,
  rtB_EnQs_GearShiftMgm *localB, rtDW_EnQs_GearShiftMgm *localDW);
extern void GearShiftMgm_fc_QsDnBlp_Calc(void);
extern void GearShiftMgm_fc_QsUpBlp_Calc(void);
extern void GearShiftMgm_fc_QsDnCtf_Calc(void);
extern void GearShiftMgm_fc_QsUpCtf_Calc(void);
extern void GearShiftMgm_ReadLookUpTables(void);
extern void GearShiftMgm_FlgEnQShift_Calc(void);
extern void GearShiftMgm_EnQs(void);
extern void GearShiftM_fc_QsDnBlp_Calc_llms(void);
extern void GearShiftM_fc_QsUpBlp_Calc_hvvt(void);
extern void GearShiftM_fc_QsDnCtf_Calc_ds5q(void);
extern void GearShiftM_fc_QsUpCtf_Calc_lq0y(void);
extern void GearShift_ReadLookUpTables_b1et(void);
extern void GearShiftMgm_T10ms(void);
extern void GearShiftMgm_Init(void);

#endif                                 /* RTW_HEADER_GearShiftMgm_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
