/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TrqEst.c
 **  File Creation Date: 10-Mar-2023
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TrqEst
 **  Model Description:
 **  Model Version:      1.1961
 **  Model Author:       <PERSON> - Wed Jul 01 14:50:43 2009
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Fri Mar 10 14:51:18 2023
 **
 **  Last Saved Modification:  LanaL - Fri Mar 10 14:48:06 2023
 **
 **
 *******************************************************************************
 **/

#include "TrqEst.h"
#include "TrqEst_private.h"
#include "div_s16s32_floor.h"
#include "mul_u32_loSR.h"

/*  Defines */

/*  Data Types */

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TRQEST_

/**************************** GLOBAL DATA *************************************/
/*  Definitions */

/* Block signals (default storage) */
BlockIO_TrqEst TrqEst_B;

/* Block states (default storage) */
D_Work_TrqEst TrqEst_DWork;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T CmeEnv;

/* Max CME Estimated (Max air flow rate) */
int16_T CmeEst;

/* Actual CME estimated (real spark advance) */
int16_T CmeEstWheel;

/* Actual CME estimated (real spark advance) - applied to wheel */
int16_T CmeEstWheelF;

/* Actual CME estimated (real spark advance) - applied to wheel */
int16_T CmeMecTarget;

/* Pedal CME Estimated (Target air flow for pedal) */
int16_T CmeTargetIMin;

/* Actual CME estimated min */
int16_T CmeTargetPMin;

/* Actual CmeTargetP min */
int16_T CmfP;

/* CMF estimation */
int16_T CmfWheel;

/* Resistant torque due to vehicle speed - applied to wheel */
int16_T CmfWheelSpL;

/* Resistant torque due to vehicle speed - applied to wheel */
int16_T CmiEnv;

/* Max CMI Estimated (Max air flow rate) */
int16_T CmiEst;

/* Actual CMI estimated (Base spark advance) */
int16_T CmiEstBase;

/* Actual CMI estimated (Base spark advance) */
uint16_T CmiGain;

/* Air flow gain */
int16_T CmiMecTarget;

/* Pedal CMI Estimated (Target air flow for pedal) */
int16_T CmiOffset;

/* Air flow offset */
int16_T CmiPotEst[4];

/* Potential CMI estimated (Optimum spark advance) */
int16_T CmiPotEstMean;

/* CmiPotEst media */
int16_T CmiTargetIMin;

/* Actual CMI min estimated */
int16_T CmiTargetPMax;

/* Max CMI slow target */
int16_T CmiTargetPMin;

/* Min CMI slow target */
int16_T DPAtmPres;

/* Difference between PresAtm and PresIntake */
int16_T DPAtmPresF;

/* Difference between PresAtm and PresIntake filtered */
int16_T DeltaCmeEstWheelF;

/* Delta Actual CME estimated (real spark advance) - applied to wheel */
uint32_T IDTrqEst;

/* ID Version */
uint32_T QAirRefOut;

/* Q air ref */
uint32_T TbCmi;

/* Cmi */

/*  Declarations  */

/***************************** FILE SCOPE DATA ********************************/

/*************************** FUNCTIONS ****************************************/

/* Output and update for function-call system: '<S2>/Reset' */
void TrqEst_Reset(void)
{
  uint8_T id;

  /* DataStoreWrite: '<S5>/Data Store Write1' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmeEnv = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write10' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmeMecTarget = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write11' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmfP = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write12' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmiTargetPMin = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write13' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmiTargetPMax = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write14' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmeTargetPMin = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write2' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmiEst = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write3' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmeEst = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write4' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmfWheel = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write5' incorporates:
   *  Constant: '<S5>/CM_INIT'
   *  DataTypeConversion: '<S5>/Data Type Conversion1'
   */
  CmeEstWheel = (int16_T)(((int16_T)CM_INIT) >> 1);

  /* DataStoreWrite: '<S5>/Data Store Write6' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmiTargetIMin = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write7' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmeTargetIMin = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write8' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmiEnv = ((int16_T)CM_INIT);

  /* DataStoreWrite: '<S5>/Data Store Write9' incorporates:
   *  Constant: '<S5>/CM_INIT'
   */
  CmiMecTarget = ((int16_T)CM_INIT);

  /* Constant: '<S5>/ID_TRQ_EST' */
  IDTrqEst = ID_TRQ_EST;

  /* Chart: '<S5>/Assign_CmiPotEst' */
  /* Gateway: TrqEst/Reset/Assign_CmiPotEst */
  /* During: TrqEst/Reset/Assign_CmiPotEst */
  /* Entry Internal: TrqEst/Reset/Assign_CmiPotEst */
  /* Transition: '<S11>:2' */
  for (id = 0U; id < ((uint8_T)N_CYL_MAX_EM); id++) {
    /* Transition: '<S11>:4' */
    CmiPotEst[(id)] = 0;

    /* Transition: '<S11>:8' */
  }

  /* Transition: '<S11>:10' */
  CmiPotEstMean = 0;

  /* End of Chart: '<S5>/Assign_CmiPotEst' */
}

/*
 * Output and update for atomic system:
 *    '<S3>/cmi_formula_cyl'
 *    '<S4>/cmi_formula_cyl'
 */
void TrqEst_cmi_formula_cyl(uint16_T rtu_qair, uint16_T rtu_effSA, uint16_T
  rtu_effLam, rtB_cmi_formula_cyl_TrqEst *localB)
{
  int32_T tmp;

  /* DataTypeConversion: '<S8>/Data Type Conversion2' incorporates:
   *  DataStoreRead: '<S8>/Data Store Read'
   *  DataStoreRead: '<S8>/Data Store Read1'
   *  DataTypeConversion: '<S8>/Data Type Conversion'
   *  DataTypeConversion: '<S8>/Data Type Conversion1'
   *  Product: '<S8>/Divide1'
   *  Sum: '<S8>/Add2'
   */
  tmp = (((int32_T)((((uint32_T)rtu_qair) * CmiGain) >> 1)) >> 13) + CmiOffset;
  if (tmp > 32767) {
    tmp = 32767;
  }

  /* Product: '<S8>/Divide4' incorporates:
   *  DataTypeConversion: '<S8>/Data Type Conversion2'
   *  Product: '<S8>/Divide2'
   *  Product: '<S8>/Divide3'
   */
  tmp = ((((uint16_T)((((uint32_T)((uint16_T)((((uint32_T)rtu_effSA) *
    rtu_effLam) >> 14))) * rtu_effSA) >> 14)) * tmp) >> 14);
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  localB->Divide4 = (int16_T)tmp;

  /* End of Product: '<S8>/Divide4' */
}

/* Output and update for function-call system: '<S2>/PreHTDC' */
void TrqEst_PreHTDC(void)
{
  uint8_T i;
  int32_T rtb_tmpCmiPotEst;

  /* Outputs for Atomic SubSystem: '<S3>/cmi_formula_cyl' */
  /* Selector: '<S3>/Selector' incorporates:
   *  Constant: '<S3>/MAXEFF'
   *  Inport: '<Root>/AbsPreHTdc'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/QAir'
   */
  TrqEst_cmi_formula_cyl(QAir[(AbsPreHTdc)], ((uint16_T)MAXEFF), EffLambda,
    &TrqEst_B.cmi_formula_cyl);

  /* End of Outputs for SubSystem: '<S3>/cmi_formula_cyl' */

  /* Chart: '<S3>/Assign_CmiPotEst' incorporates:
   *  Inport: '<Root>/AbsPreHTdc'
   */
  /* Gateway: TrqEst/PreHTDC/Assign_CmiPotEst */
  /* During: TrqEst/PreHTDC/Assign_CmiPotEst */
  /* Entry Internal: TrqEst/PreHTDC/Assign_CmiPotEst */
  /* Transition: '<S7>:10' */
  /* Transition: '<S7>:9' */
  CmiPotEst[(AbsPreHTdc)] = TrqEst_B.cmi_formula_cyl.Divide4;
  rtb_tmpCmiPotEst = 0;
  for (i = 0U; i < ((uint8_T)N_CYLINDER); i++) {
    /* Transition: '<S7>:11' */
    rtb_tmpCmiPotEst += CmiPotEst[(i)];
  }

  /* End of Chart: '<S3>/Assign_CmiPotEst' */

  /* Product: '<S3>/Divide' incorporates:
   *  Constant: '<S3>/N_CYLINDER'
   *  DataStoreWrite: '<S3>/Data Store Write6'
   */
  /* Transition: '<S7>:13' */
  CmiPotEstMean = div_s16s32_floor(rtb_tmpCmiPotEst, ((uint8_T)N_CYLINDER));
}

/* Output and update for function-call system: '<S2>/PreTDC' */
void TrqEst_PreTDC(void)
{
  uint8_T i;
  int32_T rtb_tmpCmiPotEst;

  /* Outputs for Atomic SubSystem: '<S4>/cmi_formula_cyl' */
  /* Selector: '<S4>/Selector' incorporates:
   *  Constant: '<S4>/MAXEFF'
   *  Inport: '<Root>/AbsPreTdc'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/QAir'
   */
  TrqEst_cmi_formula_cyl(QAir[(AbsPreTdc)], ((uint16_T)MAXEFF), EffLambda,
    &TrqEst_B.cmi_formula_cyl_n);

  /* End of Outputs for SubSystem: '<S4>/cmi_formula_cyl' */

  /* Chart: '<S4>/Assign_CmiPotEst' incorporates:
   *  Inport: '<Root>/AbsPreTdc'
   */
  /* Gateway: TrqEst/PreTDC/Assign_CmiPotEst */
  /* During: TrqEst/PreTDC/Assign_CmiPotEst */
  /* Entry Internal: TrqEst/PreTDC/Assign_CmiPotEst */
  /* Transition: '<S9>:2' */
  /* Transition: '<S9>:4' */
  CmiPotEst[(AbsPreTdc)] = TrqEst_B.cmi_formula_cyl_n.Divide4;
  rtb_tmpCmiPotEst = 0;
  for (i = 0U; i < ((uint8_T)N_CYLINDER); i++) {
    /* Transition: '<S9>:8' */
    rtb_tmpCmiPotEst += CmiPotEst[(i)];
  }

  /* End of Chart: '<S4>/Assign_CmiPotEst' */

  /* Product: '<S4>/Divide' incorporates:
   *  Constant: '<S4>/N_CYLINDER'
   *  DataStoreWrite: '<S4>/Data Store Write6'
   */
  /* Transition: '<S9>:10' */
  CmiPotEstMean = div_s16s32_floor(rtb_tmpCmiPotEst, ((uint8_T)N_CYLINDER));
}

/*
 * Output and update for atomic system:
 *    '<S18>/cmi_formula_cycle1'
 *    '<S18>/cmi_formula_cycle5'
 *    '<S18>/cmi_formula_cycle6'
 */
void TrqEst_cmi_formula_cycle1(uint16_T rtu_qair_cycle, uint16_T rtu_effSA,
  uint16_T rtu_effLam, uint16_T rtu_effCutoff, rtB_cmi_formula_cycle1_TrqEst
  *localB)
{
  int32_T tmp;

  /* DataTypeConversion: '<S20>/Data Type Conversion2' incorporates:
   *  Constant: '<S20>/N_CYLINDER'
   *  DataStoreRead: '<S20>/Data Store Read'
   *  DataStoreRead: '<S20>/Data Store Read1'
   *  DataTypeConversion: '<S20>/Data Type Conversion'
   *  Product: '<S20>/Divide1'
   *  Product: '<S20>/Divide5'
   *  Sum: '<S20>/Add2'
   */
  tmp = (((int32_T)((((uint32_T)rtu_qair_cycle) * CmiGain) >> 2)) >> 10) +
    ((int16_T)(CmiOffset * ((uint8_T)N_CYLINDER)));
  if (tmp > 32767) {
    tmp = 32767;
  }

  /* Product: '<S20>/Divide4' incorporates:
   *  DataTypeConversion: '<S20>/Data Type Conversion2'
   *  Product: '<S20>/Divide2'
   *  Product: '<S20>/Divide3'
   */
  tmp = ((((uint16_T)((((uint32_T)((uint16_T)((((uint32_T)rtu_effSA) *
    rtu_effLam) >> 14))) * rtu_effCutoff) >> 14)) * tmp) >> 14);
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  localB->Divide4 = (int16_T)tmp;

  /* End of Product: '<S20>/Divide4' */
}

/*
 * Output and update for atomic system:
 *    '<S18>/cmi_formula_cycle2'
 *    '<S18>/cmi_formula_cycle3'
 *    '<S18>/cmi_formula_cycle4'
 */
void TrqEst_cmi_formula_cycle2(uint16_T rtu_qair_cycle, uint16_T rtu_effSA,
  uint16_T rtu_effLam, uint16_T rtu_effCutoff, rtB_cmi_formula_cycle2_TrqEst
  *localB)
{
  int32_T tmp;

  /* DataTypeConversion: '<S21>/Data Type Conversion2' incorporates:
   *  Constant: '<S21>/N_CYLINDER'
   *  DataStoreRead: '<S21>/Data Store Read'
   *  DataStoreRead: '<S21>/Data Store Read1'
   *  DataTypeConversion: '<S21>/Data Type Conversion'
   *  Product: '<S21>/Divide1'
   *  Product: '<S21>/Divide5'
   *  Sum: '<S21>/Add2'
   */
  tmp = (((int32_T)((((uint32_T)rtu_qair_cycle) * CmiGain) >> 1)) >> 10) +
    ((int16_T)(CmiOffset * ((uint8_T)N_CYLINDER)));
  if (tmp > 32767) {
    tmp = 32767;
  }

  /* Product: '<S21>/Divide4' incorporates:
   *  DataTypeConversion: '<S21>/Data Type Conversion2'
   *  Product: '<S21>/Divide2'
   *  Product: '<S21>/Divide3'
   */
  tmp = ((((uint16_T)((((uint32_T)((uint16_T)((((uint32_T)rtu_effSA) *
    rtu_effLam) >> 14))) * rtu_effCutoff) >> 14)) * tmp) >> 14);
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  localB->Divide4 = (int16_T)tmp;

  /* End of Product: '<S21>/Divide4' */
}

/* Output and update for function-call system: '<S2>/T10ms' */
void TrqEst_T10ms(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int32_T rtb_FOF_Reset_S16_FXP_o2_d;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_Look2D_IR_U16;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  uint8_T rtb_Switch1;
  int32_T rtb_DataTypeConversion;
  uint16_T rtb_Divide2;
  int16_T rtb_DataTypeConversion2_d;
  int16_T rtb_Memory4;
  int16_T rtb_DataTypeConversion1_c;
  uint32_T rtb_Divide1_m;
  int16_T rtb_FOF_Reset_S16_FXP_o1_h;
  int16_T rtb_Sum;
  uint32_T tmp;

  /* Outputs for Atomic SubSystem: '<S6>/Friction_Interpolation' */
  /* Switch: '<S27>/Switch' incorporates:
   *  Constant: '<S27>/Constant'
   *  Constant: '<S27>/PRESTESEL'
   *  Inport: '<Root>/TrqStartFlg'
   */
  if (TrqStartFlg != 0) {
    rtb_Switch1 = PRESTESEL;
  } else {
    rtb_Switch1 = 2U;
  }

  /* End of Switch: '<S27>/Switch' */

  /* MultiPortSwitch: '<S27>/Multiport Switch' incorporates:
   *  Inport: '<Root>/PresIntake'
   *  Inport: '<Root>/PresIntk0F'
   *  Inport: '<Root>/PresObj'
   *  Inport: '<Root>/PresObjF'
   */
  switch (rtb_Switch1) {
   case 0:
    rtb_FOF_Reset_S16_FXP_o1_h = (int16_T)PresIntake;
    break;

   case 1:
    rtb_FOF_Reset_S16_FXP_o1_h = (int16_T)PresObj;
    break;

   case 2:
    rtb_FOF_Reset_S16_FXP_o1_h = PresIntk0F;
    break;

   default:
    rtb_FOF_Reset_S16_FXP_o1_h = (int16_T)PresObjF;
    break;
  }

  /* End of MultiPortSwitch: '<S27>/Multiport Switch' */

  /* Sum: '<S27>/Sum' incorporates:
   *  Inport: '<Root>/PresAtm'
   */
  rtb_Sum = (int16_T)(((int16_T)PresAtm) - rtb_FOF_Reset_S16_FXP_o1_h);

  /* Memory: '<S27>/Memory1' */
  rtb_DataTypeConversion = TrqEst_DWork.Memory1_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S47>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S27>/KFDPATMPRES'
   *  Constant: '<S27>/RESET'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1_h, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_Sum, KFDPATMPRES, rtb_Sum, ((uint8_T)0U),
                    rtb_DataTypeConversion);

  /* S-Function (PreLookUpIdSearch_S16): '<S28>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S14>/BKCMFPRES'
   *  Constant: '<S14>/BKCMFPRES_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_U16_o2, &rtb_Look2D_IR_U16,
                        rtb_FOF_Reset_S16_FXP_o1_h, &BKCMFPRES[0], ((uint8_T)
    BKCMFPRES_dim));

  /* S-Function (PreLookUpIdSearch_U16): '<S45>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S34>/BKCMRPMF'
   *  Constant: '<S34>/BKCMRPMF_dim'
   *  Inport: '<Root>/RpmF'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1, &rtb_Divide2, RpmF,
                        &BKCMRPMF[0], ((uint8_T)BKCMRPMF_dim));

  /* S-Function (Look2D_IR_U8): '<S49>/Look2D_IR_U8' incorporates:
   *  Constant: '<S14>/TBCMF'
   *  Constant: '<S29>/BKCMFPRES_dim'
   *  Constant: '<S29>/BKCMRPMF_dim'
   */
  Look2D_IR_U8( &rtb_PreLookUpIdSearch_U16_o2, &TBCMF[0],
               rtb_PreLookUpIdSearch_U16_o2, rtb_Look2D_IR_U16, ((uint8_T)
    BKCMFPRES_dim), rtb_PreLookUpIdSearch_U16_o1, rtb_Divide2, ((uint8_T)
    BKCMRPMF_dim));

  /* DataTypeConversion: '<S14>/Data Type Conversion1' */
  rtb_DataTypeConversion1_c = (int16_T)(((uint32_T)rtb_PreLookUpIdSearch_U16_o2)
    >> 5);

  /* S-Function (PreLookUpIdSearch_S16): '<S32>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S14>/BKCMFTWAT'
   *  Constant: '<S14>/BKCMFTWAT_dim'
   *  Inport: '<Root>/TWater'
   */
  PreLookUpIdSearch_S16( &rtb_Look2D_IR_U16, &rtb_PreLookUpIdSearch_U16_o2,
                        TWater, &BKCMFTWAT[0], ((uint8_T)BKCMFTWAT_dim));

  /* S-Function (Look2D_IR_S8): '<S50>/Look2D_IR_S8' incorporates:
   *  Constant: '<S14>/TBCMFTW'
   *  Constant: '<S30>/BKCMFTWAT_dim'
   *  Constant: '<S30>/BKCMRPMF_dim'
   */
  Look2D_IR_S8( &rtb_DataTypeConversion2_d, &TBCMFTW[0], rtb_Look2D_IR_U16,
               rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKCMFTWAT_dim),
               rtb_PreLookUpIdSearch_U16_o1, rtb_Divide2, ((uint8_T)BKCMRPMF_dim));

  /* DataTypeConversion: '<S14>/Data Type Conversion' */
  rtb_Memory4 = (int16_T)(rtb_DataTypeConversion2_d >> 5);

  /* S-Function (Look2D_U8_U16_S16): '<S51>/Look2D_U8_U16_S16' incorporates:
   *  Constant: '<S14>/BKCMFTWAT'
   *  Constant: '<S14>/BKTDCCRKCMF'
   *  Constant: '<S14>/TBCRKCMF'
   *  Constant: '<S31>/BKCMFTWAT_dim'
   *  Constant: '<S31>/BKTDCCRKCMF_dim'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/TWaterCrk'
   */
  Look2D_U8_U16_S16( &rtb_Look2D_IR_U16, &TBCRKCMF[0], CntTdcCrk, &BKTDCCRKCMF[0],
                    ((uint8_T)BKTDCCRKCMF_dim), TWaterCrk, &BKCMFTWAT[0],
                    ((uint8_T)BKCMFTWAT_dim));

  /* DataTypeConversion: '<S14>/Data Type Conversion2' */
  rtb_DataTypeConversion2_d = (int16_T)(((uint32_T)rtb_Look2D_IR_U16) >> 6);

  /* Sum: '<S14>/Add' incorporates:
   *  DataStoreWrite: '<S14>/Data Store Write'
   */
  CmfP = (int16_T)((rtb_DataTypeConversion1_c + rtb_Memory4) +
                   rtb_DataTypeConversion2_d);

  /* S-Function (LookUp_S16_U16): '<S42>/LookUp_S16_U16' incorporates:
   *  Constant: '<S33>/BKCMFVEH'
   *  Constant: '<S33>/VTCMFVEH'
   *  Constant: '<S39>/BKCMFVEH_dim'
   *  Inport: '<Root>/VehSpeedSetUpCAN'
   */
  LookUp_S16_U16( &rtb_Memory4, &VTCMFVEH[0], VehSpeedSetUpCAN, &BKCMFVEH[0],
                 ((uint8_T)BKCMFVEH_dim));

  /* DataStoreWrite: '<S14>/Data Store Write1' */
  CmfWheelSpL = rtb_Memory4;

  /* S-Function (LookUp_S16_U16): '<S40>/LookUp_S16_U16' incorporates:
   *  Constant: '<S33>/BKCMFVEH'
   *  Constant: '<S33>/VTCMFVEH'
   *  Constant: '<S38>/BKCMFVEH_dim'
   *  Inport: '<Root>/VehSpeed'
   */
  LookUp_S16_U16( &rtb_Memory4, &VTCMFVEH[0], VehSpeed, &BKCMFVEH[0], ((uint8_T)
    BKCMFVEH_dim));

  /* DataStoreWrite: '<S14>/Data Store Write4' */
  CmfWheel = rtb_Memory4;

  /* S-Function (PreLookUpIdSearch_U16): '<S44>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S34>/BKCMLOAD'
   *  Constant: '<S34>/BKCMLOAD_dim'
   *  Inport: '<Root>/Load'
   */
  PreLookUpIdSearch_U16( &rtb_Look2D_IR_U16, &rtb_PreLookUpIdSearch_U16_o2, Load,
                        &BKCMLOAD[0], ((uint8_T)BKCMLOAD_dim));

  /* S-Function (Look2D_IR_U16): '<S36>/Look2D_IR_U16' incorporates:
   *  Constant: '<S26>/TBCMI'
   *  Constant: '<S34>/BKCMLOAD_dim'
   *  Constant: '<S34>/BKCMRPMF_dim'
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBCMI[0], rtb_Look2D_IR_U16,
                rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKCMLOAD_dim),
                rtb_PreLookUpIdSearch_U16_o1, rtb_Divide2, ((uint8_T)
    BKCMRPMF_dim));

  /* DataTypeConversion: '<S26>/Data Type Conversion2' incorporates:
   *  DataStoreWrite: '<S26>/Data Store Write6'
   */
  TbCmi = (((uint32_T)rtb_Look2D_IR_U16) << 18);

  /* If: '<S26>/If' incorporates:
   *  Constant: '<S26>/SELQAIRREF'
   */
  if (SELQAIRREF != 0) {
    /* Outputs for IfAction SubSystem: '<S26>/If_QAirRef' incorporates:
     *  ActionPort: '<S35>/Action Port'
     */
    /* Product: '<S35>/Product1' incorporates:
     *  Constant: '<S26>/N_CYLINDER'
     *  Inport: '<Root>/Load'
     *  Inport: '<Root>/QAirRef'
     *  Product: '<S35>/Divide1'
     *  Product: '<S35>/Product'
     */
    rtb_Divide1_m = mul_u32_loSR((((uint32_T)((uint8_T)N_CYLINDER)) * QAirRef) /
      100U, Load, 3U);

    /* End of Outputs for SubSystem: '<S26>/If_QAirRef' */
  } else {
    /* Outputs for IfAction SubSystem: '<S26>/else_QAIRREFPERC' incorporates:
     *  ActionPort: '<S37>/Action Port'
     */
    /* DataTypeConversion: '<S37>/Data Type Conversion1' incorporates:
     *  Constant: '<S26>/N_CYLINDER'
     *  Constant: '<S26>/QAIRREFPERC'
     *  Product: '<S37>/Product'
     */
    tmp = ((uint32_T)((uint8_T)N_CYLINDER)) * QAIRREFPERC;
    if (tmp > 65535U) {
      tmp = 65535U;
    }

    /* Product: '<S37>/Product1' incorporates:
     *  DataTypeConversion: '<S37>/Data Type Conversion1'
     *  Inport: '<Root>/Load'
     */
    rtb_Divide1_m = ((tmp * Load) >> 2);

    /* End of Outputs for SubSystem: '<S26>/else_QAIRREFPERC' */
  }

  /* End of If: '<S26>/If' */

  /* Product: '<S26>/Divide' incorporates:
   *  DataStoreWrite: '<S26>/Data Store Write3'
   *  DataStoreWrite: '<S26>/Data Store Write6'
   */
  tmp = (rtb_Divide1_m == 0U) ? MAX_uint32_T : (TbCmi / rtb_Divide1_m);
  if (tmp > 65535U) {
    tmp = 65535U;
  }

  CmiGain = (uint16_T)tmp;

  /* End of Product: '<S26>/Divide' */

  /* DataStoreWrite: '<S26>/Data Store Write5' */
  QAirRefOut = rtb_Divide1_m;

  /* DataStoreWrite: '<S26>/Data Store Write1' incorporates:
   *  Constant: '<S26>/ZERO'
   */
  CmiOffset = 0;

  /* DataStoreWrite: '<S27>/Data Store Write2' */
  DPAtmPresF = rtb_FOF_Reset_S16_FXP_o1_h;

  /* DataStoreWrite: '<S27>/Data Store Write5' */
  DPAtmPres = rtb_Sum;

  /* Update for Memory: '<S27>/Memory1' */
  TrqEst_DWork.Memory1_PreviousInput = rtb_FOF_Reset_S16_FXP_o2;

  /* End of Outputs for SubSystem: '<S6>/Friction_Interpolation' */

  /* Outputs for Atomic SubSystem: '<S6>/Cmi_Calculation' */
  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle2' */
  /* Product: '<S18>/Product1' incorporates:
   *  Constant: '<S13>/MAXEFF'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/EffSAbase'
   *  Inport: '<Root>/QAirMinCyl'
   *  Inport: '<Root>/TbQAirGainCorr'
   */
  TrqEst_cmi_formula_cycle2((uint16_T)((((uint32_T)QAirMinCyl) * TbQAirGainCorr)
    >> 16), EffSAbase, EffLambda, ((uint16_T)MAXEFF),
    &TrqEst_B.cmi_formula_cycle2);

  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle2' */

  /* MinMax: '<S18>/MinMax' incorporates:
   *  Constant: '<S18>/ZERO'
   *  DataStoreWrite: '<S18>/Data Store Write1'
   */
  if (TrqEst_B.cmi_formula_cycle2.Divide4 > 0) {
    CmiTargetPMin = TrqEst_B.cmi_formula_cycle2.Divide4;
  } else {
    CmiTargetPMin = 0;
  }

  /* End of MinMax: '<S18>/MinMax' */

  /* Product: '<S18>/Product' incorporates:
   *  Inport: '<Root>/QAirMaxCyl'
   *  Inport: '<Root>/TbQAirGainCorr'
   */
  rtb_Divide2 = (uint16_T)((((uint32_T)QAirMaxCyl) * TbQAirGainCorr) >> 16);

  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle3' */
  /* Inport: '<Root>/EffSAbase' incorporates:
   *  Constant: '<S13>/MAXEFF'
   *  Inport: '<Root>/EffLambda'
   */
  TrqEst_cmi_formula_cycle2(rtb_Divide2, EffSAbase, EffLambda, ((uint16_T)MAXEFF),
    &TrqEst_B.cmi_formula_cycle3);

  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle3' */

  /* DataStoreWrite: '<S18>/Data Store Write13' */
  CmiTargetPMax = TrqEst_B.cmi_formula_cycle3.Divide4;

  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle5' */
  /* Inport: '<Root>/AccQAirBaseAvg' incorporates:
   *  Inport: '<Root>/EffCutoff'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/EffSAmin'
   */
  TrqEst_cmi_formula_cycle1(AccQAirBaseAvg, EffSAmin, EffLambda, EffCutoff,
    &TrqEst_B.cmi_formula_cycle5);

  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle5' */

  /* DataStoreWrite: '<S18>/Data Store Write2' */
  CmiTargetIMin = TrqEst_B.cmi_formula_cycle5.Divide4;

  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle' */
  /* DataTypeConversion: '<S19>/Data Type Conversion' incorporates:
   *  DataStoreRead: '<S19>/Data Store Read'
   *  Product: '<S19>/Divide1'
   */
  rtb_DataTypeConversion = (int32_T)((((uint32_T)rtb_Divide2) * CmiGain) >> 1);

  /* Product: '<S19>/Divide5' incorporates:
   *  Constant: '<S19>/N_CYLINDER'
   *  DataStoreRead: '<S19>/Data Store Read1'
   */
  rtb_Memory4 = (int16_T)(CmiOffset * ((uint8_T)N_CYLINDER));

  /* Sum: '<S19>/Add2' incorporates:
   *  DataTypeConversion: '<S19>/Data Type Conversion1'
   */
  rtb_DataTypeConversion = (rtb_DataTypeConversion >> 10) + rtb_Memory4;

  /* DataTypeConversion: '<S19>/Data Type Conversion2' */
  if (rtb_DataTypeConversion > 32767) {
    rtb_DataTypeConversion = 32767;
  }

  rtb_Memory4 = (int16_T)rtb_DataTypeConversion;

  /* End of DataTypeConversion: '<S19>/Data Type Conversion2' */

  /* Product: '<S19>/Divide2' incorporates:
   *  Constant: '<S13>/MAXEFF'
   *  Product: '<S19>/Divide3'
   */
  rtb_Divide2 = (uint16_T)((((uint32_T)((uint16_T)((((uint32_T)((uint16_T)MAXEFF))
    * ((uint16_T)MAXEFF)) >> 14))) * ((uint16_T)MAXEFF)) >> 14);

  /* Product: '<S19>/Divide4' incorporates:
   *  DataStoreWrite: '<S18>/Data Store Write3'
   */
  rtb_DataTypeConversion = ((rtb_Memory4 * rtb_Divide2) >> 14);
  if (rtb_DataTypeConversion > 32767) {
    rtb_DataTypeConversion = 32767;
  } else {
    if (rtb_DataTypeConversion < -32768) {
      rtb_DataTypeConversion = -32768;
    }
  }

  CmiEnv = (int16_T)rtb_DataTypeConversion;

  /* End of Product: '<S19>/Divide4' */
  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle' */

  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle6' */
  /* Inport: '<Root>/AccQAirBaseAvg' incorporates:
   *  Inport: '<Root>/EffCutoff'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/EffSAbase'
   */
  TrqEst_cmi_formula_cycle1(AccQAirBaseAvg, EffSAbase, EffLambda, EffCutoff,
    &TrqEst_B.cmi_formula_cycle6);

  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle6' */

  /* DataStoreWrite: '<S18>/Data Store Write4' */
  CmiEstBase = TrqEst_B.cmi_formula_cycle6.Divide4;

  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle1' */
  /* Inport: '<Root>/AccQAirAvg' incorporates:
   *  Inport: '<Root>/EffCutoff'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/EffSAReal'
   */
  TrqEst_cmi_formula_cycle1(AccQAirAvg, EffSAReal, EffLambda, EffCutoff,
    &TrqEst_B.cmi_formula_cycle1);

  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle1' */

  /* DataStoreWrite: '<S18>/Data Store Write5' */
  CmiEst = TrqEst_B.cmi_formula_cycle1.Divide4;

  /* Outputs for Atomic SubSystem: '<S18>/cmi_formula_cycle4' */
  /* Product: '<S18>/Product2' incorporates:
   *  Constant: '<S13>/MAXEFF'
   *  Inport: '<Root>/EffLambda'
   *  Inport: '<Root>/EffSAbase'
   *  Inport: '<Root>/QAirObj0'
   *  Inport: '<Root>/TbQAirGainCorr'
   */
  TrqEst_cmi_formula_cycle2((uint16_T)((((uint32_T)QAirObj0) * TbQAirGainCorr) >>
    16), EffSAbase, EffLambda, ((uint16_T)MAXEFF), &TrqEst_B.cmi_formula_cycle4);

  /* End of Outputs for SubSystem: '<S18>/cmi_formula_cycle4' */

  /* DataStoreWrite: '<S18>/Data Store Write6' */
  CmiMecTarget = TrqEst_B.cmi_formula_cycle4.Divide4;

  /* End of Outputs for SubSystem: '<S6>/Cmi_Calculation' */

  /* Outputs for Atomic SubSystem: '<S6>/Cmi2Cme' */
  /* Sum: '<S12>/Add3' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read'
   *  DataStoreRead: '<S12>/Data Store Read4'
   *  DataStoreWrite: '<S12>/Data Store Write11'
   */
  CmeEst = (int16_T)(CmiEst - CmfP);

  /* Switch: '<S12>/Switch1' incorporates:
   *  Constant: '<S12>/CM_1'
   *  Constant: '<S12>/DISTECLUTCH'
   *  Constant: '<S12>/TC_SPRING_UP'
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/StTcGear'
   *  Logic: '<S12>/Logical Operator'
   *  RelationalOperator: '<S12>/Relational Operator'
   */
  if (StTcGear == ((uint8_T)TC_SPRING_UP)) {
    rtb_Switch1 = 1U;
  } else {
    rtb_Switch1 = (uint8_T)((ClutchSignal != 0) || (DISTECLUTCH != 0));
  }

  /* End of Switch: '<S12>/Switch1' */

  /* Selector: '<S12>/Selector' incorporates:
   *  Inport: '<Root>/GearPos'
   *  Product: '<S12>/Product'
   */
  rtb_DataTypeConversion = (int32_T)(((uint32_T)GearPos) * rtb_Switch1);

  /* Switch: '<S12>/Switch' incorporates:
   *  Constant: '<S12>/CM_INIT'
   *  DataTypeConversion: '<S12>/Data Type Conversion1'
   *  Inport: '<Root>/InvGearRatio'
   *  Product: '<S12>/Product1'
   *  Selector: '<S12>/Selector'
   */
  if (InvGearRatio[(rtb_DataTypeConversion)] != 0) {
    /* Product: '<S12>/Product1' incorporates:
     *  DataStoreWrite: '<S12>/Data Store Write11'
     */
    rtb_DataTypeConversion = InvGearRatio[(rtb_DataTypeConversion)] * CmeEst;
    rtb_Memory4 = (int16_T)((((rtb_DataTypeConversion < 0) ? 4095 : 0) +
      rtb_DataTypeConversion) >> 12);
  } else {
    rtb_Memory4 = (int16_T)(((int16_T)CM_INIT) >> 1);
  }

  /* End of Switch: '<S12>/Switch' */

  /* DataStoreWrite: '<S12>/Data Store Write1' */
  CmeEstWheel = rtb_Memory4;

  /* Sum: '<S12>/Add2' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read'
   *  DataStoreRead: '<S12>/Data Store Read3'
   *  DataStoreWrite: '<S12>/Data Store Write2'
   */
  CmeTargetIMin = (int16_T)(CmiTargetIMin - CmfP);

  /* Memory: '<S12>/Memory' */
  rtb_DataTypeConversion = TrqEst_DWork.Memory_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S16>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S12>/KFCMEESTWHEEL'
   *  Constant: '<S12>/ZERO'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2_d,
                    rtb_Memory4, KFCMEESTWHEEL, rtb_Memory4, ((uint8_T)0U),
                    rtb_DataTypeConversion);

  /* DataStoreWrite: '<S12>/Data Store Write3' */
  CmeEstWheelF = rtb_FOF_Reset_S16_FXP_o1;

  /* Sum: '<S12>/Add4' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read'
   *  DataStoreRead: '<S12>/Data Store Read5'
   *  DataStoreWrite: '<S12>/Data Store Write4'
   */
  CmeTargetPMin = (int16_T)(CmiTargetPMin - CmfP);

  /* Sum: '<S12>/Add' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read'
   *  DataStoreRead: '<S12>/Data Store Read1'
   *  DataStoreWrite: '<S12>/Data Store Write8'
   */
  CmeMecTarget = (int16_T)(CmiMecTarget - CmfP);

  /* Sum: '<S12>/Add1' incorporates:
   *  DataStoreRead: '<S12>/Data Store Read'
   *  DataStoreRead: '<S12>/Data Store Read2'
   *  DataStoreWrite: '<S12>/Data Store Write9'
   */
  CmeEnv = (int16_T)(CmiEnv - CmfP);

  /* Memory: '<S15>/Memory4' */
  rtb_Memory4 = TrqEst_DWork.Memory4_PreviousInput;

  /* Sum: '<S15>/Add' incorporates:
   *  DataStoreWrite: '<S15>/Data Store Write5'
   */
  DeltaCmeEstWheelF = (int16_T)(rtb_FOF_Reset_S16_FXP_o1 - rtb_Memory4);

  /* Memory: '<S15>/Memory' */
  rtb_FOF_Reset_S16_FXP_o1_h = TrqEst_DWork.Memory_PreviousInput_m;

  /* Memory: '<S15>/Memory1' */
  rtb_Sum = TrqEst_DWork.Memory1_PreviousInput_o;

  /* Memory: '<S15>/Memory2' */
  rtb_DataTypeConversion1_c = TrqEst_DWork.Memory2_PreviousInput;

  /* Update for Memory: '<S12>/Memory' */
  TrqEst_DWork.Memory_PreviousInput = rtb_FOF_Reset_S16_FXP_o2_d;

  /* Update for Memory: '<S15>/Memory4' incorporates:
   *  Memory: '<S15>/Memory3'
   */
  TrqEst_DWork.Memory4_PreviousInput = TrqEst_DWork.Memory3_PreviousInput;

  /* Update for Memory: '<S15>/Memory' */
  TrqEst_DWork.Memory_PreviousInput_m = rtb_FOF_Reset_S16_FXP_o1;

  /* Update for Memory: '<S15>/Memory1' */
  TrqEst_DWork.Memory1_PreviousInput_o = rtb_FOF_Reset_S16_FXP_o1_h;

  /* Update for Memory: '<S15>/Memory2' */
  TrqEst_DWork.Memory2_PreviousInput = rtb_Sum;

  /* Update for Memory: '<S15>/Memory3' */
  TrqEst_DWork.Memory3_PreviousInput = rtb_DataTypeConversion1_c;

  /* End of Outputs for SubSystem: '<S6>/Cmi2Cme' */
}

/* Model step function */
void Trig_TrqEst_ev_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<S2>/Reset' */
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
  TrqEst_Reset();

  /* End of Outputs for SubSystem: '<S2>/Reset' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
}

/* Model step function */
void Trig_TrqEst_ev_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/Reset'
   */
  TrqEst_Reset();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_TrqEst_ev_PreHTDC(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PreHTDC' incorporates:
   *  SubSystem: '<S2>/PreHTDC'
   */
  TrqEst_PreHTDC();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreHTDC' */
}

/* Model step function */
void Trig_TrqEst_ev_PreTDC(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTDC' incorporates:
   *  SubSystem: '<S2>/PreTDC'
   */
  TrqEst_PreTDC();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTDC' */
}

/* Model step function */
void Trig_TrqEst_ev_T10ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S2>/T10ms'
   */
  TrqEst_T10ms();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
}

/* Model initialize function */
void TrqEst_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
void TrqEst_Init(void)
{
  TrqEst_Reset();
}

void TrqEst_NoSync(void)
{
  TrqEst_Reset();
}

#else

// Stub TrqEst
int16_T CmiPotEst;
extern uint16_T Load;
void Stub_CmiPotEst(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYLINDER;idx++) {
    CmiPotEst[idx] = Load;
  }
}

void TrqEst_NoSync(void)
{
  Stub_CmiPotEst();
}

void TrqEst_T10ms(void)
{
  Stub_CmiPotEst();
}

void TrqEst_Init(void)
{
  Stub_CmiPotEst();
}

void TrqEst_PreTDC(void)
{
  Stub_CmiPotEst();
}

void TrqEst_PreHTDC(void)
{
  Stub_CmiPotEst();
}

#endif                                 // _BUILD_TRQEST_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
