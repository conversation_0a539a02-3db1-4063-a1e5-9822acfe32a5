/*
 * File: FuelMgm.c
 *
 * Code generated for Simulink model 'FuelMgm'.
 *
 * Model version                  : 1.1006
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Oct  3 13:30:05 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "FuelMgm.h"
#include "FuelMgm_private.h"

/* user code (top of source file) */
/* System '<Root>/FuelMgm' */
#ifdef _BUILD_FUELMGM_

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_FuelMgm FuelMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_FuelMgm FuelMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T CntQFuelSplit;

/* FUELMGM.CntQFuelSplit: Counter to enable low bank switching */
uint16_T DPresInj;

/* FUELMGM.DPresInj: Injection pressure drop */
int16_T DQFObjCyl;

/* FUELMGM.DQFObjCyl: Injection Target Fuel mass delta */
int16_T DQFObjCylH;

/* FUELMGM.DQFObjCylH: Injection Target Fuel mass delta - HIGH INJECTOR BANK */
int16_T DQFilmCyl;

/* FUELMGM.DQFilmCyl: Fuel Film mass delta */
int16_T DQFilmCylH;

/* FUELMGM.DQFilmCylH: Fuel Film mass delta - HIGH INJECTOR BANK */
uint16_T FilmEnab;

/* FUELMGM.FilmEnab: Fuel Film Correction Enable Gain */
uint16_T FilmEnabH;

/* FUELMGM.FilmEnabH: Fuel Film Correction Enable Gain */
uint16_T FirstInjTime;

/* FUELMGM.FirstInjTime: Fuel Injection time */
uint16_T FirstSOI;

/* FUELMGM.FirstSOI: Start of injection angle */
uint8_T FlgInjTMin[8];

/* FUELMGM.FlgInjTMin: Minimum injection time saturation flag */
uint16_T FuelLt;

/* QFuel tot integral */
uint8_T FuelMgmcyl4calc;

/* FUELMGM.FuelMgmcyl4calc: FuelMgmcyl4calc */
uint8_T FuelMgmcyl4calcH;

/* FUELMGM.FuelMgmcyl4calcH: FuelMgmcyl4calcH */
uint8_T FuelMgmflagHTDC;

/* FUELMGM.FuelMgmflagHTDC: FuelMgmflagHTDC */
uint8_T FuelMgmflagTDC;

/* FUELMGM.FuelMgmflagTDC: FuelMgmflagTDC */
uint16_T GainFilm[4];

/* FUELMGM.GainFilm: Fuel Film Model Gain */
uint16_T GainFilmH;

/* FUELMGM.GainFilmH: Fuel Film Model Gain */
uint16_T GainInjT;

/* FUELMGM.GainInjT: Gain Inj Time */
uint32_T IDFuelMgm;

/* ID Version */
uint8_T InjEnable;

/* FUELMGM.InjEnable: Injection enable condition */
uint8_T InjEnableEMD;

/* FUELMGM.InjEnableEMD: Enable flag for EMD */
int16_T InjTVBat;

/* FUELMGM.InjTVBat: Injector time offset f(Vbattery) */
int16_T InjTVBatH;

/* FUELMGM.InjTVBatH: Injector time offset f(Vbattery) */
uint16_T InjTime[16];

/* FUELMGM.InjTime: Fuel Injection time */
uint16_T KFFilm[4];

/* FUELMGM.KFFilm: Fuel Film Model Filter Gain */
uint16_T KFFilmH;

/* FUELMGM.KFFilmH: Fuel Film Model Filter Gain */
int16_T OffInjT;

/* FUELMGM.OffInjT: Offset Inj Time */
uint16_T QFObj[8];

/* FUELMGM.QFObj: Injection Target Fuel mass */
uint16_T QFObjBase[8];

/* FUELMGM.QFObjBase: Injection Target Fuel mass based on QAir */
uint16_T QFObjCylH;

/* FUELMGM.QFObjCylH: Injection Target Fuel mass - HIGH INJECTOR BANK */
uint16_T QFObjOld[4];

/* FUELMGM.QFObjOld: Injection Target Fuel mass - old TDC value */
uint16_T QFilm[8];

/* FUELMGM.QFilm: Fuel Film mass */
uint16_T QFuel[8];

/* FUELMGM.QFuel: Injection fuel mass */
uint16_T QFuelAvg;

/* FUELMGM.QFuelAvg: Mean Injection Target Fuel mass */
uint16_T QFuelCyl;

/* FUELMGM.QFuelCyl: Injection fuel mass */
uint16_T QFuelCylH;

/* FUELMGM.QFuelCylH: Injection fuel mass - HIGH INJECTOR BANK */
uint16_T QFuelExtra;

/* QFuel extra */
uint32_T QFuelIntExtTot;

/* QFuel tot */
uint32_T QFuelIntLth;

/* QFuel tot integral */
uint32_T QFuelIntTot;

/* QFuel tot */
uint16_T QFuelLam;

/* FUELMGM.QFuelLam: Fuel mass calculated from lambda */
uint16_T QFuelLth;

/* FUELMGM.QFuelLth: Injected fuel volume */
int16_T QFuelSplitFrac;

/* FUELMGM.QFuelSplitFrac: Fuel mass fraction for low bank */
int16_T QFuelSplitFracH;

/* FUELMGM.QFuelSplitFracH: Fuel mass fraction for high bank */
int16_T QFuelSplitFracRL;

/* FUELMGM.QFuelSplitFracRL: Fuel mass fraction for low bank - RATE LIMITER OUT */
int16_T QFuelSplitFracTB;

/* FUELMGM.QFuelSplitFracTB: Fuel mass fraction for low bank - TABLE VALUE CONVERTED */
uint16_T QFuelTot;

/* FUELMGM.QFuelTot: Fuel mass calculated from lambda */
uint16_T SOI[16];

/* FUELMGM.SOI: Start of injection angle */
int8_T StQFAcc[4];

/* FUELMGM.StQFAcc: Fuel film transient state */
int8_T StQFAccH;

/* FUELMGM.StQFAccH: Fuel film transient state - HIGH INJECTOR BANK */
uint8_T StQFuelSplit;

/* FUELMGM.StQFuelSplit: Fuel film transient state */
uint16_T XFilm[4];

/* FUELMGM.XFilm: Fuel Film Model X Factor */
uint16_T XFilmH;

/* FUELMGM.XFilmH: Fuel Film Model X Factor */

/* Declare variables for internal data of system '<S1>/TDC' */
rtB_TDC_FuelMgm FuelMgm_TDC_B;
rtDW_TDC_FuelMgm FuelMgm_TDC_DW;

/* Declare variables for internal data of system '<S1>/T100ms' */
rtB_T100ms_FuelMgm FuelMgm_T100ms_B;
rtDW_T100ms_FuelMgm FuelMgm_T100ms_DW;

/* Declare variables for internal data of system '<S1>/HTDC' */
rtB_HTDC_FuelMgm FuelMgm_HTDC_B;

/* Declare variables for internal data of system '<S1>/PreTDC' */
rtB_PreTDC_FuelMgm FuelMgm_PreTDC_B;

/*
 * Output and update for atomic system:
 *    '<S3>/InjEnable_Calc'
 *    '<S5>/InjEnable_Calc'
 *    '<S7>/InjEnable_Calc'
 */
void FuelMgm_InjEnable_Calc(uint8_T rtu_KeySignal, uint8_T rtu_CrashSignal,
  uint8_T rtu_StopSignal, uint8_T rtu_TrestleSignal, const uint8_T rtu_VtRec[22],
  uint8_T rtu_FlgSteperRdy, uint8_T rtu_S2FlgDisLRelay, uint8_T
  rtu_S3FlgAllowStart, uint8_T rtu_S3FlgDisLRelay, boolean_T rtu_InjEnableIMMO,
  uint8_T rtu_GearPos)
{
  /* Logic: '<S69>/Logical Operator' incorporates:
   *  Constant: '<S69>/GEAR_N'
   *  DataStoreWrite: '<S69>/Data Store Write'
   *  Logic: '<S69>/Logical Operator1'
   *  Logic: '<S69>/Logical Operator10'
   *  Logic: '<S69>/Logical Operator2'
   *  Logic: '<S69>/Logical Operator3'
   *  Logic: '<S69>/Logical Operator6'
   *  Logic: '<S69>/Logical Operator7'
   *  RelationalOperator: '<S69>/Relational Operator'
   */
  InjEnableEMD = (uint8_T)((rtu_CrashSignal == 0) && (rtu_StopSignal == 0) &&
    (rtu_S2FlgDisLRelay == 0) && (rtu_S3FlgDisLRelay == 0) && (rtu_KeySignal !=
    0) && (rtu_S3FlgAllowStart != 0) && rtu_InjEnableIMMO && ((rtu_TrestleSignal
    == 0) || (rtu_GearPos == ((uint8_T)GEAR_N))));

  /* Logic: '<S69>/Logical Operator4' incorporates:
   *  Constant: '<S69>/REC_ENG_OFF'
   *  DataStoreWrite: '<S69>/Data Store Write'
   *  DataStoreWrite: '<S69>/Data Store Write1'
   *  Logic: '<S69>/Logical Operator5'
   *  Selector: '<S69>/Selector'
   */
  InjEnable = (uint8_T)((InjEnableEMD != 0) && (rtu_FlgSteperRdy != 0) &&
                        (rtu_VtRec[((uint8_T)REC_ENG_OFF)] == 0));
}

/* Output and update for atomic system: '<S3>/Reset' */
void FuelMgm_Reset(void)
{
  int32_T idx;
  int32_T idx2;
  uint32_T SOI_tmp;

  /* DataStoreWrite: '<S70>/Data Store Write' incorporates:
   *  Constant: '<S70>/ONE'
   */
  QFuelSplitFrac = 16384;

  /* DataStoreWrite: '<S70>/Data Store Write20' incorporates:
   *  Constant: '<S70>/ONE'
   */
  QFuelSplitFracRL = 16384;

  /* DataStoreWrite: '<S70>/Data Store Write1' incorporates:
   *  Constant: '<S70>/ZERO1'
   */
  FirstSOI = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write11' incorporates:
   *  Constant: '<S70>/ZERO11'
   */
  QFuelCyl = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write12' incorporates:
   *  Constant: '<S70>/ZERO12'
   */
  DQFObjCyl = 0;

  /* DataStoreWrite: '<S70>/Data Store Write13' incorporates:
   *  Constant: '<S70>/ZERO13'
   */
  DQFilmCyl = 0;

  /* DataStoreWrite: '<S70>/Data Store Write15' incorporates:
   *  Constant: '<S70>/ZERO14'
   */
  DPresInj = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write16' incorporates:
   *  Constant: '<S70>/ZERO15'
   */
  QFuelAvg = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write17' incorporates:
   *  Constant: '<S70>/ZERO5'
   */
  KFFilmH = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write18' incorporates:
   *  Constant: '<S70>/LOW_BANK'
   */
  StQFuelSplit = ((uint8_T)LOW_BANK);

  /* DataStoreWrite: '<S70>/Data Store Write19' incorporates:
   *  Constant: '<S70>/ZERO'
   */
  QFuelSplitFracH = 0;

  /* DataStoreWrite: '<S70>/Data Store Write2' incorporates:
   *  Constant: '<S70>/ZERO2'
   */
  FirstInjTime = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write21' incorporates:
   *  Constant: '<S70>/ZERO17'
   */
  CntQFuelSplit = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write3' incorporates:
   *  Constant: '<S70>/ZERO16'
   */
  XFilmH = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write4' incorporates:
   *  Constant: '<S70>/ZERO3'
   */
  FilmEnabH = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write5' incorporates:
   *  Constant: '<S70>/ZERO4'
   */
  GainFilmH = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write6' incorporates:
   *  Constant: '<S70>/ZERO6'
   */
  FilmEnab = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write7' incorporates:
   *  Constant: '<S70>/ZERO7'
   */
  GainInjT = 0U;

  /* DataStoreWrite: '<S70>/Data Store Write8' incorporates:
   *  Constant: '<S70>/ZERO8'
   */
  OffInjT = 0;

  /* Constant: '<S70>/ID_FUEL_MGM' */
  IDFuelMgm = ID_FUEL_MGM;

  /* Chart: '<S70>/Reset_Vett' */
  /* Gateway: FuelMgm/Init/Reset/Reset_Vett */
  /* During: FuelMgm/Init/Reset/Reset_Vett */
  /* Entry Internal: FuelMgm/Init/Reset/Reset_Vett */
  /* Transition: '<S72>:1' */
  for (idx = 0; (uint32_T)idx < ((uint8_T)N_CYL_MAX); idx++) {
    /* Transition: '<S72>:11' */
    QFuel[(uint32_T)idx] = 0U;
    QFObj[(uint32_T)idx] = 0U;
    QFObjBase[(uint32_T)idx] = 0U;
    QFilm[(uint32_T)idx] = 0U;
    FlgInjTMin[(uint32_T)idx] = 0U;
    idx2 = 0;
    if ((uint32_T)idx < ((uint8_T)N_CYL_MAX_EM)) {
      /* Transition: '<S72>:30' */
      XFilm[(uint32_T)idx] = 0U;
      KFFilm[(uint32_T)idx] = 0U;
      GainFilm[(uint32_T)idx] = 16384U;
      QFObjOld[(uint32_T)idx] = 0U;
      StQFAcc[(uint32_T)idx] = ((int8_T)ST_QF_STAB);
    } else {
      /* Transition: '<S72>:31' */
    }

    while ((uint32_T)idx2 < ((uint8_T)N_INJ_PRG)) {
      /* Transition: '<S72>:17' */
      SOI_tmp = idx + (uint32_T)idx2 * ((uint8_T)N_CYL_MAX);
      SOI[SOI_tmp] = 0U;
      InjTime[SOI_tmp] = 0U;
      idx2++;
    }

    /* Transition: '<S72>:18' */
  }

  /* End of Chart: '<S70>/Reset_Vett' */
  /* Transition: '<S72>:28' */
}

/*
 * Output and update for atomic system:
 *    '<S3>/VBat_Offset_Calc'
 *    '<S7>/VBat_Offset_Calc'
 */
void FuelMgm_VBat_Offset_Calc(uint16_T rtu_VBattery)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_S16_U16;
  uint8_T rtb_Conversion3_ismu;

  /* DataTypeConversion: '<S74>/Conversion3' incorporates:
   *  Constant: '<S71>/BKBATINJT_dim'
   */
  rtb_Conversion3_ismu = (uint8_T)BKBATINJT_dim;

  /* S-Function (LookUp_S16_U16): '<S74>/LookUp_S16_U16' incorporates:
   *  Constant: '<S71>/BKBATINJT'
   *  Constant: '<S71>/VTBATINJTH'
   */
  LookUp_S16_U16( &rtb_LookUp_S16_U16, &VTBATINJTH[0], rtu_VBattery, &BKBATINJT
                 [0], rtb_Conversion3_ismu);

  /* DataStoreWrite: '<S71>/Data Store Write1' */
  InjTVBatH = rtb_LookUp_S16_U16;

  /* DataTypeConversion: '<S73>/Conversion3' incorporates:
   *  Constant: '<S71>/BKBATINJT_dim'
   */
  rtb_Conversion3_ismu = (uint8_T)BKBATINJT_dim;

  /* S-Function (LookUp_S16_U16): '<S73>/LookUp_S16_U16' incorporates:
   *  Constant: '<S71>/BKBATINJT'
   *  Constant: '<S71>/VTBATINJT'
   */
  LookUp_S16_U16( &rtb_LookUp_S16_U16, &VTBATINJT[0], rtu_VBattery, &BKBATINJT[0],
                 rtb_Conversion3_ismu);

  /* DataStoreWrite: '<S71>/Data Store Write14' */
  InjTVBat = rtb_LookUp_S16_U16;
}

/* Output and update for function-call system: '<S1>/Init' */
void FuelMgm_Init(void)
{
  /* Outputs for Atomic SubSystem: '<S3>/InjEnable_Calc' */

  /* Inport: '<Root>/KeySignal' incorporates:
   *  Inport: '<Root>/CrashSignal'
   *  Inport: '<Root>/FlgSteperRdy'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/InjEnableAT'
   *  Inport: '<Root>/InjEnableIMMO'
   *  Inport: '<Root>/S2FlgDisLRelay'
   *  Inport: '<Root>/S3FlgAllowStart'
   *  Inport: '<Root>/S3FlgDisLRelay'
   *  Inport: '<Root>/StopSignal'
   *  Inport: '<Root>/TrestleSignal'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S3>/Logical Operator'
   */
  FuelMgm_InjEnable_Calc(KeySignal, CrashSignal, StopSignal, TrestleSignal,
    (&(VtRec[0])), FlgSteperRdy, S2FlgDisLRelay, S3FlgAllowStart, S3FlgDisLRelay,
    (InjEnableAT != 0) && (InjEnableIMMO != 0), GearPos);

  /* End of Outputs for SubSystem: '<S3>/InjEnable_Calc' */

  /* Outputs for Atomic SubSystem: '<S3>/VBat_Offset_Calc' */

  /* Inport: '<Root>/VBattery' */
  FuelMgm_VBat_Offset_Calc(VBattery);

  /* End of Outputs for SubSystem: '<S3>/VBat_Offset_Calc' */

  /* Outputs for Atomic SubSystem: '<S3>/Reset' */
  FuelMgm_Reset();

  /* End of Outputs for SubSystem: '<S3>/Reset' */
}

/* Output and update for atomic system: '<S5>/First_Inj_Calc' */
void FuelMgm_First_Inj_Calc(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_U16_S16;
  int32_T idx;
  uint8_T rtb_Conversion3;

  /* DataStoreWrite: '<S130>/Data Store Write1' incorporates:
   *  Constant: '<S130>/FIRSTSOI'
   */
  FirstSOI = FIRSTSOI;

  /* DataTypeConversion: '<S134>/Conversion3' incorporates:
   *  Constant: '<S130>/BKTWFUEL_dim'
   */
  rtb_Conversion3 = (uint8_T)BKTWFUEL_dim;

  /* S-Function (LookUp_U16_S16): '<S134>/LookUp_U16_S16' incorporates:
   *  Constant: '<S130>/BKTWFUEL'
   *  Constant: '<S130>/VTFIRSTINJTIME'
   *  Inport: '<Root>/TWater'
   */
  LookUp_U16_S16( &rtb_LookUp_U16_S16, &VTFIRSTINJTIME[0], TWater, &BKTWFUEL[0],
                 rtb_Conversion3);

  /* DataStoreWrite: '<S130>/Data Store Write2' */
  FirstInjTime = rtb_LookUp_U16_S16;

  /* Outputs for Enabled SubSystem: '<S130>/FirstInj_Calc' incorporates:
   *  EnablePort: '<S132>/Enable'
   */
  /* DataStoreRead: '<S130>/Data Store Read' */
  if (InjEnable > 0) {
    /* Chart: '<S132>/Assign_First_InjTime' incorporates:
     *  Constant: '<S132>/ZERO'
     */
    /* Gateway: FuelMgm/Sync/First_Inj_Calc/FirstInj_Calc/Assign_First_InjTime */
    /* During: FuelMgm/Sync/First_Inj_Calc/FirstInj_Calc/Assign_First_InjTime */
    /* Entry Internal: FuelMgm/Sync/First_Inj_Calc/FirstInj_Calc/Assign_First_InjTime */
    /* Transition: '<S135>:1' */
    for (idx = 0; (uint32_T)idx < ((uint8_T)N_CYLINDER); idx++) {
      /* Transition: '<S135>:11' */
      InjTime[(uint32_T)idx] = rtb_LookUp_U16_S16;
      InjTime[(uint32_T)idx + ((uint8_T)N_CYL_MAX)] = 0U;
    }

    /* End of Chart: '<S132>/Assign_First_InjTime' */
    /* Transition: '<S135>:14' */
  }

  /* End of Outputs for SubSystem: '<S130>/FirstInj_Calc' */

  /* Outputs for Enabled SubSystem: '<S130>/FirstSOI_Calc' incorporates:
   *  EnablePort: '<S133>/Enable'
   */
  if (InjEnable > 0) {
    /* Chart: '<S133>/Assign_First_SOI' incorporates:
     *  Constant: '<S130>/FIRSTSOI'
     *  Constant: '<S133>/EXTRASOI'
     */
    /* Gateway: FuelMgm/Sync/First_Inj_Calc/FirstSOI_Calc/Assign_First_SOI */
    /* During: FuelMgm/Sync/First_Inj_Calc/FirstSOI_Calc/Assign_First_SOI */
    /* Entry Internal: FuelMgm/Sync/First_Inj_Calc/FirstSOI_Calc/Assign_First_SOI */
    /* Transition: '<S136>:1' */
    for (idx = 0; (uint32_T)idx < ((uint8_T)N_CYLINDER); idx++) {
      /* Transition: '<S136>:11' */
      SOI[(uint32_T)idx] = FIRSTSOI;
      SOI[(uint32_T)idx + ((uint8_T)N_CYL_MAX)] = EXTRASOI;
    }

    /* End of Chart: '<S133>/Assign_First_SOI' */
    /* Transition: '<S136>:14' */
  }

  /* End of DataStoreRead: '<S130>/Data Store Read' */
  /* End of Outputs for SubSystem: '<S130>/FirstSOI_Calc' */
}

/* Output and update for function-call system: '<S1>/Sync' */
void FuelMgm_Sync(void)
{
  /* Outputs for Atomic SubSystem: '<S5>/InjEnable_Calc' */

  /* Inport: '<Root>/KeySignal' incorporates:
   *  Inport: '<Root>/CrashSignal'
   *  Inport: '<Root>/FlgSteperRdy'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/InjEnableAT'
   *  Inport: '<Root>/InjEnableIMMO'
   *  Inport: '<Root>/S2FlgDisLRelay'
   *  Inport: '<Root>/S3FlgAllowStart'
   *  Inport: '<Root>/S3FlgDisLRelay'
   *  Inport: '<Root>/StopSignal'
   *  Inport: '<Root>/TrestleSignal'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S5>/Logical Operator'
   */
  FuelMgm_InjEnable_Calc(KeySignal, CrashSignal, StopSignal, TrestleSignal,
    (&(VtRec[0])), FlgSteperRdy, S2FlgDisLRelay, S3FlgAllowStart, S3FlgDisLRelay,
    (InjEnableAT != 0) && (InjEnableIMMO != 0), GearPos);

  /* End of Outputs for SubSystem: '<S5>/InjEnable_Calc' */

  /* Outputs for Atomic SubSystem: '<S5>/First_Inj_Calc' */
  FuelMgm_First_Inj_Calc();

  /* End of Outputs for SubSystem: '<S5>/First_Inj_Calc' */
}

/* Output and update for function-call system: '<S1>/T10ms' */
void FuelMgm_T10ms(void)
{
  /* Outputs for Atomic SubSystem: '<S7>/InjEnable_Calc' */

  /* Inport: '<Root>/KeySignal' incorporates:
   *  Inport: '<Root>/CrashSignal'
   *  Inport: '<Root>/FlgSteperRdy'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/InjEnableAT'
   *  Inport: '<Root>/InjEnableIMMO'
   *  Inport: '<Root>/S2FlgDisLRelay'
   *  Inport: '<Root>/S3FlgAllowStart'
   *  Inport: '<Root>/S3FlgDisLRelay'
   *  Inport: '<Root>/StopSignal'
   *  Inport: '<Root>/TrestleSignal'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S7>/Logical Operator'
   */
  FuelMgm_InjEnable_Calc(KeySignal, CrashSignal, StopSignal, TrestleSignal,
    (&(VtRec[0])), FlgSteperRdy, S2FlgDisLRelay, S3FlgAllowStart, S3FlgDisLRelay,
    (InjEnableAT != 0) && (InjEnableIMMO != 0), GearPos);

  /* End of Outputs for SubSystem: '<S7>/InjEnable_Calc' */

  /* Outputs for Atomic SubSystem: '<S7>/VBat_Offset_Calc' */

  /* Inport: '<Root>/VBattery' */
  FuelMgm_VBat_Offset_Calc(VBattery);

  /* End of Outputs for SubSystem: '<S7>/VBat_Offset_Calc' */
}

/* Output and update for atomic system: '<S8>/Film_Par_Calc_LB' */
void FuelMgm_Film_Par_Calc_LB(void)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_IR_U8;
  uint16_T rtb_Look2D_IR_U8_eblc;
  uint16_T rtb_Sum6;
  uint16_T rtb_Sum1;
  uint8_T rtb_Conversion6;
  uint8_T rtb_Conversion7;

  /* Switch: '<S171>/Switch1' incorporates:
   *  Constant: '<S171>/0_FIX_14'
   *  Constant: '<S171>/ENFUELFILM'
   *  Inport: '<Root>/EndStartFlg'
   *  Logic: '<S171>/Logical Operator'
   */
  if ((EndStartFlg != 0) && (ENFUELFILM != 0)) {
    /* Sum: '<S171>/Sum1' incorporates:
     *  Constant: '<S171>/FILMENSTEP'
     *  DataStoreRead: '<S171>/Data Store Read'
     */
    rtb_Sum6 = (uint16_T)((uint32_T)FILMENSTEP + FilmEnab);

    /* MinMax: '<S171>/MinMax3' incorporates:
     *  Constant: '<S171>/1_FIX_14'
     */
    if (16384 < rtb_Sum6) {
      rtb_Sum6 = 16384U;
    }

    /* End of MinMax: '<S171>/MinMax3' */
  } else {
    rtb_Sum6 = 0U;
  }

  /* End of Switch: '<S171>/Switch1' */

  /* Switch: '<S171>/Switch2' incorporates:
   *  Constant: '<S171>/1_FIX_1'
   *  Constant: '<S171>/DPRESFLEN'
   *  Constant: '<S171>/THFOMINFLEN'
   *  DataStoreWrite: '<S171>/Data Store Write'
   *  Logic: '<S171>/Logical Operator1'
   *  RelationalOperator: '<S171>/Relational Operator'
   *  RelationalOperator: '<S171>/Relational Operator1'
   */
  if ((rtb_Sum6 <= THFOMINFLEN) || (FuelMgm_TDC_B.RatioCalcS16_joa2 <= DPRESFLEN))
  {
    FilmEnab = rtb_Sum6;
  } else {
    FilmEnab = 16384U;
  }

  /* End of Switch: '<S171>/Switch2' */

  /* DataTypeConversion: '<S177>/Conversion6' incorporates:
   *  Constant: '<S171>/BKRPMFUEL_dim'
   */
  rtb_Conversion6 = (uint8_T)BKRPMFUEL_dim;

  /* DataTypeConversion: '<S177>/Conversion7' incorporates:
   *  Constant: '<S171>/BKPRESFUEL_dim'
   */
  rtb_Conversion7 = (uint8_T)BKPRESFUEL_dim;

  /* S-Function (Look2D_IR_U8): '<S177>/Look2D_IR_U8' incorporates:
   *  Constant: '<S171>/TBXFILMACC'
   */
  Look2D_IR_U8( &rtb_Sum6, &TBXFILMACC[0],
               FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
               FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, rtb_Conversion6,
               FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1,
               FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2, rtb_Conversion7);

  /* Product: '<S171>/Product2' incorporates:
   *  DataStoreWrite: '<S171>/Data Store Write'
   *  Product: '<S171>/Product1'
   */
  rtb_Sum6 = (uint16_T)(((uint32_T)FilmEnab * (uint16_T)(((uint32_T)rtb_Sum6 *
    FuelMgm_TDC_B.Selector) >> 12)) >> 14);

  /* MinMax: '<S171>/MinMax1' incorporates:
   *  Constant: '<S171>/MAX_XFILM'
   */
  if (rtb_Sum6 < ((int16_T)MAX_XFILM)) {
  } else {
    rtb_Sum6 = (uint16_T)((int16_T)MAX_XFILM);
  }

  /* End of MinMax: '<S171>/MinMax1' */

  /* Chart: '<S171>/Assign_XFilm' */
  /* Gateway: FuelMgm/TDC/Film_Par_Calc_LB/XFilm_Calculation_LB/Assign_XFilm */
  /* During: FuelMgm/TDC/Film_Par_Calc_LB/XFilm_Calculation_LB/Assign_XFilm */
  /* Entry Internal: FuelMgm/TDC/Film_Par_Calc_LB/XFilm_Calculation_LB/Assign_XFilm */
  /* Transition: '<S176>:1' */
  XFilm[FuelMgmcyl4calc] = rtb_Sum6;

  /* Sum: '<S171>/Sum6' incorporates:
   *  Constant: '<S171>/1_FIX_15'
   */
  rtb_Sum6 = (uint16_T)(32768 - rtb_Sum6);

  /* Chart: '<S169>/Assign_GainFilm' incorporates:
   *  Product: '<S169>/Product'
   */
  /* Gateway: FuelMgm/TDC/Film_Par_Calc_LB/GainFilm_Calc_LB/Assign_GainFilm */
  /* During: FuelMgm/TDC/Film_Par_Calc_LB/GainFilm_Calc_LB/Assign_GainFilm */
  /* Entry Internal: FuelMgm/TDC/Film_Par_Calc_LB/GainFilm_Calc_LB/Assign_GainFilm */
  /* Transition: '<S172>:1' */
  GainFilm[FuelMgmcyl4calc] = (uint16_T)(536870912U / rtb_Sum6);

  /* Switch: '<S170>/Switch1' incorporates:
   *  Constant: '<S170>/ST_QF_DEC'
   *  DataStoreRead: '<S170>/Data Store Read'
   *  DataStoreRead: '<S170>/Data Store Read3'
   *  RelationalOperator: '<S170>/Relational Operator4'
   *  Selector: '<S170>/Selector1'
   */
  if (StQFAcc[FuelMgmcyl4calc] != ((int8_T)ST_QF_DEC)) {
    /* DataTypeConversion: '<S174>/Conversion7' incorporates:
     *  Constant: '<S170>/BKPRESFUEL_dim'
     */
    rtb_Conversion6 = (uint8_T)BKPRESFUEL_dim;

    /* DataTypeConversion: '<S174>/Conversion6' incorporates:
     *  Constant: '<S170>/BKRPMFUEL_dim'
     */
    rtb_Conversion7 = (uint8_T)BKRPMFUEL_dim;

    /* S-Function (Look2D_IR_U8): '<S174>/Look2D_IR_U8' incorporates:
     *  Constant: '<S170>/TBTAUFILMACC'
     */
    Look2D_IR_U8( &rtb_Look2D_IR_U8, &TBTAUFILMACC[0],
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, rtb_Conversion7,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2, rtb_Conversion6);
    rtb_Sum1 = rtb_Look2D_IR_U8;
  } else {
    /* DataTypeConversion: '<S175>/Conversion7' incorporates:
     *  Constant: '<S170>/BKPRESFUEL_dim'
     */
    rtb_Conversion6 = (uint8_T)BKPRESFUEL_dim;

    /* DataTypeConversion: '<S175>/Conversion6' incorporates:
     *  Constant: '<S170>/BKRPMFUEL_dim'
     */
    rtb_Conversion7 = (uint8_T)BKRPMFUEL_dim;

    /* S-Function (Look2D_IR_U8): '<S175>/Look2D_IR_U8' incorporates:
     *  Constant: '<S170>/TBTAUFILMDEC'
     */
    Look2D_IR_U8( &rtb_Look2D_IR_U8_eblc, &TBTAUFILMDEC[0],
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, rtb_Conversion7,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2, rtb_Conversion6);
    rtb_Sum1 = rtb_Look2D_IR_U8_eblc;
  }

  /* End of Switch: '<S170>/Switch1' */

  /* Chart: '<S170>/Assign_KFilm' incorporates:
   *  Constant: '<S170>/1_FIX_8'
   *  Product: '<S170>/Product3'
   *  Product: '<S170>/Product4'
   *  Product: '<S170>/Product5'
   *  Sum: '<S170>/Sum1'
   */
  /* Gateway: FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB/Assign_KFilm */
  /* During: FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB/Assign_KFilm */
  /* Entry Internal: FuelMgm/TDC/Film_Par_Calc_LB/KFFilm_Calc_LB/Assign_KFilm */
  /* Transition: '<S173>:1' */
  KFFilm[FuelMgmcyl4calc] = (uint16_T)((uint16_T)(65536U / (uint16_T)((uint16_T)
    (((uint32_T)rtb_Sum6 * (uint16_T)(((uint32_T)rtb_Sum1 *
    FuelMgm_TDC_B.Look2D_IR_U8) >> 12)) >> 15) + 256U)) << 7);
}

/*
 * Output and update for atomic system:
 *    '<S8>/InjTime_Calc'
 *    '<S2>/InjTime_Calc'
 *    '<S4>/InjTime_Calc'
 */
void FuelMgm_InjTime_Calc(uint16_T rtu_Rpm, uint16_T rtu_Load, uint16_T
  rtu_PresAtm, uint16_T rtu_PresInj, uint8_T rtu_AbsTdc, uint8_T rtu_AbsHTdc,
  rtB_InjTime_Calc_FuelMgm *localB)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S16;
  uint16_T rtb_DataTypeConversion;
  uint16_T rtb_MinMax2_p0am;
  uint16_T rtb_MinMax2_aqgr;
  uint16_T rtb_Sum2_evwq;
  uint16_T rtb_MinMax3_a5it;
  uint16_T rtb_Switch3_nzxn;
  uint8_T rtb_Conversion7_dgke;
  uint8_T rtb_Conversion6_icru;
  uint16_T rtb_Look2D_U8_U16_U16;
  int32_T qY;
  uint32_T tmp;

  /* Outputs for Atomic SubSystem: '<S149>/InjTime_cyl' */
  /* Outputs for Atomic SubSystem: '<S178>/Fuel_Film_Compensator' */
  /* Selector: '<S180>/Selector3' incorporates:
   *  DataStoreRead: '<S180>/Data Store Read2'
   *  DataStoreRead: '<S180>/Data Store Read4'
   */
  rtb_DataTypeConversion = KFFilm[FuelMgmcyl4calc];

  /* DataTypeConversion: '<S180>/Data Type Conversion' incorporates:
   *  Constant: '<S180>/1_FIX_15'
   *  DataStoreRead: '<S178>/Data Store Read1'
   *  DataStoreRead: '<S178>/Data Store Read4'
   *  DataStoreRead: '<S178>/Data Store Read6'
   *  DataStoreRead: '<S180>/Data Store Read3'
   *  DataStoreRead: '<S180>/Data Store Read4'
   *  Product: '<S180>/Product2'
   *  Product: '<S180>/Product3'
   *  Product: '<S180>/Product9'
   *  Selector: '<S178>/Selector1'
   *  Selector: '<S178>/Selector2'
   *  Selector: '<S180>/Selector1'
   *  Sum: '<S180>/Sum1'
   *  Sum: '<S180>/Sum2'
   */
  tmp = ((((uint32_T)QFObj[FuelMgmcyl4calc] * (uint16_T)(((uint32_T)
             XFilm[FuelMgmcyl4calc] * rtb_DataTypeConversion) >> 15)) >> 1) +
         (((uint32_T)(uint16_T)(32768 - rtb_DataTypeConversion) *
           QFilm[FuelMgmcyl4calc]) >> 1)) >> 14;
  if (tmp > 65535U) {
    tmp = 65535U;
  }

  rtb_DataTypeConversion = (uint16_T)tmp;

  /* End of DataTypeConversion: '<S180>/Data Type Conversion' */

  /* MinMax: '<S180>/MinMax3' incorporates:
   *  Constant: '<S180>/MAXQFILM'
   */
  if (MAXQFILM < rtb_DataTypeConversion) {
    rtb_MinMax3_a5it = MAXQFILM;
  } else {
    rtb_MinMax3_a5it = rtb_DataTypeConversion;
  }

  /* End of MinMax: '<S180>/MinMax3' */

  /* Selector: '<S180>/Selector2' incorporates:
   *  DataStoreRead: '<S180>/Data Store Read1'
   *  DataStoreRead: '<S180>/Data Store Read4'
   */
  rtb_DataTypeConversion = GainFilm[FuelMgmcyl4calc];

  /* Switch: '<S180>/Switch3' incorporates:
   *  Constant: '<S180>/ENQFOBJBASE'
   *  Sum: '<S180>/Sum4'
   *  Sum: '<S180>/Sum6'
   */
  if (ENQFOBJBASE != 0) {
    /* Sum: '<S180>/Sum6' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read4'
     *  DataStoreRead: '<S178>/Data Store Read6'
     *  Product: '<S180>/Product1'
     *  Product: '<S180>/Product12'
     *  Selector: '<S178>/Selector1'
     */
    qY = (int32_T)(((uint32_T)rtb_DataTypeConversion * QFObj[FuelMgmcyl4calc]) >>
                   14) - (int32_T)(((uint32_T)rtb_MinMax3_a5it *
      rtb_DataTypeConversion) >> 14);
    if (qY < 0) {
      qY = 0;
    } else {
      if (qY > 65535) {
        qY = 65535;
      }
    }

    /* Sum: '<S180>/Sum3' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read4'
     *  DataStoreRead: '<S178>/Data Store Read6'
     *  Selector: '<S178>/Selector1'
     *  Sum: '<S180>/Sum6'
     */
    qY -= QFObj[FuelMgmcyl4calc];
    if (qY > 32767) {
      qY = 32767;
    } else {
      if (qY < -32768) {
        qY = -32768;
      }
    }

    /* Sum: '<S180>/Sum4' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read4'
     *  DataStoreRead: '<S178>/Data Store Read7'
     *  Selector: '<S178>/Selector6'
     *  Sum: '<S180>/Sum3'
     */
    qY += QFObjBase[FuelMgmcyl4calc];
    if (qY < 0) {
      qY = 0;
    } else {
      if (qY > 65535) {
        qY = 65535;
      }
    }

    rtb_Switch3_nzxn = (uint16_T)qY;
  } else {
    /* Sum: '<S180>/Sum6' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read4'
     *  DataStoreRead: '<S178>/Data Store Read6'
     *  Product: '<S180>/Product1'
     *  Product: '<S180>/Product12'
     *  Selector: '<S178>/Selector1'
     */
    qY = (int32_T)(((uint32_T)rtb_DataTypeConversion * QFObj[FuelMgmcyl4calc]) >>
                   14) - (int32_T)(((uint32_T)rtb_MinMax3_a5it *
      rtb_DataTypeConversion) >> 14);
    if (qY < 0) {
      qY = 0;
    } else {
      if (qY > 65535) {
        qY = 65535;
      }
    }

    rtb_Switch3_nzxn = (uint16_T)qY;
  }

  /* End of Switch: '<S180>/Switch3' */
  /* End of Outputs for SubSystem: '<S178>/Fuel_Film_Compensator' */

  /* Outputs for Atomic SubSystem: '<S178>/Injection_Time_Calculation' */
  /* Outputs for Atomic SubSystem: '<S183>/Injector_Model' */
  /* Sum: '<S190>/Sum3' incorporates:
   *  Constant: '<S190>/DPRESINJ0'
   */
  rtb_Look2D_U8_U16_U16 = (uint16_T)((DPRESINJ0 + rtu_PresAtm) - rtu_PresInj);

  /* DataStoreWrite: '<S190>/Data Store Write2' */
  DPresInj = rtb_Look2D_U8_U16_U16;

  /* DataTypeConversion: '<S192>/Conversion3' incorporates:
   *  Constant: '<S190>/BKDPRES_dim'
   */
  rtb_Conversion6_icru = (uint8_T)BKDPRES_dim;

  /* DataTypeConversion: '<S194>/Data Type Conversion8' incorporates:
   *  Constant: '<S190>/BKDPRES_dim'
   */
  rtb_Conversion7_dgke = (uint8_T)BKDPRES_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S194>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S190>/BKDPRES'
   */
  PreLookUpIdSearch_U16( &rtb_Look2D_U8_U16_U16, &rtb_DataTypeConversion,
                        rtb_Look2D_U8_U16_U16, &BKDPRES[0], rtb_Conversion7_dgke);

  /* S-Function (LookUp_IR_S16): '<S192>/LookUp_IR_S16' incorporates:
   *  Constant: '<S190>/VTOFFINJT'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTOFFINJT[0], rtb_Look2D_U8_U16_U16,
                rtb_DataTypeConversion, rtb_Conversion6_icru);

  /* DataTypeConversion: '<S193>/Conversion3' incorporates:
   *  Constant: '<S190>/BKDPRES_dim'
   */
  rtb_Conversion6_icru = (uint8_T)BKDPRES_dim;

  /* S-Function (LookUp_IR_U16): '<S193>/LookUp_IR_U16' incorporates:
   *  Constant: '<S190>/VTGAININJT'
   */
  LookUp_IR_U16( &localB->LookUp_IR_U16, &VTGAININJT[0], rtb_Look2D_U8_U16_U16,
                rtb_DataTypeConversion, rtb_Conversion6_icru);

  /* Sum: '<S190>/Sum2' incorporates:
   *  DataStoreRead: '<S190>/Data Store Read3'
   */
  localB->Sum2 = (int16_T)(rtb_LookUp_IR_S16 + InjTVBat);

  /* End of Outputs for SubSystem: '<S183>/Injector_Model' */

  /* Outputs for Atomic SubSystem: '<S183>/Main_Injection_Time_Calculation' */
  /* Selector: '<S191>/Selector' incorporates:
   *  Constant: '<S191>/VTFORCEINJT'
   *  DataStoreRead: '<S191>/Data Store Read1'
   */
  rtb_Conversion6_icru = VTFORCEINJT[FuelMgmcyl4calc];

  /* Sum: '<S191>/Sum2' incorporates:
   *  Product: '<S191>/Product'
   */
  rtb_DataTypeConversion = (uint16_T)((uint16_T)(((uint32_T)rtb_Switch3_nzxn *
    localB->LookUp_IR_U16) >> 14) + localB->Sum2);

  /* MinMax: '<S191>/MinMax2' incorporates:
   *  Constant: '<S191>/INJTMIN'
   */
  if (INJTMIN > rtb_DataTypeConversion) {
    rtb_MinMax2_aqgr = INJTMIN;
  } else {
    rtb_MinMax2_aqgr = rtb_DataTypeConversion;
  }

  /* End of MinMax: '<S191>/MinMax2' */

  /* Logic: '<S191>/Logical Operator' incorporates:
   *  DataStoreRead: '<S191>/Data Store Read3'
   *  RelationalOperator: '<S191>/Relational Operator'
   */
  rtb_Conversion7_dgke = (uint8_T)((0 != rtb_Switch3_nzxn) && (InjEnable != 0));

  /* Switch: '<S191>/Switch3' incorporates:
   *  Switch: '<S191>/Switch1'
   */
  if (rtb_Conversion6_icru != 0) {
    /* Chart: '<S191>/Assign_FlgInjTMin' incorporates:
     *  Constant: '<S191>/zero3'
     */
    FlgInjTMin[FuelMgmcyl4calc] = 0U;
  } else if (rtb_Conversion7_dgke != 0) {
    /* Switch: '<S191>/Switch1' incorporates:
     *  Chart: '<S191>/Assign_FlgInjTMin'
     *  RelationalOperator: '<S191>/Relational Operator1'
     */
    FlgInjTMin[FuelMgmcyl4calc] = (uint8_T)(rtb_MinMax2_aqgr !=
      rtb_DataTypeConversion);
  } else {
    /* Chart: '<S191>/Assign_FlgInjTMin' incorporates:
     *  Constant: '<S191>/zero2'
     *  Switch: '<S191>/Switch1'
     */
    FlgInjTMin[FuelMgmcyl4calc] = 0U;
  }

  /* End of Switch: '<S191>/Switch3' */

  /* Switch: '<S191>/Switch2' incorporates:
   *  Switch: '<S191>/Switch'
   */
  /* Gateway: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation/Assign_FlgInjTMin */
  /* During: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation/Assign_FlgInjTMin */
  /* Entry Internal: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Main_Injection_Time_Calculation/Assign_FlgInjTMin */
  /* Transition: '<S197>:1' */
  if (rtb_Conversion6_icru != 0) {
    /* Chart: '<S183>/Assign_InjTime' incorporates:
     *  Constant: '<S191>/VTFORCEDINJT'
     *  DataStoreRead: '<S191>/Data Store Read1'
     *  Selector: '<S191>/Selector1'
     */
    InjTime[FuelMgmcyl4calc] = VTFORCEDINJT[FuelMgmcyl4calc];
  } else if (rtb_Conversion7_dgke != 0) {
    /* Switch: '<S191>/Switch' incorporates:
     *  Chart: '<S183>/Assign_InjTime'
     */
    InjTime[FuelMgmcyl4calc] = rtb_MinMax2_aqgr;
  } else {
    /* Chart: '<S183>/Assign_InjTime' incorporates:
     *  Constant: '<S191>/zero'
     *  Switch: '<S191>/Switch'
     */
    InjTime[FuelMgmcyl4calc] = 0U;
  }

  /* End of Switch: '<S191>/Switch2' */
  /* End of Outputs for SubSystem: '<S183>/Main_Injection_Time_Calculation' */

  /* Outputs for Atomic SubSystem: '<S183>/ExtraQFuel_Calculation' */
  /* Product: '<S188>/Product' incorporates:
   *  Constant: '<S188>/EXTRAINJGAIN'
   *  DataStoreRead: '<S178>/Data Store Read2'
   *  DataStoreRead: '<S178>/Data Store Read4'
   *  Selector: '<S178>/Selector3'
   */
  tmp = ((uint32_T)QFuel[FuelMgmcyl4calc] * EXTRAINJGAIN) >> 10;
  if (tmp > 65535U) {
    tmp = 65535U;
  }

  rtb_DataTypeConversion = (uint16_T)tmp;

  /* End of Product: '<S188>/Product' */

  /* Switch: '<S188>/Switch' incorporates:
   *  Constant: '<S188>/0_FIX_08'
   *  Constant: '<S188>/VTFORCEINJT'
   *  DataStoreRead: '<S178>/Data Store Read2'
   *  DataStoreRead: '<S178>/Data Store Read4'
   *  DataStoreRead: '<S188>/Data Store Read1'
   *  DataStoreRead: '<S188>/Data Store Read3'
   *  Logic: '<S188>/Logical Operator'
   *  Logic: '<S188>/Logical Operator2'
   *  RelationalOperator: '<S188>/Relational Operator'
   *  Selector: '<S178>/Selector3'
   *  Selector: '<S188>/Selector'
   *  Sum: '<S188>/Add'
   */
  if ((VTFORCEINJT[FuelMgmcyl4calc] != 0) || ((rtb_Switch3_nzxn >
        rtb_DataTypeConversion) && (FuelMgmflagHTDC != 0))) {
    rtb_DataTypeConversion = (uint16_T)(rtb_Switch3_nzxn - QFuel[FuelMgmcyl4calc]);
  } else {
    rtb_DataTypeConversion = 0U;
  }

  /* End of Switch: '<S188>/Switch' */
  /* End of Outputs for SubSystem: '<S183>/ExtraQFuel_Calculation' */

  /* Outputs for Atomic SubSystem: '<S183>/Extra_Injection_Time_Calculation' */
  /* Switch: '<S189>/Switch2' incorporates:
   *  Constant: '<S189>/VTFORCEINJT'
   *  DataStoreRead: '<S189>/Data Store Read1'
   *  DataStoreRead: '<S189>/Data Store Read3'
   *  Logic: '<S189>/Logical Operator'
   *  RelationalOperator: '<S189>/Relational Operator'
   *  Selector: '<S189>/Selector'
   *  Switch: '<S189>/Switch'
   */
  if (VTFORCEINJT[FuelMgmcyl4calc] != 0) {
    /* Chart: '<S183>/Assign_InjTime' incorporates:
     *  Constant: '<S189>/EXTRAFORCEDINJT'
     */
    InjTime[FuelMgmcyl4calc + ((uint8_T)N_CYL_MAX)] = EXTRAFORCEDINJT;
  } else if ((0 != rtb_DataTypeConversion) && (InjEnable != 0)) {
    /* Sum: '<S189>/Sum2' incorporates:
     *  Product: '<S189>/Product'
     *  Switch: '<S189>/Switch'
     */
    rtb_MinMax2_aqgr = (uint16_T)((uint16_T)(((uint32_T)rtb_DataTypeConversion *
      localB->LookUp_IR_U16) >> 14) + localB->Sum2);

    /* Switch: '<S189>/Switch1' incorporates:
     *  Constant: '<S189>/EXTRAINJTMIN'
     *  RelationalOperator: '<S189>/Relational Operator1'
     *  Switch: '<S189>/Switch'
     */
    if (rtb_MinMax2_aqgr >= EXTRAINJTMIN) {
      /* Chart: '<S183>/Assign_InjTime' */
      InjTime[FuelMgmcyl4calc + ((uint8_T)N_CYL_MAX)] = rtb_MinMax2_aqgr;
    } else {
      /* Chart: '<S183>/Assign_InjTime' incorporates:
       *  Constant: '<S189>/zero2'
       */
      InjTime[FuelMgmcyl4calc + ((uint8_T)N_CYL_MAX)] = 0U;
    }

    /* End of Switch: '<S189>/Switch1' */
  } else {
    /* Chart: '<S183>/Assign_InjTime' incorporates:
     *  Constant: '<S189>/zero'
     *  Switch: '<S189>/Switch'
     */
    InjTime[FuelMgmcyl4calc + ((uint8_T)N_CYL_MAX)] = 0U;
  }

  /* End of Switch: '<S189>/Switch2' */
  /* End of Outputs for SubSystem: '<S183>/Extra_Injection_Time_Calculation' */

  /* Chart: '<S183>/Assign_QFuel' */
  /* Gateway: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_InjTime */
  /* During: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_InjTime */
  /* Entry Internal: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_InjTime */
  /* Transition: '<S186>:1' */
  /* Gateway: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_QFuel */
  /* During: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_QFuel */
  /* Entry Internal: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation/Assign_QFuel */
  /* Transition: '<S187>:1' */
  QFuel[FuelMgmcyl4calc] = rtb_Switch3_nzxn;

  /* End of Outputs for SubSystem: '<S178>/Injection_Time_Calculation' */

  /* Outputs for Enabled SubSystem: '<S178>/HTDC_assignment' incorporates:
   *  EnablePort: '<S182>/Enable'
   */
  /* Logic: '<S178>/Logical Operator1' incorporates:
   *  DataStoreRead: '<S178>/Data Store Read4'
   *  DataStoreRead: '<S178>/Data Store Read8'
   *  RelationalOperator: '<S178>/Relational Operator1'
   */
  if ((FuelMgmflagHTDC != 0) && (FuelMgmcyl4calc == rtu_AbsHTdc)) {
    /* DataStoreWrite: '<S182>/Data Store Write1' */
    QFuelExtra = rtb_DataTypeConversion;

    /* DataStoreWrite: '<S182>/Data Store Write2' incorporates:
     *  DataStoreRead: '<S182>/Data Store Read'
     *  Sum: '<S182>/Sum1'
     */
    QFuelIntExtTot = rtb_DataTypeConversion + QFuelIntExtTot;
  }

  /* End of Logic: '<S178>/Logical Operator1' */
  /* End of Outputs for SubSystem: '<S178>/HTDC_assignment' */

  /* Outputs for Atomic SubSystem: '<S178>/Fuel_Film_Compensator_HighBank' */
  /* DataTypeConversion: '<S181>/Data Type Conversion' incorporates:
   *  Constant: '<S181>/1_FIX_15'
   *  DataStoreRead: '<S178>/Data Store Read1'
   *  DataStoreRead: '<S178>/Data Store Read5'
   *  DataStoreRead: '<S178>/Data Store Read6'
   *  DataStoreRead: '<S181>/Data Store Read2'
   *  DataStoreRead: '<S181>/Data Store Read3'
   *  Product: '<S181>/Product2'
   *  Product: '<S181>/Product3'
   *  Product: '<S181>/Product9'
   *  Selector: '<S178>/Selector4'
   *  Selector: '<S178>/Selector5'
   *  Sum: '<S181>/Sum1'
   *  Sum: '<S181>/Sum2'
   */
  tmp = ((((uint32_T)QFObj[FuelMgmcyl4calcH] * (uint16_T)(((uint32_T)XFilmH *
             KFFilmH) >> 15)) >> 1) + (((uint32_T)(uint16_T)(32768 - KFFilmH) *
           QFilm[FuelMgmcyl4calcH]) >> 1)) >> 14;
  if (tmp > 65535U) {
    tmp = 65535U;
  }

  rtb_DataTypeConversion = (uint16_T)tmp;

  /* End of DataTypeConversion: '<S181>/Data Type Conversion' */

  /* MinMax: '<S181>/MinMax3' incorporates:
   *  Constant: '<S181>/MAXQFILM'
   */
  if (MAXQFILM < rtb_DataTypeConversion) {
    rtb_DataTypeConversion = MAXQFILM;
  }

  /* End of MinMax: '<S181>/MinMax3' */

  /* Switch: '<S181>/Switch3' incorporates:
   *  Constant: '<S181>/ENQFOBJBASE'
   *  Sum: '<S181>/Sum4'
   *  Sum: '<S181>/Sum6'
   */
  if (ENQFOBJBASE != 0) {
    /* Sum: '<S181>/Sum6' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read5'
     *  DataStoreRead: '<S178>/Data Store Read6'
     *  DataStoreRead: '<S181>/Data Store Read1'
     *  Product: '<S181>/Product1'
     *  Product: '<S181>/Product12'
     *  Selector: '<S178>/Selector5'
     */
    qY = (int32_T)(((uint32_T)GainFilmH * QFObj[FuelMgmcyl4calcH]) >> 14) -
      (int32_T)(((uint32_T)rtb_DataTypeConversion * GainFilmH) >> 14);
    if (qY < 0) {
      qY = 0;
    } else {
      if (qY > 65535) {
        qY = 65535;
      }
    }

    /* Sum: '<S181>/Sum3' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read5'
     *  DataStoreRead: '<S178>/Data Store Read6'
     *  Selector: '<S178>/Selector5'
     *  Sum: '<S181>/Sum6'
     */
    qY -= QFObj[FuelMgmcyl4calcH];
    if (qY > 32767) {
      qY = 32767;
    } else {
      if (qY < -32768) {
        qY = -32768;
      }
    }

    /* Sum: '<S181>/Sum4' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read5'
     *  DataStoreRead: '<S178>/Data Store Read7'
     *  Selector: '<S178>/Selector7'
     *  Sum: '<S181>/Sum3'
     */
    qY += QFObjBase[FuelMgmcyl4calcH];
    if (qY < 0) {
      qY = 0;
    } else {
      if (qY > 65535) {
        qY = 65535;
      }
    }

    rtb_MinMax2_aqgr = (uint16_T)qY;
  } else {
    /* Sum: '<S181>/Sum6' incorporates:
     *  DataStoreRead: '<S178>/Data Store Read5'
     *  DataStoreRead: '<S178>/Data Store Read6'
     *  DataStoreRead: '<S181>/Data Store Read1'
     *  Product: '<S181>/Product1'
     *  Product: '<S181>/Product12'
     *  Selector: '<S178>/Selector5'
     */
    qY = (int32_T)(((uint32_T)GainFilmH * QFObj[FuelMgmcyl4calcH]) >> 14) -
      (int32_T)(((uint32_T)rtb_DataTypeConversion * GainFilmH) >> 14);
    if (qY < 0) {
      qY = 0;
    } else {
      if (qY > 65535) {
        qY = 65535;
      }
    }

    rtb_MinMax2_aqgr = (uint16_T)qY;
  }

  /* End of Switch: '<S181>/Switch3' */
  /* End of Outputs for SubSystem: '<S178>/Fuel_Film_Compensator_HighBank' */

  /* Outputs for Atomic SubSystem: '<S178>/Injection_Time_Calculation_HighBank' */
  /* Selector: '<S184>/Selector' incorporates:
   *  Constant: '<S184>/VTFORCEINJT'
   *  DataStoreRead: '<S184>/Data Store Read2'
   */
  rtb_Conversion6_icru = VTFORCEINJT[FuelMgmcyl4calcH];

  /* Sum: '<S184>/Sum2' incorporates:
   *  Constant: '<S184>/GAININJTH'
   *  Constant: '<S184>/OFFINJTH'
   *  DataStoreRead: '<S184>/Data Store Read1'
   *  Product: '<S184>/Product'
   *  Sum: '<S184>/Sum1'
   */
  rtb_Sum2_evwq = (uint16_T)((uint32_T)(uint16_T)(((uint32_T)rtb_MinMax2_aqgr *
    GAININJTH) >> 14) + (uint16_T)(OFFINJTH + InjTVBatH));

  /* MinMax: '<S184>/MinMax2' incorporates:
   *  Constant: '<S184>/INJTMIN'
   */
  if (INJTMIN > rtb_Sum2_evwq) {
    rtb_MinMax2_p0am = INJTMIN;
  } else {
    rtb_MinMax2_p0am = rtb_Sum2_evwq;
  }

  /* End of MinMax: '<S184>/MinMax2' */

  /* Logic: '<S184>/Logical Operator' incorporates:
   *  DataStoreRead: '<S184>/Data Store Read3'
   *  RelationalOperator: '<S184>/Relational Operator'
   */
  rtb_Conversion7_dgke = (uint8_T)((0 != rtb_MinMax2_aqgr) && (InjEnable != 0));

  /* Switch: '<S184>/Switch3' incorporates:
   *  Switch: '<S184>/Switch1'
   */
  if (rtb_Conversion6_icru != 0) {
    /* Chart: '<S184>/Assign_FlgInjTMin' incorporates:
     *  Constant: '<S184>/zero4'
     */
    FlgInjTMin[FuelMgmcyl4calcH] = 0U;
  } else if (rtb_Conversion7_dgke != 0) {
    /* Switch: '<S184>/Switch1' incorporates:
     *  Chart: '<S184>/Assign_FlgInjTMin'
     *  RelationalOperator: '<S184>/Relational Operator1'
     */
    FlgInjTMin[FuelMgmcyl4calcH] = (uint8_T)(rtb_MinMax2_p0am != rtb_Sum2_evwq);
  } else {
    /* Chart: '<S184>/Assign_FlgInjTMin' incorporates:
     *  Constant: '<S184>/zero3'
     *  Switch: '<S184>/Switch1'
     */
    FlgInjTMin[FuelMgmcyl4calcH] = 0U;
  }

  /* End of Switch: '<S184>/Switch3' */

  /* Switch: '<S184>/Switch2' incorporates:
   *  Switch: '<S184>/Switch'
   */
  /* Gateway: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_FlgInjTMin */
  /* During: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_FlgInjTMin */
  /* Entry Internal: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_FlgInjTMin */
  /* Transition: '<S198>:1' */
  if (rtb_Conversion6_icru != 0) {
    /* Chart: '<S184>/Assign_InjTime' incorporates:
     *  Constant: '<S184>/VTFORCEDINJT'
     *  DataStoreRead: '<S184>/Data Store Read2'
     *  Selector: '<S184>/Selector1'
     */
    InjTime[FuelMgmcyl4calcH] = VTFORCEDINJT[FuelMgmcyl4calcH];
  } else if (rtb_Conversion7_dgke != 0) {
    /* Switch: '<S184>/Switch' incorporates:
     *  Chart: '<S184>/Assign_InjTime'
     */
    InjTime[FuelMgmcyl4calcH] = rtb_MinMax2_p0am;
  } else {
    /* Chart: '<S184>/Assign_InjTime' incorporates:
     *  Constant: '<S184>/zero1'
     *  Switch: '<S184>/Switch'
     */
    InjTime[FuelMgmcyl4calcH] = 0U;
  }

  /* End of Switch: '<S184>/Switch2' */

  /* Chart: '<S184>/Assign_InjTime' incorporates:
   *  Constant: '<S184>/zero'
   */
  /* Gateway: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_InjTime */
  /* During: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_InjTime */
  /* Entry Internal: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_InjTime */
  /* Transition: '<S199>:1' */
  InjTime[FuelMgmcyl4calcH + ((uint8_T)N_CYL_MAX)] = 0U;

  /* Chart: '<S184>/Assign_QFuel' */
  /* Gateway: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_QFuel */
  /* During: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_QFuel */
  /* Entry Internal: InjTime_Calc/InjTime_cyl/Injection_Time_Calculation_HighBank/Assign_QFuel */
  /* Transition: '<S200>:1' */
  QFuel[FuelMgmcyl4calcH] = rtb_MinMax2_aqgr;

  /* End of Outputs for SubSystem: '<S178>/Injection_Time_Calculation_HighBank' */

  /* Outputs for Enabled SubSystem: '<S178>/TDC_assignment' incorporates:
   *  EnablePort: '<S185>/Enable'
   */
  /* Logic: '<S178>/Logical Operator' incorporates:
   *  DataStoreRead: '<S178>/Data Store Read3'
   *  DataStoreRead: '<S178>/Data Store Read4'
   *  RelationalOperator: '<S178>/Relational Operator'
   */
  if ((FuelMgmcyl4calc == rtu_AbsTdc) && (FuelMgmflagTDC != 0)) {
    /* Sum: '<S185>/Sum6' incorporates:
     *  DataStoreWrite: '<S185>/Data Store Write1'
     */
    QFuelTot = (uint16_T)((uint32_T)rtb_Switch3_nzxn + rtb_MinMax2_aqgr);

    /* DataStoreWrite: '<S185>/Data Store Write2' incorporates:
     *  DataStoreRead: '<S185>/Data Store Read'
     *  DataStoreWrite: '<S185>/Data Store Write1'
     *  Sum: '<S185>/Sum1'
     */
    QFuelIntTot = QFuelTot + QFuelIntTot;

    /* Chart: '<S185>/Assign_QFilm' */
    /* Gateway: InjTime_Calc/InjTime_cyl/TDC_assignment/Assign_QFilm */
    /* During: InjTime_Calc/InjTime_cyl/TDC_assignment/Assign_QFilm */
    /* Entry Internal: InjTime_Calc/InjTime_cyl/TDC_assignment/Assign_QFilm */
    /* Transition: '<S201>:1' */
    QFilm[FuelMgmcyl4calc] = rtb_MinMax3_a5it;
    QFilm[FuelMgmcyl4calcH] = rtb_DataTypeConversion;
  }

  /* End of Logic: '<S178>/Logical Operator' */
  /* End of Outputs for SubSystem: '<S178>/TDC_assignment' */
  /* End of Outputs for SubSystem: '<S149>/InjTime_cyl' */

  /* Outputs for Atomic SubSystem: '<S149>/SOI_Calculation' */
  /* DataTypeConversion: '<S203>/Conversion6' incorporates:
   *  Constant: '<S179>/BKSOIBASELOAD_dim'
   */
  rtb_Conversion7_dgke = (uint8_T)BKSOIBASELOAD_dim;

  /* DataTypeConversion: '<S203>/Conversion7' incorporates:
   *  Constant: '<S179>/BKSOIBASERPM_dim'
   */
  rtb_Conversion6_icru = (uint8_T)BKSOIBASERPM_dim;

  /* S-Function (Look2D_U8_U16_U16): '<S203>/Look2D_U8_U16_U16' incorporates:
   *  Constant: '<S179>/BKSOIBASELOAD'
   *  Constant: '<S179>/BKSOIBASERPM'
   *  Constant: '<S179>/TBSOIBASE'
   */
  Look2D_U8_U16_U16( &rtb_Look2D_U8_U16_U16, &TBSOIBASE[0], rtu_Load,
                    &BKSOIBASELOAD[0], rtb_Conversion7_dgke, rtu_Rpm,
                    &BKSOIBASERPM[0], rtb_Conversion6_icru);

  /* Chart: '<S179>/Assign_SOI' incorporates:
   *  DataTypeConversion: '<S179>/Conversion8'
   */
  SOI[FuelMgmcyl4calc] = (uint16_T)((uint32_T)rtb_Look2D_U8_U16_U16 >> 2);

  /* DataTypeConversion: '<S204>/Conversion6' incorporates:
   *  Constant: '<S179>/BKSOIBASELOAD_dim'
   */
  rtb_Conversion6_icru = (uint8_T)BKSOIBASELOAD_dim;

  /* DataTypeConversion: '<S204>/Conversion7' incorporates:
   *  Constant: '<S179>/BKSOIBASERPM_dim'
   */
  rtb_Conversion7_dgke = (uint8_T)BKSOIBASERPM_dim;

  /* S-Function (Look2D_U8_U16_U16): '<S204>/Look2D_U8_U16_U16' incorporates:
   *  Constant: '<S179>/BKSOIBASELOAD'
   *  Constant: '<S179>/BKSOIBASERPM'
   *  Constant: '<S179>/TBSOIBASEH'
   */
  Look2D_U8_U16_U16( &rtb_Look2D_U8_U16_U16, &TBSOIBASEH[0], rtu_Load,
                    &BKSOIBASELOAD[0], rtb_Conversion6_icru, rtu_Rpm,
                    &BKSOIBASERPM[0], rtb_Conversion7_dgke);

  /* Chart: '<S179>/Assign_SOI' incorporates:
   *  Constant: '<S179>/EXTRASOI'
   *  DataTypeConversion: '<S179>/Conversion1'
   */
  /* Gateway: InjTime_Calc/SOI_Calculation/Assign_SOI */
  /* During: InjTime_Calc/SOI_Calculation/Assign_SOI */
  /* Entry Internal: InjTime_Calc/SOI_Calculation/Assign_SOI */
  /* Transition: '<S202>:1' */
  SOI[FuelMgmcyl4calcH] = (uint16_T)((uint32_T)rtb_Look2D_U8_U16_U16 >> 2);
  SOI[FuelMgmcyl4calc + ((uint8_T)N_CYL_MAX)] = EXTRASOI;

  /* End of Outputs for SubSystem: '<S149>/SOI_Calculation' */
}

/*
 * Output and update for atomic system:
 *    '<S8>/QAir_selection'
 *    '<S2>/QAir_selection'
 *    '<S4>/QAir_selection'
 */
void FuelMgm_QAir_selection(const uint16_T rtu_QAirFuel[4], uint16_T
  rtu_QAirCyl0, uint16_T rtu_QAirTarget0, const uint16_T rtu_VtTbQAirGain[4],
  uint8_T rtu_cyl, uint8_T rtu_EndStartFlg, rtB_QAir_selection_FuelMgm *localB)
{
  /* Selector: '<S150>/Selector2' */
  localB->Selector2 = rtu_QAirFuel[rtu_cyl];

  /* Switch: '<S150>/Switch' */
  if (rtu_EndStartFlg != 0) {
    /* MultiPortSwitch: '<S150>/Multiport Switch' incorporates:
     *  Constant: '<S150>/USEQAIR4QFOBJ'
     *  Product: '<S150>/controllo titolo1'
     *  Product: '<S150>/controllo titolo2'
     *  Selector: '<S150>/Selector1'
     */
    switch (USEQAIR4QFOBJ) {
     case 0:
      localB->Switch = localB->Selector2;
      break;

     case 1:
      localB->Switch = (uint16_T)(((uint32_T)rtu_QAirCyl0 *
        rtu_VtTbQAirGain[rtu_cyl]) >> 15);
      break;

     case 2:
      localB->Switch = (uint16_T)(((uint32_T)rtu_QAirTarget0 *
        rtu_VtTbQAirGain[rtu_cyl]) >> 15);
      break;

     default:
      localB->Switch = localB->Selector2;
      break;
    }

    /* End of MultiPortSwitch: '<S150>/Multiport Switch' */
  } else {
    localB->Switch = localB->Selector2;
  }

  /* End of Switch: '<S150>/Switch' */
}

/*
 * Output and update for atomic system:
 *    '<S151>/Fuel_Mass_Calculation'
 *    '<S151>/Fuel_Mass_Calculation_Base'
 *    '<S152>/Fuel_Mass_Calculation'
 *    '<S18>/Fuel_Mass_Calculation'
 *    '<S18>/Fuel_Mass_Calculation_Base'
 *    '<S79>/Fuel_Mass_Calculation'
 *    '<S79>/Fuel_Mass_Calculation_Base'
 */
void FuelMgm_Fuel_Mass_Calculation(boolean_T rtu_CutoffFlg, uint16_T rtu_lambda,
  uint16_T rtu_qaircyl, rtB_Fuel_Mass_Calculation_FuelM *localB)
{
  /* Switch: '<S205>/Switch' incorporates:
   *  Constant: '<S205>/0_FIX_08'
   *  Constant: '<S205>/AF0'
   *  Constant: '<S205>/FORCEDKF'
   *  DataStoreRead: '<S205>/Data Store Read3'
   *  Logic: '<S205>/Logical Operator'
   *  Logic: '<S205>/Logical Operator2'
   *  Product: '<S205>/Product1'
   *  Product: '<S205>/calcolo base'
   *  Product: '<S205>/controllo titolo1'
   */
  if (rtu_CutoffFlg || (InjEnable == 0)) {
    localB->Switch = 0U;
  } else {
    localB->Switch = (uint16_T)(((uint32_T)(uint16_T)(((uint32_T)rtu_qaircyl <<
      12) / (uint16_T)(((uint32_T)AF0 * rtu_lambda) >> 10)) * FORCEDKF) >> 14);
  }

  /* End of Switch: '<S205>/Switch' */
}

/*
 * Output and update for enable system:
 *    '<S151>/QFuelSplitFrac_calculation'
 *    '<S18>/QFuelSplitFrac_calculation'
 *    '<S79>/QFuelSplitFrac_calculation'
 */
void Fuel_QFuelSplitFrac_calculation(uint8_T rtu_Enable, uint16_T rtu_Rpm,
  uint16_T rtu_AngThrottle)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16__h5te;
  int16_T rtb_RateLimiter_S16;
  int16_T rtb_LookUp_IR_S16_myok;
  int16_T rtb_LookUp_IR_S16_jyvk;
  boolean_T rtb_RelationalOperator_dbq1;
  boolean_T rtb_RelationalOperator2_dwy3;
  uint8_T rtb_Conversion6_iwsp;
  uint8_T rtb_Conversion3_hbgh;
  int16_T rtb_DataTypeConversion;
  int16_T rtb_DataStoreRead1_kn1c;

  /* Outputs for Enabled SubSystem: '<S151>/QFuelSplitFrac_calculation' incorporates:
   *  EnablePort: '<S209>/Enable'
   */
  if (rtu_Enable > 0) {
    /* DataTypeConversion: '<S218>/Conversion6' incorporates:
     *  Constant: '<S209>/BKANGQFSPLIT_dim'
     */
    rtb_Conversion6_iwsp = (uint8_T)BKANGQFSPLIT_dim;

    /* DataTypeConversion: '<S218>/Conversion7' incorporates:
     *  Constant: '<S209>/BKSOIBASERPM_dim'
     */
    rtb_Conversion3_hbgh = (uint8_T)BKSOIBASERPM_dim;

    /* S-Function (Look2D_U8_U16_U16): '<S218>/Look2D_U8_U16_U16' incorporates:
     *  Constant: '<S209>/BKANGQFSPLIT'
     *  Constant: '<S209>/BKSOIBASERPM'
     *  Constant: '<S209>/TBQFUELSPLIT'
     */
    Look2D_U8_U16_U16( &rtb_PreLookUpIdSearch_U16__h5te, &TBQFUELSPLIT[0],
                      rtu_AngThrottle, &BKANGQFSPLIT[0], rtb_Conversion6_iwsp,
                      rtu_Rpm, &BKSOIBASERPM[0], rtb_Conversion3_hbgh);

    /* DataTypeConversion: '<S209>/Data Type Conversion' */
    rtb_DataTypeConversion = (int16_T)((uint32_T)rtb_PreLookUpIdSearch_U16__h5te
      >> 1);

    /* DataStoreWrite: '<S209>/Data Store Write' */
    QFuelSplitFracTB = rtb_DataTypeConversion;

    /* DataStoreRead: '<S209>/Data Store Read1' */
    rtb_DataStoreRead1_kn1c = QFuelSplitFracRL;

    /* DataTypeConversion: '<S221>/Data Type Conversion8' incorporates:
     *  Constant: '<S209>/BKRPMQFSPLIT_dim'
     */
    rtb_Conversion3_hbgh = (uint8_T)BKRPMQFSPLIT_dim;

    /* S-Function (PreLookUpIdSearch_U16): '<S221>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S209>/BKRPMQFSPLIT'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16__h5te,
                          &rtb_PreLookUpIdSearch_U16_o2, rtu_Rpm, &BKRPMQFSPLIT
                          [0], rtb_Conversion3_hbgh);

    /* DataTypeConversion: '<S219>/Conversion3' incorporates:
     *  Constant: '<S209>/BKRPMQFSPLIT_dim'
     */
    rtb_Conversion3_hbgh = (uint8_T)BKRPMQFSPLIT_dim;

    /* S-Function (LookUp_IR_S16): '<S219>/LookUp_IR_S16' incorporates:
     *  Constant: '<S209>/VTQFSPLITRATEMIN'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16_myok, &VTQFSPLITRATEMIN[0],
                  rtb_PreLookUpIdSearch_U16__h5te, rtb_PreLookUpIdSearch_U16_o2,
                  rtb_Conversion3_hbgh);

    /* DataTypeConversion: '<S220>/Conversion3' incorporates:
     *  Constant: '<S209>/BKRPMQFSPLIT_dim'
     */
    rtb_Conversion3_hbgh = (uint8_T)BKRPMQFSPLIT_dim;

    /* S-Function (LookUp_IR_S16): '<S220>/LookUp_IR_S16' incorporates:
     *  Constant: '<S209>/VTQFSPLITRATEMAX'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16_jyvk, &VTQFSPLITRATEMAX[0],
                  rtb_PreLookUpIdSearch_U16__h5te, rtb_PreLookUpIdSearch_U16_o2,
                  rtb_Conversion3_hbgh);

    /* S-Function (RateLimiter_S16): '<S222>/RateLimiter_S16' */
    RateLimiter_S16( &rtb_RateLimiter_S16, rtb_DataTypeConversion,
                    rtb_DataStoreRead1_kn1c, rtb_LookUp_IR_S16_myok,
                    rtb_LookUp_IR_S16_jyvk);

    /* SwitchCase: '<S209>/Switch Case' incorporates:
     *  DataStoreRead: '<S209>/Data Store Read11'
     */
    switch (StQFuelSplit) {
     case 0:
      /* Outputs for IfAction SubSystem: '<S209>/LOW_BANK' incorporates:
       *  ActionPort: '<S217>/Action Port'
       */
      /* RelationalOperator: '<S217>/Relational Operator' incorporates:
       *  Constant: '<S217>/MAXQFSPLITBOTH'
       */
      rtb_RelationalOperator_dbq1 = (MAXQFSPLITBOTH >= rtb_DataTypeConversion);

      /* Switch: '<S217>/Switch' incorporates:
       *  Constant: '<S217>/MAXQFSPLITBOTH'
       *  Constant: '<S217>/ONE'
       *  DataStoreWrite: '<S217>/Data Store Write3'
       */
      if (rtb_RelationalOperator_dbq1) {
        QFuelSplitFracRL = MAXQFSPLITBOTH;
      } else {
        QFuelSplitFracRL = 16384;
      }

      /* End of Switch: '<S217>/Switch' */

      /* Sum: '<S217>/Sum6' incorporates:
       *  Constant: '<S217>/ONE1'
       *  DataStoreWrite: '<S217>/Data Store Write1'
       *  DataStoreWrite: '<S217>/Data Store Write3'
       */
      QFuelSplitFracH = (int16_T)(16384 - QFuelSplitFracRL);

      /* Switch: '<S217>/Switch2' incorporates:
       *  Constant: '<S217>/ONE2'
       *  Constant: '<S217>/ZERO'
       *  DataStoreRead: '<S217>/Data Store Read11'
       *  DataStoreWrite: '<S217>/Data Store Write4'
       *  Sum: '<S217>/Sum1'
       */
      if (rtb_RelationalOperator_dbq1) {
        CntQFuelSplit = (uint8_T)(CntQFuelSplit + 1U);
      } else {
        CntQFuelSplit = 0U;
      }

      /* End of Switch: '<S217>/Switch2' */

      /* Switch: '<S217>/Switch1' incorporates:
       *  Constant: '<S217>/BOTH_BANKS'
       *  Constant: '<S217>/LOW_BANK'
       *  Constant: '<S217>/N_CYLINDER'
       *  DataStoreWrite: '<S217>/Data Store Write18'
       *  DataStoreWrite: '<S217>/Data Store Write4'
       *  RelationalOperator: '<S217>/Relational Operator1'
       */
      if (CntQFuelSplit > ((uint8_T)N_CYLINDER)) {
        StQFuelSplit = ((uint8_T)BOTH_BANKS);
      } else {
        StQFuelSplit = ((uint8_T)LOW_BANK);
      }

      /* End of Switch: '<S217>/Switch1' */

      /* DataStoreWrite: '<S217>/Data Store Write2' incorporates:
       *  Constant: '<S217>/ONE'
       */
      QFuelSplitFrac = 16384;

      /* End of Outputs for SubSystem: '<S209>/LOW_BANK' */
      break;

     case 2:
      /* Outputs for IfAction SubSystem: '<S209>/HIGH_BANK' incorporates:
       *  ActionPort: '<S216>/Action Port'
       */
      /* Sum: '<S216>/Sum6' incorporates:
       *  Constant: '<S216>/MINQFSPLITBOTH'
       *  Constant: '<S216>/QFSPLITHYST'
       */
      rtb_DataStoreRead1_kn1c = (int16_T)(MINQFSPLITBOTH + QFSPLITHYST);

      /* RelationalOperator: '<S216>/Relational Operator' */
      rtb_RelationalOperator_dbq1 = (rtb_DataStoreRead1_kn1c <=
        rtb_DataTypeConversion);

      /* Switch: '<S216>/Switch' incorporates:
       *  Constant: '<S216>/ZERO'
       *  DataStoreWrite: '<S216>/Data Store Write3'
       */
      if (rtb_RelationalOperator_dbq1) {
        QFuelSplitFracRL = rtb_DataStoreRead1_kn1c;
      } else {
        QFuelSplitFracRL = 0;
      }

      /* End of Switch: '<S216>/Switch' */

      /* Sum: '<S216>/Sum1' incorporates:
       *  Constant: '<S216>/ONE1'
       *  DataStoreWrite: '<S216>/Data Store Write1'
       *  DataStoreWrite: '<S216>/Data Store Write3'
       */
      QFuelSplitFracH = (int16_T)(16384 - QFuelSplitFracRL);

      /* DataStoreWrite: '<S216>/Data Store Write2' incorporates:
       *  Constant: '<S216>/ZERO'
       */
      QFuelSplitFrac = 0;

      /* Switch: '<S216>/Switch3' incorporates:
       *  Constant: '<S216>/ONE2'
       *  Constant: '<S216>/ZERO1'
       *  DataStoreRead: '<S216>/Data Store Read11'
       *  DataStoreWrite: '<S216>/Data Store Write5'
       *  Sum: '<S216>/Sum2'
       */
      if (rtb_RelationalOperator_dbq1) {
        CntQFuelSplit = (uint8_T)(CntQFuelSplit + 1U);
      } else {
        CntQFuelSplit = 0U;
      }

      /* End of Switch: '<S216>/Switch3' */

      /* Switch: '<S216>/Switch2' incorporates:
       *  Constant: '<S216>/BOTH_BANKS'
       *  Constant: '<S216>/HIGH_BANK'
       *  Constant: '<S216>/N_CYLINDER'
       *  DataStoreWrite: '<S216>/Data Store Write4'
       *  DataStoreWrite: '<S216>/Data Store Write5'
       *  RelationalOperator: '<S216>/Relational Operator1'
       */
      if (CntQFuelSplit > ((uint8_T)N_CYLINDER)) {
        StQFuelSplit = ((uint8_T)BOTH_BANKS);
      } else {
        StQFuelSplit = ((uint8_T)HIGH_BANK);
      }

      /* End of Switch: '<S216>/Switch2' */
      /* End of Outputs for SubSystem: '<S209>/HIGH_BANK' */
      break;

     case 1:
      /* Outputs for IfAction SubSystem: '<S209>/BOTH_BANKS' incorporates:
       *  ActionPort: '<S215>/Action Port'
       */
      /* RelationalOperator: '<S215>/Relational Operator' incorporates:
       *  Constant: '<S215>/MAXQFSPLITBOTH'
       *  Constant: '<S215>/QFSPLITHYST'
       *  Sum: '<S215>/Sum6'
       */
      rtb_RelationalOperator_dbq1 = (rtb_RateLimiter_S16 > (int16_T)
        (MAXQFSPLITBOTH + QFSPLITHYST));

      /* Switch: '<S215>/Switch4' incorporates:
       *  Constant: '<S215>/MINQFSPLITBOTH'
       *  Constant: '<S215>/ONE2'
       *  Constant: '<S215>/ZERO1'
       *  DataStoreRead: '<S215>/Data Store Read11'
       *  DataStoreWrite: '<S215>/Data Store Write4'
       *  RelationalOperator: '<S215>/Relational Operator1'
       *  Sum: '<S215>/Sum2'
       */
      if (rtb_RateLimiter_S16 > MINQFSPLITBOTH) {
        CntQFuelSplit = 0U;
      } else {
        CntQFuelSplit = (uint8_T)(CntQFuelSplit + 1U);
      }

      /* End of Switch: '<S215>/Switch4' */

      /* RelationalOperator: '<S215>/Relational Operator2' incorporates:
       *  Constant: '<S215>/N_CYLINDER'
       *  DataStoreWrite: '<S215>/Data Store Write4'
       */
      rtb_RelationalOperator2_dwy3 = (CntQFuelSplit <= ((uint8_T)N_CYLINDER));

      /* Switch: '<S215>/Switch' incorporates:
       *  Constant: '<S215>/ONE'
       *  DataStoreWrite: '<S215>/Data Store Write'
       *  Switch: '<S215>/Switch2'
       */
      if (rtb_RelationalOperator_dbq1) {
        QFuelSplitFrac = 16384;
      } else if (rtb_RelationalOperator2_dwy3) {
        /* Switch: '<S215>/Switch5' incorporates:
         *  DataStoreWrite: '<S215>/Data Store Write4'
         *  Switch: '<S215>/Switch2'
         */
        if (CntQFuelSplit != 0) {
        } else {
          /* DataStoreWrite: '<S215>/Data Store Write' */
          QFuelSplitFrac = rtb_RateLimiter_S16;
        }

        /* End of Switch: '<S215>/Switch5' */
      } else {
        /* DataStoreWrite: '<S215>/Data Store Write' incorporates:
         *  Constant: '<S215>/ZERO'
         *  Switch: '<S215>/Switch2'
         */
        QFuelSplitFrac = 0;
      }

      /* End of Switch: '<S215>/Switch' */

      /* Switch: '<S215>/Switch6' incorporates:
       *  Constant: '<S215>/ONE1'
       *  DataStoreWrite: '<S215>/Data Store Write'
       *  DataStoreWrite: '<S215>/Data Store Write1'
       *  DataStoreWrite: '<S215>/Data Store Write4'
       *  Sum: '<S215>/Sum1'
       */
      if (CntQFuelSplit != 0) {
        QFuelSplitFracH = 16384;
      } else {
        QFuelSplitFracH = (int16_T)(16384 - QFuelSplitFrac);
      }

      /* End of Switch: '<S215>/Switch6' */

      /* Switch: '<S215>/Switch1' incorporates:
       *  Constant: '<S215>/HIGH_BANK'
       *  Constant: '<S215>/LOW_BANK'
       *  DataStoreWrite: '<S215>/Data Store Write18'
       *  Switch: '<S215>/Switch3'
       */
      if (rtb_RelationalOperator_dbq1) {
        StQFuelSplit = ((uint8_T)LOW_BANK);
      } else if (rtb_RelationalOperator2_dwy3) {
        /* Switch: '<S215>/Switch3' incorporates:
         *  Constant: '<S215>/BOTH_BANKS'
         *  DataStoreWrite: '<S215>/Data Store Write18'
         */
        StQFuelSplit = ((uint8_T)BOTH_BANKS);
      } else {
        StQFuelSplit = ((uint8_T)HIGH_BANK);
      }

      /* End of Switch: '<S215>/Switch1' */

      /* DataStoreWrite: '<S215>/Data Store Write2' incorporates:
       *  DataStoreWrite: '<S215>/Data Store Write'
       */
      QFuelSplitFracRL = QFuelSplitFrac;

      /* End of Outputs for SubSystem: '<S209>/BOTH_BANKS' */
      break;

     default:
      /* Outputs for IfAction SubSystem: '<S209>/default' incorporates:
       *  ActionPort: '<S223>/Action Port'
       */
      /* DataStoreWrite: '<S223>/Data Store Write' incorporates:
       *  Constant: '<S223>/ONE'
       */
      QFuelSplitFrac = 16384;

      /* DataStoreWrite: '<S223>/Data Store Write1' incorporates:
       *  Constant: '<S223>/ZERO'
       */
      QFuelSplitFracH = 0;

      /* DataStoreWrite: '<S223>/Data Store Write18' incorporates:
       *  Constant: '<S223>/LOW_BANK'
       */
      StQFuelSplit = ((uint8_T)LOW_BANK);

      /* DataStoreWrite: '<S223>/Data Store Write5' incorporates:
       *  Constant: '<S223>/ZERO1'
       */
      CntQFuelSplit = 0U;

      /* End of Outputs for SubSystem: '<S209>/default' */
      break;
    }

    /* End of SwitchCase: '<S209>/Switch Case' */
  }

  /* End of Outputs for SubSystem: '<S151>/QFuelSplitFrac_calculation' */
}

/*
 * Output and update for atomic system:
 *    '<S8>/QFObj_Calc'
 *    '<S2>/QFObj_Calc'
 *    '<S4>/QFObj_Calc'
 */
void FuelMgm_QFObj_Calc(uint16_T rtu_Rpm, uint16_T rtu_lambda, boolean_T
  rtu_CutoffFlg, uint8_T rtu_AbsTdc, uint16_T rtu_AngThrottle, uint16_T
  rtu_qaircyl, uint16_T rtu_qairbase, rtB_QFObj_Calc_FuelMgm *localB)
{
  uint8_T rtb_LogicalOperator_cgvo;

  /* Logic: '<S151>/Logical Operator' incorporates:
   *  DataStoreRead: '<S151>/Data Store Read3'
   *  DataStoreRead: '<S151>/Data Store Read4'
   *  RelationalOperator: '<S151>/Relational Operator'
   */
  rtb_LogicalOperator_cgvo = (uint8_T)((FuelMgmcyl4calc == rtu_AbsTdc) &&
    (FuelMgmflagTDC != 0));

  /* Outputs for Enabled SubSystem: '<S151>/QFuelSplitFrac_calculation' */
  /* SignalConversion generated from: '<S209>/Enable' */
  Fuel_QFuelSplitFrac_calculation(rtb_LogicalOperator_cgvo, rtu_Rpm,
    rtu_AngThrottle);

  /* End of Outputs for SubSystem: '<S151>/QFuelSplitFrac_calculation' */

  /* Outputs for Atomic SubSystem: '<S151>/Fuel_Mass_Calculation' */
  FuelMgm_Fuel_Mass_Calculation(rtu_CutoffFlg, rtu_lambda, rtu_qaircyl,
    &localB->Fuel_Mass_Calculation);

  /* End of Outputs for SubSystem: '<S151>/Fuel_Mass_Calculation' */

  /* Outputs for Atomic SubSystem: '<S151>/Fuel_Mass_Calculation_Base' */
  FuelMgm_Fuel_Mass_Calculation(rtu_CutoffFlg, rtu_lambda, rtu_qairbase,
    &localB->Fuel_Mass_Calculation_Base);

  /* End of Outputs for SubSystem: '<S151>/Fuel_Mass_Calculation_Base' */

  /* Outputs for Enabled SubSystem: '<S151>/TDC_assignment' incorporates:
   *  EnablePort: '<S210>/Enable'
   */
  if (rtb_LogicalOperator_cgvo > 0) {
    /* DataStoreWrite: '<S210>/Data Store Write' */
    QFuelLam = localB->Fuel_Mass_Calculation.Switch;
  }

  /* End of Outputs for SubSystem: '<S151>/TDC_assignment' */

  /* Outputs for Atomic SubSystem: '<S151>/Fuel_Mass_Splitter' */
  /* Chart: '<S207>/Assign_QFObj_HB' incorporates:
   *  DataStoreRead: '<S207>/Data Store Read2'
   *  Product: '<S207>/Product1'
   */
  /* Gateway: QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_HB */
  /* During: QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_HB */
  /* Entry Internal: QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_HB */
  /* Transition: '<S211>:1' */
  QFObj[FuelMgmcyl4calcH] = (uint16_T)((localB->Fuel_Mass_Calculation.Switch *
    QFuelSplitFracH) >> 14);

  /* Chart: '<S207>/Assign_QFObj_LB' incorporates:
   *  DataStoreRead: '<S207>/Data Store Read1'
   *  Product: '<S207>/Product2'
   */
  /* Gateway: QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_LB */
  /* During: QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_LB */
  /* Entry Internal: QFObj_Calc/Fuel_Mass_Splitter/Assign_QFObj_LB */
  /* Transition: '<S212>:1' */
  QFObj[FuelMgmcyl4calc] = (uint16_T)((localB->Fuel_Mass_Calculation.Switch *
    QFuelSplitFrac) >> 14);

  /* End of Outputs for SubSystem: '<S151>/Fuel_Mass_Splitter' */

  /* Outputs for Atomic SubSystem: '<S151>/Fuel_Mass_Splitter_Base' */
  /* Chart: '<S208>/Assign_QFObjBase_HB' incorporates:
   *  DataStoreRead: '<S208>/Data Store Read2'
   *  Product: '<S208>/Product1'
   */
  /* Gateway: QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_HB */
  /* During: QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_HB */
  /* Entry Internal: QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_HB */
  /* Transition: '<S213>:1' */
  QFObjBase[FuelMgmcyl4calcH] = (uint16_T)
    ((localB->Fuel_Mass_Calculation_Base.Switch * QFuelSplitFracH) >> 14);

  /* Chart: '<S208>/Assign_QFObjBase_LB' incorporates:
   *  DataStoreRead: '<S208>/Data Store Read1'
   *  Product: '<S208>/Product2'
   */
  /* Gateway: QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_LB */
  /* During: QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_LB */
  /* Entry Internal: QFObj_Calc/Fuel_Mass_Splitter_Base/Assign_QFObjBase_LB */
  /* Transition: '<S214>:1' */
  QFObjBase[FuelMgmcyl4calc] = (uint16_T)
    ((localB->Fuel_Mass_Calculation_Base.Switch * QFuelSplitFrac) >> 14);

  /* End of Outputs for SubSystem: '<S151>/Fuel_Mass_Splitter_Base' */
}

/* Output and update for atomic system: '<S152>/Delta_QFuel_Calc' */
void FuelMgm_Delta_QFuel_Calc(void)
{
  int16_T rtb_DataTypeConversion1;
  int16_T rtb_Add1;
  uint16_T tmp;
  int32_T tmp_0;

  /* Selector: '<S227>/Selector' incorporates:
   *  DataStoreRead: '<S227>/Data Store Read3'
   *  DataStoreRead: '<S227>/Data Store Read4'
   */
  FuelMgm_TDC_B.Selector = QFObj[FuelMgmcyl4calc];

  /* Sum: '<S227>/Add' incorporates:
   *  DataStoreRead: '<S227>/Data Store Read1'
   *  DataStoreRead: '<S227>/Data Store Read3'
   *  Selector: '<S227>/Selector2'
   */
  tmp_0 = FuelMgm_TDC_B.Selector - QFObjOld[FuelMgmcyl4calc];
  if (tmp_0 > 32767) {
    tmp_0 = 32767;
  } else {
    if (tmp_0 < -32768) {
      tmp_0 = -32768;
    }
  }

  rtb_DataTypeConversion1 = (int16_T)tmp_0;

  /* End of Sum: '<S227>/Add' */

  /* DataTypeConversion: '<S227>/Data Type Conversion' */
  tmp = FuelMgm_TDC_B.Selector;
  if (FuelMgm_TDC_B.Selector > 32767) {
    tmp = 32767U;
  }

  rtb_Add1 = (int16_T)tmp;

  /* End of DataTypeConversion: '<S227>/Data Type Conversion' */

  /* S-Function (RatioCalcS16): '<S234>/RatioCalcS16' incorporates:
   *  Constant: '<S227>/ONE'
   */
  RatioCalcS16( &FuelMgm_TDC_B.RatioCalcS16_ptkf, rtb_DataTypeConversion1,
               rtb_Add1, 32767);

  /* DataStoreWrite: '<S227>/Data Store Write1' */
  DQFObjCyl = FuelMgm_TDC_B.RatioCalcS16_ptkf;

  /* Selector: '<S227>/Selector1' incorporates:
   *  DataStoreRead: '<S227>/Data Store Read2'
   *  DataStoreRead: '<S227>/Data Store Read3'
   *  DataStoreWrite: '<S227>/Data Store Write2'
   */
  QFuelCyl = QFuel[FuelMgmcyl4calc];

  /* Sum: '<S227>/Add1' incorporates:
   *  DataStoreRead: '<S227>/Data Store Read3'
   *  DataStoreRead: '<S227>/Data Store Read5'
   *  DataStoreWrite: '<S227>/Data Store Write2'
   *  Selector: '<S227>/Selector3'
   */
  tmp_0 = QFuelCyl - QFObjBase[FuelMgmcyl4calc];
  if (tmp_0 > 32767) {
    tmp_0 = 32767;
  } else {
    if (tmp_0 < -32768) {
      tmp_0 = -32768;
    }
  }

  rtb_Add1 = (int16_T)tmp_0;

  /* End of Sum: '<S227>/Add1' */

  /* DataTypeConversion: '<S227>/Data Type Conversion1' incorporates:
   *  DataStoreRead: '<S227>/Data Store Read3'
   *  DataStoreRead: '<S227>/Data Store Read5'
   *  Selector: '<S227>/Selector3'
   */
  tmp = QFObjBase[FuelMgmcyl4calc];
  if (QFObjBase[FuelMgmcyl4calc] > 32767) {
    tmp = 32767U;
  }

  rtb_DataTypeConversion1 = (int16_T)tmp;

  /* End of DataTypeConversion: '<S227>/Data Type Conversion1' */

  /* S-Function (RatioCalcS16): '<S233>/RatioCalcS16' incorporates:
   *  Constant: '<S227>/ONE'
   */
  RatioCalcS16( &FuelMgm_TDC_B.RatioCalcS16_joa2, rtb_Add1,
               rtb_DataTypeConversion1, 32767);

  /* DataStoreWrite: '<S227>/Data Store Write3' */
  DQFilmCyl = FuelMgm_TDC_B.RatioCalcS16_joa2;

  /* Chart: '<S227>/Assign_QFObjOld' */
  /* Gateway: FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/Assign_QFObjOld */
  /* During: FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/Assign_QFObjOld */
  /* Entry Internal: FuelMgm/TDC/QFuelAvg_Calc/Delta_QFuel_Calc/Assign_QFObjOld */
  /* Transition: '<S232>:1' */
  QFObjOld[FuelMgmcyl4calc] = FuelMgm_TDC_B.Selector;
}

/* Output and update for atomic system: '<S152>/Delta_QFuel_Calc_HighBank' */
void FuelM_Delta_QFuel_Calc_HighBank(void)
{
  int16_T rtb_DataTypeConversion1;
  int16_T rtb_Add1;
  uint16_T tmp;
  int32_T tmp_0;

  /* Selector: '<S228>/Selector' incorporates:
   *  DataStoreRead: '<S228>/Data Store Read3'
   *  DataStoreRead: '<S228>/Data Store Read4'
   */
  FuelMgm_TDC_B.Selector = QFObj[FuelMgmcyl4calcH];

  /* Sum: '<S228>/Add' incorporates:
   *  DataStoreRead: '<S228>/Data Store Read1'
   */
  tmp_0 = FuelMgm_TDC_B.Selector - QFObjCylH;
  if (tmp_0 > 32767) {
    tmp_0 = 32767;
  } else {
    if (tmp_0 < -32768) {
      tmp_0 = -32768;
    }
  }

  rtb_DataTypeConversion1 = (int16_T)tmp_0;

  /* End of Sum: '<S228>/Add' */

  /* DataTypeConversion: '<S228>/Data Type Conversion' */
  tmp = FuelMgm_TDC_B.Selector;
  if (FuelMgm_TDC_B.Selector > 32767) {
    tmp = 32767U;
  }

  rtb_Add1 = (int16_T)tmp;

  /* End of DataTypeConversion: '<S228>/Data Type Conversion' */

  /* S-Function (RatioCalcS16): '<S238>/RatioCalcS16' incorporates:
   *  Constant: '<S228>/ONE'
   */
  RatioCalcS16( &FuelMgm_TDC_B.RatioCalcS16, rtb_DataTypeConversion1, rtb_Add1,
               32767);

  /* DataStoreWrite: '<S228>/Data Store Write1' */
  DQFObjCylH = FuelMgm_TDC_B.RatioCalcS16;

  /* Selector: '<S228>/Selector1' incorporates:
   *  DataStoreRead: '<S228>/Data Store Read2'
   *  DataStoreRead: '<S228>/Data Store Read3'
   *  DataStoreWrite: '<S228>/Data Store Write2'
   */
  QFuelCylH = QFuel[FuelMgmcyl4calcH];

  /* Sum: '<S228>/Add1' incorporates:
   *  DataStoreRead: '<S228>/Data Store Read3'
   *  DataStoreRead: '<S228>/Data Store Read5'
   *  DataStoreWrite: '<S228>/Data Store Write2'
   *  Selector: '<S228>/Selector3'
   */
  tmp_0 = QFuelCylH - QFObjBase[FuelMgmcyl4calcH];
  if (tmp_0 > 32767) {
    tmp_0 = 32767;
  } else {
    if (tmp_0 < -32768) {
      tmp_0 = -32768;
    }
  }

  rtb_Add1 = (int16_T)tmp_0;

  /* End of Sum: '<S228>/Add1' */

  /* DataTypeConversion: '<S228>/Data Type Conversion1' incorporates:
   *  DataStoreRead: '<S228>/Data Store Read3'
   *  DataStoreRead: '<S228>/Data Store Read5'
   *  Selector: '<S228>/Selector3'
   */
  tmp = QFObjBase[FuelMgmcyl4calcH];
  if (QFObjBase[FuelMgmcyl4calcH] > 32767) {
    tmp = 32767U;
  }

  rtb_DataTypeConversion1 = (int16_T)tmp;

  /* End of DataTypeConversion: '<S228>/Data Type Conversion1' */

  /* S-Function (RatioCalcS16): '<S237>/RatioCalcS16' incorporates:
   *  Constant: '<S228>/ONE'
   */
  RatioCalcS16( &FuelMgm_TDC_B.RatioCalcS16_nc4k, rtb_Add1,
               rtb_DataTypeConversion1, 32767);

  /* DataStoreWrite: '<S228>/Data Store Write3' */
  DQFilmCylH = FuelMgm_TDC_B.RatioCalcS16_nc4k;

  /* DataStoreWrite: '<S228>/Data Store Write5' */
  QFObjCylH = FuelMgm_TDC_B.Selector;
}

/* Output and update for atomic system: '<S152>/StQFAcc_Calc' */
void FuelMgm_StQFAcc_Calc(void)
{
  /* Switch: '<S230>/Switch4' incorporates:
   *  DataStoreRead: '<S230>/Data Store Read'
   *  Logic: '<S230>/Logical Operator'
   *  RelationalOperator: '<S230>/Relational Operator3'
   *  RelationalOperator: '<S230>/Relational Operator4'
   */
  if ((FuelMgm_TDC_B.Selector > 0) && (FilmEnab >= 16384)) {
    /* Switch: '<S230>/Switch1' incorporates:
     *  Constant: '<S230>/THRQFACC'
     *  Constant: '<S230>/THRQFDEC'
     *  Constant: '<S230>/THRQFSTABACC'
     *  Constant: '<S230>/THRQFSTABDEC'
     *  Logic: '<S230>/Logical Operator1'
     *  RelationalOperator: '<S230>/Relational Operator'
     *  RelationalOperator: '<S230>/Relational Operator1'
     *  RelationalOperator: '<S230>/Relational Operator2'
     *  RelationalOperator: '<S230>/Relational Operator5'
     *  Switch: '<S230>/Switch2'
     *  Switch: '<S230>/Switch3'
     */
    if (FuelMgm_TDC_B.RatioCalcS16_ptkf > THRQFACC) {
      /* Chart: '<S230>/Assign_StQFAcc' incorporates:
       *  Constant: '<S230>/ST_QF_ACC'
       */
      StQFAcc[FuelMgmcyl4calc] = ((int8_T)ST_QF_ACC);
    } else if (FuelMgm_TDC_B.RatioCalcS16_ptkf < THRQFDEC) {
      /* Switch: '<S230>/Switch2' incorporates:
       *  Chart: '<S230>/Assign_StQFAcc'
       *  Constant: '<S230>/ST_QF_DEC'
       */
      StQFAcc[FuelMgmcyl4calc] = ((int8_T)ST_QF_DEC);
    } else if ((FuelMgm_TDC_B.RatioCalcS16_joa2 <= THRQFSTABACC) &&
               (FuelMgm_TDC_B.RatioCalcS16_joa2 >= THRQFSTABDEC)) {
      /* Switch: '<S230>/Switch3' incorporates:
       *  Chart: '<S230>/Assign_StQFAcc'
       *  Constant: '<S230>/ST_QF_STAB'
       *  Switch: '<S230>/Switch2'
       */
      StQFAcc[FuelMgmcyl4calc] = ((int8_T)ST_QF_STAB);
    } else {
      /* Chart: '<S230>/Assign_StQFAcc' incorporates:
       *  DataStoreRead: '<S230>/Data Store Read1'
       *  DataStoreRead: '<S230>/Data Store Read3'
       *  Selector: '<S230>/Selector1'
       *  Switch: '<S230>/Switch2'
       *  Switch: '<S230>/Switch3'
       */
      StQFAcc[FuelMgmcyl4calc] = StQFAcc[FuelMgmcyl4calc];
    }

    /* End of Switch: '<S230>/Switch1' */
  } else {
    /* Chart: '<S230>/Assign_StQFAcc' incorporates:
     *  Constant: '<S230>/ST_QF_STAB'
     */
    StQFAcc[FuelMgmcyl4calc] = ((int8_T)ST_QF_STAB);
  }

  /* End of Switch: '<S230>/Switch4' */
  /* Gateway: FuelMgm/TDC/QFuelAvg_Calc/StQFAcc_Calc/Assign_StQFAcc */
  /* During: FuelMgm/TDC/QFuelAvg_Calc/StQFAcc_Calc/Assign_StQFAcc */
  /* Entry Internal: FuelMgm/TDC/QFuelAvg_Calc/StQFAcc_Calc/Assign_StQFAcc */
  /* Transition: '<S241>:1' */
}

/* Output and update for atomic system: '<S152>/StQFAcc_Calc_HighBank' */
void FuelMgm_StQFAcc_Calc_HighBank(void)
{
  /* Switch: '<S231>/Switch4' incorporates:
   *  Constant: '<S231>/ST_QF_STAB'
   *  DataStoreRead: '<S231>/Data Store Read'
   *  DataStoreWrite: '<S231>/Data Store Write'
   *  Logic: '<S231>/Logical Operator'
   *  RelationalOperator: '<S231>/Relational Operator3'
   *  RelationalOperator: '<S231>/Relational Operator4'
   */
  if ((FuelMgm_TDC_B.Selector > 0) && (FilmEnabH >= 16384)) {
    /* Switch: '<S231>/Switch1' incorporates:
     *  Constant: '<S231>/THRQFACC'
     *  Constant: '<S231>/THRQFDEC'
     *  Constant: '<S231>/THRQFSTABACC'
     *  Constant: '<S231>/THRQFSTABDEC'
     *  Logic: '<S231>/Logical Operator1'
     *  RelationalOperator: '<S231>/Relational Operator'
     *  RelationalOperator: '<S231>/Relational Operator1'
     *  RelationalOperator: '<S231>/Relational Operator2'
     *  RelationalOperator: '<S231>/Relational Operator5'
     *  Switch: '<S231>/Switch2'
     *  Switch: '<S231>/Switch3'
     */
    if (FuelMgm_TDC_B.RatioCalcS16 > THRQFACC) {
      /* DataStoreWrite: '<S231>/Data Store Write' incorporates:
       *  Constant: '<S231>/ST_QF_ACC'
       */
      StQFAccH = ((int8_T)ST_QF_ACC);
    } else if (FuelMgm_TDC_B.RatioCalcS16 < THRQFDEC) {
      /* DataStoreWrite: '<S231>/Data Store Write' incorporates:
       *  Constant: '<S231>/ST_QF_DEC'
       *  Switch: '<S231>/Switch2'
       */
      StQFAccH = ((int8_T)ST_QF_DEC);
    } else {
      if ((FuelMgm_TDC_B.RatioCalcS16_nc4k <= THRQFSTABACC) &&
          (FuelMgm_TDC_B.RatioCalcS16_nc4k >= THRQFSTABDEC)) {
        /* DataStoreWrite: '<S231>/Data Store Write' incorporates:
         *  Constant: '<S231>/ST_QF_STAB'
         *  Switch: '<S231>/Switch2'
         *  Switch: '<S231>/Switch3'
         */
        StQFAccH = ((int8_T)ST_QF_STAB);
      }
    }

    /* End of Switch: '<S231>/Switch1' */
  } else {
    StQFAccH = ((int8_T)ST_QF_STAB);
  }

  /* End of Switch: '<S231>/Switch4' */
}

/* Output and update for atomic system: '<S8>/QFuelAvg_Calc' */
void FuelMgm_QFuelAvg_Calc(void)
{
  /* Outputs for Atomic SubSystem: '<S152>/Fuel_Mass_Calculation' */

  /* Inport: '<Root>/LamObj' incorporates:
   *  Inport: '<Root>/QAirBaseAvg'
   */
  FuelMgm_Fuel_Mass_Calculation(FuelMgm_TDC_B.LogicalOperator, LamObj,
    QAirBaseAvg, &FuelMgm_TDC_B.Fuel_Mass_Calculation);

  /* End of Outputs for SubSystem: '<S152>/Fuel_Mass_Calculation' */

  /* DataStoreWrite: '<S152>/Data Store Write' */
  QFuelAvg = FuelMgm_TDC_B.Fuel_Mass_Calculation.Switch;

  /* Outputs for Atomic SubSystem: '<S152>/Delta_QFuel_Calc' */
  FuelMgm_Delta_QFuel_Calc();

  /* End of Outputs for SubSystem: '<S152>/Delta_QFuel_Calc' */

  /* Outputs for Atomic SubSystem: '<S152>/StQFAcc_Calc' */
  FuelMgm_StQFAcc_Calc();

  /* End of Outputs for SubSystem: '<S152>/StQFAcc_Calc' */

  /* Outputs for Atomic SubSystem: '<S152>/Delta_QFuel_Calc_HighBank' */
  FuelM_Delta_QFuel_Calc_HighBank();

  /* End of Outputs for SubSystem: '<S152>/Delta_QFuel_Calc_HighBank' */

  /* Outputs for Atomic SubSystem: '<S152>/StQFAcc_Calc_HighBank' */
  FuelMgm_StQFAcc_Calc_HighBank();

  /* End of Outputs for SubSystem: '<S152>/StQFAcc_Calc_HighBank' */
}

/* Output and update for function-call system: '<S1>/TDC' */
void FuelMgm_TDC(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_IR_U8;
  uint16_T rtb_LookUp_IR_U8_m1kh;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_Sum2;
  uint16_T rtb_Memory;
  uint8_T rtb_Conversion3;
  uint16_T rtb_MultiportSwitch;
  uint8_T rtb_Conversion7;
  uint8_T rtb_Conversion6;
  int32_T exitg1;

  /* DataStoreWrite: '<S8>/Data Store Write' incorporates:
   *  Constant: '<S8>/ONE'
   */
  FuelMgmflagTDC = 1U;

  /* DataStoreWrite: '<S8>/Data Store Write1' incorporates:
   *  Constant: '<S8>/ZERO'
   */
  FuelMgmflagHTDC = 0U;

  /* Sum: '<S8>/Sum1' incorporates:
   *  Constant: '<S8>/OFF_HIGH_BANK'
   *  DataStoreWrite: '<S8>/Data Store Write3'
   *  Inport: '<Root>/AbsTdc'
   */
  FuelMgmcyl4calcH = (uint8_T)((uint32_T)((uint8_T)OFF_HIGH_BANK) + AbsTdc);

  /* DataStoreWrite: '<S8>/Data Store Write2' incorporates:
   *  Inport: '<Root>/AbsTdc'
   */
  FuelMgmcyl4calc = AbsTdc;

  /* Selector: '<S8>/Selector2' incorporates:
   *  Inport: '<Root>/AbsTdc'
   *  Inport: '<Root>/VtIdxCtfFlg'
   */
  rtb_Conversion3 = VtIdxCtfFlg[AbsTdc];

  /* Logic: '<S153>/Logical Operator' incorporates:
   *  Constant: '<S153>/IGN_INJ_CTF'
   *  Constant: '<S153>/INJ_CTF'
   *  RelationalOperator: '<S153>/Relational Operator'
   *  RelationalOperator: '<S153>/Relational Operator1'
   */
  FuelMgm_TDC_B.LogicalOperator = ((INJ_CTF == rtb_Conversion3) ||
    (rtb_Conversion3 == IGN_INJ_CTF));

  /* Outputs for Atomic SubSystem: '<S8>/QAir_selection' */
  /* Inport: '<Root>/QAirFuel' incorporates:
   *  Inport: '<Root>/AbsTdc'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/QAirCyl0'
   *  Inport: '<Root>/QAirTarget0'
   *  Inport: '<Root>/VtTbQAirGain'
   */
  FuelMgm_QAir_selection((&(QAirFuel[0])), QAirCyl0, QAirTarget0,
    (&(VtTbQAirGain[0])), AbsTdc, EndStartFlg, &FuelMgm_TDC_B.QAir_selection);

  /* End of Outputs for SubSystem: '<S8>/QAir_selection' */

  /* Outputs for Atomic SubSystem: '<S8>/QFObj_Calc' */
  /* Inport: '<Root>/Rpm' incorporates:
   *  Inport: '<Root>/AbsTdc'
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/LamComp'
   *  Selector: '<S8>/Selector1'
   */
  FuelMgm_QFObj_Calc(Rpm, LamComp[AbsTdc], FuelMgm_TDC_B.LogicalOperator, AbsTdc,
                     AngThrottle, FuelMgm_TDC_B.QAir_selection.Switch,
                     FuelMgm_TDC_B.QAir_selection.Selector2,
                     &FuelMgm_TDC_B.QFObj_Calc);

  /* End of Outputs for SubSystem: '<S8>/QFObj_Calc' */

  /* Outputs for Atomic SubSystem: '<S8>/Film_Interpolation' */
  /* DataTypeConversion: '<S160>/Data Type Conversion8' incorporates:
   *  Constant: '<S154>/BKPRESFUEL_dim'
   */
  rtb_Conversion3 = (uint8_T)BKPRESFUEL_dim;

  /* Switch: '<S155>/Switch' incorporates:
   *  Constant: '<S155>/Constant'
   *  Constant: '<S155>/FUELFILMPRES'
   *  Inport: '<Root>/TrqStartFlg'
   */
  if (TrqStartFlg != 0) {
    rtb_Conversion7 = FUELFILMPRES;
  } else {
    rtb_Conversion7 = 1U;
  }

  /* End of Switch: '<S155>/Switch' */

  /* MultiPortSwitch: '<S155>/Multiport Switch' incorporates:
   *  Inport: '<Root>/PresIntake'
   *  Inport: '<Root>/PresIntk0'
   *  Inport: '<Root>/PresObj'
   *  Inport: '<Root>/PresObjF'
   */
  switch (rtb_Conversion7) {
   case 0:
    rtb_MultiportSwitch = PresIntake;
    break;

   case 1:
    rtb_MultiportSwitch = PresIntk0;
    break;

   case 2:
    rtb_MultiportSwitch = PresObj;
    break;

   case 3:
    rtb_MultiportSwitch = PresObjF;
    break;

   default:
    rtb_MultiportSwitch = PresIntake;
    break;
  }

  /* End of MultiPortSwitch: '<S155>/Multiport Switch' */

  /* S-Function (PreLookUpIdSearch_U16): '<S160>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S154>/BKPRESFUEL'
   */
  PreLookUpIdSearch_U16( &FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1,
                        &FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2,
                        rtb_MultiportSwitch, &BKPRESFUEL[0], rtb_Conversion3);

  /* DataTypeConversion: '<S161>/Data Type Conversion8' incorporates:
   *  Constant: '<S154>/BKRPMFUEL_dim'
   */
  rtb_Conversion3 = (uint8_T)BKRPMFUEL_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S161>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S154>/BKRPMFUEL'
   *  Inport: '<Root>/Rpm'
   */
  PreLookUpIdSearch_U16( &FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
                        &FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, Rpm,
                        &BKRPMFUEL[0], rtb_Conversion3);

  /* DataTypeConversion: '<S156>/Conversion6' incorporates:
   *  Constant: '<S154>/BKTWFUEL_dim'
   */
  rtb_Conversion3 = (uint8_T)BKTWFUEL_dim;

  /* DataTypeConversion: '<S156>/Conversion7' incorporates:
   *  Constant: '<S154>/BKTDCFUEL_dim'
   */
  rtb_Conversion7 = (uint8_T)BKTDCFUEL_dim;

  /* DataTypeConversion: '<S158>/Data Type Conversion8' incorporates:
   *  Constant: '<S154>/BKTWFUEL_dim'
   */
  rtb_Conversion6 = (uint8_T)BKTWFUEL_dim;

  /* S-Function (PreLookUpIdSearch_S16): '<S158>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S154>/BKTWFUEL'
   *  Inport: '<Root>/TWater'
   */
  PreLookUpIdSearch_S16( &rtb_Sum2, &rtb_PreLookUpIdSearch_S16_o2, TWater,
                        &BKTWFUEL[0], rtb_Conversion6);

  /* DataTypeConversion: '<S159>/Data Type Conversion8' incorporates:
   *  Constant: '<S154>/BKTDCFUEL_dim'
   */
  rtb_Conversion6 = (uint8_T)BKTDCFUEL_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S159>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S154>/BKTDCFUEL'
   *  Inport: '<Root>/CntTdcCrkTot'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1, &rtb_Memory,
                        CntTdcCrkTot, &BKTDCFUEL[0], rtb_Conversion6);

  /* S-Function (Look2D_IR_U8): '<S156>/Look2D_IR_U8' incorporates:
   *  Constant: '<S154>/TBTWXFILM'
   */
  Look2D_IR_U8( &FuelMgm_TDC_B.Selector, &TBTWXFILM[0], rtb_Sum2,
               rtb_PreLookUpIdSearch_S16_o2, rtb_Conversion3,
               rtb_PreLookUpIdSearch_U16_o1, rtb_Memory, rtb_Conversion7);

  /* DataTypeConversion: '<S157>/Conversion6' incorporates:
   *  Constant: '<S154>/BKTWFUEL_dim'
   */
  rtb_Conversion6 = (uint8_T)BKTWFUEL_dim;

  /* DataTypeConversion: '<S157>/Conversion7' incorporates:
   *  Constant: '<S154>/BKTDCFUEL_dim'
   */
  rtb_Conversion7 = (uint8_T)BKTDCFUEL_dim;

  /* S-Function (Look2D_IR_U8): '<S157>/Look2D_IR_U8' incorporates:
   *  Constant: '<S154>/TBTWTAUFILM'
   */
  Look2D_IR_U8( &FuelMgm_TDC_B.Look2D_IR_U8, &TBTWTAUFILM[0], rtb_Sum2,
               rtb_PreLookUpIdSearch_S16_o2, rtb_Conversion6,
               rtb_PreLookUpIdSearch_U16_o1, rtb_Memory, rtb_Conversion7);

  /* Memory: '<S155>/Memory' */
  rtb_Memory = FuelMgm_TDC_DW.Memory_PreviousInput;

  /* Sum: '<S155>/Add' */
  FuelMgm_TDC_B.RatioCalcS16_joa2 = (int16_T)((int16_T)rtb_MultiportSwitch -
    (int16_T)rtb_Memory);

  /* Update for Memory: '<S155>/Memory' */
  FuelMgm_TDC_DW.Memory_PreviousInput = rtb_MultiportSwitch;

  /* End of Outputs for SubSystem: '<S8>/Film_Interpolation' */

  /* Outputs for Atomic SubSystem: '<S8>/Film_Par_Calc_LB' */
  FuelMgm_Film_Par_Calc_LB();

  /* End of Outputs for SubSystem: '<S8>/Film_Par_Calc_LB' */

  /* Outputs for Atomic SubSystem: '<S8>/Film_Par_Calc_HB' */
  /* Outputs for Atomic SubSystem: '<S147>/XFilm_Calculation_HB' */
  /* Chart: '<S164>/Chart' */
  /* Gateway: FuelMgm/TDC/Film_Par_Calc_HB/XFilm_Calculation_HB/Chart */
  /* During: FuelMgm/TDC/Film_Par_Calc_HB/XFilm_Calculation_HB/Chart */
  /* Entry Internal: FuelMgm/TDC/Film_Par_Calc_HB/XFilm_Calculation_HB/Chart */
  /* Transition: '<S167>:2' */
  rtb_Conversion3 = 0U;
  rtb_Conversion7 = 0U;
  do {
    exitg1 = 0;
    if (rtb_Conversion3 < ((uint8_T)N_CYLINDER)) {
      /* Transition: '<S167>:4' */
      if (QFObj[rtb_Conversion3 + ((uint8_T)OFF_HIGH_BANK)] != 0) {
        /* Transition: '<S167>:13' */
        rtb_Conversion7 = 1U;
        exitg1 = 1;
      } else {
        /* Transition: '<S167>:5' */
        rtb_Conversion3++;
      }
    } else {
      /* Transition: '<S167>:15' */
      exitg1 = 1;
    }
  } while (exitg1 == 0);

  /* Switch: '<S164>/Switch2' incorporates:
   *  Constant: '<S164>/0_FIX_14_1'
   *  Constant: '<S164>/ENFUELFILM'
   *  DataStoreWrite: '<S164>/Data Store Write2'
   *  Inport: '<Root>/EndStartFlg'
   *  Logic: '<S164>/Logical Operator1'
   */
  if ((EndStartFlg != 0) && (ENFUELFILM != 0) && (rtb_Conversion7 != 0)) {
    /* Sum: '<S164>/Sum2' incorporates:
     *  Constant: '<S164>/FILMENSTEPH'
     *  DataStoreRead: '<S164>/Data Store Read1'
     */
    rtb_MultiportSwitch = (uint16_T)((uint32_T)FILMENSTEPH + FilmEnabH);

    /* MinMax: '<S164>/MinMax2' incorporates:
     *  Constant: '<S164>/1_FIX_14_1'
     *  DataStoreWrite: '<S164>/Data Store Write2'
     */
    if (16384 < rtb_MultiportSwitch) {
      FilmEnabH = 16384U;
    } else {
      FilmEnabH = rtb_MultiportSwitch;
    }

    /* End of MinMax: '<S164>/MinMax2' */
  } else {
    FilmEnabH = 0U;
  }

  /* End of Switch: '<S164>/Switch2' */

  /* DataTypeConversion: '<S168>/Conversion3' incorporates:
   *  Constant: '<S164>/BKRPMFUEL_dim'
   */
  rtb_Conversion3 = (uint8_T)BKRPMFUEL_dim;

  /* S-Function (LookUp_IR_U8): '<S168>/LookUp_IR_U8' incorporates:
   *  Constant: '<S164>/VTXFILMACC'
   */
  LookUp_IR_U8( &rtb_Sum2, &VTXFILMACC[0],
               FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
               FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, rtb_Conversion3);

  /* Product: '<S164>/Product4' incorporates:
   *  DataStoreWrite: '<S164>/Data Store Write2'
   *  Product: '<S164>/Product3'
   */
  rtb_Sum2 = (uint16_T)(((uint32_T)(uint16_T)(((uint32_T)FuelMgm_TDC_B.Selector *
    rtb_Sum2) >> 12) * FilmEnabH) >> 14);

  /* MinMax: '<S164>/MinMax4' incorporates:
   *  Constant: '<S164>/MAX_XFILM1'
   *  DataStoreWrite: '<S164>/Data Store Write3'
   */
  if (rtb_Sum2 < ((int16_T)MAX_XFILM)) {
    XFilmH = rtb_Sum2;
  } else {
    XFilmH = (uint16_T)((int16_T)MAX_XFILM);
  }

  /* End of MinMax: '<S164>/MinMax4' */

  /* Sum: '<S164>/Sum3' incorporates:
   *  Constant: '<S164>/1_FIX_1'
   *  DataStoreWrite: '<S164>/Data Store Write3'
   */
  rtb_MultiportSwitch = (uint16_T)(32768 - XFilmH);

  /* End of Outputs for SubSystem: '<S147>/XFilm_Calculation_HB' */

  /* Outputs for Atomic SubSystem: '<S147>/GainFilm_Calc_HB' */
  /* Product: '<S162>/Product1' incorporates:
   *  DataStoreWrite: '<S162>/Data Store Write1'
   */
  GainFilmH = (uint16_T)(536870912U / rtb_MultiportSwitch);

  /* End of Outputs for SubSystem: '<S147>/GainFilm_Calc_HB' */

  /* Outputs for Atomic SubSystem: '<S147>/KFFilm_Calc_HB' */
  /* Switch: '<S163>/Switch2' incorporates:
   *  Constant: '<S163>/ST_QF_DEC'
   *  DataStoreRead: '<S163>/Data Store Read1'
   *  RelationalOperator: '<S163>/Relational Operator1'
   */
  if (StQFAccH != ((int8_T)ST_QF_DEC)) {
    /* DataTypeConversion: '<S165>/Conversion3' incorporates:
     *  Constant: '<S163>/BKRPMFUEL_dim'
     */
    rtb_Conversion3 = (uint8_T)BKRPMFUEL_dim;

    /* S-Function (LookUp_IR_U8): '<S165>/LookUp_IR_U8' incorporates:
     *  Constant: '<S163>/VTTAUFILMACC'
     */
    LookUp_IR_U8( &rtb_LookUp_IR_U8, &VTTAUFILMACC[0],
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, rtb_Conversion3);
    rtb_Sum2 = rtb_LookUp_IR_U8;
  } else {
    /* DataTypeConversion: '<S166>/Conversion3' incorporates:
     *  Constant: '<S163>/BKRPMFUEL_dim'
     */
    rtb_Conversion3 = (uint8_T)BKRPMFUEL_dim;

    /* S-Function (LookUp_IR_U8): '<S166>/LookUp_IR_U8' incorporates:
     *  Constant: '<S163>/VTTAUFILMDEC'
     */
    LookUp_IR_U8( &rtb_LookUp_IR_U8_m1kh, &VTTAUFILMDEC[0],
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o1_avwy,
                 FuelMgm_TDC_B.PreLookUpIdSearch_U16_o2_axaf, rtb_Conversion3);
    rtb_Sum2 = rtb_LookUp_IR_U8_m1kh;
  }

  /* End of Switch: '<S163>/Switch2' */

  /* Sum: '<S163>/Sum2' incorporates:
   *  Constant: '<S163>/1_FIX_8_1'
   *  Product: '<S163>/Product1'
   *  Product: '<S163>/Product6'
   */
  rtb_Sum2 = (uint16_T)((uint16_T)(((uint32_T)rtb_MultiportSwitch * (uint16_T)
    (((uint32_T)FuelMgm_TDC_B.Look2D_IR_U8 * rtb_Sum2) >> 12)) >> 15) + 256U);

  /* Product: '<S163>/Product2' incorporates:
   *  DataStoreWrite: '<S163>/Data Store Write1'
   */
  KFFilmH = (uint16_T)(8388608U / rtb_Sum2);

  /* End of Outputs for SubSystem: '<S147>/KFFilm_Calc_HB' */
  /* End of Outputs for SubSystem: '<S8>/Film_Par_Calc_HB' */

  /* Outputs for Atomic SubSystem: '<S8>/InjTime_Calc' */
  /* Inport: '<Root>/Rpm' incorporates:
   *  Inport: '<Root>/AbsHTdc'
   *  Inport: '<Root>/AbsTdc'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/PresAtm'
   *  Inport: '<Root>/PresInj'
   */
  FuelMgm_InjTime_Calc(Rpm, Load, PresAtm, PresInj, AbsTdc, AbsHTdc,
                       &FuelMgm_TDC_B.InjTime_Calc);

  /* End of Outputs for SubSystem: '<S8>/InjTime_Calc' */

  /* DataStoreWrite: '<S8>/Data Store Write4' */
  GainInjT = FuelMgm_TDC_B.InjTime_Calc.LookUp_IR_U16;

  /* DataStoreWrite: '<S8>/Data Store Write5' */
  OffInjT = FuelMgm_TDC_B.InjTime_Calc.Sum2;

  /* Outputs for Atomic SubSystem: '<S8>/QFuelAvg_Calc' */
  FuelMgm_QFuelAvg_Calc();

  /* End of Outputs for SubSystem: '<S8>/QFuelAvg_Calc' */
}

/* Output and update for atomic system: '<S6>/QFuelLth_Calc' */
void FuelMgm_QFuelLth_Calc(void)
{
  uint32_T QFuelExtTot0;
  uint32_T QFuelTot0;

  /* Chart: '<S138>/Chart' incorporates:
   *  DataStoreRead: '<S138>/Data Store Read1'
   *  DataStoreRead: '<S138>/Data Store Read2'
   *  Inport: '<Root>/Rpm'
   */
  /* Gateway: FuelMgm/T100ms/QFuelLth_Calc/Chart */
  /* During: FuelMgm/T100ms/QFuelLth_Calc/Chart */
  /* Entry Internal: FuelMgm/T100ms/QFuelLth_Calc/Chart */
  /* Transition: '<S139>:2' */
  if (QFuelIntTot < FuelMgm_T100ms_DW.oldQFuelIntTot) {
    /* Transition: '<S139>:4' */
    QFuelTot0 = (MAX_OVF - FuelMgm_T100ms_DW.oldQFuelIntTot) + QFuelIntTot;
    FuelMgm_T100ms_DW.oldQFuelIntTot = QFuelIntTot;
  } else {
    /* Transition: '<S139>:5' */
    QFuelTot0 = QFuelIntTot - FuelMgm_T100ms_DW.oldQFuelIntTot;
    FuelMgm_T100ms_DW.oldQFuelIntTot = QFuelIntTot;
  }

  if (QFuelIntExtTot < FuelMgm_T100ms_DW.oldQFuelIntExtTot) {
    /* Transition: '<S139>:6' */
    QFuelExtTot0 = (MAX_OVF - FuelMgm_T100ms_DW.oldQFuelIntExtTot) +
      QFuelIntExtTot;
    FuelMgm_T100ms_DW.oldQFuelIntExtTot = QFuelIntExtTot;
  } else {
    /* Transition: '<S139>:7' */
    QFuelExtTot0 = QFuelIntExtTot - FuelMgm_T100ms_DW.oldQFuelIntExtTot;
    FuelMgm_T100ms_DW.oldQFuelIntExtTot = QFuelIntExtTot;
  }

  /* Transition: '<S139>:10' */
  FuelMgm_T100ms_DW.tmpQFuelTot0Lth = (FuelMgm_T100ms_DW.tmpQFuelTot0Lth +
    QFuelTot0) + QFuelExtTot0;
  if ((FuelMgm_T100ms_DW.cnt >= 9) || (Rpm == 0)) {
    /* Transition: '<S139>:28' */
    FuelMgm_T100ms_B.QFuelTot0Lth = FuelMgm_T100ms_DW.tmpQFuelTot0Lth;
    FuelMgm_T100ms_DW.tmpQFuelTot0Lth = 0U;
    FuelMgm_T100ms_DW.cnt = 0U;
  } else {
    /* Transition: '<S139>:29' */
    FuelMgm_T100ms_DW.cnt++;
  }

  /* End of Chart: '<S138>/Chart' */

  /* Switch: '<S138>/Switch' incorporates:
   *  Constant: '<S138>/750000 [mg//l]'
   *  Constant: '<S138>/SELQFLTH'
   *  DataStoreWrite: '<S138>/Data Store Write1'
   *  Inport: '<Root>/Rpm'
   *  Product: '<S138>/MulDiv'
   *  Product: '<S138>/MulDiv1'
   *  Product: '<S138>/MulDiv2'
   *  Product: '<S138>/MulDiv3'
   *  Product: '<S138>/MulDiv4'
   */
  if (SELQFLTH != 0) {
    /* Product: '<S138>/MulDiv2' incorporates:
     *  Constant: '<S138>/N_CYLINDER'
     *  DataStoreRead: '<S138>/Data Store Read'
     */
    QFuelTot0 = (uint32_T)QFuelAvg * ((uint8_T)N_CYLINDER);
    if (QFuelTot0 > 65535U) {
      QFuelTot0 = 65535U;
    }

    QFuelLth = (uint16_T)(((uint32_T)(uint16_T)((QFuelTot0 * Rpm) >> 13) << 10) /
                          3125U);
  } else {
    QFuelLth = (uint16_T)(FuelMgm_T100ms_B.QFuelTot0Lth * 36U / QFUELDENS);
  }

  /* End of Switch: '<S138>/Switch' */

  /* Sum: '<S138>/Add' incorporates:
   *  DataStoreRead: '<S138>/Data Store Read3'
   *  DataStoreWrite: '<S138>/Data Store Write1'
   */
  QFuelTot0 = ((uint32_T)QFuelLth >> 2) + QFuelIntLth;

  /* DataStoreWrite: '<S138>/Data Store Write2' */
  QFuelIntLth = QFuelTot0;

  /* Product: '<S138>/MulDiv5' incorporates:
   *  DataStoreWrite: '<S138>/Data Store Write3'
   */
  FuelLt = (uint16_T)(QFuelTot0 / 36000U);
}

/* Output and update for function-call system: '<S1>/T100ms' */
void FuelMgm_T100ms(void)
{
  /* Outputs for Atomic SubSystem: '<S6>/QFuelLth_Calc' */
  FuelMgm_QFuelLth_Calc();

  /* End of Outputs for SubSystem: '<S6>/QFuelLth_Calc' */
}

/* Output and update for function-call system: '<S1>/HTDC' */
void FuelMgm_HTDC(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  FuelMgmflagTDC = 0U;

  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/ONE'
   */
  FuelMgmflagHTDC = 1U;

  /* Sum: '<S2>/Sum1' incorporates:
   *  Constant: '<S2>/OFF_HIGH_BANK'
   *  DataStoreWrite: '<S2>/Data Store Write3'
   *  Inport: '<Root>/AbsHTdc'
   */
  FuelMgmcyl4calcH = (uint8_T)((uint32_T)AbsHTdc + ((uint8_T)OFF_HIGH_BANK));

  /* DataStoreWrite: '<S2>/Data Store Write2' incorporates:
   *  Inport: '<Root>/AbsHTdc'
   */
  FuelMgmcyl4calc = AbsHTdc;

  /* Outputs for Atomic SubSystem: '<S2>/QAir_selection' */

  /* Inport: '<Root>/QAirFuel' incorporates:
   *  Inport: '<Root>/AbsHTdc'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/QAirCyl0'
   *  Inport: '<Root>/QAirTarget0'
   *  Inport: '<Root>/VtTbQAirGain'
   */
  FuelMgm_QAir_selection((&(QAirFuel[0])), QAirCyl0, QAirTarget0,
    (&(VtTbQAirGain[0])), AbsHTdc, EndStartFlg, &FuelMgm_HTDC_B.QAir_selection);

  /* End of Outputs for SubSystem: '<S2>/QAir_selection' */

  /* Outputs for Atomic SubSystem: '<S2>/QFObj_Calc' */

  /* Inport: '<Root>/Rpm' incorporates:
   *  Constant: '<S19>/IGN_INJ_CTF'
   *  Constant: '<S19>/INJ_CTF'
   *  Inport: '<Root>/AbsHTdc'
   *  Inport: '<Root>/AbsTdc'
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/LamComp'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/PresAtm'
   *  Inport: '<Root>/PresInj'
   *  Inport: '<Root>/VtIdxCtfFlg'
   *  Logic: '<S19>/Logical Operator'
   *  RelationalOperator: '<S19>/Relational Operator'
   *  RelationalOperator: '<S19>/Relational Operator1'
   *  Selector: '<S2>/Selector1'
   *  Selector: '<S2>/Selector2'
   */
  FuelMgm_QFObj_Calc(Rpm, LamComp[AbsHTdc], (INJ_CTF == VtIdxCtfFlg[AbsHTdc]) ||
                     (VtIdxCtfFlg[AbsHTdc] == IGN_INJ_CTF), AbsTdc, AngThrottle,
                     FuelMgm_HTDC_B.QAir_selection.Switch,
                     FuelMgm_HTDC_B.QAir_selection.Selector2,
                     &FuelMgm_HTDC_B.QFObj_Calc);

  /* End of Outputs for SubSystem: '<S2>/QFObj_Calc' */

  /* Outputs for Atomic SubSystem: '<S2>/InjTime_Calc' */
  FuelMgm_InjTime_Calc(Rpm, Load, PresAtm, PresInj, AbsTdc, AbsHTdc,
                       &FuelMgm_HTDC_B.InjTime_Calc);

  /* End of Outputs for SubSystem: '<S2>/InjTime_Calc' */

  /* DataStoreWrite: '<S2>/Data Store Write4' */
  GainInjT = FuelMgm_HTDC_B.InjTime_Calc.LookUp_IR_U16;

  /* DataStoreWrite: '<S2>/Data Store Write5' */
  OffInjT = FuelMgm_HTDC_B.InjTime_Calc.Sum2;
}

/* Output and update for function-call system: '<S1>/PreTDC' */
void FuelMgm_PreTDC(void)
{
  /* DataStoreWrite: '<S4>/Data Store Write' incorporates:
   *  Constant: '<S4>/ZERO'
   */
  FuelMgmflagTDC = 0U;

  /* DataStoreWrite: '<S4>/Data Store Write1' incorporates:
   *  Constant: '<S4>/ONE'
   */
  FuelMgmflagHTDC = 1U;

  /* Sum: '<S4>/Sum1' incorporates:
   *  Constant: '<S4>/OFF_HIGH_BANK'
   *  DataStoreWrite: '<S4>/Data Store Write3'
   *  Inport: '<Root>/AbsPreTdc'
   */
  FuelMgmcyl4calcH = (uint8_T)((uint32_T)AbsPreTdc + ((uint8_T)OFF_HIGH_BANK));

  /* DataStoreWrite: '<S4>/Data Store Write2' incorporates:
   *  Inport: '<Root>/AbsPreTdc'
   */
  FuelMgmcyl4calc = AbsPreTdc;

  /* Outputs for Atomic SubSystem: '<S4>/QAir_selection' */

  /* Inport: '<Root>/QAirFuel' incorporates:
   *  Inport: '<Root>/AbsPreTdc'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/QAirCyl0'
   *  Inport: '<Root>/QAirTarget0'
   *  Inport: '<Root>/VtTbQAirGain'
   */
  FuelMgm_QAir_selection((&(QAirFuel[0])), QAirCyl0, QAirTarget0,
    (&(VtTbQAirGain[0])), AbsPreTdc, EndStartFlg,
    &FuelMgm_PreTDC_B.QAir_selection);

  /* End of Outputs for SubSystem: '<S4>/QAir_selection' */

  /* Outputs for Atomic SubSystem: '<S4>/QFObj_Calc' */

  /* Inport: '<Root>/Rpm' incorporates:
   *  Constant: '<S80>/IGN_INJ_CTF'
   *  Constant: '<S80>/INJ_CTF'
   *  Inport: '<Root>/AbsHTdc'
   *  Inport: '<Root>/AbsPreTdc'
   *  Inport: '<Root>/AbsTdc'
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/LamComp'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/PresAtm'
   *  Inport: '<Root>/PresInj'
   *  Inport: '<Root>/VtIdxCtfFlg'
   *  Logic: '<S80>/Logical Operator'
   *  RelationalOperator: '<S80>/Relational Operator'
   *  RelationalOperator: '<S80>/Relational Operator1'
   *  Selector: '<S4>/Selector1'
   *  Selector: '<S4>/Selector2'
   */
  FuelMgm_QFObj_Calc(Rpm, LamComp[AbsPreTdc], (INJ_CTF == VtIdxCtfFlg[AbsPreTdc])
                     || (VtIdxCtfFlg[AbsPreTdc] == IGN_INJ_CTF), AbsTdc,
                     AngThrottle, FuelMgm_PreTDC_B.QAir_selection.Switch,
                     FuelMgm_PreTDC_B.QAir_selection.Selector2,
                     &FuelMgm_PreTDC_B.QFObj_Calc);

  /* End of Outputs for SubSystem: '<S4>/QFObj_Calc' */

  /* Outputs for Atomic SubSystem: '<S4>/InjTime_Calc' */
  FuelMgm_InjTime_Calc(Rpm, Load, PresAtm, PresInj, AbsTdc, AbsHTdc,
                       &FuelMgm_PreTDC_B.InjTime_Calc);

  /* End of Outputs for SubSystem: '<S4>/InjTime_Calc' */

  /* DataStoreWrite: '<S4>/Data Store Write4' */
  GainInjT = FuelMgm_PreTDC_B.InjTime_Calc.LookUp_IR_U16;

  /* DataStoreWrite: '<S4>/Data Store Write5' */
  OffInjT = FuelMgm_PreTDC_B.InjTime_Calc.Sum2;
}

/* Model step function */
void FuelMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/FuelMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem6' incorporates:
   *  TriggerPort: '<S14>/Trigger'
   */
  /* Inport: '<Root>/ev_HTDC' */
  if ((FuelMgm_U.ev_HTDC > 0) &&
      (FuelMgm_PrevZCSigState.TriggeredSubsystem6_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S14>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/HTDC'
     */
    FuelMgm_HTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S14>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem6_Trig_ZCE = (ZCSigState)
    (FuelMgm_U.ev_HTDC > 0);

  /* End of Inport: '<Root>/ev_HTDC' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem6' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem1' incorporates:
   *  TriggerPort: '<S9>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' incorporates:
   *  Inport: '<Root>/ev_NoSync'
   */
  if (((FuelMgm_U.ev_PowerOn > 0) &&
       (FuelMgm_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[0] != POS_ZCSIG)) ||
      ((FuelMgm_U.ev_NoSync > 0) &&
       (FuelMgm_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[1] != POS_ZCSIG))) {
    /* S-Function (fcncallgen): '<S9>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    FuelMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S9>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[0] = (ZCSigState)
    (FuelMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */

  /* Inport: '<Root>/ev_NoSync' */
  FuelMgm_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[1] = (ZCSigState)
    (FuelMgm_U.ev_NoSync > 0);

  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem1' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem7' incorporates:
   *  TriggerPort: '<S15>/Trigger'
   */
  /* Inport: '<Root>/ev_PreTDC' */
  if ((FuelMgm_U.ev_PreTDC > 0) &&
      (FuelMgm_PrevZCSigState.TriggeredSubsystem7_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S15>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PreTDC'
     */
    FuelMgm_PreTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S15>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem7_Trig_ZCE = (ZCSigState)
    (FuelMgm_U.ev_PreTDC > 0);

  /* End of Inport: '<Root>/ev_PreTDC' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem7' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem2' incorporates:
   *  TriggerPort: '<S10>/Trigger'
   */
  /* Inport: '<Root>/ev_Sync' */
  if ((FuelMgm_U.ev_Sync > 0) &&
      (FuelMgm_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S10>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Sync'
     */
    FuelMgm_Sync();

    /* End of Outputs for S-Function (fcncallgen): '<S10>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE = (ZCSigState)
    (FuelMgm_U.ev_Sync > 0);

  /* End of Inport: '<Root>/ev_Sync' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem2' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem5' incorporates:
   *  TriggerPort: '<S13>/Trigger'
   */
  /* Inport: '<Root>/ev_100ms' */
  if ((FuelMgm_U.ev_100ms > 0) &&
      (FuelMgm_PrevZCSigState.TriggeredSubsystem5_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S13>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T100ms'
     */
    FuelMgm_T100ms();

    /* End of Outputs for S-Function (fcncallgen): '<S13>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem5_Trig_ZCE = (ZCSigState)
    (FuelMgm_U.ev_100ms > 0);

  /* End of Inport: '<Root>/ev_100ms' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem5' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem3' incorporates:
   *  TriggerPort: '<S11>/Trigger'
   */
  /* Inport: '<Root>/ev_10ms' */
  if ((FuelMgm_U.ev_10ms > 0) &&
      (FuelMgm_PrevZCSigState.TriggeredSubsystem3_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S11>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    FuelMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S11>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem3_Trig_ZCE = (ZCSigState)
    (FuelMgm_U.ev_10ms > 0);

  /* End of Inport: '<Root>/ev_10ms' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem3' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem4' incorporates:
   *  TriggerPort: '<S12>/Trigger'
   */
  /* Inport: '<Root>/ev_TDC' */
  if ((FuelMgm_U.ev_TDC > 0) &&
      (FuelMgm_PrevZCSigState.TriggeredSubsystem4_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S12>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/TDC'
     */
    FuelMgm_TDC();

    /* End of Outputs for S-Function (fcncallgen): '<S12>/Function-Call Generator' */
  }

  FuelMgm_PrevZCSigState.TriggeredSubsystem4_Trig_ZCE = (ZCSigState)
    (FuelMgm_U.ev_TDC > 0);

  /* End of Inport: '<Root>/ev_TDC' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem4' */

  /* End of Outputs for SubSystem: '<Root>/FuelMgm' */
}

/* Model initialize function */
void FuelMgm_initialize(void)
{
  FuelMgm_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[0] = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[1] = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem3_Trig_ZCE = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem4_Trig_ZCE = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem5_Trig_ZCE = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem6_Trig_ZCE = POS_ZCSIG;
  FuelMgm_PrevZCSigState.TriggeredSubsystem7_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/FuelMgm' */
void FuelMgm_NoSync(void)
{
  FuelMgm_Init();
}

#else

// Stub FuelMgm
uint16_T QFuelLth;

#endif                                 // _BUILD_FUELMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
