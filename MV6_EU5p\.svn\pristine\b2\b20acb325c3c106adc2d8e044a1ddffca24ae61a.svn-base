/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/MV1/tree/DD/COMMON/SPIST_L99#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1936   $                                                                                          */
/* $Date:: 2009-11-16 17:44:27 +0100 (lun, 16 nov 2009)   $                                                      */
/* $Author:: ToniS                   $                                                                       */
/*****************************************************************************************************************/

#ifndef __SPIST_L9958__
#define __SPIST_L9958__

#include "typedefs.h"
#include "Hbridge.h"
#include "OS_api.h"
#include "OS_resources.h"
#include "spi.h"
#include "Timing.h"

/***************************************************************
// Comandi */
#define SPIST_L9958_RESET_DREG 0x0000 /* Reset registro diagnosi. */
#define SPIST_L9958_NORESET_DREG 0x0002 /* Non resetta tutte le diagnosi nel registro diagnosi. */
#define SPIST_L9958_LIM_2_5A 0x0000 /* Limitatore a 2.5A. */
#define SPIST_L9958_LIM_4_0A 0x0004 /* Limitatore a 4.0A. */
#define SPIST_L9958_LIM_6_6A 0x0008 /* Limitatore a 6.6A. */
#define SPIST_L9958_LIM_8_6A 0x000C /* Limitatore a 8.6A. */
#define SPIST_L9958_VSR_4 0x0000 /* Voltage slewrate a 4V/us. */
#define SPIST_L9958_VSR_2 0x0100 /* Voltage slewrate a 2V/us. */
#define SPIST_L9958_ISR_3 0x0000 /* I slewrate a 3A/us. */
#define SPIST_L9958_ISR_0_3 0x0200 /* I slewrate a 0.3A/us. */
#define SPIST_L9958_ISR_DIS 0x0000 /* I slewrate control disable. */
#define SPIST_L9958_ISR_EN 0x0400 /* I slewrate control enable. */
#define SPIST_L9958_OL_DIAG_OFF 0x0000 /* Diagnosi Open load disattiva su stato On del device. */
#define SPIST_L9958_OL_ONSTATE 0x0800 /* Diagnosi Open load addiva su stato On del device. */
/*************************************************************
//DIAG Table Mask. */
#define SPIST_L9958_OL_OFF 0x0001 /* Open load in condizione off. */
#define SPIST_L9958_OL_ON 0x0002 /* Open load in condizione On. */
#define SPIST_L9958_VS_UV 0x0004 /* Under voltage. */
#define SPIST_L9958_VDD_OV 0x0008 /* Over voltage. */
#define SPIST_L9958_LIM 0x0010 /* Limitazione di corrente. */
#define SPIST_L9958_TEMPWARN 0x0020 /* Warning di temperatura. */
#define SPIST_L9958_TEMPOV 0x0040 /* Sovratemperatura. */
#define SPIST_L9958_ACT 0x0080 /* H-Bridge Enable. */
#define SPIST_L9958_OC_LS1 0x0100 /* Sovracorrente LSD 1. */
#define SPIST_L9958_OC_LS2 0x0200 /* Sovracorrente LSD 2. */
#define SPIST_L9958_OC_HS1 0x0400 /* Sovracorrente HSD 1. */
#define SPIST_L9958_OC_HS2 0x0800 /* Sovracorrente HSD 2. */
#define SPIST_L9958_SS_GND 0x4000 /* Corto a massa. */
#define SPIST_L9958_SS_VBAT 0x8000 /* Corto a VBatt. */
#define SPIST_L9958_RESERVED 0x3FFF /* Filtro su comando. */
#define SPIST_L9958_TEST 0xFF00
/*************************************************************
//Control Setup. */
#define SPIST_L9958_CMDCFG_H1 (SPIST_L9958_OL_DIAG_OFF| SPIST_L9958_ISR_3 | SPIST_L9958_VSR_4 | SPIST_L9958_LIM_8_6A)
#define SPIST_L9958_GETDIAG_H1 SPIST_L9958_CMDCFG_H1 /* Richiesta diagnosi. */
#define SPIST_L9958_RES_GETDIAG_H1 (SPIST_L9958_CMDCFG_H1 | SPIST_L9958_RESET_DREG) /* Richiesta reset diagnosi e diagnosi. */

#define SPIST_L9958_CMDCFG_H2 (SPIST_L9958_OL_DIAG_OFF| SPIST_L9958_ISR_3 | SPIST_L9958_VSR_4 | SPIST_L9958_LIM_2_5A)
#define SPIST_L9958_GETDIAG_H2 SPIST_L9958_CMDCFG_H2 /* Richiesta diagnosi. */
#define SPIST_L9958_RES_GETDIAG_H2 (SPIST_L9958_CMDCFG_H2 | SPIST_L9958_RESET_DREG) /* Richiesta reset diagnosi e diagnosi. */

#define SPIST_L9958_CFGCTRL 0
#define SPIST_L9958_NORMALMODE 1

#define SPIST_L9958_NOTCFG 0x0
#define SPIST_L9958_RQCFG 0xC3
#define SPIST_L9958_ACK_TRX 0xA5
/*************************************************************
// Attivazione Debug. */
#define _SPIST_L9958_DEBUG_

/**************** PINOUT REMAP ******************/
/* Canali e ChipSelect SPI per i ST_L9958. */
#define SPIST_L9958_SIZE_COM 1
/* BRIDGE_A */
#define SPIST_L9958_H1_CH SPI_CH_C 
#define SPIST_L9958_H1_PCS PCS2
/* BRIDGE_B */
#define SPIST_L9958_H2_CH SPI_CH_C 
#define SPIST_L9958_H2_PCS PCS3
/**************** PINOUT REMAP ******************/

#undef TEST_MSG_HB /* Test invio messaggi SPI. */

/* Var Esterne. */
extern uint8_T SpiSTL9958OFFH1;
extern uint8_T SpiSTL9958OFFH2;

/* Interfaccie esterne. */
void SPIST_L9958Cmd_Init (void);
void Hbridge_Diagnosis(uint8_T bridge);

#endif /* END_ST_L9958 */

