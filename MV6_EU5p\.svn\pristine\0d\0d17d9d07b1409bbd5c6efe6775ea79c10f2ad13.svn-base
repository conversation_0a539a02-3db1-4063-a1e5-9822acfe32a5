/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_PATMMODEL_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//PATMMODEL.BKRPMANGPATM: Rpm breakpoint values [rpm]
CALQUAL uint16_T BKRPMANGPATM[6] = 
{
      0u,      0u,      0u,      0u,      0u,      0u
};
//PATMMODEL.NRUNWAITPATM: Number of TDC tasks before MapSignal acquisition [counter]
CALQUAL uint16_T NRUNWAITPATM =    0u;   //   0
//PATMMODEL.NWAITPATM: Number of 5ms tasks before MapSignal acquisition [counter]
CALQUAL uint16_T NWAITPATM =   25u;   //  25
//PATMMODEL.PATMADAPTGAIN: Gain for atmpspheric pressure correction in running mode [gain]
CALQUAL uint16_T PATMADAPTGAIN = 4915u;   //(0.149993896484375*32768)
//PATMMODEL.PATMBUFFSIZE: MapSignal buffer size [counter]
CALQUAL uint8_T PATMBUFFSIZE =  10u;   // 10
//PATMMODEL.PRESATMGAIN: Atmospheric Pressure Gain [mbar/mV]
CALQUAL uint16_T PRESATMGAIN = 192u;   //( 0.1875000000*1024)
//PATMMODEL.PRESATMMAX: (SR) Maximum Atmospheric Pressure [mbar]
CALQUAL uint16_T PRESATMMAX =  1080u;   // 1080
//PATMMODEL.PRESATMMIN: (SR) Minimum Atmospheric Pressure [mbar]
CALQUAL uint16_T PRESATMMIN =   700u;   //  700
//PATMMODEL.PRESATMNOM: (SR) Nominal atmospheric pressure [mbar]
CALQUAL uint16_T PRESATMNOM =  1024u;   // 1024
//PATMMODEL.PRESATMOFFSET: Atmospheric Pressure Offset [mbar]
CALQUAL uint16_T PRESATMOFFSET =   306u;   //  306
//PATMMODEL.USEPATMSENSOR: Calibration flag to enable PresAtm from sensor [flag]
CALQUAL uint8_T USEPATMSENSOR =  0u;   // 0
//PATMMODEL.VPRESATMMAX: Maximum value of VPresAtm for diagnosis [mV]
CALQUAL uint16_T VPRESATMMAX = 993u;   //(4848.6328125*0.2048)
//PATMMODEL.VPRESATMMIN: Minimum value of VPresAtm for diagnosis [mV]
CALQUAL uint16_T VPRESATMMIN = 31u;   //( 151.3671875*0.2048)
//PATMMODEL.VTANGTHRPATMMAX: AngThrPAtmMax vector [%]
CALQUAL uint16_T VTANGTHRPATMMAX[6] = 
{
 1600u, 1600u, 1600u, 1600u, 1600u, 1600u
};
//PATMMODEL.VTANGTHRPATMMIN: AngThrPAtmMin vector [%]
CALQUAL uint16_T VTANGTHRPATMMIN[6] = 
{
 1280u, 1280u, 1280u, 1280u, 1280u, 1280u
};

#endif /* _BUILD_PATMMODEL_ */

