/*
 * File: div_nzp_repeat_s32_sat.h
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.149
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Tue Aug  6 12:12:43 2019
 */

#ifndef SHARE_div_nzp_repeat_s32_sat
#define SHARE_div_nzp_repeat_s32_sat
#include "rtwtypes.h"

extern int32_T div_nzp_repeat_s32_sat(int32_T numerator, int32_T denominator,
  uint32_T nRepeatSub);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
