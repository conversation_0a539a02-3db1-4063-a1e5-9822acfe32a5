VERSION "HIPBNYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY/4/%%%/4/'%**4YYY///"


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_

BS_:

BU_: KUBO NST


BO_ 272 NST1: 8 NST
 SG_ VehSpeed2CAN : 7|16@0+ (0.25,0) [0|16383.8] "Km/h"  KUBO
 SG_ VehSpeedLimCAN : 23|16@0+ (0.25,0) [0|16383.8] "Km/h"  KUBO
 SG_ VehSpeedLimReqFlg : 39|1@0+ (1,0) [0|1] "boolean"  KUBO
 SG_ VDVehSpeed2CAN : 38|1@0+ (-1,1) [0|1] "boolean"  KUBO
 SG_ RpmLimReqFlg : 47|1@0+ (1,0) [0|1] "boolean"  KUBO

BO_ 288 NST_Engine1: 8 KUBO
 SG_ ThrottleDataValid : 7|1@0+ (1,0) [0|1] "boolean"  NST
 SG_ RpmDataValid : 6|1@0+ (1,0) [0|1] "boolean"  NST
 SG_ VehSpeedLimitDone : 5|1@0+ (1,0) [0|1] "boolean"  NST
 SG_ reserved : 4|5@0+ (1,0) [0|0] ""  NST
 SG_ Rpm : 15|16@0+ (1,0) [0|20000] "rpm"  NST
 SG_ AngThrottle : 31|8@0+ (1,0) [0|100] "%"  NST
 SG_ RpmLimitationDone : 39|1@0+ (1,0) [0|1] "boolean"  NST
 SG_ reserved1 : 38|7@0+ (1,0) [0|0] ""  NST
 SG_ ECUIdentifier : 47|8@0+ (1,0) [0|255] "id"  NST
 SG_ reserved2 : 55|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved3 : 63|8@0+ (1,0) [0|0] ""  NST

BO_ 384 NST_Engine2: 8 KUBO
 SG_ ThrottleCCtoVCC : 7|1@0+ (1,0) [0|1] "boolean"  NST
 SG_ ThrottleCCtoGND : 6|1@0+ (1,0) [0|1] "boolean"  NST
 SG_ reserved0 : 5|6@0+ (1,0) [0|0] ""  NST
 SG_ reserved1 : 15|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved2 : 23|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved3 : 31|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved4 : 39|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved5 : 47|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved6 : 55|8@0+ (1,0) [0|0] ""  NST
 SG_ reserved7 : 63|8@0+ (1,0) [0|0] ""  NST


CM_ BU_ KUBO "Eldor ECU";
CM_ BU_ NST "Suspension Control Unit";
CM_ BO_ 272 "100Hz";
CM_ SG_ 272 VehSpeedLimReqFlg "";
CM_ BO_ 288 "200Hz";
CM_ SG_ 288 ThrottleDataValid "Throttle position valid data";
CM_ SG_ 288 RpmDataValid "Rpm valid data";
CM_ SG_ 288 VehSpeedLimitDone "Vehicle speed limitation done flag";
CM_ SG_ 288 reserved "reserved";
CM_ SG_ 288 Rpm "Engine Speed";
CM_ SG_ 288 AngThrottle "Throttle opening percentage ";
CM_ SG_ 288 RpmLimitationDone "Rpm limitation done flag";
CM_ SG_ 288 reserved1 "reserved";
CM_ SG_ 288 ECUIdentifier "Ecu identifier byte";
CM_ SG_ 288 reserved2 "reserved";
CM_ SG_ 288 reserved3 "reserved";
CM_ BO_ 384 "10Hz";
CM_ SG_ 384 ThrottleCCtoVCC "Throttle pot. CC to VCC";
CM_ SG_ 384 ThrottleCCtoGND "Throttle pot. CC to GND";
CM_ SG_ 384 reserved0 "reserved";
CM_ SG_ 384 reserved1 "reserved";
CM_ SG_ 384 reserved2 "reserved";
CM_ SG_ 384 reserved3 "reserved";
CM_ SG_ 384 reserved4 "reserved";
CM_ SG_ 384 reserved5 "reserved";
CM_ SG_ 384 reserved6 "reserved";
CM_ SG_ 384 reserved7 "reserved";
