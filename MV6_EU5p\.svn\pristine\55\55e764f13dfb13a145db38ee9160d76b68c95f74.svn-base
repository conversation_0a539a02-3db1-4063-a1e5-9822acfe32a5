/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef __CCP_H_
#define __CCP_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

#define CCP_MOTOROLA
#define CCP_BYTE    uint8_t          //unsigned char
#define CCP_WORD    uint16_t         //unsigned short
#define CCP_DWORD   uint32_t         //unsigned long
#define CCP_BYTEPTR uint8_t*         //unsigned char*
#define CCP_DAQBYTEPTR CCP_BYTEPTR
#define CCP_MTABYTEPTR CCP_BYTEPTR


/*----------------------------------------------------------------------------*/
/* Disable/Enable Interrupts */

/* Has to be defined if ccpSendCallBack may interrupt ccpDaq */
//#define CCP_DISABLE_INTERRUPT
//#define CCP_ENABLE_INTERRUPT

/*----------------------------------------------------------------------------*/

#define CCP_STATION_ID "CCPtest"
#define CCP_STATION_ADDR 0x3900

//#define ID_CCP_DTO 0x200  PAY ATTENTION!! This symbols are defined into... 
//#define ID_CCP_CRO 0x201  ...CAN.cfg module

#define CCP_DAQ                         
#define CCP_MAX_ODT 25                  
#define CCP_MAX_DAQ 6                   

#define CCP_ODT_ENTRY_SIZE      

//#define CCP_SEND_QUEUE_OVERRUN_INDICATION

#define CCP_SEND_QUEUE
//#define CCP_SEND_QUEUE_SIZE 6  /* = CCP_MAX_ODT*CCP_MAX_DAQ  */

//#define CCP_SEND_SINGLE
#define CCP_SET_SESSION_STATUS

#define CCP_CHECKSUM
#define CCP_CHECKSUM_TYPE CCP_WORD

//#define CCP_BOOTLOADER_DOWNLOAD

#define CCP_CALPAGE
#define CCP_PROGRAM

/* end ccppar.h */

#define CCP_START_OF_RANGE   CCP_TXCH 
#define CCP_END_OF_RANGE     CCP_TXCHEND

/* Event Channel declaration */
#define EVENT_CHANNEL_1  1
#define EVENT_CHANNEL_2  2
#define EVENT_CHANNEL_3  3
#define EVENT_CHANNEL_4  4
#define EVENT_CHANNEL_5  5
#define EVENT_CHANNEL_6  6
                                                  
/* Declare CCP-protocol version */
#define CCP_VERSION_MAJOR   0x02
#define CCP_VERSION_MINOR   0x01


/* Declare module implementation version number */
#define CCP_DRIVER_VERSION 142
#define CCP_DRIVER_BUGFIX_VERSION 0


/* Basic */
#define CC_CONNECT           0x01
#define CC_SET_MTA           0x02
#define CC_DNLOAD            0x03
#define CC_UPLOAD            0x04
#define CC_TEST              0x05 /* V2.1 */
#define CC_START_STOP        0x06
#define CC_DISCONNECT        0x07
#define CC_START_STOP_ALL    0x08 /* V2.1 */
#define CC_SHORT_UPLOAD      0x0F
#define CC_GET_DAQ_SIZE      0x14
#define CC_SET_DAQ_PTR       0x15
#define CC_WRITE_DAQ         0x16
#define CC_EXCHANGE_ID       0x17
#define CC_GET_CCP_VERSION   0x1B /* V2.1 */
#define CC_DNLOAD6           0x23

/* Optional */
#define CC_GET_CAL_PAGE      0x09
#define CC_SET_S_STATUS      0x0C
#define CC_GET_S_STATUS      0x0D
#define CC_BUILD_CHKSUM      0x0E
#define CC_CLEAR_MEMORY      0x10
#define CC_SET_CAL_PAGE      0x11
#define CC_GET_SEED          0x12
#define CC_UNLOCK            0x13
#define CC_PROGRAM           0x18
#define CC_MOVE_MEMORY       0x19
#define CC_DIAG_SERVICE      0x20
#define CC_ACTION_SERVICE    0x21
#define CC_PROGRAM6          0x22 

/* Vector extensions */
#define CC_PROGRAM_PREPARE   0x1E   /* Prepare for flash kernel download */
#define CC_PROGRAM_START     0x1F   /* Start flash kernel at MTA[0] */

/* Returncodes */
#define CRC_OK                 0x00

/* C1 */
#define CRC_CMD_BUSY           0x10
#define CRC_DAQ_BUSY           0x11
#define CRC_KEY_REQUEST        0x18
#define CRC_STATUS_REQUEST     0x19

/* C2 */
#define CRC_COLD_START_REQUEST 0x20
#define CRC_CAL_INIT_REQUEST   0x21
#define CRC_DAQ_INIT_REQUEST   0x22
#define CRC_CODE_REQUEST       0x23

/* C3 (Errors) */
#define CRC_CMD_UNKNOWN        0x30
#define CRC_CMD_SYNTAX         0x31
#define CRC_OUT_OF_RANGE       0x32
#define CRC_ACCESS_DENIED      0x33
#define CRC_OVERLOAD           0x34
#define CRC_ACCESS_LOCKED      0x35


/* Session Status */
#define SS_CAL                0x01
#define SS_DAQ                0x02
#define SS_RESUME             0x04
#define SS_TMP_DISCONNECTED   0x10
#define SS_CONNECTED          0x20
#define SS_STORE              0x40
#define SS_RUN                0x80

/* Priviledge Level */
#define PL_CAL 0x01
#define PL_DAQ 0x02
#define PL_PGM 0x40

/* Broadcast Station Address */
#define CCP_BROADCAST_STATION_ADDR 0

/*--------------------------------------------------------------------------*/
/* Datatypes */
/*--------------------------------------------------------------------------*/

/* Vector CAN Driver Intel/Motorola */
#ifdef C_CPUTYPE_BIGENDIAN
  #define CCP_MOTOROLA
#endif
#ifdef C_CPUTYPE_LITTLEENDIAN
  #define CCP_INTEL
#endif

#ifndef CCP_INTEL
  #ifndef CCP_MOTOROLA
    #error Please #define CCP_INTEL or CCP_MOTOROLA as the target system byte order
  #endif
#endif

/*--------------------------------------------------------------------------*/
/* DAQ Lists, Type Definition */
/*--------------------------------------------------------------------------*/

/* Note:
   - Adressextensions are not used in this example
   - Object size > 1 are not used in this example and must be handled in
     the calibration system
*/

/* The maximum number of ODTs is restricted
   PID=0xFE,0xFF is reserved
*/
#ifdef CCP_DAQ
#ifndef CCP_SEND_QUEUE_OVERRUN_INDICATION
  #if ((CCP_MAX_DAQ*CCP_MAX_ODT)>254)
  #error Too many ODTs, maximum is CCP_MAX_DAQ*CCP_MAX_ODT = 254
  #endif
#else
  #if ((CCP_MAX_DAQ*CCP_MAX_ODT)>126)
  #error Too many ODTs, maximum is CCP_MAX_DAQ*CCP_MAX_ODT = 126
  #endif
#endif
#endif

/* CCP_DAQBYTEPTR and CCP_MTABYTEPTR may be defined different to CCP_BYTEPTR to save memory
   Example:
     #define CCP_BYTEPTR     unsigned char *
     #define CCP_MTABYTEPTR  huge unsigned char *
     #define CCP_DAQBYTEPTR  unsigned char *
*/
#ifndef CCP_DAQBYTEPTR
  #ifdef CCP_DAQ_BASE_ADDR         /* CCP_DAQBYTEPTR required, if CCP_DAQ_BASE_ADDR is defined */
    #error Please define CCP_DAQBYTEPTR, which is the type of ODT entries
  #else
    #define CCP_DAQBYTEPTR CCP_BYTEPTR
  #endif
#endif
#ifndef CCP_MTABYTEPTR
  #define CCP_MTABYTEPTR CCP_BYTEPTR
#endif


#ifdef CCP_DAQ

/* ODT entry */
typedef struct ccpOdtEntry 
{
  CCP_DAQBYTEPTR ptr;
} ccpOdtEntry_t;

/* ODT */
typedef ccpOdtEntry_t ccpODT_t[7];

/* DAQ list */
#define DAQ_FLAG_START    0x01
#define DAQ_FLAG_SEND     0x02
#define DAQ_FLAG_PREPARED 0x04
#define DAQ_FLAG_OVERRUN  0x80
typedef struct ccpDaqList {
  ccpODT_t    odt[CCP_MAX_ODT];
#ifdef CCP_ODT_ENTRY_SIZE
  CCP_BYTE    odt_siz[CCP_MAX_ODT][7];
#endif

  CCP_WORD        prescaler;
  CCP_WORD        cycle;
  CCP_BYTE        eventChannel;
  CCP_BYTE        last;
  CCP_BYTE        flags;
} ccpDaqList_t;

#else
  #undef CCP_SEND_QUEUE
  #undef CCP_SEND_SINGLE
#endif

/*--------------------------------------------------------------------------*/
/* Transmit Queue, Type Definition */
/*--------------------------------------------------------------------------*/
#ifdef CCP_SEND_QUEUE

/* Checks */

#ifndef CCP_SEND_QUEUE_SIZE
  #define CCP_SEND_QUEUE_SIZE (CCP_MAX_ODT*CCP_MAX_DAQ)
#endif

#ifdef CCP_SEND_SINGLE
  #error Do not use CCP_SEND_SINGLE together with CCP_SEND_QUEUE
#endif

#if CCP_MAX_ODT > CCP_SEND_QUEUE_SIZE
  #error CCP_SEND_QUEUE_SIZE is too small
#endif

/*--------------------------------------------------------------------------*/
/* CCP Driver Variables, Type Definition */
/*--------------------------------------------------------------------------*/
#define CCP_MAX_MTA 2
#define CCP_INTERNAL_MTA (CCP_MAX_MTA-1)

/* Return values for ccpWriteMTA and ccpCheckWriteEEPROM */
#define CCP_WRITE_DENIED  0
#define CCP_WRITE_OK      1
#define CCP_WRITE_PENDING 2
#define CCP_WRITE_ERROR   3

/* Bitmasks for ccp.SendStatus */
#define CCP_CRM_REQUEST  0x01
#define CCP_DTM_REQUEST  0x02
#define CCP_USR_REQUEST  0x04
#define CCP_CMD_PENDING  0x08
#define CCP_CRM_PENDING  0x10
#define CCP_DTM_PENDING  0x20
#define CCP_USR_PENDING  0x40
#define CCP_TX_PENDING   0x80
#define CCP_SEND_PENDING (CCP_DTM_PENDING|CCP_CRM_PENDING|CCP_USR_PENDING)


/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef struct
{
    CCP_BYTE b[8];
} ccpMsg_t;

typedef struct 
{
    CCP_BYTE     len;
    CCP_BYTE     rp;
    ccpMsg_t msg[CCP_SEND_QUEUE_SIZE];
} ccpQueue_t;

#endif

struct CCP 
{

    CCP_BYTE Crm[8];                           /* CRM Command Return Message buffer */
    CCP_BYTE SessionStatus;
    CCP_BYTE SendStatus;
    CCP_MTABYTEPTR MTA[CCP_MAX_MTA];           /* Memory Transfer Address */
#ifdef CCP_DAQ

#ifdef CCP_SEND_QUEUE
    ccpQueue_t Queue;                     /* 1200 Bytes */
#else
    CCP_BYTE Dtm[8];                       /* DTM Data Transmission Message buffer */
#endif

#ifdef CCP_SEND_SINGLE
    CCP_BYTE CurrentDaq;
    CCP_BYTE CurrentOdt;
#endif
    ccpOdtEntry_t *DaqListPtr;               /* Pointer for SET_DAQ_PTR, make it near to save RAM  */
#ifdef CCP_ODT_ENTRY_SIZE
    CCP_BYTE      *DaqListSizPtr;
#endif
    ccpDaqList_t   DaqList[CCP_MAX_DAQ];     /* DAQ list */ /* 5500 Bytes */
#endif

    CCP_BYTE UserSessionStatus;                /* Used for GET/SET_SESSION_STATUS */

#ifdef CCP_SEED_KEY
    CCP_BYTE ProtectionStatus;               /* Resource Protection Status */
#endif

#ifdef CCP_CHECKSUM
    CCP_CHECKSUM_TYPE CheckSumSize;                 /* Counter for checksum calculation */
#endif
};


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern struct CCP ccp;
extern uint8_t  buffsel;
extern uint8_t  spican_bufSel;
extern volatile bool     txBufFull[3];




/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/

/*--------------------------------------------------------------------------*/
/* Prototypes */
/*--------------------------------------------------------------------------*/

/* Functions from ccp.c */
/*----------------------*/


/*--------------------------------------------------------------------------*
 * ccpInit - Initialize
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
extern  void ccpInit( void );

/*--------------------------------------------------------------------------*
 * ccpDaq - DAQ processor
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern  void ccpDaq( CCP_BYTE eventChannel );

/*--------------------------------------------------------------------------*
 * ccpSendCallBack -  Transmit Notification; returns 0 when the CCP driver is idle 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpSendCallBack( void );

/*--------------------------------------------------------------------------*
 * ccpBackground - Background Loop; return 1 (TRUE) when still pending 
 * 
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * Used only if Checksum Calculation or EEPROM Programming is required 
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpBackground( void );

/* Functions provided externally */
/*-------------------------------*/

/*--------------------------------------------------------------------------*
 * ccpSend - Transmit a message returns 1 (TRUE) when successfull 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern uint8_t  ccpSend( CCP_BYTEPTR msg );

#ifndef ccpUserBackground
/*--------------------------------------------------------------------------*
 * ccpUserBackground - Background calculation 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern void ccpUserBackground( void );
#endif

#if !defined(ccpGetPointer) || defined(__BOOT_H__) /* Not defined as macro */
/*--------------------------------------------------------------------------*
 * ccpGetPointer - Generate a pointer from addr_ext and addr 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_MTABYTEPTR ccpGetPointer( CCP_BYTE addr_ext, 
                                      CCP_DWORD addr );
#endif

#ifdef CCP_DAQ_BASE_ADDR
/*--------------------------------------------------------------------------*
 * ccpGetDaqPointer - Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_DAQBYTEPTR ccpGetDaqPointer( CCP_BYTE addr_ext,
                                         CCP_DWORD addr );
#endif

#ifdef CCP_STANDARD
/*--------------------------------------------------------------------------*
 * ccpGetMTA0 - Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern void ccpGetMTA0( CCP_BYTE *e,
                         CCP_DWORD *a);
#endif

#ifdef CCP_WRITE_PROTECTION
#ifndef ccpCheckWriteAccess /* Not defined as macro */
/*--------------------------------------------------------------------------*
 * ccpCheckWriteAccess - Check addresses for valid write access. Returns 0 (false) 
 *                       if access denied 
 * 
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * Used only, if write protection of memory areas is required 
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpCheckWriteAccess( CCP_MTABYTEPTR a, 
                                      CCP_BYTE size );
#endif
#endif

#ifdef CCP_BOOTLOADER_DOWNLOAD

/*--------------------------------------------------------------------------*
 * ccpDisableNormalOperation -  Function Description 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * Used only of Download of the Flash Kernel is required
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpDisableNormalOperation( CCP_MTABYTEPTR a, 
                                            CCP_WORD size );
#endif


#ifdef CCP_PROGRAM
/* Flash Programming */
/* Used only if Flash Programming is required */
/* If Flash Programming is performed by a special loader, */
/* the following function will be called on CLEAR_MEMORY command */
#ifdef CCP_BOOTLOADER

/*--------------------------------------------------------------------------*
 * ccpBootLoaderStartup -  Flash Kernel Download 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * Used only of Download of the Flash Kernel is required
 *--------------------------------------------------------------------------*/
 extern void ccpBootLoaderStartup( struct CCP *ccp,
                                   CCP_BYTEPTR com );

#else

/*--------------------------------------------------------------------------*
 * ccpFlashClear -  Flash Kernel Download 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * Used only of Download of the Flash Kernel is required
 *--------------------------------------------------------------------------*/
 extern void ccpFlashClear( CCP_MTABYTEPTR a,
                            CCP_DWORD size );

/*--------------------------------------------------------------------------*
 * Function Description  -  Flash Kernel Download 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * CCP_WRITE_OK      - FLASH written
 * CCP_WRITE_PENDING - FLASH write in progress, call ccpSendCrm when done
 *
 * Usage notes:
 * Used only of Download of the Flash Kernel is required
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpFlashProgramm( CCP_BYTEPTR data, 
                                   CCP_MTABYTEPTR a,
                                   CCP_BYTE size );
#endif

#endif


/* RAM/ROM Switching */
#ifdef CCP_CALPAGE
/*--------------------------------------------------------------------------*
 * ccpInitCalPage -  Function Description 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern void ccpInitCalPage( void );

/*--------------------------------------------------------------------------*
 * ccpGetCalPage -  Function Description 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_DWORD ccpGetCalPage( void );

/*--------------------------------------------------------------------------*
 * ccpSetCalPage -  Function Description  
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern void ccpSetCalPage( CCP_DWORD a );
#endif


/* Seed&Key*/
#ifdef CCP_SEED_KEY
/*--------------------------------------------------------------------------*
 * ccpGetSeed -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_DWORD ccpGetSeed( CCP_BYTE resourceMask );

/*--------------------------------------------------------------------------*
 * ccpUnlock -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpUnlock( CCP_BYTE *key );
#endif


/* EEPROM Programing */
#ifdef CCP_WRITE_EEPROM
/*--------------------------------------------------------------------------*
 * ccpCheckWriteEEPROM -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 *  CCP_WRITE_OK     - EEPROM written
 *  CCP_WRITE_DENIED - This is not EEPROM
 *  CCP_WRITE_PENDING - EEPROM write in progress, call ccpSendCrm when done
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpCheckWriteEEPROM( CCP_MTABYTEPTR addr, 
                                      CCP_BYTE size, 
                                      CCP_BYTEPTR data );
#endif

#ifdef CCP_READ_EEPROM
/*--------------------------------------------------------------------------*
 * ccpCheckReadEEPROM -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 *  0 (FALSE)    - This is not EEPROM
 *  1 (TRUE)     - EEPROM read
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_BYTE ccpCheckReadEEPROM( CCP_MTABYTEPTR addr,
                                     CCP_BYTE size, 
                                     CCP_BYTEPTR data );
#endif


/*--------------------------------------------------------------------------*/
/* Special Features */
/* Please contact Vector for details */
/*--------------------------------------------------------------------------*/

/* ECU Timestamping */
#ifdef CCP_TIMESTAMPING
#ifndef ccpGetTimestamp /* Not defined as macro */
/*--------------------------------------------------------------------------*
 * ccpGetTimestamp -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None 
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern CCP_WORD ccpGetTimestamp( void );
#endif
#ifndef ccpClearTimestamp /* Not defined as macro */
/*--------------------------------------------------------------------------*
 * ccpClearTimestamp -  Function Description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None 
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 extern void ccpClearTimestamp( void );
#endif
#endif

/*--------------------------------------------------------------------------*
 * ccpStopAllDaq - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ccpStopAllDaq( void );

/*--------------------------------------------------------------------------*
 * GetBuffer - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t GetBuffer(void);

/*--------------------------------------------------------------------------*
 * CCP_PeriodicMonitorTask100ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_PeriodicMonitorTask100ms(void);

/*--------------------------------------------------------------------------*
 * CCP_PeriodicMonitorTask10ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_PeriodicMonitorTask10ms(void); 

/*--------------------------------------------------------------------------*
 * CCP_PeriodicMonitorTask5ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_PeriodicMonitorTask5ms(void); 

/*--------------------------------------------------------------------------*
 * CCP_MonitorEventTDC - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_MonitorEventTDC(void); 

/*--------------------------------------------------------------------------*
 * CCP_MonitorEventHTDC - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_MonitorEventHTDC(void); 

/*--------------------------------------------------------------------------*
 * CCP_MonitorEventEOA - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_MonitorEventEOA(void); 

/*--------------------------------------------------------------------------*
 * CCP_ActivityMonitoring - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CCP_ActivityMonitoring(void);

/*--------------------------------------------------------------------------*
 * CalculateChecksum - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CalculateChecksum(void);

/*--------------------------------------------------------------------------*
 * ccpGetConnectionStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void  ccpGetConnectionStatus(uint8_t *ccpConnectionStatus);

/*--------------------------------------------------------------------------*
 * FuncTaskCheckSum - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void FuncTaskCheckSum(void);

#endif /* __CCP_H_ */ 
