/*******************************************************************
 *
 *    DESCRIPTION:
 *
 *    AUTHOR:
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _ION_LAMBDA_H_
#define _ION_LAMBDA_H_

/** include files **/

/** default settings **/

/** internal functions **/

/** public data **/
extern uint8_T FlgForceOL;
extern uint16_T LamObjSelfAdj;
extern uint16_T LamEstAvg;
extern uint16_T LamEstAvg_ANN;
extern uint16_T LamEstSlow[];
extern uint8_T  FlgLamRel[];

/** private data **/

/** public functions **/

void IonLambda_Init(void);
void IonLambda_TDC(void);
void IonLambda_NoSync(void);

#endif
