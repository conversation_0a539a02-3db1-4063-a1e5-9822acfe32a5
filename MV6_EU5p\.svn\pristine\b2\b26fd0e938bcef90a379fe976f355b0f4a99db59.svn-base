/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_RECOVERY_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "wdt.h"
#include "sys.h"
#include "mpc5500_spr_macros.h"
#include "recovery.h"
#include "Flash_out.h"
    
#ifdef _TEST_IVOR2_
#include "Utils.h"
#endif
    
    
    
#include "eemgm.h"
#include "ee.h"
#include "vsrammgm.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
int8_t EE_flashErrorOnBlock0 = NO_ERROR; /*OUTPUT-> FOR EE:: !=0 if flashError found */
int8_t EE_flashErrorOnBlock1 = NO_ERROR; /*OUTPUT-> FOR EE:: !=0 if flashError found */

FlashFaultStruct_typ FlashFaultStruct = {0,0,0,0,0,0,0,0};

static BlockDescription InvalidateRegTag;

/* Externals */

extern uint8_t  IvorIndex;
extern uint32_t SRR0_Value;
extern uint32_t SRR1_Value;
extern uint32_t CSRR0_Value;
extern uint32_t CSRR1_Value;
extern uint32_t SPR_ESRValue;
extern uint32_t SPR_DEARValue;
extern uint32_t SPR_MCSRValue;



extern SSD_CONFIG ssdConfig_BK0A0;
extern SSD_CONFIG ssdConfig_BK1A1;
extern SSD_CONFIG ssdConfig_BK1A2;
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define FLASH_BLOCK_RECOVER_OFF 0
#define FLASH_BLOCK_RECOVER_ON 1
#undef TESTME

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
typedef struct flash_block_def_s 
{
    uint32_t recover_enable;
    uint32_t offset;
    uint32_t size;
    void (*callback)(uint32_t ivor_idx);
} flash_block_def_t;

#ifdef _TEST_IVOR2_
typedef enum 
{
    NO_EE_CORRUPTION    = (uint8_T) 0,
    EE_CORR_BY_ADDR     = (uint8_T) 55,
    EE_CORR_PAGE0_HALF0 = (uint8_T) 11,
    EE_CORR_PAGE0_HALF1 = (uint8_T) 12,
    EE_CORR_PAGE1_HALF0 = (uint8_T) 21,
    EE_CORR_PAGE1_HALF1 = (uint8_T) 22
}EE_CorruptZoneTyp;

typedef enum
{
    FLASH_ADDR_FROM =0,
    FLASH_ADDR_TO   =1
}FLASH_CorruptZoneVecPos;
#endif


/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
#ifdef TESTME
/*--------------------------------------------------------------------------*
 * test_hang - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void test_hang(void);

/*--------------------------------------------------------------------------*
 * my_EraseCallback - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int32_t my_EraseCallback(void);

/*--------------------------------------------------------------------------*
 * recovery_unit_test - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint32_t recovery_unit_test();


#endif

#ifdef _TEST_IVOR2_
/*--------------------------------------------------------------------------*
 * EE_CorruptionTest - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void EE_CorruptionTest(uint32_T* ee_from, 
                              uint32_T* ee_to);

/*--------------------------------------------------------------------------*
 * EE_callIvor2ByReading - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_T EE_callIvor2ByReading(void);
#endif

/*--------------------------------------------------------------------------*
 * flash_fault_ee - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void flash_fault_ee(uint32_t ivor_idx);

/*--------------------------------------------------------------------------*
 * flash_fault_calib - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void flash_fault_calib(uint32_t ivor_idx);

/*--------------------------------------------------------------------------*
 * flash_fault_appli - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void flash_fault_appli(uint32_t ivor_idx);

/*--------------------------------------------------------------------------*
 * flash_fault_backup - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void flash_fault_backup(uint32_t ivor_idx);




/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/

#ifdef TESTME
static uint32_t my_callbackCount;
static uint32_t mycount;
static uint32_t stop;
static BlockDescription bd;
#endif



static uint8_T flash_fault_idx = FLASH_FAULT_IDX_DEFAULT;
static const flash_block_def_t flash_array_conf[FLASH_N_LOGICAL_BLOCKS]; 

static volatile uint8_T FlashFault_StartupCheck; /*must be VOLATILE to avoid compiler 
                                                  *optimization and a check will not be
                                                  *done if it isn't declared as VOLATILE
                                                  */
uint8_t Ivor2_ReturnJumpingFaultInstr = 0; /*Shared between Ivor2 handler and 
                                            *checkFaultyAddrFromLastIvor2 function
                                            *to make the Ivor2 return to the address
                                            *of the faulty instruction + 4,
                                            *to not execute it again.
                                            */
#ifdef _TEST_IVOR2_
static uint32_T * FLASH_CorruptionZone[] = { NULL, /*FLASH_ADDR_FROM*/ 
                                             NULL  /*FLASH_ADDR_TO*/
                                            };
static uint8_T testIvor2FlashEraseEnable = 0;
static uint8_T testStartIvor2 = 0;
static uint8_T testStartIvor2_Corruption = 0;
static EE_CorruptZoneTyp testCorruptEE = NO_EE_CORRUPTION;
#endif

// static const flash_block_def_t flash_array_conf[6+2+12] = {
static const flash_block_def_t flash_array_conf[FLASH_N_LOGICAL_BLOCKS] = 
{
#if (USE_LOW_ADDR_SPACE==1u)
    /* Low space block 0 */ /* 0 */
    // 0x00000000 /* Offset of low block 0 */
    // 0x4000     /* 16KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_LOW_BLOCK0, LOW_BLOCK0_SIZE, 0 },

    /* Low space block 1 */ /* 1 */
    // 0x00004000 /* Offset of low block 1, in same partition with block 0 */
    // 0xC000     /* 48KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_LOW_BLOCK1, LOW_BLOCK1_SIZE + LOW_BLOCK2_SIZE, flash_fault_ee },

    /* Low space block 2 */ /* 2 */
    // 0x00010000 /* Offset of low block 2, in same partition with block 0 and 1 */
    // 0xC000     /* 48KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_LOW_BLOCK3, LOW_BLOCK3_SIZE + LOW_BLOCK4_SIZE, flash_fault_ee },

    /* Low space block 3 */ /* 3 */
    // 0x0001C000 /* Offset of low block 3, in same partition with block 0, 1 and 2 */
    // 0x4000     /* 16KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_LOW_BLOCK5, LOW_BLOCK5_SIZE, 0 },

    /* Low space block 4 */ /* 4 */
    // 0x00020000 /* Offset of low block 4, in same partition with block 0, 1 and 2 */
    // 0x10000    /* 64KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_LOW_BLOCK6, LOW_BLOCK6_SIZE, 0 },

    /* Low space block 5 */ /* 5 */
    // 0x00030000 /* Offset of low block 5, in same partition with block 0, 1 and 2 */
    // 0x10000    /* 64KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_LOW_BLOCK7, LOW_BLOCK7_SIZE, flash_fault_calib },
#endif

#if (USE_MID_ADDR_SPACE == 1u)
    /* Mid space block 0 */ /* 6 */
    // 0x00040000 /* Offset of midium block 0 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_MID_BLOCK0, MID_BLOCK0_SIZE, flash_fault_appli },

    /* Mid space block 1 */ /* 7 */
    // 0x00060000 /* Offset of midium block 1 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_MID_BLOCK1, MID_BLOCK1_SIZE, flash_fault_appli },
#endif

#if (USE_HIGH_ADDR_SPACE == 1u)
#if (USE_BANK1_ARRAY1==1u)

    /* High space block 0 */ /* 8 */
    // 0x00080000 /* Offset of high block 0 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_HIGH_BLOCK0, HIGH_BLOCK0_SIZE, flash_fault_appli },

    /* High space block 1 */ /* 9 */
    // 0x000A0000 /* Offset of high block 1 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_ON, OFFSET_HIGH_BLOCK1, HIGH_BLOCK1_SIZE, flash_fault_appli }, 

    /* High space block 2 */ /* 10 */
    // 0x000C0000 /* Offset of high block 2 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_HIGH_BLOCK2, HIGH_BLOCK2_SIZE, flash_fault_appli },
    /* High space block 3 */ /* 11 */
    // 0x000E0000 /* Offset of high block 3 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_HIGH_BLOCK3, HIGH_BLOCK3_SIZE, flash_fault_appli }

#endif
#if (USE_BANK1_ARRAY2==1u)

    ,/* High space block 4 */ /* 12 */
    // 0x00100000 /* Offset of high block 4 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_HIGH_BLOCK4, HIGH_BLOCK4_SIZE, flash_fault_appli },

    /* High space block 5 */ /* 13 */
    // 0x00120000 /* Offset of high block 5 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_HIGH_BLOCK5, HIGH_BLOCK5_SIZE, flash_fault_appli },

    /* High space block 6 */ /* 14 */
    // 0x00140000 /* Offset of high block 6 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_HIGH_BLOCK6, HIGH_BLOCK6_SIZE, flash_fault_appli },

    /* High space block 7 */ /* 15 */
    // 0x00160000 /* Offset of high block 7 */
    // 0x20000    /* 128KB size */
    { FLASH_BLOCK_RECOVER_OFF, OFFSET_HIGH_BLOCK7, HIGH_BLOCK7_SIZE, flash_fault_backup },
#endif
#endif	
};


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
#ifdef _TEST_IVOR2_
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Ivor2_TestTask100ms(void)
{

/******************* EE Corruption management  ********************************/

    if(testCorruptEE != NO_EE_CORRUPTION)
    {
        /* Corrompo EE tramite due program. Bisogna prima settare gli indirizzi
           di EE da corrompere.
        */
        uint32_T* ee_from;
        uint32_T* ee_to;

        switch(testCorruptEE)
        {
            case EE_CORR_BY_ADDR:
            {
                ee_from = FLASH_CorruptionZone[FLASH_ADDR_FROM];
                ee_to   = FLASH_CorruptionZone[FLASH_ADDR_TO];
                
                if(ee_from < ee_to)
                {
                    if( (ee_from >= (uint32_T*)EEPROM_START()) &&
                        (ee_to < (uint32_T*)EEPROM_END()) )
                    {
                        /* Force to be Word Aligned */
                        ee_from = (uint32_T *) (((uint32_T)ee_from) & 0xFFFFFFFC);
                        ee_to = (uint32_T *) (((uint32_T)ee_to) & 0xFFFFFFFC);

                        EE_CorruptionTest(ee_from, ee_to);
                    }
                }
            }
            break;
            case EE_CORR_PAGE0_HALF0:
            {
                ee_from = (uint32_T*) 0x4000;
                ee_to   = (uint32_T*) (0x4000 + 0x80);
                FLASH_CorruptionZone[FLASH_ADDR_FROM] = ee_from;
                FLASH_CorruptionZone[FLASH_ADDR_TO] = ee_to;

                EE_CorruptionTest(ee_from, ee_to);
            }
            break;
            case EE_CORR_PAGE0_HALF1:
            {
                ee_from = (uint32_T*) 0xA000;
                ee_to   = (uint32_T*) (0xA000 + 0x80);
                FLASH_CorruptionZone[FLASH_ADDR_FROM] = ee_from;
                FLASH_CorruptionZone[FLASH_ADDR_TO] = ee_to;

                EE_CorruptionTest(ee_from, ee_to);
            }
            break;
            case EE_CORR_PAGE1_HALF0:
            {
                ee_from = (uint32_T*) 0x10000;
                ee_to   = (uint32_T*) (0x10000 + 0x80);
                FLASH_CorruptionZone[FLASH_ADDR_FROM] = ee_from;
                FLASH_CorruptionZone[FLASH_ADDR_TO] = ee_to;

                EE_CorruptionTest(ee_from, ee_to);
            }
            break;
            case EE_CORR_PAGE1_HALF1:
            {
                ee_from = (uint32_T*) 0x16000;
                ee_to   = (uint32_T*) (0x16000 + 0x80);
                FLASH_CorruptionZone[FLASH_ADDR_FROM] = ee_from;
                FLASH_CorruptionZone[FLASH_ADDR_TO] = ee_to;

                EE_CorruptionTest(ee_from, ee_to);
            }
            break;
            default:
            {
                /*do nothing*/
            }
            break;
        }

        testStartIvor2_Corruption = testCorruptEE;
        testCorruptEE = NO_EE_CORRUPTION;
        

    }

/******************* Ivor2 calls management  **********************************/ 
    if(testStartIvor2 != 0)
    {
        if(testStartIvor2_Corruption == 0xFF)
        {
            IVOR2_Manager();
        }
        else if(testStartIvor2_Corruption != 0)
        {

            EE_callIvor2ByReading();

        }
        else
        {
            /*do Nothing*/
        }
    }


}
#endif

/*--------------------------------------------------------------------------*
 * checkFaultyAddrFromLastIvor2 - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_t checkFaultyAddrFromLastIvor2(void)
{
    uint8_T result = DO_EE_STD_SETUP;
    
#ifdef _BUILD_VSRAMMGM_

    
    uint8_T* lastFaultyAddress = (uint8_T*)FlashFaultStruct.lastFaultyFlashAddress;
    uint8_T* lastFaultyBlock;
    uint32_T lastFaultyBlockSize;
    uint32_t localSum=0;

    lastFaultyBlock = (uint8_T*)flash_array_conf[FlashFaultStruct.lastFaultyFlashLogicalIdx].offset;
    lastFaultyBlockSize = (EEPROM_SIZE()>>1);
    
    if( /* Insert here Flash zone where check is enabled */
        (lastFaultyBlock >= (uint8_T*)EEPROM_START()) &&  /*EEPROM*/
        (lastFaultyBlock < (uint8_T*)EEPROM_END())       /*EEPROM*/
      )
    {
        FlashFault_StartupCheck = 1; /* it will be zeroed at the end of function 
                                      * if an ivor2 fault on an EEPROM address 
                                      * is generated
                                      */

        Ivor2_ReturnJumpingFaultInstr = 4; /*Shared between Ivor2 handler and 
                                            *checkFaultyAddrFromLastIvor2 function
                                            *to make the Ivor2 return to the address
                                            *of the faulty instruction + 4,
                                            *to not execute it again.
                                            */

        result = DO_EE_CORRUPTED_SETUP;

        EE_flashErrorOnBlock0 = NO_ERROR;   /* Supposing no errors */
        EE_flashErrorOnBlock1 = NO_ERROR;   /* Supposing no errors */
        
        if(lastFaultyBlock <= lastFaultyAddress)
        {
            /*FAULTY BLOCK INTEGRITY CHECK*/
            while(lastFaultyBlockSize > 0)
            {   
                localSum += *lastFaultyBlock;
                lastFaultyBlock     += 16;      /*per row (4 word)*/
                lastFaultyBlockSize -= 16;      /*per row (4 word)*/
                
                //lastFaultyAddress   += 4;     /*per word*/
                //lastFaultyBlockSize -= 4;     /*per word*/
                
                if(FlashFault_StartupCheck == 0)
                {
                    /* Ivor2 generated.FlashBlock is declared corrupted
                     * and MAX_Erase has been Reached.
                     */

                    break;
                }
            }
        }

        lastFaultyBlock = (uint8_T*)flash_array_conf[FlashFaultStruct.lastFaultyFlashLogicalIdx].offset;

        if(FlashFault_StartupCheck == 1) /* NOT setted By flash_fault_ee - BLOCK IS OK -- ERASE should has be DONE*/
        {   

        /* Here only one of the two error variable is: 
              EE_flashErrorOnBlock1 = (int8_t) EE_IVOR2_ERROR;
           or
              EE_flashErrorOnBlock0 = (int8_t) EE_IVOR2_ERROR;
        */

            if(lastFaultyBlock == (uint8_T*)EEPROM_BLK0_START()) /*EE PAGE 0*/
            {
                if(FlashFaultStruct.faultCounterEE_page1 < MAX_FLASH_RECOVERY_RESETS) /* If the other block is not faulty */
                {
                    /* Try to recover the data from the other page */
                    EE_flashErrorOnBlock0 = EE_DO_COPY_FROM_OTHER_PAGE;
                    EE_flashErrorOnBlock1 = NO_ERROR;
                }
                else
                {
                    /* Left this page blank and do not use the other one.*/
                    EE_flashErrorOnBlock0 = NO_ERROR;       /*This should be blank but NOT corrupted*/
                    EE_flashErrorOnBlock1 = EE_IVOR2_ERROR; /*Other block is faulty*/
                }
            }

            if(lastFaultyBlock == (uint8_T*)EEPROM_BLK1_START()) /*EE PAGE 1*/
            {
                if(FlashFaultStruct.faultCounterEE_page0 < MAX_FLASH_RECOVERY_RESETS) /* If the other block is not faulty */
                {
                    EE_flashErrorOnBlock0 = NO_ERROR;
                    EE_flashErrorOnBlock1 = EE_DO_COPY_FROM_OTHER_PAGE;
                }
                else
                {
                    /* Left this page blank and do not use the other one.*/
                    EE_flashErrorOnBlock0 = EE_IVOR2_ERROR; /*Other block is faulty*/
                    EE_flashErrorOnBlock1 = NO_ERROR;       /*This should be blank but NOT corrupted*/
                }
            }
        }
        else if(FlashFault_StartupCheck == 0) /* Ivor2 generated while testing - BLOCK IS FAULTY YET*/
        {
            if(lastFaultyBlock == (uint8_T*)EEPROM_BLK0_START())
            {
                if(FlashFaultStruct.faultCounterEE_page1 < MAX_FLASH_RECOVERY_RESETS)
                {
                    EE_flashErrorOnBlock0 = EE_IVOR2_ERROR;
                    EE_flashErrorOnBlock1 = NO_ERROR; /* Page 1 will be the only page to use */
                }
                else
                {
                    EE_flashErrorOnBlock0 = EE_IVOR2_ERROR;
                    EE_flashErrorOnBlock1 = EE_IVOR2_ERROR; /* !!!!!!!!!! DO NOT USE EEPROM !!!!!!!!!!! */
                }
            }

            if(lastFaultyBlock == (uint8_T*)EEPROM_BLK1_START())
            {
                if(FlashFaultStruct.faultCounterEE_page0 < MAX_FLASH_RECOVERY_RESETS)
                {
                    EE_flashErrorOnBlock0 = NO_ERROR; /* Page 1 will be the only page to use */
                    EE_flashErrorOnBlock1 = EE_IVOR2_ERROR;
                }
                else
                {
                    EE_flashErrorOnBlock0 = EE_IVOR2_ERROR; /* !!!!!!!!!! DO NOT USE EEPROM !!!!!!!!!!! */
                    EE_flashErrorOnBlock1 = EE_IVOR2_ERROR;
                }
            }
        }
        else
        {
            /*Misra 14.10*/
        }
    }
    else
    {
        result = DO_EE_STD_SETUP;
    }

    /* Restoring Ivor2 return point to the faulty instruction */
    Ivor2_ReturnJumpingFaultInstr = 0;     /* Shared between Ivor2 handler and 
                                            * checkFaultyAddrFromLastIvor2 function
                                            * to make the Ivor2 return to the address
                                            * of the faulty instruction + 4,
                                            * to not execute it again.
                                            */

    /* Declaring to be exiting from checkFaultyAddrFromLastIvor2 function*/
    FlashFault_StartupCheck = 0;

#endif /*_BUILD_VSRAMMGM_*/

    return result;
}

/*--------------------------------------------------------------------------*
 * FlashFaultChecksumUpdate - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_T FlashFaultChecksumUpdate(void)
{
    uint8_t faultyAddrSum = 0;
    uint8_t* faultyAddrPtr = (uint8_t*) &FlashFaultStruct.lastFaultyFlashAddress;

    faultyAddrSum    = *faultyAddrPtr;
    faultyAddrPtr++;
    faultyAddrSum   += *faultyAddrPtr;
    faultyAddrPtr++;
    faultyAddrSum   += *faultyAddrPtr;
    faultyAddrPtr++;
    faultyAddrSum   += *faultyAddrPtr;
    faultyAddrPtr++;

    
    /*(uint8_T)(FlashFaultStruct.lastFaultyFlashAddress >> 3)+
    (uint8_T)(FlashFaultStruct.lastFaultyFlashAddress >> 2)+
    (uint8_T)(FlashFaultStruct.lastFaultyFlashAddress >> 1)+
    (uint8_T)(FlashFaultStruct.lastFaultyFlashAddress)     +*/

    FlashFaultStruct.StructChecksum =
                                      faultyAddrSum                              +
                                      FlashFaultStruct.lastFaultyFlashLogicalIdx +
                                      FlashFaultStruct.faultCounterMboot         +
                                      FlashFaultStruct.faultCounterEE_page0      +
                                      FlashFaultStruct.faultCounterEE_page1      +
                                      FlashFaultStruct.faultCounterBoot0         +
                                      FlashFaultStruct.faultCounterBoot1         +
                                      FlashFaultStruct.faultCounterCalib         +
                                      FlashFaultStruct.faultCounterAppl0         +
                                      FlashFaultStruct.faultCounterAppl1         +
                                      FlashFaultStruct.faultCounterAppl2         +
                                      FlashFaultStruct.faultCounterBackup;

    return FlashFaultStruct.StructChecksum;
}

/*--------------------------------------------------------------------------*
 * FlashFaultChecksumIsValid - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_T FlashFaultChecksumIsValid( void )
{
    uint8_t sum = 0;
    uint8_t res = NO_ERROR;
    uint8_t* faultyAddrPtr = (uint8_t*) &FlashFaultStruct.lastFaultyFlashAddress;

    sum              = *faultyAddrPtr;
    faultyAddrPtr++;
    sum             += *faultyAddrPtr;
    faultyAddrPtr++;
    sum             += *faultyAddrPtr;
    faultyAddrPtr++;
    sum             += *faultyAddrPtr;
    faultyAddrPtr++;

    
    sum = sum                                           +
          FlashFaultStruct.lastFaultyFlashLogicalIdx    +
          FlashFaultStruct.faultCounterMboot            +
          FlashFaultStruct.faultCounterEE_page0         +
          FlashFaultStruct.faultCounterEE_page1         +
          FlashFaultStruct.faultCounterBoot0            +
          FlashFaultStruct.faultCounterBoot1            +
          FlashFaultStruct.faultCounterCalib            +
          FlashFaultStruct.faultCounterAppl0            +
          FlashFaultStruct.faultCounterAppl1            +
          FlashFaultStruct.faultCounterAppl2            +
          FlashFaultStruct.faultCounterBackup;

    if(sum != FlashFaultStruct.StructChecksum)
    {
        res = 0xFF;
    }

    return res;
}

/*--------------------------------------------------------------------------*
 * FlashFaultResetInfo - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FlashFaultResetInfo(void)
{
    FlashFaultStruct.lastFaultyFlashAddress     = 0;
    FlashFaultStruct.lastFaultyFlashLogicalIdx  = 0;
    FlashFaultStruct.faultCounterMboot          = 0;
    FlashFaultStruct.faultCounterEE_page0       = 0;
    FlashFaultStruct.faultCounterEE_page1       = 0;
    FlashFaultStruct.faultCounterBoot0          = 0;
    FlashFaultStruct.faultCounterBoot1          = 0;
    FlashFaultStruct.faultCounterCalib          = 0;
    FlashFaultStruct.faultCounterAppl0          = 0;
    FlashFaultStruct.faultCounterAppl1          = 0;
    FlashFaultStruct.faultCounterAppl2          = 0;
    FlashFaultStruct.faultCounterBackup         = 0;
    FlashFaultStruct.StructChecksum             = 0;
}

/*--------------------------------------------------------------------------*
 * FLASH_ConfigEEPROM_Corrupted - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t FLASH_ConfigEEPROM_Corrupted (void) 
{
    
    
    register int16_t returnCode = NO_ERROR ;     /* return code of low level primitives */
    
    if(FLASH_ConfigStatus == FLASH_STATUS_NOT_CONFIGURED) 
    {       
        returnCode |= FlashInit( &ssdConfig_BK0A0 );

/************* ELuzzu: Flash may have an error on the first block but if it's
 *************         on the EEPROM zone it's managed by excluding all read  
 *************         and write operations on that Flash zone                */
        if( (EE_flashErrorOnBlock0 != NO_ERROR) || (EE_flashErrorOnBlock1 != NO_ERROR) )
        {
            returnCode = NO_ERROR;
        }
/*************/

        returnCode |= FlashInit( &ssdConfig_BK1A1 );
#if (TARGET_TYPE == MPC5634)
        returnCode |= FlashInit( &ssdConfig_BK1A2 );
#endif
        
        if ( NO_ERROR != returnCode )
        {
            returnCode = PERIPHERAL_FAILURE;
        }
        else
        {
            /*==================== Lock to Protect Shadow Row ====================*/
            

            returnCode |= SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_PRIMARY, 1, FLASH_LMLR_PASSWORD );
           
            returnCode |= SetLock( &ssdConfig_BK1A1, LOCK_SHADOW_PRIMARY, 1, FLASH_LMLR_PASSWORD );
#if (TARGET_TYPE == MPC5634)
            returnCode |= SetLock( &ssdConfig_BK1A2, LOCK_SHADOW_PRIMARY, 1, FLASH_LMLR_PASSWORD );
#endif            
            
        }
        
        if ( NO_ERROR != returnCode )
        {
#ifdef CHECK_BIOS_FAULTS
            BIOS_Faults |= BIOS_FAILURE_FLASH;
#endif /* CHECK_BIOS_FAULTS */
            returnCode = PERIPHERAL_FAILURE;
        }
        else
        {
            returnCode = SetLock( &ssdConfig_BK0A0, LOCK_SHADOW_SECONDARY, 1, FLASH_SLMLR_PASSWORD );
            
            returnCode = SetLock( &ssdConfig_BK1A1, LOCK_SHADOW_SECONDARY, 1, FLASH_SLMLR_PASSWORD );
#if (TARGET_TYPE == MPC5634)
            returnCode = SetLock( &ssdConfig_BK1A2, LOCK_SHADOW_SECONDARY, 1, FLASH_SLMLR_PASSWORD );
#endif
        }

        if ( NO_ERROR != returnCode )
        {
#ifdef CHECK_BIOS_FAULTS
            BIOS_Faults |= BIOS_FAILURE_FLASH;
#endif /* CHECK_BIOS_FAULTS */
            returnCode = PERIPHERAL_FAILURE;
        }
        else
        {
            FLASH_ConfigStatus = FLASH_STATUS_CONFIGURED;
        }
    } 
    else
    {
        returnCode = PERIPHERAL_ALREADY_CONFIGURED; 
    }
    
    return returnCode;
}

/*--------------------------------------------------------------------------*
 * IVOR2_faultmgm - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void IVOR2_faultmgm(void)
{
    /* PSSD_CONFIG pSSDConfig = &ssdConfig; :: ssdConfig hasn't been configured

       nblocks  = pSSDConfig->lowBlockNum;
       nblocks += pSSDConfig->midBlockNum;
       nblocks += pSSDConfig->highBlockNum;
    */
    
    uint32_t addr;
    uint32_t i;
    uint32_t nblocks;

    nblocks = FLASH_N_LOGICAL_BLOCKS;

    DisableAllInterrupts();
    SYS_DisableWatchdog();

    // test = SPR_ESRValue & SPR_ESR_ECC_DATA_ERROR_MASK;
    // if (test)
    /* chech ESR: retrieve ECC errors on flash memory */
    if (ECSM.ESR.B.FNCE == SPR_ESR_ECC_DATA_ERROR_MASK)
    {
        /* flash block lookup */
        for (i=0; i<nblocks; i++)
        {
            addr = flash_array_conf[i].offset + flash_array_conf[i].size;
            if (SPR_DEARValue < addr)
            {
                /* block found */
                flash_fault_idx = i;
                break;
            }
        }

        FlashFaultStruct.lastFaultyFlashLogicalIdx = flash_fault_idx;
        
        if (flash_fault_idx != FLASH_FAULT_IDX_DEFAULT)
        {
            /* recover flash: erase corrupted flash block */
            if (flash_array_conf[flash_fault_idx].recover_enable)
            {
                /* call corresponding callback */
                flash_array_conf[flash_fault_idx].callback(2);
            }
        }
    }

    if((FlashFaultStruct.lastFaultyFlashLogicalIdx != FLASH_EE_PAGE0_SECTION) &&
       (FlashFaultStruct.lastFaultyFlashLogicalIdx != FLASH_EE_PAGE1_SECTION)
      )
    {
        IVOR_Common_ISR();/* Not called when a callback is activated if the 
                           * callback internally calls ShutdownOS(E_NO_ERROR)
                           */
    }
} 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * test_hang - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
#ifdef TESTME
static void test_hang(void)
{
    while (1);
}
#endif

#ifdef _TEST_IVOR2_
/*--------------------------------------------------------------------------*
 * EE_callIvor2ByReading - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_T EE_callIvor2ByReading(void)
{
    uint32_T res;

    uint32_T* readedAddr = FLASH_CorruptionZone[FLASH_ADDR_FROM];

    while(readedAddr < FLASH_CorruptionZone[FLASH_ADDR_TO])
    {
        res = *readedAddr;
        readedAddr = readedAddr + sizeof(uint32_T);
    }

    return res;
}

/*--------------------------------------------------------------------------*
 * EE_callIvor2ByReading - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void EE_CorruptionTest(uint32_T* ee_from, uint32_T* ee_to)
{
    uint32_T* startPoint = ee_from;
    t_EEBlockDescriptionVar dummyData;

    dummyData.EEsynchWord = 0xA1B2C3D4;
    dummyData.EEid = 9151612;
    dummyData.EEsize = 261968974;
    dummyData.padding[0] = 6519519;
    dummyData.EEcheckVersion[0] = 56;
    dummyData.EEheaderChecksum =319781610;
    dummyData.EElast = 913284;
    dummyData.EEdataChecksum = 12687318;
    
    ee_from = startPoint;
    
    /* DATA_CORRUPTION */
    while(ee_from < ee_to)
    {
        uint32_T programStart = (uint32_T)ee_from;

        dummyData.EEsynchWord       = 0xFFFFFFFF ^ dummyData.EEsynchWord;
        dummyData.EEid              = 0xFFFFFFFF ^ dummyData.EEid;
        dummyData.EEsize            = 0xFFFFFFFF ^ dummyData.EEsize;
        dummyData.padding[0]        = 0xFFFFFFFF ^ dummyData.padding[0];
        dummyData.EEcheckVersion[0] = 0xFFFFFFFF ^ dummyData.EEcheckVersion[0];
        dummyData.EEheaderChecksum  = 0xFFFFFFFF ^ dummyData.EEheaderChecksum;
        dummyData.EElast            = 0xFFFFFFFF ^ dummyData.EElast;
        dummyData.EEdataChecksum    = 0xFFFFFFFF ^ dummyData.EEdataChecksum;

        DisableAllInterrupts();
        FLASH_Program((uint32_t) programStart, sizeof(dummyData) ,(uint32_t) (&dummyData));
        EnableAllInterrupts();
        
        ee_from += 8;

        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop();
    }

    while(ee_from < ee_to)
    {
        uint32_T programStart = (uint32_T)ee_from;

        dummyData.EEsynchWord       = 0xFFFFFFFF ^ dummyData.EEsynchWord + 0x12312377;
        dummyData.EEid              = 0xFFFFFFFF ^ dummyData.EEid + 0x12312377;
        dummyData.EEsize            = 0xFFFFFFFF ^ dummyData.EEsize + 0x12312377;
        dummyData.padding[0]        = 0xFFFFFFFF ^ dummyData.padding[0] + 0x12312377;
        dummyData.EEcheckVersion[0] = 0xFFFFFFFF ^ dummyData.EEcheckVersion[0] + 0x12312377;
        dummyData.EEheaderChecksum  = 0xFFFFFFFF ^ dummyData.EEheaderChecksum + 0x12312377;
        dummyData.EElast            = 0xFFFFFFFF ^ dummyData.EElast + 0x12312377;
        dummyData.EEdataChecksum    = 0xFFFFFFFF ^ dummyData.EEdataChecksum + 0x12312377;
        
        DisableAllInterrupts();
        FLASH_Program((uint32_t) programStart, sizeof(dummyData) ,(uint32_t) (&dummyData));
        EnableAllInterrupts();
        
        ee_from += 8;

        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop(); UTILS_nop(); UTILS_nop();
        UTILS_nop();
    }
    
}
#endif

/*--------------------------------------------------------------------------*
 * flash_fault_ee - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void flash_fault_ee(uint32_t ivor_idx)
{
    uint32_T eraseAddress;
    uint8_T skipErase = 0;
    uint8_T resetForIvorCnt = MAX_FLASH_RECOVERY_RESETS;
    uint8_T eeFaultyPage = 0xFF;

    uint8_T IvorDuringCheckAfterReset = 0;
    
    if (ivor_idx == 2)
    {
        if(FlashFault_StartupCheck == 1) /*check if this IVOR is generated by the check after Reset.*/
        {
            IvorDuringCheckAfterReset = 1;
            FlashFault_StartupCheck = 0;
        }
    
        if(flash_fault_idx == FLASH_EE_PAGE0_SECTION)
        {
            /*USE EE_PAGE_0 Variable*/
            eraseAddress = (uint32_T) EEPROM_BLK0_START();
            eeFaultyPage = EE_PAGE_0;
            resetForIvorCnt = FlashFaultStruct.faultCounterEE_page0;
            FlashFaultStruct.lastFaultyFlashAddress = SPR_DEARValue; /*Allow check after reset.*/
            
            if(resetForIvorCnt != 0xFF)/* avoid resetting to 0 by adding 1 */
            {   
                FlashFaultStruct.faultCounterEE_page0 ++;
            }
        }
        else if(flash_fault_idx == FLASH_EE_PAGE1_SECTION)
        {
            /*USE EE_PAGE_1 Variable*/
            eraseAddress = (uint32_T) EEPROM_BLK1_START();
            eeFaultyPage = EE_PAGE_1;
            resetForIvorCnt = FlashFaultStruct.faultCounterEE_page1;
            FlashFaultStruct.lastFaultyFlashAddress = SPR_DEARValue; /*Allow check after reset.*/

            if(resetForIvorCnt != 0xFF) /* avoid resetting to 0 by adding 1 */
            {
                FlashFaultStruct.faultCounterEE_page1 ++;
            }
        }
        else
        {
            skipErase = 1;
        }

        
        
        if(resetForIvorCnt < MAX_FLASH_RECOVERY_RESETS)
        {
            if(skipErase == 0)
            {
#ifdef _TEST_IVOR2_
                if(testIvor2FlashEraseEnable != 0)
                {
                    FLASH_Erase(eraseAddress, EEPROM_SIZE()>>1, (void(*)(void))NULL_CALLBACK);
                }
#else
                FLASH_Erase(eraseAddress, EEPROM_SIZE()>>1, (void(*)(void))NULL_CALLBACK);
#endif
            }
            
            VSRAMMGM_StoreIvor2Data();
            VSRAMMGM_Update(); /*To update VsRam checksum*/
            ShutdownOS(E_NO_ERROR);  /*<- RESET! */
        }
        else if(eeFaultyPage == EE_PAGE_0)
        {
            /* CONFIGURE THE EEPROM TO CONTINUE WITH SINGLE PAGE_0 */
            if(IvorDuringCheckAfterReset == 1)
            {
                /* Controlled conditions. During STARTUP PROCEDURE */
                /* Generated during checkFaultyAddrFromLastIvor2   */
                EE_flashErrorOnBlock0 = EE_IVOR2_ERROR;
                /*
                    When this code is executed Ivor2 will return to
                    checkFaultyAddrFromLastIvor2() function
                */
            }
            else
            {
                extern uint8_T EE_errorOnBlock0;
                /* LETTURA NONOSTANTE DISABILITAZIONE PAGINA!! */
                EE_flashErrorOnBlock0 = EE_IVOR2_RUNTIME_READ;

                EE_errorOnBlock0 = 1; /*disable future use of block 0*/
                
                /*
                    DON't know where is Ivor return point: ??resetting here anyway????????????
                */
                VSRAMMGM_StoreIvor2Data();
                VSRAMMGM_Update(); /*To update VsRam checksum*/
                ShutdownOS(E_NO_ERROR);  /*<- RESET! */
            }
        }
        else if(eeFaultyPage == EE_PAGE_1)
        {
            /* CONFIGURE THE EEPROM TO CONTINUE WITH SINGLE PAGE_1 */
            if(IvorDuringCheckAfterReset == 1)
            {
                /* Controlled conditions. During STARTUP PROCEDURE */
                /* Generated during checkFaultyAddrFromLastIvor2   */
                EE_flashErrorOnBlock1 = (int8_t) EE_IVOR2_ERROR;
                /*
                    we will return (UTILS_rfi()) to
                    checkFaultyAddrFromLastIvor2() function
                */
            }
            else
            {
                extern uint8_T EE_errorOnBlock1;
                /* LETTURA MENTRE IL SISTEMA E' IN RUNNING!! */
                EE_flashErrorOnBlock1 = (int8_t) EE_IVOR2_RUNTIME_READ;

                EE_errorOnBlock1 = 1; /*disable future use of block 0*/

                /*
                    DON't know where is Ivor return point:  ??resetting here anyway????????????
                */
                VSRAMMGM_StoreIvor2Data();
                VSRAMMGM_Update(); /*To update VsRam checksum*/
                ShutdownOS(E_NO_ERROR);  /*<- RESET! */
            }
        }
        else
        {
            /*do nothing*/
        }
        
        VSRAMMGM_StoreIvor2Data();
        VSRAMMGM_Update();     /*To update VsRam checksum*/
    }
}

/*--------------------------------------------------------------------------*
 * flash_fault_calib - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void flash_fault_calib(uint32_t ivor_idx)
{
    uint32_T addr_l;
    uint32_T size_l;
    uint32_T source_l;

#ifdef TESTME
    test_hang();
#endif

    if (ivor_idx == 2)
    {
        BlockDescription bd_tag;

        addr_l = ((uint32_T)(&__CALIB_ROM_START)+(uint32_T)(&__CALIB_ROM_SIZE))-sizeof(BlockDescription);
        InvalidateRegTag = *((BlockDescription*) addr_l);
        InvalidateRegTag.validMemoryRegion = 0u;
        source_l = (uint32_T)&InvalidateRegTag;
        size_l = sizeof(BlockDescription);

        //just invalidate application region. erase and program performed in boot mode
        FLASH_Program(addr_l, size_l, source_l);
        FLASH_ProgramVerify(addr_l, size_l, source_l);

        ShutdownOS(E_NO_ERROR);
    }
}

/*--------------------------------------------------------------------------*
 * flash_fault_appli - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void flash_fault_appli(uint32_t ivor_idx)
{
    uint32_T addr_l;
    uint32_T size_l;
    uint32_T source_l;

#ifdef TESTME
    test_hang();
#endif

    if (ivor_idx == 2)
    {
        addr_l = ((uint32_T)(&__APP_START)+(uint32_T)(&__APP_SIZE))-sizeof(BlockDescription);
        InvalidateRegTag = *((BlockDescription*) addr_l);
        InvalidateRegTag.validMemoryRegion = 0u;
        source_l = (uint32_T)&InvalidateRegTag;
        size_l = sizeof(BlockDescription);

        //just invalidate application region. erase and program performed in boot mode
        FLASH_Program(addr_l, size_l, source_l);
        FLASH_ProgramVerify(addr_l, size_l, source_l);
    }
}

/*--------------------------------------------------------------------------*
 * flash_fault_backup - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void flash_fault_backup(uint32_t ivor_idx)
{
    uint32_t addr;
    uint32_t size;

#ifdef TESTME
    test_hang();
#endif

    if (ivor_idx == 2)
    {
        addr = flash_array_conf[15].offset;
        size = flash_array_conf[15].size;
        FLASH_Erase(addr, size, (void(*)(void))NULL_CALLBACK);

        ShutdownOS(E_NO_ERROR);
    }
}

#ifdef TESTME


/*--------------------------------------------------------------------------*
 * my_EraseCallback - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int32_t my_EraseCallback(void)
{
    uint8_t suspendState0;
    uint8_t suspendFlag0;
    uint8_t suspendState1;
    uint8_t suspendFlag1;
    uint8_t suspendState2;
    uint8_t suspendFlag2;
    uint32_t boot_addr;
    uint32_t calib_addr;
    uint32_t app_addr1;
    uint32_t app_addr2;
    uint32_t app_addr3;
    uint32_t back_addr;
    uint32_t ee1_addr;
    uint32_t ee2_addr;
    uint32_t addr;
    uint32_t dummy;

    ee1_addr = (uint32_t) (& __EEPROM_START);
    boot_addr = (uint32_t) (& __BOOT_START);
    calib_addr = (uint32_t) (& __CALIB_ROM_START);
    app_addr1 = (uint32_t) (& __APP_START);
    app_addr2 = app_addr1 + 0x20000;
    app_addr3 = app_addr2 + 0x20000;
    back_addr = (uint32_t) (& __BACKUP_START);

    my_callbackCount++;

    if (my_callbackCount & 0xfffc)
    {
        my_callbackCount = 0;
        mycount++;

        if (mycount>1) 
        {
            dummy = *((uint8_t *)(back_addr+0));

            // while(1);
        }

       
        FLASH_Suspend(&suspendState0, &suspendFlag0,&suspendState1, &suspendFlag1,&suspendState2, &suspendFlag2);

        if ((suspendState0 == 0) && (suspendState1 == 0) && (suspendState2 == 0))
        {
            FLASH_Resume(&suspendState0,&suspendState1,&suspendState2);
            return dummy;
        }


        WDT_PeriodicServiceRoutine();
        EnableAllInterrupts();

        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");
        asm("nop");

        DisableAllInterrupts();
        FLASH_Resume(&suspendState0,&suspendState1,&suspendState2);
    }

    return dummy;
}

/*--------------------------------------------------------------------------*
 * recovery_unit_test - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint32_t recovery_unit_test()
{
    uint32_t boot_addr;
    uint32_t calib_addr;
    uint32_t app_addr1;
    uint32_t app_addr2;
    uint32_t app_addr3;
    uint32_t back_addr;
    uint32_t ee1_addr;
    uint32_t ee2_addr;
    uint32_t source;
    uint32_t addr;
    uint32_t i;
    uint8_t dummy = 0;
    uint32_t size;

    ee1_addr = (uint32_t) (& __EEPROM_START);
    boot_addr = (uint32_t) (& __BOOT_START);
    calib_addr = (uint32_t) (& __CALIB_ROM_START);
    app_addr1 = (uint32_t) (& __APP_START);
    app_addr2 = app_addr1 + 0x20000;
    app_addr3 = app_addr2 + 0x20000;
    back_addr = (uint32_t) (& __BACKUP_START);

    my_callbackCount = 0;
    mycount = 0;

    source = back_addr;
    size = 0x10000;
    // scan memory
    for (i=1; i<size; i++)
    {
        dummy += *((uint8_t *)(source+i));
    }

    DisableAllInterrupts();

#if 1
    for (i=1; i<10; i++)
    {
        bd.blockType = i;
        addr = flash_array_conf[i].offset;
        if (flash_array_conf[i].recover_enable)
            FLASH_Program(addr, sizeof(BlockDescription), (uint32_t)(&bd));
    }
#endif
    FLASH_Erase(source, size, (void(*)(void))my_EraseCallback);

    EnableAllInterrupts();

    return dummy;
}
#endif

#endif /*  _BUILD_RECOVERY_ */
