/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV7/tree/APPLICATION/ENGFLAG/engflag.c   $   */
/* $ Description:                                                                                                */
/* $Revision:: 13888  $                                                                                          */
/* $Date:: 2022-03-01 09:54:25 +0100 (mar, 01 mar 2022)   $                                                      */
/* $Author:: LanaL                   $                                                                           */
/*****************************************************************************************************************/
#ifndef CTRL_ACTIVE_OUT_H
#define CTRL_ACTIVE_OUT_H

#include "rtwtypes.h"

extern uint16_T FlgCtrlActive;
extern uint16_T FlgCtrlActiveTot;

void CtrlActive_Init(void);
void CtrlActive_T10ms(void);

#endif

