#include "typedefs.h"
#include "diagmgm_def.h"

#ifdef _BUILD_DIAGMGM_

const uint8_T StDiag[DIAG_NUMBER] = 
{ 
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 0  to 7 */
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 8  to 15*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 16 to 23*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 24 to 31*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 32 to 39*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 40 to 47*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 48 to 55*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 56 to 63*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 64 to 71*/
  NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, NO_FAULT, /* 72 to 79*/
  NO_FAULT, NO_FAULT, NO_FAULT  /* 80 to 91*/
};                /* eeprom */
const uint8_T DiagCnt[DIAG_NUMBER] = 
{ 
  0, 0, 0, 0, 0, 0, 0, 0, /* 0  to 7*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 8  to 15*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 16 to 23*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 24 to 31*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 32 to 39*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 40 to 47*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 48 to 55*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 56 to 63*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 64 to 71*/
  0, 0, 0, 0, 0, 0, 0, 0, /* 72 to 79*/
  0, 0, 0  /* 80 to 91*/
};           /* eeprom*/


const DiagDataFaultStruct DiagDataFault[DIAG_FAULT_LENGTH] =       /* eeprom */
{
    INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT,  /* 0 to 3 */
    INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT,  /* 4 to 7 */
    INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT, INIT_DIAGDATAFAULT   /* 8 to 11 */
};

const uint8_T StoredDiag[DIAG_FAULT_LENGTH] =            /* eeprom */
{
    255, 255, 255, 255,  /* 0 to 3 */
    255, 255, 255, 255,  /* 4 to 7 */
    255, 255, 255, 255   /* 8 to 11 */
};

const uint8_T StoredFault[DIAG_FAULT_LENGTH] =            /* eeprom */
{
    0, 0, 0, 0,  /* 0 to 3 */
    0, 0, 0, 0,  /* 4 to 7 */
    0, 0, 0, 0   /* 8 to 11 */
};

const uint8_T EventCounter[DIAG_FAULT_LENGTH] =            /* eeprom */
{
    0, 0, 0, 0,  /* 0 to 3 */
    0, 0, 0, 0,  /* 4 to 7 */
    0, 0, 0, 0   /* 8 to 11 */
};

/* Driving Cycle management */
const int8_T DRVCNoFaultCnt[DIAG_FAULT_LENGTH] =           /* eeprom */
{
    0, 0, 0, 0, /* 0 to 3 */
    0, 0, 0, 0, /* 4 to 7 */
    0, 0, 0, 0  /* 8 to 11 */
};

/* First DTC that enlights the MIL be Switched ON */
const StoredDtcPosStruct firstDtcCausedMILSwitchON = {0xFF, NULL_DTC, -1};             /* eeprom */

/* BitField: valid since KeyOn */
const uint16_T LastDRVCFault;                              /* eeprom */

/* OBD Monitor */
const uint8_T OBDMonitorSinceLastClear;                              /* eeprom */

#endif // _BUILD_DIAGMGM_
