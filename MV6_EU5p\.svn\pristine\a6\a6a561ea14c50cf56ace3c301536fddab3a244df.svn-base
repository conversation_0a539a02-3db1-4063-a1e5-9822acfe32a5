/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_PRESTARGET_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//QAirTargetMod breakpoint for TBPRESOBJ [mgcc]
CALQUAL uint16_T BKQAIRTMOD[17] = 
{
 489u, 1052u, 1614u, 2177u, 2739u, 3301u, 3864u, 4426u, 4988u, 5551u, 6113u, 6676u, 7238u, 7800u, 8363u, 8925u, 9488u
};
//Rpm breakpoint for TBPRESOBJ [Rpm]
CALQUAL uint16_T BKRPMPRESOBJ[29] = 
{
    900u,   1200u,   1450u,   2200u,   2600u,   2900u,   3300u,   3800u,   4100u,   4600u,   4850u,   5250u,   5650u,   6250u,   6750u,   7200u,   7500u,   8000u,   8300u,   8750u,   9400u,  10000u,  10400u,  10750u,  11500u,  12250u,  12850u,  13300u,  13800u
};
//PresObj table [mbar]
CALQUAL uint16_T TBPRESOBJ[29*17] = 
{
    270u,    386u,    458u,    509u,    571u,    641u,    849u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    386u,    452u,    506u,    544u,    607u,    719u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    384u,    455u,    499u,    539u,    589u,    688u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    410u,    477u,    518u,    557u,    613u,    936u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    410u,    474u,    515u,    549u,    636u,    912u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    396u,    462u,    499u,    546u,    619u,    706u,   1116u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    389u,    455u,    490u,    536u,    592u,    683u,   1093u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    382u,    441u,    483u,    536u,    600u,    697u,   1051u,   1101u,   1126u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    368u,    434u,    478u,    529u,    617u,    745u,    982u,   1087u,   1119u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    337u,    408u,    445u,    490u,    555u,    635u,    724u,    985u,   1056u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    316u,    377u,    416u,    458u,    520u,    586u,    665u,    776u,    983u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    297u,    362u,    402u,    449u,    499u,    569u,    657u,    773u,    968u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    283u,    335u,    393u,    432u,    482u,    562u,    650u,    773u,    970u,   1088u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    280u,    346u,    400u,    448u,    499u,    580u,    670u,    776u,    968u,   1035u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    302u,    379u,    445u,    499u,    577u,    683u,    757u,    886u,    976u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    303u,    372u,    420u,    455u,    518u,    606u,    705u,    811u,   1005u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    270u,    349u,    398u,    451u,    515u,    590u,    684u,    788u,   1007u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    270u,    312u,    368u,    433u,    510u,    604u,    669u,    750u,    988u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    270u,    306u,    362u,    447u,    532u,    613u,    681u,    770u,    936u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    270u,    295u,    367u,    443u,    544u,    629u,    704u,    779u,    908u,   1030u,   1150u,   1150u,   1150u,   1150u,   1150u,   1150u,
    270u,    270u,    295u,    379u,    445u,    528u,    610u,    684u,    760u,    829u,    904u,   1017u,   1138u,   1150u,   1150u,   1150u,   1150u,
    270u,    270u,    300u,    386u,    443u,    532u,    616u,    686u,    744u,    796u,    849u,    931u,   1028u,   1089u,   1150u,   1150u,   1150u,
    270u,    270u,    307u,    400u,    472u,    542u,    616u,    696u,    757u,    809u,    871u,    936u,   1013u,   1085u,   1150u,   1150u,   1150u,
    270u,    270u,    317u,    407u,    479u,    550u,    622u,    698u,    757u,    808u,    851u,    927u,   1017u,   1089u,   1150u,   1150u,   1150u,
    270u,    270u,    307u,    396u,    464u,    540u,    614u,    687u,    741u,    780u,    822u,    868u,    934u,   1036u,   1150u,   1150u,   1150u,
    270u,    270u,    297u,    378u,    432u,    488u,    556u,    629u,    691u,    739u,    777u,    831u,    927u,   1005u,   1108u,   1150u,   1150u,
    270u,    270u,    295u,    373u,    426u,    465u,    520u,    583u,    645u,    703u,    756u,    814u,    916u,   1006u,   1079u,   1150u,   1150u,
    270u,    270u,    289u,    367u,    423u,    467u,    517u,    574u,    625u,    675u,    728u,    783u,    884u,    953u,   1035u,   1128u,   1150u,
    270u,    270u,    282u,    350u,    403u,    450u,    505u,    556u,    607u,    654u,    692u,    739u,    814u,    903u,    985u,   1072u,   1150u
};
//Gain [gain]
CALQUAL uint16_T VTKFILTDQAIRT[17] = 
{
 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u
};

#endif /* _BUILD_PRESTARGET_ */

