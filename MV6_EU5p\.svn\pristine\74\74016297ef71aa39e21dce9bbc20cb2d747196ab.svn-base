/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_QAIRTARGETMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Breakpoints of Rpm for VTKFILTQAIRTRG [rpm]
CALQUAL uint16_T BKKFILTQAIRTRG[8] = 
{
   1000u,   2000u,   3000u,   4000u,   5000u,   6000u,   8000u,  12000u
};
//kf [kf]
CALQUAL uint16_T KFILTQAIRTRG = 16384u;   //(1.00000000000000*16384)
//Inc QAirTarget [mgcc]
CALQUAL int16_T RTQATINC = 102;   //(0.0996093750*1024)
//Threshold stab QAirTarget [Nm]
CALQUAL uint16_T THRSTSTABDYN = 160u;   //(   5.00000*32)
//Time stab QAirTarget [ms]
CALQUAL uint16_T TIMSTSTABDYN =     50u;   //    50
//kf [kf]
CALQUAL uint16_T VTKFILTQAIRTRG[8] = 
{
 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u, 16384u
};

#endif /* _BUILD_QAIRTARGETMGM_ */

