/*
 * File: DiagFlags.h
 *
 * Code generated for Simulink model 'DiagFlags'.
 *
 * Model version                  : 1.339
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Apr 13 17:54:06 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (29), Warnings (4), Error (0)
 */

#ifndef RTW_HEADER_DiagFlags_h_
#define RTW_HEADER_DiagFlags_h_
#ifndef DiagFlags_COMMON_INCLUDES_
# define DiagFlags_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* DiagFlags_COMMON_INCLUDES_ */

#include "DiagFlags_types.h"

/* Includes for objects with custom storage classes. */
#include "DiagFlags_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define ID_DIAG_FLAGS                  22464692U                 /* Referenced by: '<S3>/ID_DIAG_FLAGS' */

/* mask */

/* Model entry point functions */
extern void DiagFlags_initialize(void);

/* Exported entry point function */
extern void Trig_DiagFlags_Init(void);

/* Exported entry point function */
extern void Trig_DiagFlags_T10(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'DiagFlags'
 * '<S1>'   : 'DiagFlags/DiagFlags'
 * '<S2>'   : 'DiagFlags/Model Info'
 * '<S3>'   : 'DiagFlags/DiagFlags/Init'
 * '<S4>'   : 'DiagFlags/DiagFlags/Merger'
 * '<S5>'   : 'DiagFlags/DiagFlags/T10ms'
 * '<S6>'   : 'DiagFlags/DiagFlags/T10ms/Assign'
 * '<S7>'   : 'DiagFlags/DiagFlags/T10ms/Compare To Constant'
 * '<S8>'   : 'DiagFlags/DiagFlags/T10ms/Compare To Constant1'
 * '<S9>'   : 'DiagFlags/DiagFlags/T10ms/Compare To Constant2'
 * '<S10>'  : 'DiagFlags/DiagFlags/T10ms/Compare To Constant3'
 * '<S11>'  : 'DiagFlags/DiagFlags/T10ms/Compare To Constant4'
 * '<S12>'  : 'DiagFlags/DiagFlags/T10ms/Compare To Constant5'
 */

/*-
 * Requirements for '<Root>': DiagFlags
 */
#endif                                 /* RTW_HEADER_DiagFlags_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
