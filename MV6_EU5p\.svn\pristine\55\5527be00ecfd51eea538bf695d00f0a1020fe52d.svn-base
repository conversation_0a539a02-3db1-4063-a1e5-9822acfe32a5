/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  MATLAB_MEX_FILE
#include "EM_4Cy4Co4In.h"
#define _BUILD_LOADMGM_
#endif
#include "air_mgm.h"
#include "loadmgm.h"
#include "mathlib.h"
#include "diagmgm_out.h"

// Uscite
uint16_T Load;                                  /* 2^-7 Range 0 .. 100% */
uint16_T LoadObj;                               /* 2^-7 Range 0 .. 100% */
uint16_T LoadObjMax;                            /* 2^-7 Range 0 .. 100% */

/* VtLoad used only in engine control projects */
uint16_T VtLoad[N_CYL_MAX_EM];                    /* 2^-7 Range 0 .. 100% */

#ifdef _BUILD_LOADMGM_

// Funzioni
void Load_Selection(void);
void Load_Selection_Init(void);

//Functions implementation 
void LoadMgm_Init(void)
{
    Load_Selection_Init();
}

void LoadMgm_T5ms(void)
{
    Load_Selection();
}

void LoadMgm_TDC(void)
{
    Load_Selection();
}

void LoadMgm_HTDC(void)
{
    Load_Selection();
}

void Load_Selection_Init(void)
{
    uint8_T     cyl; /* cycle index */
    
#if (LOAD_TYPE == USE_MAPRATIO)
    Load = MapRatio;                            // 2^-7
    LoadObj = MapRatio;                         // 2^-7
    LoadObjMax = MapRatio;                      // 2^-7
    for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
    {
        VtLoad[cyl] = Load;
    }
    
#elif (LOAD_TYPE == USE_LOAD_CAN)
    Load = LoadCAN;                             // 2^-7
    LoadObj = LoadCAN;                          // 2^-7
    LoadObjMax = LoadCAN;                       // 2^-7
    for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
    {
        VtLoad[cyl] = Load;
    }
 
#elif (LOAD_TYPE == USE_ANGTHR)
    Load = (AngThrCorr << 3);                   // 2^-4 ==> 2^-7
    LoadObj = (AngThrCorr << 3);                // 2^-4 ==> 2^-7
    LoadObjMax = LoadObj;                       // 2^-4 ==> 2^-7
    for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
    {
        VtLoad[cyl] = Load;
    }

#elif (LOAD_TYPE == USE_DBW)
    Load = (AngThrottle<<3);                    // 2^-4 ==> 2^-7
    LoadObj = (AngThrObj0<<3);                  // 2^-4 ==> 2^-7
    LoadObjMax = LoadObj;                       // 2^-4 ==> 2^-7
    for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
    {
        VtLoad[cyl] = Load;
    }

#elif (LOAD_TYPE == USE_QAIRRATIO)
    Load = QAirRatio0;                          // 2^-7
    LoadObj = QAirRatioTarget;                  // 2^-7
    LoadObjMax = QAirRatioTargetMax;            // 2^-7
    for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
    {
        VtLoad[cyl] = QAirRatio[cyl];
    }

#else
#error Error Wrong Configuration!!!
#endif
} // Load_Selection_Init
    
void Load_Selection(void)
{
    uint8_T cyl;
    uint16_T tmpLoad0;
    uint32_T tmpLoadObj0;
    uint32_T tmpLoadObj0Max;
    uint16_T tmpVtLoad[N_CYL_MAX_EM];
    uint8_T PtFaultLoad = NO_PT_FAULT;
    uint8_T StDiagLoad = NO_FAULT;

#if (LOAD_TYPE == USE_MAPRATIO)
    tmpLoad0 = MapRatio;                        // 2^-7
    tmpLoadObj0 = MapRatio;                    // 2^-7
    
    // Manca la Diagnosi PtFaultLoad ????

#elif (LOAD_TYPE == USE_LOAD_CAN)    
      tmpLoad0 = LoadCAN;                        // 2^-7
      tmpLoadObj0 = LoadCAN;                // 2^-7
      if(VDLoadCAN == 0)
      {
        PtFaultLoad = SIGNAL_NOT_VALID;
      }
 
#elif (LOAD_TYPE == USE_ANGTHR) 
    tmpLoad0 = (AngThrCorr << 3);            // 2^-4 ==> 2^-7
    tmpLoadObj0 = (AngThrCorr << 3);    // 2^-4 ==> 2^-7

    // Manca la Diagnosi PtFaultLoad ????

#elif (LOAD_TYPE == USE_DBW) 
    tmpLoad0 = (AngThrottle<<3);        // 2^-4 ==> 2^-7
    tmpLoadObj0 = (AngThrObj0<<3);        // 2^-4 ==> 2^-7
    
    // Manca la Diagnosi PtFaultLoad ????

#elif (LOAD_TYPE == USE_QAIRRATIO)
    tmpLoad0 = QAirRatio0;                    // 2^-7
    tmpLoadObj0 = QAirRatioTarget;                // 2^-7
    tmpLoadObj0Max = QAirRatioTargetMax;                // 2^-7
    for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
    {
        tmpVtLoad[cyl] = QAirRatio[cyl];
    }

    /* Manca la Diagnosi PtFaultLoad ???? */

#else
#error Error Wrong Configuration!!!
#endif

#ifdef _BUILD_SPARKMGM_
    // Applico la correzione per transitorio di ingresso/uscita Cutoff
    #if (LOAD_TYPE == USE_ANGTHR)
    if(CutoffGain != FIX_10)
    {
        tmpLoadObj0 = (tmpLoadObj0 * (uint32_T)CutoffGain) >> 10;          // 2^-7
        tmpLoadObj0Max = (tmpLoadObj0Max * (uint32_T)CutoffGain) >> 10;    // 2^-7
    }
    #else
    #endif
#endif

#ifdef DIAG_LOAD
#ifndef  MATLAB_MEX_FILE
    DiagMgm_SetDiagState(DIAG_LOAD, PtFaultLoad, &StDiagLoad);
#endif
#endif
  
    switch(StDiagLoad) 
    {
        case FAULT_FILTERING:
              /* mantiene il valore precedente di Load */
            break;
        case FAULT:
            /* valore di recovery per Load */
            Load = 0;
            LoadObj = 0;
            LoadObjMax = 0;
            for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
            {
                VtLoad[cyl] = 0;
            }
            break;
        case NO_FAULT:
        default: /* default theoretically not possible! */    
            Load = tmpLoad0;
            LoadObj = tmpLoadObj0;
            LoadObjMax = tmpLoadObj0Max;
            for (cyl=0; cyl < N_CYL_MAX_EM; cyl++)
            {
                VtLoad[cyl] = tmpVtLoad[cyl];
            }
            break;
    } 
}    // Load_Selection

#endif // _BUILD_LOADMGM_
