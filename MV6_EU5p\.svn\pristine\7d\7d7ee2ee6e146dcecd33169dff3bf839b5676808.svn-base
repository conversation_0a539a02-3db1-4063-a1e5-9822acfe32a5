/******************************************************************************/
/*                                    NOTE:                                   */
/* Modified by <PERSON><PERSON><PERSON>; it does not use Checksum_C opcodes                     */
/* released by Freescale                                                      */
/******************************************************************************/
#ifdef _BUILD_FLASH_
 
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include    "typedefs.h"
#include    "ssd_types.h"
#include    "ssd_c90fl.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* None */


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define CALLBACK_BC		    90
#define CALLBACK_PV		    80
#define CALLBACK_CS		    120

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
* BDMEnable - Function description
*
* Arguments:
* None
*
* Returned value:
* None
*
* Usage notes:
* None
*--------------------------------------------------------------------------*/
static inline void BDMEnable(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static uint32_t returnCodeLocal;     


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CheckSum - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
 uint32_t CheckSum ( PSSD_CONFIG pSSDConfig,
                     uint32_t dest,
                     uint32_t size,
                     uint32_t *pSum,
                     void (*CallBack)(void))

{

    uint32_t destIndex;               /* destination address index */
    uint32_t shadowRowEnd;            /* shadow row base + shadow size */
    uint32_t mainArrayEnd;            /* main array base + main array size */
    uint32_t temp;        	        /* dest + size, or CallBack step */
            
    returnCodeLocal = C90FL_OK; 
    
    /* Check alignments */
    if ( ((dest | size) % C90FL_DWORD_SIZE) != 0 )
    {
        returnCodeLocal = C90FL_ERROR_ALIGNMENT;
        
        if (pSSDConfig->BDMEnable)
            BDMEnable();
        return returnCodeLocal;
        
    }
    
    /* The flash range falls within either main array or shadow row */
    shadowRowEnd = pSSDConfig->shadowRowBase + pSSDConfig->shadowRowSize;
    mainArrayEnd = pSSDConfig->mainArrayBase + pSSDConfig->mainArraySize;
    temp = dest + size;
    
    /* The program range should fall within shadow row or main array */
    if ( !((dest >= pSSDConfig->shadowRowBase) && (dest < shadowRowEnd) && 
           (size <= pSSDConfig->shadowRowSize) && (temp <= shadowRowEnd)) 
         &&
         !((dest >= pSSDConfig->mainArrayBase) && (dest <  mainArrayEnd) &&
           (size <= pSSDConfig->mainArraySize) && (temp <= mainArrayEnd)) )  
         
    {
        returnCodeLocal = C90FL_ERROR_RANGE;
        if (pSSDConfig->BDMEnable)
            BDMEnable();
        
        return returnCodeLocal;
    }
    
    *pSum = 0;
    temp = 0;
    
    /* word by word checksum */
    for (destIndex = 0; destIndex < (size / C90FL_WORD_SIZE); destIndex++)
    {
/**********************/        
        uint32_t swappedWord = 0;
        
        swappedWord |=  *(uint8_t *)(dest  );
        swappedWord |= (*(uint8_t *)(dest+1))<<8;
        swappedWord |= (*(uint8_t *)(dest+2))<<16;
        swappedWord |= (*(uint8_t *)(dest+3))<<24;

        *pSum += swappedWord;
/**********************/        
/*        *pSum += *(uint32_t *)dest;          */
        
        dest += C90FL_WORD_SIZE;
        
        /* CallBack */
        if( (CallBack != NULL_CALLBACK) && (destIndex == temp) )
        {
            CallBack();
            temp += CALLBACK_CS;
        }
    }    
    
    return returnCodeLocal;
}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/

/*--------------------------------------------------------------------------*
 * BDMEnable - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static __asm  inline void BDMEnable(void)
{
    lis r5, returnCodeLocal@h     # Load upper address of BIUAPR into R5
    ori r5, r5, returnCodeLocal@l # Load lower address of BIUAPR into R5
    mr r3,r5
    sc                  /* generate system call interrupt */
}



#endif /* _BUILD_FLASH_ */
