/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Application/THROTTLEMODEL/main_trunk/ThrottleModel_ert_rtw/ThrottleModel_private.h $ */
/* $Description:  $ */
/* $Revision: 5834 $ */
/* $Date: 2014-07-15 18:19:18 +0200 (mar, 15 lug 2014) $ */
/* $Author: LanaL $ */
/*****************************************************************************************************************/
/*
 * File: ThrottleModel_private.h
 *
 * Real-Time Workshop code generated for Simulink model ThrottleModel.
 *
 * Model version                        : 1.686
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Tue Jul 15 18:16:27 2014
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Tue Jul 15 18:16:28 2014
 */
#ifndef RTW_HEADER_ThrottleModel_private_h_
#define RTW_HEADER_ThrottleModel_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "air_mgm.h"
#include "prestarget_mgm.h"
#include "syncmgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error "Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error "Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Imported (extern) block parameters */
extern uint16_T BKPRESANGTHRTARG[17];  /* Variable: BKPRESANGTHRTARG
                                        * '<S6>/BKPRESANGTHRTARG'
                                        * PresObj breakpoints
                                        */
extern uint16_T BKRPMANGTHRTARG[29];   /* Variable: BKRPMANGTHRTARG
                                        * '<S6>/BKRPMANGTHRTARG'
                                        * RpmF breakpoints
                                        */
extern uint16_T TBANGTHRTARG[493];     /* Variable: TBANGTHRTARG
                                        * '<S6>/TBANGTHRTARG'
                                        * Ang throttle model target
                                        */
void ThrottleModel_T10ms(void);
void ThrottleModel_Init(void);

#endif                                 /* RTW_HEADER_ThrottleModel_private_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
