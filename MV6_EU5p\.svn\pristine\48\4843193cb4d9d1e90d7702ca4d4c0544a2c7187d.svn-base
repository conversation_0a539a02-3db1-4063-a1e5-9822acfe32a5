/*
 * File: CmiDriverMgm_types.h
 *
 * Code generated for Simulink model 'CmiDriverMgm'.
 *
 * Model version                  : 1.2254
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 11:51:07 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (22), Warnings (3), Errors (8)
 */

#ifndef RTW_HEADER_CmiDriverMgm_types_h_
#define RTW_HEADER_CmiDriverMgm_types_h_
#endif                                 /* RTW_HEADER_CmiDriverMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
