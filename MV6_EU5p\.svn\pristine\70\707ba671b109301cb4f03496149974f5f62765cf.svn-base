#ifndef _TASKDEFS_H_
#define _TASKDEFS_H_

#include "os_api.h"


/* TASK PRIORITY */

// Task applicativo eventi sporadici
//#define TaskPowerOff_PRIORITY       S_IRQ6
#define TaskNoSync_PRIORITY         S_IRQ0
#define TaskSync_PRIORITY           S_IRQ7
#define TaskKeyOn_PRIORITY          S_IRQ0
#define TaskKeyOff_PRIORITY         S_IRQ0

// Task Applicativo eventi angolari
#define TaskHTDC_PRIORITY           S_IRQ4
#define TaskPreHTDC_PRIORITY        S_IRQ7  //  S_IRQ4
#define TaskPreTDC_PRIORITY         S_IRQ7  //  S_IRQ4
#define TaskTDC_PRIORITY            S_IRQ4
#define TaskEOA_PRIORITY            S_IRQ5
#define TaskAngle_PRIORITY          S_IRQ6
#define TaskCamEdge_PRIORITY        S_IRQ6
                                                                             
// Task eventi legati alla acq della ion
#define TaskExtTrigger_PRIORITY     S_IRQ6
#define TaskSparkOff_PRIORITY       S_IRQ6
#define TaskSparkEvent_PRIORITY     S_IRQ6
#define TaskIonEvent_PRIORITY       S_IRQ6
#define TaskStartOfAcq_PRIORITY     S_IRQ6

// Task attuazione IGN/INJ
#define TaskINJ_PRG_PRIORITY        S_IRQ7
#define TaskIGN_PRG_PRIORITY        S_IRQ7

// Task comunicazione SPI e CAN

#define SPI_PRIORITY                S_IRQ5
#define CAN_PRIORITY                S_IRQ7
#define UART_PRIORITY               S_IRQ7
#define RX_UART_PRIORITY            S_IRQ6
#define TX_UART_PRIORITY            S_IRQ5

#define SCI_PRIORITY                S_IRQ5
#define LIN_PRIORITY                S_IRQ7
// Task a tempo
#define Task1ms_PRIORITY            USER_1MS_RATE_ISR_PRIORITY
#define Task5ms_PRIORITY            USER_HIGH_RATE_ISR_PRIORITY
#define Task10ms_PRIORITY           USER_MIDDLE_RATE_ISR_PRIORITY
#define Task100ms_PRIORITY          USER_LOW_RATE_ISR_PRIORITY

#ifdef _BUILD_DIAGCANMGM_ 
#define TaskFlashErase_PRIORITY     S_IRQ0
#define TaskEOL_PRIORITY            S_IRQ0
#endif
#define TaskCheckSum_PRIORITY       S_IRQ1

// EXTIRQ
#define MPC2515_INT_PRIORITY        S_IRQ4
#define MPC2515_RX0BF_PRIORITY      S_IRQ7
#define MPC2515_RX1BF_PRIORITY      S_IRQ7

#define CCP_PRIORITY                PRI_8

#ifdef _BUILD_PIOTEST_
#define PIOTEST_PRIORITY            S_IRQ6
#endif

#ifdef _BUILD_ADC_
#define RECOVERY_ADC_PRIORITY       S_IRQ7
#endif


/*  Task functions declaration */
DeclareTask(Task5ms);
DeclareTask(Task10ms);
DeclareTask(Task100ms);
DeclareTask(Task1ms); 

DeclareTask(CAN_ExRxDoneChA);
DeclareTask(CAN_ExTxDoneChA);
DeclareTask(CAN_ExRxDoneChB);
DeclareTask(CAN_ExTxDoneChB);
DeclareTask(CAN_ExRxDoneChC);
DeclareTask(CAN_ExTxDoneChC);

DeclareTask(TaskINJ_PRG);
#ifdef _BUILD_INJCMD_
DeclareTask(INJCMD_Ex_Cyl_0);
DeclareTask(INJCMD_Ex_Cyl_1);
DeclareTask(INJCMD_Ex_Cyl_2);
DeclareTask(INJCMD_Ex_Cyl_3);
DeclareTask(INJCMD_Ex_Cyl_4);
DeclareTask(INJCMD_Ex_Cyl_5);
DeclareTask(INJCMD_Ex_Cyl_6);
DeclareTask(INJCMD_Ex_Cyl_7);
DeclareTask(TaskSOI);
DeclareTask(TaskEOI);
#endif
DeclareTask(TaskIGN_PRG);
#ifdef _BUILD_IGNCMD_
DeclareTask(IGNCMD_Ex_Coil_0);
DeclareTask(IGNCMD_Ex_Coil_1);
DeclareTask(IGNCMD_Ex_Coil_2);
DeclareTask(IGNCMD_Ex_Coil_3);
DeclareTask(IGNCMD_Diag_Coil_0);
DeclareTask(IGNCMD_Diag_Coil_1);
DeclareTask(IGNCMD_Diag_Coil_2);
DeclareTask(IGNCMD_Diag_Coil_3);
#endif
DeclareTask(TaskSparkOff);
DeclareTask(TaskEOA);
//DeclareTask(TaskPowerOff);
DeclareTask(TaskAngle);
DeclareTask(TaskTDC);
DeclareTask(TaskHTDC);
DeclareTask(TaskPreTDC);
DeclareTask(TaskPreHTDC);
DeclareTask(TaskKeyOn);
DeclareTask(TaskKeyOff);
DeclareTask(TaskSync);
DeclareTask(TaskNoSync);
DeclareTask(TaskCamEdge);
#ifdef _BUILD_IONACQ_
DeclareTask(IonAcq_StartOfAcq_A);
DeclareTask(IonAcq_StartOfAcq_B);
DeclareTask(TaskIONCircuitSelect);
#ifdef SPK_EVN_A_CHANNEL
DeclareTask(SparkEvent_A_triggered);
#endif
#ifdef SPK_EVN_B_CHANNEL
DeclareTask(SparkEvent_B_triggered);
#endif
#ifdef ION_EVN_A_CHANNEL
DeclareTask(IonEvent_A_triggered);
#endif
#ifdef ION_EVN_B_CHANNEL
DeclareTask(IonEvent_B_triggered);
#endif
#endif
#ifdef _BUILD_SPI_
DeclareTask(SPI_ExTxDoneChA_PCS0);
DeclareTask(SPI_ExTxDoneChA_PCS1);
DeclareTask(SPI_ExTxDoneChA_PCS2);
DeclareTask(SPI_ExTxDoneChA_PCS3);
DeclareTask(SPI_ExTxDoneChA_PCS4);
DeclareTask(SPI_ExTxDoneChA_PCS5);
DeclareTask(SPI_ExTxDoneChB_PCS0);
DeclareTask(SPI_ExTxDoneChB_PCS1);
DeclareTask(SPI_ExTxDoneChB_PCS2);
DeclareTask(SPI_ExTxDoneChB_PCS3);
DeclareTask(SPI_ExTxDoneChB_PCS4);
DeclareTask(SPI_ExTxDoneChB_PCS5);
DeclareTask(SPI_ExTxDoneChC_PCS0);
DeclareTask(SPI_ExTxDoneChC_PCS1);
DeclareTask(SPI_ExTxDoneChC_PCS2);
DeclareTask(SPI_ExTxDoneChC_PCS3);
DeclareTask(SPI_ExTxDoneChC_PCS4);
DeclareTask(SPI_ExTxDoneChC_PCS5);
DeclareTask(SPI_ExTxDoneChD_PCS0);
DeclareTask(SPI_ExTxDoneChD_PCS1);
DeclareTask(SPI_ExTxDoneChD_PCS2);
DeclareTask(SPI_ExTxDoneChD_PCS3);
DeclareTask(SPI_ExTxDoneChD_PCS4);
DeclareTask(SPI_ExTxDoneChD_PCS5);
#endif
#ifdef _BUILD_SCIMGM_
DeclareTask(SCI_ExStopTxA);
DeclareTask(SCI_ExStopRxA);
DeclareTask(SCI_ExOverrunErrorA);
DeclareTask(SCI_ExStopTxB);
DeclareTask(SCI_ExStopRxB);
DeclareTask(SCI_ExOverrunErrorB);
#endif
#ifdef _BUILD_KLINE_
DeclareTask(KLINE_ExStopTx);
DeclareTask(KLINE_ExStopRx);
DeclareTask(KLINE_ExOverrunError);
#endif

#ifdef _BUILD_LINMGM_
DeclareTask(LIN_ExStopTx);
DeclareTask(LIN_ExStopRx);
#endif

#ifdef _BUILD_UARTMGM_
DeclareTask(UART_RxTask);
DeclareTask(UART_RxLoop);
DeclareTask(UART_TxTask);
#endif
#ifdef _BUILD_PIOTEST_
DeclareTask(PIOChannel_triggered);
#endif
#if (ION_TRG_SOURCE == ION_TRG_EXTERN)
#if (N_CYLINDER >= 1)
DeclareTask(Channel_1_triggered);
#endif
#if (N_CYLINDER >= 2)
DeclareTask(Channel_2_triggered);
#endif
#if (N_CYLINDER >= 3)
DeclareTask(Channel_3_triggered);
#endif
#if (N_CYLINDER >= 4)
DeclareTask(Channel_4_triggered);
#endif
#if (N_CYLINDER >= 5)
DeclareTask(Channel_5_triggered);
#endif
#if (N_CYLINDER >= 6)
DeclareTask(Channel_6_triggered);
#endif
#if (N_CYLINDER >= 7)
DeclareTask(Channel_7_triggered);
#endif
#if (N_CYLINDER >= 8)
DeclareTask(Channel_8_triggered);
#endif
#endif
#ifdef _BUILD_HUMSNS_
DeclareTask(Channel_humidity1);
DeclareTask(Channel_humidity2);
#endif
#ifdef _BUILD_EXTIRQ_
DeclareTask(MPC2515_INT);
DeclareTask(MPC2515_RX0BF);
DeclareTask(MPC2515_RX1BF);
#endif

DeclareTask(CCPBackgroundTask);
DeclareTask(BackgroundTask);
DeclareTask(KWPDecoding);
DeclareTask(TaskEndOfLine);

DeclareTask(TaskWaitForReset);
DeclareTask(TaskCheckSum);

#ifdef _BUILD_VCALIB_
DeclareTask(TaskVCALIB_update_region);
#endif

#ifdef _BUILD_PITTEST_
DeclareTask(Task_PitTestCase01);
#endif

DeclareTask(MapAngAcq_EndOfAcq);

#ifdef _BUILD_ADC_
//DeclareTask(Task_ResetFIFO300usec);    
//DeclareTask(Task_ResetFIFO2msec);
//DeclareTask(Task_ResetFIFO10msec);
#endif

DeclareTask(TaskDelayed0);
DeclareTask(TaskDelayed1);
DeclareTask(TaskDelayed2);
DeclareTask(TaskDelayed3);
DeclareTask(TaskDelayed4);
DeclareTask(TaskExVPwmIn);


/*  Task identifiers  */
enum TaskID {
    Task5msID,
    Task10msID,
    Task100msID,
    #if((CAN_CHA_BUF0_EXC!=0) || \
        (CAN_CHA_BUF1_EXC!=0) || \
        CAN_CHA_BUF2_EXC || \
        CAN_CHA_BUF3_EXC || \
        CAN_CHA_BUF4_EXC || \
        CAN_CHA_BUF5_EXC || \
        CAN_CHA_BUF6_EXC || \
        CAN_CHA_BUF7_EXC || \
        CAN_CHA_BUF8_EXC || \
        CAN_CHA_BUF9_EXC || \
        CAN_CHA_BUF10_EXC || \
        CAN_CHA_BUF11_EXC || \
        CAN_CHA_BUF12_EXC || \
        CAN_CHA_BUF13_EXC || \
        CAN_CHA_BUF14_EXC || \
        CAN_CHA_BUF15_EXC || \
        CAN_CHA_BUF16_EXC || \
        CAN_CHA_BUF17_EXC || \
        CAN_CHA_BUF18_EXC || \
        CAN_CHA_BUF19_EXC || \
        CAN_CHA_BUF20_EXC || \
        CAN_CHA_BUF21_EXC || \
        CAN_CHA_BUF22_EXC || \
        CAN_CHA_BUF23_EXC || \
        CAN_CHA_BUF24_EXC || \
        CAN_CHA_BUF25_EXC || \
        CAN_CHA_BUF26_EXC || \
        CAN_CHA_BUF27_EXC || \
        CAN_CHA_BUF28_EXC || \
        CAN_CHA_BUF29_EXC || \
        CAN_CHA_BUF30_EXC || \
        CAN_CHA_BUF31_EXC || \
        CAN_CHA_BUF32_EXC || \
        CAN_CHA_BUF33_EXC || \
        CAN_CHA_BUF34_EXC || \
        CAN_CHA_BUF35_EXC || \
        CAN_CHA_BUF36_EXC || \
        CAN_CHA_BUF37_EXC || \
        CAN_CHA_BUF38_EXC || \
        CAN_CHA_BUF39_EXC || \
        CAN_CHA_BUF40_EXC || \
        CAN_CHA_BUF41_EXC || \
        CAN_CHA_BUF42_EXC || \
        CAN_CHA_BUF43_EXC || \
        CAN_CHA_BUF43_EXC || \
        CAN_CHA_BUF44_EXC || \
        CAN_CHA_BUF45_EXC || \
        CAN_CHA_BUF46_EXC || \
        CAN_CHA_BUF47_EXC || \
        CAN_CHA_BUF48_EXC || \
        CAN_CHA_BUF49_EXC || \
        CAN_CHA_BUF50_EXC || \
        CAN_CHA_BUF51_EXC || \
        CAN_CHA_BUF52_EXC || \
        CAN_CHA_BUF53_EXC || \
        CAN_CHA_BUF54_EXC || \
        CAN_CHA_BUF55_EXC || \
        CAN_CHA_BUF56_EXC || \
        CAN_CHA_BUF57_EXC || \
        CAN_CHA_BUF58_EXC || \
        CAN_CHA_BUF59_EXC || \
        CAN_CHA_BUF60_EXC || \
        CAN_CHA_BUF61_EXC || \
        CAN_CHA_BUF62_EXC || \
        CAN_CHA_BUF63_EXC )
    CAN_ExRxDoneChAID,
    CAN_ExTxDoneChAID,
#endif
    #if(CAN_CHB_BUF0_EXC || \
        CAN_CHB_BUF1_EXC || \
        CAN_CHB_BUF2_EXC || \
        CAN_CHB_BUF3_EXC || \
        CAN_CHB_BUF4_EXC || \
        CAN_CHB_BUF5_EXC || \
        CAN_CHB_BUF6_EXC || \
        CAN_CHB_BUF7_EXC || \
        CAN_CHB_BUF8_EXC || \
        CAN_CHB_BUF9_EXC || \
        CAN_CHB_BUF10_EXC || \
        CAN_CHB_BUF11_EXC || \
        CAN_CHB_BUF12_EXC || \
        CAN_CHB_BUF13_EXC || \
        CAN_CHB_BUF14_EXC || \
        CAN_CHB_BUF15_EXC || \
        CAN_CHB_BUF16_EXC || \
        CAN_CHB_BUF17_EXC || \
        CAN_CHB_BUF18_EXC || \
        CAN_CHB_BUF19_EXC || \
        CAN_CHB_BUF20_EXC || \
        CAN_CHB_BUF21_EXC || \
        CAN_CHB_BUF22_EXC || \
        CAN_CHB_BUF23_EXC || \
        CAN_CHB_BUF24_EXC || \
        CAN_CHB_BUF25_EXC || \
        CAN_CHB_BUF26_EXC || \
        CAN_CHB_BUF27_EXC || \
        CAN_CHB_BUF28_EXC || \
        CAN_CHB_BUF29_EXC || \
        CAN_CHB_BUF30_EXC || \
        CAN_CHB_BUF31_EXC || \
        CAN_CHB_BUF32_EXC || \
        CAN_CHB_BUF33_EXC || \
        CAN_CHB_BUF34_EXC || \
        CAN_CHB_BUF35_EXC || \
        CAN_CHB_BUF36_EXC || \
        CAN_CHB_BUF37_EXC || \
        CAN_CHB_BUF38_EXC || \
        CAN_CHB_BUF39_EXC || \
        CAN_CHB_BUF40_EXC || \
        CAN_CHB_BUF41_EXC || \
        CAN_CHB_BUF42_EXC || \
        CAN_CHB_BUF43_EXC || \
        CAN_CHB_BUF43_EXC || \
        CAN_CHB_BUF44_EXC || \
        CAN_CHB_BUF45_EXC || \
        CAN_CHB_BUF46_EXC || \
        CAN_CHB_BUF47_EXC || \
        CAN_CHB_BUF48_EXC || \
        CAN_CHB_BUF49_EXC || \
        CAN_CHB_BUF50_EXC || \
        CAN_CHB_BUF51_EXC || \
        CAN_CHB_BUF52_EXC || \
        CAN_CHB_BUF53_EXC || \
        CAN_CHB_BUF54_EXC || \
        CAN_CHB_BUF55_EXC || \
        CAN_CHB_BUF56_EXC || \
        CAN_CHB_BUF57_EXC || \
        CAN_CHB_BUF58_EXC || \
        CAN_CHB_BUF59_EXC || \
        CAN_CHB_BUF60_EXC || \
        CAN_CHB_BUF61_EXC || \
        CAN_CHB_BUF62_EXC || \
        CAN_CHB_BUF63_EXC )
    CAN_ExRxDoneChBID,
    CAN_ExTxDoneChBID,
#endif
    #if(CAN_CHC_BUF0_EXC || \
        CAN_CHC_BUF1_EXC || \
        CAN_CHC_BUF2_EXC || \
        CAN_CHC_BUF3_EXC || \
        CAN_CHC_BUF4_EXC || \
        CAN_CHC_BUF5_EXC || \
        CAN_CHC_BUF6_EXC || \
        CAN_CHC_BUF7_EXC || \
        CAN_CHC_BUF8_EXC || \
        CAN_CHC_BUF9_EXC || \
        CAN_CHC_BUF10_EXC || \
        CAN_CHC_BUF11_EXC || \
        CAN_CHC_BUF12_EXC || \
        CAN_CHC_BUF13_EXC || \
        CAN_CHC_BUF14_EXC || \
        CAN_CHC_BUF15_EXC || \
        CAN_CHC_BUF16_EXC || \
        CAN_CHC_BUF17_EXC || \
        CAN_CHC_BUF18_EXC || \
        CAN_CHC_BUF19_EXC || \
        CAN_CHC_BUF20_EXC || \
        CAN_CHC_BUF21_EXC || \
        CAN_CHC_BUF22_EXC || \
        CAN_CHC_BUF23_EXC || \
        CAN_CHC_BUF24_EXC || \
        CAN_CHC_BUF25_EXC || \
        CAN_CHC_BUF26_EXC || \
        CAN_CHC_BUF27_EXC || \
        CAN_CHC_BUF28_EXC || \
        CAN_CHC_BUF29_EXC || \
        CAN_CHC_BUF30_EXC || \
        CAN_CHC_BUF31_EXC || \
        CAN_CHC_BUF32_EXC || \
        CAN_CHC_BUF33_EXC || \
        CAN_CHC_BUF34_EXC || \
        CAN_CHC_BUF35_EXC || \
        CAN_CHC_BUF36_EXC || \
        CAN_CHC_BUF37_EXC || \
        CAN_CHC_BUF38_EXC || \
        CAN_CHC_BUF39_EXC || \
        CAN_CHC_BUF40_EXC || \
        CAN_CHC_BUF41_EXC || \
        CAN_CHC_BUF42_EXC || \
        CAN_CHC_BUF43_EXC || \
        CAN_CHC_BUF43_EXC || \
        CAN_CHC_BUF44_EXC || \
        CAN_CHC_BUF45_EXC || \
        CAN_CHC_BUF46_EXC || \
        CAN_CHC_BUF47_EXC || \
        CAN_CHC_BUF48_EXC || \
        CAN_CHC_BUF49_EXC || \
        CAN_CHC_BUF50_EXC || \
        CAN_CHC_BUF51_EXC || \
        CAN_CHC_BUF52_EXC || \
        CAN_CHC_BUF53_EXC || \
        CAN_CHC_BUF54_EXC || \
        CAN_CHC_BUF55_EXC || \
        CAN_CHC_BUF56_EXC || \
        CAN_CHC_BUF57_EXC || \
        CAN_CHC_BUF58_EXC || \
        CAN_CHC_BUF59_EXC || \
        CAN_CHC_BUF60_EXC || \
        CAN_CHC_BUF61_EXC || \
        CAN_CHC_BUF62_EXC || \
        CAN_CHC_BUF63_EXC )
    CAN_ExRxDoneChCID,
    CAN_ExTxDoneChCID,
#endif
    TaskINJ_PRGID,
#ifdef _BUILD_INJCMD_
    INJCMD_Ex_Cyl_0ID,
    INJCMD_Ex_Cyl_1ID,
    INJCMD_Ex_Cyl_2ID,
    INJCMD_Ex_Cyl_3ID,
    INJCMD_Ex_Cyl_4ID,
    INJCMD_Ex_Cyl_5ID,
    INJCMD_Ex_Cyl_6ID,
    INJCMD_Ex_Cyl_7ID,
    TaskSOIID,
    TaskEOIID,
#endif
    TaskIGN_PRGID,
#ifdef _BUILD_IGNCMD_
    IGNCMD_Ex_Coil_0ID,
    IGNCMD_Ex_Coil_1ID,
    IGNCMD_Ex_Coil_2ID,
    IGNCMD_Ex_Coil_3ID,
    IgnCmd_Diag_0ID,
    IgnCmd_Diag_1ID,
    IgnCmd_Diag_2ID,
    IgnCmd_Diag_3ID,
#endif
    TaskSparkOffID,
    TaskEOAID,
    //TaskPowerOffID,
    TaskAngleID,
    TaskTDCID,
    TaskHTDCID,
    TaskPreTDCID,
    TaskPreHTDCID,
    TaskKeyOnID,
    TaskKeyOffID,
    TaskSyncID,
    TaskNoSyncID,
    TaskCamEdgeID,
#ifdef _BUILD_IONACQ_
    IonAcq_StartOfAcq_AID,
    IonAcq_StartOfAcq_BID,
    SelIONCircuitID,
#ifdef SPK_EVN_A_CHANNEL
    SparkEvent_A_triggeredID,
#endif
#ifdef SPK_EVN_B_CHANNEL
    SparkEvent_B_triggeredID,
#endif
#ifdef ION_EVN_A_CHANNEL
    IonEvent_A_triggeredID,
#endif
#ifdef ION_EVN_B_CHANNEL
    IonEvent_B_triggeredID,
#endif
#endif
#ifdef _BUILD_SPI_
    SPI_ExTxDoneChA_PCS0ID,
    SPI_ExTxDoneChA_PCS1ID,
    SPI_ExTxDoneChA_PCS2ID,
    SPI_ExTxDoneChA_PCS3ID,
    SPI_ExTxDoneChA_PCS4ID,
    SPI_ExTxDoneChA_PCS5ID,
    SPI_ExTxDoneChB_PCS0ID,
    SPI_ExTxDoneChB_PCS1ID,
    SPI_ExTxDoneChB_PCS2ID,
    SPI_ExTxDoneChB_PCS3ID,
    SPI_ExTxDoneChB_PCS4ID,
    SPI_ExTxDoneChB_PCS5ID,
    SPI_ExTxDoneChC_PCS0ID,
    SPI_ExTxDoneChC_PCS1ID,
    SPI_ExTxDoneChC_PCS2ID,
    SPI_ExTxDoneChC_PCS3ID,
    SPI_ExTxDoneChC_PCS4ID,
    SPI_ExTxDoneChC_PCS5ID,
    SPI_ExTxDoneChD_PCS0ID,
    SPI_ExTxDoneChD_PCS1ID,
    SPI_ExTxDoneChD_PCS2ID,
    SPI_ExTxDoneChD_PCS3ID,
    SPI_ExTxDoneChD_PCS4ID,
    SPI_ExTxDoneChD_PCS5ID,
#endif
#ifdef _BUILD_SCIMGM_
    SCI_ExStopTxAID,
    SCI_ExStopRxAID,
    SCI_ExOverrunErrorAID,
    SCI_ExStopTxBID,
    SCI_ExStopRxBID,
    SCI_ExOverrunErrorBID ,
#endif

#ifdef _BUILD_KLINE_
    KLINE_ExStopTxID,
    KLINE_ExStopRxID,
    KLINE_ExOverrunErrorID,
#endif

#ifdef _BUILD_LINMGM_
    LIN_ExStopTxID,
    LIN_ExStopRxID,
#endif

#ifdef _BUILD_PIOTEST_
    PIOChannel_triggeredID,
#endif

#if (ION_TRG_SOURCE == ION_TRG_EXTERN)
#if (N_CYLINDER >= 1)
    Channel_1_triggeredID,
#endif
#if (N_CYLINDER >= 2)
    Channel_2_triggeredID,
#endif
#if (N_CYLINDER >= 3)
    Channel_3_triggeredID,
#endif
#if (N_CYLINDER >= 4)
    Channel_4_triggeredID,
#endif
#if (N_CYLINDER >= 5)
    Channel_5_triggeredID,
#endif
#if (N_CYLINDER >= 6)
    Channel_6_triggeredID,
#endif
#if (N_CYLINDER >= 7)
    Channel_7_triggeredID,
#endif
#if (N_CYLINDER >= 8)
    Channel_8_triggeredID,
#endif
#endif
#ifdef _BUILD_HUMSNS_
    Channel_humidity1ID,
    Channel_humidity2ID,
#endif
#ifdef _BUILD_SPICAN_
    MPC2515_INTID,
    MPC2515_RX0BFID,
    MPC2515_RX1BFID,
#endif

#ifdef _BUILD_UARTMGM_
    UART_RxTaskID,
    UART_RxLoopID,
    UART_TxTaskID,    
#endif
    CCPBackgroundTaskID,
    BackgroundTaskID,

#ifdef _BUILD_DIAGCANMGM_

    KWPDecodingID,
#endif    
    Task1msID, 
#ifdef _BUILD_DIAGCANMGM_
TaskEndOfLineID,
TaskWaitForResetID,
#endif
#ifdef _BUILD_CCP_
TaskCheckSumID,
#endif
#ifdef _BUILD_VCALIB_
    TaskVCALIB_update_regionID,
#endif

#ifdef _BUILD_PITTEST_
    Task_PitTestCase01ID,
#endif 

    MapAngAcq_EndOfAcqID,

#ifdef _BUILD_ADC_
    //Task_ResetFIFO300usecID,            
    //Task_ResetFIFO2msecID,     
    //Task_ResetFIFO10msecID,      
#endif
    TaskDelayedEmios0ID,
    TaskDelayedEmios1ID,
    TaskDelayedEmios2ID,
    TaskDelayedEmios3ID,
    TaskDelayedEmios4ID,
    TaskExVPwmInID,
    MAX_NUM_TASK
};

#endif
