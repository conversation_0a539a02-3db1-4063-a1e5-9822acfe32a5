/*
 * File: creep_limiter_mgm.h
 *
 * Code generated for Simulink model 'CreepLimiterMgm'.
 *
 * Model version                  : 1.96
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Fri Oct  4 09:09:50 2019
 */

#ifndef RTW_HEADER_creep_limiter_mgm_h_
#define RTW_HEADER_creep_limiter_mgm_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define ST_CREEPING                    2U                        /* status */
#define ST_FREE                        1U                        /* status */
#define ST_INIT                        0U                        /* status */
#define ST_RETURN                      3U                        /* status */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T CmiCreepLimI;           /* Cmi Creep limiter I */
extern int32_T CmiCreepLimIHiR;        /* Cmi Creep control */
extern int16_T CmiCreepLimP;           /* Cmi Creep limiter P */
extern int32_T CmiCreepLimPHiR;        /* Cmi Creep control */
extern int32_T CreepLimDProp;          /* Cmi Creep control */
extern int32_T CreepLimInt;            /* Cmi Creep limiter Int */
extern int32_T CreepLimProp;           /* Cmi Creep limiter Prop */
extern uint8_T FlgEnCreepLimProp;      /* En CreepLimProp */
extern int16_T OldCmiCreepLimI;        /* Cmi Creep limiter I */
extern int16_T OldCmiCreepLimP;        /* Cmi Creep limiter P */
extern uint8_T StCreepLim;             /* Creep limiter status */
extern int16_T VehSpdCreepErr;         /* Veh speed creep error */
extern uint16_T VehSpdCreepTrg;        /* Veh speed creep target */

#endif                                 /* RTW_HEADER_creep_limiter_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
