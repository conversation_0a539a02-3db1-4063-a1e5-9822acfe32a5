/*
 * File: ExhValMgm.c
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1587
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jun 18 16:31:59 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_EXHVALMGM_

#include "ExhValMgm.h"
#include "ExhValMgm_private.h"
#include "div_s16s32_floor.h"
#include "div_s32_floor.h"
#include "mul_s32_loSR.h"

/* Named constants for Chart: '<S8>/Self_Machine' */
#define ExhValMgm_IN_SELF_HOLD_CLOSED  ((uint8_T)1U)
#define ExhValMgm_IN_SELF_HOLD_OPEN    ((uint8_T)1U)
#define ExhValMgm_IN_SELF_IDLE         ((uint8_T)1U)
#define ExhValMgm_IN_SELF_INIT         ((uint8_T)2U)
#define ExhValMgm_IN_SELF_PEAK_CLOSED  ((uint8_T)2U)
#define ExhValMgm_IN_SELF_PEAK_OPEN    ((uint8_T)2U)
#define ExhValMgm_IN_SELF_TO_CLOSED    ((uint8_T)3U)
#define ExhValMgm_IN_SELF_TO_OPEN      ((uint8_T)4U)
#define ExhValMgm_IN_SELF_WAIT_MOVE    ((uint8_T)3U)

/* Named constants for Chart: '<S9>/PbyRateSel' */
#define ExhValMgm_IN_NORMAL_RATE       ((uint8_T)1U)
#define ExhValMgm_IN_PBY_ACTION_RATE   ((uint8_T)2U)
#define ExhValMgm_IN_PBY_WAIT_EXIT_RATE ((uint8_T)3U)

/* Named constants for Chart: '<S10>/Calc_FlgExhVDiagOn' */
#define ExhValMgm_IN_DIAG_ON           ((uint8_T)1U)
#define ExhValMgm_IN_RESET             ((uint8_T)2U)

/* Named constants for Chart: '<S10>/Diag_Position' */
#define ExhValMgm_IN_STEP_1A           ((uint8_T)1U)
#define ExhValMgm_IN_STEP_1B           ((uint8_T)2U)
#define ExhValMgm_IN_STEP_1D           ((uint8_T)3U)

/* Named constants for Chart: '<S11>/Dirive_ZeroPosition' */
#define ExhVa_IN_WAIT_ZERO_POSITION_REQ ((uint8_T)2U)
#define ExhValMgm_IN_FIND_ZERO_POSITION ((uint8_T)1U)

/* Named constants for Chart: '<S94>/Calc_StFoExVSL' */
#define ExhValMgm_IN_EXV_MANUAL_IDLE   ((uint8_T)1U)
#define ExhValMgm_IN_EXV_MANUAL_POS    ((uint8_T)2U)
#define ExhValMgm_IN_EXV_MANUAL_SELF   ((uint8_T)3U)

/* Named constants for Chart: '<S135>/RecExhValve' */
#define ExhValMgm_IN_DISABLE           ((uint8_T)1U)
#define ExhValMgm_IN_ENABLE            ((uint8_T)2U)
#define ExhValMgm_IN_RETRY_ENABLE      ((uint8_T)3U)

/* Block signals and states (default storage) */
DW_ExhValMgm_T ExhValMgm_DW;

/* External outputs (root outports fed by signals with default storage) */
ExtY_ExhValMgm_T ExhValMgm_Y;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T AngExhErr;

/* exh. valve angle error */
int16_T AngExhErr1;

/* Exh. valve angle error at time step -1 */
int16_T AngExhTrg;

/* exh. valve angle target filtered */
int16_T AngExhTrg0;

/* exh. valve angle target */
int32_T AngExhValPerc;

/* valve opening angle [%] */
uint8_T CntExhVA;

/* Plausibility ok counter */
uint8_T CntExhVB;

/* Plausibility ok counter */
uint16_T CntExhVEval;

/* Time counter to evaluate diagnosis */
uint8_T CntExhVMiss;

/* Miss diagnosis */
uint8_T CntExhVWrong;

/* Plausibility error counter */
uint8_T CntReqExhSelf;

/* ExhV Self trigger counter */
uint16_T CntVOutExh;

/* counter diagnosis */
int16_T ExhVMaxTrg;

/* Near max saturation  */
int16_T ExhVMinTrg;

/* Near min saturation  */
uint32_T ExhValModuleTime;

/* Module timing counter */
uint16_T ExhvalRelTime;

/* time-history relative time */
uint8_T FlgExVPWLamp;

/* Pos WLamp */
uint8_T FlgExhVDiagOn;

/* Diagnosis flag */
uint8_T FlgExhVZeroPos;

/* flag of Active Diag to search zero position */
uint8_T FlgExhValHBEna;

/* enable flag of H-bridge driving exhaust gas valve */
uint8_T FlgSelfExhLMSDone;

/* flag to indicate the LMS self-learning has been done */
uint8_T FlgSelfExhReq;

/* flag to request self-learning of exh. valve by I/O control */
uint8_T FlgSelfExhUMSDone;

/* flag to indicate the UMS self-learning has been done */
int16_T FrzAngExhValPerc;

/* valve opening angle freezed [%] */
int16_T FrzVOutExh;

/* freeze direction diagnosis */
uint32_T IDExhValMgm;

/* ID Version */
uint8_T SelfExhVStab;

/* Stability flag */
uint8_T StDiagExhVPos;

/* status diag pos */
uint8_T StSelfExh;

/* status of the self-learning of the exh. valve */
uint8_T StSelfExhVStab;

/* Stability status */
uint8_T TrigExhVMinMaxTrg;

/* Trig min max trg calculus */
int16_T VAngExhValF;

/* angle voltage valve filtered */
int16_T VExhPID;

/* Exh. valve PID regulator output voltage at current time step */
int32_T VExhPID1;

/* Exh. valve PID regulator output voltage at time step -1 */
int32_T VExhSatPIDMax;

/* Max saturation PI */
int32_T VExhSatPIDMin;

/* Min saturation PI */
int16_T VOutExh;

/* Exh. gas valve command voltage */

/* Output and update for function-call system: '<S1>/Init' */
void ExhValMgm_Init(void)
{
  {
    /* user code (Output function Header for TID4) */

    /* System '<S1>/Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    ExhValMgm_initialize();

    /* SignalConversion generated from: '<S3>/FlgExVPWLamp' incorporates:
     *  Constant: '<S3>/ZERO2'
     */
    FlgExVPWLamp = ((uint8_T)0U);

    /* SignalConversion generated from: '<S3>/FlgExhValHBEna' incorporates:
     *  Constant: '<S3>/ZERO2'
     */
    FlgExhValHBEna = ((uint8_T)0U);

    /* SignalConversion generated from: '<S3>/FlgFbExhVZeroPos' incorporates:
     *  Constant: '<S3>/ZERO2'
     */
    FlgExhVZeroPos = ((uint8_T)0U);

    /* Constant: '<S3>/ID_EXHVALVE_MGM' */
    IDExhValMgm = ID_EXHVALVE_MGM;

    /* Constant: '<S3>/ZERO3' */
    VOutExh = 0;

    /* Constant: '<S3>/ZERO' */
    AngExhValPerc = 0;

    /* Constant: '<S3>/ZERO1' */
    AngExhTrg = 0;

    /* user code (Output function Trailer for TID4) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Output and update for function-call system: '<S18>/Subsystem' */
void ExhValMgm_Subsystem(int32_T rtu_AngExhValPerc0, int16_T rtu_ExhVMaxTrg,
  int16_T rtu_ExhVMinTrg, int32_T *rty_AngExhValPerc, int32_T
  *rty_AngExhValPercTP, boolean_T *rty_FlgExhVNearMax, boolean_T
  *rty_FlgExhVNearMin)
{
  /* Switch: '<S22>/Switch2' incorporates:
   *  Constant: '<S20>/-10'
   *  Constant: '<S20>/110'
   *  RelationalOperator: '<S22>/LowerRelop1'
   *  RelationalOperator: '<S22>/UpperRelop'
   *  Switch: '<S22>/Switch'
   */
  if (rtu_AngExhValPerc0 > ((int32_T)28160)) {
    *rty_AngExhValPerc = (int32_T)28160;
  } else if (rtu_AngExhValPerc0 < ((int32_T)(-2560))) {
    /* Switch: '<S22>/Switch' incorporates:
     *  Constant: '<S20>/-10'
     */
    *rty_AngExhValPerc = (int32_T)(-2560);
  } else {
    *rty_AngExhValPerc = rtu_AngExhValPerc0;
  }

  /* End of Switch: '<S22>/Switch2' */

  /* RelationalOperator: '<S20>/Relational Operator' incorporates:
   *  Constant: '<S20>/BKMAXVEXHOUTOPEN'
   *  Constant: '<S20>/BKMAXVEXHOUTOPEN_dim'
   *  Selector: '<S20>/Selector1'
   *  Sum: '<S20>/Add'
   */
  *rty_FlgExhVNearMax = ((*rty_AngExhValPerc) > ((int32_T)((int16_T)
    (rtu_ExhVMaxTrg - BKMAXVEXHOUTOPEN[((uint8_T)BKMAXVEXHOUTOPEN_dim)]))));

  /* SignalConversion generated from: '<S20>/AngExhValPercTP' */
  *rty_AngExhValPercTP = *rty_AngExhValPerc;

  /* RelationalOperator: '<S20>/Relational Operator1' incorporates:
   *  Constant: '<S20>/BKMAXVEXHOUTOPEN'
   *  Constant: '<S20>/BKMAXVEXHOUTOPEN_dim'
   *  Selector: '<S20>/Selector1'
   *  Sum: '<S20>/Add1'
   */
  *rty_FlgExhVNearMin = ((*rty_AngExhValPerc) < ((int32_T)((int16_T)
    (rtu_ExhVMinTrg + BKMAXVEXHOUTOPEN[((uint8_T)BKMAXVEXHOUTOPEN_dim)]))));
}

/* System initialize for function-call system: '<S18>/fc_Calc_MinMax' */
void ExhValMgm_fc_Calc_MinMax_Init(DW_fc_Calc_MinMax_ExhValMgm_T *localDW)
{
  /* SystemInitialize for Iterator SubSystem: '<S21>/TBEXHVALANGTGT_Min_Max' */
  /* SystemInitialize for Iterator SubSystem: '<S23>/Subsystem' */
  /* InitializeConditions for Memory: '<S25>/Memory' */
  localDW->Memory_PreviousInput_kjb = 25600;

  /* End of SystemInitialize for SubSystem: '<S23>/Subsystem' */
  /* End of SystemInitialize for SubSystem: '<S21>/TBEXHVALANGTGT_Min_Max' */

  /* SystemInitialize for Iterator SubSystem: '<S21>/VTEXHVANGTGTIDLE_Min_Max' */
  /* InitializeConditions for Memory: '<S24>/Memory' */
  localDW->Memory_PreviousInput = 25600;

  /* End of SystemInitialize for SubSystem: '<S21>/VTEXHVANGTGTIDLE_Min_Max' */
}

/* Output and update for function-call system: '<S18>/fc_Calc_MinMax' */
void ExhValMgm_fc_Calc_MinMax(DW_fc_Calc_MinMax_ExhValMgm_T *localDW)
{
  int16_T rtb_Memory1_cag;
  int32_T s25_iter;
  int32_T s23_iter;
  int32_T tmp;

  /* Outputs for Iterator SubSystem: '<S21>/TBEXHVALANGTGT_Min_Max' incorporates:
   *  ForIterator: '<S23>/For Iterator'
   */
  /* Constant: '<S21>/BKEXHVALCME_dim' */
  for (s23_iter = 0; s23_iter < ((int32_T)((uint8_T)(((uint32_T)((uint8_T)
            BKEXHVALCME_dim)) + 1U))); s23_iter++) {
    /* Outputs for Iterator SubSystem: '<S23>/Subsystem' incorporates:
     *  ForIterator: '<S25>/For Iterator'
     */
    /* Constant: '<S21>/BKEXHVALRPM_dim' */
    for (s25_iter = 0; s25_iter < ((int32_T)((uint8_T)(((uint32_T)((uint8_T)
              BKEXHVALRPM_dim)) + 1U))); s25_iter++) {
      /* Selector: '<S25>/Selector' incorporates:
       *  Constant: '<S25>/TBEXHVALANGTGT'
       */
      tmp = s23_iter + (5 * s25_iter);

      /* MinMax: '<S25>/MinMax' incorporates:
       *  Constant: '<S25>/TBEXHVALANGTGT'
       *  Memory: '<S25>/Memory'
       *  Selector: '<S25>/Selector'
       */
      if (localDW->Memory_PreviousInput_kjb < TBEXHVALANGTGT[tmp]) {
        localDW->MinMax_k30 = localDW->Memory_PreviousInput_kjb;
      } else {
        localDW->MinMax_k30 = TBEXHVALANGTGT[tmp];
      }

      /* End of MinMax: '<S25>/MinMax' */

      /* Memory: '<S25>/Memory1' */
      rtb_Memory1_cag = localDW->Memory1_PreviousInput_puf;

      /* MinMax: '<S25>/MinMax1' incorporates:
       *  Constant: '<S25>/TBEXHVALANGTGT'
       *  Selector: '<S25>/Selector'
       */
      if (TBEXHVALANGTGT[tmp] > rtb_Memory1_cag) {
        localDW->MinMax1_a11 = TBEXHVALANGTGT[tmp];
      } else {
        localDW->MinMax1_a11 = rtb_Memory1_cag;
      }

      /* End of MinMax: '<S25>/MinMax1' */

      /* Update for Memory: '<S25>/Memory' */
      localDW->Memory_PreviousInput_kjb = localDW->MinMax_k30;

      /* Update for Memory: '<S25>/Memory1' */
      localDW->Memory1_PreviousInput_puf = localDW->MinMax1_a11;
    }

    /* End of Outputs for SubSystem: '<S23>/Subsystem' */
  }

  /* End of Constant: '<S21>/BKEXHVALCME_dim' */
  /* End of Outputs for SubSystem: '<S21>/TBEXHVALANGTGT_Min_Max' */

  /* Outputs for Iterator SubSystem: '<S21>/VTEXHVANGTGTIDLE_Min_Max' incorporates:
   *  ForIterator: '<S24>/For Iterator'
   */
  /* Constant: '<S21>/BKEXHVALRPM_dim' */
  for (s23_iter = 0; s23_iter < ((int32_T)((uint8_T)(((uint32_T)((uint8_T)
            BKEXHVALRPM_dim)) + 1U))); s23_iter++) {
    /* MinMax: '<S24>/MinMax' incorporates:
     *  Constant: '<S24>/VTEXHVANGTGTIDLE'
     *  Memory: '<S24>/Memory'
     *  Selector: '<S24>/Selector'
     */
    if (localDW->Memory_PreviousInput < VTEXHVANGTGTIDLE[s23_iter]) {
      localDW->MinMax = localDW->Memory_PreviousInput;
    } else {
      localDW->MinMax = VTEXHVANGTGTIDLE[s23_iter];
    }

    /* End of MinMax: '<S24>/MinMax' */

    /* Memory: '<S24>/Memory1' */
    rtb_Memory1_cag = localDW->Memory1_PreviousInput;

    /* MinMax: '<S24>/MinMax1' incorporates:
     *  Constant: '<S24>/VTEXHVANGTGTIDLE'
     *  Selector: '<S24>/Selector'
     */
    if (VTEXHVANGTGTIDLE[s23_iter] > rtb_Memory1_cag) {
      localDW->MinMax1 = VTEXHVANGTGTIDLE[s23_iter];
    } else {
      localDW->MinMax1 = rtb_Memory1_cag;
    }

    /* End of MinMax: '<S24>/MinMax1' */

    /* Update for Memory: '<S24>/Memory' */
    localDW->Memory_PreviousInput = localDW->MinMax;

    /* Update for Memory: '<S24>/Memory1' */
    localDW->Memory1_PreviousInput = localDW->MinMax1;
  }

  /* End of Outputs for SubSystem: '<S21>/VTEXHVANGTGTIDLE_Min_Max' */

  /* MinMax: '<S21>/MinMax1' */
  if (localDW->MinMax1_a11 > localDW->MinMax1) {
    ExhVMaxTrg = localDW->MinMax1_a11;
  } else {
    ExhVMaxTrg = localDW->MinMax1;
  }

  /* End of MinMax: '<S21>/MinMax1' */

  /* MinMax: '<S21>/MinMax2' */
  if (localDW->MinMax_k30 < localDW->MinMax) {
    ExhVMinTrg = localDW->MinMax_k30;
  } else {
    ExhVMinTrg = localDW->MinMax;
  }

  /* End of MinMax: '<S21>/MinMax2' */
}

/* Output and update for function-call system: '<S31>/fc_ExhVSelfSteadySt' */
void ExhValMgm_fc_ExhVSelfSteadySt(int16_T rtu_VAngExhValF, uint8_T
  rtu_resetStab, DW_fc_ExhVSelfSteadySt_ExhVal_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_Conversion1_lb1;
  uint16_T rtb_Conversion2_lcv;
  uint16_T rtb_Conversion3_fks;
  uint8_T rtb_Conversion4_acc;
  uint8_T rtb_Conversion5_gfi;
  uint16_T rtb_Memory_cz4;
  uint16_T rtb_Memory1_eqt;

  /* DataTypeConversion: '<S64>/Conversion1' incorporates:
   *  Constant: '<S34>/THREXHVSLSTAB'
   *  DataTypeConversion: '<S34>/Data Type Conversion'
   */
  rtb_Conversion1_lb1 = THREXHVSLSTAB;

  /* DataTypeConversion: '<S64>/Conversion2' incorporates:
   *  DataTypeConversion: '<S34>/Conversion5'
   */
  rtb_Conversion2_lcv = (uint16_T)rtu_VAngExhValF;

  /* DataTypeConversion: '<S64>/Conversion3' incorporates:
   *  Constant: '<S34>/TIMEXHVSLSTAB'
   */
  rtb_Conversion3_fks = (uint16_T)TIMEXHVSLSTAB;

  /* DataTypeConversion: '<S64>/Conversion4' */
  rtb_Conversion4_acc = rtu_resetStab;

  /* Memory: '<S34>/Memory' */
  rtb_Conversion5_gfi = localDW->Memory_PreviousInput_h5f;

  /* Memory: '<S64>/Memory' */
  rtb_Memory_cz4 = localDW->Memory_PreviousInput;

  /* Memory: '<S64>/Memory1' */
  rtb_Memory1_eqt = localDW->Memory1_PreviousInput;

  /* S-Function (SigStab): '<S64>/SigStab' */
  SigStab( (&(SelfExhVStab)), (&(StSelfExhVStab)), &rtb_SigStab_o3,
          &rtb_SigStab_o4, rtb_Conversion2_lcv, rtb_Conversion4_acc,
          rtb_Conversion1_lb1, rtb_Conversion3_fks, rtb_Conversion5_gfi,
          rtb_Memory1_eqt, rtb_Memory_cz4);

  /* Update for Memory: '<S34>/Memory' */
  localDW->Memory_PreviousInput_h5f = StSelfExhVStab;

  /* Update for Memory: '<S64>/Memory' */
  localDW->Memory_PreviousInput = rtb_SigStab_o4;

  /* Update for Memory: '<S64>/Memory1' */
  localDW->Memory1_PreviousInput = rtb_SigStab_o3;
}

/* Output and update for function-call system: '<S31>/fc_EvaluateSelf' */
void ExhValMgm_fc_EvaluateSelf(int16_T rtu_AngExhValPerc, boolean_T
  rtu_MasterDisable, uint16_T rtu_Rpm, uint8_T rtu_FlgSelfExhLMSOnce, uint8_T
  rtu_FlgSelfExhUMSOnce, uint32_T rtu_FlgLMSTripEnable, uint32_T
  rtu_FlgUMSTripEnable, uint8_T rtu_FlgSelfExhLMSDone, uint8_T
  rtu_FlgSelfExhUMSDone, uint8_T rtu_FlgSelfExhReq, uint16_T rtu_VBattery,
  int16_T rtu_ExhVMinTrg, int16_T rtu_ExhVMaxTrg, uint8_T *rty_LMSEnSelfCond,
  uint8_T *rty_UMSEnSelfCond, boolean_T *rty_FlgExVSelfWLamp,
  DW_fc_EvaluateSelf_ExhValMgm_T *localDW)
{
  uint32_T rtb_Add1_ew1;
  boolean_T rtb_Memory1_em2;
  boolean_T rtb_LogicalOperator1_gil;
  boolean_T rtb_LogicalOperator2_btp;
  boolean_T rtb_LogicalOperator2_pnm;
  uint8_T rtb_LogicalOperator1_gkd;
  uint8_T rtb_LogicalOperator2_pkg;

  /* Sum: '<S42>/Add1' incorporates:
   *  Constant: '<S42>/Constant'
   *  Memory: '<S42>/Memory'
   */
  rtb_Add1_ew1 = 1U + localDW->Memory_PreviousInput;

  /* Logic: '<S42>/Logical Operator' incorporates:
   *  Constant: '<S42>/THVBATTEXHVDIAGEN'
   *  Constant: '<S42>/TIMPWONSELFDIS'
   *  Constant: '<S45>/Constant'
   *  RelationalOperator: '<S42>/Relational Operator1'
   *  RelationalOperator: '<S42>/Relational Operator3'
   *  RelationalOperator: '<S45>/Compare'
   */
  rtb_Memory1_em2 = (((rtu_VBattery > THVBATTEXHVDIAGEN) && (rtu_MasterDisable ==
    false)) && (((uint32_T)TIMPWONSELFDIS) <= rtb_Add1_ew1));

  /* Logic: '<S58>/Logical Operator1' incorporates:
   *  Constant: '<S58>/RPMTHRSELFEXH'
   *  Constant: '<S58>/THRANGSELFEXH'
   *  Constant: '<S60>/Constant'
   *  Constant: '<S61>/Constant'
   *  Logic: '<S58>/Logical Operator'
   *  Memory: '<S58>/Memory'
   *  RelationalOperator: '<S58>/Relational Operator'
   *  RelationalOperator: '<S58>/Relational Operator3'
   *  RelationalOperator: '<S60>/Compare'
   *  RelationalOperator: '<S61>/Compare'
   *  Sum: '<S58>/Add'
   */
  rtb_LogicalOperator1_gil = ((((((localDW->Memory_PreviousInput_gan) ||
    (rtu_AngExhValPerc < ((int16_T)(rtu_ExhVMinTrg + THRANGSELFEXH)))) &&
    (rtu_Rpm > RPMTHRSELFEXH)) && (rtu_FlgLMSTripEnable != 0U)) &&
    (rtu_FlgSelfExhLMSDone == ((uint8_T)0U))) && rtb_Memory1_em2);

  /* Logic: '<S46>/Logical Operator2' incorporates:
   *  Constant: '<S50>/Constant'
   *  Constant: '<S51>/Constant'
   *  Constant: '<S52>/Constant'
   *  Constant: '<S53>/Constant'
   *  Logic: '<S46>/Logical Operator'
   *  Memory: '<S46>/Memory'
   *  RelationalOperator: '<S50>/Compare'
   *  RelationalOperator: '<S51>/Compare'
   *  RelationalOperator: '<S52>/Compare'
   *  RelationalOperator: '<S53>/Compare'
   */
  rtb_LogicalOperator2_btp = ((((((localDW->Memory_PreviousInput_jbu) ||
    (rtu_FlgSelfExhReq != ((uint8_T)0U))) || (rtu_FlgSelfExhLMSOnce == ((uint8_T)
    0U))) && rtb_Memory1_em2) && (rtu_FlgSelfExhLMSDone == ((uint8_T)0U))) &&
    (rtu_Rpm == ((uint16_T)0U)));

  /* Switch: '<S58>/Switch' incorporates:
   *  Constant: '<S58>/Constant'
   *  Constant: '<S58>/Constant1'
   */
  if (rtb_LogicalOperator1_gil) {
    rtb_LogicalOperator1_gkd = ((uint8_T)2U);
  } else {
    rtb_LogicalOperator1_gkd = ((uint8_T)0U);
  }

  /* End of Switch: '<S58>/Switch' */

  /* Switch: '<S43>/Switch' incorporates:
   *  Constant: '<S43>/Constant'
   *  Constant: '<S43>/Constant1'
   */
  if (rtb_LogicalOperator2_btp) {
    rtb_LogicalOperator2_pkg = ((uint8_T)1U);
  } else {
    rtb_LogicalOperator2_pkg = ((uint8_T)0U);
  }

  /* End of Switch: '<S43>/Switch' */

  /* MinMax: '<S41>/MinMax' */
  if (rtb_LogicalOperator1_gkd > rtb_LogicalOperator2_pkg) {
    *rty_LMSEnSelfCond = rtb_LogicalOperator1_gkd;
  } else {
    *rty_LMSEnSelfCond = rtb_LogicalOperator2_pkg;
  }

  /* End of MinMax: '<S41>/MinMax' */

  /* Logic: '<S59>/Logical Operator2' incorporates:
   *  Constant: '<S59>/RPMTHRSELFEXH1'
   *  Constant: '<S59>/THRANGSELFEXH'
   *  Constant: '<S62>/Constant'
   *  Constant: '<S63>/Constant'
   *  Logic: '<S59>/Logical Operator'
   *  Memory: '<S59>/Memory'
   *  RelationalOperator: '<S59>/Relational Operator2'
   *  RelationalOperator: '<S59>/Relational Operator3'
   *  RelationalOperator: '<S62>/Compare'
   *  RelationalOperator: '<S63>/Compare'
   *  Sum: '<S59>/Add'
   */
  rtb_LogicalOperator2_pnm = ((((((localDW->Memory_PreviousInput_poe) ||
    (rtu_AngExhValPerc > ((int16_T)(rtu_ExhVMaxTrg - THRANGSELFEXH)))) &&
    (rtu_Rpm > RPMTHRSELFEXH)) && (rtu_FlgUMSTripEnable != 0U)) &&
    (rtu_FlgSelfExhUMSDone == ((uint8_T)0U))) && rtb_Memory1_em2);

  /* Logic: '<S47>/Logical Operator2' incorporates:
   *  Constant: '<S54>/Constant'
   *  Constant: '<S55>/Constant'
   *  Constant: '<S56>/Constant'
   *  Constant: '<S57>/Constant'
   *  Logic: '<S47>/Logical Operator'
   *  Memory: '<S47>/Memory'
   *  RelationalOperator: '<S54>/Compare'
   *  RelationalOperator: '<S55>/Compare'
   *  RelationalOperator: '<S56>/Compare'
   *  RelationalOperator: '<S57>/Compare'
   */
  rtb_Memory1_em2 = ((((((localDW->Memory_PreviousInput_lvt) ||
    (rtu_FlgSelfExhReq != ((uint8_T)0U))) || (rtu_FlgSelfExhUMSOnce == ((uint8_T)
    0U))) && rtb_Memory1_em2) && (rtu_FlgSelfExhUMSDone == ((uint8_T)0U))) &&
                     (rtu_Rpm == ((uint16_T)0U)));

  /* Switch: '<S59>/Switch' incorporates:
   *  Constant: '<S59>/Constant'
   *  Constant: '<S59>/Constant1'
   */
  if (rtb_LogicalOperator2_pnm) {
    rtb_LogicalOperator1_gkd = ((uint8_T)2U);
  } else {
    rtb_LogicalOperator1_gkd = ((uint8_T)0U);
  }

  /* End of Switch: '<S59>/Switch' */

  /* Switch: '<S43>/Switch1' incorporates:
   *  Constant: '<S43>/Constant2'
   *  Constant: '<S43>/Constant3'
   */
  if (rtb_Memory1_em2) {
    rtb_LogicalOperator2_pkg = ((uint8_T)1U);
  } else {
    rtb_LogicalOperator2_pkg = ((uint8_T)0U);
  }

  /* End of Switch: '<S43>/Switch1' */

  /* MinMax: '<S41>/MinMax1' */
  if (rtb_LogicalOperator1_gkd > rtb_LogicalOperator2_pkg) {
    *rty_UMSEnSelfCond = rtb_LogicalOperator1_gkd;
  } else {
    *rty_UMSEnSelfCond = rtb_LogicalOperator2_pkg;
  }

  /* End of MinMax: '<S41>/MinMax1' */

  /* Logic: '<S43>/Logical Operator' incorporates:
   *  Constant: '<S48>/Constant'
   *  Constant: '<S49>/Constant'
   *  Logic: '<S43>/Logical Operator1'
   *  Memory: '<S43>/Memory1'
   *  Memory: '<S43>/Memory2'
   *  RelationalOperator: '<S48>/Compare'
   *  RelationalOperator: '<S49>/Compare'
   */
  *rty_FlgExVSelfWLamp = (((rtu_FlgSelfExhLMSDone == ((uint8_T)1U)) &&
    (rtu_FlgSelfExhUMSDone == ((uint8_T)1U))) &&
    ((localDW->Memory2_PreviousInput) || (localDW->Memory1_PreviousInput)));

  /* Update for Memory: '<S42>/Memory' */
  localDW->Memory_PreviousInput = rtb_Add1_ew1;

  /* Update for Memory: '<S58>/Memory' */
  localDW->Memory_PreviousInput_gan = rtb_LogicalOperator1_gil;

  /* Update for Memory: '<S46>/Memory' */
  localDW->Memory_PreviousInput_jbu = rtb_LogicalOperator2_btp;

  /* Update for Memory: '<S59>/Memory' */
  localDW->Memory_PreviousInput_poe = rtb_LogicalOperator2_pnm;

  /* Update for Memory: '<S47>/Memory' */
  localDW->Memory_PreviousInput_lvt = rtb_Memory1_em2;

  /* Update for Memory: '<S43>/Memory2' */
  localDW->Memory2_PreviousInput = rtb_LogicalOperator2_btp;

  /* Update for Memory: '<S43>/Memory1' */
  localDW->Memory1_PreviousInput = rtb_Memory1_em2;
}

/* Output and update for function-call system: '<S10>/fc_diag_calc' */
void ExhValMgm_fc_diag_calc(uint8_T rtu_ptfault, uint8_T *rty_stDiag, uint8_T
  *rty_cntDiagCall, DW_fc_diag_calc_ExhValMgm_T *localDW)
{
  /* S-Function (DiagMgm_SetDiagState): '<S91>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S90>/DIAG_EXHVALVPOS'
   */
  DiagMgm_SetDiagState( DIAG_EXHVALVPOS, rtu_ptfault, rty_stDiag);

  /* Sum: '<S90>/Add' incorporates:
   *  Constant: '<S90>/Constant'
   *  Memory: '<S90>/Memory'
   */
  *rty_cntDiagCall = (uint8_T)(((uint32_T)((uint8_T)1U)) + ((uint32_T)
    localDW->Memory_PreviousInput));

  /* Update for Memory: '<S90>/Memory' */
  localDW->Memory_PreviousInput = *rty_cntDiagCall;
}

/* System initialize for function-call system: '<S1>/T10ms' */
void ExhValMgm_T10ms_Init(void)
{
  ExhValMgm_DW.is_active_c6_ExhValMgm = 0U;
  ExhValMgm_DW.oldTrigExhVMinMaxTrg = 0U;
  TrigExhVMinMaxTrg = 0U;

  /* SystemInitialize for Chart: '<S18>/Manage_MinMax_Trg' incorporates:
   *  SubSystem: '<S18>/fc_Calc_MinMax'
   */
  ExhValMgm_fc_Calc_MinMax_Init(&ExhValMgm_DW.fc_Calc_MinMax);

  /* SystemInitialize for Chart: '<S8>/Self_Machine' */
  ExhValMgm_DW.is_SELF_TO_CLOSED = 0;
  ExhValMgm_DW.is_SELF_TO_OPEN = 0;
  ExhValMgm_DW.is_active_c3_ExhValMgm = 0U;
  ExhValMgm_DW.is_c3_ExhValMgm = 0;
  ExhValMgm_DW.cntDrvEn = 0U;
  ExhValMgm_DW.oldCntReqExhSelf = 0U;
  ExhValMgm_DW.VOutSelf = 0;
  StSelfExh = 0U;
  FlgSelfExhLMSDone = 0U;
  FlgSelfExhUMSDone = 0U;
  FlgSelfExhReq = 0U;
  ExhValMgm_DW.resetStab = 0U;

  /* SystemInitialize for Chart: '<S9>/PbyRateSel' */
  ExhValMgm_DW.is_active_c7_ExhValMgm = 0U;
  ExhValMgm_DW.is_c7_ExhValMgm = 0;
  ExhValMgm_DW.flgPbyRate = 0U;
  ExhValMgm_DW.flgZeroTarget = 0U;

  /* SystemInitialize for Chart: '<S10>/Calc_FlgExhVDiagOn' */
  ExhValMgm_DW.is_active_c9_ExhValMgm = 0U;
  ExhValMgm_DW.is_c9_ExhValMgm = 0;
  ExhValMgm_DW.oldCntDiagCall = 0U;
  ExhValMgm_DW.oldDrivingCycle = 0U;
  FlgExhVDiagOn = 0U;

  /* SystemInitialize for Chart: '<S10>/Diag_Position' */
  ExhValMgm_DW.is_active_c10_ExhValMgm = 0U;
  ExhValMgm_DW.is_c10_ExhValMgm = 0;
  CntExhVA = 0U;
  CntExhVB = 0U;
  CntExhVEval = 0U;
  CntExhVMiss = 0U;
  CntExhVWrong = 0U;
  FrzAngExhValPerc = 0;
  FrzVOutExh = 0;
  CntVOutExh = 0U;
  StDiagExhVPos = 0U;
  ExhValMgm_DW.ptFault = 0U;

  /* SystemInitialize for Chart: '<S11>/Dirive_ZeroPosition' */
  ExhValMgm_DW.is_active_c5_ExhValMgm = 0U;
  ExhValMgm_DW.is_c5_ExhValMgm = 0;
  ExhValMgm_DW.cntZeroFind = 0U;
  ExhValMgm_DW.cntZeroPosTO = 0U;
  ExhValMgm_DW.FlgExVPWLamp_fgf = 0U;
  ExhValMgm_DW.FlgExhVZeroPos_gpr = 0U;

  /* SystemInitialize for Chart: '<S94>/Calc_StFoExVSL' */
  ExhValMgm_DW.is_active_c8_ExhValMgm = 0U;
  ExhValMgm_DW.is_c8_ExhValMgm = 0;
  ExhValMgm_DW.FlgExVManPos = 0U;
  ExhValMgm_DW.FlgExVManSelf = 0U;

  /* SystemInitialize for Chart: '<S135>/RecExhValve' */
  ExhValMgm_DW.is_active_c2_ExhValMgm = 0U;
  ExhValMgm_DW.is_c2_ExhValMgm = 0;
  ExhValMgm_DW.cntDisExhV = 0U;
  ExhValMgm_DW.cntEnExhVMotion = 0U;
  ExhValMgm_DW.enableEhxV = 0U;

  /* SystemInitialize for Chart: '<S135>/SafePosition' */
  ExhValMgm_DW.cntExhVSafePos = 0U;
  ExhValMgm_DW.enableExhVFdbk = 0U;
}

/* Start for function-call system: '<S1>/T10ms' */
void ExhValMgm_T10ms_Start(void)
{
  /* Start for Chart: '<S8>/Self_Machine' incorporates:
   *  SubSystem: '<S31>/fc_ResetCntExhVTrip'
   */

  /* Start for Chart: '<S8>/Self_Machine' incorporates:
   *  SubSystem: '<S31>/fc_IncCntExhVTrip'
   */

  /* Start for Chart: '<S8>/Self_Machine' incorporates:
   *  SubSystem: '<S31>/fc_ResetLMSOnce'
   */

  /* Start for Chart: '<S8>/Self_Machine' incorporates:
   *  SubSystem: '<S31>/fc_ResetUMSOnce'
   */

  /* Start for Chart: '<S8>/Self_Machine' incorporates:
   *  SubSystem: '<S31>/fc_WriteExhVClose'
   */

  /* Start for Chart: '<S8>/Self_Machine' incorporates:
   *  SubSystem: '<S31>/fc_WriteExhVOpen'
   */
}

/* Output and update for function-call system: '<S1>/T10ms' */
void ExhValMgm_T10ms(void)
{
  boolean_T rtb_Merge;
  uint16_T rtb_Switch2_kkx;
  uint16_T rtb_Add_ilx;
  boolean_T rtb_Compare_ojb;
  uint8_T rtb_RelationalOperator1;
  boolean_T rtb_Compare_os1;
  uint8_T rtb_LogicalOperator;
  uint32_T rtb_MinMax;
  boolean_T rtb_Switch2_j4c;
  uint8_T rtb_Add1_gt3;
  int32_T rtb_Mem;
  uint16_T rtb_Abs;
  int16_T rtb_DataTypeConversion1;
  int32_T rtb_Conversion7;
  int16_T rtb_Conversion_odt;
  int16_T rtb_Switch2_fal;
  int16_T rtb_MultiportSwitch1;
  int16_T rtb_MultiportSwitch;
  int16_T rtb_Switch_etj;
  int16_T rtb_Conversion3;
  uint16_T rtb_LookUp_U16_S16;
  int16_T rtb_Conversion2;
  uint8_T rtb_RelationalOperator8;
  int32_T rtb_Product2;
  boolean_T LogicalOperator;
  int16_T rtb_Conversion4[5];
  uint8_T rtb_Conversion6;
  uint16_T rtb_Conversion_le3[6];
  uint8_T rtb_LogicalOperator_j42;
  int16_T rtb_Reshape[30];
  int16_T rtb_Conversion1_and[6];
  int16_T rtb_Conversion1_mwl;
  int16_T rtb_Conversion2_kqm;
  uint16_T rtb_Conversion[4];
  int16_T rtb_Conversion1_nas[4];
  uint16_T rtb_Conversion1[3];
  int16_T rtb_Conversion_bcx[3];
  boolean_T RelationalOperator;
  boolean_T RelationalOperator1;
  int32_T Divide;
  int16_T DataTypeConversion1;
  int32_T Switch2;
  int32_T OutportBufferForAngExhValPercTP;
  int32_T i;
  uint32_T tmp;

  /* Switch: '<S98>/Switch' incorporates:
   *  Inport: '<Root>/StartSignal'
   *  Memory: '<S98>/Memory'
   */
  if (((int32_T)StartSignal) != 0) {
    rtb_Switch2_kkx = ExhValMgm_DW.Memory_PreviousInput_pjn;
  } else {
    rtb_Switch2_kkx = (uint16_T)StartSignal;
  }

  /* End of Switch: '<S98>/Switch' */

  /* Sum: '<S98>/Add' incorporates:
   *  Inport: '<Root>/StartSignal'
   */
  rtb_Add_ilx = (uint16_T)(((uint32_T)StartSignal) + ((uint32_T)rtb_Switch2_kkx));

  /* Logic: '<S98>/Logical Operator' incorporates:
   *  Constant: '<S100>/Constant'
   *  Constant: '<S101>/Constant'
   *  Constant: '<S98>/TIMEXVSL'
   *  Constant: '<S99>/Constant'
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/KeySignal'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/TrestleSignal'
   *  RelationalOperator: '<S100>/Compare'
   *  RelationalOperator: '<S101>/Compare'
   *  RelationalOperator: '<S98>/Relational Operator'
   *  RelationalOperator: '<S99>/Compare'
   */
  rtb_Compare_ojb = ((((((((int32_T)KeySignal) != 0) && (((int32_T)TrestleSignal)
    != 0)) && (GearPos > ((uint8_T)0U))) && (Rpm == ((uint16_T)0U))) &&
                      (ClutchSignal != ((uint8_T)0U))) && (TIMEXVSL <=
    rtb_Add_ilx));

  /* Sum: '<S94>/Add1' incorporates:
   *  Constant: '<S97>/Constant'
   *  Inport: '<Root>/GearPos'
   *  Logic: '<S94>/Logical Operator1'
   *  RelationalOperator: '<S97>/Compare'
   */
  rtb_Add1_gt3 = (uint8_T)(((uint32_T)(rtb_Compare_ojb ? 1U : 0U)) + ((uint32_T)
    ((rtb_Compare_ojb && (GearPos == ((uint8_T)2U))) ? 1 : 0)));

  /* Chart: '<S94>/Calc_StFoExVSL' */
  /* Gateway: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Calc_StFoExVSL */
  /* During: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Calc_StFoExVSL */
  if (((uint32_T)ExhValMgm_DW.is_active_c8_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Calc_StFoExVSL */
    ExhValMgm_DW.is_active_c8_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/Calc_StFoExVSL */
    /* Transition: '<S96>:2' */
    ExhValMgm_DW.FlgExVManSelf = 0U;
    ExhValMgm_DW.FlgExVManPos = 0U;
    ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_IDLE;
  } else {
    switch (ExhValMgm_DW.is_c8_ExhValMgm) {
     case ExhValMgm_IN_EXV_MANUAL_IDLE:
      /* During 'EXV_MANUAL_IDLE': '<S96>:1' */
      /* Transition: '<S96>:5' */
      switch (rtb_Add1_gt3) {
       case 1:
        /* Transition: '<S96>:6' */
        ExhValMgm_DW.FlgExVManSelf = 1U;
        ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_SELF;
        break;

       case 2:
        /* Transition: '<S96>:13' */
        /* Transition: '<S96>:15' */
        ExhValMgm_DW.FlgExVManPos = 1U;
        ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_POS;
        break;

       default:
        /* Transition: '<S96>:14' */
        break;
      }
      break;

     case ExhValMgm_IN_EXV_MANUAL_POS:
      /* During 'EXV_MANUAL_POS': '<S96>:11' */
      /* Transition: '<S96>:17' */
      if (((int32_T)rtb_Add1_gt3) == 0) {
        /* Transition: '<S96>:18' */
        ExhValMgm_DW.FlgExVManPos = 0U;
        ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_IDLE;
      } else {
        /* Transition: '<S96>:20' */
        ExhValMgm_DW.FlgExVManPos = 0U;
      }
      break;

     default:
      /* During 'EXV_MANUAL_SELF': '<S96>:3' */
      /* Transition: '<S96>:26' */
      if (((int32_T)rtb_Add1_gt3) == 0) {
        /* Transition: '<S96>:10' */
        ExhValMgm_DW.FlgExVManSelf = 0U;
        ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_IDLE;
      } else {
        /* Transition: '<S96>:9' */
        ExhValMgm_DW.FlgExVManSelf = 0U;
      }
      break;
    }
  }

  /* End of Chart: '<S94>/Calc_StFoExVSL' */

  /* Memory: '<S121>/Mem' */
  rtb_Mem = ExhValMgm_DW.Mem_PreviousInput;

  /* Chart: '<S11>/Dirive_ZeroPosition' incorporates:
   *  Constant: '<S11>/TIMEXHADFBINACTIVE'
   *  Constant: '<S11>/TIMEXHADFBTIMEOUT'
   *  DataTypeConversion: '<S113>/Data Type Conversion1'
   *  Inport: '<Root>/EnExhVZeroPos'
   *  Logic: '<S94>/Logical Operator3'
   *  Memory: '<S121>/Mem'
   */
  /* Gateway: ExhValMgm/T10ms/Drive_ActiveDiag/Dirive_ZeroPosition */
  /* During: ExhValMgm/T10ms/Drive_ActiveDiag/Dirive_ZeroPosition */
  if (((uint32_T)ExhValMgm_DW.is_active_c5_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Drive_ActiveDiag/Dirive_ZeroPosition */
    ExhValMgm_DW.is_active_c5_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Drive_ActiveDiag/Dirive_ZeroPosition */
    /* Transition: '<S93>:4' */
    ExhValMgm_DW.FlgExhVZeroPos_gpr = 0U;
    ExhValMgm_DW.FlgExVPWLamp_fgf = 0U;
    ExhValMgm_DW.is_c5_ExhValMgm = ExhVa_IN_WAIT_ZERO_POSITION_REQ;
  } else if (((uint32_T)ExhValMgm_DW.is_c5_ExhValMgm) ==
             ExhValMgm_IN_FIND_ZERO_POSITION) {
    /* During 'FIND_ZERO_POSITION': '<S93>:3' */
    /* Transition: '<S93>:12' */
    if (((int32_T)((int16_T)(ExhValMgm_DW.Mem_PreviousInput >> ((uint32_T)11))))
        <= ((int32_T)((uint32_T)(((uint32_T)((uint8_T)TH_ADFB_VPIDINACTIVE)) <<
           ((uint32_T)10))))) {
      /* Transition: '<S93>:10' */
      ExhValMgm_DW.cntZeroFind = (uint16_T)((int32_T)(((int32_T)
        ExhValMgm_DW.cntZeroFind) + 1));
    } else {
      /* Transition: '<S93>:13' */
      ExhValMgm_DW.cntZeroFind = 0U;
    }

    if ((ExhValMgm_DW.cntZeroPosTO > TIMEXHADFBTIMEOUT) ||
        (ExhValMgm_DW.cntZeroFind > TIMEXHADFBINACTIVE)) {
      /* Transition: '<S93>:8' */
      ExhValMgm_DW.FlgExhVZeroPos_gpr = 0U;
      ExhValMgm_DW.FlgExVPWLamp_fgf = (uint8_T)((ExhValMgm_DW.cntZeroPosTO <=
        TIMEXHADFBTIMEOUT) ? 1 : 0);
      ExhValMgm_DW.is_c5_ExhValMgm = ExhVa_IN_WAIT_ZERO_POSITION_REQ;
    } else {
      /* Transition: '<S93>:29' */
      ExhValMgm_DW.cntZeroPosTO = (uint16_T)((int32_T)(((int32_T)
        ExhValMgm_DW.cntZeroPosTO) + 1));
    }
  } else {
    /* During 'WAIT_ZERO_POSITION_REQ': '<S93>:1' */
    /* Transition: '<S93>:26' */
    if ((((int32_T)EnExhVZeroPos) != 0) || (((int32_T)ExhValMgm_DW.FlgExVManPos)
         != 0)) {
      /* Transition: '<S93>:7' */
      ExhValMgm_DW.FlgExhVZeroPos_gpr = 1U;
      ExhValMgm_DW.cntZeroPosTO = 0U;
      ExhValMgm_DW.cntZeroFind = 0U;
      ExhValMgm_DW.is_c5_ExhValMgm = ExhValMgm_IN_FIND_ZERO_POSITION;
    } else {
      /* Transition: '<S93>:27' */
      ExhValMgm_DW.FlgExVPWLamp_fgf = 0U;
    }
  }

  /* End of Chart: '<S11>/Dirive_ZeroPosition' */

  /* Switch: '<S15>/Switch2' incorporates:
   *  Constant: '<S15>/Constant4'
   *  Inport: '<Root>/VAngExhOpen'
   */
  if (((int32_T)ExhValMgm_DW.FlgExhVZeroPos_gpr) != 0) {
    rtb_Switch2_kkx = ((uint16_T)0U);
  } else {
    rtb_Switch2_kkx = VAngExhOpen;
  }

  /* End of Switch: '<S15>/Switch2' */

  /* Switch: '<S15>/Switch1' incorporates:
   *  Constant: '<S15>/EXHVMAXFBKACTIVEDIAG'
   *  DataTypeConversion: '<S15>/Data Type Conversion'
   *  Inport: '<Root>/VAngExhClosed'
   */
  if (((int32_T)ExhValMgm_DW.FlgExhVZeroPos_gpr) != 0) {
    rtb_Abs = EXHVMAXFBKACTIVEDIAG;
  } else {
    rtb_Abs = VAngExhClosed;
  }

  /* End of Switch: '<S15>/Switch1' */

  /* DataTypeConversion: '<S16>/Data Type Conversion1' incorporates:
   *  Inport: '<Root>/VAngExhVal'
   */
  rtb_DataTypeConversion1 = (int16_T)VAngExhVal;

  /* DataTypeConversion: '<S26>/Conversion5' */
  rtb_Conversion3 = rtb_DataTypeConversion1;

  /* DataTypeConversion: '<S26>/Conversion1' incorporates:
   *  Constant: '<S16>/KFILTVANGEXHVAL'
   */
  rtb_LookUp_U16_S16 = KFILTVANGEXHVAL;

  /* DataTypeConversion: '<S26>/Conversion2' */
  rtb_Conversion2 = rtb_DataTypeConversion1;

  /* DataTypeConversion: '<S26>/Conversion3' incorporates:
   *  Constant: '<S16>/reset'
   */
  rtb_RelationalOperator8 = ((uint8_T)0U);

  /* UnitDelay: '<S16>/Unit Delay3' */
  rtb_Product2 = ExhValMgm_DW.UnitDelay3_DSTATE;

  /* S-Function (FOF_Reset_S16_FXP): '<S26>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_Conversion3, &rtb_Product2, rtb_Conversion3,
                    rtb_LookUp_U16_S16, rtb_Conversion2, rtb_RelationalOperator8,
                    rtb_Product2);

  /* DataTypeConversion: '<S27>/Conversion' */
  VAngExhValF = rtb_Conversion3;

  /* Product: '<S17>/Divide' incorporates:
   *  Constant: '<S17>/100'
   *  Product: '<S17>/Product'
   *  Sum: '<S17>/Add1'
   *  Sum: '<S17>/Add2'
   */
  Divide = div_s32_floor(((int32_T)((int16_T)(((int32_T)VAngExhValF) - ((int32_T)
    rtb_Abs)))) * ((int32_T)25600), (int32_T)((int16_T)((int32_T)(((int32_T)
    rtb_Switch2_kkx) - ((int32_T)rtb_Abs)))));

  /* RelationalOperator: '<S17>/Relational Operator1' */
  rtb_RelationalOperator1 = (uint8_T)((rtb_Switch2_kkx >= rtb_Abs) ? 1 : 0);

  /* Chart: '<S18>/Manage_MinMax_Trg' */
  /* Gateway: ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Manage_MinMax_Trg */
  /* During: ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Manage_MinMax_Trg */
  if (((uint32_T)ExhValMgm_DW.is_active_c6_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Manage_MinMax_Trg */
    ExhValMgm_DW.is_active_c6_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Calc_Position/CalcExhValAngle/Saturator/Manage_MinMax_Trg */
    /* Transition: '<S19>:2' */
    ExhValMgm_DW.oldTrigExhVMinMaxTrg = TrigExhVMinMaxTrg;

    /* Outputs for Function Call SubSystem: '<S18>/fc_Calc_MinMax' */
    /* Event: '<S19>:9' */
    ExhValMgm_fc_Calc_MinMax(&ExhValMgm_DW.fc_Calc_MinMax);

    /* End of Outputs for SubSystem: '<S18>/fc_Calc_MinMax' */

    /* Outputs for Function Call SubSystem: '<S18>/Subsystem' */
    /* Event: '<S19>:10' */
    ExhValMgm_Subsystem(Divide, ExhVMaxTrg, ExhVMinTrg, &Switch2,
                        &OutportBufferForAngExhValPercTP, &RelationalOperator,
                        &RelationalOperator1);

    /* End of Outputs for SubSystem: '<S18>/Subsystem' */
  } else {
    /* During 'TRIG_MIN_MAX_TRG': '<S19>:1' */
    /* Transition: '<S19>:4' */
    if (TrigExhVMinMaxTrg != ExhValMgm_DW.oldTrigExhVMinMaxTrg) {
      /* Transition: '<S19>:5' */
      ExhValMgm_DW.oldTrigExhVMinMaxTrg = TrigExhVMinMaxTrg;

      /* Outputs for Function Call SubSystem: '<S18>/fc_Calc_MinMax' */
      /* Event: '<S19>:9' */
      ExhValMgm_fc_Calc_MinMax(&ExhValMgm_DW.fc_Calc_MinMax);

      /* End of Outputs for SubSystem: '<S18>/fc_Calc_MinMax' */

      /* Outputs for Function Call SubSystem: '<S18>/Subsystem' */
      /* Event: '<S19>:10' */
      ExhValMgm_Subsystem(Divide, ExhVMaxTrg, ExhVMinTrg, &Switch2,
                          &OutportBufferForAngExhValPercTP, &RelationalOperator,
                          &RelationalOperator1);

      /* End of Outputs for SubSystem: '<S18>/Subsystem' */
    } else {
      /* Outputs for Function Call SubSystem: '<S18>/Subsystem' */
      /* Transition: '<S19>:6' */
      /* Event: '<S19>:10' */
      ExhValMgm_Subsystem(Divide, ExhVMaxTrg, ExhVMinTrg, &Switch2,
                          &OutportBufferForAngExhValPercTP, &RelationalOperator,
                          &RelationalOperator1);

      /* End of Outputs for SubSystem: '<S18>/Subsystem' */
    }
  }

  /* End of Chart: '<S18>/Manage_MinMax_Trg' */

  /* DataTypeConversion: '<S15>/Data Type Conversion1' */
  DataTypeConversion1 = (int16_T)Switch2;

  /* DataTypeConversion: '<S26>/Conversion7' */
  rtb_Conversion7 = rtb_Product2;

  /* RelationalOperator: '<S32>/Compare' incorporates:
   *  Constant: '<S28>/FOEXHVSELF'
   *  Constant: '<S32>/Constant'
   */
  rtb_Compare_os1 = (FOEXHVSELF != ((uint8_T)0U));

  /* Memory: '<S28>/Memory1' */
  rtb_RelationalOperator8 = ExhValMgm_DW.Memory1_PreviousInput_pyc;

  /* Sum: '<S28>/Add' incorporates:
   *  Logic: '<S28>/Logical Operator'
   *  Logic: '<S28>/Logical Operator1'
   *  Memory: '<S28>/Memory'
   */
  CntReqExhSelf = (uint8_T)(((uint32_T)(((ExhValMgm_DW.Memory_PreviousInput_jyx
    != rtb_Compare_os1) && rtb_Compare_os1) ? 1 : 0)) + ((uint32_T)
    rtb_RelationalOperator8));

  /* Logic: '<S94>/Logical Operator2' incorporates:
   *  Inport: '<Root>/EnExhVSelf'
   */
  rtb_Compare_ojb = ((((int32_T)ExhValMgm_DW.FlgExVManSelf) != 0) || (((int32_T)
    EnExhVSelf) != 0));

  /* RelationalOperator: '<S135>/Relational Operator8' incorporates:
   *  Constant: '<S135>/DIAG_EXHVALVPOS3'
   *  Constant: '<S135>/DIAG_EXHVALVPOS5'
   *  Inport: '<Root>/StDiag'
   *  Selector: '<S135>/Selector2'
   */
  rtb_RelationalOperator8 = (uint8_T)((FAULT == StDiag[(DIAG_HBRIDGE_B)]) ? 1 :
    0);

  /* Logic: '<S135>/Logical Operator2' incorporates:
   *  Constant: '<S135>/DIAG_EXHVALVPOS1'
   *  Constant: '<S135>/DIAG_EXHVALVPOS3'
   *  Inport: '<Root>/StDiag'
   *  RelationalOperator: '<S135>/Relational Operator5'
   *  Selector: '<S135>/Selector'
   */
  rtb_Add1_gt3 = (uint8_T)(((FAULT == StDiag[(DIAG_EXHVALVPOS)]) || (((int32_T)
    rtb_RelationalOperator8) != 0)) ? 1 : 0);

  /* Chart: '<S135>/RecExhValve' */
  /* Gateway: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/RecExhValve */
  /* During: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/RecExhValve */
  if (((uint32_T)ExhValMgm_DW.is_active_c2_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/RecExhValve */
    ExhValMgm_DW.is_active_c2_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/RecExhValve */
    /* Transition: '<S137>:1' */
    ExhValMgm_DW.enableEhxV = 1U;
    ExhValMgm_DW.is_c2_ExhValMgm = ExhValMgm_IN_ENABLE;
  } else {
    switch (ExhValMgm_DW.is_c2_ExhValMgm) {
     case ExhValMgm_IN_DISABLE:
      /* During 'DISABLE': '<S137>:6' */
      /* Transition: '<S137>:7' */
      if (ExhValMgm_DW.cntDisExhV < TIMDISEXHVMOTION) {
        /* Transition: '<S137>:11' */
        ExhValMgm_DW.cntDisExhV = (uint16_T)((int32_T)(((int32_T)
          ExhValMgm_DW.cntDisExhV) + 1));
      } else {
        /* Transition: '<S137>:15' */
        ExhValMgm_DW.cntEnExhVMotion = 0U;
        ExhValMgm_DW.enableEhxV = 1U;
        ExhValMgm_DW.is_c2_ExhValMgm = ExhValMgm_IN_RETRY_ENABLE;
      }
      break;

     case ExhValMgm_IN_ENABLE:
      /* During 'ENABLE': '<S137>:3' */
      /* Transition: '<S137>:18' */
      if (((int32_T)rtb_Add1_gt3) == 1) {
        /* Transition: '<S137>:21' */
        ExhValMgm_DW.cntDisExhV = 0U;
        ExhValMgm_DW.enableEhxV = 0U;
        ExhValMgm_DW.is_c2_ExhValMgm = ExhValMgm_IN_DISABLE;
      } else {
        /* Transition: '<S137>:32' */
      }
      break;

     default:
      /* During 'RETRY_ENABLE': '<S137>:31' */
      /* Transition: '<S137>:34' */
      if (ExhValMgm_DW.cntEnExhVMotion < TIMENEXHVMOTION) {
        /* Transition: '<S137>:37' */
        ExhValMgm_DW.cntEnExhVMotion = (uint16_T)((int32_T)(((int32_T)
          ExhValMgm_DW.cntEnExhVMotion) + 1));
        if (((int32_T)rtb_Add1_gt3) == 0) {
          /* Transition: '<S137>:39' */
          ExhValMgm_DW.is_c2_ExhValMgm = ExhValMgm_IN_ENABLE;
        } else {
          /* Transition: '<S137>:41' */
        }
      } else {
        /* Transition: '<S137>:38' */
        ExhValMgm_DW.cntDisExhV = 0U;
        ExhValMgm_DW.enableEhxV = 0U;
        ExhValMgm_DW.is_c2_ExhValMgm = ExhValMgm_IN_DISABLE;
      }
      break;
    }
  }

  /* End of Chart: '<S135>/RecExhValve' */

  /* RelationalOperator: '<S135>/Relational Operator6' incorporates:
   *  Constant: '<S135>/DIAG_EXHVALVPOS2'
   *  Constant: '<S135>/DIAG_EXHVALVPOS3'
   *  Inport: '<Root>/StDiag'
   *  Selector: '<S135>/Selector1'
   */
  rtb_Add1_gt3 = (uint8_T)((FAULT == StDiag[(DIAG_EXHVALVEFDBK)]) ? 1 : 0);

  /* Chart: '<S135>/SafePosition' */
  /* Gateway: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/SafePosition */
  /* During: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/SafePosition */
  /* Entry Internal: ExhValMgm/T10ms/Recovery_ExhValve/Diag_Valve/SafePosition */
  /* Transition: '<S138>:43' */
  if (((int32_T)rtb_Add1_gt3) == 1) {
    /* Transition: '<S138>:47' */
    if (ExhValMgm_DW.cntExhVSafePos < TIMEXHTOSAFEOPEN) {
      /* Transition: '<S138>:51' */
      rtb_DataTypeConversion1 = VOUTEXHVUMSHOLD;
      ExhValMgm_DW.cntExhVSafePos = (uint16_T)((int32_T)(((int32_T)
        ExhValMgm_DW.cntExhVSafePos) + 1));

      /* Transition: '<S138>:58' */
    } else {
      /* Transition: '<S138>:53' */
      ExhValMgm_DW.enableExhVFdbk = 0U;
      rtb_DataTypeConversion1 = 0;

      /* Transition: '<S138>:56' */
    }
  } else {
    /* Transition: '<S138>:49' */
    ExhValMgm_DW.cntExhVSafePos = 0U;
    ExhValMgm_DW.enableExhVFdbk = 1U;
    rtb_DataTypeConversion1 = 0;

    /* Transition: '<S138>:54' */
    /* Transition: '<S138>:56' */
  }

  /* End of Chart: '<S135>/SafePosition' */

  /* Logic: '<S14>/Logical Operator' incorporates:
   *  Constant: '<S131>/Constant'
   *  Constant: '<S132>/Constant'
   *  Constant: '<S133>/Constant'
   *  Constant: '<S134>/Constant'
   *  Constant: '<S14>/FORCEEXHOBJ'
   *  Inport: '<Root>/KeySignal'
   *  Inport: '<Root>/S3FlgAllowStart'
   *  Inport: '<Root>/S3FlgDisL1'
   *  Logic: '<S135>/Logical Operator4'
   *  RelationalOperator: '<S131>/Compare'
   *  RelationalOperator: '<S132>/Compare'
   *  RelationalOperator: '<S133>/Compare'
   *  RelationalOperator: '<S134>/Compare'
   */
  LogicalOperator = ((((((((int32_T)ExhValMgm_DW.enableEhxV) == 0) || (((int32_T)
    ExhValMgm_DW.enableExhVFdbk) == 0)) || (KeySignal == ((uint8_T)0U))) ||
                       (S3FlgDisL1 != ((uint8_T)0U))) || (S3FlgAllowStart ==
    ((uint8_T)0U))) || (FORCEEXHOBJ == ((uint8_T)3U)));

  /* Chart: '<S8>/Self_Machine' incorporates:
   *  Inport: '<Root>/CntSelfExhTripEnable'
   *  Inport: '<Root>/FlgLMSTripEnable'
   *  Inport: '<Root>/FlgSelfExhLMSOnce'
   *  Inport: '<Root>/FlgSelfExhUMSOnce'
   *  Inport: '<Root>/FlgUMSTripEnable'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/VBattery'
   */
  /* Gateway: ExhValMgm/T10ms/Calc_Self/Self_Machine */
  /* During: ExhValMgm/T10ms/Calc_Self/Self_Machine */
  if (((uint32_T)ExhValMgm_DW.is_active_c3_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Calc_Self/Self_Machine */
    ExhValMgm_DW.is_active_c3_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Calc_Self/Self_Machine */
    /* Transition: '<S29>:2' */
    ExhValMgm_DW.VOutSelf = 0;
    FlgSelfExhLMSDone = 0U;
    FlgSelfExhUMSDone = 0U;
    FlgSelfExhReq = 0U;
    StSelfExh = ((uint8_T)SELF_INIT);
    ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SELF_INIT;
  } else {
    switch (ExhValMgm_DW.is_c3_ExhValMgm) {
     case ExhValMgm_IN_SELF_IDLE:
      /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
      /* During 'SELF_IDLE': '<S29>:1' */
      /* Transition: '<S29>:161' */
      /* Event: '<S29>:115' */
      ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
        FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable, FlgUMSTripEnable,
        FlgSelfExhLMSDone, FlgSelfExhUMSDone, FlgSelfExhReq, VBattery,
        ExhVMinTrg, ExhVMaxTrg, &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
        &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

      /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
      if (rtb_Compare_ojb || (CntReqExhSelf != ExhValMgm_DW.oldCntReqExhSelf)) {
        /* Transition: '<S29>:162' */
        ExhValMgm_DW.oldCntReqExhSelf = CntReqExhSelf;
        FlgSelfExhReq = 1U;
        FlgSelfExhLMSDone = 0U;
        FlgSelfExhUMSDone = 0U;
      } else {
        /* Transition: '<S29>:6' */
      }

      if (((int32_T)ExhValMgm_DW.MinMax) != 0) {
        /* Transition: '<S29>:8' */
        FlgSelfExhReq = 0U;

        /* Outputs for Function Call SubSystem: '<S31>/fc_ResetLMSOnce' */
        /* Event: '<S29>:63' */

        /* user code (Output function Body for TID3) */

        /* System '<S31>/fc_ResetLMSOnce' */
        FlgSelfExhLMSOnce = 1;
        FlgLMSTripEnable = 0;

        /* user code (Output function Trailer for TID3) */

        /* System '<S31>/fc_ResetLMSOnce' */

        /* PILOTAGGIO USCITE - 10ms */

        /* End of Outputs for SubSystem: '<S31>/fc_ResetLMSOnce' */
        StSelfExh = ((uint8_T)SELF_WAIT_MOVE);
        ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SELF_TO_CLOSED;
        ExhValMgm_DW.is_SELF_TO_CLOSED = ExhValMgm_IN_SELF_WAIT_MOVE;
      } else {
        /* Transition: '<S29>:10' */
        if (((int32_T)ExhValMgm_DW.MinMax1) != 0) {
          /* Transition: '<S29>:23' */
          FlgSelfExhReq = 0U;

          /* Outputs for Function Call SubSystem: '<S31>/fc_ResetUMSOnce' */
          /* Event: '<S29>:71' */

          /* user code (Output function Body for TID3) */

          /* System '<S31>/fc_ResetUMSOnce' */
          FlgSelfExhUMSOnce = 1;
          FlgUMSTripEnable = 0;

          /* user code (Output function Trailer for TID3) */

          /* System '<S31>/fc_ResetUMSOnce' */

          /* PILOTAGGIO USCITE - 10ms */

          /* End of Outputs for SubSystem: '<S31>/fc_ResetUMSOnce' */
          StSelfExh = ((uint8_T)SELF_WAIT_MOVE);
          ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SELF_TO_OPEN;
          ExhValMgm_DW.is_SELF_TO_OPEN = ExhValMgm_IN_SELF_WAIT_MOVE;
        } else {
          /* Transition: '<S29>:24' */
          ExhValMgm_DW.VOutSelf = 0;
        }
      }
      break;

     case ExhValMgm_IN_SELF_INIT:
      /* During 'SELF_INIT': '<S29>:15' */
      /* Transition: '<S29>:17' */
      if (CntSelfExhTripEnable >= ((uint16_T)THEXHSELFTRIPENABLE)) {
        /* Outputs for Function Call SubSystem: '<S31>/fc_ResetCntExhVTrip' */
        /* Transition: '<S29>:19' */
        /* Event: '<S29>:35' */

        /* user code (Output function Body for TID3) */

        /* System '<S31>/fc_ResetCntExhVTrip' */
        CntSelfExhTripEnable = 0;
        FlgLMSTripEnable = 1;
        FlgUMSTripEnable = 1;

        /* user code (Output function Trailer for TID3) */

        /* System '<S31>/fc_ResetCntExhVTrip' */

        /* PILOTAGGIO USCITE - 10ms */

        /* End of Outputs for SubSystem: '<S31>/fc_ResetCntExhVTrip' */
      } else {
        /* Outputs for Function Call SubSystem: '<S31>/fc_IncCntExhVTrip' */
        /* Transition: '<S29>:20' */
        /* Event: '<S29>:36' */

        /* user code (Output function Body for TID3) */

        /* System '<S31>/fc_IncCntExhVTrip' */
        CntSelfExhTripEnable++;

        /* user code (Output function Trailer for TID3) */

        /* System '<S31>/fc_IncCntExhVTrip' */

        /* PILOTAGGIO USCITE - 10ms */

        /* End of Outputs for SubSystem: '<S31>/fc_IncCntExhVTrip' */
      }

      /* Transition: '<S29>:21' */
      StSelfExh = ((uint8_T)SELF_IDLE);
      ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SELF_IDLE;
      break;

     case ExhValMgm_IN_SELF_TO_CLOSED:
      /* During 'SELF_TO_CLOSED': '<S29>:3' */
      if (((int32_T)ExhValMgm_DW.MinMax) == 0) {
        /* Transition: '<S29>:60' */
        ExhValMgm_DW.VOutSelf = ((int16_T)V_EXHV_WAIT);
        StSelfExh = ((uint8_T)SELF_IDLE);

        /* Exit Internal 'SELF_TO_CLOSED': '<S29>:3' */
        ExhValMgm_DW.is_SELF_TO_CLOSED = 0;
        ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SELF_IDLE;
      } else {
        switch (ExhValMgm_DW.is_SELF_TO_CLOSED) {
         case ExhValMgm_IN_SELF_HOLD_CLOSED:
          /* During 'SELF_HOLD_CLOSED': '<S29>:54' */
          /* Transition: '<S29>:56' */
          if (ExhValMgm_DW.cntDrvEn >= TIMEXHTOCLOSED) {
            /* Transition: '<S29>:57' */
            FlgSelfExhLMSDone = 2U;
            FlgSelfExhUMSDone = 2U;

            /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
            /* Event: '<S29>:115' */
            ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
              FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
              FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
              FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
              &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
              &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

            /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
            ExhValMgm_DW.VOutSelf = ((int16_T)V_EXHV_WAIT);
          } else {
            /* Outputs for Function Call SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            /* Transition: '<S29>:61' */
            /* Event: '<S29>:66' */
            ExhValMgm_fc_ExhVSelfSteadySt(VAngExhValF, ExhValMgm_DW.resetStab,
              &ExhValMgm_DW.fc_ExhVSelfSteadySt);

            /* End of Outputs for SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            if (((int32_T)SelfExhVStab) != 0) {
              /* Outputs for Function Call SubSystem: '<S31>/fc_WriteExhVClose' */
              /* Transition: '<S29>:70' */
              /* Event: '<S29>:75' */

              /* user code (Output function Body for TID3) */

              /* System '<S31>/fc_WriteExhVClose' */
              VAngExhClosed = VAngExhVal;

              /* user code (Output function Trailer for TID3) */

              /* System '<S31>/fc_WriteExhVClose' */

              /* PILOTAGGIO USCITE - 10ms */

              /* End of Outputs for SubSystem: '<S31>/fc_WriteExhVClose' */
              FlgSelfExhLMSDone = 1U;

              /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
              /* Event: '<S29>:115' */
              ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator,
                Rpm, FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
                FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
                FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
                &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
                &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

              /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
              ExhValMgm_DW.VOutSelf = ((int16_T)V_EXHV_WAIT);
            } else {
              /* Transition: '<S29>:65' */
              i = ((int32_T)ExhValMgm_DW.cntDrvEn) + 1;
              if (i > 65535) {
                i = 65535;
              }

              ExhValMgm_DW.cntDrvEn = (uint16_T)i;

              /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
              /* Event: '<S29>:115' */
              ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator,
                Rpm, FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
                FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
                FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
                &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
                &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

              /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
            }
          }
          break;

         case ExhValMgm_IN_SELF_PEAK_CLOSED:
          /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
          /* During 'SELF_PEAK_CLOSED': '<S29>:50' */
          /* Transition: '<S29>:53' */
          /* Event: '<S29>:115' */
          ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
            FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
            FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
            FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
            &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
            &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

          /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
          if ((ExhValMgm_DW.cntDrvEn >= ((uint16_T)TIMEXHVSLPEAK)) || (((int32_T)
                ExhValMgm_DW.MinMax) > 1)) {
            /* Transition: '<S29>:46' */
            ExhValMgm_DW.VOutSelf = VOUTEXHVLMSHOLD;
            ExhValMgm_DW.cntDrvEn = 0U;
            ExhValMgm_DW.resetStab = 1U;

            /* Outputs for Function Call SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            /* Event: '<S29>:66' */
            ExhValMgm_fc_ExhVSelfSteadySt(VAngExhValF, ExhValMgm_DW.resetStab,
              &ExhValMgm_DW.fc_ExhVSelfSteadySt);

            /* End of Outputs for SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            ExhValMgm_DW.resetStab = 0U;
            StSelfExh = ((uint8_T)SELF_HOLD_CLOSED);
            ExhValMgm_DW.is_SELF_TO_CLOSED = ExhValMgm_IN_SELF_HOLD_CLOSED;
          } else {
            /* Transition: '<S29>:52' */
            i = ((int32_T)ExhValMgm_DW.cntDrvEn) + 1;
            if (i > 65535) {
              i = 65535;
            }

            ExhValMgm_DW.cntDrvEn = (uint16_T)i;
          }
          break;

         default:
          /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
          /* During 'SELF_WAIT_MOVE': '<S29>:154' */
          /* Transition: '<S29>:155' */
          /* Event: '<S29>:115' */
          ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
            FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
            FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
            FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
            &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
            &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

          /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
          if (ExhValMgm_DW.cntDrvEn >= ((uint16_T)((uint8_T)TIM_LMS_EN))) {
            /* Transition: '<S29>:157' */
            i = -((int32_T)VOUTEXHVPEAK);
            if (i > 32767) {
              i = 32767;
            }

            ExhValMgm_DW.VOutSelf = (int16_T)i;
            ExhValMgm_DW.cntDrvEn = 0U;
            StSelfExh = ((uint8_T)SELF_PEAK_CLOSED);
            ExhValMgm_DW.is_SELF_TO_CLOSED = ExhValMgm_IN_SELF_PEAK_CLOSED;
          } else {
            /* Transition: '<S29>:156' */
            i = ((int32_T)ExhValMgm_DW.cntDrvEn) + 1;
            if (i > 65535) {
              i = 65535;
            }

            ExhValMgm_DW.cntDrvEn = (uint16_T)i;
          }
          break;
        }
      }
      break;

     default:
      /* During 'SELF_TO_OPEN': '<S29>:131' */
      if (((int32_T)ExhValMgm_DW.MinMax1) == 0) {
        /* Transition: '<S29>:59' */
        i = -((int32_T)((int16_T)V_EXHV_WAIT));
        if (i > 32767) {
          i = 32767;
        }

        ExhValMgm_DW.VOutSelf = (int16_T)i;
        StSelfExh = ((uint8_T)SELF_IDLE);

        /* Exit Internal 'SELF_TO_OPEN': '<S29>:131' */
        ExhValMgm_DW.is_SELF_TO_OPEN = 0;
        ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SELF_IDLE;
      } else {
        switch (ExhValMgm_DW.is_SELF_TO_OPEN) {
         case ExhValMgm_IN_SELF_HOLD_OPEN:
          /* During 'SELF_HOLD_OPEN': '<S29>:134' */
          /* Transition: '<S29>:137' */
          if (ExhValMgm_DW.cntDrvEn >= TIMEXHTOOPEN) {
            /* Transition: '<S29>:138' */
            FlgSelfExhLMSDone = 2U;
            FlgSelfExhUMSDone = 2U;

            /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
            /* Event: '<S29>:115' */
            ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
              FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
              FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
              FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
              &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
              &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

            /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
            i = -((int32_T)((int16_T)V_EXHV_WAIT));
            if (i > 32767) {
              i = 32767;
            }

            ExhValMgm_DW.VOutSelf = (int16_T)i;
          } else {
            /* Outputs for Function Call SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            /* Transition: '<S29>:139' */
            /* Event: '<S29>:66' */
            ExhValMgm_fc_ExhVSelfSteadySt(VAngExhValF, ExhValMgm_DW.resetStab,
              &ExhValMgm_DW.fc_ExhVSelfSteadySt);

            /* End of Outputs for SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            if (((int32_T)SelfExhVStab) != 0) {
              /* Outputs for Function Call SubSystem: '<S31>/fc_WriteExhVOpen' */
              /* Transition: '<S29>:140' */
              /* Event: '<S29>:76' */

              /* user code (Output function Body for TID3) */

              /* System '<S31>/fc_WriteExhVOpen' */
              VAngExhOpen = VAngExhVal;

              /* user code (Output function Trailer for TID3) */

              /* System '<S31>/fc_WriteExhVOpen' */

              /* PILOTAGGIO USCITE - 10ms */

              /* End of Outputs for SubSystem: '<S31>/fc_WriteExhVOpen' */
              FlgSelfExhUMSDone = 1U;

              /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
              /* Event: '<S29>:115' */
              ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator,
                Rpm, FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
                FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
                FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
                &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
                &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

              /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
              i = -((int32_T)((int16_T)V_EXHV_WAIT));
              if (i > 32767) {
                i = 32767;
              }

              ExhValMgm_DW.VOutSelf = (int16_T)i;
            } else {
              /* Transition: '<S29>:141' */
              i = ((int32_T)ExhValMgm_DW.cntDrvEn) + 1;
              if (i > 65535) {
                i = 65535;
              }

              ExhValMgm_DW.cntDrvEn = (uint16_T)i;

              /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
              /* Event: '<S29>:115' */
              ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator,
                Rpm, FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
                FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
                FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
                &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
                &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

              /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
            }
          }
          break;

         case ExhValMgm_IN_SELF_PEAK_OPEN:
          /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
          /* During 'SELF_PEAK_OPEN': '<S29>:142' */
          /* Transition: '<S29>:144' */
          /* Event: '<S29>:115' */
          ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
            FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
            FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
            FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
            &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
            &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

          /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
          if ((ExhValMgm_DW.cntDrvEn >= ((uint16_T)TIMEXHVSLPEAK)) || (((int32_T)
                ExhValMgm_DW.MinMax1) > 1)) {
            /* Transition: '<S29>:132' */
            ExhValMgm_DW.VOutSelf = VOUTEXHVUMSHOLD;
            ExhValMgm_DW.cntDrvEn = 0U;
            ExhValMgm_DW.resetStab = 1U;

            /* Outputs for Function Call SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            /* Event: '<S29>:66' */
            ExhValMgm_fc_ExhVSelfSteadySt(VAngExhValF, ExhValMgm_DW.resetStab,
              &ExhValMgm_DW.fc_ExhVSelfSteadySt);

            /* End of Outputs for SubSystem: '<S31>/fc_ExhVSelfSteadySt' */
            ExhValMgm_DW.resetStab = 0U;
            StSelfExh = ((uint8_T)SELF_HOLD_OPEN);
            ExhValMgm_DW.is_SELF_TO_OPEN = ExhValMgm_IN_SELF_HOLD_OPEN;
          } else {
            /* Transition: '<S29>:145' */
            i = ((int32_T)ExhValMgm_DW.cntDrvEn) + 1;
            if (i > 65535) {
              i = 65535;
            }

            ExhValMgm_DW.cntDrvEn = (uint16_T)i;
          }
          break;

         default:
          /* Outputs for Function Call SubSystem: '<S31>/fc_EvaluateSelf' */
          /* During 'SELF_WAIT_MOVE': '<S29>:148' */
          /* Transition: '<S29>:150' */
          /* Event: '<S29>:115' */
          ExhValMgm_fc_EvaluateSelf(DataTypeConversion1, LogicalOperator, Rpm,
            FlgSelfExhLMSOnce, FlgSelfExhUMSOnce, FlgLMSTripEnable,
            FlgUMSTripEnable, FlgSelfExhLMSDone, FlgSelfExhUMSDone,
            FlgSelfExhReq, VBattery, ExhVMinTrg, ExhVMaxTrg,
            &ExhValMgm_DW.MinMax, &ExhValMgm_DW.MinMax1,
            &ExhValMgm_DW.LogicalOperator_hoi, &ExhValMgm_DW.fc_EvaluateSelf);

          /* End of Outputs for SubSystem: '<S31>/fc_EvaluateSelf' */
          if (ExhValMgm_DW.cntDrvEn >= ((uint16_T)((uint8_T)TIM_UMS_EN))) {
            /* Transition: '<S29>:151' */
            ExhValMgm_DW.VOutSelf = VOUTEXHVPEAK;
            ExhValMgm_DW.cntDrvEn = 0U;
            StSelfExh = ((uint8_T)SELF_PEAK_OPEN);
            ExhValMgm_DW.is_SELF_TO_OPEN = ExhValMgm_IN_SELF_PEAK_OPEN;
          } else {
            /* Transition: '<S29>:152' */
            i = ((int32_T)ExhValMgm_DW.cntDrvEn) + 1;
            if (i > 65535) {
              i = 65535;
            }

            ExhValMgm_DW.cntDrvEn = (uint16_T)i;
          }
          break;
        }
      }
      break;
    }
  }

  /* End of Chart: '<S8>/Self_Machine' */

  /* Logic: '<S30>/Logical Operator' */
  FlgExVPWLamp = (uint8_T)(((((int32_T)ExhValMgm_DW.FlgExVPWLamp_fgf) != 0) ||
    (ExhValMgm_DW.LogicalOperator_hoi)) ? 1 : 0);

  /* Chart: '<S9>/PbyRateSel' incorporates:
   *  Inport: '<Root>/StPassBy'
   *  UnitDelay: '<S70>/Unit Delay1'
   *  UnitDelay: '<S70>/Unit Delay3'
   */
  /* Gateway: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
  /* During: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
  if (((uint32_T)ExhValMgm_DW.is_active_c7_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
    ExhValMgm_DW.is_active_c7_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
    /* Transition: '<S69>:5' */
    ExhValMgm_DW.flgZeroTarget = 0U;
    ExhValMgm_DW.flgPbyRate = 0U;
    ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_NORMAL_RATE;
  } else {
    switch (ExhValMgm_DW.is_c7_ExhValMgm) {
     case ExhValMgm_IN_NORMAL_RATE:
      /* During 'NORMAL_RATE': '<S69>:1' */
      if ((StPassBy == WAIT_WOT_PBY) || (StPassBy == ACT_PBY)) {
        /* Transition: '<S69>:4' */
        ExhValMgm_DW.flgZeroTarget = 1U;
        ExhValMgm_DW.flgPbyRate = 1U;
        ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_PBY_ACTION_RATE;
      }
      break;

     case ExhValMgm_IN_PBY_ACTION_RATE:
      /* During 'PBY_ACTION_RATE': '<S69>:2' */
      if ((StPassBy == INIT_PBY) || (StPassBy == DISABLE_PBY)) {
        /* Transition: '<S69>:13' */
        ExhValMgm_DW.flgZeroTarget = 0U;
        ExhValMgm_DW.flgPbyRate = 1U;
        ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_PBY_WAIT_EXIT_RATE;
      } else {
        /* Transition: '<S69>:8' */
        if ((StPassBy == EXIT_ACT_PBY) || (StPassBy == WAIT_INIT_PBY)) {
          /* Transition: '<S69>:11' */
          ExhValMgm_DW.flgZeroTarget = 0U;
        } else {
          /* Transition: '<S69>:12' */
        }
      }
      break;

     default:
      /* During 'PBY_WAIT_EXIT_RATE': '<S69>:3' */
      if (ExhValMgm_DW.UnitDelay1_DSTATE >= ExhValMgm_DW.UnitDelay3_DSTATE_oui)
      {
        /* Transition: '<S69>:15' */
        ExhValMgm_DW.flgZeroTarget = 0U;
        ExhValMgm_DW.flgPbyRate = 0U;
        ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_NORMAL_RATE;
      }
      break;
    }
  }

  /* End of Chart: '<S9>/PbyRateSel' */

  /* DataTypeConversion: '<S84>/Conversion5' incorporates:
   *  Constant: '<S71>/TBEXHVALANGTGT'
   */
  memcpy(&rtb_Reshape[0], &TBEXHVALANGTGT[0], 30U * (sizeof(int16_T)));

  /* DataTypeConversion: '<S84>/Conversion3' incorporates:
   *  Inport: '<Root>/CmeDriver'
   */
  rtb_Conversion2 = CmeDriver;

  /* DataTypeConversion: '<S84>/Conversion4' incorporates:
   *  Constant: '<S71>/BKEXHVALCME'
   */
  for (i = 0; i < 5; i++) {
    rtb_Conversion4[i] = BKEXHVALCME[i];
  }

  /* End of DataTypeConversion: '<S84>/Conversion4' */

  /* DataTypeConversion: '<S84>/Conversion6' incorporates:
   *  Constant: '<S71>/BKEXHVALCME_dim'
   */
  rtb_Conversion6 = ((uint8_T)BKEXHVALCME_dim);

  /* DataTypeConversion: '<S84>/Conversion2' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_LookUp_U16_S16 = Rpm;

  /* DataTypeConversion: '<S84>/Conversion1' incorporates:
   *  Constant: '<S71>/BKEXHVALRPM'
   */
  for (i = 0; i < 6; i++) {
    rtb_Conversion_le3[i] = BKEXHVALRPM[i];
  }

  /* End of DataTypeConversion: '<S84>/Conversion1' */

  /* DataTypeConversion: '<S84>/Conversion7' incorporates:
   *  Constant: '<S71>/BKEXHVALRPM_dim'
   */
  rtb_LogicalOperator_j42 = ((uint8_T)BKEXHVALRPM_dim);

  /* S-Function (Look2D_S16_S16_U16): '<S84>/Look2D_S16_S16_U16' */
  Look2D_S16_S16_U16( &rtb_Conversion2, &rtb_Reshape[0], rtb_Conversion2,
                     &rtb_Conversion4[0], rtb_Conversion6, rtb_LookUp_U16_S16,
                     &rtb_Conversion_le3[0], rtb_LogicalOperator_j42);

  /* DataTypeConversion: '<S86>/Conversion' */
  rtb_Conversion_odt = rtb_Conversion2;

  /* Logic: '<S68>/Logical Operator' incorporates:
   *  Constant: '<S77>/Constant'
   *  Constant: '<S78>/Constant'
   *  Inport: '<Root>/FlgVehStop'
   *  Inport: '<Root>/GearPos'
   *  RelationalOperator: '<S77>/Compare'
   *  RelationalOperator: '<S78>/Compare'
   */
  rtb_LogicalOperator = (uint8_T)(((GearPos != ((uint8_T)0U)) || (FlgVehStop ==
    ((uint8_T)0U))) ? 1 : 0);

  /* DataTypeConversion: '<S85>/Conversion2' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_LookUp_U16_S16 = Rpm;
  for (i = 0; i < 6; i++) {
    /* DataTypeConversion: '<S85>/Conversion1' incorporates:
     *  Constant: '<S71>/VTEXHVANGTGTIDLE'
     */
    rtb_Conversion1_and[i] = VTEXHVANGTGTIDLE[i];

    /* DataTypeConversion: '<S85>/Conversion' incorporates:
     *  Constant: '<S71>/BKEXHVALRPM'
     *  DataTypeConversion: '<S85>/Conversion1'
     */
    rtb_Conversion_le3[i] = BKEXHVALRPM[i];
  }

  /* DataTypeConversion: '<S85>/Conversion3' incorporates:
   *  Constant: '<S71>/BKEXHVALRPM_dim'
   */
  rtb_LogicalOperator_j42 = ((uint8_T)BKEXHVALRPM_dim);

  /* S-Function (LookUp_S16_U16): '<S85>/LookUp_S16_U16' */
  LookUp_S16_U16( &rtb_Conversion2, &rtb_Conversion1_and[0], rtb_LookUp_U16_S16,
                 &rtb_Conversion_le3[0], rtb_LogicalOperator_j42);

  /* MultiPortSwitch: '<S9>/Multiport Switch2' incorporates:
   *  Constant: '<S9>/[V]'
   */
  if (((int32_T)ExhValMgm_DW.flgZeroTarget) == 0) {
    /* Switch: '<S9>/Switch' incorporates:
     *  Constant: '<S136>/DIAG_GEAR_SENSOR'
     *  Constant: '<S136>/DIAG_VEHSPEED'
     *  Constant: '<S136>/FAULT'
     *  DataTypeConversion: '<S87>/Conversion'
     *  Inport: '<Root>/StDiag'
     *  Logic: '<S136>/Logical Operator2'
     *  RelationalOperator: '<S136>/Relational Operator5'
     *  RelationalOperator: '<S136>/Relational Operator6'
     *  Selector: '<S136>/Selector'
     *  Selector: '<S136>/Selector1'
     *  Switch: '<S9>/Switch1'
     */
    if ((FAULT == StDiag[(DIAG_VEHSPEED)]) || (FAULT == StDiag[(DIAG_GEAR_SENSOR)]))
    {
      AngExhTrg0 = rtb_Conversion_odt;
    } else if (((int32_T)rtb_LogicalOperator) != 0) {
      /* Switch: '<S9>/Switch1' */
      AngExhTrg0 = rtb_Conversion_odt;
    } else {
      AngExhTrg0 = rtb_Conversion2;
    }

    /* End of Switch: '<S9>/Switch' */
  } else {
    AngExhTrg0 = 0;
  }

  /* End of MultiPortSwitch: '<S9>/Multiport Switch2' */

  /* DataTypeConversion: '<S80>/Conversion' */
  rtb_Conversion2 = AngExhTrg0;

  /* DataTypeConversion: '<S80>/Conversion1' incorporates:
   *  UnitDelay: '<S70>/Unit Delay1'
   */
  rtb_Conversion3 = ExhValMgm_DW.UnitDelay1_DSTATE;

  /* DataTypeConversion: '<S80>/Conversion2' incorporates:
   *  Constant: '<S70>/RATEANGTGRMIN'
   */
  rtb_Conversion1_mwl = RATEANGTGRMIN;

  /* DataTypeConversion: '<S80>/Conversion3' incorporates:
   *  Constant: '<S70>/RATEANGTGRPBYMAX'
   */
  rtb_Conversion2_kqm = RATEANGTGRPBYMAX;

  /* S-Function (RateLimiter_S16): '<S80>/RateLimiter_S16' */
  RateLimiter_S16( &rtb_Conversion2, rtb_Conversion2, rtb_Conversion3,
                  rtb_Conversion1_mwl, rtb_Conversion2_kqm);

  /* DataTypeConversion: '<S82>/Conversion' */
  rtb_Conversion_odt = rtb_Conversion2;

  /* DataTypeConversion: '<S81>/Conversion' */
  rtb_Conversion2_kqm = AngExhTrg0;

  /* DataTypeConversion: '<S81>/Conversion1' incorporates:
   *  UnitDelay: '<S70>/Unit Delay3'
   */
  rtb_Conversion1_mwl = ExhValMgm_DW.UnitDelay3_DSTATE_oui;

  /* DataTypeConversion: '<S81>/Conversion2' incorporates:
   *  Constant: '<S70>/RATEANGTGRMIN'
   */
  rtb_Conversion2 = RATEANGTGRMIN;

  /* DataTypeConversion: '<S81>/Conversion3' incorporates:
   *  Constant: '<S70>/RATEANGTGRMAX'
   */
  rtb_Conversion3 = RATEANGTGRMAX;

  /* S-Function (RateLimiter_S16): '<S81>/RateLimiter_S16' */
  RateLimiter_S16( &rtb_Conversion2_kqm, rtb_Conversion2_kqm,
                  rtb_Conversion1_mwl, rtb_Conversion2, rtb_Conversion3);

  /* Update for UnitDelay: '<S70>/Unit Delay3' incorporates:
   *  DataTypeConversion: '<S83>/Conversion'
   */
  ExhValMgm_DW.UnitDelay3_DSTATE_oui = rtb_Conversion2_kqm;

  /* Switch: '<S70>/Switch2' incorporates:
   *  DataTypeConversion: '<S83>/Conversion'
   */
  if (((int32_T)ExhValMgm_DW.flgPbyRate) != 0) {
    rtb_Switch2_fal = rtb_Conversion_odt;
  } else {
    rtb_Switch2_fal = rtb_Conversion2_kqm;
  }

  /* End of Switch: '<S70>/Switch2' */

  /* Sum: '<S74>/Add' incorporates:
   *  Constant: '<S74>/ONE'
   *  Memory: '<S74>/Memory'
   */
  ExhValModuleTime = 1U + ExhValMgm_DW.Memory_PreviousInput;

  /* Sum: '<S72>/Add' incorporates:
   *  Memory: '<S72>/Memory'
   */
  ExhvalRelTime = (uint16_T)((int32_T)(((int32_T)ExhValModuleTime) - ((int32_T)
    ExhValMgm_DW.Memory_PreviousInput_do4)));
  for (i = 0; i < 4; i++) {
    /* DataTypeConversion: '<S75>/Conversion' incorporates:
     *  Constant: '<S66>/BKEXHVALTIME'
     */
    rtb_Conversion[i] = BKEXHVALTIME[i];

    /* DataTypeConversion: '<S75>/Conversion1' incorporates:
     *  Constant: '<S66>/VTEXHVALWAVE'
     *  DataTypeConversion: '<S75>/Conversion'
     */
    rtb_Conversion1_nas[i] = VTEXHVALWAVE[i];
  }

  /* DataTypeConversion: '<S75>/Conversion2' */
  rtb_LookUp_U16_S16 = ExhvalRelTime;

  /* DataTypeConversion: '<S75>/Conversion3' incorporates:
   *  Constant: '<S66>/BKEXHVALTIME_dim'
   */
  rtb_LogicalOperator_j42 = ((uint8_T)BKEXHVALTIME_dim);

  /* S-Function (LookUp_S16_U16): '<S75>/LookUp_S16_U16' */
  LookUp_S16_U16( &rtb_Conversion2_kqm, &rtb_Conversion1_nas[0],
                 rtb_LookUp_U16_S16, &rtb_Conversion[0], rtb_LogicalOperator_j42);

  /* MultiPortSwitch: '<S67>/Multiport Switch1' incorporates:
   *  Constant: '<S67>/FORCEEXHOBJ'
   *  Constant: '<S67>/[V]'
   *  DataTypeConversion: '<S76>/Conversion'
   */
  switch (FORCEEXHOBJ) {
   case 0:
    /* Switch: '<S65>/Switch2' incorporates:
     *  ArithShift: '<S79>/Shift Arithmetic'
     *  Constant: '<S65>/100'
     *  Constant: '<S65>/DUAL'
     *  Constant: '<S65>/EXHVALFORCEVPOS'
     *  Memory: '<S79>/Memory'
     *  Memory: '<S79>/Memory1'
     *  Memory: '<S79>/Memory2'
     *  Product: '<S65>/Divide1'
     *  Product: '<S65>/Product1'
     *  Sum: '<S65>/Add'
     *  Sum: '<S79>/Add'
     */
    if (((int32_T)ExhValMgm_DW.FlgExhVZeroPos_gpr) != 0) {
      /* MinMax: '<S65>/MinMax' incorporates:
       *  Constant: '<S65>/EXHVALFORCEVPOS'
       *  Constant: '<S65>/EXHVMAXFBKACTIVEDIAG'
       */
      if (EXHVALFORCEVPOS > EXHVMAXFBKACTIVEDIAG) {
        rtb_Switch2_kkx = EXHVALFORCEVPOS;
      } else {
        rtb_Switch2_kkx = EXHVMAXFBKACTIVEDIAG;
      }

      /* End of MinMax: '<S65>/MinMax' */
      rtb_MultiportSwitch1 = (int16_T)(25600 - div_s16s32_floor((((int32_T)
        EXHVALFORCEVPOS) * ((int32_T)100)) << ((uint32_T)8), (int32_T)
        rtb_Switch2_kkx));
    } else {
      rtb_MultiportSwitch1 = (int16_T)((((ExhValMgm_DW.Memory2_PreviousInput +
        ExhValMgm_DW.Memory1_PreviousInput) +
        ExhValMgm_DW.Memory_PreviousInput_f5m) + rtb_Switch2_fal) >> ((uint32_T)
        2));
    }

    /* End of Switch: '<S65>/Switch2' */
    break;

   case 1:
    rtb_MultiportSwitch1 = 0;
    break;

   case 2:
    rtb_MultiportSwitch1 = rtb_Conversion2_kqm;
    break;

   default:
    rtb_MultiportSwitch1 = 0;
    break;
  }

  /* End of MultiPortSwitch: '<S67>/Multiport Switch1' */

  /* RelationalOperator: '<S95>/Compare' incorporates:
   *  Constant: '<S95>/Constant'
   *  Inport: '<Root>/EnExhVMoving'
   */
  rtb_Compare_ojb = (EnExhVMoving != ((uint8_T)0U));

  /* DataTypeConversion: '<S125>/Conversion1' incorporates:
   *  Constant: '<S122>/VTMAXVEXHOUTOPEN'
   */
  for (i = 0; i < 3; i++) {
    rtb_Conversion1[i] = VTMAXVEXHOUTOPEN[i];
  }

  /* End of DataTypeConversion: '<S125>/Conversion1' */

  /* Switch: '<S111>/Switch3' incorporates:
   *  Constant: '<S111>/EXHERRTHRHIGH'
   *  Constant: '<S111>/EXHERRTHRLOW'
   *  UnitDelay: '<S111>/Unit Delay'
   */
  if (((int32_T)ExhValMgm_DW.UnitDelay_DSTATE) != 0) {
    rtb_LookUp_U16_S16 = EXHERRTHRHIGH;
  } else {
    rtb_LookUp_U16_S16 = EXHERRTHRLOW;
  }

  /* End of Switch: '<S111>/Switch3' */

  /* If: '<S112>/If' incorporates:
   *  Constant: '<S116>/FORCEEXHOBJ'
   *  Constant: '<S117>/FORCEEXHOBJ'
   *  Constant: '<S118>/Constant'
   *  Constant: '<S119>/Constant'
   *  Constant: '<S120>/Constant'
   *  Inport: '<Root>/Rpm'
   *  Logic: '<S117>/Logical Operator3'
   *  RelationalOperator: '<S118>/Compare'
   *  RelationalOperator: '<S119>/Compare'
   *  RelationalOperator: '<S120>/Compare'
   */
  if (((int32_T)Rpm) == 0) {
    /* Outputs for IfAction SubSystem: '<S112>/if' incorporates:
     *  ActionPort: '<S117>/Action Port'
     */
    rtb_Switch2_j4c = ((FORCEEXHOBJ != ((uint8_T)2U)) &&
                       (ExhValMgm_DW.FlgExhVZeroPos_gpr == ((uint8_T)0U)));

    /* End of Outputs for SubSystem: '<S112>/if' */
  } else {
    /* Outputs for IfAction SubSystem: '<S112>/else' incorporates:
     *  ActionPort: '<S116>/Action Port'
     */
    rtb_Switch2_j4c = (FORCEEXHOBJ != ((uint8_T)0U));

    /* End of Outputs for SubSystem: '<S112>/else' */
  }

  /* End of If: '<S112>/If' */

  /* Logic: '<S112>/Logical Operator' incorporates:
   *  Constant: '<S114>/Constant'
   *  Constant: '<S115>/Constant'
   *  RelationalOperator: '<S114>/Compare'
   *  RelationalOperator: '<S115>/Compare'
   */
  rtb_Switch2_j4c = (((((rtb_Switch2_j4c || LogicalOperator) || (((int32_T)
    rtb_RelationalOperator1) != 0)) || rtb_Compare_ojb) ||
                      (rtb_DataTypeConversion1 != 0)) || (ExhValMgm_DW.VOutSelf
    != 0));

  /* Switch: '<S111>/Switch' incorporates:
   *  Constant: '<S111>/boolean2'
   *  Sum: '<S111>/Add1'
   */
  if (rtb_Switch2_j4c) {
    rtb_Conversion2_kqm = 0;
  } else {
    rtb_Conversion2_kqm = (int16_T)(rtb_MultiportSwitch1 - DataTypeConversion1);
  }

  /* End of Switch: '<S111>/Switch' */

  /* Abs: '<S111>/Abs' */
  if (rtb_Conversion2_kqm < 0) {
    rtb_Switch2_kkx = (uint16_T)((int32_T)(-((int32_T)rtb_Conversion2_kqm)));
  } else {
    rtb_Switch2_kkx = (uint16_T)rtb_Conversion2_kqm;
  }

  /* End of Abs: '<S111>/Abs' */

  /* RelationalOperator: '<S111>/Relational Operator' */
  rtb_RelationalOperator1 = (uint8_T)((rtb_LookUp_U16_S16 > rtb_Switch2_kkx) ? 1
    : 0);

  /* Switch: '<S111>/Switch1' incorporates:
   *  Constant: '<S111>/boolean1'
   */
  if (((int32_T)rtb_RelationalOperator1) != 0) {
    AngExhErr = 0;
  } else {
    AngExhErr = rtb_Conversion2_kqm;
  }

  /* End of Switch: '<S111>/Switch1' */

  /* DataTypeConversion: '<S125>/Conversion2' */
  rtb_Conversion2_kqm = AngExhErr;

  /* DataTypeConversion: '<S125>/Conversion' incorporates:
   *  Constant: '<S122>/BKMAXVEXHOUTOPEN'
   */
  for (i = 0; i < 3; i++) {
    rtb_Conversion_bcx[i] = BKMAXVEXHOUTOPEN[i];
  }

  /* End of DataTypeConversion: '<S125>/Conversion' */

  /* DataTypeConversion: '<S125>/Conversion3' incorporates:
   *  Constant: '<S122>/BKMAXVEXHOUTOPEN_dim'
   */
  rtb_LogicalOperator_j42 = ((uint8_T)BKMAXVEXHOUTOPEN_dim);

  /* S-Function (LookUp_U16_S16): '<S125>/LookUp_U16_S16' */
  LookUp_U16_S16( &rtb_LookUp_U16_S16, &rtb_Conversion1[0], rtb_Conversion2_kqm,
                 &rtb_Conversion_bcx[0], rtb_LogicalOperator_j42);

  /* Switch: '<S122>/Switch3' incorporates:
   *  Constant: '<S122>/BKMAXVEXHOUTOPEN_dim'
   *  Constant: '<S122>/VTMAXVEXHOUTOPEN'
   *  Selector: '<S122>/Selector1'
   */
  if (RelationalOperator) {
    /* Switch: '<S122>/Switch1' incorporates:
     *  Constant: '<S122>/GNVEXHOUTIDLE'
     *  DataTypeConversion: '<S128>/Conversion'
     *  Product: '<S122>/Product'
     */
    if (((int32_T)rtb_LogicalOperator) != 0) {
      rtb_MinMax = (((uint32_T)rtb_LookUp_U16_S16) << ((uint32_T)5));
    } else {
      rtb_MinMax = ((uint32_T)rtb_LookUp_U16_S16) * ((uint32_T)GNVEXHOUTIDLE);
    }

    /* End of Switch: '<S122>/Switch1' */
  } else {
    rtb_MinMax = (((uint32_T)VTMAXVEXHOUTOPEN[((uint8_T)BKMAXVEXHOUTOPEN_dim)]) <<
                  ((uint32_T)5));
  }

  /* End of Switch: '<S122>/Switch3' */

  /* MinMax: '<S127>/MinMax1' incorporates:
   *  Inport: '<Root>/VBattery'
   *  MinMax: '<S127>/MinMax'
   */
  tmp = (((uint32_T)VBattery) << ((uint32_T)5));
  if (rtb_MinMax < tmp) {
    /* DataTypeConversion: '<S127>/Data Type Conversion1' */
    VExhSatPIDMax = (((int32_T)rtb_MinMax) << ((uint32_T)12));
  } else {
    /* DataTypeConversion: '<S127>/Data Type Conversion1' */
    VExhSatPIDMax = (((int32_T)tmp) << ((uint32_T)12));
  }

  /* End of MinMax: '<S127>/MinMax1' */

  /* Switch: '<S111>/Switch2' incorporates:
   *  Constant: '<S111>/SELEXHVZEROPI'
   */
  if (((int32_T)SELEXHVZEROPI) != 0) {
    rtb_Switch2_j4c = (((int32_T)rtb_RelationalOperator1) != 0);
  }

  /* End of Switch: '<S111>/Switch2' */

  /* Product: '<S121>/Product' incorporates:
   *  Constant: '<S124>/Constant'
   *  RelationalOperator: '<S124>/Compare'
   */
  VExhPID1 = (rtb_Switch2_j4c == false) ? rtb_Mem : 0;

  /* Sum: '<S113>/Sum6' incorporates:
   *  Constant: '<S113>/PIDEXHKI'
   *  Constant: '<S113>/PIDEXHKP'
   */
  rtb_MinMax = ((uint32_T)PIDEXHKI) + ((uint32_T)PIDEXHKP);
  if (rtb_MinMax > 65535U) {
    rtb_MinMax = 65535U;
  }

  rtb_LookUp_U16_S16 = (uint16_T)rtb_MinMax;

  /* End of Sum: '<S113>/Sum6' */

  /* Product: '<S113>/Product2' */
  rtb_Product2 = ((int32_T)AngExhErr) * ((int32_T)rtb_LookUp_U16_S16);

  /* Memory: '<S123>/Mem' */
  rtb_Conversion2_kqm = ExhValMgm_DW.Mem_PreviousInput_ixw;

  /* Switch: '<S123>/Switch' incorporates:
   *  Constant: '<S123>/Constant'
   */
  if (rtb_Switch2_j4c) {
    AngExhErr1 = 0;
  } else {
    AngExhErr1 = rtb_Conversion2_kqm;
  }

  /* End of Switch: '<S123>/Switch' */

  /* Sum: '<S113>/Sum5' incorporates:
   *  Constant: '<S113>/PIDEXHKP'
   *  Product: '<S113>/Product1'
   *  Sum: '<S113>/Sum4'
   */
  rtb_Mem = (VExhPID1 + rtb_Product2) - (((int32_T)AngExhErr1) * ((int32_T)
    PIDEXHKP));

  /* DataTypeConversion: '<S126>/Conversion2' incorporates:
   *  Gain: '<S122>/Gain1'
   */
  rtb_Conversion2_kqm = (int16_T)(((-32768) * AngExhErr) >> ((uint32_T)15));
  for (i = 0; i < 3; i++) {
    /* DataTypeConversion: '<S126>/Conversion1' incorporates:
     *  Constant: '<S122>/VTMAXVEXHOUTCLOSED'
     */
    rtb_Conversion1[i] = VTMAXVEXHOUTCLOSED[i];

    /* DataTypeConversion: '<S126>/Conversion' incorporates:
     *  Constant: '<S122>/BKMAXVEXHOUTOPEN'
     *  DataTypeConversion: '<S126>/Conversion1'
     */
    rtb_Conversion_bcx[i] = BKMAXVEXHOUTOPEN[i];
  }

  /* DataTypeConversion: '<S126>/Conversion3' incorporates:
   *  Constant: '<S122>/BKMAXVEXHOUTOPEN_dim'
   */
  rtb_LogicalOperator_j42 = ((uint8_T)BKMAXVEXHOUTOPEN_dim);

  /* S-Function (LookUp_U16_S16): '<S126>/LookUp_U16_S16' */
  LookUp_U16_S16( &rtb_LookUp_U16_S16, &rtb_Conversion1[0], rtb_Conversion2_kqm,
                 &rtb_Conversion_bcx[0], rtb_LogicalOperator_j42);

  /* Switch: '<S122>/Switch4' incorporates:
   *  Constant: '<S122>/BKMAXVEXHOUTOPEN_dim'
   *  Constant: '<S122>/VTMAXVEXHOUTCLOSED'
   *  Selector: '<S122>/Selector'
   */
  if (RelationalOperator1) {
    /* Switch: '<S122>/Switch2' incorporates:
     *  Constant: '<S122>/GNVEXHOUTIDLE'
     *  DataTypeConversion: '<S129>/Conversion'
     *  Product: '<S122>/Product1'
     */
    if (((int32_T)rtb_LogicalOperator) != 0) {
      rtb_MinMax = (((uint32_T)rtb_LookUp_U16_S16) << ((uint32_T)5));
    } else {
      rtb_MinMax = ((uint32_T)GNVEXHOUTIDLE) * ((uint32_T)rtb_LookUp_U16_S16);
    }

    /* End of Switch: '<S122>/Switch2' */
  } else {
    rtb_MinMax = (((uint32_T)VTMAXVEXHOUTCLOSED[((uint8_T)BKMAXVEXHOUTOPEN_dim)])
                  << ((uint32_T)5));
  }

  /* End of Switch: '<S122>/Switch4' */

  /* MinMax: '<S127>/MinMax' */
  if (tmp < rtb_MinMax) {
    rtb_MinMax = tmp;
  }

  /* Gain: '<S127>/Gain' incorporates:
   *  DataTypeConversion: '<S127>/Data Type Conversion2'
   */
  VExhSatPIDMin = mul_s32_loSR((int32_T)-32768, ((int32_T)rtb_MinMax) <<
    ((uint32_T)12), 15U);

  /* Switch: '<S130>/Switch2' incorporates:
   *  RelationalOperator: '<S130>/LowerRelop1'
   *  RelationalOperator: '<S130>/UpperRelop'
   *  Switch: '<S130>/Switch'
   */
  if (rtb_Mem > VExhSatPIDMax) {
    rtb_Mem = VExhSatPIDMax;
  } else {
    if (rtb_Mem < VExhSatPIDMin) {
      /* Switch: '<S130>/Switch' */
      rtb_Mem = VExhSatPIDMin;
    }
  }

  /* End of Switch: '<S130>/Switch2' */

  /* DataTypeConversion: '<S113>/Data Type Conversion' */
  VExhPID = (int16_T)(rtb_Mem >> ((uint32_T)11));

  /* MultiPortSwitch: '<S12>/Multiport Switch' incorporates:
   *  Constant: '<S12>/FORCEEXHOBJ'
   *  Constant: '<S12>/FORCEEXHVOUT'
   *  Constant: '<S12>/[V]'
   */
  switch (FORCEEXHOBJ) {
   case 0:
    rtb_MultiportSwitch = VExhPID;
    break;

   case 1:
    rtb_MultiportSwitch = FORCEEXHVOUT;
    break;

   case 2:
    rtb_MultiportSwitch = VExhPID;
    break;

   default:
    rtb_MultiportSwitch = 0;
    break;
  }

  /* End of MultiPortSwitch: '<S12>/Multiport Switch' */

  /* Switch: '<S12>/Switch' incorporates:
   *  Inport: '<Root>/VOutExhActiveDiag'
   *  Switch: '<S12>/Switch1'
   *  Switch: '<S12>/Switch2'
   */
  if (rtb_Compare_ojb) {
    rtb_Switch_etj = VOutExhActiveDiag;
  } else if (rtb_DataTypeConversion1 != 0) {
    /* Switch: '<S12>/Switch2' */
    rtb_Switch_etj = rtb_DataTypeConversion1;
  } else if (ExhValMgm_DW.VOutSelf != 0) {
    /* Switch: '<S12>/Switch1' incorporates:
     *  Switch: '<S12>/Switch2'
     */
    rtb_Switch_etj = ExhValMgm_DW.VOutSelf;
  } else {
    /* Switch: '<S12>/Switch2' incorporates:
     *  Switch: '<S12>/Switch1'
     */
    rtb_Switch_etj = rtb_MultiportSwitch;
  }

  /* End of Switch: '<S12>/Switch' */

  /* If: '<S103>/If' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  if (((int32_T)Rpm) == 0) {
    /* Outputs for IfAction SubSystem: '<S103>/if' incorporates:
     *  ActionPort: '<S109>/Action Port'
     */
    /* MultiPortSwitch: '<S109>/Multiport Switch' incorporates:
     *  Constant: '<S109>/DIS'
     *  Constant: '<S109>/EN'
     *  Constant: '<S109>/FORCEEXHOBJ'
     */
    switch (FORCEEXHOBJ) {
     case 0:
      rtb_LogicalOperator = ((uint8_T)0U);
      break;

     case 1:
      rtb_LogicalOperator = ((uint8_T)1U);
      break;

     case 2:
      rtb_LogicalOperator = ((uint8_T)1U);
      break;

     default:
      rtb_LogicalOperator = ((uint8_T)0U);
      break;
    }

    /* End of MultiPortSwitch: '<S109>/Multiport Switch' */

    /* Logic: '<S109>/Logical Operator' incorporates:
     *  Constant: '<S110>/Constant'
     *  RelationalOperator: '<S110>/Compare'
     */
    rtb_Merge = ((ExhValMgm_DW.FlgExhVZeroPos_gpr != ((uint8_T)0U)) ||
                 (((int32_T)rtb_LogicalOperator) != 0));

    /* End of Outputs for SubSystem: '<S103>/if' */
  } else {
    /* Outputs for IfAction SubSystem: '<S103>/else' incorporates:
     *  ActionPort: '<S108>/Action Port'
     */
    /* SignalConversion generated from: '<S108>/enCond' incorporates:
     *  Constant: '<S108>/EN'
     */
    rtb_Merge = true;

    /* End of Outputs for SubSystem: '<S103>/else' */
  }

  /* End of If: '<S103>/If' */

  /* Logic: '<S103>/Logical Operator' incorporates:
   *  Constant: '<S105>/Constant'
   *  Constant: '<S106>/Constant'
   *  Constant: '<S107>/Constant'
   *  Logic: '<S103>/Logical Operator1'
   *  Logic: '<S103>/Logical Operator2'
   *  RelationalOperator: '<S105>/Compare'
   *  RelationalOperator: '<S106>/Compare'
   *  RelationalOperator: '<S107>/Compare'
   */
  rtb_LogicalOperator_j42 = (uint8_T)((((((rtb_Compare_ojb != false) ||
    (rtb_DataTypeConversion1 != 0)) || (ExhValMgm_DW.VOutSelf != 0)) ||
    rtb_Merge) && (!LogicalOperator)) ? 1 : 0);

  /* Chart: '<S10>/Diag_Position' incorporates:
   *  Constant: '<S104>/Constant'
   *  Logic: '<S102>/Logical Operator'
   *  RelationalOperator: '<S104>/Compare'
   */
  /* Gateway: ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Position */
  /* During: ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Position */
  if (((uint32_T)ExhValMgm_DW.is_active_c10_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Position */
    ExhValMgm_DW.is_active_c10_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Diag_ExhValve_Position/Diag_Position */
    /* Transition: '<S89>:72' */
    StDiagExhVPos = 0U;
    CntExhVWrong = 0U;
    CntExhVA = 0U;
    CntExhVB = 0U;
    ExhValMgm_DW.is_c10_ExhValMgm = ExhValMgm_IN_STEP_1A;
  } else {
    switch (ExhValMgm_DW.is_c10_ExhValMgm) {
     case ExhValMgm_IN_STEP_1A:
      /* During 'STEP_1A': '<S89>:5' */
      /* Transition: '<S89>:8' */
      if (((((int32_T)rtb_LogicalOperator_j42) != 0) && (rtb_MultiportSwitch !=
            0)) && (((((int32_T)AngExhTrg0) >= (((int32_T)DataTypeConversion1) +
              ((int32_T)EXHERRTHRHIGH))) && (rtb_Switch_etj > 0)) || ((((int32_T)
              AngExhTrg0) <= (((int32_T)DataTypeConversion1) - ((int32_T)
               EXHERRTHRHIGH))) && (rtb_Switch_etj < 0)))) {
        /* Transition: '<S89>:13' */
        FrzVOutExh = rtb_Switch_etj;
        FrzAngExhValPerc = DataTypeConversion1;
        CntExhVEval = 0U;
        CntVOutExh = 0U;
        StDiagExhVPos = 1U;
        ExhValMgm_DW.is_c10_ExhValMgm = ExhValMgm_IN_STEP_1B;
      } else {
        /* Transition: '<S89>:14' */
      }
      break;

     case ExhValMgm_IN_STEP_1B:
      /* During 'STEP_1B': '<S89>:12' */
      /* Transition: '<S89>:16' */
      if (((AngExhTrg0 < FrzAngExhValPerc) && (FrzVOutExh > 0)) || ((AngExhTrg0 >
            FrzAngExhValPerc) && (FrzVOutExh < 0))) {
        /* Transition: '<S89>:17' */
        CntExhVMiss = (uint8_T)((int32_T)(((int32_T)CntExhVMiss) + 1));
        StDiagExhVPos = 0U;
        ExhValMgm_DW.is_c10_ExhValMgm = ExhValMgm_IN_STEP_1A;
      } else {
        /* Transition: '<S89>:18' */
        if ((rtb_Switch_etj * FrzVOutExh) > 0) {
          /* Transition: '<S89>:20' */
          CntVOutExh = (uint16_T)((int32_T)(((int32_T)CntVOutExh) + 1));
        } else {
          /* Transition: '<S89>:22' */
        }

        /* Transition: '<S89>:26' */
        if (CntVOutExh > TIMOBSEXHVPOSHIP) {
          /* Transition: '<S89>:29' */
          CntExhVWrong = (uint8_T)((int32_T)(((int32_T)CntExhVWrong) + 1));
          StDiagExhVPos = 2U;
          ExhValMgm_DW.is_c10_ExhValMgm = ExhValMgm_IN_STEP_1D;
        } else {
          /* Transition: '<S89>:27' */
          if (((((int32_T)DataTypeConversion1) - ((int32_T)EXHERRTHRHIGH)) <
               ((int32_T)AngExhTrg0)) && (((int32_T)AngExhTrg0) < (((int32_T)
                 DataTypeConversion1) + ((int32_T)EXHERRTHRHIGH)))) {
            /* Transition: '<S89>:32' */
            if (CntExhVEval > TIMOBSEXHVPOSLOP) {
              /* Transition: '<S89>:85' */
              StDiagExhVPos = 2U;
              if (FrzVOutExh > 0) {
                /* Transition: '<S89>:81' */
                CntExhVA = (uint8_T)((int32_T)(((int32_T)CntExhVA) + 1));
              } else {
                /* Transition: '<S89>:80' */
                CntExhVB = (uint8_T)((int32_T)(((int32_T)CntExhVB) + 1));
              }

              /* Transition: '<S89>:86' */
              ExhValMgm_DW.is_c10_ExhValMgm = ExhValMgm_IN_STEP_1D;
            } else {
              /* Transition: '<S89>:84' */
              CntExhVEval = (uint16_T)((int32_T)(((int32_T)CntExhVEval) + 1));
            }
          } else {
            /* Transition: '<S89>:31' */
          }
        }
      }
      break;

     default:
      /* During 'STEP_1D': '<S89>:23' */
      /* Transition: '<S89>:41' */
      if ((CntExhVA > THANGEXHVPOSNUM) && (CntExhVB > THANGEXHVPOSNUM)) {
        /* Transition: '<S89>:42' */
        ExhValMgm_DW.ptFault = NO_PT_FAULT;

        /* Outputs for Function Call SubSystem: '<S10>/fc_diag_calc' */
        /* Event: '<S89>:71' */
        ExhValMgm_fc_diag_calc(ExhValMgm_DW.ptFault,
          &ExhValMgm_DW.DiagMgm_SetDiagState, &ExhValMgm_DW.Add,
          &ExhValMgm_DW.fc_diag_calc);

        /* End of Outputs for SubSystem: '<S10>/fc_diag_calc' */
        CntExhVWrong = 0U;
        CntExhVA = 0U;
        CntExhVB = 0U;
        StDiagExhVPos = 0U;
      } else {
        /* Transition: '<S89>:44' */
        if (CntExhVWrong > THANGEXHVPOSNUMW) {
          /* Transition: '<S89>:45' */
          ExhValMgm_DW.ptFault = SIG_NOT_PLAUSIBLE;

          /* Outputs for Function Call SubSystem: '<S10>/fc_diag_calc' */
          /* Event: '<S89>:71' */
          ExhValMgm_fc_diag_calc(ExhValMgm_DW.ptFault,
            &ExhValMgm_DW.DiagMgm_SetDiagState, &ExhValMgm_DW.Add,
            &ExhValMgm_DW.fc_diag_calc);

          /* End of Outputs for SubSystem: '<S10>/fc_diag_calc' */
          CntExhVWrong = 0U;
          StDiagExhVPos = 0U;
        } else {
          /* Transition: '<S89>:47' */
          /* Transition: '<S89>:48' */
          StDiagExhVPos = 0U;
        }
      }

      ExhValMgm_DW.is_c10_ExhValMgm = ExhValMgm_IN_STEP_1A;
      break;
    }
  }

  /* End of Chart: '<S10>/Diag_Position' */

  /* Chart: '<S10>/Calc_FlgExhVDiagOn' incorporates:
   *  Inport: '<Root>/DrivingCycle'
   *  Logic: '<S135>/Logical Operator1'
   */
  /* Gateway: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
  /* During: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
  if (((uint32_T)ExhValMgm_DW.is_active_c9_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
    ExhValMgm_DW.is_active_c9_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
    /* Transition: '<S88>:2' */
    ExhValMgm_DW.oldDrivingCycle = DRVC_OFF;
    ExhValMgm_DW.oldCntDiagCall = ExhValMgm_DW.Add;
    FlgExhVDiagOn = 0U;
    ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_RESET;
  } else if (((uint32_T)ExhValMgm_DW.is_c9_ExhValMgm) == ExhValMgm_IN_DIAG_ON) {
    /* During 'DIAG_ON': '<S88>:3' */
    /* Transition: '<S88>:9' */
    if ((DrivingCycle == DRVC_START) && (ExhValMgm_DW.oldDrivingCycle ==
         DRVC_OFF)) {
      /* Transition: '<S88>:10' */
      ExhValMgm_DW.oldCntDiagCall = ExhValMgm_DW.Add;
      ExhValMgm_DW.oldDrivingCycle = DrivingCycle;
      FlgExhVDiagOn = 0U;
      ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_RESET;
    } else {
      /* Transition: '<S88>:11' */
      ExhValMgm_DW.oldDrivingCycle = DrivingCycle;
    }
  } else {
    /* During 'RESET': '<S88>:1' */
    /* Transition: '<S88>:25' */
    if ((((int32_T)FORCEEXHOBJ) == 3) || ((((int32_T)rtb_RelationalOperator8) !=
          0) || (((int32_T)rtb_Add1_gt3) != 0))) {
      /* Transition: '<S88>:26' */
      FlgExhVDiagOn = 1U;
    } else {
      /* Transition: '<S88>:5' */
      if ((ExhValMgm_DW.Add != ExhValMgm_DW.oldCntDiagCall) &&
          ((ExhValMgm_DW.DiagMgm_SetDiagState == FAULT) ||
           (ExhValMgm_DW.DiagMgm_SetDiagState == NO_FAULT))) {
        /* Transition: '<S88>:6' */
        ExhValMgm_DW.oldDrivingCycle = DrivingCycle;
        FlgExhVDiagOn = 1U;
        ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_DIAG_ON;
      } else {
        /* Transition: '<S88>:7' */
      }
    }
  }

  /* End of Chart: '<S10>/Calc_FlgExhVDiagOn' */

  /* SignalConversion generated from: '<S5>/AngExhTrg' */
  AngExhTrg = rtb_MultiportSwitch1;

  /* SignalConversion generated from: '<S5>/AngExhValPerc' */
  AngExhValPerc = OutportBufferForAngExhValPercTP;

  /* SignalConversion generated from: '<S5>/FlgExhVZeroPos' */
  FlgExhVZeroPos = ExhValMgm_DW.FlgExhVZeroPos_gpr;

  /* SignalConversion generated from: '<S5>/FlgExhValHBEna' */
  FlgExhValHBEna = rtb_LogicalOperator_j42;

  /* SignalConversion generated from: '<S5>/VOutExh' */
  VOutExh = rtb_Switch_etj;

  /* Update for Memory: '<S98>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput_pjn = rtb_Add_ilx;

  /* Switch: '<S121>/Switch1' */
  if (rtb_Switch2_j4c) {
    /* Update for Memory: '<S121>/Mem' incorporates:
     *  Constant: '<S121>/Constant1'
     */
    ExhValMgm_DW.Mem_PreviousInput = 0;
  } else {
    /* Update for Memory: '<S121>/Mem' */
    ExhValMgm_DW.Mem_PreviousInput = rtb_Mem;
  }

  /* End of Switch: '<S121>/Switch1' */

  /* Update for UnitDelay: '<S16>/Unit Delay3' */
  ExhValMgm_DW.UnitDelay3_DSTATE = rtb_Conversion7;

  /* Update for Memory: '<S28>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput_jyx = rtb_Compare_os1;

  /* Update for Memory: '<S28>/Memory1' */
  ExhValMgm_DW.Memory1_PreviousInput_pyc = CntReqExhSelf;

  /* Update for Memory: '<S79>/Memory2' incorporates:
   *  Memory: '<S79>/Memory1'
   */
  ExhValMgm_DW.Memory2_PreviousInput = ExhValMgm_DW.Memory1_PreviousInput;

  /* Update for Memory: '<S79>/Memory1' incorporates:
   *  Memory: '<S79>/Memory'
   */
  ExhValMgm_DW.Memory1_PreviousInput = ExhValMgm_DW.Memory_PreviousInput_f5m;

  /* Update for Memory: '<S79>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput_f5m = rtb_Switch2_fal;

  /* Update for UnitDelay: '<S70>/Unit Delay1' */
  ExhValMgm_DW.UnitDelay1_DSTATE = rtb_Conversion_odt;

  /* Update for Memory: '<S74>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput = ExhValModuleTime;

  /* Switch: '<S72>/Switch' incorporates:
   *  Constant: '<S66>/FORCEEXHOBJ'
   *  Constant: '<S72>/BKEXHVALTIME'
   *  Constant: '<S72>/BKEXHVALTIME_dim'
   *  Constant: '<S73>/Constant'
   *  Logic: '<S72>/Logical Operator'
   *  RelationalOperator: '<S72>/Relational Operator'
   *  RelationalOperator: '<S73>/Compare'
   *  Selector: '<S72>/Selector'
   */
  if ((ExhvalRelTime >= BKEXHVALTIME[((uint8_T)BKEXHVALTIME_dim)]) ||
      (FORCEEXHOBJ != ((uint8_T)2U))) {
    /* Update for Memory: '<S72>/Memory' */
    ExhValMgm_DW.Memory_PreviousInput_do4 = ExhValModuleTime;
  }

  /* End of Switch: '<S72>/Switch' */

  /* Update for UnitDelay: '<S111>/Unit Delay' */
  ExhValMgm_DW.UnitDelay_DSTATE = rtb_RelationalOperator1;

  /* Switch: '<S123>/Switch1' */
  if (rtb_Switch2_j4c) {
    /* Update for Memory: '<S123>/Mem' incorporates:
     *  Constant: '<S123>/Constant'
     */
    ExhValMgm_DW.Mem_PreviousInput_ixw = 0;
  } else {
    /* Update for Memory: '<S123>/Mem' */
    ExhValMgm_DW.Mem_PreviousInput_ixw = AngExhErr;
  }

  /* End of Switch: '<S123>/Switch1' */
}

/* Model step function */
void Trig_ExhValMgm_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<S1>/Init' */
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
  ExhValMgm_Init();

  /* End of Outputs for SubSystem: '<S1>/Init' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
}

/* Model step function */
void Trig_ExhValMgm_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S1>/Init'
   */
  ExhValMgm_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_ExhValMgm_T10ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  ExhValMgm_T10ms();

  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S6>/TP_OUT'
   */
  /* Outport: '<Root>/BUS_TP' incorporates:
   *  SignalConversion generated from: '<S139>/BUS_TP'
   */
  ExhValMgm_Y.BUS_TP = ExhValMgm_ConstB.Constant;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
}

/* Model initialize function */
void ExhValMgm_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  ExhValModuleTime = 0U;
  IDExhValMgm = 0U;
  AngExhValPerc = 0;
  VExhSatPIDMax = 0;
  VExhPID1 = 0;
  VExhSatPIDMin = 0;
  ExhvalRelTime = 0U;
  CntExhVEval = 0U;
  CntVOutExh = 0U;
  VAngExhValF = 0;
  AngExhTrg = 0;
  AngExhTrg0 = 0;
  AngExhErr = 0;
  AngExhErr1 = 0;
  FrzAngExhValPerc = 0;
  VOutExh = 0;
  VExhPID = 0;
  FrzVOutExh = 0;
  FlgExhValHBEna = 0U;
  FlgExhVZeroPos = 0U;
  FlgExVPWLamp = 0U;
  CntReqExhSelf = 0U;
  CntExhVA = 0U;
  CntExhVB = 0U;
  CntExhVMiss = 0U;
  CntExhVWrong = 0U;
  StDiagExhVPos = 0U;
  FlgExhVDiagOn = 0U;
  StSelfExh = 0U;
  FlgSelfExhLMSDone = 0U;
  FlgSelfExhUMSDone = 0U;
  FlgSelfExhReq = 0U;
  TrigExhVMinMaxTrg = 0U;
  SelfExhVStab = 0U;
  StSelfExhVStab = 0U;
  ExhVMaxTrg = 0;
  ExhVMinTrg = 0;

  /* states (dwork) */
  (void) memset((void *)&ExhValMgm_DW, 0,
                sizeof(DW_ExhValMgm_T));

  /* external outputs */
  (void) memset((void *)&ExhValMgm_Y, 0,
                sizeof(ExtY_ExhValMgm_T));

  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  ExhValMgm_T10ms_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */

  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  ExhValMgm_T10ms_Init();

  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S6>/TP_OUT'
   */
  /* SystemInitialize for Outport: '<Root>/BUS_TP' incorporates:
   *  SignalConversion generated from: '<S139>/BUS_TP'
   */
  ExhValMgm_Y.BUS_TP = ExhValMgm_ConstB.Constant;

  /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
  /* SystemInitialize for Merge: '<S4>/Merge4' */
  VOutExh = 0;

  /* SystemInitialize for Merge: '<S4>/Merge5' */
  FlgExhValHBEna = ((uint8_T)0U);

  /* SystemInitialize for Merge: '<S4>/Merge' */
  AngExhValPerc = 0;

  /* SystemInitialize for Merge: '<S4>/Merge1' */
  AngExhTrg = 0;

  /* SystemInitialize for Merge: '<S4>/Merge2' */
  FlgExhVZeroPos = ((uint8_T)0U);

  /* SystemInitialize for Merge: '<S4>/Merge3' */
  FlgExVPWLamp = ((uint8_T)0U);
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 /* _BUILD_EXHVALMGM_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
