/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  _BUILD_CAN_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include <string.h>
#include "CAN.h"
#include "events.h"
#include "task.h"
#include "tasksdefs.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
#undef DEB_CAN_A_MB
#ifdef DEB_CAN_A_MB
uint16_t cntMB_CANA[64];
#endif


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FuncCAN_ExTxDoneChA - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExTxDoneChA (void)
{
   /*
    This exception will be generated
    at the end of CAN data transmission
    of the Channel A
    */
    if (ExTxDoneChA)
    {
        ExTxDoneChA();
    }
    TerminateTask();
}

/*--------------------------------------------------------------------------*
 * FuncCAN_ExRxDoneChA - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExRxDoneChA (void)
{
    /*
    This exception will be generated
    at the end of CAN data reception
    of the Channel A
    */
    if (ExRxDoneChA)
    {
        ExRxDoneChA();
    }
    TerminateTask();


}

#if (TARGET_TYPE == MPC5554)
/*--------------------------------------------------------------------------*
 * FuncCAN_ExTxDoneChB - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExTxDoneChB (void)
{
    /*
    This exception will be generated
    at the end of CAN data transmission
    of the Channel B
    */
    if (ExTxDoneChB)
    {
        ExTxDoneChB();
    }
    TerminateTask();
}

/*--------------------------------------------------------------------------*
 * FuncCAN_ExRxDoneChB - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExRxDoneChB (void)
{
    /*
    This exception will be generated
    at the end of CAN data reception
    of the Channel B
    */
    if (ExRxDoneChB)
    {
        ExRxDoneChB();
    }
    TerminateTask();
}
#endif /* TARGET_TYPE == MPC5554 */

/*--------------------------------------------------------------------------*
 * FuncCAN_ExTxDoneChA - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExTxDoneChC (void)
{
    if (ExTxDoneChC)
    {
        ExTxDoneChC();
    }
    TerminateTask();
}

/*--------------------------------------------------------------------------*
 * FuncCAN_ExTxDoneChA - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExRxDoneChC (void)
{
    if (ExRxDoneChC)
    {
        ExRxDoneChC();
    }
    TerminateTask();
}
/**********************************************/
/*            CAN Interrupt Service Routine   */
/**********************************************/

void CAN_CHA_MB0(void){
#ifdef CAN_CHA_BUF0_DIR
#if (CAN_CHA_BUF0_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[0].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[0].ID.R;
    MEM_WRITE(A,0)     /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,0)

    if (IS_BUFFER_OVERRUN(A,0)) {
      UPDATE_RD_ID(A,0)
    } else
    if (IS_BUFFER_FULL(A,0)) {
      UPDATE_BS(A,0,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,0)
    } else
    if(WRITE_ID(A,0) == READ_ID(A,0)) {
      UPDATE_BS(A,0,BUFFER_FULL);
    }
    else
    {
    }

    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF0_EXC
    ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else

    #if CAN_CHA_BUF0_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
#ifdef DEB_CAN_A_MB
cntMB_CANA[0]++;
#endif
(CAN_A.IFRL.R)&=(1<<0);
}


void CAN_CHA_MB1(void){
#ifdef CAN_CHA_BUF1_DIR
#if (CAN_CHA_BUF1_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[1].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[1].ID.R;
    MEM_WRITE(A,1)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,1)

    if (IS_BUFFER_OVERRUN(A,1)) {
      UPDATE_RD_ID(A,1)
    } else
    if (IS_BUFFER_FULL(A,1)) {
      UPDATE_BS(A,1,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,1)
    } else
    if(WRITE_ID(A,1) == READ_ID(A,1)) {
      UPDATE_BS(A,1,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF1_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else

    #if CAN_CHA_BUF1_EXC
    ActivateTask(CAN_ExTxDoneChAID);
    #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<1);
}


void CAN_CHA_MB2(void){

#ifdef CAN_CHA_BUF2_DIR
#if (CAN_CHA_BUF2_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[2].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[2].ID.R;
    MEM_WRITE(A,2)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,2)
    if (IS_BUFFER_OVERRUN(A,2)) {
      UPDATE_RD_ID(A,2)
    } else
    if (IS_BUFFER_FULL(A,2)) {
      UPDATE_BS(A,2,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,2)
    } else
    if(WRITE_ID(A,2) == READ_ID(A,2)) {
      UPDATE_BS(A,2,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF2_EXC
     ActivateTask(CAN_ExRxDoneChAID);
        #endif
  }
#else
    #if CAN_CHA_BUF2_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<2);
}


void CAN_CHA_MB3(void){

#ifdef CAN_CHA_BUF3_DIR
#if (CAN_CHA_BUF3_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[3].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[3].ID.R;
    MEM_WRITE(A,3)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,3)
    if (IS_BUFFER_OVERRUN(A,3)) {
      UPDATE_RD_ID(A,3)
    } else
    if (IS_BUFFER_FULL(A,3)) {
      UPDATE_BS(A,3,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,3)
    } else
    if(WRITE_ID(A,3) == READ_ID(A,3)) {
      UPDATE_BS(A,3,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF3_EXC
     ActivateTask(CAN_ExRxDoneChAID);
        #endif
  }
 #else

    #if CAN_CHA_BUF3_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<3);
}

void CAN_CHA_MB4(void){
#ifdef CAN_CHA_BUF4_DIR
#if (CAN_CHA_BUF4_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[4].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[4].ID.R;
    MEM_WRITE(A,4)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,4)
    if (IS_BUFFER_OVERRUN(A,4)) {
      UPDATE_RD_ID(A,4)
    } else
    if (IS_BUFFER_FULL(A,4)) {
      UPDATE_BS(A,4,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,4)
    } else
    if(WRITE_ID(A,4) == READ_ID(A,4)) {
      UPDATE_BS(A,4,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF4_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF5_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<4);
}


void CAN_CHA_MB5(void){
#ifdef CAN_CHA_BUF5_DIR
#if (CAN_CHA_BUF5_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[5].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[5].ID.R;
    MEM_WRITE(A,5)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,5)
    if (IS_BUFFER_OVERRUN(A,5)) {
      UPDATE_RD_ID(A,5)
    } else
    if (IS_BUFFER_FULL(A,5)) {
      UPDATE_BS(A,5,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,5)
    } else
    if(WRITE_ID(A,5) == READ_ID(A,5)) {
      UPDATE_BS(A,5,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF5_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }

#else
    #if CAN_CHA_BUF5_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<5);
}

void CAN_CHA_MB6(void){
#ifdef CAN_CHA_BUF6_DIR
#if (CAN_CHA_BUF6_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[6].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[6].ID.R;
        MEM_WRITE(A,6)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,6)
    if (IS_BUFFER_OVERRUN(A,6)) {
      UPDATE_RD_ID(A,6)
    } else
    if (IS_BUFFER_FULL(A,6)) {
      UPDATE_BS(A,6,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,6)
    } else
    if(WRITE_ID(A,6) == READ_ID(A,6)) {
      UPDATE_BS(A,6,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF6_EXC
     ActivateTask(CAN_ExRxDoneChAID);
        #endif
  }
#else
    #if CAN_CHA_BUF6_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<6);
}

void CAN_CHA_MB7(void){
#ifdef CAN_CHA_BUF7_DIR
#if (CAN_CHA_BUF7_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[7].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[7].ID.R;
    MEM_WRITE(A,7)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,7)
    if (IS_BUFFER_OVERRUN(A,7)) {
      UPDATE_RD_ID(A,7)
    } else
    if (IS_BUFFER_FULL(A,7)) {
      UPDATE_BS(A,7,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,7)
    } else
    if(WRITE_ID(A,7) == READ_ID(A,7)) {
      UPDATE_BS(A,7,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF7_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF7_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<7);
}

void CAN_CHA_MB8(void){
#ifdef CAN_CHA_BUF8_DIR
#if (CAN_CHA_BUF8_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[8].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[8].ID.R;
    MEM_WRITE(A,8)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,8)
    if (IS_BUFFER_OVERRUN(A,8)) {
      UPDATE_RD_ID(A,8)
    } else
    if (IS_BUFFER_FULL(A,8)) {
      UPDATE_BS(A,8,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,8)
    } else
    if(WRITE_ID(A,8) == READ_ID(A,8)) {
      UPDATE_BS(A,8,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF8_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF8_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<8);
}

void CAN_CHA_MB9(void){
#ifdef CAN_CHA_BUF9_DIR
#if (CAN_CHA_BUF9_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[9].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[9].ID.R;
    MEM_WRITE(A,9)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,9)
    if (IS_BUFFER_OVERRUN(A,9)) {
      UPDATE_RD_ID(A,9)
    } else
    if (IS_BUFFER_FULL(A,9)) {
      UPDATE_BS(A,9,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,9)
    } else
    if(WRITE_ID(A,9) == READ_ID(A,9)) {
      UPDATE_BS(A,9,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF9_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF9_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<9);
}

void CAN_CHA_MB10(void){
#ifdef CAN_CHA_BUF10_DIR
#if (CAN_CHA_BUF10_DIR==CAN_RX)
vuint32_t ts;
 if ((CAN_A.BUF[10].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[10].ID.R;
    MEM_WRITE(A,10)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,10)
    if (IS_BUFFER_OVERRUN(A,10)) {
      UPDATE_RD_ID(A,10)
    } else
    if (IS_BUFFER_FULL(A,10)) {
      UPDATE_BS(A,10,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,10)
    } else
    if(WRITE_ID(A,10) == READ_ID(A,10)) {
      UPDATE_BS(A,10,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF10_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF10_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<10);
}

void CAN_CHA_MB11(void){
#ifdef CAN_CHA_BUF11_DIR
#if (CAN_CHA_BUF11_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[11].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[11].ID.R;
    MEM_WRITE(A,11)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,11)
    if (IS_BUFFER_OVERRUN(A,11)) {
      UPDATE_RD_ID(A,11)
    } else
    if (IS_BUFFER_FULL(A,11)) {
      UPDATE_BS(A,11,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,11)
    } else
    if(WRITE_ID(A,11) == READ_ID(A,11)) {
      UPDATE_BS(A,11,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF11_EXC
     ActivateTask(CAN_ExRxDoneChAID);
        #endif
  }
#else
    #if CAN_CHA_BUF11_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<11);

}

void CAN_CHA_MB12(void){
#ifdef CAN_CHA_BUF12_DIR
#if (CAN_CHA_BUF12_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[12].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[12].ID.R;
    MEM_WRITE(A,12)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,12)
    if (IS_BUFFER_OVERRUN(A,12)) {
      UPDATE_RD_ID(A,12)
    } else
    if (IS_BUFFER_FULL(A,12)) {
      UPDATE_BS(A,12,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,12)
    } else
    if(WRITE_ID(A,12) == READ_ID(A,12)) {
      UPDATE_BS(A,12,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF12_EXC
     ActivateTask(CAN_ExRxDoneChAID);
        #endif
  }
#else
    #if CAN_CHA_BUF12_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<12);

}

void CAN_CHA_MB13(void){
#ifdef CAN_CHA_BUF13_DIR
#if (CAN_CHA_BUF13_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[13].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[13].ID.R;
    MEM_WRITE(A,13)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,13)
    if (IS_BUFFER_OVERRUN(A,13)) {
      UPDATE_RD_ID(A,13)
    } else
    if (IS_BUFFER_FULL(A,13)) {
      UPDATE_BS(A,13,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,13)
    } else
    if(WRITE_ID(A,13) == READ_ID(A,13)) {
      UPDATE_BS(A,13,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF13_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF13_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<13);
}


void CAN_CHA_MB14(void){
#ifdef CAN_CHA_BUF14_DIR
#if (CAN_CHA_BUF14_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[14].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[14].ID.R;
    MEM_WRITE(A,14)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,14)
    if (IS_BUFFER_OVERRUN(A,14)) {
      UPDATE_RD_ID(A,14)
    } else
    if (IS_BUFFER_FULL(A,14)) {
      UPDATE_BS(A,14,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,14)
    } else
    if(WRITE_ID(A,14) == READ_ID(A,14)) {
      UPDATE_BS(A,14,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF14_EXC
     ActivateTask(CAN_ExRxDoneChAID);
    #endif
  }
#else
    #if CAN_CHA_BUF14_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<14);
}

void CAN_CHA_MB15(void){
#ifdef CAN_CHA_BUF15_DIR
#if (CAN_CHA_BUF15_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_A.BUF[15].CS.R & (1<<27))==0) {
    ts=CAN_A.BUF[15].ID.R;
    MEM_WRITE(A,15)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(A,15)
    if (IS_BUFFER_OVERRUN(A,15)) {
      UPDATE_RD_ID(A,15)
    } else
    if (IS_BUFFER_FULL(A,15)) {
      UPDATE_BS(A,15,BUFFER_OVERRUN);
      UPDATE_RD_ID(A,15)
    } else
    if(WRITE_ID(A,15) == READ_ID(A,15)) {
      UPDATE_BS(A,15,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_A.TIMER.R;
    #if CAN_CHA_BUF15_EXC
     ActivateTask(CAN_ExRxDoneChAID);
        #endif
  }
#else
    #if CAN_CHA_BUF15_EXC
    ActivateTask(CAN_ExTxDoneChAID);
        #endif
#endif
#endif
(CAN_A.IFRL.R)&=(1<<15);

}


void CAN_CHA_MB16_31(void){
#ifdef CAN_CHA_BUF16_DIR
  if ((CAN_A.IFRL.R)&(1<<16))
  {
    #if (CAN_CHA_BUF16_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[16].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[16].ID.R;
        MEM_WRITE(A,16)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,16)
        if (IS_BUFFER_OVERRUN(A,16)) {
          UPDATE_RD_ID(A,16)
        } else
        if (IS_BUFFER_FULL(A,16)) {
          UPDATE_BS(A,16,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,16)
        } else
        if(WRITE_ID(A,16) == READ_ID(A,16)) {
          UPDATE_BS(A,16,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF16_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF16_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF16_DIR==CAN_RX)
    (CAN_A.IFRL.R)&=(1<<16);
  }
#endif

#ifdef CAN_CHA_BUF17_DIR
  if ((CAN_A.IFRL.R)&(1<<17))
  {
    #if (CAN_CHA_BUF17_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[17].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[17].ID.R;
        MEM_WRITE(A,17)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,17)
        if (IS_BUFFER_OVERRUN(A,17)) {
          UPDATE_RD_ID(A,17)
        } else
        if (IS_BUFFER_FULL(A,17)) {
          UPDATE_BS(A,17,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,17)
        } else
        if(WRITE_ID(A,17) == READ_ID(A,17)) {
          UPDATE_BS(A,17,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF17_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF17_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<17);
  }
#endif

#ifdef CAN_CHA_BUF18_DIR
  if ((CAN_A.IFRL.R)&(1<<18))
  {
    #if (CAN_CHA_BUF18_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[18].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[18].ID.R;
        MEM_WRITE(A,18)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,18)
        if (IS_BUFFER_OVERRUN(A,18)) {
          UPDATE_RD_ID(A,18)
        } else
        if (IS_BUFFER_FULL(A,18)) {
          UPDATE_BS(A,18,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,18)
        } else
        if(WRITE_ID(A,18) == READ_ID(A,18)) {
          UPDATE_BS(A,18,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF18_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
      }
     }
    #else
        #if CAN_CHA_BUF18_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<18);
  }
#endif

#ifdef CAN_CHA_BUF19_DIR
  if ((CAN_A.IFRL.R)&(1<<19))
  {
    #if (CAN_CHA_BUF19_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[19].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[19].ID.R;
        MEM_WRITE(A,19)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,19)
        if (IS_BUFFER_OVERRUN(A,19)) {
          UPDATE_RD_ID(A,19)
        } else
        if (IS_BUFFER_FULL(A,19)) {
          UPDATE_BS(A,19,BUFFER_OVERRUN)
          UPDATE_RD_ID(A,19)
        } else
        if(WRITE_ID(A,19) == READ_ID(A,19)) {
          UPDATE_BS(A,19,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF19_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF19_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<19);
  }
#endif

#ifdef CAN_CHA_BUF20_DIR
  if ((CAN_A.IFRL.R)&(1<<20))
  {
    #if (CAN_CHA_BUF20_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[20].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[20].ID.R;
        MEM_WRITE(A,20)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,20)
        if (IS_BUFFER_OVERRUN(A,20)) {
          UPDATE_RD_ID(A,20)
        } else
        if (IS_BUFFER_FULL(A,20)) {
          UPDATE_BS(A,20,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,20)
        } else
        if(WRITE_ID(A,20) == READ_ID(A,20)) {
          UPDATE_BS(A,20,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF20_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
      }
     }
    #else
        #if CAN_CHA_BUF20_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<20);
  }
#endif

#ifdef CAN_CHA_BUF21_DIR
  if ((CAN_A.IFRL.R)&(1<<21))
  {
    #if (CAN_CHA_BUF21_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[21].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[21].ID.R;
        MEM_WRITE(A,21)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,21)
        if (IS_BUFFER_OVERRUN(A,21)) {
          UPDATE_RD_ID(A,21)
        } else
        if (IS_BUFFER_FULL(A,21)) {
          UPDATE_BS(A,21,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,21)
        } else
        if(WRITE_ID(A,21) == READ_ID(A,21)) {
          UPDATE_BS(A,21,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF21_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF21_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<21);
  }
#endif

#ifdef CAN_CHA_BUF22_DIR
  if ((CAN_A.IFRL.R)&(1<<22))
  {
    #if (CAN_CHA_BUF22_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[22].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[22].ID.R;
        MEM_WRITE(A,22)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,22)
        if (IS_BUFFER_OVERRUN(A,22)) {
          UPDATE_RD_ID(A,22)
        } else
        if (IS_BUFFER_FULL(A,22)) {
          UPDATE_BS(A,22,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,22)
        } else
        if(WRITE_ID(A,22) == READ_ID(A,22)) {
          UPDATE_BS(A,22,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF22_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF22_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<22);
  }
#endif

#ifdef CAN_CHA_BUF23_DIR
  if ((CAN_A.IFRL.R)&(1<<23))
  {
    #if (CAN_CHA_BUF23_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[23].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[23].ID.R;
        MEM_WRITE(A,23)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,23)
        if (IS_BUFFER_OVERRUN(A,23)) {
          UPDATE_RD_ID(A,23)
        } else
        if (IS_BUFFER_FULL(A,23)) {
          UPDATE_BS(A,23,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,23)
        } else
        if(WRITE_ID(A,23) == READ_ID(A,23)) {
          UPDATE_BS(A,23,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF23_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF23_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<23);
  }
#endif

#ifdef CAN_CHA_BUF24_DIR
  if ((CAN_A.IFRL.R)&(1<<24))
  {
    #if (CAN_CHA_BUF24_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[24].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[24].ID.R;
        MEM_WRITE(A,24)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,24)
        if (IS_BUFFER_OVERRUN(A,24)) {
          UPDATE_RD_ID(A,24)
        } else
        if (IS_BUFFER_FULL(A,24)) {
          UPDATE_BS(A,24,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,24)
        } else
        if(WRITE_ID(A,24) == READ_ID(A,24)) {
          UPDATE_BS(A,24,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF24_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF24_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<24);
  }
#endif

#ifdef CAN_CHA_BUF25_DIR
  if ((CAN_A.IFRL.R)&(1<<25))
  {
    #if (CAN_CHA_BUF25_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[25].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[25].ID.R;
        MEM_WRITE(A,25)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,25)
        if (IS_BUFFER_OVERRUN(A,25)) {
          UPDATE_RD_ID(A,25)
        } else
        if (IS_BUFFER_FULL(A,25)) {
          UPDATE_BS(A,25,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,25)
        } else
        if(WRITE_ID(A,25) == READ_ID(A,25)) {
          UPDATE_BS(A,25,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF25_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
      }
     }
    #else
        #if CAN_CHA_BUF25_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<25);
  }
#endif

#ifdef CAN_CHA_BUF26_DIR
  if ((CAN_A.IFRL.R)&(1<<26))
  {
    #if (CAN_CHA_BUF26_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[26].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[26].ID.R;
        MEM_WRITE(A,26)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,26)
        if (IS_BUFFER_OVERRUN(A,26)) {
          UPDATE_RD_ID(A,26)
        } else
        if (IS_BUFFER_FULL(A,26)) {
          UPDATE_BS(A,26,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,26)
        } else
        if(WRITE_ID(A,26) == READ_ID(A,26)) {
          UPDATE_BS(A,26,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF26_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
      }
     }
    #else
        #if CAN_CHA_BUF26_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<26);
  }
#endif

#ifdef CAN_CHA_BUF27_DIR
  if ((CAN_A.IFRL.R)&(1<<27))
  {
    #if (CAN_CHA_BUF27_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[27].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[27].ID.R;
        MEM_WRITE(A,27)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,27)
        if (IS_BUFFER_OVERRUN(A,27)) {
          UPDATE_RD_ID(A,27)
        } else
        if (IS_BUFFER_FULL(A,27)) {
          UPDATE_BS(A,27,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,27)
        } else
        if(WRITE_ID(A,27) == READ_ID(A,27)) {
          UPDATE_BS(A,27,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF27_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF27_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<27);
  }
#endif

#ifdef CAN_CHA_BUF28_DIR
  if ((CAN_A.IFRL.R)&(1<<28))
  {
    #if (CAN_CHA_BUF28_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[28].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[28].ID.R;
        MEM_WRITE(A,28)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,28)
        if (IS_BUFFER_OVERRUN(A,28)) {
          UPDATE_RD_ID(A,28)
        } else
        if (IS_BUFFER_FULL(A,28)) {
          UPDATE_BS(A,28,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,28)
        } else
        if(WRITE_ID(A,28) == READ_ID(A,4)) {
          UPDATE_BS(A,28,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF28_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
      }
     }
    #else
        #if CAN_CHA_BUF28_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<28);
  }
#endif

#ifdef CAN_CHA_BUF29_DIR
  if ((CAN_A.IFRL.R)&(1<<29))
  {
    #if (CAN_CHA_BUF29_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[29].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[29].ID.R;
        MEM_WRITE(A,29)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,29)
        if (IS_BUFFER_OVERRUN(A,29)) {
          UPDATE_RD_ID(A,29)
        } else
        if (IS_BUFFER_FULL(A,29)) {
          UPDATE_BS(A,29,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,29)
        } else
        if(WRITE_ID(A,29) == READ_ID(A,29)) {
          UPDATE_BS(A,29,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF29_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF29_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<29);
  }
#endif

#ifdef CAN_CHA_BUF30_DIR
  if ((CAN_A.IFRL.R)&(1<<30))
  {
    #if (CAN_CHA_BUF30_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[30].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[30].ID.R;
        MEM_WRITE(A,30)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,30)
        if (IS_BUFFER_OVERRUN(A,30)) {
          UPDATE_RD_ID(A,30)
        } else
        if (IS_BUFFER_FULL(A,30)) {
          UPDATE_BS(A,30,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,30)
        } else
        if(WRITE_ID(A,30) == READ_ID(A,30)) {
          UPDATE_BS(A,30,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF30_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF30_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(1<<30);
  }
#endif

#ifdef CAN_CHA_BUF31_DIR
if ((CAN_A.IFRL.R)&(uint32_t)(1<<31))
  {
    #if (CAN_CHA_BUF31_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[31].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[31].ID.R;
        MEM_WRITE(A,31)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,31)
        if (IS_BUFFER_OVERRUN(A,31)) {
          UPDATE_RD_ID(A,31)
        } else
        if (IS_BUFFER_FULL(A,31)) {
          UPDATE_BS(A,31,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,31)
        } else
        if(WRITE_ID(A,31) == READ_ID(A,31)) {
          UPDATE_BS(A,31,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF31_EXC
         ActivateTask(CAN_ExRxDoneChAID);
            #endif
      }
     }
    #else
        #if CAN_CHA_BUF31_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif
    (CAN_A.IFRL.R)&=(uint32_t)(1<<31);
  }
#endif

}

void CAN_CHA_MB32_63(void){
#ifdef CAN_CHA_BUF32_DIR
  if ((CAN_A.IFRH.R)&(1<<0))
  {
    #if (CAN_CHA_BUF32_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[32].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[32].ID.R;
        MEM_WRITE(A,32)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,32)
        if (IS_BUFFER_OVERRUN(A,32)) {
          UPDATE_RD_ID(A,32)
        } else
        if (IS_BUFFER_FULL(A,32)) {
          UPDATE_BS(A,32,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,32)
        } else
        if(WRITE_ID(A,32) == READ_ID(A,32)) {
          UPDATE_BS(A,32,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF32_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF32_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF32_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<0);
  }
#endif

#ifdef CAN_CHA_BUF33_DIR
  if ((CAN_A.IFRH.R)&(1<<1))
  {
    #if (CAN_CHA_BUF33_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[33].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[33].ID.R;
        MEM_WRITE(A,33)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,33)
        if (IS_BUFFER_OVERRUN(A,33)) {
          UPDATE_RD_ID(A,33)
        } else
        if (IS_BUFFER_FULL(A,33)) {
          UPDATE_BS(A,33,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,33)
        } else
        if(WRITE_ID(A,33) == READ_ID(A,33)) {
          UPDATE_BS(A,33,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF33_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF33_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF33_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<1);
  }
#endif

#ifdef CAN_CHA_BUF34_DIR
  if ((CAN_A.IFRH.R)&(1<<2))
  {
    #if (CAN_CHA_BUF34_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[34].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[34].ID.R;
        MEM_WRITE(A,34)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,34)
        if (IS_BUFFER_OVERRUN(A,34)) {
          UPDATE_RD_ID(A,34)
        } else
        if (IS_BUFFER_FULL(A,34)) {
          UPDATE_BS(A,34,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,34)
        } else
        if(WRITE_ID(A,34) == READ_ID(A,34)) {
          UPDATE_BS(A,34,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF34_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF34_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF34_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<2);
  }
#endif

#ifdef CAN_CHA_BUF35_DIR
  if ((CAN_A.IFRH.R)&(1<<3))
  {
    #if (CAN_CHA_BUF35_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[35].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[35].ID.R;
        MEM_WRITE(A,35)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,35)
        if (IS_BUFFER_OVERRUN(A,35)) {
          UPDATE_RD_ID(A,35)
        } else
        if (IS_BUFFER_FULL(A,35)) {
          UPDATE_BS(A,35,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,35)
        } else
        if(WRITE_ID(A,35) == READ_ID(A,35)) {
          UPDATE_BS(A,35,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF35_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF35_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF35_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<3);
  }
#endif

#ifdef CAN_CHA_BUF36_DIR
  if ((CAN_A.IFRH.R)&(1<<4))
  {
    #if (CAN_CHA_BUF36_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[36].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[36].ID.R;
        MEM_WRITE(A,36)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,36)
        if (IS_BUFFER_OVERRUN(A,36)) {
          UPDATE_RD_ID(A,36)
        } else
        if (IS_BUFFER_FULL(A,36)) {
          UPDATE_BS(A,36,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,36)
        } else
        if(WRITE_ID(A,36) == READ_ID(A,36)) {
          UPDATE_BS(A,36,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF36_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF36_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF36_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<4);
  }
#endif

#ifdef CAN_CHA_BUF37_DIR
  if ((CAN_A.IFRH.R)&(1<<5))
  {
    #if (CAN_CHA_BUF37_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[37].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[37].ID.R;
        MEM_WRITE(A,37)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,37)
        if (IS_BUFFER_OVERRUN(A,37)) {
          UPDATE_RD_ID(A,37)
        } else
        if (IS_BUFFER_FULL(A,37)) {
          UPDATE_BS(A,37,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,37)
        } else
        if(WRITE_ID(A,37) == READ_ID(A,37)) {
          UPDATE_BS(A,37,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF37_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF37_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF37_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<5);
  }
#endif

#ifdef CAN_CHA_BUF38_DIR
  if ((CAN_A.IFRH.R)&(1<<6))
  {
    #if (CAN_CHA_BUF38_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[38].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[38].ID.R;
        MEM_WRITE(A,38)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,38)
        if (IS_BUFFER_OVERRUN(A,38)) {
          UPDATE_RD_ID(A,38)
        } else
        if (IS_BUFFER_FULL(A,38)) {
          UPDATE_BS(A,38,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,38)
        } else
        if(WRITE_ID(A,38) == READ_ID(A,38)) {
          UPDATE_BS(A,38,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF38_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF38_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF38_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<6);
  }
#endif

#ifdef CAN_CHA_BUF39_DIR
  if ((CAN_A.IFRH.R)&(1<<7))
  {
    #if (CAN_CHA_BUF39_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[39].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[39].ID.R;
        MEM_WRITE(A,39)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,39)
        if (IS_BUFFER_OVERRUN(A,39)) {
          UPDATE_RD_ID(A,39)
        } else
        if (IS_BUFFER_FULL(A,39)) {
          UPDATE_BS(A,39,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,39)
        } else
        if(WRITE_ID(A,39) == READ_ID(A,39)) {
          UPDATE_BS(A,39,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF39_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF39_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF39_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<7);
  }
#endif

#ifdef CAN_CHA_BUF40_DIR
  if ((CAN_A.IFRH.R)&(1<<8))
  {
    #if (CAN_CHA_BUF40_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[40].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[40].ID.R;
        MEM_WRITE(A,40)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,40)
        if (IS_BUFFER_OVERRUN(A,40)) {
          UPDATE_RD_ID(A,40)
        } else
        if (IS_BUFFER_FULL(A,40)) {
          UPDATE_BS(A,40,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,40)
        } else
        if(WRITE_ID(A,40) == READ_ID(A,40)) {
          UPDATE_BS(A,40,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF40_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF40_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF40_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<8);
  }
#endif

#ifdef CAN_CHA_BUF41_DIR
  if ((CAN_A.IFRH.R)&(1<<9))
  {
    #if (CAN_CHA_BUF41_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[41].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[41].ID.R;
        MEM_WRITE(A,41)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,41)
        if (IS_BUFFER_OVERRUN(A,41)) {
          UPDATE_RD_ID(A,41)
        } else
        if (IS_BUFFER_FULL(A,41)) {
          UPDATE_BS(A,41,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,41)
        } else
        if(WRITE_ID(A,41) == READ_ID(A,41)) {
          UPDATE_BS(A,41,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF41_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF41_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF41_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<9);
  }
#endif

#ifdef CAN_CHA_BUF42_DIR
  if ((CAN_A.IFRH.R)&(1<<10))
  {
    #if (CAN_CHA_BUF42_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[42].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[42].ID.R;
        MEM_WRITE(A,42)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,42)
        if (IS_BUFFER_OVERRUN(A,42)) {
          UPDATE_RD_ID(A,42)
        } else
        if (IS_BUFFER_FULL(A,42)) {
          UPDATE_BS(A,42,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,42)
        } else
        if(WRITE_ID(A,42) == READ_ID(A,42)) {
          UPDATE_BS(A,42,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF42_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF42_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF42_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<10);
  }
#endif

#ifdef CAN_CHA_BUF43_DIR
  if ((CAN_A.IFRH.R)&(1<<11))
  {
    #if (CAN_CHA_BUF43_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[43].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[43].ID.R;
        MEM_WRITE(A,43)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,43)
        if (IS_BUFFER_OVERRUN(A,43)) {
          UPDATE_RD_ID(A,43)
        } else
        if (IS_BUFFER_FULL(A,43)) {
          UPDATE_BS(A,43,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,43)
        } else
        if(WRITE_ID(A,43) == READ_ID(A,43)) {
          UPDATE_BS(A,43,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF43_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF43_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF43_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<11);
  }
#endif

#ifdef CAN_CHA_BUF44_DIR
  if ((CAN_A.IFRH.R)&(1<<12))
  {
    #if (CAN_CHA_BUF44_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[44].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[44].ID.R;
        MEM_WRITE(A,44)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,44)
        if (IS_BUFFER_OVERRUN(A,44)) {
          UPDATE_RD_ID(A,44)
        } else
        if (IS_BUFFER_FULL(A,44)) {
          UPDATE_BS(A,44,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,44)
        } else
        if(WRITE_ID(A,44) == READ_ID(A,44)) {
          UPDATE_BS(A,44,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF44_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF44_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF44_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<12);
  }
#endif

#ifdef CAN_CHA_BUF45_DIR
  if ((CAN_A.IFRH.R)&(1<<13))
  {
    #if (CAN_CHA_BUF45_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[45].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[45].ID.R;
        MEM_WRITE(A,45)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,45)
        if (IS_BUFFER_OVERRUN(A,45)) {
          UPDATE_RD_ID(A,45)
        } else
        if (IS_BUFFER_FULL(A,45)) {
          UPDATE_BS(A,45,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,45)
        } else
        if(WRITE_ID(A,45) == READ_ID(A,45)) {
          UPDATE_BS(A,45,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF45_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF45_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF45_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<13);
  }
#endif

#ifdef CAN_CHA_BUF46_DIR
  if ((CAN_A.IFRH.R)&(1<<14))
  {
    #if (CAN_CHA_BUF46_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[46].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[46].ID.R;
        MEM_WRITE(A,46)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,46)
        if (IS_BUFFER_OVERRUN(A,46)) {
          UPDATE_RD_ID(A,46)
        } else
        if (IS_BUFFER_FULL(A,46)) {
          UPDATE_BS(A,46,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,46)
        } else
        if(WRITE_ID(A,46) == READ_ID(A,46)) {
          UPDATE_BS(A,46,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF46_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF46_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF46_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<14);
  }
#endif

#ifdef CAN_CHA_BUF47_DIR
  if ((CAN_A.IFRH.R)&(1<<15))
  {
    #if (CAN_CHA_BUF47_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[47].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[47].ID.R;
        MEM_WRITE(A,47)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,47)
        if (IS_BUFFER_OVERRUN(A,47)) {
          UPDATE_RD_ID(A,47)
        } else
        if (IS_BUFFER_FULL(A,47)) {
          UPDATE_BS(A,47,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,47)
        } else
        if(WRITE_ID(A,47) == READ_ID(A,47)) {
          UPDATE_BS(A,47,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF47_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF47_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF47_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<15);
  }
#endif

#ifdef CAN_CHA_BUF48_DIR
  if ((CAN_A.IFRH.R)&(1<<16))
  {
    #if (CAN_CHA_BUF48_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[48].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[48].ID.R;
        MEM_WRITE(A,48)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,48)
        if (IS_BUFFER_OVERRUN(A,48)) {
          UPDATE_RD_ID(A,48)
        } else
        if (IS_BUFFER_FULL(A,48)) {
          UPDATE_BS(A,48,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,48)
        } else
        if(WRITE_ID(A,48) == READ_ID(A,48)) {
          UPDATE_BS(A,48,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF48_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF48_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF48_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<16);
  }
#endif

#ifdef CAN_CHA_BUF49_DIR
  if ((CAN_A.IFRH.R)&(1<<17))
  {
    #if (CAN_CHA_BUF49_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[49].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[49].ID.R;
        MEM_WRITE(A,49)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,49)
        if (IS_BUFFER_OVERRUN(A,49)) {
          UPDATE_RD_ID(A,49)
        } else
        if (IS_BUFFER_FULL(A,49)) {
          UPDATE_BS(A,49,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,49)
        } else
        if(WRITE_ID(A,49) == READ_ID(A,49)) {
          UPDATE_BS(A,49,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF49_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF49_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF49_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<17);
  }
#endif

#ifdef CAN_CHA_BUF50_DIR
  if ((CAN_A.IFRH.R)&(1<<18))
  {
    #if (CAN_CHA_BUF50_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[50].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[50].ID.R;
        MEM_WRITE(A,50)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,50)
        if (IS_BUFFER_OVERRUN(A,50)) {
          UPDATE_RD_ID(A,50)
        } else
        if (IS_BUFFER_FULL(A,50)) {
          UPDATE_BS(A,50,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,50)
        } else
        if(WRITE_ID(A,50) == READ_ID(A,50)) {
          UPDATE_BS(A,50,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF50_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF50_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF50_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<18);
  }
#endif

#ifdef CAN_CHA_BUF51_DIR
  if ((CAN_A.IFRH.R)&(1<<19))
  {
    #if (CAN_CHA_BUF51_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[51].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[51].ID.R;
        MEM_WRITE(A,51)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,51)
        if (IS_BUFFER_OVERRUN(A,51)) {
          UPDATE_RD_ID(A,51)
        } else
        if (IS_BUFFER_FULL(A,51)) {
          UPDATE_BS(A,51,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,51)
        } else
        if(WRITE_ID(A,51) == READ_ID(A,51)) {
          UPDATE_BS(A,51,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF51_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF51_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF51_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<19);
  }
#endif

#ifdef CAN_CHA_BUF52_DIR
  if ((CAN_A.IFRH.R)&(1<<20))
  {
    #if (CAN_CHA_BUF52_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[52].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[52].ID.R;
        MEM_WRITE(A,52)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,52)
        if (IS_BUFFER_OVERRUN(A,52)) {
          UPDATE_RD_ID(A,52)
        } else
        if (IS_BUFFER_FULL(A,52)) {
          UPDATE_BS(A,52,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,52)
        } else
        if(WRITE_ID(A,52) == READ_ID(A,52)) {
          UPDATE_BS(A,52,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF52_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF52_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF52_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<20);
  }
#endif

#ifdef CAN_CHA_BUF53_DIR
  if ((CAN_A.IFRH.R)&(1<<21))
  {
    #if (CAN_CHA_BUF53_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[53].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[53].ID.R;
        MEM_WRITE(A,53)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,53)
        if (IS_BUFFER_OVERRUN(A,53)) {
          UPDATE_RD_ID(A,53)
        } else
        if (IS_BUFFER_FULL(A,53)) {
          UPDATE_BS(A,53,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,53)
        } else
        if(WRITE_ID(A,53) == READ_ID(A,53)) {
          UPDATE_BS(A,53,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF53_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF53_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF53_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<21);
  }
#endif

#ifdef CAN_CHA_BUF54_DIR
  if ((CAN_A.IFRH.R)&(1<<22))
  {
    #if (CAN_CHA_BUF54_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[54].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[54].ID.R;
        MEM_WRITE(A,54)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,54)
        if (IS_BUFFER_OVERRUN(A,54)) {
          UPDATE_RD_ID(A,54)
        } else
        if (IS_BUFFER_FULL(A,54)) {
          UPDATE_BS(A,54,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,54)
        } else
        if(WRITE_ID(A,54) == READ_ID(A,54)) {
          UPDATE_BS(A,54,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF54_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF54_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF54_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<22);
  }
#endif

#ifdef CAN_CHA_BUF55_DIR
  if ((CAN_A.IFRH.R)&(1<<23))
  {
    #if (CAN_CHA_BUF55_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[55].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[55].ID.R;
        MEM_WRITE(A,55)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,55)
        if (IS_BUFFER_OVERRUN(A,55)) {
          UPDATE_RD_ID(A,55)
        } else
        if (IS_BUFFER_FULL(A,55)) {
          UPDATE_BS(A,55,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,55)
        } else
        if(WRITE_ID(A,55) == READ_ID(A,55)) {
          UPDATE_BS(A,55,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF55_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF55_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF55_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<23);
  }
#endif

#ifdef CAN_CHA_BUF56_DIR
  if ((CAN_A.IFRH.R)&(1<<24))
  {
    #if (CAN_CHA_BUF56_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[56].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[56].ID.R;
        MEM_WRITE(A,56)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,56)
        if (IS_BUFFER_OVERRUN(A,56)) {
          UPDATE_RD_ID(A,56)
        } else
        if (IS_BUFFER_FULL(A,56)) {
          UPDATE_BS(A,56,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,56)
        } else
        if(WRITE_ID(A,56) == READ_ID(A,56)) {
          UPDATE_BS(A,56,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF56_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF56_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF56_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<24);
  }
#endif

#ifdef CAN_CHA_BUF57_DIR
  if ((CAN_A.IFRH.R)&(1<<25))
  {
    #if (CAN_CHA_BUF57_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[57].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[57].ID.R;
        MEM_WRITE(A,57)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,57)
        if (IS_BUFFER_OVERRUN(A,57)) {
          UPDATE_RD_ID(A,57)
        } else
        if (IS_BUFFER_FULL(A,57)) {
          UPDATE_BS(A,57,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,57)
        } else
        if(WRITE_ID(A,57) == READ_ID(A,57)) {
          UPDATE_BS(A,57,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF57_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF57_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF57_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<25);
  }
#endif

#ifdef CAN_CHA_BUF58_DIR
  if ((CAN_A.IFRH.R)&(1<<26))
  {
    #if (CAN_CHA_BUF58_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[58].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[58].ID.R;
        MEM_WRITE(A,58)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,58)
        if (IS_BUFFER_OVERRUN(A,58)) {
          UPDATE_RD_ID(A,58)
        } else
        if (IS_BUFFER_FULL(A,58)) {
          UPDATE_BS(A,58,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,58)
        } else
        if(WRITE_ID(A,58) == READ_ID(A,58)) {
          UPDATE_BS(A,58,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF58_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF58_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF58_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<26);
  }
#endif

#ifdef CAN_CHA_BUF59_DIR
  if ((CAN_A.IFRH.R)&(1<<27))
  {
    #if (CAN_CHA_BUF58_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[59].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[59].ID.R;
        MEM_WRITE(A,59)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,59)
        if (IS_BUFFER_OVERRUN(A,59)) {
          UPDATE_RD_ID(A,59)
        } else
        if (IS_BUFFER_FULL(A,59)) {
          UPDATE_BS(A,59,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,59)
        } else
        if(WRITE_ID(A,59) == READ_ID(A,59)) {
          UPDATE_BS(A,59,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF59_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF59_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF59_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<27);
  }
#endif

#ifdef CAN_CHA_BUF60_DIR
  if ((CAN_A.IFRH.R)&(1<<28))
  {
    #if (CAN_CHA_BUF60_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[60].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[60].ID.R;
        MEM_WRITE(A,60)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,60)
        if (IS_BUFFER_OVERRUN(A,60)) {
          UPDATE_RD_ID(A,60)
        } else
        if (IS_BUFFER_FULL(A,60)) {
          UPDATE_BS(A,60,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,60)
        } else
        if(WRITE_ID(A,60) == READ_ID(A,60)) {
          UPDATE_BS(A,60,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF60_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF60_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF60_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<28);
  }
#endif

#ifdef CAN_CHA_BUF61_DIR
  if ((CAN_A.IFRH.R)&(1<<29))
  {
    #if (CAN_CHA_BUF61_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[61].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[61].ID.R;
        MEM_WRITE(A,61)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,61)
        if (IS_BUFFER_OVERRUN(A,61)) {
          UPDATE_RD_ID(A,61)
        } else
        if (IS_BUFFER_FULL(A,61)) {
          UPDATE_BS(A,61,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,61)
        } else
        if(WRITE_ID(A,61) == READ_ID(A,61)) {
          UPDATE_BS(A,61,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF61_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF61_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF61_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<29);
  }
#endif

#ifdef CAN_CHA_BUF62_DIR
  if ((CAN_A.IFRH.R)&(1<<30))
  {
    #if (CAN_CHA_BUF62_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[62].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[62].ID.R;
        MEM_WRITE(A,62)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,62)
        if (IS_BUFFER_OVERRUN(A,62)) {
          UPDATE_RD_ID(A,62)
        } else
        if (IS_BUFFER_FULL(A,62)) {
          UPDATE_BS(A,62,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,62)
        } else
        if(WRITE_ID(A,62) == READ_ID(A,62)) {
          UPDATE_BS(A,62,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF62_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF62_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF62_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(1<<30);
  }
#endif

#ifdef CAN_CHA_BUF63_DIR
  if ((CAN_A.IFRH.R)&(uint32_t)(1<<31))
  {
    #if (CAN_CHA_BUF63_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_A.BUF[63].CS.R & (1<<27))==0) {
        ts=CAN_A.BUF[63].ID.R;
        MEM_WRITE(A,63)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(A,63)
        if (IS_BUFFER_OVERRUN(A,63)) {
          UPDATE_RD_ID(A,63)
        } else
        if (IS_BUFFER_FULL(A,63)) {
          UPDATE_BS(A,63,BUFFER_OVERRUN);
          UPDATE_RD_ID(A,63)
        } else
        if(WRITE_ID(A,63) == READ_ID(A,63)) {
          UPDATE_BS(A,63,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_A.TIMER.R;
        #if CAN_CHA_BUF63_EXC
         ActivateTask(CAN_ExRxDoneChAID);
        #endif
       }
     }
    #else

        #if CAN_CHA_BUF63_EXC
        ActivateTask(CAN_ExTxDoneChAID);
            #endif
    #endif   //  (CAN_CHA_BUF63_DIR==CAN_RX)
    (CAN_A.IFRH.R)&=(uint32_t)(1<<31);
  }
#endif
}

void CAN_CHB_MB0(void){
#ifdef CAN_CHB_BUF0_DIR
#if (CAN_CHB_BUF0_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[0].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[0].ID.R;
    MEM_WRITE(B,0)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,0)

    if (IS_BUFFER_OVERRUN(B,0)) {
      UPDATE_RD_ID(B,0)
    } else
    if (IS_BUFFER_FULL(B,0)) {
      UPDATE_BS(B,0,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,0)
    } else
    if(WRITE_ID(B,0) == READ_ID(B,0)) {
      UPDATE_BS(B,0,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF0_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF0_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<0);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB1(void){
#ifdef CAN_CHB_BUF1_DIR
#if (CAN_CHB_BUF1_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[1].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[1].ID.R;
    MEM_WRITE(B,1)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,1)

    if (IS_BUFFER_OVERRUN(B,1)) {
      UPDATE_RD_ID(B,1)
    } else
    if (IS_BUFFER_FULL(B,1)) {
      UPDATE_BS(B,1,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,1)
    } else
    if(WRITE_ID(B,1) == READ_ID(B,1)) {
      UPDATE_BS(B,1,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF1_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF1_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<1);
#endif
}


#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB2(void){

#ifdef CAN_CHB_BUF2_DIR
#if (CAN_CHB_BUF2_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[2].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[2].ID.R;
    MEM_WRITE(B,2)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,2)
    if (IS_BUFFER_OVERRUN(B,2)) {
      UPDATE_RD_ID(B,2)
    } else
    if (IS_BUFFER_FULL(B,2)) {
      UPDATE_BS(B,2,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,2)
    } else
    if(WRITE_ID(B,2) == READ_ID(B,2)) {
      UPDATE_BS(B,2,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF2_EXC
    ActivateTask(CAN_ExRxDoneChBID);
        #endif
  }
#else
    #if CAN_CHB_BUF2_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<2);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB3(void){

#ifdef CAN_CHB_BUF3_DIR
#if (CAN_CHB_BUF3_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[3].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[3].ID.R;
    MEM_WRITE(B,3)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,3)
    if (IS_BUFFER_OVERRUN(B,3)) {
      UPDATE_RD_ID(B,3)
    } else
    if (IS_BUFFER_FULL(B,3)) {
      UPDATE_BS(B,4,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,3)
    } else
    if(WRITE_ID(B,3) == READ_ID(B,3)) {
      UPDATE_BS(B,3,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF3_EXC
    ActivateTask(CAN_ExRxDoneChBID);
        #endif
  }
 #else
    #if CAN_CHB_BUF3_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<3);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB4(void){
#ifdef CAN_CHB_BUF4_DIR
#if (CAN_CHB_BUF4_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[4].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[4].ID.R;
    MEM_WRITE(B,4)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,4)
    if (IS_BUFFER_OVERRUN(B,4)) {
      UPDATE_RD_ID(B,4)
    } else
    if (IS_BUFFER_FULL(B,4)) {
      UPDATE_BS(B,4,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,4)
    } else
    if(WRITE_ID(B,4) == READ_ID(B,4)) {
      UPDATE_BS(B,4,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF4_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF5_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<4);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */


void CAN_CHB_MB5(void){
#ifdef CAN_CHB_BUF5_DIR
#if (CAN_CHB_BUF5_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[5].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[5].ID.R;
    MEM_WRITE(B,5)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,5)
    if (IS_BUFFER_OVERRUN(B,5)) {
      UPDATE_RD_ID(B,5)
    } else
    if (IS_BUFFER_FULL(B,5)) {
      UPDATE_BS(B,5,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,5)
    } else
    if(WRITE_ID(B,5) == READ_ID(B,5)) {
      UPDATE_BS(B,5,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF5_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }

#else
    #if CAN_CHB_BUF5_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<5);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB6(void){
#ifdef CAN_CHB_BUF6_DIR
#if (CAN_CHB_BUF6_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[6].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[6].ID.R;
        MEM_WRITE(B,6)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,6)
    if (IS_BUFFER_OVERRUN(B,6)) {
      UPDATE_RD_ID(B,6)
    } else
    if (IS_BUFFER_FULL(B,6)) {
      UPDATE_BS(B,6,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,6)
    } else
    if(WRITE_ID(B,6) == READ_ID(B,6)) {
      UPDATE_BS(B,6,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF6_EXC
    ActivateTask(CAN_ExRxDoneChBID);
        #endif
  }
#else
    #if CAN_CHB_BUF6_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<6);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */


void CAN_CHB_MB7(void){
#ifdef CAN_CHB_BUF7_DIR
#if (CAN_CHB_BUF7_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[7].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[7].ID.R;
    MEM_WRITE(B,7)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,7)
    if (IS_BUFFER_OVERRUN(B,7)) {
      UPDATE_RD_ID(B,7)
    } else
    if (IS_BUFFER_FULL(B,7)) {
      UPDATE_BS(B,7,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,7)
    } else
    if(WRITE_ID(B,7) == READ_ID(B,7)) {
      UPDATE_BS(B,7,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF7_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF7_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<7);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB8(void){
#ifdef CAN_CHB_BUF8_DIR
#if (CAN_CHB_BUF8_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[8].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[8].ID.R;
    MEM_WRITE(B,8)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,8)
    if (IS_BUFFER_OVERRUN(B,8)) {
      UPDATE_RD_ID(B,8)
    } else
    if (IS_BUFFER_FULL(B,8)) {
      UPDATE_BS(B,8,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,8)
    } else
    if(WRITE_ID(B,8) == READ_ID(B,8)) {
      UPDATE_BS(B,8,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF8_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF8_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<8);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB9(void){
#ifdef CAN_CHB_BUF9_DIR
#if (CAN_CHB_BUF9_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[9].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[9].ID.R;
    MEM_WRITE(B,9)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,9)
    if (IS_BUFFER_OVERRUN(B,9)) {
      UPDATE_RD_ID(B,9)
    } else
    if (IS_BUFFER_FULL(B,9)) {
      UPDATE_BS(B,9,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,9)
    } else
    if(WRITE_ID(B,9) == READ_ID(B,9)) {
      UPDATE_BS(B,9,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF9_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF9_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<9);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB10(void){
#ifdef CAN_CHB_BUF10_DIR
#if (CAN_CHB_BUF10_DIR==CAN_RX)
vuint32_t ts;
 if ((CAN_B.BUF[10].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[10].ID.R;
    MEM_WRITE(B,10)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,10)
    if (IS_BUFFER_OVERRUN(B,10)) {
      UPDATE_RD_ID(B,10)
    } else
    if (IS_BUFFER_FULL(B,10)) {
      UPDATE_BS(B,10,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,10)
    } else
    if(WRITE_ID(B,10) == READ_ID(B,10)) {
      UPDATE_BS(B,10,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF10_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF10_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<10);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB11(void){
#ifdef CAN_CHB_BUF11_DIR
#if (CAN_CHB_BUF11_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[11].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[11].ID.R;
    MEM_WRITE(B,11)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,11)
    if (IS_BUFFER_OVERRUN(B,11)) {
      UPDATE_RD_ID(B,11)
    } else
    if (IS_BUFFER_FULL(B,11)) {
      UPDATE_BS(B,11,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,11)
    } else
    if(WRITE_ID(B,11) == READ_ID(B,11)) {
      UPDATE_BS(B,11,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF11_EXC
    ActivateTask(CAN_ExRxDoneChBID);
        #endif
  }
#else
    #if CAN_CHB_BUF11_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<11);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB12(void){
#ifdef CAN_CHB_BUF12_DIR
#if (CAN_CHB_BUF12_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[12].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[12].ID.R;
    MEM_WRITE(B,12)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,12)
    if (IS_BUFFER_OVERRUN(B,12)) {
      UPDATE_RD_ID(B,12)
    } else
    if (IS_BUFFER_FULL(B,12)) {
      UPDATE_BS(B,12,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,12)
    } else
    if(WRITE_ID(B,12) == READ_ID(B,12)) {
      UPDATE_BS(B,12,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF12_EXC
    ActivateTask(CAN_ExRxDoneChBID);
        #endif
  }
#else
    #if CAN_CHB_BUF12_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<12);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB13(void){
#ifdef CAN_CHB_BUF13_DIR
#if (CAN_CHB_BUF13_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[13].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[13].ID.R;
    MEM_WRITE(B,13)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,13)
    if (IS_BUFFER_OVERRUN(B,13)) {
      UPDATE_RD_ID(B,13)
    } else
    if (IS_BUFFER_FULL(B,13)) {
      UPDATE_BS(B,13,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,13)
    } else
    if(WRITE_ID(B,13) == READ_ID(B,13)) {
      UPDATE_BS(B,13,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF13_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF13_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<13);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB14(void){
#ifdef CAN_CHB_BUF14_DIR
#if (CAN_CHB_BUF14_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[14].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[14].ID.R;
    MEM_WRITE(B,14)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,14)
    if (IS_BUFFER_OVERRUN(B,14)) {
      UPDATE_RD_ID(B,14)
    } else
    if (IS_BUFFER_FULL(B,14)) {
      UPDATE_BS(B,14,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,14)
    } else
    if(WRITE_ID(B,14) == READ_ID(B,14)) {
      UPDATE_BS(B,14,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF14_EXC
    ActivateTask(CAN_ExRxDoneChBID);
    #endif
  }
#else
    #if CAN_CHB_BUF14_EXC
    ActivateTask(CAN_ExTxDoneChBID);
    #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<14);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB15(void){
#ifdef CAN_CHB_BUF15_DIR
#if (CAN_CHB_BUF15_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_B.BUF[15].CS.R & (1<<27))==0) {
    ts=CAN_B.BUF[15].ID.R;
    MEM_WRITE(B,15)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(B,15)
    if (IS_BUFFER_OVERRUN(B,15)) {
      UPDATE_RD_ID(B,15)
    } else
    if (IS_BUFFER_FULL(B,15)) {
      UPDATE_BS(B,15,BUFFER_OVERRUN);
      UPDATE_RD_ID(B,15)
    } else
    if(WRITE_ID(B,15) == READ_ID(B,15)) {
      UPDATE_BS(B,15,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_B.TIMER.R;
    #if CAN_CHB_BUF15_EXC
    ActivateTask(CAN_ExRxDoneChBID);
        #endif
  }
#else
    #if CAN_CHB_BUF15_EXC
    ActivateTask(CAN_ExTxDoneChBID);
        #endif
#endif
#endif
#ifdef CAN_B
(CAN_B.IFRL.R)&=(1<<15);
#endif
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHB_MB16_31(void){
#ifdef CAN_CHB_BUF16_DIR
  if((CAN_B.IFRL.R)&(1<<16))
  {
    #if (CAN_CHB_BUF16_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[16].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[16].ID.R;
        MEM_WRITE(B,16)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,16)
        if (IS_BUFFER_OVERRUN(B,16)) {
          UPDATE_RD_ID(B,16)
        } else
        if (IS_BUFFER_FULL(B,16)) {
          UPDATE_BS(B,16,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,16)
        } else
        if(WRITE_ID(B,16) == READ_ID(B,16)) {
          UPDATE_BS(B,16,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF16_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
       }
     }
    #else
        #if CAN_CHB_BUF16_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<16);
  }
#endif

#ifdef CAN_CHB_BUF17_DIR
  if((CAN_B.IFRL.R)&(1<<17))
  {
    #if (CAN_CHB_BUF17_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[17].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[17].ID.R;
        MEM_WRITE(B,17)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,17)
        if (IS_BUFFER_OVERRUN(B,17)) {
          UPDATE_RD_ID(B,17)
        } else
        if (IS_BUFFER_FULL(B,17)) {
          UPDATE_BS(B,17,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,17)
        } else
        if(WRITE_ID(B,17) == READ_ID(B,17)) {
          UPDATE_BS(B,17,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF17_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF17_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<17);
  }
#endif

#ifdef CAN_CHB_BUF18_DIR
  if((CAN_B.IFRL.R)&(1<<18))
  {
    #if (CAN_CHB_BUF18_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[18].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[18].ID.R;
        MEM_WRITE(B,18)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,18)
        if (IS_BUFFER_OVERRUN(B,18)) {
          UPDATE_RD_ID(B,18)
        } else
        if (IS_BUFFER_FULL(B,18)) {
          UPDATE_BS(B,18,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,18)
        } else
        if(WRITE_ID(B,18) == READ_ID(B,18)) {
          UPDATE_BS(B,18,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF18_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
      }
     }
    #else
        #if CAN_CHB_BUF18_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<18);
  }
#endif

#ifdef CAN_CHB_BUF19_DIR
  if((CAN_B.IFRL.R)&(1<<19))
  {
    #if (CAN_CHB_BUF19_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[19].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[19].ID.R;
        MEM_WRITE(B,19)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,19)
        if (IS_BUFFER_OVERRUN(B,19)) {
          UPDATE_RD_ID(B,19)
        } else
        if (IS_BUFFER_FULL(B,19)) {
          UPDATE_BS(B,19,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,19)
        } else
        if(WRITE_ID(B,19) == READ_ID(B,19)) {
          UPDATE_BS(B,19,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF19_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF19_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<19);
  }
#endif

#ifdef CAN_CHB_BUF20_DIR
  if((CAN_B.IFRL.R)&(1<<20))
  {
    #if (CAN_CHB_BUF20_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[20].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[20].ID.R;
        MEM_WRITE(B,20)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,20)
        if (IS_BUFFER_OVERRUN(B,20)) {
          UPDATE_RD_ID(B,20)
        } else
        if (IS_BUFFER_FULL(B,20)) {
          UPDATE_BS(B,20,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,20)
        } else
        if(WRITE_ID(B,20) == READ_ID(B,20)) {
          UPDATE_BS(B,20,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF20_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
      }
     }
    #else
        #if CAN_CHB_BUF20_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<20);
  }
#endif

#ifdef CAN_CHB_BUF21_DIR
  if((CAN_B.IFRL.R)&(1<<21))
  {
    #if (CAN_CHB_BUF21_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[21].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[21].ID.R;
        MEM_WRITE(B,21)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,21)
        if (IS_BUFFER_OVERRUN(B,21)) {
          UPDATE_RD_ID(B,21)
        } else
        if (IS_BUFFER_FULL(B,21)) {
          UPDATE_BS(B,21,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,21)
        } else
        if(WRITE_ID(B,21) == READ_ID(B,21)) {
          UPDATE_BS(B,21,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF21_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF21_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<21);
  }
#endif

#ifdef CAN_CHB_BUF22_DIR
  if((CAN_B.IFRL.R)&(1<<22))
  {
    #if (CAN_CHB_BUF22_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[22].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[22].ID.R;
        MEM_WRITE(B,22)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,22)
        if (IS_BUFFER_OVERRUN(B,22)) {
          UPDATE_RD_ID(B,22)
        } else
        if (IS_BUFFER_FULL(B,22)) {
          UPDATE_BS(B,22,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,22)
        } else
        if(WRITE_ID(B,22) == READ_ID(B,22)) {
          UPDATE_BS(B,22,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF22_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
      }
     }
    #else
        #if CAN_CHB_BUF22_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<22);
  }
#endif

#ifdef CAN_CHB_BUF23_DIR
  if((CAN_B.IFRL.R)&(1<<23))
  {
    #if (CAN_CHB_BUF23_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[23].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[23].ID.R;
        MEM_WRITE(B,23)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,23)
        if (IS_BUFFER_OVERRUN(B,23)) {
          UPDATE_RD_ID(B,23)
        } else
        if (IS_BUFFER_FULL(B,23)) {
          UPDATE_BS(B,23,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,23)
        } else
        if(WRITE_ID(B,23) == READ_ID(B,23)) {
          UPDATE_BS(B,23,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF23_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF23_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<23);
  }
#endif

#ifdef CAN_CHB_BUF24_DIR
  if((CAN_B.IFRL.R)&(1<<24))
  {
    #if (CAN_CHB_BUF24_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[24].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[24].ID.R;
        MEM_WRITE(B,24)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,24)
        if (IS_BUFFER_OVERRUN(B,24)) {
          UPDATE_RD_ID(B,24)
        } else
        if (IS_BUFFER_FULL(B,24)) {
          UPDATE_BS(B,24,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,24)
        } else
        if(WRITE_ID(B,24) == READ_ID(B,24)) {
          UPDATE_BS(B,24,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF24_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF24_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<24);
  }
#endif

#ifdef CAN_CHB_BUF25_DIR
  if((CAN_B.IFRL.R)&(1<<25))
  {
    #if (CAN_CHB_BUF25_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[25].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[25].ID.R;
        MEM_WRITE(B,25)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,25)
        if (IS_BUFFER_OVERRUN(B,25)) {
          UPDATE_RD_ID(B,25)
        } else
        if (IS_BUFFER_FULL(B,25)) {
          UPDATE_BS(B,25,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,25)
        } else
        if(WRITE_ID(B,25) == READ_ID(B,25)) {
          UPDATE_BS(B,25,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF25_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
      }
     }
    #else
        #if CAN_CHB_BUF25_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<25);
  }
#endif

#ifdef CAN_CHB_BUF26_DIR
  if((CAN_B.IFRL.R)&(1<<26))
  {
    #if (CAN_CHB_BUF26_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[26].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[26].ID.R;
        MEM_WRITE(B,26)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,26)
        if (IS_BUFFER_OVERRUN(B,26)) {
          UPDATE_RD_ID(B,26)
        } else
        if (IS_BUFFER_FULL(B,26)) {
          UPDATE_BS(B,26,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,26)
        } else
        if(WRITE_ID(B,26) == READ_ID(B,26)) {
          UPDATE_BS(B,26,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF26_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
      }
     }
    #else
        #if CAN_CHB_BUF26_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<26);
  }
#endif

#ifdef CAN_CHB_BUF27_DIR
  if((CAN_B.IFRL.R)&(1<<27))
  {
    #if (CAN_CHB_BUF27_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[27].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[27].ID.R;
        MEM_WRITE(B,27)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,27)
        if (IS_BUFFER_OVERRUN(B,27)) {
          UPDATE_RD_ID(B,27)
        } else
        if (IS_BUFFER_FULL(B,27)) {
          UPDATE_BS(B,27,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,27)
        } else
        if(WRITE_ID(B,27) == READ_ID(B,27)) {
          UPDATE_BS(B,27,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF27_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF27_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<27);
  }
#endif

#ifdef CAN_CHB_BUF28_DIR
  if((CAN_B.IFRL.R)&(1<<28))
  {
    #if (CAN_CHB_BUF28_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[28].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[28].ID.R;
        MEM_WRITE(B,28)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,28)
        if (IS_BUFFER_OVERRUN(B,28)) {
          UPDATE_RD_ID(B,28)
        } else
        if (IS_BUFFER_FULL(B,28)) {
          UPDATE_BS(B,28,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,28)
        } else
        if(WRITE_ID(B,28) == READ_ID(B,4)) {
          UPDATE_BS(B,28,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF28_EXC
        ActivateTask(CAN_ExRxDoneChBID);
        #endif
      }
     }
    #else
        #if CAN_CHB_BUF28_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<28);
  }
#endif

#ifdef CAN_CHB_BUF29_DIR
  if((CAN_B.IFRL.R)&(1<<29))
  {
    #if (CAN_CHB_BUF29_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[29].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[29].ID.R;
        MEM_WRITE(B,29)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,29)
        if (IS_BUFFER_OVERRUN(B,29)) {
          UPDATE_RD_ID(B,29)
        } else
        if (IS_BUFFER_FULL(B,29)) {
          UPDATE_BS(B,29,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,29)
        } else
        if(WRITE_ID(B,29) == READ_ID(B,29)) {
          UPDATE_BS(B,29,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF29_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF29_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<29);
  }
#endif

#ifdef CAN_CHB_BUF30_DIR
  if((CAN_B.IFRL.R)&(1<<30))
  {
    #if (CAN_CHB_BUF30_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[30].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[30].ID.R;
        MEM_WRITE(B,30)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,30)
        if (IS_BUFFER_OVERRUN(B,30)) {
          UPDATE_RD_ID(B,30)
        } else
        if (IS_BUFFER_FULL(B,30)) {
          UPDATE_BS(B,30,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,30)
        } else
        if(WRITE_ID(B,30) == READ_ID(B,30)) {
          UPDATE_BS(B,30,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF30_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF30_EXC
        ActivateTask(CAN_ExTxDoneChBID);
        #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<30);
  }
#endif

#ifdef CAN_CHB_BUF31_DIR
  if((CAN_B.IFRL.R)&(1<<31))
  {
    #if (CAN_CHB_BUF31_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_B.BUF[31].CS.R & (1<<27))==0) {
        ts=CAN_B.BUF[31].ID.R;
        MEM_WRITE(B,31)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(B,31)
        if (IS_BUFFER_OVERRUN(B,31)) {
          UPDATE_RD_ID(B,31)
        } else
        if (IS_BUFFER_FULL(B,31)) {
          UPDATE_BS(B,31,BUFFER_OVERRUN);
          UPDATE_RD_ID(B,31)
        } else
        if(WRITE_ID(B,31) == READ_ID(B,31)) {
          UPDATE_BS(B,31,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_B.TIMER.R;
        #if CAN_CHB_BUF31_EXC
        ActivateTask(CAN_ExRxDoneChBID);
            #endif
      }
     }
    #else
        #if CAN_CHB_BUF31_EXC
        ActivateTask(CAN_ExTxDoneChBID);
            #endif
    #endif
    (CAN_B.IFRL.R)&=(1<<31);
  }
#endif

}


#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB0(void){
#ifdef CAN_CHC_BUF0_DIR
#if (CAN_CHC_BUF0_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[0].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[0].ID.R;
    MEM_WRITE(C,0)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,0)

    if (IS_BUFFER_OVERRUN(C,0)) {
      UPDATE_RD_ID(C,0)
    } else
    if (IS_BUFFER_FULL(C,0)) {
      UPDATE_BS(C,0,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,0)
    } else
    if(WRITE_ID(C,0) == READ_ID(C,0)) {
      UPDATE_BS(C,0,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF0_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF0_EXC
    ActivateTask(CAN_ExTxDoneChCID);
        #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<0);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB1(void){
#ifdef CAN_CHC_BUF1_DIR
#if (CAN_CHC_BUF1_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[1].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[1].ID.R;
    MEM_WRITE(C,1)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,1)

    if (IS_BUFFER_OVERRUN(C,1)) {
      UPDATE_RD_ID(C,1)
    } else
    if (IS_BUFFER_FULL(C,1)) {
      UPDATE_BS(C,1,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,1)
    } else
    if(WRITE_ID(C,1) == READ_ID(C,1)) {
      UPDATE_BS(C,1,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF1_EXC
  ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF1_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<1);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB2(void){

#ifdef CAN_CHC_BUF2_DIR
#if (CAN_CHC_BUF2_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[2].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[2].ID.R;
    MEM_WRITE(C,2)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,2)
    if (IS_BUFFER_OVERRUN(C,2)) {
      UPDATE_RD_ID(C,2)
    } else
    if (IS_BUFFER_FULL(C,2)) {
      UPDATE_BS(C,2,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,2)
    } else
    if(WRITE_ID(C,2) == READ_ID(C,2)) {
      UPDATE_BS(C,2,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF2_EXC
   ActivateTask(CAN_ExRxDoneChCID);
        #endif
  }
#else
    #if CAN_CHC_BUF2_EXC
    ActivateTask(CAN_ExTxDoneChCID);
        #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<2);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB3(void){

#ifdef CAN_CHC_BUF3_DIR
#if (CAN_CHC_BUF3_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[3].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[3].ID.R;
    MEM_WRITE(C,3)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,3)
    if (IS_BUFFER_OVERRUN(C,3)) {
      UPDATE_RD_ID(C,3)
    } else
    if (IS_BUFFER_FULL(C,3)) {
      UPDATE_BS(C,4,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,3)
    } else
    if(WRITE_ID(C,3) == READ_ID(C,3)) {
      UPDATE_BS(C,3,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF3_EXC
   ActivateTask(CAN_ExRxDoneChCID);
        #endif
  }
 #else
    #if CAN_CHC_BUF3_EXC
    ActivateTask(CAN_ExTxDoneChCID);
        #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<3);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB4(void){
#ifdef CAN_CHC_BUF4_DIR
#if (CAN_CHC_BUF4_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[4].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[4].ID.R;
    MEM_WRITE(C,4)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,4)
    if (IS_BUFFER_OVERRUN(C,4)) {
      UPDATE_RD_ID(C,4)
    } else
    if (IS_BUFFER_FULL(C,4)) {
      UPDATE_BS(C,4,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,4)
    } else
    if(WRITE_ID(C,4) == READ_ID(C,4)) {
      UPDATE_BS(C,4,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF4_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF4_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<4);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB5(void){
#ifdef CAN_CHC_BUF5_DIR
#if (CAN_CHC_BUF5_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[5].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[5].ID.R;
    MEM_WRITE(C,5)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,5)
    if (IS_BUFFER_OVERRUN(C,5)) {
      UPDATE_RD_ID(C,5)
    } else
    if (IS_BUFFER_FULL(C,5)) {
      UPDATE_BS(C,5,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,5)
    } else
    if(WRITE_ID(C,5) == READ_ID(C,5)) {
      UPDATE_BS(C,5,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF5_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }

#else
    #if CAN_CHC_BUF5_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<5);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB6(void){
#ifdef CAN_CHC_BUF6_DIR
#if (CAN_CHC_BUF6_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[6].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[6].ID.R;
        MEM_WRITE(C,6)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,6)
    if (IS_BUFFER_OVERRUN(C,6)) {
      UPDATE_RD_ID(C,6)
    } else
    if (IS_BUFFER_FULL(C,6)) {
      UPDATE_BS(C,6,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,6)
    } else
    if(WRITE_ID(C,6) == READ_ID(C,6)) {
      UPDATE_BS(C,6,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF6_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF6_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<6);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB7(void){
#ifdef CAN_CHC_BUF7_DIR
#if (CAN_CHC_BUF7_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[7].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[7].ID.R;
    MEM_WRITE(C,7)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,7)
    if (IS_BUFFER_OVERRUN(C,7)) {
      UPDATE_RD_ID(C,7)
    } else
    if (IS_BUFFER_FULL(C,7)) {
      UPDATE_BS(C,7,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,7)
    } else
    if(WRITE_ID(C,7) == READ_ID(C,7)) {
      UPDATE_BS(C,7,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF7_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF7_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<7);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB8(void){
#ifdef CAN_CHC_BUF8_DIR
#if (CAN_CHC_BUF8_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[8].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[8].ID.R;
    MEM_WRITE(C,8)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,8)
    if (IS_BUFFER_OVERRUN(C,8)) {
      UPDATE_RD_ID(C,8)
    } else
    if (IS_BUFFER_FULL(C,8)) {
      UPDATE_BS(C,8,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,8)
    } else
    if(WRITE_ID(C,8) == READ_ID(C,8)) {
      UPDATE_BS(C,8,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF8_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF8_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<8);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB9(void){
#ifdef CAN_CHC_BUF9_DIR
#if (CAN_CHC_BUF9_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[9].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[9].ID.R;
    MEM_WRITE(C,9)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,9)
    if (IS_BUFFER_OVERRUN(C,9)) {
      UPDATE_RD_ID(C,9)
    } else
    if (IS_BUFFER_FULL(C,9)) {
      UPDATE_BS(C,9,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,9)
    } else
    if(WRITE_ID(C,9) == READ_ID(C,9)) {
      UPDATE_BS(C,9,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF9_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF9_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<9);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB10(void){
#ifdef CAN_CHC_BUF10_DIR
#if (CAN_CHC_BUF10_DIR==CAN_RX)
vuint32_t ts;
 if ((CAN_C.BUF[10].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[10].ID.R;
    MEM_WRITE(C,10)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,10)
    if (IS_BUFFER_OVERRUN(C,10)) {
      UPDATE_RD_ID(C,10)
    } else
    if (IS_BUFFER_FULL(C,10)) {
      UPDATE_BS(C,10,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,10)
    } else
    if(WRITE_ID(C,10) == READ_ID(C,10)) {
      UPDATE_BS(C,10,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF10_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF10_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<10);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB11(void){
#ifdef CAN_CHC_BUF11_DIR
#if (CAN_CHC_BUF11_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[11].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[11].ID.R;
    MEM_WRITE(C,11)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,11)
    if (IS_BUFFER_OVERRUN(C,11)) {
      UPDATE_RD_ID(C,11)
    } else
    if (IS_BUFFER_FULL(C,11)) {
      UPDATE_BS(C,11,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,11)
    } else
    if(WRITE_ID(C,11) == READ_ID(C,11)) {
      UPDATE_BS(C,11,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF11_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF11_EXC
    ActivateTask(CAN_ExTxDoneChCID);
        #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<11);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB12(void){
#ifdef CAN_CHC_BUF12_DIR
#if (CAN_CHC_BUF12_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[12].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[12].ID.R;
    MEM_WRITE(C,12)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,12)
    if (IS_BUFFER_OVERRUN(C,12)) {
      UPDATE_RD_ID(C,12)
    } else
    if (IS_BUFFER_FULL(C,12)) {
      UPDATE_BS(C,12,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,12)
    } else
    if(WRITE_ID(C,12) == READ_ID(C,12)) {
      UPDATE_BS(C,12,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF12_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF12_EXC
    ActivateTask(CAN_ExTxDoneChCID);
        #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<12);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB13(void){
#ifdef CAN_CHC_BUF13_DIR
#if (CAN_CHC_BUF13_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[13].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[13].ID.R;
    MEM_WRITE(C,13)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,13)
    if (IS_BUFFER_OVERRUN(C,13)) {
      UPDATE_RD_ID(C,13)
    } else
    if (IS_BUFFER_FULL(C,13)) {
      UPDATE_BS(C,13,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,13)
    } else
    if(WRITE_ID(C,13) == READ_ID(C,13)) {
      UPDATE_BS(C,13,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF13_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF13_EXC
    ActivateTask(CAN_ExTxDoneChCID);
        #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<13);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB14(void){
#ifdef CAN_CHC_BUF14_DIR
#if (CAN_CHC_BUF14_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[14].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[14].ID.R;
    MEM_WRITE(C,14)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,14)
    if (IS_BUFFER_OVERRUN(C,14)) {
      UPDATE_RD_ID(C,14)
    } else
    if (IS_BUFFER_FULL(C,14)) {
      UPDATE_BS(C,14,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,14)
    } else
    if(WRITE_ID(C,14) == READ_ID(C,14)) {
      UPDATE_BS(C,14,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF14_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF14_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<14);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB15(void){
#ifdef CAN_CHC_BUF15_DIR
#if (CAN_CHC_BUF15_DIR==CAN_RX)
vuint32_t ts;
  if ((CAN_C.BUF[15].CS.R & (1<<27))==0) {
    ts=CAN_C.BUF[15].ID.R;
    MEM_WRITE(C,15)   /* without ; for MISRA 14.3 */
    UPDATE_WR_ID(C,15)
    if (IS_BUFFER_OVERRUN(C,15)) {
      UPDATE_RD_ID(C,15)
    } else
    if (IS_BUFFER_FULL(C,15)) {
      UPDATE_BS(C,15,BUFFER_OVERRUN);
      UPDATE_RD_ID(C,15)
    } else
    if(WRITE_ID(C,15) == READ_ID(C,15)) {
      UPDATE_BS(C,15,BUFFER_FULL);
    }
    else
    {
    }
    ts=CAN_C.TIMER.R;
    #if CAN_CHC_BUF15_EXC
    ActivateTask(CAN_ExRxDoneChCID);
    #endif
  }
#else
    #if CAN_CHC_BUF15_EXC
    ActivateTask(CAN_ExTxDoneChCID);
    #endif
#endif
#endif
(CAN_C.IFRL.R)&=(1<<15);
}

#ifdef __MWERKS__
#pragma force_active off
#pragma force_active on
#endif /* __MWERKS__ */

void CAN_CHC_MB16_31(void){
#ifdef CAN_CHC_BUF16_DIR
  if((CAN_C.IFRL.R)&(1<<16))
  {
    #if (CAN_CHC_BUF16_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[16].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[16].ID.R;
        MEM_WRITE(C,16)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,16)
        if (IS_BUFFER_OVERRUN(C,16)) {
          UPDATE_RD_ID(C,16)
        } else
        if (IS_BUFFER_FULL(C,16)) {
          UPDATE_BS(C,16,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,16)
        } else
        if(WRITE_ID(C,16) == READ_ID(C,16)) {
          UPDATE_BS(C,16,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF16_EXC
       ActivateTask(CAN_ExRxDoneChCID);
        #endif
       }
     }
    #else
        #if CAN_CHC_BUF16_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<16);
  }
#endif

#ifdef CAN_CHC_BUF17_DIR
  if((CAN_C.IFRL.R)&(1<<17))
  {
    #if (CAN_CHC_BUF17_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[17].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[17].ID.R;
        MEM_WRITE(C,17)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,17)
        if (IS_BUFFER_OVERRUN(C,17)) {
          UPDATE_RD_ID(C,17)
        } else
        if (IS_BUFFER_FULL(C,17)) {
          UPDATE_BS(C,17,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,17)
        } else
        if(WRITE_ID(C,17) == READ_ID(C,17)) {
          UPDATE_BS(C,17,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF17_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF17_EXC
        ActivateTask(CAN_ExTxDoneChCID);
        #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<17);
  }
#endif

#ifdef CAN_CHC_BUF18_DIR
  if((CAN_C.IFRL.R)&(1<<18))
  {
    #if (CAN_CHC_BUF18_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[18].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[18].ID.R;
        MEM_WRITE(C,18)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,18)
        if (IS_BUFFER_OVERRUN(C,18)) {
          UPDATE_RD_ID(C,18)
        } else
        if (IS_BUFFER_FULL(C,18)) {
          UPDATE_BS(C,18,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,18)
        } else
        if(WRITE_ID(C,18) == READ_ID(C,18)) {
          UPDATE_BS(C,18,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF18_EXC
       ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF18_EXC
        ActivateTask(CAN_ExTxDoneChCID);
        #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<18);
  }
#endif

#ifdef CAN_CHC_BUF19_DIR
  if((CAN_C.IFRL.R)&(1<<19))
  {
    #if (CAN_CHC_BUF19_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[19].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[19].ID.R;
        MEM_WRITE(C,19)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,19)
        if (IS_BUFFER_OVERRUN(C,19)) {
          UPDATE_RD_ID(C,19)
        } else
        if (IS_BUFFER_FULL(C,19)) {
          UPDATE_BS(C,19,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,19)
        } else
        if(WRITE_ID(C,19) == READ_ID(C,19)) {
          UPDATE_BS(C,19,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF19_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF19_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<19);
  }
#endif

#ifdef CAN_CHC_BUF20_DIR
  if((CAN_C.IFRL.R)&(1<<20))
  {
    #if (CAN_CHC_BUF20_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[20].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[20].ID.R;
        MEM_WRITE(C,20)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,20)
        if (IS_BUFFER_OVERRUN(C,20)) {
          UPDATE_RD_ID(C,20)
        } else
        if (IS_BUFFER_FULL(C,20)) {
          UPDATE_BS(C,20,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,20)
        } else
        if(WRITE_ID(C,20) == READ_ID(C,20)) {
          UPDATE_BS(C,20,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF20_EXC
       ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF20_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<20);
  }
#endif

#ifdef CAN_CHC_BUF21_DIR
  if((CAN_C.IFRL.R)&(1<<21))
  {
    #if (CAN_CHC_BUF21_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[21].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[21].ID.R;
        MEM_WRITE(C,21)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,21)
        if (IS_BUFFER_OVERRUN(C,21)) {
          UPDATE_RD_ID(C,21)
        } else
        if (IS_BUFFER_FULL(C,21)) {
          UPDATE_BS(C,21,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,21)
        } else
        if(WRITE_ID(C,21) == READ_ID(C,21)) {
          UPDATE_BS(C,21,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF21_EXC
       ActivateTask(CAN_ExRxDoneChCID);
            #endif
      }
     }
    #else
        #if CAN_CHC_BUF21_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<21);
  }
#endif

#ifdef CAN_CHC_BUF22_DIR
  if((CAN_C.IFRL.R)&(1<<22))
  {
    #if (CAN_CHC_BUF22_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[22].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[22].ID.R;
        MEM_WRITE(C,22)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,22)
        if (IS_BUFFER_OVERRUN(C,22)) {
          UPDATE_RD_ID(C,22)
        } else
        if (IS_BUFFER_FULL(C,22)) {
          UPDATE_BS(C,22,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,22)
        } else
        if(WRITE_ID(C,22) == READ_ID(C,22)) {
          UPDATE_BS(C,22,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF22_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF22_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<22);
  }
#endif

#ifdef CAN_CHC_BUF23_DIR
  if((CAN_C.IFRL.R)&(1<<23))
  {
    #if (CAN_CHC_BUF23_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[23].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[23].ID.R;
        MEM_WRITE(C,23)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,23)
        if (IS_BUFFER_OVERRUN(C,23)) {
          UPDATE_RD_ID(C,23)
        } else
        if (IS_BUFFER_FULL(C,23)) {
          UPDATE_BS(C,23,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,23)
        } else
        if(WRITE_ID(C,23) == READ_ID(C,23)) {
          UPDATE_BS(C,23,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF23_EXC
       ActivateTask(CAN_ExRxDoneChCID);
            #endif
      }
     }
    #else
        #if CAN_CHC_BUF23_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<23);
  }
#endif

#ifdef CAN_CHC_BUF24_DIR
  if((CAN_C.IFRL.R)&(1<<24))
  {
    #if (CAN_CHC_BUF24_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[24].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[24].ID.R;
        MEM_WRITE(C,24)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,24)
        if (IS_BUFFER_OVERRUN(C,24)) {
          UPDATE_RD_ID(C,24)
        } else
        if (IS_BUFFER_FULL(C,24)) {
          UPDATE_BS(C,24,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,24)
        } else
        if(WRITE_ID(C,24) == READ_ID(C,24)) {
          UPDATE_BS(C,24,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF24_EXC
       ActivateTask(CAN_ExRxDoneChCID);
            #endif
      }
     }
    #else
        #if CAN_CHC_BUF24_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<24);
  }
#endif

#ifdef CAN_CHC_BUF25_DIR
  if((CAN_C.IFRL.R)&(1<<25))
  {
    #if (CAN_CHC_BUF25_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[25].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[25].ID.R;
        MEM_WRITE(C,25)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,25)
        if (IS_BUFFER_OVERRUN(C,25)) {
          UPDATE_RD_ID(C,25)
        } else
        if (IS_BUFFER_FULL(C,25)) {
          UPDATE_BS(C,25,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,25)
        } else
        if(WRITE_ID(C,25) == READ_ID(C,25)) {
          UPDATE_BS(C,25,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF25_EXC
       ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF25_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<25);
  }
#endif

#ifdef CAN_CHC_BUF26_DIR
  if((CAN_C.IFRL.R)&(1<<26))
  {
    #if (CAN_CHC_BUF26_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[26].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[26].ID.R;
        MEM_WRITE(C,26)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,26)
        if (IS_BUFFER_OVERRUN(C,26)) {
          UPDATE_RD_ID(C,26)
        } else
        if (IS_BUFFER_FULL(C,26)) {
          UPDATE_BS(C,26,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,26)
        } else
        if(WRITE_ID(C,26) == READ_ID(C,26)) {
          UPDATE_BS(C,26,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF26_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF26_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<26);
  }
#endif

#ifdef CAN_CHC_BUF27_DIR
  if((CAN_C.IFRL.R)&(1<<27))
  {
    #if (CAN_CHC_BUF27_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[27].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[27].ID.R;
        MEM_WRITE(C,27)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,27)
        if (IS_BUFFER_OVERRUN(C,27)) {
          UPDATE_RD_ID(C,27)
        } else
        if (IS_BUFFER_FULL(C,27)) {
          UPDATE_BS(C,27,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,27)
        } else
        if(WRITE_ID(C,27) == READ_ID(C,27)) {
          UPDATE_BS(C,27,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF27_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF27_EXC
        ActivateTask(CAN_ExTxDoneChCID);
            #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<27);
  }
#endif

#ifdef CAN_CHC_BUF28_DIR
  if((CAN_C.IFRL.R)&(1<<28))
  {
    #if (CAN_CHC_BUF28_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[28].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[28].ID.R;
        MEM_WRITE(C,28)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,28)
        if (IS_BUFFER_OVERRUN(C,28)) {
          UPDATE_RD_ID(C,28)
        } else
        if (IS_BUFFER_FULL(C,28)) {
          UPDATE_BS(C,28,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,28)
        } else
        if(WRITE_ID(C,28) == READ_ID(C,28)) {
          UPDATE_BS(C,28,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF28_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF28_EXC
        ActivateTask(CAN_ExTxDoneChCID);
        #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<28);
  }
#endif

#ifdef CAN_CHC_BUF29_DIR
  if((CAN_C.IFRL.R)&(1<<29))
  {
    #if (CAN_CHC_BUF29_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[29].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[29].ID.R;
        MEM_WRITE(C,29)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,29)
        if (IS_BUFFER_OVERRUN(C,29)) {
          UPDATE_RD_ID(C,29)
        } else
        if (IS_BUFFER_FULL(C,29)) {
          UPDATE_BS(C,29,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,29)
        } else
        if(WRITE_ID(C,29) == READ_ID(C,29)) {
          UPDATE_BS(C,29,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF29_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF29_EXC
        ActivateTask(CAN_ExTxDoneChCID);
        #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<29);
  }
#endif

#ifdef CAN_CHC_BUF30_DIR
  if((CAN_C.IFRL.R)&(1<<30))
  {
    #if (CAN_CHC_BUF30_DIR==CAN_RX)
    {
      vuint32_t ts;
      if ((CAN_C.BUF[30].CS.R & (1<<27))==0) {
        ts=CAN_C.BUF[30].ID.R;
        MEM_WRITE(C,30)   /* without ; for MISRA 14.3 */
        UPDATE_WR_ID(C,30)
        if (IS_BUFFER_OVERRUN(C,30)) {
          UPDATE_RD_ID(C,30)
        } else
        if (IS_BUFFER_FULL(C,30)) {
          UPDATE_BS(C,30,BUFFER_OVERRUN);
          UPDATE_RD_ID(C,30)
        } else
        if(WRITE_ID(C,30) == READ_ID(C,30)) {
          UPDATE_BS(C,30,BUFFER_FULL);
        }
        else
        {
        }
        ts=CAN_C.TIMER.R;
        #if CAN_CHC_BUF30_EXC
        ActivateTask(CAN_ExRxDoneChCID);
        #endif
      }
     }
    #else
        #if CAN_CHC_BUF30_EXC
        ActivateTask(CAN_ExTxDoneChCID);
        #endif
    #endif
    (CAN_C.IFRL.R)&=(1<<30);
  }
#endif

#ifdef CAN_CHC_BUF31_DIR
  if((CAN_C.IFRL.R)&(uint32_t)(1<<31))
  {
    #if (CAN_CHC_BUF31_DIR==CAN_RX)
    {
    vuint32_t ts;
    if ((CAN_C.BUF[31].CS.R & (1<<27))==0) {
      ts=CAN_C.BUF[31].ID.R;
      MEM_WRITE(C,31)   /* without ; for MISRA 14.3 */
      UPDATE_WR_ID(C,31)
      if (IS_BUFFER_OVERRUN(C,31)) {
        UPDATE_RD_ID(C,31)
      } else
      if (IS_BUFFER_FULL(C,31)) {
        UPDATE_BS(C,31,BUFFER_OVERRUN);
        UPDATE_RD_ID(C,31)
      } else
      if(WRITE_ID(C,31) == READ_ID(C,31)) {
        UPDATE_BS(C,31,BUFFER_FULL);
      }
      else
      {
      }
      ts=CAN_C.TIMER.R;
      #if CAN_CHC_BUF31_EXC
     ActivateTask(CAN_ExRxDoneChCID);
        #endif
    }
    }
    #else
      #if CAN_CHC_BUF31_EXC
      ActivateTask(CAN_ExTxDoneChCID);
      #endif
    #endif
    (CAN_C.IFRL.R)&=(uint32_t)(1<<31);
  }
#endif

}

#ifdef __MWERKS__
#pragma force_active off
#endif /* __MWERKS__ */


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif
