#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_IONMGM_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 

//Vector of steps for the load (IonMgm module) [%]
__declspec(section ".calib") uint16_T BKLOADION[9] = 
{
 1920, 2560, 3200, 3840, 5120, 6400, 7680, 8960, 10240
};
//Rpm Breakpoint vector for VTCHDELAY [rpm]
__declspec(section ".calib") uint16_T BKRPMCHDELAY[3] = 
{
   3000,   4000,   8000
};
//Vector of steps for the engine speed (IonMgm module) [rpm]
__declspec(section ".calib") uint16_T BKRPMION[12] = 
{
   1600,   2000,   3000,   4000,   5000,   6000,   6500,   7000,   7500,   8000,   8500,   9000
};
//Confidence interval on chemical phase nominal duration [degree]
__declspec(section ".calib") uint8_T CHBAND = 128;   //( 8.0000*16)
//Angle limit to find the start of the chemical phase [degree]
__declspec(section ".calib") uint8_T CHEND = 128;   //( 8.0000*16)
//Ion current derivative threshold to detect the first peak [uA/deg]
__declspec(section ".calib") uint16_T DEDGE = 5760;   //( 180.00000*32)
//IONLATESTCH [gain]
__declspec(section ".calib") uint8_T IONLATESTCH = 64;   //(0.5000000*128)
//IONLATESTEDGE [gain]
__declspec(section ".calib") uint8_T IONLATESTEDGE = 38;   //(0.2968750*128)
#ifdef _BUILD_ANGLESTAMPS_
//Filter constant for ThPeakIdAngle [coeff]
__declspec(section ".calib") uint16_T KFTHPKIDANG = 492;   //(0.03002929687500*16384)
#endif
//Max dwell time to detect the ion chemical phase [us]
__declspec(section ".calib") uint32_T MAXDWELLION =       65535;   //      65535
//Max spark time to detect the ion chemical phase [us]
__declspec(section ".calib") uint16_T MAXSPARKION =  65535;   // 65535
//Ion high gradient selection threshold [uA/deg]
__declspec(section ".calib") int32_T MIN_DION_H = 32;   //(   1.00000*32)
//Ion high gradient selection threshold [uA/deg]
__declspec(section ".calib") int32_T MIN_DION_L = -32;   //(  -1.00000*32)
//Table of the chemical phase nominal duration [degree]
__declspec(section ".calib") uint8_T TBCHNOMDURATION[12][9] = 
{
 { 90, 76, 62, 50, 34, 30, 28, 28, 28 },
 { 90, 76, 62, 50, 34, 30, 28, 28, 28 },
 { 68, 56, 44, 34, 30, 30, 28, 28, 28 },
 { 68, 60, 48, 40, 40, 32, 28, 28, 28 },
 { 66, 60, 60, 52, 42, 30, 24, 24, 24 },
 { 62, 60, 60, 52, 52, 34, 24, 20, 20 },
 { 60, 60, 56, 50, 40, 36, 24, 20, 20 },
 { 58, 60, 56, 50, 50, 40, 24, 20, 20 },
 { 56, 60, 56, 50, 50, 42, 24, 20, 20 },
 { 52, 52, 52, 50, 50, 42, 24, 18, 18 },
 { 72, 72, 72, 68, 54, 42, 24, 18, 18 },
 { 72, 72, 72, 68, 54, 42, 24, 18, 18 },
};

//Minimum interval between the first peak of the ion current and the start of the chemical phase [us]
__declspec(section ".calib") uint16_T VTCHDELAY[3] = 
{
    170,    150,    150
};

// Delay for StartIon detection
__declspec(section ".calib") uint16_T STARTIONDELAY = 50*256;


// Enable IntIon offset calculation
__declspec(section ".calib") uint8_T ENINTIONOFFSET = 0;

// Max IntIonOffset value
__declspec(section ".calib") uint16_T MAXINTIONOFFSET = 20*16;

// Start sample for IntIonOffset calculation
__declspec(section ".calib") uint8_T STARTOFFSEARCH = 3;

// Number of samples before StartIon for IntIonOffset calculation
__declspec(section ".calib") uint8_T STOPOFFSEARCH = 2;

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_IONMGM_
