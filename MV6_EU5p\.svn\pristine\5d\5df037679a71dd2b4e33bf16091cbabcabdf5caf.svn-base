/*
 * File: CreepLimiterMgm_types.h
 *
 * Code generated for Simulink model 'CreepLimiterMgm'.
 *
 * Model version                  : 1.96
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Fri Oct  4 09:09:50 2019
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Generic->32-bit Embedded Processor
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#ifndef RTW_HEADER_CreepLimiterMgm_types_h_
#define RTW_HEADER_CreepLimiterMgm_types_h_
#endif                                 /* RTW_HEADER_CreepLimiterMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
