/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV7/tree/DD/HEATGRIPDRIVEMGM/Hea#$   */
/* $ Description:                                                                                                */
/* $Revision:: 13841  $                                                                                          */
/* $Date:: 2022-02-21 11:01:27 +0100 (lun, 21 feb 2022)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              GasPosFiltMgm.c
 *
 * Author(s): Lana L.
 *
 *
 * Implementation notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "GasPosFiltMgm.h"

#ifdef _BUILD_GASPOSFILTMGM_

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint16_T GasPos;
uint8_T GasPosFiltCnt;
int8_T HGasPosFiltIdx;
int8_T TGasPosFiltIdx;
uint16_T GasPosFiltBuff[GASPOSFILT_DEEP];

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * GasPosFiltMgm_Init - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void GasPosFiltMgm_Init (void)
{
    uint8_T i;

    GasPos = 0;
    for (i = 0; i < GASPOSFILT_DEEP; i++)
    {
        GasPosFiltBuff[i] = 0;
    }
    GasPosFiltCnt = TIMGASSUPHILL;
    HGasPosFiltIdx = 0;
    TGasPosFiltIdx = 0;
}

/*--------------------------------------------------------------------------*
 * GasPosFiltMgm_T5m - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void GasPosFiltMgm_T10ms (void)
{
    uint8_T i;
    int16_T diff;
    uint8_T worker;
    uint16_T minGas;
    uint16_T maxGas;
    static int32_T meanGasPos = 0;
    static uint8_T decim = 0;
    static uint8_T persist = 0;
    static uint8_T jump = 0;
    static uint8_T longShort = 0;

    if (decim >= GASPOSFILTDECIM)
    {
        decim = 0;
        meanGasPos = 0;
        minGas = GasPos0;
        maxGas = GasPos0;
        
        // Head mgm
        HGasPosFiltIdx++;
        if ((HGasPosFiltIdx >= GASPOSFILTDEEP) || (HGasPosFiltIdx >= GASPOSFILT_DEEP))
        {
            HGasPosFiltIdx = 0;
        }
        else { /* MISRA */ }

        // Insert data
        GasPosFiltBuff[HGasPosFiltIdx] = GasPos0;

        // Tail mgm
        if (HGasPosFiltIdx == TGasPosFiltIdx)
        {
            TGasPosFiltIdx++;
        }
        else { /* MISRA */ }

        if ((TGasPosFiltIdx >= GASPOSFILTDEEP) || (TGasPosFiltIdx >= GASPOSFILT_DEEP))
        {
            TGasPosFiltIdx = 0;
        }
        else { /* MISRA */ }

        for (i = 0; ((i < GASPOSFILTDEEP) && (i < GASPOSFILT_DEEP)); i++)
        {
            minGas = min(minGas, GasPosFiltBuff[i]);
            maxGas = max(maxGas, GasPosFiltBuff[i]);
        }

        if (longShort == 0)
        {
            if (HGasPosFiltIdx >= TGasPosFiltIdx)
            {
                diff = HGasPosFiltIdx - TGasPosFiltIdx;
            }
            else
            {
                diff = GASPOSFILTDEEP + HGasPosFiltIdx - TGasPosFiltIdx;
            }

            // Tail mgm
            if ((diff >= GASPOSFILTDEEPSHORT) || (diff >= GASPOSFILTDEEP))
            {
                TGasPosFiltIdx++;
                diff--;
            }
            else { /* MISRA */ }

            if (HGasPosFiltIdx == TGasPosFiltIdx)
            {
                TGasPosFiltIdx++;
            }
            else { /* MISRA */ }

            if ((TGasPosFiltIdx >= GASPOSFILTDEEP) || (TGasPosFiltIdx >= GASPOSFILT_DEEP))
            {
                TGasPosFiltIdx = 0;
            }
            else { /* MISRA */ }
        }
        else 
        {
            if (HGasPosFiltIdx >= TGasPosFiltIdx)
            {
                diff = HGasPosFiltIdx - TGasPosFiltIdx;
            }
            else
            {
                diff = GASPOSFILTDEEP + HGasPosFiltIdx - TGasPosFiltIdx;
            }
        
            // Tail mgm
            if (diff >= GASPOSFILTDEEP)
            {
                TGasPosFiltIdx++;
                diff--;
            }
            else { /* MISRA */ }

            if (HGasPosFiltIdx == TGasPosFiltIdx)
            {
                TGasPosFiltIdx++;
            }
            else { /* MISRA */ }

            if ((TGasPosFiltIdx >= GASPOSFILTDEEP) || (TGasPosFiltIdx >= GASPOSFILT_DEEP))
            {
                TGasPosFiltIdx = 0;
            }
            else { /* MISRA */ }
        
        }

        worker = HGasPosFiltIdx;
        for (i = 0; ((i < diff) && (i < GASPOSFILTDEEP) && (i < GASPOSFILT_DEEP)); i++)
        {
            meanGasPos = meanGasPos + GasPosFiltBuff[worker];
            if (worker > 0)
            {
                worker--;
            }
            else 
            {
                worker = min((GASPOSFILTDEEP-1), (GASPOSFILT_DEEP-1));
            }
        }
        meanGasPos = meanGasPos / diff;
    }
    else
    {
        decim++;
    }

    // Assign Gas
    diff = (meanGasPos - (int16_T)GasPos0);
    if ((GasPos0 < MINGASATTACH) || (abs(diff) > (2 * THRGASATTACH)))
    {
        if (jump != 0)
        {
            GasPos = (((GasPos0 + meanGasPos) >> 1) >> 1);
            jump = 0;
        }
        else
        {
            GasPos = (GasPos0 >> 1);
        }
        GasPosFiltCnt = TIMGASSUPHILL;
        HGasPosFiltIdx = TGasPosFiltIdx;
        longShort = 0;
    }
    else if (abs(diff) > THRGASATTACH)
    {
        if (GasPosFiltCnt >= TIMGASSUPHILL)
        {
            GasPos = (((GasPos0 + meanGasPos) >> 1) >> 1);
            HGasPosFiltIdx = TGasPosFiltIdx;
            longShort = 0;
        }
        else
        {
            GasPosFiltCnt++;
            GasPos = (meanGasPos >> 1);
        }
        jump = 1;
    }
    else
    {
        GasPosFiltCnt = 0;
        GasPos = (meanGasPos >> 1);
        jump = 1;
        if ((maxGas - minGas) < (2 * THRGASATTACH))
        {
            longShort = 1;
        }
        else { /* MISRA */ }
    }
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/

#else

#endif

/****************************************************************************
 ****************************************************************************/

