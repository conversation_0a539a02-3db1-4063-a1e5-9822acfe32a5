/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/
#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Vehicle speed band for pass by [Km/h]
__declspec(section ".calib") uint16_T DVEHSPEEDPBY = 112U;   //(  7.0000*16)
//Max number of pby activations after key-on [counter]
__declspec(section ".calib") uint8_T MAXPBYCYCLES =   15U;   //  15
//Max gear to activate pby [counter]
__declspec(section ".calib") uint8_T MAXPBYGEAR =  3U;   // 3
//Max Km odometer to enable PBY [Km]
__declspec(section ".calib") uint16_T MAXPBYKMODO = 160U;   //(  10.0000*16)
//Min gear to activate pby [counter]
__declspec(section ".calib") uint8_T MINPBYGEAR =  2U;   // 2
//Duration time of pass by [ms]
__declspec(section ".calib") uint16_T TDURPBY =   8000U;   //  8000
//Number of wuc to disable pass by [counter]
__declspec(section ".calib") uint16_T THCNTWUCPBY =      0U;   //     0
//GasPosCC threshold to enable pass by [%]
__declspec(section ".calib") uint16_T THGASPBY = 960U;   //( 60.0000*16)
//Stability exit time for pass by [ms]
__declspec(section ".calib") uint16_T TSTABEXITPBY =  15000U;   // 15000
//Stability time for pass by [ms]
__declspec(section ".calib") uint16_T TSTABPBY =   3000U;   //  3000
//First vehicle speed set point for pass by [Km/h]
__declspec(section ".calib") uint16_T VEHSPEEDPBY1 = 800U;   //( 50.0000*16)
//Second vehicle speed set point for pass by [Km/h]
__declspec(section ".calib") uint16_T VEHSPEEDPBY2 = 5600U;   //(350.0000*16)
//Third vehicle speed set point for pass by [Km/h]
__declspec(section ".calib") uint16_T VEHSPEEDPBY3 = 5600U;   //(350.0000*16)
//Max torque for pass by [Nm]
__declspec(section ".calib") int16_T VTCMEMAXPBY[7] = 
{
 768, 768, 1344, 1600, 640, 640, 640
};
