/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_EXHVALMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Breakpoints of Coppia Media Effettiva (CME) [N*m]
CALQUAL int16_T BKEXHVALCME[5] = 
{
 160, 320, 640, 1280, 2240
};
//Breakpoints of rpm [rpm]
CALQUAL uint16_T BKEXHVALRPM[6] = 
{
   3500u,   4500u,   5500u,   6500u,   7500u,   9000u
};
//Breakpoints of time for time-history [ms]
CALQUAL uint16_T BKEXHVALTIME[4] = 
{
 0u, 4000u, 4200u, 8000u
};
//Breakpoints [%]
CALQUAL int16_T BKMAXVEXHOUTOPEN[3] = 
{
 -1280, 1280, 2560
};
//error High threshold below which to disable the exh. valve [%]
CALQUAL uint16_T EXHERRTHRHIGH = 512u;   //( 2.00000000*256)
//error Low threshold below which to disable the exh. valve [%]
CALQUAL uint16_T EXHERRTHRLOW = 256u;   //( 1.00000000*256)
//Target voltage level for Zero position of actuator [mV]
CALQUAL uint16_T EXHVALFORCEVPOS = 328u;   //(1601.5625000*0.2048)
//Max voltage level produced by sensor [mV]
CALQUAL uint16_T EXHVMAXFBKACTIVEDIAG = 1024u;   //(5000.0000000*0.2048)
//ExhV Self trigger [flag]
CALQUAL uint8_T FOEXHVSELF =  0u;   // 0
//force exh. gas valve target: 0=Torque Law, 1=no op., 2=Time history, 3= force output [status]
CALQUAL uint8_T FORCEEXHOBJ =  0u;   // 0
//Manual exh. gas valve command voltage [V]
CALQUAL int16_T FORCEEXHVOUT = 0;   //( 0.0000000000*1024)
//Saturator gain idle [gain]
CALQUAL uint16_T GNVEXHOUTIDLE = 42u;   //(1.31250*32)
//K Filter Input VAngExhValv [K]
CALQUAL uint16_T KFILTVANGEXHVAL = 3277u;   //(0.20001220703125*16384)
//PID regulator integral gain factor [V/%]
CALQUAL uint16_T PIDEXHKI = 123u;   //(0.0150146484375*8192)
//PID regulator proportional gain factor [V/%]
CALQUAL uint16_T PIDEXHKP = 4915u;   //(0.5999755859375*8192)
//Max Rate limiter [%]
CALQUAL int16_T RATEANGTGRMAX = 512;   //(  2.00000000*256)
//Min Rate limiter [%]
CALQUAL int16_T RATEANGTGRMIN = -512;   //( -2.00000000*256)
//Max Rate Pby limiter [%]
CALQUAL int16_T RATEANGTGRPBYMAX = 51;   //(  0.19921875*256)
//min. threshold of angular speed to make exh. valve self-learning [rpm]
CALQUAL uint16_T RPMTHRSELFEXH =   1500u;   //  1500
//select zero PI [flag]
CALQUAL uint8_T SELEXHVZEROPI =  1u;   // 1
//Exh. valve angle target [%]
CALQUAL int16_T TBEXHVALANGTGT[5*6] = 
{
 1280, 1280, 1280, 1280, 1280, 1280,
 1280, 1280, 1280, 1280, 1280, 1280,
 11520, 11520, 11520, 11520, 11520, 11520,
 23040, 23040, 23040, 23040, 23040, 23040,
 24320, 24320, 24320, 24320, 24320, 24320
};
//Counter [counter]
CALQUAL uint8_T THANGEXHVPOSNUM =    1u;   //   1
//Counter [counter]
CALQUAL uint8_T THANGEXHVPOSNUMW =    6u;   //   6
//Number of trip for enable Self request in running [counter]
CALQUAL uint8_T THEXHSELFTRIPENABLE = 30u;   //30
//ABS Min Voltage Threshold to observe diagnosis position [V]
CALQUAL uint16_T THOBSVOUTEXHMIN = 5120u;   //(5.0000000000*1024)
//Angle threshold to launch the selflearning for LMS/UMS [%]
CALQUAL int16_T THRANGSELFEXH = 3840;   //( 15.00000000*256)
//Self Stability threshold [mV]
CALQUAL uint16_T THREXHVSLSTAB = 11u;   //(53.7109375*0.2048)
//Threshold minimum of VBatt to enable ExhValve diagnosis and self [V]
CALQUAL uint16_T THVBATTEXHVDIAGEN = 176u;   //(11.0000*16)
//Time to confirm Recovery ExhValve position [ms]
CALQUAL uint16_T TIMDISEXHVMOTION = 30000u;   //30000
//Time to deconfirm Recovery ExhValve position [ms]
CALQUAL uint16_T TIMENEXHVMOTION = 200u;   //200
//Active Diag Time with PID in idle in zero position [ms]
CALQUAL uint16_T TIMEXHADFBINACTIVE = 50u;   //50
//Active Diag Timeout to serch ExhValve zero position [ms]
CALQUAL uint16_T TIMEXHADFBTIMEOUT = 500u;   //500
//Time to close exh. valve [ms]
CALQUAL uint16_T TIMEXHTOCLOSED = 500u;   //500
//Time to open exh. valve [ms]
CALQUAL uint16_T TIMEXHTOOPEN = 500u;   //500
//Time ExhValve to obtain safe open recovery [ms]
CALQUAL uint16_T TIMEXHTOSAFEOPEN = 20u;   //20
//Exh. valve command time to peak valve [counter]
CALQUAL uint8_T TIMEXHVSLPEAK =    3u;   //   3
//Exh. valve command time to stable valve [counter]
CALQUAL uint8_T TIMEXHVSLSTAB =  100u;   // 100
//Counter [ms]
CALQUAL uint16_T TIMEXVSL =    700u;   //   700
//Time ExhValve to observe position in heavy power movimentation [ms]
CALQUAL uint16_T TIMOBSEXHVPOSHIP = 50u;   //50
//Time ExhValve to observe position in low power movimentation [ms]
CALQUAL uint16_T TIMOBSEXHVPOSLOP = 50u;   //50
//Time to Enable possibile Self and funcional diagnosis [ms]
CALQUAL uint16_T TIMPWONSELFDIS = 100u;   //100
//Exh. valve command voltage to Hold valve [V]
CALQUAL int16_T VOUTEXHVLMSHOLD = -2458;   //(-2.4003906250*1024)
//Exh. valve command voltage to Peak valve [V]
CALQUAL int16_T VOUTEXHVPEAK = 8704;   //( 8.5000000000*1024)
//Exh. valve command voltage to Hold valve [V]
CALQUAL int16_T VOUTEXHVUMSHOLD = 2458;   //( 2.4003906250*1024)
//target angle points of time-history [%]
CALQUAL int16_T VTEXHVALWAVE[4] = 
{
 -25600, 0, 0, 25600
};
//Angle target in Idle condition [%]
CALQUAL int16_T VTEXHVANGTGTIDLE[6] = 
{
 1280, 5632, 12800, 20480, 23040, 23040
};
//Saturator PI [V]
CALQUAL uint16_T VTMAXVEXHOUTCLOSED[3] = 
{
 32u, 56u, 96u
};
//Saturator PI [V]
CALQUAL uint16_T VTMAXVEXHOUTOPEN[3] = 
{
 32u, 48u, 80u
};

#endif /* _BUILD_EXHVALMGM_ */

