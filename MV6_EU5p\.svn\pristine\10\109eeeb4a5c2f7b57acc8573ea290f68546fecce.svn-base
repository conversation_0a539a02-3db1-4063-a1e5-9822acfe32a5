/*
 * File: IdleCtfMgm_types.h
 *
 * Code generated for Simulink model 'IdleCtfMgm'.
 *
 * Model version                  : 1.2297
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Mar  1 09:28:46 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_IdleCtfMgm_types_h_
#define RTW_HEADER_IdleCtfMgm_types_h_
#endif                                 /* RTW_HEADER_IdleCtfMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
