
/* ************************* AKHELA *************************** */
/*          WARNING this file is automatically generated        */
/*                       DO NOT EDIT IT!                               */
/* ************************* AKHELA *************************** */

 #include "..\auto\etpu_angle_clock_func_auto.h"

#include "sys.h"

 #define __etpu_globals(name,datatype,address)\
 void write_##name (datatype value) { fs_memset32((void*)(address + ETPU_DATA_RAM -1), (int)value, sizeof(datatype)); } \
 datatype read_##name (void) { return (0x00ffffff)&((datatype) *((datatype*)(address + ETPU_DATA_RAM -1)));}


 __etpu_globals(AC_NextToothTime                 ,uint24    ,0x01E9)
__etpu_globals(AngleClockUnexpectedEvent        ,sint24    ,0x01E1)
__etpu_globals(BlankingPeriod                   ,uint24    ,0x01AD)
__etpu_globals(CrankAngle                       ,uint24    ,0x01D5)
__etpu_globals(CrankStatus                      ,uint24    ,0x01DD)
__etpu_globals(EdgeTime                         ,uint24    ,0x01B9)
__etpu_globals(FalseTeeth                       ,uint24    ,0x01C1)
__etpu_globals(LastFalseTooth                   ,uint24    ,0x01C5)
__etpu_globals(LastFalseToothLowTime            ,uint24    ,0x01D1)
__etpu_globals(LastFalseToothPeriod             ,uint24    ,0x01CD)
__etpu_globals(LastTooth_1                      ,uint24    ,0x0159)
__etpu_globals(LastTooth_2                      ,uint24    ,0x015D)
__etpu_globals(Last_State                       ,uint24    ,0x01D9)
__etpu_globals(NumRevPerCycle                   ,uint24    ,0x0155)
__etpu_globals(NumberMissing                    ,uint24    ,0x0189)
__etpu_globals(PreviousEdgeTime                 ,uint24    ,0x0151)
__etpu_globals(PreviousToothPeriod              ,uint24    ,0x014D)
__etpu_globals(RatioFirstToothHigh              ,ufract24  ,0x01A9)
__etpu_globals(RatioFirstToothLow               ,ufract24  ,0x01A5)
__etpu_globals(RatioHoleHigh                    ,ufract24  ,0x01A1)
__etpu_globals(RatioHoleLow                     ,ufract24  ,0x019D)
__etpu_globals(RatioSyncHigh                    ,ufract24  ,0x0191)
__etpu_globals(RatioSyncLow                     ,ufract24  ,0x018D)
__etpu_globals(RatioToothHigh                   ,ufract24  ,0x0199)
__etpu_globals(RatioToothLow                    ,ufract24  ,0x0195)
__etpu_globals(StallPeriod                      ,uint24    ,0x01B1)
__etpu_globals(StartUpBlankingPeriod            ,uint24    ,0x0181)
__etpu_globals(StartUpSkippedTeeth              ,uint24    ,0x0185)
__etpu_globals(TeethPerRev                      ,uint24    ,0x0145)
__etpu_globals(TicksPerTooth                    ,uint24    ,0x0149)
__etpu_globals(ToothCount                       ,uint24    ,0x01BD)
__etpu_globals(ToothPeriod                      ,uint24    ,0x01B5)
__etpu_globals(angleClockGain                   ,uint24    ,0x0165)
__etpu_globals(angleClockGain_15d               ,uint24    ,0x0171)
__etpu_globals(angleClockGain_30d               ,uint24    ,0x0175)
__etpu_globals(angleClockGain_Tdc               ,uint24    ,0x016D)
__etpu_globals(angleClockGain_m15d              ,uint24    ,0x0169)
__etpu_globals(angleExGeneratorUnexpectedEvent  ,sint24    ,0x0265)
__etpu_globals(angleValue                       ,uint24    ,0x00A1)
__etpu_globals(captureRegA                      ,uint24    ,0x0269)
__etpu_globals(captureRegB                      ,uint24    ,0x0161)
__etpu_globals(capturedTime                     ,uint24    ,0x0275)
__etpu_globals(espToothPeriod                   ,uint24    ,0x01ED)
__etpu_globals(event_divider                    ,uint24    ,0x0179)
__etpu_globals(exAngleNum                       ,uint24    ,0x0261)
__etpu_globals(exToothNum                       ,uint24    ,0x01C9)
__etpu_globals(indPosCh30                       ,uint24    ,0x026D)
__etpu_globals(indPosCh31                       ,uint24    ,0x0271)
__etpu_globals(sampleNumber                     ,uint24    ,0x0001)
__etpu_globals(skippedTeeth                     ,uint24    ,0x01E5)
__etpu_globals(sparkHandlerUnexpectedEvent      ,sint24    ,0x017D)
__etpu_globals(zeroToothTime                    ,uint24    ,0x0141)



 void write_sampleNumberVal(uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM+( 0x0001-1))+position), (int)value, sizeof(uint24));
 }


 uint24 read_sampleNumberVal(uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM+( 0x0001-1))+position));
 }


 void write_angleValueVal(uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM+( 0x00A1-1))+position), (int)value, sizeof(uint24));
 }


 uint24 read_angleValueVal(uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM+( 0x00A1-1))+position));
 }


 void write_zeroToothTimeVal(uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM+( 0x0141-1))+position), (int)value, sizeof(uint24));
 }


 uint24 read_zeroToothTimeVal(uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM+( 0x0141-1))+position));
 }


 #define __etpu_param(name,datatype,address) \
 void write_##name (datatype value,int channel) { fs_memset32((void*)((address + ETPU_DATA_RAM+(ETPU.CHAN[channel].CR.B.CPBA << 3)) -1), (int)value, sizeof(datatype)); } \
 datatype read_##name (int channel) { return (0x00ffffff)&((datatype) *((datatype*)(address + ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA << 3) -1)));}


 
__etpu_param(toothCounter                    ,uint24    ,0x0001)
__etpu_param(AngleCorrection                 ,uint24    ,0x0005)
__etpu_param(polarity                        ,uint24    ,0x0009)
__etpu_param(maxToothPeriod                  ,uint24    ,0x000D)
__etpu_param(angleClockChanFlags             ,uint24    ,0x0011)



 
__etpu_param(captureTransition               ,uint24    ,0x0001)
__etpu_param(sparkCaptureAngle               ,uint24    ,0x0005)
__etpu_param(sparkCaptureTime                ,uint24    ,0x0009)
__etpu_param(sparkHandlerChanFlags           ,uint24    ,0x000D)



 
__etpu_param(angleExGeneratorChanFlags       ,uint24    ,0x0001)



 
__etpu_param(dutyCycle                       ,uint24    ,0x0001)
__etpu_param(period                          ,uint24    ,0x0005)
__etpu_param(mode                            ,uint24    ,0x0009)
__etpu_param(numOfEdges                      ,uint24    ,0x000D)
__etpu_param(continuousMode                  ,uint24    ,0x0011)
__etpu_param(startingAngle                   ,uint24    ,0x0015)
__etpu_param(pwmChanFlags                    ,uint24    ,0x0019)



 
__etpu_param(ionTrPeriod                     ,uint24    ,0x0001)
__etpu_param(ionTrNumOfEdges                 ,uint24    ,0x0005)
__etpu_param(ionTrChanFlags                  ,uint24    ,0x0009)
__etpu_param(indPosCh                        ,uint24    ,0x000D)



 
__etpu_param(adcTrPeriod                     ,uint24    ,0x0001)



 
__etpu_param(pwm_in_HighPeriodBuff           ,uint24    ,0x0001)
__etpu_param(pwm_in_LowPeriodBuff            ,uint24    ,0x0025)
__etpu_param(pwm_in_wrBuffIndex              ,uint24    ,0x0049)
__etpu_param(pwm_in_hPeriod                  ,uint24    ,0x004D)
__etpu_param(pwm_in_lPeriod                  ,uint24    ,0x0051)
__etpu_param(pwm_in_Timeout                  ,uint24    ,0x0055)
__etpu_param(pwm_in_ChanFlags                ,uint24    ,0x0059)
__etpu_param(pwm_in_lastTransictionTime      ,uint24    ,0x005D)


 
__etpu_param(handle                          ,uint24    ,0x0001)
__etpu_param(pulseStartAdj                   ,uint24    ,0x0065)
__etpu_param(pulseStopAdj                    ,uint24    ,0x0071)
__etpu_param(dummy                           ,uint24    ,0x007D)



 
__etpu_param(trigStartAngle                  ,uint24    ,0x0001)
__etpu_param(index                           ,uint24    ,0x00C1)
__etpu_param(trigMode                        ,uint24    ,0x00C5)
__etpu_param(trigPeriod                      ,uint24    ,0x00C9)
__etpu_param(trigNum                         ,uint24    ,0x00CD)
__etpu_param(indexRestart                    ,uint24    ,0x00D1)



 
__etpu_param(etpuDelay                       ,uint24    ,0x0001)



 
__etpu_param(PhEntriesNumber                 ,uint24    ,0x0001)
__etpu_param(Ph_maxToothPeriod               ,uint24    ,0x0005)
__etpu_param(phaseChanFlags                  ,uint24    ,0x0009)



 
__etpu_param(minOnTime                       ,uint24    ,0x0001)
__etpu_param(minOffTime                      ,uint24    ,0x0005)
__etpu_param(FL_OUT_chanFlags                ,uint24    ,0x0009)
__etpu_param(lastTransitionTime              ,uint24    ,0x000D)
__etpu_param(lastOnTime                      ,uint24    ,0x0011)
__etpu_param(lastOffTime                     ,uint24    ,0x0015)
__etpu_param(switchingTimes                  ,uint24    ,0x0019)
__etpu_param(directionChannel                ,uint24    ,0x001D)
__etpu_param(switchingMode                   ,uint24    ,0x0021)



 
__etpu_param(linkedChan                      ,uint24    ,0x0001)
__etpu_param(FL_IN_chanFlags                 ,uint24    ,0x0005)



 
__etpu_param(FL_DIR_chanFlags                ,uint24    ,0x0001)



 
__etpu_param(FS_ETPU_UART_ACTUAL_BIT_COUNT   ,sint24    ,0x0001)
__etpu_param(FS_ETPU_UART_MATCH_RATE         ,sint24    ,0x0005)
__etpu_param(FS_ETPU_UART_PARITY_TEMP        ,sint24    ,0x0009)
__etpu_param(FS_ETPU_UART_SHIFT_REG          ,sint24    ,0x000D)
__etpu_param(FS_ETPU_UART_RX_ERROR           ,sint24    ,0x0011)
__etpu_param(FS_ETPU_UART_TX_RX_DATA         ,sint24    ,0x0015)
__etpu_param(FS_ETPU_UART_BITS_PER_DATA_WORD ,sint24    ,0x0019)



 #undef __etpu_param


 void write_handleVal(uint24 channel,uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0001-1))+position), (int)value, sizeof(uint24));
 }


 void write_pulseStartAdjVal(uint24 channel,uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0065-1))+position), (int)value, sizeof(uint24));
 }


 void write_pulseStopAdjVal(uint24 channel,uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0071-1))+position), (int)value, sizeof(uint24));
 }


 void write_trigStartAngleVal(uint24 channel,uint24 position, uint24 value) {
     fs_memset32((void*)((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0001-1))+position), (int)value, sizeof(uint24));
 }


 uint24 read_handleVal(uint24 channel,uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0001-1))+position));
 }


 uint24 read_pulseStartAdjVal(uint24 channel,uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0065-1))+position));
 }


 uint24 read_StopAdjVal(uint24 channel,uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0071-1))+position));
 }


 uint24 read_trigStartAngleVal(uint24 channel,uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0001-1))+position));
 }


 uint24 read_pwm_in_HighPeriodBuffVal(uint24 channel,uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0001-1))+position));
 }


 uint24 read_pwm_in_LowPeriodBuffVal(uint24 channel,uint24 position) {
     return 0x00ffffff & (*((uint24*)(ETPU_DATA_RAM + (ETPU.CHAN[channel].CR.B.CPBA<< 3) + ( 0x0025-1))+position));
 }

