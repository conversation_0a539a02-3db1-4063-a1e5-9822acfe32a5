/*  File    : steppercmd.c
 *  Author  : <PERSON><PERSON><PERSON>
 *  Date    : 05/04/2005 12.57
 *  Revision: StepperCmd 1.0
 *	Note	: Prima versione
 * 
 *  Copyright 2005 Eldor Corporation
 */
 
#ifndef _STEPPERCMD_H_
#define _STEPPERCMD_H_
/** include files **/
#include "typedefs.h"

/** local definitions **/
#define N_STP_CMD	4
#define N_STEP		4	// = 4 Full Steps = 8 Half Steps

// Forza lo spegnimento dello stepper al raggiungimento 
// della posizione obiettivo per ridurre dissipazione.
#define STP_STOP_OFF   

// Const StStepper
typedef uint8_t typStStepper;
#define STP_RESET   0
#define STP_STOP    1
#define STP_DOWN    2
#define STP_UP      3
#define STP_PAUSE   4


/** default settings **/

/** external functions **/

/** external data **/
extern uint16_T StepperObj;
extern uint16_T VBattery;

/** internal functions **/

/** public data **/
extern typStStepper StStepper;
extern uint16_T StepperPos;
extern uint16_T StpPosObj;
extern uint16_T StpPos;
extern uint8_T  StpAbsPos;
extern uint8_T  StepperCmd[N_STP_CMD];
extern uint8_T  FlgSteperRdy;

/** private data **/

/** public functions **/
extern void StepperCmd_Init(void);
extern void StepperCmd_NoSync(void);
extern void StepperCmd_T5ms(void);

/** private functions **/

#endif
