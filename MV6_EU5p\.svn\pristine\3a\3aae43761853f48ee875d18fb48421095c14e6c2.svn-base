/*
 * File: VSpeedCtrl.h
 *
 * Real-Time Workshop code generated for Simulink model VSpeedCtrl.
 *
 * Model version                        : 1.371
 * Real-Time Workshop file version      : 6.6  (R2007a)  01-Feb-2007
 * Real-Time Workshop file generated on : Mon Oct 01 11:42:13 2007
 * TLC version                          : 6.6 (Jan 16 2007)
 * C source code generated on           : Mon Oct 01 11:42:13 2007
 */

#ifndef _RTW_HEADER_VSpeedCtrl_h_
#define _RTW_HEADER_VSpeedCtrl_h_
#ifndef _VSpeedCtrl_COMMON_INCLUDES_
# define _VSpeedCtrl_COMMON_INCLUDES_
#include <stddef.h>
#include "rtwtypes.h"
#include "rtlibsrc.h"
#endif                                 /* _VSpeedCtrl_COMMON_INCLUDES_ */

#include "VSpeedCtrl_types.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

#define CMI_MAX                        4190208

/* Block signals (auto storage) */
typedef struct {
  int16_T VSpeedLimErr_g;              /* '<S5>/MinMax1' */
} BlockIO_VSpeedCtrl;

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  int32_T Selector_DWORK1;             /* '<S4>/Selector' */
  uint8_T UnitDelay3_DSTATE;           /* '<S4>/Unit Delay3' */
} D_Work_VSpeedCtrl;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_Reset_ZCE[2];          /* '<S1>/fc_Reset' */
  ZCSigState fc_10ms_ZCE;              /* '<S1>/fc_10ms' */
} PrevZCSigStates_VSpeedCtrl;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
} ExternalInputs_VSpeedCtrl;

/* Block signals (auto storage) */
extern BlockIO_VSpeedCtrl VSpeedCtrl_B;

/* Block states (auto storage) */
extern D_Work_VSpeedCtrl VSpeedCtrl_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_VSpeedCtrl VSpeedCtrl_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  RTW declares the memory for these signals
 * and exports their symbols.
 *
 */
extern int32_T vspeedlimpropfact;      /* '<S6>/Product' */
extern int32_T vspeedlimintfact;       /* '<S6>/Product2' */
extern int32_T deltacmivspeedlimiter;  /* '<S6>/Sum1' */
extern int32_T CmiVSpeedLimNoSat;      /* '<S6>/Sum5'
                                        * Torque to limit the vehicle speed (not saturated)
                                        */
extern int16_T deltavspeedlimerr;      /* '<S6>/Sum4' */
extern uint8_T EnVSpeedLim;            /* '<S4>/Logical Operator'
                                        * The speed limiter is active (=1)
                                        */

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  RTW declares the memory for these states
 * and exports their symbols.
 *
 */
extern int32_T CmiVSpeedLimiter;       /* '<Root>/_DataStoreBlk_1'
                                        * Torque to limit the vehicle speed
                                        */
extern int16_T CmiSpeedP;              /* '<Root>/_DataStoreBlk_2'
                                        * Predicted CMI target after vehicle speed treatments
                                        */
extern int16_T CmiSpeedI;              /* '<Root>/_DataStoreBlk_3'
                                        * Instantaneous CMI target after vehicle speed treatments
                                        */
extern int16_T VSpeedLimErr;           /* '<Root>/_DataStoreBlk_4'
                                        * Vehicle speed error for limiter
                                        */
extern uint8_T CtfVSpeedLimFlg;        /* '<Root>/_DataStoreBlk_5'
                                        * Enable cutoff to limit the vehicle speed (=1)
                                        */

/* Model entry point functions */
extern void VSpeedCtrl_initialize(boolean_T firstTime);
extern void VSpeedCtrl_step(void);

/*
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('vspeedctrl_gen/VSpeedCtrl')    - opens subsystem vspeedctrl_gen/VSpeedCtrl
 * hilite_system('vspeedctrl_gen/VSpeedCtrl/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : vspeedctrl_gen
 * '<S1>'   : vspeedctrl_gen/VSpeedCtrl
 * '<S2>'   : vspeedctrl_gen/VSpeedCtrl/fc_10ms
 * '<S3>'   : vspeedctrl_gen/VSpeedCtrl/fc_Reset
 * '<S4>'   : vspeedctrl_gen/VSpeedCtrl/fc_10ms/f_10ms
 * '<S5>'   : vspeedctrl_gen/VSpeedCtrl/fc_10ms/f_10ms/Error_Calculation
 * '<S6>'   : vspeedctrl_gen/VSpeedCtrl/fc_10ms/f_10ms/PI_regulator
 * '<S7>'   : vspeedctrl_gen/VSpeedCtrl/fc_10ms/f_10ms/Saturation Dynamic
 * '<S8>'   : vspeedctrl_gen/VSpeedCtrl/fc_10ms/f_10ms/Torque_Calculation
 * '<S9>'   : vspeedctrl_gen/VSpeedCtrl/fc_Reset/f_Reset
 */
#endif                                 /* _RTW_HEADER_VSpeedCtrl_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
