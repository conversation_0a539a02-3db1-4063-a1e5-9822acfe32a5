/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_VSRAM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "vsrammgm.h"
#include "diagcanmgm.h"
#include "..\include\vsram_shared_content.h"
#ifdef _BUILD_KLINE_
#include "kline_init.h"
#endif


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint32_t comm_protocol;
    
#ifdef _BUILD_KLINE_
kwpTimingStruct kwp_timing;
#endif
    
#ifdef _BUILD_DIAGCANMGM_
uint32_t KWPsession;
union DownloadStruct_tag KWPdownloadStruct;
taskFlashEraseParams_t flashEraseParams;
taskCheckSumParams_t CheckSumParams;
uint32_t kwp_baud; 
#else    
uint32_t kwp_baud;
#endif /* _BUILD_DIAGCANMGM_ */


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * VSRAMMGM_LoadFromSharedMemory - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void VSRAMMGM_LoadFromSharedMemory(void)
{
    comm_protocol = *((uint32_t*)(vsram_shared_memory+COMM_PROTOCOL_POS));
    
#ifdef _BUILD_KLINE_
    kwp_timing.tempP1min = *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP1MIN_POS));
    kwp_timing.tempP2min = *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP2MIN_POS));
    kwp_timing.tempP2max = *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP2MAX_POS));
    kwp_timing.tempP3min = *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP3MIN_POS));
    kwp_timing.tempP3max = *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP3MAX_POS));
    kwp_timing.tempP4min = *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP4MIN_POS));
#endif
    
#ifdef _BUILD_DIAGCANMGM_
    KWPsession = *((uint32_t*)(vsram_shared_memory+KWPSESSION_POS));
    KWPdownloadStruct.CF = *((uint32_t*)(vsram_shared_memory+KWPDOWNLOADSTRUCT_POS));

    flashEraseParams.startingAddress = *((uint32_t*)(vsram_shared_memory+FLASHERASEPARAMS_STARTINGADDRESS_POS));
    flashEraseParams.size = *((uint32_t*)(vsram_shared_memory+FLASHERASEPARAMS_SIZE_POS));
    flashEraseParams.callback = (void(*)(void))(*((uint32_t*)(vsram_shared_memory+FLASHERASEPARAMS_CALLBACK_POS)));

    CheckSumParams.checkSumStartAddress = *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_CHECKSUMSTARTADDRESS_POS));
    CheckSumParams.checkSumEndAddress = *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_CHECKSUMENDADDRESS_POS));
    CheckSumParams.checksum = *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_CHECKSUM_POS));
    CheckSumParams.init_crc = *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_INIT_CRC_POS));
    CheckSumParams.data_ptr = (uint8_t*)*((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_DATA_PTR_POS));
    CheckSumParams.data_size = *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_DATA_SIZE_POS));
    

#ifdef _BUILD_KLINE_
    kwp_baud = *((uint32_t*)(vsram_shared_memory+KWP_BAUD_POS));
#endif
#endif /* _BUILD_DIAGCANMGM_ */

    //VSRAMMGM_LoadIvor2Data();<<-- is done automatically at powerOn, if needed.


}


/*--------------------------------------------------------------------------*
 * VSRAMMGM_StoreToSharedMemory - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void VSRAMMGM_StoreToSharedMemory(void)
{
    *((uint32_t*)(vsram_shared_memory+COMM_PROTOCOL_POS)) = comm_protocol;
    
#ifdef _BUILD_KLINE_
    *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP1MIN_POS)) = kwp_timing.tempP1min;
    *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP2MIN_POS)) = kwp_timing.tempP2min;
    *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP2MAX_POS)) = kwp_timing.tempP2max;
    *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP3MIN_POS)) = kwp_timing.tempP3min;
    *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP3MAX_POS)) = kwp_timing.tempP3max;
    *((uint32_t*)(vsram_shared_memory+KWP_TIMING_TEMPP4MIN_POS)) = kwp_timing.tempP4min;
#endif
    
#ifdef _BUILD_DIAGCANMGM_
    *((uint32_t*)(vsram_shared_memory+KWPSESSION_POS)) = KWPsession;
    *((uint32_t*)(vsram_shared_memory+KWPDOWNLOADSTRUCT_POS)) = KWPdownloadStruct.CF;

    *((uint32_t*)(vsram_shared_memory+FLASHERASEPARAMS_STARTINGADDRESS_POS)) = flashEraseParams.startingAddress;
    *((uint32_t*)(vsram_shared_memory+FLASHERASEPARAMS_SIZE_POS)) = flashEraseParams.size;
    *((uint32_t*)(vsram_shared_memory+FLASHERASEPARAMS_CALLBACK_POS)) = (uint32_t)(flashEraseParams.callback);

    *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_CHECKSUMSTARTADDRESS_POS)) = CheckSumParams.checkSumStartAddress;
    *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_CHECKSUMENDADDRESS_POS)) = CheckSumParams.checkSumEndAddress;
    *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_CHECKSUM_POS)) = CheckSumParams.checksum;
    *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_INIT_CRC_POS)) = CheckSumParams.init_crc;
    *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_DATA_PTR_POS)) = (uint32_t)(CheckSumParams.data_ptr);
    *((uint32_t*)(vsram_shared_memory+CHECKSUMPARAMS_DATA_SIZE_POS)) = CheckSumParams.data_size;


#ifdef _BUILD_KLINE_
    *((uint32_t*)(vsram_shared_memory+KWP_BAUD_POS)) = kwp_baud;
#endif
#endif /* _BUILD_DIAGCANMGM_ */

    //VSRAMMGM_StoreIvor2Data();
}

/*--------------------------------------------------------------------------*
 * VSRAMMGM_StoreIvor2Data - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void VSRAMMGM_StoreIvor2Data(void)
{
    
    uint32_t* ivor2FaultyAddressPtr = (uint32_t*)(vsram_shared_memory+IVOR2_DATA_POS);
    uint8_t*  ivor2VsramDataPtr;

    FlashFaultChecksumUpdate();
    
    *(ivor2FaultyAddressPtr) = FlashFaultStruct.lastFaultyFlashAddress;
    ivor2VsramDataPtr = (uint8_t*)(ivor2FaultyAddressPtr + sizeof(uint32_T));
    
    *(ivor2VsramDataPtr) = FlashFaultStruct.lastFaultyFlashLogicalIdx;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterMboot;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterEE_page0;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterEE_page1;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterBoot0;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterBoot1;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterCalib;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterAppl0;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterAppl1;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterAppl2;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.faultCounterBackup;
    ivor2VsramDataPtr++;
    *(ivor2VsramDataPtr) = FlashFaultStruct.StructChecksum;

}

/*--------------------------------------------------------------------------*
 * VSRAMMGM_LoadIvor2Data - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void VSRAMMGM_LoadIvor2Data(void)
{
    uint32_t* ivor2FaultyAddressPtr = (uint32_t*)(vsram_shared_memory+IVOR2_DATA_POS);
    uint8_t* ivor2VsramDataPtr;

    
    FlashFaultStruct.lastFaultyFlashAddress     = *(ivor2FaultyAddressPtr);
    ivor2VsramDataPtr = (uint8_t*)(ivor2FaultyAddressPtr + sizeof(uint32_T));
    
    FlashFaultStruct.lastFaultyFlashLogicalIdx  = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterMboot          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterEE_page0       = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterEE_page1       = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterBoot0          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterBoot1          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterCalib          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterAppl0          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterAppl1          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterAppl2          = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.faultCounterBackup         = *(ivor2VsramDataPtr);
    ivor2VsramDataPtr++;
    FlashFaultStruct.StructChecksum             = *(ivor2VsramDataPtr);

    if(NO_ERROR != FlashFaultChecksumIsValid())
    {
        FlashFaultResetInfo();
    }
    else
    {
        /*VsRam Data are Ok*/
    }

}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _BUILD_VSRAM_ */