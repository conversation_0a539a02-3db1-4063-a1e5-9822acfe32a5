/*
 * File: lambdamgm.h
 *
 * Code generated for Simulink model 'lambdamgm'.
 *
 * Model version                  : 1.1171
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jul  5 09:54:45 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#ifndef RTW_HEADER_lambdamgm_h_
#define RTW_HEADER_lambdamgm_h_
#include <string.h>
#ifndef lambdamgm_COMMON_INCLUDES_
# define lambdamgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "diagmgm_out.h"
#include "mathlib.h"
#endif                                 /* lambdamgm_COMMON_INCLUDES_ */

#include "lambdamgm_types.h"

/* Includes for objects with custom storage classes. */
#include "lambda_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKLAMMGMLOAD_dim               3U                        /* Referenced by:
                                                                  * '<S21>/BKLAMMGMLOAD_dim'
                                                                  * '<S23>/BKLAMMGMLOAD_dim'
                                                                  * '<S28>/BKLAMMGMLOAD_dim'
                                                                  * '<S30>/BKLAMMGMLOAD_dim'
                                                                  */

/* Length of BKLAMMGMLOAD */
#define BKLAMMGMRPM_dim                5U                        /* Referenced by:
                                                                  * '<S21>/BKLAMMGMRPM_dim'
                                                                  * '<S23>/BKLAMMGMRPM_dim'
                                                                  * '<S28>/BKLAMMGMRPM_dim'
                                                                  * '<S30>/BKLAMMGMRPM_dim'
                                                                  */

/* Length of BKLAMMGMRPM */
#define BKTHVLSTWAT_dim                4U                        /* Referenced by:
                                                                  * '<S8>/BKTHVLSTWAT_dim'
                                                                  * '<S22>/BKTHVLSTWAT_dim'
                                                                  * '<S29>/BKTHVLSTWAT_dim'
                                                                  */

/* Length of BKTHVLSTWAT */
#define BKTHVLTDCCRK_dim               3U                        /* Referenced by:
                                                                  * '<S22>/BKTHVLTDCCRK_dim'
                                                                  * '<S29>/BKTHVLTDCCRK_dim'
                                                                  */

/* Length of BKTHVLTDCCRK */
#define ID_LAMBDA_MGM                  18736795U                 /* Referenced by: '<S5>/ID_LAMBDA_MGM' */

/* mask */
#define INIT_CAT                       0U                        /* Referenced by: '<S9>/Chart' */

/* INIT_CAT */
#define MAX_LAM_PERIOD                 50000U                    /* Referenced by: '<S4>/VLambdaState_Calc' */

/* Max lambda period */
#define MAX_LAM_V                      65520U                    /* Referenced by: '<S4>/VLambdaState_Calc' */

/* MAX_LAM_V */
#define ONE_RES_LAM                    1024U                     /* Referenced by: '<S9>/Chart' */

/* ONE_RES_LAM */
#define READY_CAT                      4U                        /* Referenced by: '<S9>/Chart' */

/* READY_LAM2 */
#define STOICHIOM_RATIO                1024U                     /* Referenced by: '<S4>/VLambdaState_Calc' */

/* Stoichiometric ratio A/F */
#define VLAMPOOR_INIT                  0U                        /* Referenced by: '<S4>/VLambdaState_Calc' */

/* INIT */
#define VLAMPOOR_NORMAL                1U                        /* Referenced by: '<S4>/VLambdaState_Calc' */

/* NORMAL in POOR */
#define VLAMRICH_CUTOFF                5U                        /* Referenced by: '<S4>/VLambdaState_Calc' */

/* CUTOFF in RICH */
#define VLAMRICH_NORMAL                4U                        /* Referenced by: '<S4>/VLambdaState_Calc' */

/* NORMAL in RICH */
#define VLAMRICH_WAIT                  6U                        /* Referenced by: '<S4>/VLambdaState_Calc' */

/* WAIT in RICH */
#define WAIT_COND_CAT                  3U                        /* Referenced by: '<S9>/Chart' */

/* WAIT_COND_CAT */
#define WAIT_READY_CAT                 1U                        /* Referenced by: '<S9>/Chart' */

/* WAIT_READY_CAT */
#define WAIT_TDC_CAT                   2U                        /* Referenced by: '<S9>/Chart' */

/* WAIT_TDC_CAT */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  int32_T Memory_PreviousInput_lku;    /* '<S37>/Memory' */
  int32_T Memory_PreviousInput_gja;    /* '<S18>/Memory' */
  uint32_T Memory_PreviousInput;       /* '<S52>/Memory' */
  uint32_T Memory1_PreviousInput;      /* '<S52>/Memory1' */
  uint32_T t_entry_poor;               /* '<S4>/VLambdaState_Calc' */
  uint32_T Memory_PreviousInput_izo;   /* '<S12>/Memory' */
  uint32_T Memory1_PreviousInput_nnq;  /* '<S12>/Memory1' */
  uint32_T tmpCntAbsTdc;               /* '<S9>/Chart' */
  uint32_T INIT_PreviousInput;         /* '<S12>/INIT' */
  uint16_T DataTypeConversion1;        /* '<S8>/Data Type Conversion1' */
  uint16_T out;                        /* '<S51>/Chart' */
  uint16_T out_ns4;                    /* '<S50>/Chart' */
  uint16_T Memory1_PreviousInput_nvw;  /* '<S68>/Memory1' */
  uint16_T Memory_PreviousInput_bxf;   /* '<S68>/Memory' */
  uint16_T Memory1_PreviousInput_mg1;  /* '<S64>/Memory1' */
  uint16_T Memory_PreviousInput_gnc;   /* '<S64>/Memory' */
  uint16_T oldTdcCrk;                  /* '<S4>/VLambdaState_Calc' */
  uint16_T oldTdcCrk2;                 /* '<S4>/VLambdaState_Calc' */
  uint16_T offCntTdcCrk;               /* '<S51>/Chart' */
  uint16_T offCntTdcCrk_iga;           /* '<S50>/Chart' */
  uint16_T cnt;                        /* '<S17>/Calc_Transitions' */
  uint16_T cnt2;                       /* '<S17>/Calc_Transitions' */
  uint16_T wdt;                        /* '<S17>/Calc_Transitions' */
  uint8_T ptfault_lambda;              /* '<S4>/VLambdaState_Calc' */
  uint8_T flgresfiltfreq;              /* '<S4>/VLambdaState_Calc' */
  uint8_T FlgO2DiagOn_o2g;             /* '<S4>/VLambdaState_Calc' */
  uint8_T pt_fault_l2;                 /* '<S4>/VLambdaState_Calc' */
  uint8_T ptfault_cat;                 /* '<S9>/Chart' */
  uint8_T LamOBD2End_abf;              /* '<S17>/Calc_Transitions' */
  uint8_T FlgO2DiagOn_dta;             /* '<S17>/Calc_Transitions' */
  uint8_T ptFault_rich;                /* '<S17>/Calc_Transitions' */
  uint8_T ptFault_lean;                /* '<S17>/Calc_Transitions' */
  uint8_T Memory_PreviousInput_d3k;    /* '<S4>/Memory' */
  uint8_T Memory1_PreviousInput_jee;   /* '<S4>/Memory1' */
  uint8_T Memory_PreviousInput_mhv;    /* '<S56>/Memory' */
  uint8_T Memory_PreviousInput_cqm;    /* '<S54>/Memory' */
  uint8_T Memory_PreviousInput_c24;    /* '<S17>/Memory' */
  uint8_T is_active_c1_lambdamgm;      /* '<S4>/VLambdaState_Calc' */
  uint8_T is_VLambdaState;             /* '<S4>/VLambdaState_Calc' */
  uint8_T is_Lam_Trans;                /* '<S4>/VLambdaState_Calc' */
  uint8_T is_LAM_CUTOFF_DIAG;          /* '<S4>/VLambdaState_Calc' */
  uint8_T is_DIAG_FREQ;                /* '<S4>/VLambdaState_Calc' */
  uint8_T is_VLambdaState2;            /* '<S4>/VLambdaState_Calc' */
  uint8_T is_Lam_Trans_hpo;            /* '<S4>/VLambdaState_Calc' */
  uint8_T tmpDiag;                     /* '<S4>/VLambdaState_Calc' */
  uint8_T trigLDDec;                   /* '<S4>/VLambdaState_Calc' */
  uint8_T trigLDInc;                   /* '<S4>/VLambdaState_Calc' */
  uint8_T flgCFTrigDiag;               /* '<S4>/VLambdaState_Calc' */
  uint8_T setLam2Liveness;             /* '<S4>/VLambdaState_Calc' */
  uint8_T setLamLiveness;              /* '<S4>/VLambdaState_Calc' */
  uint8_T trigLD2Inc;                  /* '<S4>/VLambdaState_Calc' */
  uint8_T is_active_c4_lambdamgm;      /* '<S9>/Chart' */
  uint8_T is_c4_lambdamgm;             /* '<S9>/Chart' */
  uint8_T is_DIAG_CAT;                 /* '<S9>/Chart' */
  uint8_T oldCntLam2Liveness;          /* '<S9>/Chart' */
  uint8_T oldCntLamLiveness;           /* '<S9>/Chart' */
  uint8_T is_active_c5_lambdamgm;      /* '<S51>/Chart' */
  uint8_T is_c5_lambdamgm;             /* '<S51>/Chart' */
  uint8_T is_active_c2_lambdamgm;      /* '<S50>/Chart' */
  uint8_T is_c2_lambdamgm;             /* '<S50>/Chart' */
  uint8_T is_active_c3_lambdamgm;      /* '<S17>/Calc_Transitions' */
  uint8_T is_LAM_TIM_TRANSITIONS;      /* '<S17>/Calc_Transitions' */
  uint8_T is_LAM_DISTURBANCE;          /* '<S17>/Calc_Transitions' */
  uint8_T is_LAM_OBD2_DIAG;            /* '<S17>/Calc_Transitions' */
  uint8_T is_LAM_DIAG_OBD2;            /* '<S17>/Calc_Transitions' */
  uint8_T flgDone;                     /* '<S17>/Calc_Transitions' */
  uint8_T end;                         /* '<S17>/Calc_Transitions' */
  uint8_T wdtlr;                       /* '<S17>/Calc_Transitions' */
  uint8_T endL2R;                      /* '<S17>/Calc_Transitions' */
  uint8_T endR2L;                      /* '<S17>/Calc_Transitions' */
  boolean_T Memory_PreviousInput_nqz;  /* '<S71>/Memory' */
  boolean_T Memory_PreviousInput_dy5;  /* '<S76>/Memory' */
  boolean_T Memory1_PreviousInput_pf2; /* '<S55>/Memory1' */
  boolean_T Memory1_PreviousInput_pwi; /* '<S53>/Memory1' */
  boolean_T Memory_PreviousInput_idy;  /* '<S57>/Memory' */
  boolean_T Memory_PreviousInput_oab;  /* '<S53>/Memory' */
  boolean_T Memory_PreviousInput_i3l;  /* '<S55>/Memory' */
} D_Work_lambdamgm_T;

/* Block signals and states (default storage) */
extern D_Work_lambdamgm_T lambdamgm_DWork;

/* Model entry point functions */
extern void lambdamgm_initialize(void);

/* Exported entry point function */
extern void Trig_lambdamgm_NoSync(void);

/* Exported entry point function */
extern void Trig_lambdamgm_Init(void);

/* Exported entry point function */
extern void Trig_lambdamgm_T5(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S19>/Data Type Duplicate' : Unused code path elimination
 * Block '<S20>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S26>/Data Type Propagation' : Unused code path elimination
 * Block '<S27>/Constant' : Unused code path elimination
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Propagation' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S33>/Data Type Propagation' : Unused code path elimination
 * Block '<S34>/Constant' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Propagation' : Unused code path elimination
 * Block '<S64>/Data Type Duplicate' : Unused code path elimination
 * Block '<S68>/Data Type Duplicate' : Unused code path elimination
 * Block '<S37>/Data Type Duplicate' : Unused code path elimination
 * Block '<S73>/Data Type Duplicate' : Unused code path elimination
 * Block '<S72>/Data Type Duplicate' : Unused code path elimination
 * Block '<S74>/Data Type Duplicate' : Unused code path elimination
 * Block '<S75>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S78>/Data Type Duplicate' : Unused code path elimination
 * Block '<S77>/Data Type Duplicate' : Unused code path elimination
 * Block '<S79>/Data Type Duplicate' : Unused code path elimination
 * Block '<S80>/Data Type Duplicate' : Unused code path elimination
 * Block '<S81>/Data Type Duplicate' : Unused code path elimination
 * Block '<S81>/Data Type Propagation' : Unused code path elimination
 * Block '<S82>/Data Type Duplicate' : Unused code path elimination
 * Block '<S82>/Data Type Propagation' : Unused code path elimination
 * Block '<S85>/Data Type Duplicate' : Unused code path elimination
 * Block '<S87>/Data Type Duplicate' : Unused code path elimination
 * Block '<S86>/Data Type Duplicate' : Unused code path elimination
 * Block '<S86>/Data Type Propagation' : Unused code path elimination
 * Block '<S24>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S26>/Reshape' : Reshape block reduction
 * Block '<S27>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S27>/Reshape' : Reshape block reduction
 * Block '<S31>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S33>/Reshape' : Reshape block reduction
 * Block '<S34>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S34>/Reshape' : Reshape block reduction
 * Block '<S64>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S64>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S64>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S64>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S72>/Conversion' : Eliminate redundant data type conversion
 * Block '<S72>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S72>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S72>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S78>/Conversion' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion3' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'lambdamgm'
 * '<S1>'   : 'lambdamgm/Model Info'
 * '<S2>'   : 'lambdamgm/lambdamgm'
 * '<S3>'   : 'lambdamgm/lambdamgm/T5ms'
 * '<S4>'   : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms'
 * '<S5>'   : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold'
 * '<S6>'   : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Diag_Func'
 * '<S7>'   : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Diag_Func2'
 * '<S8>'   : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Interp_lready'
 * '<S9>'   : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag'
 * '<S10>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Select_Load'
 * '<S11>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/VLambdaState_Calc'
 * '<S12>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/cal_freqosclam'
 * '<S13>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Calc_Diag_Vlam'
 * '<S14>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Calc_Diag_Vlam2'
 * '<S15>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables'
 * '<S16>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2'
 * '<S17>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis'
 * '<S18>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Rate_VLambda2'
 * '<S19>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Calc_Diag_Vlam/SetDiagState'
 * '<S20>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Calc_Diag_Vlam2/SetDiagState'
 * '<S21>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Calc_Ratio'
 * '<S22>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Look-up 2D TBOFFSVLAMCRK'
 * '<S23>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Look-up 2D TBTHRVLAMR2P'
 * '<S24>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Calc_Ratio/PreLookUpIdSearch_U16_1'
 * '<S25>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Calc_Ratio/PreLookUpIdSearch_U16_2'
 * '<S26>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Look-up 2D TBOFFSVLAMCRK/Look2D_S8_U16_S16'
 * '<S27>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables/Look-up 2D TBTHRVLAMR2P/Look2D_IR_U8'
 * '<S28>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Calc_Ratio'
 * '<S29>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Look-up 2D TBOFFSVLAM2CRK'
 * '<S30>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Look-up 2D TBTHRVLAM2R2P'
 * '<S31>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Calc_Ratio/PreLookUpIdSearch_U16_1'
 * '<S32>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Calc_Ratio/PreLookUpIdSearch_U16_2'
 * '<S33>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Look-up 2D TBOFFSVLAM2CRK/Look2D_S8_U16_S16'
 * '<S34>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/LookUp_Tables2/Look-up 2D TBTHRVLAM2R2P/Look2D_IR_U8'
 * '<S35>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Calc_Transitions'
 * '<S36>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition'
 * '<S37>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Rate_VLambda'
 * '<S38>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/fc_SlowLean'
 * '<S39>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/fc_SlowRich'
 * '<S40>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant'
 * '<S41>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant1'
 * '<S42>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant2'
 * '<S43>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant3'
 * '<S44>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant4'
 * '<S45>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant5'
 * '<S46>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Constant6'
 * '<S47>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Zero1'
 * '<S48>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Zero3'
 * '<S49>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Compare To Zero4'
 * '<S50>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc'
 * '<S51>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc1'
 * '<S52>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTim'
 * '<S53>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Load_Hyst'
 * '<S54>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Load_Stab'
 * '<S55>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Rpm_Hyst'
 * '<S56>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Rpm_Stab'
 * '<S57>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/TWater_Hyst'
 * '<S58>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc/Chart'
 * '<S59>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc1/Chart'
 * '<S60>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTim/Compare To Zero'
 * '<S61>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Load_Hyst/Compare To Zero'
 * '<S62>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Load_Hyst/Dead Zone Dynamic'
 * '<S63>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Load_Stab/Compare To Zero'
 * '<S64>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Load_Stab/Steady_State_Detect'
 * '<S65>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Rpm_Hyst/Compare To Zero'
 * '<S66>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Rpm_Hyst/Dead Zone Dynamic'
 * '<S67>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Rpm_Stab/Compare To Zero'
 * '<S68>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/Rpm_Stab/Steady_State_Detect'
 * '<S69>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/TWater_Hyst/Compare To Zero'
 * '<S70>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/TWater_Hyst/Dead Zone Dynamic'
 * '<S71>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Rate_VLambda/FoVLamRate'
 * '<S72>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Rate_VLambda/RateLimiter_S32_1'
 * '<S73>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Rate_VLambda/RateLimiter_S32_1/Data Type Conversion Inherited1'
 * '<S74>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/fc_SlowLean/SetDiagState'
 * '<S75>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/fc_SlowRich/SetDiagState'
 * '<S76>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Rate_VLambda2/FoVLam2Rate'
 * '<S77>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Rate_VLambda2/RateLimiter_S32_1'
 * '<S78>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/Rate_VLambda2/RateLimiter_S32_1/Data Type Conversion Inherited1'
 * '<S79>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Diag_Func/SetDiagState'
 * '<S80>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Diag_Func2/SetDiagState'
 * '<S81>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Interp_lready/LookUp_U8_S1'
 * '<S82>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Interp_lready/LookUp_U8_S16'
 * '<S83>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Chart'
 * '<S84>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Diag_CAT'
 * '<S85>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Diag_CAT/SetDiagState'
 * '<S86>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/cal_freqosclam/FOF_Reset_S16_FXP'
 * '<S87>'  : 'lambdamgm/lambdamgm/T5ms/Lambda_T5ms/cal_freqosclam/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': lambdamgm
 */
#endif                                 /* RTW_HEADER_lambdamgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
