#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
#define USE_RPM_CAN                  0
#undef USE_ANGLE_EX

#define MAX_INDEX             20
#define INDEX_GAP0            1 /* Indice dell'evento successivo al primo dente dopo il primo buco */
#define INDEX_GAP1            11 /* Indice dell'evento successivo al primo dente dopo il secondo buco */

#define INDEX_TOOTH_2_GROWING   12
#define INDEX_TOOTH_2_FALLING   5

#define CAM_TYPE                            CAM_MAP_SENS    // =0 sensore assente =1 solo livello (DIGIO) =2 livello + fronti (PIO) =3 uso pressione collettore
#ifdef CAMSENSE_CHANNEL
#define CAM_EDGE                       (ETPUA_UC0+CAMSENSE_CHANNEL)    // Canale di eTPU usato per generare interrupt ad ogni fronte di Camma
#endif

/* COSTANTI PER CALCOLO AbsHTdc, AbsPreTdc, AbsPreHTdc */
#define TDC2PREHTDC 0
#define TDC2PRETDC 1
#define TDC2HTDC 2

#define INDEX_CAM0                  7  /* Indice dell'evento corrispondente a camma 0 */
#define INDEX_CAM1                  15 /* Indice dell'evento corrispondente a camma 1  */
#define N_CAM_EDGE                  0
#define ENABLE_RESYNC_ON_EX_SYNC    0
#define ENABLE_RESYNC_ON_EV_CAMTEST 1
#define MAX_TOOTH_DELETED           2 

typedef struct 
{
  uint8_T   ToothNumber;    // Numero di dente sul quale deve essere generata l'eccezione
  uint8_T   AbsTdcIndex;    // Absolute Tdc index table
  uint16_T  EventType;      // Event table type
} EVENT_STRUCT;
#endif

#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
#define USE_RPM_CAN           0
#undef USE_ANGLE_EX

#define MAX_INDEX             17
#define INDEX_GAP0            1 /* Indice dell'evento successivo al primo dente dopo il primo buco */
#define INDEX_GAP1            10 /* Indice dell'evento successivo al primo dente dopo il secondo buco */

#define INDEX_TOOTH_2_GROWING   15
#define INDEX_TOOTH_2_FALLING    8

#define CAM_TYPE                            CAM_MAP_SENS    // =0 sensore assente =1 solo livello (DIGIO) =2 livello + fronti (PIO) =3 uso pressione collettore
#ifdef CAMSENSE_CHANNEL
#define CAM_EDGE                       (ETPUA_UC0+CAMSENSE_CHANNEL)    // Canale di eTPU usato per generare interrupt ad ogni fronte di Camma
#endif

/* COSTANTI PER CALCOLO AbsHTdc, AbsPreTdc, AbsPreHTdc */
#define TDC2PREHTDC 0
#define TDC2PRETDC 1
#define TDC2HTDC 2

#define INDEX_CAM0                   8 /* Indice dell'evento corrispondente a camma 0 */
#define INDEX_CAM1                  15 /* Indice dell'evento corrispondente a camma 1  */
#define N_CAM_EDGE                   0
#define ENABLE_RESYNC_ON_EX_SYNC     0
#define ENABLE_RESYNC_ON_EV_CAMTEST  1
#define MAX_TOOTH_DELETED            2

typedef struct 
{
  uint8_T   ToothNumber;    // Numero di dente sul quale deve essere generata l'eccezione
  uint8_T   AbsTdcIndex;    // Absolute Tdc index table
  uint16_T  EventType;      // Event table type
} EVENT_STRUCT;
#endif

#if (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
#define USE_RPM_CAN           0
#undef USE_ANGLE_EX

#define MAX_INDEX             18
#define INDEX_GAP0            1 /* Indice dell'evento successivo al primo dente dopo il primo buco */
#define INDEX_GAP1            10 /* Indice dell'evento successivo al primo dente dopo il secondo buco */

#define INDEX_TOOTH_2_GROWING   15
#define INDEX_TOOTH_2_FALLING    8

#define CAM_TYPE                            CAM_MAP_SENS    // =0 sensore assente =1 solo livello (DIGIO) =2 livello + fronti (PIO) =3 uso pressione collettore
#ifdef CAMSENSE_CHANNEL
#define CAM_EDGE                       (ETPUA_UC0+CAMSENSE_CHANNEL)    // Canale di eTPU usato per generare interrupt ad ogni fronte di Camma
#endif

/* COSTANTI PER CALCOLO AbsHTdc, AbsPreTdc, AbsPreHTdc */
#define TDC2PREHTDC 0
#define TDC2PRETDC 1
#define TDC2HTDC 2

#define INDEX_CAM0                  15 /* Indice dell'evento corrispondente a camma 0 */
#define INDEX_CAM1                   8 /* Indice dell'evento corrispondente a camma 1  */
#define N_CAM_EDGE                   0
#define ENABLE_RESYNC_ON_EX_SYNC     0
#define ENABLE_RESYNC_ON_EV_CAMTEST  1
#define MAX_TOOTH_DELETED            2

typedef struct 
{
  uint8_T   ToothNumber;    // Numero di dente sul quale deve essere generata l'eccezione
  uint8_T   AbsTdcIndex;    // Absolute Tdc index table
  uint16_T  EventType;      // Event table type
} EVENT_STRUCT;
#endif

