/*
 * File: launchctrl_out.h
 *
 * Code generated for Simulink model 'LaunchCtrl'.
 *
 * Model version                  : 1.234
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jun 14 15:33:33 2022
 */

#ifndef RTW_HEADER_launchctrl_out_h_
#define RTW_HEADER_launchctrl_out_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define LC_DISABLE                     0U

/* LC_DISABLE */
#define LC_ENABLING                    1U

/* LC_ENABLING */
#define LC_LAUNCH                      4U

/* LC_LAUNCH */
#define LC_READY                       3U

/* LC_READY */
#define LC_RET                         5U

/* LC_RET */
#define LC_TO_IDLE                     7U

/* LC_TO_IDLE */
#define LC_TO_LIM                      6U

/* LC_TO_LIM */
#define LC_WAIT_IDLE                   2U

/* LC_WAIT_IDLE */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T AwLevel;

/* selector */
extern int16_T CmeLcSat;

/* CmiLcP */
extern int16_T CmeLcSatOffI;

/* CmiLcP */
extern uint8_T EnAwMaxLevel;

/* selector */
extern uint8_T EnLcMaxLevel;

/* selector */
extern uint8_T EnTcMaxLevel;

/* selector */
extern uint8_T FlgCtfLc;

/* flg cutoff */
extern uint8_T FlgEnLcKm;

/* flg Km */
extern uint8_T FlgLcCmiLow;

/* flag */
extern uint8_T FlgLcDiag;

/* flag */
extern uint8_T FlgLcEn;

/* flag */
extern uint8_T FlgLcEnd;

/* flag */
extern uint8_T FlgLcLaunch;

/* flag */
extern uint8_T FlgLcLevel;

/* flag */
extern uint8_T FlgLcLim;

/* flg limiter */
extern uint8_T FlgLcReady;

/* flag */
extern uint8_T FlgLcRet;

/* flag */
extern uint8_T FlgLcTrg;

/* flag cutoff */
extern uint32_T IDLaunchCtrl;

/* ID Version */
extern uint8_T IdxLcCutOff;

/* idx cutoff */
extern uint8_T LcActive;

/* LC Active */
extern uint8_T LcLevel;

/* selector */
extern int16_T LcRpmErr;

/* Err */
extern int8_T LcTrip;

/* counter Trip */
extern uint16_T RpmLcTrgCme;

/* RpmLcTrg */
extern int16_T RpmLcTrgCtf;

/* RpmLcTrg */
extern uint8_T SetTracCtrl;

/* selector */
extern uint8_T StLc;

/* Lc Status */
extern uint16_T VehLcAxIntSat;

/* VehRbVfAxInt saturation in Spring Up */

extern uint16_T  EELcTrip;             /* EE output */
extern uint32_T  EELcKm;               /* EE output */

#endif                                 /* RTW_HEADER_launchctrl_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
