/*
 * File: IdleMgm_private.h
 *
 * Code generated for Simulink model 'IdleMgm'.
 *
 * Model version                  : 1.926
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Oct  6 15:25:43 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (26), Warnings (7), Error (0)
 */

#ifndef RTW_HEADER_IdleMgm_private_h_
#define RTW_HEADER_IdleMgm_private_h_
#include "rtwtypes.h"
#include "IdleMgm.h"

/* Includes for objects with custom storage classes. */
#include "mathlib.h"
#include "rpm_limiter.h"
#include "GearPosClu_Mgm.h"
#include "trq_est.h"
#include "trq_saflim.h"
#include "DiagFlags_out.h"
#include "engflag.h"
#include "Idlectf_mgm.h"
#include "vspeed_mgm.h"
#include "lightoffmgm_out.h"
#include "PTrain_Diag.h"
#include "Trq_Driver.h"
#include "GearPosClu_mgm.h"
#include "idlectf_mgm.h"
#include "Patm_model.h"
#include "Gearshift_mgm.h"
#include "syncmgm.h"
#include "trq_drivmgm.h"
#include "Launchctrl_out.h"
#include "temp_mgm.h"
#include "throttle_target.h"
#include "analogin.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int32_T DNTHINTVBATDIS;         /* Variable: DNTHINTVBATDIS
                                        * Referenced by: '<S65>/Relay'
                                        * Lower value of integral to disable Economy Mode Idle Rpm set-point
                                        */
extern int32_T MAXINTVBATERR;          /* Variable: MAXINTVBATERR
                                        * Referenced by: '<S65>/MAXINTVBATERR'
                                        * Saturation of integral of error on Vbat
                                        */
extern int32_T UPTHINTVBATENA;         /* Variable: UPTHINTVBATENA
                                        * Referenced by: '<S65>/Relay'
                                        * Higher value of integral to enable Economy Mode Idle Rpm set-point
                                        */
extern int16_T BKIDLRPMERR[7];         /* Variable: BKIDLRPMERR
                                        * Referenced by: '<S18>/BKIDLRPMERR'
                                        * IdlRpmErr breakpoint for VTIDLPROP/INT
                                        */
extern int16_T BKIDLTRQOFFSTP[4];      /* Variable: BKIDLTRQOFFSTP
                                        * Referenced by: '<S13>/BKIDLTRQOFFSTP'
                                        * BK Switch off step for Idle Speed Control
                                        */
extern int16_T IDLRPMEDBAND;           /* Variable: IDLRPMEDBAND
                                        * Referenced by: '<S15>/IDLRPMEDBAND'
                                        * Dead Band on Rpm Error for Idle Speed Control
                                        */
extern int16_T IDLRPMEMAX;             /* Variable: IDLRPMEMAX
                                        * Referenced by: '<S15>/IDLRPMEMAX'
                                        * Max rpm error for idle speed Control
                                        */
extern int16_T MAXRATEIDLE;            /* Variable: MAXRATEIDLE
                                        * Referenced by: '<S67>/MAXRATEIDLE'
                                        * Idle Rpm Target Increasing step
                                        */
extern int16_T THRRPMERRADP;           /* Variable: THRRPMERRADP
                                        * Referenced by: '<S45>/THRRPMERRADP'
                                        * Stability threshold for abs(IdlRpmErr)
                                        */
extern int16_T THRRPMOBJADP;           /* Variable: THRRPMOBJADP
                                        * Referenced by: '<S45>/THRRPMOBJADP'
                                        * Stability threshold for abs(RpmIdleObj-RpmIdleObj0)
                                        */
extern int16_T VTIDLRPMEMIN[7];        /* Variable: VTIDLRPMEMIN
                                        * Referenced by: '<S15>/VTIDLRPMEMIN'
                                        * Min rpm error for idle speed Control
                                        */
extern int16_T VTIDLRPMEMINSP[7];      /* Variable: VTIDLRPMEMINSP
                                        * Referenced by: '<S15>/VTIDLRPMEMINSP'
                                        * Min rpm error for idle speed Control
                                        */
extern int16_T VTIDLACCINT[7];         /* Variable: VTIDLACCINT
                                        * Referenced by: '<S25>/VTIDLACCINT'
                                        * Integral term for idle speed control
                                        */
extern int16_T VTIDLINT[7];            /* Variable: VTIDLINT
                                        * Referenced by: '<S23>/VTIDLINT'
                                        * Integral term for idle speed control
                                        */
extern int16_T IDLTRQOFFSTP;           /* Variable: IDLTRQOFFSTP
                                        * Referenced by: '<S13>/IDLTRQOFFSTP'
                                        * Switch off step for Idle Speed Control
                                        */
extern int16_T VTIDLTRQOFFSTP[4];      /* Variable: VTIDLTRQOFFSTP
                                        * Referenced by: '<S13>/VTIDLTRQOFFSTP'
                                        * Switch off step for Idle Speed Control
                                        */
extern int16_T BKTWIDLE[10];           /* Variable: BKTWIDLE
                                        * Referenced by: '<S85>/BKTWIDLE'
                                        * TWater breakpoint for VTRPMIDLE
                                        */
extern int16_T IDLTRQMAX;              /* Variable: IDLTRQMAX
                                        * Referenced by: '<S9>/IDLTRQMAX'
                                        * Maximum value of IdleTrqI
                                        */
extern int16_T THRIDLTRQADP;           /* Variable: THRIDLTRQADP
                                        * Referenced by: '<S45>/THRIDLTRQADP'
                                        * Stability threshold for abs(IdleTrqI-IdleTrqF)
                                        */
extern int16_T MAXIDLETRQINTLOFF;      /* Variable: MAXIDLETRQINTLOFF
                                        * Referenced by: '<S19>/MAXIDLETRQINTLOFF'
                                        * offset LOff
                                        */
extern int16_T VTIDLACCPROP[7];        /* Variable: VTIDLACCPROP
                                        * Referenced by: '<S25>/VTIDLACCPROP'
                                        * Proportional term for idle speed control
                                        */
extern int16_T VTIDLACCPROPGAIN[4];    /* Variable: VTIDLACCPROPGAIN
                                        * Referenced by: '<S25>/VTIDLACCPROPGAIN'
                                        * Prop gain
                                        */
extern int16_T VTIDLPROP[7];           /* Variable: VTIDLPROP
                                        * Referenced by: '<S23>/VTIDLPROP'
                                        * Proportional term for idle speed control
                                        */
extern int16_T VTMAXIDLETRQINT[2];     /* Variable: VTMAXIDLETRQINT
                                        * Referenced by: '<S19>/VTMAXIDLETRQINT'
                                        * Maximum value of integral term
                                        */
extern int16_T VTMINIDLETRQINT[2];     /* Variable: VTMINIDLETRQINT
                                        * Referenced by: '<S19>/VTMINIDLETRQINT'
                                        * Minimum value of integral term
                                        */
extern int16_T VTMINIDLETRQINTOFF0[5]; /* Variable: VTMINIDLETRQINTOFF0
                                        * Referenced by: '<S14>/VTMINIDLETRQINTOFF0'
                                        * Minimum value of integral term offest
                                        */
extern uint16_T KFIDLEADP;             /* Variable: KFIDLEADP
                                        * Referenced by: '<S8>/KFIDLEADP'
                                        * Filter constant for IdleTrq
                                        */
extern uint16_T KFILTIDLEACC;          /* Variable: KFILTIDLEACC
                                        * Referenced by: '<S70>/KFILTIDLEACC'
                                        * kf
                                        */
extern uint16_T VTKFILTIDLE[7];        /* Variable: VTKFILTIDLE
                                        * Referenced by: '<S71>/VTKFILTIDLE'
                                        * Idle Rpm Target Filter Coefficient
                                        */
extern uint16_T BKGASOFFRPMIDLE[5];    /* Variable: BKGASOFFRPMIDLE
                                        * Referenced by: '<S70>/BKGASOFFRPMIDLE'
                                        * Gas Idle offset
                                        */
extern uint16_T THVBATRPMOBJ;          /* Variable: THVBATRPMOBJ
                                        * Referenced by: '<S65>/THVBATRPMOBJ'
                                        * Threshold on vbat to increase the rpm idle target
                                        */
extern uint16_T VTGNIDLEINT[7];        /* Variable: VTGNIDLEINT
                                        * Referenced by: '<S23>/VTGNIDLEINT'
                                        * Integral gain
                                        */
extern uint16_T BKIDLPROPGAIN[4];      /* Variable: BKIDLPROPGAIN
                                        * Referenced by: '<S22>/BKIDLPROPGAIN'
                                        * Bk Prop gain
                                        */
extern uint16_T BKMINIDLETRQINTOFF0[5];/* Variable: BKMINIDLETRQINTOFF0
                                        * Referenced by: '<S14>/BKMINIDLETRQINTOFF0'
                                        * Bk VTMINIDLETRQINTOFF0
                                        */
extern uint16_T ECONRPMIDLE;           /* Variable: ECONRPMIDLE
                                        * Referenced by: '<S67>/Constant'
                                        * Idle Rpm Target in Economy Mode
                                        */
extern uint16_T IDLADPTIMEOUT;         /* Variable: IDLADPTIMEOUT
                                        * Referenced by: '<S8>/IdleTrqAdp_Calc'
                                        * Timeout for Idle speed control Adaptation
                                        */
extern uint16_T MAXRPMIDLEOBJ0RATE;    /* Variable: MAXRPMIDLEOBJ0RATE
                                        * Referenced by: '<S73>/MAXRPMIDLEOBJ0RATE'
                                        * RpmIdleObj rate limiter
                                        */
extern uint16_T MAXRPMIDLEOBJ0RATEN;   /* Variable: MAXRPMIDLEOBJ0RATEN
                                        * Referenced by: '<S73>/MAXRPMIDLEOBJ0RATEN'
                                        * RpmIdleObj rate limiter - neutral
                                        */
extern uint16_T RAISERPMCLUTCH;        /* Variable: RAISERPMCLUTCH
                                        * Referenced by: '<S67>/RAISERPMCLUTCH'
                                        * Delta to increase the rpm idle target clutch
                                        */
extern uint16_T RAISERPMOBJ;           /* Variable: RAISERPMOBJ
                                        * Referenced by: '<S85>/RAISERPMOBJ'
                                        * Delta to increase the rpm idle target
                                        */
extern uint16_T VTGASOFFRPMIDLE[5];    /* Variable: VTGASOFFRPMIDLE
                                        * Referenced by: '<S70>/VTGASOFFRPMIDLE'
                                        * Rpm Idle offset
                                        */
extern uint16_T VTRPMIDLE[10];         /* Variable: VTRPMIDLE
                                        * Referenced by: '<S85>/VTRPMIDLE'
                                        * (SR) Idle Rpm Static Target Vector
                                        */
extern uint8_T HYSTRPMIDLEOBJ0INC;     /* Variable: HYSTRPMIDLEOBJ0INC
                                        * Referenced by:
                                        *   '<S70>/HYSTRPMIDLEOBJ0INC'
                                        *   '<S72>/HYSTRPMIDLEOBJ0INC'
                                        *   '<S74>/HYSTRPMIDLEOBJ0INC'
                                        * Hyst applied to RpmIdleObj0 to enable RpmIdleObj rate limiter
                                        */
extern uint8_T RPMIDLEOBJ0INC;         /* Variable: RPMIDLEOBJ0INC
                                        * Referenced by:
                                        *   '<S70>/RPMIDLEOBJ0INC'
                                        *   '<S71>/RPMIDLEOBJ0INC'
                                        *   '<S72>/RPMIDLEOBJ0INC'
                                        *   '<S74>/RPMIDLEOBJ0INC'
                                        * Delta to increase RpmIdleObj0 out of idle
                                        */
extern uint8_T DISABLESTIDLE2;         /* Variable: DISABLESTIDLE2
                                        * Referenced by: '<S4>/IdleTrq_Mgm'
                                        * DISABLESTIDLE2
                                        */
extern uint8_T ENIDLEADP;              /* Variable: ENIDLEADP
                                        * Referenced by: '<S45>/ENIDLEADP'
                                        * Enable Idle speed control Adaptation term (=1)
                                        */
extern uint8_T FORCEECONMODE;          /* Variable: FORCEECONMODE
                                        * Referenced by: '<S64>/FORCEECONMODE'
                                        * Force Idle speed Economy Mode (=1)
                                        */
extern uint8_T TIMOUTIDLE;             /* Variable: TIMOUTIDLE
                                        * Referenced by: '<S66>/RpmIdleObj_State'
                                        * Timeout idle
                                        */
extern void IdleMgm_vtIDLTRQOFFSTP(uint16_T rtu_Rpm, uint8_T rtu_from, int16_T
  *rty_IdlTrqOffstp);
extern void IdleMgm_Idle_trq_reset(int16_T rtu_IdlTrqOffstp, int16_T rtu_CmiSafI,
  uint8_T rtu_resetMem, int16_T rtu_CmiSafP, uint8_T rtu_FlgCmeLow);
extern void IdleMgm_Idle_trq_calc(int16_T rtu_CmiSafI, int16_T rtu_CmiSafP,
  int16_T rtu_PiIdleTrqProp, int16_T rtu_PiIdleTrqInt, boolean_T
  *rty_intStopSatP);
extern void IdleMgm_outidle(uint16_T rtu_Rpm, uint16_T rtu_RpmIdleObj0_raw);
extern void IdleMgm_filt(uint16_T rtu_RpmIdleObj0_raw, uint8_T rtu_GearPosClu);
extern void IdleMgm_fc_accCtrl(uint16_T rtu_Rpm, uint16_T rtu_RpmIdleObj0_raw,
  uint16_T rtu_GasPosCC);
extern void IdleMgm_rate(uint16_T rtu_RpmIdleObj0_raw, uint8_T rtu_GearPosClu);
extern void IdleMgm_test(uint16_T rtu_Rpm, boolean_T *rty_flgRpmOver);
extern void IdleMgm_test1(uint16_T rtu_Rpm, uint8_T rtu_GearPosClu, uint8_T
  rtu_oldGpc, uint16_T rtu_FlgDeltaVehSpeedSup);
extern void IdleMgm_Idle_Controller(void);
extern void IdleMgm_rpmtarget_Init(void);
extern void IdleMgm_rpmtarget(void);
extern void IdleMgm_calc_cond(void);
extern void IdleMgm_Idle_Learning(void);
extern void IdleMgm_Calc_Init(void);
extern void IdleMgm_Calc(void);
extern void IdleMgm_Reset(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint32_T IdleMgmTimer;          /* '<Root>/IdleMgmTimer' */

/* Counter */
extern int16_T IdleTrqAdp;             /* '<S1>/Data Store Memory5' */

/* Idle speed control Torque Adaptation term */
#endif                                 /* RTW_HEADER_IdleMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
