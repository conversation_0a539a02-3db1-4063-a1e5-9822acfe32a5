rem FILE DI CONFIFURAZIONE per BatchRunnerPostLinker

rem i valori di configurazione sono reperibili nei file .map e .ld;
rem nel map file compaiono le "omonime" label(=__NomeVariabile)
rem i valori inseriti in hex sono automaticamente convertiti in dec

rem Parametri utilizzati da gmemfile
set  APP_END_HEX=0x160000
set /A APP_END=APP_END_HEX
rem echo %APP_END%

rem Parametri utilizzati da bin2srec per SCU
set APP_START_HEX=0x40000
set /A APP_START=APP_START_HEX
rem Parametri usati per il taglio dei binari
set /a APP_LENGTH=%APP_END%-%APP_START%
rem echo prova app len %APP_LENGTH%

rem Parametri utilizzati da checksum_calc 
rem Per la regione di APPLICATVIO
set /A APP_START=APP_START_HEX
rem echo %APP_START%
set /A APP_TAG_START=0x15FFC0
rem echo %APP_TAG_START%
set /a APP_TAG_CHKSUM_ADDR= %APP_TAG_START% + 56
rem echo %APP_TAG_CHKSUM_ADDR%

rem Per la regione di MINIBOOT
set /A MINIBOOT_START=0x0000
rem echo %MINIBOOT_START%
set /A MINIBOOT_TAG_START=0x3FC0
rem echo %MINIBOOT_TAG_START%
set /a MINIBOOT_TAG_CHKSUM_ADDR= %MINIBOOT_TAG_START% + 56
rem echo %MINIBOOT_TAG_CHKSUM_ADDR%

rem Per la regione di BOOT
set /A BOOT_START=0x1C000
rem echo %BOOT_START%
set /A BOOT_TAG_START=0x2FFC0
rem echo %BOOT_TAG_START%
set /a BOOT_TAG_CHKSUM_ADDR= %BOOT_TAG_START% + 56
rem echo %BOOT_TAG_CHKSUM_ADDR%

rem Parametri utilizzati da bin2srec
set CALIB_ROM_START_HEX=0x30000
rem echo %CALIB_ROM_START_HEX%
set CALIB_ROM_END_HEX=0x40000

rem Parametri utilizzati da checksum_calc per la regione di calibrazione
set /A CALIB_ROM_START=CALIB_ROM_START_HEX
rem echo %CALIB_ROM_START%

set /A CALIB_TAG_START=0x35FC0
rem echo %CALIB_TAG_START%
set /a CALIB_TAG_CHKSUM_ADDR= %CALIB_TAG_START% + 56
echo %CALIB_TAG_CHKSUM_ADDR%

rem Parametri utilizzati per il taglio del bin di calib
set /A CALIB_ROM_END=CALIB_ROM_END_HEX
set /a CALIB_ROM_LENGTH=%CALIB_ROM_END%-%CALIB_ROM_START%
echo prova calib len %CALIB_ROM_LENGTH%

rem Parametri utilizzati da binmerge per l'aggiornamento della calib_buildCode all'inizio di .calib
rem con il valore della versione di applicativo definita nella APP_TAG
set UPDATE_CALIB_BUILDCODE="1"

IF %UPDATE_CALIB_BUILDCODE%=="1"  (
  set /a APP_TAG_APPVERS_ADDR= %APP_TAG_START% + 12
  set /a CALIB_ROM_APPCHKSUM= %CALIB_ROM_START% + 12
)

rem Parametri utilizzati da PostProcessSRecMot e A2lUnion
set VMEMORY_USAGE="NO_VIRTUAL_MEMORY"
rem merge della regione di app e di calib invertita (CALIB+APP+RAM)
set MERGE_APPL_AND_CALIB="YES-i"

set COMPILER_IN_USE="1.6"

rem Parametri per le estensioni che vengono create per i file .hex o .s37
set OUTPUT_EXTENSION="s37"
rem Parametri utilizzati per la copia ROM-RAM
set NUM_OF_ADDITIONAL_CALIB_SECT=0
set MULTI_CALIB_SECTION=""

rem Parametri utilizzati dal  Profiling
set /A RAM_SIZE_TOTAL=0x10000
rem echo %RAM_SIZE_TOTAL%

rem Abilitazione dell'analisi degli errori MISRA mediante l'utilizzo di MisraAnalysis.pl
set ENABLE_MISRA_ANALYSIS="0"
echo %ENABLE_MISRA_ANALYSIS%

set BUILD_KWP2_KIT="0"

