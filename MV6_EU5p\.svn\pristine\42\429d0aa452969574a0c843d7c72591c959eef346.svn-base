/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV7/tree/APPLICATION/ENGFLAG/engflag.c   $   */
/* $ Description:                                                                                                */
/* $Revision:: 13888  $                                                                                          */
/* $Date:: 2022-03-01 09:54:25 +0100 (mar, 01 mar 2022)   $                                                      */
/* $Author:: LanaL                   $                                                                           */
/*****************************************************************************************************************/

#include "ctrlactive.h"

#ifdef _BUILD_CTRLACTIVE_

uint16_T FlgCtrlActive;
uint16_T FlgCtrlActiveTot;

void CtrlActive_Init(void)
{
    FlgCtrlActive = 0u;
    FlgCtrlActiveTot = 0u;
}

void CtrlActive_T10ms(void)
{
    uint16_T tmp;

    FlgCtrlActive = 0u;
    FlgCtrlActiveTot = 0u;

    /* Tot */
    FlgCtrlActiveTot = (IdxCtfFlg != 0); // Cut off

    /* Act */
    tmp = (IdleFlg != 0);
    FlgCtrlActive = FlgCtrlActive | (tmp << 1); // Idle control

    /* Act */
    FlgCtrlActive = FlgCtrlActive | ((uint16_T)FlgCmiLimit << 2); // Rpm limiter

    /* Act */
    tmp = ((StTracCtrl == TC_ACC) || (StTracCtrl == TC_SMOOTH) || (StTracCtrl == TC_ACC_GEARUP_FILT));
    FlgCtrlActive = FlgCtrlActive | (tmp << 3); // TC

    /* Act */
    tmp = (StSatAw != ST_SAT_MAX);
    FlgCtrlActive = FlgCtrlActive | (tmp << 4); // FLC

    /* Act */
    FlgCtrlActive = FlgCtrlActive | ((uint16_T)LcActive << 5); // LC

    /* Tot */
    tmp = (StQShift != QSHIFT_DISABLE);
    FlgCtrlActiveTot = FlgCtrlActiveTot | (tmp << 6); // Quick shifter

    /* Act */
    tmp = (StSpL  != SPL_DISABLE);
    FlgCtrlActive = FlgCtrlActive | (tmp << 7); // Speed limiter

    /* Act */
    tmp = ((StCreepLim == ST_CREEPING) || (StCreepLim == ST_RETURN));
    FlgCtrlActive = FlgCtrlActive | (tmp << 8); // Creep limiter

    /* Act */
    FlgCtrlActive = FlgCtrlActive | ((uint16_T)FlgCmiSatP << 9); // Saturation control

    /* Tot */
    FlgCtrlActiveTot = FlgCtrlActiveTot | FlgCtrlActive;
}

#endif

