/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_ANALOGQS_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Flag forced [flag]
CALQUAL uint8_T FOGSFLAGS =  0u;   // 0
//faultt threshold [mV]
CALQUAL int16_T THVQSFAULTHIGH = 6;   //(  29.2968750*0.2048)
//faultt threshold [mV]
CALQUAL int16_T THVQSFAULTLOW = 6;   //(  29.2968750*0.2048)
//UP Shift threshold [mV]
CALQUAL int16_T THVQSHIGH = 6;   //(  29.2968750*0.2048)
//threshold rel [mV]
CALQUAL int16_T THVQSHIGHREL = 6;   //(  29.2968750*0.2048)
//Down Shift threshold [mV]
CALQUAL int16_T THVQSLOW = 6;   //(  29.2968750*0.2048)
//threshold rel [mV]
CALQUAL int16_T THVQSLOWREL = 6;   //(  29.2968750*0.2048)
//Time match threshold [ms]
CALQUAL uint8_T TIMAQSMATCH =    0u;   //   0
//Time match threshold [ms]
CALQUAL uint8_T TIMAQSMATCH1 =    0u;   //   0
//Blanking time QS [ms]
CALQUAL uint16_T TIMVQSBLANKDN =     30u;   //    30
//Blanking time QS [ms]
CALQUAL uint16_T TIMVQSBLANKUP =     30u;   //    30
//Fault time QS [ms]
CALQUAL uint32_T TIMVQSFAULT =           0u;   //          0
//Time to find Down shift [ms]
CALQUAL uint16_T TIMVQSFINDDOWN =      0u;   //     0
//Time to find Up shift [ms]
CALQUAL uint16_T TIMVQSFINDUP =      0u;   //     0
//Time of validate fault [ms]
CALQUAL uint16_T TIMVQSVALIDF =     30u;   //    30
//AQS Sensor polarity [flag]
CALQUAL uint8_T VQSUPLOW =  1u;   // 1

#endif /* _BUILD_ANALOGQS_ */

