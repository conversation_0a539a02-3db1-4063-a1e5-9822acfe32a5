#ifndef __ACTIVE_DIAG_H__
#define __ACTIVE_DIAG_H__



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "relaymgm.h"
#include "diagmgm_out.h"
#include "diagcanmgm.h"
#include "igncmd.h"
#include "sync.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define LAM_HEAT_PERIOD                100000U
    
    
#define COIL_0 (0)
#define COIL_1 (1)
#define COIL_2 (2)
#define COIL_3 (3)

#define INIT_CLEAN   0x1
#define START_CLEAN  0x2
#define FAILED_CLEAN 0x4
#define DONE_CLEAN   0x8

//#define START_TIME_DIAG     (0)
#define STOP_TIME_IGN0       (1500)  
#define STOP_TIME_IGN1       (1500) 
#define STOP_TIME_IGN2       (1500) 
#define STOP_TIME_IGN3       (1500) 

#define STOP_TIME_INJ0_LOW        (2000) 
#define STOP_TIME_INJ1_LOW        (2000) 
#define STOP_TIME_INJ2_LOW        (2000) 
#define STOP_TIME_INJ3_LOW        (2000) 

#define STOP_TIME_INJ0_HIGH       (2000) 
#define STOP_TIME_INJ1_HIGH       (2000) 
#define STOP_TIME_INJ2_HIGH       (2000) 
#define STOP_TIME_INJ3_HIGH       (2000) 

#define N_INJ_PULSE_DIAG    (1)
#define N_IGN_PULSE_DIAG    (1)
#define SATURATION_DIAG     (25)
#define IGN0_PERIOD          (10)
#define IGN0_TIME            (200)
#define IGN1_PERIOD          (10)
#define IGN1_TIME            (200)
#define IGN2_PERIOD          (10)
#define IGN2_TIME            (200)
#define IGN3_PERIOD          (10)
#define IGN3_TIME            (200)

#define INJ0_LOW_PERIOD           (30)
#define INJ0_LOW_TIME             (30)
#define INJ1_LOW_PERIOD           (30)
#define INJ1_LOW_TIME             (30)
#define INJ2_LOW_PERIOD           (30)
#define INJ2_LOW_TIME             (30)
#define INJ3_LOW_PERIOD           (30)
#define INJ3_LOW_TIME             (30)

#define INJ0_HIGH_PERIOD           (30)
#define INJ0_HIGH_TIME             (30)
#define INJ1_HIGH_PERIOD           (30)
#define INJ1_HIGH_TIME             (30)
#define INJ2_HIGH_PERIOD           (30)
#define INJ2_HIGH_TIME             (30)
#define INJ3_HIGH_PERIOD           (30)
#define INJ3_HIGH_TIME             (30)



#define HLAMBDA_TIME              300             /*30 sec*/

#define HLAMBDA_PERIOD       (84000)              /*     (11.9Hz)    */

#define HLAMBDA_DUTY         (50*256)              /*     (50%)    */

//#define CLEANFUEL_TIME_INJ (((TCLEANFUEL*9)/100))   /* 90% of TCLEANFUEL */
#define CLEANFUEL_STOP_TIME_INJ 450000 /*      1,8s di inj       */
//#define CLEANFUEL_INJ_PERIOD 5         /*         2s             */
//#define CLEANFUEL_START_TIME_DIAG 100
#define CLEANFUEL_TIME_FIRST_INJ (((TCLEANFUEL)/200))   /* 0.5% of TCLEANFUEL */


#define TMAX                (100*256)      /*WATER TEMP MAX */
#define TMIN                (0)            /*WATER TEMP MIN */

/* Note: the injector must be active for a time of 90% of the time which the pump is active */
#define FANCOIL_TIME              300
/* 30 s */
#define MILCMD_TIME               300
/* 2 s */
#define MILCMD_PERIOD             20
#define LOAD_TIME 3000
#define LOWBEAM_TIME              300
#define DRL_TIME                  300
#define STARTER_TIME              1000
#define REARPOSPLATELIGHT_TIME    1000
#define SELF_EXHAUSTVALV_TIME     1
#define DCMOTOR_TIME 50
#define EXHAUSTVALV_TIME 30
//#define PURGECANISTER_TIME        70
//#define PURGECANISTER_DUTY        50*256
//#define PURGECANISTER_PERIOD      40000
#define EXHVALVRST_TIME_ACTIVATION  (3) /*3*5ms = 10ms minimum time.*/

#define VDBW (6*1024)
#define VDBW_MAX (12*1024)
#define VDBW_MIN (-12*1024)

#define SCALING_BIT (100)
#define PERC_SCALING (10)
#define FACT_SCALING (100)




/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef enum 
{  
    FREE    = 0,
    BUSY    = 1
} typBusy_Diag;

typedef union 
{                 /* Pad Configuration Registers */
    vuint16_t R;
    struct {
    vuint16_t:3;
    vuint16_t PA:3;
    vuint16_t OBE:1;
    vuint16_t IBE:1;
    vuint16_t DSC:2;
    vuint16_t ODE:1;
    vuint16_t HYS:1;
    vuint16_t SRC:2;
    vuint16_t WPE:1;
    vuint16_t WPS:1;
    } B;
} PCR_DIAG;

typedef struct
{
    int16_t selfLearning;
    int16_t relFuel;
    int16_t fanCoil;
    int16_t riscLambda;
    int16_t cleanfuelL;
    int16_t cleanfuelH;
    int16_t resetParamAutoAdatt;
    int16_t allStop;
    int16_t starterRelay;
    int16_t LowBeamRelay;
    int16_t TSSValve;
    int16_t SelfExhaustValve;
    int16_t DCMotor;
    int16_t ExhaustValv;
    int16_t HLambda;
    int16_t HLambda2;
    int16_t ExhValvRst;
    int16_t MILCmd;
    int16_t DRLRelay;
    int16_T RearPosPlateLight;
} diagIOStruct;


typedef struct
{
    typBusy_Diag    Inj_Flag_Busy;
    uint8_t        Inj_Test_DIAG ;  
    uint16_t       Iniettore ;
    uint32_t       StopTimeInj;
    uint32_t       StartAngleInj;
    uint32_t        Saturation; 
    uint32_t       TimePeriodInj;
} diagIO_INJStruct;


typedef struct
{
    typBusy_Diag    Ign_Flag_Busy;
    uint8_t        Ign_Test_DIAG ;  
    uint16_t       Bobina ;
    uint32_t       StopTimeIgn;
    uint32_t       StartAngleIgn;
    uint32_t        Saturation; 
    uint32_t       TimePeriodIgn;
} diagIO_IGNStruct;


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern diagIO_INJStruct Inj0_Low;
extern diagIO_INJStruct Inj1_Low;
extern diagIO_INJStruct Inj2_Low;
extern diagIO_INJStruct Inj3_Low;

extern diagIO_INJStruct Inj0_High;
extern diagIO_INJStruct Inj1_High;
extern diagIO_INJStruct Inj2_High;
extern diagIO_INJStruct Inj3_High;

extern diagIO_IGNStruct Ign0;
extern diagIO_IGNStruct Ign1;
extern diagIO_IGNStruct Ign2;
extern diagIO_IGNStruct Ign3;

extern typRelayCmd  FanCoilCmd_DIAG;
extern typRelayCmd  LoadCmd_DIAG;
extern uint8_T      LoadCmd_store,FanCoilCmd_store,LowBeamCmd_store,DRLCmd_store,PurgeCanisterCmd_store;
extern typRelayCmd  StarterCmd_DIAG;
extern typRelayCmd  LowBeamCmd_DIAG;
extern typRelayCmd  DRL_DIAG;
extern typRelayCmd  RearPosPlateLight_DIAG;
//extern typRelayCmd  PurgeCanisterCmd_DIAG;

extern uint8_t      FlgDbwActiveDiag;
extern int16_t      VDbwActiveDiag;
extern uint8_t      StPurgeFuelLine;

extern int16_t      VOutExhActiveDiag;
extern uint8_t      EnExhVMoving;
extern uint8_T      EnExhVSelf;
extern uint8_t      EnExhVZeroPos;

extern uint8_T      LowBeamTest_DIAG;
extern uint8_T      PurgeCanisterTest_DIAG;

extern typBusy_Diag ActveDiag_Enable;
extern diagIOStruct diagIO;
extern typBusy_Diag Load_Flag_Busy;
extern typBusy_Diag FanCoil_Flag_Busy;
extern typBusy_Diag SelfLearning_Flag_Busy;
extern typBusy_Diag CleanFuelLOW_Bank_Flag_Busy;
extern typBusy_Diag CleanFuelHIGHBank_Flag_Busy;
extern typBusy_Diag EraseStressCond_Flag_Busy;

extern typBusy_Diag ParamAutoAdatt_Busy;
extern typBusy_Diag Stater_Flag_Busy;
extern typBusy_Diag SelfExhValv_Flag_Busy;
extern typBusy_Diag DCMotor_Flag_Busy;
extern typBusy_Diag LowBeam_Flag_Busy;
extern typBusy_Diag DRL_Flag_Busy;
extern typBusy_Diag TSSValve_Flag_Busy;
extern typBusy_Diag HLambda_Flag_Busy;
extern typBusy_Diag HLambda2_Flag_Busy;
extern typBusy_Diag ExhaustValv_Flag_Busy;

extern typBusy_Diag RearPosPlateLight_Flag_Busy;
extern typBusy_Diag EraseHours_Flag_Busy;
extern typBusy_Diag ExhValvRst_Flag_Busy;
extern typBusy_Diag EraseLampOnTime_Flag_Busy;
extern typBusy_Diag BAM_Flag_Busy;
extern typBusy_Diag VehConfigReset_Flag_Busy;

extern t_DutyCycle DCLamHActiveDiag;
extern t_DutyCycle DCLamHActiveDiag2;
extern t_Period    PLamHActiveDiag;
extern t_Period    PLamHActiveDiag2;
extern uint8_T FlgLamHActiveDiag;
extern uint8_T FlgLamHActiveDiag2;

/* IOLI 0x31 - MIL */
extern uint8_T MILCmd_DIAG;
extern uint8_T MILCmd_store;
extern typBusy_Diag MILCmd_Flag_Busy;


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern const int16_t   TCLEANFUEL;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ActiveDiagMgm_T100ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ActiveDiagMgm_T100ms(void);

/*--------------------------------------------------------------------------*
 * ActiveDiagMgm_T5ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ActiveDiagMgm_T5ms(void);

/*--------------------------------------------------------------------------*
 * cmdIOCleanFuel_LOW_Bank - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOCleanFuel_LOW_Bank(void);

/*--------------------------------------------------------------------------*
 * cmdIOCleanFuel_HIGH_Bank - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOCleanFuel_HIGH_Bank(void);

/*--------------------------------------------------------------------------*
 * cmdIOFanCoil - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOFanCoil(void);

/*--------------------------------------------------------------------------*
 * cmdIOLoad - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOLoad(void);

/*--------------------------------------------------------------------------*
 * cmdIOSelfLearning - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOSelfLearning(void);

/*--------------------------------------------------------------------------*
 * cmdIOMIL - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOMIL(void);

/*--------------------------------------------------------------------------*
 * CLEANLow_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CLEANLow_Active_Diag (void);

/*--------------------------------------------------------------------------*
 * CLEANHigh_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CLEANHigh_Active_Diag (void);

/*--------------------------------------------------------------------------*
 * cmdIOInj0_Low - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj0_Low(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj1_Low - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj1_Low(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj2_Low - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj2_Low(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj3_Low - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj3_Low(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj0_High - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj0_High(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj1_High - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj1_High(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj2_High - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj2_High(void);

/*--------------------------------------------------------------------------*
 * cmdIOInj3_High - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOInj3_High(void);

/*--------------------------------------------------------------------------*
 * cmdIOIgn_0 - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOIgn_0(void);

/*--------------------------------------------------------------------------*
 * cmdIOIgn_1 - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOIgn_1(void);

/*--------------------------------------------------------------------------*
 * cmdIOIgn_2 - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOIgn_2(void);

/*--------------------------------------------------------------------------*
 * cmdIOIgn_3 - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOIgn_3(void);

/*--------------------------------------------------------------------------*
 * cmdIOStarterRelay - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOStarterRelay(void);

/*--------------------------------------------------------------------------*
 * cmdIORearPosPlateLight - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIORearPosPlateLight(void);

/*--------------------------------------------------------------------------*
 * cmdIOLowBeamRelay - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOLowBeamRelay(void);

/*--------------------------------------------------------------------------*
 * cmdIODRLRelay - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIODRLRelay(void);

/*--------------------------------------------------------------------------*
 * cmdIOPurgeCanister - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOPurgeCanister(void);

/*--------------------------------------------------------------------------*
 * cmdIOSelfExhaustValve - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOSelfExhaustValve(void);

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIODCMotor(void);

/*--------------------------------------------------------------------------*
 * cmdBAM - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdBAM(void);

/*--------------------------------------------------------------------------*
 * cmdVehConfigReset - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdVehConfigReset(void);


/*--------------------------------------------------------------------------*
 * cmdIOExhaustValv - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOExhaustValv(void);

/*--------------------------------------------------------------------------*
 * cmdIOresetParamAutoAdatt - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOresetParamAutoAdatt(void);

/*--------------------------------------------------------------------------*
 * cmdIOHLambda - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOHLambda(void);

 /*--------------------------------------------------------------------------*
  * cmdIOHLambda - Function description
  *
  * Arguments:
  * None
  *
  * Returned value:
  * None
  *
  * Usage notes:
  * None
  *--------------------------------------------------------------------------*/
 void cmdIOHLambda2(void);

/*--------------------------------------------------------------------------*
 * cmdEraseStressCond - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdEraseStressCond(void);

/*--------------------------------------------------------------------------*
 * cmdEraseLampOnTime - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdEraseLampOnTime(void);

/*--------------------------------------------------------------------------*
 * cmdEraseHours - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdEraseHours(void);

/*--------------------------------------------------------------------------*
 * cmdExhValveRst - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdExhValveRst(void);


/*--------------------------------------------------------------------------*
 * cmdBAM - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Inj_Active_Diag (t_INJ_CHANNELS chan,
                       diagIO_INJStruct * InjUsed);

/*--------------------------------------------------------------------------*
 * Ign_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Ign_Active_Diag (t_INJ_CHANNELS chan, 
                       diagIO_IGNStruct * IgnUsed,
                       int8_t coil);

/*--------------------------------------------------------------------------*
 * Fan_Coil_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Fan_Coil_Active_Diag (void);

/*--------------------------------------------------------------------------*
 * SelfExhaustValve_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void SelfExhaustValve_Active_Diag(void);

/*--------------------------------------------------------------------------*
 * DCMotor_Actve_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void DCMotor_Actve_Diag(void);

/*--------------------------------------------------------------------------*
 * ExhaustValv_Actve_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ExhaustValv_Actve_Diag(void);

/*--------------------------------------------------------------------------*
 * LowBeam_Relay_Actve_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void LowBeam_Relay_Actve_Diag(void);

/*--------------------------------------------------------------------------*
 * TSSValve_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void TSSValve_Active_Diag(void) ;

/*--------------------------------------------------------------------------*
 * Load_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Load_Active_Diag(void) ;

/*--------------------------------------------------------------------------*
 * SelfLearning_Active_Diag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void SelfLearning_Active_Diag(void);

/*--------------------------------------------------------------------------*
 * Reset_DIAG - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Reset_DIAG(uint8_t diag);

/*--------------------------------------------------------------------------*
 * Reset_All_DIAG - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Reset_All_DIAG(void);

/*--------------------------------------------------------------------------*
 * Reset_All_DIAG_EngRun - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Reset_All_DIAG_EngRun(void);

/*--------------------------------------------------------------------------*
 * Init_ActiveDiag - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Init_ActiveDiag(void);

/*--------------------------------------------------------------------------*
 * Get_ActveDiag_Enable_Condition - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 typBusy_Diag Get_ActveDiag_Enable_Condition(void);




#endif /* __ACTIVE_DIAG_H__ */
