/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV7/tree/DD/HEATGRIPDRIVEMGM/Hea#$   */
/* $ Description:                                                                                                */
/* $Revision:: 13841  $                                                                                          */
/* $Date:: 2022-02-21 11:01:27 +0100 (lun, 21 feb 2022)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              TransportLock.c
 *
 * Author(s): Lana L.
 *
 *
 * Implementation notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "TransportLock.h"

#ifdef _BUILD_TRANSPORT_LOCK_

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint8_T TempUnlockCntDown;
uint8_T TransportLockEcu;
uint8_T TransportLockAck;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
CCPTEST uint8_T StTransportLock;

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * TransportLock_Init - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void TransportLock_Init (void)
{
    StTransportLock = TransportLockEE;
    TransportLockEcu = TransportLockEE;
    TransportLockAck = TRS_LOCK_NOT;
}

/*--------------------------------------------------------------------------*
 * TransportLock_T100m - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void TransportLock_T100m (void)
{
    uint32_T tmpWorker;

    if (TRSLOCKTIME == 0u)
    {
        TransportLockEE = 1u;
        TransportLockEcu = 1u;
        TransportLockAck = TRS_LOCK_NOT;
    }
    else
    {   
        if (StTransportLock == 0u) /* LOCK */
        {
            if (TransportLockEcu == 2u) /* TO -> TEMPORARY UNLOCK */
            {
                StTransportLock = 2u;
                TransportLockEE = 2u;
                TransportLockAck = TRS_LOCK_LOCK;
                TempUnlockCntDown = TRSLOCKTIME;
                TempUnlockCntDownEE = (TRSLOCKTIME * 600u);
            }
            else if (TransportLockEcu == 1u) /* TO -> UNLOCK */
            {
                StTransportLock = 1u;
                TransportLockEE = 1u;
                TransportLockAck = TRS_LOCK_LOCK;
                TempUnlockCntDown = 0u;
                TempUnlockCntDownEE = (0u * 600u);
            }
            else /* LOCK */
            {
                StTransportLock = 0u;
                TransportLockEE = 0u;
                TransportLockAck = (TRS_LOCK_UNLOCK | TRS_LOCK_T_UNLOCK);
                TempUnlockCntDown = TRSLOCKTIME;
                TempUnlockCntDownEE = (TRSLOCKTIME * 600u);
            }
        }
        else if (StTransportLock == 2u) /* TEMPORARY UNLOCK */
        {
            if (TransportLockEcu == 2u) /* TEMPORARY UNLOCK */
            {
                if (TempUnlockCntDownEE == 0u) /* TO -> LOCK */
                {
                    if (Rpm == 0u)
                    {
                        if (KeySignal == 0u)
                        {
                            StTransportLock = 0u;
                            TransportLockEcu = 0u;
                            TempUnlockCntDown = TRSLOCKTIME;
                            TempUnlockCntDownEE = (TRSLOCKTIME * 600u);
                        }
                        else { /* MISRA */ }
                        TransportLockEE = 0u;
                    }
                    else { /* MISRA */ }
                }
                else
                {
                    TempUnlockCntDownEE--;
                    tmpWorker = (TempUnlockCntDownEE >> 3);
                    TempUnlockCntDown = tmpWorker / 75u;
                }
                if (Rpm == 0u)
                {
                    TransportLockAck = TRS_LOCK_LOCK;
                }
                else
                {
                    TransportLockAck = TRS_LOCK_NOT;
                }
            }
            else if (TransportLockEcu == 1u) /* UNLOCK ERROR */
            {
                StTransportLock = 2u;
                TransportLockEcu = 2u;
                TransportLockEE = 2u;
                TransportLockAck = TRS_LOCK_LOCK;
            }
            else /* TO -> LOCK */
            {
                StTransportLock = 0u;
                TransportLockEE = 0u;
                TransportLockAck = (TRS_LOCK_UNLOCK | TRS_LOCK_T_UNLOCK);
                TempUnlockCntDown = TRSLOCKTIME;
                TempUnlockCntDownEE = (TRSLOCKTIME * 600u);
            }
        }
        else /* UNLOCK */
        {
            if (TransportLockEcu == 1u) /* UNLOCK */
            {
                StTransportLock = 1u;
                TransportLockEcu = 1u;
                TransportLockEE = 1u;
                if (Rpm == 0u)
                {
                    TransportLockAck = TRS_LOCK_LOCK;
                }
                else
                {
                    TransportLockAck = TRS_LOCK_NOT;
                }
            }
            else if (TransportLockEcu == 2u) /* UNLOCK ERROR */
            {
                StTransportLock = 1u;
                TransportLockEcu = 1u;
                TransportLockEE = 1u;
                TransportLockAck = TRS_LOCK_LOCK;
            }
            else /* TO -> LOCK */
            {
                StTransportLock = 0u;
                TransportLockEE = 0u;
                TransportLockAck = (TRS_LOCK_UNLOCK | TRS_LOCK_T_UNLOCK);
                TempUnlockCntDown = TRSLOCKTIME;
                TempUnlockCntDownEE = (TRSLOCKTIME * 600u);
            }
        }
    }
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/

#else

/* None */

#endif

/****************************************************************************
 ****************************************************************************/

