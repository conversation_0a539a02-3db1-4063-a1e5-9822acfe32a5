/*
 * File: TrqDrivMgm.h
 *
 * Code generated for Simulink model 'TrqDrivMgm'.
 *
 * Model version                  : 1.2254
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Mon May 07 08:46:29 2018
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA-C:2004 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (13), Warnings (4), Error (0)
 */

#ifndef RTW_HEADER_TrqDrivMgm_h_
#define RTW_HEADER_TrqDrivMgm_h_
#ifndef TrqDrivMgm_COMMON_INCLUDES_
# define TrqDrivMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* TrqDrivMgm_COMMON_INCLUDES_ */

#include "TrqDrivMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "trq_drivmgm.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

/* Exported data define */
#define DRIVECHANGE                    ((uint8_T) 4U)
#define DRIVEDOWN                      ((uint8_T) 3U)
#define DRIVESTAB                      ((uint8_T) 1U)
#define DRIVEUP                        ((uint8_T) 2U)
#define INIT_ST                        ((uint8_T) 0U)
#define PBYACTIVE                      ((uint8_T) 5U)

/* Block signals (auto storage) */
typedef struct {
  int16_T Switch2;                     /* '<S11>/Switch2' */
  int16_T Conversion;                  /* '<S13>/Conversion' */
  int16_T Sub;                         /* '<S6>/Sub' */
  int16_T Conversion_n;                /* '<S12>/Conversion' */
  uint8_T DataStoreRead2;              /* '<S8>/Data Store Read2' */
  uint8_T Add;                         /* '<S8>/Add' */
  uint8_T FlgCmeFilt_h;                /* '<S2>/FiltState' */
  uint8_T FlgFromPby_o;                /* '<S2>/FiltState' */
  uint8_T StTrqDriv_l;                 /* '<S2>/FiltState' */
} BlockIO_TrqDrivMgm;

/* Block states (auto storage) for system '<Root>' */
typedef struct {
  uint32_T lastCntAbsTdc;              /* '<S2>/FiltState' */
  struct {
    uint_T is_ST_TRQ_DRIV:2;           /* '<S2>/FiltState' */
    uint_T is_EngineRunning:2;         /* '<S2>/FiltState' */
    uint_T is_NoPassBy:2;              /* '<S2>/FiltState' */
    uint_T is_DRIVE:2;                 /* '<S2>/FiltState' */
    uint_T is_active_c2_TrqDrivMgm:1;  /* '<S2>/FiltState' */
    uint_T is_active_UPDATE_OLD_VAR:1; /* '<S2>/FiltState' */
    uint_T is_active_ST_TRQ_DRIV:1;    /* '<S2>/FiltState' */
  } bitsForTID0;

  uint8_T lastlocalMode;               /* '<S1>/Data Store Memory1' */
  uint8_T localMode;                   /* '<S1>/Data Store Memory2' */
} D_Work_TrqDrivMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_TrqDrivMgm_Init_Trig_ZCE[2];/* '<S1>/fc_TrqDrivMgm_Init' */
  ZCSigState fc_TrqDrivMgm_Calc_Trig_ZCE;/* '<S1>/fc_TrqDrivMgm_Calc' */
} PrevZCSigStates_TrqDrivMgm;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
} ExternalInputs_TrqDrivMgm;

/* Block signals (auto storage) */
extern BlockIO_TrqDrivMgm TrqDrivMgm_B;

/* Block states (auto storage) */
extern D_Work_TrqDrivMgm TrqDrivMgm_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_TrqDrivMgm TrqDrivMgm_U;

/* Model entry point functions */
extern void TrqDrivMgm_initialize(void);
extern void TrqDrivMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm')    - opens subsystem TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm
 * hilite_system('TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm'
 * '<S1>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm'
 * '<S2>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc'
 * '<S3>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Init'
 * '<S4>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/fc_TrqDrivMgm_Calc'
 * '<S5>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/fc_TrqDrivMgm_Init'
 * '<S6>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/CmeDriver_Split'
 * '<S7>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/FiltState'
 * '<S8>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/localMode_Calc'
 * '<S9>'   : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/CmeDriver_Split/GenAbs'
 * '<S10>'  : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/CmeDriver_Split/GenAbs1'
 * '<S11>'  : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/CmeDriver_Split/Saturation Dynamic'
 * '<S12>'  : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/CmeDriver_Split/GenAbs/Data Type Conversion Inherited'
 * '<S13>'  : 'TrqDrivMgm_fxp/TrqDrivMgm/TrqDrivMgm/TrqDrivMgm/Calc/CmeDriver_Split/GenAbs1/Data Type Conversion Inherited'
 */

/*-
 * Requirements for '<Root>': TrqDrivMgm
 */
#endif                                 /* RTW_HEADER_TrqDrivMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
