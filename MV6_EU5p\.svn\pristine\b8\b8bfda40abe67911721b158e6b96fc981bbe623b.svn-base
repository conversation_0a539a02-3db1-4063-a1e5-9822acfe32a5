#ifndef     _MPC5554_CONFIG_H_
#define     _MPC5554_CONFIG_H_

/* 
   Solinas 06/05/2005 The define FSYS_80 should be set as defined in the prefixRAM.inc or in 
   prefixFlash.inc  depends on the target. 
   1) Indicate that the processor frequency is 80 Mhz
   0) indicate that the processor frequenct is 128 Mhz 
 */
   
#if (FSYS_60==1)
  #define FSYS 60
  #define ONE_OVER_FSYS (0x11111111U)   /* scaling 2^38 */
#elif (FSYS_80==1)
  #define FSYS 80
  #define ONE_OVER_FSYS (0xcccccccdU)   /* scaling 2^38 */
#else   
  #define FSYS 128
  #define ONE_OVER_FSYS (0x80000000U)   /* scaling 2^38 */
#endif

/****************************************************************************
     Peripherals defines 
 ****************************************************************************/
#ifdef  _BUILD_ADC_
#include "ADC.cfg"
#endif /* _BUILD_ADC_ */
#ifdef  _BUILD_CAN_
#include "CAN.cfg"
#endif /* _BUILD_CAN_ */
#ifdef  _BUILD_DIGIO_
#include "DIGIO.cfg"
#endif /* _BUILD_DIGIO_ */
#ifdef  _BUILD_DMA_
#include "DMA.cfg"
#endif /* _BUILD_DMA_ */
#ifdef  _BUILD_EXTIRQ_
#include "EXTIRQ.cfg"
#endif /* _BUILD_EXTIRQ_ */
#ifdef  _BUILD_FLASH_
#include "FLASH.cfg"
#endif /* _BUILD_FLASH_ */
#ifdef  _BUILD_MATHLIB_
#include "MATHLIB.cfg"
#endif /* _BUILD_MATHLIB_ */
#ifdef  _BUILD_PIO_
#include "PIO.cfg"
#endif /* _BUILD_PIO_ */
#ifdef  _BUILD_SCI_
#include "SCI.cfg"
#endif /* _BUILD_SCI_ */
#ifdef  _BUILD_SPI_
#include "SPI.cfg"
#endif /* _BUILD_SPI_ */
#ifdef  _BUILD_SYNC_
#include "SYNC.cfg"
#endif /* _BUILD_SYNC_ */
#ifdef  _BUILD_SYS_
#include "SYS.cfg"
#endif /* _BUILD_SYS_ */
#ifdef  _BUILD_TASK_
#include "TASK.cfg"
#endif /* _BUILD_TASK_ */
#ifdef  _BUILD_TIMING_
#include "TIMING.cfg"
#endif /* _BUILD_TIMING_ */

#ifdef  _BUILD_UART_
#include "UART.cfg"
#endif /* _BUILD_UART_ */

#ifdef  _BUILD_WDT_
#include "WDT.cfg"
#endif  /* _BUILD_WTDG_ */

#ifdef  _BUILD_INJ_
#include "INJ.cfg"
#endif /* _BUILD_INJ_ */

#ifdef  _BUILD_IGN_
#include "IGN.cfg"
#endif /* _BUILD_INJ_ */

#ifdef  _BUILD_PHASE_
#include "PHASE.cfg"
#endif /* _BUILD_PHASE_ */

/****************************************************************************
     Common errors defines 
 ****************************************************************************/
#define NO_ERROR                           0
#define PERIPHERAL_NOT_CONFIGURED         -1
#define PERIPHERAL_ALREADY_CONFIGURED     -2
#define PERIPHERAL_NOT_INITIALIZED        -3
#define PERIPHERAL_ALREADY_INITIALIZED    -4

#define PERIPHERAL_NOT_PRESENT            -5

#define PERIPHERAL_NOT_RESET              -6
#define PERIPHERAL_FAILURE                -256


#endif
