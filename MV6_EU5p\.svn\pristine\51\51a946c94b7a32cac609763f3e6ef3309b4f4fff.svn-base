/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  _BUILD_SYS_

#include "sync.h"
#include "timing.h"
#include "OS_api.h"
#include "digio.h"
#include "digitalin.h"

#include "ETPU_VrsDefs.h"
#include "etpu_util.h"    /* prototypes and useful defines */
#include "..\include\ETPU_HostInterface.h"
#include "..\common\ETPU_Shared.h"
#include "..\auto\etpu_angle_clock_auto.h"
#include "..\auto\etpu_angle_clock_func_auto.h"

#if (ENGINE_TYPE == PI_500_1C_4V) 
#include "..\auto\etpu_piaggio_generated_image.c"
#elif (ENGINE_TYPE == FE_6300_12C_48V) 
#include "..\auto\etpu_ferrari_generated_image.c"
#elif (ENGINE_TYPE == FE_4300_8C_32V)
#include "..\auto\etpu_ferrari_v8_generated_image.c"
#elif (ENGINE_TYPE == FE_4300_8C_32V_TDN)
#include "..\auto\etpu_ferrari_v8_tdn_generated_image.c"

#elif (ENGINE_TYPE == FE_4300_8C_32V_GT2)
#include "..\auto\etpu_ferrari_v8_gt2_generated_image.c"    
#elif (ENGINE_TYPE == FE_151_12C) || (ENGINE_TYPE == LA_L539_12C)
#include "..\auto\etpu_ferrari_v12_f151_generated_image.c"    
#elif (ENGINE_TYPE == FE_599_12C_TDN)
#include "..\auto\etpu_ferrari_v12_599_tdn_generated_image.c"
#elif (ENGINE_TYPE == FE_142_8C) || (ENGINE_TYPE == FE_A1GP_8C)
#include "..\auto\etpu_ferrari_v8_f142_generated_image.c"    

#elif (ENGINE_TYPE == YP_250_G) 
#include "..\auto\etpu_yamaha_generated_image.c"
#elif (ENGINE_TYPE == MA_MC12_12C) 
#include "..\auto\etpu_maserati_generated_image.c"
#elif (ENGINE_TYPE == VW_1400_4C_16V)
#include "..\auto\etpu_vw_generated_image.c"
#elif (ENGINE_TYPE == EM_VW_1400_4C_16V)
#include "..\auto\etpu_em_4cyl_generated_image.c"
#elif (ENGINE_TYPE == MV_AGUSTA_4C)
#include "..\auto\etpu_em_4cyl_generated_image.c"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
#include "..\auto\etpu_em_3cyl_generated_image.c"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
#include "..\auto\etpu_em_3cyl_new_generated_image.c"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
#include "..\auto\etpu_em_3cyl_36_2_generated_image.c"
#elif (ENGINE_TYPE == MV_AGUSTA_4C_TDC_0_9)
#include "..\auto\etpu_em_4cyl_36_2_generated_image.c"
#elif (ENGINE_TYPE == PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID)
#include "..\auto\etpu_piaggio_generated_image.c"
#else
#error WRONG CONFIGURATION!!!
#endif

#include "..\auto\etpu_angle_clock_func_auto.c"
#include "..\auto\etpu_spark_handler_auto.h"
#include "..\auto\etpu_PWM_auto.h"
#include "..\auto\etpu_PWM_IN_auto.h"
#include "..\auto\etpu_IONTRIGGER_auto.h"
#include "..\auto\etpu_ADCTRIGGER_auto.h"
#include "..\auto\etpu_DELAY_auto.h"
#include "..\auto\etpu_FastLinkedChan_IN_auto.h"
#include "..\auto\etpu_FastLinkedChan_OUT_auto.h"
#include "..\auto\etpu_FastLinkedChan_DIR_auto.h"
#include "..\auto\etpu_ANGTRIGGER_auto.h"

#ifdef _BUILD_PHASE_
#include "phase.h"
#endif

#include "inj.h"
#include "ign.h"
#include "sys.h"

#undef _TEST_TPU_EX_
#undef _TEST_TRIGGER_EX_
#undef _TEST_ETPU1_EX_
#undef _TEST_ETPU2_EX_
#undef _TEST_ETPU3_EX_
#undef _TEST_ETPU4_EX_
#undef _TEST_ETPU5_EX_

#define HYST_ENABLE 1
#define HYST_DISABLE 0
TaskType _ETPU_A_vect_user_isr[ETPU_NUM_OF_CHANNELS] = { 0xff };

#if (USE_ETPU_B == 1)
TaskType _ETPU_B_vect_user_isr[ETPU_NUM_OF_CHANNELS] = { 0xff };
#endif

/* LOCAL SYMBOLS */
extern uint32_T TaskTimerISRVRSms;
extern uint32_T TaskTimerISRCh01eTPUms;
extern uint32_T TaskTimerISRCh02eTPUms;
extern uint32_T TaskTimerISRCh03eTPUms;
extern uint32_T TaskTimerISRCh04eTPUms;
extern uint32_T TaskTimerISRCh05eTPUms;
extern uint32_T TaskTimerISRCh06eTPUms;
extern uint32_T TaskTimerISRCh07eTPUms;
extern uint32_T TaskTimerISRCh08eTPUms;
extern uint32_T TaskTimerISRCh09eTPUms;
extern uint32_T TaskTimerISRCh10eTPUms;
extern uint32_T TaskTimerISRCh11eTPUms;
extern uint32_T TaskTimerISRCh12eTPUms;
extern uint32_T TaskTimerISRCh13eTPUms;
extern uint32_T TaskTimerISRCh14eTPUms;
extern uint32_T TaskTimerISRCh15eTPUms;
extern uint32_T TaskTimerISRCh16eTPUms;
extern uint32_T TaskTimerISRCh17eTPUms;
extern uint32_T TaskTimerISRCh18eTPUms;
extern uint32_T TaskTimerISRCh19eTPUms;
extern uint32_T TaskTimerISRCh20eTPUms;
extern uint32_T TaskTimerISRCh21eTPUms;
extern uint32_T TaskTimerISRCh22eTPUms;
extern uint32_T TaskTimerISRCh23eTPUms;
extern uint32_T TaskTimerISRCh24eTPUms;
extern uint32_T TaskTimerISRCh25eTPUms;
extern uint32_T TaskTimerISRCh26eTPUms;

extern uint16_T EtpuACh0Int;
extern uint8_T  CntTaskVRS;
extern uint8_T  CntTaskeTPUCh01;
extern uint8_T  CntTaskeTPUCh02;
extern uint8_T  CntTaskeTPUCh03;
extern uint8_T  CntTaskeTPUCh04;
extern uint8_T  CntTaskeTPUCh05;
extern uint8_T  CntTaskeTPUCh06;
extern uint8_T  CntTaskeTPUCh07;
extern uint8_T  CntTaskeTPUCh08;
extern uint8_T  CntTaskeTPUCh09;
extern uint8_T  CntTaskeTPUCh10;
extern uint8_T  CntTaskeTPUCh11;
extern uint8_T  CntTaskeTPUCh12;
extern uint8_T  CntTaskeTPUCh13;
extern uint8_T  CntTaskeTPUCh14;
extern uint8_T  CntTaskeTPUCh15;
extern uint8_T  CntTaskeTPUCh16;
extern uint8_T  CntTaskeTPUCh17;
extern uint8_T  CntTaskeTPUCh18;
extern uint8_T  CntTaskeTPUCh19;
extern uint8_T  CntTaskeTPUCh20;
extern uint8_T  CntTaskeTPUCh21;
extern uint8_T  CntTaskeTPUCh22;
extern uint8_T  CntTaskeTPUCh23;
extern uint8_T  CntTaskeTPUCh24;
extern uint8_T  CntTaskeTPUCh25;
extern uint8_T  CntTaskeTPUCh26;

/* END OF LOCAL SYMBOLS */

void  ETPU_InCfg(uint8_T pid, uint8_T hyst_ena, uint8_T pull_type )
{
    /* configure pin pid as an output pin driven by ETPU */
    SIU.PCR[pid].B.PA   = 3;
    SIU.PCR[pid].B.IBE  = 1;
    SIU.PCR[pid].B.OBE  = 0;
    SIU.PCR[pid].B.HYS  = hyst_ena;
    SIU.PCR[pid].B.WPE = (pull_type & 0x02) >> 1;
    SIU.PCR[pid].B.WPS = (pull_type & 0x01);
    
}   /* end ETPU_InCfg(.) */


void ETPU_OutCfgExt(uint8_T pid, uint8_T openDrnEna, uint8_T inBufEna, uint8_t slewRate)
{
    /* configure pin pid as an output pin driven by ETPU */
    SIU.PCR[pid].B.PA   = 3;
    SIU.PCR[pid].B.IBE  = inBufEna;
    SIU.PCR[pid].B.OBE  = 1;
    SIU.PCR[pid].B.SRC  = slewRate;    /* slew rate */
    SIU.PCR[pid].B.ODE  = openDrnEna;     /* open drain */
}

/******************************************************************************
FUNCTION     : InitializeSIU_for_eTPU
PURPOSE      : Initialize SIU for eTPU requirements
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void ETPU_InitializeSIU_for_eTPU(void)
{
   /* SIU_PCR113: PA=3,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */
#ifdef CTRCLK_CHANNEL_PIN
    ETPU_InCfg(CTRCLK_CHANNEL_PIN, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IPE_CamSens
#ifndef IDN_BUTTONMAP
    ETPU_InCfg(IPE_CamSens, HYST_ENABLE, DISABLE_PULL);
#endif
#endif

#ifdef IPE_SpdCam
    ETPU_InCfg(IPE_SpdCam, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IonEvnA
    ETPU_InCfg(IN_IonEvnA, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_SpkEvnA
    ETPU_InCfg(IN_SpkEvnA, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IonEvnB
    ETPU_InCfg(IN_IonEvnB, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_SpkEvnB
    ETPU_InCfg(IN_SpkEvnB, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_1
    ETPU_InCfg(IN_IgnCmd_1, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_2
    ETPU_InCfg(IN_IgnCmd_2, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_3
    ETPU_InCfg(IN_IgnCmd_3, HYST_ENABLE, DISABLE_PULL);
#endif  

#ifdef IN_IgnCmd_4
    ETPU_InCfg(IN_IgnCmd_4, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_5
    ETPU_InCfg(IN_IgnCmd_5, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_6
    ETPU_InCfg(IN_IgnCmd_6, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_7
    ETPU_InCfg(IN_IgnCmd_7, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_IgnCmd_8
    ETPU_InCfg(IN_IgnCmd_8, HYST_ENABLE, DISABLE_PULL);
#endif 
 
#ifdef IN_Humidity_1
    ETPU_InCfg(IN_Humidity_1, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef IN_Humidity_2 
    ETPU_InCfg(IN_Humidity_2, HYST_ENABLE, DISABLE_PULL);
#endif

#ifdef _BUILD_INJ_
  #ifdef OUT_InjDrv_1
    ETPU_OutCfgExt(OUT_InjDrv_1, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif  

  #ifdef OUT_InjDrv_2
    ETPU_OutCfgExt(OUT_InjDrv_2, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif  

  #ifdef OUT_InjDrv_3
    ETPU_OutCfgExt(OUT_InjDrv_3, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif  

  #ifdef OUT_InjDrv_4
    ETPU_OutCfgExt(OUT_InjDrv_4, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif  

  #ifdef OUT_InjDrv_5
    SYS_OutPinConfig(OUT_InjDrv_5, INJDRV_5_ETPU_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MAXIMUM_SLEW_RATE);
  #endif  

  #ifdef OUT_InjDrv_6
    SYS_OutPinConfig(OUT_InjDrv_6, INJDRV_6_ETPU_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MAXIMUM_SLEW_RATE);
  #endif

  #ifdef OUT_InjDrv_7
    SYS_OutPinConfig(OUT_InjDrv_7, INJDRV_7_ETPU_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MAXIMUM_SLEW_RATE);
  #endif
  
  #ifdef OUT_InjDrv_8
    SYS_OutPinConfig(OUT_InjDrv_8, INJDRV_8_ETPU_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MAXIMUM_SLEW_RATE);
  #endif
#endif

#ifdef _BUILD_IGN_
  #ifdef OUT_IgnDrv_1
    ETPU_OutCfgExt(OUT_IgnDrv_1, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif

  #ifdef  OUT_IgnDrv_2
    ETPU_OutCfgExt(OUT_IgnDrv_2, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif

  #ifdef  OUT_IgnDrv_3
    ETPU_OutCfgExt(OUT_IgnDrv_3, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif

  #ifdef OUT_IgnDrv_4
    ETPU_OutCfgExt(OUT_IgnDrv_4, OUT_PUSHPULL, EN_INPUT_BUF, MAX_SLEWRATE);
  #endif
#endif

#ifdef IDN_TACH_CONS
    ETPU_OutCfgExt(IDN_TACH_CONS, OUT_PUSHPULL, EN_INPUT_BUF, MIN_SLEWRATE);
#endif /* IDN_TACH_CONS */

#ifdef _TEST_ETPU1_EX_
    DIGIO_OutCfgExt(OUT_D1, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
#ifdef _TEST_ETPU2_EX_
    DIGIO_OutCfgExt(OUT_D2, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
#ifdef _TEST_ETPU3_EX_
    DIGIO_OutCfgExt(OUT_D1, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
#ifdef _TEST_ETPU4_EX_
    DIGIO_OutCfgExt(OUT_D2, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
#ifdef _TEST_ETPU5_EX_
    DIGIO_OutCfgExt(OUT_D2, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
#ifdef _TEST_TPU_EX_
    DIGIO_OutCfgExt(IN_IgnCmd_7, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
#ifdef _TEST_TRIGGER_EX_
    DIGIO_OutCfgExt(IN_IgnCmd_7, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif 
}


/*
  Channel specific setup configuration
*/




/*************************************************************************/
int16_t ETPU_EnableInterruptUC(uint16_t channel)
{
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif /* CHECK_BIOS_FAULTS */
    if (returnCode == NO_ERROR)
    {
        ETPU.CHAN[channel].CR.B.CIE = 1;
    }

    return returnCode;
}
/*************************************************************************/
int16_t ETPU_DisableInterruptUC(uint16_t channel)
{
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif    
    if (returnCode == NO_ERROR)
    {
        ETPU.CHAN[channel].CR.B.CIE = 0;
    }
    return returnCode;
}

/*************************************************************************/
int16_t ETPU_SetInterruptHandlerUC(uint16_t channel, TaskType taskID)
{
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      returnCode = PERIPHERAL_FAILURE;
  }
#endif
  if (returnCode == NO_ERROR)
  {
      if(channel < ETPU_NUM_OF_CHANNELS+1)
      {
        _ETPU_A_vect_user_isr[channel] = taskID;
      }
#if (USE_ETPU_B == 1)
      else
      {
        channel -= 64;
        _ETPU_B_vect_user_isr[channel] = taskID;
      }
#endif
  }
  return returnCode;                                         
}

int16_t ETPU_PIN_Config(uint16_t channel, 
                        t_EdgeSelection edgesel, t_EdgePolarity edgepol, 
                        uint16_t priority, uint16_t countMode, 
                        uint16_t tableSelect, uint16_t funcNum)
{
   int16_t  returnCode = NO_ERROR;
   uint32_t ParameterBaseAddress;
   uint32_t matchingTransition;
   uint32_t polarityTmp;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       returnCode = ETPU_ChannelInitRam(channel, ETPU_SPARK_HANDLER_NUM_PARMS, &ParameterBaseAddress);

       if(returnCode == NO_ERROR) 
       {
           /* Initialize channel 0 to manage crank signal */
           returnCode = ETPU_ChannelInit(channel, priority, countMode, 
                     funcNum, tableSelect, ParameterBaseAddress );

           if(returnCode == NO_ERROR) 
           {
               if(edgepol==RISING_EDGE)
               { 
                  polarityTmp = read_sparkHandlerChanFlags(channel) | (POLARITY_MASK);
                  write_sparkHandlerChanFlags(polarityTmp, channel);
               } 
               else
               {
                  polarityTmp = read_sparkHandlerChanFlags(channel) & ~(POLARITY_MASK);
                  write_sparkHandlerChanFlags(polarityTmp, channel);
               }

               if (edgesel == BOTH_EDGE)
               {
                  matchingTransition = ( EX_SPARK_ON | EX_SPARK_OFF);
               }
               else
               if (edgesel == SINGLE_RISING_EDGE)
               {
                  matchingTransition = ((edgepol == RISING_EDGE)? EX_SPARK_ON: EX_SPARK_OFF);
               }
               else
               if (edgesel == SINGLE_FALLING_EDGE)
               {
                  matchingTransition = ((edgepol == RISING_EDGE)? EX_SPARK_OFF: EX_SPARK_ON);
               }
               else
               {
                  returnCode = PERIPHERAL_NOT_CONFIGURED;
               }
               
               if (returnCode == NO_ERROR)
               {
                   write_captureTransition(matchingTransition, (uint16_t)channel);
               }
           }
       }
   }
   return returnCode;                                         
}

int16_t ETPU_PIN_Enable(uint16_t channel)
{
   int32_t channelFlags;
   int16_t  returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif 
   if (returnCode == NO_ERROR)
   {
       channelFlags = read_sparkHandlerChanFlags(channel);

       write_sparkHandlerChanFlags(channelFlags | EXCEPTIONS_ENABLED, channel);
       returnCode = ETPU_set_hsr(channel,HSR_INIT_SPARK_HANDLER_VAL);                                         
   }
   return returnCode;                                         
}

int16_t ETPU_PIN_Disable(uint16_t channel)
{
   int16_t returnCode = NO_ERROR;
   int32_t channelFlags;
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       channelFlags = read_sparkHandlerChanFlags(channel);
       write_sparkHandlerChanFlags(channelFlags & ~EXCEPTIONS_ENABLED, channel);

   }
   return returnCode;                                         
}

int16_t ETPU_PIN_GetState(uint16_t channel, uint16_t *state)
{
   int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       *state = (read_sparkHandlerChanFlags(channel) & EXCEPTIONS_ENABLED)?1:0;
   }
   return returnCode;                                         
}

int16_t ETPU_PIN_GetLastTransition(uint16_t channel, t_EdgePolarity *transition) 
{
    int32_t sparkFlags;
    int32_t currentFlags;
    t_ActivePolarity channelPolarity;
    int16_t returnCode = NO_ERROR;
    
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        currentFlags = 0x00ffffff & read_sparkHandlerChanFlags((uint32_t)channel);
        sparkFlags = currentFlags & (EX_SPARK_ON | EX_SPARK_OFF);
        
        currentFlags = (currentFlags & (EXCEPTIONS_ENABLED|POLARITY_MASK)); 
        write_sparkHandlerChanFlags(currentFlags, channel);

        if (sparkFlags == (EX_SPARK_ON | EX_SPARK_OFF))
        {
             returnCode = SYS_SPARK_OVERFLOW;
        }
        else if(!sparkFlags)
        {
             returnCode = SYS_NO_SPARK;
        }
        else
        {
        }
        
        if (returnCode == NO_ERROR)
        {
            ETPU_PIN_GetChannelPolarity (channel, &channelPolarity);


            if(channelPolarity == POL_LOW)
            {
                if (sparkFlags == EX_SPARK_ON)
                {
                    *transition = FALLING_EDGE;
                }
                else
                {
                    *transition = RISING_EDGE;
                }
            }
            else
            {
                if (sparkFlags == EX_SPARK_ON)
                {
                    *transition = RISING_EDGE;
                }
                else
                {
                    *transition = FALLING_EDGE;
                }
            }
        }
    }
    return returnCode;

}

int16_t ETPU_PIN_GetPinLogicalLevel(uint16_t channel, t_ActiveEdge *pinLogicalLevel)
{
    
    t_ActivePolarity functionPolarity;
    uint32_t pinPhisicalLevel;
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        pinPhisicalLevel = ETPU.CHAN[channel].SCR.B.IPS;
            
        ETPU_PIN_GetChannelPolarity (channel, &functionPolarity);


        if(functionPolarity == POL_LOW)
        {
            if(pinPhisicalLevel) 
            {
                *pinLogicalLevel = ACTIVE_LOW;
            } 
            else
            {
                *pinLogicalLevel = ACTIVE_HIGH;
            } 
        }
        else
        {
            if(pinPhisicalLevel) 
            {
                *pinLogicalLevel = ACTIVE_HIGH;
            } 
            else
            {
                *pinLogicalLevel = ACTIVE_LOW;
            } 
        }
    }
    return returnCode;                                         
}

int16_t ETPU_PinGetLastEdgeTime(t_PIO_CHANNELS _channel, uint32_t *_time)
{
  int16_t returnCode = NO_ERROR;
  
#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      returnCode = PERIPHERAL_FAILURE;
  }
#endif
  if (returnCode == NO_ERROR)
  {
      *_time = 0x00ffffff & read_sparkCaptureTime(_channel); 
  }
  return returnCode;
}

int16_t ETPU_PinGetLastEdgeAngle(t_PIO_CHANNELS _channel, uint32_t *_angle)
{
  int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      returnCode = PERIPHERAL_FAILURE;
  }
#endif
  if (returnCode == NO_ERROR)
  {
      *_angle = 0x00ffffff & read_sparkCaptureAngle(_channel); 
      *_angle *= TICKS2DEGREE16;
  }
  return returnCode;
}

/**********************************************************************/
int16_t ETPU_PWM_OutConfig(uint16_t channel, t_EdgePolarity edgepol, uint8_t matchMode,
                        t_DutyCycle dutycicle, t_Period period, t_Period startAngle,
                        uint16_t priority, uint16_t countMode, 
                        uint16_t tableSelect, uint16_t funcNum, t_EdgeCount _edgecount)
{
   int16_t  returnCode = NO_ERROR;
   uint32_t ParameterBaseAddress;
   uint32_t polarityTmp;
   
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       /* if ((channel == (uint16_T)(ION_TRIGGER_A - ETPUA_UC0)) || (channel == (uint16_T)ION_TRIGGER_B -ETPUA_UC0)) */
       if ((channel == (uint16_T)(ION_TRIGGER_A - ETPUA_UC0)) )
       {
         returnCode = ETPU_ChannelInitRam(channel, IONTRIGGER_FUNC_NUM_PARMS, &ParameterBaseAddress);
         if(returnCode == NO_ERROR) 
         {
             /* Initialize channel 0 to manage crank signal */
             returnCode = ETPU_ChannelInit(channel, FS_ETPU_PRIORITY_HIGH, countMode, 
                       IONTRIGGER_FUNC_FUNCTION_NUMBER, tableSelect, ParameterBaseAddress );

             if(returnCode == NO_ERROR)
             {
                 write_ionTrPeriod (period, channel); 
             }
         }
       }
       else 
       if (    (channel == CRANKEXGEN_CHANNEL) 
            || (channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
            || (channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
            || (channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
         returnCode = ETPU_ChannelInitRam(channel, ADCTRIGGER_FUNC_NUM_PARMS, &ParameterBaseAddress);
         if(returnCode == NO_ERROR) 
         {
             /* Initialize channel 0 to manage crank signal */
             returnCode = ETPU_ChannelInit(channel, priority, countMode, 
                       ADCTRIGGER_FUNC_FUNCTION_NUMBER, tableSelect, ParameterBaseAddress );
             if(returnCode == NO_ERROR) 
             {
                write_adcTrPeriod (period, channel);
             }
         }
       }
       else
       {
         t_Period timeDutyCicle; // dutycicle expressed in usec

         returnCode = ETPU_ChannelInitRam(channel, PWM_FUNC_NUM_PARMS, &ParameterBaseAddress);
         if(returnCode == NO_ERROR) 
         {
             /* Initialize channel 0 to manage crank signal */
             returnCode = ETPU_ChannelInit(channel, priority, countMode, 
                       funcNum, tableSelect, ParameterBaseAddress );
             if(returnCode == NO_ERROR) 
             {
                 if(edgepol==RISING_EDGE)
                 { 
                    polarityTmp = read_pwmChanFlags(channel) | (POLARITY_MASK);
                    write_pwmChanFlags(polarityTmp, channel);
                 } 
                 else
                 {
                    polarityTmp = read_pwmChanFlags(channel) & ~(POLARITY_MASK);
                    write_pwmChanFlags(polarityTmp, channel);
                 }

                 // dutycicle is expressed in percentage format; 
                 //  converting dutycicle just before the write_dutyCycle(.. , ..) 
                 if(matchMode == PWM_MATCH_ANGLE)
                 {
                    period /= TCR2_TICK_PRESCALER;
                    startAngle /= TCR2_TICK_PRESCALER;
                    write_startingAngle(startAngle, channel);
                 }
                 timeDutyCicle = (((uint64_t)dutycicle * period / (50<<8)) +1 ) >> 1;

                 write_mode (matchMode, channel); 
                
                 write_dutyCycle (timeDutyCicle, channel);
                 write_period (period, channel); 
                 if(countMode == ETPU_COUNT_MODE_SINGLE) 
                 { 
                    write_numOfEdges(_edgecount, channel);
                    write_continuousMode(0, channel);
                 }
                 else
                 {
                     write_continuousMode(1, channel);     
                 }
             }
         }
       }
   }
   return returnCode;                                         
}


/* ---------------------------------------------------------------- */
int16_t ETPU_PWM_OutSetDutyCicle(t_PIO_CHANNELS channel, 
                              t_DutyCycle dutycicle, 
                              t_Period period)
{
   int16_t returnCode = NO_ERROR;
   t_Period timeDutyCicle; // dutycicle expressed in usec

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       /* if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) || (channel == (uint16_T)(ION_TRIGGER_B -ETPUA_UC0))) */
       if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) )
       {
       }
       else 
       if ((channel == CRANKEXGEN_CHANNEL) //26 
        ||(channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
       }
       else
       {
         timeDutyCicle = (((uint64_t)dutycicle * period / (50<<8)) +1 ) >> 1;
         write_dutyCycle (timeDutyCicle, channel);
       }
   }
   return returnCode;                                         
}

int16_t ETPU_PWM_OutSetEdgeCount(t_PIO_CHANNELS channel, 
                              t_EdgeCount edgecount)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
       returnCode = PERIPHERAL_FAILURE;
    }
#endif
   if (returnCode == NO_ERROR)
   {
       /* if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) || (channel == (uint16_T)(ION_TRIGGER_B -ETPUA_UC0))) */
       if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) )
       {
       }
       else 
       if ((channel == CRANKEXGEN_CHANNEL) 
        ||(channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
       }
       else
       {
         write_numOfEdges(edgecount, channel);
       }
   }
   return returnCode;                                         
}


/* ---------------------------------------------------------------- */

int16_t ETPU_PWM_OutSetPeriod(t_PIO_CHANNELS channel, 
                           t_Period period)
{   
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
        returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       /* if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) || (channel == (uint16_T)(ION_TRIGGER_B -ETPUA_UC0))) */
       if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) )
       {
         write_ionTrPeriod (period, channel);
       }
       else 
       if ((channel == CRANKEXGEN_CHANNEL) 
        ||(channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
         write_adcTrPeriod (period, channel);
       }
       else
       {
         uint32_t matchMode;
         uint32_t old_period;
         uint32_t timeDutyCycle;

         matchMode = read_mode(channel);   
         if(matchMode == PWM_MATCH_ANGLE)
         {
           period /= TCR2_TICK_PRESCALER;
         }
         timeDutyCycle = read_dutyCycle (channel);
         old_period = read_period (channel);
         timeDutyCycle = ((uint64_t)timeDutyCycle * period)/old_period;
         write_period (period, channel);
         write_dutyCycle (timeDutyCycle, channel);
       }
   }
   return returnCode;                                         
}

/* ---------------------------------------------------------------- */

int16_t ETPU_PWM_OutSetMatchMode(t_PIO_CHANNELS channel, 
                           uint8_t matchMode)
{   
   int16_t retValue = NO_ERROR;
   
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       retValue = PERIPHERAL_FAILURE;
   }
#endif
   if (retValue == NO_ERROR)
   {
       if ((channel == CRANKEXGEN_CHANNEL)
        ||(channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0))
        ||(channel == (uint16_T)(ANGULAR_TRIGGER - ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
         retValue = PWM_MATCHMODE_NOT_SUPPORTERD;
       }
       else
       {
         write_mode (matchMode, channel);
       }
   }
   return retValue;                                         
}

/* ---------------------------------------------------------------- */

int16_t ETPU_PWM_OutEnable(t_PIO_CHANNELS channel)
{
   int16_t returnCode = NO_ERROR;
   int32_t channelFlags;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       /* if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) || (channel == (uint16_T)(ION_TRIGGER_B -ETPUA_UC0))) */
       if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) )
       {
           returnCode = ETPU_set_hsr(channel,HSR_INIT_IONTRIGGER_VAL);
       }
       else 
       if ((channel == CRANKEXGEN_CHANNEL) 
        ||(channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
           returnCode = ETPU_set_hsr(channel,HSR_INIT_ADCTRIGGER_VAL);
       }
       else
       {
           channelFlags = read_pwmChanFlags(channel);
           write_pwmChanFlags(channelFlags | EXCEPTIONS_ENABLED, channel);
           returnCode = ETPU_set_hsr(channel,HSR_INIT_PWM_VAL);
       }
   }
   return returnCode;                                         
}

/* ---------------------------------------------------------------- */

int16_t ETPU_PWM_OutDisable(uint16_t channel)
{
   int16_t returnCode = NO_ERROR;
   int32_t channelFlags;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       /* if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) || (channel == (uint16_T)(ION_TRIGGER_B -ETPUA_UC0))) */
       if ((channel == (uint16_T)(ION_TRIGGER_A -ETPUA_UC0)) )
       {
           returnCode = ETPU_set_hsr(channel,HSR_DISABLE_IONTRIGGER_VAL);
       }
       else 
       if ((channel == CRANKEXGEN_CHANNEL) 
        ||(channel == (uint16_T)(TRIGGER_10_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_2_MILLISEC -ETPUA_UC0))
        ||(channel == (uint16_T)(TRIGGER_450_MICROSEC -ETPUA_UC0)))
       {
          returnCode = ETPU_set_hsr(channel,HSR_DISABLE_ADCTRIGGER_VAL);
       }
       else
       {
           returnCode = ETPU_set_hsr(channel,HSR_DISABLE_PWM_VAL);
           if (returnCode == NO_ERROR)
           {
               channelFlags = read_pwmChanFlags(channel);
               write_pwmChanFlags(channelFlags & ~EXCEPTIONS_ENABLED, channel);
           }
       }
   }
   return returnCode;                                         
}

/* ---------------------------------------------------------------- */

int16_t ETPU_PWM_OutGetState(uint16_t channel, t_State *state)
{
   int16_t returnCode = NO_ERROR;
   
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       *state = (read_pwmChanFlags(channel) & EXCEPTIONS_ENABLED)?(ENABLE):(DISABLE);
   }
   return returnCode;                                         
}

/**********************************************************************/




int16_t ETPU_PWM_InConfig(uint16_t channel, t_EdgePolarity edgepol, t_Period timeout,
                        uint16_t priority, uint16_t countMode, 
                        uint16_t tableSelect, uint16_t funcNum)
{
   uint32_t ParameterBaseAddress;
   uint32_t polarityTmp;
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
//   ParameterBaseAddress = ETPU_ChannelInitRam(channel, PWM_IN_FUNC_NUM_PARMS);
   if (returnCode == NO_ERROR)
   {
       returnCode = ETPU_ChannelInitRam(channel, PWM_IN_FUNC_NUM_PARMS, &ParameterBaseAddress );
       if(returnCode == NO_ERROR)
       {
           /* Initialize channel 0 to manage crank signal */
           returnCode = ETPU_ChannelInit(channel, priority, countMode, 
                     funcNum, tableSelect, ParameterBaseAddress );
           if(returnCode == NO_ERROR) 
           {
               if(edgepol==RISING_EDGE)
               { 
                  polarityTmp = read_pwm_in_ChanFlags(channel) | (POLARITY_MASK);
                  write_pwm_in_ChanFlags(polarityTmp, channel);
               } 
               else
               {
                  polarityTmp = read_pwm_in_ChanFlags(channel) & ~(POLARITY_MASK);
                  write_pwm_in_ChanFlags(polarityTmp, channel);
               }
               
               write_pwm_in_Timeout(timeout, channel);
           }
       }
   }   
   return returnCode;
}
                        
int16_t ETPU_PWM_InEnable(uint16_t channel)
{
   int32_t channelFlags;
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       channelFlags = read_pwm_in_ChanFlags(channel);
       write_pwm_in_ChanFlags(channelFlags | EXCEPTIONS_ENABLED, channel);
       returnCode = ETPU_set_hsr(channel,HSR_INIT_PWM_IN_VAL);                                         
   }
   return returnCode;
}

int16_t ETPU_PWM_InDisable(uint16_t channel)
{
   int32_t channelFlags;
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       channelFlags = read_pwm_in_ChanFlags(channel);
       write_pwm_in_ChanFlags(channelFlags & ~EXCEPTIONS_ENABLED, channel);
       returnCode = ETPU_set_hsr(channel,HSR_DISABLE_PWM_IN_VAL);                                         
   }
   return returnCode;

}

int16_t ETPU_PWM_InGetDutyCycle(t_PIO_CHANNELS channel, t_DutyCycle *dutycicle)
{
  uint32_t periodHigh;
  uint32_t periodLow;
  uint32_t polarityTmp;
  uint32_t periodIdx;
  int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
      returnCode = PERIPHERAL_FAILURE;
   }
#endif
  if (returnCode == NO_ERROR)
  {
      periodIdx = read_pwm_in_wrBuffIndex(channel) & 0x00ffffff;

      /* adjust index */
      if (periodIdx == 0)
      {
        periodIdx = PWM_IN_BUFF_SIZE - 1;
      }
      else
      {
        periodIdx--;
      }
      periodHigh = read_pwm_in_HighPeriodBuffVal(channel, periodIdx);
      periodLow  = read_pwm_in_LowPeriodBuffVal(channel, periodIdx);

      polarityTmp = read_pwm_in_ChanFlags(channel) & (POLARITY_MASK);

      if(polarityTmp)
      {
        *dutycicle = (uint32_t)((((uint64_t)periodHigh<<12)/(periodHigh+periodLow))*100)>>4;
      }
      else
      {
        *dutycicle = (uint32_t)((((uint64_t)periodLow<<12)/(periodHigh+periodLow))*100)>>4;
      }
  }
  return returnCode;
}

int16_t ETPU_PWM_InGetPeriod(t_PIO_CHANNELS channel, t_Period *period)
{
  uint32_t periodIdx;
  int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      returnCode = PERIPHERAL_FAILURE;
  }
#endif
  if (returnCode == NO_ERROR)
  {
      periodIdx = read_pwm_in_wrBuffIndex(channel) & 0x00ffffff ;

      /* adjust index */
      if (periodIdx == 0)
      {
        periodIdx = PWM_IN_BUFF_SIZE - 1;
      }
      else
      {
        periodIdx--;
      }
                            
      *period = read_pwm_in_HighPeriodBuffVal(channel, periodIdx);
      if(*period == 0x00FFFFFF)
      {
        *period = 0xFFFFFFFF;
      }
      else
      {
        *period += read_pwm_in_LowPeriodBuffVal(channel, periodIdx);
      }
  }
  return returnCode;
}

int16_t ETPU_PWM_InGetBuffPeriod(uint16_t channel, t_Period periodBuff[], uint8_t *buffSize)
{
    uint32_t periodIdx;
    int16_t i, k;
    int16_t countEl = PWM_IN_BUFF_SIZE;
    t_Period period;    
    t_Period tempPeriodHigh[PWM_IN_BUFF_SIZE], tempPeriodLow[PWM_IN_BUFF_SIZE];
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        /* retrive stored values */
        for (i = 0; i < PWM_IN_BUFF_SIZE; i++)
        {
            tempPeriodHigh[i] = read_pwm_in_HighPeriodBuffVal(channel, i);
            tempPeriodLow[i]  = read_pwm_in_LowPeriodBuffVal(channel, i);
        }    
        periodIdx = read_pwm_in_wrBuffIndex(channel) & 0x00ffffff ;

        /* adjust index */
        if (periodIdx == 0)
        {
            periodIdx = PWM_IN_BUFF_SIZE - 1;
        }
        else
        {
            periodIdx--;
        }
        
        /* if are required a number of elements < of stored elements */
        if (*buffSize < PWM_IN_BUFF_SIZE)
        {
            countEl = *buffSize+1;
        }

        *buffSize = 0;
        /* write period output buffer */
        for (i = 0; i < countEl - 1; i++)
        {
            k = periodIdx - i - 1;
            if (k < 0)
            {
                k += PWM_IN_BUFF_SIZE;
            }
            period = tempPeriodHigh[k];
            if(period == 0x00FFFFFF)
            {
                period = 0xFFFFFFFF;
            }
            else
            {
                period += tempPeriodLow[k];

                periodBuff[i] = period;
                /* number of elements written in the buffer */
                *buffSize = i+1;
            }
        }
    }
    return returnCode;
}

int16_t ETPU_PWM_InGetBuffDutyCycle(uint16_t channel, t_DutyCycle dutyBuffer[], uint8_t *buffSize)
{
    int16_t i, k;
    t_Period tempPeriodHigh[PWM_IN_BUFF_SIZE], tempPeriodLow[PWM_IN_BUFF_SIZE];
    t_Period periodHigh, periodLow;
    uint32_t periodIdx;
    uint32_t polarityTmp;
    int16_t countEl = PWM_IN_BUFF_SIZE;
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        /* retrive stored values */
        for (i = 0; i < PWM_IN_BUFF_SIZE; i++)
        {
            tempPeriodHigh[i] = read_pwm_in_HighPeriodBuffVal(channel, i);
            tempPeriodLow[i]  = read_pwm_in_LowPeriodBuffVal(channel, i);
        }    
        periodIdx = read_pwm_in_wrBuffIndex(channel) & 0x00ffffff ;

        /* adjust index */
        if (periodIdx == 0)
        {
            periodIdx = PWM_IN_BUFF_SIZE - 1;
        }
        else
        {
            periodIdx--;
        }

        polarityTmp = read_pwm_in_ChanFlags(channel) & (POLARITY_MASK);
        
        /* if are required a number of elements < of stored elements */
        if (*buffSize < PWM_IN_BUFF_SIZE)
        {
            countEl = *buffSize+1;
        }
        
        *buffSize = 0;
        
        /* write duty cycle output buffer  */
        for (i = 0; i < countEl-1; i++)
        {
            k = periodIdx - i - 1;
            if (k < 0)
            {
                k += PWM_IN_BUFF_SIZE;
            }

            periodHigh = tempPeriodHigh[k];
            periodLow  = tempPeriodLow[k]; 
            if(periodHigh == 0x00FFFFFF && periodLow == 0x00FFFFFF )
            {
                periodHigh = 0xFFFFFFFF;
                periodLow  = 0xFFFFFFFF;
            }
            else
            {
                if(polarityTmp)
                {
                    dutyBuffer[i] = (((periodHigh<<12)/(periodHigh+periodLow))*100)>>4;
                }
                else
                {
                    dutyBuffer[i] = (((periodLow<<12)/(periodHigh+periodLow))*100)>>4;
                }

                /* number of elements written in the buffer */
                *buffSize = i+1;
            }
        }
    }    
    return returnCode;
}


int16_t ETPU_PWM_InGetState(uint16_t channel, uint16_t *state)
{
  int32_t channelFlags;
  int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      returnCode = PERIPHERAL_FAILURE;
  }
#endif
  if (returnCode == NO_ERROR)
  {
      channelFlags = read_pwm_in_ChanFlags(channel);
      if(channelFlags & EXCEPTIONS_ENABLED)
      {
        *state = ENABLE;
      }
      else
      {
        *state = DISABLE;
      }
  }  
  return returnCode;
}

/* ============================================================== */

int16_T ETPU_ChannelInitRam(uint8_T channel, uint16_T chanParameters, uint32_T *memptr)
{
    /* channel = ETPU channel */
    /* chanParameters = num bytes to allocate */
    /* retaddr = address of memory allocated */
    /* ret. value = error code */
    uint32_T    *pba;    /* parameter base address for 32&24 bit parameters */
    int16_T     errcode = NO_ERROR;   /* return value */

    *memptr = (uint32_T)NULL;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        errcode = PERIPHERAL_FAILURE;
    }    
#endif
    if (errcode == NO_ERROR)
    {
        /* Disable channel to assign function safely */
        fs_etpu_disable( channel );

        pba = fs_etpu_malloc2( (uint8_t) channel, chanParameters);
        if (pba == NULL) /* error */
        {
            errcode = PERIPHERAL_FAILURE;
        }
        *memptr = (uint32_T)pba;
    }
    return errcode;
}


/******************************************************************************
FUNCTION     : eTPU_channel_init
PURPOSE      : To initialize an eTPU channel to look for inputs
INPUTS NOTES : This function has 7 parameters:
               channel - This is the channel number.
                           0-31 for FS_ETPU_A and 64-95 for FS_ETPU_B.
               priority - This is the priority to assign to the channel.
                          This parameter should be assigned a value of:
                          FS_ETPU_PRIORITY_HIGH, FS_ETPU_PRIORITY_MIDDLE or
                          FS_ETPU_PRIORITY_LOW.
               mode - This should be assigned a value of: 
                        ETPU_IC_MODE_SINGLE or ETPU_IC_MODE_CONT.
               function - eTPU channel function
               hsrInitFunction - eTPU channel init entry vector
               entryTable - entry table to use
               EntryTableBase - address of the entry table            
               Polarity - input pin signal polarity
RETURNS NOTES: Actually none
WARNING      : *This function does not configure the pin only the eTPU. In a
                  system a pin may need to be configured to select the eTPU.
******************************************************************************/
int16_t ETPU_ChannelInit(uint8_t channel, uint8_t priority, 
                  uint8_t mode, uint8_t function,
                  uint8_t entryTable, uint32_t ParameterBaseAddress )
{
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        if(ETPU.CHAN[channel].CR.B.CPBA != 0)
        {
            returnCode = PERIPHERAL_ALREADY_INITIALIZED;
        }
        else
        {
            ETPU.CHAN[channel].SCR.R = mode;
          
            ETPU.CHAN[channel].CR.B.CPR   = priority;
            ETPU.CHAN[channel].CR.B.ETCS  = entryTable;
            ETPU.CHAN[channel].CR.B.CFS   = function;
            ETPU.CHAN[channel].CR.B.CPBA  = ((ParameterBaseAddress - fs_etpu_data_ram_start) >> 3);
        }
    }
    return returnCode;
}

/******************************************************************************
FUNCTION     : ETPU_PIN_GetChannelPolarity
PURPOSE      : ***
INPUTS NOTES : This function has 2 parameters:
               channel - This is the channel number.
                           0-31 for FS_ETPU_A and 64-95 for FS_ETPU_B.
               Polarity - input pin signal polarity
RETURNS NOTES: Actually none
WARNING      : ***
******************************************************************************/
int16_t ETPU_PIN_GetChannelPolarity(uint8_t channel, t_ActivePolarity *polarity)
{
    int16_t returnCode = NO_ERROR;
    uint32_t tmpPolarity;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        tmpPolarity = read_sparkHandlerChanFlags(channel);
        tmpPolarity = tmpPolarity & (POLARITY_MASK);      
        *polarity = (tmpPolarity)?POL_HIGH:POL_LOW;
    }
    return returnCode;
}


/**********************************************************************/

int16_t ETPU_PINOUT_FastConfig(uint16_t channel_in, uint16_t channel_out, uint16_t channel_dir)
{
   uint32_t ParameterBaseAddress;
   int16_t  returnCode = NO_ERROR;
   uint16_t priority   = (uint16_t) FS_ETPU_PRIORITY_HIGH;
   uint16_t countMode  = (uint16_t) ETPU_COUNT_MODE_CONT; 
   
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
//   ParameterBaseAddress = ETPU_ChannelInitRam(channel_in, ETPU_FASTLINKEDCHAN_IN_NUM_PARMS);
       returnCode = ETPU_ChannelInitRam(channel_in, ETPU_FASTLINKEDCHAN_IN_NUM_PARMS, &ParameterBaseAddress );
       if(returnCode == NO_ERROR) 
       {
           /* Initialize channel 0 to manage crank signal */
           returnCode = ETPU_ChannelInit(channel_in, priority, countMode, 
                     FASTLINKEDCHAN_IN_FUNC, FASTLINKEDCHAN_IN_TABLE_SELECT, ParameterBaseAddress );

           if(returnCode == NO_ERROR)
           {
            //   ParameterBaseAddress = ETPU_ChannelInitRam(channel_out, ETPU_FASTLINKEDCHAN_OUT_NUM_PARMS);
               returnCode = ETPU_ChannelInitRam(channel_out, ETPU_FASTLINKEDCHAN_OUT_NUM_PARMS, &ParameterBaseAddress );
               if(returnCode == NO_ERROR) 
               {
               /* Initialize channel 0 to manage crank signal */
                   returnCode = ETPU_ChannelInit(channel_out, priority, countMode, 
                             FASTLINKEDCHAN_OUT_FUNC, FASTLINKEDCHAN_OUT_TABLE_SELECT, ParameterBaseAddress );

                   if(returnCode == NO_ERROR) 
                   {
                       //   ParameterBaseAddress = ETPU_ChannelInitRam(channel_dir, ETPU_FASTLINKEDCHAN_DIR_NUM_PARMS);
                       returnCode = ETPU_ChannelInitRam(channel_dir, ETPU_FASTLINKEDCHAN_DIR_NUM_PARMS, &ParameterBaseAddress );
                       if(returnCode == NO_ERROR) 
                       {
                           /* Initialize channel 0 to manage crank signal */
                           returnCode = ETPU_ChannelInit(channel_dir, priority, countMode, 
                                     FASTLINKEDCHAN_DIR_FUNC, FASTLINKEDCHAN_DIR_TABLE_SELECT, ParameterBaseAddress );

                           if(returnCode == NO_ERROR)
                           {
                               write_linkedChan(channel_out,channel_in);

                               write_switchingMode(SWITCHINGMODE_FAST, channel_out);
                               write_directionChannel(channel_dir, channel_out);
                           }
                       }
                   }
               }
           }
       }
   }
   return returnCode;                                         
}

int16_t ETPU_PINOUT_SlowConfig(uint16_t channel_in, uint16_t channel_out)
{
   uint32_t ParameterBaseAddress;
   int16_t  returnCode = NO_ERROR;
   uint16_t priority   = (uint16_t) FS_ETPU_PRIORITY_HIGH;
   uint16_t countMode  = (uint16_t) ETPU_COUNT_MODE_CONT; 
   
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif       
//   ParameterBaseAddress = ETPU_ChannelInitRam(channel_in, ETPU_FASTLINKEDCHAN_IN_NUM_PARMS);
    if (returnCode == NO_ERROR)
    {
        returnCode = ETPU_ChannelInitRam(channel_in, ETPU_FASTLINKEDCHAN_IN_NUM_PARMS, &ParameterBaseAddress );
        if(returnCode == NO_ERROR)
        {
            /* Initialize channel 0 to manage crank signal */
            returnCode = ETPU_ChannelInit(channel_in, priority, countMode, 
                     FASTLINKEDCHAN_IN_FUNC, FASTLINKEDCHAN_IN_TABLE_SELECT, ParameterBaseAddress );

            if(returnCode == NO_ERROR)
            {
            //    ParameterBaseAddress = ETPU_ChannelInitRam(channel_out, ETPU_FASTLINKEDCHAN_OUT_NUM_PARMS);
                returnCode = ETPU_ChannelInitRam(channel_out, ETPU_FASTLINKEDCHAN_OUT_NUM_PARMS, &ParameterBaseAddress );
                if(returnCode == NO_ERROR) 
                {
                    /* Initialize channel 0 to manage crank signal */
                    returnCode = ETPU_ChannelInit(channel_out, priority, countMode, 
                             FASTLINKEDCHAN_OUT_FUNC, FASTLINKEDCHAN_OUT_TABLE_SELECT, ParameterBaseAddress );

                    if(returnCode == NO_ERROR) 
                    {
                        write_linkedChan(channel_out,channel_in);
                        write_switchingMode(SWITCHINGMODE_SLOW, channel_out);
                    }
                }
            }
        }
    }    
    return returnCode;                                         
}

int16_t ETPU_PINOUT_FastEnable(uint16_t channel_in, uint16_t channel_out, uint16_t channel_dir)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       returnCode = ETPU_set_hsr(channel_out,HSR_INIT_FLCHAN_OUT_VAL);
       if(returnCode == NO_ERROR)
       {
          returnCode = ETPU_set_hsr(channel_dir,HSR_INIT_FLCHAN_DIR_VAL);
          if(returnCode == NO_ERROR)
          {
             returnCode = ETPU_set_hsr(channel_in,HSR_INIT_FLCHAN_IN_VAL);
          }
       }
   }
   return returnCode;                                         
}
                                    
int16_t ETPU_PINOUT_SlowEnable(uint16_t channel_in, uint16_t channel_out)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       returnCode = ETPU_set_hsr(channel_out,HSR_INIT_FLCHAN_OUT_VAL);
       if(returnCode == NO_ERROR)
       {
           returnCode = ETPU_set_hsr(channel_in,HSR_INIT_FLCHAN_IN_VAL);
       }
   }
   return returnCode;                                         
}
                                    
int16_t ETPU_PINOUT_FastDisable(uint16_t channel_in, uint16_t channel_out, uint16_t channel_dir)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif       
   if (returnCode == NO_ERROR)
   {
       returnCode = ETPU_set_hsr(channel_out,HSR_CLOSE_FLCHAN_OUT_VAL);
       if(returnCode == NO_ERROR)
       {
          returnCode = ETPU_set_hsr(channel_dir,HSR_CLOSE_FLCHAN_IN_VAL);
          if(returnCode == NO_ERROR)
          {
             returnCode = ETPU_set_hsr(channel_in,HSR_CLOSE_FLCHAN_IN_VAL);
          }
       }
   }
   return returnCode;                                         
}

int16_t ETPU_PINOUT_SlowDisable(uint16_t channel_in, uint16_t channel_out)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif       
   if (returnCode == NO_ERROR)
   {
       returnCode = ETPU_set_hsr(channel_out,HSR_CLOSE_FLCHAN_OUT_VAL);
       if(returnCode == NO_ERROR)
       {
           returnCode = ETPU_set_hsr(channel_in,HSR_CLOSE_FLCHAN_IN_VAL);
       }
   }
   return returnCode;                                         
}

int16_t ETPU_PINOUT_SetMinTimes(uint16_t channel_out, uint32_t timeOn, uint32_t timeOff)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       write_minOnTime(timeOn,channel_out);
       write_minOffTime(timeOff,channel_out);
   }
   return returnCode;                                         
}

int16_t ETPU_PINOUT_GetSwitchingTimes(uint16_t channel_out, uint32_t *switchingTimes)
{
   int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       *switchingTimes = read_switchingTimes(channel_out);
   }
   return returnCode;                                         
}

int16_t ETPU_PINOUT_GetLastPeriods(uint16_t channel_out, uint32_t *timeOn, uint32_t *timeOff)
{
   int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       *timeOn = read_lastOnTime(channel_out);
       *timeOff = read_lastOffTime(channel_out);
   }
   return returnCode;                                         
}

int16_t ETPU_ANGTRIG_Config(uint16_t chan)
{
    uint32_t ParameterBaseAddress;
    int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        returnCode = ETPU_ChannelInitRam(chan, ANGTRIGGER_FUNC_NUM_PARMS, &ParameterBaseAddress);
        if(returnCode == NO_ERROR) 
        {

            /* Initialize channel */
            returnCode = ETPU_ChannelInit( chan, 
                                        (uint16_t)FS_ETPU_PRIORITY_HIGH, 
                                        (uint16_t)ETPU_COUNT_MODE_CONT, 
                                        (uint16_t)ANGTRIGGER_FUNC_FUNCTION_NUMBER, 
                                        (uint16_t)ANGTRIGGER_FUNC_TABLE_SELECT, 
                                        ParameterBaseAddress);

            
            if (returnCode == NO_ERROR)
            {
                write_trigMode (TIME_TRIGGER_MODE, chan);
            }
        }
    }
    
    return returnCode;
                                    
}

int16_t ETPU_ANGTRIG_Set (uint16_t chan, uint32_t *startAngle, t_Period trigPeriod, uint8_t trigMode, uint8_t trigNum)
{
    int16_t returnCode = NO_ERROR;
    int8_t i;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        returnCode = PERIPHERAL_FAILURE;
    }    
#endif
    if (returnCode == NO_ERROR)
    {
        write_trigMode (trigMode, chan); 
        write_trigNum  (trigNum, chan); 

        if (trigMode == ANGLE_TRIGGER_MODE)
        {    
            for (i = 0; i < trigNum; i++)
            {
                write_trigStartAngleVal(chan, i, startAngle[i]/TCR2_TICK_PRESCALER);
            }
        }
        else
        {    
            write_trigPeriod(trigPeriod, chan);
        }
    }
    return returnCode;
}

int16_t ETPU_ANGTRIG_Enable (uint16_t chan)
{
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & (BIOS_FAILURE_ETPU|BIOS_FAILURE_INJ))
    {
        returnCode = PERIPHERAL_FAILURE;
    }    
#endif
    if (returnCode == NO_ERROR)
    {
        returnCode = ETPU_set_hsr(chan,HSR_INIT_ANGTRIG_VAL);
    }
    return returnCode;
}

int16_t ETPU_ANGTRIG_Disable (uint16_t chan, uint8_t forceTrigger)
{
    int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        write_indexRestart(chan, forceTrigger);
        returnCode = ETPU_set_hsr(chan, HSR_DISABLE_ANGTRIG_VAL);
    }
    return returnCode;
}
/******************************************************************************
FUNCTION     : SYNC_StartTCRx
PURPOSE      : Set global time and start Tcrx count
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: none
WARNING      : 
******************************************************************************/
int16_t ETPU_StartTCRx(void)
{
  int16_t returnCode = NO_ERROR;
  #ifdef CHECK_BIOS_FAULTS
  if(BIOS_Faults & BIOS_FAILURE_ETPU)
  {
      returnCode = PERIPHERAL_FAILURE;
  }
#endif    
  if (returnCode == NO_ERROR)
  {
      ETPU.MCR.B.GTBE = 1;          // Global Time Base Enable 
  }
  return returnCode;
}

/******************************************************************************
FUNCTION     : SYNC_SetAngleModeBothETPU
PURPOSE      : Set angle mode for both eTPU
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: none
WARNING      : 
******************************************************************************/
int16_t  ETPU_SetAngleModeBothETPU(void)
{
   int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
   if(BIOS_Faults & BIOS_FAILURE_ETPU)
   {
       returnCode = PERIPHERAL_FAILURE;
   }
#endif
   if (returnCode == NO_ERROR)
   {
       ETPU.TBCR_A.B.AM      = 1;
#if (TARGET_TYPE == MPC5554)
       ETPU.TBCR_B.B.AM      = 1;
#endif
   }
   return returnCode;
}

/******************************************************************************
FUNCTION     : SYNC_set_hsr
PURPOSE      : request HSR to the eTPU
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
int16_t ETPU_set_hsr(uint8_t channel, uint8_t hsr)
{
    uint8_t oldCpr;
    timeoutHandler_t etpuTimeout;
    int16_t returnCode = NO_ERROR;

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif        
    if (returnCode == NO_ERROR)
    {
        TIMING_SetTimeout(SYS_ETPU_TIMEOUT, &etpuTimeout);
        while(ETPU.CHAN[channel].HSRR.R)
        {
            uint8_t status;
            TIMING_GetTimeoutStatus(etpuTimeout, &status);
            if(status == TIMEOUT_EXPIRED)
            {
#ifdef CHECK_BIOS_FAULTS
                BIOS_Faults |= BIOS_FAILURE_ETPU;
#endif /* CHECK_BIOS_FAULTS */
                returnCode = PERIPHERAL_FAILURE;
            }
        }
        
        if (returnCode == NO_ERROR)
        {
            oldCpr = (uint8_t) (ETPU.CHAN[channel].CR.B.CPR);

            DisableAllInterrupts();
            ETPU.CHAN[channel].CR.B.CPR = 0;
            ETPU.CHAN[channel].HSRR.R = hsr;
            ETPU.CHAN[channel].CR.B.CPR = oldCpr;
            EnableAllInterrupts();
        }
    }
    return NO_ERROR;
}


/******************************************************************************
FUNCTION     : fs_memCopyVerifier
PURPOSE      : Test correctness of a mem copy
INPUTS NOTES : This function has 3 parameters:
RETURNS NOTES: error code
WARNING      : 
******************************************************************************/
uint32_t fs_memCopyVerifier(char * dest, char * source, uint32_t size)
{
    char *p = dest;
    char *q = source;
    uint32_t err = 0;


    while((size)&&(err==0)){
	
      if (*p != *q)
      {
          err = 1;
      }
      p++;
      q++;
      size--;
    }
  return (err);
}

/*****************************************************************************/



/*****************************************************************************
FUNCTION     : ETPU_Delay_Config
PURPOSE      : Create an ETPU programmable delay
INPUTS NOTES : 
RETURNS NOTES: error code
WARNING      : 
******************************************************************************/
int16_t ETPU_Delay_Config (uint16_t chan)
{
    uint32_T ParameterBaseAddress;
    int16_T   errtmp = NO_ERROR;
  

#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
      errtmp = PERIPHERAL_FAILURE;
    }
#endif
    if (errtmp == NO_ERROR)
    {
    //  ParameterBaseAddress = ETPU_ChannelInitRam(chan, DELAY_FUNC_NUM_PARMS);
        errtmp = ETPU_ChannelInitRam(chan, DELAY_FUNC_NUM_PARMS, &ParameterBaseAddress);
      
        if (errtmp != NO_ERROR)
        {
            errtmp = (PERIPHERAL_NOT_CONFIGURED);
        }
        else
        {
       /* Initialize channel 0 to manage crank signal */
            errtmp = ETPU_ChannelInit( chan,  
                            (uint16_t)FS_ETPU_PRIORITY_LOW, 
                            (uint16_t)ETPU_COUNT_MODE_CONT, 
                            (uint16_t)DELAY_FUNC_FUNCTION_NUMBER, 
                            (uint16_t)DELAY_FUNC_TABLE_SELECT, 
                            ParameterBaseAddress);
        }
    }
    return errtmp;
}

/*****************************************************************************/



/******************************************************************************
FUNCTION     : ETPU_Delay_Enable
PURPOSE      : Enable an ETPU programmed delay
INPUTS NOTES : 
RETURNS NOTES: error code
WARNING      : 
******************************************************************************/
int16_t ETPU_Delay_Enable (uint16_t chan)
{
    int16_t returnCode = NO_ERROR;
#ifdef CHECK_BIOS_FAULTS
    if(BIOS_Faults & BIOS_FAILURE_ETPU)
    {
        returnCode = PERIPHERAL_FAILURE;
    }
#endif
    if (returnCode == NO_ERROR)
    {
        if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
        {
           returnCode = -1;
        }
        else
        {
           returnCode = ETPU_set_hsr(chan,HSR_INIT_DELAY_VAL);
        }
    }
    return returnCode;
  
}

/*****************************************************************************/


/*---------------------------------------------------------------------------*
 |                                                                           |
 |      eTPU channels exceptions                                             |
 |                                                                           |
 *---------------------------------------------------------------------------*/
/******************************************************************************
FUNCTION     : _ETPU_A_Channel0Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 0 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel0Interrupt_Handler(void)
{
    uint24_t volatile retVal=0;
    uint32_t tmp;    
    volatile uint32_t exType;
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif
    
    exType = 0x00ffffff & read_angleClockChanFlags(CRANKANGLE_CHANNEL);
    tmp = exType & EXCEPTIONS_ENABLED;
    write_angleClockChanFlags(tmp, CRANKANGLE_CHANNEL);

    ETPU.CHAN[0].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    if (exType & EX_SYNC) 
    {
        SYNC_ex_Sync();
    }
    else if (exType & EX_NO_SYNC) 
    {
        SYNC_ex_NoSync();
    }
    else if (exType & EX_TOOTH) 
    {
        SYNC_ex_Tooth();
    }
    else { /* MISRA */ }

#ifdef _TEST_TPU_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif  

    CntTaskVRS++;
    
#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRVRSms)
        {
            TaskTimerISRVRSms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel1Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 1 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel1Interrupt_Handler(void) 
{
#ifdef _BUILD_PHASE_
#if (BOARD_TYPE == BOARD_1)
    volatile uint32_t exType;
    uint32_t tmp;    

    // Clear the interrupt
    ETPU.CHAN[1].SCR.B.CIS = 1;
   
    exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
    tmp = exType & (~(PHASE_EX_LOCKED | PHASE_EX_LOST | PHASE_EX_EDGE));
    write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);
    
    if(exType & PHASE_EX_LOCKED) 
    {
        exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
        tmp = exType & (~PHASE_EX_LOCKED);
        write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);

        PHASE_ExLocked();
        
    }
    else if(exType & PHASE_EX_LOST) 
    {
        exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
        tmp = exType & (~PHASE_EX_LOST);
        write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);

        PHASE_ExLost();
    }
    else if(exType & PHASE_EX_EDGE) 
    {
        exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
        tmp = exType & (~PHASE_EX_EDGE);
        write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);

        PHASE_ExEdge();
    }
    else
    {
    }
#else
    // Clear the interrupt
    ETPU.CHAN[1].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_01]);
#endif    
#else
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    CntTaskeTPUCh01++;

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_01].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_01]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh01eTPUms)
        {
            TaskTimerISRCh01eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
#endif
    /* SOLO PER DEBUG */
    EtpuACh0Int++;
#ifdef _TEST_ETPU1_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(OUT_D1, (&pin));
        DIGIO_OutSet(OUT_D1, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel2Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 2 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel2Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_02].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh02++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_02]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh02eTPUms)
        {
            TaskTimerISRCh02eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_ETPU2_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(OUT_D2, (&pin));
        DIGIO_OutSet(OUT_D2, !pin);
    }
#endif
#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel3Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 3 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel3Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_03].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh03++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_03]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh03eTPUms)
        {
            TaskTimerISRCh03eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_ETPU3_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(OUT_D1, (&pin));
        DIGIO_OutSet(OUT_D1, !pin);
    }
#endif
#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel4Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 4 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel4Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_04].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh04++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_04]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh04eTPUms)
        {
            TaskTimerISRCh04eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_ETPU4_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(OUT_D2, (&pin));
        DIGIO_OutSet(OUT_D2, !pin);
    }
#endif
#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel5Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 5 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel5Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_05].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh05++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_05]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh05eTPUms)
        {
            TaskTimerISRCh05eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_ETPU5_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(OUT_D2, (&pin));
        DIGIO_OutSet(OUT_D2, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel6Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 6 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel6Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_06].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh06++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_06]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh06eTPUms)
        {
            TaskTimerISRCh06eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel7Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 7 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel7Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_07].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh07++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_07]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh07eTPUms)
        {
            TaskTimerISRCh07eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel8Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 8 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel8Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_08].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh08++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_08]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh08eTPUms)
        {
            TaskTimerISRCh08eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel9Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 9 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel9Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_09].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh09++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_09]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh09eTPUms)
        {
            TaskTimerISRCh09eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel9Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 9 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel10Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_10].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh10++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_10]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh10eTPUms)
        {
            TaskTimerISRCh10eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif /* _TEST_TRIGGER_EX_ */
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel9Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 9 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel11Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_11].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh11++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_11]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh11eTPUms)
        {
            TaskTimerISRCh11eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif /* _TEST_TRIGGER_EX_ */
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel9Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 9 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel12Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_12].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh12++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_12]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh12eTPUms)
        {
            TaskTimerISRCh12eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif /* _TEST_TRIGGER_EX_ */
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel13Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 13 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel13Interrupt_Handler(void)
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_13].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh13++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_13]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh13eTPUms)
        {
            TaskTimerISRCh13eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif

#ifdef _TEST_TRIGGER_EX_
    {
        uint8_t pin;
        DIGIO_OutGet(IN_IgnCmd_7, (&pin));
        DIGIO_OutSet(IN_IgnCmd_7, !pin);
    }
#endif /* _TEST_TRIGGER_EX_ */
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel14Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 14 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel14Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_14].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh14++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_14]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh14eTPUms)
        {
            TaskTimerISRCh14eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel15Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 15 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel15Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_15].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh15++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_15]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh15eTPUms)
        {
            TaskTimerISRCh15eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel16Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 16 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel16Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_16].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh16++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_16]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh16eTPUms)
        {
            TaskTimerISRCh16eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

/******************************************************************************
FUNCTION     : _ETPU_A_Channel15Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 15 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel17Interrupt_Handler(void) 
{
#ifdef _BUILD_PHASE_
#if (BOARD_TYPE == BOARD_2)
    volatile uint32_t exType;
    uint32_t tmp;    

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_17].SCR.B.CIS = 1;
   
    exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
    tmp = exType & (~(PHASE_EX_LOCKED | PHASE_EX_LOST | PHASE_EX_EDGE));
    write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);
    
    if(exType & PHASE_EX_LOCKED) 
    {
        exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
        tmp = exType & (~PHASE_EX_LOCKED);
        write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);

        PHASE_ExLocked();
        
    }
    else if(exType & PHASE_EX_LOST) 
    {
        exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
        tmp = exType & (~PHASE_EX_LOST);
        write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);

        PHASE_ExLost();
    }
    else if(exType & PHASE_EX_EDGE) 
    {
        exType = 0x00ffffff & read_phaseChanFlags(CAMSENSE_CHANNEL);
        tmp = exType & (~PHASE_EX_EDGE);
        write_phaseChanFlags(tmp, CAMSENSE_CHANNEL);

        PHASE_ExEdge();
    }
    else
    {
    }
#else
    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_17].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_17]);
#endif    
#else
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_17].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh17++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_17]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh17eTPUms)
        {
            TaskTimerISRCh17eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
#endif    
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel18Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 18 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel18Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_18].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh18++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_18]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh18eTPUms)
        {
            TaskTimerISRCh18eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel19Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 19 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel19Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_19].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh19++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_19]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh19eTPUms)
        {
            TaskTimerISRCh19eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel20Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 20 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel20Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_20].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh20++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_20]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh20eTPUms)
        {
            TaskTimerISRCh20eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel21Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 21 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel21Interrupt_Handler(void) 
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_21].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh21++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_21]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh21eTPUms)
        {
            TaskTimerISRCh21eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel22Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 22 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel22Interrupt_Handler(void)
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_22].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh22++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_22]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh22eTPUms)
        {
            TaskTimerISRCh22eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel23Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 23 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel23Interrupt_Handler(void)
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_23].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh23++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_23]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh23eTPUms)
        {
            TaskTimerISRCh23eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel24Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 24 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel24Interrupt_Handler(void)
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_24].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh24++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_24]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh24eTPUms)
        {
            TaskTimerISRCh24eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


/******************************************************************************
FUNCTION     : _ETPU_A_Channel25Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 25 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel25Interrupt_Handler(void)
{
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    // Clear the interrupt
    ETPU.CHAN[ETPU_A_CHAN_25].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh25++;

    //programming the ISR;
    ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_25]);

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh25eTPUms)
        {
            TaskTimerISRCh25eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}



/******************************************************************************
FUNCTION     : _ETPU_A_Channel26Interrupt_Handler
PURPOSE      : manage ISR from the eTPU_A channel 26 
INPUTS NOTES : This function has 0 parameters:
RETURNS NOTES: void
WARNING      : 
******************************************************************************/
void _ETPU_A_Channel26Interrupt_Handler(void)
{
    volatile uint32_t exType;
#ifdef _TEST_ISR_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
#endif

    exType = 0x00ffffff & read_angleExGeneratorChanFlags(CRANKEXGEN_CHANNEL);
    write_angleExGeneratorChanFlags(0, CRANKEXGEN_CHANNEL);

    // Clear the interrupt
    ETPU.CHAN[CRANKEXGEN_CHANNEL].SCR.B.CIS = 1;

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskeTPUCh26++;

    //programming the ISR;
    //ActivateTask(_ETPU_A_vect_user_isr[ETPU_A_CHAN_26]);

    if (exType & EX_ANGLE) 
    {
        SYNC_ex_Angle();
    }
    else { /* MISRA */ }

#ifdef _TEST_ISR_TIMING_
    if (ENTESTISRTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerISRCh26eTPUms)
        {
            TaskTimerISRCh26eTPUms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}




/* ===================================================================
**     Methods     :  _ETPU_B_ChannelxxInterrupt_Handler 
**
**     Description :  ETPU B ISRs
**         
**
**         
**     Parameters  :  
**     Returns     : none
** ===================================================================
*/
#if (USE_ETPU_B == 1)
void _ETPU_B_Channel0Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_00].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_00-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel1Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_01].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_01-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel2Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_02].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_02-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel3Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_03].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_03-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel4Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_04].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_04-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel5Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_05].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_05-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel6Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_06].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_06-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel7Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_07].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_07-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel8Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_08].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_08-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel9Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_09].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_09-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel10Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_10].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_10-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel11Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_11].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_11-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel12Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_12].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_12-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel13Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_13].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_13-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel14Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_14].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_14-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel15Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_15].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_15-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel16Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_16].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_16-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel17Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_17].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_17-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel18Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_18].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_18-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel19Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_19].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_19-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel20Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_20].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_20-ETPU_B_CHAN_00]);
}


void _ETPU_B_Channel21Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_21].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_21-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel22Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_22].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_22-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel23Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_23].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_23-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel24Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_24].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_24-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel25Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_25].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_25-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel26Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_26].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_26-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel27Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_27].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_27-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel28Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_28].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_28-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel29Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_29].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_29-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel30Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_30].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_30-ETPU_B_CHAN_00]);
}

void _ETPU_B_Channel31Interrupt_Handler(void)
{
    // Clear the interrupt
    ETPU.CHAN[ETPU_B_CHAN_31].SCR.B.CIS = 1;

    //programming the ISR;
    ActivateTask(_ETPU_B_vect_user_isr[ETPU_B_CHAN_31-ETPU_B_CHAN_00]);
}

#endif

#endif //_BUILD_SYS_
