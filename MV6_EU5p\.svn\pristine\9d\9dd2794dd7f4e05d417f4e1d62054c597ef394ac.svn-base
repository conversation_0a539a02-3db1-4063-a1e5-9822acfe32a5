// T32TPU20001 Wed Oct 23 12:01:28 2019

B::

TOOLBAR   ON
STATUSBAR ON
FramePOS 15.625,0.35714,,,Maximized
WinPAGE.RESet

WinPAGE.Create P000
WinCLEAR

WinPOS 49.625 61.857 66. 25. 13. 1. W007
Data.dump (SD:0x16000) /DIALOG

WinPOS 88.125 0.28571 77. 20. 0. 0. W000
Register

WinPOS 0.0 0.35714 83. 18. 0. 0. W001
Var.Watch

VAR.ADDWATCH %H %E %SL MaxCpuLoadCnt
VAR.ADDWATCH %H %E %SL CpuLoadPerc
VAR.ADDWATCH %H %E %SL ECULoad
VAR.ADDWATCH %H %E %SL Rpm
VAR.ADDWATCH %H %E %SL DISABLEINT
VAR.ADDWATCH %H %E %SL KWPsession
VAR.ADDWATCH %H %E %SL EEIDVersion
VAR.ADDWATCH %H %E %SL FlgUDSPwrLSlow
VAR.ADDWATCH %H %E %SL FlgSetIdVersionStart
VAR.ADDWATCH %H %E %SL IUMPR 
VAR.ADDWATCH %H %E %SL Start_Ch[0]
VAR.ADDWATCH %H %E %SL Start_Ch[1]
VAR.ADDWATCH %H %E %SL Start_Ch[2]
VAR.ADDWATCH %H %E %SL Start_Ch[3]
VAR.ADDWATCH %H %E %SL Start_Ch[4]
VAR.ADDWATCH %H %E %SL Start_Ch[5]
VAR.ADDWATCH %H %E %SL Start_Ch[6]
VAR.ADDWATCH %H %E %SL Start_Ch[7]
VAR.ADDWATCH %H %E %SL StMisf[0]
VAR.ADDWATCH %H %E %SL StMisf[1]
VAR.ADDWATCH %H %E %SL StMisf[2]
VAR.ADDWATCH %H %E %SL StMisf[3]
VAR.ADDWATCH %H %E %SL StMisf[4]
VAR.ADDWATCH %H %E %SL StMisf[5]
VAR.ADDWATCH %H %E %SL StMisf[6]
VAR.ADDWATCH %H %E %SL StMisf[7]

WinPAGE.select P000

ENDDO
