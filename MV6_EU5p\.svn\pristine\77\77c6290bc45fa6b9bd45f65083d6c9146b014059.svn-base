/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function PWM, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

//#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_SharedTypes.h"
#include "..\..\common\ETPU_Shared.h"
#include "..\include\sparkHandler.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

//int sparkHandlerUnexpectedEvent = 0;

//unsigned int sparkCaptureRegA= 0xFFFFFF;
//unsigned int sparkCaptureRegB= 0xFFFFFF;

#pragma library;
#pragma option +l;  // List the library
#pragma option v;


unsigned int indPosCh30;
unsigned int indPosCh31;
/********************************************************************************
* FUNCTION: PWM                                                                 *
* PURPOSE:  This function rise an ISR to the host at high to low or low to high *
* input pin transition                                                          *
* CrankAngle parameter.                                                         *
*                                                                               *
* INPUTS NOTES: This function has 2 parameters                                  *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
void PWM(unsigned int dutyCycle, unsigned int period, unsigned int mode, 
         unsigned int numOfEdges, unsigned int continuousMode, unsigned int startingAngle, unsigned int pwmChanFlags)
{
#ifdef _BUILD_ETPUPWM_
    unsigned int currentTCR;
    unsigned int currentTCRB;
    unsigned int polarity;

    if (HSR_INIT_PWM)   // Required to initialize
    {
        if(pwmChanFlags & POLARITY_MASK)
            polarity = 1; // active high
        else
            polarity = 0; // active low

        if(continuousMode == 1)
        {
            numOfEdges = 0x7fffff;
        }
        else
        {
            if(GetCurrentChanNum() == 30)
            {
                indPosCh30=0;
            }else
            if(GetCurrentChanNum() == 31)
            {
                indPosCh31=20;
            }
            pwmChanFlags |= LINK_MASK;
        }
        if (mode)
        {// TIME MODE
            currentTCR = tcr1;

            EnableOutputBuffer();
            SetChannelMode(em_nb_st);

            if(polarity)
            {
                if(dutyCycle == period)
                {
                    SetPinHigh();
                    SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_ge, NoChange);
                    SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, NoChange);
                }
                else if(dutyCycle == 0)
                {
                    SetPinLow();
                    SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_ge, NoChange);
                    SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, NoChange);
                }
                else
                {
                    SetupMatch_A(currentTCR + dutyCycle, Mtcr1_Ctcr1_ge, PinLow);
                    SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, PinHigh);
                }
            }
            else
            {
                if(dutyCycle == period)
                {
                    SetPinLow();
                    SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_ge, NoChange);
                    SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, NoChange);
                }
                else if(dutyCycle == 0)
                {
                    SetPinHigh();
                    SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_ge, NoChange);
                    SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, NoChange);
                }
                else
                {
                    SetupMatch_A(currentTCR + dutyCycle, Mtcr1_Ctcr1_ge, PinHigh);
                    SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, PinLow);
                }
            }
        }
        else
        {// ANGLE MODE
            currentTCR = startingAngle;
            currentTCRB = currentTCR;
            currentTCRB = currentTCRB + dutyCycle;

            SetChannelMode(em_nb_st);

            if(polarity)
            {
                SetupMatch_A(currentTCRB , Mtcr2_Ctcr2_ge, PinLow);
                SetupMatch_B(currentTCR, Mtcr2_Ctcr2_eq, PinHigh);
            }
            else
            {
                SetupMatch_A(currentTCRB , Mtcr2_Ctcr2_ge, PinHigh);
                SetupMatch_B(currentTCR, Mtcr2_Ctcr2_eq, PinLow);
            }
        }

    }
    else if (HSR_DISABLE_PWM)
    {
        ClearLSRLatch();
        DisableMatchDetection();
        ClearMatchBLatch();
        ClearTransLatch();
        if(polarity)
        {
            SetPinLow();
        }
        else
        {
            SetPinHigh();
        }
    }
    else if (MatchB)
    {
        if(pwmChanFlags & POLARITY_MASK)
            polarity = 1; // active high
        else
            polarity = 0; // active low

        if(continuousMode == 0)
        {
            unsigned int currentIndex;
            numOfEdges = numOfEdges - 1;

            if(numOfEdges == 0)
                pwmChanFlags |= LINK_MASK;

            if(pwmChanFlags & LINK_MASK)
            {
                if(GetCurrentChanNum() == 30)
                {
                    sampleNumber [indPosCh30]= numOfEdges;
                    angleValue   [indPosCh30]= tcr2;
                    indPosCh30++;
                } else 
                if(GetCurrentChanNum() == 31)
                {
                    sampleNumber [indPosCh31]= numOfEdges;
                    angleValue   [indPosCh31]= tcr2;
                    indPosCh31++;
                }
        
                pwmChanFlags &= ~ LINK_MASK;
            }
        }

        if(numOfEdges > 0)
        {
            if (mode)
            {// TIME MODE

                currentTCR = GetCapRegB();

                ClearMatchBLatch();

                if(polarity)
                {
                    if(dutyCycle == period)
                    {
                        SetPinHigh();
                        SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_eq, NoChange);
                        SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_eq, NoChange);
                    }
                    else if(dutyCycle == 0)
                    {
                        SetPinLow();
                        SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_eq, NoChange);
                        SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_eq, NoChange);
                    }
                    else
                    {
                        SetupMatch_A(currentTCR + dutyCycle, Mtcr1_Ctcr1_ge, PinLow);
                        SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, PinHigh);
                    }
                }
                else
                {
                    if(dutyCycle == period)
                    {
                        SetPinLow();
                        SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_eq, NoChange);
                        SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_eq, NoChange);
                    }
                    else if(dutyCycle == 0)
                    {
                        SetPinHigh();
                        SetupMatch_A(currentTCR + period-1, Mtcr1_Ctcr1_eq, NoChange);
                        SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_eq, NoChange);
                    }
                    else
                    {
                        SetupMatch_A(currentTCR + dutyCycle, Mtcr1_Ctcr1_ge, PinHigh);
                        SetupMatch_B(currentTCR + period, Mtcr1_Ctcr1_ge, PinLow);
                    }
                }
            }
            else
            {// ANGLE MODE
                currentTCR = GetCapRegB();
                currentTCRB = currentTCR;

                currentTCR += dutyCycle;

                if (currentTCR >= (TEETH_PER_CICLE*TICKS_PER_TOOTH))
                {
                    currentTCR -= (TEETH_PER_CICLE*TICKS_PER_TOOTH);
                }

                if(period != (TEETH_PER_CICLE*TICKS_PER_TOOTH))
                {
                    currentTCRB += period;

                    if (currentTCRB >= (TEETH_PER_CICLE*TICKS_PER_TOOTH))
                    {
                        unsigned int tmpAngle;
                        tmpAngle = TEETH_PER_CICLE;
                        tmpAngle *= TICKS_PER_TOOTH;
                        currentTCRB = currentTCRB - tmpAngle;
                    }
                }

                ClearMatchBLatch();
                if(polarity)
                {
                    SetupMatch_A(currentTCR, Mtcr2_Ctcr2_eq, PinLow);
                    SetupMatch_B(currentTCRB, Mtcr2_Ctcr2_eq, PinHigh);
                }
                else
                {
                    SetupMatch_A(currentTCR, Mtcr2_Ctcr2_eq, PinHigh);
                    SetupMatch_B(currentTCRB, Mtcr2_Ctcr2_eq, PinLow);
                }
            }
        }
        else
        {
            ClearLSRLatch();
            ClearMatchALatch();
            ClearMatchBLatch();
            ClearTransLatch();
        }
    }
    else if (LinkServiceRequest)
    {
            pwmChanFlags |= LINK_MASK;
            ClearLinkServiceRequestEvent();
    }
    else
    {
        //This else statement is used to catch all unspecified entry table conditions
        // Clear all possible event sources
        // And set the unexpected event error indicator
        ClearMatchALatch();
        ClearTransLatch();
    };
#endif /* _BUILD_ETPUPWM_ */

}

#pragma endlibrary;

