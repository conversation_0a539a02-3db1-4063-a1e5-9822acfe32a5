/*
 * File: div_s32_floor.c
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1416
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Apr 17 08:45:22 2020
 */

#include "rtwtypes.h"
#include "div_s32_floor.h"

int32_T div_s32_floor(int32_T numerator, int32_T denominator)
{
  int32_T quotient;
  uint32_T absNumerator;
  uint32_T absDenominator;
  uint32_T tempAbsQuotient;
  boolean_T quotientNeedsNegation;
  if (denominator == 0) {
    quotient = (numerator >= 0) ? MAX_int32_T : MIN_int32_T;

    /* Divide by zero handler */
  } else {
    absNumerator = (numerator < 0) ? ((~((uint32_T)numerator)) + 1U) :
      ((uint32_T)numerator);
    absDenominator = (denominator < 0) ? ((~((uint32_T)denominator)) + 1U) :
      ((uint32_T)denominator);
    quotientNeedsNegation = ((numerator < 0) != (denominator < 0));
    tempAbsQuotient = absNumerator / absDenominator;
    if (quotientNeedsNegation) {
      absNumerator %= absDenominator;
      if (absNumerator > 0U) {
        tempAbsQuotient++;
      }
    }

    quotient = quotientNeedsNegation ? (-((int32_T)tempAbsQuotient)) : ((int32_T)
      tempAbsQuotient);
  }

  return quotient;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
