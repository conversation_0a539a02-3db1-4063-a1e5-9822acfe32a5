/*
 * File: div_nzp_repeat_u32_sat.h
 *
 * Code generated for Simulink model 'CreepLimiterMgm'.
 *
 * Model version                  : 1.87
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Tue Apr 10 15:00:36 2018
 */

#ifndef SHARE_div_nzp_repeat_u32_sat
#define SHARE_div_nzp_repeat_u32_sat
#include "rtwtypes.h"

extern uint32_T div_nzp_repeat_u32_sat(uint32_T numerator, uint32_T denominator,
  uint32_T nRepeatSub);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
