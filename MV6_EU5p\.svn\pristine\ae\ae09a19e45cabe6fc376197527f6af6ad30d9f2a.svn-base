/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function adc_trigger, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

//#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_SharedTypes.h"
#include "..\..\common\ETPU_Shared.h"
#include "..\include\sparkHandler.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

//int sparkHandlerUnexpectedEvent = 0;

//unsigned int sparkCaptureRegA= 0xFFFFFF;
//unsigned int sparkCaptureRegB= 0xFFFFFF;

#pragma library;
#pragma option +l;  // List the library
#pragma option v;

unsigned int indPosCh30;
unsigned int indPosCh31;
/********************************************************************************
* FUNCTION: PWM                                                                 *
* PURPOSE:  This function rise an ISR to the host at high to low or low to high *
* input pin transition                                                          *
* CrankAngle parameter.                                                         *
*                                                                               *
* INPUTS NOTES: This function has 2 parameters                                  *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
void adc_trigger(unsigned int adcTrPeriod)
{
#ifdef _BUILD_ETPUPWM_
    unsigned int currentTCR;

    if (HSR_INIT_ADCTRIGGER)   // Required to initialize
    {
        currentTCR = tcr1;

        EnableOutputBuffer();
        SetChannelMode(em_nb_st);

        SetupMatch_A(currentTCR + adcTrPeriod/2, Mtcr1_Ctcr1_ge, PinHigh);
        SetupMatch_B(currentTCR + adcTrPeriod, Mtcr1_Ctcr1_ge, PinLow);
    }
    else if (HSR_DISABLE_ADCTRIGGER)
    {
        ClearLSRLatch();
        ClearMatchALatch();
        ClearTransLatch();
    }
    else if (MatchB)
    {
        currentTCR = GetCapRegB();

        SetupMatch_A(currentTCR + adcTrPeriod/2, Mtcr1_Ctcr1_ge, PinHigh);
        SetupMatch_B(currentTCR + adcTrPeriod, Mtcr1_Ctcr1_ge, PinLow);
    }
    else
    {
        //This else statement is used to catch all unspecified entry table conditions
        // Clear all possible event sources
        // And set the unexpected event error indicator
        ClearLSRLatch();
        ClearMatchALatch();
        ClearTransLatch();
    };
#endif /* _BUILD_ETPUPWM_ */

}

#pragma endlibrary;

