/*
 * File: mul_u32_u32_u32_sr10_sat.c
 *
 * Code generated for Simulink model 'CreepLimiterMgm'.
 *
 * Model version                  : 1.89
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Thu Apr 12 13:27:52 2018
 */

#include "rtwtypes.h"
#include "mul_wide_u32.h"
#include "mul_u32_u32_u32_sr10_sat.h"

uint32_T mul_u32_u32_u32_sr10_sat(uint32_T a, uint32_T b)
{
  uint32_T result;
  uint32_T u32_chi;
  mul_wide_u32(a, b, &u32_chi, &result);
  result = (u32_chi << 22U) | (result >> 10U);
  u32_chi >>= 10U;
  if (u32_chi) {
    result = MAX_uint32_T;
  }

  return result;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
