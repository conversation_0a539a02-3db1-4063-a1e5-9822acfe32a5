/************************************************************************
 *                                                                       *
 *                   Standard Software C90LC Driver for MPC563xM         *
 *                                                                       *
 * FILE NAME     :  SetLock.c                                         *
 * DATE          :                                                       *
 *                                                                       *
 * AUTHOR        :                                                       *
 * E-mail        :                                                       *
 *                                                                       *
 *************************************************************************/

 /******************************* CHANGES *********************************
 0.0a    01-24-2008  Sindhu R01         Initial Version
 0.1     02-08-2008  <PERSON> He            Updated for final release.
 0.2     03-18-2008  Chen He            Add FlashECCLogicCheck
 1.0     04-10-2008  Sindhu R01         Updated for final Release
 1.1     06-12-2008  <PERSON><PERSON>      Updates to JDP SW template.
 1.2     08-20-2008  A<PERSON><PERSON> after changes in SSD to support 
                                        MPC5668x.
 1.3     01-05-2009  Sindhu R01         Added Return codes related to FlashECCLogicCheck  
                                        and FlashArrayIntegrityCheck in Section 2.5.
                                        Modified Table 5.
 0.2.0   01-20-2009  Sindhu R01         Added API FactoryMarginReadCheck
 0.3.0   02-13-2009  Arvind Awasthi     Added eccValue parameter to the prototype of 
                                        FlashECCLogicCheck API.
 0.4.0   11-25-2009  Leonardo Colombo   Updated after changes in SSD to support all xPC56xx.
 0.5.0   06-15-2010  Cosimo Stornaiuolo Modified Test_FlashArrayUntegrityCheck.c to 
                                        support EmbAlgo 3.7
                                        Added support for DFO
                                        Tets Suite Updated for xPC56xx.
 *************************************************************************/

/******************************* AKHELA **********************************
 1.0.0  2012.06.06       Mocci A.       C90LC v1.0.1 porting
 *************************************************************************/ 
 
#ifdef  _BUILD_FLASH_
 /*-----------------------------------*
  * INCLUDE FILES
  *-----------------------------------*/
#include    "ssd_c90fl.h"

           
/*-----------------------------------*
* PUBLIC VARIABLE DEFINITIONS
*-----------------------------------*/
/* C90LC Driver v1.0.1 */
const unsigned long SetLock_C[] = 
{
      0x7C681B78, 0x7C892378, 0x7CAA2B78, 0x7CCB3378, 0x28090006, 0x4081000C, 0x38800800
    , 0x480000E8, 0x38800000, 0x38A00004, 0x3CC00010, 0x38E00014, 0x2C090002, 0x40820014
    , 0x3CC00001, 0x38C6FFFF, 0x38E00000, 0x48000070, 0x2C090004, 0x40820010, 0x3CC00003
    , 0x38E00010, 0x4800005C, 0x2C090000, 0x41820054, 0x38A0000C, 0x2C090003, 0x40820014
    , 0x3CC00001, 0x38C6FFFF, 0x38E00000, 0x48000038, 0x2C090005, 0x40820010, 0x3CC00003
    , 0x38E00010, 0x48000024, 0x2C090006, 0x7CE0389E, 0x39800FFF, 0x2C090006, 0x7CCC309E
    , 0x39800008, 0x2C090006, 0x7CAC289E, 0x81880000, 0x7CA56214, 0x81850000, 0x558C0001
    , 0x4082001C, 0x91650000, 0x81850000, 0x558C0001, 0x4082000C, 0x38800A00, 0x48000028
    , 0x7CCB30F8, 0x81850000, 0x7D8C5838, 0x91850000, 0x7D4C3830, 0x7D8C3038, 0x81650000
    , 0x7D6B6378, 0x91650000, 0x81880024, 0x2C0C0000, 0x41820008, 0x44000002, 0x7C8C2378
    , 0x7D836378, 0x4E800020
    , 0x30393530, 0x37464646
   
}; /* Total Size = 74 words */

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_t functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */



/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * SetLock - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/  

uint32_t SetLock ( PSSD_CONFIG pSSDConfig,
                uint8_t blkLockIndicator,
                uint32_t blkLockState,
                uint32_t password
                )

{
  FlashFunctionLoader( (unsigned long*)SetLock_C, sizeof(SetLock_C)/4);
  return ((pSETLOCK)FlashFunctionPointer)(pSSDConfig, blkLockIndicator,
                                             blkLockState, password);
}

#endif /* _BUILD_FLASH_ */
