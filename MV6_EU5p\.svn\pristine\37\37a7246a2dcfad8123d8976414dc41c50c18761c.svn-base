/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_GEARPOSCLUMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Enable Recovery gear [flag]
CALQUAL uint8_T ENRBGEAR =  0u;   // 0
//Recovery gear [status]
CALQUAL uint8_T RCGEAR =    2u;   //   2
//Speed thr stab [Km/h]
CALQUAL uint16_T THRCLUTCHRB = 16u;   //(  1.0000*16)
//Speed tim stab [ms]
CALQUAL uint8_T TIMCLUTCHRB =   15u;   //  15

#endif /* _BUILD_GEARPOSCLUMGM_ */

