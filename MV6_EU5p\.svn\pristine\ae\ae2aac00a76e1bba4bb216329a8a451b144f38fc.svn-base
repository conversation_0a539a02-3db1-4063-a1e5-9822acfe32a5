#ifndef _OS_API_H_
#define _OS_API_H_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/    
#include "typedefs.h"
#include "OS_errors.h"


/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/*  Constants */
#define UNKNOWN         ((TaskStateType)0xFF)/* Constant of data type TaskStateType for non existent task.   */
#define RUNNING         ((TaskStateType)0x0) /* Constant of data type TaskStateType for task state running.  */
#define WAITING         ((TaskStateType)0x1) /* Constant of data type TaskStateType for task state waiting.  */
#define READY           ((TaskStateType)0x2) /* Constant of data type TaskStateType for task state ready.    */
#define SUSPENDED       ((TaskStateType)0x3) /* Constant of data type TaskStateType for task state suspended.*/
        
#define NULLTASK        0xFF
        
/* Public OSEK types */
#define STATUSTYPEDEFINED                       /* required by OSEK/VDX Binding Specification */
#define E_OK                (StatusType)0       /* No error, successful completion  */
#define OSNUMTSKS            92                 /* number of app tasks 	*/
    
#define OSAPPMODE    0x00
#define OSBOOTMODE   0x01

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
#define DeclareTask(TaskId)     void Func##TaskId(void)
#define OSTASKENTRY(taskId)     (OsTaskTable[taskId].entry)
#define OSTASKPRI(taskId)       (OsTaskTable[taskId].pri)
#define OSTASKSTATUS(taskId)    (OsTaskTable[taskId].status)
#define OSTASKSTACKPTR(taskId)  (OsTaskTable[taskId].stkPtr)
#define OSTASKID(taskId)        (OsTaskTable[taskId].tskId)
#define INTC_CUR_PRI()          (INTC.CPR.B.PRI)
#define FIFO_QUEUE_ID()			(INTC_CUR_PRI()-1)
/***  macros to handle service watch  ***/
/* set specified service identifier     */
#define OSrtiSetServiceWatch( ServiceId )    OsrtiRunningServiceId = (ServiceId) | 1
/* set service watch on leaving service */
#define OSrtiServiceWatchOnExit( ServiceId ) OsrtiRunningServiceId = ServiceId                                                
/* remember current service ID          */
#define OSrtiSetOldServiceID( ServiceId )    OsrtiOldServiceId = ServiceId

/* restore ID on exit                   */
#define OSrtiResetIdOnExit()                   \
if( OsrtiOldServiceId )                        \
{                                              \
    OsrtiRunningServiceId = OsrtiOldServiceId; \
    OsrtiOldServiceId = 0;                     \
}


/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef void    (*TASKENTRY)( void );
typedef unsigned char       TaskType;
typedef TaskType            *TaskRefType;
typedef unsigned char       TaskStateType;
typedef TaskStateType       *TaskStateRefType;


typedef unsigned char       StatusType;         /* OSEK: Status type             */



/* Task block structure */
struct TagTaskCfg
{
    /*  Configuration Data  */
    TASKENTRY        entry;            /* entry point of task */
    TaskType         tskId;            /* task id (task number in task table)  */
    uint8_t            pri;            /* task priority */
};

/* Task control block structure */
struct TagTaskCBS
{
    /*  Configuration Data  */
    TASKENTRY   entry;      /* entry point of task */
    TaskType    tskId;      /* task id (task number in task table)  */
    uint8_t     status;     /* task status   */
    uint8_t     pri;        /* task priority */
    uint32_t    stkPtr;     /* stack pointer value */ 
};

/* Tasks */
typedef struct  TagTaskCfg     TaskCfg;    /* Task configuration table */
typedef         TaskCfg       *TaskCfgPTR; /* Pointer to the task configuration table */

/* Tasks */
typedef struct  TagTaskCBS     TaskCBS;    /* Task table */
typedef         TaskCBS       *TagCBSPTR;  /* Pointer to the task configuration table */


typedef unsigned char      AppModeType;       /* OSEK: Application mode type  */


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern const TaskCfg   OsTaskCfgTable[OSNUMTSKS];
extern TaskCBS  OsTaskTable[OSNUMTSKS];         /* Tasks table     */


/*----------- Error handling section -----------------------*/
/* declaration of service watch: */
/* run-time variable containing the idenfifier of */
/* currently running OSEK OS service */
extern  vuint8_t  OsrtiRunningServiceId;
extern  vuint8_t  OsrtiOldServiceId;
extern  OSSERVICEIDTYPE OsService;          /* for OSErrorGetServiceId() from ErrorHook */

extern uint32_t terminationAddress;
extern StatusType runningTaskId;



/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
 /*--------------------------------------------------------------------------*
 * StartOS - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void StartOS ( AppModeType mode );

 /*--------------------------------------------------------------------------*
 * ShutdownOS - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ShutdownOS(StatusType error);

 /*--------------------------------------------------------------------------*
 * ShutdownOSerrorHandler - This method handle different types of shutdown, avoiding 
 *                          recursive calls of ShutdownOS function 
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ShutdownOSerrorHandler( StatusType error);
 
 /*--------------------------------------------------------------------------*
 * GetActiveApplicationMode - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 AppModeType GetActiveApplicationMode ( void );



#endif  /* _OS_API_H_ */
