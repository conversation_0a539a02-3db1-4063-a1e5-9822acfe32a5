#ifdef _BUILD_CENSORSHIP_RECOVERY_TEST_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "ssd_c90fl.h"
#include "sys.h"
#include "wdt.h"
    
#include "recovery_secure.h"
#include "..\include\recovery_test_private.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
extern SSD_CONFIG ssdConfig;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define CENSORSHIP_ADDRESS      (0x00FFFDE0)
#define CENSORSHIP_VALUE        (0x55AA55AA)
#define SERIAL_BOOT_ADDRESS     (0x00FFFDD8)
#define SERIAL_BOOT_ADDRESS1    (0x00FFFDDC)
#define SERIAL_BOOT_PWD1_RESET  (0xFFFFFFFF) //all bit zeroes should have to be tested!!!
#define SERIAL_BOOT_PWD2_RESET  (0xFFFFFFFF) //all bit zeroes should have to be tested!!!

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * RECOVERY_TEST_Enable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t RECOVERY_TEST_Enable(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static uint8_t censorshipRecoverySecured = 0;


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * RECOVERY_SECURE_Init - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint16_t RECOVERY_SECURE_Init(void)
{
    if(CENSORSHIPRECOVERY1 == 1)
    {
        RECOVERY_TEST_Enable(); // reset serial pwd and restore censorship
        censorshipRecoverySecured = 0;
    }
    else
    {
        // DO NOTHING!!! CALIB CENSORSHIPRECOVERY1 NOT SET!!!
        //censorshipRecoverySecured = 1;
    }

    return NO_ERROR;
}

 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * RECOVERY_TEST_Enable - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_t RECOVERY_TEST_Enable(void)
{
    const uint32_t serialPassword_buff[] =
    { 
        0xFFFFFFFF, 0xFFFFFFFF, SERIAL_BOOT_PWD1_RESET, SERIAL_BOOT_PWD2_RESET 
    };

    const uint32_t censorship_buff[] =
    {   CENSORSHIP_VALUE, CENSORSHIP_VALUE, 0xFFFFFFFF, 0xFFFFFFFF };
    
    int16_t returnVal = NO_ERROR;

    if (returnVal == NO_ERROR)
    {
        DisableAllInterrupts();
        WDT_Disable();
        SetLock( &ssdConfig, LOCK_SHADOW_PRIMARY, 0, FLASH_LMLR_PASSWORD );
        SetLock( &ssdConfig, LOCK_SHADOW_SECONDARY, 0, FLASH_SLMLR_PASSWORD );
        //if(*((uint32_t*)CENSORSHIP_ADDRESS) != 0xFFFFFFFF)
        //{
            FlashErase( &ssdConfig, 1, 0, 0, 0, (void(*)(void))NULL_CALLBACK );
        //}
        // resetting serial password                
        // FlashProgram(&ssdConfig,(SERIAL_BOOT_ADDRESS - 0x8),sizeof(serialPassword_buff),(uint32_t)serialPassword_buff,(void(*)(void))NULL_CALLBACK);
        // programming censorship word
        FlashProgram(&ssdConfig,CENSORSHIP_ADDRESS,sizeof(censorship_buff),(uint32_t)censorship_buff,(void(*)(void))NULL_CALLBACK);
        SetLock( &ssdConfig, LOCK_SHADOW_PRIMARY, 1, FLASH_LMLR_PASSWORD );
        SetLock( &ssdConfig, LOCK_SHADOW_SECONDARY, 1, FLASH_SLMLR_PASSWORD );
        EnableAllInterrupts();
        WDT_Config(); // WDT enable

    }
    return returnVal;
}


#endif /* _BUILD_CENSORSHIP_RECOVERY_TEST_ */
