/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL$ */
/* $Description:  $ */
/* $Revision$ */
/* $Date$ */
/* $Author$ */
/*****************************************************************************************************************/

#ifndef RTW_HEADER_awheeling_ctrl_h_
#define RTW_HEADER_awheeling_ctrl_h_
#include "rtwtypes.h"

extern int16_T CmiAwI;                        /* AntiWheeling Torque I */
extern int16_T CmiAwP;                        /* AntiWheeling Torque P */

/** public functions **/
extern void AWheelingCtrl_NoSync(void);
extern void AWheelingCtrl_Init(void);
extern void AWheelingCtrl_T10ms(void);

#endif                                 /* RTW_HEADER_trac_ctrl_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */

