/**
 ******************************************************************************
 **  Filename:      C:\localmodules\local_TrqEst\slprj\ert\_sharedutils\div_u32.h
 **  Date:          22-May-2019
 **
 **  Model Version: 1.1900
 ******************************************************************************
 **/

#ifndef SHARE_div_u32
#define SHARE_div_u32

extern uint32_T div_u32(uint32_T numerator, uint32_T denominator);

#endif

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 8.0 (R2012b)20-Jul-2012                                             *
 * Simulink 8.0 (R2012b)20-Jul-2012                                           *
 * Simulink Coder 8.3 (R2012b)20-Jul-2012                                     *
 * Embedded Coder 6.3 (R2012b)20-Jul-2012                                     *
 * Stateflow 8.0 (R2012b)20-Jul-2012                                          *
 * Simulink Fixed Point 7.2 (R2012b)20-Jul-2012                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed-point_blocks                                                         *
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
