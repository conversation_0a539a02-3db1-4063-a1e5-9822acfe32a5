/*
 * File: CmeFilterMgm_types.h
 *
 * Code generated for Simulink model 'CmeFilterMgm'.
 *
 * Model version                  : 1.2383
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar 27 14:58:16 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (19), Warnings (6), Errors (8)
 */

#ifndef RTW_HEADER_CmeFilterMgm_types_h_
#define RTW_HEADER_CmeFilterMgm_types_h_

#include "diagmgm_out.h"

/*
 * Registered constraints for dimension variants
 */
/* Constraint 'DIAG_VEHSPEED > 0' registered by:
 * '<S24>/CmeDriverI_Offset'
 * '<S28>/GasResp_calc'
 */
#if DIAG_VEHSPEED <= 0
# error "The preprocessor definition 'DIAG_VEHSPEED' must be greater than '0'"
#endif

/* Constraint 'DIAG_VEH_CAN_NODE_3 > 0' registered by:
 * '<S24>/CmeDriverI_Offset'
 */
#if DIAG_VEH_CAN_NODE_3 <= 0
# error "The preprocessor definition 'DIAG_VEH_CAN_NODE_3' must be greater than '0'"
#endif

/* Constraint 'DIAG_VEHSPEED < 2147483647' registered by:
 * '<S24>/CmeDriverI_Offset'
 * '<S28>/GasResp_calc'
 */
#if DIAG_VEHSPEED >= 2147483647
# error "The preprocessor definition 'DIAG_VEHSPEED' must be less than '2147483647'"
#endif

/* Constraint 'DIAG_VEH_CAN_NODE_3 < 2147483647' registered by:
 * '<S24>/CmeDriverI_Offset'
 */
#if DIAG_VEH_CAN_NODE_3 >= 2147483647
# error "The preprocessor definition 'DIAG_VEH_CAN_NODE_3' must be less than '2147483647'"
#endif
#endif                                 /* RTW_HEADER_CmeFilterMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
