/*
 * File: GasPosMgm_private.h
 *
 * Code generated for Simulink model 'GasPosMgm'.
 *
 * Model version                  : 1.898
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb  1 14:21:31 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GasPosMgm_private_h_
#define RTW_HEADER_GasPosMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "GasPosMgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "mathlib.h"
#include "recmgm.h"
#include "thrposmgm.h"
#include "canmgm.h"
#include "diagcanmgm.h"
#include "ptrain_diag.h"
#include "GearPosClu_Mgm.h"
#include "digitalin.h"
#include "analogin.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T FORCEGASTIMEHIST;       /* Variable: FORCEGASTIMEHIST
                                        * Referenced by: '<S13>/Calc_Final_GasPos'
                                        * Force GasPos time history
                                        */
extern int16_T GASPOSGAIN[2];          /* Variable: GASPOSGAIN
                                        * Referenced by: '<S20>/GASPOSGAIN'
                                        * (SR) Gain of the acc pos characteristic
                                        */
extern int16_T FORCEGASPOS;            /* Variable: FORCEGASPOS
                                        * Referenced by: '<S13>/Calc_Final_GasPos'
                                        * Force GasPos value
                                        */
extern int16_T GASNEGPERC;             /* Variable: GASNEGPERC
                                        * Referenced by: '<S20>/GASNEGPERC'
                                        * Negative gas threshold
                                        */
extern int16_T THGASPOSCOH;            /* Variable: THGASPOSCOH
                                        * Referenced by: '<S18>/THGASPOSCOH'
                                        * (SR) Threshold for coherence gas pos diag
                                        */
extern int16_T THGASPOSNOTCOH;         /* Variable: THGASPOSNOTCOH
                                        * Referenced by: '<S18>/THGASPOSNOTCOH'
                                        * (SR) Threshold for incoherence gas pos diag
                                        */
extern int16_T SATMAXPERCGAS;          /* Variable: SATMAXPERCGAS
                                        * Referenced by: '<S20>/Saturation'
                                        * (SR) Threshold for coherence gas pos diag
                                        */
extern int16_T GASPOSOFFSET[2];        /* Variable: GASPOSOFFSET
                                        * Referenced by: '<S20>/GASPOSOFFSET'
                                        * (SR) Basic offset of the acc pos characteristic
                                        */
extern uint16_T BKTIMHISTGAS[24];      /* Variable: BKTIMHISTGAS
                                        * Referenced by: '<S24>/BKTIMHISTGAS'
                                        * Breakpoints for Gaspos time history for the EOL
                                        */
extern uint16_T GPOSSLEW2ZERO;         /* Variable: GPOSSLEW2ZERO
                                        * Referenced by: '<S13>/Calc_Final_GasPos'
                                        * Step to slew GasPos to 0 when rec is active
                                        */
extern uint16_T GPOSSLEW2TRG;          /* Variable: GPOSSLEW2TRG
                                        * Referenced by: '<S13>/Calc_Final_GasPos'
                                        * Step to slew GasPos to traget when rec is active
                                        */
extern uint16_T THLOW1GASSWDIA;        /* Variable: THLOW1GASSWDIA
                                        * Referenced by: '<S23>/THLOW1GASSWDIA'
                                        * (SR) Lower 2nd threshold for electric gas switch diag
                                        */
extern uint16_T THLOW2GASSWDIA;        /* Variable: THLOW2GASSWDIA
                                        * Referenced by: '<S23>/THLOW2GASSWDIA'
                                        * (SR) Lower 2nd threshold for electric gas switch diag
                                        */
extern uint16_T THLOWGASPOSDIA;        /* Variable: THLOWGASPOSDIA
                                        * Referenced by: '<S19>/THLOWGASPOSDIA'
                                        * (SR) Lower threshold for electric gas pos diag
                                        */
extern uint16_T THUPP1GASSWDIA;        /* Variable: THUPP1GASSWDIA
                                        * Referenced by: '<S23>/THUPP1GASSWDIA'
                                        * (SR) Lower 2nd threshold for electric gas switch diag
                                        */
extern uint16_T THUPP2GASSWDIA;        /* Variable: THUPP2GASSWDIA
                                        * Referenced by: '<S23>/THUPP2GASSWDIA'
                                        * (SR) Lower 2nd threshold for electric gas switch diag
                                        */
extern uint16_T THUPPGASPOSDIA;        /* Variable: THUPPGASPOSDIA
                                        * Referenced by: '<S19>/THUPPGASPOSDIA'
                                        * (SR) Upper threshold for electric gas pos diag
                                        */
extern uint16_T THVGASIDLE1;           /* Variable: THVGASIDLE1
                                        * Referenced by: '<S41>/THVGASIDLE1'
                                        * (SR) VGasPos1 thr for idle switch coeherence
                                        */
extern uint16_T THVGASIDLE2;           /* Variable: THVGASIDLE2
                                        * Referenced by: '<S42>/THVGASIDLE2'
                                        * (SR) VGasPos2 thr for idle switch coeherence
                                        */
extern uint16_T THVGASOUTIDLE1;        /* Variable: THVGASOUTIDLE1
                                        * Referenced by: '<S41>/THVGASOUTIDLE1'
                                        * (SR) VGasPos1 thr for idle switch coeherence
                                        */
extern uint16_T THVGASOUTIDLE2;        /* Variable: THVGASOUTIDLE2
                                        * Referenced by: '<S42>/THVGASOUTIDLE2'
                                        * (SR) VGasPos2 thr for idle switch coeherence
                                        */
extern uint8_T VTTIMHISTGAS[24];       /* Variable: VTTIMHISTGAS
                                        * Referenced by: '<S24>/VTTIMHISTGAS'
                                        * Gaspos time history for the EOL
                                        */
extern uint8_T DBW;                    /* Variable: DBW
                                        * Referenced by: '<S11>/Choose'
                                        * Configure DBW (=0 no dbw; =1 dbw)
                                        */
extern uint8_T DEFMASTSENS;            /* Variable: DEFMASTSENS
                                        * Referenced by:
                                        *   '<S10>/DEFMASTSENS'
                                        *   '<S13>/Calc_Recovery'
                                        * Default master sensor (1 or 2)
                                        */
extern uint8_T ENSTOPCREEPING;         /* Variable: ENSTOPCREEPING
                                        * Referenced by: '<S21>/ENSTOPCREEPING'
                                        * Enable Stop creeping flag
                                        */
extern uint8_T FORCEGASSENS;           /* Variable: FORCEGASSENS
                                        * Referenced by:
                                        *   '<S13>/Calc_Final_GasPos'
                                        *   '<S13>/Calc_Recovery'
                                        * Force used gas sensor:  0=no gas forced; 1=GasPos1; 2=GasPos2
                                        */
extern uint8_T GPNEGWITHIDLE;          /* Variable: GPNEGWITHIDLE
                                        * Referenced by:
                                        *   '<S20>/GPNEGWITHIDLE'
                                        *   '<S23>/GPNEGWITHIDLE'
                                        * Enable negative gas flag
                                        */
extern void GasPo_IdleSW_VGasPos1_Func_Diag(uint8_T rtu_StIdleSwitch, uint16_T
  rtu_vgaspos, rtB_IdleSW_VGasPos1_Func_Diag_G *localB);
extern void GasPo_IdleSW_VGasPos2_Func_Diag(uint8_T rtu_StIdleSwitch, uint16_T
  rtu_vgaspos, rtB_IdleSW_VGasPos2_Func_Diag_G *localB);
extern void GasPosMgm_timehist_gas_Reset(void);
extern void GasPosMgm_timehist_gas(void);
extern void GasPosM_Calc_Final_GasPos_Reset(void);
extern void GasPos_Calc_Final_GasPos_Enable(void);
extern void GasPosMgm_Calc_Final_GasPos(void);
extern void GasPosMgm_Calc_delta_Reset(void);
extern void GasPosMgm_Calc_delta(void);
extern void GasPosMgm_dbw_Reset(void);
extern void GasPosMgm_dbw_Enable(void);
extern void GasPosMgm_dbw(void);
extern void GasPosMgm_T10ms_Enable(void);
extern void GasPosMgm_T10ms(void);
extern void GasPosMgm_Init(void);

#endif                                 /* RTW_HEADER_GasPosMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
