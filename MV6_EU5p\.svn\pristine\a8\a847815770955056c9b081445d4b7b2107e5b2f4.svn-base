/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "diagmgm_out.h"    
#include "cpumgm.h"
#include "sys.h"
#include "eemgm.h"
#include "ee.h"


#ifdef  MATLAB_MEX_FILE
#define _BUILD_CPUMGM_
#endif

#ifdef  _BUILD_CPUMGM_

extern uint8_T THRERRCPU;   /* CPUMGM */
extern uint8_T GenIvorCnt;  /* CPUMGM */

uint8_T  PtFaultCpu = NO_PT_FAULT;
uint8_t     PowerOnType = PWON_UNKNOWN;
uint8_t     ResetType;

void CPUMGM_Init(void)
{
    uint8_T   StDiagCpu = NO_FAULT;
    ResetType = 0xff;

    SYS_GetLastReset(&ResetType);
    
    switch(ResetType)
    {
      case POWERONRESET:
        PowerOnType = PWON_NORMAL;
        break;
      case EXTERNALRESET:
        PowerOnType = PWON_EXCEPTION;
        break;
      case LOSSOFLOCKRESET:
        PowerOnType = PWON_EXCEPTION;
        ++GenIvorCnt;
        EEMGM_SetEventID(EE_PWON_AFTER_RESET_CORE);
        EEMGM_EETaskCmd();
        break;
      case LOSSOFCLOCKRESET:
        PowerOnType = PWON_EXCEPTION;
        ++GenIvorCnt;
        EEMGM_SetEventID(EE_PWON_AFTER_RESET_CORE);
        EEMGM_EETaskCmd();
        break;
      case WATCHDOGRESET:
        PowerOnType = PWON_EXCEPTION;
        break;
      case CHECKSTOPRESET:
        PowerOnType = PWON_EXCEPTION;
        ++GenIvorCnt;
        EEMGM_SetEventID(EE_PWON_AFTER_RESET_CORE);
        EEMGM_EETaskCmd();
        break;
      case SOFTWARESYSTEMRESET:
        PowerOnType = PWON_EXCEPTION;
        break;
      case SOFTWAREEXTERNALRESET:
        PowerOnType = PWON_EXCEPTION;
        EEMGM_SetEventID(EE_PWON_AFTER_RESET_CORE);
        EEMGM_EETaskCmd();
        break;
      default:
        PowerOnType = PWON_UNKNOWN;
        break;
    }

    if (GenIvorCnt >= THRERRCPU)
    {
        PtFaultCpu = CIRCUIT_MALFUNCTION;
    }
    DiagMgm_SetDiagState(DIAG_CPU, PtFaultCpu, &StDiagCpu);
}

#endif // _BUILD_CPUMGM_
