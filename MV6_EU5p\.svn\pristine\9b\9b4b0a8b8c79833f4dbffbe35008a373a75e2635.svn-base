/*
 * File: TracCtrl_data.c
 *
 * Code generated for Simulink model 'TracCtrl'.
 *
 * Model version                  : 1.825
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 13:03:51 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "TracCtrl.h"
#include "TracCtrl_private.h"

/*
 * Invariant block signals and block parameters
 * for system '<S1>/T10ms'
 */
const rtC_T10ms_TracCtrl
  TracCtrl_T10ms_C = {
  /* Start of '<S9>/fc_TC_AccWheelCalc' */
  {
    { 0U, 1U, 2U }                     /* '<S24>/Data Type Conversion' */
  }
  /* End of '<S9>/fc_TC_AccWheelCalc' */
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
