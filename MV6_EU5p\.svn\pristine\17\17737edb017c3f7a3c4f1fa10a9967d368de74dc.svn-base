#!gbuild
[Subproject]
	--misra_2004=16.2
DD\EXHVALMGM\SBS\ExhValMgm_SBS_data.c
DD\EXHVALMGM\SBS\ExhValMgm_SBS_calib.c
DD\EXHVALMGM\SBS\ExhValMgm_SBS.c
DD\EXHVALMGM\MIKUNI\ExhValMgm_data.c
DD\EXHVALMGM\MIKUNI\ExhValMgm_calib.c
DD\EXHVALMGM\MIKUNI\ExhValMgm.c
DD\EXHVALPWM\ExhValPwm_calib.c
DD\EXHVALPWM\ExhValPwm.c
DD\FOINJCTFMGM\FOInjCtfMgm_calib.c
DD\FOINJCTFMGM\FOInjCtfMgm.c
	--misra_2004=-15.3
DD\ANTITAMPERING\antiTampering.c
DD\ANTITAMPERING\antiTampering_calib.c
DD\HEATGRIPDRIVEMGM\HeatGripDriveMgm_calib.c
DD\HEATGRIPDRIVEMGM\HeatGripDriveMgm.c
DD\GEARPOSCLUMGM\GearPosCluMgm_calib.c
DD\GEARPOSCLUMGM\GearPosCluMgm.c
DD\ANALOGQS\analogQS_calib.c
DD\ANALOGQS\AnalogQS.c
DD\DIAGCANMGM\censorship_recovery_ssd.c
DD\DIGITALIN\digitalin_calib.c
DD\ANALOGIN\analogin.c
DD\ANALOGIN\analogin_calib.c
DD\CCP\src\ccp.c
DD\CCP\src\ccp_can_interface.c
DD\CCP\src\ccptxdata.c
DD\CPUMGM\cpumgm.c
DD\CPUMGM\cpumgm_calib.c
DD\TESTIO\TestIO.c
DD\LAMHEATERMGM\lamheatermgm.c
DD\LAMHEATERMGM\lamheatermgm_calib.c
DD\SAF2MODULES\saf2modules_calib.c
DD\DIAGCANMGM\Rli.c
DD\DIAGCANMGM\Dtc.c
DD\DIAGCANMGM\Active_Diag_calib.c
DD\DIAGCANMGM\Active_Diag.c
DD\DIAGCANMGM\Kwp2000Main.c
DD\DIAGCANMGM\Kwp2000_ll.c
	--misra_2004=-14.1
DD\DIAGCANMGM\Kwp2000_calib.c
DD\DIAGCANMGM\DiagCanMgm_IdOption.c
DD\DIAGCANMGM\DiagCanEE.c
DD\DIAGCANMGM\OBDMain.c
DD\DIAGCANMGM\OBD_ll.c
DD\DBWMGM\dbwmgm.c
DD\DBWMGM\dbwmgm_calib.c
DD\GEARMGM\gearmgm.c
DD\GEARMGM\gearmgm_calib.c
DD\DIAGMGM\diagmgm.c
DD\DIAGMGM\diagmgm_calib.c
DD\DIGITALIN\digitalin.c
DD\DIGITALOUT\digitalout.c
DD\EEMGM\src\ee_ID0.c
DD\EEMGM\src\ee_ID1.c
DD\EEMGM\src\ee_ID2.c
DD\EEMGM\src\ee_ID3.c
DD\EEMGM\src\ee_ID4.c
DD\EEMGM\src\ee_ID5.c
DD\EEMGM\src\ee_ID6.c
DD\EEMGM\src\ee_ID7.c
DD\EEMGM\src\ee_ID8.c
DD\EEMGM\src\ee_ID9.c
DD\EEMGM\src\ee_ID10.c
DD\EEMGM\src\eemgm.c
DD\FLASHMGM\flashmgm.c
DD\FLASHMGM\flashmgm_calib.c
DD\GASPOSMGM\gasposmgm.c
DD\GASPOSMGM\gasposmgm_calib.c
DD\GASPOSFILTMGM\GasPosFiltMgm_calib.c
DD\GASPOSFILTMGM\GasPosFiltMgm.c
DD\HBRIDGE\HB_COMMON.c
DD\IGNCMD\igncmd.c
DD\IGNCMD\igncmd_calib.c
DD\INJCMD\injcmd.c
DD\INJCMD\injcmd_calib.c
DD\INTSRCMGM\INTSRCMGM.C		[C]
DD\IONACQ\ionacq.c
DD\IONACQ\ionacq_calib.c
DD\KLINE\kline_utils.c
DD\KLINE\kline_timer.c
DD\KLINE\kline_init.c
DD\KLINE\kline_frame.c
DD\KLINE\kline_exception.c
DD\KLINE\kline_api.c
DD\LAMBDAMGM\lambdamgm.c
DD\LAMBDAMGM\lambdamgm_calib.c
DD\LINMGM\linmgm.c
DD\LINMGM\linmgm_tl.c
DD\PHASEMGM\Src\phasemgm.c
DD\PHASEMGM\Src\phasemgm_calib.c
DD\PWRMGM\pwrmgm.c
DD\PWRMGM\pwrmgm_calib.c
DD\PITTEST\src\PIT_Test.c
DD\PWRSPLY\pwrsply.c
DD\RECMGM\recmgm.c
DD\RECMGM\recmgm_calib.c
DD\RECOVERYTEST\src\recovery_test_calib.c
DD\RECOVERYTEST\src\recovery_test.c
DD\RELAYMGM\src\relaymgm.c
DD\RELAYMGM\src\relaymgm_calib.c
DD\SAF2MGM\Saf2Mgm.c
DD\SAF2MGM\Saf2Mgm_calib.c
DD\SAF3MGM\smp_code_qc16.c
DD\SAF3MGM\dummy_smp_code_qc16.c
DD\SAF3MGM\Saf3_MMP.c
DD\SAF3MGM\Saf3_MMP_SPIProcom.c
DD\SAF3MGM\Saf3Mgm_calib.c
DD\SCIMGM\SCIMGM.c
DD\SECURE\src\secure.c
DD\SECURE\src\secure_calib.c
DD\SPIMGM\SPIMGM.c
DD\SPIGAIN\SPIGain.c
DD\SPIGAIN\SPIGain_calib.c
DD\SPITLE6244X\SPITLE6244X_calib.c
DD\SPITLE6244X\SPITLE6244X.c
DD\STEPPERCMD\src\steppercmd.c
DD\STEPPERCMD\src\steppercmd_calib.c
DD\TACH_CONS\tach_cons.c
DD\TACH_CONS\tachcons_calib.c
DD\TEMPMGM\TempMgm.c
DD\TEMPMGM\TempMgm_calib.c
DD\THRPOSMGM\src\thrposmgm.c
DD\THRPOSMGM\src\thrposmgm_calib.c
DD\TPE\tpe_api.c
DD\TPE\tpe_exception.c
DD\TPE\tpe_mngr.c
DD\TPE\tpe_utils.c
DD\UARTMGM\uartmgm.c
DD\VCALIB\src\vcalib.c
DD\VCALIB\src\vcalib_callback.c
DD\VSPEEDMGM\vspeedmgm.c
DD\VSPEEDMGM\vspeedmgm_calib.c
DD\VSRAMMGM\src\vsrammgm.c
DD\VSRAMMGM\src\vsram_shared_content.c
DD\WATTEMPMGM\src\WatTempMgm.c
DD\WATTEMPMGM\src\WatTempMgm_calib.c
	-Ospeed
DD\VSRAMMGM\src\Vsram_shared_IO.c
DD\VSRAMMGM\src\vsram_content.c
DD\VSRAMMGM\src\vsram_checksum.c
DD\WATPUMPMGM\src\watpumpmgm.c
DD\WATPUMPMGM\src\watpumpmgm_calib.c
DD\LAMPMGM\src\lampmgm.c
DD\LAMPMGM\src\lampmgm_calib.c
DD\PIOTESTNG\src\piotesting.c
DD\ANGLETRIGTEST\src\angleTrigTest.c


