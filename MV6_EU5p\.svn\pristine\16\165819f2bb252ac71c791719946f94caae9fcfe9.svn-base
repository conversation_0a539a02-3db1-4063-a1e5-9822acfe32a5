#include "ETPU_EngineTypes.h"

/* Engine configuration */
#define ENGINE_TYPE             (MV_AGUSTA_3C_TDC_0_20)

/*Time Base Configuration*/
#define MS_1    1
#define MS_5    5
#define _TIME_BASE_ (MS_1)

#define _TEST_TIMING_
#define _TEST_ISR_TIMING_

/* Target Configuration */
#define TARGET_TYPE             (MPC5634)
#define FSYS_80                 (1)
#define SMP_TARGET_TYPE         (QC16)

/* Board Configuration */
#define BOARD_TYPE              (BOARD_M3)

#define APP_TAG_PREF        "M3A36R-" 

#define _BUILD_APP_
#define _BUILD_DD_

#define _TARGET_R_

/* Precompiler defines for BIOS modules inclusion */
#define  _BUILD_ADC_
#define  _BUILD_CAN_
#define  _BUILD_DIGIO_
#define  _BUILD_DMA_
#define  _BUILD_FLASH_
#define  _BUILD_EEPROM_
#define  _BUILD_UTILS_
#define  _BUILD_MATHLIB_
#define  _BUILD_NEXUS_
#define  _BUILD_PIO_
#undef  _BUILD_EXTIRQ_
#define  _BUILD_SCI_
#define  _BUILD_SPI_   
#define  _BUILD_SYNC_
#define  _BUILD_SYS_
#define  _BUILD_TASK_
#define  _BUILD_TIMING_
#define  _BUILD_WDT_
#define  _BUILD_INJ_
#define  _BUILD_IGN_
#undef  _BUILD_PHASE_
#undef  _BUILD_UART_
#undef  _BUILD_LIN_
#define _BUILD_VSRAM_
#undef  _BUILD_MMU_
#define _BUILD_RECOVERY_
#define _BUILD_EMIOS_
#define _BUILD_WS_
#define _BUILD_PIT_

/* Precompiler defines for DD modules inclusion */
#ifdef _BUILD_DD_
#define _BUILD_ANALOGIN_
#define _BUILD_ANALOGQS_
#define _BUILD_ANTITAMPERING_
#define _BUILD_CANMGM_
#undef _BUILD_CCP_
#define _BUILD_IONACQ_
#define _BUILD_SPIMGM_
#define _BUILD_SPITLE6244X_
#define _BUILD_SPIGAIN_
#undef _BUILD_PWRSPLY_
#undef  _BUILD_SCIMGM_
#define _BUILD_SYNCMGM_
#define _BUILD_DIAGMGM_
#define _BUILD_RECMGM_
#define _BUILD_DIGITALIN_
#define _BUILD_DIGITALOUT_
#define _BUILD_CPUMGM_
#define _BUILD_PWRMGM_
#define _BUILD_RELAYMGM_
#define _BUILD_LAMPMGM_
#define _BUILD_VSRAMMGM_
#define _BUILD_EEMGM_
#define _BUILD_INTSRCMGM_
#define _BUILD_INJCMD_
#define _BUILD_IGNCMD_
#define _BUILD_HBRIDGE_
#undef _BUILD_LSD_
#define _BUILD_DBWMGM_
#define _BUILD_EXHVALMGM_
#define _BUILD_GEARMGM_
#undef _BUILD_PHASEMGM_
#define _BUILD_GASPOSFILTMGM_
#define _BUILD_GASPOSMGM_
#define _BUILD_THRPOSMGM_
#undef _BUILD_STEPPERCMD_
#define  _BUILD_SAF3MGM_
#undef _SMP_DUMMY_APPLICATION_ // dummy!!
#undef _BUILD_UARTMGM_
#undef _BUILD_LINMGM_
#undef  _BUILD_SAF2MGM_
#undef _BUILD_SAF2MODULES_
#undef  _BUILD_S2_STORE_RESULTS_
#define _BUILD_TEMPMGM_
#define _BUILD_VSPEEDMGM_
#define _BUILD_DIAGCANMGM_
#define _BUILD_TPE_
#undef _BUILD_KLINE_
#define _BUILD_RLI_
#define _BUILD_DTC_
#define _BUILD_OBD_
#define _BUILD_ACTIVE_DIAG_
#undef _BUILD_WATPUMPMGM_
#undef _BUILD_WATTEMPMGM_
#undef  _BUILD_SECURE_
#define _BUILD_LAMHEATERMGM_
#define _BUILD_LAMBDAMGM_
#define _BUILD_FLASHMGM_
#undef  _BUILD_VCALIB_
#undef  _BUILD_PIOTEST_
#define _BUILD_TESTIO_
#undef  _BUILD_ANGTRIGTEST_
#define _BUILD_HEATGRIP_DRIVEMGM_
/* usage: reset of serial flash password @0x00ff_fdd8 */
#undef _BUILD_CENSORSHIP_RECOVERY_TEST_
/* usage: activate MICROTECH canmgm simulation */
#define _BUILD_FOINJCTFMGM_
#define _BUILD_SPEEDLIMCTRL_
#define _BUILD_EXHVALPWM_
#endif /* _BUILD_DD_ */
//mi: for PIO testing

#ifdef _BUILD_PIOTEST_

#undef _BUILD_IGNCMD_
#undef _BUILD_INJCMD_
#undef _BUILD_RLI_
#undef _BUILD_ACTIVE_DIAG_
#undef _BUILD_RELAYMGM_
#undef _BUILD_LAMBDAMGM_
#undef _BUILD_LAMHEATERMGM_
#undef _BUILD_THRPOSMGM_
#define _BUILD_DIAGCANMGM_
#undef _BUILD_DTC_
#undef _BUILD_IONACQ_
#undef _BUILD_APP_
#define _BUILD_PATMMODEL_

#endif

/* usage: it intenionally set trigger for 450 usec FIFO to 20 usec to cause TORF interrupt */
#undef _BUILD_TORF_RECOVERY_

/* Precompiler defines for APPLICATION modules inclusion */
#ifdef _BUILD_APP_
#define _BUILD_LOADMGM_
#undef _BUILD_ADDAIRMGM_
#define _BUILD_IONKNOCK_
#undef _BUILD_IONLAMBDA_
#undef _BUILD_IONLAMBDAANN_
#define _BUILD_IONMGM_
#define _BUILD_IONMISF_
#define _BUILD_KNOCKCORR_
#define _BUILD_SPARKMGM_
#define _BUILD_SABASICMGM_
#define _BUILD_AIRMGM_
#define _BUILD_AIRDIAGMGM_
#define _BUILD_TRQEST_
#define _BUILD_TRQDRIVER_
#define _BUILD_PBYMGM_
#define _BUILD_IDLECTFMGM_
#define _BUILD_TRQDRIVMGM_
#define _BUILD_CMEFILTERMGM_
#define _BUILD_CMIDRIVERMGM_
#define _BUILD_CMISATMGM_
#define _BUILD_TRACCTRL_
#undef _BUILD_AWHEELINGCTRL_
#define _BUILD_TRQEXTREQ_
#define _BUILD_TRQSAFLIM_
#define _BUILD_IDLEMGM_
#define _BUILD_RPMLIMITER_
#define _BUILD_QAIRTARGETMGM_
#define _BUILD_PRESTARGET_
#define _BUILD_THROTTLEMODEL_
#define _BUILD_THROTTLETARGET_
#define _BUILD_THROTTLEADAPT_
#define _BUILD_PATMMODEL_
#define _BUILD_FUELMGM_
#define _BUILD_ENGFLAG_
#define _BUILD_AFCTRL_
#define SPE_OPTIMIZATION   /* WARNING: only direct transform implemented */
#define _BUILD_SELFMGM_
#define _BUILD_LIGHTOFFMGM_
#undef _BUILD_VSPEEDCTRL_
#define _BUILD_PTRAINDIAG_
#undef  _BUILD_IMMO_
#undef  _BUILD_IMMOLED_
#undef _BUILD_WATPUMPCTRL_
#define _BUILD_GEARSHIFTMGM_
#define _BUILD_GEARPOSCLUMGM_
#define _BUILD_CREEPLIMITERMGM_
#define _BUILD_LAUNCHCTRL_
#define _BUILD_CTRLACTIVE_
#define _BUILD_TRC2WZERO_
#define _BUILD_MISFOBD2_
#define _BUILD_IDXCTFCTRL_
#define _BUILD_IUPR_
#define _BUILD_DIAGFLAGS_
#define _BUILD_TRANSPORT_LOCK_
#endif /* _BUILD_APP_ */

#undef _CANDEBUG_  /* debug messages sent onto VEHICLE CAN */

/* target specific diagnosis */
#undef  EN_DIAG_POW_OAK
#undef  EN_DIAG_RPM

#define _OSEK_
#define _BUILD_CHECKSUMVALIDATION_
#undef _BUILD_DEVELOPMENT_ /* defined only in "Dev" configuration !!! */

#include "ETPU_EngineDefs.h"
#include "mpc563xm_config.h"

/****************************************************************************
     Peripherals defines 
 ****************************************************************************/
#include "ADC.cfg"
#include "CAN.cfg"
#include "CAN_MV_AGUSTA_3C.cfg"
#include "DIGIO.cfg"
#include "DIGIO_BOARD_M2.cfg"
#include "DMA.cfg"
#include "EXTIRQ.cfg"
#include "FLASH_MV5.cfg"
#include "PIO.cfg"
#include "PIO_MV_BOARD_M1.cfg"
#include "pit.cfg"
#include "SCI.cfg"
#include "SPI.cfg"
#include "SPI_MV_AGUSTA_M1.cfg"
#include "SYNC.cfg"
#include "SYNC_MV_BOARD_M1.cfg"
#include "SYS_MPC563x.cfg"
#include "TASK.cfg"
#include "TIMING.cfg"
#include "UART.cfg"
#include "WDT.cfg"
#include "INJ.cfg"
#include "IGN.cfg"
#include "PHASE.cfg"

