#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_SAF3MGM_

#pragma ghs section rodata=".calib" 


//(S3) Hysteresis on the threshold on Vbat to disable safety 3 [V]
__declspec(section ".calib") uint16_T S3DELTABATMMPOFF = 5;   //( 0.3125*16)
//(S3) Force right answer for safety 23 (=1) [flag]
__declspec(section ".calib") uint8_T S3FORCERIGHTANSW =  1;   // 1
//(S3) Max number of SMP flashing attempts [counter]
__declspec(section ".calib") uint8_T S3MAXSMPPRGRETRY =    3;   //   3
//(S3) Number of TDCs to detect stability of Rpm [TDC]
__declspec(section ".calib") uint16_T S3NUMTSTABRPM =     50;   //    50
//(S3) Rpm threshold to start safety 2 [rpm]
__declspec(section ".calib") uint16_T S3RPMSTARTS2 =   3900;   //  3900
//(S3) Correct answers of S23 [counter]
__declspec(section ".calib") uint16_T S3SAF2MODANSW[3*16] = 
{
    0,      1064,   16,     8,      0,      16,     0,      0,  160,    1,      303,    532,    1,      467,    532,    0,
    0,      1,      1064,   1064,   0,      0,      303,    0,  0,      303,    1,      0,      768,    0,      768,    768,
    1064,   8,      1,      0,      1064,   1064,   0,      0,  0,      0,      208,    1,      532,    532,    467,    532
};
//(S3) Threshold on Vbat to disable safety 3 [V]
__declspec(section ".calib") uint16_T S3THBATMMPOFF = 96;   //( 6.0000*16)
//(S3) Threshold on throttle position for DBW disable test [mV]
//(S3) Threshold on spi first comm err for safety 3 [counter]
__declspec(section ".calib") uint8_T S3THFIRSTCOMERR =   30;   //  30
//(S3) Threshold for the repeated rnd code counter [counter]
__declspec(section ".calib") uint16_T S3THRCNTREPCODE =      5;   //     5
//(S3) Threshold on S3MMPBufErrCnt to force a S3 engine stop [counter]
__declspec(section ".calib") uint8_T S3THRMMPBUFERRCNT =    5;   //   5
//(S3) Threshold on S3MMPCntErr to force a S3 engine stop [counter]
__declspec(section ".calib") uint8_T S3THRMMPCNTERR =    3;   //   3
//(S3) Threshold on spi comm err for safety 3 [counter]
__declspec(section ".calib") uint8_T S3THRSPIERR =    5;   //   5
//(S3) Threshold to detect stability for Rpm [%]
__declspec(section ".calib") uint16_T S3THRSTABRPM = 384;   //(  3.0000000*128)
//(S3) Threshold for ADC test [mV]
__declspec(section ".calib") uint8_T S3THRTESTADC = 20;   //( 392.156862745098070*0.051)
//(S3) Value of VKam to declare SMP active [mV]
__declspec(section ".calib") uint16_T S3TVKAMSMPACT = 0; //2046; //(   10000/5000*1023)
//(S3) Time to allow SMP starting the application [ms]
__declspec(section ".calib") uint16_T S3TWAPPLSMP = 10; //20;
//(S3) Time to allow SMP boot strap [ms]
__declspec(section ".calib") uint16_T S3TWBOOTSMP = 90; //150; //   220
//(S3) Time to allow SMP raise vkam pin [ms]
__declspec(section ".calib") uint16_T S3TWVKAMSMP = 90; //150; //  1000
//(S3) DBW voltage set point for DBW disable test [mV]
__declspec(section ".calib") uint8_T S3VTANSWRIGHT[16] = 
{
    15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
};

#endif // _BUILD_SAF3MGM_
