/*
 * File: CmiSatMgm_private.h
 *
 * Code generated for Simulink model 'CmiSatMgm'.
 *
 * Model version                  : 1.285
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu May 16 16:15:34 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_CmiSatMgm_private_h_
#define RTW_HEADER_CmiSatMgm_private_h_
#include "rtwtypes.h"
#include "CmiSatMgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "canmgm.h"
#include "gearshift_mgm.h"
#include "trq_est.h"
#include "launchctrl_out.h"
#include "cmidriver_mgm.h"
#include "gearposclu_mgm.h"
#include "gear_mgm.h"
#include "syncmgm.h"
#include "sabasic_mgm.h"
#include "ionacq.h"
#include "trac_ctrl.h"
#include "vspeed_mgm.h"
#include "ptrain_diag.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T VTCMIAWCTRLP[10];       /* Variable: VTCMIAWCTRLP
                                        * Referenced by: '<S35>/VTCMIAWCTRLP'
                                        * Cmi Aw ctrl P
                                        */
extern int16_T TBCMEEWTCSAT[63];       /* Variable: TBCMEEWTCSAT
                                        * Referenced by: '<S57>/TBCMEEWTCSAT'
                                        * Tc Sat
                                        */
extern int16_T VTCMEEWAWSAT[7];        /* Variable: VTCMEEWAWSAT
                                        * Referenced by: '<S51>/VTCMEEWAWSAT'
                                        * CmeEstWheelF saturation
                                        */
extern int16_T CMIMINAWSACTF;          /* Variable: CMIMINAWSACTF
                                        * Referenced by: '<S28>/CMIMINAWSACTF'
                                        * sat
                                        */
extern int16_T CMIOFSAWI;              /* Variable: CMIOFSAWI
                                        * Referenced by: '<S6>/St_Aw'
                                        * Offset
                                        */
extern int16_T HYSCMISATAWRET;         /* Variable: HYSCMISATAWRET
                                        * Referenced by: '<S6>/St_Aw'
                                        * Exit Aw strategy
                                        */
extern int16_T KDAWWEIGTHI;            /* Variable: KDAWWEIGTHI
                                        * Referenced by: '<S18>/KDAWWEIGTHI'
                                        * selector
                                        */
extern int16_T KDAWWEIGTHP;            /* Variable: KDAWWEIGTHP
                                        * Referenced by: '<S18>/KDAWWEIGTHP'
                                        * selector
                                        */
extern int16_T KPAWWEIGTHI;            /* Variable: KPAWWEIGTHI
                                        * Referenced by: '<S18>/KPAWWEIGTHI'
                                        * selector
                                        */
extern int16_T KPAWWEIGTHP;            /* Variable: KPAWWEIGTHP
                                        * Referenced by: '<S18>/KPAWWEIGTHP'
                                        * selector
                                        */
extern int16_T MAXRATEAWRET;           /* Variable: MAXRATEAWRET
                                        * Referenced by: '<S19>/MAXRATEAWRET'
                                        * max rete aw
                                        */
extern int16_T MINRATEAWRET;           /* Variable: MINRATEAWRET
                                        * Referenced by: '<S15>/MINRATEAWRET'
                                        * max rete aw
                                        */
extern int16_T OFFCMIGRUFILAW;         /* Variable: OFFCMIGRUFILAW
                                        * Referenced by: '<S6>/St_Aw'
                                        * Exit Aw GearUp
                                        */
extern int16_T VTAWOFSSATLEV[4];       /* Variable: VTAWOFSSATLEV
                                        * Referenced by: '<S8>/VTAWOFSSATLEV'
                                        * Offset sat level
                                        */
extern int16_T VTMRTDPSMOOTH[5];       /* Variable: VTMRTDPSMOOTH
                                        * Referenced by: '<S19>/VTMRTDPSMOOTH'
                                        * Gear gain control
                                        */
extern int16_T VTOFFCMIAWLEVEL[4];     /* Variable: VTOFFCMIAWLEVEL
                                        * Referenced by: '<S11>/VTOFFCMIAWLEVEL'
                                        * Cmi offset control level
                                        */
extern int16_T VTAWGAINVS[6];          /* Variable: VTAWGAINVS
                                        * Referenced by: '<S33>/VTAWGAINVS'
                                        * Cmi gain control for VehSpeed
                                        */
extern int16_T VTCMIAWCTRLD[8];        /* Variable: VTCMIAWCTRLD
                                        * Referenced by: '<S34>/VTCMIAWCTRLD'
                                        * Cmi step Aw ctrl D
                                        */
extern int16_T VTGAINAWCTRLP[10];      /* Variable: VTGAINAWCTRLP
                                        * Referenced by: '<S35>/VTGAINAWCTRLP'
                                        * Cmi gain Aw ctrl P
                                        */
extern int16_T VTGAINAWGEAR[7];        /* Variable: VTGAINAWGEAR
                                        * Referenced by: '<S29>/VTGAINAWGEAR'
                                        * Gear gain control
                                        */
extern int16_T BKDPITCHAWCTRL[8];      /* Variable: BKDPITCHAWCTRL
                                        * Referenced by: '<S34>/BKDPITCHAWCTRL'
                                        * breakpoint
                                        */
extern int16_T BKERRPITCHAWCTRL[10];   /* Variable: BKERRPITCHAWCTRL
                                        * Referenced by: '<S35>/BKERRPITCHAWCTRL'
                                        * breakpoint
                                        */
extern int16_T BKMRTDPSMOOTH[5];       /* Variable: BKMRTDPSMOOTH
                                        * Referenced by: '<S19>/BKMRTDPSMOOTH'
                                        * Smooth BK
                                        */
extern int16_T HYSDPITCH;              /* Variable: HYSDPITCH
                                        * Referenced by: '<S10>/HYSDPITCH'
                                        * speed control
                                        */
extern int16_T THRDPDSTART;            /* Variable: THRDPDSTART
                                        * Referenced by: '<S6>/St_Aw'
                                        * Delta Pitch deep threshold
                                        */
extern int16_T THRDPITCH;              /* Variable: THRDPITCH
                                        * Referenced by: '<S10>/THRDPITCH'
                                        * speed control
                                        */
extern int16_T VTAWCUTOFFTHR[6];       /* Variable: VTAWCUTOFFTHR
                                        * Referenced by: '<S30>/VTAWCUTOFFTHR'
                                        * Pitch cutoff threshold
                                        */
extern int16_T VTDBDPITCHLEV[4];       /* Variable: VTDBDPITCHLEV
                                        * Referenced by: '<S34>/VTDBDPITCHLEV'
                                        * DB Level
                                        */
extern int16_T VTPITCHAWTRG[4];        /* Variable: VTPITCHAWTRG
                                        * Referenced by: '<S16>/VTPITCHAWTRG'
                                        * AW Level target
                                        */
extern uint16_T BKAWGAINVS[6];         /* Variable: BKAWGAINVS
                                        * Referenced by: '<S7>/BKAWGAINVS'
                                        * VehSpeed breakpoint
                                        */
extern uint16_T TBCMEAWGAIN[30];       /* Variable: TBCMEAWGAIN
                                        * Referenced by: '<S51>/TBCMEAWGAIN'
                                        * gain sat
                                        */
extern uint16_T BKAWGAINRL[5];         /* Variable: BKAWGAINRL
                                        * Referenced by: '<S7>/BKAWGAINRL'
                                        * RollCAN breakpoint
                                        */
extern uint16_T BKCMEEWAWSAT[7];       /* Variable: BKCMEEWAWSAT
                                        * Referenced by: '<S7>/BKCMEEWAWSAT'
                                        * Bk CmeEstWheelF saturation
                                        */
extern uint8_T AWCTFPITCH;             /* Variable: AWCTFPITCH
                                        * Referenced by: '<S28>/AWCTFPITCH'
                                        * selector
                                        */
extern uint8_T AWCTFSA;                /* Variable: AWCTFSA
                                        * Referenced by: '<S28>/AWCTFSA'
                                        * selector
                                        */
extern uint8_T DISAWCTRLINT;           /* Variable: DISAWCTRLINT
                                        * Referenced by:
                                        *   '<S13>/DISAWCTRLINT'
                                        *   '<S18>/DISAWCTRLINT'
                                        * selector
                                        */
extern uint8_T DISAWSMFRZINIT;         /* Variable: DISAWSMFRZINIT
                                        * Referenced by: '<S22>/DISAWSMFRZINIT'
                                        * selector
                                        */
extern uint8_T DPITCH;                 /* Variable: DPITCH
                                        * Referenced by: '<S10>/Memory_DP'
                                        * Deltapitch deep
                                        */
extern uint8_T DPITCHDEEP;             /* Variable: DPITCHDEEP
                                        * Referenced by: '<S10>/Memory_DP'
                                        * Deltapitch deep
                                        */
extern uint8_T DPITCHRFS;              /* Variable: DPITCHRFS
                                        * Referenced by: '<S6>/St_Aw'
                                        * Control refresh deep
                                        */
extern uint8_T DPITCHRSMOOTH;          /* Variable: DPITCHRSMOOTH
                                        * Referenced by: '<S6>/St_Aw'
                                        * Control refresh deep
                                        */
extern uint8_T MULPRFS;                /* Variable: MULPRFS
                                        * Referenced by: '<S6>/St_Aw'
                                        * mult step ctrl
                                        */
extern uint8_T SELBKDSMOOTH;           /* Variable: SELBKDSMOOTH
                                        * Referenced by: '<S23>/SELBKDSMOOTH'
                                        * selector
                                        */
extern uint8_T SELTHRDPITCH;           /* Variable: SELTHRDPITCH
                                        * Referenced by: '<S10>/SELTHRDPITCH'
                                        * selector
                                        */
extern uint8_T TIMAWRETURN;            /* Variable: TIMAWRETURN
                                        * Referenced by: '<S6>/St_Aw'
                                        * Tim exit aw
                                        */
extern void CmiSatMgm_target_aw_Init(rtDW_target_aw_CmiSatMgm_T *localDW);
extern void CmiSatMgm_target_aw(int16_T rtu_PitchCANInit, int16_T
  rtu_DeltaPitchDeep, uint8_T rtu_FlgDeltaPitch, int16_T rtu_PitchCAN, boolean_T
  rtu_disCtrl, uint8_T rtu_AwLevel, uint8_T rtu_GearPos, int16_T rtu_SAout,
  int16_T rtu_SAmin, int16_T rtu_CmiDriverP, uint16_T rtu_Bus_Ratio, uint16_T
  rtu_Bus_Ratio_jks, int32_T *rty_CmiStepAwCtrlP, int32_T *rty_CmiStepAwCtrlD,
  int16_T *rty_ErrPitchAw, rtDW_target_aw_CmiSatMgm_T *localDW);
extern void CmiSatMgm_sat_aw(int16_T rtu_CmiMainSatPInit, int16_T
  rtu_CmiMainSatIInit, uint8_T rtu_flgRtInit, int16_T rtu_CmfP, uint8_T
  rtu_flg_virtual_sat, int16_T rtu_CmiAwSat, uint8_T rtu_flgCalcD, uint8_T
  rtu_flgCalcP, int16_T rtu_CmiMainSatI, int32_T rtu_CmiStepAwCtrlP, int32_T
  rtu_CmiStepAwCtrlD, int16_T *rty_CmiSatAwP, int16_T *rty_CmiSatAwI,
  rtDW_sat_aw_CmiSatMgm_T *localDW);
extern void CmiSatMgm_sat_ret_aw(int16_T rtu_CmiMainSatI, int16_T
  rtu_CmiMainSatP, uint8_T rtu_flgRtInit, int16_T rtu_CmiMainSatPInit, int16_T
  rtu_CmiMainSatIInit, int16_T *rty_CmiSatAwP, int16_T *rty_CmiSatAwI,
  rtDW_sat_ret_aw_CmiSatMgm_T *localDW);
extern void CmiSatMgm_Calc_DeltaPitch_Init(int16_T *rty_DeltaPitchDeep);
extern void CmiSatMgm_Calc_DeltaPitch(uint8_T rtu_flgSmInit, int16_T
  rtu_PitchCAN, int16_T *rty_DeltaPitchDeep, uint8_T *rty_FlgDeltaPitch,
  rtDW_Calc_DeltaPitch_CmiSatMg_T *localDW);
extern void CmiSatMgm_Init(void);
extern void CmiSatMgm_T10ms_Init(void);
extern void CmiSatMgm_T10ms(void);

#endif                                 /* RTW_HEADER_CmiSatMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
