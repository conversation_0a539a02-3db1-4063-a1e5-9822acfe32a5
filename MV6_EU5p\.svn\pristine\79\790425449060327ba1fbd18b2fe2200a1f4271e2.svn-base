#ifndef _RECOVERY_H_
#define _RECOVERY_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#ifdef _BUILD_VSRAMMGM_
#include "vsrammgm.h"
#endif

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#undef _TEST_IVOR2_                

#define MAX_FLASH_RECOVERY_RESETS   (1)

#if (TARGET_TYPE == MPC5633)
#define FLASH_N_LOGICAL_BLOCKS      (8+2+4) /* 8LAS, 2MAS & 4HAS blocks */
#elif (TARGET_TYPE == MPC5634)
#define FLASH_N_LOGICAL_BLOCKS      (8+2+8) /* 8LAS, 2MAS & 8HAS blocks */
#endif
#define SPR_ESR_ECC_DATA_ERROR_MASK (0x01)

#define FLASH_FAULT_STRUCT_LEN (  4  + /*lastFaultyFlashAddress*/    \
                                  1  + /*lastFaultyFlashLogicalIdx*/ \
                                  FLASH_N_LOGICAL_BLOCKS +           \
                                  1    /*StructChecksum*/            \
                               )
/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef enum
{
    DO_EE_STD_SETUP       = 0,
    DO_EE_CORRUPTED_SETUP
}EE_SetupChose;

typedef struct
{
    uint32_T lastFaultyFlashAddress;
    uint8_T lastFaultyFlashLogicalIdx;
    uint8_T faultCounterMboot;
    uint8_T faultCounterEE_page0;
    uint8_T faultCounterEE_page1;
    uint8_T faultCounterBoot0;
    uint8_T faultCounterBoot1;
    uint8_T faultCounterCalib;
    uint8_T faultCounterAppl0;
    uint8_T faultCounterAppl1;
    uint8_T faultCounterAppl2;
    uint8_T faultCounterBackup;
    uint8_T StructChecksum;
}FlashFaultStruct_typ;

typedef enum
{
    FLASH_MINIBOOT_SECTION      = 0,
    FLASH_EE_PAGE0_SECTION      = 1,
    FLASH_EE_PAGE1_SECTION      = 2,
    FLASH_BOOT_BLK_1_SECTION    = 3,
    FLASH_BOOT_BLK_2_SECTION    = 4,
    FLASH_CALIB_SECTION         = 5,
    FLASH_APPL_BLK_1_SECTION    = 6,
    FLASH_APPL_BLK_2_SECTION    = 7,
    FLASH_APPL_BLK_3_SECTION    = 8,
    FLASH_BACKUP_SECTION        = 9,
    FLASH_FAULT_IDX_DEFAULT     = 0xFF
}flash_array_conf_line;


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern FlashFaultStruct_typ FlashFaultStruct;


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */
/* Example:
extern const uint16_T TEMPLATECAL;
*/
/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FlashFaultChecksumIsValid - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 uint8_T FlashFaultChecksumIsValid( void );

/*--------------------------------------------------------------------------*
 * FlashFaultChecksumUpdate - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 uint8_T FlashFaultChecksumUpdate(void);

/*--------------------------------------------------------------------------*
 * FlashFaultResetInfo - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void FlashFaultResetInfo(void);

 /* read from last faulty value:
 * this will generate Ivor2 if it is faulty yet.
 */
/*--------------------------------------------------------------------------*
 * checkFaultyAddrFromLastIvor2 - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 uint8_t checkFaultyAddrFromLastIvor2(void);

/*--------------------------------------------------------------------------*
 * FLASH_ConfigEEPROM_Corrupted - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t FLASH_ConfigEEPROM_Corrupted (void);

/*--------------------------------------------------------------------------*
 * IVOR2_faultmgm - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void IVOR2_faultmgm(void);

#ifdef _TEST_IVOR2_
/*--------------------------------------------------------------------------*
 * Ivor2_TestTask100ms - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void Ivor2_TestTask100ms(void);
#endif


#endif /* _RECOVERY_H_ */
