/*
 * File: C:\localmodules\local_AirMgm_PI6\slprj\ert\_sharedutils\mul_s32_s32_u32_sat.c
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2438
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Thu Feb 14 08:15:05 2019
 */

#include "rtwtypes.h"
#include "rtw_shared_utils.h"

int32_T mul_s32_s32_u32_sat(int32_T a, uint32_T b)
{
  int32_T result;
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_su32(a, b, &u32_chi, &u32_clo);
  if ((((int32_T)u32_chi) > 0) || ((u32_chi == 0U) && (u32_clo >= 2147483648U)))
  {
    result = MAX_int32_T;
  } else if ((((int32_T)u32_chi) < -1) || ((((int32_T)u32_chi) == -1) &&
              (u32_clo < 2147483648U))) {
    result = MIN_int32_T;
  } else {
    result = (int32_T)u32_clo;
  }

  return result;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
