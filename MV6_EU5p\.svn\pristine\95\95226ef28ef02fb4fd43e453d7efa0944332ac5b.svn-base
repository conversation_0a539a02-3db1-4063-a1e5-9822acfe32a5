/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

#include "pio.h"
#include "tach_cons.h"
#include "diagmgm_out.h"
#include "digitalin.h"
#include "sync.h"
#include "ETPU_VrsDefs.h"
#include "digio.h"
#include "sys.h"
#include "fuel_mgm.h"

extern uint8_T  StDiag[DIAG_NUMBER];              // eeprom

/* tuning parameters */
extern uint16_T QFUELLTHMAX;

#ifdef _BUILD_TACH_CONS_
static void SYNCMGM_ComputeConsDuty(void);

#define TACH_CONS_CONFIGURED       (0x01)
#define TACH_CONS_STOPPED          (0x02)
#define TACH_CONS_STARTED          (0x04)
#define TACH_CONS_STARTINGANGLE    ((N_TEETH_CYCLE * DEGREE_PER_TOOTH * DEGREE_PRECISION)/2)

/* local variables */
static uint8_t   tachcons_status        = 0;
static int8_t    tachcons_exSign        = 1;
static uint32_t  tachcons_exAngle;
static uint32_t  tachcons_period;

uint16_t ConsDuty;         /* 2^-8 (100% = 25600) */

int16_t TACHCONS_Config(uint8_t tc_matchMode, uint32_t tc_duty, uint32_t tc_period)
{
    int16_t retVal;
    
    if((tachcons_status & TACH_CONS_CONFIGURED) == 0)
    {
        if(TACHCONS_MODE == TACH)
        {
            tc_duty = 50 << 8;
        }
        retVal = PIO_PwmOutConfig(TACH_CONS_PWM, TACHCONS_POLARITY, tc_matchMode, 
                         (t_DutyCycle) tc_duty, (t_Period) tc_period, TACH_CONS_STARTINGANGLE);
        if(retVal == NO_ERROR)
        {
            tachcons_status = TACH_CONS_CONFIGURED|TACH_CONS_STOPPED;
            tachcons_period = tc_period;
        }
    }
    else
    {
        retVal = ERROR_ALREADY_CONFIGURED;
    }

    return retVal;        
}

int16_t TACHCONS_SetParams(uint8_t tc_matchMode, uint32_t tc_duty, uint32_t tc_period)
{
    int16_t retVal ;

    if((tachcons_status & TACH_CONS_CONFIGURED) == 0)
    {
        retVal = ERROR_NOT_CONFIGURED;
    }
    else if((tachcons_status & TACH_CONS_STOPPED) == TACH_CONS_STOPPED)
    {
        retVal = PIO_PwmOutSetMatchMode(TACH_CONS_PWM, tc_matchMode);
        retVal |= PIO_PwmOutSetPeriod(TACH_CONS_PWM, tc_period);
        retVal |= PIO_PwmOutSetDutyCicle(TACH_CONS_PWM, tc_duty);
        tachcons_period = tc_period;
    }
    else
    {
        retVal = ERROR_TACHCONS_RUNNING;
    }

    return retVal;
}

int16_t TACHCONS_SetDuty(uint8_t tc_matchMode, uint16_t tc_duty)
{
    int16_t retVal;

    if((tachcons_status & TACH_CONS_CONFIGURED) == 0)
    {
        retVal = ERROR_NOT_CONFIGURED;
    }
    else
    {
        if(TACHCONS_MODE == TACH)
        {
            tc_duty = (uint16_t)50 << 8;    
        }
        retVal = PIO_PwmOutSetDutyCicle(TACH_CONS_PWM, tc_duty);
    }
    
    return retVal;
}

int16_t TACHCONS_Start(void)
{
    int16_t retVal ;

    if((tachcons_status & TACH_CONS_STOPPED) == TACH_CONS_STOPPED)
    {
        retVal = PIO_PwmOutEnable(TACH_CONS_PWM);
        tachcons_status &= ~TACH_CONS_STOPPED;
        tachcons_status |= TACH_CONS_STARTED;
        tachcons_exSign = 1;
        tachcons_exAngle = TACH_CONS_STARTINGANGLE + tachcons_exSign*(tachcons_period*5)/100; 

        SYNC_SetNextAngle(tachcons_exAngle);
    }
    else
    {
        retVal = ERROR_TACHCONS_RUNNING;
    }
    
    return retVal;
}

int16_t TACHCONS_Stop(void)
{
    int16_t retVal ;

    if((tachcons_status & TACH_CONS_STARTED) == TACH_CONS_STARTED)
    {
        retVal = PIO_PwmOutDisable(TACH_CONS_PWM);
        tachcons_status &= ~TACH_CONS_STARTED;
        tachcons_status |= TACH_CONS_STOPPED;
    }
    else
    {
        retVal = ERROR_TACHCONS_STOPPED;
    }
    
    return retVal;
}

void TACHCONS_Ex_Angle(void)
{
    if((tachcons_status & TACH_CONS_STARTED) == TACH_CONS_STARTED)
    {
        tachcons_exSign *= -1;
        if(tachcons_exSign > 0)
        {
            tachcons_exAngle += tachcons_period/10;
        }
        else
        {
            tachcons_exAngle += tachcons_period - tachcons_period/10;
        }
        
        if(tachcons_exAngle > TACH_CONS_STARTINGANGLE*2)
        {
            tachcons_exAngle -= TACH_CONS_STARTINGANGLE*2;
        }  
          
        SYNC_SetNextAngle(tachcons_exAngle);
        TACHCONS_Diagnosis();
    }
}

void TACHCONS_Diagnosis(void)
{
    static  uint8_T PtFaultDashboard = NO_PT_FAULT;
            uint8_T  StDiagDashboard = NO_FAULT;
            uint8_t    tach_outcmd;
            uint8_t    tach_fbk;
    static  uint8_t    tachToVBatFlg = 0;

    DIGIO_InGet(IDN_TACH_CONS,&tach_outcmd);
    DIGIO_InGet(IDE_TachCons_Fault,&tach_fbk);
            
    if(tach_outcmd == 1)  
    {            
                if(tach_fbk == 0) 
                {
                    if(PtFaultDashboard == CC_TO_VBAT)
                    {
                        PtFaultDashboard = NO_PT_FAULT;
                    }    
                }
                else    // tach_fbk == 1
                {
                    PtFaultDashboard = CC_TO_VBAT;
                    /* protect!!! */
//                    DIGIO_OutCfg(IDN_TACH_CONS, 0);
                    DIGIO_OutCfgExt(IDN_TACH_CONS, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
                    DIGIO_OutSet(IDN_TACH_CONS, 0);
                    tachToVBatFlg = 1;
                }
    } 
    else    /* (tach_outcmd == 0) */ 
    {
        if(tach_fbk == 0) 
        {
              PtFaultDashboard = CC_TO_GND;
        }
        else
        {
            if (PtFaultDashboard == CC_TO_GND)
            {
                  PtFaultDashboard = NO_PT_FAULT;
            }            
        }
        if ((tachToVBatFlg))
        {
//            ETPU_OutCfgExt(IDN_TACH_CONS, OUT_PUSHPULL, EN_INPUT_BUF, MIN_SLEWRATE);
            SYS_OutPinConfig(IDN_TACH_CONS, PRIMARY_FUNCTION, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);

            tachToVBatFlg = 0;
        }
    }
    DiagMgm_SetDiagState(DIAG_LSD_DASHBOARD, PtFaultDashboard, &StDiagDashboard);
}

void SYNCMGM_Dashboard_Init(void)
{
    ConsDuty = 0;
    TACHCONS_Config(PWM_MATCH_ANGLE, 50<<8, TACHCONS_ANGLE_PERIOD);
}

static void SYNCMGM_ComputeConsDuty(void)
{

  /* Tachcons duty cycle
   * ConsDuty = 0.1 + (QFuelLth * 0.8) / QFUELLTHMAX
   */
  uint32_T tmpConsDuty;

  tmpConsDuty = CONS_DUTY_OFFSET + ((CONS_DUTY_GAIN * (uint32_T)QFuelLth) / QFUELLTHMAX);
  if (tmpConsDuty < CONS_DUTY_OFFSET) /* confronto inutile se QFuelLth < QFUELLTHMAX sempre */
  {
    ConsDuty = CONS_DUTY_OFFSET;
  }
  else if (tmpConsDuty > CONS_DUTY_MAX) /* confronto inutile se QFuelLth < QFUELLTHMAX sempre */
  {
    ConsDuty = CONS_DUTY_MAX;
  }
  else
  {
  	ConsDuty = tmpConsDuty;
  }

}

void TACHCONS_T100ms(void)
{
    SYNCMGM_ComputeConsDuty();
}    

void SYNCMGM_Dashboard_Sync(void) 
{
  TACHCONS_Stop();
  TACHCONS_SetParams(PWM_MATCH_ANGLE, 50<<8, TACHCONS_ANGLE_PERIOD);
  TACHCONS_Start();
}

void SYNCMGM_Dashboard_NoSync(void)
{
  TACHCONS_Stop();
}

void SYNCMGM_Dashboard(void)
{
  TACHCONS_SetDuty(PWM_MATCH_ANGLE, (256*100) - ConsDuty);	/* toni */
  TACHCONS_Start();
}

#else   /* _BUILD_TACH_CONS_ not defined */

int16_t TACHCONS_Config(uint8_t tc_matchMode, uint32_t tc_duty, uint32_t tc_period)
{
    return ERROR_NOT_CONFIGURED;
}

int16_t TACHCONS_SetParams(uint8_t tc_matchMode, uint32_t tc_duty, uint32_t tc_period)
{
    return ERROR_NOT_CONFIGURED;
}

int16_t TACHCONS_SetDuty(uint8_t tc_matchMode, uint16_t tc_duty)
{
    return ERROR_NOT_CONFIGURED;
}

int16_t TACHCONS_Start(void)
{
    return ERROR_NOT_CONFIGURED;
}

int16_t TACHCONS_Stop(void)
{
    return ERROR_NOT_CONFIGURED;
}

void TACHCONS_Ex_Angle(void)
{
}

void TACHCONS_Diagnosis(void)
{
}

void TACHCONS_T100ms(void)
{
}    

#endif  /* _BUILD_TACH_CONS_ */

