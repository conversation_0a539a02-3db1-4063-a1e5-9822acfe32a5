/*****************************************************************************************************************/
/* $HeadURL::                                                                                                     $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  MATLAB_MEX_FILE
#include "ETPU_EngineDefs.h"
#define _BUILD_SPARKMGM_
#endif

#include "sparkmgm.h"
#include "sabasic_mgm.h"
#include "trq_drivmgm.h"
#include "diagcanmgm.h"
#include "mathlib.h"
#include "timing.h"
#include "sync.h"
#include "syncmgm.h"
#include "gearshift_mgm.h"
#include "lambda_mgm.h"
#include "af_ctrl.h"
#include "trac_ctrl.h"
#include "idlectf_mgm.h"
#include "cmidriver_mgm.h"
#include "Trqext_req.h"
#include "Qair_target.h"
#include "trq_est.h"
#include "idxctfctrl_out.h"

//---------OUTPUT-----------------------
uint8_T CutoffFlg;
int16_T DSAOut[N_CYL_MAX];
uint8_T FlgNoTrqCtrSA;
uint16_T CutoffGainMax;

#ifdef _BUILD_SPARKMGM_

//---------OUTPUT-----------------------
uint16_T    DwellTime;
int16_T     SAobj[N_CYL_MAX];
uint16_T    RpmSparkObj;
uint8_T     FlgSAobjSat;
uint16_T    MaxDwellTime;
uint8_T     SparkMode;

//---------VARIABLE---------------------
uint16_T EffSAobj0;
uint16_T EffSAobjMean;
int16_T  SAobj0;
uint16_T EffSAobj;
int16_T  DEffSAPerc;
uint16_T EffSAobjNoCtf;
uint16_T EffSAReal;                
int16_T  SAmaxCyl;
uint16_T EffCutoff;
int16_T  ThrEffSAToll;
int16_T  EffSAobjOffset;

//---------TUNING PARAMETERS-------------
extern uint16_T BKDWELLTRPM[];                    
extern uint16_T BKDWELLTLOAD[];
extern uint8_T  TBDWELLTIME[];       
extern uint16_T BKDWELLTBATT[];
extern uint8_T  VTDWELLGAIN[];
extern int16_T  BKEFFSA[];       
extern uint16_T VTEFFSA[];       
extern uint8_T  ENCUTOFF;       
extern int16_T  TWCUTOFF;       
extern int16_T  CUTOFFGAIN;
extern uint8_T  FORCESA;
extern int16_T  FORCEDSA;
extern uint8_T  SPARKMOD;
extern uint16_T KFCTFENTRY;
extern uint8_T  ENSPARKTIME;
extern uint8_T  SELEFFSAMEAN;
extern uint16_T VTGAINSPARKTIME[];
extern int16_T  BKGAINSPARKTIME[];
extern uint16_T TIMEIDLECUTOFF;
extern uint8_T  VTDWELLMAX[];
extern uint8_T  VTDWELLMAXCRK[];
extern int8_T   VTTHREFFSATOLL[BKCMIEFFSATOLL_dim];
extern int16_T  BKCMIEFFSATOLL[BKCMIEFFSATOLL_dim];
extern uint8_T  ENVLAMEFFSA;
extern uint16_T BKVLAMEFFSA[BKVLAMEFFSA_dim];
extern int16_T  VTOFFSETEFFSA[BKVLAMEFFSA_dim];

//---------DICHIARAZIONI DELLE FUNZIONI------
void SparkModeCalculation(void);
void DwellTimeCalculation(void);
void Target_SA_Efficiency_Calculation(uint8_T cyl_id);
void Cutoff_Request_Calculation(void);
void Final_Target_SA_Calculation(uint8_T cyl_id);
void SparkMgm_Cmi2Eff_Calculation (uint16_T *tmpEffSAobj, int16_T cmitarget, uint8_T cyl);
void SparkMgm_Cmi2Eff_Mean_Calculation (uint16_T *tmpEffSAobj, int16_T cmitarget);

//-------EVENT POWER ON----------------------
void SparkMgm_Init(void)
{
    uint8_T cyl;
    
    SparkMode = SPARK_OFF;
    DwellTime = 0;
    CutoffFlg = 0;
    for(cyl=0; cyl < N_CYL_MAX; cyl++)
    {
        SAobj[cyl] = 0;
        DSAOut[cyl] = 0;
    }
    SAobj0 = 0;
    SAmaxCyl = 0;
    EffSAobj = EFF_MAX;
    EffSAobjNoCtf = EFF_MAX;
    EffSAobj0 = EFF_MAX;
    EffSAobjMean = EFF_MAX;
    EffSAReal = EFF_MAX;
    RpmSparkObj = 0;        
    FlgNoTrqCtrSA = 1;
    FlgSAobjSat = 0;
    EffCutoff = EFF_MAX;
    MaxDwellTime = 0;
}

//-------EVENT NO SYNC------------------------
void SparkMgm_NoSync(void) 
{
    SparkMgm_Init();
}

void SparkMgm_Sync(void)
{
    uint8_T cyl;
    
  /* Faccio gli stessi conti del TDC sperando che vadano bene per l'avviamento */
    SparkModeCalculation();
    
    DwellTimeCalculation();

    for(cyl=0; cyl<N_CYLINDER; cyl++)
    {
        Target_SA_Efficiency_Calculation(cyl);
    }
    
    Cutoff_Request_Calculation();
    
    for(cyl=0; cyl<N_CYLINDER; cyl++)
    {
        Final_Target_SA_Calculation(cyl);
    }
}

void SparkMgm_T5ms(void)
{
}

//--------EVENT TDC---------------------------
void SparkMgm_PreHTDC(void)
{
    SparkModeCalculation();
    
    DwellTimeCalculation();
    
    Target_SA_Efficiency_Calculation(AbsPreHTdc);
    
    Cutoff_Request_Calculation();
    
    Final_Target_SA_Calculation(AbsPreHTdc);
}

//--------EVENT TDC---------------------------
void SparkMgm_PreTDC(void)
{
    if (FlgUpdateSA != 0)
    {

        SparkModeCalculation();
        
        DwellTimeCalculation();
        
        Target_SA_Efficiency_Calculation(AbsPreTdc);
            
        Final_Target_SA_Calculation(AbsPreTdc);
    }
}

//----------SPARK OFF CALCULATION--------------
void SparkMgm_SparkOff(void)
{
    int16_T tmpDSAOut;
        
    if((StSync == SYNCH) && (FlgSyncPhased)) 
    {
        tmpDSAOut = SAoutCyl[SparkAbsTdc] - SAopt;
        LookUp_U16_S16(&(EffSAReal), VTEFFSA, tmpDSAOut, BKEFFSA, (BKEFFSADimension-1));

        DSAOut[SparkAbsTdc] = tmpDSAOut;
    }
}

//----------SPARK MODE CALCULATION-------------
void SparkModeCalculation(void)
{
    uint16_T    tmpVTGAINSPARKTIME;
    int16_T        tpmRpmError;
    
    if(InjEnable)
    {
        //    SPARK_OFF       = 0,
        //    SPARK_NORMAL    = 1,
        //    SPARK_ANG_TIME  = 2,
        //    SPARK_TIME_ANG  = 3,
        //    SPARK_TIME_TIME = 4
#ifdef  EN_SPARK_TIMETIME
        if(IdleFlg && ENSPARKTIME)
        {
            // Attuazione anticipo tempo-tempo solo al minimo
            SparkMode = SPARK_TIME_TIME;
        } 
        else
#endif            
        {
            SparkMode = SPARK_TIME_ANG;
        }
    }
    else
    {
        // Attuazione anticipo disabilitata
        SparkMode = SPARK_OFF;
    }
         
    // Calcolo RpmSparkObj applicato solo in SPARK_TIME_TIME
    tpmRpmError = (int16_T)Rpm - (int16_T)RpmIdleObj0;
    LookUp_U16_S16(&(tmpVTGAINSPARKTIME), VTGAINSPARKTIME, tpmRpmError, BKGAINSPARKTIME, (BKGAINSPARKTIME_dim-1));
     RpmSparkObj = RpmIdleObj + ((((int32_T)(Rpm - RpmIdleObj)) * tmpVTGAINSPARKTIME) >> 10);
}

//------DWELL TIME CALCULATION------------------
void DwellTimeCalculation(void)
{
    uint16_T tmpTBDWELLTIME;
    uint16_T vbatIndex;
    uint16_T vbatRatio;
    uint16_T tmpinterp;
    
    // DwellTime = TBDWELLTIME(Rpm,Load) * VTDWELLGAIN(VBattery)
    Look2D_U8_U16_U16(&(tmpTBDWELLTIME), TBDWELLTIME, Load, BKDWELLTLOAD, (BKDWELLTLOADDimension-1), Rpm, BKDWELLTRPM, (BKDWELLTRPMDimension-1));

    PreLookUpIdSearch_U16(&vbatIndex, &vbatRatio, VBattery, &(BKDWELLTBATT[0]), (BKDWELLTBATTDimension-1));
    LookUp_IR_U8(&(tmpinterp), &(VTDWELLGAIN[0]), vbatIndex, vbatRatio, (BKDWELLTBATTDimension-1));
    DwellTime = (uint16_T)(((uint32_T)(tmpinterp>>5) * (uint32_T)tmpTBDWELLTIME) >> 15);

    if (EndStartFlg != 0)
    {
        LookUp_IR_U8(&(tmpinterp), &(VTDWELLMAX[0]), vbatIndex, vbatRatio, (BKDWELLTBATTDimension-1));
    }
    else
    {
        LookUp_IR_U8(&(tmpinterp), &(VTDWELLMAXCRK[0]), vbatIndex, vbatRatio, (BKDWELLTBATTDimension-1));
    }
    MaxDwellTime = (tmpinterp>>8);
}

//-----TARGET SA EFFICIENCY CALCULATION-----------
void Target_SA_Efficiency_Calculation(uint8_T cyl_id)
{
    int32_T tmpEffSAobj;
    int32_T tmpEffSAobjDiff;
    int32_T tmpEffSAbaseDiff;
    int16_T tmpBKEFFSA;
    int16_T tmpThreffsatoll;
    int32_T threffsatoll;
    uint16_T tmpEffIdle;
    uint32_T tmpCutoffGainMax;
    int32_T tmpDEffSAPerc;

    /* Calcolo efficienza obiettivo */
    SparkMgm_Cmi2Eff_Calculation(&EffSAobj0, CmiTargetI, cyl_id);

    /* Calcolo efficienza obiettivo media */
    SparkMgm_Cmi2Eff_Mean_Calculation(&EffSAobjMean, CmiTargetI);

    // Calcolo FlgNoTrqCtrSA = 0 solo se EffSAobj < EffSAmax con isteresi
    LookUp_S8_S16(&tmpThreffsatoll, &VTTHREFFSATOLL[0], CmiPotEstMean, &BKCMIEFFSATOLL[0], (BKCMIEFFSATOLL_dim-1)); // 2^-(7+8) = 2^-15
    threffsatoll = (tmpThreffsatoll >> 1); // Scale 2^-15 --> 2^-14
    ThrEffSAToll = threffsatoll;
    
    tmpEffSAobjDiff = EffSAmax - EffSAobj0;
    
    if(FlgNoTrqCtrSA)
    {
        FlgNoTrqCtrSA = (tmpEffSAobjDiff <= threffsatoll); // 2^0
    }
    else
    {
        FlgNoTrqCtrSA = (tmpEffSAobjDiff <= 0); // 2^0
    }
   
//    // Calcolo EffSAobj scelto tra tmpEffSAobj e EffSAmax
//    tmpEffSAobjDiff = abs(tmpEffSAobjDiff);
//    tmpEffSAbaseDiff = abs(EffSAmax - EffSAbase);
//    if((THREFFSABASETOLL > 0) && (tmpEffSAobjDiff < threffsatoll) && (tmpEffSAbaseDiff < THREFFSABASETOLL))
//    {
//        EffSAobj = EffSAmax;                                                                    // 2^-14    
//    }
//    else
//    {
//        EffSAobj = EffSAobj0;                                                        // 2^-14
//    }

    /* NUOVA GESTIONE */
    tmpEffSAbaseDiff = abs(EffSAbase - EffSAobjMean);
    if (IdleFlg != 0)
    {
        if (SELEFFSAMEAN == 0)
        {
            tmpEffIdle = EffSAobj0;
        }
        else if (SELEFFSAMEAN == 1)
        {
            tmpEffIdle = EffSAobj0;
        }
        else if (SELEFFSAMEAN == 2)
        {
            tmpEffIdle = EffSAobjMean;
        }
        else
        {
            tmpEffIdle = EffSAbase;
        }
        /*
        tmpEffSAobj = ((int32_T)(1024 - GnRtQAirTarget0) * EffSAbase);
        tmpEffSAobj = ((int32_T)GnRtQAirTarget0 * tmpEffIdle) + tmpEffSAobj;
        tmpEffSAobj = tmpEffSAobj >> 10;
        */
        tmpEffSAobj = tmpEffIdle;
    }
    else
    {
        if ((StTracCtrl == TC_ACC) || (StTracCtrl == TC_SMOOTH))
        {
            tmpEffSAobj = EffSAobj0;
        }
        else
        {
            if(tmpEffSAbaseDiff < threffsatoll)
            {
                tmpEffSAobj = EffSAbase;
            }
            else
            {
                if (SELEFFSAMEAN != 0)
                {
                    tmpEffSAobj = EffSAobjMean;
                }
                else
                {
                    tmpEffSAobj = EffSAobj0;
                }
            }
        }
    }
    tmpEffSAobj = min(tmpEffSAobj, (int32_T)EffSAmax);

    LookUp_S16_U16(&EffSAobjOffset, VTOFFSETEFFSA, VLambdaCrk, BKVLAMEFFSA, (BKVLAMEFFSA_dim-1));
    if ((ENVLAMEFFSA != 0) && (IdleReqFlg == 0) && (EnCutOff == 0) && (FlgClosedLoopOnOff != 0) && (VLambdaState != VLAMINIT))
    {
        tmpEffSAobj = (int32_T)(tmpEffSAobj + EffSAobjOffset);
        tmpEffSAobj = max(tmpEffSAobj,0);
        tmpEffSAobj = min(tmpEffSAobj, EFF_MAX);
        EffSAobj = tmpEffSAobj;
    }
    else
    {
        EffSAobj = tmpEffSAobj;
    }

    if (EffSAbase != 0)
    {
        tmpDEffSAPerc = EffSAobj;
        tmpDEffSAPerc = (tmpDEffSAPerc << 14);
        tmpDEffSAPerc = (tmpDEffSAPerc / EffSAbase);
        tmpDEffSAPerc = min(tmpDEffSAPerc, MAX_int16_T);
        tmpDEffSAPerc = max(tmpDEffSAPerc, MIN_int16_T);
    }
    else
    {
        tmpDEffSAPerc = MAX_int16_T;
    } 
    DEffSAPerc = tmpDEffSAPerc;
    
    // SAobj0 = SAopt + BKEFFSA(EffSAobj)
    LookUp_S16_U16(&(tmpBKEFFSA), BKEFFSA, EffSAobj, VTEFFSA, (VTEFFSADimension-1));
    SAobj0 = SAopt + tmpBKEFFSA;        // 2^-14

    /* Calcolo efficienza senza effetto di CutoffGain */
    SparkMgm_Cmi2Eff_Calculation(&EffSAobjNoCtf, CmiDriverIQS, cyl_id);
    tmpCutoffGainMax = ((uint32_T)EffSAmin) * CUTOFFGAIN; // 2^-14 * 2^-10 = 2^-24
    if (EffSAobjNoCtf >= EffSAmin)
    {
        /* None */
    }
    else
    {
        EffSAobjNoCtf = EffSAmin;
    }
    tmpCutoffGainMax = (tmpCutoffGainMax / EffSAobjNoCtf); // 2^-24 / 2^-14 = 2^-10
    if(tmpCutoffGainMax > MAX_uint16_T)
    {
        CutoffGainMax = MAX_uint16_T;
    }
    else
    {
        CutoffGainMax = (uint16_T)tmpCutoffGainMax;
    }

}


//------CUTOFF REQUEST CALCULATION----------------
void Cutoff_Request_Calculation(void)
{
    uint16_T EffSAmin_tmp;

    if (CutoffFlg == 0)
    {
        if (EnCutOff != 0)
        {
            EffSAmin_tmp = (uint16_T)(((uint32_T)EffSAmin * CUTOFFGAIN) >> 10);
            CutoffFlg = (EffSAobj < EffSAmin_tmp);
        }
        else { /* MISRA */ }
    } 
    else
    {
        CutoffFlg = EnCutOff;
    }
    
    // Calcolo efficienza di cutoff EffCutoff = 0/1
    EffCutoff = (CutoffFlg) ? (0) : (EFF_MAX); // 2^-14
}

//-------FINAL TARGET SA CALCULATION-----------
void Final_Target_SA_Calculation(uint8_T cyl_id)
{
    int16_T SAKnock_cyl, SAobj_cyl;
    uint8_T tmpFlgSAobjSat = 0;

    // Calcolo di cyl_id e SAKnock_cyl
    SAKnock_cyl = SAKnock[cyl_id];

    // Calcolo di SAobj_cyl
    SAobj_cyl = SAobj0;

/* OLD-STRATEGY
    // Contributo detonazione
    if(SAKnock_cyl>0) 
    {
        // Somma per contributo Knocking solo se positivo
        SAobj_cyl += SAKnock_cyl;
    }

    // Calcolo saturazione anticipo
    SAmaxCyl = min(SAopt, (SAmax + SAKnock_cyl));   
*/

    // Somma per contributo Knocking
    SAobj_cyl += SAKnock_cyl;
    
    // Calcolo saturazione anticipo
    SAmaxCyl = min(SAopt, SAmax);
    
    // Saturazione superiore anticipo a SAmax
    if(SAobj_cyl > SAmaxCyl) 
    {
        SAobj_cyl = SAmaxCyl;
        tmpFlgSAobjSat = 1;
    }  
    // Saturazione inferiore anticipo a SAmin
    if(SAobj_cyl < SAmin) 
    {
        SAobj_cyl = SAmin;
        tmpFlgSAobjSat = 1;
    }
    // Calcolo flag saturazione anticipo
    FlgSAobjSat = tmpFlgSAobjSat;

    // Anticipo all'avviamento
    if(!EndStartFlg) 
    {
        SAobj_cyl = SAstart;
    }

    // Forzatura anticipo accensione
    if(FORCESA == 1) 
    {
        SAobj_cyl = FORCEDSA;
    } 
    else if(FORCESA == 2) 
    {
        SAobj_cyl = SAopt;
    } 
    else if(FORCESA == 3) 
    {
        SAobj_cyl = SAbase;
    }
    else if(FORCESA == 4) 
    {
        SAobj_cyl = SAmin;
    }
    else if(FORCESA == 5) 
    {
        SAobj_cyl = SAIdle;
    }
    else /* Toni - MISRA */
    {
    }
    // Saturazioni MIN_SA/MAX_SA    
    if(SAobj_cyl < MIN_SA) 
    {
        SAobj_cyl = MIN_SA;
    }
    else if(SAobj_cyl > MAX_SA) 
    {
        SAobj_cyl = MAX_SA;
    }
    else /* Toni - MISRA */
    {
    }
    // Buffering SAobj
    SAobj[cyl_id] = SAobj_cyl;
}

void SparkMgm_Cmi2Eff_Calculation (uint16_T *tmpEffSAobj, int16_T cmitarget, uint8_T cyl)
{
    int16_T tmpCmiTargetI;
    int16_T cmipotest_cyl;
    int32_T tmpEff;

    // EffSAobj = CmiTargetI / CmiPotEst
    tmpCmiTargetI = (cmitarget / N_CYLINDER);
    cmipotest_cyl = CmiPotEst[cyl];
    if(cmipotest_cyl > 0)
    {
        tmpEff = max(cmipotest_cyl, tmpCmiTargetI);
        tmpEff = ((int32_T)tmpCmiTargetI << 14) / tmpEff;     // 2^-5 / 2^-5 => 2^-14
        tmpEff = max(tmpEff, 0);
        tmpEff = min(tmpEff, EFF_MAX);
    }
    else
    {
        tmpEff = EFF_MAX; // 2^-14
    }

    *tmpEffSAobj = (uint16_T)tmpEff;
}

void SparkMgm_Cmi2Eff_Mean_Calculation (uint16_T *tmpEffSAobj, int16_T cmitarget)
{
    int16_T tmpCmiTargetI;
    int16_T cmipotest_cyl;
    int32_T tmpEff;

    // EffSAobj = CmiTargetI / CmiPotEst
    tmpCmiTargetI = (cmitarget / N_CYLINDER);
    cmipotest_cyl = CmiPotEstMean;
    if(cmipotest_cyl > 0)
    {
        tmpEff = max(cmipotest_cyl, tmpCmiTargetI);
        tmpEff = ((int32_T)tmpCmiTargetI << 14) / tmpEff;     // 2^-5 / 2^-5 => 2^-14
        tmpEff = max(tmpEff, 0);
        tmpEff = min(tmpEff, EFF_MAX);
    }
    else
    {
        tmpEff = EFF_MAX; // 2^-14
    }

    *tmpEffSAobj = (uint16_T)tmpEff;
}


#else

void SparkMgm_Init(void)
{
  uint8_T cyl;
  
    CutoffFlg= 0;
    for(cyl=0; cyl < N_CYL_MAX; cyl++)
    {
        DSAOut[cyl] = 0;        
    }
    FlgNoTrqCtrSA = TRUE;
}

void SparkMgm_NoSync(void) 
{
    SparkMgm_Init();
}

void SparkMgm_PreTDC(void)
{
    FlgNoTrqCtrSA = FlgNoTrqCtrSACAN;
}

void SparkMgm_T5ms(void)
{
    FlgNoTrqCtrSA = FlgNoTrqCtrSACAN;
}

void SparkMgm_PreHTDC(void)
{
    FlgNoTrqCtrSA = FlgNoTrqCtrSACAN;
}
#endif    // _BUILD_SPARKMGM_
