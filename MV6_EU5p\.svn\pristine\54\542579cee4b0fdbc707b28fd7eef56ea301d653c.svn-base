
/* ------------------------------------------------------------------------- */
/* SYS - <PERSON><PERSON>enter define for shutdown routine                             */
/* ------------------------------------------------------------------------- */
#define SHTDWN_PERIOD  (0xFFFFFFFF) /* This macro sets decrementer period    */
                                    /* value; 0xFFFFFFFF i.e. 53,687s @80MHz */


/* ------------------------------------------------------------------------- */
/* BIOS error coding                                                         */
/* ------------------------------------------------------------------------- */

/* peripheral bit indices */
#define ADC_IDX          0
#define CAN_IDX          1
#define DMA_IDX          2
#define EXTIRQ_IDX       3
#define FLASH_IDX        4
#define PIO_IDX          5
#define SPI_IDX          6
#define ETPU_IDX         7
#define SCI_IDX          8
#define SIU_IDX          9
#define TASK_IDX        10
#define IVOR_IDX        11
#define TMR_IDX         12
#define SYNC_IDX        13
#define IGN_IDX         14
#define INJ_IDX         15
#define PHASE_IDX       16

#define OSEK_IDX         23

#define END_PWON_IDX    31

#define BIOS_FAILURE_ADC          0x00000001
#define BIOS_FAILURE_CAN          0x00000002
#define BIOS_FAILURE_DIGIO        0x00000004
#define BIOS_FAILURE_DMA          0x00000008
#define BIOS_FAILURE_EEPROM       0x00000010
#define BIOS_FAILURE_EXTIRQ       0x00000020
#define BIOS_FAILURE_FLASH        0x00000040
#define BIOS_FAILURE_IGN          0x00000080
#define BIOS_FAILURE_INJ          0x00000100
#define BIOS_FAILURE_LIN          0x00000200
#define BIOS_FAILURE_NEXUS        0x00000400
#define BIOS_FAILURE_PHASE        0x00000800
#define BIOS_FAILURE_PIO          0x00001000
#define BIOS_FAILURE_SCI          0x00002000
#define BIOS_FAILURE_SPI          0x00004000
#define BIOS_FAILURE_SYNC         0x00008000
#define BIOS_FAILURE_SYS          0x00010000
#define BIOS_FAILURE_ETPU         0x00020000
#define BIOS_FAILURE_TASK         0x00040000
#define BIOS_FAILURE_TIMING       0x00080000
#define BIOS_FAILURE_UART         0x00100000
#define BIOS_FAILURE_VSRAM        0x00200000
#define BIOS_FAILURE_WDT          0x00400000
#define BIOS_FAILURE_OSEK         0x00800000

#define  CHECK_BIOS_FAULTS 

#define SYS_ETPU_TIMEOUT     20


/* ------------------------------------------------------------------------- */
/* SIU - PCR fields definition                                               */
/* ------------------------------------------------------------------------- */
/* MPC5632 */
#define PA119_ETPUA05_FUNCTION      0x0400

#define PA120_ETPUA06_FUNCTION      0x0400
#define PA120_ETPUA18_FUNCTION      0x0800

#define PA121_ETPUA07_FUNCTION      0x0400
#define PA121_ETPUA19_FUNCTION      0x0800

#define PA122_ETPUA08_FUNCTION      0x0400
#define PA122_ETPUA20_FUNCTION      0x0800

#define PA123_ETPUA09_FUNCTION      0x0400
#define PA123_ETPUA21_FUNCTION      0x0800

#define PA139_ETPU25_FUNCTION       0x0400

#define PA138_ETPU24_FUNCTION       0x0400

#define PA179_EMIOS00_FUNCTION      0x0400

#define PA187_EMIOS08_FUNCTION      0x0400

#define PA188_EMIOS09_FUNCTION      0x0400

#define PA191_EMIOS12_FUNCTION      0x0400

#define PA193_EMIOS14_FUNCTION      0x0400

/* MPC5533 */
/* PA field - port assignment*/
#define GPIO_FUNCTION           0x0000
#define ALTERNATE_FUNCTION      0x0800
#define PRIMARY_FUNCTION        0x0C00

#define INPUT_ENABLE            0x0100
#define INPUT_DISABLE           0x0000
#define OUTPUT_ENABLE           0x0200
#define OUTPUT_DISABLE          0x0000

/* Fields used for Input */
#define DISABLE_HYSTERESIS      0x0000
#define ENABLE_HYSTERESIS       0x0010

/* treat WPE and WPS as 1 field for weak pull configuration */
#define WEAK_PULL_DISABLE       0x0000
#define WEAK_PULL_DOWN          0x0002
#define WEAK_PULL_UP            0x0003

/* Fields used for Output */
/* DSC field - drive strangth control */
#define DRIVE_STRENGTH_10PF     0x0000
#define DRIVE_STRENGTH_20PF     0x0040
#define DRIVE_STRENGTH_30PF     0x0080
#define DRIVE_STRENGTH_50PF     0x00C0

/* ODE */
#define PUSH_PULL_ENABLE        0x0000
#define OPEN_DRAIN_ENABLE       0x0020

/* SRC field - slew rate control */
#define MINIMUM_SLEW_RATE       0x0000
#define MEDIUM_SLEW_RATE        0x0004
#define MAXIMUM_SLEW_RATE       0x000C



/****************************************************************************
     Peripheral errors defines 
 ****************************************************************************/
#define SYS_PERIPHERAL_NOT_ENABLED         -5
#define SYS_NO_SPARK                         -6
#define SYS_SPARK_OVERFLOW                 -7
#define SYS_CONFIGURATION_NOT_SUPPORTED  -8
#define BOOT_APP_INCOHERENCY                         -9
#define CALIB_APP_INCOHERENCY                        -10
