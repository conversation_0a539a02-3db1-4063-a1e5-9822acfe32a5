/*
 * File: TempMgm_private.h
 *
 * Code generated for Simulink model 'TempMgm'.
 *
 * Model version                  : 1.805
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jul  9 15:25:12 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TempMgm_private_h_
#define RTW_HEADER_TempMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "TempMgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "engflag.h"
#include "analogin.h"
#include "trq_est.h"
#include "syncmgm.h"
#include "canmgm.h"
#include "ptrain_diag.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T STEPTAIR;               /* Variable: STEPTAIR
                                        * Referenced by: '<S33>/STEPTAIR'
                                        * Diagnosis TAir
                                        */
extern int16_T STEPTWAT;               /* Variable: STEPTWAT
                                        * Referenced by:
                                        *   '<S74>/STEPTWAT'
                                        *   '<S82>/STEPTWAT'
                                        * Diagnosis TWater
                                        */
extern int16_T STEPTWATHT;             /* Variable: STEPTWATHT
                                        * Referenced by:
                                        *   '<S74>/STEPTWATHT'
                                        *   '<S82>/STEPTWATHT'
                                        * Diagnosis TWater
                                        */
extern int16_T TAIRMAXERR;             /* Variable: TAIRMAXERR
                                        * Referenced by: '<S33>/TAIRMAXERR'
                                        * Diagnosis TAir
                                        */
extern int16_T TAIRREC;                /* Variable: TAIRREC
                                        * Referenced by:
                                        *   '<S8>/TAIRREC'
                                        *   '<S29>/Rec_TAir'
                                        * (SR) Recovery air temperature value
                                        */
extern int16_T TH2OFINALVALUE;         /* Variable: TH2OFINALVALUE
                                        * Referenced by: '<S56>/TH2OFINALVALUE'
                                        * (SR) Coolant temperature expected final value
                                        */
extern int16_T THCOHTW2DIAGTW2;        /* Variable: THCOHTW2DIAGTW2
                                        * Referenced by: '<S51>/THCOHWWATMOD1'
                                        * (SR) Coherence threshold Low coolant temperature 2
                                        */
extern int16_T THCOHTWDIAGTW2;         /* Variable: THCOHTWDIAGTW2
                                        * Referenced by: '<S51>/THCOHWWATMOD2'
                                        * (SR) Coherence threshold High coolant temperature 2
                                        */
extern int16_T THCOHWWATAIR;           /* Variable: THCOHWWATAIR
                                        * Referenced by: '<S8>/THCOHWWATAIR'
                                        * Coherence threshold for coolant and air temp
                                        */
extern int16_T THCOHWWATMOD;           /* Variable: THCOHWWATMOD
                                        * Referenced by: '<S51>/THCOHWWATMOD'
                                        * (SR) Coherence threshold for model coolant temperature
                                        */
extern int16_T TW1CRK;                 /* Variable: TW1CRK
                                        * Referenced by: '<S74>/TW1CRK'
                                        * Diagnosis TWater
                                        */
extern int16_T TW2CRK;                 /* Variable: TW2CRK
                                        * Referenced by: '<S82>/TW2CRK'
                                        * Diagnosis TWater
                                        */
extern int16_T VTTAIR[16];             /* Variable: VTTAIR
                                        * Referenced by:
                                        *   '<S8>/VTTAIR'
                                        *   '<S29>/VTTAIR'
                                        * (SR) Air temperature vector
                                        */
extern int16_T VTTWATER[16];           /* Variable: VTTWATER
                                        * Referenced by:
                                        *   '<S8>/VTTWATER'
                                        *   '<S8>/VTTWATER1'
                                        *   '<S51>/VTTWATER'
                                        *   '<S51>/VTTWATER1'
                                        * (SR) Coolant temperature vector
                                        */
extern int16_T BKCMIKFTH2O[5];         /* Variable: BKCMIKFTH2O
                                        * Referenced by: '<S56>/BKCMIKFTH2O'
                                        * CmiPotEst breakpoints
                                        */
extern uint16_T TVALFTWFUNC;           /* Variable: TVALFTWFUNC
                                        * Referenced by: '<S51>/TVALFTWFUNC'
                                        * Validation time for twater functional fault
                                        */
extern uint16_T TVALFTWFUNC2;          /* Variable: TVALFTWFUNC2
                                        * Referenced by: '<S51>/TVALFTWFUNC2'
                                        * Validation time for twater2 functional fault
                                        */
extern uint16_T KFTAIR;                /* Variable: KFTAIR
                                        * Referenced by: '<S34>/KFTAIR'
                                        * Filt const for air temp
                                        */
extern uint16_T KFTWATER;              /* Variable: KFTWATER
                                        * Referenced by:
                                        *   '<S54>/KFTWATER'
                                        *   '<S55>/KFTWATER'
                                        * (SR) Filt const for cool temp
                                        */
extern uint16_T TBKFTH2O[25];          /* Variable: TBKFTH2O
                                        * Referenced by: '<S56>/TBKFTH2O'
                                        * (SR) Filt const for cool temp model
                                        */
extern uint16_T VEHDIAGTAIR;           /* Variable: VEHDIAGTAIR
                                        * Referenced by: '<S33>/VEHDIAGTAIR'
                                        * Diagnosis TAir
                                        */
extern uint16_T BKTAIR[16];            /* Variable: BKTAIR
                                        * Referenced by:
                                        *   '<S8>/BKTAIR'
                                        *   '<S29>/BKTAIR'
                                        * Air temperature breakpoints
                                        */
extern uint16_T BKTWATER[16];          /* Variable: BKTWATER
                                        * Referenced by:
                                        *   '<S8>/BKTWATER'
                                        *   '<S8>/BKTWATER1'
                                        *   '<S51>/BKTWATER'
                                        *   '<S51>/BKTWATER1'
                                        * Coolant temperature breakpoints
                                        */
extern uint16_T VTAIRHIGH;             /* Variable: VTAIRHIGH
                                        * Referenced by: '<S29>/VTAIRHIGH'
                                        * (SR) Upper threshold for air sensor electric diag
                                        */
extern uint16_T VTAIRLOW;              /* Variable: VTAIRLOW
                                        * Referenced by: '<S29>/VTAIRLOW'
                                        * (SR) Lower threshold for air sensor electric diag
                                        */
extern uint16_T VTAIRMAX;              /* Variable: VTAIRMAX
                                        * Referenced by:
                                        *   '<S8>/VTAIRMAX'
                                        *   '<S29>/VTAIRMAX'
                                        * (SR) Upper threshold for air sensor electric diag
                                        */
extern uint16_T VTAIRMIN;              /* Variable: VTAIRMIN
                                        * Referenced by:
                                        *   '<S8>/VTAIRMIN'
                                        *   '<S29>/VTAIRMIN'
                                        * (SR) Lower threshold for air sensor electric diag
                                        */
extern uint16_T VTWATERMAX;            /* Variable: VTWATERMAX
                                        * Referenced by:
                                        *   '<S8>/VTWATERMAX'
                                        *   '<S8>/VTWATERMAX1'
                                        *   '<S52>/VTWATERMAX'
                                        *   '<S53>/VTWATERMAX'
                                        * (SR) Upper threshold for wat temp electric diag
                                        */
extern uint16_T VTWATERMIN;            /* Variable: VTWATERMIN
                                        * Referenced by:
                                        *   '<S8>/VTWATERMIN'
                                        *   '<S8>/VTWATERMIN1'
                                        *   '<S52>/VTWATERMIN'
                                        *   '<S53>/VTWATERMIN'
                                        * (SR) Lower threshold for wat temp electric diag
                                        */
extern uint16_T BKRPMKFTH2O[5];        /* Variable: BKRPMKFTH2O
                                        * Referenced by: '<S56>/BKRPMKFTH2O'
                                        * Rpm breakpoints
                                        */
extern uint16_T TAIRTDCFORSTUCK;       /* Variable: TAIRTDCFORSTUCK
                                        * Referenced by: '<S33>/TAIRTDCFORSTUCK'
                                        * Diagnosis TAir
                                        */
extern uint16_T TAIRTIMCOMPARE;        /* Variable: TAIRTIMCOMPARE
                                        * Referenced by: '<S40>/TAIRTIMCOMPARE'
                                        * Diagnosis TAir
                                        */
extern uint16_T TW1TDCFORSTUCK;        /* Variable: TW1TDCFORSTUCK
                                        * Referenced by: '<S74>/TW1TDCFORSTUCK'
                                        * Diagnosis TWater
                                        */
extern uint16_T TW1TDCFORSTUCKHT;      /* Variable: TW1TDCFORSTUCKHT
                                        * Referenced by: '<S74>/TW1TDCFORSTUCKHT'
                                        * Diagnosis TWater
                                        */
extern uint16_T TW2TDCFORSTUCK;        /* Variable: TW2TDCFORSTUCK
                                        * Referenced by: '<S82>/TW2TDCFORSTUCK'
                                        * Diagnosis TWater
                                        */
extern uint16_T TW2TDCFORSTUCKHT;      /* Variable: TW2TDCFORSTUCKHT
                                        * Referenced by: '<S82>/TW2TDCFORSTUCKHT'
                                        * Diagnosis TWater 2
                                        */
extern uint8_T USETWATER2;             /* Variable: USETWATER2
                                        * Referenced by:
                                        *   '<S8>/USETWATER1'
                                        *   '<S30>/Calc_TWater'
                                        * Enable Sensor Temperature 2
                                        */
extern void TempMgm_Call_Diag(boolean_T rtu_Enable, uint8_T rtu_diagId, uint8_T
  rtu_fault_elett, boolean_T rtu_fault_stuck, rtB_Call_Diag_TempMgm *localB);
extern void TempMgm_diag_twater2(void);
extern void TempMgm_diag_twater(void);
extern void TempMgm_calc_twatertmp(void);
extern void TempMgm_T100ms(void);
extern void TempMgm_Init(void);

#endif                                 /* RTW_HEADER_TempMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
