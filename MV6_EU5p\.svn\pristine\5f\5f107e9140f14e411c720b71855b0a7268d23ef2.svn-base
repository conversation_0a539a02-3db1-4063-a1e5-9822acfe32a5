/*
 * File: Pby_Mgm_private.h
 *
 * Code generated for Simulink model 'Pby_Mgm'.
 *
 * Model version                  : 1.2249
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Fri May 16 09:49:09 2014
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA-C:2004 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (14), Warnings (3), Error (0)
 */

#ifndef RTW_HEADER_Pby_Mgm_private_h_
#define RTW_HEADER_Pby_Mgm_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "cmefilter_mgm.h"
#include "syncmgm.h"
#include "trq_driver.h"
#include "GearPosClu_mgm.h"
#include "Timing.h"
#include "PTrain_Diag.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error "Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compiler's limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, which will disable the preprocessor word size checks."
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error "Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Imported (extern) block parameters */
extern uint16_T TDURPBY;               /* Variable: TDURPBY
                                        * Referenced by: '<S4>/StPby_Calc'
                                        * Duration time of pass by
                                        */
extern uint16_T THCNTWUCPBY;           /* Variable: THCNTWUCPBY
                                        * Referenced by: '<S4>/StPby_Calc'
                                        * Number of wuc to disable pass by
                                        */
extern uint16_T TSTABEXITPBY;          /* Variable: TSTABEXITPBY
                                        * Referenced by: '<S4>/StPby_Calc'
                                        * Stability exit time for pass by
                                        */
extern uint16_T TSTABPBY;              /* Variable: TSTABPBY
                                        * Referenced by: '<S4>/StPby_Calc'
                                        * Stability time for pass by
                                        */
extern int16_T VTCMEMAXPBY[7];         /* Variable: VTCMEMAXPBY
                                        * Referenced by: '<S8>/VTCMEMAXPBY'
                                        * Max torque for pass by
                                        */
extern uint16_T DVEHSPEEDPBY;          /* Variable: DVEHSPEEDPBY
                                        * Referenced by: '<S8>/DVEHSPEEDPBY'
                                        * Vehicle speed band for pass by
                                        */
extern uint16_T MAXPBYKMODO;           /* Variable: MAXPBYKMODO
                                        * Referenced by: '<S8>/MAXPBYKMODO'
                                        * Max Km odometer to enable PBY
                                        */
extern uint16_T THGASPBY;              /* Variable: THGASPBY
                                        * Referenced by: '<S4>/StPby_Calc'
                                        * GasPosCC threshold to enable pass by
                                        */
extern uint16_T VEHSPEEDPBY1;          /* Variable: VEHSPEEDPBY1
                                        * Referenced by: '<S8>/VEHSPEEDPBY1'
                                        * First vehicle speed set point for pass by
                                        */
extern uint16_T VEHSPEEDPBY2;          /* Variable: VEHSPEEDPBY2
                                        * Referenced by: '<S8>/VEHSPEEDPBY2'
                                        * Second vehicle speed set point for pass by
                                        */
extern uint16_T VEHSPEEDPBY3;          /* Variable: VEHSPEEDPBY3
                                        * Referenced by: '<S8>/VEHSPEEDPBY3'
                                        * Third vehicle speed set point for pass by
                                        */
extern uint8_T MAXPBYCYCLES;           /* Variable: MAXPBYCYCLES
                                        * Referenced by: '<S8>/MAXPBYCYCLES'
                                        * Max number of pby activations after key-on
                                        */
extern uint8_T MAXPBYGEAR;             /* Variable: MAXPBYGEAR
                                        * Referenced by: '<S8>/MAXPBYGEAR'
                                        * Max gear to activate pby
                                        */
extern uint8_T MINPBYGEAR;             /* Variable: MINPBYGEAR
                                        * Referenced by: '<S8>/MINPBYGEAR'
                                        * Min gear to activate pby
                                        */
extern void Pby_Mgm_FlgStabPassBy_Calc(void);
extern void Pby_Mgm_T10ms(void);
extern void Pby_Mgm_Init(void);
extern void Pby_Mgm_Off(void);

/* Exported data declaration */

/* Declaration for custom storage class: ImportFromFile */
extern uint16_T AbsCntWUC;

#endif                                 /* RTW_HEADER_Pby_Mgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
