#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_TRQSAFLIM_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 

// [Nm]
__declspec(section ".calib") int16_T CMEMAX[2] = 
{
 160, 256
};
// [Nm/Ts]
__declspec(section ".calib") int16_T CMIMAXRATE[2] = 
{
 2, 3
};
//Min Cmi gradient in torque limitation [Nm/Ts]
__declspec(section ".calib") int16_T CMIMINRATE = -2;   //(  -0.06250*32)

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_TRQSAFLIM_
