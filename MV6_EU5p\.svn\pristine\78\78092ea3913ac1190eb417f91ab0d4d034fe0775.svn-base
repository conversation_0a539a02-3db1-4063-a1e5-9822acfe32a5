/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Err [Km/h]
CALQUAL int16_T BKCREEPLIMERR[7] = 
{
 -160, -80, -16, 0, 16, 80, 160
};
//Delta Cmi creeplimiter exit [Nm]
CALQUAL int16_T DELCMICRLIMRET = 160;   //(   5.00000*32)
//Enable creep upshift [fLag]
CALQUAL uint8_T ENCRPUPSHIFT =  1u;   // 1
//Max Cme creeplimiter rate [Nm]
CALQUAL int16_T MAXRATECMICREEP = 64;   //(   2.00000*32)
//Min Cme creeplimiter start [Nm]
CALQUAL int16_T MINCMECREEPSTART = 800;   //(  25.00000*32)
//Min saturation Ctrl [Nm]
CALQUAL int32_T MINCMICREEPSAT = 65536;   //(    1.0000000000000000*65536)
//Min gear creeplimiter start [gear]
CALQUAL uint8_T MINGPCCREEPSTART =  3u;   // 3
//Min Cme creeplimiter rate [Nm]
CALQUAL int16_T MINRATECMICREEP = -64;   //(  -2.00000*32)
//Rpm PI Target [Rpm]
CALQUAL uint16_T RPMCREEPTRG =   1400u;   //  1400
//VehSpeed creep upshift [Km/h]
CALQUAL uint16_T VEHCREEPFREE = 104u;   //(  6.5000*16)
//Prop [gain]
CALQUAL int16_T VTCREEPLIMINTGAIN[7] = 
{
 20, 12, 4, 0, 4, 12, 20
};
//Prop [gain]
CALQUAL int16_T VTCREEPLIMPROPGAIN[7] = 
{
 4096, 1638, 12, 0, 12, 1638, 4096
};
//Max Cme creeplimiter sat [Nm]
CALQUAL int32_T VTMAXCMECREEPSAT[7] = 
{
 1966080, 1966080, 1966080, 1966080, 1966080, 1966080, 1966080
};
//Min Cme creeplimiter sat [Nm]
CALQUAL int32_T VTMINCMECREEPSAT[7] = 
{
 -1966080, -1966080, -1966080, -1966080, -1966080, -1966080, -1966080
};
