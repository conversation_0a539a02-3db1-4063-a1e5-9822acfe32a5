/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "typedefs.h"
#include "saf2mgm.h"

/** Declare here all the variables to be stored in VSRAM memory location **/

#ifdef _BUILD_SAF2MGM_
uint8_t   S2ValErr[SIZE_S2PUNERR]; //must be stored in  EERAM
uint32_t  S2RestartCnt; //must be stored in  EERAM
#endif /* _BUILD_SAF2MGM_ */
uint8_t     GenIvorCnt;



