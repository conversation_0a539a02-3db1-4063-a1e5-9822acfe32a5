/*
 * File: LightOffMgm_private.h
 *
 * Code generated for Simulink model 'LightOffMgm'.
 *
 * Model version                  : 1.97
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jul 11 11:18:54 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Generic->32-bit Embedded Processor
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (29), Warnings (4), Error (0)
 */

#ifndef RTW_HEADER_LightOffMgm_private_h_
#define RTW_HEADER_LightOffMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "LightOffMgm.h"

/* Includes for objects with custom storage classes. */
#include "Trq_driver.h"
#include "af_ctrl.h"
#include "engflag.h"
#include "Diagcanmgm.h"
#include "sabasic_mgm.h"
#include "idle_mgm.h"
#include "patm_model.h"
#include "syncmgm.h"
#include "temp_mgm.h"
#include "throttle_target.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int32_T RATEMAXLOFF;            /* Variable: RATEMAXLOFF
                                        * Referenced by:
                                        *   '<S12>/RATEMAXLOFF'
                                        *   '<S14>/RATEMAXLOFF'
                                        * Light off rate max
                                        */
extern int32_T RATEMINLOFF;            /* Variable: RATEMINLOFF
                                        * Referenced by:
                                        *   '<S12>/RATEMINLOFF'
                                        *   '<S14>/RATEMINLOFF'
                                        * Light off rate min
                                        */
extern int16_T BKTWATLOFF[5];          /* Variable: BKTWATLOFF
                                        * Referenced by:
                                        *   '<S12>/BKTWATLOFF'
                                        *   '<S13>/BKTWATLOFF'
                                        *   '<S14>/BKTWATLOFF'
                                        * Breakpoints of TWaterCrk
                                        */
extern int16_T BKCMELOFF[6];           /* Variable: BKCMELOFF
                                        * Referenced by:
                                        *   '<S12>/BKCMELOFF'
                                        *   '<S14>/BKCMELOFF'
                                        * Vector of breakpoints for CmeDriver
                                        */
extern uint16_T BKRPMLOFF[10];         /* Variable: BKRPMLOFF
                                        * Referenced by:
                                        *   '<S12>/BKRPMLOFF'
                                        *   '<S14>/BKRPMLOFF'
                                        * Vector of breakpoints for rpm
                                        */
extern uint16_T BKTDCLOFF[10];         /* Variable: BKTDCLOFF
                                        * Referenced by: '<S13>/BKTDCLOFF'
                                        * Vector of breakpoints for CntTdcCrk
                                        */
extern uint16_T THPATMLOFF;            /* Variable: THPATMLOFF
                                        * Referenced by: '<S4>/Control_Flow'
                                        * Min Patm to enable lightoff
                                        */
extern int8_T TBSALOFFBASE[60];        /* Variable: TBSALOFFBASE
                                        * Referenced by: '<S14>/TBSALOFFBASE'
                                        * (SR) Table of light off SA offsets on base SA
                                        */
extern int8_T VTSALOFFIDLE[5];         /* Variable: VTSALOFFIDLE
                                        * Referenced by: '<S14>/VTSALOFFIDLE'
                                        * Vector of light off SA offsets at idle
                                        */
extern int8_T TBGNSALOFF[50];          /* Variable: TBGNSALOFF
                                        * Referenced by: '<S13>/TBGNSALOFF'
                                        * (SR) Table of gains for modulating light off
                                        */
extern uint8_T VTDEFFLOFFIDLE[5];      /* Variable: VTDEFFLOFFIDLE
                                        * Referenced by: '<S12>/VTDEFFLOFFIDLE'
                                        * Delta eff idle
                                        */
extern uint8_T TBDEFFLOFFBASE[60];     /* Variable: TBDEFFLOFFBASE
                                        * Referenced by: '<S12>/TBDEFFLOFFBASE'
                                        * Delta eff base
                                        */
extern uint8_T ENLIGHTOFF;             /* Variable: ENLIGHTOFF
                                        * Referenced by: '<S4>/Control_Flow'
                                        * Light off enable flag
                                        */
extern void LightOffMgm_NoSync(void);
extern void LightOffMgm_PowerOn(void);
extern void LightOffMgm_Calc_LOff_SA(void);
extern void LightOffMgm_Control_Flow_Init(void);
extern void LightOffMgm_Control_Flow(void);
extern void LightOffMgm_PreHTDC_Init(void);
extern void LightOffMgm_PreHTDC(void);

#endif                                 /* RTW_HEADER_LightOffMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
