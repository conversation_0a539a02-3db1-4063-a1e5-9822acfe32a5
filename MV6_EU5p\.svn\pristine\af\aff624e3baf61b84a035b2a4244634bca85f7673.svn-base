/*  File    : addairmgm.h
 *  Author  : <PERSON><PERSON><PERSON>
 *  Date    : 23/10/2006 10.10
 *  Revision: AddAirMgm 1.1
 *	Note	  : modificate scalature angoli da 1/128 a 1/16
 * 
 *  Copyright 2006 Eldor Corporation
 */
#ifndef _ADDAIRMGM_H_
#define _ADDAIRMGM_H_

/** include files **/
#include "typedefs.h"

/** local definitions **/
#define BKTWADDAIR_dim		10
#define BKRPMADDAIR_dim		10
#define BKSTPADDAIR_dim 	9
#define BKTDCADDAIR_dim 	4
#define BKTHRADDAIR_dim		5
#define BKGASPOS_dim			10

#define MAX_THROTTLE			1600		// 100% 2^-4
#define MAX_STPOBJ				32768		// 2^15
#define MIN_STPOBJ				0				// 0

/** default settings **/

/** external functions **/

/** external data **/
extern uint16_T AngThrObj0;
extern uint16_T AngThrottle;
extern uint16_T CntTdcCrk;
extern uint8_T  IdleFlg;
extern uint16_T Rpm;
extern uint16_T RpmIdleObj;
extern int16_T  TWater;
extern int16_T  TWaterCrk;
extern uint16_T AngThrCorrObj;

/** internal functions **/

/** public data **/
extern uint16_T	Load;
extern uint16_T	LoadObj;
extern uint16_T BaseAddAir;
extern uint16_T CrankAddAir;
extern uint16_T CrankStep;
extern int16_T  RpmErr;
extern int32_T	IdleAddAir;
extern uint16_T StepperObj;
extern uint16_T StepperAngleObj;
extern uint16_T	AngThrCorr;

/** private data **/

/** public functions **/
void AddAirMgm_Init(void);
void AddAirMgm_T5ms(void);
void AddAirMgm_TDC(void);

/** private functions **/

#endif
