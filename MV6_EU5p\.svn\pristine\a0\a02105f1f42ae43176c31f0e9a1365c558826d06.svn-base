/*
 * File: LamHeaterMgm_private.h
 *
 * Code generated for Simulink model 'LamHeaterMgm'.
 *
 * Model version                  : 1.747
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Jul 10 10:29:05 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_LamHeaterMgm_private_h_
#define RTW_HEADER_LamHeaterMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "LamHeaterMgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "relaymgm.h"
#include "pio.h"
#include "Addairmgm.h"
#include "ThrPosMgm.h"
#include "activeDiag.h"
#include "engflag.h"
#include "loadmgm.h"
#include "syncmgm.h"
#include "temp_mgm.h"
#include "analogin.h"
#include "lambda_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKTWATLHEAT[4];         /* Variable: BKTWATLHEAT
                                        * Referenced by:
                                        *   '<S32>/BKTWATLHEAT'
                                        *   '<S33>/BKTWATLHEAT'
                                        *   '<S33>/BKTWATLHEAT1'
                                        *   '<S34>/BKTWATLHEAT'
                                        *   '<S37>/BKTWATLHEAT'
                                        *   '<S45>/BKTWATLHEAT2'
                                        *   '<S48>/BKTWATLHEAT'
                                        * TWater breakpoints
                                        */
extern int16_T THTWAT2D;               /* Variable: THTWAT2D
                                        * Referenced by: '<S19>/Lambda_Heater_2'
                                        * Coolant temp threshold to bypass in phase B
                                        */
extern int16_T THTWATD;                /* Variable: THTWATD
                                        * Referenced by: '<S18>/Lambda_Heater'
                                        * Coolant temp threshold to bypass in phase B
                                        */
extern uint16_T VBATNORMDC;            /* Variable: VBATNORMDC
                                        * Referenced by: '<S33>/VBATNORMDC'
                                        * Nominal voltage for lambda sensor heating
                                        */
extern uint16_T VTTHRVO2H[4];          /* Variable: VTTHRVO2H
                                        * Referenced by: '<S45>/VTTHRVO2H'
                                        * Lambda diagnosis threshold
                                        */
extern uint16_T VTTHRVO2H2[4];         /* Variable: VTTHRVO2H2
                                        * Referenced by: '<S34>/VTTHRVO2H2'
                                        * Lambda 2 diagnosis threshold
                                        */
extern uint16_T BKLOADLHEAT[3];        /* Variable: BKLOADLHEAT
                                        * Referenced by:
                                        *   '<S37>/BKLOADLHEAT'
                                        *   '<S48>/BKLOADLHEAT'
                                        * Load breakpoints
                                        */
extern uint16_T BKRPMLHEAT[4];         /* Variable: BKRPMLHEAT
                                        * Referenced by:
                                        *   '<S37>/BKRPMLHEAT'
                                        *   '<S48>/BKRPMLHEAT'
                                        * Rpm breakpoints
                                        */
extern uint16_T THEAT2A;               /* Variable: THEAT2A
                                        * Referenced by: '<S19>/Lambda_Heater_2'
                                        * Duration of phase A for lambda 2 sensor heating
                                        */
extern uint16_T THEATA;                /* Variable: THEATA
                                        * Referenced by: '<S18>/Lambda_Heater'
                                        * Duration of phase A for lambda sensor heating
                                        */
extern uint16_T THEATREPRISE;          /* Variable: THEATREPRISE
                                        * Referenced by: '<S18>/Lambda_Heater'
                                        * Minimum time in off mode to restart heating
                                        */
extern uint16_T THEATREPRISE2;         /* Variable: THEATREPRISE2
                                        * Referenced by: '<S19>/Lambda_Heater_2'
                                        * Minimum time in off mode to restart heating 2
                                        */
extern uint8_T VTTHEATB[4];            /* Variable: VTTHEATB
                                        * Referenced by: '<S33>/VTTHEATB'
                                        * Duration of phase B for lambda sensor heating
                                        */
extern uint8_T VTTHEATB2[4];           /* Variable: VTTHEATB2
                                        * Referenced by: '<S32>/VTTHEATB2'
                                        * Duration of phase B for lambda 2 sensor heating
                                        */
extern uint8_T VTTHEATC[4];            /* Variable: VTTHEATC
                                        * Referenced by: '<S33>/VTTHEATC'
                                        * Duration of phase C for lambda sensor heating
                                        */
extern uint8_T VTTHEATC2[4];           /* Variable: VTTHEATC2
                                        * Referenced by: '<S32>/VTTHEATC2'
                                        * Duration of phase C for lambda 2 sensor heating
                                        */
extern uint8_T TBDCLAMHD[12];          /* Variable: TBDCLAMHD
                                        * Referenced by: '<S48>/TBDCLAMHD'
                                        * Lambda heater duty cycle in phase D
                                        */
extern uint8_T TBDCLAMHD2[12];         /* Variable: TBDCLAMHD2
                                        * Referenced by: '<S37>/TBDCLAMHD2'
                                        * Lambda 2 heater duty cycle in phase D
                                        */
extern uint8_T ENLAMHEATER;            /* Variable: ENLAMHEATER
                                        * Referenced by:
                                        *   '<S18>/Lambda_Heater'
                                        *   '<S19>/Lambda_Heater_2'
                                        * Enable lambda sensor heating
                                        */
extern uint8_T SELLAMHLOAD;            /* Variable: SELLAMHLOAD
                                        * Referenced by: '<S17>/SELLAMHLOAD'
                                        * Select Load input
                                        */
extern uint8_T VTDCLAMHEATERA[4];      /* Variable: VTDCLAMHEATERA
                                        * Referenced by: '<S48>/VTDCLAMHEATERA'
                                        * Lambda heater duty cycle in phase A
                                        */
extern uint8_T VTDCLAMHEATERA2[4];     /* Variable: VTDCLAMHEATERA2
                                        * Referenced by: '<S37>/VTDCLAMHEATERA2'
                                        * Lambda 2 heater duty cycle in phase A
                                        */
extern uint8_T VTDCLAMHEATERC[4];      /* Variable: VTDCLAMHEATERC
                                        * Referenced by: '<S48>/VTDCLAMHEATERC'
                                        * Lambda heater duty cycle in phase C
                                        */
extern uint8_T VTDCLAMHEATERC2[4];     /* Variable: VTDCLAMHEATERC2
                                        * Referenced by: '<S37>/VTDCLAMHEATERC2'
                                        * Lambda 2 heater duty cycle in phase C
                                        */
extern void LamHeaterMgm_fc_diag_func(uint8_T rtu_ptFault);
extern void LamHeaterMgm_prog_duty(void);
extern void LamHeaterMgm_active_diag_entry(void);
extern void LamHeaterMgm_active_diag_exit(void);
extern void LamHeaterMgm_fc_diag_func2(uint8_T rtu_ptFault2);
extern void LamHeaterMgm_prog_duty2(void);
extern void LamHeaterMgm_active_diag_entry2(void);
extern void LamHeaterMgm_active_diag_exit2(void);
extern void LamHeaterMgm_T100ms_Init(void);
extern void LamHeaterMgm_T100ms(void);

#endif                                 /* RTW_HEADER_LamHeaterMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
