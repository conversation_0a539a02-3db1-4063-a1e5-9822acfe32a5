/*
 * File: GearPosCluMgm.h
 *
 * Code generated for Simulink model 'GearPosCluMgm'.
 *
 * Model version                  : 1.323
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Jun 12 09:40:12 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GearPosCluMgm_h_
#define RTW_HEADER_GearPosCluMgm_h_
#ifndef GearPosCluMgm_COMMON_INCLUDES_
# define GearPosCluMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#endif                                 /* GearPosCluMgm_COMMON_INCLUDES_ */

#include "GearPosCluMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "GearPosClu_mgm.h"

/* Macros for accessing real-time model data structure */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  struct {
    uint_T is_c6_GearPosCluMgm:3;      /* '<S10>/LocalGear_calc' */
    uint_T is_c1_GearPosCluMgm:2;      /* '<S9>/Chart1' */
    uint_T is_active_c6_GearPosCluMgm:1;/* '<S10>/LocalGear_calc' */
    uint_T is_active_c1_GearPosCluMgm:1;/* '<S9>/Chart1' */
  } bitsForTID0;

  uint8_T localGear;                   /* '<S10>/LocalGear_calc' */
  uint8_T out;                         /* '<S9>/Chart1' */
  uint8_T cnt;                         /* '<S9>/Chart1' */
  uint8_T oldGearPos;                  /* '<S9>/Chart1' */
} D_Work_GearPosCluMgm_T;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
  ZCSigState trig_to_fc_Trig_ZCE;      /* '<S1>/trig_to_fc' */
} PrevZCSigStates_GearPosCluMgm_T;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PowerOff;                 /* '<Root>/ev_PowerOff' */
  uint8_T GearPos_ncq;                 /* '<Root>/GearPos' */
  uint8_T ClutchSignal_bei;            /* '<Root>/ClutchSignal' */
  uint8_T ClutchSignal2_exs;           /* '<Root>/ClutchSignal2' */
  uint8_T DiagFlg00_mgu;               /* '<Root>/DiagFlg00' */
  uint8_T DiagFlg03_ism;               /* '<Root>/DiagFlg03' */
  uint8_T DiagFlg04_hbd;               /* '<Root>/DiagFlg04' */
  int16_T DVehSpeedRearRbLc_lwp;       /* '<Root>/DVehSpeedRearRbLc' */
} ExternalInputs_GearPosCluMgm_T;

/* Block signals and states (default storage) */
extern D_Work_GearPosCluMgm_T GearPosCluMgm_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_GearPosCluMgm_T GearPosCluMgm_U;

/* Model entry point functions */
extern void GearPosCluMgm_initialize(void);
extern void GearPosCluMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'GearPosCluMgm'
 * '<S1>'   : 'GearPosCluMgm/GearPosCluMgm'
 * '<S2>'   : 'GearPosCluMgm/Model Info'
 * '<S3>'   : 'GearPosCluMgm/GearPosCluMgm/Init'
 * '<S4>'   : 'GearPosCluMgm/GearPosCluMgm/Off'
 * '<S5>'   : 'GearPosCluMgm/GearPosCluMgm/T10ms'
 * '<S6>'   : 'GearPosCluMgm/GearPosCluMgm/trig_to_fc'
 * '<S7>'   : 'GearPosCluMgm/GearPosCluMgm/trig_to_fc1'
 * '<S8>'   : 'GearPosCluMgm/GearPosCluMgm/trig_to_fc2'
 * '<S9>'   : 'GearPosCluMgm/GearPosCluMgm/T10ms/Calc_ClutchSignalRb'
 * '<S10>'  : 'GearPosCluMgm/GearPosCluMgm/T10ms/GearPosClu_Calc'
 * '<S11>'  : 'GearPosCluMgm/GearPosCluMgm/T10ms/Calc_ClutchSignalRb/Chart1'
 * '<S12>'  : 'GearPosCluMgm/GearPosCluMgm/T10ms/Calc_ClutchSignalRb/Compare To Zero'
 * '<S13>'  : 'GearPosCluMgm/GearPosCluMgm/T10ms/Calc_ClutchSignalRb/Compare To Zero1'
 * '<S14>'  : 'GearPosCluMgm/GearPosCluMgm/T10ms/GearPosClu_Calc/LocalGear_calc'
 */

/*-
 * Requirements for '<Root>': GearPosCluMgm
 */
#endif                                 /* RTW_HEADER_GearPosCluMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
