/*
 * File: patm_model.h
 *
 * Code generated for Simulink model 'PAtmModel'.
 *
 * Model version                  : 1.764
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jun 21 11:58:30 2022
 */

#ifndef RTW_HEADER_patm_model_h_
#define RTW_HEADER_patm_model_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint16_T AngThrPAtmMax;

/* PATMMODEL.AngThrPAtmMax: Max AngThrottle value for atmospheric pressure estimation */
extern uint16_T AngThrPAtmMin;

/* PATMMODEL.AngThrPAtmMin: Min AngThrottle value for atmospheric pressure estimation */
extern uint8_T FlgPAtmEst;

/* PATMMODEL.FlgPAtmEst: Estimation correctly terminated */
extern uint16_T PAtmBuffer;

/* PATMMODEL.PAtmBuffer: Buffer for atmospheric pressure estimation */
extern uint16_T PresAtmTmp;

/* PATMMODEL.PresAtmTmp: Atmospheric pressure estimated (not saturated) */
extern uint8_T StPAtmRun;

/* PATMMODEL.StPAtmRun: Running estimation state */
extern uint8_T StPAtmStart;

/* PATMMODEL.PresAtm: EE Presuure atmosferic */
extern uint16_T PresAtm;

extern void PAtmModel_PowerOn(void);
extern void PAtmModel_T5ms(void);
extern void PAtmModel_T100ms(void);
extern void PAtmModel_TDC(void);

/* PATMMODEL.StPAtmStart: PowerOn estimation state */
#endif                                 /* RTW_HEADER_patm_model_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
