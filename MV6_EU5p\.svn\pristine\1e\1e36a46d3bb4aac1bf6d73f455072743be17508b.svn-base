/*  File    : watpumpmgm.h
 *  Copyright 2007 Eldor Corporation
 */
#ifndef _WATPUMPMGM_H_
#define _WATPUMPMGM_H_

/** include files **/
#include "typedefs.h"
#include "analogin.h"
#include "pio.h"
#include "relaymgm.h"

/** common definitions **/
#if ((ENGINE_TYPE != MV_AGUSTA_3C_TDC_0_08) && (ENGINE_TYPE != MV_AGUSTA_4C) && (ENGINE_TYPE != MV_AGUSTA_3C_TDC_0_20) && (ENGINE_TYPE != MV_AGUSTA_3C_TDC_0_30) && (ENGINE_TYPE != MV_AGUSTA_4C_TDC_0_9))
  #define Water_Pump_PWM      ETPUA_UC16
#endif  
#define WAT_PUMP_DUTY_MAX   25600
#define WAT_PUMP_DUTY_MIN   0
#undef EN_WAT_PUMP_ON_OFF

// StWatPumpDiag
#define NO_FORCED_WAIT_MAX  0
#define MAX_DUTY_FORCED     1
#define NO_FORCED_WAIT_MIN  2
#define MIN_DUTY_FORCED     3

// StWatPumpValidDiad
#define NONE 0
#define DUTY_MAX_FORCING 1
#define DUTY_MIN_FORCING 2

/** external functions **/


/** external data **/
extern uint8_T FlgWatPumpCtrlOn;
extern uint16_T WatPumpPWMDuty;
extern uint16_T WatPumpEffDuty;
extern uint8_T  WatPumpPwmEnFlg;
extern uint8_T LoadCmd;

/** internal functions **/

/** public data **/   

/** private data **/

/** public functions **/
extern void WatPumpMgm_Init(void);
extern void WatPumpMgm_T5ms(void);
extern void WatPumpMgm_T100ms(void);
extern void WatPumpMgm_ActiveDiagInit(uint8_T diagstate, uint8_T rst_validdiag);

/** private functions **/

//extern int16_t WatPumpMgm_PwmEnable(uint16_t watpumpmgm_Input);

#endif
