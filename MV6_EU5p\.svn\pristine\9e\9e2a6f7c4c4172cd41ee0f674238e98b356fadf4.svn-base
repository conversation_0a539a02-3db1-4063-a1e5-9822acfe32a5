/*
 * File: TracCtrl.h
 *
 * Code generated for Simulink model 'TracCtrl'.
 *
 * Model version                  : 1.825
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 13:03:51 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TracCtrl_h_
#define RTW_HEADER_TracCtrl_h_
#ifndef TracCtrl_COMMON_INCLUDES_
# define TracCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* TracCtrl_COMMON_INCLUDES_ */

#include "TracCtrl_types.h"

/* Includes for objects with custom storage classes. */
#include "canmgm.h"
#include "trac_ctrl.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKACCDVS_dim                   6U                        /* Referenced by: '<S67>/BKACCDVS_dim' */
#define BKCMEESTWHEEL_dim              9U                        /* Referenced by:
                                                                  * '<S24>/BKCMEESTWHEEL_dim'
                                                                  * '<S29>/BKCMEESTWHEEL_dim'
                                                                  */
#define BKDCMEESTWHEEL_dim             5U                        /* Referenced by:
                                                                  * '<S24>/BKDCMEESTWHEEL_dim'
                                                                  * '<S29>/BKDCMEESTWHEEL_dim'
                                                                  */
#define BKDVSERRCUTOFF_dim             4U                        /* Referenced by: '<S23>/BKDVSERRCUTOFF_dim' */
#define BKDVSERR_dim                   6U                        /* Referenced by:
                                                                  * '<S67>/BKDVSERR_dim'
                                                                  * '<S81>/BKDVSERR_dim'
                                                                  */
#define BKIDTRACCTRLRID_dim            2U                        /* Referenced by:
                                                                  * '<S24>/BKIDTRACCTRLRID_dim'
                                                                  * '<S27>/BKIDTRACCTRLRID_dim'
                                                                  * '<S29>/BKIDTRACCTRLRID_dim'
                                                                  */
#define BKIDTRACCTRL_dim               7U                        /* Referenced by: '<S28>/BKIDTRACCTRL_dim' */
#define BKROLLANGLETARGCORR_dim        6U                        /* Referenced by:
                                                                  * '<S24>/BKROLLANGLETARGCORR_dim'
                                                                  * '<S64>/BKROLLANGLETARGCORR_dim'
                                                                  * '<S65>/BKROLLANGLETARGCORR_dim'
                                                                  * '<S28>/BKROLLANGLETARGCORR_dim'
                                                                  * '<S29>/BKROLLANGLETARGCORR_dim1'
                                                                  * '<S81>/BKROLLANGLETARGCORR_dim'
                                                                  */
#define BKROLLTCINIT_dim               2U                        /* Referenced by:
                                                                  * '<S24>/BKROLLTCINIT_dim'
                                                                  * '<S27>/BKROLLTCINIT_dim'
                                                                  */
#define BKSMINTGAIN_dim                3U                        /* Referenced by: '<S80>/BKSMINTGAIN_dim' */
#define BKTCLEVIDX_dim                 2U                        /* Referenced by:
                                                                  * '<S64>/BKTCLEVIDX_dim'
                                                                  * '<S65>/BKTCLEVIDX_dim'
                                                                  * '<S81>/BKTCLEVIDX_dim'
                                                                  */
#define BKTCRPMCUTOFF_dim              2U                        /* Referenced by: '<S23>/BKTCRPMCUTOFF_dim' */
#define BKVEHSPEEDTRAC_dim             6U                        /* Referenced by:
                                                                  * '<S24>/BKVEHSPEEDTRAC_dim'
                                                                  * '<S28>/BKVEHSPEEDTRAC_dim'
                                                                  */
#define ID_TRAC_CTRL                   30618419U                 /* Referenced by: '<S3>/ID_TRAC_CTRL' */

/* mask */
#define MAX_sat                        32767                     /* Referenced by: '<S9>/Calc_TracCtrl_DVS' */

/* Block signals for system '<S9>/fc_TC_AccWheelCalc' */
typedef struct {
  uint16_T Sum5;                       /* '<S22>/Sum5' */
  int16_T Sum2;                        /* '<S25>/Sum2' */
  int16_T Switch;                      /* '<S20>/Switch' */
  int16_T Conversion;                  /* '<S49>/Conversion' */
} rtB_fc_TC_AccWheelCalc_TracCtrl;

/* Block states (default storage) for system '<S9>/fc_TC_AccWheelCalc' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S45>/Memory' */
  struct {
    uint_T is_c7_TracCtrl:2;           /* '<S46>/init_rate' */
    uint_T is_active_c7_TracCtrl:1;    /* '<S46>/init_rate' */
  } bitsForTID0;
} rtDW_fc_TC_AccWheelCalc_TracCtr;

/* Block signals for system '<S64>/Prop_Acc_To_Smooth' */
typedef struct {
  int32_T TCPropPTerm_out;             /* '<S64>/Prop_Acc_To_Smooth' */
} rtB_Prop_Acc_To_Smooth_TracCtrl;

/* Block states (default storage) for system '<S64>/Prop_Acc_To_Smooth' */
typedef struct {
  struct {
    uint_T is_c8_TracCtrl:2;           /* '<S64>/Prop_Acc_To_Smooth' */
    uint_T is_active_c8_TracCtrl:1;    /* '<S64>/Prop_Acc_To_Smooth' */
  } bitsForTID0;
} rtDW_Prop_Acc_To_Smooth_TracCtr;

/* Block signals for system '<S9>/fc_TC_CmiControl' */
typedef struct {
  int16_T Product2;                    /* '<S15>/Product2' */
  int16_T Product4;                    /* '<S15>/Product4' */
  rtB_Prop_Acc_To_Smooth_TracCtrl sf_Prop_Acc_To_Smooth_o;/* '<S65>/Prop_Acc_To_Smooth' */
  rtB_Prop_Acc_To_Smooth_TracCtrl sf_Prop_Acc_To_Smooth;/* '<S64>/Prop_Acc_To_Smooth' */
} rtB_fc_TC_CmiControl_TracCtrl;

/* Block states (default storage) for system '<S9>/fc_TC_CmiControl' */
typedef struct {
  rtDW_Prop_Acc_To_Smooth_TracCtr sf_Prop_Acc_To_Smooth_o;/* '<S65>/Prop_Acc_To_Smooth' */
  rtDW_Prop_Acc_To_Smooth_TracCtr sf_Prop_Acc_To_Smooth;/* '<S64>/Prop_Acc_To_Smooth' */
} rtDW_fc_TC_CmiControl_TracCtrl;

/* Block signals for system '<S87>/Function-Call Subsystem' */
typedef struct {
  int16_T DataTypeConversion;          /* '<S92>/Data Type Conversion' */
  int16_T DataTypeConversion1;         /* '<S92>/Data Type Conversion1' */
} rtB_FunctionCallSubsystem_TracC;

/* Block states (default storage) for system '<S87>/Function-Call Subsystem' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S92>/Memory' */
  int32_T Memory1_PreviousInput;       /* '<S92>/Memory1' */
} rtDW_FunctionCallSubsystem_Trac;

/* Block signals for system '<S1>/T10ms' */
typedef struct {
  int16_T Merge1;                      /* '<S89>/Merge1' */
  int16_T Switch;                      /* '<S11>/Switch' */
  uint8_T Merge4;                      /* '<S89>/Merge4' */
  uint8_T DiagFlg00FO;                 /* '<S87>/Chart' */
  uint8_T DiagFlg01FO_j;               /* '<S87>/Chart' */
  uint8_T outFlgYawRecFO;              /* '<S87>/Chart' */
  uint8_T reset;                       /* '<S87>/Chart' */
  uint8_T FlgYawRecFO_e;               /* '<S87>/Chart' */
  uint8_T flg_reset_filt;              /* '<S9>/Calc_TracCtrl_DVS' */
  uint8_T localgear;                   /* '<S9>/Calc_TracCtrl_DVS' */
  uint8_T flg_cmi_virtual_sat;         /* '<S9>/Calc_TracCtrl_DVS' */
  uint8_T recSmooth;                   /* '<S9>/Calc_TracCtrl_DVS' */
  rtB_FunctionCallSubsystem_TracC FunctionCallSubsystem;/* '<S87>/Function-Call Subsystem' */
  rtB_fc_TC_CmiControl_TracCtrl fc_TC_CmiControl;/* '<S9>/fc_TC_CmiControl' */
  rtB_fc_TC_AccWheelCalc_TracCtrl fc_TC_AccWheelCalc;/* '<S9>/fc_TC_AccWheelCalc' */
} rtB_T10ms_TracCtrl;

/* Block states (default storage) for system '<S1>/T10ms' */
typedef struct {
  struct {
    uint_T is_TC_ENABLED:3;            /* '<S9>/Calc_TracCtrl_DVS' */
    uint_T is_c3_TracCtrl:2;           /* '<S87>/Chart' */
    uint_T is_GEAR_MANAGE:2;           /* '<S9>/Calc_TracCtrl_DVS' */
    uint_T is_GEAR_CHANGE:2;           /* '<S9>/Calc_TracCtrl_DVS' */
    uint_T is_NO_CHANGE:2;             /* '<S9>/Calc_TracCtrl_DVS' */
    uint_T is_SPRING_UP:2;             /* '<S9>/Calc_TracCtrl_DVS' */
    uint_T is_TRAC_CTRL_FSM:2;         /* '<S9>/Calc_TracCtrl_DVS' */
    uint_T is_active_c3_TracCtrl:1;    /* '<S87>/Chart' */
    uint_T is_active_c5_TracCtrl:1;    /* '<S9>/Calc_TracCtrl_DVS' */
  } bitsForTID0;

  uint16_T index_RollCAN;              /* '<S9>/Data Store Memory16' */
  uint16_T cnt;                        /* '<S87>/Chart' */
  uint16_T cntspuexit;                 /* '<S9>/Calc_TracCtrl_DVS' */
  uint16_T ratio_RollCAN;              /* '<S9>/Data Store Memory1' */
  uint8_T returRec;                    /* '<S9>/Data Store Memory2' */
  uint8_T oldStartSignal;              /* '<S87>/Chart' */
  uint8_T cnt_h;                       /* '<S9>/Calc_TracCtrl_DVS' */
  uint8_T localgear_old;               /* '<S9>/Calc_TracCtrl_DVS' */
  uint8_T cntspu;                      /* '<S9>/Calc_TracCtrl_DVS' */
  rtDW_FunctionCallSubsystem_Trac FunctionCallSubsystem;/* '<S87>/Function-Call Subsystem' */
  rtDW_fc_TC_CmiControl_TracCtrl fc_TC_CmiControl;/* '<S9>/fc_TC_CmiControl' */
  rtDW_fc_TC_AccWheelCalc_TracCtr fc_TC_AccWheelCalc;/* '<S9>/fc_TC_AccWheelCalc' */
} rtDW_T10ms_TracCtrl;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState Trig_2_fc2_Trig_ZCE;      /* '<S1>/Trig_2_fc2' */
  ZCSigState Trig_2_fc1_Trig_ZCE[2];   /* '<S1>/Trig_2_fc1' */
  ZCSigState Trig_2_fc_Trig_ZCE;       /* '<S1>/Trig_2_fc' */
} PrevZCSigStates_TracCtrl;

/* Invariant block signals for system '<S9>/fc_TC_AccWheelCalc' */
typedef struct {
  const uint16_T DataTypeConversion[3];/* '<S24>/Data Type Conversion' */
} rtC_fc_TC_AccWheelCalc_TracCtrl;

/* Invariant block signals for system '<S1>/T10ms' */
typedef struct {
  rtC_fc_TC_AccWheelCalc_TracCtrl fc_TC_AccWheelCalc;/* '<S9>/fc_TC_AccWheelCalc' */
} rtC_T10ms_TracCtrl;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_PreTDC;                   /* '<Root>/ev_PreTDC' */
} ExternalInputs_TracCtrl;

/* Extern declarations of internal data for system '<S1>/T10ms' */
extern rtB_T10ms_TracCtrl TracCtrl_T10ms_B;
extern rtDW_T10ms_TracCtrl TracCtrl_T10ms_DW;
extern const rtC_T10ms_TracCtrl TracCtrl_T10ms_C;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_TracCtrl TracCtrl_U;

/* Model entry point functions */
extern void TracCtrl_initialize(void);
extern void TracCtrl_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('TracCtrl_gen/Model/TracCtrl')    - opens subsystem TracCtrl_gen/Model/TracCtrl
 * hilite_system('TracCtrl_gen/Model/TracCtrl/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TracCtrl_gen/Model'
 * '<S1>'   : 'TracCtrl_gen/Model/TracCtrl'
 * '<S2>'   : 'TracCtrl_gen/Model/TracCtrl/PreTDC'
 * '<S3>'   : 'TracCtrl_gen/Model/TracCtrl/Reset'
 * '<S4>'   : 'TracCtrl_gen/Model/TracCtrl/T10ms'
 * '<S5>'   : 'TracCtrl_gen/Model/TracCtrl/Trig_2_fc'
 * '<S6>'   : 'TracCtrl_gen/Model/TracCtrl/Trig_2_fc1'
 * '<S7>'   : 'TracCtrl_gen/Model/TracCtrl/Trig_2_fc2'
 * '<S8>'   : 'TracCtrl_gen/Model/TracCtrl/Reset/fc_Reset'
 * '<S9>'   : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model'
 * '<S10>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec'
 * '<S11>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/Selector'
 * '<S12>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/Calc_TracCtrl_DVS'
 * '<S13>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc'
 * '<S14>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Assign'
 * '<S15>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl'
 * '<S16>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_GrUp_Filt_Assign'
 * '<S17>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Limit'
 * '<S18>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_NoLimit'
 * '<S19>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth'
 * '<S20>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold'
 * '<S21>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc'
 * '<S22>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IdTracCtrlRid'
 * '<S23>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IdxTcCutOff'
 * '<S24>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio'
 * '<S25>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_DVSCtrlErr'
 * '<S26>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_DVSSmoothLev'
 * '<S27>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_OffCMITRACP'
 * '<S28>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target'
 * '<S29>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model'
 * '<S30>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_OffCMITRACP/Look2D_IR_S16'
 * '<S31>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_OffCMITRACP/Look2D_IR_S16/Data Type Conversion Inherited1'
 * '<S32>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target/Look2D_IR_S16_3'
 * '<S33>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target/Look2D_IR_U16'
 * '<S34>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target/RateLimiter_S16'
 * '<S35>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target/Look2D_IR_S16_3/Data Type Conversion Inherited1'
 * '<S36>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S37>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Delta_Target/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S38>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model/Look2D_IR_S16_1'
 * '<S39>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model/Look2D_IR_S16_2'
 * '<S40>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model/Look2D_IR_S16_3'
 * '<S41>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model/Look2D_IR_S16_1/Data Type Conversion Inherited1'
 * '<S42>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model/Look2D_IR_S16_2/Data Type Conversion Inherited1'
 * '<S43>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_AccRear_Threshold/Calc_Slip_Model/Look2D_IR_S16_3/Data Type Conversion Inherited1'
 * '<S44>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate'
 * '<S45>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrl_Filt'
 * '<S46>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl'
 * '<S47>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/RateLimiter_S16'
 * '<S48>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/init_rate'
 * '<S49>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S50>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrl_Filt/FOF_Reset_S16_FXP'
 * '<S51>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrl_Filt/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S52>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IdxTcCutOff/Look2D_U16_S16_U1'
 * '<S53>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IdxTcCutOff/Look2D_U16_S16_U16'
 * '<S54>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IdxTcCutOff/Look2D_U16_S16_U1/Data Type Conversion Inherited1'
 * '<S55>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IdxTcCutOff/Look2D_U16_S16_U16/Data Type Conversion Inherited1'
 * '<S56>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/Compare To Constant'
 * '<S57>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/PreLookUpIdSearch_S16_1'
 * '<S58>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/PreLookUpIdSearch_S16_2'
 * '<S59>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/PreLookUpIdSearch_S16_3'
 * '<S60>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/PreLookUpIdSearch_S16_4'
 * '<S61>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/PreLookUpIdSearch_U16_1'
 * '<S62>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_IndexRatio/PreLookUpIdSearch_U16_2'
 * '<S63>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_PostGain'
 * '<S64>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI'
 * '<S65>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINP'
 * '<S66>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/KI'
 * '<S67>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/KP'
 * '<S68>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Compare To Zero'
 * '<S69>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Look2D_IR_S16'
 * '<S70>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Prop_Acc_To_Smooth'
 * '<S71>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Look2D_IR_S16/Data Type Conversion Inherited1'
 * '<S72>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINP/Compare To Zero'
 * '<S73>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINP/Look2D_IR_S16'
 * '<S74>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINP/Prop_Acc_To_Smooth'
 * '<S75>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINP/Look2D_IR_S16/Data Type Conversion Inherited1'
 * '<S76>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/KP/Look2D_S16_S16_S16'
 * '<S77>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/KP/Look2D_S16_S16_S16/Data Type Conversion Inherited1'
 * '<S78>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Assign_Smooth'
 * '<S79>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio'
 * '<S80>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_DVSSmInt_Gain'
 * '<S81>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_StepSmooth'
 * '<S82>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_DVSSmInt_Gain/LookUp_S16_S16'
 * '<S83>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_DVSSmInt_Gain/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S84>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_StepSmooth/Compare To Constant'
 * '<S85>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_StepSmooth/Look2D_U8_S16_S16'
 * '<S86>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_Smooth/Calc_Smooth_Ratio/Calc_StepSmooth/LookUp_U8_S16'
 * '<S87>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec'
 * '<S88>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/KEY_0x1CABF38'
 * '<S89>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/Merge'
 * '<S90>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/Normal_Signal'
 * '<S91>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Chart'
 * '<S92>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Function-Call Subsystem'
 * '<S93>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Function-Call Subsystem/Compare To Zero'
 * '<S94>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Function-Call Subsystem/FOF_Reset_S16_FXP'
 * '<S95>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Function-Call Subsystem/FOF_Reset_S16_FXP1'
 * '<S96>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Function-Call Subsystem/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S97>'  : 'TracCtrl_gen/Model/TracCtrl/T10ms/FO_Rec/FO_Rec/Function-Call Subsystem/FOF_Reset_S16_FXP1/Data Type Conversion Inherited1'
 */
#endif                                 /* RTW_HEADER_TracCtrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
