/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "digitalout.h"
#include "digio.h"
#include "pio.h"
#include "lamheater_mgm.h"

#undef  _LED_T5MS_    //ECU LED pilotato al Task 100ms
#undef  _LED_T100MS_    //ECU LED pilotato al Task 100ms

#ifdef _BUILD_DIGITALOUT_

void DigitalOut_Init(void)
{
#ifdef Vbatt2_off 
    DIGIO_OutCfgExt(Vbatt2_off, 1, OUT_PUSHPULL, DRVSTR_50PF, MIN_SLEWRATE);
#endif
#if defined _LED_T100MS_ || defined _LED_T5MS_
    DIGIO_OutCfgExt(LD1, LED_OFF, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif
    // 139 --> ODE_H_Lambda_1
    SYS_OutPinConfig(ODE_H_Lambda_1, LAMHEAT_PWM_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(PIO_HLAMBDA_1, LAMHEAT_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0, LAM_HEAT_PERIOD, 0);
    PIO_PwmOutEnable(PIO_HLAMBDA_1);

    // 138 --> ODE_H_Lambda_2
    SYS_OutPinConfig(ODE_H_Lambda_2, LAMHEAT_2_PWM_FUNC, INPUT_ENABLE, DRIVE_STRENGTH_10PF, PUSH_PULL_ENABLE, MINIMUM_SLEW_RATE);
    PIO_PwmOutConfig(PIO_HLAMBDA_2, LAMHEAT_PWM_ACTIVE_LEVEL, PWM_MATCH_TIME, 0, LAM_HEAT_PERIOD, 0);
    PIO_PwmOutEnable(PIO_HLAMBDA_2);
}

void DigitalOut_T10ms(void)
{
#ifdef Vbatt2_off 
  DIGIO_OutSet(Vbatt2_off,KeySignal);
#endif
}

void DigitalOut_T100ms(void)
{
#ifdef _LED_T100MS_
      {
        uint8_t pin;
        DIGIO_OutGet(LD1, (&pin));
        DIGIO_OutSet(LD1, !pin);
      }
#endif
}


#endif
