/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _LOADMGM_H_
#define _LOADMGM_H_

/** include files **/
#include "typedefs.h"

/** local definitions **/
#define USE_MAPRATIO			0
#define USE_LOAD_CAN			1
#define USE_ANGTHR  			2
#define USE_DBW						3
#define USE_QAIRRATIO 		4

#ifdef  MATLAB_MEX_FILE
#define LOAD_TYPE     USE_QAIRRATIO
#define _BUILD_SPARKMGM_

#elif (ENGINE_TYPE==VW_1400_4C_16V)
#define LOAD_TYPE     USE_MAPRATIO

#elif (ENGINE_TYPE==EM_VW_1400_4C_16V)
#define LOAD_TYPE     USE_DBW

#elif (ENGINE_TYPE==PI_500_1C_4V)
#define LOAD_TYPE     USE_ANGTHR

#elif (ENGINE_TYPE==YP_250_G)
#define LOAD_TYPE     USE_QAIRRATIO

#elif (ENGINE_TYPE==FE_6300_12C_48V)
#define LOAD_TYPE     USE_LOAD_CAN

#elif (ENGINE_TYPE==FE_4300_8C_32V) || (ENGINE_TYPE==FE_4300_8C_32V_TDN)  || (ENGINE_TYPE==FE_4300_8C_32V_GT2)
#define LOAD_TYPE     USE_LOAD_CAN

#elif (ENGINE_TYPE==MA_MC12_12C)
#define LOAD_TYPE     USE_LOAD_CAN

#elif (ENGINE_TYPE==MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
#define LOAD_TYPE     USE_QAIRRATIO

#elif (ENGINE_TYPE==PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_08) || (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20) || (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
#define LOAD_TYPE     USE_QAIRRATIO

#else
#error ATTENZIONE: Combinazione proibita!!!
#endif

/** default settings **/

/** external functions **/

/** external data **/
extern uint16_T AngThrCorr;  // 2^-4
extern uint16_T AngThrObj0;  // 2^-4
extern uint16_T AngThrottle;  // 2^-4
extern uint16_T MapRatio;  // 2^-7
extern uint16_T LoadCAN;  // 2^-7
extern uint8_T  VDLoadCAN;  // 2^0
extern uint16_T CutoffGain;  // 2^-10

/** internal functions **/

/** public data **/
extern uint16_T    Load;  // 2^-7
extern uint16_T    LoadObj;  // 2^-7
extern uint16_T    LoadObjMax;  // 2^-7
/* VtLoad used only in engine control projects */
extern uint16_T VtLoad[];                    /* 2^-7 Range 0 .. 100% */

/** private data **/

/** public functions **/
void LoadMgm_Init(void);
void LoadMgm_T5ms(void);
void LoadMgm_TDC(void);
void LoadMgm_HTDC(void);

/** private functions **/

#endif // _LOADMGM_H_
