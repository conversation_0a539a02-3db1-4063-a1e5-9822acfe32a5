/*
 * File: mul_s32_loSR_sat.h
 *
 * Code generated for Simulink model 'CmiSatMgm'.
 *
 * Model version                  : 1.199
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Tue Sep 10 09:05:22 2019
 */

#ifndef SHARE_mul_s32_loSR_sat
#define SHARE_mul_s32_loSR_sat
#include "rtwtypes.h"

extern int32_T mul_s32_loSR_sat(int32_T a, int32_T b, uint32_T aShift);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
