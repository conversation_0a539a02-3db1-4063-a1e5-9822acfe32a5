/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_ACTIVE_DIAG_

#ifndef _BUILD_DIAGCANMGM_
#error Active_Diag module enabled without _BUILD_DIAGCANMGM_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/    
#include "typedefs.h"
#include "digio.h"
#include "diagcanmgm.h"
#include "diagmgm_out.h"
#include "fuel_mgm.h"
#include "injcmd.h"
#include "IMMOapp.h"
#include "recmgm.h"
#include "pio.h"
#include "ign.h"
#include "inj.h"
#include "ee.h"
#include "activeDiag.h"
#include "selfmgm.h"
#include "watpumpmgm.h"
#include "wattempmgm.h"
#include "tach_cons.h"
#include "lamp_mgm.h"
#include "eemgm.h"
#include "injcmd.h"
#include "Hbridge.h"
#include "censorship_recovery_ssd.h"
#include "timing.h"
#include "exhval_mgm.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint8_t StPurgeFuelLine;

typRelayCmd FanCoilCmd_DIAG;
typRelayCmd LoadCmd_DIAG,StarterCmd_DIAG,LowBeamCmd_DIAG,DRL_DIAG,RearPosPlateLight_DIAG/*,PurgeCanisterCmd_DIAG*/;
uint8_T     LoadCmd_store,FanCoilCmd_store,StarterCmd_store,LowBeamCmd_store,DRLCmd_store, RearPosPlateLight_store/*,PurgeCanisterCmd_store*/;


typBusy_Diag  ActveDiag_Enable= FREE;
typBusy_Diag Load_Flag_Busy= FREE;
typBusy_Diag ParamAutoAdatt_Busy= FREE;
typBusy_Diag FanCoil_Flag_Busy= FREE;
typBusy_Diag CleanFuelLOW_Bank_Flag_Busy= FREE;
typBusy_Diag CleanFuelHIGHBank_Flag_Busy= FREE;
typBusy_Diag SelfLearning_Flag_Busy= FREE;
typBusy_Diag Stater_Flag_Busy= FREE;
typBusy_Diag SelfExhValv_Flag_Busy= FREE;
typBusy_Diag DCMotor_Flag_Busy= FREE;
typBusy_Diag LowBeam_Flag_Busy= FREE;
typBusy_Diag DRL_Flag_Busy= FREE;
typBusy_Diag HLambda_Flag_Busy= FREE;
typBusy_Diag HLambda2_Flag_Busy= FREE;
typBusy_Diag ExhaustValv_Flag_Busy= FREE;
typBusy_Diag TSSValve_Flag_Busy = FREE;
typBusy_Diag EraseStressCond_Flag_Busy= FREE;
typBusy_Diag EraseHours_Flag_Busy= FREE;
typBusy_Diag ExhValvRst_Flag_Busy = FREE;
typBusy_Diag EraseLampOnTime_Flag_Busy= FREE;
typBusy_Diag RearPosPlateLight_Flag_Busy = FREE;
typBusy_Diag BAM_Flag_Busy= FREE;

typBusy_Diag VehConfigReset_Flag_Busy= FREE;



uint8_T         FlgDbwActiveDiag;
int16_T         VDbwActiveDiag;
t_DutyCycle DCLamHActiveDiag=0;
t_DutyCycle DCLamHActiveDiag2=0;
t_Period    PLamHActiveDiag=0;
t_Period    PLamHActiveDiag2=0;
uint8_T FlgLamHActiveDiag=0;
uint8_T FlgLamHActiveDiag2=0;

uint8_T      EnExhVSelf;
uint8_t      EnExhVMoving;
uint8_t      EnExhVZeroPos;
int16_t      VOutExhActiveDiag;



diagIOStruct     diagIO   = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};
diagIO_INJStruct Inj0_Low = {FREE,0,0,0,0,0};
diagIO_INJStruct Inj1_Low = {FREE,0,0,0,0,0};
diagIO_INJStruct Inj2_Low = {FREE,0,0,0,0,0};
diagIO_INJStruct Inj3_Low = {FREE,0,0,0,0,0};

diagIO_INJStruct Inj0_High= {FREE,0,0,0,0,0};
diagIO_INJStruct Inj1_High= {FREE,0,0,0,0,0};
diagIO_INJStruct Inj2_High= {FREE,0,0,0,0,0};
diagIO_INJStruct Inj3_High= {FREE,0,0,0,0,0};

diagIO_IGNStruct Ign0     = {FREE,0,0,0,0,0,0};
diagIO_IGNStruct Ign1     = {FREE,0,0,0,0,0,0};
diagIO_IGNStruct Ign2     = {FREE,0,0,0,0,0,0};
diagIO_IGNStruct Ign3     = {FREE,0,0,0,0,0,0};

uint16_t Clean_Test_DiagL=0;
uint32_t Clean_StopTimeL=0;
uint32_t clean_SatL=0;

uint16_t Clean_Test_DiagH=0;
uint32_t Clean_StopTimeH=0;
uint32_t clean_SatH=0;

/* IOLI 0x31 - MIL */
uint8_T         MILCmd_DIAG = MIL_LAMP_GLOBAL_OFF;
uint8_T         MILCmd_store = MIL_LAMP_GLOBAL_OFF;
typBusy_Diag    MILCmd_Flag_Busy = FREE;
int8_T          MILCmd_counter = 0;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static uint8_t flagCleanL=0;
static uint8_t flagCleanH=0;


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * cmdBAM - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdBAM(void)
{
    BAM_Flag_Busy=BUSY;
    /* FLASH PASSWORD & DEVICE CENSORSHIP ROUTINE */
       
   checkFlashPWD();
    BAM_Flag_Busy=FREE;
}

/*--------------------------------------------------------------------------*
 * cmdVehConfigReset - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdVehConfigReset(void)
{
    VehConfigReset_Flag_Busy=BUSY;
    CANMGM_ResetVehOptConfig();
    VehConfigReset_Flag_Busy=FREE;
}


/*--------------------------------------------------------------------------*
 * cmdEraseLampOnTime - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdEraseLampOnTime(void)
{
    EraseLampOnTime_Flag_Busy=BUSY;
    EraseLampOnTime();
    EraseLampOnTime_Flag_Busy=FREE;
}

/*--------------------------------------------------------------------------*
 * cmdEraseStressCond - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdEraseStressCond(void)
{
    EraseStressCond_Flag_Busy=BUSY;
    EraseStressCond();
    EraseStressCond_Flag_Busy=FREE;
}

/*--------------------------------------------------------------------------*
 * cmdEraseHours - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdEraseHours(void)
{
    EraseHours_Flag_Busy=BUSY;
    EraseHours();
    EraseHours_Flag_Busy=FREE;
}

/*--------------------------------------------------------------------------*
 * cmdIOSelfExhaustValve - Function description
 *
 * Implementation notes:
 * Self Learning Exhaust Valve KWP IO service 30, par 0x0E
 *--------------------------------------------------------------------------*/
void cmdIOSelfExhaustValve(void) 
{
    SelfExhValv_Flag_Busy = BUSY;
    EnExhVSelf = 1;
    diagIO.SelfExhaustValve = SELF_EXHAUSTVALV_TIME;
}


/*--------------------------------------------------------------------------*
 * SelfExhaustValve_Active_Diag - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void SelfExhaustValve_Active_Diag(void)
{
    if(SelfExhValv_Flag_Busy == BUSY)   
    {
        if(diagIO.SelfExhaustValve > 0)
        {
            diagIO.SelfExhaustValve = diagIO.SelfExhaustValve - 1;
        }
        else
        {
            SelfExhValv_Flag_Busy = FREE;
            EnExhVSelf = 0;
        }
    }
    else
    {
        //SelfExhValv_Flag_Busy = FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdExhValveRst - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdExhValveRst(void)
{
    ExhValvRst_Flag_Busy = BUSY;
    EnExhVZeroPos = 1;
    diagIO.ExhValvRst = 3;
}

/*--------------------------------------------------------------------------*
 * ExhValveRst_Active_Diag - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ExhValveRst_Active_Diag(void)
{
    /*5MS task*/
    if( ExhValvRst_Flag_Busy == BUSY)   
    {
        if(diagIO.ExhValvRst > 0)
        {
            /*wait at least 10ms here for ioli activation for sure.*/
            diagIO.ExhValvRst--; 
        }
        else
        {
            EnExhVZeroPos = 0; /*ioli activation has been completed*/

            if(FlgExhVZeroPos == 0)
            {
                /*IOLI has finished.*/
                
                diagIO.ExhValvRst = 0;
                ExhValvRst_Flag_Busy = FREE;
            }
        }
    }
    else
    {
        //ExhValvRst_Flag_Busy = FREE;
    }
    
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIODCMotor(void)
{
    DCMotor_Flag_Busy=BUSY;
    FlgDbwActiveDiag=1;
    VDbwActiveDiag=VDBW;
    diagIO.DCMotor = DCMOTOR_TIME;
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void DCMotor_Actve_Diag(void)
{
    if(diagIO.DCMotor>0) 
    {
        diagIO.DCMotor--;
    }
    else
    {
        DCMotor_Flag_Busy=FREE;
        FlgDbwActiveDiag =0;
    }    
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOExhaustValv(void)
{
    int32_t tmp32;
    
    ExhaustValv_Flag_Busy=BUSY;
    EnExhVMoving=1;

    tmp32 = (((SCALING_BIT*(T_DataREQ.Data[3])>>7)- FACT_SCALING)*((VBattery)*PERC_SCALING)>>4);
    
    if (tmp32 > MAX_int16_T)
    {
        VOutExhActiveDiag = MAX_int16_T;
    }
    else if (tmp32 < MIN_int16_T)
    {
        VOutExhActiveDiag = MIN_int16_T;
    }
    else
    {
        VOutExhActiveDiag = (int16_T)tmp32;
    }
    
    diagIO.ExhaustValv = EXHAUSTVALV_TIME;
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ExhaustValv_Actve_Diag(void)
{
    if(diagIO.ExhaustValv>0) 
    {
        diagIO.ExhaustValv--;
    }
    else
    {
        ExhaustValv_Flag_Busy=FREE;
        EnExhVMoving=0;
    }
}





/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOFanCoil(void)
{
    FanCoil_Flag_Busy=BUSY;
#ifdef IDN_FANCOIL_RLY
    DIGIO_OutGet(IDN_FANCOIL_RLY, &FanCoilCmd_store);
#endif
    FanCoilCmd_DIAG = RELAY_ON;   // led ON
    diagIO.fanCoil = FANCOIL_TIME;
}

void Fan_Coil_Active_Diag (void)
{
    if(diagIO.fanCoil>0) 
    {
        diagIO.fanCoil--;
        if(diagIO.fanCoil == 1)
        {
            FanCoilCmd_DIAG =(typRelayCmd ) FanCoilCmd_store;
        } 
    }
    else
    {
        FanCoil_Flag_Busy=FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
 void cmdIOMIL(void)
{
    MILCmd_Flag_Busy = BUSY;
    MILCmd_store = MIL_LampStatus;
    MILCmd_DIAG = MIL_LAMP_GLOBAL_ON;
    MILCmd_counter = MILCMD_PERIOD;
    diagIO.MILCmd = MILCMD_TIME;
}

void MILCmd_Active_Diag(void)
{
    if(diagIO.MILCmd > 0)
    {
        diagIO.MILCmd--;
        if(diagIO.MILCmd <= 1)
        {
            MILCmd_DIAG = MILCmd_store;
        }
        else
        {
            /* Gestione attuazione ON/OFF */
            MILCmd_counter--;
            
            if(MILCmd_counter <= 0)
            {
                MILCmd_counter = MILCMD_PERIOD;
                if (MILCmd_DIAG == MIL_LAMP_GLOBAL_OFF)
                {
                    MILCmd_DIAG = MIL_LAMP_GLOBAL_ON;
                }
                else if (MILCmd_DIAG == MIL_LAMP_GLOBAL_ON)
                {
                    MILCmd_DIAG = MIL_LAMP_GLOBAL_OFF;
                }
                else
                {
                    /* Valore non atteso */
                }
            }
        }
    }
    else
    {
        MILCmd_Flag_Busy=FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOLoad(void)
{
    Load_Flag_Busy=BUSY;
#ifdef  IDN_LOAD_RLY
    DIGIO_OutGet(IDN_LOAD_RLY, &LoadCmd_store);
#endif
    diagIO.relFuel = LOAD_TIME ;
    LoadCmd_DIAG = LOAD_ON; 
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Load_Active_Diag (void)
{ 
    if(diagIO.relFuel>0)
    {
        diagIO.relFuel--;
        
        if(diagIO.relFuel == 1)
        {
            LoadCmd_DIAG =(typRelayCmd ) LoadCmd_store;
        }
    } 
    else
    {
        Load_Flag_Busy=FREE;
        
    }
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/

void cmdIOTSSValve(void)
{
    TSSValve_Flag_Busy=BUSY;
    diagIO.TSSValve = TSSVALVEACTTIME;
}

void TSSValve_Active_Diag(void)
{
    if(diagIO.TSSValve > 0)
    {
        diagIO.TSSValve--;
    }
    else
    {
        TSSValve_Flag_Busy = FREE;
    }
}


/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOStarterRelay(void)
{
    Stater_Flag_Busy=BUSY;
#ifdef IDN_ENGSTART_RLY 
    DIGIO_OutGet(IDN_ENGSTART_RLY, &StarterCmd_store);
#endif
    diagIO.starterRelay = STARTER_TIME ;
    StarterCmd_DIAG = RELAY_ON; 
}


/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Starter_Relay_Active_Diag (void)
{ 
    if(diagIO.starterRelay>0) 
    {
        diagIO.starterRelay--;

        if(diagIO.starterRelay == (STARTER_TIME>>1))
        {
            StarterCmd_DIAG =RELAY_OFF;
        } 
        
        if(diagIO.starterRelay == 1)
        {
            StarterCmd_DIAG =(typRelayCmd ) StarterCmd_store;
        } 
    }
    else
    {
        Stater_Flag_Busy=FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdIORearPosPlateLight - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIORearPosPlateLight(void)
{
    RearPosPlateLight_Flag_Busy =BUSY;

    /* Add your code here */

    diagIO.RearPosPlateLight = REARPOSPLATELIGHT_TIME;
    RearPosPlateLight_DIAG = RELAY_ON; 
}


/*--------------------------------------------------------------------------*
 * RearPosPlateLight_Active_Diag - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void RearPosPlateLight_Active_Diag (void)
{ 
    if(diagIO.RearPosPlateLight > 0) 
    {
        diagIO.RearPosPlateLight--;

        if(diagIO.RearPosPlateLight == (REARPOSPLATELIGHT_TIME>>1))
        {
            RearPosPlateLight_DIAG =RELAY_OFF;
        } 
        
        if(diagIO.RearPosPlateLight == 1)
        {
            RearPosPlateLight_DIAG = (uint8_T)RearPosPlateLight_store;
        } 
    }
    else
    {
        RearPosPlateLight_Flag_Busy=FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOLowBeamRelay(void)
{
    LowBeam_Flag_Busy=BUSY;
    DIGIO_OutGet(IDN_HIGH_LAMP, &LowBeamCmd_store);
    diagIO.LowBeamRelay = LOWBEAM_TIME ;
    LowBeamCmd_DIAG = HIGH_LAMP_ON; 
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIODRLRelay(void)
{
    DRL_Flag_Busy=BUSY;
    DIGIO_OutGet(IDN_DRL_LAMP, &DRLCmd_store);
    diagIO.DRLRelay = DRL_TIME;
    DRL_DIAG = DRL_LAMP_ON; 
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void LowBeam_Relay_Active_Diag (void)
{ 
    if(diagIO.LowBeamRelay>0) 
    {
        diagIO.LowBeamRelay--;
        if(diagIO.LowBeamRelay == 1)
        {
            LowBeamCmd_DIAG =(typRelayCmd ) LowBeamCmd_store;
        } 
    }
    else
    {
        LowBeam_Flag_Busy=FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void DRL_Relay_Active_Diag (void)
{ 
    if(diagIO.DRLRelay>0) 
    {
        diagIO.DRLRelay--;
        if(diagIO.DRLRelay == 1)
        {
            DRL_DIAG =(typRelayCmd ) DRLCmd_store;
        } 
    }
    else
    {
        DRL_Flag_Busy=FREE;
    }
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOSelfLearning(void){
    SelfLearning_Flag_Busy=BUSY;
    SelfMgm_ExtSelfRequest();
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void SelfLearning_Active_Diag(void)
{
    if((SelfLearning_Flag_Busy == BUSY) && ((StDbwSelf == DBW_SELF_END) || (StDbwSelf == DBW_SELF_FAILED)))
    {
        SelfLearning_Flag_Busy=FREE;
    }

}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOCleanFuel_LOW_Bank(void){
    CleanFuelLOW_Bank_Flag_Busy=BUSY;
    StPurgeFuelLine= INIT_CLEAN;
    DIGIO_OutGet(IDN_LOAD_RLY, &LoadCmd_store);
    diagIO.cleanfuelL = TCLEANFUEL ;
    Clean_StopTimeL=CLEANFUEL_STOP_TIME_INJ;
    Clean_Test_DiagL=0;
    flagCleanL=0;
    clean_SatL=SATURATION_DIAG;
    LoadCmd_DIAG = LOAD_ON; 
}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOCleanFuel_HIGH_Bank(void){
    CleanFuelHIGHBank_Flag_Busy=BUSY;
    StPurgeFuelLine= INIT_CLEAN;
    DIGIO_OutGet(IDN_LOAD_RLY, &LoadCmd_store);
    diagIO.cleanfuelH = TCLEANFUEL ;
    Clean_StopTimeH=CLEANFUEL_STOP_TIME_INJ;
    Clean_Test_DiagH=0;
    flagCleanH=0;
    clean_SatH=SATURATION_DIAG;
    LoadCmd_DIAG = LOAD_ON; 
}


/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CLEANLow_Active_Diag (void)
{


    uint32_t StartAngleInj;


    if(diagIO.cleanfuelL>0) 
    {
        diagIO.cleanfuelL--;
        
        StPurgeFuelLine= START_CLEAN;
        if(diagIO.cleanfuelL<=(TCLEANFUEL-4))
        {
            if(!(Clean_Test_DiagL) && (flagCleanL!=2))
            {
                // Programmo una nuova iniettata per l'iniettore <injector>
                flagCleanL++;
                SYNC_GetCURRENT_Angle(&(StartAngleInj));
                INJ_Set(IDN_INJECTOR_0, &(StartAngleInj), &(Clean_StopTimeL), N_INJ_PULSE_DIAG, clean_SatL, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(IDN_INJECTOR_0, ENABLE);

                INJ_Set(IDN_INJECTOR_1, &(StartAngleInj), &(Clean_StopTimeL), N_INJ_PULSE_DIAG, clean_SatL, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(IDN_INJECTOR_1, ENABLE);

                INJ_Set(IDN_INJECTOR_2, &(StartAngleInj), &(Clean_StopTimeL), N_INJ_PULSE_DIAG, clean_SatL, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(IDN_INJECTOR_2, ENABLE);
                Clean_Test_DiagL = 120; /*600ms*/
            }
            Clean_Test_DiagL--;
        }
     if(diagIO.cleanfuelL == 1)
        {
            LoadCmd_DIAG =(typRelayCmd ) LoadCmd_store;
        }   
    }
    else
    {
        CleanFuelLOW_Bank_Flag_Busy=FREE;
       if(CleanFuelHIGHBank_Flag_Busy==FREE)
        {
            StPurgeFuelLine= DONE_CLEAN;
        }
        

    }

}

/*--------------------------------------------------------------------------*
 * cmdIODCMotor - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CLEANHigh_Active_Diag (void)
{

#ifdef N_INJECTOR_HB 

    uint32_t StartAngleInj;


    if(diagIO.cleanfuelH>0) 
    {
        diagIO.cleanfuelH--;
        StPurgeFuelLine= START_CLEAN;
        if(diagIO.cleanfuelH<=(TCLEANFUEL-4))
        {
            if(!(Clean_Test_DiagH) && (flagCleanH!=2))
            {
                // Programmo una nuova iniettata per l'iniettore <injector>
                flagCleanH++;
                SYNC_GetCURRENT_Angle(&(StartAngleInj));
                INJ_Set(IDN_INJECTOR_4, &(StartAngleInj), &(Clean_StopTimeH), N_INJ_PULSE_DIAG, clean_SatH, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(IDN_INJECTOR_4, ENABLE);

                INJ_Set(IDN_INJECTOR_5, &(StartAngleInj), &(Clean_StopTimeH), N_INJ_PULSE_DIAG, clean_SatH, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(IDN_INJECTOR_5, ENABLE);

                INJ_Set(IDN_INJECTOR_6, &(StartAngleInj), &(Clean_StopTimeH), N_INJ_PULSE_DIAG, clean_SatH, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(IDN_INJECTOR_6, ENABLE);
                Clean_Test_DiagH = 120; 
            }
            Clean_Test_DiagH--;
        }
        if(diagIO.cleanfuelH == 1)
    {
            LoadCmd_DIAG =(typRelayCmd ) LoadCmd_store;
        } 
    }
    else
    {
        CleanFuelHIGHBank_Flag_Busy=FREE;
        if(CleanFuelLOW_Bank_Flag_Busy==FREE)
        {
            StPurgeFuelLine= DONE_CLEAN;
        }

    }
#endif  
}


/*--------------------------------------------------------------------------*
 * cmdIOInj0_Low - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj0_Low(void){
        Inj0_Low.Inj_Flag_Busy=BUSY;
        Inj0_Low.Inj_Test_DIAG = 0;  
        Inj0_Low.Iniettore = INJ0_LOW_TIME;
        Inj0_Low.Saturation=SATURATION_DIAG;
        Inj0_Low.StopTimeInj= STOP_TIME_INJ0_LOW;
        Inj0_Low.TimePeriodInj= INJ0_LOW_PERIOD;
}

/*--------------------------------------------------------------------------*
 * cmdIOInj1_Low - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj1_Low(void){
        Inj1_Low.Inj_Flag_Busy=BUSY;
        Inj1_Low.Inj_Test_DIAG = 0;  
        Inj1_Low.Iniettore = INJ1_LOW_TIME;
        Inj1_Low.Saturation=SATURATION_DIAG;
        Inj1_Low.StopTimeInj= STOP_TIME_INJ1_LOW;
        Inj1_Low.TimePeriodInj= INJ1_LOW_PERIOD;
    }
/*--------------------------------------------------------------------------*
 * cmdIOInj2_Low - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj2_Low(void){
        Inj2_Low.Inj_Flag_Busy=BUSY;
        Inj2_Low.Inj_Test_DIAG = 0;  
        Inj2_Low.Iniettore = INJ2_LOW_TIME;
        Inj2_Low.Saturation=SATURATION_DIAG;
        Inj2_Low.StopTimeInj= STOP_TIME_INJ2_LOW;
        Inj2_Low.TimePeriodInj= INJ2_LOW_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOInj3_Low - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj3_Low(void){
        Inj3_Low.Inj_Flag_Busy=BUSY;
        Inj3_Low.Inj_Test_DIAG = 0;  
        Inj3_Low.Iniettore = INJ3_LOW_TIME;
        Inj3_Low.Saturation=SATURATION_DIAG;
        Inj3_Low.StopTimeInj= STOP_TIME_INJ3_LOW;
        Inj3_Low.TimePeriodInj= INJ3_LOW_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOInj0_High - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj0_High(void){
        Inj0_High.Inj_Flag_Busy=BUSY;
        Inj0_High.Inj_Test_DIAG = 0;  
        Inj0_High.Iniettore = INJ0_HIGH_TIME;
        Inj0_High.Saturation=SATURATION_DIAG;
        Inj0_High.StopTimeInj= STOP_TIME_INJ0_HIGH;
        Inj0_High.TimePeriodInj= INJ0_HIGH_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOInj1_High - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj1_High(void){
        Inj1_High.Inj_Flag_Busy=BUSY;
        Inj1_High.Inj_Test_DIAG = 0;  
        Inj1_High.Iniettore = INJ1_HIGH_TIME;
        Inj1_High.Saturation=SATURATION_DIAG;
        Inj1_High.StopTimeInj= STOP_TIME_INJ1_HIGH;
        Inj1_High.TimePeriodInj= INJ1_HIGH_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOInj2_High - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj2_High(void){
        Inj2_High.Inj_Flag_Busy=BUSY;
        Inj2_High.Inj_Test_DIAG = 0;  
        Inj2_High.Iniettore = INJ2_HIGH_TIME;
        Inj2_High.Saturation=SATURATION_DIAG;
        Inj2_High.StopTimeInj= STOP_TIME_INJ2_HIGH;
        Inj2_High.TimePeriodInj= INJ2_HIGH_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOInj3_High - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOInj3_High(void){
        Inj3_High.Inj_Flag_Busy=BUSY;
        Inj3_High.Inj_Test_DIAG = 0;  
        Inj3_High.Iniettore = INJ3_HIGH_TIME;
        Inj3_High.Saturation=SATURATION_DIAG;
        Inj3_High.StopTimeInj= STOP_TIME_INJ3_HIGH;
        Inj3_High.TimePeriodInj= INJ3_HIGH_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOIgn_0 - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOIgn_0(void){
    Ign0.Ign_Flag_Busy=BUSY;
    Ign0.Ign_Test_DIAG = 0;  
    Ign0.Bobina = IGN0_TIME;
    Ign0.Saturation=SATURATION_DIAG;
    Ign0.StopTimeIgn= STOP_TIME_IGN0;
    Ign0.TimePeriodIgn= IGN0_PERIOD;
}

/*--------------------------------------------------------------------------*
 * cmdIOIgn_1 - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOIgn_1(void){
    Ign1.Ign_Flag_Busy=BUSY;
    Ign1.Ign_Test_DIAG = 0;  
    Ign1.Bobina = IGN1_TIME;
    Ign1.Saturation=SATURATION_DIAG;
    Ign1.StopTimeIgn= STOP_TIME_IGN1;
    Ign1.TimePeriodIgn= IGN1_PERIOD;
    }

/*--------------------------------------------------------------------------*
 * cmdIOIgn_2 - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOIgn_2(void){
    Ign2.Ign_Flag_Busy=BUSY;
    Ign2.Ign_Test_DIAG = 0;
    Ign2.Bobina = IGN2_TIME;
    Ign2.Saturation=SATURATION_DIAG;
    Ign2.StopTimeIgn= STOP_TIME_IGN2;
    Ign2.TimePeriodIgn= IGN2_PERIOD;
}

/*--------------------------------------------------------------------------*
 * cmdIOIgn_3 - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOIgn_3(void){
    Ign3.Ign_Flag_Busy=BUSY;
    Ign3.Ign_Test_DIAG = 0;
    Ign3.Bobina = IGN3_TIME;
    Ign3.Saturation=SATURATION_DIAG;
    Ign3.StopTimeIgn= STOP_TIME_IGN3;
    Ign3.TimePeriodIgn= IGN3_PERIOD;
}

/*--------------------------------------------------------------------------*
 * cmdIOresetParamAutoAdatt - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void cmdIOresetParamAutoAdatt(void){
      ParamAutoAdatt_Busy=BUSY;
      EEMGM_SetEventID(EE_INVALIDATE_ADPT_PARAMS);
      EEMGM_EETaskCmd();
      ParamAutoAdatt_Busy=FREE; 
}

/*
void cmdIOWatPump(void)
{
#ifdef Water_Pump_PWM    
    Wat_Pump_Flag_Busy=BUSY;
    WatPumpEffDuty=MAX_DUTY_WATPUMP;
    PIO_PwmOutSetDutyCicle(Water_Pump_PWM, WatPumpEffDuty);
    PIO_PwmOutEnable(Water_Pump_PWM);
    WatPumpMgm_ActiveDiagInit(MAX_DUTY_FORCED,1);   
    WaterPump_Count = WATERPUMP_INTER_TIME;
    diagIO.watpump = WATPUMP_TIME;
#endif    
}

void WatPump_Active_Diag(void) 
{
#ifdef Water_Pump_PWM    
    if(diagIO.watpump > 0) 
    {
        diagIO.watpump--;

        if(diagIO.watpump > 1) 
        {
            if (!(--WaterPump_Count) ) 
            {
                
                if (WatPumpEffDuty == MAX_DUTY_WATPUMP) 
                {
                    WatPumpEffDuty = MIN_DUTY_WATPUMP;
                    PIO_PwmOutSetDutyCicle(Water_Pump_PWM,WatPumpEffDuty);
                    WatPumpMgm_ActiveDiagInit(MIN_DUTY_FORCED,0);   
                }
                else
                {
                    WatPumpEffDuty = MAX_DUTY_WATPUMP;   
                    PIO_PwmOutSetDutyCicle(Water_Pump_PWM,WatPumpEffDuty);
                    WatPumpMgm_ActiveDiagInit(MAX_DUTY_FORCED,0);
                }
                WaterPump_Count = WATERPUMP_INTER_TIME;  
            }
            else
            {
            }
            
        } 
        else
        {
            WatPumpMgm_Init();
            WatPumpMgm_ActiveDiagInit(MIN_DUTY_FORCED,0);   
        }    
    }    
    else 
    {
        Wat_Pump_Flag_Busy=FREE;
    }
#endif
}

*/
void cmdIOHLambda(void)
{
    HLambda_Flag_Busy=BUSY;
    FlgLamHActiveDiag=1;

    DCLamHActiveDiag=HLAMBDA_DUTY;
    PLamHActiveDiag=HLAMBDA_PERIOD;
    diagIO.HLambda = HLAMBDA_TIME;

}

void HLambda_Active_Diag(void) {
    if(diagIO.HLambda > 0) 
    {
        diagIO.HLambda--;
        
    }
    else  
    {
        HLambda_Flag_Busy=FREE;
        FlgLamHActiveDiag=0;
    }  
}

void cmdIOHLambda2(void)
{
    HLambda2_Flag_Busy=BUSY;
    FlgLamHActiveDiag2=1;

    DCLamHActiveDiag2=HLAMBDA_DUTY;
    PLamHActiveDiag2=HLAMBDA_PERIOD;
    diagIO.HLambda2 = HLAMBDA_TIME;

}

void HLambda2_Active_Diag(void) {
    if(diagIO.HLambda2 > 0) 
    {
        diagIO.HLambda2--;
        
    }
    else  
    {
        HLambda2_Flag_Busy=FREE;
        FlgLamHActiveDiag2=0;
    }  
}


/*--------------------------------------------------------------------------*
 * Reset_DIAG - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Reset_DIAG(uint8_t diag) 
{
    switch(diag)
    {
        case CMD_FAN_COIL1:
            FanCoilCmd_DIAG = (typRelayCmd)FanCoilCmd_store;
            diagIO.fanCoil = 0;
            FanCoil_Flag_Busy=FREE;
        break;
        
        case CMD_POMPA_BENZINA:
            LoadCmd_DIAG =(typRelayCmd ) LoadCmd_store;
            diagIO.relFuel = 0;
            Load_Flag_Busy=FREE;
        break;
        
        case CMD_INIETTORE0_LOW:
            Inj0_Low.Inj_Flag_Busy=FREE;
            Inj0_Low.Iniettore=0;
        break;
        
        case CMD_INIETTORE1_LOW:
            Inj1_Low.Inj_Flag_Busy=FREE;
            Inj1_Low.Iniettore=0;
        break;
        
        case CMD_INIETTORE2_LOW:
            Inj2_Low.Inj_Flag_Busy=FREE;
            Inj2_Low.Iniettore=0;
        break;
        
        case CMD_INIETTORE3_LOW:
            Inj3_Low.Inj_Flag_Busy=FREE;
            Inj3_Low.Iniettore=0;
        break;
        
        case CMD_INIETTORE0_HIGH:
            Inj0_High.Inj_Flag_Busy=FREE;
            Inj0_High.Iniettore=0;
        break;
        
        case CMD_INIETTORE1_HIGH:
            Inj1_High.Inj_Flag_Busy=FREE;
            Inj1_High.Iniettore=0;
        break;
        
        case CMD_INIETTORE2_HIGH:
            Inj2_High.Inj_Flag_Busy=FREE;
            Inj2_High.Iniettore=0;
        break;
        
        case CMD_INIETTORE3_HIGH:
            Inj3_High.Inj_Flag_Busy=FREE;
            Inj3_High.Iniettore=0;
        break;
        
        case CMD_BOBINA_0:
            Ign0.Bobina=0;
            Ign0.Ign_Flag_Busy=FREE;
        break;
        
        case CMD_BOBINA_1:
            Ign1.Bobina=0;
            Ign1.Ign_Flag_Busy=FREE;
        break;
        
        case CMD_BOBINA_2:
            Ign2.Bobina=0;
            Ign2.Ign_Flag_Busy=FREE;
        break;
        
        case CMD_BOBINA_3:
            Ign3.Bobina=0;
            Ign3.Ign_Flag_Busy=FREE;
        break;
        
        case CMD_CLEAN_FUEL_HIGH:
            LoadCmd_DIAG =(typRelayCmd ) LoadCmd_store;
            diagIO.cleanfuelH= 0 ;
            StPurgeFuelLine= FAILED_CLEAN;
            CleanFuelHIGHBank_Flag_Busy=FREE;
        break;
        
        case CMD_CLEAN_FUEL_LOW:
            LoadCmd_DIAG =(typRelayCmd ) LoadCmd_store;
            diagIO.cleanfuelL= 0 ;
            StPurgeFuelLine= FAILED_CLEAN;
            CleanFuelLOW_Bank_Flag_Busy=FREE;
        break;
        
        case CMD_STARTER_RELAY:
            StarterCmd_DIAG =(typRelayCmd ) StarterCmd_store;
            diagIO.starterRelay=0;
            Stater_Flag_Busy=FREE;
        break;

        case CMD_REARPOS_PLATE_LIGHT:
            RearPosPlateLight_DIAG =(typRelayCmd) RearPosPlateLight_store;
            diagIO.RearPosPlateLight=0;
            RearPosPlateLight_Flag_Busy=FREE;
        break;

        case CMD_LOW_BEAM:
            LowBeamCmd_DIAG =(typRelayCmd ) LowBeamCmd_store;
            diagIO.LowBeamRelay=0;
            LowBeam_Flag_Busy=FREE;
        break;

        case CMD_DRL:
            DRL_DIAG =(typRelayCmd ) DRLCmd_store;
            diagIO.DRLRelay=0;
            DRL_Flag_Busy=FREE;
        break;
        
        case CMD_DBW:
            DCMotor_Flag_Busy=FREE;
            FlgDbwActiveDiag =0;
            diagIO.DCMotor = 0;
        break;
        
        case CMD_EXHVALV:
            ExhaustValv_Flag_Busy=FREE;
            EnExhVMoving=0;
            diagIO.ExhaustValv= 0;
        break;
        
        case CMD_HLAMBDA:
            HLambda_Flag_Busy=FREE;
            diagIO.HLambda = 0;
            PIO_PwmOutSetPeriod(PIO_HLAMBDA_1, LAM_HEAT_PERIOD);
        break;

        case CMD_HLAMBDA2:
            HLambda2_Flag_Busy=FREE;
            diagIO.HLambda2 = 0;
            PIO_PwmOutSetPeriod(PIO_HLAMBDA_2, LAM_HEAT_PERIOD);
        break;
        
        case CMD_TSSVALVE:
            TSSValve_Flag_Busy=FREE;
            diagIO.TSSValve = 0;
        break;
        
        case CMD_ERASESTRESSCOND:
            EraseStressCond_Flag_Busy=FREE;
        break;
        
        case CMD_ERASELAMPONTIME:
            EraseLampOnTime_Flag_Busy=FREE;
        break;
        
        case CMD_ERASEHOURS:
            EraseHours_Flag_Busy=FREE;
        break;
        
        case CMD_EXHVALV_RST:
            ExhValvRst_Flag_Busy = FREE;
        break;
        
        case CMD_BAM:
            BAM_Flag_Busy=FREE;
        break;
        
        case CMD_RSTVEHCONFIG:
            VehConfigReset_Flag_Busy=FREE;
        break;
        
        case CMD_MIL:
            MILCmd_DIAG = MILCmd_store;
            diagIO.MILCmd = 1;
            MILCmd_counter = 0;
        break;

        default:
        break;
    }
}

/*--------------------------------------------------------------------------*
 * Reset_All_DIAG - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Reset_All_DIAG(void) 
{
    diagIO.fanCoil = 0;
    FanCoil_Flag_Busy=FREE;
    diagIO.cleanfuelH=0;
    diagIO.cleanfuelL=0;
    CleanFuelLOW_Bank_Flag_Busy=FREE;
    
    CleanFuelHIGHBank_Flag_Busy=FREE;
    if(StPurgeFuelLine== START_CLEAN || StPurgeFuelLine== FAILED_CLEAN)
    {
        StPurgeFuelLine= FAILED_CLEAN;
    }
    else
    {
        StPurgeFuelLine= INIT_CLEAN;
    }
    diagIO.relFuel = 0;
    Load_Flag_Busy=FREE;
    
    Inj0_Low.Inj_Flag_Busy=FREE;
    Inj0_Low.Iniettore=0;
    
    Inj1_Low.Inj_Flag_Busy=FREE;
    Inj1_Low.Iniettore=0;
    
    Inj2_Low.Inj_Flag_Busy=FREE;
    Inj2_Low.Iniettore=0;
    
    Inj3_Low.Inj_Flag_Busy=FREE;
    Inj3_Low.Iniettore=0;
#ifdef N_INJECTOR_HB     
    Inj0_High.Inj_Flag_Busy=FREE;
    Inj0_High.Iniettore=0;
    
    Inj1_High.Inj_Flag_Busy=FREE;
    Inj1_High.Iniettore=0;
    
    Inj2_High.Inj_Flag_Busy=FREE;
    Inj2_High.Iniettore=0;
    
    Inj3_High.Inj_Flag_Busy=FREE;
    Inj3_High.Iniettore=0;
#endif
    Ign0.Bobina=0;
    Ign0.Ign_Flag_Busy=FREE;
    
    Ign1.Bobina=0;
    Ign1.Ign_Flag_Busy=FREE;

    Ign2.Bobina=0;
    Ign2.Ign_Flag_Busy=FREE;

    Ign3.Bobina=0;
    Ign3.Ign_Flag_Busy=FREE;

    diagIO.starterRelay = 0;
    Stater_Flag_Busy=FREE;

    diagIO.RearPosPlateLight = 0;
    RearPosPlateLight_Flag_Busy = FREE;

    diagIO.DCMotor = 0;
    FlgDbwActiveDiag =0;
    DCMotor_Flag_Busy=FREE;
        
    ExhaustValv_Flag_Busy=FREE;
    EnExhVMoving=0;
    diagIO.ExhaustValv= 0;

    LowBeamCmd_DIAG =(typRelayCmd ) LowBeamCmd_store;
    diagIO.LowBeamRelay = 0;
    LowBeam_Flag_Busy=FREE;

    DRL_DIAG =(typRelayCmd ) DRLCmd_store;
    diagIO.DRLRelay = 0;
    DRL_Flag_Busy=FREE;

    diagIO.TSSValve= 0;
    TSSValve_Flag_Busy=FREE;

    diagIO.HLambda= 0;
    HLambda_Flag_Busy=FREE;
    PIO_PwmOutSetPeriod(PIO_HLAMBDA_1, LAM_HEAT_PERIOD);

    diagIO.HLambda2= 0;
    HLambda2_Flag_Busy=FREE;
    PIO_PwmOutSetPeriod(PIO_HLAMBDA_2, LAM_HEAT_PERIOD);
    
    EraseLampOnTime_Flag_Busy=FREE;
    BAM_Flag_Busy=FREE;
    VehConfigReset_Flag_Busy=FREE;
    EraseHours_Flag_Busy=FREE;
    ExhValvRst_Flag_Busy = FREE;

    diagIO.MILCmd = 0;
    MILCmd_Flag_Busy = FREE;

    ActveDiag_Enable = FREE;
    EraseStressCond_Flag_Busy=FREE;
}

/*--------------------------------------------------------------------------*
 * Reset_All_DIAG_EngRun - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Reset_All_DIAG_EngRun(void) 
{
    diagIO.fanCoil = 0;
    FanCoil_Flag_Busy=FREE;
    diagIO.cleanfuelH=0;
    diagIO.cleanfuelL=0;
    CleanFuelLOW_Bank_Flag_Busy=FREE;
    
    CleanFuelHIGHBank_Flag_Busy=FREE;
    if(StPurgeFuelLine== START_CLEAN || StPurgeFuelLine== FAILED_CLEAN)
    {
        StPurgeFuelLine= FAILED_CLEAN;
    }
    else
    {
        StPurgeFuelLine= INIT_CLEAN;
    }
    diagIO.relFuel = 0;
    Load_Flag_Busy=FREE;
    
    Inj0_Low.Inj_Flag_Busy=FREE;
    Inj0_Low.Iniettore=0;
    
    Inj1_Low.Inj_Flag_Busy=FREE;
    Inj1_Low.Iniettore=0;
    
    Inj2_Low.Inj_Flag_Busy=FREE;
    Inj2_Low.Iniettore=0;
    
    Inj3_Low.Inj_Flag_Busy=FREE;
    Inj3_Low.Iniettore=0;
#ifdef N_INJECTOR_HB     
    Inj0_High.Inj_Flag_Busy=FREE;
    Inj0_High.Iniettore=0;
    
    Inj1_High.Inj_Flag_Busy=FREE;
    Inj1_High.Iniettore=0;
    
    Inj2_High.Inj_Flag_Busy=FREE;
    Inj2_High.Iniettore=0;
    
    Inj3_High.Inj_Flag_Busy=FREE;
    Inj3_High.Iniettore=0;
#endif
    Ign0.Bobina=0;
    Ign0.Ign_Flag_Busy=FREE;
    
    Ign1.Bobina=0;
    Ign1.Ign_Flag_Busy=FREE;

    Ign2.Bobina=0;
    Ign2.Ign_Flag_Busy=FREE;

    Ign3.Bobina=0;
    Ign3.Ign_Flag_Busy=FREE;

    diagIO.starterRelay = 0;
    Stater_Flag_Busy=FREE;

    diagIO.RearPosPlateLight = 0;
    RearPosPlateLight_Flag_Busy = FREE;

    diagIO.DCMotor = 0;
    FlgDbwActiveDiag =0;
    DCMotor_Flag_Busy=FREE;
        
    ExhaustValv_Flag_Busy=FREE;
    EnExhVMoving=0;
    diagIO.ExhaustValv= 0;

    LowBeamCmd_DIAG =(typRelayCmd ) LowBeamCmd_store;
    diagIO.LowBeamRelay = 0;
    LowBeam_Flag_Busy=FREE;

    DRL_DIAG =(typRelayCmd ) DRLCmd_store;
    diagIO.DRLRelay = 0;
    DRL_Flag_Busy=FREE;

    diagIO.HLambda= 0;
    HLambda_Flag_Busy=FREE;
    PIO_PwmOutSetPeriod(PIO_HLAMBDA_1, LAM_HEAT_PERIOD);

    diagIO.HLambda2= 0;
    HLambda2_Flag_Busy=FREE;
    PIO_PwmOutSetPeriod(PIO_HLAMBDA_2, LAM_HEAT_PERIOD);
    
    EraseLampOnTime_Flag_Busy=FREE;
    BAM_Flag_Busy=FREE;
    VehConfigReset_Flag_Busy=FREE;
    EraseHours_Flag_Busy=FREE;
    ExhValvRst_Flag_Busy = FREE;
    
    diagIO.MILCmd = 0;
    MILCmd_Flag_Busy = FREE;

    ActveDiag_Enable = FREE;
    EraseStressCond_Flag_Busy=FREE;
}

/*--------------------------------------------------------------------------*
 * Inj_Active_Diag - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Init_ActiveDiag(void){
      ParamAutoAdatt_Busy=FREE;
      Reset_All_DIAG();
}

/*--------------------------------------------------------------------------*
 * Inj_Active_Diag - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Inj_Active_Diag (t_INJ_CHANNELS chan, diagIO_INJStruct * InjUsed){


        if(InjUsed->Iniettore>0) 
        {
            InjUsed->Iniettore--;
            if(!(InjUsed->Inj_Test_DIAG/*--*/))
            {
                // Programmo una nuova iniettata per l'iniettore <injector>
                
                SYNC_GetCURRENT_Angle(&(InjUsed->StartAngleInj));
                INJ_Set(chan, &(InjUsed->StartAngleInj), &(InjUsed->StopTimeInj), N_INJ_PULSE_DIAG, InjUsed->Saturation, MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
                // Abilito l'iniettore
                INJ_Enable(chan, ENABLE);
                InjUsed->Inj_Test_DIAG = InjUsed->TimePeriodInj; 
            }
        InjUsed->Inj_Test_DIAG--;
        } 
        else
        {
            InjUsed->Inj_Flag_Busy=FREE;
        }
    }



/*--------------------------------------------------------------------------*
 * Ign_Active_Diag - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void Ign_Active_Diag (t_IGN_CHANNELS chan,
                      diagIO_IGNStruct *IgnUsed,
                      int8_t coil){ 
    if(IgnUsed->Bobina>0) 
    {   
        IgnUsed->Bobina--;
        if(!(IgnUsed->Ign_Test_DIAG))  
        {
            
            SYNC_GetCURRENT_Angle(&(IgnUsed->StartAngleIgn));
            IGN_Set(chan, &(IgnUsed->StartAngleIgn), &(IgnUsed->StopTimeIgn), N_IGN_PULSE_DIAG, &(IgnUsed->Saturation), MATCH_TIME, ANGLE_TIME, TIME_PRIORITY);
            IGN_Enable(chan, ENABLE);
           IgnUsed->Ign_Test_DIAG = IgnUsed->TimePeriodIgn;
        }
        else
        {
          if (IgnUsed->Ign_Test_DIAG == ((IgnUsed->TimePeriodIgn)-1))
          {
            IgnCmd_Diag(coil, 0);
          }
        }
    IgnUsed->Ign_Test_DIAG--;
    }
    else  
    {
        IgnUsed->Ign_Flag_Busy=FREE;
    }
}


/*--------------------------------------------------------------------------*
 * Get_ActveDiag_Enable_Condition - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
typBusy_Diag Get_ActveDiag_Enable_Condition(void)
{
    if((DCMotor_Flag_Busy == BUSY) || (Stater_Flag_Busy == BUSY) || (ParamAutoAdatt_Busy==BUSY) || 
        (SelfLearning_Flag_Busy==BUSY) || (FanCoil_Flag_Busy==BUSY) || (HLambda_Flag_Busy==BUSY) || (HLambda2_Flag_Busy==BUSY) || 
        (Load_Flag_Busy==BUSY) || (Ign1.Ign_Flag_Busy==BUSY) || (Ign2.Ign_Flag_Busy==BUSY) || (Ign3.Ign_Flag_Busy==BUSY) || 
        (Ign0.Ign_Flag_Busy==BUSY) || (LowBeam_Flag_Busy==BUSY) || (DRL_Flag_Busy==BUSY) || (TSSValve_Flag_Busy==BUSY)|| 
        (Inj3_Low.Inj_Flag_Busy==BUSY) || (Inj2_Low.Inj_Flag_Busy==BUSY) || (Inj1_Low.Inj_Flag_Busy==BUSY) || (Inj0_Low.Inj_Flag_Busy==BUSY) || 
        (Inj3_High.Inj_Flag_Busy==BUSY) || (Inj2_High.Inj_Flag_Busy==BUSY) || (CleanFuelLOW_Bank_Flag_Busy==BUSY) || (CleanFuelHIGHBank_Flag_Busy==BUSY) || 
        (Inj1_High.Inj_Flag_Busy==BUSY) || (Inj0_High.Inj_Flag_Busy==BUSY) || (ExhaustValv_Flag_Busy==BUSY) || 
        (EraseStressCond_Flag_Busy == BUSY)||  (ExhValvRst_Flag_Busy == BUSY)  || (EraseLampOnTime_Flag_Busy == BUSY)|| 
        (EraseHours_Flag_Busy == BUSY)  || (BAM_Flag_Busy==BUSY) || (VehConfigReset_Flag_Busy == BUSY) || (MILCmd_Flag_Busy == BUSY)
        || (RearPosPlateLight_Flag_Busy == BUSY))
    {
         ActveDiag_Enable = BUSY;
    }
    else
    {
        ActveDiag_Enable = FREE;
    }

   return ActveDiag_Enable;
}


/*--------------------------------------------------------------------------*
 * ActiveDiagMgm_T100ms - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ActiveDiagMgm_T100ms(void)
{

    Fan_Coil_Active_Diag();
    Ign_Active_Diag (IGNCMD_21_CHANNEL,&Ign0, COIL_0); 
    Ign_Active_Diag (IGNCMD_22_CHANNEL,&Ign1, COIL_1); 
    Ign_Active_Diag (IGNCMD_23_CHANNEL,&Ign2, COIL_2); 
    Ign_Active_Diag (IGNCMD_24_CHANNEL,&Ign3, COIL_3); 


    Inj_Active_Diag ( IDN_INJECTOR_0, &Inj0_Low);
    Inj_Active_Diag ( IDN_INJECTOR_1, &Inj1_Low);
    Inj_Active_Diag ( IDN_INJECTOR_2, &Inj2_Low);
#if(N_INJECTOR == 4)
    Inj_Active_Diag ( IDN_INJECTOR_3, &Inj3_Low);
#endif
#ifdef N_INJECTOR_HB  
    Inj_Active_Diag ( IDN_INJECTOR_4, &Inj0_High);
    Inj_Active_Diag ( IDN_INJECTOR_5, &Inj1_High);
    Inj_Active_Diag ( IDN_INJECTOR_6, &Inj2_High);
#if(N_INJECTOR == 4)
    Inj_Active_Diag ( IDN_INJECTOR_7, &Inj3_High);
#endif
#endif
    LowBeam_Relay_Active_Diag();
    DRL_Relay_Active_Diag();
    HLambda_Active_Diag();
    HLambda2_Active_Diag();
    DCMotor_Actve_Diag();
    ExhaustValv_Actve_Diag();
    SelfExhaustValve_Active_Diag();
    //  PurgeCanister_Active_Diag();
    ExhValveRst_Active_Diag();
    MILCmd_Active_Diag();
}

/*--------------------------------------------------------------------------*
 * ActiveDiagMgm_T5ms - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ActiveDiagMgm_T5ms(void)
{
    CLEANLow_Active_Diag();
    CLEANHigh_Active_Diag();
    SelfLearning_Active_Diag();
}



/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
/* Example:
static void TemplatePrivateFunc(void){}
*/


#else /* _BUILD_ACTIVE_DIAG_ */


//Stubs section

#include "typedefs.h"
#include "activeDiag.h"
diagIOStruct     diagIO   = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};

diagIO_INJStruct Inj0_Low={FREE,0,0,0,0,0,0};
diagIO_INJStruct Inj1_Low={FREE,0,0,0,0,0,0};
diagIO_INJStruct Inj2_Low={FREE,0,0,0,0,0,0};
diagIO_INJStruct Inj3_Low={FREE,0,0,0,0,0,0};

diagIO_INJStruct Inj0_High={FREE,0,0,0,0,0,0};
diagIO_INJStruct Inj1_High={FREE,0,0,0,0,0,0};
diagIO_INJStruct Inj2_High={FREE,0,0,0,0,0,0};
diagIO_INJStruct Inj3_High={FREE,0,0,0,0,0,0};

diagIO_IGNStruct Ign0={FREE,0,0,0,0,0};
diagIO_IGNStruct Ign1={FREE,0,0,0,0,0};
diagIO_IGNStruct Ign2={FREE,0,0,0,0,0};
diagIO_IGNStruct Ign3={FREE,0,0,0,0,0};

typBusy_Diag  ActveDiag_Enable = FREE;
uint8_t StPurgeFuelLine = 0;

typBusy_Diag Load_Flag_Busy = FREE;

typBusy_Diag ParamAutoAdatt_Busy = FREE;
typBusy_Diag FanCoil_Flag_Busy = FREE;
typBusy_Diag CleanFuelLOW_Bank_Flag_Busy = FREE;
typBusy_Diag CleanFuelHIGHBank_Flag_Busy = FREE;

typBusy_Diag SelfLearning_Flag_Busy = FREE;
typBusy_Diag HighLamp_Flag_Busy = FREE;
typBusy_Diag Wat_Pump_Flag_Busy = FREE;
typBusy_Diag Stater_Flag_Busy = FREE;
typBusy_Diag LowBeam_Flag_Busy = FREE;
typBusy_Diag DRL_Flag_Busy = FREE;
typBusy_Diag TSSValve_Flag_Busy = FREE;
typBusy_Diag HLambda_Flag_Busy = FREE;
typBusy_Diag HLambda2_Flag_Busy = FREE;
typBusy_Diag EraseStressCond_Flag_Busy= FREE;
typBusy_Diag EraseHours_Flag_Busy= FREE;
typBusy_Diag ExhValvRst_Flag_Busy = FREE;
typBusy_Diag EraseLampOnTime_Flag_Busy= FREE;
typBusy_Diag BAM_Flag_Busy=FREE;
typBusy_Diag SelfExhValv_Flag_Busy= FREE;
typBusy_Diag DCMotor_Flag_Busy = FREE;
typRelayCmd WarningLmpCmd_DIAG,LoadCmd_DIAG;

typRelayCmd     ImmoLedCmd_DIAG;
typRelayCmd     HighLampCmd_DIAG;
uint8_T         ImmoLedCnt_DIAG,ImmoLedCmd_Store,HighLampCmd_store;
typRelayCmd     FanCoilCmd_DIAG,StarterCmd_DIAG,LowBeamCmd_DIAG,DRL_DIAG, RearPosPlateLight_DIAG;
uint8_T         WarningLmpTest_DIAG,WarningLmpCmd_Store,LoadCmd_store,FanCoilCmd_store,StarterCmd_store, earPosPlateLight_store;


uint8_T         FlgDbwActiveDiag;
int16_T         VDbwActiveDiag;
uint16_t Clean_Test_DiagL=0;
uint32_t Clean_StopTimeL=0;
uint32_t clean_SatL=0;
static uint8_t flagCleanL=0;

uint16_t Clean_Test_DiagH=0;
uint32_t Clean_StopTimeH=0;
uint32_t clean_SatH=0;
static uint8_t flagCleanH=0;

/* IOLI 0x31 - MIL */
uint8_T         MILCmd_DIAG = MIL_LAMP_GLOBAL_OFF;
uint8_T         MILCmd_store = MIL_LAMP_GLOBAL_OFF;
typBusy_Diag    MILCmd_Flag_Busy = FREE;

void Reset_All_DIAG(void) 
{

}

void Reset_All_DIAG_EngRun(void)
{

}

void Reset_DIAG(uint8_t diag) 
{

}

typBusy_Diag Get_ActveDiag_Enable_Condition(void)
{   
    return FREE;
}

void cmdIOCleanFuel_HIGH_Bank(void){
}

void cmdIOCleanFuel_LOW_Bank(void){
}

void cmdIOInj0_Low(void)
{

}

void cmdIOInj1_Low(void)
{

}

void cmdIOInj2_Low(void)
{

}

void cmdIOInj3_Low(void)
{

}

void CLEANLow_Active_Diag(void)
{

}

void CLEANHigh_Active_Diag(void)
{

}


void cmdIOInj0_High(void)
{

}

void cmdIOInj1_High(void)
{

}

void cmdIOInj2_High(void)
{

}

void cmdIOInj3_High(void)
{

}

void cmdIOSelfExhaustValve(void) 
{

}

void cmdExhValveRst(void)
{

}


void cmdIODCMotor(void)
{

}


void cmdIOSelfLearning(void)
{

}

void cmdIOFanCoil(void)
{

}

void cmdIOMIL(void)
{

}

void cmdIOIgn_0(void)
{

}

void cmdIOIgn_1(void)
{

}
void cmdIOIgn_2(void)
{

}
void cmdIOIgn_3(void)
{

}


void cmdIOLoad(void)
{

}

void cmdIORearPosPlateLight(void)
{

}

void cmdIOStarterRelay(void)
{

}

void cmdIOLowBeamRelay(void)
{

}

void cmdIODRLRelay(void)
{

}

void cmdIOresetParamAutoAdatt(void)
{

}

void Load_Active_Diag (void)
{

} 

void Starter_Relay_Active_Diag (void)
{
}

void LowBeam_Relay_Active_Diag (void)
{
}

void DRL_Relay_Active_Diag (void)
{
}

/*void PurgeCanister_Active_Diag(void) 
{
}*/



#endif /* _BUILD_ACTIVE_DIAG_ */

