/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  MATLAB_MEX_FILE
#include "EM_4Cy4Co4In.h"
#define _BUILD_ADDAIRMGM_
#define _BUILD_SPARKMGM_
#endif
#include "addairmgm.h"
#include "diagmgm_out.h"
#include "mathlib.h"

/* Outputs */
uint16_T AngThrCorr;				/* uint16_T 	0.0	..	100.0	%			2^-4 	*/
uint16_T AngThrCorrObj0;		/* uint16_T 	0.0	..	100.0	%		  2^-4 	*/
uint16_T StepperObj; 				/* uint16_T 	0.0	..	1.0		gain	2^-15 */
uint16_T CalcStepperObj;        /* calculated target stepper position 0.0 .. 1.0 gain */


#ifdef _BUILD_ADDAIRMGM_
	
/* Variables */
uint16_T BaseAddAir;		/* uint16_T 	0.0	..	1.0		%		2^-15 	*/
uint16_T CrankAddAir;		/* uint16_T 	0.0	..	1.0		gain	2^-15 	*/
int16_T  RpmErr;			/* uint16_T 	0.0	..	20000	rpm		2^0 	*/
int16_T  RpmErr_old;		/* uint16_T 	0.0	..	20000	rpm		2^0 	*/
int32_T	 IdleAddAir;		/* int32_T 	   -1.0 ..	1.0		gain	2^-28 	*/
uint16_T StepperAngleObj;		/* uint16_T 	0.0	..	100.0	%			2^-4 	*/
uint16_T OpenLoopAddAir;	/* uint16_T 	0.0	..	1.0		%		2^-15 	*/

/* Calibrations */
extern int16_T  BKTWADDAIR[BKTWADDAIR_dim];
extern uint16_T BKTDCADDAIR[BKTDCADDAIR_dim];
extern uint16_T TBCRANKADDAIR[BKTDCADDAIR_dim*BKTWADDAIR_dim];
extern uint16_T BKRPMADDAIR[BKRPMADDAIR_dim];
extern uint16_T TBBASEADDAIR[BKRPMADDAIR_dim*BKTWADDAIR_dim];
extern uint16_T BKSTPADDAIR[BKSTPADDAIR_dim];
extern uint16_T TBSTPANGLE[BKRPMADDAIR_dim*BKSTPADDAIR_dim];
extern int16_T  RPMERRMAX;
extern int16_T  RPMERRMIN;
extern uint16_T IDLEINTGAIN;
extern uint16_T IDLEPROPGAIN;
extern uint16_T IDLEOFFSTEP;
extern uint16_T TBSTPCORRGAIN[BKRPMADDAIR_dim*BKTHRADDAIR_dim];
extern uint16_T BKTHRADDAIR[BKTHRADDAIR_dim];
extern uint8_T  FLGFORCESTPPOS;
extern uint16_T FORCEDSTPPOS;

/* Functions */
void Base_Cranking_Add_Air(void);
void Additional_Air_TDC(void);
void StepperAngle_Calculation(void);
void Additional_Air_TDC(void);

void AddAirMgm_Init(void)
{
	AngThrCorr = AngThrottle;			    // 2^-4
	AngThrCorrObj0 = AngThrObj0;		    // 2^-4

	BaseAddAir = 0;

	Look2D_U16_S16_U16(&(CrankAddAir), TBCRANKADDAIR, TWaterCrk, BKTWADDAIR, (BKTWADDAIR_dim-1), CntTdcCrk, BKTDCADDAIR, (BKTDCADDAIR_dim-1));
	
	RpmErr = 0;
	RpmErr_old = 0;
	IdleAddAir = 0;

	StepperObj = 0;
	StepperAngleObj = 0;
}

void AddAirMgm_T5ms(void)
{
	Base_Cranking_Add_Air();
		
	StepperAngle_Calculation();	
}

void AddAirMgm_TDC(void)
{
	Additional_Air_TDC();
}
	
void Base_Cranking_Add_Air(void)
{
	uint32_T tmpOpenLoopAddAir;
	
	Look2D_U16_S16_U16(&(BaseAddAir), TBBASEADDAIR, TWater, BKTWADDAIR, (BKTWADDAIR_dim-1), Rpm, BKRPMADDAIR, (BKRPMADDAIR_dim-1));
	
	Look2D_U16_S16_U16(&(CrankAddAir), TBCRANKADDAIR, TWaterCrk, BKTWADDAIR, (BKTWADDAIR_dim-1), CntTdcCrk, BKTDCADDAIR, (BKTDCADDAIR_dim-1));

	tmpOpenLoopAddAir = BaseAddAir + CrankAddAir;	// 2^-15

	OpenLoopAddAir = (uint16_T)min(tmpOpenLoopAddAir, MAX_STPOBJ);
} // Additional_Air_Calculation

void Additional_Air_TDC(void)
{
	int16_T tmpRpmErr;
	int32_T PiAddAir;
	int32_T IDLEOFFSTEP_tmp;
	int64_T tmp64;
	int64_T tmp64_1;
	
	// Rpm_Error_Calculation
	RpmErr_old = RpmErr;
	tmpRpmErr = RpmIdleObj - Rpm;		//	2^0
	if(tmpRpmErr >= RPMERRMAX)
		RpmErr = RPMERRMAX;						//	2^0
	else if(tmpRpmErr <= RPMERRMIN)
		RpmErr = RPMERRMIN;						//	2^0
	else
		RpmErr = tmpRpmErr;						//	2^0
	
	// Idle_PI_regulator
	// PiAddAir = (RpmErr * IDLEINTGAIN - RpmErr_old) * IDLEPROPGAIN + IdleAddAir
	tmp64 = (int64_T)RpmErr;
	tmp64 *= (int64_T)IDLEINTGAIN;	//	2^-14
	tmp64_1 = (int64_T)RpmErr_old;
	tmp64_1 <<= 14;
	tmp64 -= tmp64_1;								//	2^-14
	tmp64 *= (int64_T)IDLEPROPGAIN;	//	2^-28
	tmp64 += IdleAddAir;						//	2^-28

	if(tmp64 > MAX_int32_T)
		PiAddAir = MAX_int32_T;				//	2^-28
	else if(tmp64 < MIN_int32_T)
		PiAddAir = MIN_int32_T;				//	2^-28
	else
		PiAddAir = (int32_T)tmp64;		//	2^-28
	
	// Idle_Output_Calc
	if (IdleFlg)
	{
		int32_T MIN_PI;
		int32_T MAX_PI;
		
		// MIN_PI = - OpenLoopAddAir
		MIN_PI = -((int32_T)OpenLoopAddAir<<13);	//	2^-28
		// MAX_PI = 1 - OpenLoopAddAir
		MAX_PI = FIX_28 + MIN_PI;									//	2^-28
		
		if (PiAddAir > MAX_PI)
			IdleAddAir = MAX_PI;										//	2^-28
		else if (PiAddAir < MIN_PI)
			IdleAddAir = MIN_PI;										//	2^-28
		else
			IdleAddAir = PiAddAir;									//	2^-28
	}
	else
	{
		IDLEOFFSTEP_tmp = (int32_T)(IDLEOFFSTEP<<18);
		if (IdleAddAir > IDLEOFFSTEP_tmp)
			IdleAddAir -= IDLEOFFSTEP_tmp;					//	2^-28
		else if (IdleAddAir < (-IDLEOFFSTEP_tmp))
			IdleAddAir += IDLEOFFSTEP_tmp;					//	2^-28
		else
			IdleAddAir = 0;													//	2^-28
	}
}	// Idle_Speed_Controller

void StepperAngle_Calculation(void)
{
	int32_T  tmpStepperObj;
	uint32_T tmpAngThrCorr;
	uint16_T tmpTBSTPANGLE;
	uint16_T tmpTBSTPCORRGAIN;
	uint32_T tmpStepperAngleObj;
		
	// StepperObj = OpenLoopAddAir + IdleAddAir saturato tra MIN_STPOBJ e MAX_STPOBJ
	tmpStepperObj = OpenLoopAddAir + (IdleAddAir>>13);	//	2^-15

    // range check
	if(tmpStepperObj >= MAX_STPOBJ)
		CalcStepperObj = MAX_STPOBJ;													//	2^-15
	else if(tmpStepperObj <= MIN_STPOBJ)
		CalcStepperObj = MIN_STPOBJ;													//	2^-15
	else
		CalcStepperObj = tmpStepperObj;												//	2^-15

    if (FLGFORCESTPPOS) // force target position for testing
    {
        StepperObj = FORCEDSTPPOS;    
    } 
    else    // normal behaviour
    {
        StepperObj = CalcStepperObj;    
    }    
	// StepperAngleObj = TBSTPANGLE(StepperObj, Rpm) * TBSTPCORRGAIN(AngThrottle, Rpm)
	Look2D_U16_U16_U16(&(tmpTBSTPANGLE), TBSTPANGLE, StepperObj, BKSTPADDAIR, (BKSTPADDAIR_dim-1), Rpm, BKRPMADDAIR, (BKRPMADDAIR_dim-1));
	Look2D_U16_U16_U16(&(tmpTBSTPCORRGAIN), TBSTPCORRGAIN, AngThrottle, BKTHRADDAIR, (BKTHRADDAIR_dim-1), Rpm, BKRPMADDAIR, (BKRPMADDAIR_dim-1));
	tmpStepperAngleObj = (uint32_T)tmpTBSTPANGLE;				// 2^-4
	tmpStepperAngleObj *= (uint32_T)tmpTBSTPCORRGAIN;		// 2^-4 * 2^-10
	tmpStepperAngleObj >>= 10;													// 2^-4
	if(tmpStepperAngleObj > MAX_uint16_T)
	{
		tmpStepperAngleObj = MAX_uint16_T;								// 2^-4
	}
	
	StepperAngleObj = (uint16_T)tmpStepperAngleObj;			// 2^-4
			
	// AngThrCorr = min((AngThrottle+StepperAngleObj), MAX_THROTTLE)
	tmpAngThrCorr = AngThrottle + tmpStepperAngleObj;
	if(tmpAngThrCorr > MAX_THROTTLE)
	{
		tmpAngThrCorr = MAX_THROTTLE;											// 2^-4
	}
	
	AngThrCorr = (uint16_T)tmpAngThrCorr;								// 2^-4

	// AngThrCorrObj0 = min((AngThrObj0+StepperAngleObj), MAX_THROTTLE)
	tmpAngThrCorr = AngThrObj0 + tmpStepperAngleObj;
	if (tmpAngThrCorr > MAX_THROTTLE)
	{
		tmpAngThrCorr = MAX_THROTTLE;											// 2^-4
	}

	AngThrCorrObj0 = (uint16_T)tmpAngThrCorr;						// 2^-4
}	// StepperAngle_Calculation

#elif defined(_BUILD_AIRMGM_)

/* Codice eseguito solo se non viene compilato _BUILD_ADDAIRMGM_ */

void AddAirMgm_Init(void)
{
	AngThrCorrObj0 = AngThrCorrObj;	 // 2^-4
	AngThrCorr = AngThrottle; 			// 2^-4
}

void AddAirMgm_T5ms(void)
{
	AngThrCorrObj0 = AngThrCorrObj;	 // 2^-4
	AngThrCorr = AngThrottle; 			// 2^-4
}

void AddAirMgm_TDC(void)
{
	AngThrCorrObj0 = AngThrCorrObj;	 	// 2^-4
	AngThrCorr = AngThrottle; 			// 2^-4
}

#else

void AddAirMgm_Init(void)
{
	AngThrCorrObj0 = (Load>>3);	  // 2^-4
	AngThrCorr = (LoadObj >> 3);    // 2^-4
}

void AddAirMgm_T5ms(void)
{
	AngThrCorrObj0 = (Load>>3);	  // 2^-4
	AngThrCorr = (LoadObj >> 3);    // 2^-4
}

void AddAirMgm_TDC(void)
{
	AngThrCorrObj0 = (Load >> 3);	  // 2^-4
	AngThrCorr = (LoadObj >> 3);    // 2^-4
}

#endif // _BUILD_ADDAIRMGM_
