/*
 * File: GasPosMgm.h
 *
 * Code generated for Simulink model 'GasPosMgm'.
 *
 * Model version                  : 1.898
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb  1 14:21:31 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GasPosMgm_h_
#define RTW_HEADER_GasPosMgm_h_
#ifndef GasPosMgm_COMMON_INCLUDES_
# define GasPosMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#endif                                 /* GasPosMgm_COMMON_INCLUDES_ */

#include "GasPosMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "gaspos_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKTIMHISTGAS_dim               23U                       /* Referenced by: '<S24>/BKTIMHISTGAS_dim' */

/* Dim of BKTIMHISTGAS */
#define ID_SPEED_LIM_CTRL              31843156U                 /* Referenced by: '<S10>/ID_SPEED_LIM_CTRL' */

/* mask */
#define MIN_D_2_SENS                   3U                        /* Referenced by:
                                                                  * '<S13>/Calc_Final_GasPos'
                                                                  * '<S17>/ANGTHROFFSET3'
                                                                  */

/* RecGasType 03: Sensor with min derivative */
#define MIN_SENS                       2U                        /* Referenced by:
                                                                  * '<S13>/Calc_Final_GasPos'
                                                                  * '<S13>/Calc_Recovery'
                                                                  */

/* RecGasType 02: Minimum of the sensors */
#define USE_1ST_SENS                   0U                        /* Referenced by:
                                                                  * '<S10>/USE_1ST_SENS'
                                                                  * '<S13>/Calc_Final_GasPos'
                                                                  * '<S13>/Calc_Recovery'
                                                                  * '<S40>/USE_1ST_SENS'
                                                                  */

/* RecGasType 00: use the 1st sensor */
#define USE_2ND_SENS                   1U                        /* Referenced by:
                                                                  * '<S10>/USE_2ND_SENS'
                                                                  * '<S13>/Calc_Final_GasPos'
                                                                  * '<S13>/Calc_Recovery'
                                                                  * '<S40>/USE_2ND_SENS'
                                                                  */

/* RecGasType 01: use the 2nd sensor */
#define USE_LAST_VALUE                 5U                        /* Referenced by: '<S13>/Calc_Final_GasPos' */

/* RecGasType 05: No sensors available */
#define WAIT_IDLE                      6U                        /* Referenced by:
                                                                  * '<S13>/Calc_Final_GasPos'
                                                                  * '<S13>/Calc_Recovery'
                                                                  */

/* RecGasType 06: set gas to zero and wait for idle */

/* Block signals for system '<S22>/IdleSW_VGasPos1_Func_Diag' */
typedef struct {
  uint8_T LogicalOperator1;            /* '<S41>/Logical Operator1' */
  uint8_T Switch3;                     /* '<S41>/Switch3' */
} rtB_IdleSW_VGasPos1_Func_Diag_G;

/* Block signals for system '<S22>/IdleSW_VGasPos2_Func_Diag' */
typedef struct {
  uint8_T LogicalOperator1;            /* '<S42>/Logical Operator1' */
  uint8_T Switch3;                     /* '<S42>/Switch3' */
} rtB_IdleSW_VGasPos2_Func_Diag_G;

/* Block signals (default storage) */
typedef struct {
  uint16_T tmpGasPos;                  /* '<S13>/Calc_Final_GasPos' */
  uint16_T vtgaspos[2];                /* '<S20>/Data Type Conversion' */
  uint16_T gaspos_th;                  /* '<S24>/Data Type Conversion' */
  uint8_T DiagMgm_SetDiagState;        /* '<S29>/DiagMgm_SetDiagState' */
  boolean_T LogicalOperator1;          /* '<S21>/Logical Operator1' */
  rtB_IdleSW_VGasPos2_Func_Diag_G IdleSW_VGasPos2_Func_Diag;/* '<S22>/IdleSW_VGasPos2_Func_Diag' */
  rtB_IdleSW_VGasPos1_Func_Diag_G IdleSW_VGasPos1_Func_Diag;/* '<S22>/IdleSW_VGasPos1_Func_Diag' */
} BlockIO_GasPosMgm;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  uint32_T UnitDelay_DSTATE;           /* '<S24>/Unit Delay' */
  uint16_T Memory_PreviousInput[2];    /* '<S17>/Memory' */
  int16_T Memory1_PreviousInput[2];    /* '<S17>/Memory1' */
  int16_T Memory2_PreviousInput[2];    /* '<S17>/Memory2' */
  int16_T Memory3_PreviousInput[2];    /* '<S17>/Memory3' */
  struct {
    uint_T is_c2_GasPosMgm:2;          /* '<S13>/Calc_Freeze_condition' */
    uint_T is_active_c2_GasPosMgm:1;   /* '<S13>/Calc_Freeze_condition' */
  } bitsForTID0;

  uint16_T tmpOldGasPos;               /* '<S13>/Calc_Final_GasPos' */
  uint8_T UnitDelay_DSTATE_ayxw;       /* '<S23>/Unit Delay' */
  uint8_T DelayInput1_DSTATE;          /* '<S25>/Delay Input1' */
  uint8_T Memory1_PreviousInput_mxi5;  /* '<S18>/Memory1' */
  uint8_T Memory_PreviousInput_eqrl;   /* '<S18>/Memory' */
  uint8_T stRtUp;                      /* '<S13>/Calc_Final_GasPos' */
  boolean_T Sensor_coherence_MODE;     /* '<S18>/Sensor_coherence' */
} D_Work_GasPosMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState Call_Reset_Trig_ZCE;      /* '<S1>/Call_Reset' */
  ZCSigState Call_10ms_Trig_ZCE;       /* '<S1>/Call_10ms' */
} PrevZCSigStates_GasPosMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
} ExternalInputs_GasPosMgm;

/* Block signals (default storage) */
extern BlockIO_GasPosMgm GasPosMgm_B;

/* Block states (default storage) */
extern D_Work_GasPosMgm GasPosMgm_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_GasPosMgm GasPosMgm_U;

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  Code generation will declare the memory for
 * these signals and export their symbols.
 *
 */
extern uint16_T vtgaspos_old[2];       /* '<S17>/Memory' */
extern int16_T dvtgaspos[2];           /* '<S17>/Switch' */

/* Model entry point functions */
extern void GasPosMgm_initialize(void);
extern void GasPosMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('gaspos2sens_gen/GasPosMgm')    - opens subsystem gaspos2sens_gen/GasPosMgm
 * hilite_system('gaspos2sens_gen/GasPosMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'gaspos2sens_gen'
 * '<S1>'   : 'gaspos2sens_gen/GasPosMgm'
 * '<S8>'   : 'gaspos2sens_gen/GasPosMgm/Call_10ms'
 * '<S9>'   : 'gaspos2sens_gen/GasPosMgm/Call_Reset'
 * '<S10>'  : 'gaspos2sens_gen/GasPosMgm/Init'
 * '<S11>'  : 'gaspos2sens_gen/GasPosMgm/T10ms'
 * '<S12>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/Choose'
 * '<S13>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw'
 * '<S14>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Calc_Final_GasPos'
 * '<S15>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Calc_Freeze_condition'
 * '<S16>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Calc_Recovery'
 * '<S17>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Calc_delta'
 * '<S18>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Coherence_diag'
 * '<S19>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/ElectricDiagTest'
 * '<S20>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/InverseSensorCharacteristic'
 * '<S21>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Stop_Creeping'
 * '<S22>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest'
 * '<S23>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch ElectricDiagTest'
 * '<S24>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/timehist_gas'
 * '<S25>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Calc_delta/Detect Increase'
 * '<S26>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Coherence_diag/GenAbs'
 * '<S27>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Coherence_diag/Sensor_coherence'
 * '<S28>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Coherence_diag/GenAbs/Data Type Conversion Inherited'
 * '<S29>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Coherence_diag/Sensor_coherence/SetDiagState'
 * '<S30>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/ElectricDiagTest/DiagMgm_RangeCheck_U1'
 * '<S31>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/ElectricDiagTest/DiagMgm_RangeCheck_U16'
 * '<S32>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/ElectricDiagTest/SetDiagState'
 * '<S33>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/ElectricDiagTest/SetDiagState1'
 * '<S34>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Stop_Creeping/Compare To Zero'
 * '<S35>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Stop_Creeping/Compare To Zero1'
 * '<S36>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Stop_Creeping/Compare To Zero2'
 * '<S37>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Stop_Creeping/Compare To Zero3'
 * '<S38>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/Chart'
 * '<S39>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/Diag_GasSW_coh'
 * '<S40>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/FlgGasZero_Calc'
 * '<S41>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/IdleSW_VGasPos1_Func_Diag'
 * '<S42>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/IdleSW_VGasPos2_Func_Diag'
 * '<S43>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/Diag_GasSW_coh/SetDiagState'
 * '<S44>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/FlgGasZero_Calc/Compare To Zero'
 * '<S45>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/FlgGasZero_Calc/Compare To Zero1'
 * '<S46>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch ElectricDiagTest/DiagMgm_RangeCheck_U16'
 * '<S47>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch ElectricDiagTest/Interval Test Dynamic'
 * '<S48>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch ElectricDiagTest/Interval Test Dynamic1'
 * '<S49>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch ElectricDiagTest/Interval Test Dynamic2'
 * '<S50>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/Switch ElectricDiagTest/SetDiagState'
 * '<S51>'  : 'gaspos2sens_gen/GasPosMgm/T10ms/dbw/timehist_gas/LookUp_U8_U16'
 */

/*-
 * Requirements for '<Root>': GasPosMgm
 */
#endif                                 /* RTW_HEADER_GasPosMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
