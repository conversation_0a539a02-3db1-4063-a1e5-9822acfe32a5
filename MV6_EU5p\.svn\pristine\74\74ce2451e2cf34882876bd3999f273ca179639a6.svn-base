/*
 * File: PresTarget.h
 *
 * Code generated for Simulink model 'PresTarget'.
 *
 * Model version                  : 1.2290
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Sep 12 15:33:58 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (21), Warnings (4), Errors (8)
 */

#ifndef RTW_HEADER_PresTarget_h_
#define RTW_HEADER_PresTarget_h_
#ifndef PresTarget_COMMON_INCLUDES_
# define PresTarget_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* PresTarget_COMMON_INCLUDES_ */

#include "PresTarget_types.h"

/* Includes for objects with custom storage classes. */
#include "prestarget_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKQAIRTMOD_dim                 16U                       /* Referenced by:
                                                                  * '<S8>/BKQAIRTMOD_dim'
                                                                  * '<S8>/BKQAIRTMOD_dim1'
                                                                  */

/* BKQAIRTMOD dimension */
#define BKRPMPRESOBJ_dim               28U                       /* Referenced by: '<S8>/BKRPMPRESOBJ_dim' */

/* BKQAIRTMOD dimension */

/* Block states (default storage) for system '<Root>' */
typedef struct {
  int32_T DQAirTHiR;                   /* '<S1>/Data Store Memory4' */
} D_Work_PresTarget;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
  ZCSigState trig_to_fc_Trig_ZCE;      /* '<S1>/trig_to_fc' */
} PrevZCSigStates_PresTarget;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PowerOff;                 /* '<Root>/ev_PowerOff' */
} ExternalInputs_PresTarget;

/* Block states (default storage) */
extern D_Work_PresTarget PresTarget_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_PresTarget PresTarget_U;

/* Model entry point functions */
extern void PresTarget_initialize(void);
extern void PresTarget_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget')    - opens subsystem PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget
 * hilite_system('PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PresTarget_fxp/PresTarget/PresTarget_1_0'
 * '<S1>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget'
 * '<S2>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/Init'
 * '<S3>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/NoSync'
 * '<S4>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms'
 * '<S5>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/trig_to_fc'
 * '<S6>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/trig_to_fc1'
 * '<S7>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/trig_to_fc2'
 * '<S8>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj'
 * '<S9>'   : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Reset_var'
 * '<S10>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/Calc_QAirTargetMod'
 * '<S11>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/Clear_QAirTargetMod'
 * '<S12>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/Compare To Constant'
 * '<S13>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/Compare To Constant2'
 * '<S14>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/FOF_Reset_S16_FXP'
 * '<S15>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/Look2D_U16_U16_U16'
 * '<S16>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/LookUp_U16_U16'
 * '<S17>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S18>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S19>'  : 'PresTarget_fxp/PresTarget/PresTarget_1_0/PresTarget/T10ms/Calc_PresObj/LookUp_U16_U16/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': PresTarget
 */
#endif                                 /* RTW_HEADER_PresTarget_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
