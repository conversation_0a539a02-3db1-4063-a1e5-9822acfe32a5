/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef  _BUILD_SYS_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "mpc5500_spr_macros.h"
    
#include "sys.h"
#include "adc.h"
#include "task.h" //needed to use TASK_DisablePeripherals
#include "intsrcmgm.h"
#include "digio.h"
#include "mathlib.h"
#include "pit.h"
    
    
#include "etpu_util.h"
#include "..\common\ETPU_Shared.h"
#include "..\include\ETPU_HostInterface.h"
    
#include "..\auto\etpu_angle_clock_auto.h"
#include "..\auto\etpu_PWM_auto.h"
    
#if (ENGINE_TYPE == PI_500_1C_4V) 
#include "..\auto\etpu_piaggio_generated_image.h"
#elif (ENGINE_TYPE == FE_6300_12C_48V) 
#include "..\auto\etpu_ferrari_generated_image.h"
#elif (ENGINE_TYPE == FE_4300_8C_32V)
#include "..\auto\etpu_ferrari_v8_generated_image.h"
#elif (ENGINE_TYPE == FE_4300_8C_32V_TDN)
#include "..\auto\etpu_ferrari_v8_tdn_generated_image.h"
    
#elif (ENGINE_TYPE == FE_4300_8C_32V_GT2)
#include "..\auto\etpu_ferrari_v8_gt2_generated_image.h"
//#warning DA CAMBIARE IN GT2 ! ! !
    
#elif (ENGINE_TYPE == YP_250_G) 
#include "..\auto\etpu_yamaha_generated_image.h"
#elif (ENGINE_TYPE == MA_MC12_12C) 
#include "..\auto\etpu_maserati_generated_image.h"
#elif (ENGINE_TYPE == VW_1400_4C_16V)
#include "..\auto\etpu_vw_generated_image.h"
#elif (ENGINE_TYPE == EM_VW_1400_4C_16V)
#include "..\auto\etpu_em_4cyl_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_4C)
#include "..\auto\etpu_em_4cyl_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_08)
#include "..\auto\etpu_em_3cyl_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
#include "..\auto\etpu_em_3cyl_new_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
#include "..\auto\etpu_em_3cyl_36_2_generated_image.h"
#elif (ENGINE_TYPE == MV_AGUSTA_4C_TDC_0_9)
#include "..\auto\etpu_em_4cyl_36_2_generated_image.h"
#elif (ENGINE_TYPE == PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID) 
#include "..\auto\etpu_piaggio_generated_image.h"
#else
#error WRONG CONFIGURATION!!!
#endif

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

const uint32_t fs_etpu_code_start     = (uint32_t)ETPU_CODE_RAM;
const uint32_t fs_etpu_data_ram_start = (uint32_t)ETPU_DATA_RAM;
const uint32_t fs_etpu_data_ram_end   = (uint32_t)ETPU_DATA_RAM_END;
const uint32_t fs_etpu_data_ram_ext   = (uint32_t)ETPU_DATA_RAM_EXT;

uint16_t    sysInternalState;
uint8_t     sysEnablingState;
uint32_t    BIOS_Faults;        /* variable for storing bios failures       */
uint32_T    BiosInit;

uint32_t    PeriodVarRateA;   /* to export the period set for IONTRIGGER_A */
uint32_t    PeriodVarRateB;   /* to export the period set for IONTRIGGER_B */


/* Symbols used in the OS errors managing */
OSSERVICEIDTYPE tempOsService;  /* for OSErrorGetServiceId() from ErrorHook */
uint32_t        tempOsObjId;    /* for first parameter                      */


/* Imported from analogin.c */
extern uint8_T ForceInitMapAcqAngle;
/* Imported from ionacq.c */
extern uint16_t NSampleChA;
extern uint16_t NSampleChB;
/* Imported from ETPU_HostInterfaces.c */
extern TaskType _ETPU_A_vect_user_isr[ETPU_NUM_OF_CHANNELS];
#if (USE_ETPU_B == 1)
extern TaskType _ETPU_B_vect_user_isr[ETPU_NUM_OF_CHANNELS];
#endif
/* Imported from ivor.c */
extern void (*IVOR10_UserFunction) (void);


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/

#define MAX_SAMPLE_NUM 500


/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
typedef struct adcTrigger
{
    uint16_t sampling;
    uint16_t triggerSource;
    uint16_t channel;
    uint8_t  isEMIOS;
    uint8_t  isETPU;
}adcTrigger_t;

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
* SYS_DecrementerFunc - Function description
*
* Arguments:
* None
*
* Returned value:
* None
*
* Usage notes:
* None
*--------------------------------------------------------------------------*/
static void SYS_DecrementerFunc(void);

/*--------------------------------------------------------------------------*
* SYS_ShutdownTimedOut - User function called by IVOR10 after shutdown timout
*
* Arguments:
* None
*
* Returned value:
* None
*
* Usage notes:
* None
*--------------------------------------------------------------------------*/
static void SYS_ShutdownTimedOut (void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static const adcTrigger_t ADC_triggerConfigTable[6] =
{
    {VARIABLE_RATE_A    ,CFIFO_00_TRG, ION_TRIGGER_A         ,0 ,0},
    {ANGULAR_SAMPLING   ,CFIFO_01_TRG, ANGULAR_TRIGGER       ,0 ,1},
    {_450_MICROSEC      ,CFIFO_02_TRG, TRIGGER_450_MICROSEC  ,0 ,0},
    {_2_MILLISEC        ,CFIFO_03_TRG, TRIGGER_2_MILLISEC    ,0 ,0},
    {_10_MILLISEC       ,CFIFO_04_TRG, TRIGGER_10_MILLISEC   ,0 ,0},
    {SOFTWARE_TRIGGERED ,CFIFO_05_TRG, SOFTWARE_TRIGGERED    ,1 ,0}
};


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * SYS_Config - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_Config(void)
{
    int16_t errorCode = NO_ERROR;
    uint16_t i;


    sysEnablingState = SYE_STATE_DISABLED;

    for (i=0; i<ETPU_NUM_OF_CHANNELS; i++)
    {
        _ETPU_A_vect_user_isr[i] = NULLTASK;
#if (USE_ETPU_B == 1)
        _ETPU_B_vect_user_isr[i] = NULLTASK;
#endif
    } 

    if((sysInternalState == SYI_STATE_PRE_CONFIGURATION)) 
    {
        /* Pointer to the eTPU shared code RAM */
         char *scram = (char*)fs_etpu_code_start;
        
        /* Set ETPU Entry Table Base Address */
        ETPU.ECR_A.B.ETB = ETPU_ENTRYTABLEADDR / 0x800;
        
#if (TARGET_TYPE == MPC5554)
        ETPU.ECR_B.B.ETB = ETPU_ENTRYTABLEADDR / 0x800;
#endif
        
        /* Load microcode */
        /* In order to write the eTPU code in RAM, both eTPU engines have to be stopped.  */
        /* Stopping eTPU engines can be done by setting the low power stop bit.           */
        ETPU.ECR_A.B.MDIS = 1;       // Low power Stop
#if (TARGET_TYPE == MPC5554)
        ETPU.ECR_B.B.MDIS = 1;       // Low power Stop
#endif
        
#if(ETPU_CODE_ENABLED==1)
        /* Enable writing to SCM */
        ETPU.MCR.B.VIS = 1;        // SCM Visability
        
        /* Copy microcode into SCRAM */ 
        /* done by debugger          */
        fs_memcpy8((void *)scram, (void *)etpucode, sizeof(etpucode));
        errorCode = fs_memCopyVerifier ((char *)scram, (char *)etpucode, sizeof(etpucode));

        /* Disable writing to SCM */
        ETPU.MCR.B.VIS = 0;         // SCM Visability
        
        if(errorCode)
        {
#ifdef CHECK_BIOS_FAULTS        
            BIOS_Faults |= BIOS_FAILURE_ETPU;
#endif /* CHECK_BIOS_FAULTS */
            errorCode = PERIPHERAL_FAILURE;
        }
#endif
        if (errorCode == NO_ERROR)
        {
            /* After writing the eTPU code in RAM, both eTPU engines have to be re-started.  */
            /* Restarting eTPU engines can be done by clearing the low power stop bit.       */
            ETPU.MCR.B.SCMMISEN = 0;        // 1 SCM MISC Enable 
            //ETPU.MCR.B.GTBE     = 1;          // Global Time Base Enable 
            
            ETPU.ECR_A.B.MDIS   = 0;            // Restart - Low power Stop
#if (TARGET_TYPE == MPC5554)
            ETPU.ECR_B.B.MDIS   = 0;            // Restart - Low power Stop
#endif
         
            /* ETPU_TBCR_A: TCR2CTL=2,TCRCF=0,AM=1,TCR2P=2,TCR1CTL=2,TCR1P=5 */
            /* setReg32(ETPU_TBCR_A, 0x42028005);  */
            /* eTPU_A Time Base Configuration Register */ 
            ETPU.TBCR_A.B.TCR1CTL = ETPU_TBCR_TCR1CTL_SELECT_SYSTEMCLOCK_DIV2;  // TCR1 Clock/Gate Control 
            ETPU.TBCR_A.B.TCR2CTL = ETPU_TBCR_TCR2CTL_TCRCLK_TRANS;  // 2 TCR2 Clock/Gate Control 
            ETPU.TBCR_A.B.TCR1P   = ETPU_A_TCR1P;    // 39 TCR1 Prescaler Control 
            ETPU.TBCR_A.B.TCR2P   = ETPU_A_TCR2P;    // 2 TCR2 Prescaler Control 
            ETPU.TBCR_A.B.TCRCF   = ETPU_A_TCRF;     // 0 TCRCLK Signal Filter Control 
            //ETPU.TBCR_A.B.AM      = 1;             // 1 Angle Mode 

            /* ETPU_TBCR_B: TCR2CTL=2,TCRCF=0,AM=1,TCR2P=2,TCR1CTL=2,TCR1P=5 */
            /* setReg32(ETPU_TBCR_B, 0x42028005);  */
            /* eTPU_B Time Base Configuration Register */ 
#if (USE_ETPU_B == 1)
#if (TARGET_TYPE == MPC5554)
            ETPU.TBCR_B.B.TCR1CTL = ETPU_TBCR_TCR1CTL_SELECT_SYSTEMCLOCK_DIV2;  // TCR1 Clock/Gate Control 
            ETPU.TBCR_B.B.TCR2CTL = ETPU_TBCR_TCR2CTL_TCRCLK_TRANS;  // 2 TCR2 Clock/Gate Control 
            ETPU.TBCR_B.B.TCR1P   = ETPU_B_TCR1P;    // 39 TCR1 Prescaler Control 
            ETPU.TBCR_B.B.TCR2P   = ETPU_B_TCR2P;    // 2 TCR2 Prescaler Control 
            ETPU.TBCR_B.B.TCRCF   = ETPU_B_TCRF;     // 0 TCRCLK Signal Filter Control 
#endif
#endif

            /* ETPU_ECR_A: FEND=0,STOP=0,FPSCK=0,CDFC=0 */
            /* setReg32(ETPU_ECR_A, 0x00); */
            /* eTPU_A Engine Configuration Register */    
            ETPU.ECR_A.B.FEND   = 0;      // Force END to FALSE (normal operation mode) 
            ETPU.ECR_A.B.STF    = 0;      // STOP flag to FALSE 
            ETPU.ECR_A.B.FPSCK  = ETPU_A_FPSCK;      // Filter Prescaler Clock Control 
            ETPU.ECR_A.B.CDFC   = ETPU_ECR_CDFC_THREE_SAMPLE_MODE;      //3 Channel digital filter control. 

            /* ETPU_ECR_B: FEND=0,STOP=0,FPSCK=0,CDFC=0 */
            /* setReg32(ETPU_ECR_B, 0x00); */
            /* eTPU_B Engine Configuration Register */    
#if (USE_ETPU_B == 1)
#if (TARGET_TYPE == MPC5554)
            ETPU.ECR_B.B.FEND   = 0;      // Force END to FALSE (normal operation mode) 
            ETPU.ECR_B.B.STF    = 0;      // STOP flag to FALSE 
            ETPU.ECR_B.B.FPSCK  = ETPU_B_FPSCK;      // Filter Prescaler Clock Control 
            ETPU.ECR_B.B.CDFC   = ETPU_ECR_CDFC_THREE_SAMPLE_MODE;      //3 Channel digital filter control. 
#endif
#endif

            /* 
              ETPU.ECR_A.B.CDFC = 3
              eTPU continuous mode: Signal needs to be stable for the whole filter
              clock period. This mode compares all the values at the rate of system
              clock divided by two, between two consecutive filter clock pulses. If all
              the values agree with each other, input signal state is updated.
            */

            /* ETPU_REDCR_A: REN1=0,RSC1=0,SRV1=0,REN2=1,RSC2=0,SRV2=1 */
            /* setReg32(ETPU_REDCR_A, 0x8001);     */
            /* STAC Bus Configuration Register */ 
            ETPU.REDCR_A.B.REN1   = 0;
            ETPU.REDCR_A.B.RSC1   = 0;
            ETPU.REDCR_A.B.SRV1   = 0;
            ETPU.REDCR_A.B.REN2   = 0;
            ETPU.REDCR_A.B.RSC2   = 0;
            ETPU.REDCR_A.B.SRV2   = 0;
            
            /* ETPU_REDCR_B: REN1=0,RSC1=0,SRV1=0,REN2=1,RSC2=0,SRV2=1 */
            /* setReg32(ETPU_REDCR_B, 0x8001);     */
            /* STAC Bus Configuration Register */ 
#if (USE_ETPU_B == 1)
#if (TARGET_TYPE == MPC5554)
            ETPU.REDCR_B.B.REN1   = 0;
            ETPU.REDCR_B.B.RSC1   = 0;
            ETPU.REDCR_B.B.SRV1   = 0;
            ETPU.REDCR_B.B.REN2   = 0;
            ETPU.REDCR_B.B.RSC2   = 0;
            ETPU.REDCR_B.B.SRV2   = 0;
#endif
#endif

            /* ETPU global variables initialization */    
            {
                uint32_t size = globalVarImageSize;
                /* Copy intial global values to parameter RAM. */
                fs_memcpy8 ( (void *)fs_etpu_data_ram_start, (void *)globalVarImage, size);
                errorCode = fs_memCopyVerifier ((char *)fs_etpu_data_ram_start, (char *)globalVarImage, size);
                if(errorCode)
                {
#ifdef CHECK_BIOS_FAULTS        
                    BIOS_Faults |= BIOS_FAILURE_ETPU;
#endif /* CHECK_BIOS_FAULTS */
                    errorCode = PERIPHERAL_FAILURE;
                }
                if (errorCode == NO_ERROR)
                {
                    /* initialize parameters memory area */
                    fs_free_param = (uint32_t *)(fs_etpu_data_ram_start + 0x400);
                }
            }
            if (errorCode == NO_ERROR)
            {
                /* Initialize SIU in order to use eTPU related Pins */
                ETPU_InitializeSIU_for_eTPU();

                // change module internal state 
                sysInternalState = SYI_STATE_CONFIGURED;
            }
        }
  }
  else
  {
    errorCode = PERIPHERAL_ALREADY_CONFIGURED;
  }
  
  return (errorCode) ;
}

/*--------------------------------------------------------------------------*
 * SYS_ADC_SourceConfig - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_ADC_SourceConfig(uint16_t _samplingMode, t_TriggerBase _triggerBase)
{
    t_AdcSource _source;
    int16_t retValue = NO_ERROR;
    uint16_t channel;
    uint16_t period;

    if(ADC_triggerConfigTable[_samplingMode].isETPU)
    {
        _source = ETPUA_SOURCE;
    }
    else if(ADC_triggerConfigTable[_samplingMode].isEMIOS)
    {
        _source = EMIOS_SOURCE;
    }
    else
    {
        _source = ETRIG_SOURCE;
    }
    
    if(
        ((_source != ETPUA_SOURCE)&&(_triggerBase == ANGLE_BASE)) ||
        ((_source == GPIO_SOURCE) &&(_triggerBase == TIME_BASE))  ||
        ((_source == ETPUA_SOURCE)&&(_triggerBase == EXT_BASE))   ||
        ((_source == EMIOS_SOURCE)&&(_triggerBase == EXT_BASE))   ||
        (_samplingMode == SOFTWARE_TRIGGERED)
    )
    {
        retValue = SYS_CONFIGURATION_NOT_SUPPORTED;
    }
    else
    {
    
        if(_source == ETPUA_SOURCE)
        {
            channel = ADC_triggerConfigTable[_samplingMode].channel - ETPUA_UC0;

            switch (_samplingMode)
            {
                case VARIABLE_RATE_A:
                    period = 100;
                break;
                case ANGULAR_SAMPLING:
                    period = 1000;
                break;
                case _450_MICROSEC: 
                    period = 450;
                break;
                case _2_MILLISEC:
                    period = 2000;
                break;
                case _10_MILLISEC:
                    period = 10000;
                break;
                case SOFTWARE_TRIGGERED:
                break;
                default:
                break;
            }
            if (_samplingMode == ANGULAR_SAMPLING)
            {
                retValue = ETPU_ANGTRIG_Config(channel);
            }
            else
            {
                if(ETPU_PWM_OutConfig(channel, (t_EdgePolarity)ACTIVE_LOW, PWM_MATCH_TIME,(50<<8), period, 0,
                            (uint16_t)FS_ETPU_PRIORITY_MIDDLE, (uint16_t)ETPU_COUNT_MODE_CONT,
                            (uint16_t)PWM_FUNC_TABLE_SELECT, (uint16_t)PWM_FUNC_FUNCTION_NUMBER, 
                            (t_EdgeCount)0) < 0)
                {
                    retValue = PERIPHERAL_FAILURE;
                }
            }
        }
        else
        if(_source == ETRIG_SOURCE)
        {
            channel = ADC_triggerConfigTable[_samplingMode].channel;

            if (_samplingMode == VARIABLE_RATE_A) 
            {
                period = 100;
                if(PIT_SetBurst((t_PIT_Burst)channel, MAX_SAMPLE_NUM, period, PIT_BURST_SET)< 0)
                {
                    retValue = PERIPHERAL_FAILURE;
                }
            }
            else
            {
                switch (_samplingMode)
                {
                    case _450_MICROSEC: 
                        period = 450;
                    break;
                    case _2_MILLISEC:
                        period = 2000;
                    break;
                    case _10_MILLISEC:
                        period = 10000;
                    break;
                    case SOFTWARE_TRIGGERED:
                    break;
                    default:
                    break;
                }
                if(PIT_SetTimer((t_PIT_Timer)channel, period, PIT_TIMER_SET, NULLTASK)< 0)
                {
                    retValue = PERIPHERAL_FAILURE;
                }
            }
        }
        else
        {
        }
    }
    return retValue;     
}


/*--------------------------------------------------------------------------*
 * SYS_ADC_SetPeriod - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_ADC_SetPeriod(uint16_t _samplingMode,
                          uint32_t _period)
{
    uint16_t channel;
    int16_t retValue = NO_ERROR;
    
    if((_samplingMode != SOFTWARE_TRIGGERED)&&(_samplingMode != ANGULAR_SAMPLING))
    {
        channel = ADC_triggerConfigTable[_samplingMode].channel;
        
        if(ADC_triggerConfigTable[_samplingMode].isETPU)
        {
            channel -= ETPUA_UC0;
            if(ETPU_PWM_OutSetPeriod(channel, _period) < 0)
            {
                retValue = PERIPHERAL_FAILURE;
            }
            if(ETPU_PWM_OutSetDutyCicle(channel, (50<<8), _period) < 0)
            {
                retValue = PERIPHERAL_FAILURE;
            }
        }    
        else if(ADC_triggerConfigTable[_samplingMode].isEMIOS)
        {
            if(PIO_PwmOutConfig(channel, ACTIVE_LOW, PWM_MATCH_TIME, 50<<8, _period, 0) < 0)
            {
                retValue = PERIPHERAL_FAILURE;
            }
        }
        else
        {
            if (channel == ION_TRIGGER_A)
            {
                 PeriodVarRateA = _period;
            }
            else
            {   
                if(PIT_SetTimer((t_PIT_Timer)channel, _period, PIT_TIMER_SET, NULLTASK)< 0)
                {
                    retValue = PERIPHERAL_FAILURE;
                }
            }
        }
    }
    return retValue;     
}


/*--------------------------------------------------------------------------*
 * SYS_ADC_SetStatus - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_ADC_SetStatus(uint16_t _samplingMode, 
                          t_TriggerStatus _status)
{
    uint16_t channel;
    uint16_t dmaChannel1, dmaChannel2, dmaChannel3;
    int16_t  error = NO_ERROR;

    if(_samplingMode != SOFTWARE_TRIGGERED)
    {
        channel = ADC_triggerConfigTable[_samplingMode].channel;

        switch (_samplingMode)
        {
            case VARIABLE_RATE_A:
              dmaChannel1 = (uint16_t)DMA_EQADC_FISR5_CFFF5;
              dmaChannel2 = (uint16_t)DMA_EQADC_FISR0_RFDF0;
              dmaChannel3 = (uint16_t)DMA_EQADC_FISR0_CFFF0;
            break;
            case ANGULAR_SAMPLING:
              dmaChannel2 = (uint16_t)DMA_EQADC_FISR1_RFDF1;
              dmaChannel3 = (uint16_t)DMA_EQADC_FISR1_CFFF1;
            break;
            default:
            break;
        }

        if(ADC_triggerConfigTable[_samplingMode].isETPU)
        {
            channel -= ETPUA_UC0;
            if (_samplingMode != ANGULAR_SAMPLING)
            {
                if(_status == TRIGGER_ON)
                {
                    error = ETPU_PWM_OutEnable(channel);
                }
                else
                {
                    error = DMA_Disable(dmaChannel1);
                    error = DMA_Disable(dmaChannel2);
                    error = DMA_Disable(dmaChannel3); 
                    error = ETPU_PWM_OutDisable(channel);
                }
            }
            else    /* ANGULAR_SAMPLING */
            {
                if(_status == TRIGGER_ON)
                {
                    error = ETPU_ANGTRIG_Enable(channel);
                }
                else
                {
                    if (ForceInitMapAcqAngle)
                    {
                        error = ETPU_ANGTRIG_Disable(channel, 1);
                        error = DMA_Disable(dmaChannel2);
                        error = DMA_Disable(dmaChannel3); 
                    }
                    else
                    {
                        error = DMA_Disable(dmaChannel2);
                        error = DMA_Disable(dmaChannel3); 
                        error = ETPU_ANGTRIG_Disable(channel,0);
                    }
                }
            }
            if(error < 0)
            {
                error = PERIPHERAL_FAILURE;
            }
        }    
        else if(ADC_triggerConfigTable[_samplingMode].isEMIOS)
        {
            if(_status == TRIGGER_ON)
            {
                error = PIO_Enable();
                if((error == NO_ERROR)||(error == PERIPHERAL_ALREADY_ENABLED))
                {
                    error = PIO_PwmOutEnable(channel);
                }
            }
            else
            {
                error = DMA_Disable(dmaChannel1);
                error = DMA_Disable(dmaChannel2);
                error = DMA_Disable(dmaChannel3); 
                error = PIO_PwmOutDisable(channel);
            }
            if(error < 0)
            {
                error = PERIPHERAL_FAILURE;
            }
        }
        else
        {
            if(_status == TRIGGER_ON)
            {
                if (_samplingMode == VARIABLE_RATE_A) 
                {
                    error = PIT_Burst((t_PIT_Burst)channel);      
                }
                else
                {
                    error = PIT_EnableTimer((t_PIT_Timer)channel);
                }
            }
            else
            {
                if (_samplingMode == VARIABLE_RATE_A)
                {
                    error = DMA_Disable(dmaChannel1);
                    error = DMA_Disable(dmaChannel2);
                    error = DMA_Disable(dmaChannel3); 
                    error = PIT_UnSetBurst((t_PIT_Burst)channel);      
                }
                else
                {
                    error = PIT_DisableTimer((t_PIT_Timer)channel);
                }
            }
            
            if(error < 0)
            {
                error = PERIPHERAL_FAILURE;
            }
        }
    }
    return error;     
}

/*--------------------------------------------------------------------------*
 * SYS_ADC_SetPitBurst - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_ADC_SetPitBurst(uint16_t _samplingMode, 
                            uint16_t _pulseNum)
{
    uint16_t channel;
    int16_t res;

    channel = ADC_triggerConfigTable[_samplingMode].channel;
    
    res = PIT_SetBurst((t_PIT_Burst)channel, _pulseNum, PeriodVarRateA, PIT_BURST_SET);

    return res;
}

/*--------------------------------------------------------------------------*
 * SYS_ADC_GetSampMode - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint16_t SYS_ADC_GetSampMode(uint16_t idxQueue)
{
    return ADC_triggerConfigTable[idxQueue].sampling;
}


/*--------------------------------------------------------------------------*
 * SYS_ADC_SetAngTrigger - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_ADC_SetAngTrigger(uint32_t *angTrigBuf, 
                              uint32_t _period, 
                              uint8_t trigNum)
{
    uint16_t channel;
    int16_t retValue = NO_ERROR;
    uint8_t trigMode;

    channel = (ADC_triggerConfigTable[ANGULAR_SAMPLING].channel - ETPUA_UC0);

    if (_period != 0)
    {
        trigMode = TIME_TRIGGER_MODE;
    }
    else
    {
        trigMode = ANGLE_TRIGGER_MODE;
    }
    
    retValue = ETPU_ANGTRIG_Set(channel, angTrigBuf, _period, trigMode, trigNum);
    
    return retValue;     
}


/*--------------------------------------------------------------------------*
 * SYS_ProgDelayedShutdown - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void SYS_ProgDelayedShutdown (void)
{
    DisableAllInterrupts();       /* Disable external interrupts          */
    SYS_DisableWatchdog();        /* Stop watchdog timer                  */
    TASK_DisablePeripherals();    /* Set to zero all Peripherals priority */
    IVOR10_UserFunction = (void (*)(void))SYS_ShutdownTimedOut;
    SYS_DecrementerFunc();        /* Activate shutdown routine            */
    EnableAllInterrupts();        /* Re-enable external interrupts        */
}

/*--------------------------------------------------------------------------*
 * SYS_GetLastReset - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_GetLastReset(uint8_t *lastReset)
{ 
    if(SIU.RSR.B.PORS == 1)
    {
        *lastReset = POWERONRESET;
    }
    else if(SIU.RSR.B.ERS == 1)
    {
        *lastReset = EXTERNALRESET;
    }
    else if(SIU.RSR.B.SSRS == 1)
    {
        *lastReset = SOFTWARESYSTEMRESET;
    }
    else if(SIU.RSR.B.LLRS == 1)
    {
        *lastReset = LOSSOFLOCKRESET;
    }
    else if(SIU.RSR.B.LCRS == 1)
    {
        *lastReset = LOSSOFCLOCKRESET;
    }
    else if(SIU.RSR.B.WDRS == 1)
    {
        *lastReset = WATCHDOGRESET;
    }
    else if(SIU.RSR.B.CRS == 1)
    {
        *lastReset = CHECKSTOPRESET;
    }
    else if(SIU.RSR.B.SERF == 1)
    {
        *lastReset = SOFTWAREEXTERNALRESET;
    }
    else
    {
        /* DO NOTHING!!! MISRA 14.10*/
    }

    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_GetBiosFaults - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_GetBiosFaults(uint32_t *faults)
{
    *faults = BIOS_Faults;

    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_GetBiosInit - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_GetBiosInit(uint32_t *outvar)
{
    *outvar = BiosInit;

    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_AssignBitBiosInit - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_AssignBitBiosInit(uint8_T bitidx, 
                              uint8_T bitvalue)
{
    if (bitvalue == 1)
    {
        SETBIT(BiosInit,bitidx);
    }
    else
    {
        RESBIT(BiosInit,bitidx);
    }
    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_AssignBitBiosFaults - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_AssignBitBiosFaults(uint8_T bitidx,
                                uint8_T bitvalue)
{
    if (bitvalue == 1)
    {
        SETBIT(BIOS_Faults,bitidx);
    }
    else
    {
        RESBIT(BIOS_Faults,bitidx);
    }
    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_WriteBiosInit - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_WriteBiosInit(uint32_t value)
{
    BiosInit = value;
    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_WriteBiosFaults - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t SYS_WriteBiosFaults(uint32_t value)
{
    BIOS_Faults = value;
    return NO_ERROR;
}

/*--------------------------------------------------------------------------*
 * SYS_ADC_GetCFIFOStatus - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint16_t SYS_ADC_GetCFIFOStatus(uint16_t idxQueue)
{
    uint16_t CFIFOStatus;

    switch (idxQueue)
    {
        case 0 :
        {
            CFIFOStatus = EQADC.CFSR.B.CFS0; /* CFIFO0 status */
            break;
        }
        case 1 :
        {
            CFIFOStatus = EQADC.CFSR.B.CFS1; /* CFIFO1 status */
            break;
        }
        case 2 :
        {
            CFIFOStatus = EQADC.CFSR.B.CFS2; /* CFIFO2 status */
            break;
        }
        case 3 :
        {
            CFIFOStatus = EQADC.CFSR.B.CFS3; /* CFIFO3 status */
            break;
        }
        case 4 :
        {
            CFIFOStatus = EQADC.CFSR.B.CFS4; /* CFIFO4 status */
            break;
        }
        case 5 :
        {
            CFIFOStatus = EQADC.CFSR.B.CFS5; /* CFIFO5 status */
            break;
        }
        default:
        {
            break;
        }
    }

    return (CFIFOStatus);
}
            
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * SYS_DecrementerFunc - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static asm void SYS_DecrementerFunc(void) 
{
    lis     r0, SHTDWN_PERIOD@h         /* Load DEC value defined in sys.h module */
    ori     r0, r0, SHTDWN_PERIOD@l
    mtdec   r0
    lis     r0, 0x0400                  /* Enable DEC interrupt   */
    mttcr   r0          
    li      r0, 0x4000                  /* Enable Time Base and Decrementer (set TBEN) */
    mthid0  r0
}

static void SYS_ShutdownTimedOut(void)
{
  /* ShutdownOS(E_OS_TIMEDOUT_KEY_OFF); */
  /* AM - changed after MISRA 16.2 activation - avoid recursive funtions 
     moved from pwrmgm.c                                              */
  ShutdownOSerrorHandler(E_OS_TIMEDOUT_KEY_OFF); 
}



#endif /* _BUILD_SYS_ */
