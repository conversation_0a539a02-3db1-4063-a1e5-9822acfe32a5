/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef __TESTIO__
#define __TESTIO__

#include "typedefs.h"
#include "digio.h"
#include "pio.h"
#include "analogin.h"
#include "lamp_mgm.h"

#define TEST_IDLE 0
#define TEST_VVT 1
#define TEST_AIRCUT 2
#define TEST_CANISTER 3
#define TEST_LEDIMMO 4
#define TEST_WLAMP 5
#define TEST_INJA1 6
#define TEST_INJA2 7
#define TEST_INJA3 8
#define TEST_INJA4 9
#define TEST_TSS 10
#define TEST_LAMH2 11
#define TEST_LOWBEAM 12
#define TEST_REARSTOP 13

#define TEST_OFF 0
#define TEST_ON 1

/* Var Esterne. */
extern uint8_T TestOutEnable;

/* Interfaccie esterne. */

#endif /* END__TESTIO */

