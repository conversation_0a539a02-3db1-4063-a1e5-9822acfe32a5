#ifndef __MMU_H__
#define __MMU_H__

#define MMU_TLB_SIZE_4K    0x00000100  // 0x1 # TLB page size =  4K bytes
#define MMU_TLB_SIZE_16K   0x00000200  // 0x2 # TLB page size = 16K bytes
#define MMU_TLB_SIZE_64K   0x00000400  // 0x3 # TLB page size = 64K bytes
#define MMU_TLB_SIZE_256K  0x00000400  // 0x4 # TLB page size =256K bytes 
#define MMU_TLB_SIZE_1M    0x00000500  // 0x5 # TLB page size =  1M bytes 
#define MMU_TLB_SIZE_4M    0x00000600  // 0x6 # TLB page size =  4M bytes 
#define MMU_TLB_SIZE_16M   0x00000700  // 0x7 # TLB page size = 16M bytes 
#define MMU_TLB_SIZE_64M   0x00000800  // 0x8 # TLB page size = 64M bytes 
#define MMU_TLB_SIZE_256M  0x00000900  // 0x9 # TLB page size =256M bytes  

void MMU_switch_Tlb(uint8_t tlbsel, uint32_t virtual_addr, uint32_t physic_addr, uint32_t tlb_size);

#endif
