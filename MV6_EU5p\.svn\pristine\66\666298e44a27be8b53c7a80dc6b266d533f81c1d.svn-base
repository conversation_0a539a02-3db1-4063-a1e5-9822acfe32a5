/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/MCU/appl_calib/trunk/tree/DD/COMMON/HeatGripDriveMg#$   */
/* $ Description:                                                                                                */
/* $Revision:: 6369   $                                                                                          */
/* $Date:: 2014-03-25 09:13:52 +0100 (mar, 25 mar 2014)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              HeatGripDriveMgm.h
 *
 * Author(s): Lana L.
 * 
 * 
 * Description:
 * 
 *
 * Usage notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifndef _HEATGRIPDRIVEMGM_H_
#define _HEATGRIPDRIVEMGM_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#undef HEATGRIP_DEBUG

#define HEAT_GRIP_INIT_DONE  1U

#define HEAT_GRIP_PERIOD     1000 /* 1KHz */
#define HEAT_GRIP_DUTY_SCALE 256  /* 2^-8 */

#define HEAT_GRIP_LEVELS 4

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint16_T HeatGripOut;
extern uint8_T  HeatedGripSt;
extern uint8_T  EnHeatGripMaxLevel;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
#ifdef HEATGRIP_DEBUG
extern const uint16_T STUBFLGHEATGRIP;
#endif
extern const uint8_T  ENHGMAXLEVEL;
extern const uint16_T DISHEATGRIPRPM;
extern const uint16_T HYSDISHEATGRIPRPM;
extern const uint8_T  TIM1SELFHG;
extern const uint8_T  TIM2SELFHG;
extern const uint16_T VTHEATGRIPDUTY[HEAT_GRIP_LEVELS];
/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
  /*--------------------------------------------------------------------------*
 * HeatGripDriveMgm_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void HeatGripDriveMgm_Init (void);

 /*--------------------------------------------------------------------------*
 * HeatGripDrive_T5m - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
int16_T HeatGripDrive_T100m (void);

#endif 

/****************************************************************************
 ****************************************************************************/
 
