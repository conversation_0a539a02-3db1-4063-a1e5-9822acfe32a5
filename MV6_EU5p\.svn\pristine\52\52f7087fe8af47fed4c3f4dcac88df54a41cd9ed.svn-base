/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

// -----------------------------------------------------------------------------
// INCLUDE FILES
// -----------------------------------------------------------------------------
//#include "ccp_can_interface.h"   // additional functions for CCP usage
// -----------------------------------------------------------------------------
#ifdef _BUILD_CCP_



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "ccp.h"
#include "CAN.h"
#include "..\include\ccp_can_interface.h"
#include "canmgm.h"
#ifdef _BUILD_SPICAN_
#include "spican.h"
#endif
#include "tasksdefs.h"
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* None */
/* Example:
uint16_T templatePublicVar;
*/

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define CALROM_ADDR 0x0
#define CALRAM_ADDR 0x3ffc000


#define MAX_TX_ERROR   300

#undef DEB_CAN_A_MB
/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
/* Example:
static void TemplatePrivateFunc(void);
*/

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
#ifdef DEB_CAN_A_MB
static uint16_t cntCCP_CANA[64];
#endif

static uint8_t ccpCalPage;
static uint8_t ccpSendRetCode;



/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ccpSend - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_t ccpSend(uint8_t *msg)
{
    static uint16_t cnterr = 0;
    uint8_t returncode = NO_ERROR;
    
    if(returncode==NO_ERROR)
    {
      if (GetBuffer() != NO_ERROR)
      {
          if (cnterr>MAX_TX_ERROR)
          {
            ccp.SessionStatus &= ~SS_CONNECTED;
#ifdef CCP_DAQ
            ccpStopAllDaq();
#endif
#ifdef CCP_SEED_KEY
            ccp.ProtectionStatus = 0; /* Clear Protection Status */
#endif
            returncode=CAN_BUSOFF;// return CAN_BUSOFF; misra 14.7 
          } 
          else
          {
            cnterr++; 
            returncode = CAN_TX_BUSY;
          }
      
      }
      if(returncode==NO_ERROR)
      {
        cnterr = 0;
        ccpSendRetCode = CAN_TxData(CCP_CAN,buffsel,msg);  //new method 2
        #ifdef DEB_CAN_A_MB
        cntCCP_CANA[buffsel]++;
        #endif
        //returncode=ccpSendRetCode;/*MISRA 14.7*/
      }
    }
    if(returncode==NO_ERROR)// misra 14.7 
    {
        returncode=ccpSendRetCode;
    }// misra 14.7 

    return (returncode);/*MISRA 14.7*/
}

/*--------------------------------------------------------------------------*
 * ccpTxCrmPossible - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int ccpTxCrmPossible( void )
{
    return ccpSendRetCode;
}

/*--------------------------------------------------------------------------*
 * ccpGetPointer - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_t * ccpGetPointer( uint8_t addr_ext, 
                         uint32_t addr )        
{                                                     
    return (uint8_t *) addr;
}


// -----------------------------------------------------------------------------
/*----------------------------------------------------------------------------*/
/*  */

/*--------------------------------------------------------------------------*
 * ccpSetCalPage - Calibration RAM/ROM Selection
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ccpSetCalPage( uint32_t a )
{
    ccpCalPage = (uint8_t)a;

    if (ccpCalPage==1)/* RAM */
    { 
    }
    else /* ROM */
    {             
    }

}


/*--------------------------------------------------------------------------*
 * ccpGetCalPage - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint32_t ccpGetCalPage( void )
{
  return (uint32_t)ccpCalPage;
}



/*--------------------------------------------------------------------------*
 * ccpInitCalPage - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ccpInitCalPage( void ) 
{

    unsigned char *p1 = (unsigned char *)CALROM_ADDR;
    unsigned char *p2 = (unsigned char *)CALRAM_ADDR;
    unsigned int i;
    for (i=0;i<0x4000;i++) 
    {
        *p2 = *p1;
        p2++;
        p1++;
    }
}


/*--------------------------------------------------------------------------*
 * CCP_PeriodicMonitorTask100ms - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_PeriodicMonitorTask100ms(void)
{
    int16_T CCPCanStatus;

    ccpDaq(EVENT_CHANNEL_1);
    if (!ccpTxCrmPossible())
    {
        ccpSendCallBack();
    }

    // Diagnosi cella can CCP
#if (CCP_CAN != SPICAN_CH)
    CCPCanStatus = CAN_GetStatus(CCP_CAN);
#endif
    if(CCPCanStatus == CAN_BUSOFF)
    {
        CAN_BusOffRecovery(CCP_CAN);
    } 
}

/*--------------------------------------------------------------------------*
 * CCP_PeriodicMonitorTask10ms - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_PeriodicMonitorTask10ms(void)
{
   ccpDaq(EVENT_CHANNEL_2);
   if (!ccpTxCrmPossible())
   {
      ccpSendCallBack();
   }
}

/*--------------------------------------------------------------------------*
 * CCP_PeriodicMonitorTask5ms - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_PeriodicMonitorTask5ms(void)
{
    ccpDaq(EVENT_CHANNEL_3);
    if (!ccpTxCrmPossible())
    {
        ccpSendCallBack();
    }
}

/*--------------------------------------------------------------------------*
 * CCP_MonitorEventTDC - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_MonitorEventTDC(void)
{
    ccpDaq(EVENT_CHANNEL_4);
    if (!ccpTxCrmPossible())
    {
        ccpSendCallBack();
    }
}

/*--------------------------------------------------------------------------*
 * CCP_MonitorEventHTDC - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_MonitorEventHTDC(void)
{
    ccpDaq(EVENT_CHANNEL_5);
    if (!ccpTxCrmPossible())
    {
        ccpSendCallBack();
    }
}

/*--------------------------------------------------------------------------*
 * CCP_MonitorEventEOA - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_MonitorEventEOA(void)
{
    ccpDaq(EVENT_CHANNEL_6);
    if (!ccpTxCrmPossible())
    {
        ccpSendCallBack();
    }
}
 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /* _BUILD_CCP_ */
