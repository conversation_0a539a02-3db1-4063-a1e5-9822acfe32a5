/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_IDXCTFCTRL_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Bk cutoff [%]
CALQUAL uint16_T BKANGTHCTFSPARE[9] = 
{
 160u, 320u, 480u, 640u, 800u, 960u, 1120u, 1280u, 1440u
};
//Bk cutoff [Rpm]
CALQUAL uint16_T BKRPMCTFSPARE[9] = 
{
   1000u,   2000u,   3000u,   4000u,   5000u,   6000u,   7000u,   8000u,   9000u
};
//selector [flag]
CALQUAL uint8_T ENIDXCUTOFF =  1u;   // 1
//selector [flag]
CALQUAL uint8_T ENIDXSPARECUTOFF =  0u;   // 0
//hyst [%]
CALQUAL uint16_T HYSANGTHCTFSPARE = 48u;   //(  3.0000*16)
//hyst [rpm]
CALQUAL uint16_T HYSRPMCTFSPARE =    200u;   //   200
//rate max [rate]
CALQUAL int16_T MAXRTIDXSPCTF = 32563;   //(15.89990234375*2048)
//rate min [rate]
CALQUAL int16_T MINRTIDXSPCTF = -32563;   //(-15.89990234375*2048)
//idx selector [idx]
CALQUAL uint8_T TBIDXCTFSPARE[9*9] = 
{
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,
    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u,    0u
};
//index player [counter]
CALQUAL uint8_T VTIDXCTFMAXPTR[8] = 
{
  12u,  12u,  12u,  12u,  12u,  12u,  12u,  12u
};
//index player [counter]
CALQUAL uint8_T VTIDXCTFTYPE[9] = 
{
  2u,  2u,  2u,  2u,  2u,  2u,  2u,  2u,  2u
};
//index player [counter]
CALQUAL uint8_T VTIDXCUTOFF1[12] = 
{
  0u,  0u,  0u,  1u,  0u,  0u,  0u,  1u,  0u,  0u,  0u,  1u
};
//index player [counter]
CALQUAL uint8_T VTIDXCUTOFF2[12] = 
{
  0u,  0u,  1u,  0u,  0u,  1u,  0u,  0u,  1u,  0u,  0u,  1u
};
//index player [counter]
CALQUAL uint8_T VTIDXCUTOFF3[12] = 
{
  0u,  1u,  0u,  1u,  0u,  1u,  0u,  1u,  0u,  1u,  0u,  1u
};
//index player [counter]
CALQUAL uint8_T VTIDXCUTOFF4[12] = 
{
  0u,  1u,  1u,  0u,  1u,  1u,  0u,  1u,  1u,  0u,  1u,  1u
};
//index player [counter]
CALQUAL uint8_T VTIDXCUTOFF5[12] = 
{
  0u,  1u,  1u,  1u,  0u,  1u,  1u,  1u,  0u,  1u,  1u,  1u
};
//index player [counter]
CALQUAL uint8_T VTIDXCUTOFF6[12] = 
{
  0u,  1u,  1u,  1u,  1u,  1u,  0u,  1u,  1u,  1u,  1u,  1u
};

#endif /* _BUILD_IDXCTFCTRL_ */

