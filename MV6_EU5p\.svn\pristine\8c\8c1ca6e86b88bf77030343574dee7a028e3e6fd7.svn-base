/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                          $   */
/* $Revision::                                                                                                             $   */
/* $Date::                                                                                                                 $   */
/* $Author::                                                                                                               $   */
/*******************************************************************************************************************************/

#ifdef _BUILD_ANTITAMPERING_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "antitampering.h"


#pragma ghs section rodata=".calib"

/*!
 * \defgroup Calibrations Calibrations
 \brief Parameters that can be tuned via CCP
 
 * \sgroup
 */
/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/

/// Anti-tampering anyway ok
CALQUAL uint8_T  ECUVINLOCK = 1;

/// Anti-tampering strategy enable flag
CALQUAL uint8_T  ENVINSTORAGE = 0u;

/// Anti-tampering strategy blanking time
CALQUAL uint8_T  TIMATINJSTARTEREN = 12;

/*!\egroup*/
/****************************************************************************
 ****************************************************************************/


#endif // _BUILD_ANTITAMPERING_

