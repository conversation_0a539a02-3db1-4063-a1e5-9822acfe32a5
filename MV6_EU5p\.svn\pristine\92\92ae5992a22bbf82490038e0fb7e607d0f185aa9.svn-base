/*
 * File: lambda_mgm.h
 *
 * Code generated for Simulink model 'lambdamgm'.
 *
 * Model version                  : 1.1167
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed May 29 15:36:27 2024
 */

#ifndef RTW_HEADER_lambda_mgm_h_
#define RTW_HEADER_lambda_mgm_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define VLAMINIT                       0U

/* Init state of the lambda sensor automaton */
#define VLAMPOOR                       1U

/* Poor state of the lambda sensor automaton */
#define VLAMRICH                       2U

/* Rich state of the lambda sensor automaton */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint32_T AbsCntTransLam;

/* Abs counter of lam1 sensor transition */
extern uint8_T CatDiagCond;

/* Enable cat functional diagnosis */
extern uint16_T CntLam1Trs;

/* transition counter */
extern uint8_T CntLam2Liveness;

/* lambda2 liveness */
extern uint16_T CntLam2Trs;

/* transition counter */
extern uint16_T CntLam2TrsMax;

/* transition counter max */
extern uint16_T CntLam2TrsMin;

/* transition counter min */
extern uint16_T CntLamFunDiag;

/* Abs counter of lam1 func diag */
extern uint8_T CntLamLiveness;

/* lambda1 liveness */
extern uint16_T CntLamNoDiag;

/* CntLamNoDiag */
extern uint16_T CntLamWatchdog;

/* lambda1 Watchdog */
extern uint16_T CntMinMaxLam2;

/* Lambda2 func counter diagnosis */
extern uint16_T CntOBD2LamL2R;

/* Time lambda1 transition L2R */
extern uint16_T CntOBD2LamL2RMax;

/* Time lambda1 transition L2R max */
extern uint16_T CntOBD2LamL2RMin;

/* Time lambda1 transition L2R min */
extern uint16_T CntOBD2LamR2L;

/* Time lambda1 transition R2L */
extern uint16_T CntOBD2LamR2LMax;

/* Time lambda1 transition R2L max */
extern uint16_T CntOBD2LamR2LMin;

/* Time lambda1 transition R2L min */
extern uint8_T CntOBD2TstL2R;

/* number of diagnosis L2R test */
extern uint8_T CntOBD2TstR2L;

/* number of diagnosis R2L test */
extern uint8_T FlgCatDiagOn;

/* Cat diagnosis flag */
extern uint8_T FlgLam2Ready;

/* Flag to indicate Lam2 closed loop ready */
extern uint8_T FlgLamNotCoherent;

/* Flag to indicate Lam1 not coherent */
extern uint8_T FlgLamReady;

/* Flag to indicate Lam1 closed loop ready */
extern uint8_T FlgO22DiagOn;

/* Flag diagnosis lambda2 on */
extern uint8_T FlgO2DiagOn;

/* Flag diagnosis lambda1 on */
extern uint8_T FlgVeryRichPoor;

/* Flag to indicate very rich-poor state */
extern uint16_T FreqOscLambda;

/* Average lambda1 sensor oscillation frequency */
extern uint32_T IDLambdaMgm;

/* ID Version */
extern uint8_T Lam2DiagCond;

/* Enable lambda2 functional diagnosis */
extern uint8_T LamDiagCond;

/* Enable lambda1 functional diagnosis */
extern uint8_T LamOBD2End;

/* End of lam1 diagnosis */
extern uint16_T LamObjOBD2;

/* Force lambda1 Obj for diagnosis OBD2 */
extern uint32_T PerOscLambda;

/* Lambda1 sensor oscillation period */
extern uint32_T PerOscLambdaMax;

/* Lambda1 sensor oscillation period max */
extern uint32_T PerOscLambdaMin;

/* Lambda1 sensor oscillation period min */
extern uint32_T RatioCatDiag;

/* Ratio cat diagnosis */
extern uint8_T StCatDiag;

/* Cat diagnosis status */
extern uint8_T StLamFuncDiag;

/* Substate for lam1 func diag */
extern uint8_T StLamOBD2DIag;

/* OBD2 Transition lambda1 status */
extern uint16_T ThrVLam2P2R;

/* Poor to rich vlambda2 threshold */
extern uint16_T ThrVLam2R2P;

/* Rich to poor vlambda2 threshold */
extern uint16_T ThrVLamP2R;

/* Poor to rich vlambda1 threshold */
extern uint16_T ThrVLamR2P;

/* Rich to poor vlambda1 threshold */
extern uint16_T VLambda;

/* Lambda voltage */
extern uint16_T VLambda0;

/* Lambda1 voltage */
extern uint16_T VLambda2;

/* Lambda2 voltage */
extern uint16_T VLambda20;

/* Lambda2 voltage */
extern uint16_T VLambdaCrk;

/* Lambda1 voltage corrected during crank */
extern uint8_T VLambdaState;

/* VLambda1 state */
extern uint8_T VLambdaState2;

/* VLambda2 state */
extern uint16_T VMaxLam2;

/* Lambda1 voltage 2 max */
extern uint16_T VMinLam2;

/* Lambda1 voltage 2 min */
#endif                                 /* RTW_HEADER_lambda_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
