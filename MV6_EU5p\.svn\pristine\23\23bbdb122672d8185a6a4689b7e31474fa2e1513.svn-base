/************************************************************************
 *                                                                       *
 *                   Standard Software C90LC Driver for MPC563xM         *
 *                                                                       *
 * FILE NAME     :  FlashResume.c                                         *
 * DATE          :                                                       *
 *                                                                       *
 * AUTHOR        :                                                       *
 * E-mail        :                                                       *
 *                                                                       *
 *************************************************************************/

 /******************************* CHANGES *********************************
 0.0a    01-24-2008  Sindhu R01         Initial Version
 0.1     02-08-2008  <PERSON> He            Updated for final release.
 0.2     03-18-2008  Chen He            Add FlashECCLogicCheck
 1.0     04-10-2008  Sindhu R01         Updated for final Release
 1.1     06-12-2008  <PERSON><PERSON>      Updates to JDP SW template.
 1.2     08-20-2008  A<PERSON><PERSON>     Updated after changes in SSD to support 
                                        MPC5668x.
 1.3     01-05-2009  Sindhu R01         Added Return codes related to FlashECCLogicCheck  
                                        and FlashArrayIntegrityCheck in Section 2.5.
                                        Modified Table 5.
 0.2.0   01-20-2009  Sindhu R01         Added API FactoryMarginReadCheck
 0.3.0   02-13-2009  Arvind Awasthi     Added eccValue parameter to the prototype of 
                                        FlashECCLogicCheck API.
 0.4.0   11-25-2009  Leonardo Colombo   Updated after changes in SSD to support all xPC56xx.
 0.5.0   06-15-2010  Cosimo Stornaiuolo Modified Test_FlashArrayUntegrityCheck.c to 
                                        support EmbAlgo 3.7
                                        Added support for DFO
                                        Tets Suite Updated for xPC56xx.
 *************************************************************************/

/******************************* AKHELA **********************************
 1.0.0  2012.06.06       Mocci A.       C90LC v1.0.1 porting
 *************************************************************************/ 

#ifdef  _BUILD_FLASH_
 
 /*-----------------------------------*
  * INCLUDE FILES
  *-----------------------------------*/
#include    "ssd_c90fl.h"

/*-----------------------------------*
* PUBLIC VARIABLE DEFINITIONS
*-----------------------------------*/
/* C90LC Driver v1.0.1 */
const unsigned long FlashSuspend_C[] = 
{
      0x7C691B78, 0x7C8A2378, 0x7CAB2B78, 0x38E00000, 0x39800000, 0x998A0000, 0x39800000
    , 0x998B0000, 0x80C90000, 0x81060000, 0x710C0014, 0x418200B8, 0x550C06F7, 0x40820014
    , 0x550C077B, 0x41820068, 0x550C07BD, 0x40820060, 0x550C07FF, 0x40820028, 0x550C06F7
    , 0x4182000C, 0x39800001, 0x998A0000, 0x550C077B, 0x41820064, 0x39800002, 0x998A0000
    , 0x48000058, 0x550C06F7, 0x41820014, 0x38E01000, 0x39800008, 0x998A0000, 0x4800005C
    , 0x81860000, 0x618C0002, 0x558C049E, 0x91860000, 0x39800001, 0x998B0000, 0x81060000
    , 0x550C07BD, 0x4182001C, 0x39800005, 0x998A0000, 0x550C0529, 0x4182000C, 0x39800006
    , 0x998A0000, 0x81860000, 0x558C056B, 0x4182FFF8, 0x81860000, 0x558C003C, 0x558C049E
    , 0x91860000, 0x81890024, 0x2C0C0000, 0x41820008, 0x44000002, 0x7CEC3B78, 0x7D836378
    , 0x4E800020
    , 0x30393531, 0x32464646
    
}; /* Total Size = 66 words */

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_t functionSize);


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FlashSuspend - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/    
uint32_t FlashSuspend (PSSD_CONFIG pSSDConfig, uint8_t *suspendState, uint8_t *suspendFlag)
{
    FlashFunctionLoader( (unsigned long*)FlashSuspend_C, sizeof(FlashSuspend_C)/4);
    return ((pFLASHSUSPEND)FlashFunctionPointer)(pSSDConfig, suspendState, suspendFlag);
}
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /*  _BUILD_FLASH_ */
