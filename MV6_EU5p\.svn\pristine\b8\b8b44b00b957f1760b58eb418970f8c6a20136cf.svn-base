/**
 ******************************************************************************
 **  Filename:      GearShiftMgm.h
 **  Date:          28-Feb-2024
 **
 **  Model Version: 1.1792
 ******************************************************************************
 **/

#ifndef RTW_HEADER_GearShiftMgm_h_
#define RTW_HEADER_GearShiftMgm_h_
#ifndef GearShiftMgm_COMMON_INCLUDES_
# define GearShiftMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* GearShiftMgm_COMMON_INCLUDES_ */

#include "GearShiftMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "canmgm.h"
#include "gearshift_mgm.h"
#include "recmgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKCMEGEARSHIFT_dim             7U                        /* Referenced by:
                                                                  * '<S8>/BKCMEGEARSHIFT_dim'
                                                                  * '<S89>/BKCMEGEARSHIFT_dim'
                                                                  */
#define BKFLGQSLOW_dim                 4U                        /* Referenced by:
                                                                  * '<S10>/BKFLGQSLOW_dim'
                                                                  * '<S91>/BKFLGQSLOW_dim'
                                                                  */
#define BKGEARPOS_dim                  6U                        /* Referenced by:
                                                                  * '<S70>/Constant1'
                                                                  * '<S70>/Constant3'
                                                                  * '<S70>/Constant5'
                                                                  * '<S83>/Constant1'
                                                                  * '<S83>/Constant3'
                                                                  * '<S83>/Constant5'
                                                                  * '<S160>/Constant1'
                                                                  * '<S160>/Constant3'
                                                                  * '<S160>/Constant5'
                                                                  * '<S173>/Constant1'
                                                                  * '<S173>/Constant3'
                                                                  * '<S173>/Constant5'
                                                                  * '<S27>/Constant3'
                                                                  * '<S110>/Constant3'
                                                                  */
#define BKRPMGEARSHIFT_dim             7U                        /* Referenced by:
                                                                  * '<S8>/BKRPMGEARSHIFT_dim'
                                                                  * '<S89>/BKRPMGEARSHIFT_dim'
                                                                  */
#define BKRPMQSCTFOFFSET_dim           4U                        /* Referenced by:
                                                                  * '<S8>/BKRPMQSCTFOFFSET_dim'
                                                                  * '<S89>/BKRPMQSCTFOFFSET_dim'
                                                                  */
#define BKRPMQSGASPOS_dim              1U                        /* Referenced by:
                                                                  * '<S8>/BKRPMQSGASPOS_dim'
                                                                  * '<S89>/BKRPMQSGASPOS_dim'
                                                                  */
#define ID_GEARSHIFT_MGM               19783693U                 /* Referenced by: '<S2>/ID_GEARSHIFT_MGM' */

/* mask */
#define QSMAXGEARPOS                   6U                        /* Referenced by:
                                                                  * '<S28>/QSMAXGEARPOS'
                                                                  * '<S112>/QSMAXGEARPOS'
                                                                  */

/* Block signals for system '<S7>/Calc_Ratio' */
typedef struct {
  uint32_T BKCMEGEARSHIFT_dim_ddf5;    /* '<S89>/BKCMEGEARSHIFT_dim' */
  uint32_T BKRPMGEARSHIFT_dim_emnh;    /* '<S89>/BKRPMGEARSHIFT_dim' */
  uint32_T BKRPMQSCTFOFFSET_dim_a3bk;  /* '<S89>/BKRPMQSCTFOFFSET_dim' */
  uint32_T BKRPMQSGASPOS_dim_nyed;     /* '<S89>/BKRPMQSGASPOS_dim' */
  uint16_T PreLookUpIdSearch_S16_o1;   /* '<S97>/PreLookUpIdSearch_S16' */
  uint16_T PreLookUpIdSearch_S16_o1_ol1s;/* '<S98>/PreLookUpIdSearch_S16' */
  uint16_T PreLookUpIdSearch_U16_o1;   /* '<S99>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o1_gdfa;/* '<S100>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o1_n2je;/* '<S101>/PreLookUpIdSearch_U16' */
  uint16_T DataTypeConversion1;        /* '<S97>/Data Type Conversion1' */
  uint16_T DataTypeConversion1_ce1j;   /* '<S98>/Data Type Conversion1' */
  uint16_T DataTypeConversion1_hijm;   /* '<S99>/Data Type Conversion1' */
  uint16_T DataTypeConversion1_c1sk;   /* '<S100>/Data Type Conversion1' */
  uint16_T DataTypeConversion1_jci4;   /* '<S101>/Data Type Conversion1' */
} rtB_Calc_Ratio_GearShiftMgm;

/* Block signals for system '<S110>/Hys_<_x' */
typedef struct {
  uint8_T out;                         /* '<S110>/Hys_<_x' */
} rtB_Hys__x_GearShiftMgm;

/* Block states (default storage) for system '<S110>/Hys_<_x' */
typedef struct {
  struct {
    uint_T is_c3_GearShiftMgm:2;       /* '<S110>/Hys_<_x' */
    uint_T is_active_c3_GearShiftMgm:1;/* '<S110>/Hys_<_x' */
  } bitsForTID0;
} rtDW_Hys__x_GearShiftMgm;

/* Block signals for system '<S112>/Hys_>_x' */
typedef struct {
  uint8_T out;                         /* '<S112>/Hys_>_x' */
} rtB_Hys__x_GearShiftMgm_hazx;

/* Block states (default storage) for system '<S112>/Hys_>_x' */
typedef struct {
  struct {
    uint_T is_c12_GearShiftMgm:2;      /* '<S112>/Hys_>_x' */
    uint_T is_active_c12_GearShiftMgm:1;/* '<S112>/Hys_>_x' */
  } bitsForTID0;
} rtDW_Hys__x_GearShiftMgm_knoo;

/* Block signals for system '<S124>/Calc_KFilt_Ctf' */
typedef struct {
  uint16_T Switch;                     /* '<S126>/Switch' */
} rtB_Calc_KFilt_Ctf_GearShiftMgm;

/* Block signals for system '<S124>/Calc_KFilt_Blp' */
typedef struct {
  uint16_T Switch;                     /* '<S125>/Switch' */
} rtB_Calc_KFilt_Blp_GearShiftMgm;

/* Block signals for system '<S6>/EnQs' */
typedef struct {
  uint8_T DataTypeConversion;          /* '<S24>/Data Type Conversion' */
  uint8_T LogicalOperator1;            /* '<S24>/Logical Operator1' */
  uint8_T LogicalOperator3;            /* '<S28>/Logical Operator3' */
  uint8_T LogicalOperator2;            /* '<S25>/Logical Operator2' */
  uint8_T LogicalOperator2_jub4;       /* '<S27>/Logical Operator2' */
  uint8_T LogicalOperator8;            /* '<S25>/Logical Operator8' */
  rtB_Hys__x_GearShiftMgm_hazx sf_Hys__x_apvf;/* '<S28>/Hys_>_x' */
  rtB_Hys__x_GearShiftMgm sf_Hys__x;   /* '<S27>/Hys_<_x' */
} rtB_EnQs_GearShiftMgm;

/* Block states (default storage) for system '<S6>/EnQs' */
typedef struct {
  uint8_T Memory1_PreviousInput;       /* '<S25>/Memory1' */
  uint8_T Memory2_PreviousInput;       /* '<S25>/Memory2' */
  uint8_T Memory3_PreviousInput;       /* '<S25>/Memory3' */
  uint8_T Memory4_PreviousInput;       /* '<S25>/Memory4' */
  rtDW_Hys__x_GearShiftMgm_knoo sf_Hys__x_apvf;/* '<S28>/Hys_>_x' */
  rtDW_Hys__x_GearShiftMgm sf_Hys__x;  /* '<S27>/Hys_<_x' */
} rtDW_EnQs_GearShiftMgm;

/* Block signals (default storage) */
typedef struct {
  uint16_T RpmQShift_jdme;             /* '<S6>/Chart' */
  uint16_T RpmQShift_avy2;             /* '<S7>/Chart' */
  int16_T CmeQsI_crco;                 /* '<S58>/Merge' */
  int16_T CmeQsP_btqz;                 /* '<S58>/Merge1' */
  int16_T CmeQsRedI;                   /* '<S58>/Merge6' */
  int16_T CmeQsRedP;                   /* '<S58>/Merge7' */
  int16_T CmeDriverQShift_g55o;        /* '<S6>/Chart' */
  int16_T Conversion;                  /* '<S54>/Conversion' */
  int16_T Switch1;                     /* '<S47>/Switch1' */
  int16_T CmeQsI_edjv;                 /* '<S148>/Merge' */
  int16_T CmeQsP_h0dh;                 /* '<S148>/Merge1' */
  int16_T CmeDriverQShift_kcqy;        /* '<S7>/Chart' */
  int16_T Conversion_egs1;             /* '<S144>/Conversion' */
  int16_T Switch1_b2ua;                /* '<S137>/Switch1' */
  uint8_T QsBlpTime;                   /* '<S58>/Merge2' */
  uint8_T QsCtfTime;                   /* '<S58>/Merge3' */
  uint8_T CmeQsIPeriod_gnn3;           /* '<S58>/Merge4' */
  uint8_T QsBlpToTime;                 /* '<S58>/Merge5' */
  uint8_T QSCntDlbTime_a5p1;           /* '<S58>/Merge8' */
  uint8_T QSCntDlbToTime_dn1n;         /* '<S58>/Merge9' */
  uint8_T QuickGearShiftBlpOn_a3xq;    /* '<S6>/Chart' */
  uint8_T QuickGearShiftCtfOn_cwxy;    /* '<S6>/Chart' */
  uint8_T StQShift_h20l;               /* '<S6>/Chart' */
  uint8_T GearPosQShift_p4ss;          /* '<S6>/Chart' */
  uint8_T reset;                       /* '<S6>/Chart' */
  uint8_T QShiftCnt_oz3p;              /* '<S6>/Chart' */
  uint8_T upDown;                      /* '<S6>/Chart' */
  uint8_T cluReset;                    /* '<S6>/Chart' */
  uint8_T cmeGain;                     /* '<S6>/Chart' */
  uint8_T filtGain;                    /* '<S6>/Chart' */
  uint8_T flgDbl;                      /* '<S6>/Chart' */
  uint8_T LogicalOperator1;            /* '<S46>/Logical Operator1' */
  uint8_T LogicalOperator2;            /* '<S48>/Logical Operator2' */
  uint8_T LogicalOperator4;            /* '<S48>/Logical Operator4' */
  uint8_T QsBlpTime_co1y;              /* '<S148>/Merge2' */
  uint8_T QsCtfTime_nkkv;              /* '<S148>/Merge3' */
  uint8_T CmeQsIPeriod_gdc0;           /* '<S148>/Merge4' */
  uint8_T QuickGearShiftBlpOn_gcmm;    /* '<S7>/Chart' */
  uint8_T QuickGearShiftCtfOn_fgyi;    /* '<S7>/Chart' */
  uint8_T StQShift_gx4w;               /* '<S7>/Chart' */
  uint8_T GearPosQShift_l0lg;          /* '<S7>/Chart' */
  uint8_T reset_d5yl;                  /* '<S7>/Chart' */
  uint8_T QShiftCnt_lib2;              /* '<S7>/Chart' */
  uint8_T upDown_nqyh;                 /* '<S7>/Chart' */
  uint8_T DIAG_EXHVALVPOS1;            /* '<S106>/DIAG_EXHVALVPOS1' */
  uint8_T LogicalOperator1_poxk;       /* '<S106>/Logical Operator1' */
  uint8_T LogicalOperator;             /* '<S105>/Logical Operator' */
  uint8_T LogicalOperator1_dent;       /* '<S105>/Logical Operator1' */
  uint8_T LogicalOperator4_dvla;       /* '<S105>/Logical Operator4' */
  uint8_T LogicalOperator2_fmjj;       /* '<S107>/Logical Operator2' */
  uint8_T LogicalOperator3;            /* '<S107>/Logical Operator3' */
  uint8_T LogicalOperator1_k5sx;       /* '<S136>/Logical Operator1' */
  uint8_T LogicalOperator2_hkya;       /* '<S138>/Logical Operator2' */
  uint8_T LogicalOperator4_obph;       /* '<S138>/Logical Operator4' */
  rtB_Calc_KFilt_Blp_GearShiftMgm Calc_KFilt_Blp_jcgws;/* '<S34>/Calc_KFilt_Blp' */
  rtB_Calc_KFilt_Ctf_GearShiftMgm Calc_KFilt_Ctf_jyzjp;/* '<S34>/Calc_KFilt_Ctf' */
  rtB_EnQs_GearShiftMgm EnQs_lgkn;     /* '<S6>/EnQs' */
  rtB_Calc_Ratio_GearShiftMgm Calc_Ratio_lg2t3;/* '<S6>/Calc_Ratio' */
  rtB_Calc_KFilt_Blp_GearShiftMgm Calc_KFilt_Blp;/* '<S124>/Calc_KFilt_Blp' */
  rtB_Calc_KFilt_Ctf_GearShiftMgm Calc_KFilt_Ctf;/* '<S124>/Calc_KFilt_Ctf' */
  rtB_Hys__x_GearShiftMgm_hazx sf_Hys__x_iuw3;/* '<S112>/Hys_>_x' */
  rtB_Hys__x_GearShiftMgm sf_Hys__x;   /* '<S110>/Hys_<_x' */
  rtB_Calc_Ratio_GearShiftMgm Calc_Ratio;/* '<S7>/Calc_Ratio' */
} BlockIO_GearShiftMgm;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  struct {
    uint_T is_c1_GearShiftMgm:4;       /* '<S6>/Chart' */
    uint_T is_c2_GearShiftMgm:3;       /* '<S7>/Chart' */
    uint_T is_active_c1_GearShiftMgm:1;/* '<S6>/Chart' */
    uint_T is_active_c2_GearShiftMgm:1;/* '<S7>/Chart' */
  } bitsForTID0;

  uint8_T ctfPeriod;                   /* '<S6>/Chart' */
  uint8_T flgGearCL;                   /* '<S6>/Chart' */
  uint8_T qsCntTo;                     /* '<S6>/Chart' */
  uint8_T flgJustDbl;                  /* '<S6>/Chart' */
  uint8_T ctfPeriod_csmp;              /* '<S7>/Chart' */
  uint8_T Memory2_PreviousInput;       /* '<S107>/Memory2' */
  uint8_T Memory3_PreviousInput;       /* '<S107>/Memory3' */
  rtDW_EnQs_GearShiftMgm EnQs_lgkn;    /* '<S6>/EnQs' */
  rtDW_Hys__x_GearShiftMgm_knoo sf_Hys__x_iuw3;/* '<S112>/Hys_>_x' */
  rtDW_Hys__x_GearShiftMgm sf_Hys__x;  /* '<S110>/Hys_<_x' */
} D_Work_GearShiftMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_GearShiftMgm_Init_Trig_ZCE[2];/* '<S1>/fc_GearShiftMgm_Init' */
  ZCSigState fc_GearShiftMgm_Calc_Trig_ZCE;/* '<S1>/fc_GearShiftMgm_Calc' */
} PrevZCSigStates_GearShiftMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
} ExternalInputs_GearShiftMgm;

/* Block signals (default storage) */
extern BlockIO_GearShiftMgm GearShiftMgm_B;

/* Block states (default storage) */
extern D_Work_GearShiftMgm GearShiftMgm_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_GearShiftMgm GearShiftMgm_U;

/* Model entry point functions */
extern void GearShiftMgm_initialize(void);
extern void GearShiftMgm_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('GearShiftMgm_gen/BOX/GearShiftMgm')    - opens subsystem GearShiftMgm_gen/BOX/GearShiftMgm
 * hilite_system('GearShiftMgm_gen/BOX/GearShiftMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'GearShiftMgm_gen/BOX'
 * '<S1>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm'
 * '<S2>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/Init'
 * '<S3>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms'
 * '<S4>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/fc_GearShiftMgm_Calc'
 * '<S5>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/fc_GearShiftMgm_Init'
 * '<S6>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog'
 * '<S7>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital'
 * '<S8>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Calc_Ratio'
 * '<S9>'   : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Chart'
 * '<S10>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Clac_FlgQSLow'
 * '<S11>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs'
 * '<S12>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt'
 * '<S13>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Output'
 * '<S14>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables'
 * '<S15>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters'
 * '<S16>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Calc_Ratio/PreLookUpIdSearch_S16_1'
 * '<S17>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Calc_Ratio/PreLookUpIdSearch_S16_2'
 * '<S18>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Calc_Ratio/PreLookUpIdSearch_U16_1'
 * '<S19>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Calc_Ratio/PreLookUpIdSearch_U16_2'
 * '<S20>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Calc_Ratio/PreLookUpIdSearch_U16_3'
 * '<S21>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Clac_FlgQSLow/LookUp_S16_U16_1'
 * '<S22>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Clac_FlgQSLow/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S23>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc'
 * '<S24>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSFlgEn_Calc'
 * '<S25>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSGearSignals_Calc'
 * '<S26>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc'
 * '<S27>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down'
 * '<S28>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up'
 * '<S29>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Compare To Zero'
 * '<S30>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Hys_<_x'
 * '<S31>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Look2D_IR_U16_1'
 * '<S32>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Look2D_IR_U16_1/Data Type Conversion Inherited1'
 * '<S33>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Hys_>_x'
 * '<S34>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt'
 * '<S35>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Blp'
 * '<S36>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Ctf'
 * '<S37>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_3'
 * '<S38>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_4'
 * '<S39>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_3/Data Type Conversion Inherited3'
 * '<S40>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_4/Data Type Conversion Inherited3'
 * '<S41>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_1'
 * '<S42>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_2'
 * '<S43>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S44>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_2/Data Type Conversion Inherited3'
 * '<S45>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl'
 * '<S46>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/Calc_CtfGearShift'
 * '<S47>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc'
 * '<S48>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/QSOut_Calc'
 * '<S49>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt'
 * '<S50>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt'
 * '<S51>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP_1'
 * '<S52>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP_2'
 * '<S53>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP_1/Data Type Conversion Inherited1'
 * '<S54>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP_2/Data Type Conversion Inherited1'
 * '<S55>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt/Compare To Constant'
 * '<S56>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt/FOF_Reset_S16_FXP_1'
 * '<S57>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt/FOF_Reset_S16_FXP_1/Data Type Conversion Inherited1'
 * '<S58>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/Merger'
 * '<S59>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc'
 * '<S60>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc'
 * '<S61>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc'
 * '<S62>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc'
 * '<S63>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme'
 * '<S64>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Time'
 * '<S65>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S1'
 * '<S66>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S2'
 * '<S67>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S68>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S69>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Cme'
 * '<S70>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time'
 * '<S71>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Cme/LookUp_IR_S16_1'
 * '<S72>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Cme/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S73>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time/Look2D_IR_S8_1'
 * '<S74>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time/Look2D_IR_U8_1'
 * '<S75>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time/Look2D_IR_U8_2'
 * '<S76>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme'
 * '<S77>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Time'
 * '<S78>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S16_1'
 * '<S79>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S16_2'
 * '<S80>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S81>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S16_2/Data Type Conversion Inherited3'
 * '<S82>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Cme'
 * '<S83>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time'
 * '<S84>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Cme/LookUp_IR_S16_1'
 * '<S85>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Cme/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S86>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time/Look2D_IR_S8_1'
 * '<S87>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time/Look2D_IR_U8_1'
 * '<S88>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Analog/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time/Look2D_IR_U8_2'
 * '<S89>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Calc_Ratio'
 * '<S90>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Chart'
 * '<S91>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Clac_FlgQSLow'
 * '<S92>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs'
 * '<S93>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt'
 * '<S94>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Output'
 * '<S95>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables'
 * '<S96>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters'
 * '<S97>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Calc_Ratio/PreLookUpIdSearch_S16_1'
 * '<S98>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Calc_Ratio/PreLookUpIdSearch_S16_2'
 * '<S99>'  : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Calc_Ratio/PreLookUpIdSearch_U16_1'
 * '<S100>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Calc_Ratio/PreLookUpIdSearch_U16_2'
 * '<S101>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Calc_Ratio/PreLookUpIdSearch_U16_3'
 * '<S102>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Clac_FlgQSLow/LookUp_S16_U16_1'
 * '<S103>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Clac_FlgQSLow/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S104>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc'
 * '<S105>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/Calc_EnQSByCAN'
 * '<S106>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSFlgEn_Calc'
 * '<S107>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSGearSignals_Calc'
 * '<S108>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc'
 * '<S109>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/Calc_EnQSByCAN/Compare To Constant2'
 * '<S110>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down'
 * '<S111>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Neutral'
 * '<S112>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up'
 * '<S113>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Compare To Zero'
 * '<S114>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Hys_<_x'
 * '<S115>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Look2D_IR_U16_1'
 * '<S116>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Down/Look2D_IR_U16_1/Data Type Conversion Inherited1'
 * '<S117>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Neutral/Compare To Zero'
 * '<S118>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Neutral/Compare To Zero1'
 * '<S119>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Neutral/Compare To Zero2'
 * '<S120>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Neutral/Compare To Zero3'
 * '<S121>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Neutral/Compare To Zero4'
 * '<S122>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Compare To Zero'
 * '<S123>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/EnQs/FlgEnQShift_Calc/QSUpDnEnFlg_Calc/En_Up/Hys_>_x'
 * '<S124>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt'
 * '<S125>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Blp'
 * '<S126>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Ctf'
 * '<S127>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_3'
 * '<S128>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_4'
 * '<S129>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_3/Data Type Conversion Inherited3'
 * '<S130>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Blp/LookUp_IR_U16_4/Data Type Conversion Inherited3'
 * '<S131>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_1'
 * '<S132>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_2'
 * '<S133>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S134>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/KFilt/Calc_KFilt/Calc_KFilt_Ctf/LookUp_IR_U16_2/Data Type Conversion Inherited3'
 * '<S135>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl'
 * '<S136>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/Calc_CtfGearShift'
 * '<S137>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc'
 * '<S138>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/QSOut_Calc'
 * '<S139>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt'
 * '<S140>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt'
 * '<S141>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP1'
 * '<S142>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP2'
 * '<S143>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP1/Data Type Conversion Inherited1'
 * '<S144>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Blp_Filt/FOF_Reset_S16_FXP2/Data Type Conversion Inherited1'
 * '<S145>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt/Compare To Constant'
 * '<S146>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt/FOF_Reset_S16_FXP'
 * '<S147>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/ReadLookUpTables/Ctrl/CmeQs_Calc/Ctf_Filt/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S148>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/Merger'
 * '<S149>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc'
 * '<S150>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc'
 * '<S151>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc'
 * '<S152>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc'
 * '<S153>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme'
 * '<S154>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Time'
 * '<S155>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S1'
 * '<S156>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S2'
 * '<S157>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S158>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnBlp_Calc/Calc_Cme/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S159>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Cme'
 * '<S160>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time'
 * '<S161>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Cme/LookUp_IR_S16_1'
 * '<S162>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Cme/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S163>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time/Look2D_IR_S8_1'
 * '<S164>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time/Look2D_IR_U8_1'
 * '<S165>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsDnCtf_Calc/Calc_Time/Look2D_IR_U8_2'
 * '<S166>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme'
 * '<S167>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Time'
 * '<S168>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S1'
 * '<S169>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S2'
 * '<S170>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S171>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpBlp_Calc/Calc_Cme/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S172>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Cme'
 * '<S173>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time'
 * '<S174>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Cme/LookUp_IR_S16_1'
 * '<S175>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Cme/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S176>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time/Look2D_IR_S8_1'
 * '<S177>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time/Look2D_IR_U8_1'
 * '<S178>' : 'GearShiftMgm_gen/BOX/GearShiftMgm/T10ms/Digital/Schedule_Parameters/fc_QsUpCtf_Calc/Calc_Time/Look2D_IR_U8_2'
 */

/*-
 * Requirements for '<Root>': GearShiftMgm
 */
#endif                                 /* RTW_HEADER_GearShiftMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
