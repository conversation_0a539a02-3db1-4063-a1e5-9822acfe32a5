/*  File    : airdiagmgm.h
 *  Author  : <PERSON><PERSON>
 *  Date    : 30-Mar-2007 15:15:07
 *  Revision: AirDiagMgm 1.1
 *  Note    : first version
 * 
 *  Copyright 2007 Eldor Corporation
 */

#ifndef RTW_HEADER_airdiag_mgm_h_
#define RTW_HEADER_airdiag_mgm_h_

extern int16_T AvgEnerLam;             /* Mean energy of the lambda control */
extern uint8_T FlgPresCoh1;            /* Coherence test result for throttle 1 */
extern uint8_T FlgPresCoh2;            /* Coherence test result for throttle 2 */

/* public functions */
extern void AirDiagMgm_Init(void);
extern void AirDiagMgm_NoSync(void);
extern void AirDiagMgm_TDC(void);

#endif                                 /* RTW_HEADER_airdiag_mgm_h_ */
