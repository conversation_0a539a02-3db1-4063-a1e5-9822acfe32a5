/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_GASPOSMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Breakpoints for Gaspos time history for the EOL [s]
CALQUAL uint16_T BKTIMHISTGAS[24] = 
{
 0u, 2u, 3u, 5u, 6u, 8u, 10u, 11u, 13u, 14u, 16u, 18u, 19u, 21u, 22u, 24u, 26u, 27u, 29u, 30u, 32u, 34u, 35u, 37u
};
//Configure DBW (=0 no dbw; =1 dbw) [flag]
CALQUAL uint8_T DBW =  1u;   // 1
//Default master sensor (1 or 2) [flag]
CALQUAL uint8_T DEFMASTSENS =  2u;   // 2
//Enable Stop creeping flag [flag]
CALQUAL uint8_T ENSTOPCREEPING =  0u;   // 0
//Enable the gas pos learning (=1) [flag]
CALQUAL uint8_T FLGENGPOSAD =  0u;   // 0
//Force GasPos value [%]
CALQUAL int16_T FORCEGASPOS = -160;   //(-10.0000*16)
//Force used gas sensor:  0=no gas forced; 1=GasPos1; 2=GasPos2 [flag]
CALQUAL uint8_T FORCEGASSENS =  0u;   // 0
//Force GasPos time history [%]
CALQUAL int16_T FORCEGASTIMEHIST =  0;   // 0
//Negative gas threshold [%]
CALQUAL int16_T GASNEGPERC = -32;   //( -2.0000*16)
//(SR) Gain of the acc pos characteristic [%/mV]
CALQUAL int16_T GASPOSGAIN[2] = 
{
 -417, 417
};
//(SR) Basic offset of the acc pos characteristic [mV]
CALQUAL int16_T GASPOSOFFSET[2] = 
{
 911, 113
};
//Enable negative gas flag [flag]
CALQUAL uint8_T GPNEGWITHIDLE =  0u;   // 0
//Dead band for the sensor inv characteristic [mV]
CALQUAL uint16_T GPOSDBAND = 10u;   //(  48.8281250*0.2048)
//Step to slew GasPos to traget when rec is active [%]
CALQUAL uint16_T GPOSSLEW2TRG = 3u;   //(0.01171875*256)
//Step to slew GasPos to 0 when rec is active [%]
CALQUAL uint16_T GPOSSLEW2ZERO = 5u;   //(0.3125*16)
//Max offset of the acc pos characteristic [mV]
CALQUAL int16_T MAXGASPOSOFF = 113;   //(  551.7578125*0.2048)
//Min offset of the acc pos characteristic [mV]
CALQUAL int16_T MINGASPOSOFF = 92;   //(  449.2187500*0.2048)
//(SR) Threshold for coherence gas pos diag [%]
CALQUAL int16_T SATMAXPERCGAS = 3200;   //(100.00000*32)
//Adaptive step to decrement GasPosOffsetAd [mV]
CALQUAL uint16_T STDECGPOSAD = 2u;   //(   9.7656250*0.2048)
//Adaptive step to increment GasPosOffsetAd [mV]
CALQUAL uint16_T STINCGPOSAD = 1u;   //(   4.8828125*0.2048)
//Numb of 100ms events to confirm gas pos under thr [events]
CALQUAL uint16_T TGASUNDERTH =     50u;   //    50
//(SR) Threshold for coherence gas pos diag [%]
CALQUAL int16_T THGASPOSCOH = 80;   //(  5.0000*16)
//(SR) Threshold for incoherence gas pos diag [%]
CALQUAL int16_T THGASPOSNOTCOH = 88;   //(  5.5000*16)
//(SR) Lower 2nd threshold for electric gas switch diag [mV]
CALQUAL uint16_T THLOW1GASSWDIA = 51u;   //( 249.0234375*0.2048)
//(SR) Lower 2nd threshold for electric gas switch diag [mV]
CALQUAL uint16_T THLOW2GASSWDIA = 307u;   //(1499.0234375*0.2048)
//(SR) Lower threshold for electric gas pos diag [mV]
CALQUAL uint16_T THLOWGASPOSDIA = 31u;   //( 151.3671875*0.2048)
//Numb of 10ms event with gas pos over thr to adapt [counter]
CALQUAL uint32_T THRGASUNCH =        1000u;   //       1000
//Numb of times that gas pos under thr to adapt [counter]
CALQUAL uint8_T THRGASUNDERTH =    3u;   //   3
//(SR) Lower 2nd threshold for electric gas switch diag [mV]
CALQUAL uint16_T THUPP1GASSWDIA = 717u;   //(3500.9765625*0.2048)
//(SR) Lower 2nd threshold for electric gas switch diag [mV]
CALQUAL uint16_T THUPP2GASSWDIA = 973u;   //(4750.9765625*0.2048)
//(SR) Upper threshold for electric gas pos diag [mV]
CALQUAL uint16_T THUPPGASPOSDIA = 973u;   //(4750.9765625*0.2048)
//(SR) VGasPos1 thr for idle switch coeherence [mV]
CALQUAL uint16_T THVGASIDLE1 = 917u;   //(4477.5390625*0.2048)
//(SR) VGasPos2 thr for idle switch coeherence [mV]
CALQUAL uint16_T THVGASIDLE2 = 105u;   //( 512.6953125*0.2048)
//(SR) VGasPos1 thr for idle switch coeherence [mV]
CALQUAL uint16_T THVGASOUTIDLE1 = 798u;   //(3896.4843750*0.2048)
//(SR) VGasPos2 thr for idle switch coeherence [mV]
CALQUAL uint16_T THVGASOUTIDLE2 = 226u;   //(1103.5156250*0.2048)
//Gaspos time history for the EOL [%]
CALQUAL uint8_T VTTIMHISTGAS[24] = 
{
 0u, 10u, 20u, 30u, 40u, 50u, 60u, 70u, 80u, 90u, 100u, 110u, 120u, 130u, 140u, 150u, 160u, 170u, 180u, 190u, 200u, 180u, 160u, 20u
};

#endif /* _BUILD_GASPOSMGM_ */

