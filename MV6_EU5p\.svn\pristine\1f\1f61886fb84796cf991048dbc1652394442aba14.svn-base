/*
 * File: RpmLimiter_data.c
 *
 * Code generated for Simulink model 'RpmLimiter'.
 *
 * Model version                  : 1.2476
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar  1 10:17:25 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#include "RpmLimiter.h"
#include "RpmLimiter_private.h"

/* Invariant block signals (default storage) */
const ConstBlockIO_RpmLimiter RpmLimiter_ConstB = {
  { 0, 1, 2 },                         /* '<S54>/Data Type Conversion' */

  { 0, 1, 2, 3, 4, 5, 6 },             /* '<S54>/Data Type Conversion2' */

  { 0, 1, 2 }                          /* '<S32>/Data Type Conversion' */
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
