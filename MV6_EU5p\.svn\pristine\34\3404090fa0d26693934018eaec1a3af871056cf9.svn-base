/*
 * File: VSpeedCtrl_private.h
 *
 * Real-Time Workshop code generated for Simulink model VSpeedCtrl.
 *
 * Model version                        : 1.371
 * Real-Time Workshop file version      : 6.6  (R2007a)  01-Feb-2007
 * Real-Time Workshop file generated on : Mon Oct 01 11:42:13 2007
 * TLC version                          : 6.6 (Jan 16 2007)
 * C source code generated on           : Mon Oct 01 11:42:13 2007
 */

#ifndef _RTW_HEADER_VSpeedCtrl_private_h_
#define _RTW_HEADER_VSpeedCtrl_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "trqext_req.h"
#include "recmgm.h"
#include "vspeed_mgm.h"
/* Nella prossima generazione di codice aggiungere #include "PTrain_Diag.h" per il nuovo modulo. */
#include "PTrain_Diag.h"
#include "canmgm.h"
#  include "rtlibsrc.h"
#define CALL_EVENT                     (MAX_uint8_T)
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks.
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Computed Parameter: Gain
 * '<S5>/Gain'
 */
#define rtcP_Gain_Gain                 (-32768)

/* Imported (extern) block parameters */
extern int16_T DVSPEEDOUTLIMIT;        /* Variable: DVSPEEDOUTLIMIT
                                        * '<S4>/DVSPEEDOUTLIMIT'
                                        * vehicle speed err threshold to enable cutoff for vehicle speed limiter
                                        */
extern int16_T VSPEEDLIMERRCTF;        /* Variable: VSPEEDLIMERRCTF
                                        * '<S5>/VSPEEDLIMERRCTF'
                                        * vehicle speed err threshold to enable cutoff for vehicle speed limiter
                                        */
extern int16_T VSPEEDLIMERRMAX;        /* Variable: VSPEEDLIMERRMAX
                                        * '<S5>/VSPEEDLIMERRMAX'
                                        * Max vehicle speed err for vehicle speed limiter
                                        */
extern uint16_T VSPEEDLIMINTGAIN;      /* Variable: VSPEEDLIMINTGAIN
                                        * '<S6>/VSPEEDLIMINTGAIN'
                                        * Integral gain for vehicle speed limiter
                                        */
extern uint16_T VSPEEDLIMPROPGAIN;     /* Variable: VSPEEDLIMPROPGAIN
                                        * '<S6>/VSPEEDLIMPROPGAIN'
                                        * Proportional gain for vehicle speed limiter
                                        */
extern uint8_T ENVSPEEDCTRL;           /* Variable: ENVSPEEDCTRL
                                        * '<S4>/ENVSPEEDCTRL'
                                        * Activate the vehicle speed limiter (=1)
                                        */
void VSpeedCtrl_f_10ms(void);
void VSpeedCtrl_f_Reset(void);
void VSpeedCtrl_step(void);
void VSpeedCtrl_initialize(boolean_T firstTime);
void VSpeedCtrl_Init(void);
void VSpeedCtrl_NoSync(void);
void VSpeedCtrl_T10ms(void);

#endif                                 /* _RTW_HEADER_VSpeedCtrl_private_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
