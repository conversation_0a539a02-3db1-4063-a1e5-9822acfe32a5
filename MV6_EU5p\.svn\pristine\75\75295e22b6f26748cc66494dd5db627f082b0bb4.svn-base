/*
 * File: ExhValMgm.c
 *
 * Code generated for Simulink model 'ExhValMgm'.
 *
 * Model version                  : 1.1699
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Aug  9 12:27:20 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (28), Warnings (4), Error (0)
 */

#ifdef _BUILD_EXHVALMGM_SBS_

#include "ExhValMgm_SBS.h"
#include "ExhValMgm_SBS_private.h"

/* Named constants for Chart: '<S13>/Chart' */
#define ExhValMgm_IN_NO_ACTIVE_CHILD   ((uint8_T)0U)
#define ExhValMgm_IN_POS_ACT           ((uint8_T)1U)
#define ExhValMgm_IN_POS_TRIG          ((uint8_T)2U)
#define ExhValMgm_IN_SLF_ACT           ((uint8_T)3U)
#define ExhValMgm_IN_SLF_DONE          ((uint8_T)4U)
#define ExhValMgm_IN_SLF_IDLE          ((uint8_T)5U)
#define ExhValMgm_IN_SLF_MISSED        ((uint8_T)6U)
#define ExhValMgm_IN_SLF_TRIG          ((uint8_T)7U)

/* Named constants for Chart: '<S8>/PbyRateSel' */
#define ExhValMgm_IN_NORMAL_RATE       ((uint8_T)1U)
#define ExhValMgm_IN_PBY_ACTION_RATE   ((uint8_T)2U)
#define ExhValMgm_IN_PBY_WAIT_EXIT_RATE ((uint8_T)3U)

/* Named constants for Chart: '<S9>/Calc_FlgExhVDiagOn' */
#define ExhValMgm_IN_DIAG_ON           ((uint8_T)1U)
#define ExhValMgm_IN_RESET             ((uint8_T)2U)

/* Named constants for Chart: '<S9>/ExhV_Diag_Func' */
#define ExhValMgm_IN_DIAG              ((uint8_T)1U)
#define ExhValMgm_IN_INIT              ((uint8_T)2U)
#define ExhValMgm_IN_SEARCHING         ((uint8_T)3U)
#define ExhValMgm_IN_STAB              ((uint8_T)4U)

/* Named constants for Chart: '<S51>/Calc_StFoExVSL' */
#define ExhValMgm_IN_EXV_MANUAL_IDLE   ((uint8_T)1U)
#define ExhValMgm_IN_EXV_MANUAL_POS    ((uint8_T)2U)
#define ExhValMgm_IN_EXV_MANUAL_SELF   ((uint8_T)3U)

/* user code (top of source file) */
/* System '<Root>' */

/* Block signals and states (default storage) */
DW_ExhValMgm_T ExhValMgm_DW;

/* External outputs (root outports fed by signals with default storage) */
ExtY_ExhValMgm_T ExhValMgm_Y;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T AngExhTrg;

/* Exh. valve angle target filtered */
int16_T AngExhTrg0;

/* Exh. valve angle target */
uint8_T CntExhVMgmSelf;

/* Self armed */
uint8_T CntReqExhSelf;

/* ExhV Self trigger counter */
uint8_T EnExhVSelfTrg;

/* Self trigger */
uint8_T ExhVPwmPlaStab;

/* Plausibility stability */
uint32_T ExhValModuleTime;

/* Module timing counter */
uint16_T ExhvalRelTime;

/* Time-history relative time */
uint8_T FlgExVPWLamp;

/* WLamp Pos or Self completed */
uint8_T FlgExhVDiagOn;

/* Diagnosis flag confirmed */
uint8_T FlgExhVZeroPos;

/* flag of Active Diag to search zero position */
uint32_T IDExhValMgm;

/* ID Version */

/* Output and update for function-call system: '<S1>/Init' */
void ExhValMgm_Init(void)
{
  {
    /* user code (Output function Header for TID1) */

    /* System '<S1>/Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    ExhValMgm_initialize();

    /* Constant: '<S3>/ZERO1' */
    AngExhTrg = 0;

    /* SignalConversion generated from: '<S3>/FlgExVPWLamp' incorporates:
     *  Constant: '<S3>/ZERO2'
     */
    FlgExVPWLamp = ((uint8_T)0U);

    /* SignalConversion generated from: '<S3>/FlgFbExhVZeroPos' incorporates:
     *  Constant: '<S3>/ZERO2'
     */
    FlgExhVZeroPos = ((uint8_T)0U);

    /* Constant: '<S3>/ID_EXHVALVE_MGM' */
    IDExhValMgm = ID_EXHVALVE_MGM;

    /* user code (Output function Trailer for TID1) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Output and update for function-call system: '<S9>/Diag_Stab' */
void ExhValMgm_Diag_Stab(uint16_T rtu_THREXVDOUTSTAB, uint16_T
  rtu_TIMEXVDOUTSTAB, uint16_T rtu_ExVDutyOut, uint8_T rtu_reset,
  DW_Diag_Stab_ExhValMgm_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint8_T rtb_SigStab_o2;
  uint8_T rtb_Conversion5;
  uint16_T rtb_Conversion1_fr1;
  uint16_T rtb_Conversion2_g2i;
  uint16_T rtb_Conversion3_ppu;
  uint8_T rtb_Conversion4_fz5;
  uint16_T rtb_Memory_gpp;
  uint16_T rtb_Memory1_mjm;

  /* Memory: '<S41>/Memory' */
  rtb_Conversion5 = localDW->Memory_PreviousInput_mvd;

  /* DataTypeConversion: '<S45>/Conversion1' */
  rtb_Conversion1_fr1 = rtu_THREXVDOUTSTAB;

  /* DataTypeConversion: '<S45>/Conversion2' */
  rtb_Conversion2_g2i = rtu_ExVDutyOut;

  /* DataTypeConversion: '<S45>/Conversion3' */
  rtb_Conversion3_ppu = rtu_TIMEXVDOUTSTAB;

  /* DataTypeConversion: '<S45>/Conversion4' */
  rtb_Conversion4_fz5 = rtu_reset;

  /* Memory: '<S45>/Memory' */
  rtb_Memory_gpp = localDW->Memory_PreviousInput;

  /* Memory: '<S45>/Memory1' */
  rtb_Memory1_mjm = localDW->Memory1_PreviousInput;

  /* S-Function (SigStab): '<S45>/SigStab' */
  SigStab( (&(ExhVPwmPlaStab)), &rtb_SigStab_o2, &rtb_SigStab_o3,
          &rtb_SigStab_o4, rtb_Conversion2_g2i, rtb_Conversion4_fz5,
          rtb_Conversion1_fr1, rtb_Conversion3_ppu, rtb_Conversion5,
          rtb_Memory1_mjm, rtb_Memory_gpp);

  /* Update for Memory: '<S41>/Memory' */
  localDW->Memory_PreviousInput_mvd = rtb_SigStab_o2;

  /* Update for Memory: '<S45>/Memory' */
  localDW->Memory_PreviousInput = rtb_SigStab_o4;

  /* Update for Memory: '<S45>/Memory1' */
  localDW->Memory1_PreviousInput = rtb_SigStab_o3;
}

/* Output and update for function-call system: '<S9>/fc_diag_calc' */
void ExhValMgm_fc_diag_calc(uint8_T rtu_ptfault, uint8_T *rty_stDiag, uint8_T
  *rty_cntDiagCall, DW_fc_diag_calc_ExhValMgm_T *localDW)
{
  /* S-Function (DiagMgm_SetDiagState): '<S48>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S44>/DIAG_EXHVALVPOS'
   */
  DiagMgm_SetDiagState( DIAG_EXHVALVPOS, rtu_ptfault, rty_stDiag);

  /* Sum: '<S44>/Add' incorporates:
   *  Constant: '<S44>/Constant'
   *  Memory: '<S44>/Memory'
   */
  *rty_cntDiagCall = (uint8_T)(((uint32_T)((uint8_T)1U)) + ((uint32_T)
    localDW->Memory_PreviousInput));

  /* Update for Memory: '<S44>/Memory' */
  localDW->Memory_PreviousInput = *rty_cntDiagCall;
}

/* System initialize for function-call system: '<S1>/T10ms' */
void ExhValMgm_T10ms_Init(void)
{
  uint8_T reset;

  /* SystemInitialize for Chart: '<S13>/Chart' */
  ExhValMgm_DW.is_active_c1_ExhValMgm = 0U;
  ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_NO_ACTIVE_CHILD;
  ExhValMgm_DW.oldCntReqExhSelf = 0U;
  ExhValMgm_DW.cnt = 0U;
  ExhValMgm_DW.FlgExVSelfWLamp = 0U;
  CntExhVMgmSelf = 0U;
  ExhValMgm_DW.FlgExhVZeroPos_jtz = 0U;

  /* SystemInitialize for Chart: '<S8>/PbyRateSel' */
  ExhValMgm_DW.is_active_c7_ExhValMgm = 0U;
  ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_NO_ACTIVE_CHILD;
  ExhValMgm_DW.flgPbyRate = 0U;
  ExhValMgm_DW.flgZeroTarget = 0U;

  /* SystemInitialize for Chart: '<S9>/Calc_FlgExhVDiagOn' */
  ExhValMgm_DW.is_active_c9_ExhValMgm = 0U;
  ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_NO_ACTIVE_CHILD;
  ExhValMgm_DW.oldCntDiagCall = 0U;
  ExhValMgm_DW.oldDrivingCycle = 0U;
  FlgExhVDiagOn = 0U;

  /* SystemInitialize for Chart: '<S9>/ExhV_Diag_Func' */
  ExhValMgm_DW.is_active_c3_ExhValMgm = 0U;
  ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_NO_ACTIVE_CHILD;
  ExhValMgm_DW.oldExVDutyOut = 0U;
  ExhValMgm_DW.diff = 0U;
  reset = 0U;
  ExhValMgm_DW.ptFault = 0U;

  /* SystemInitialize for IfAction SubSystem: '<S49>/IF' */
  /* SystemInitialize for Chart: '<S51>/Calc_StFoExVSL' */
  ExhValMgm_DW.is_active_c8_ExhValMgm = 0U;
  ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_NO_ACTIVE_CHILD;
  ExhValMgm_DW.FlgExVManPos = 0U;
  ExhValMgm_DW.FlgExVManSelf = 0U;

  /* End of SystemInitialize for SubSystem: '<S49>/IF' */
}

/* Output and update for function-call system: '<S1>/T10ms' */
void ExhValMgm_T10ms(void)
{
  /* local block i/o variables */
  int16_T rtb_Conversion3;
  boolean_T rtb_Compare_cdg;
  uint8_T rtb_Merge1;
  uint16_T rtb_Switch_kvh;
  boolean_T rtb_LogicalOperator_mnd;
  int16_T rtb_Conversion_o0d;
  int16_T rtb_Conversion_fe5;
  int16_T rtb_Switch2;
  uint8_T reset;
  uint8_T rtb_Conversion6;
  uint16_T rtb_Conversion[4];
  int16_T rtb_Conversion1[4];
  uint16_T rtb_Conversion2;
  int16_T rtb_Conversion4[5];
  uint16_T rtb_Conversion_h03[6];
  uint8_T rtb_Conversion3_fgz;
  int16_T rtb_Reshape[30];
  int16_T rtb_Conversion1_cfg[6];
  int16_T rtb_Conversion2_njg;
  int16_T rtb_Conversion1_kfn;
  int16_T rtb_RateLimiter_S16;
  int32_T i;
  uint32_T q0;
  uint32_T qY;

  /* RelationalOperator: '<S15>/Compare' incorporates:
   *  Constant: '<S12>/FOEXHVSELF'
   *  Constant: '<S15>/Constant'
   */
  rtb_Compare_cdg = (FOEXHVSELF != ((uint8_T)0U));

  /* Memory: '<S12>/Memory1' */
  rtb_Conversion6 = ExhValMgm_DW.Memory1_PreviousInput_pyc;

  /* Sum: '<S12>/Add' incorporates:
   *  Logic: '<S12>/Logical Operator'
   *  Logic: '<S12>/Logical Operator1'
   *  Memory: '<S12>/Memory'
   */
  CntReqExhSelf = (uint8_T)(((uint32_T)(((ExhValMgm_DW.Memory_PreviousInput_jyx
    != rtb_Compare_cdg) && rtb_Compare_cdg) ? 1 : 0)) + ((uint32_T)
    rtb_Conversion6));

  /* If: '<S49>/If' incorporates:
   *  Inport: '<Root>/FlgVehStop'
   */
  if (((int32_T)FlgVehStop) != 0) {
    /* Outputs for IfAction SubSystem: '<S49>/IF' incorporates:
     *  ActionPort: '<S51>/Action Port'
     */
    /* Switch: '<S54>/Switch' incorporates:
     *  Constant: '<S54>/Constant'
     *  Inport: '<Root>/StartSignal'
     *  Sum: '<S54>/Add'
     */
    if (((int32_T)StartSignal) != 0) {
      /* Sum: '<S54>/Add' incorporates:
       *  Memory: '<S54>/Memory'
       */
      qY = ((uint32_T)StartSignal) + ((uint32_T)
        ExhValMgm_DW.Memory_PreviousInput_pjn);
      if (qY > 65535U) {
        qY = 65535U;
      }

      rtb_Switch_kvh = (uint16_T)qY;
    } else {
      rtb_Switch_kvh = ((uint16_T)0U);
    }

    /* End of Switch: '<S54>/Switch' */

    /* Logic: '<S54>/Logical Operator' incorporates:
     *  Constant: '<S54>/TIMEXVSL'
     *  Constant: '<S55>/Constant'
     *  Constant: '<S56>/Constant'
     *  Constant: '<S57>/Constant'
     *  Constant: '<S58>/Constant'
     *  Inport: '<Root>/ClutchSignal'
     *  Inport: '<Root>/GearPos'
     *  Inport: '<Root>/KeySignal'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/TrestleSignal'
     *  RelationalOperator: '<S54>/Relational Operator'
     *  RelationalOperator: '<S55>/Compare'
     *  RelationalOperator: '<S56>/Compare'
     *  RelationalOperator: '<S57>/Compare'
     *  RelationalOperator: '<S58>/Compare'
     */
    rtb_LogicalOperator_mnd = (((((((((int32_T)KeySignal) != 0) && (((int32_T)
      TrestleSignal) != 0)) && (GearPos > ((uint8_T)0U))) && (Rpm == ((uint16_T)
      0U))) && (ClutchSignal != ((uint8_T)0U))) && (FlgVehStop != ((uint8_T)0U)))
      && (TIMEXVSL <= rtb_Switch_kvh));

    /* Sum: '<S51>/Add1' incorporates:
     *  Constant: '<S53>/Constant'
     *  Inport: '<Root>/GearPos'
     *  Logic: '<S51>/Logical Operator1'
     *  RelationalOperator: '<S53>/Compare'
     */
    rtb_Merge1 = (uint8_T)(((uint32_T)(rtb_LogicalOperator_mnd ? 1U : 0U)) +
      ((uint32_T)((rtb_LogicalOperator_mnd && (GearPos == ((uint8_T)2U))) ? 1 :
                  0)));

    /* Chart: '<S51>/Calc_StFoExVSL' */
    /* Gateway: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/Calc_StFoExVSL */
    /* During: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/Calc_StFoExVSL */
    if (((uint32_T)ExhValMgm_DW.is_active_c8_ExhValMgm) == 0U) {
      /* Entry: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/Calc_StFoExVSL */
      ExhValMgm_DW.is_active_c8_ExhValMgm = 1U;

      /* Entry Internal: ExhValMgm/T10ms/Drive_ActiveDiag/Manual_Procedure/IF/Calc_StFoExVSL */
      /* Transition: '<S52>:2' */
      ExhValMgm_DW.FlgExVManSelf = 0U;
      ExhValMgm_DW.FlgExVManPos = 0U;
      ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_IDLE;
    } else {
      switch (ExhValMgm_DW.is_c8_ExhValMgm) {
       case ExhValMgm_IN_EXV_MANUAL_IDLE:
        /* During 'EXV_MANUAL_IDLE': '<S52>:1' */
        /* Transition: '<S52>:5' */
        switch (rtb_Merge1) {
         case 1:
          /* Transition: '<S52>:6' */
          ExhValMgm_DW.FlgExVManSelf = 1U;
          ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_SELF;
          break;

         case 2:
          /* Transition: '<S52>:13' */
          /* Transition: '<S52>:15' */
          ExhValMgm_DW.FlgExVManPos = 1U;
          ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_POS;
          break;

         default:
          /* Transition: '<S52>:14' */
          break;
        }
        break;

       case ExhValMgm_IN_EXV_MANUAL_POS:
        /* During 'EXV_MANUAL_POS': '<S52>:11' */
        /* Transition: '<S52>:17' */
        if (((int32_T)rtb_Merge1) == 0) {
          /* Transition: '<S52>:18' */
          ExhValMgm_DW.FlgExVManPos = 0U;
          ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_IDLE;
        } else {
          /* Transition: '<S52>:20' */
          ExhValMgm_DW.FlgExVManPos = 0U;
        }
        break;

       default:
        /* During 'EXV_MANUAL_SELF': '<S52>:3' */
        /* Transition: '<S52>:26' */
        if (((int32_T)rtb_Merge1) == 0) {
          /* Transition: '<S52>:10' */
          ExhValMgm_DW.FlgExVManSelf = 0U;
          ExhValMgm_DW.is_c8_ExhValMgm = ExhValMgm_IN_EXV_MANUAL_IDLE;
        } else {
          /* Transition: '<S52>:9' */
          ExhValMgm_DW.FlgExVManSelf = 0U;
        }
        break;
      }
    }

    /* End of Chart: '<S51>/Calc_StFoExVSL' */

    /* Logic: '<S51>/Logical Operator2' incorporates:
     *  Inport: '<Root>/EnExhVSelf'
     */
    EnExhVSelfTrg = (uint8_T)(((((int32_T)ExhValMgm_DW.FlgExVManSelf) != 0) ||
      (((int32_T)EnExhVSelf) != 0)) ? 1 : 0);

    /* Logic: '<S51>/Logical Operator3' incorporates:
     *  Inport: '<Root>/EnExhVZeroPos'
     */
    rtb_Merge1 = (uint8_T)(((((int32_T)EnExhVZeroPos) != 0) || (((int32_T)
      ExhValMgm_DW.FlgExVManPos) != 0)) ? 1 : 0);

    /* Update for Memory: '<S54>/Memory' */
    ExhValMgm_DW.Memory_PreviousInput_pjn = rtb_Switch_kvh;

    /* End of Outputs for SubSystem: '<S49>/IF' */
  } else {
    /* Outputs for IfAction SubSystem: '<S49>/Else' incorporates:
     *  ActionPort: '<S50>/Action Port'
     */
    /* SignalConversion generated from: '<S50>/EnExhVSelfTrg' incorporates:
     *  Constant: '<S50>/Constant'
     */
    EnExhVSelfTrg = ((uint8_T)0U);

    /* SignalConversion generated from: '<S50>/EnExhVZeroPosTrg' incorporates:
     *  Constant: '<S50>/Constant1'
     */
    rtb_Merge1 = ((uint8_T)0U);

    /* End of Outputs for SubSystem: '<S49>/Else' */
  }

  /* End of If: '<S49>/If' */

  /* RelationalOperator: '<S17>/Relational Operator3' incorporates:
   *  Constant: '<S17>/THVBATTEXHVDIAGEN'
   *  Inport: '<Root>/VBattery'
   */
  rtb_LogicalOperator_mnd = (VBattery > THVBATTEXHVDIAGEN);

  /* Chart: '<S13>/Chart' incorporates:
   *  Inport: '<Root>/StExVPwm'
   */
  /* Gateway: ExhValMgm/T10ms/Calc_Self/Self/Chart */
  /* During: ExhValMgm/T10ms/Calc_Self/Self/Chart */
  if (((uint32_T)ExhValMgm_DW.is_active_c1_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Calc_Self/Self/Chart */
    ExhValMgm_DW.is_active_c1_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Calc_Self/Self/Chart */
    /* Transition: '<S16>:2' */
    ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_IDLE;
  } else {
    switch (ExhValMgm_DW.is_c1_ExhValMgm) {
     case ExhValMgm_IN_POS_ACT:
      /* During 'POS_ACT': '<S16>:45' */
      /* Transition: '<S16>:50' */
      if ((StExVPwm == EXV_POS_END) && (((int32_T)ExhValMgm_DW.cnt) > 50)) {
        /* Transition: '<S16>:48' */
        ExhValMgm_DW.FlgExVSelfWLamp = 1U;
        ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_DONE;
      } else {
        /* Transition: '<S16>:49' */
        i = ((int32_T)ExhValMgm_DW.cnt) + 1;
        if (i > 255) {
          i = 255;
        }

        ExhValMgm_DW.cnt = (uint8_T)i;
      }
      break;

     case ExhValMgm_IN_POS_TRIG:
      /* During 'POS_TRIG': '<S16>:38' */
      /* Transition: '<S16>:43' */
      /* Transition: '<S16>:46' */
      i = ((int32_T)ExhValMgm_DW.FlgExhVZeroPos_jtz) + 1;
      if (i > 255) {
        i = 255;
      }

      ExhValMgm_DW.FlgExhVZeroPos_jtz = (uint8_T)i;
      ExhValMgm_DW.cnt = 0U;
      ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_POS_ACT;
      break;

     case ExhValMgm_IN_SLF_ACT:
      /* During 'SLF_ACT': '<S16>:9' */
      /* Transition: '<S16>:16' */
      if ((StExVPwm == EXV_NORMAL) && (((int32_T)ExhValMgm_DW.cnt) > 50)) {
        /* Transition: '<S16>:15' */
        ExhValMgm_DW.FlgExVSelfWLamp = 1U;
        ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_DONE;
      } else {
        /* Transition: '<S16>:19' */
        if ((StExVPwm == EXV_WAIT_OFF) && (((int32_T)ExhValMgm_DW.cnt) > 50)) {
          /* Transition: '<S16>:18' */
          ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_MISSED;
        } else {
          /* Transition: '<S16>:21' */
          i = ((int32_T)ExhValMgm_DW.cnt) + 1;
          if (i > 255) {
            i = 255;
          }

          ExhValMgm_DW.cnt = (uint8_T)i;
        }
      }
      break;

     case ExhValMgm_IN_SLF_DONE:
      /* During 'SLF_DONE': '<S16>:12' */
      /* Transition: '<S16>:27' */
      /* Transition: '<S16>:26' */
      ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_IDLE;
      break;

     case ExhValMgm_IN_SLF_IDLE:
      /* During 'SLF_IDLE': '<S16>:1' */
      /* Transition: '<S16>:6' */
      ExhValMgm_DW.FlgExVSelfWLamp = 0U;
      if (((int32_T)rtb_Merge1) != 0) {
        /* Transition: '<S16>:44' */
        ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_POS_TRIG;
      } else {
        /* Transition: '<S16>:40' */
        if (rtb_LogicalOperator_mnd && ((CntReqExhSelf !=
              ExhValMgm_DW.oldCntReqExhSelf) || (((int32_T)EnExhVSelfTrg) != 0)))
        {
          /* Transition: '<S16>:5' */
          ExhValMgm_DW.oldCntReqExhSelf = CntReqExhSelf;
          ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_TRIG;
        } else {
          /* Transition: '<S16>:7' */
        }
      }
      break;

     case ExhValMgm_IN_SLF_MISSED:
      /* During 'SLF_MISSED': '<S16>:13' */
      /* Transition: '<S16>:25' */
      /* Transition: '<S16>:24' */
      ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_IDLE;
      break;

     default:
      /* During 'SLF_TRIG': '<S16>:3' */
      /* Transition: '<S16>:11' */
      /* Transition: '<S16>:10' */
      i = ((int32_T)CntExhVMgmSelf) + 1;
      if (i > 255) {
        i = 255;
      }

      CntExhVMgmSelf = (uint8_T)i;
      ExhValMgm_DW.cnt = 0U;
      ExhValMgm_DW.is_c1_ExhValMgm = ExhValMgm_IN_SLF_ACT;
      break;
    }
  }

  /* End of Chart: '<S13>/Chart' */

  /* Logic: '<S14>/Logical Operator' incorporates:
   *  Constant: '<S7>/Constant1'
   */
  FlgExVPWLamp = (uint8_T)(((((int32_T)((uint8_T)0U)) != 0) || (((int32_T)
    ExhValMgm_DW.FlgExVSelfWLamp) != 0)) ? 1 : 0);

  /* Sum: '<S26>/Add' incorporates:
   *  Constant: '<S26>/ONE'
   *  Memory: '<S26>/Memory'
   */
  ExhValModuleTime = 1U + ExhValMgm_DW.Memory_PreviousInput;

  /* Sum: '<S24>/Add' incorporates:
   *  Memory: '<S24>/Memory'
   */
  ExhvalRelTime = (uint16_T)((int32_T)(((int32_T)ExhValModuleTime) - ((int32_T)
    ExhValMgm_DW.Memory_PreviousInput_do4)));
  for (i = 0; i < 4; i++) {
    /* DataTypeConversion: '<S27>/Conversion' incorporates:
     *  Constant: '<S18>/BKEXHVALTIME'
     */
    rtb_Conversion[i] = BKEXHVALTIME[i];

    /* DataTypeConversion: '<S27>/Conversion1' incorporates:
     *  Constant: '<S18>/VTEXHVALWAVE'
     *  DataTypeConversion: '<S27>/Conversion'
     */
    rtb_Conversion1[i] = VTEXHVALWAVE[i];
  }

  /* DataTypeConversion: '<S27>/Conversion2' */
  rtb_Conversion2 = ExhvalRelTime;

  /* DataTypeConversion: '<S27>/Conversion3' incorporates:
   *  Constant: '<S18>/BKEXHVALTIME_dim'
   */
  rtb_Conversion6 = ((uint8_T)BKEXHVALTIME_dim);

  /* S-Function (LookUp_S16_U16): '<S27>/LookUp_S16_U16' */
  LookUp_S16_U16( &rtb_Conversion3, &rtb_Conversion1[0], rtb_Conversion2,
                 &rtb_Conversion[0], rtb_Conversion6);

  /* DataTypeConversion: '<S28>/Conversion' */
  rtb_Conversion_o0d = rtb_Conversion3;

  /* Chart: '<S8>/PbyRateSel' incorporates:
   *  Inport: '<Root>/StPassBy'
   *  UnitDelay: '<S22>/Unit Delay1'
   *  UnitDelay: '<S22>/Unit Delay3'
   */
  /* Gateway: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
  /* During: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
  if (((uint32_T)ExhValMgm_DW.is_active_c7_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
    ExhValMgm_DW.is_active_c7_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Calc_Target/PbyRateSel */
    /* Transition: '<S21>:5' */
    ExhValMgm_DW.flgZeroTarget = 0U;
    ExhValMgm_DW.flgPbyRate = 0U;
    ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_NORMAL_RATE;
  } else {
    switch (ExhValMgm_DW.is_c7_ExhValMgm) {
     case ExhValMgm_IN_NORMAL_RATE:
      /* During 'NORMAL_RATE': '<S21>:1' */
      if ((StPassBy == WAIT_WOT_PBY) || (StPassBy == ACT_PBY)) {
        /* Transition: '<S21>:4' */
        ExhValMgm_DW.flgZeroTarget = 1U;
        ExhValMgm_DW.flgPbyRate = 1U;
        ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_PBY_ACTION_RATE;
      }
      break;

     case ExhValMgm_IN_PBY_ACTION_RATE:
      /* During 'PBY_ACTION_RATE': '<S21>:2' */
      if ((StPassBy == INIT_PBY) || (StPassBy == DISABLE_PBY)) {
        /* Transition: '<S21>:13' */
        ExhValMgm_DW.flgZeroTarget = 0U;
        ExhValMgm_DW.flgPbyRate = 1U;
        ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_PBY_WAIT_EXIT_RATE;
      } else {
        /* Transition: '<S21>:8' */
        if ((StPassBy == EXIT_ACT_PBY) || (StPassBy == WAIT_INIT_PBY)) {
          /* Transition: '<S21>:11' */
          ExhValMgm_DW.flgZeroTarget = 0U;
        } else {
          /* Transition: '<S21>:12' */
        }
      }
      break;

     default:
      /* During 'PBY_WAIT_EXIT_RATE': '<S21>:3' */
      if (ExhValMgm_DW.UnitDelay1_DSTATE >= ExhValMgm_DW.UnitDelay3_DSTATE) {
        /* Transition: '<S21>:15' */
        ExhValMgm_DW.flgZeroTarget = 0U;
        ExhValMgm_DW.flgPbyRate = 0U;
        ExhValMgm_DW.is_c7_ExhValMgm = ExhValMgm_IN_NORMAL_RATE;
      }
      break;
    }
  }

  /* End of Chart: '<S8>/PbyRateSel' */

  /* DataTypeConversion: '<S36>/Conversion5' incorporates:
   *  Constant: '<S23>/TBEXHVALANGTGT'
   */
  memcpy(&rtb_Reshape[0], &TBEXHVALANGTGT[0], 30U * (sizeof(int16_T)));

  /* DataTypeConversion: '<S36>/Conversion3' incorporates:
   *  Inport: '<Root>/CmeDriver'
   */
  rtb_Conversion3 = CmeDriver;

  /* DataTypeConversion: '<S36>/Conversion4' incorporates:
   *  Constant: '<S23>/BKEXHVALCME'
   */
  for (i = 0; i < 5; i++) {
    rtb_Conversion4[i] = BKEXHVALCME[i];
  }

  /* End of DataTypeConversion: '<S36>/Conversion4' */

  /* DataTypeConversion: '<S36>/Conversion6' incorporates:
   *  Constant: '<S23>/BKEXHVALCME_dim'
   */
  rtb_Conversion6 = ((uint8_T)BKEXHVALCME_dim);

  /* DataTypeConversion: '<S36>/Conversion2' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_Conversion2 = Rpm;

  /* DataTypeConversion: '<S36>/Conversion1' incorporates:
   *  Constant: '<S23>/BKEXHVALRPM'
   */
  for (i = 0; i < 6; i++) {
    rtb_Conversion_h03[i] = BKEXHVALRPM[i];
  }

  /* End of DataTypeConversion: '<S36>/Conversion1' */

  /* DataTypeConversion: '<S36>/Conversion7' incorporates:
   *  Constant: '<S23>/BKEXHVALRPM_dim'
   */
  rtb_Conversion3_fgz = ((uint8_T)BKEXHVALRPM_dim);

  /* S-Function (Look2D_S16_S16_U16): '<S36>/Look2D_S16_S16_U16' */
  Look2D_S16_S16_U16( &rtb_Conversion3, &rtb_Reshape[0], rtb_Conversion3,
                     &rtb_Conversion4[0], rtb_Conversion6, rtb_Conversion2,
                     &rtb_Conversion_h03[0], rtb_Conversion3_fgz);

  /* DataTypeConversion: '<S38>/Conversion' */
  rtb_Conversion_fe5 = rtb_Conversion3;

  /* DataTypeConversion: '<S37>/Conversion2' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_Conversion2 = Rpm;
  for (i = 0; i < 6; i++) {
    /* DataTypeConversion: '<S37>/Conversion1' incorporates:
     *  Constant: '<S23>/VTEXHVANGTGTIDLE'
     */
    rtb_Conversion1_cfg[i] = VTEXHVANGTGTIDLE[i];

    /* DataTypeConversion: '<S37>/Conversion' incorporates:
     *  Constant: '<S23>/BKEXHVALRPM'
     *  DataTypeConversion: '<S37>/Conversion1'
     */
    rtb_Conversion_h03[i] = BKEXHVALRPM[i];
  }

  /* DataTypeConversion: '<S37>/Conversion3' incorporates:
   *  Constant: '<S23>/BKEXHVALRPM_dim'
   */
  rtb_Conversion3_fgz = ((uint8_T)BKEXHVALRPM_dim);

  /* S-Function (LookUp_S16_U16): '<S37>/LookUp_S16_U16' */
  LookUp_S16_U16( &rtb_Conversion3, &rtb_Conversion1_cfg[0], rtb_Conversion2,
                 &rtb_Conversion_h03[0], rtb_Conversion3_fgz);

  /* MultiPortSwitch: '<S8>/Multiport Switch2' incorporates:
   *  Constant: '<S8>/[V]'
   */
  if (((int32_T)ExhValMgm_DW.flgZeroTarget) == 0) {
    /* Switch: '<S8>/Switch' incorporates:
     *  Constant: '<S29>/Constant'
     *  Constant: '<S30>/Constant'
     *  Constant: '<S59>/DIAG_GEAR_SENSOR'
     *  Constant: '<S59>/DIAG_VEHSPEED'
     *  Constant: '<S59>/FAULT'
     *  DataTypeConversion: '<S39>/Conversion'
     *  Inport: '<Root>/FlgVehStop'
     *  Inport: '<Root>/GearPos'
     *  Inport: '<Root>/StDiag'
     *  Logic: '<S20>/Logical Operator'
     *  Logic: '<S59>/Logical Operator2'
     *  RelationalOperator: '<S29>/Compare'
     *  RelationalOperator: '<S30>/Compare'
     *  RelationalOperator: '<S59>/Relational Operator5'
     *  RelationalOperator: '<S59>/Relational Operator6'
     *  Selector: '<S59>/Selector'
     *  Selector: '<S59>/Selector1'
     *  Switch: '<S8>/Switch1'
     */
    if ((FAULT == StDiag[(DIAG_VEHSPEED)]) || (FAULT == StDiag[(DIAG_GEAR_SENSOR)]))
    {
      AngExhTrg0 = rtb_Conversion_fe5;
    } else if ((GearPos != ((uint8_T)0U)) || (FlgVehStop == ((uint8_T)0U))) {
      /* Switch: '<S8>/Switch1' */
      AngExhTrg0 = rtb_Conversion_fe5;
    } else {
      AngExhTrg0 = rtb_Conversion3;
    }

    /* End of Switch: '<S8>/Switch' */
  } else {
    AngExhTrg0 = 0;
  }

  /* End of MultiPortSwitch: '<S8>/Multiport Switch2' */

  /* DataTypeConversion: '<S32>/Conversion' */
  rtb_Conversion3 = AngExhTrg0;

  /* DataTypeConversion: '<S32>/Conversion1' incorporates:
   *  UnitDelay: '<S22>/Unit Delay1'
   */
  rtb_Conversion2_njg = ExhValMgm_DW.UnitDelay1_DSTATE;

  /* DataTypeConversion: '<S32>/Conversion2' incorporates:
   *  Constant: '<S22>/RATEANGTGRMIN'
   */
  rtb_Conversion1_kfn = RATEANGTGRMIN;

  /* DataTypeConversion: '<S32>/Conversion3' incorporates:
   *  Constant: '<S22>/RATEANGTGRPBYMAX'
   */
  rtb_RateLimiter_S16 = RATEANGTGRPBYMAX;

  /* S-Function (RateLimiter_S16): '<S32>/RateLimiter_S16' */
  RateLimiter_S16( &rtb_Conversion3, rtb_Conversion3, rtb_Conversion2_njg,
                  rtb_Conversion1_kfn, rtb_RateLimiter_S16);

  /* DataTypeConversion: '<S34>/Conversion' */
  rtb_Conversion_fe5 = rtb_Conversion3;

  /* DataTypeConversion: '<S33>/Conversion' */
  rtb_RateLimiter_S16 = AngExhTrg0;

  /* DataTypeConversion: '<S33>/Conversion1' incorporates:
   *  UnitDelay: '<S22>/Unit Delay3'
   */
  rtb_Conversion1_kfn = ExhValMgm_DW.UnitDelay3_DSTATE;

  /* DataTypeConversion: '<S33>/Conversion2' incorporates:
   *  Constant: '<S22>/RATEANGTGRMIN'
   */
  rtb_Conversion2_njg = RATEANGTGRMIN;

  /* DataTypeConversion: '<S33>/Conversion3' incorporates:
   *  Constant: '<S22>/RATEANGTGRMAX'
   */
  rtb_Conversion3 = RATEANGTGRMAX;

  /* S-Function (RateLimiter_S16): '<S33>/RateLimiter_S16' */
  RateLimiter_S16( &rtb_RateLimiter_S16, rtb_RateLimiter_S16,
                  rtb_Conversion1_kfn, rtb_Conversion2_njg, rtb_Conversion3);

  /* Switch: '<S22>/Switch2' incorporates:
   *  DataTypeConversion: '<S35>/Conversion'
   */
  if (((int32_T)ExhValMgm_DW.flgPbyRate) != 0) {
    rtb_Switch2 = rtb_Conversion_fe5;
  } else {
    rtb_Switch2 = rtb_RateLimiter_S16;
  }

  /* End of Switch: '<S22>/Switch2' */

  /* MultiPortSwitch: '<S19>/Multiport Switch1' incorporates:
   *  ArithShift: '<S31>/Shift Arithmetic'
   *  Constant: '<S19>/FORCEEXHOBJ'
   *  Constant: '<S19>/[%]'
   *  Memory: '<S31>/Memory'
   *  Memory: '<S31>/Memory1'
   *  Memory: '<S31>/Memory2'
   *  Sum: '<S31>/Add'
   */
  switch (FORCEEXHOBJ) {
   case 0:
    AngExhTrg = (int16_T)((((ExhValMgm_DW.Memory2_PreviousInput +
      ExhValMgm_DW.Memory1_PreviousInput) +
      ExhValMgm_DW.Memory_PreviousInput_f5m) + rtb_Switch2) >> ((uint32_T)2));
    break;

   case 1:
    AngExhTrg = 0;
    break;

   case 2:
    AngExhTrg = rtb_Conversion_o0d;
    break;

   default:
    AngExhTrg = 0;
    break;
  }

  /* End of MultiPortSwitch: '<S19>/Multiport Switch1' */

  /* Logic: '<S43>/Logical Operator1' incorporates:
   *  Constant: '<S46>/Constant'
   *  Constant: '<S47>/Constant'
   *  Inport: '<Root>/StDiagExVPwmRaw'
   *  Logic: '<S43>/Logical Operator2'
   *  RelationalOperator: '<S46>/Compare'
   *  RelationalOperator: '<S47>/Compare'
   */
  rtb_LogicalOperator_mnd = (((StDiagExVPwmRaw != EXV_DIAG_NORMAL) &&
    (StDiagExVPwmRaw != EXV_DIAG_OUT_ERROR)) || (!rtb_LogicalOperator_mnd));

  /* Chart: '<S9>/ExhV_Diag_Func' incorporates:
   *  Constant: '<S9>/THREXVDOUTSTAB'
   *  Constant: '<S9>/TIMEXVDOUTSTAB'
   *  Inport: '<Root>/ExVDutyOut'
   *  Inport: '<Root>/ExVPwmDiagOut'
   *  Inport: '<Root>/ExhVDutyInFdbk'
   *  Inport: '<Root>/StExVPwm'
   */
  /* Gateway: ExhValMgm/T10ms/Diag_ExhValve_Position/ExhV_Diag_Func */
  /* During: ExhValMgm/T10ms/Diag_ExhValve_Position/ExhV_Diag_Func */
  if (((uint32_T)ExhValMgm_DW.is_active_c3_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Diag_ExhValve_Position/ExhV_Diag_Func */
    ExhValMgm_DW.is_active_c3_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Diag_ExhValve_Position/ExhV_Diag_Func */
    /* Transition: '<S42>:28' */
    ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_INIT;
  } else {
    switch (ExhValMgm_DW.is_c3_ExhValMgm) {
     case ExhValMgm_IN_DIAG:
      /* During 'DIAG': '<S42>:7' */
      /* Transition: '<S42>:50' */
      if (ExhValMgm_DW.diff > (((uint32_T)THREXHVPLADIAG) >> ((uint32_T)1))) {
        /* Transition: '<S42>:49' */
        ExhValMgm_DW.ptFault = SIG_NOT_PLAUSIBLE;
      } else {
        /* Transition: '<S42>:51' */
        ExhValMgm_DW.ptFault = NO_PT_FAULT;
      }

      /* Outputs for Function Call SubSystem: '<S9>/fc_diag_calc' */
      /* Transition: '<S42>:4' */
      /* Event: '<S42>:5' */
      ExhValMgm_fc_diag_calc(ExhValMgm_DW.ptFault,
        &ExhValMgm_DW.DiagMgm_SetDiagState, &ExhValMgm_DW.Add,
        &ExhValMgm_DW.fc_diag_calc);

      /* End of Outputs for SubSystem: '<S9>/fc_diag_calc' */

      /* Outputs for Function Call SubSystem: '<S9>/Diag_Stab' */
      /* Event: '<S42>:60' */
      ExhValMgm_Diag_Stab(THREXVDOUTSTAB, TIMEXVDOUTSTAB, ExVDutyOut, 1,
                          &ExhValMgm_DW.Diag_Stab);

      /* End of Outputs for SubSystem: '<S9>/Diag_Stab' */
      ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_INIT;
      break;

     case ExhValMgm_IN_INIT:
      /* During 'INIT': '<S42>:6' */
      /* Transition: '<S42>:9' */
      if ((ExVPwmDiagOut != NO_PT_FAULT) && (!rtb_LogicalOperator_mnd)) {
        /* Transition: '<S42>:63' */
        ExhValMgm_DW.ptFault = ExVPwmDiagOut;

        /* Outputs for Function Call SubSystem: '<S9>/fc_diag_calc' */
        /* Event: '<S42>:5' */
        ExhValMgm_fc_diag_calc(ExhValMgm_DW.ptFault,
          &ExhValMgm_DW.DiagMgm_SetDiagState, &ExhValMgm_DW.Add,
          &ExhValMgm_DW.fc_diag_calc);

        /* End of Outputs for SubSystem: '<S9>/fc_diag_calc' */
      } else {
        /* Transition: '<S42>:62' */
        if ((StExVPwm == EXV_NORMAL) && ((ExVDutyOut > CMD_EXV_VAL_MIN) &&
             (ExVDutyOut < CMD_EXV_VAL_MAX))) {
          /* Transition: '<S42>:11' */
          /* Transition: '<S42>:13' */
          ExhValMgm_DW.oldExVDutyOut = ExVDutyOut;

          /* Transition: '<S42>:18' */
          ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_SEARCHING;
        } else {
          /* Transition: '<S42>:10' */
          /* Transition: '<S42>:14' */
        }
      }
      break;

     case ExhValMgm_IN_SEARCHING:
      /* During 'SEARCHING': '<S42>:19' */
      /* Transition: '<S42>:21' */
      if ((StExVPwm != EXV_NORMAL) || ((ExVDutyOut <= CMD_EXV_VAL_MIN) ||
           (ExVDutyOut >= CMD_EXV_VAL_MAX))) {
        /* Transition: '<S42>:27' */
        ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_INIT;
      } else {
        /* Transition: '<S42>:26' */
        if ((ExVDutyOut > (ExhValMgm_DW.oldExVDutyOut + EXHVDIFSRC)) ||
            ((ExVDutyOut + EXHVDIFSRC) < ExhValMgm_DW.oldExVDutyOut)) {
          /* Outputs for Function Call SubSystem: '<S9>/Diag_Stab' */
          /* Transition: '<S42>:24' */
          /* Event: '<S42>:60' */
          ExhValMgm_Diag_Stab(THREXVDOUTSTAB, TIMEXVDOUTSTAB, ExVDutyOut, 1,
                              &ExhValMgm_DW.Diag_Stab);

          /* End of Outputs for SubSystem: '<S9>/Diag_Stab' */
          ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_STAB;
        } else {
          /* Transition: '<S42>:22' */
        }
      }
      break;

     default:
      /* During 'STAB': '<S42>:38' */
      /* Transition: '<S42>:43' */
      if ((StExVPwm != EXV_NORMAL) || ((ExVDutyOut <= CMD_EXV_VAL_MIN) ||
           (ExVDutyOut >= CMD_EXV_VAL_MAX))) {
        /* Outputs for Function Call SubSystem: '<S9>/Diag_Stab' */
        /* Transition: '<S42>:44' */
        /* Event: '<S42>:60' */
        ExhValMgm_Diag_Stab(THREXVDOUTSTAB, TIMEXVDOUTSTAB, ExVDutyOut, 1,
                            &ExhValMgm_DW.Diag_Stab);

        /* End of Outputs for SubSystem: '<S9>/Diag_Stab' */
        ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_INIT;
      } else {
        /* Transition: '<S42>:46' */
        reset = rtb_LogicalOperator_mnd ? ((uint8_T)1) : ((uint8_T)0);

        /* Outputs for Function Call SubSystem: '<S9>/Diag_Stab' */
        /* Event: '<S42>:60' */
        ExhValMgm_Diag_Stab(THREXVDOUTSTAB, TIMEXVDOUTSTAB, ExVDutyOut, reset,
                            &ExhValMgm_DW.Diag_Stab);

        /* End of Outputs for SubSystem: '<S9>/Diag_Stab' */
        if (((int32_T)ExhVPwmPlaStab) != 0) {
          /* Transition: '<S42>:40' */
          qY = ExhVDutyInFdbk + /*MW:OvSatOk*/ ((uint32_T)ExVDutyOut);
          if (qY < ExhVDutyInFdbk) {
            qY = MAX_uint32_T;
          }

          ExhValMgm_DW.diff = (qY >> ((uint32_T)1));
          if (ExhVDutyInFdbk > ((uint32_T)ExVDutyOut)) {
            q0 = ExhVDutyInFdbk;
          } else {
            q0 = (uint32_T)ExVDutyOut;
          }

          qY = q0 - /*MW:OvSatOk*/ ExhValMgm_DW.diff;
          if (qY > q0) {
            qY = 0U;
          }

          ExhValMgm_DW.diff = qY;
          ExhValMgm_DW.is_c3_ExhValMgm = ExhValMgm_IN_DIAG;
        } else {
          /* Transition: '<S42>:47' */
        }
      }
      break;
    }
  }

  /* End of Chart: '<S9>/ExhV_Diag_Func' */

  /* Chart: '<S9>/Calc_FlgExhVDiagOn' incorporates:
   *  Constant: '<S59>/DIAG_EXHVALVEFDBK'
   *  Constant: '<S59>/DIAG_HBRIDGE_B'
   *  Constant: '<S59>/FAULT'
   *  Inport: '<Root>/DrivingCycle'
   *  Inport: '<Root>/StDiag'
   *  Logic: '<S59>/Logical Operator1'
   *  RelationalOperator: '<S59>/Relational Operator2'
   *  RelationalOperator: '<S59>/Relational Operator8'
   *  Selector: '<S59>/Selector3'
   *  Selector: '<S59>/Selector4'
   */
  /* Gateway: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
  /* During: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
  if (((uint32_T)ExhValMgm_DW.is_active_c9_ExhValMgm) == 0U) {
    /* Entry: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
    ExhValMgm_DW.is_active_c9_ExhValMgm = 1U;

    /* Entry Internal: ExhValMgm/T10ms/Diag_ExhValve_Position/Calc_FlgExhVDiagOn */
    /* Transition: '<S40>:2' */
    ExhValMgm_DW.oldDrivingCycle = DRVC_OFF;
    ExhValMgm_DW.oldCntDiagCall = ExhValMgm_DW.Add;
    FlgExhVDiagOn = 0U;
    ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_RESET;
  } else if (((uint32_T)ExhValMgm_DW.is_c9_ExhValMgm) == ExhValMgm_IN_DIAG_ON) {
    /* During 'DIAG_ON': '<S40>:3' */
    /* Transition: '<S40>:9' */
    if ((DrivingCycle == DRVC_START) && (ExhValMgm_DW.oldDrivingCycle ==
         DRVC_OFF)) {
      /* Transition: '<S40>:10' */
      ExhValMgm_DW.oldCntDiagCall = ExhValMgm_DW.Add;
      ExhValMgm_DW.oldDrivingCycle = DrivingCycle;
      FlgExhVDiagOn = 0U;
      ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_RESET;
    } else {
      /* Transition: '<S40>:11' */
      ExhValMgm_DW.oldDrivingCycle = DrivingCycle;
    }
  } else {
    /* During 'RESET': '<S40>:1' */
    /* Transition: '<S40>:25' */
    if ((((int32_T)FORCEEXHOBJ) == 3) || ((FAULT == StDiag[(DIAG_HBRIDGE_B)]) ||
         (FAULT == StDiag[(DIAG_EXHVALVEFDBK)]))) {
      /* Transition: '<S40>:26' */
      FlgExhVDiagOn = 1U;
    } else {
      /* Transition: '<S40>:5' */
      if ((ExhValMgm_DW.Add != ExhValMgm_DW.oldCntDiagCall) &&
          ((ExhValMgm_DW.DiagMgm_SetDiagState == FAULT) ||
           (ExhValMgm_DW.DiagMgm_SetDiagState == NO_FAULT))) {
        /* Transition: '<S40>:6' */
        ExhValMgm_DW.oldDrivingCycle = DrivingCycle;
        FlgExhVDiagOn = 1U;
        ExhValMgm_DW.is_c9_ExhValMgm = ExhValMgm_IN_DIAG_ON;
      } else {
        /* Transition: '<S40>:7' */
      }
    }
  }

  /* End of Chart: '<S9>/Calc_FlgExhVDiagOn' */

  /* SignalConversion generated from: '<S5>/FlgExhVZeroPos' */
  FlgExhVZeroPos = ExhValMgm_DW.FlgExhVZeroPos_jtz;

  /* Update for Memory: '<S12>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput_jyx = rtb_Compare_cdg;

  /* Update for Memory: '<S12>/Memory1' */
  ExhValMgm_DW.Memory1_PreviousInput_pyc = CntReqExhSelf;

  /* Update for Memory: '<S26>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput = ExhValModuleTime;

  /* Switch: '<S24>/Switch' incorporates:
   *  Constant: '<S18>/FORCEEXHOBJ'
   *  Constant: '<S24>/BKEXHVALTIME'
   *  Constant: '<S24>/BKEXHVALTIME_dim'
   *  Constant: '<S25>/Constant'
   *  Logic: '<S24>/Logical Operator'
   *  RelationalOperator: '<S24>/Relational Operator'
   *  RelationalOperator: '<S25>/Compare'
   *  Selector: '<S24>/Selector'
   */
  if ((ExhvalRelTime >= BKEXHVALTIME[((uint8_T)BKEXHVALTIME_dim)]) ||
      (FORCEEXHOBJ != ((uint8_T)2U))) {
    /* Update for Memory: '<S24>/Memory' */
    ExhValMgm_DW.Memory_PreviousInput_do4 = ExhValModuleTime;
  }

  /* End of Switch: '<S24>/Switch' */

  /* Update for Memory: '<S31>/Memory2' incorporates:
   *  Memory: '<S31>/Memory1'
   */
  ExhValMgm_DW.Memory2_PreviousInput = ExhValMgm_DW.Memory1_PreviousInput;

  /* Update for Memory: '<S31>/Memory1' incorporates:
   *  Memory: '<S31>/Memory'
   */
  ExhValMgm_DW.Memory1_PreviousInput = ExhValMgm_DW.Memory_PreviousInput_f5m;

  /* Update for Memory: '<S31>/Memory' */
  ExhValMgm_DW.Memory_PreviousInput_f5m = rtb_Switch2;

  /* Update for UnitDelay: '<S22>/Unit Delay1' */
  ExhValMgm_DW.UnitDelay1_DSTATE = rtb_Conversion_fe5;

  /* Update for UnitDelay: '<S22>/Unit Delay3' incorporates:
   *  DataTypeConversion: '<S35>/Conversion'
   */
  ExhValMgm_DW.UnitDelay3_DSTATE = rtb_RateLimiter_S16;
}

/* Model step function */
void Trig_ExhValMgm_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S1>/Init'
   */
  ExhValMgm_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_ExhValMgm_T10ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  ExhValMgm_T10ms();

  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S6>/TP_OUT'
   */
  /* Outport: '<Root>/BUS_TP' incorporates:
   *  SignalConversion generated from: '<S60>/BUS_TP'
   */
  ExhValMgm_Y.BUS_TP = ExhValMgm_ConstB.Constant;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
}

/* Model initialize function */
void ExhValMgm_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  ExhValModuleTime = 0U;
  IDExhValMgm = 0U;
  ExhvalRelTime = 0U;
  AngExhTrg = 0;
  AngExhTrg0 = 0;
  FlgExhVZeroPos = 0U;
  FlgExVPWLamp = 0U;
  CntReqExhSelf = 0U;
  EnExhVSelfTrg = 0U;
  FlgExhVDiagOn = 0U;
  CntExhVMgmSelf = 0U;
  ExhVPwmPlaStab = 0U;

  /* states (dwork) */
  (void) memset((void *)&ExhValMgm_DW, 0,
                sizeof(DW_ExhValMgm_T));

  /* external outputs */
  (void) memset((void *)&ExhValMgm_Y, 0,
                sizeof(ExtY_ExhValMgm_T));

  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  ExhValMgm_T10ms_Init();

  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S6>/TP_OUT'
   */
  /* SystemInitialize for Outport: '<Root>/BUS_TP' incorporates:
   *  SignalConversion generated from: '<S60>/BUS_TP'
   */
  ExhValMgm_Y.BUS_TP = ExhValMgm_ConstB.Constant;

  /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */

  /* SystemInitialize for Merge: '<S4>/Merge1' */
  AngExhTrg = 0;

  /* SystemInitialize for Merge: '<S4>/Merge2' */
  FlgExhVZeroPos = ((uint8_T)0U);

  /* SystemInitialize for Merge: '<S4>/Merge3' */
  FlgExVPWLamp = ((uint8_T)0U);
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 /* _BUILD_EXHVALMGM_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
