#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_SPARKMGM_

#include "sparkmgm.h"

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 

//SPARKMGM.BKCMIEFFSATOLL: Vbattery Breakpoint vector for VTTHREFFSATOLL
__declspec(section ".calib") uint16_T BKCMIEFFSATOLL[BKCMIEFFSATOLL_dim] = 
{
 6, 16, 24, 48
};
//SPARKMGM.BKDWELLTBATT: Vbattery Breakpoint vector for VTDWELLGAIN [Volt]
__declspec(section ".calib") uint16_T BKDWELLTBATT[16] = 
{
 96, 104, 112, 120, 128, 136, 144, 160, 168, 184, 192, 208, 224, 240, 256, 384
};
//SPARKMGM.BKDWELLTLOAD: Load Breakpoint vector for TBDWELLTIME [%]
__declspec(section ".calib") uint16_T BKDWELLTLOAD[8] = 
{
 2560, 3840, 4480, 5120, 5760, 7040, 8320, 9600
};
//SPARKMGM.BKDWELLTRPM: Rpm Breakpoint vector for TBDWELLTIME [rpm]
__declspec(section ".calib") uint16_T BKDWELLTRPM[16] = 
{
    500,   1300,   2500,   3500,   4500,   5000,   5500,   6000,   6500,   7500,   8500,   9500,  10500,  11500,  12500,  13500
};
//SPARKMGM.BKGAINSPARKTIME: Rpm error for spark time strategy [rpm]
__declspec(section ".calib") int16_T BKGAINSPARKTIME[7] = 
{
   -100,    -50,      0,     50,    150,    300,    500
};
//SPARKMGM.SELEFFSAMEAN: Sel SA
__declspec(section ".calib") uint8_T SELEFFSAMEAN = 0;
//SPARKMGM.CUTOFFGAIN: Cutoff Threshold gain [gain]
__declspec(section ".calib") int16_T CUTOFFGAIN = 1024;   //( 1.0000000000*1024)
//SPARKMGM.ENSPARKTIME: Enable Spark Time Strategy [boolean]
__declspec(section ".calib") uint8_T ENSPARKTIME =  0;   // 0
//SPARKMGM.FORCEDSA: Forced SA (only in development phase) [degree]
__declspec(section ".calib") int16_T FORCEDSA = 544;   //( 34.0000*16)
//SPARKMGM.FORCESA: Force SA Flag (1-FORCEDSA, 2-Saopt, 3-Sabase, 4-Samin, 5-SAstart) [flag]
__declspec(section ".calib") uint8_T FORCESA =  3;   // 3
//SPARKMGM.SPARKMOD: Modo spark [counter]
__declspec(section ".calib") uint8_T SPARKMOD =  3;   // 3
//SPARKMGM.TBDWELLTIME: Basic Dwell Time Table [ms]
__declspec(section ".calib") uint8_T TBDWELLTIME[16*8] = 
{
 24, 24, 24, 24, 24, 24, 24, 24,
 24, 24, 24, 24, 24, 24, 24, 24,
 24, 24, 24, 24, 24, 24, 24, 24,
 24, 24, 24, 24, 24, 24, 24, 24,
 24, 24, 24, 24, 24, 24, 24, 24,
 18, 18, 18, 18, 24, 24, 24, 24,
 18, 18, 18, 18, 24, 24, 24, 24,
 18, 18, 18, 18, 24, 24, 24, 24,
 18, 18, 18, 18, 23, 23, 23, 23,
 15, 15, 15, 15, 22, 22, 22, 22,
 21, 21, 21, 21, 21, 21, 21, 21,
 21, 21, 21, 21, 21, 21, 21, 21,
 21, 21, 21, 21, 21, 21, 21, 21,
 21, 21, 21, 21, 21, 32, 32, 32,
 21, 21, 21, 21, 21, 32, 32, 32,
 21, 21, 21, 21, 21, 32, 34, 34
};

//SPARKMGM.VTDWELLMAX: MaxDwellTime vector [ms]
__declspec(section ".calib") uint8_T VTDWELLMAX[16] = 
{
 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255
};

//SPARKMGM.VTDWELLMAXCRK: MaxDwellTime vector [ms]
__declspec(section ".calib") uint8_T VTDWELLMAXCRK[16] = 
{
 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255
};

//SPARKMGM.VTDWELLGAIN: Dwell Time Battery correction [gain]
__declspec(section ".calib") uint8_T VTDWELLGAIN[16] = 
{
 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16
};
//SPARKMGM.VTGAINSPARKTIME: Gain factor for spark time strategy [gain]
__declspec(section ".calib") uint16_T VTGAINSPARKTIME[7] = 
{
 1024, 1024, 1024, 1024, 1024, 1024, 1024
};
//SPARKMGM.VTTHREFFSATOLL: Spark advance tollerance for FlgNoTrqCtrSA calculation [gain]
__declspec(section ".calib") int8_T VTTHREFFSATOLL[BKCMIEFFSATOLL_dim] = 
{
 26, 19, 13, 6
};

__declspec(section ".calib") uint8_T ENVLAMEFFSA = 0;

__declspec(section ".calib") uint16_T BKVLAMEFFSA[BKVLAMEFFSA_dim] = 
{
 (uint16_T)(0    * 16),
 (uint16_T)(500  * 16),
 (uint16_T)(1000 * 16),
 (uint16_T)(2000 * 16),
 (uint16_T)(3000 * 16),
 (uint16_T)(3500 * 16),
 (uint16_T)(4095 * 16)
};
__declspec(section ".calib") int16_T VTOFFSETEFFSA[BKVLAMEFFSA_dim] = 
{
 (int16_T)(-0.2  * 16384), 
 (int16_T)(-0.1  * 16384),
 (int16_T)(-0.05 * 16384),
 (int16_T)( 0    * 16384),
 (int16_T)( 0.05 * 16384),
 (int16_T)( 0.1  * 16384),
 (int16_T)( 0.2  * 16384)
};

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_SPARKMGM_
