#ifdef _BUILD_OBD_

#include "diagcanmgm.h"
#include "tpe.h"
#include "..\tree\DD\common\tpe_utils.h"

#ifdef _BUILD_KLINE_  
    extern  void klineTickTimer(void);
    extern  void klineInitStart(void);  
#endif //_BUILD_KLINE_

// Variables from VSBMemory section
extern uint32_t      KWPsession;    //sessione corrente di diagnosi

/********************************************************/

extern struct DecAnswer Decode;

extern uint8_t kwp_com_enabled;
extern int8_t OBD_service_error;
extern uint32_t SessionEnabledServices;

// Global Buffers
extern T_DataInd_tag  T_DataIND;
extern T_DataREG_tag  T_DataREQ;

// Local function declarations //

/**************************************************/

extern uint32_t SessionEnabledServices;


//=============================================================================
//  Method      :  OBD_Decoding
//  Description :  This methods decodes requests coming from external tester 
//                 and it handles positive responses
//  Parameters  :  None
//  Returns     :  None
//=============================================================================
void OBD_Decoding(void)
{
    //(i.e. somma le condizioni di diagnosi)

    Decode.message = 0;
    Decode.message_session = DIAG_MESSAGE_RECEIVED;


    //T_DataREQ.Data[0] = T_DataIND.Data[0] + 0x40;  // Positive Response Service Id  0x40 

    switch (T_DataIND.Data[0])    // Request Service Id
        {
        //-----> REQUEST_POWERTRAIN_DIAG_DATA
        case SERVICE_PWTR_DIAG_DATA:
            if ((SessionEnabledServices & SERVICE_PWTR_DIAG_DATA_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength <= 7) // Length control
                {
/*****************************************/
                    OBD_service01();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;
            } 
            break;

        case SERVICE_PWTR_FREEZED_DATA:
            if ((SessionEnabledServices & SERVICE_PWTR_FREEZED_DATA_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength <= 7) // Length control
                {
/*****************************************/
                    OBD_service02();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;  
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;  
            }
            break;
            
        case SERVICE_EMISSION_DTC :
            if ((SessionEnabledServices & SERVICE_EMISSION_DTC_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength <= 7) // Length control
                {
/*****************************************/
                    OBD_service03();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;  
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED; 
            }
            break;
        case SERVICE_CLEAR_EMISSION_INFO :
        {
            if ((SessionEnabledServices & SERVICE_EMISSION_DTC_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength == 1) // Length control
                {
/*****************************************/
                    OBD_service04();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;  
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;
            }
        }
        break;
        case SERVICE_ONBOARD_MONITORING_TEST_RES:
        {
            if ((SessionEnabledServices & SERVICE_ONBOARD_MONITORING_TEST_RES_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength <= 7) // Length control
                {
/*****************************************/
                    OBD_service06();
/*****************************************/
                }
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;  
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;
            }
        }
        break;
        case SERVICE_EM_DTC_LAST_DC :
        {
            if ((SessionEnabledServices & SERVICE_EM_DTC_LAST_DC_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength < 2) // Length control
                {
/*****************************************/
                    OBD_service07();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;  
            }
        }
        break;
        case SERVICE_VEHICLE_INFO :
            if ((SessionEnabledServices & SERVICE_VEHICLE_INFO_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength <= 7) // Length control
                {
/*****************************************/
                    OBD_service09();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;  
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;  
            }
            break;
        
        case SERVICE_EM_DTC_PERM_STATUS :
        {
            if ((SessionEnabledServices & SERVICE_EM_DTC_PERM_STATUS_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_OBD_SESSION;
                if (T_DataIND.DataLength < 2) // Length control
                {
/*****************************************/
                    OBD_service0A();
/*****************************************/
                } 
                else
                {
                    /* ECU shall not answer */
                    OBD_service_error = RESPONSE_NOT_ALLOWED;  
                }
            }
            else
            {
                /* ECU shall not answer */
                OBD_service_error = RESPONSE_NOT_ALLOWED;  
            }
        }
        break;
        default:  // Default dello switch T_DataIND.Data[0] : request Service Id
            OBD_service_error = RESPONSE_NOT_ALLOWED;  // 0x33 Security access requested
        break;

    }  
}

//=============================================================================
//  Method      :  OBD_DST
//  Description :  Questo modulo esegue le transizioni degli stati di diagnosi
//                 sulla base parametri sotto indicati
//  Parameters  :  - control: comandi ricevuti
//                 - MessageSession:  sessione a cui appartiene il messaggio
//                 - KeySignal:  stato della chiave
//                 - diagtimer: timout
//  Returns     :  None
//  Funzione chiamante  : DiagApl
//  Funzioni richiamate : None
//=============================================================================
void OBD_DST (uint8_t MessageSession)
{

}

#endif





