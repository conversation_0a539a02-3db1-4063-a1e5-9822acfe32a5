/********************************************************************************/
/* FILE NAME: sparkHandler.c                     COPYRIGHT (c) Akhela srl       */
/* VERSION:  0.1.1                                    All Rights Reserved       */
/*                                                                              */
/********************************************************************************/

/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function FastLinkedChan_IN, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

#include "..\..\common\ETPU_Shared.h"
#include "..\..\common\ETPU_SharedTypes.h"
#include "..\include\FastLinkedChan.h"
#include "..\include\etpuc.h"
#include "..\include\eTPUC_common.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

#pragma library;
#pragma option +l;  // List the library
#pragma option v;


/********************************************************************************
* FUNCTION: FastLinkedChan_IN                                                   *
* PURPOSE:  This function send a fast link to the output channel of this        *
* functionality                                                                 *
*                                                                               *
* INPUTS NOTES:                                                                 *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
unsigned int capturedTime;

void FastLinkedChan_IN(unsigned int linkedChan, unsigned int FL_IN_chanFlags)
{
#ifdef _BUILD_FASTLINKEDCHAN_
    if (HSR_INIT_FLCHAN_IN)   // Required to initialize
    {
            capturedTime = 0xFFFFFF;

            SetChannelMode(sm_st);

            SetupCaptureTrans_A(Mtcr2_Ctcr2_eq, any_trans);
    }
    else if(HSR_CLOSE_FLCHAN_IN) /* stop channel operations */
    {
        DisableMatchAndTransitionEventHandling();
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
    }
    else if (IsMatchBOrTransitionAEvent())
    {
//          Transition is detected
            capturedTime = GetCapRegB();

            if(IsInputPinHigh())
            {
                FLCHAN_IN_level = 1;
            }
            else
            {
                FLCHAN_IN_level = 0;
            }
            ClearTransLatch();
            EnableEventHandling();

            LinkToChannel(linkedChan);
//            SetupCaptureTrans_A(Mtcr2_Ctcr2_eq, any_trans);
    }
    else
    {
        //This else statement is used to catch all unspecified entry table conditions
        // Clear all possible event sources
        // And set the unexpected event error indicator
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
    }
#endif /* _BUILD_FASTLINKEDCHAN_ */
}

#pragma endlibrary;

