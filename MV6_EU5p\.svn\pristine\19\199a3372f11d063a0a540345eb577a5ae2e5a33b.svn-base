/**************************************************************************/
/* FILE NAME: Task_Isr&PriTable.c            COPYRIGHT (c) Freescale 2004 */
/*                                                                        */
/* DESCRIPTION:                                                           */
/* This file contains a table of ISRs for INTC in software (SW) vector    */
/*  mode. The table contains addresses for 308 ISR vectors.               */
/*  Table address stards at base of section ".isrvectbl"                  */
/*  The correct Interrupt Service Routine call will be replaced by the    */
/*  ISR function defined by user setting the proper TASK.cfg section      */
/*========================================================================*/
/* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
/* ---   -----------   ----------   ---------------------                 */
/*                                                                                                */
/**************************************************************************/

#include "sys.h"
#include "typedefs.h"
#include "task.h"
#include "events.h"
#include "OS_tasks.h"

#ifdef _BUILD_SYS_
#include "..\sys\include\ETPU_HostInterface.h"
#endif
#include "EMIOS.h"
#ifdef _BUILD_PIT_
    #include "PIT.h"
#endif

#ifdef _BUILD_EXTIRQ_
void EXTIRQ00_Isr(void);
void EXTIRQ01_Isr(void);
void EXTIRQ02_Isr(void);
void EXTIRQ03_Isr(void);
void EXTIRQ04_15_Isr(void);
#endif

#ifdef _BUILD_ADC_
void CFIFO_Underflow(void);
void EndOfAcq_A(void);
void EndOfAcq_B(void);
void ALLFIFOFault_Isr(void);
void EndOfAcquisition_450u_Isr(void);
void EndOfAcquisition_2m_Isr(void);
void EndOfAcquisition_10m_Isr(void);
void EndOfAngAcq(void);
#endif /* _BUILD_ADC_ */

#ifdef _BUILD_SCI_
void SCI_A_INT_ISR(void); 
void SCI_B_INT_ISR(void);
#endif /* _BUILD_SCI_ */

#ifdef _BUILD_SCI_
void l_ifc_tx_master(void);
void l_ifc_rx_master(void);
#endif

void dummy (void);
/* to be moved to pio.h */
extern void IsrFunc_UC2(void);
extern void IsrFunc_UC4(void);
extern void IsrFunc_UC5(void);
extern void IsrFunc_UC6(void);
extern void IsrFunc_UC11(void);

extern void IsrFunc_UC20(void);
extern void IsrFunc_UC21(void);
extern void IsrFunc_UC22(void);
/*                      */

#ifdef _BUILD_PIT_
    #include "PIT.h"
#endif
#ifdef __MWERKS__
#pragma section const_type ".isrvectbl"
#endif

#ifdef __DCC__
#pragma section const_type ".isrvectbl" ".isrvectbl"
#pragma use_section const_type IntcIsrVectorTable
#endif

#ifdef __GHS__
#pragma ghs section const ".isrvectbl"
#endif

#ifdef __GNU__
#pragma section const_type ".isrvectbl"
#endif

#define DUMMY_FUNC      dummy
#define RESERVED        dummy
#define RESERVED_PRI    PRI_0

void dummy (void)  {
   while (1)
   {
   }                 /* Wait forever or for timeout */
 }



const uint32_t IntcIsrVectorTable[] = {   /* Interrupt vector table */
(uint32_t) INTC_SSCIR0_ISR,               /* Interrupt no. 0    - ivINT_SSCIR0_CLR0  */
(uint32_t) INTC_SSCIR1_ISR,               /* Interrupt no. 1    - ivINT_SSCIR1_CLR1  */
(uint32_t) INTC_SSCIR2_ISR,               /* Interrupt no. 2    - ivINT_SSCIR2_CLR2  */
(uint32_t) INTC_SSCIR3_ISR,               /* Interrupt no. 3    - ivINT_SSCIR3_CLR3  */
(uint32_t) INTC_SSCIR4_ISR,               /* Interrupt no. 4    - ivINT_SSCIR4_CLR4  */
(uint32_t) INTC_SSCIR5_ISR,               /* Interrupt no. 5    - ivINT_SSCIR5_CLR5  */
(uint32_t) INTC_SSCIR6_ISR,               /* Interrupt no. 6    - ivINT_SSCIR6_CLR6  */
(uint32_t) INTC_SSCIR7_ISR,               /* Interrupt no. 7    - ivINT_SSCIR7_CLR7  */
(uint32_t) ECSM_SWTIR_SWTIC_ISR,          /* Interrupt no. 8    - ivECSM_SWTIR_SWTIC  */
(uint32_t) ECSM_ESR_ERROR_ISR,            /* Interrupt no. 9    - ivECSM_ESR_ERROR  */
(uint32_t) EDMA_ERL_ERR31_ERR0_ISR,       /* Interrupt no. 10   - ivINT_EDMA_ERRL_ERR31_0  */
(uint32_t) EDMA_IRQRL_INT00_ISR,          /* Interrupt no. 11   - ivINT_EDMA_IRQRL_INT0  */
(uint32_t) EDMA_IRQRL_INT01_ISR,          /* Interrupt no. 12   - ivINT_EDMA_IRQRL_INT1  */
(uint32_t) EDMA_IRQRL_INT02_ISR,          /* Interrupt no. 13   - ivINT_EDMA_IRQRL_INT2  */
(uint32_t) EDMA_IRQRL_INT03_ISR,          /* Interrupt no. 14   - ivINT_EDMA_IRQRL_INT3  */
(uint32_t) EDMA_IRQRL_INT04_ISR,          /* Interrupt no. 15   - ivINT_EDMA_IRQRL_INT4  */
(uint32_t) EDMA_IRQRL_INT05_ISR,          /* Interrupt no. 16   - ivINT_EDMA_IRQRL_INT5  */
(uint32_t) EDMA_IRQRL_INT06_ISR,          /* Interrupt no. 17   - ivINT_EDMA_IRQRL_INT6  */
(uint32_t) EDMA_IRQRL_INT07_ISR,          /* Interrupt no. 18   - ivINT_EDMA_IRQRL_INT7  */
(uint32_t) EDMA_IRQRL_INT08_ISR,          /* Interrupt no. 19   - ivINT_EDMA_IRQRL_INT8  */
(uint32_t) EDMA_IRQRL_INT09_ISR,          /* Interrupt no. 20   - ivINT_EDMA_IRQRL_INT9  */
(uint32_t) EDMA_IRQRL_INT10_ISR,          /* Interrupt no. 21   - ivINT_EDMA_IRQRL_INT10  */
(uint32_t) EDMA_IRQRL_INT11_ISR,          /* Interrupt no. 22   - ivINT_EDMA_IRQRL_INT11  */
(uint32_t) EDMA_IRQRL_INT12_ISR,          /* Interrupt no. 23   - ivINT_EDMA_IRQRL_INT12  */
(uint32_t) EDMA_IRQRL_INT13_ISR,          /* Interrupt no. 24   - ivINT_EDMA_IRQRL_INT13  */
(uint32_t) EDMA_IRQRL_INT14_ISR,          /* Interrupt no. 25   - ivINT_EDMA_IRQRL_INT14  */
(uint32_t) EDMA_IRQRL_INT15_ISR,          /* Interrupt no. 26   - ivINT_EDMA_IRQRL_INT15  */
(uint32_t) EDMA_IRQRL_INT16_ISR,          /* Interrupt no. 27   - ivINT_EDMA_IRQRL_INT16  */
(uint32_t) EDMA_IRQRL_INT17_ISR,          /* Interrupt no. 28   - ivINT_EDMA_IRQRL_INT17  */
(uint32_t) EDMA_IRQRL_INT18_ISR,          /* Interrupt no. 29   - ivINT_EDMA_IRQRL_INT18  */
(uint32_t) EDMA_IRQRL_INT19_ISR,          /* Interrupt no. 30   - ivINT_EDMA_IRQRL_INT19  */
(uint32_t) EDMA_IRQRL_INT20_ISR,          /* Interrupt no. 31   - ivINT_EDMA_IRQRL_INT20  */
(uint32_t) EDMA_IRQRL_INT21_ISR,          /* Interrupt no. 32   - ivINT_EDMA_IRQRL_INT21  */
(uint32_t) EDMA_IRQRL_INT22_ISR,          /* Interrupt no. 33   - ivINT_EDMA_IRQRL_INT22  */
(uint32_t) EDMA_IRQRL_INT23_ISR,          /* Interrupt no. 34   - ivINT_EDMA_IRQRL_INT23  */
(uint32_t) EDMA_IRQRL_INT24_ISR,          /* Interrupt no. 35   - ivINT_EDMA_IRQRL_INT24  */
(uint32_t) EDMA_IRQRL_INT25_ISR,          /* Interrupt no. 36   - ivINT_EDMA_IRQRL_INT25  */
(uint32_t) EDMA_IRQRL_INT26_ISR,          /* Interrupt no. 37   - ivINT_EDMA_IRQRL_INT26  */
(uint32_t) EDMA_IRQRL_INT27_ISR,          /* Interrupt no. 38   - ivINT_EDMA_IRQRL_INT27  */
(uint32_t) EDMA_IRQRL_INT28_ISR,          /* Interrupt no. 39   - ivINT_EDMA_IRQRL_INT28  */
(uint32_t) EDMA_IRQRL_INT29_ISR,          /* Interrupt no. 40   - ivINT_EDMA_IRQRL_INT29  */
(uint32_t) EDMA_IRQRL_INT30_ISR,          /* Interrupt no. 41   - ivINT_EDMA_IRQRL_INT30  */
(uint32_t) EDMA_IRQRL_INT31_ISR,          /* Interrupt no. 42   - ivINT_EDMA_IRQRL_INT31  */
(uint32_t) FMPLL_SYNSR_LOCF_ISR,          /* Interrupt no. 43   - ivFMPLL_SYNSR_LOCF  */
(uint32_t) FMPLL_SYNSR_LOLF_ISR,          /* Interrupt no. 44   - ivFMPLL_SYNSR_LOLF  */
(uint32_t) SIU_OSR_OVF15_OVF0_ISR,        /* Interrupt no. 45   - ivINT_SIU_OSR_OVF15_0  */
(uint32_t) SIU_EISR_EIF0_ISR,             /* Interrupt no. 46   - ivINT_SIU_EISR_EIF0  */
(uint32_t) SIU_EISR_EIF1_ISR,             /* Interrupt no. 47   - ivINT_SIU_EISR_EIF1  */
(uint32_t) SIU_EISR_EIF2_ISR,             /* Interrupt no. 48   - ivINT_SIU_EISR_EIF2  */
(uint32_t) SIU_EISR_EIF3_ISR,             /* Interrupt no. 49   - ivINT_SIU_EISR_EIF3  */
(uint32_t) SIU_EISR_EIF15_EIF4_ISR,       /* Interrupt no. 50   - ivINT_SIU_EISR_EIF15_4  */
(uint32_t) EMIOS_GFR_F0_ISR,              /* Interrupt no. 51   - ivINT_EMIOS_GFR_F0  */
(uint32_t) EMIOS_GFR_F1_ISR,              /* Interrupt no. 52   - ivINT_EMIOS_GFR_F1  */
(uint32_t) EMIOS_GFR_F2_ISR,              /* Interrupt no. 53   - ivINT_EMIOS_GFR_F2  */
(uint32_t) EMIOS_GFR_F3_ISR,              /* Interrupt no. 54   - ivINT_EMIOS_GFR_F3  */
(uint32_t) EMIOS_GFR_F4_ISR,              /* Interrupt no. 55   - ivINT_EMIOS_GFR_F4  */
(uint32_t) EMIOS_GFR_F5_ISR,              /* Interrupt no. 56   - ivINT_EMIOS_GFR_F5  */
(uint32_t) EMIOS_GFR_F6_ISR,              /* Interrupt no. 57   - ivINT_EMIOS_GFR_F6  */
(uint32_t) EMIOS_GFR_F7_ISR,              /* Interrupt no. 58   - ivINT_EMIOS_GFR_F7  */
(uint32_t) EMIOS_GFR_F8_ISR,              /* Interrupt no. 59   - ivINT_EMIOS_GFR_F8  */
(uint32_t) EMIOS_GFR_F9_ISR,              /* Interrupt no. 60   - ivINT_EMIOS_GFR_F9  */
(uint32_t) EMIOS_GFR_F10_ISR,             /* Interrupt no. 61   - ivINT_EMIOS_GFR_F10  */
(uint32_t) EMIOS_GFR_F11_ISR,             /* Interrupt no. 62   - ivINT_EMIOS_GFR_F11  */
(uint32_t) EMIOS_GFR_F12_ISR,             /* Interrupt no. 63   - ivINT_EMIOS_GFR_F12  */
(uint32_t) EMIOS_GFR_F13_ISR,             /* Interrupt no. 64   - ivINT_EMIOS_GFR_F13  */
(uint32_t) EMIOS_GFR_F14_ISR,             /* Interrupt no. 65   - ivINT_EMIOS_GFR_F14  */
(uint32_t) EMIOS_GFR_F15_ISR,             /* Interrupt no. 66   - ivINT_EMIOS_GFR_F15  */
(uint32_t) ETPU_MCR_MGE_ILF_SCMMISF_ISR,  /* Interrupt no. 67   - ivINT_ETPU_MCR_MGE_ILF_SCMMISF  */
(uint32_t) ETPU_CISR_A_CIS0_ISR,          /* Interrupt no. 68   - ivINT_ETPU_CISR_A_CIS0  */
(uint32_t) ETPU_CISR_A_CIS1_ISR,          /* Interrupt no. 69   - ivINT_ETPU_CISR_A_CIS1  */
(uint32_t) ETPU_CISR_A_CIS2_ISR,          /* Interrupt no. 70   - ivINT_ETPU_CISR_A_CIS2  */
(uint32_t) ETPU_CISR_A_CIS3_ISR,          /* Interrupt no. 71   - ivINT_ETPU_CISR_A_CIS3  */
(uint32_t) ETPU_CISR_A_CIS4_ISR,          /* Interrupt no. 72   - ivINT_ETPU_CISR_A_CIS4  */
(uint32_t) ETPU_CISR_A_CIS5_ISR,          /* Interrupt no. 73   - ivINT_ETPU_CISR_A_CIS5  */
(uint32_t) ETPU_CISR_A_CIS6_ISR,          /* Interrupt no. 74   - ivINT_ETPU_CISR_A_CIS6  */
(uint32_t) ETPU_CISR_A_CIS7_ISR,          /* Interrupt no. 75   - ivINT_ETPU_CISR_A_CIS7  */
(uint32_t) ETPU_CISR_A_CIS8_ISR,          /* Interrupt no. 76   - ivINT_ETPU_CISR_A_CIS8  */
(uint32_t) ETPU_CISR_A_CIS9_ISR,          /* Interrupt no. 77   - ivINT_ETPU_CISR_A_CIS9  */
(uint32_t) ETPU_CISR_A_CIS10_ISR,         /* Interrupt no. 78   - ivINT_ETPU_CISR_A_CIS10  */
(uint32_t) ETPU_CISR_A_CIS11_ISR,         /* Interrupt no. 79   - ivINT_ETPU_CISR_A_CIS11  */
(uint32_t) ETPU_CISR_A_CIS12_ISR,         /* Interrupt no. 80   - ivINT_ETPU_CISR_A_CIS12  */
(uint32_t) ETPU_CISR_A_CIS13_ISR,         /* Interrupt no. 81   - ivINT_ETPU_CISR_A_CIS13  */
(uint32_t) ETPU_CISR_A_CIS14_ISR,         /* Interrupt no. 82   - ivINT_ETPU_CISR_A_CIS14  */
(uint32_t) ETPU_CISR_A_CIS15_ISR,         /* Interrupt no. 83   - ivINT_ETPU_CISR_A_CIS15  */
(uint32_t) ETPU_CISR_A_CIS16_ISR,         /* Interrupt no. 84   - ivINT_ETPU_CISR_A_CIS16  */
(uint32_t) ETPU_CISR_A_CIS17_ISR,         /* Interrupt no. 85   - ivINT_ETPU_CISR_A_CIS17  */
(uint32_t) ETPU_CISR_A_CIS18_ISR,         /* Interrupt no. 86   - ivINT_ETPU_CISR_A_CIS18  */
(uint32_t) ETPU_CISR_A_CIS19_ISR,         /* Interrupt no. 87   - ivINT_ETPU_CISR_A_CIS19  */
(uint32_t) ETPU_CISR_A_CIS20_ISR,         /* Interrupt no. 88   - ivINT_ETPU_CISR_A_CIS20  */
(uint32_t) ETPU_CISR_A_CIS21_ISR,         /* Interrupt no. 89   - ivINT_ETPU_CISR_A_CIS21  */
(uint32_t) ETPU_CISR_A_CIS22_ISR,         /* Interrupt no. 90   - ivINT_ETPU_CISR_A_CIS22  */
(uint32_t) ETPU_CISR_A_CIS23_ISR,         /* Interrupt no. 91   - ivINT_ETPU_CISR_A_CIS23  */
(uint32_t) ETPU_CISR_A_CIS24_ISR,         /* Interrupt no. 92   - ivINT_ETPU_CISR_A_CIS24  */
(uint32_t) ETPU_CISR_A_CIS25_ISR,         /* Interrupt no. 93   - ivINT_ETPU_CISR_A_CIS25  */
(uint32_t) ETPU_CISR_A_CIS26_ISR,         /* Interrupt no. 94   - ivINT_ETPU_CISR_A_CIS26  */
(uint32_t) ETPU_CISR_A_CIS27_ISR,         /* Interrupt no. 95   - ivINT_ETPU_CISR_A_CIS27  */
(uint32_t) ETPU_CISR_A_CIS28_ISR,         /* Interrupt no. 96   - ivINT_ETPU_CISR_A_CIS28  */
(uint32_t) ETPU_CISR_A_CIS29_ISR,         /* Interrupt no. 97   - ivINT_ETPU_CISR_A_CIS29  */
(uint32_t) ETPU_CISR_A_CIS30_ISR,         /* Interrupt no. 98   - ivINT_ETPU_CISR_A_CIS30  */
(uint32_t) ETPU_CISR_A_CIS31_ISR,         /* Interrupt no. 99   - ivINT_ETPU_CISR_A_CIS31  */
(uint32_t) EQADC_FISR_TORF_RFOF_CFUF_ISR, /* Interrupt no. 100  - ivINT_EQADC_FISR_TORF_RFOF_CFUF  */
(uint32_t) EQADC_FISR0_NCF_ISR,           /* Interrupt no. 101  - ivINT_EQADC_FISR0_NCF  */
(uint32_t) EQADC_FISR0_PF_ISR,            /* Interrupt no. 102  - ivINT_EQADC_FISR0_PF  */
(uint32_t) EQADC_FISR0_EOQF_ISR,          /* Interrupt no. 103  - ivINT_EQADC_FISR0_EOQF  */
(uint32_t) EQADC_FISR0_CFFF_ISR,          /* Interrupt no. 104  - ivINT_EQADC_FISR0_CFFF  */
(uint32_t) EQADC_FISR0_RFDF_ISR,          /* Interrupt no. 105  - ivINT_EQADC_FISR0_RFDF  */
(uint32_t) EQADC_FISR1_NCF_ISR,           /* Interrupt no. 106  - ivINT_EQADC_FISR1_NCF  */
(uint32_t) EQADC_FISR1_PF_ISR,            /* Interrupt no. 107  - ivINT_EQADC_FISR1_PF  */
(uint32_t) EQADC_FISR1_EOQF_ISR,          /* Interrupt no. 108  - ivINT_EQADC_FISR1_EOQF  */
(uint32_t) EQADC_FISR1_CFFF_ISR,          /* Interrupt no. 109  - ivINT_EQADC_FISR1_CFFF  */
(uint32_t) EQADC_FISR1_RFDF_ISR,          /* Interrupt no. 110  - ivINT_EQADC_FISR1_RFDF  */
(uint32_t) EQADC_FISR2_NCF_ISR,           /* Interrupt no. 111  - ivINT_EQADC_FISR2_NCF  */
(uint32_t) EQADC_FISR2_PF_ISR,            /* Interrupt no. 112  - ivINT_EQADC_FISR2_PF  */
(uint32_t) EQADC_FISR2_EOQF_ISR,          /* Interrupt no. 113  - ivINT_EQADC_FISR2_EOQF  */
(uint32_t) EQADC_FISR2_CFFF_ISR,          /* Interrupt no. 114  - ivINT_EQADC_FISR2_CFFF  */
(uint32_t) EQADC_FISR2_RFDF_ISR,          /* Interrupt no. 115  - ivINT_EQADC_FISR2_RFDF  */
(uint32_t) EQADC_FISR3_NCF_ISR,           /* Interrupt no. 116  - ivINT_EQADC_FISR3_NCF  */
(uint32_t) EQADC_FISR3_PF_ISR,            /* Interrupt no. 117  - ivINT_EQADC_FISR3_PF  */
(uint32_t) EQADC_FISR3_EOQF_ISR,          /* Interrupt no. 118  - ivINT_EQADC_FISR3_EOQF  */
(uint32_t) EQADC_FISR3_CFFF_ISR,          /* Interrupt no. 119  - ivINT_EQADC_FISR3_CFFF  */
(uint32_t) EQADC_FISR3_RFDF_ISR,          /* Interrupt no. 120  - ivINT_EQADC_FISR3_RFDF  */
(uint32_t) EQADC_FISR4_NCF_ISR,           /* Interrupt no. 121  - ivINT_EQADC_FISR4_NCF  */
(uint32_t) EQADC_FISR4_PF_ISR,            /* Interrupt no. 122  - ivINT_EQADC_FISR4_PF  */
(uint32_t) EQADC_FISR4_EOQF_ISR,          /* Interrupt no. 123  - ivINT_EQADC_FISR4_EOQF  */
(uint32_t) EQADC_FISR4_CFFF_ISR,          /* Interrupt no. 124  - ivINT_EQADC_FISR4_CFFF  */
(uint32_t) EQADC_FISR4_RFDF_ISR,          /* Interrupt no. 125  - ivINT_EQADC_FISR4_RFDF  */
(uint32_t) EQADC_FISR5_NCF_ISR,           /* Interrupt no. 126  - ivINT_EQADC_FISR5_NCF  */
(uint32_t) EQADC_FISR5_PF_ISR,            /* Interrupt no. 127  - ivINT_EQADC_FISR5_PF  */
(uint32_t) EQADC_FISR5_EOQF_ISR,          /* Interrupt no. 128  - ivINT_EQADC_FISR5_EOQF  */
(uint32_t) EQADC_FISR5_CFFF_ISR,          /* Interrupt no. 129  - ivINT_EQADC_FISR5_CFFF  */
(uint32_t) EQADC_FISR5_RFDF_ISR,          /* Interrupt no. 130  - ivINT_EQADC_FISR5_RFDF  */
(uint32_t) DSPI_B_ISR_TFUF_RFOF_ISR,      /* Interrupt no. 131  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
(uint32_t) DSPI_B_ISR_EOQF_ISR,           /* Interrupt no. 132  - ivINT_DSPI_B_ISR_EOQF  */
(uint32_t) DSPI_B_ISR_TFFF_ISR,           /* Interrupt no. 133  - ivINT_DSPI_B_ISR_TFFF  */
(uint32_t) DSPI_B_ISR_TCF_ISR,            /* Interrupt no. 134  - ivINT_DSPI_B_ISR_TCF  */
(uint32_t) DSPI_B_ISR_RFDF_ISR,           /* Interrupt no. 135  - ivINT_DSPI_B_ISR_RFDF  */
(uint32_t) DSPI_C_ISR_TFUF_RFOF_ISR,      /* Interrupt no. 136  - ivINT_DSPI_C_ISR_TFUF_RFOF  */
(uint32_t) DSPI_C_ISR_EOQF_ISR,           /* Interrupt no. 137  - ivINT_DSPI_C_ISR_EOQF  */
(uint32_t) DSPI_C_ISR_TFFF_ISR,           /* Interrupt no. 138  - ivINT_DSPI_C_ISR_TFFF  */
(uint32_t) DSPI_C_ISR_TCF_ISR,            /* Interrupt no. 139  - ivINT_DSPI_C_ISR_TCF  */
(uint32_t) DSPI_C_ISR_RFDF_ISR,           /* Interrupt no. 140  - ivINT_DSPI_C_ISR_RFDF  */
(uint32_t) DSPI_D_ISR_TFUF_RFOF_ISR,      /* Interrupt no. 141  - ivINT_DSPI_D_ISR_TFUF_RFOF  */
(uint32_t) DSPI_D_ISR_EOQF_ISR,           /* Interrupt no. 142  - ivINT_DSPI_D_ISR_EOQF  */
(uint32_t) DSPI_D_ISR_TFFF_ISR,           /* Interrupt no. 143  - ivINT_DSPI_D_ISR_TFFF  */
(uint32_t) DSPI_D_ISR_TCF_ISR,            /* Interrupt no. 144  - ivINT_DSPI_D_ISR_TCF  */
(uint32_t) DSPI_D_ISR_RFDF_ISR,           /* Interrupt no. 145  - ivINT_DSPI_D_ISR_RFDF  */
(uint32_t) ESCI_A_ISR,                    /* Interrupt no. 146  - ivINT_ESCI_A  */
(uint32_t) RESERVED,                      /* Interrupt no. 147  - ivINT_Reserved0  */
(uint32_t) RESERVED,                      /* Interrupt no. 148  - ivINT_Reserved1  */
(uint32_t) ESCI_B_ISR,                    /* Interrupt no. 149  - ivINT_ESCI_B  */
(uint32_t) RESERVED,                      /* Interrupt no. 150  - ivINT_Reserved2  */
(uint32_t) RESERVED,                      /* Interrupt no. 151  - ivINT_Reserved3  */
(uint32_t) CAN_A_ESR_BOFF_INT_ISR,        /* Interrupt no. 152  - ivINT_CAN_A_ESR_BOFF_INT  */
(uint32_t) CAN_A_ESR_ERR_INT_ISR,         /* Interrupt no. 153  - ivINT_CAN_A_ESR_ERR_INT  */
(uint32_t) RESERVED,                      /* Interrupt no. 154  - ivINT_Reserved4  */
(uint32_t) CAN_A_IFRL_BUF0I_ISR,          /* Interrupt no. 155  - ivINT_CAN_A_IFRL_BUF0  */
(uint32_t) CAN_A_IFRL_BUF1I_ISR,          /* Interrupt no. 156  - ivINT_CAN_A_IFRL_BUF1  */
(uint32_t) CAN_A_IFRL_BUF2I_ISR,          /* Interrupt no. 157  - ivINT_CAN_A_IFRL_BUF2  */
(uint32_t) CAN_A_IFRL_BUF3I_ISR,          /* Interrupt no. 158  - ivINT_CAN_A_IFRL_BUF3  */
(uint32_t) CAN_A_IFRL_BUF4I_ISR,          /* Interrupt no. 159  - ivINT_CAN_A_IFRL_BUF4  */
(uint32_t) CAN_A_IFRL_BUF5I_ISR,          /* Interrupt no. 160  - ivINT_CAN_A_IFRL_BUF5  */
(uint32_t) CAN_A_IFRL_BUF6I_ISR,          /* Interrupt no. 161  - ivINT_CAN_A_IFRL_BUF6  */
(uint32_t) CAN_A_IFRL_BUF7I_ISR,          /* Interrupt no. 162  - ivINT_CAN_A_IFRL_BUF7  */
(uint32_t) CAN_A_IFRL_BUF8I_ISR,          /* Interrupt no. 163  - ivINT_CAN_A_IFRL_BUF8  */
(uint32_t) CAN_A_IFRL_BUF9I_ISR,          /* Interrupt no. 164  - ivINT_CAN_A_IFRL_BUF9  */
(uint32_t) CAN_A_IFRL_BUF10I_ISR,         /* Interrupt no. 165  - ivINT_CAN_A_IFRL_BUF10  */
(uint32_t) CAN_A_IFRL_BUF11I_ISR,         /* Interrupt no. 166  - ivINT_CAN_A_IFRL_BUF11  */
(uint32_t) CAN_A_IFRL_BUF12I_ISR,         /* Interrupt no. 167  - ivINT_CAN_A_IFRL_BUF12  */
(uint32_t) CAN_A_IFRL_BUF13I_ISR,         /* Interrupt no. 168  - ivINT_CAN_A_IFRL_BUF13  */
(uint32_t) CAN_A_IFRL_BUF14I_ISR,         /* Interrupt no. 169  - ivINT_CAN_A_IFRL_BUF14  */
(uint32_t) CAN_A_IFRL_BUF15I_ISR,         /* Interrupt no. 170  - ivINT_CAN_A_IFRL_BUF15  */
(uint32_t) CAN_A_IFRL_BUF31I_BUF16I_ISR,  /* Interrupt no. 171  - ivINT_CAN_A_IFRL_BUF31_16  */
(uint32_t) CAN_A_IFRH_BUF63I_BUF32I_ISR,  /* Interrupt no. 172  - ivINT_CAN_A_IFRL_BUF63_32  */
(uint32_t) CAN_C_ESR_BOFF_INT_ISR,        /* Interrupt no. 173  - ivINT_CAN_C_ESR_BOFF_INT  */
(uint32_t) CAN_C_ESR_ERR_INT_ISR,         /* Interrupt no. 174  - ivINT_CAN_C_ESR_ERR_INT  */
(uint32_t) RESERVED,                      /* Interrupt no. 175  - ivINT_Reserved5  */
(uint32_t) CAN_C_IFRL_BUF0I_ISR,          /* Interrupt no. 176  - ivINT_CAN_C_IFRL_BUF0  */
(uint32_t) CAN_C_IFRL_BUF1I_ISR,          /* Interrupt no. 177  - ivINT_CAN_C_IFRL_BUF1  */
(uint32_t) CAN_C_IFRL_BUF2I_ISR,          /* Interrupt no. 178  - ivINT_CAN_C_IFRL_BUF2  */
(uint32_t) CAN_C_IFRL_BUF3I_ISR,          /* Interrupt no. 179  - ivINT_CAN_C_IFRL_BUF3  */
(uint32_t) CAN_C_IFRL_BUF4I_ISR,          /* Interrupt no. 180  - ivINT_CAN_C_IFRL_BUF4  */
(uint32_t) CAN_C_IFRL_BUF5I_ISR,          /* Interrupt no. 181  - ivINT_CAN_C_IFRL_BUF5  */
(uint32_t) CAN_C_IFRL_BUF6I_ISR,          /* Interrupt no. 182  - ivINT_CAN_C_IFRL_BUF6  */
(uint32_t) CAN_C_IFRL_BUF7I_ISR,          /* Interrupt no. 183  - ivINT_CAN_C_IFRL_BUF7  */
(uint32_t) CAN_C_IFRL_BUF8I_ISR,          /* Interrupt no. 184  - ivINT_CAN_C_IFRL_BUF8  */
(uint32_t) CAN_C_IFRL_BUF9I_ISR,          /* Interrupt no. 185  - ivINT_CAN_C_IFRL_BUF9  */
(uint32_t) CAN_C_IFRL_BUF10I_ISR,         /* Interrupt no. 186  - ivINT_CAN_C_IFRL_BUF10  */
(uint32_t) CAN_C_IFRL_BUF11I_ISR,         /* Interrupt no. 187  - ivINT_CAN_C_IFRL_BUF11  */
(uint32_t) CAN_C_IFRL_BUF12I_ISR,         /* Interrupt no. 188  - ivINT_CAN_C_IFRL_BUF12  */
(uint32_t) CAN_C_IFRL_BUF13I_ISR,         /* Interrupt no. 189  - ivINT_CAN_C_IFRL_BUF13  */
(uint32_t) CAN_C_IFRL_BUF14I_ISR,         /* Interrupt no. 190  - ivINT_CAN_C_IFRL_BUF14  */
(uint32_t) CAN_C_IFRL_BUF15I_ISR,         /* Interrupt no. 191  - ivINT_CAN_C_IFRL_BUF15  */
(uint32_t) CAN_C_IFRL_BUF31I_BUF16I_ISR,  /* Interrupt no. 192  - ivINT_CAN_C_IFRL_BUF31_16  */
(uint32_t) CAN_C_IFRH_BUF63I_BUF32I_ISR,  /* Interrupt no. 193  - ivINT_CAN_C_IFRL_BUF63_32  */
(uint32_t) RESERVED,                      /* Interrupt no. 194  - ivINT_Reserved6  */
(uint32_t) RESERVED,                      /* Interrupt no. 195  - ivINT_Reserved7  */
(uint32_t) RESERVED,                      /* Interrupt no. 196  - ivINT_Reserved8  */
(uint32_t) RESERVED,                      /* Interrupt no. 197  - ivINT_Reserved9  */
(uint32_t) RESERVED,                      /* Interrupt no. 198  - ivINT_Reserved10  */
(uint32_t) RESERVED,                      /* Interrupt no. 199  - ivINT_Reserved11  */
(uint32_t) RESERVED,                      /* Interrupt no. 200  - ivINT_Reserved12  */
(uint32_t) RESERVED,                      /* Interrupt no. 201  - ivINT_Reserved13  */
(uint32_t) EMIOS_GFR_F16_ISR,             /* Interrupt no. 202  - ivINT_EMIOS_GFR_F16  */
(uint32_t) EMIOS_GFR_F17_ISR,             /* Interrupt no. 203  - ivINT_EMIOS_GFR_F17  */
(uint32_t) EMIOS_GFR_F18_ISR,             /* Interrupt no. 204  - ivINT_EMIOS_GFR_F18  */
(uint32_t) EMIOS_GFR_F19_ISR,             /* Interrupt no. 205  - ivINT_EMIOS_GFR_F19  */
(uint32_t) EMIOS_GFR_F20_ISR,             /* Interrupt no. 206  - ivINT_EMIOS_GFR_F20  */
(uint32_t) EMIOS_GFR_F21_ISR,             /* Interrupt no. 207  - ivINT_EMIOS_GFR_F21  */
(uint32_t) EMIOS_GFR_F22_ISR,             /* Interrupt no. 208  - ivINT_EMIOS_GFR_F22  */
(uint32_t) EMIOS_GFR_F23_ISR,             /* Interrupt no. 209  - ivINT_EMIOS_GFR_F23  */
(uint32_t) EDMA_ERRH_ERR63_ERR32_ISR,     /* Interrupt no. 210  - ivINT_EDMA_ERRH_ERR63_32  */
(uint32_t) EDMA_IRQRH_INT32_ISR,          /* Interrupt no. 211  - ivINT_EDMA_IRQRH_INT32  */
(uint32_t) EDMA_IRQRH_INT33_ISR,          /* Interrupt no. 212  - ivINT_EDMA_IRQRH_INT33  */
(uint32_t) EDMA_IRQRH_INT34_ISR,          /* Interrupt no. 213  - ivINT_EDMA_IRQRH_INT34  */
(uint32_t) EDMA_IRQRH_INT35_ISR,          /* Interrupt no. 214  - ivINT_EDMA_IRQRH_INT35  */
(uint32_t) EDMA_IRQRH_INT36_ISR,          /* Interrupt no. 215  - ivINT_EDMA_IRQRH_INT36  */
(uint32_t) EDMA_IRQRH_INT37_ISR,          /* Interrupt no. 216  - ivINT_EDMA_IRQRH_INT37  */
(uint32_t) EDMA_IRQRH_INT38_ISR,          /* Interrupt no. 217  - ivINT_EDMA_IRQRH_INT38  */
(uint32_t) EDMA_IRQRH_INT39_ISR,          /* Interrupt no. 218  - ivINT_EDMA_IRQRH_INT39  */
(uint32_t) EDMA_IRQRH_INT40_ISR,          /* Interrupt no. 219  - ivINT_EDMA_IRQRH_INT40  */
(uint32_t) EDMA_IRQRH_INT41_ISR,          /* Interrupt no. 220  - ivINT_EDMA_IRQRH_INT41  */
(uint32_t) EDMA_IRQRH_INT42_ISR,          /* Interrupt no. 221  - ivINT_EDMA_IRQRH_INT42  */
(uint32_t) EDMA_IRQRH_INT43_ISR,          /* Interrupt no. 222  - ivINT_EDMA_IRQRH_INT43  */
(uint32_t) EDMA_IRQRH_INT44_ISR,          /* Interrupt no. 223  - ivINT_EDMA_IRQRH_INT44  */
(uint32_t) EDMA_IRQRH_INT45_ISR,          /* Interrupt no. 224  - ivINT_EDMA_IRQRH_INT45  */
(uint32_t) EDMA_IRQRH_INT46_ISR,          /* Interrupt no. 225  - ivINT_EDMA_IRQRH_INT46  */
(uint32_t) EDMA_IRQRH_INT47_ISR,          /* Interrupt no. 226  - ivINT_EDMA_IRQRH_INT47  */
(uint32_t) EDMA_IRQRH_INT48_ISR,          /* Interrupt no. 227  - ivINT_EDMA_IRQRH_INT48  */
(uint32_t) EDMA_IRQRH_INT49_ISR,          /* Interrupt no. 228  - ivINT_EDMA_IRQRH_INT49  */
(uint32_t) EDMA_IRQRH_INT50_ISR,          /* Interrupt no. 229  - ivINT_EDMA_IRQRH_INT50  */
(uint32_t) EDMA_IRQRH_INT51_ISR,          /* Interrupt no. 230  - ivINT_EDMA_IRQRH_INT51  */
(uint32_t) EDMA_IRQRH_INT52_ISR,          /* Interrupt no. 231  - ivINT_EDMA_IRQRH_INT52  */
(uint32_t) EDMA_IRQRH_INT53_ISR,          /* Interrupt no. 232  - ivINT_EDMA_IRQRH_INT53  */
(uint32_t) EDMA_IRQRH_INT54_ISR,          /* Interrupt no. 233  - ivINT_EDMA_IRQRH_INT54  */
(uint32_t) EDMA_IRQRH_INT55_ISR,          /* Interrupt no. 234  - ivINT_EDMA_IRQRH_INT55  */
(uint32_t) EDMA_IRQRH_INT56_ISR,          /* Interrupt no. 235  - ivINT_EDMA_IRQRH_INT56  */
(uint32_t) EDMA_IRQRH_INT57_ISR,          /* Interrupt no. 236  - ivINT_EDMA_IRQRH_INT57  */
(uint32_t) EDMA_IRQRH_INT58_ISR,          /* Interrupt no. 237  - ivINT_EDMA_IRQRH_INT58  */
(uint32_t) EDMA_IRQRH_INT59_ISR,          /* Interrupt no. 238  - ivINT_EDMA_IRQRH_INT59  */
(uint32_t) EDMA_IRQRH_INT60_ISR,          /* Interrupt no. 239  - ivINT_EDMA_IRQRH_INT60  */
(uint32_t) EDMA_IRQRH_INT61_ISR,          /* Interrupt no. 240  - ivINT_EDMA_IRQRH_INT61  */
(uint32_t) EDMA_IRQRH_INT62_ISR,          /* Interrupt no. 241  - ivINT_EDMA_IRQRH_INT62  */
(uint32_t) EDMA_IRQRH_INT63_ISR,          /* Interrupt no. 242  - ivINT_EDMA_IRQRH_INT63  */
(uint32_t) ETPU_CISR_B_CIS0_ISR,          /* Interrupt no. 243  - ivINT_ETPU_CISR_B_CIS0  */
(uint32_t) ETPU_CISR_B_CIS1_ISR,          /* Interrupt no. 244  - ivINT_ETPU_CISR_B_CIS1  */
(uint32_t) ETPU_CISR_B_CIS2_ISR,          /* Interrupt no. 245  - ivINT_ETPU_CISR_B_CIS2  */
(uint32_t) ETPU_CISR_B_CIS3_ISR,          /* Interrupt no. 246  - ivINT_ETPU_CISR_B_CIS3  */
(uint32_t) ETPU_CISR_B_CIS4_ISR,          /* Interrupt no. 247  - ivINT_ETPU_CISR_B_CIS4  */
(uint32_t) ETPU_CISR_B_CIS5_ISR,          /* Interrupt no. 248  - ivINT_ETPU_CISR_B_CIS5  */
(uint32_t) ETPU_CISR_B_CIS6_ISR,          /* Interrupt no. 249  - ivINT_ETPU_CISR_B_CIS6  */
(uint32_t) ETPU_CISR_B_CIS7_ISR,          /* Interrupt no. 250  - ivINT_ETPU_CISR_B_CIS7  */
(uint32_t) ETPU_CISR_B_CIS8_ISR,          /* Interrupt no. 251  - ivINT_ETPU_CISR_B_CIS8  */
(uint32_t) ETPU_CISR_B_CIS9_ISR,          /* Interrupt no. 252  - ivINT_ETPU_CISR_B_CIS9  */
(uint32_t) ETPU_CISR_B_CIS10_ISR,         /* Interrupt no. 253  - ivINT_ETPU_CISR_B_CIS10  */
(uint32_t) ETPU_CISR_B_CIS11_ISR,         /* Interrupt no. 254  - ivINT_ETPU_CISR_B_CIS11  */
(uint32_t) ETPU_CISR_B_CIS12_ISR,         /* Interrupt no. 255  - ivINT_ETPU_CISR_B_CIS12  */
(uint32_t) ETPU_CISR_B_CIS13_ISR,         /* Interrupt no. 256  - ivINT_ETPU_CISR_B_CIS13  */
(uint32_t) ETPU_CISR_B_CIS14_ISR,         /* Interrupt no. 257  - ivINT_ETPU_CISR_B_CIS14  */
(uint32_t) ETPU_CISR_B_CIS15_ISR,         /* Interrupt no. 258  - ivINT_ETPU_CISR_B_CIS15  */
(uint32_t) ETPU_CISR_B_CIS16_ISR,         /* Interrupt no. 259  - ivINT_ETPU_CISR_B_CIS16  */
(uint32_t) ETPU_CISR_B_CIS17_ISR,         /* Interrupt no. 260  - ivINT_ETPU_CISR_B_CIS17  */
(uint32_t) ETPU_CISR_B_CIS18_ISR,         /* Interrupt no. 261  - ivINT_ETPU_CISR_B_CIS18  */
(uint32_t) ETPU_CISR_B_CIS19_ISR,         /* Interrupt no. 262  - ivINT_ETPU_CISR_B_CIS19  */
(uint32_t) ETPU_CISR_B_CIS20_ISR,         /* Interrupt no. 263  - ivINT_ETPU_CISR_B_CIS20  */
(uint32_t) ETPU_CISR_B_CIS21_ISR,         /* Interrupt no. 264  - ivINT_ETPU_CISR_B_CIS21  */
(uint32_t) ETPU_CISR_B_CIS22_ISR,         /* Interrupt no. 265  - ivINT_ETPU_CISR_B_CIS22  */
(uint32_t) ETPU_CISR_B_CIS23_ISR,         /* Interrupt no. 266  - ivINT_ETPU_CISR_B_CIS23  */
(uint32_t) ETPU_CISR_B_CIS24_ISR,         /* Interrupt no. 267  - ivINT_ETPU_CISR_B_CIS24  */
(uint32_t) ETPU_CISR_B_CIS25_ISR,         /* Interrupt no. 268  - ivINT_ETPU_CISR_B_CIS25  */
(uint32_t) ETPU_CISR_B_CIS26_ISR,         /* Interrupt no. 269  - ivINT_ETPU_CISR_B_CIS26  */
(uint32_t) ETPU_CISR_B_CIS27_ISR,         /* Interrupt no. 270  - ivINT_ETPU_CISR_B_CIS27  */
(uint32_t) ETPU_CISR_B_CIS28_ISR,         /* Interrupt no. 271  - ivINT_ETPU_CISR_B_CIS28  */
(uint32_t) ETPU_CISR_B_CIS29_ISR,         /* Interrupt no. 272  - ivINT_ETPU_CISR_B_CIS29  */
(uint32_t) ETPU_CISR_B_CIS30_ISR,         /* Interrupt no. 273  - ivINT_ETPU_CISR_B_CIS30  */
(uint32_t) ETPU_CISR_B_CIS31_ISR,         /* Interrupt no. 274  - ivINT_ETPU_CISR_B_CIS31  */
(uint32_t) DSPI_A_ISR_TFUF_RFOF_ISR,      /* Interrupt no. 275  - ivINT_DSPI_A_ISR_TFUF_RFOF  */
(uint32_t) DSPI_A_ISR_EOQF_ISR,           /* Interrupt no. 276  - ivINT_DSPI_A_ISR_EOQF  */
(uint32_t) DSPI_A_ISR_TFFF_ISR,           /* Interrupt no. 277  - ivINT_DSPI_A_ISR_TFFF  */
(uint32_t) DSPI_A_ISR_TCF_ISR,            /* Interrupt no. 278  - ivINT_DSPI_A_ISR_TCF  */
(uint32_t) DSPI_A_ISR_RFDF_ISR,           /* Interrupt no. 279  - ivINT_DSPI_A_ISR_RFDF  */
(uint32_t) CAN_B_ESR_BOFF_INT_ISR,        /* Interrupt no. 280  - ivINT_CAN_B_ESR_BOFF_INT  */
(uint32_t) CAN_B_ESR_ERR_INT_ISR,         /* Interrupt no. 281  - ivINT_CAN_B_ESR_ERR_INT  */
(uint32_t) RESERVED,                                            /* Interrupt no. 282  - ivINT_Reserved14  */
(uint32_t) CAN_B_IFRL_BUF0I_ISR,          /* Interrupt no. 283  - ivINT_CAN_B_IFRL_BUF0  */
(uint32_t) CAN_B_IFRL_BUF1I_ISR,          /* Interrupt no. 284  - ivINT_CAN_B_IFRL_BUF1  */
(uint32_t) CAN_B_IFRL_BUF2I_ISR,          /* Interrupt no. 285  - ivINT_CAN_B_IFRL_BUF2  */
(uint32_t) CAN_B_IFRL_BUF3I_ISR,          /* Interrupt no. 286  - ivINT_CAN_B_IFRL_BUF3  */
(uint32_t) CAN_B_IFRL_BUF4I_ISR,          /* Interrupt no. 287  - ivINT_CAN_B_IFRL_BUF4  */
(uint32_t) CAN_B_IFRL_BUF5I_ISR,          /* Interrupt no. 288  - ivINT_CAN_B_IFRL_BUF5  */
(uint32_t) CAN_B_IFRL_BUF6I_ISR,          /* Interrupt no. 289  - ivINT_CAN_B_IFRL_BUF6  */
(uint32_t) CAN_B_IFRL_BUF7I_ISR,          /* Interrupt no. 290  - ivINT_CAN_B_IFRL_BUF7  */
(uint32_t) CAN_B_IFRL_BUF8I_ISR,          /* Interrupt no. 291  - ivINT_CAN_B_IFRL_BUF8  */
(uint32_t) CAN_B_IFRL_BUF9I_ISR,          /* Interrupt no. 292  - ivINT_CAN_B_IFRL_BUF9  */
(uint32_t) CAN_B_IFRL_BUF10I_ISR,         /* Interrupt no. 293  - ivINT_CAN_B_IFRL_BUF10  */
(uint32_t) CAN_B_IFRL_BUF11I_ISR,         /* Interrupt no. 294  - ivINT_CAN_B_IFRL_BUF11  */
(uint32_t) CAN_B_IFRL_BUF12I_ISR,         /* Interrupt no. 295  - ivINT_CAN_B_IFRL_BUF12  */
(uint32_t) CAN_B_IFRL_BUF13I_ISR,         /* Interrupt no. 296  - ivINT_CAN_B_IFRL_BUF13  */
(uint32_t) CAN_B_IFRL_BUF14I_ISR,         /* Interrupt no. 297  - ivINT_CAN_B_IFRL_BUF14  */
(uint32_t) CAN_B_IFRL_BUF15I_ISR,         /* Interrupt no. 298  - ivINT_CAN_B_IFRL_BUF15  */
(uint32_t) CAN_B_IFRL_BUF31I_BUF16I_ISR,  /* Interrupt no. 299  - ivINT_CAN_B_IFRL_BUF31_16  */
(uint32_t) CAN_B_IFRH_BUF63I_BUF32I_ISR,  /* Interrupt no. 300  - ivINT_CAN_B_IFRL_BUF63_32  */
(uint32_t) PIT_INT_PIT0_ISR,              /* Interrupt no. 301  - ivINT_PIT_INT_PIT0      */ 
(uint32_t) PIT_INT_PIT1_ISR,              /* Interrupt no. 302  - ivINT_PIT_INT_PIT1      */
(uint32_t) PIT_INT_PIT2_ISR,              /* Interrupt no. 303  - ivINT_PIT_INT_PIT2      */
(uint32_t) PIT_INT_PIT3_ISR,              /* Interrupt no. 304  - ivINT_PIT_INT_PIT3      */
(uint32_t) PIT_INT_RTI_ISR,               /* Interrupt no. 305  - ivINT_PIT_INT_RTI       */
(uint32_t) RESERVED,                      /* Interrupt no. 306  - ivINT_Reserved20  */
(uint32_t) RESERVED                       /* Interrupt no. 307  - ivINT_Reserved21  */
};





const uint8_t INTC_PSRnVector[] = {         /* Priority select register table */
INTC_SSCIR0_LVL,                                              /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
INTC_SSCIR1_LVL,                                              /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
INTC_SSCIR2_LVL,                                              /* Interrupt no. 2  - ivINT_SSCIR2_CLR2  */
INTC_SSCIR3_LVL,                                              /* Interrupt no. 3  - ivINT_SSCIR3_CLR3  */
INTC_SSCIR4_LVL,                                              /* Interrupt no. 4  - ivINT_SSCIR4_CLR4  */
INTC_SSCIR5_LVL,                                              /* Interrupt no. 5  - ivINT_SSCIR5_CLR5  */
INTC_SSCIR6_LVL,                                              /* Interrupt no. 6  - ivINT_SSCIR6_CLR6  */
INTC_SSCIR7_LVL,                                              /* Interrupt no. 7  - ivINT_SSCIR7_CLR7  */
ECSM_SWTIR_SWTIC_LVL,                                       /* Interrupt no. 8  - ivECSM_SWTIR_SWTIC  */
ECSM_ESR_ERROR_LVL,                                         /* Interrupt no. 9  - ivECSM_ESR_ERROR  */
EDMA_ERL_ERR31_ERR0_LVL,                                /* Interrupt no. 10 - ivINT_EDMA_ERRL_ERR31_0  */
EDMA_IRQRL_INT00_LVL,                                       /* Interrupt no. 11 - ivINT_EDMA_IRQRL_INT0  */
EDMA_IRQRL_INT01_LVL,                                       /* Interrupt no. 12 - ivINT_EDMA_IRQRL_INT1  */
EDMA_IRQRL_INT02_LVL,                                       /* Interrupt no. 13 - ivINT_EDMA_IRQRL_INT2  */
EDMA_IRQRL_INT03_LVL,                                       /* Interrupt no. 14 - ivINT_EDMA_IRQRL_INT3  */
EDMA_IRQRL_INT04_LVL,                                       /* Interrupt no. 15 - ivINT_EDMA_IRQRL_INT4  */
EDMA_IRQRL_INT05_LVL,                                       /* Interrupt no. 16 - ivINT_EDMA_IRQRL_INT5  */
EDMA_IRQRL_INT06_LVL,                                       /* Interrupt no. 17 - ivINT_EDMA_IRQRL_INT6  */
EDMA_IRQRL_INT07_LVL,                                       /* Interrupt no. 18 - ivINT_EDMA_IRQRL_INT7  */
EDMA_IRQRL_INT08_LVL,                                       /* Interrupt no. 19 - ivINT_EDMA_IRQRL_INT8  */
EDMA_IRQRL_INT09_LVL,                                       /* Interrupt no. 20 - ivINT_EDMA_IRQRL_INT9  */
EDMA_IRQRL_INT10_LVL,                                       /* Interrupt no. 21 - ivINT_EDMA_IRQRL_INT10  */
EDMA_IRQRL_INT11_LVL,                                       /* Interrupt no. 22 - ivINT_EDMA_IRQRL_INT11  */
EDMA_IRQRL_INT12_LVL,                                       /* Interrupt no. 23 - ivINT_EDMA_IRQRL_INT12  */
EDMA_IRQRL_INT13_LVL,                                       /* Interrupt no. 24 - ivINT_EDMA_IRQRL_INT13  */
EDMA_IRQRL_INT14_LVL,                                       /* Interrupt no. 25 - ivINT_EDMA_IRQRL_INT14  */
EDMA_IRQRL_INT15_LVL,                                       /* Interrupt no. 26 - ivINT_EDMA_IRQRL_INT15  */
EDMA_IRQRL_INT16_LVL,                                       /* Interrupt no. 27 - ivINT_EDMA_IRQRL_INT16  */
EDMA_IRQRL_INT17_LVL,                                       /* Interrupt no. 28 - ivINT_EDMA_IRQRL_INT17  */
EDMA_IRQRL_INT18_LVL,                                       /* Interrupt no. 29 - ivINT_EDMA_IRQRL_INT18  */
EDMA_IRQRL_INT19_LVL,                                       /* Interrupt no. 30 - ivINT_EDMA_IRQRL_INT19  */
EDMA_IRQRL_INT20_LVL,                                       /* Interrupt no. 31 - ivINT_EDMA_IRQRL_INT20  */
EDMA_IRQRL_INT21_LVL,                                       /* Interrupt no. 32 - ivINT_EDMA_IRQRL_INT21  */
EDMA_IRQRL_INT22_LVL,                                       /* Interrupt no. 33 - ivINT_EDMA_IRQRL_INT22  */
EDMA_IRQRL_INT23_LVL,                                       /* Interrupt no. 34 - ivINT_EDMA_IRQRL_INT23  */
EDMA_IRQRL_INT24_LVL,                                       /* Interrupt no. 35 - ivINT_EDMA_IRQRL_INT24  */
EDMA_IRQRL_INT25_LVL,                                       /* Interrupt no. 36 - ivINT_EDMA_IRQRL_INT25  */
EDMA_IRQRL_INT26_LVL,                                       /* Interrupt no. 37 - ivINT_EDMA_IRQRL_INT26  */
EDMA_IRQRL_INT27_LVL,                                       /* Interrupt no. 38 - ivINT_EDMA_IRQRL_INT27  */
EDMA_IRQRL_INT28_LVL,                                       /* Interrupt no. 39 - ivINT_EDMA_IRQRL_INT28  */
EDMA_IRQRL_INT29_LVL,                                       /* Interrupt no. 40 - ivINT_EDMA_IRQRL_INT29  */
EDMA_IRQRL_INT30_LVL,                                       /* Interrupt no. 41 - ivINT_EDMA_IRQRL_INT30  */
EDMA_IRQRL_INT31_LVL,                                       /* Interrupt no. 42 - ivINT_EDMA_IRQRL_INT31  */
FMPLL_SYNSR_LOCF_LVL,                                       /* Interrupt no. 43 - ivFMPLL_SYNSR_LOCF  */
FMPLL_SYNSR_LOLF_LVL,                                       /* Interrupt no. 44 - ivFMPLL_SYNSR_LOLF  */
SIU_OSR_OVF15_OVF0_LVL,                                 /* Interrupt no. 45 - ivINT_SIU_OSR_OVF15_0  */
SIU_EISR_EIF0_LVL,                                        /* Interrupt no. 46 - ivINT_SIU_EISR_EIF0  */
SIU_EISR_EIF1_LVL,                                          /* Interrupt no. 47 - ivINT_SIU_EISR_EIF1  */
SIU_EISR_EIF2_LVL,                                          /* Interrupt no. 48 - ivINT_SIU_EISR_EIF2  */
SIU_EISR_EIF3_LVL,                                          /* Interrupt no. 49 - ivINT_SIU_EISR_EIF3  */
SIU_EISR_EIF15_EIF4_LVL,                                /* Interrupt no. 50 - ivINT_SIU_EISR_EIF15_4  */
EMIOS_GFR_F0_LVL,                                               /* Interrupt no. 51 - ivINT_EMIOS_GFR_F0  */
EMIOS_GFR_F1_LVL,                                               /* Interrupt no. 52 - ivINT_EMIOS_GFR_F1  */
EMIOS_GFR_F2_LVL,                                               /* Interrupt no. 53 - ivINT_EMIOS_GFR_F2  */
EMIOS_GFR_F3_LVL,                                               /* Interrupt no. 54 - ivINT_EMIOS_GFR_F3  */
EMIOS_GFR_F4_LVL,                                               /* Interrupt no. 55 - ivINT_EMIOS_GFR_F4  */
EMIOS_GFR_F5_LVL,                                               /* Interrupt no. 56 - ivINT_EMIOS_GFR_F5  */
EMIOS_GFR_F6_LVL,                                               /* Interrupt no. 57 - ivINT_EMIOS_GFR_F6  */
EMIOS_GFR_F7_LVL,                                               /* Interrupt no. 58 - ivINT_EMIOS_GFR_F7  */
EMIOS_GFR_F8_LVL,                                               /* Interrupt no. 59 - ivINT_EMIOS_GFR_F8  */
EMIOS_GFR_F9_LVL,                                               /* Interrupt no. 60 - ivINT_EMIOS_GFR_F9  */
EMIOS_GFR_F10_LVL,                                          /* Interrupt no. 61 - ivINT_EMIOS_GFR_F10  */
EMIOS_GFR_F11_LVL,                                          /* Interrupt no. 62 - ivINT_EMIOS_GFR_F11  */
EMIOS_GFR_F12_LVL,                                          /* Interrupt no. 63 - ivINT_EMIOS_GFR_F12  */
EMIOS_GFR_F13_LVL,                                          /* Interrupt no. 64 - ivINT_EMIOS_GFR_F13  */
EMIOS_GFR_F14_LVL,                                          /* Interrupt no. 65 - ivINT_EMIOS_GFR_F14  */
EMIOS_GFR_F15_LVL,                                          /* Interrupt no. 66 - ivINT_EMIOS_GFR_F15  */
ETPU_MCR_MGE_ILF_SCMMISF_LVL,                       /* Interrupt no. 67 - ivINT_ETPU_MCR_MGE_ILF_SCMMISF  */
ETPU_CISR_A_CIS0_LVL,                                       /* Interrupt no. 68 - ivINT_ETPU_CISR_A_CIS0  */
ETPU_CISR_A_CIS1_LVL,                                       /* Interrupt no. 69 - ivINT_ETPU_CISR_A_CIS1  */
ETPU_CISR_A_CIS2_LVL,                                       /* Interrupt no. 70 - ivINT_ETPU_CISR_A_CIS2  */
ETPU_CISR_A_CIS3_LVL,                                       /* Interrupt no. 71 - ivINT_ETPU_CISR_A_CIS3  */
ETPU_CISR_A_CIS4_LVL,                                       /* Interrupt no. 72 - ivINT_ETPU_CISR_A_CIS4  */
ETPU_CISR_A_CIS5_LVL,                                       /* Interrupt no. 73 - ivINT_ETPU_CISR_A_CIS5  */
ETPU_CISR_A_CIS6_LVL,                                       /* Interrupt no. 74 - ivINT_ETPU_CISR_A_CIS6  */
ETPU_CISR_A_CIS7_LVL,                                       /* Interrupt no. 75 - ivINT_ETPU_CISR_A_CIS7  */
ETPU_CISR_A_CIS8_LVL,                                       /* Interrupt no. 76 - ivINT_ETPU_CISR_A_CIS8  */
ETPU_CISR_A_CIS9_LVL,                                       /* Interrupt no. 77 - ivINT_ETPU_CISR_A_CIS9  */
ETPU_CISR_A_CIS10_LVL,                                  /* Interrupt no. 78 - ivINT_ETPU_CISR_A_CIS10  */
ETPU_CISR_A_CIS11_LVL,                                  /* Interrupt no. 79 - ivINT_ETPU_CISR_A_CIS11  */
ETPU_CISR_A_CIS12_LVL,                                  /* Interrupt no. 80 - ivINT_ETPU_CISR_A_CIS12  */
ETPU_CISR_A_CIS13_LVL,                                  /* Interrupt no. 81 - ivINT_ETPU_CISR_A_CIS13  */
ETPU_CISR_A_CIS14_LVL,                                  /* Interrupt no. 82 - ivINT_ETPU_CISR_A_CIS14  */
ETPU_CISR_A_CIS15_LVL,                                  /* Interrupt no. 83 - ivINT_ETPU_CISR_A_CIS15  */
ETPU_CISR_A_CIS16_LVL,                                  /* Interrupt no. 84 - ivINT_ETPU_CISR_A_CIS16  */
ETPU_CISR_A_CIS17_LVL,                                  /* Interrupt no. 85 - ivINT_ETPU_CISR_A_CIS17  */
ETPU_CISR_A_CIS18_LVL,                                  /* Interrupt no. 86 - ivINT_ETPU_CISR_A_CIS18  */
ETPU_CISR_A_CIS19_LVL,                                  /* Interrupt no. 87 - ivINT_ETPU_CISR_A_CIS19  */
ETPU_CISR_A_CIS20_LVL,                                  /* Interrupt no. 88 - ivINT_ETPU_CISR_A_CIS20  */
ETPU_CISR_A_CIS21_LVL,                                  /* Interrupt no. 89 - ivINT_ETPU_CISR_A_CIS21  */
ETPU_CISR_A_CIS22_LVL,                                  /* Interrupt no. 90 - ivINT_ETPU_CISR_A_CIS22  */
ETPU_CISR_A_CIS23_LVL,                                  /* Interrupt no. 91 - ivINT_ETPU_CISR_A_CIS23  */
ETPU_CISR_A_CIS24_LVL,                                  /* Interrupt no. 92 - ivINT_ETPU_CISR_A_CIS24  */
ETPU_CISR_A_CIS25_LVL,                                  /* Interrupt no. 93 - ivINT_ETPU_CISR_A_CIS25  */
ETPU_CISR_A_CIS26_LVL,                                  /* Interrupt no. 94 - ivINT_ETPU_CISR_A_CIS26  */
ETPU_CISR_A_CIS27_LVL,                                  /* Interrupt no. 95 - ivINT_ETPU_CISR_A_CIS27  */
ETPU_CISR_A_CIS28_LVL,                                  /* Interrupt no. 96 - ivINT_ETPU_CISR_A_CIS28  */
ETPU_CISR_A_CIS29_LVL,                                  /* Interrupt no. 97 - ivINT_ETPU_CISR_A_CIS29  */
ETPU_CISR_A_CIS30_LVL,                                  /* Interrupt no. 98 - ivINT_ETPU_CISR_A_CIS30  */
ETPU_CISR_A_CIS31_LVL,                                  /* Interrupt no. 99 - ivINT_ETPU_CISR_A_CIS31  */
EQADC_FISR_TORF_RFOF_CFUF_LVL,                  /* Interrupt no. 100 - ivINT_EQADC_FISR_TORF_RFOF_CFUF  */
EQADC_FISR0_NCF_LVL,                                        /* Interrupt no. 101 - ivINT_EQADC_FISR0_NCF  */
EQADC_FISR0_PF_LVL,                                         /* Interrupt no. 102 - ivINT_EQADC_FISR0_PF  */
EQADC_FISR0_EOQF_LVL,                                       /* Interrupt no. 103 - ivINT_EQADC_FISR0_EOQF  */
EQADC_FISR0_CFFF_LVL,                                       /* Interrupt no. 104 - ivINT_EQADC_FISR0_CFFF  */
EQADC_FISR0_RFDF_LVL,                                       /* Interrupt no. 105 - ivINT_EQADC_FISR0_RFDF  */
EQADC_FISR1_NCF_LVL,                                        /* Interrupt no. 106 - ivINT_EQADC_FISR1_NCF  */
EQADC_FISR1_PF_LVL,                                         /* Interrupt no. 107 - ivINT_EQADC_FISR1_PF  */
EQADC_FISR1_EOQF_LVL,                                       /* Interrupt no. 108 - ivINT_EQADC_FISR1_EOQF  */
EQADC_FISR1_CFFF_LVL,                                       /* Interrupt no. 109 - ivINT_EQADC_FISR1_CFFF  */
EQADC_FISR1_RFDF_LVL,                                       /* Interrupt no. 110 - ivINT_EQADC_FISR1_RFDF  */
EQADC_FISR2_NCF_LVL,                                        /* Interrupt no. 111 - ivINT_EQADC_FISR2_NCF  */
EQADC_FISR2_PF_LVL,                                         /* Interrupt no. 112 - ivINT_EQADC_FISR2_PF  */
EQADC_FISR2_EOQF_LVL,                                       /* Interrupt no. 113 - ivINT_EQADC_FISR2_EOQF  */
EQADC_FISR2_CFFF_LVL,                                       /* Interrupt no. 114 - ivINT_EQADC_FISR2_CFFF  */
EQADC_FISR2_RFDF_LVL,                                       /* Interrupt no. 115 - ivINT_EQADC_FISR2_RFDF  */
EQADC_FISR3_NCF_LVL,                                        /* Interrupt no. 116 - ivINT_EQADC_FISR3_NCF  */
EQADC_FISR3_PF_LVL,                                         /* Interrupt no. 117 - ivINT_EQADC_FISR3_PF  */
EQADC_FISR3_EOQF_LVL,                                       /* Interrupt no. 118 - ivINT_EQADC_FISR3_EOQF  */
EQADC_FISR3_CFFF_LVL,                                       /* Interrupt no. 119 - ivINT_EQADC_FISR3_CFFF  */
EQADC_FISR3_RFDF_LVL,                                       /* Interrupt no. 120 - ivINT_EQADC_FISR3_RFDF  */
EQADC_FISR4_NCF_LVL,                                        /* Interrupt no. 121 - ivINT_EQADC_FISR4_NCF  */
EQADC_FISR4_PF_LVL,                                         /* Interrupt no. 122 - ivINT_EQADC_FISR4_PF  */
EQADC_FISR4_EOQF_LVL,                                       /* Interrupt no. 123 - ivINT_EQADC_FISR4_EOQF  */
EQADC_FISR4_CFFF_LVL,                                       /* Interrupt no. 124 - ivINT_EQADC_FISR4_CFFF  */
EQADC_FISR4_RFDF_LVL,                                       /* Interrupt no. 125 - ivINT_EQADC_FISR4_RFDF  */
EQADC_FISR5_NCF_LVL,                                        /* Interrupt no. 126 - ivINT_EQADC_FISR5_NCF  */
EQADC_FISR5_PF_LVL,                                         /* Interrupt no. 127 - ivINT_EQADC_FISR5_PF  */
EQADC_FISR5_EOQF_LVL,                                       /* Interrupt no. 128 - ivINT_EQADC_FISR5_EOQF  */
EQADC_FISR5_CFFF_LVL,                                       /* Interrupt no. 129 - ivINT_EQADC_FISR5_CFFF  */
EQADC_FISR5_RFDF_LVL,                                       /* Interrupt no. 130 - ivINT_EQADC_FISR5_RFDF  */
DSPI_B_ISR_TFUF_RFOF_LVL,                               /* Interrupt no. 131 - ivINT_DSPI_B_ISR_TFUF_RFOF  */
DSPI_B_ISR_EOQF_LVL,                                        /* Interrupt no. 132 - ivINT_DSPI_B_ISR_EOQF  */
DSPI_B_ISR_TFFF_LVL,                                        /* Interrupt no. 133 - ivINT_DSPI_B_ISR_TFFF  */
DSPI_B_ISR_TCF_LVL,                                         /* Interrupt no. 134 - ivINT_DSPI_B_ISR_TCF  */
DSPI_B_ISR_RFDF_LVL,                                        /* Interrupt no. 135 - ivINT_DSPI_B_ISR_RFDF  */
DSPI_C_ISR_TFUF_RFOF_LVL,                               /* Interrupt no. 136 - ivINT_DSPI_C_ISR_TFUF_RFOF  */
DSPI_C_ISR_EOQF_LVL,                                        /* Interrupt no. 137 - ivINT_DSPI_C_ISR_EOQF  */
DSPI_C_ISR_TFFF_LVL,                                        /* Interrupt no. 138 - ivINT_DSPI_C_ISR_TFFF  */
DSPI_C_ISR_TCF_LVL,                                         /* Interrupt no. 139 - ivINT_DSPI_C_ISR_TCF  */
DSPI_C_ISR_RFDF_LVL,                                        /* Interrupt no. 140 - ivINT_DSPI_C_ISR_RFDF  */
DSPI_D_ISR_TFUF_RFOF_LVL,                               /* Interrupt no. 141 - ivINT_DSPI_D_ISR_TFUF_RFOF  */
DSPI_D_ISR_EOQF_LVL,                                        /* Interrupt no. 142 - ivINT_DSPI_D_ISR_EOQF  */
DSPI_D_ISR_TFFF_LVL,                                        /* Interrupt no. 143 - ivINT_DSPI_D_ISR_TFFF  */
DSPI_D_ISR_TCF_LVL,                                         /* Interrupt no. 144 - ivINT_DSPI_D_ISR_TCF  */
DSPI_D_ISR_RFDF_LVL,                                        /* Interrupt no. 145 - ivINT_DSPI_D_ISR_RFDF  */
ESCI_A_LVL,                                                         /* Interrupt no. 146 - ivINT_ESCI_A  */
RESERVED_PRI,                                                       /* Interrupt no. 147 - ivINT_Reserved0  */
RESERVED_PRI,                                                     /* Interrupt no. 148 - ivINT_Reserved1  */
ESCI_B_LVL,                                                         /* Interrupt no. 149 - ivINT_ESCI_B  */
RESERVED_PRI,                                                       /* Interrupt no. 150 - ivINT_Reserved2  */
RESERVED_PRI,                                                       /* Interrupt no. 151 - ivINT_Reserved3  */
CAN_A_ESR_BOFF_INT_LVL,                                 /* Interrupt no. 152 - ivINT_CAN_A_ESR_BOFF_INT  */
CAN_A_ESR_ERR_INT_LVL,                                  /* Interrupt no. 153 - ivINT_CAN_A_ESR_ERR_INT  */
RESERVED_PRI,                                                       /* Interrupt no. 154 - ivINT_Reserved4  */
CAN_A_IFRL_BUF0I_LVL,                                       /* Interrupt no. 155 - ivINT_CAN_A_IFRL_BUF0  */
CAN_A_IFRL_BUF1I_LVL,                                       /* Interrupt no. 156 - ivINT_CAN_A_IFRL_BUF1  */
CAN_A_IFRL_BUF2I_LVL,                                       /* Interrupt no. 157 - ivINT_CAN_A_IFRL_BUF2  */
CAN_A_IFRL_BUF3I_LVL,                                       /* Interrupt no. 158 - ivINT_CAN_A_IFRL_BUF3  */
CAN_A_IFRL_BUF4I_LVL,                                       /* Interrupt no. 159 - ivINT_CAN_A_IFRL_BUF4  */
CAN_A_IFRL_BUF5I_LVL,                                       /* Interrupt no. 160 - ivINT_CAN_A_IFRL_BUF5  */
CAN_A_IFRL_BUF6I_LVL,                                       /* Interrupt no. 161 - ivINT_CAN_A_IFRL_BUF6  */
CAN_A_IFRL_BUF7I_LVL,                                       /* Interrupt no. 162 - ivINT_CAN_A_IFRL_BUF7  */
CAN_A_IFRL_BUF8I_LVL,                                       /* Interrupt no. 163 - ivINT_CAN_A_IFRL_BUF8  */
CAN_A_IFRL_BUF9I_LVL,                                       /* Interrupt no. 164 - ivINT_CAN_A_IFRL_BUF9  */
CAN_A_IFRL_BUF10I_LVL,                                  /* Interrupt no. 165 - ivINT_CAN_A_IFRL_BUF10  */
CAN_A_IFRL_BUF11I_LVL,                                  /* Interrupt no. 166 - ivINT_CAN_A_IFRL_BUF11  */
CAN_A_IFRL_BUF12I_LVL,                                  /* Interrupt no. 167 - ivINT_CAN_A_IFRL_BUF12  */
CAN_A_IFRL_BUF13I_LVL,                                  /* Interrupt no. 168 - ivINT_CAN_A_IFRL_BUF13  */
CAN_A_IFRL_BUF14I_LVL,                                  /* Interrupt no. 169 - ivINT_CAN_A_IFRL_BUF14  */
CAN_A_IFRL_BUF15I_LVL,                                  /* Interrupt no. 170 - ivINT_CAN_A_IFRL_BUF15  */
CAN_A_IFRL_BUF31I_BUF16I_LVL,                       /* Interrupt no. 171 - ivINT_CAN_A_IFRL_BUF31_16  */
CAN_A_IFRH_BUF63I_BUF32I_LVL,                       /* Interrupt no. 172 - ivINT_CAN_A_IFRL_BUF63_32  */
CAN_C_ESR_BOFF_INT_LVL,                                 /* Interrupt no. 173 - ivINT_CAN_C_ESR_BOFF_INT  */
CAN_C_ESR_ERR_INT_LVL,                                  /* Interrupt no. 174 - ivINT_CAN_C_ESR_ERR_INT  */
RESERVED_PRI,                                                       /* Interrupt no. 175 - ivINT_Reserved5  */
CAN_C_IFRL_BUF0I_LVL,                                       /* Interrupt no. 176 - ivINT_CAN_C_IFRL_BUF0  */
CAN_C_IFRL_BUF1I_LVL,                                       /* Interrupt no. 177 - ivINT_CAN_C_IFRL_BUF1  */
CAN_C_IFRL_BUF2I_LVL,                                       /* Interrupt no. 178 - ivINT_CAN_C_IFRL_BUF2  */
CAN_C_IFRL_BUF3I_LVL,                                       /* Interrupt no. 179 - ivINT_CAN_C_IFRL_BUF3  */
CAN_C_IFRL_BUF4I_LVL,                                       /* Interrupt no. 180 - ivINT_CAN_C_IFRL_BUF4  */
CAN_C_IFRL_BUF5I_LVL,                                       /* Interrupt no. 181 - ivINT_CAN_C_IFRL_BUF5  */
CAN_C_IFRL_BUF6I_LVL,                                       /* Interrupt no. 182 - ivINT_CAN_C_IFRL_BUF6  */
CAN_C_IFRL_BUF7I_LVL,                                       /* Interrupt no. 183 - ivINT_CAN_C_IFRL_BUF7  */
CAN_C_IFRL_BUF8I_LVL,                                       /* Interrupt no. 184 - ivINT_CAN_C_IFRL_BUF8  */
CAN_C_IFRL_BUF9I_LVL,                                       /* Interrupt no. 185 - ivINT_CAN_C_IFRL_BUF9  */
CAN_C_IFRL_BUF10I_LVL,                                  /* Interrupt no. 186 - ivINT_CAN_C_IFRL_BUF10  */
CAN_C_IFRL_BUF11I_LVL,                                  /* Interrupt no. 187 - ivINT_CAN_C_IFRL_BUF11  */
CAN_C_IFRL_BUF12I_LVL,                                  /* Interrupt no. 188 - ivINT_CAN_C_IFRL_BUF12  */
CAN_C_IFRL_BUF13I_LVL,                                  /* Interrupt no. 189 - ivINT_CAN_C_IFRL_BUF13  */
CAN_C_IFRL_BUF14I_LVL,                                  /* Interrupt no. 190 - ivINT_CAN_C_IFRL_BUF14  */
CAN_C_IFRL_BUF15I_LVL,                                  /* Interrupt no. 191 - ivINT_CAN_C_IFRL_BUF15  */
CAN_C_IFRL_BUF31I_BUF16I_LVL,                       /* Interrupt no. 192 - ivINT_CAN_C_IFRL_BUF31_16  */
CAN_C_IFRH_BUF63I_BUF32I_LVL,                       /* Interrupt no. 193 - ivINT_CAN_C_IFRL_BUF63_32  */
RESERVED_PRI,                                                       /* Interrupt no. 194 - ivINT_Reserved6  */
RESERVED_PRI,                                                       /* Interrupt no. 195 - ivINT_Reserved7  */
RESERVED_PRI,                                                       /* Interrupt no. 196 - ivINT_Reserved8  */
RESERVED_PRI,                                                       /* Interrupt no. 197 - ivINT_Reserved9  */
RESERVED_PRI,                                                       /* Interrupt no. 198 - ivINT_Reserved10  */
RESERVED_PRI,                                                       /* Interrupt no. 199 - ivINT_Reserved11  */
RESERVED_PRI,                                                       /* Interrupt no. 200 - ivINT_Reserved12  */
RESERVED_PRI,                                                       /* Interrupt no. 201 - ivINT_Reserved13  */
EMIOS_GFR_F16_LVL,                                          /* Interrupt no. 202 - ivINT_EMIOS_GFR_F16  */
EMIOS_GFR_F17_LVL,                                          /* Interrupt no. 203 - ivINT_EMIOS_GFR_F17  */
EMIOS_GFR_F18_LVL,                                          /* Interrupt no. 204 - ivINT_EMIOS_GFR_F18  */
EMIOS_GFR_F19_LVL,                                          /* Interrupt no. 205 - ivINT_EMIOS_GFR_F19  */
EMIOS_GFR_F20_LVL,                                          /* Interrupt no. 206 - ivINT_EMIOS_GFR_F20  */
EMIOS_GFR_F21_LVL,                                          /* Interrupt no. 207 - ivINT_EMIOS_GFR_F21  */
EMIOS_GFR_F22_LVL,                                          /* Interrupt no. 208 - ivINT_EMIOS_GFR_F22  */
EMIOS_GFR_F23_LVL,                                          /* Interrupt no. 209 - ivINT_EMIOS_GFR_F23  */
EDMA_ERRH_ERR63_ERR32_LVL,                          /* Interrupt no. 210 - ivINT_EDMA_ERRH_ERR63_32  */
EDMA_IRQRH_INT32_LVL,                                       /* Interrupt no. 211 - ivINT_EDMA_IRQRH_INT32  */
EDMA_IRQRH_INT33_LVL,                                       /* Interrupt no. 212 - ivINT_EDMA_IRQRH_INT33  */
EDMA_IRQRH_INT34_LVL,                                       /* Interrupt no. 213 - ivINT_EDMA_IRQRH_INT34  */
EDMA_IRQRH_INT35_LVL,                                       /* Interrupt no. 214 - ivINT_EDMA_IRQRH_INT35  */
EDMA_IRQRH_INT36_LVL,                                       /* Interrupt no. 215 - ivINT_EDMA_IRQRH_INT36  */
EDMA_IRQRH_INT37_LVL,                                       /* Interrupt no. 216 - ivINT_EDMA_IRQRH_INT37  */
EDMA_IRQRH_INT38_LVL,                                       /* Interrupt no. 217 - ivINT_EDMA_IRQRH_INT38  */
EDMA_IRQRH_INT39_LVL,                                       /* Interrupt no. 218 - ivINT_EDMA_IRQRH_INT39  */
EDMA_IRQRH_INT40_LVL,                                       /* Interrupt no. 219 - ivINT_EDMA_IRQRH_INT40  */
EDMA_IRQRH_INT41_LVL,                                       /* Interrupt no. 220 - ivINT_EDMA_IRQRH_INT41  */
EDMA_IRQRH_INT42_LVL,                                       /* Interrupt no. 221 - ivINT_EDMA_IRQRH_INT42  */
EDMA_IRQRH_INT43_LVL,                                       /* Interrupt no. 222 - ivINT_EDMA_IRQRH_INT43  */
EDMA_IRQRH_INT44_LVL,                                       /* Interrupt no. 223 - ivINT_EDMA_IRQRH_INT44  */
EDMA_IRQRH_INT45_LVL,                                       /* Interrupt no. 224 - ivINT_EDMA_IRQRH_INT45  */
EDMA_IRQRH_INT46_LVL,                                       /* Interrupt no. 225 - ivINT_EDMA_IRQRH_INT46  */
EDMA_IRQRH_INT47_LVL,                                       /* Interrupt no. 226 - ivINT_EDMA_IRQRH_INT47  */
EDMA_IRQRH_INT48_LVL,                                       /* Interrupt no. 227 - ivINT_EDMA_IRQRH_INT48  */
EDMA_IRQRH_INT49_LVL,                                       /* Interrupt no. 228 - ivINT_EDMA_IRQRH_INT49  */
EDMA_IRQRH_INT50_LVL,                                       /* Interrupt no. 229 - ivINT_EDMA_IRQRH_INT50  */
EDMA_IRQRH_INT51_LVL,                                       /* Interrupt no. 230 - ivINT_EDMA_IRQRH_INT51  */
EDMA_IRQRH_INT52_LVL,                                       /* Interrupt no. 231 - ivINT_EDMA_IRQRH_INT52  */
EDMA_IRQRH_INT53_LVL,                                       /* Interrupt no. 232 - ivINT_EDMA_IRQRH_INT53  */
EDMA_IRQRH_INT54_LVL,                                       /* Interrupt no. 233 - ivINT_EDMA_IRQRH_INT54  */
EDMA_IRQRH_INT55_LVL,                                       /* Interrupt no. 234 - ivINT_EDMA_IRQRH_INT55  */
EDMA_IRQRH_INT56_LVL,                                       /* Interrupt no. 235 - ivINT_EDMA_IRQRH_INT56  */
EDMA_IRQRH_INT57_LVL,                                       /* Interrupt no. 236 - ivINT_EDMA_IRQRH_INT57  */
EDMA_IRQRH_INT58_LVL,                                       /* Interrupt no. 237 - ivINT_EDMA_IRQRH_INT58  */
EDMA_IRQRH_INT59_LVL,                                       /* Interrupt no. 238 - ivINT_EDMA_IRQRH_INT59  */
EDMA_IRQRH_INT60_LVL,                                       /* Interrupt no. 239 - ivINT_EDMA_IRQRH_INT60  */
EDMA_IRQRH_INT61_LVL,                                       /* Interrupt no. 240 - ivINT_EDMA_IRQRH_INT61  */
EDMA_IRQRH_INT62_LVL,                                       /* Interrupt no. 241 - ivINT_EDMA_IRQRH_INT62  */
EDMA_IRQRH_INT63_LVL,                                       /* Interrupt no. 242 - ivINT_EDMA_IRQRH_INT63  */
ETPU_CISR_B_CIS0_LVL,                                       /* Interrupt no. 243 - ivINT_ETPU_CISR_B_CIS0  */
ETPU_CISR_B_CIS1_LVL,                                       /* Interrupt no. 244 - ivINT_ETPU_CISR_B_CIS1  */
ETPU_CISR_B_CIS2_LVL,                                       /* Interrupt no. 245 - ivINT_ETPU_CISR_B_CIS2  */
ETPU_CISR_B_CIS3_LVL,                                       /* Interrupt no. 246 - ivINT_ETPU_CISR_B_CIS3  */
ETPU_CISR_B_CIS4_LVL,                                       /* Interrupt no. 247 - ivINT_ETPU_CISR_B_CIS4  */
ETPU_CISR_B_CIS5_LVL,                                       /* Interrupt no. 248 - ivINT_ETPU_CISR_B_CIS5  */
ETPU_CISR_B_CIS6_LVL,                                       /* Interrupt no. 249 - ivINT_ETPU_CISR_B_CIS6  */
ETPU_CISR_B_CIS7_LVL,                                       /* Interrupt no. 250 - ivINT_ETPU_CISR_B_CIS7  */
ETPU_CISR_B_CIS8_LVL,                                       /* Interrupt no. 251 - ivINT_ETPU_CISR_B_CIS8  */
ETPU_CISR_B_CIS9_LVL,                                       /* Interrupt no. 252 - ivINT_ETPU_CISR_B_CIS9  */
ETPU_CISR_B_CIS10_LVL,                                  /* Interrupt no. 253 - ivINT_ETPU_CISR_B_CIS10  */
ETPU_CISR_B_CIS11_LVL,                                  /* Interrupt no. 254 - ivINT_ETPU_CISR_B_CIS11  */
ETPU_CISR_B_CIS12_LVL,                                  /* Interrupt no. 255 - ivINT_ETPU_CISR_B_CIS12  */
ETPU_CISR_B_CIS13_LVL,                                  /* Interrupt no. 256 - ivINT_ETPU_CISR_B_CIS13  */
ETPU_CISR_B_CIS14_LVL,                                  /* Interrupt no. 257 - ivINT_ETPU_CISR_B_CIS14  */
ETPU_CISR_B_CIS15_LVL,                                  /* Interrupt no. 258 - ivINT_ETPU_CISR_B_CIS15  */
ETPU_CISR_B_CIS16_LVL,                                  /* Interrupt no. 259 - ivINT_ETPU_CISR_B_CIS16  */
ETPU_CISR_B_CIS17_LVL,                                  /* Interrupt no. 260 - ivINT_ETPU_CISR_B_CIS17  */
ETPU_CISR_B_CIS18_LVL,                                  /* Interrupt no. 261 - ivINT_ETPU_CISR_B_CIS18  */
ETPU_CISR_B_CIS19_LVL,                                  /* Interrupt no. 262 - ivINT_ETPU_CISR_B_CIS19  */
ETPU_CISR_B_CIS20_LVL,                                  /* Interrupt no. 263 - ivINT_ETPU_CISR_B_CIS20  */
ETPU_CISR_B_CIS21_LVL,                                  /* Interrupt no. 264 - ivINT_ETPU_CISR_B_CIS21  */
ETPU_CISR_B_CIS22_LVL,                                  /* Interrupt no. 265 - ivINT_ETPU_CISR_B_CIS22  */
ETPU_CISR_B_CIS23_LVL,                                  /* Interrupt no. 266 - ivINT_ETPU_CISR_B_CIS23  */
ETPU_CISR_B_CIS24_LVL,                                  /* Interrupt no. 267 - ivINT_ETPU_CISR_B_CIS24  */
ETPU_CISR_B_CIS25_LVL,                                  /* Interrupt no. 268 - ivINT_ETPU_CISR_B_CIS25  */
ETPU_CISR_B_CIS26_LVL,                                  /* Interrupt no. 269 - ivINT_ETPU_CISR_B_CIS26  */
ETPU_CISR_B_CIS27_LVL,                                  /* Interrupt no. 270 - ivINT_ETPU_CISR_B_CIS27  */
ETPU_CISR_B_CIS28_LVL,                                  /* Interrupt no. 271 - ivINT_ETPU_CISR_B_CIS28  */
ETPU_CISR_B_CIS29_LVL,                                  /* Interrupt no. 272 - ivINT_ETPU_CISR_B_CIS29  */
ETPU_CISR_B_CIS30_LVL,                                  /* Interrupt no. 273 - ivINT_ETPU_CISR_B_CIS30  */
ETPU_CISR_B_CIS31_LVL,                                  /* Interrupt no. 274 - ivINT_ETPU_CISR_B_CIS31  */
DSPI_A_ISR_TFUF_RFOF_LVL,                               /* Interrupt no. 275 - ivINT_DSPI_A_ISR_TFUF_RFOF  */
DSPI_A_ISR_EOQF_LVL,                                        /* Interrupt no. 276 - ivINT_DSPI_A_ISR_EOQF  */
DSPI_A_ISR_TFFF_LVL,                                        /* Interrupt no. 277 - ivINT_DSPI_A_ISR_TFFF  */
DSPI_A_ISR_TCF_LVL,                                         /* Interrupt no. 278 - ivINT_DSPI_A_ISR_TCF  */
DSPI_A_ISR_RFDF_LVL,                                        /* Interrupt no. 279 - ivINT_DSPI_A_ISR_RFDF  */
CAN_B_ESR_BOFF_INT_LVL,                                 /* Interrupt no. 280 - ivINT_CAN_B_ESR_BOFF_INT  */
CAN_B_ESR_ERR_INT_LVL,                                  /* Interrupt no. 281 - ivINT_CAN_B_ESR_ERR_INT  */
RESERVED_PRI,                                                       /* Interrupt no. 282 - ivINT_Reserved14  */
CAN_B_IFRL_BUF0I_LVL,                                       /* Interrupt no. 283 - ivINT_CAN_B_IFRL_BUF0  */
CAN_B_IFRL_BUF1I_LVL,                                       /* Interrupt no. 284 - ivINT_CAN_B_IFRL_BUF1  */
CAN_B_IFRL_BUF2I_LVL,                                       /* Interrupt no. 285 - ivINT_CAN_B_IFRL_BUF2  */
CAN_B_IFRL_BUF3I_LVL,                                       /* Interrupt no. 286 - ivINT_CAN_B_IFRL_BUF3  */
CAN_B_IFRL_BUF4I_LVL,                                       /* Interrupt no. 287 - ivINT_CAN_B_IFRL_BUF4  */
CAN_B_IFRL_BUF5I_LVL,                                       /* Interrupt no. 288 - ivINT_CAN_B_IFRL_BUF5  */
CAN_B_IFRL_BUF6I_LVL,                                       /* Interrupt no. 289 - ivINT_CAN_B_IFRL_BUF6  */
CAN_B_IFRL_BUF7I_LVL,                                       /* Interrupt no. 290 - ivINT_CAN_B_IFRL_BUF7  */
CAN_B_IFRL_BUF8I_LVL,                                       /* Interrupt no. 291 - ivINT_CAN_B_IFRL_BUF8  */
CAN_B_IFRL_BUF9I_LVL,                                       /* Interrupt no. 292 - ivINT_CAN_B_IFRL_BUF9  */
CAN_B_IFRL_BUF10I_LVL,                                  /* Interrupt no. 293 - ivINT_CAN_B_IFRL_BUF10  */
CAN_B_IFRL_BUF11I_LVL,                                  /* Interrupt no. 294 - ivINT_CAN_B_IFRL_BUF11  */
CAN_B_IFRL_BUF12I_LVL,                                  /* Interrupt no. 295 - ivINT_CAN_B_IFRL_BUF12  */
CAN_B_IFRL_BUF13I_LVL,                                  /* Interrupt no. 296 - ivINT_CAN_B_IFRL_BUF13  */
CAN_B_IFRL_BUF14I_LVL,                                  /* Interrupt no. 297 - ivINT_CAN_B_IFRL_BUF14  */
CAN_B_IFRL_BUF15I_LVL,                                  /* Interrupt no. 298 - ivINT_CAN_B_IFRL_BUF15  */
CAN_B_IFRL_BUF31I_BUF16I_LVL,                       /* Interrupt no. 299 - ivINT_CAN_B_IFRL_BUF31_16  */
CAN_B_IFRH_BUF63I_BUF32I_LVL,                       /* Interrupt no. 300 - ivINT_CAN_B_IFRL_BUF63_32  */
PIT_INT_PIT0_LVL,                                       /* Interrupt no. 301 - ivINT_PIT_INT_PIT0     */
PIT_INT_PIT1_LVL,                                       /* Interrupt no. 302 - ivINT_PIT_INT_PIT1     */
PIT_INT_PIT2_LVL,                                       /* Interrupt no. 303 - ivINT_PIT_INT_PIT2     */
PIT_INT_PIT3_LVL,                                       /* Interrupt no. 304 - ivINT_PIT_INT_PIT3     */
PIT_INT_RTI_LVL,                                        /* Interrupt no. 305 - ivINT_PIT_INT_RTI      */
RESERVED_PRI,                                                       /* Interrupt no. 306 - ivINT_Reserved20  */
RESERVED_PRI                                                        /* Interrupt no. 307 - ivINT_Reserved21  */
};
