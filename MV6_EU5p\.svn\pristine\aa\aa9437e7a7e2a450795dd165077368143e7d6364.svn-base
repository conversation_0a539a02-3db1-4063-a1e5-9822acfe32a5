/*
 * File: IdleMgm.h
 *
 * Code generated for Simulink model 'IdleMgm'.
 *
 * Model version                  : 1.926
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Oct  6 15:25:43 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (26), Warnings (7), Error (0)
 */

#ifndef RTW_HEADER_IdleMgm_h_
#define RTW_HEADER_IdleMgm_h_
#ifndef IdleMgm_COMMON_INCLUDES_
# define IdleMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* IdleMgm_COMMON_INCLUDES_ */

#include "IdleMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "idle_mgm.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKGASOFFRPMIDLE_dim            4U                        /* Referenced by: '<S70>/BKGASOFFRPMIDLE_dim' */
#define BKIDLPROPGAIN_dim              3U                        /* Referenced by:
                                                                  * '<S22>/BKIDLPROPGAIN_dim'
                                                                  * '<S25>/BKIDLPROPGAIN_dim'
                                                                  */
#define BKIDLRPMERR_dim                6U                        /* Referenced by:
                                                                  * '<S18>/BKIDLRPMERR_dim'
                                                                  * '<S23>/BKIDLRPMERR_dim'
                                                                  * '<S25>/BKIDLRPMERR_dim'
                                                                  */
#define BKIDLTRQOFFSTP_dim             3U                        /* Referenced by: '<S13>/BKIDLTRQOFFSTP_dim' */
#define BKMINIDLETRQINTOFF0_dim        4U                        /* Referenced by: '<S14>/BKMINIDLETRQINTOFF0_dim' */
#define BKTWIDLE_dim                   9U                        /* Referenced by: '<S85>/BKTWIDLE_dim' */
#define ID_IDLE_MGM                    32312918U                 /* Referenced by: '<S5>/ID_IDLE_MGM' */

/* mask */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S65>/Memory' */
  uint32_T timer;                      /* '<S8>/IdleTrqAdp_Calc' */
  int16_T FixPtUnitDelay1_DSTATE;      /* '<S87>/FixPt Unit Delay1' */
  uint8_T cond;                        /* '<S45>/Logical Operator' */
  uint8_T resetMem;                    /* '<S4>/IdleTrq_Mgm' */
  uint8_T from;                        /* '<S4>/IdleTrq_Mgm' */
  uint8_T trqstart;                    /* '<S4>/IdleTrq_Mgm' */
  uint8_T oldGpc;                      /* '<S66>/RpmIdleObj_State' */
  uint8_T FixPtUnitDelay2_DSTATE;      /* '<S87>/FixPt Unit Delay2' */
  uint8_T is_active_c3_IdleMgm;        /* '<S8>/IdleTrqAdp_Calc' */
  uint8_T is_c3_IdleMgm;               /* '<S8>/IdleTrqAdp_Calc' */
  uint8_T is_active_c2_IdleMgm;        /* '<S4>/IdleTrq_Mgm' */
  uint8_T is_c2_IdleMgm;               /* '<S4>/IdleTrq_Mgm' */
  uint8_T GearPosCluold;               /* '<S4>/IdleTrq_Mgm' */
  uint8_T GearPosCluold2;              /* '<S4>/IdleTrq_Mgm' */
  uint8_T is_active_c4_IdleMgm;        /* '<S66>/RpmIdleObj_State' */
  uint8_T is_c4_IdleMgm;               /* '<S66>/RpmIdleObj_State' */
  uint8_T is_IN_IDLE;                  /* '<S66>/RpmIdleObj_State' */
  uint8_T cntOutIdle;                  /* '<S66>/RpmIdleObj_State' */
  boolean_T RelationalOperator1;       /* '<S9>/Relational Operator1' */
  boolean_T Relay_Mode;                /* '<S65>/Relay' */
} D_Work_IdleMgm_T;

/* Block signals and states (default storage) */
extern D_Work_IdleMgm_T IdleMgm_DWork;

/* Model entry point functions */
extern void IdleMgm_initialize(void);

/* Exported entry point function */
extern void Trig_IdleMgm_ev_10ms(void);

/* Exported entry point function */
extern void Trig_IdleMgm_ev_NoSync(void);

/* Exported entry point function */
extern void Trig_IdleMgm_ev_PowerOn(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S20>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S36>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Propagation' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S42>/Data Type Propagation' : Unused code path elimination
 * Block '<S46>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Propagation' : Unused code path elimination
 * Block '<S50>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S52>/Data Type Duplicate' : Unused code path elimination
 * Block '<S53>/Data Type Duplicate' : Unused code path elimination
 * Block '<S53>/Data Type Propagation' : Unused code path elimination
 * Block '<S78>/Data Type Duplicate' : Unused code path elimination
 * Block '<S76>/Data Type Duplicate' : Unused code path elimination
 * Block '<S76>/Data Type Propagation' : Unused code path elimination
 * Block '<S79>/Data Type Duplicate' : Unused code path elimination
 * Block '<S77>/Data Type Duplicate' : Unused code path elimination
 * Block '<S81>/Data Type Duplicate' : Unused code path elimination
 * Block '<S80>/Data Type Duplicate' : Unused code path elimination
 * Block '<S80>/Data Type Propagation' : Unused code path elimination
 * Block '<S83>/Data Type Duplicate' : Unused code path elimination
 * Block '<S82>/Data Type Duplicate' : Unused code path elimination
 * Block '<S89>/Data Type Duplicate' : Unused code path elimination
 * Block '<S88>/Data Type Duplicate' : Unused code path elimination
 * Block '<S90>/Data Type Duplicate' : Unused code path elimination
 * Block '<S86>/Data Type Duplicate' : Unused code path elimination
 * Block '<S87>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S93>/Data Type Duplicate' : Unused code path elimination
 * Block '<S92>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Conversion' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S47>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S48>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S49>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S79>/Conversion' : Eliminate redundant data type conversion
 * Block '<S80>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S80>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S83>/Conversion' : Eliminate redundant data type conversion
 * Block '<S88>/Conversion' : Eliminate redundant data type conversion
 * Block '<S88>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S88>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S89>/Conversion' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S90>/Conversion' : Eliminate redundant data type conversion
 * Block '<S92>/Conversion' : Eliminate redundant data type conversion
 * Block '<S92>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S92>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S93>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IdleMgm'
 * '<S1>'   : 'IdleMgm/IdleMgm'
 * '<S2>'   : 'IdleMgm/Model Info'
 * '<S3>'   : 'IdleMgm/IdleMgm/IdleMgm'
 * '<S4>'   : 'IdleMgm/IdleMgm/IdleMgm/Calc'
 * '<S5>'   : 'IdleMgm/IdleMgm/IdleMgm/Reset'
 * '<S6>'   : 'IdleMgm/IdleMgm/IdleMgm/Calc/IdleTrq_Mgm'
 * '<S7>'   : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller'
 * '<S8>'   : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning'
 * '<S9>'   : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_calc'
 * '<S10>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset'
 * '<S11>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/reset_int'
 * '<S12>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget'
 * '<S13>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/vtIDLTRQOFFSTP'
 * '<S14>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor'
 * '<S15>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Rpm_Error_Calculation'
 * '<S16>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Subsystem'
 * '<S17>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/LookUp_S16_U16_1'
 * '<S18>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI'
 * '<S19>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/Saturator'
 * '<S20>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/LookUp_S16_U16_1/Data Type Conversion Inherited3'
 * '<S21>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/Compare To Constant1'
 * '<S22>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc'
 * '<S23>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Idle'
 * '<S24>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PreLookUpIdSearch_S16_1'
 * '<S25>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc'
 * '<S26>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PreLookUpIdSearch_U16_1'
 * '<S27>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc/LookUp_IR_S16_gain'
 * '<S28>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc/LookUp_IR_S16_int'
 * '<S29>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc/LookUp_IR_S16_prop'
 * '<S30>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc/LookUp_IR_S16_gain/Data Type Conversion Inherited3'
 * '<S31>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc/LookUp_IR_S16_int/Data Type Conversion Inherited3'
 * '<S32>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Acc/PI_Acc/LookUp_IR_S16_prop/Data Type Conversion Inherited3'
 * '<S33>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Idle/LookUp_IR_S16_int'
 * '<S34>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Idle/LookUp_IR_S16_prop'
 * '<S35>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Idle/LookUp_IR_S16_int/Data Type Conversion Inherited3'
 * '<S36>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/PI/PI_Idle/LookUp_IR_S16_prop/Data Type Conversion Inherited3'
 * '<S37>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/Saturator/Compare To Zero'
 * '<S38>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/Saturator/Compare To Zero1'
 * '<S39>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Idle_PI_governor/Saturator/Saturation Dynamic'
 * '<S40>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Rpm_Error_Calculation/Compare To Constant'
 * '<S41>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Rpm_Error_Calculation/Dead Zone Dynamic'
 * '<S42>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Controller/Rpm_Error_Calculation/Saturation Dynamic'
 * '<S43>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/FOF_Reset_S16_FXP'
 * '<S44>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/IdleTrqAdp_Calc'
 * '<S45>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond'
 * '<S46>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S47>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond/GenAbs'
 * '<S48>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond/GenAbs1'
 * '<S49>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond/GenAbs2'
 * '<S50>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond/GenAbs/Data Type Conversion Inherited'
 * '<S51>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond/GenAbs1/Data Type Conversion Inherited'
 * '<S52>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_Learning/calc_cond/GenAbs2/Data Type Conversion Inherited'
 * '<S53>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_calc/Saturation Dynamic1'
 * '<S54>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Compare To Zero'
 * '<S55>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Compare To Zero1'
 * '<S56>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Normal'
 * '<S57>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Return'
 * '<S58>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Return/IdleTrq_slow_reset'
 * '<S59>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Return/IdleTrq_slow_reset1'
 * '<S60>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Return/IdleTrq_slow_reset/Dead Zone Dynamic'
 * '<S61>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/Idle_trq_reset/Return/IdleTrq_slow_reset1/Dead Zone Dynamic'
 * '<S62>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/reset_int/ELSE'
 * '<S63>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/reset_int/IF'
 * '<S64>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/EnEconFlg_Calc'
 * '<S65>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/FlgEconVbat_Calc'
 * '<S66>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc'
 * '<S67>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc'
 * '<S68>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/FlgEconVbat_Calc/Dead Zone Dynamic'
 * '<S69>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/RpmIdleObj_State'
 * '<S70>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/fc_accCtrl'
 * '<S71>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/filt'
 * '<S72>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/outidle'
 * '<S73>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/rate'
 * '<S74>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/test'
 * '<S75>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/test1'
 * '<S76>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/fc_accCtrl/FOF_Reset_S16_FXP'
 * '<S77>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/fc_accCtrl/LookUp_U16_U16_1'
 * '<S78>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/fc_accCtrl/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S79>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/fc_accCtrl/LookUp_U16_U16_1/Data Type Conversion Inherited3'
 * '<S80>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/filt/FOF_Reset_S16_FXP'
 * '<S81>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/filt/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S82>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/rate/RateLimiter_S16'
 * '<S83>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/rate/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S84>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_Calc/test1/Reset_obj'
 * '<S85>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc/CAlc_rpmidleobj0'
 * '<S86>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc/RateLimiter_S16'
 * '<S87>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc/Unit Delay External IC'
 * '<S88>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc/CAlc_rpmidleobj0/LookUp_U16_S16_1'
 * '<S89>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc/CAlc_rpmidleobj0/LookUp_U16_S16_1/Data Type Conversion Inherited3'
 * '<S90>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/rpmtarget/RpmIdleObj0_raw_Calc/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S91>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/vtIDLTRQOFFSTP/Compare To Constant'
 * '<S92>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/vtIDLTRQOFFSTP/LookUp_S16_S16'
 * '<S93>'  : 'IdleMgm/IdleMgm/IdleMgm/Calc/vtIDLTRQOFFSTP/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S94>'  : 'IdleMgm/IdleMgm/IdleMgm/Reset/fc_reset'
 */

/*-
 * Requirements for '<Root>': IdleMgm
 */
#endif                                 /* RTW_HEADER_IdleMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
