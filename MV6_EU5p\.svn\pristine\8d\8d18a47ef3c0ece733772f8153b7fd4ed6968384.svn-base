/*
 * File: lambdamgm_private.h
 *
 * Code generated for Simulink model 'lambdamgm'.
 *
 * Model version                  : 1.1171
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jul  5 09:54:45 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#ifndef RTW_HEADER_lambdamgm_private_h_
#define RTW_HEADER_lambdamgm_private_h_
#include "rtwtypes.h"
#include "lambdamgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "engflag.h"
#include "lamheater_mgm.h"
#include "Addairmgm.h"
#include "ThrPosMgm.h"
#include "Syncmgm.h"
#include "af_ctrl.h"
#include "canmgm.h"
#include "Idle_mgm.h"
#include "idxctfctrl_out.h"
#include "digitalin.h"
#include "timing.h"
#include "loadmgm.h"
#include "Timing.h"
#include "temp_mgm.h"
#include "analogin.h"
#include "ptrain_diag.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int32_T RTVLAM2MAX;             /* Variable: RTVLAM2MAX
                                        * Referenced by: '<S18>/RTVLAM2MAX'
                                        * Rate max VLambda2 obd2 diagnosis
                                        */
extern int32_T RTVLAM2MIN;             /* Variable: RTVLAM2MIN
                                        * Referenced by: '<S18>/RTVLAM2MIN'
                                        * Rate min VLambda2 obd2 diagnosis
                                        */
extern int32_T RTVLAMMAX;              /* Variable: RTVLAMMAX
                                        * Referenced by: '<S37>/RTVLAMMAX'
                                        * Rate max VLambda obd2 diagnosis
                                        */
extern int32_T RTVLAMMIN;              /* Variable: RTVLAMMIN
                                        * Referenced by: '<S37>/RTVLAMMIN'
                                        * Rate min VLambda obd2 diagnosis
                                        */
extern uint32_T THRRATIOCATDIAG;       /* Variable: THRRATIOCATDIAG
                                        * Referenced by: '<S9>/THRRATIOCATDIAG'
                                        * Ratio thresholt diagnosis cat
                                        */
extern uint32_T MINODOCAT;             /* Variable: MINODOCAT
                                        * Referenced by: '<S9>/Chart'
                                        * Min Km to enable diagnosis cat
                                        */
extern uint32_T LAMOBD2TIMDIAG;        /* Variable: LAMOBD2TIMDIAG
                                        * Referenced by:
                                        *   '<S42>/Constant'
                                        *   '<S52>/LAMOBD2TIMDIAG'
                                        * Time to enable lambda functional diagnosis
                                        */
extern uint32_T NTDCCAT;               /* Variable: NTDCCAT
                                        * Referenced by: '<S9>/NTDCCAT'
                                        * Tdc to enable diagnosis cat
                                        */
extern int16_T LAMOBD2RMPMAX;          /* Variable: LAMOBD2RMPMAX
                                        * Referenced by: '<S55>/LAMOBD2RMPMAX'
                                        * Rpm max to enable lambda functional diagnosis
                                        */
extern int16_T LAMOBD2RMPMIN;          /* Variable: LAMOBD2RMPMIN
                                        * Referenced by: '<S55>/LAMOBD2RMPMIN'
                                        * Rpm min to enable lambda functional diagnosis
                                        */
extern int16_T LAMOBD2RPMHYS;          /* Variable: LAMOBD2RPMHYS
                                        * Referenced by: '<S55>/LAMOBD2RPMHYS'
                                        * Rpm hysteresis lambda signal stable conditions
                                        */
extern int16_T BKTHVLSTWAT[5];         /* Variable: BKTHVLSTWAT
                                        * Referenced by:
                                        *   '<S8>/BKTHVLSTWAT'
                                        *   '<S15>/BKTHVLSTWAT'
                                        *   '<S16>/BKTHVLSTWAT'
                                        * Bk: TBOFFSVLAMCRK, TBOFFSVLAM2CRK, VTTDCLAMBDAREADY
                                        */
extern int16_T LAMOBD2TWHYS;           /* Variable: LAMOBD2TWHYS
                                        * Referenced by: '<S57>/LAMOBD2TWHYS'
                                        * TWater hysteresis lambda signal stable conditions
                                        */
extern int16_T LAMOBD2TWMAX;           /* Variable: LAMOBD2TWMAX
                                        * Referenced by: '<S57>/LAMOBD2TWMAX'
                                        * Temperature max to enable lambda functional diagnosis
                                        */
extern int16_T LAMOBD2TWMIN;           /* Variable: LAMOBD2TWMIN
                                        * Referenced by: '<S57>/LAMOBD2TWMIN'
                                        * Temperature min to enable lambda functional diagnosis
                                        */
extern int16_T MINTWCAT;               /* Variable: MINTWCAT
                                        * Referenced by: '<S9>/MINTWCAT'
                                        * Min temperature to enable diagnosis cat
                                        */
extern int16_T LAMOBD2LOADHYS;         /* Variable: LAMOBD2LOADHYS
                                        * Referenced by: '<S53>/LAMOBD2LOADHYS'
                                        * Load hysteresis lambda signal stable conditions
                                        */
extern int16_T LAMOBD2LOADMAX;         /* Variable: LAMOBD2LOADMAX
                                        * Referenced by: '<S53>/LAMOBD2LOADMAX'
                                        * Load max to enable lambda functional diagnosis
                                        */
extern int16_T LAMOBD2LOADMIN;         /* Variable: LAMOBD2LOADMIN
                                        * Referenced by: '<S53>/LAMOBD2LOADMIN'
                                        * Load min to enable lambda functional diagnosis
                                        */
extern uint16_T LAMOBD2LEAN;           /* Variable: LAMOBD2LEAN
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Lambda1 obj obd2 threshold lean
                                        */
extern uint16_T LAMOBD2RICH;           /* Variable: LAMOBD2RICH
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Lambda1 obj obd2 threshold rich
                                        */
extern uint16_T KFFREQLAM;             /* Variable: KFFREQLAM
                                        * Referenced by: '<S12>/KFFREQLAM'
                                        * Filter constant for FreqOscLambda
                                        */
extern uint16_T MINCATSPEED;           /* Variable: MINCATSPEED
                                        * Referenced by: '<S9>/Chart'
                                        * Min vehicle speed to enable cat diagnosis
                                        */
extern uint16_T THRLAM2MINMAX;         /* Variable: THRLAM2MINMAX
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * Threshold for Lambda2 stuck diagnosis
                                        */
extern uint16_T THROBD2LAMLEAN;        /* Variable: THROBD2LAMLEAN
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Threshold VLambda1 obd2 diagnosis lean
                                        */
extern uint16_T THROBD2LAMRICH;        /* Variable: THROBD2LAMRICH
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Threshold VLambda1 obd2 diagnosis rich
                                        */
extern uint16_T BKLAMMGMLOAD[4];       /* Variable: BKLAMMGMLOAD
                                        * Referenced by:
                                        *   '<S21>/BKLAMMGMLOAD'
                                        *   '<S28>/BKLAMMGMLOAD'
                                        * Bk: TBTHRVLAMR2P, TBTHRVLAM2R2P
                                        */
extern uint16_T THRLAMOBD2LOADSTAB;    /* Variable: THRLAMOBD2LOADSTAB
                                        * Referenced by: '<S54>/THRLAMOBD2LOADSTAB'
                                        * Load threshold lambda signal stable conditions
                                        */
extern uint16_T VLAM2MAX;              /* Variable: VLAM2MAX
                                        * Referenced by: '<S14>/VLAM2MAX'
                                        * High threshold to detect short circuit of lamdba2 sensor to battery voltage
                                        */
extern uint16_T VLAM2OCMIN;            /* Variable: VLAM2OCMIN
                                        * Referenced by: '<S14>/VLAM2OCMIN'
                                        * Lower threshold to detect lambda2 sensor not connected
                                        */
extern uint16_T VLAMMAX;               /* Variable: VLAMMAX
                                        * Referenced by: '<S13>/VLAMMAX'
                                        * High threshold to detect short circuit of lamdba sensor to battery voltage
                                        */
extern uint16_T VLAMOCMIN;             /* Variable: VLAMOCMIN
                                        * Referenced by: '<S13>/VLAMOCMIN'
                                        * Lower threshold to detect lambda1 sensor not connected
                                        */
extern uint16_T BKLAMMGMRPM[6];        /* Variable: BKLAMMGMRPM
                                        * Referenced by:
                                        *   '<S21>/BKLAMMGMRPM'
                                        *   '<S28>/BKLAMMGMRPM'
                                        * Bk: TBTHRVLAMR2P, TBTHRVLAM2R2P
                                        */
extern uint16_T BKTHVLTDCCRK[4];       /* Variable: BKTHVLTDCCRK
                                        * Referenced by:
                                        *   '<S15>/BKTHVLTDCCRK'
                                        *   '<S16>/BKTHVLTDCCRK'
                                        * Bk: TBOFFSVLAMCRK, TBOFFSVLAM2CRK
                                        */
extern uint16_T ENDOBD2LAML2R;         /* Variable: ENDOBD2LAML2R
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Time to confitm transition L2R
                                        */
extern uint16_T ENDOBD2LAMR2L;         /* Variable: ENDOBD2LAMR2L
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Time to confitm transition R2L
                                        */
extern uint16_T LAMOBD2TDCCRK;         /* Variable: LAMOBD2TDCCRK
                                        * Referenced by:
                                        *   '<S40>/Constant'
                                        *   '<S46>/Constant'
                                        * Tdc passed to cranking to enable lambda functional diagnosis
                                        */
extern uint16_T THRCATDIAG;            /* Variable: THRCATDIAG
                                        * Referenced by: '<S9>/THRCATDIAG'
                                        * Threshold window of lambda transition to enable diagnosis cat
                                        */
extern uint16_T THRLAMOBD2RPMSTAB;     /* Variable: THRLAMOBD2RPMSTAB
                                        * Referenced by: '<S56>/THRLAMOBD2RPMSTAB'
                                        * Rpm threshold lambda signal stable conditions
                                        */
extern uint16_T THRR2PRESDIAG;         /* Variable: THRR2PRESDIAG
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * Number of lambda sensor transition to reset the functional diagnosis
                                        */
extern uint16_T THRVLAM2HYST;          /* Variable: THRVLAM2HYST
                                        * Referenced by: '<S5>/THRVLAM2HYST'
                                        * PROBE ON-OFF: Hysteresis for VLambdaState2 commutation
                                        */
extern uint16_T THRVLAMHYST;           /* Variable: THRVLAMHYST
                                        * Referenced by: '<S5>/THRVLAMHYST'
                                        * PROBE ON-OFF: Hysteresis for VLambdaState commutation
                                        */
extern uint16_T THRVLAMVERYPOOR;       /* Variable: THRVLAMVERYPOOR
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * VLambda value to set very rich-poor flag in poor state
                                        */
extern uint16_T THRVLAMVERYRICH;       /* Variable: THRVLAMVERYRICH
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * VLambda value to set very rich-poor flag in rich state
                                        */
extern uint16_T TIMCTOFFLAMRICH;       /* Variable: TIMCTOFFLAMRICH
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * Timout to validate the too rich fault in cutoff
                                        */
extern uint16_T TIMLAM2MINMAX;         /* Variable: TIMLAM2MINMAX
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * Time for Lambda2 stuck diagnosis
                                        */
extern uint16_T TIMLAMOBD2LOADSTAB;    /* Variable: TIMLAMOBD2LOADSTAB
                                        * Referenced by: '<S54>/TIMLAMOBD2LOADSTAB'
                                        * Load time lambda signal stable conditions
                                        */
extern uint16_T TIMLAMOBD2RPMSTAB;     /* Variable: TIMLAMOBD2RPMSTAB
                                        * Referenced by: '<S56>/TIMLAMOBD2RPMSTAB'
                                        * Rpm time lambda signal stable conditions
                                        */
extern uint16_T TIMNOSCLAM;            /* Variable: TIMNOSCLAM
                                        * Referenced by: '<S4>/VLambdaState_Calc'
                                        * Timout to declare that the lambda1 sensor is not oscillating
                                        */
extern uint16_T TIMOBD2LAML2R;         /* Variable: TIMOBD2LAML2R
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Timeout to confirm diagnosis lambda L2R
                                        */
extern uint16_T TIMOBD2LAMR2L;         /* Variable: TIMOBD2LAMR2L
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Timeout to confirm diagnosis lambda R2L
                                        */
extern uint16_T TIMOBD2LAMWDT;         /* Variable: TIMOBD2LAMWDT
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Timeout no transition lambda diagnosis
                                        */
extern int8_T TBOFFSVLAM2CRK[20];      /* Variable: TBOFFSVLAM2CRK
                                        * Referenced by: '<S16>/TBOFFSVLAM2CRK'
                                        * Offset on vlambda2 threshold for crank
                                        */
extern int8_T TBOFFSVLAMCRK[20];       /* Variable: TBOFFSVLAMCRK
                                        * Referenced by: '<S15>/TBOFFSVLAMCRK'
                                        * Offset on vlambda threshold for crank
                                        */
extern uint8_T TBTHRVLAM2R2P[24];      /* Variable: TBTHRVLAM2R2P
                                        * Referenced by: '<S16>/TBTHRVLAM2R2P'
                                        * PROBE ON-OFF: threshold for rich to poor commutation
                                        */
extern uint8_T TBTHRVLAMR2P[24];       /* Variable: TBTHRVLAMR2P
                                        * Referenced by: '<S15>/TBTHRVLAMR2P'
                                        * PROBE ON-OFF: threshold for poor to rich commutation
                                        */
extern uint8_T VTTDCLAMBDAREADY[5];    /* Variable: VTTDCLAMBDAREADY
                                        * Referenced by: '<S8>/VTTDCLAMBDAREADY'
                                        * Number of tdc to get lambda ready
                                        */
extern uint8_T VTTDCLAMBDAREADY2[5];   /* Variable: VTTDCLAMBDAREADY2
                                        * Referenced by: '<S8>/VTTDCLAMBDAREADY2'
                                        * Number of tdc to get lambda 2 ready
                                        */
extern uint8_T FOVLAM2RATE;            /* Variable: FOVLAM2RATE
                                        * Referenced by: '<S76>/FOVLAM2RATE'
                                        * Force VLambda2 rate
                                        */
extern uint8_T FOVLAMRATE;             /* Variable: FOVLAMRATE
                                        * Referenced by: '<S71>/FOVLAMRATE'
                                        * Force VLambda rate
                                        */
extern uint8_T NOBD2LAMDEC;            /* Variable: NOBD2LAMDEC
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Number of decrement disgnosis lambda1 plausubility test
                                        */
extern uint8_T NOBD2LAMINC;            /* Variable: NOBD2LAMINC
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Number of increment disgnosis lambda1 plausibility test
                                        */
extern uint8_T NOBD2LAMTST;            /* Variable: NOBD2LAMTST
                                        * Referenced by: '<S17>/Calc_Transitions'
                                        * Number of disgnosis lambda1 plausibility test
                                        */
extern uint8_T SELLAMMGMLOAD;          /* Variable: SELLAMMGMLOAD
                                        * Referenced by: '<S10>/SELLAMMGMLOAD'
                                        * Force Load input selection
                                        */
extern void lambdamgm_cal_freqosclam_Init(void);
extern void lambdamgm_cal_freqosclam(void);
extern void lambdamgm_Lambda_T5ms_Init(void);
extern void lambdamgm_Lambda_T5ms(void);
extern void lambdamgm_T5ms_Init(void);
extern void lambdamgm_T5ms(void);

#endif                                 /* RTW_HEADER_lambdamgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
