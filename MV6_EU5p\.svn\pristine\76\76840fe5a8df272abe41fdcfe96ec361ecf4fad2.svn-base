/**
 ******************************************************************************
 **  Filename:      C:\localmodules\local_TrqEst\slprj\ert\_sharedutils\div_repeat_u32_sat.h
 **  Date:          22-May-2019
 **
 **  Model Version: 1.1898
 ******************************************************************************
 **/

#ifndef SHARE_div_repeat_u32_sat
#define SHARE_div_repeat_u32_sat

extern uint32_T div_repeat_u32_sat(uint32_T numerator, uint32_T denominator,
  uint32_T nRepeatSub);

#endif

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 8.0 (R2012b)20-Jul-2012                                             *
 * Simulink 8.0 (R2012b)20-Jul-2012                                           *
 * Simulink Coder 8.3 (R2012b)20-Jul-2012                                     *
 * Embedded Coder 6.3 (R2012b)20-Jul-2012                                     *
 * Stateflow 8.0 (R2012b)20-Jul-2012                                          *
 * Simulink Fixed Point 7.2 (R2012b)20-Jul-2012                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed-point_blocks                                                         *
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
