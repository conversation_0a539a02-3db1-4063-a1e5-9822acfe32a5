/*
 * File: cmisatmgm_out.h
 *
 * Code generated for Simulink model 'CmiSatMgm'.
 *
 * Model version                  : 1.275
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Apr 19 15:02:55 2024
 */

#ifndef RTW_HEADER_cmisatmgm_out_h_
#define RTW_HEADER_cmisatmgm_out_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define AW_ACC_GEARUP                  4U

/* AW_ACC_GEARUP */
#define AW_ACC_GEARUP_FILT             5U

/* AW_ACC_GEARUP_FILT */
#define ST_RETURN_AW                   3U

/* ST_RETURN_AW */
#define ST_SAT_AW                      1U

/* ST_SAT_AW */
#define ST_SAT_AW_SMOOTH               2U

/* ST_SAT_AW_SMOOTH */
#define ST_SAT_MAX                     0U

/* ST_SAT_MAX */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T AccPitch;

/* Pitch acc */
extern int16_T AccPitchDeep;

/* Pitch acc */
extern uint16_T CmeEstTcWheel;

/* CmeEstTcWheel */
extern uint16_T CmeEstWcWheel;

/* CmeEstTcWheel */
extern int16_T CmeEstWcWheelSat;

/* CmeEstWcWheelSat */
extern uint16_T CmeGainSatP;

/* Gain control P */
extern int16_T CmeMainSatP;

/* CmeMainSatP */
extern int16_T CmiAwCtrlD;

/* Cmi control D */
extern int16_T CmiAwCtrlP;

/* Cmi control P */
extern int16_T CmiAwCtrlSatSmI;

/* Cmi control sat */
extern int16_T CmiAwCtrlSatSmP;

/* Cmi control sat */
extern int16_T CmiMainSatI;

/* Cmi main strategy */
extern int16_T CmiMainSatIInit;

/* Cmi init strategy */
extern int16_T CmiMainSatP;

/* Cmi main strategy */
extern int16_T CmiMainSatPInit;

/* Cmi init strategy */
extern int16_T CmiSatAwI;

/* CmiSatAwI */
extern int16_T CmiSatAwP;

/* CmiSatAwP */
extern int16_T CmiSatI;

/* CmiSatP */
extern int16_T CmiSatP;

/* CmiSatP */
extern int32_T CmiStepAwCtrlD;

/* step control D */
extern int32_T CmiStepAwCtrlP;

/* step control P */
extern int32_T CmiStepIAwCtrlKD;

/* step control WP */
extern int32_T CmiStepIAwCtrlKP;

/* step control WI */
extern int32_T CmiStepPAwCtrlKD;

/* step control WP */
extern int32_T CmiStepPAwCtrlKP;

/* step control WP */
extern int16_T CtfAwThr;

/* Pitch Error */
extern int16_T DeltaPitch;

/* Pitch delta */
extern int16_T DeltaPitchDeep;

/* Pitch delta */
extern int16_T DeltaPitchDeepInit;

/* Pitch delta */
extern int16_T ErrPitchAw;

/* Pitch Error */
extern int16_T ErrPitchAwInit;

/* Pitch Error */
extern uint8_T FlgCmiSatP;

/* flag cutoff */
extern uint8_T FlgCtfAw;

/* flag cutoff */
extern uint8_T FlgDeltaPitch;

/* flag speed control */
extern uint8_T FlgKDEn;

/* flag */
extern int16_T GainAwCtrlP;

/* Gain control P */
extern uint8_T IdxAwCutOff;

/* idx cutoff */
extern int16_T MaxRateAwRet;

/* rate max */
extern int16_T PitchCANInit;

/* Pitch init strategy */
extern int16_T PitchInitTrg;

/* Pitch init strategy */
extern uint8_T StSatAw;

/* status of AW */
extern int16_T VtAPitch[35];

/* Acc pitch */
extern int16_T VtDPitch[35];

/* Delta pitch */
#endif                                 /* RTW_HEADER_cmisatmgm_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
