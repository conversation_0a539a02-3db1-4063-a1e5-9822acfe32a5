/*
 * File: div_su32.c
 *
 * Code generated for Simulink model 'PAtmModel'.
 *
 * Model version                  : 1.764
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jun 21 11:58:30 2022
 */

#include "rtwtypes.h"
#include "div_su32.h"

int32_T div_su32(uint32_T numerator, uint32_T denominator)
{
  int32_T quotient;
  if (denominator == 0U) {
    quotient = MAX_int32_T;

    /* Divide by zero handler */
  } else {
    quotient = (int32_T)(numerator / denominator);
  }

  return quotient;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
