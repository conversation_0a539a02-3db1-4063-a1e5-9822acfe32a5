/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_TRC2WZERO_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Db stab [Km/h]
CALQUAL uint16_T DBTC2WZERO = 32u;   //(  2.0000*16)
//Db stab [Km/h]
CALQUAL uint16_T DBTC2WZERO2 = 13u;   //(  0.8125*16)
//gain [gain]
CALQUAL uint16_T DEFTCZERO = 1024u;   //(1.0000000000*1024)
//selector [flag]
CALQUAL uint8_T ENTC2WZERORET =  0u;   // 0
//selector [flag]
CALQUAL uint8_T ENTRC2WZERO =  1u;   // 1
//selector [flag]
CALQUAL uint8_T FORCETC2WZTST =  0u;   // 0
//roll max [deg]
CALQUAL uint16_T MAXROLLTC2WZ = 1200u;   //( 12.00000000000000000000*100)
//Thr max [Km/h]
CALQUAL uint16_T THRTC2WZEROHI = 672u;   //( 42.0000*16)
//Thr min [Km/h]
CALQUAL uint16_T THRTC2WZEROLO = 608u;   //( 38.0000*16)
//counter [ms]
CALQUAL uint8_T TIMTC2WZERO =  7u;   // 7
//counter [ms]
CALQUAL uint16_T TIMTC2WZSTAB =    200u;   //   200
//counter [ms]
CALQUAL uint16_T TIMTC2WZSTAB2 =    300u;   //   300
//tim [ms]
CALQUAL uint16_T TIMTPMSNODE =  150u;   // 150
//tollerance gain max [gain]
CALQUAL uint16_T TOLLTCZEROMAX = 1044u;   //(1.0195312500*1024)
//tollerance gain min [gain]
CALQUAL uint16_T TOLLTCZEROMIN = 1004u;   //(0.9804687500*1024)

#endif /* _BUILD_TRC2WZERO_ */

