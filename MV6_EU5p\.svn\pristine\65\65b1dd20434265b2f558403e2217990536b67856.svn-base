/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_CMISATMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//selector [counter]
CALQUAL uint8_T AWCTFPITCH =  3u;   // 3
//selector [counter]
CALQUAL uint8_T AWCTFSA =  1u;   // 1
//RollCAN breakpoint [deg]
CALQUAL uint16_T BKAWGAINRL[5] = 
{
 500u, 1000u, 1500u, 2000u, 5000u
};
//VehSpeed breakpoint [Km/h]
CALQUAL uint16_T BKAWGAINVS[6] = 
{
 0u, 800u, 1600u, 1920u, 2400u, 3200u
};
//Bk CmeEstWheelF saturation [Rpm]
CALQUAL uint16_T BKCMEEWAWSAT[7] = 
{
   1000u,   2000u,   3000u,   4000u,   5000u,   6000u,   7000u
};
//breakpoint [deg]
CALQUAL int16_T BKDPITCHAWCTRL[8] = 
{
 -200, -100, -50, -20, -10, 0, 10, 20
};
//breakpoint [deg]
CALQUAL int16_T BKERRPITCHAWCTRL[10] = 
{
 -4000, -3000, -2000, -1000, -500, -200, -100, -50, 0, 200
};
//Smooth BK [deg]
CALQUAL int16_T BKMRTDPSMOOTH[5] = 
{
 -100, 0, 200, 500, 1000
};
//sat [Nm]
CALQUAL int16_T CMIMINAWSACTF = 320;   //(  10.00000*32)
//Offset [Nm]
CALQUAL int16_T CMIOFSAWI = -48;   //(  -1.50000*32)
//selector [flag]
CALQUAL uint8_T DISAWCTRLINT =  1u;   // 1
//selector [flag]
CALQUAL uint8_T DISAWSMFRZINIT =  0u;   // 0
//Deltapitch deep [counter]
CALQUAL uint8_T DPITCH =   3u;   //  3
//Deltapitch deep [counter]
CALQUAL uint8_T DPITCHDEEP =  15u;   // 15
//Control refresh deep [counter]
CALQUAL uint8_T DPITCHRFS =   3u;   //  3
//Control refresh deep [counter]
CALQUAL uint8_T DPITCHRSMOOTH =  20u;   // 20
//Exit Aw strategy [Nm]
CALQUAL int16_T HYSCMISATAWRET = 128;   //(   4.00000*32)
//speed control [deg/sample]
CALQUAL int16_T HYSDPITCH = 50;   //(  0.50000000000000000000*100)
//selector [gain]
CALQUAL int16_T KDAWWEIGTHI = 32;   //(1.00000*32)
//selector [gain]
CALQUAL int16_T KDAWWEIGTHP = 32;   //(1.00000*32)
//selector [gain]
CALQUAL int16_T KPAWWEIGTHI = 32;   //(1.00000*32)
//selector [gain]
CALQUAL int16_T KPAWWEIGTHP = 32;   //(1.00000*32)
//max rete aw [Nm]
CALQUAL int16_T MAXRATEAWRET = 10;   //(   0.31250*32)
//max rete aw [Nm]
CALQUAL int16_T MINRATEAWRET = -2;   //(  -0.06250*32)
//mult step ctrl [counter]
CALQUAL uint8_T MULPRFS =   8u;   //  8
//Exit Aw GearUp [Nm]
CALQUAL int16_T OFFCMIGRUFILAW = 128;   //(   4.00000*32)
//selector [flag]
CALQUAL uint8_T SELBKDSMOOTH =  1u;   // 1
//selector [counter]
CALQUAL uint8_T SELTHRDPITCH =  0u;   // 0
//gain sat [gain]
CALQUAL uint16_T TBCMEAWGAIN[5*6] = 
{
 64u, 64u, 64u, 64u, 64u, 64u,
 128u, 128u, 128u, 128u, 128u, 128u,
 128u, 128u, 128u, 128u, 128u, 128u,
 192u, 192u, 192u, 192u, 192u, 192u,
 256u, 256u, 256u, 256u, 256u, 256u
};
//Tc Sat [Nm]
CALQUAL int16_T TBCMEEWTCSAT[7*9] = 
{
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368,
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368,
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368,
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368,
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368,
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368,
 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368, 16368
};
//Delta Pitch deep threshold [deg/sample]
CALQUAL int16_T THRDPDSTART = -500;   //( -5.00000000000000000000*100)
//speed control [deg/sample]
CALQUAL int16_T THRDPITCH = 20;   //(  0.20000000000000001110*100)
//Tim exit aw [ms]
CALQUAL uint8_T TIMAWRETURN =    5u;   //   5
//Pitch cutoff threshold [deg]
CALQUAL int16_T VTAWCUTOFFTHR[6] = 
{
 -1500, -2000, -2000, -2000, -2500, -3000
};
//Cmi gain control for VehSpeed [Nm]
CALQUAL int16_T VTAWGAINVS[6] = 
{
 256, 256, 256, 256, 256, 256
};
//Offset sat level [Nm]
CALQUAL int16_T VTAWOFSSATLEV[4] = 
{
 0, 96, 64, 0
};
//CmeEstWheelF saturation [Nm]
CALQUAL int16_T VTCMEEWAWSAT[7] = 
{
 800, 1280, 1600, 1920, 2240, 2560, 2880
};
//Cmi step Aw ctrl D [Nm]
CALQUAL int16_T VTCMIAWCTRLD[8] = 
{
 -3840, -2560, -1280, -768, -512, 0, 1024, 1920
};
//Cmi Aw ctrl P [Nm/deg]
CALQUAL int16_T VTCMIAWCTRLP[10] = 
{
 -3072, -1536, -819, -614, -410, -307, -205, -154, 102, 102
};
//DB Level [deg]
CALQUAL int16_T VTDBDPITCHLEV[4] = 
{
 0, 100, 200, 300
};
//Cmi gain Aw ctrl P [gain]
CALQUAL int16_T VTGAINAWCTRLP[10] = 
{
 256, 256, 256, 256, 256, 256, 256, 256, 256, 256
};
//Gear gain control [gain]
CALQUAL int16_T VTGAINAWGEAR[7] = 
{
 256, 256, 256, 256, 256, 256, 256
};
//Gear gain control [rate max]
CALQUAL int16_T VTMRTDPSMOOTH[5] = 
{
 2, 32, 64, 96, 320
};
//Cmi offset control level [Nm]
CALQUAL int16_T VTOFFCMIAWLEVEL[4] = 
{
 0, 32, 64, 96
};
//AW Level target [deg]
CALQUAL int16_T VTPITCHAWTRG[4] = 
{
 -2000, 100, 200, 300
};

#endif /* _BUILD_CMISATMGM_ */

