/*
 * File: GearMgm.h
 *
 * Code generated for Simulink model 'GearMgm'.
 *
 * Model version                  : 1.903
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb 28 14:57:43 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GearMgm_h_
#define RTW_HEADER_GearMgm_h_
#ifndef GearMgm_COMMON_INCLUDES_
# define GearMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "diagmgm_out.h"
#include "mathlib.h"
#endif                                 /* GearMgm_COMMON_INCLUDES_ */

#include "GearMgm_types.h"

/* Includes for objects with custom storage classes. */
#include "DigitalIn.h"
#include "cmefilter_mgm.h"
#include "DiagMgm_out.h"
#include "gear_mgm.h"
#include "trac_ctrl.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define ID_GEAR_POS                    31927042U                 /* Referenced by: '<S2>/Gear1' */

/* mask */
#define NUM_GR_BUFF                    8U                        /* Referenced by: '<S22>/Chart_Update_GearRatio' */

/* Block signals for system '<S9>/fc_Diag' */
typedef struct {
  uint8_T DiagMgm_SetDiagState;        /* '<S17>/DiagMgm_SetDiagState' */
} rtB_fc_Diag_GearMgm;

/* Block signals for system '<S22>/Calc_Stab_GearRatio' */
typedef struct {
  uint8_T SteadyStateDetect_o1;        /* '<S31>/SteadyStateDetect' */
} rtB_Calc_Stab_GearRatio_GearMgm;

/* Block states (default storage) for system '<S22>/Calc_Stab_GearRatio' */
typedef struct {
  uint16_T Memory_PreviousInput;       /* '<S31>/Memory' */
  uint16_T Memory1_PreviousInput;      /* '<S31>/Memory1' */
  uint8_T Memory_PreviousInput_iaon;   /* '<S29>/Memory' */
} rtDW_Calc_Stab_GearRatio_GearMg;

/* Block signals (default storage) */
typedef struct {
  uint16_T roduct1;                    /* '<S21>/roduct1' */
  uint16_T gearRatioStart;             /* '<S22>/Chart_Update_GearRatio' */
  uint8_T GearPosRec_fxmu;             /* '<S18>/Switch' */
  uint8_T ssReset;                     /* '<S22>/Chart_Update_GearRatio' */
  uint8_T tmpGearPos;                  /* '<S22>/Chart_Update_GearRatio' */
  uint8_T output;                      /* '<S18>/Chart' */
  uint8_T FoGearDownSignal_mnjm;       /* '<S14>/Double_Blip' */
  uint8_T FoGearUpSignal_jnuy;         /* '<S14>/Double_Blip' */
  uint8_T FoTrgQS_nju5;                /* '<S14>/Double_Blip' */
  uint8_T ptFault;                     /* '<S9>/Diag_Gear' */
  uint8_T GearPos_ohmm;                /* '<S9>/DetectGearPos' */
  rtB_Calc_Stab_GearRatio_GearMgm Calc_Stab_GearRatio;/* '<S22>/Calc_Stab_GearRatio' */
  rtB_fc_Diag_GearMgm fc_Diag;         /* '<S9>/fc_Diag' */
} BlockIO_GearMgm;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  uint32_T Memory_PreviousInput;       /* '<S21>/Memory' */
  struct {
    uint_T is_GEAR_RATIO_CALC:3;       /* '<S22>/Chart_Update_GearRatio' */
    uint_T is_ON:3;                    /* '<S14>/Double_Blip' */
    uint_T is_DIAG_PLAUSIBILITY:3;     /* '<S9>/Diag_Gear' */
    uint_T is_c5_GearMgm:2;            /* '<S22>/Chart_Update_GearRatio' */
    uint_T is_c6_GearMgm:2;            /* '<S14>/Double_Blip' */
    uint_T is_c1_GearMgm:2;            /* '<S9>/DetectGearPos' */
    uint_T is_active_c5_GearMgm:1;     /* '<S22>/Chart_Update_GearRatio' */
    uint_T is_active_c6_GearMgm:1;     /* '<S14>/Double_Blip' */
    uint_T is_active_c3_GearMgm:1;     /* '<S9>/Diag_Gear' */
    uint_T is_active_c1_GearMgm:1;     /* '<S9>/DetectGearPos' */
  } bitsForTID0;

  uint16_T cnt;                        /* '<S9>/Diag_Gear' */
  uint8_T i;                           /* '<S22>/Chart_Update_GearRatio' */
  uint8_T oldGearPos;                  /* '<S22>/Chart_Update_GearRatio' */
  uint8_T cnt_kwoq;                    /* '<S18>/Chart' */
  uint8_T input_old;                   /* '<S18>/Chart' */
  uint8_T Memory_PreviousInput_dhiq;   /* '<S9>/Memory' */
  uint8_T cnt_i23r;                    /* '<S14>/Double_Blip' */
  uint8_T oldGearPos_p4ow;             /* '<S14>/Double_Blip' */
  uint8_T updn;                        /* '<S14>/Double_Blip' */
  uint8_T flgEleDiagOn;                /* '<S9>/Diag_Gear' */
  uint8_T flgPlaDiagOn;                /* '<S9>/Diag_Gear' */
  uint8_T flgDiag;                     /* '<S9>/Diag_Gear' */
  uint8_T oldDrivingCycle;             /* '<S9>/Diag_Gear' */
  uint8_T ptFaultPla;                  /* '<S9>/Diag_Gear' */
  uint8_T oldGearPosRec;               /* '<S9>/Diag_Gear' */
  uint8_T cnt_bmg2;                    /* '<S9>/DetectGearPos' */
  uint8_T gearPos;                     /* '<S9>/Calc_Thresholds_CL' */
  uint8_T trigUpDn;                    /* '<S9>/Calc_Thresholds_CL' */
  rtDW_Calc_Stab_GearRatio_GearMg Calc_Stab_GearRatio;/* '<S22>/Calc_Stab_GearRatio' */
} D_Work_GearMgm;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_GearMgm_T5ms_Trig_ZCE; /* '<S1>/fc_GearMgm_T5ms' */
  ZCSigState fc_GearMgm_Init_Trig_ZCE; /* '<S1>/fc_GearMgm_Init' */
} PrevZCSigStates_GearMgm;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T5ms;                     /* '<Root>/ev_T5ms' */
} ExternalInputs_GearMgm;

/* Block signals (default storage) */
extern BlockIO_GearMgm GearMgm_B;

/* Block states (default storage) */
extern D_Work_GearMgm GearMgm_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_GearMgm GearMgm_U;

/* Model entry point functions */
extern void GearMgm_initialize(void);
extern void GearMgm_step(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S7>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Propagation' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S22>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion5' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('gearmgm_gen/GearMgm')    - opens subsystem gearmgm_gen/GearMgm
 * hilite_system('gearmgm_gen/GearMgm/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'gearmgm_gen'
 * '<S1>'   : 'gearmgm_gen/GearMgm'
 * '<S2>'   : 'gearmgm_gen/GearMgm/Init'
 * '<S3>'   : 'gearmgm_gen/GearMgm/T5ms'
 * '<S4>'   : 'gearmgm_gen/GearMgm/fc_GearMgm_Init'
 * '<S5>'   : 'gearmgm_gen/GearMgm/fc_GearMgm_T5ms'
 * '<S6>'   : 'gearmgm_gen/GearMgm/Init/Calc_GR_Threshold'
 * '<S7>'   : 'gearmgm_gen/GearMgm/Init/Calc_InvGearRatio'
 * '<S8>'   : 'gearmgm_gen/GearMgm/Init/Init_GearRatio'
 * '<S9>'   : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos'
 * '<S10>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited'
 * '<S11>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/Calc_Thresholds_CL'
 * '<S12>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/DetectGearPos'
 * '<S13>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/Diag_Gear'
 * '<S14>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/Double_Blip'
 * '<S15>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/fc_Diag'
 * '<S16>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/Double_Blip/Double_Blip'
 * '<S17>'  : 'gearmgm_gen/GearMgm/T5ms/CalcGearPos/fc_Diag/SetDiagState'
 * '<S18>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Assign_GearPosRec'
 * '<S19>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Calc_Diag'
 * '<S20>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Calc_GearPosRec'
 * '<S21>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Calc_GearRatio'
 * '<S22>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Learning_GearRatio'
 * '<S23>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Assign_GearPosRec/Chart'
 * '<S24>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Calc_GearRatio/Compare To Zero'
 * '<S25>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Calc_GearRatio/FOF_Reset_S16_FXP'
 * '<S26>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Calc_GearRatio/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S27>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Learning_GearRatio/Calc_GR_Threshold'
 * '<S28>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Learning_GearRatio/Calc_InvGearRatio'
 * '<S29>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Learning_GearRatio/Calc_Stab_GearRatio'
 * '<S30>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Learning_GearRatio/Chart_Update_GearRatio'
 * '<S31>'  : 'gearmgm_gen/GearMgm/T5ms/GearPos Reconstruited/Learning_GearRatio/Calc_Stab_GearRatio/Steady_State_Detect'
 */

/*-
 * Requirements for '<Root>': GearMgm
 */
#endif                                 /* RTW_HEADER_GearMgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
