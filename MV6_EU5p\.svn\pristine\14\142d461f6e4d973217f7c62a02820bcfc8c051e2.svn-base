/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "cpumgm.h"
#include "selfmgm.h"
#include "timing.h"
#include "recmgm.h"
#include "selfmgm_ee.h"

#ifdef  MATLAB_MEX_FILE
#define _BUILD_SELFMGM_
#endif

/* variables in EEPROM */
extern uint16_T VAngThrMin1;
extern uint16_T VAngThrMin2;
extern uint16_T VAngThrLh1;
extern uint16_T VAngThrLh2;
extern uint8_T  StDbwSelfError;
extern uint8_T  FlgSelfDisable;

/* Calibrazioni */
extern uint8_T  ENSELFMGM;
extern uint8_T  NSELFSAMPLE;
extern uint16_T SELFTIME0;
extern uint16_T SELFTIME2;
extern uint16_T SELFTIME4;
extern uint16_T VANGTHRLHMAX1;
extern uint16_T VANGTHRLHMAX2;
extern uint16_T VANGTHRLHMIN1;
extern uint16_T VANGTHRLHMIN2;
extern uint16_T VANGTHRMNMAX1;
extern uint16_T VANGTHRMNMAX2;
extern uint16_T VANGTHRMNMIN1;
extern uint16_T VANGTHRMNMIN2;
extern uint16_T VBATSELFMIN;
extern int16_T  VSLDBWCLOSE;
//extern int16_T  VSLDBWSTP;
extern uint16_T KFVDBW;        
extern uint16_T RPMSELFTHR;
extern uint16_T FORCEDVANGTHRMIN1;
extern uint16_T FORCEDVANGTHRMIN2;
extern int16_T  VDBWSTARTCLOSE ; 


/* Functions */
void ForceVAngThrMin(void);
void SelfMgm_StDbwSelf(void);
void SelfMgm_FlgSelfDisable(void);
void SelfMgm_ExtSelfRequest(void);

//const uint16_T KFVDBW = 1<<11 /*(1<<13)*/;
//const int16_T VDBWSTARTCLOSE = -10<<10; 

/* Output variables */
uint8_T     FlgSelfLearning = 0;
uint8_T     FlgSelf_FirstDone;
int16_T     VDbwIn;
int32_T     VDbwIn_HR;

void    ForceVAngThrMin(void)
{
    if (FORCEDVANGTHRMIN1 != 0)
    {
        VAngThrMin1 = FORCEDVANGTHRMIN1;
    }    
    if (FORCEDVANGTHRMIN2 != 0)
    {
        VAngThrMin2 = FORCEDVANGTHRMIN2;
    }    
}

#ifdef _BUILD_SELFMGM_

/* Variabili */
typStDbwSelf StDbwSelf;
uint64_T SelfTimer;
uint8_T  CntSelfSample; 
uint32_T tmpVAngThrLh1;
uint32_T tmpVAngThrLh2;
uint32_T tmpVAngThrMin1;
uint32_T tmpVAngThrMin2;
uint8_T  FlgSelfRequest;

void SelfMgm_Init(void)
{   
    if(ENSELFMGM != 0) 
    {
        /* In caso di dati in EEPROM corrotti */
        FlgSelfLearning = 1;
        FlgSelf_FirstDone=1;
        VDbwIn = 0;
        StDbwSelf = DBW_SELF_INIT;
    } 
    else 
    {
        /* Solo in caso di self learning disabilitato */
        FlgSelfLearning = 0;
        FlgSelf_FirstDone=1; 
        VDbwIn = 0;
        StDbwSelf = DBW_SELF_END;
    }
    StDbwSelfError = DBW_SELF_NO_ERROR;
    CntSelfSample = 0;
    tmpVAngThrLh1 = 0;
    tmpVAngThrLh2 = 0;
    TIMING_GetAbsTimer(&SelfTimer);   
}

void SelfMgm_T5ms(void)
{
    static uint8_T selfFirstKeyOn = 0;
    ForceVAngThrMin();

    if (KeySignal != 0)
    {
        selfFirstKeyOn = 1;
    }
    else
    {
        /* Attendi la stabilizzazione elettrica del sistema. */
    }
    if(((FlgSelfLearning !=0) || (FlgSelfRequest != 0)) && (selfFirstKeyOn != 0))
    {
        SelfMgm_FlgSelfDisable();
        SelfMgm_StDbwSelf();
    }
}

void SelfMgm_FlgSelfDisable(void)
{
    if(KeySignal == 0) 
    {
        /* DISABILITAZIONE PER CHIAVE OFF */
        FlgSelfDisable = NOSELF_KEYOFF_COND;
    }
    else if(VBattery < VBATSELFMIN)
    {
        /* DISABILITAZIONE PER TENSIONE BATTERIA */
        FlgSelfDisable = NOSELF_VBATT_COND;   
    }
    else if(VtRec[REC_NO_DBW_SELF] != 0)
    {   
        /* DISABILITAZIONE PER RECOVERY "SELF LEARNING NON POSSIBILE" */
        FlgSelfDisable = NOSELF_REC_NO_DBW_COND;   
    }
    else if(VtRec[REC_DBW_OFF] != 0)
    {   
         /* DISABILITAZIONE PER RECOVERY "DBW OFF" */
        FlgSelfDisable = NOSELF_REC_DBW_OFF_COND;   
    }
    else if(SelfLearning_Flag_Busy == BUSY)
    {
        if((StDbwSelf == DBW_SELF_END) ||(StDbwSelf == DBW_SELF_FAILED))
        {
            /* ABILITAZIONE FORZATA DEL SELF-LEARNING DA DIAGNOSI ATTIVA 
            SE NON E' IN CORSO UN ALTRO SELF-LEARNING*/
            FlgSelfDisable = SELF_NORMAL_COND;
        }
    }
    else if(ENSELFMGM == 0)
    {
        /* DISABILITAZIONE DA CALIBRAZIONE */
        FlgSelfDisable = NOSELF_FORCE_DIS_COND;
    }
    else if(Rpm > RPMSELFTHR)
    {
        /* DISABILITAZIONE PER GIRI MOTORE*/
        FlgSelfDisable = NOSELF_RPM_COND;   
    }
    else if(PowerOnType != PWON_NORMAL)
    {
        /* DISABILITAZIONE PER TIPO DI POWER-ON*/
        FlgSelfDisable = NOSELF_POWERON_COND;   
    }
    else
    {
        /* ABILITAZIONE SELF-LEARNING */
        FlgSelfDisable = SELF_NORMAL_COND;
    }
}

void SelfMgm_StDbwSelf(void)
{
    uint64_T timer;

    TIMING_GetAbsTimer(&timer); 
    TIMING_TicksToMilliSeconds((timer-SelfTimer), &timer);

    switch(StDbwSelf)
    {
        case DBW_SELF_INIT:         
            if((FlgSelfDisable != SELF_NORMAL_COND) && (FlgSelfDisable != NOSELF_VBATT_COND))
            {
                // Self fallito per disabilitazione condizioni generali
                StDbwSelfError = DBW_SELF_INIT_ERROR;
                StDbwSelf = DBW_SELF_FAILED;
                VDbwIn = 0;
                FlgSelfLearning = 0;
                FlgSelf_FirstDone=0;
            }
            else 
            {
                /* Attende un timer per evitare fallimenti dovuti a problemi di inizializzazione. 
                 Inoltre attende che il controllo sia pronto ad eseguire il self-leraning */
                if((timer >= SELFTIME0) && (StDbwCtrl == DBW_SELF_LEARNING) && (S3FlgAllowStart != 0))
                {
                    TIMING_GetAbsTimer(&SelfTimer);
                    StDbwSelf = DBW_SELF_CLOSING;
                    VDbwIn = VDBWSTARTCLOSE ; /* [2^-10 V] */
                    VDbwIn_HR = VDBWSTARTCLOSE<<14; /* [2^-24 V] */
                }
                else
                {
                }
            }      
            break;
        case DBW_SELF_CLOSING:
            if(FlgSelfDisable != SELF_NORMAL_COND)
            {
                /* Self fallito per disabilitazione condizioni generali */
                StDbwSelfError = DBW_SELF_CLOSING_ERROR;
                StDbwSelf = DBW_SELF_FAILED;
                VDbwIn = 0;
                FlgSelfLearning = 0;
                FlgSelf_FirstDone=0;
            }
            else if((timer >= SELFTIME2))
            {
                StDbwSelf = DBW_SELF_CLOSED;
                VDbwIn = VSLDBWCLOSE;
                TIMING_GetAbsTimer(&SelfTimer); 
                CntSelfSample = 0;
                tmpVAngThrMin1 = 0;
                tmpVAngThrMin2 = 0;
            }
            else 
            {
                /* void FOF_Reset_S16_FXP(int16_T *Y, int32_T *Y_hires, int16_T U, const uint16_T KFILT, int16_T RESETVALUE, uint8_T EVRESET, int32_T Y_hires_old); */
                FOF_Reset_S16_FXP(&VDbwIn, &VDbwIn_HR, VSLDBWCLOSE, KFVDBW, 0, FALSE, VDbwIn_HR);
            }    
            break;
        
        case DBW_SELF_CLOSED:
            VDbwIn = VSLDBWCLOSE;
            if(FlgSelfDisable != SELF_NORMAL_COND)
            {
                // Self fallito per disabilitazione condizioni generali
                StDbwSelfError = DBW_SELF_CLOSED_ERROR;
                StDbwSelf = DBW_SELF_FAILED;
                VDbwIn = 0;
                FlgSelfLearning = 0;
                FlgSelf_FirstDone=0;
            }
            else 
            {
                if((VAngThrottle1 > VANGTHRMNMIN1) && (VAngThrottle1 < VANGTHRMNMAX1) &&
                 (VAngThrottle2 > VANGTHRMNMIN2) && (VAngThrottle2 < VANGTHRMNMAX2)) 
                {
                    // Incremento i contatori di campioni memorizzati per apprendimento
                    CntSelfSample++;
                    tmpVAngThrMin1 += VAngThrottle1;
                    tmpVAngThrMin2 += VAngThrottle2;
                    
                    if(CntSelfSample >= NSELFSAMPLE)
                    {
                        // Media dei campioni memorizzati e passaggio alla fase succesiva
                        tmpVAngThrMin1 = tmpVAngThrMin1 / CntSelfSample;
                        tmpVAngThrMin2 = tmpVAngThrMin2 / CntSelfSample;
                        StDbwSelf = DBW_SELF_RELEASE;
                        TIMING_GetAbsTimer(&SelfTimer); 
                        VDbwIn = 0;
                    }
                }
                else
                {
                    // Self fallito per angolo farfalla non compreso nel range di battuta minima
                    StDbwSelfError = DBW_SELF_LMS_ERROR;
                    StDbwSelf = DBW_SELF_FAILED;
                    VDbwIn = 0;
                    FlgSelfLearning = 0;
                    FlgSelf_FirstDone=0;
                }
            }           
            break;
        case DBW_SELF_RELEASE:
            VDbwIn = 0;
            
            if (FlgSelfDisable != SELF_NORMAL_COND)
            {
                // Self fallito per disabilitazione condizioni generali
                StDbwSelfError = DBW_SELF_RELEASE_ERROR;
                StDbwSelf = DBW_SELF_FAILED;
                VDbwIn = 0;
                FlgSelfLearning = 0;
                FlgSelf_FirstDone = 0;
            }
            else
            {
                if (timer >= SELFTIME4)
                {
                    StDbwSelf = DBW_SELF_RELEASED;
                    CntSelfSample = 0;
                    tmpVAngThrLh1 = 0;
                    tmpVAngThrLh2 = 0;
                }
                else /* MISRA rule 14.10 */
                {
                }
            }
            break;
        case DBW_SELF_RELEASED: 
            VDbwIn = 0;
            if(FlgSelfDisable != SELF_NORMAL_COND)
            {
                // Self fallito per disabilitazione condizioni generali
                StDbwSelfError = DBW_SELF_RELEASED_ERROR;
                StDbwSelf = DBW_SELF_FAILED;
                VDbwIn = 0;
                FlgSelfLearning = 0;
                FlgSelf_FirstDone=0;
            }
            else 
            {
                if(    (VAngThrottle1 > VANGTHRLHMIN1) && (VAngThrottle1 < VANGTHRLHMAX1) && 
                    (VAngThrottle2 > VANGTHRLHMIN2) && (VAngThrottle2 < VANGTHRLHMAX2)) 
                {
                    // Incremento i contatori di campioni memorizzati per apprendimento
                    CntSelfSample++;
                    tmpVAngThrLh1 += VAngThrottle1;
                    tmpVAngThrLh2 += VAngThrottle2;
                    if ( CntSelfSample >= NSELFSAMPLE)
                    {
                        StDbwSelf = DBW_SELF_END;
                        StDbwSelfError = DBW_SELF_NO_ERROR;
                        VDbwIn = 0;
                        FlgSelfLearning = 0;
                        FlgSelf_FirstDone = 0;
                        /* Media dei campioni memorizzati e passaggio alla fase successiva */
                        tmpVAngThrLh1 = tmpVAngThrLh1 / CntSelfSample;
                        tmpVAngThrLh2 = tmpVAngThrLh2 / CntSelfSample;
                        VAngThrLh1  = (uint16_T)tmpVAngThrLh1;
                        VAngThrLh2  = (uint16_T)tmpVAngThrLh2;
                        VAngThrMin1 = (uint16_T)tmpVAngThrMin1;
                        VAngThrMin2 = (uint16_T)tmpVAngThrMin2;
                    }
                }
                else
                {
                    /* Self fallito per angolo farfalla non compreso nel range di limp-home */
                    StDbwSelfError = DBW_SELF_LIMP_HOME_ERROR;
                    StDbwSelf = DBW_SELF_FAILED;
                    VDbwIn = 0;
                    FlgSelfLearning = 0;
                    FlgSelf_FirstDone=0;
                }
            }           
            break;
        case DBW_SELF_FAILED:
        case DBW_SELF_END:
            if (FlgSelfRequest != 0) 
            {
                if (FlgSelfDisable == SELF_NORMAL_COND) 
                {  
                    FlgSelfLearning = 1;
                    FlgSelf_FirstDone=0;
                    VDbwIn = 0;
                    FlgSelfDisable = SELF_NORMAL_COND;
                    StDbwSelf = DBW_SELF_INIT;
                    StDbwSelfError = DBW_SELF_NO_ERROR;
                    TIMING_GetAbsTimer(&SelfTimer);   
                }
                FlgSelfRequest = 0;
            } 
            break;
        default:
            StDbwSelf = DBW_SELF_FAILED;
            break;
    }
}

void SelfMgm_ExtSelfRequest(void)
{
    FlgSelfRequest = 1;
}
#else /* stubs */

void SelfMgm_T5ms(void)
{ 
    ForceVAngThrMin();
}


#endif /* _BUILD_SELFMGM_ */
