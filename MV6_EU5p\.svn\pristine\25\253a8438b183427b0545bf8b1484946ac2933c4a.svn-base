/************************************************************************
 *                                                                       *
 *                   Standard Software C90LC Driver for MPC563xM         *
 *                                                                       *
 * FILE NAME     :  FlashErase.c                                         *
 * DATE          :                                                       *
 *                                                                       *
 * AUTHOR        :                                                       *
 * E-mail        :                                                       *
 *                                                                       *
 *************************************************************************/

 /******************************* CHANGES *********************************
 0.0a    01-24-2008  Sindhu R01         Initial Version
 0.1     02-08-2008  <PERSON> He            Updated for final release.
 0.2     03-18-2008  Chen He            Add FlashECCLogicCheck
 1.0     04-10-2008  Sindhu R01         Updated for final Release
 1.1     06-12-2008  <PERSON><PERSON>      Updates to JDP SW template.
 1.2     08-20-2008  A<PERSON><PERSON> after changes in SSD to support 
                                        MPC5668x.
 1.3     01-05-2009  Sindhu R01         Added Return codes related to FlashECCLogicCheck  
                                        and FlashArrayIntegrityCheck in Section 2.5.
                                        Modified Table 5.
 0.2.0   01-20-2009  Sindhu R01         Added API FactoryMarginReadCheck
 0.3.0   02-13-2009  Arvind Awasthi     Added eccValue parameter to the prototype of 
                                        FlashECCLogicCheck API.
 0.4.0   11-25-2009  Leonardo Colombo   Updated after changes in SSD to support all xPC56xx.
 0.5.0   06-15-2010  Cosimo Stornaiuolo Modified Test_FlashArrayUntegrityCheck.c to 
                                        support EmbAlgo 3.7
                                        Added support for DFO
                                        Tets Suite Updated for xPC56xx.
 *************************************************************************/

/******************************* AKHELA **********************************
 1.0.0  2012.06.06       Mocci A.       C90LC v1.0.1 porting
 *************************************************************************/ 


#ifdef  _BUILD_FLASH_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ssd_c90fl.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* C90LC Driver v1.0.1 */
const unsigned long FlashErase_C[] = 
{
      0x7C0802A6, 0x9421FFE0, 0x119CEA2D, 0x11811321, 0x117EFA2D, 0x11611B21, 0x90010024
    , 0x7C7F1B78, 0x7C8B2378, 0x7CC93378, 0x7CEA3B78, 0x7D1C4378, 0x80DF0000, 0x7CDD3378
    , 0x819D0000, 0x38E60010, 0x718C0014, 0x4182000C, 0x3BC00300, 0x480001B4, 0x819F000C
    , 0x2C0C0000, 0x4182000C, 0x819F000C, 0x390C00E0, 0x3BC00000, 0x2C0B0000, 0x40820064
    , 0x811F0004, 0x819F0014, 0x216C0020, 0x3980FFFF, 0x7D8C5C30, 0x7CA56038, 0x819F0018
    , 0x216C0020, 0x3980FFFF, 0x7D8C5C30, 0x7D2C6038, 0x558C801E, 0x7CA56378, 0x819F001C
    , 0x216C0020, 0x3980FFFF, 0x7D8C5C30, 0x7D4A6038, 0x7CAC5379, 0x41820144, 0x90A70000
    , 0x39860014, 0x914C0000, 0x48000010, 0x819F000C, 0x2C0C0000, 0x41820128, 0x819D0000
    , 0x618C0004, 0x558C049E, 0x919D0000, 0x3980FFFF, 0x91880000, 0x819D0000, 0x618C0001
    , 0x558C049E, 0x919D0000, 0x48000014, 0x2C1CFFFF, 0x4182000C, 0x7F8903A6, 0x4E800421
    , 0x819D0000, 0x558C056B, 0x4182FFE8, 0x819D0000, 0x558C003C, 0x558C049E, 0x919D0000
    , 0x819D0000, 0x558B05AC, 0x39800500, 0x2C0B0000, 0x7FCCF09E, 0x817F0000, 0x3D80C3F9
    , 0x398CC000, 0x7C0B6040, 0x4182004C, 0x3D80C3F9, 0x398C801C, 0x818C0000, 0x7D8A6378
    , 0x3D80C3F9, 0x398C801C, 0x818C0000, 0x558C003C, 0x3D60C3F9, 0x396B801C, 0x918B0000
    , 0x819D0000, 0x558C07B8, 0x558C049E, 0x919D0000, 0x3D80C3F9, 0x398C801C, 0x914C0000
    , 0x817F0000, 0x3D80C3F9, 0x398CC000, 0x7C0B6040, 0x4082004C, 0x3D80C3F9, 0x398C8020
    , 0x818C0000, 0x7D8A6378, 0x3D80C3F9, 0x398C8020, 0x818C0000, 0x558C003C, 0x3D60C3F9
    , 0x396B8020, 0x918B0000, 0x819D0000, 0x558C07B8, 0x558C049E, 0x919D0000, 0x3D80C3F9
    , 0x398C8020, 0x914C0000, 0x819F0024, 0x2C0C0000, 0x41820008, 0x44000002, 0x7FCCF378
    , 0x7D836378, 0x83810010, 0x83A10014, 0x83C10018, 0x83E1001C, 0x80010024, 0x38210020
    , 0x7C0803A6, 0x4E800020
    , 0x30393530, 0x32464646
    
}; /* Total Size = 144 words */

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_t functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FlashErase - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint32_t FlashErase ( PSSD_CONFIG pSSDConfig,
                bool shadowFlag,
                uint32_t lowEnabledBlocks,
                uint32_t midEnabledBlocks,
                uint32_t highEnabledBlocks,
                void (*CallBack)(void)
                )
{
  FlashFunctionLoader( (unsigned long*)FlashErase_C, sizeof(FlashErase_C)/4);
  return ((pFLASHERASE)FlashFunctionPointer)(pSSDConfig, shadowFlag, lowEnabledBlocks,
                                             midEnabledBlocks, highEnabledBlocks, 
                                             CallBack);
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */

#endif /*  _BUILD_FLASH_ */
