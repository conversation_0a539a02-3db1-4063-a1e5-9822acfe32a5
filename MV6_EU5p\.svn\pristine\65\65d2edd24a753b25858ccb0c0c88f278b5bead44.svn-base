/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#ifdef _BUILD_UART_ /* see \config\c */


#include "..\..\sys\include\etpu_util.h"          /* Utility routines for working eTPU */
               /* eTPU SPI API header */

#include "..\..\sys\auto\etpu_UART_auto.h"
#include "..\sys\include\ETPU_HostInterface.h"
#include "..\..\sys\auto\etpu_angle_clock_func_auto.h"

#include "..\..\LIN\inc\linbase.h"

#include "uart.h"
#include "sys.h"

/***************************************************************************
 * Function :   EtpuUartConfig
 *
 * Description: 
 *             - enable GPIO
 *             - call EtpuUartRxTxChannelInit()
 * 
 * Returns:     int32_t
 *
 * Notes:       
 *
 **************************************************************************/

int32_t EtpuUartConfig( void ) 
{

      uint32_t error;  
      
      /* configure the pins on the MPC5554*/
      
      /* eTPU UART TX(n129/ch12) - Output */
#ifdef OUT_UART_TX
      
    SYS_OutPinConfig(OUT_UART_TX, PRIMARY_FUNCTION, INPUT_DISABLE, DRIVE_STRENGTH_50PF, PUSH_PULL_ENABLE, MAXIMUM_SLEW_RATE) ;
    /* eTPU UART RX(n128/ch11) - Input */
    SYS_InPinConfig(IN_UART_RX,PRIMARY_FUNCTION, ENABLE_HYSTERESIS, WEAK_PULL_DISABLE);     /* WEAK_PULL_UP??? */
      
#endif      
      
      /*initialise the channel for UART */
      error = EtpuUartRxTxChannelInit (
                                    ETPU_UART_TX,           /* tx_channel: engine A   */
                                    ETPU_UART_RX,           /* rx_channel: engine A   */
                                    FS_ETPU_PRIORITY_HIGH,  /* priority: High  */
                                    SCI_9600_BR,            /* baud_rate: */
                                    BITS_PER_DATA_WORD_8,    /* bits_per_data_word: */
                                    FS_ETPU_UART_NO_PARITY, /* parity: */
                                    UART_TIMEBASE_FREQ
                                          /* timebase_freq = sysclk/2/prescalers */
                                    );
                                    
      return error;                              
      

}



/************************************************************************
FUNCTION:       EtpuUartRxTxChannelInit
************************************************************************
Purpose:        To initialize a channel to function as a UART transmitter and 
            receiver
Input Notes:    This function has 7 parameters:

                        tx_channel -    This is the channel number of the transmitter.
                                0-31 for ETPU_A and 64-95 for ETPU_B.

                        rx_channel -    This is the channel number of the receiver.
                                0-31 for ETPU_A and 64-95 for ETPU_B.

                        priority   -    This is the priority to assign to the channel.
                                            This parameter should be assigned a value of:
                                            ETPU_PRIORITY_HIGH, ETPU_PRIORITY_MIDDLE or
                                        ETPU_PRIORITY_LOW.

                         baud_rate -    Baud rate is a measure of the number of times per
                                      second a signal in a communications channel varies,
                                      or makes a transition between states (states being
                                      frequencies, voltage levels, or phase angles). One
                                      baud is one such change. Thus, a 300-baud modem's
                                      signal changes state 300 times each second, while
                                      a 600- baud modem's signal changes state 600 
                                      times per second. The baud_rate value is the 
                                      number of TCR1 counts per bit time, and is 
                                      calculated by the following equation:

                          # Timer Count Register1 (TCR1) counts / second
                          -------------------------------------------------
                          number of transitions (baud)/ second
                           
                           
      bits_per_data_word -  This is the number of bits to be transmitted in 
                            one data word.  This bits_per_data_word commonly 
                            has a value of eight, because most serial 
                            protocols use 8-bit words.
      
                  parity -  This is the desired parity.  This parameter 
                            should be assigned a value of 
                            FS_ETPU_UART_NO_PARITY,  FS_ETPU_UART_ODD_PARITY,
                            or FS_ETPU_UART_EVEN_PARITY. The TPU baud rates
                            for the UART function are defined in tpu_uart.h.
      
           timebase_freq -  This is the frequency of the selected timebase.
                                The range of this is the same as the range of the
                                timebase frequency on the device.  
                                This parameter is a uint32_t.
                            
RETURNS NOTES:         Error code if channel could not be initialized. Error code that
                       can be returned are: FS_ETPU_ERROR_MALLOC , FS_ETPU_ERROR_FREQ
WARNING                : *This function does not configure the pin only the eTPU. In a
                       system a pin may need to be configured to select the eTPU.
******************************************************************************/

int32_t EtpuUartRxTxChannelInit   (  uint8_t  tx_channel, 
                                     uint8_t  rx_channel,
                                     uint8_t  priority, 
                                     uint32_t baud_rate,
                                     uint8_t  bits_per_data_word, 
                                     uint8_t  parity,
                                     uint32_t timebase_freq)

{
    uint32_t ParameterBaseAddress; /* parameter base address for channel  */
    uint32_t chan_match_rate;      /* value to be used for each bit time  */
    int16_t returnCode = NO_ERROR;

    /* ------ initialize receive channel ------- */
    returnCode = ETPU_ChannelInitRam(rx_channel, FS_ETPU_UART_NUM_PARMS, &ParameterBaseAddress);

    /* check for errors */
    if (returnCode != NO_ERROR)
        return UART_RX_CHANNEL_NOT_CONFIGURED;

    /* Initialize RX channel */
    returnCode = ETPU_ChannelInit( rx_channel,  
                                (uint8_t)priority, 
                                (uint8_t)parity, 
                                (uint8_t)FS_ETPU_UART_FUNCTION_NUMBER, 
                                (uint8_t)FS_ETPU_UART_TABLE_SELECT, 
                                ParameterBaseAddress);

    if(returnCode != NO_ERROR) 
       return returnCode;
    
    /* Determine bit time of serially transmitted data  */
    /* integer division truncates, so any fractional part is discarded */
    chan_match_rate = timebase_freq / baud_rate;

    if ((chan_match_rate == 0) || (chan_match_rate > 0x007FFFFF))
        return (UART_ETPU_ERROR_FREQ);

    /* write match_rate calculated from time base frequency and desired baud rate        */
    write_FS_ETPU_UART_MATCH_RATE(chan_match_rate, rx_channel);

    /* configure the number of bits per data word.This number represents 
    only the number of data bits and does not include start, stop, or 
    parity bits. */
    write_FS_ETPU_UART_BITS_PER_DATA_WORD(bits_per_data_word, rx_channel);

    /* write hsr to start channel running */
    //eTPU->CHAN[rx_channel].HSRR.R = FS_ETPU_UART_RX_INIT;

    /* ------ initialize transmit channel ------ */

//    ParameterBaseAddress = ETPU_ChannelInitRam(tx_channel, FS_ETPU_UART_NUM_PARMS);
    returnCode = ETPU_ChannelInitRam(tx_channel, FS_ETPU_UART_NUM_PARMS, &ParameterBaseAddress );

    /* check for errors */
    if (returnCode != NO_ERROR)
        return UART_TX_CHANNEL_NOT_CONFIGURED;

    /* Initialize TX channel */
    returnCode = ETPU_ChannelInit( tx_channel,  
                                (uint8_t)priority, 
                                (uint8_t)parity, 
                                (uint8_t)FS_ETPU_UART_FUNCTION_NUMBER, 
                                (uint8_t)FS_ETPU_UART_TABLE_SELECT, 
                                ParameterBaseAddress);

    if(returnCode != NO_ERROR) 
       return returnCode;

    /* write match_rate calculated from time base frequency and desired baud rate        */
    //*(ParameterBaseAddress + ((FS_ETPU_UART_MATCH_RATE_OFFSET - 1) >> 2)) = chan_match_rate;
    write_FS_ETPU_UART_MATCH_RATE(chan_match_rate, tx_channel);

    /* set the TDRE flag to indicate that the transmit data reg is empty*/
    //*(ParameterBaseAddress + ((FS_ETPU_UART_TX_RX_DATA_OFFSET - 1) >> 2)) = 0x00800000;
    write_FS_ETPU_UART_TX_RX_DATA(SET_TDRE, tx_channel);
    /* a one in the MSB indicates that the data register is empty */

    /* configure the number of bits per data word.This number represents 
    only the number of data bits and does not include start, stop, or 
    parity bits. */
    //*(ParameterBaseAddress + ((FS_ETPU_UART_BITS_PER_DATA_WORD_OFFSET - 1) >> 2)) = bits_per_data_word;
    write_FS_ETPU_UART_BITS_PER_DATA_WORD(bits_per_data_word,tx_channel);

    /* write hsr to start channel running */
    //eTPU->CHAN[tx_channel].HSRR.R = FS_ETPU_UART_TX_INIT;

    return NO_ERROR;
}




/************************************************************************
FUNCTION:       EtpuUartRxTxChannelInit
************************************************************************
Purpose:        To initialize a channel to function as a UART transmitter and 
            receiver
Input Notes:    This function has 7 parameters:

                        tx_channel -    This is the channel number of the transmitter.
                                0-31 for ETPU_A and 64-95 for ETPU_B.

                        rx_channel -    This is the channel number of the receiver.
                                0-31 for ETPU_A and 64-95 for ETPU_B.

                        priority   -    This is the priority to assign to the channel.
                                            This parameter should be assigned a value of:
                                            ETPU_PRIORITY_HIGH, ETPU_PRIORITY_MIDDLE or
                                        ETPU_PRIORITY_LOW.

                         baud_rate -    Baud rate is a measure of the number of times per
                                      second a signal in a communications channel varies,
                                      or makes a transition between states (states being
                                      frequencies, voltage levels, or phase angles). One
                                      baud is one such change. Thus, a 300-baud modem's
                                      signal changes state 300 times each second, while
                                      a 600- baud modem's signal changes state 600 
                                      times per second. The baud_rate value is the 
                                      number of TCR1 counts per bit time, and is 
                                      calculated by the following equation:

                          # Timer Count Register1 (TCR1) counts / second
                          -------------------------------------------------
                          number of transitions (baud)/ second
                           
                           
      bits_per_data_word -  This is the number of bits to be transmitted in 
                            one data word.  This bits_per_data_word commonly 
                            has a value of eight, because most serial 
                            protocols use 8-bit words.
      
                  parity -  This is the desired parity.  This parameter 
                            should be assigned a value of 
                            FS_ETPU_UART_NO_PARITY,  FS_ETPU_UART_ODD_PARITY,
                            or FS_ETPU_UART_EVEN_PARITY. The TPU baud rates
                            for the UART function are defined in tpu_uart.h.
      
           timebase_freq -  This is the frequency of the selected timebase.
                                The range of this is the same as the range of the
                                timebase frequency on the device.  
                                This parameter is a uint32_t.
                            
RETURNS NOTES:         Error code if channel could not be initialized. Error code that
                       can be returned are: FS_ETPU_ERROR_MALLOC , FS_ETPU_ERROR_FREQ
WARNING                : *This function does not configure the pin only the eTPU. In a
                       system a pin may need to be configured to select the eTPU.
******************************************************************************/

int32_t EtpuUartRxTxChannelModify   (  uint8_t  tx_channel, 
                                     uint8_t  rx_channel,
                                     uint32_t baud_rate,
                                     uint8_t  bits_per_data_word, 
                                     uint32_t timebase_freq)

{
    uint32_t chan_match_rate;      /* value to be used for each bit time  */

    /* Initialize RX channel */
    /* Determine bit time of serially transmitted data  */
    /* integer division truncates, so any fractional part is discarded */
    chan_match_rate = timebase_freq / baud_rate;
    
      if ((chan_match_rate == 0) || (chan_match_rate > 0x007FFFFF))
        return (UART_ETPU_ERROR_FREQ);

    /* write match_rate calculated from time base frequency and desired baud rate        */
    write_FS_ETPU_UART_MATCH_RATE(chan_match_rate, rx_channel);
 
    /* configure the number of bits per data word.This number represents 
       only the number of data bits and does not include start, stop, or 
       parity bits. */
    write_FS_ETPU_UART_BITS_PER_DATA_WORD(bits_per_data_word, rx_channel);

    /* write hsr to start channel running */
    //eTPU->CHAN[rx_channel].HSRR.R = FS_ETPU_UART_RX_INIT;

/* ------ initialize transmit channel ------ */
    
    /* check for errors */
        /* Initialize TX channel */
    /* write match_rate calculated from time base frequency and desired baud rate        */
    //*(ParameterBaseAddress + ((FS_ETPU_UART_MATCH_RATE_OFFSET - 1) >> 2)) = chan_match_rate;
        write_FS_ETPU_UART_MATCH_RATE(chan_match_rate, tx_channel);
    
    /* set the TDRE flag to indicate that the transmit data reg is empty*/
    //*(ParameterBaseAddress + ((FS_ETPU_UART_TX_RX_DATA_OFFSET - 1) >> 2)) = 0x00800000;
    write_FS_ETPU_UART_TX_RX_DATA(SET_TDRE, tx_channel);
    /* a one in the MSB indicates that the data register is empty */

    /* configure the number of bits per data word.This number represents 
       only the number of data bits and does not include start, stop, or 
       parity bits. */
    //*(ParameterBaseAddress + ((FS_ETPU_UART_BITS_PER_DATA_WORD_OFFSET - 1) >> 2)) = bits_per_data_word;
        write_FS_ETPU_UART_BITS_PER_DATA_WORD(bits_per_data_word,tx_channel);
    
    /* write hsr to start channel running */
    //eTPU->CHAN[tx_channel].HSRR.R = FS_ETPU_UART_TX_INIT;

    return NO_ERROR;
}

/***************************************************************************
 * Function :   EtpuUartEnable
 *
 * Description: 
 *             - call Host Service Request for initialization
 *             
 * Returns:     int16_t
 *
 * Notes:       
 *
 **************************************************************************/

int16_t EtpuUartEnable(uint16_t rx_channel, uint16_t tx_channel)
{
    int16_t returnCode;

    returnCode = ETPU_set_hsr(rx_channel,FS_ETPU_UART_RX_INIT );
    if(returnCode == NO_ERROR)
        returnCode = ETPU_set_hsr(tx_channel,FS_ETPU_UART_TX_INIT );

    return returnCode;                                         
}




/***************************************************************************
 * Function :   EtpuUartIoCtrl
 *
 * Description: 
 *             - SET_BAUDERATE
 *             - SET_BITS_PER_DATA
 *             - SET_PARITY
 *             - GET_BAUDERATE
 *             - GET_BITS_PER_DATA
 *             - GET_PARITY
 *               
 *             
 * Returns:     int16_t
 *
 * Notes:       
 *
 **************************************************************************/

int16_t EtpuUartIoCtrl(uint16_t channel, uint16_t op, uint32_t *ref_value)
{
  
    uint32_t value = (uint32_t) (*ref_value); 
     
  switch(op) {          

    case SET_BAUDERATE:
                      /* write match_rate calculated from time base frequency 
                         and desired baud rate */
                      write_FS_ETPU_UART_MATCH_RATE((UART_TIMEBASE_FREQ / value), channel);
                      break;
    case SET_BITS_PER_DATA:
                      /* configure the number of bits per data word.This number represents 
                         only the number of data bits and does not include start, stop, or 
                         parity bits. */
                      write_FS_ETPU_UART_BITS_PER_DATA_WORD(value,channel);        
                      break;
    case SET_PARITY:
                      ETPU.CHAN[channel].SCR.B.FM0 = (value & 0x00000001);
                      ETPU.CHAN[channel].SCR.B.FM1 = ((value >> 1) & 0x00000001);
                      break;                  
    case GET_BAUDERATE:
                      *ref_value = (UART_TIMEBASE_FREQ / read_FS_ETPU_UART_MATCH_RATE(channel));
                      break;
    case GET_BITS_PER_DATA:
                      *ref_value = read_FS_ETPU_UART_BITS_PER_DATA_WORD(channel);      
                      break;
    case GET_PARITY:
                      *ref_value = (0x3 & ETPU.CHAN[channel].SCR.R);
                      break;
    
    
    default:
           return UART_INVALID_IOCTRL_OPTION;
  }
  return NO_ERROR;
                                         
}

/************************************************************************
FUNCTION:       fs_etpu_uart_write_transmit_data
************************************************************************
Purpose:        To write data to eTPU UART transmitter channel for data to be 
            Serially shifted out

Input Notes: This function has 2 parameters

            channel - This is the channel number.
                      0-31 for ETPU_A and 64-95 for ETPU_B.
      
      transmit_data - This the actual data to be transmitted.  
                      Up to 22 bits of data per data word can be transmitted.
      
******************************************************************************/

void EtpuUartWriteTxData(uint8_t tx_channel,uint32_t transmit_data)
{

 /* write the transmit data register    */
 /* clear MSB to indicate valid data to transmit    */
 write_FS_ETPU_UART_TX_RX_DATA((transmit_data & 0x7fffff),tx_channel);
 #if(LINDEBUG_2)
   LIN_DBG_CLR_PORT_0;
 #endif
}










/************************************************************************
FUNCTION:       fs_etpu_uart_read_receive_data
************************************************************************
Purpose:        To read received data from eTPU UART receive channel,
                and to optionally check for parity and or framing errors

Input Notes:    This function has 2 parameters

                channel -       This is the channel number.
                              0-31 for ETPU_A and 64-95 for ETPU_B.
                
                
              *rx_error - This is a pointer to a location to contain an error 
                          code
                    A value of O - no error
                           0x000080  - parity error
                           0x000040  - framing error
                           0x0000c0  - both parity and framing error
                will be placed in the location pointed to by pointer rx_error

A parity error is detected when the odd/even number of ones in the data does
not match the programmed (expected) value.  A framing error occurs when the 
UART function determines that a stop bit is low instead of high.  All errors
are only valid for each received data word.

The function returns the received data right justified.

******************************************************************************/

int32_t EtpuUartReadRxData (uint8_t rx_channel, uint24_t *rx_error)
{
    uint32_t receive_data;

    /* place error code in location pointed to by *rx_error pointer */
    //*rx_error = *(pba8 + FS_ETPU_UART_RX_ERROR_OFFSET);
        *rx_error = read_FS_ETPU_UART_RX_ERROR(rx_channel);
        
        
    /* get received data  */
    //receive_data = *(pba + ((FS_ETPU_UART_TX_RX_DATA_OFFSET - 1) >> 2));
    receive_data = read_FS_ETPU_UART_TX_RX_DATA(rx_channel);
    
    /* clear MSByte  */
    receive_data &= 0x00ffffff;
    
    /* right justify received data */
    return (receive_data >> (23 - (read_FS_ETPU_UART_BITS_PER_DATA_WORD(rx_channel))));
    
}







/***************************************************************************
 * Function :   EtpuUartEnableEx
 *
 * Description: 
 *             - task initialization
 *             
 * Returns:     int16_t
 *
 * Notes:       
 *
 **************************************************************************/
 
int16_t EtpuUartEnableEx  (uint16_t   chan,  TaskType  taskID)
                       
{
  
  if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
   return UART_NO_EX_ENABLED;

  
  ETPU_SetInterruptHandlerUC(chan, taskID);
  
  //ETPU_EnableInterruptUC(chan);
    
  return NO_ERROR;
}


/***************************************************************************
 * Function :   EtpuUartIrqEnable
 *
 * Description: 
 *             - task initialization
 *             
 * Returns:     int16_t
 *
 * Notes:       
 *
 **************************************************************************/
 
int16_t EtpuUartIrqEnable(uint16_t   chan)
                       
{
  
  if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
   return UART_NO_EX_ENABLED;
  
  // Clear the interrupt
    ETPU.CHAN[chan].SCR.B.CIS = 1;
    ETPU.CHAN[chan].SCR.B.CIOS = 1;
  
  ETPU_EnableInterruptUC(chan);
    
  return NO_ERROR;
}

/***************************************************************************
 * Function :   EtpuUartIrqDisable
 *
 * Description: 
 *             - task initialization
 *             
 * Returns:     int16_t
 *
 * Notes:       
 *
 **************************************************************************/
 
int16_t EtpuUartIrqDisable(uint16_t   chan)
                       
{
  
  if (!(ETPU.CHAN[chan].CR.B.CPBA > 0))
   return UART_NO_EX_ENABLED;
  ETPU_DisableInterruptUC(chan);
    
  return NO_ERROR;
}

#endif /*_BUILD_UART_*/
