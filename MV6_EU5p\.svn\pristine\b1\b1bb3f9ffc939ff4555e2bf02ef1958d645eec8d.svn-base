/*****************************************************************************/
/* Supported engine types ENGINE_TYPE                                        */
/*****************************************************************************/
#define VW_1400_4C_16V           (1)
#define PI_500_1C_4V             (2)
#define FE_6300_12C_48V          (3)
#define FE_4300_8C_32V           (4)
#define YP_250_G                 (5)
#define EM_VW_1400_4C_16V        (6)
#define MA_MC12_12C              (7)
#define FE_4300_8C_32V_TDN       (8)
#define MV_AGUSTA_4C             (9)
#define PI_250_1C_DBW           (10)
#define PI_250_1C_HYBRID        (11)
#define FE_4300_8C_32V_GT2      (12)
#define FE_142_8C               (13)
#define FE_151_12C              (14)
#define FE_A1GP_8C              (15)
#define MV_AGUSTA_3C            (16)
#define FE_599_12C_TDN          (17)
#define LA_L539_12C             (18)
#define MV_AGUSTA_3C_TDC_0_08   (19)
#define MV_AGUSTA_3C_TDC_0_20   (20)
#define MV_AGUSTA_3C_TDC_0_30   (21)
#define MV_AGUSTA_4C_TDC_0_9    (22)

/*****************************************************************************/

/*****************************************************************************/
/* Supported ECU Board targets BOARD_TYPE                                    */
/*****************************************************************************/
#define BOARD_1                 (1)   /* ECU Ferrari (by Digitek) basata su MPC5554       */
#define BOARD_2                 (2)   /* ECU Ferrari (by Simea)   basata su MPC5533       */
#define BOARD_3                 (3)   /* ECU Piaggio (by Destura rev.B) basata su MPC5533 */
#define BOARD_3_A               (4)   /* ECU Piaggio (by Destura rev.A) basata su MPC5533 */
#define BOARD_4                 (5)   /* ECU Piaggio (by Destura rev.B) basata su MPC5533 */
#define BOARD_5                 (6)   /* ECU Piaggio (by Destura rev.C) basata su MPC5533 */
#define BOARD_S4                (7)   /* ECU EI versione 2.1 basata su MPC5533       */
#define BOARD_M1                (8)   /* ECU NEMO MV (proto rev. A) basata su MPC563M o MPC5633 */
#define BOARD_M2                (9)   /* ECU NEMO MV (proto rev. B) basata su MPC563M o MPC5633 */
#define BOARD_M3                (10)  /* ECU NEMO MV (proto rev. C) basata su MPC563M o MPC5633 */
/*****************************************************************************/

/*****************************************************************************/
/* Supported processor targets TARGET_TYPE                                   */
/*****************************************************************************/
#define MPC5554                 (1)   /* MPC5554           */
#define MPC5534                 (2)   /* MPC5534/MPC5533   */
#define MPC563XM                (3)   /* MPC563xM          */
#define MPC5633                 (4)   /* ECU MV produzione */
#define MPC5634                 (5)   /* EVB Monaco        */

#define QC16                    (1)
#define QB8                     (2)
/*****************************************************************************/


