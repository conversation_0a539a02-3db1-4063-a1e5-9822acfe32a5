/*
 * File: GasPosMgm.c
 *
 * Code generated for Simulink model 'GasPosMgm'.
 *
 * Model version                  : 1.898
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb  1 14:21:31 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "GasPosMgm.h"
#include "GasPosMgm_private.h"

/* Named constants for Chart: '<S13>/Calc_Freeze_condition' */
#define GasPosM_IN_NO_ACTIVE_CHILD_p3gm ((uint8_T)0U)
#define GasPosMgm_IN_FREEZE            ((uint8_T)1U)
#define GasPosMgm_IN_NO_FREEZE         ((uint8_T)2U)

/* user code (top of source file) */
/* System '<Root>/GasPosMgm' */
#ifdef _BUILD_GASPOSMGM_

/* Exported block signals */
uint16_T vtgaspos_old[2];              /* '<S17>/Memory' */
int16_T dvtgaspos[2];                  /* '<S17>/Switch' */

/* Block signals (default storage) */
BlockIO_GasPosMgm GasPosMgm_B;

/* Block states (default storage) */
D_Work_GasPosMgm GasPosMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_GasPosMgm GasPosMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_GasPosMgm GasPosMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T FlgFreezeRecGas;

/* Freezes the current gas recovery until idling (=1) */
uint8_T FlgGasNeg;

/* Negative gas flag */
uint8_T FlgGasZero;

/* Most working gas sensors got 0 (=1) */
uint8_T GasCoh_DiagEn;

/* GasPos Coherence Diagnosis enable condition  */
uint16_T GasPos0;

/* Accelerator position */
uint16_T GasPos1;

/* Accelerator 1 position */
int16_T GasPos1Tot;

/* Total gas position */
uint16_T GasPos2;

/* Accelerator 2 position */
uint8_T GasSwCoh_DiagEn;

/* Idle Switch Coherence Diagnosis enable condition  */
uint32_T IDGasPosMgm;

/* ID Version */
uint8_T IndUsedSens;

/* Accelerator sensor in use */
uint8_T RecGasType;

/* Method to calculate GasPos */
uint8_T RecGasType_tmp;

/* Method to calculate GasPos */
uint8_T RecLimTorqueGas;

/* Torque limitation request for gas */
uint8_T RecLimTorqueGas_Freeze;

/* Frozen torque limitation request for gas */
uint8_T RecLimTorqueGas_tmp;

/* Torque limitation request for gas */
uint8_T StDiagGasSwCoh;

/* Idle Switch Coheren Diagnosis state */
uint8_T StIdleSwitch;

/* Idle Switch state */

/* System reset for function-call system: '<S13>/timehist_gas' */
void GasPosMgm_timehist_gas_Reset(void)
{
  /* InitializeConditions for UnitDelay: '<S24>/Unit Delay' */
  GasPosMgm_DWork.UnitDelay_DSTATE = 0U;
}

/* Output and update for function-call system: '<S13>/timehist_gas' */
void GasPosMgm_timehist_gas(void)
{
  uint16_T rtb_LookUp_U8_U16;
  uint32_T y;

  /* Switch: '<S24>/Switch' incorporates:
   *  Constant: '<S24>/ONE'
   *  Constant: '<S24>/ZERO'
   *  Inport: '<Root>/FlgEOL'
   *  Sum: '<S24>/Add'
   *  UnitDelay: '<S24>/Unit Delay'
   */
  if (FlgEOL != 0) {
    GasPosMgm_DWork.UnitDelay_DSTATE++;
  } else {
    GasPosMgm_DWork.UnitDelay_DSTATE = 0U;
  }

  /* End of Switch: '<S24>/Switch' */

  /* Selector: '<S24>/Selector' incorporates:
   *  Constant: '<S24>/BKTIMHISTGAS'
   *  Constant: '<S24>/BKTIMHISTGAS_dim'
   */
  rtb_LookUp_U8_U16 = BKTIMHISTGAS[((uint8_T)BKTIMHISTGAS_dim)];

  /* Math: '<S24>/Math Function' incorporates:
   *  Constant: '<S24>/10ms_to_s'
   *  DataTypeConversion: '<S24>/Data Type Conversion3'
   *  DataTypeConversion: '<S24>/Data Type Conversion4'
   *  Product: '<S24>/Divide1'
   *  UnitDelay: '<S24>/Unit Delay'
   */
  if (rtb_LookUp_U8_U16 == 0U) {
    y = 0U;
  } else {
    y = (GasPosMgm_DWork.UnitDelay_DSTATE << 4) / 100U % rtb_LookUp_U8_U16;
  }

  /* End of Math: '<S24>/Math Function' */

  /* DataTypeConversion: '<S24>/Data Type Conversion1' */
  rtb_LookUp_U8_U16 = (uint16_T)y;

  /* S-Function (LookUp_U8_U16): '<S51>/LookUp_U8_U16' incorporates:
   *  Constant: '<S24>/BKTIMHISTGAS'
   *  Constant: '<S24>/BKTIMHISTGAS_dim'
   *  Constant: '<S24>/VTTIMHISTGAS'
   */
  LookUp_U8_U16( &rtb_LookUp_U8_U16, &VTTIMHISTGAS[0], rtb_LookUp_U8_U16,
                &BKTIMHISTGAS[0], ((uint8_T)BKTIMHISTGAS_dim));

  /* DataTypeConversion: '<S24>/Data Type Conversion' */
  GasPosMgm_B.gaspos_th = (uint16_T)((uint32_T)rtb_LookUp_U8_U16 >> 5);
}

/* System reset for atomic system: '<S13>/Calc_Final_GasPos' */
void GasPosM_Calc_Final_GasPos_Reset(void)
{
  GasPosMgm_DWork.stRtUp = 0U;
  GasPosMgm_DWork.tmpOldGasPos = 0U;
  GasPosMgm_B.tmpGasPos = 0U;
}

/* Enable for atomic system: '<S13>/Calc_Final_GasPos' */
void GasPos_Calc_Final_GasPos_Enable(void)
{
  /* SystemReset for Function Call SubSystem: '<S13>/timehist_gas' */
  GasPosMgm_timehist_gas_Reset();

  /* End of SystemReset for SubSystem: '<S13>/timehist_gas' */
}

/* Output and update for atomic system: '<S13>/Calc_Final_GasPos' */
void GasPosMgm_Calc_Final_GasPos(void)
{
  uint8_T flgRtUp;
  int32_T u1;

  /* Chart: '<S13>/Calc_Final_GasPos' */
  /* Gateway: GasPosMgm/T10ms/dbw/Calc_Final_GasPos */
  /* During: GasPosMgm/T10ms/dbw/Calc_Final_GasPos */
  /* Entry Internal: GasPosMgm/T10ms/dbw/Calc_Final_GasPos */
  /* Transition: '<S14>:3' */
  flgRtUp = 0U;
  if (FORCEGASPOS >= 0) {
    /* Transition: '<S14>:80' */
    GasPosMgm_B.tmpGasPos = (uint16_T)((uint16_T)FORCEGASPOS << 4);

    /* Transition: '<S14>:84' */
    /* Transition: '<S14>:85' */
    /* Transition: '<S14>:86' */
    /* Transition: '<S14>:87' */
    /* Transition: '<S14>:88' */
    /* Transition: '<S14>:89' */
    /* Transition: '<S14>:90' */
    /* Transition: '<S14>:91' */
    /* Transition: '<S14>:92' */
    /* Transition: '<S14>:94' */
  } else {
    /* Transition: '<S14>:78' */
    if (FORCEGASSENS > 0) {
      /* Transition: '<S14>:13' */
      IndUsedSens = FORCEGASSENS;
      GasPosMgm_B.tmpGasPos = (uint16_T)(GasPosMgm_B.vtgaspos[IndUsedSens - 1] <<
        3);

      /* Transition: '<S14>:85' */
      /* Transition: '<S14>:86' */
      /* Transition: '<S14>:87' */
      /* Transition: '<S14>:88' */
      /* Transition: '<S14>:89' */
      /* Transition: '<S14>:90' */
      /* Transition: '<S14>:91' */
      /* Transition: '<S14>:92' */
      /* Transition: '<S14>:94' */
    } else {
      /* Transition: '<S14>:15' */
      if ((VtRec[REC_NO_GAS] != 0) || (FlgEMDSoftStop == 1) || (RecGasType ==
           ((uint8_T)WAIT_IDLE)) || GasPosMgm_B.LogicalOperator1) {
        /* Transition: '<S14>:12' */
        u1 = GasPosMgm_B.tmpGasPos - (GPOSSLEW2ZERO << 4);
        if (0 > u1) {
          GasPosMgm_B.tmpGasPos = 0U;
        } else {
          GasPosMgm_B.tmpGasPos = (uint16_T)u1;
        }

        GasPosMgm_DWork.tmpOldGasPos = GasPosMgm_B.tmpGasPos;
        flgRtUp = 1U;

        /* Transition: '<S14>:86' */
        /* Transition: '<S14>:87' */
        /* Transition: '<S14>:88' */
        /* Transition: '<S14>:89' */
        /* Transition: '<S14>:90' */
        /* Transition: '<S14>:91' */
        /* Transition: '<S14>:92' */
        /* Transition: '<S14>:94' */
      } else {
        /* Outputs for Function Call SubSystem: '<S13>/timehist_gas' */
        /* Transition: '<S14>:1' */
        /* Event: '<S14>:76' */
        GasPosMgm_timehist_gas();

        /* End of Outputs for SubSystem: '<S13>/timehist_gas' */
        if ((FORCEGASTIMEHIST == 1) || (FlgEOL == 1)) {
          /* Transition: '<S14>:23' */
          GasPosMgm_B.tmpGasPos = (uint16_T)(GasPosMgm_B.gaspos_th << 4);

          /* Transition: '<S14>:87' */
          /* Transition: '<S14>:88' */
          /* Transition: '<S14>:89' */
          /* Transition: '<S14>:90' */
          /* Transition: '<S14>:91' */
          /* Transition: '<S14>:92' */
          /* Transition: '<S14>:94' */
        } else {
          /* Transition: '<S14>:22' */
          if (RecGasType == ((uint8_T)USE_1ST_SENS)) {
            /* Transition: '<S14>:2' */
            IndUsedSens = 1U;
            GasPosMgm_B.tmpGasPos = (uint16_T)(GasPosMgm_B.vtgaspos[IndUsedSens
              - 1] << 3);

            /* Transition: '<S14>:88' */
            /* Transition: '<S14>:89' */
            /* Transition: '<S14>:90' */
            /* Transition: '<S14>:91' */
            /* Transition: '<S14>:92' */
            /* Transition: '<S14>:94' */
          } else {
            /* Transition: '<S14>:24' */
            if (RecGasType == ((uint8_T)USE_2ND_SENS)) {
              /* Transition: '<S14>:25' */
              IndUsedSens = 2U;
              GasPosMgm_B.tmpGasPos = (uint16_T)
                (GasPosMgm_B.vtgaspos[IndUsedSens - 1] << 3);

              /* Transition: '<S14>:89' */
              /* Transition: '<S14>:90' */
              /* Transition: '<S14>:91' */
              /* Transition: '<S14>:92' */
              /* Transition: '<S14>:94' */
            } else {
              /* Transition: '<S14>:5' */
              if (RecGasType == ((uint8_T)USE_LAST_VALUE)) {
                /* Transition: '<S14>:6' */
                /* Transition: '<S14>:90' */
                /* Transition: '<S14>:91' */
                /* Transition: '<S14>:92' */
                /* Transition: '<S14>:94' */
              } else {
                /* Transition: '<S14>:11' */
                if (RecGasType == ((uint8_T)MIN_SENS)) {
                  /* Transition: '<S14>:17' */
                  if (GasPosMgm_B.vtgaspos[0] < GasPosMgm_B.vtgaspos[1]) {
                    /* Transition: '<S14>:18' */
                    IndUsedSens = 1U;
                  } else {
                    /* Transition: '<S14>:21' */
                    IndUsedSens = 2U;
                  }

                  /* Transition: '<S14>:7' */
                  GasPosMgm_B.tmpGasPos = (uint16_T)
                    (GasPosMgm_B.vtgaspos[IndUsedSens - 1] << 3);

                  /* Transition: '<S14>:91' */
                  /* Transition: '<S14>:92' */
                  /* Transition: '<S14>:94' */
                } else {
                  /* Transition: '<S14>:16' */
                  if (RecGasType == ((uint8_T)MIN_D_2_SENS)) {
                    /* Transition: '<S14>:14' */
                    if (dvtgaspos[0] < dvtgaspos[1]) {
                      /* Transition: '<S14>:8' */
                      IndUsedSens = 1U;
                    } else {
                      /* Transition: '<S14>:9' */
                      IndUsedSens = 2U;
                    }

                    /* Transition: '<S14>:10' */
                    GasPosMgm_B.tmpGasPos = (uint16_T)
                      (GasPosMgm_B.vtgaspos[IndUsedSens - 1] << 3);

                    /* Transition: '<S14>:92' */
                    /* Transition: '<S14>:94' */
                  } else {
                    /* Transition: '<S14>:19' */
                    if (RecGasType == ((uint8_T)WAIT_IDLE)) {
                      /* Transition: '<S14>:4' */
                      /*  Keep current value of GasPos. Then REC_NO_GAS will slew GasPos to 0. */
                      /* Transition: '<S14>:94' */
                    } else {
                      /* Transition: '<S14>:20' */
                      /*  default case) */
                      GasPosMgm_B.tmpGasPos = 0U;

                      /* Transition: '<S14>:95' */
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  if (flgRtUp != 0) {
    /* Transition: '<S14>:104' */
    GasPosMgm_DWork.stRtUp = 1U;

    /* Transition: '<S14>:109' */
  } else {
    /* Transition: '<S14>:106' */
    if (GasPosMgm_DWork.stRtUp != 0) {
      /* Transition: '<S14>:101' */
      u1 = GasPosMgm_DWork.tmpOldGasPos + GPOSSLEW2TRG;
      if (25600 < u1) {
        GasPosMgm_DWork.tmpOldGasPos = 25600U;
      } else {
        GasPosMgm_DWork.tmpOldGasPos = (uint16_T)u1;
      }

      if (GasPosMgm_DWork.tmpOldGasPos >= GasPosMgm_B.tmpGasPos) {
        /* Transition: '<S14>:97' */
        GasPosMgm_DWork.stRtUp = 0U;
      } else {
        /* Transition: '<S14>:102' */
        GasPosMgm_B.tmpGasPos = GasPosMgm_DWork.tmpOldGasPos;
      }
    } else {
      /* Transition: '<S14>:99' */
      GasPosMgm_DWork.tmpOldGasPos = 0U;
    }

    /* Transition: '<S14>:108' */
  }

  /* End of Chart: '<S13>/Calc_Final_GasPos' */
}

/* System reset for atomic system: '<S13>/Calc_delta' */
void GasPosMgm_Calc_delta_Reset(void)
{
  int32_T i;

  /* InitializeConditions for UnitDelay: '<S25>/Delay Input1'
   *
   * Block description for '<S25>/Delay Input1':
   *
   *  Store in Global RAM
   */
  GasPosMgm_DWork.DelayInput1_DSTATE = 0U;
  for (i = 0; i < 2; i++) {
    /* InitializeConditions for Memory: '<S17>/Memory' */
    GasPosMgm_DWork.Memory_PreviousInput[i] = 0U;

    /* InitializeConditions for Memory: '<S17>/Memory1' */
    GasPosMgm_DWork.Memory1_PreviousInput[i] = 0;

    /* InitializeConditions for Memory: '<S17>/Memory2' */
    GasPosMgm_DWork.Memory2_PreviousInput[i] = 0;

    /* InitializeConditions for Memory: '<S17>/Memory3' */
    GasPosMgm_DWork.Memory3_PreviousInput[i] = 0;
  }
}

/* Output and update for atomic system: '<S13>/Calc_delta' */
void GasPosMgm_Calc_delta(void)
{
  uint8_T rtb_Uk1;
  int32_T i;
  int16_T rtb_Memory2;
  int16_T u;

  /* UnitDelay: '<S25>/Delay Input1'
   *
   * Block description for '<S25>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_Uk1 = GasPosMgm_DWork.DelayInput1_DSTATE;

  /* RelationalOperator: '<S17>/Relational Operator' incorporates:
   *  Constant: '<S17>/ANGTHROFFSET3'
   *  DataStoreRead: '<S17>/Data Store Read5'
   *  UnitDelay: '<S25>/Delay Input1'
   *
   * Block description for '<S25>/Delay Input1':
   *
   *  Store in Global RAM
   */
  GasPosMgm_DWork.DelayInput1_DSTATE = (uint8_T)(RecGasType == ((uint8_T)
    MIN_D_2_SENS));

  /* RelationalOperator: '<S25>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S25>/Delay Input1'
   *
   * Block description for '<S25>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_Uk1 = (uint8_T)(GasPosMgm_DWork.DelayInput1_DSTATE > rtb_Uk1);
  for (i = 0; i < 2; i++) {
    /* Memory: '<S17>/Memory' */
    vtgaspos_old[i] = GasPosMgm_DWork.Memory_PreviousInput[i];

    /* Memory: '<S17>/Memory2' */
    rtb_Memory2 = GasPosMgm_DWork.Memory2_PreviousInput[i];

    /* Switch: '<S17>/Switch' incorporates:
     *  Memory: '<S17>/Memory1'
     *  Memory: '<S17>/Memory3'
     */
    if (rtb_Uk1 != 0) {
      dvtgaspos[i] = GasPosMgm_DWork.Memory3_PreviousInput[i];
    } else {
      dvtgaspos[i] = GasPosMgm_DWork.Memory1_PreviousInput[i];
    }

    /* End of Switch: '<S17>/Switch' */

    /* Update for Memory: '<S17>/Memory' */
    GasPosMgm_DWork.Memory_PreviousInput[i] = GasPosMgm_B.vtgaspos[i];

    /* Update for Memory: '<S17>/Memory1' */
    GasPosMgm_DWork.Memory1_PreviousInput[i] = dvtgaspos[i];

    /* Sum: '<S17>/Subtract' */
    u = (int16_T)(GasPosMgm_B.vtgaspos[i] - vtgaspos_old[i]);

    /* Abs: '<S17>/Abs' */
    if (u < 0) {
      /* Update for Memory: '<S17>/Memory2' */
      GasPosMgm_DWork.Memory2_PreviousInput[i] = (int16_T)-u;
    } else {
      /* Update for Memory: '<S17>/Memory2' */
      GasPosMgm_DWork.Memory2_PreviousInput[i] = u;
    }

    /* End of Abs: '<S17>/Abs' */

    /* Update for Memory: '<S17>/Memory3' */
    GasPosMgm_DWork.Memory3_PreviousInput[i] = rtb_Memory2;
  }
}

/* Output and update for atomic system: '<S22>/IdleSW_VGasPos1_Func_Diag' */
void GasPo_IdleSW_VGasPos1_Func_Diag(uint8_T rtu_StIdleSwitch, uint16_T
  rtu_vgaspos, rtB_IdleSW_VGasPos1_Func_Diag_G *localB)
{
  uint8_T rtb_RelationalOperator3_bqpv;
  uint8_T rtb_RelationalOperator1_lnfo;

  /* RelationalOperator: '<S41>/Relational Operator3' incorporates:
   *  Constant: '<S41>/THVGASOUTIDLE1'
   */
  rtb_RelationalOperator3_bqpv = (uint8_T)(rtu_vgaspos <= THVGASOUTIDLE1);

  /* RelationalOperator: '<S41>/Relational Operator1' incorporates:
   *  Constant: '<S41>/THVGASIDLE1'
   */
  rtb_RelationalOperator1_lnfo = (uint8_T)(rtu_vgaspos > THVGASIDLE1);

  /* Logic: '<S41>/Logical Operator1' */
  localB->LogicalOperator1 = (uint8_T)((rtb_RelationalOperator3_bqpv != 0) ||
    (rtb_RelationalOperator1_lnfo != 0));

  /* Switch: '<S41>/Switch3' incorporates:
   *  Constant: '<S41>/GAS_IDLE'
   *  Constant: '<S41>/GAS_OUT_IDLE'
   *  Constant: '<S41>/NO_COHERENT_SENS'
   *  Constant: '<S41>/NO_PT_FAULT'
   *  Logic: '<S41>/Logical Operator2'
   *  Logic: '<S41>/Logical Operator3'
   *  Logic: '<S41>/Logical Operator4'
   *  RelationalOperator: '<S41>/Relational Operator2'
   *  RelationalOperator: '<S41>/Relational Operator8'
   */
  if (((rtu_StIdleSwitch == ((uint8_T)GAS_IDLE)) &&
       (rtb_RelationalOperator1_lnfo != 0)) || ((rtu_StIdleSwitch == ((uint8_T)
         GAS_OUT_IDLE)) && (rtb_RelationalOperator3_bqpv != 0))) {
    localB->Switch3 = NO_PT_FAULT;
  } else {
    localB->Switch3 = NO_COHERENT_SENS;
  }

  /* End of Switch: '<S41>/Switch3' */
}

/* Output and update for atomic system: '<S22>/IdleSW_VGasPos2_Func_Diag' */
void GasPo_IdleSW_VGasPos2_Func_Diag(uint8_T rtu_StIdleSwitch, uint16_T
  rtu_vgaspos, rtB_IdleSW_VGasPos2_Func_Diag_G *localB)
{
  uint8_T rtb_RelationalOperator3_pyr0;
  uint8_T rtb_RelationalOperator1_nlv2;

  /* RelationalOperator: '<S42>/Relational Operator3' incorporates:
   *  Constant: '<S42>/THVGASOUTIDLE2'
   */
  rtb_RelationalOperator3_pyr0 = (uint8_T)(rtu_vgaspos >= THVGASOUTIDLE2);

  /* RelationalOperator: '<S42>/Relational Operator1' incorporates:
   *  Constant: '<S42>/THVGASIDLE2'
   */
  rtb_RelationalOperator1_nlv2 = (uint8_T)(rtu_vgaspos < THVGASIDLE2);

  /* Logic: '<S42>/Logical Operator1' */
  localB->LogicalOperator1 = (uint8_T)((rtb_RelationalOperator3_pyr0 != 0) ||
    (rtb_RelationalOperator1_nlv2 != 0));

  /* Switch: '<S42>/Switch3' incorporates:
   *  Constant: '<S42>/GAS_IDLE'
   *  Constant: '<S42>/GAS_OUT_IDLE'
   *  Constant: '<S42>/NO_COHERENT_SENS'
   *  Constant: '<S42>/NO_PT_FAULT'
   *  Logic: '<S42>/Logical Operator2'
   *  Logic: '<S42>/Logical Operator3'
   *  Logic: '<S42>/Logical Operator4'
   *  RelationalOperator: '<S42>/Relational Operator2'
   *  RelationalOperator: '<S42>/Relational Operator8'
   */
  if (((rtu_StIdleSwitch == ((uint8_T)GAS_IDLE)) &&
       (rtb_RelationalOperator1_nlv2 != 0)) || ((rtu_StIdleSwitch == ((uint8_T)
         GAS_OUT_IDLE)) && (rtb_RelationalOperator3_pyr0 != 0))) {
    localB->Switch3 = NO_PT_FAULT;
  } else {
    localB->Switch3 = NO_COHERENT_SENS;
  }

  /* End of Switch: '<S42>/Switch3' */
}

/* System reset for function-call system: '<S11>/dbw' */
void GasPosMgm_dbw_Reset(void)
{
  /* SystemReset for Atomic SubSystem: '<S13>/Switch ElectricDiagTest' */
  /* InitializeConditions for UnitDelay: '<S23>/Unit Delay' */
  GasPosMgm_DWork.UnitDelay_DSTATE_ayxw = 0U;

  /* End of SystemReset for SubSystem: '<S13>/Switch ElectricDiagTest' */

  /* SystemReset for Atomic SubSystem: '<S13>/Calc_delta' */
  GasPosMgm_Calc_delta_Reset();

  /* End of SystemReset for SubSystem: '<S13>/Calc_delta' */

  /* SystemReset for Atomic SubSystem: '<S13>/Coherence_diag' */
  /* InitializeConditions for Memory: '<S18>/Memory1' */
  GasPosMgm_DWork.Memory1_PreviousInput_mxi5 = 0U;

  /* InitializeConditions for Memory: '<S18>/Memory' */
  GasPosMgm_DWork.Memory_PreviousInput_eqrl = 0U;

  /* End of SystemReset for SubSystem: '<S13>/Coherence_diag' */

  /* SystemReset for Chart: '<S13>/Calc_Recovery' */
  RecLimTorqueGas_tmp = 0U;
  RecGasType_tmp = 0U;

  /* SystemReset for Chart: '<S13>/Calc_Freeze_condition' */
  GasPosMgm_DWork.bitsForTID0.is_active_c2_GasPosMgm = 0U;
  GasPosMgm_DWork.bitsForTID0.is_c2_GasPosMgm = GasPosM_IN_NO_ACTIVE_CHILD_p3gm;
  FlgFreezeRecGas = 0U;

  /* SystemReset for Chart: '<S13>/Calc_Final_GasPos' */
  GasPosM_Calc_Final_GasPos_Reset();
}

/* Enable for function-call system: '<S11>/dbw' */
void GasPosMgm_dbw_Enable(void)
{
  /* Enable for Chart: '<S13>/Calc_Final_GasPos' */
  GasPos_Calc_Final_GasPos_Enable();
}

/* Output and update for function-call system: '<S11>/dbw' */
void GasPosMgm_dbw(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T rtb_DiagMgm_SetDiagState_npjx;
  uint8_T rtb_DiagMgm_SetDiagState_pdv3;
  uint8_T rtb_Compare;
  uint8_T rtb_FixPtLogicalOperator_jtbx;
  int16_T rtb_Product2[2];
  uint8_T rtb_Compare_oj4h;
  boolean_T rtb_RelationalOperator_avbk[2];
  int16_T rtb_Conversion;
  int32_T rtb_GenAbs;
  int32_T i;
  boolean_T guard1 = false;

  /* Outputs for Atomic SubSystem: '<S13>/Switch ElectricDiagTest' */
  /* Switch: '<S23>/Switch2' incorporates:
   *  Constant: '<S23>/GAS_IDLE'
   *  Constant: '<S23>/GAS_NOT_PLAUSIBLE'
   *  Constant: '<S23>/THLOW1GASSWDIA'
   *  Constant: '<S23>/THLOW2GASSWDIA'
   *  Constant: '<S23>/THUPP1GASSWDIA'
   *  Constant: '<S23>/THUPP2GASSWDIA'
   *  Inport: '<Root>/VGasIDLSw'
   *  Logic: '<S48>/FixPt Logical Operator'
   *  Logic: '<S49>/FixPt Logical Operator'
   *  RelationalOperator: '<S48>/Lower Test'
   *  RelationalOperator: '<S48>/Upper Test'
   *  RelationalOperator: '<S49>/Lower Test'
   *  RelationalOperator: '<S49>/Upper Test'
   *  Switch: '<S23>/Switch4'
   */
  if ((THUPP1GASSWDIA <= VGasIDLSw) && (VGasIDLSw <= THUPP2GASSWDIA)) {
    FlgGasNeg = ((uint8_T)GAS_IDLE);
  } else if ((THLOW1GASSWDIA <= VGasIDLSw) && (VGasIDLSw <= THLOW2GASSWDIA)) {
    /* Switch: '<S23>/Switch4' incorporates:
     *  Constant: '<S23>/GAS_OUT_IDLE'
     */
    FlgGasNeg = ((uint8_T)GAS_OUT_IDLE);
  } else {
    FlgGasNeg = ((uint8_T)GAS_NOT_PLAUSIBLE);
  }

  /* End of Switch: '<S23>/Switch2' */

  /* MultiPortSwitch: '<S23>/Multiport Switch' incorporates:
   *  Constant: '<S23>/GPNEGWITHIDLE'
   *  Constant: '<S23>/ZERO'
   */
  switch (GPNEGWITHIDLE) {
   case 0:
    StIdleSwitch = FlgGasNeg;
    break;

   case 1:
    StIdleSwitch = FlgGasNeg;
    break;

   case 2:
    StIdleSwitch = 0U;
    break;

   default:
    StIdleSwitch = 0U;
    break;
  }

  /* End of MultiPortSwitch: '<S23>/Multiport Switch' */

  /* S-Function (DiagMgm_RangeCheck_U16): '<S46>/DiagMgm_RangeCheck_U16' incorporates:
   *  Constant: '<S23>/CC_TO_GND'
   *  Constant: '<S23>/CC_TO_VCC'
   *  Constant: '<S23>/THLOW1GASSWDIA'
   *  Constant: '<S23>/THUPP2GASSWDIA'
   *  Inport: '<Root>/VGasIDLSw'
   */
  DiagMgm_RangeCheck_U16( &rtb_Compare, VGasIDLSw, THLOW1GASSWDIA,
    THUPP2GASSWDIA, CC_TO_GND, CC_TO_VCC);

  /* Logic: '<S47>/FixPt Logical Operator' incorporates:
   *  Constant: '<S23>/THLOW2GASSWDIA'
   *  Constant: '<S23>/THUPP1GASSWDIA'
   *  Inport: '<Root>/VGasIDLSw'
   *  RelationalOperator: '<S47>/Lower Test'
   *  RelationalOperator: '<S47>/Upper Test'
   */
  rtb_FixPtLogicalOperator_jtbx = (uint8_T)((THLOW2GASSWDIA < VGasIDLSw) &&
    (VGasIDLSw < THUPP1GASSWDIA));

  /* Switch: '<S23>/Switch1' incorporates:
   *  Constant: '<S23>/NO_PT_FAULT'
   *  RelationalOperator: '<S23>/Relational Operator'
   */
  if (NO_PT_FAULT == rtb_Compare) {
    /* Switch: '<S23>/Switch3' incorporates:
     *  Constant: '<S23>/SIG_NOT_PLAUSIBLE'
     *  Logic: '<S23>/Logical Operator'
     *  UnitDelay: '<S23>/Unit Delay'
     */
    if ((rtb_FixPtLogicalOperator_jtbx != 0) &&
        (GasPosMgm_DWork.UnitDelay_DSTATE_ayxw != 0)) {
      rtb_Compare = SIG_NOT_PLAUSIBLE;
    } else {
      rtb_Compare = NO_PT_FAULT;
    }

    /* End of Switch: '<S23>/Switch3' */
  }

  /* End of Switch: '<S23>/Switch1' */

  /* S-Function (DiagMgm_SetDiagState): '<S50>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S23>/DIAG_VGASSWITCH'
   */
  DiagMgm_SetDiagState( DIAG_VGASSWITCH, rtb_Compare, &rtb_DiagMgm_SetDiagState);

  /* Update for UnitDelay: '<S23>/Unit Delay' */
  GasPosMgm_DWork.UnitDelay_DSTATE_ayxw = rtb_FixPtLogicalOperator_jtbx;

  /* End of Outputs for SubSystem: '<S13>/Switch ElectricDiagTest' */

  /* Outputs for Atomic SubSystem: '<S13>/InverseSensorCharacteristic' */
  /* Product: '<S20>/Product2' incorporates:
   *  Constant: '<S20>/GASPOSGAIN'
   *  Constant: '<S20>/GASPOSOFFSET'
   *  Inport: '<Root>/VGasPos1'
   *  Inport: '<Root>/VGasPos2'
   *  Sum: '<S20>/Sum2'
   */
  rtb_GenAbs = ((int16_T)(VGasPos1 - GASPOSOFFSET[0]) * GASPOSGAIN[0]) >> 7;
  if (rtb_GenAbs > 32767) {
    rtb_GenAbs = 32767;
  } else {
    if (rtb_GenAbs < -32768) {
      rtb_GenAbs = -32768;
    }
  }

  rtb_GenAbs = (rtb_GenAbs * 625) >> 9;
  if (rtb_GenAbs > 32767) {
    rtb_GenAbs = 32767;
  } else {
    if (rtb_GenAbs < -32768) {
      rtb_GenAbs = -32768;
    }
  }

  rtb_Product2[0] = (int16_T)rtb_GenAbs;
  i = ((int16_T)(VGasPos2 - GASPOSOFFSET[1]) * GASPOSGAIN[1]) >> 7;
  if (i > 32767) {
    i = 32767;
  } else {
    if (i < -32768) {
      i = -32768;
    }
  }

  i = (i * 625) >> 9;
  if (i > 32767) {
    i = 32767;
  } else {
    if (i < -32768) {
      i = -32768;
    }
  }

  rtb_Product2[1] = (int16_T)i;

  /* DataTypeConversion: '<S20>/Data Type Conversion' */
  for (i = 0; i < 2; i++) {
    /* Saturate: '<S20>/Saturation' */
    if (rtb_Product2[i] > SATMAXPERCGAS) {
      GasPosMgm_B.vtgaspos[i] = (uint16_T)SATMAXPERCGAS;
    } else if (rtb_Product2[i] < 0) {
      GasPosMgm_B.vtgaspos[i] = 0U;
    } else {
      GasPosMgm_B.vtgaspos[i] = (uint16_T)rtb_Product2[i];
    }

    /* End of Saturate: '<S20>/Saturation' */
  }

  /* End of DataTypeConversion: '<S20>/Data Type Conversion' */

  /* MultiPortSwitch: '<S20>/Multiport Switch' incorporates:
   *  Constant: '<S20>/GPNEGWITHIDLE'
   *  Constant: '<S20>/ZERO'
   *  Logic: '<S20>/Logical Operator'
   */
  switch (GPNEGWITHIDLE) {
   case 0:
    FlgGasNeg = 0U;
    break;

   case 1:
    /* RelationalOperator: '<S20>/Relational Operator' incorporates:
     *  Constant: '<S20>/GASNEGPERC'
     */
    for (i = 0; i < 2; i++) {
      rtb_RelationalOperator_avbk[i] = (rtb_Product2[i] < (GASNEGPERC << 1));
    }

    /* End of RelationalOperator: '<S20>/Relational Operator' */
    FlgGasNeg = (uint8_T)(rtb_RelationalOperator_avbk[0] ||
                          rtb_RelationalOperator_avbk[1]);
    break;

   case 2:
    break;

   default:
    FlgGasNeg = 0U;
    break;
  }

  /* End of MultiPortSwitch: '<S20>/Multiport Switch' */

  /* SignalConversion: '<S20>/Signal Conversion' incorporates:
   *  Product: '<S20>/Product2'
   */
  GasPos1Tot = (int16_T)rtb_GenAbs;

  /* End of Outputs for SubSystem: '<S13>/InverseSensorCharacteristic' */

  /* Outputs for Atomic SubSystem: '<S13>/Calc_delta' */
  GasPosMgm_Calc_delta();

  /* End of Outputs for SubSystem: '<S13>/Calc_delta' */

  /* Outputs for Atomic SubSystem: '<S13>/ElectricDiagTest' */
  /* S-Function (DiagMgm_RangeCheck_U16): '<S30>/DiagMgm_RangeCheck_U16' incorporates:
   *  Constant: '<S19>/CC_TO_GND'
   *  Constant: '<S19>/CC_TO_VCC'
   *  Constant: '<S19>/THLOWGASPOSDIA'
   *  Constant: '<S19>/THUPPGASPOSDIA'
   *  Inport: '<Root>/VGasPos2'
   */
  DiagMgm_RangeCheck_U16( &rtb_Compare_oj4h, VGasPos2, THLOWGASPOSDIA,
    THUPPGASPOSDIA, CC_TO_GND, CC_TO_VCC);

  /* S-Function (DiagMgm_RangeCheck_U16): '<S31>/DiagMgm_RangeCheck_U16' incorporates:
   *  Constant: '<S19>/CC_TO_GND'
   *  Constant: '<S19>/CC_TO_VCC'
   *  Constant: '<S19>/THLOWGASPOSDIA'
   *  Constant: '<S19>/THUPPGASPOSDIA'
   *  Inport: '<Root>/VGasPos1'
   */
  DiagMgm_RangeCheck_U16( &rtb_Compare, VGasPos1, THLOWGASPOSDIA, THUPPGASPOSDIA,
    CC_TO_GND, CC_TO_VCC);

  /* S-Function (DiagMgm_SetDiagState): '<S32>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S19>/DIAG_VGASPOS_1'
   */
  DiagMgm_SetDiagState( DIAG_VGASPOS_1, rtb_Compare,
                       &rtb_DiagMgm_SetDiagState_npjx);

  /* S-Function (DiagMgm_SetDiagState): '<S33>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S19>/DIAG_VGASPOS_2'
   */
  DiagMgm_SetDiagState( DIAG_VGASPOS_2, rtb_Compare_oj4h,
                       &rtb_DiagMgm_SetDiagState_pdv3);

  /* End of Outputs for SubSystem: '<S13>/ElectricDiagTest' */

  /* Outputs for Atomic SubSystem: '<S13>/Coherence_diag' */
  /* DataTypeConversion: '<S26>/Data Type Conversion4' incorporates:
   *  Sum: '<S18>/Subtract'
   */
  rtb_GenAbs = (int16_T)(GasPosMgm_B.vtgaspos[0] - GasPosMgm_B.vtgaspos[1]);

  /* S-Function (GenAbs): '<S26>/GenAbs' incorporates:
   *  Constant: '<S18>/INT16_TYPE'
   */
  rtb_GenAbs = GenAbs( rtb_GenAbs, INT16_TYPE);

  /* DataTypeConversion: '<S28>/Conversion' */
  rtb_Conversion = (int16_T)rtb_GenAbs;

  /* Logic: '<S18>/Logical Operator' incorporates:
   *  Constant: '<S18>/NO_PT_FAULT'
   *  RelationalOperator: '<S18>/Relational Operator1'
   *  RelationalOperator: '<S18>/Relational Operator3'
   */
  GasCoh_DiagEn = (uint8_T)((rtb_DiagMgm_SetDiagState_npjx == NO_PT_FAULT) &&
    (rtb_DiagMgm_SetDiagState_pdv3 == NO_PT_FAULT));

  /* RelationalOperator: '<S18>/Relational Operator' incorporates:
   *  Constant: '<S18>/THGASPOSNOTCOH'
   */
  rtb_Compare_oj4h = (uint8_T)(rtb_Conversion > (THGASPOSNOTCOH << 1));

  /* Logic: '<S18>/Logical Operator2' */
  rtb_Compare = (uint8_T)((GasCoh_DiagEn != 0) && (rtb_Compare_oj4h != 0));

  /* Memory: '<S18>/Memory1' */
  rtb_Compare_oj4h = GasPosMgm_DWork.Memory1_PreviousInput_mxi5;

  /* Switch: '<S18>/Switch2' incorporates:
   *  Constant: '<S18>/ONE'
   *  Constant: '<S18>/THGASPOSCOH'
   *  Logic: '<S18>/Logical Operator1'
   *  RelationalOperator: '<S18>/Relational Operator2'
   *  Switch: '<S18>/Switch3'
   */
  if ((rtb_Compare != 0) && (rtb_Compare_oj4h != 0)) {
    GasPosMgm_DWork.Memory_PreviousInput_eqrl = 1U;
  } else {
    if (rtb_Conversion < (THGASPOSCOH << 1)) {
      /* Switch: '<S18>/Switch3' incorporates:
       *  Constant: '<S18>/ZERO'
       */
      GasPosMgm_DWork.Memory_PreviousInput_eqrl = 0U;
    }
  }

  /* End of Switch: '<S18>/Switch2' */

  /* Switch: '<S18>/Switch1' incorporates:
   *  Constant: '<S18>/NO_COHERENT_SENS'
   *  Constant: '<S18>/NO_PT_FAULT1'
   */
  if (GasPosMgm_DWork.Memory_PreviousInput_eqrl != 0) {
    rtb_Compare_oj4h = NO_COHERENT_SENS;
  } else {
    rtb_Compare_oj4h = NO_PT_FAULT;
  }

  /* End of Switch: '<S18>/Switch1' */

  /* Outputs for Enabled SubSystem: '<S18>/Sensor_coherence' incorporates:
   *  EnablePort: '<S27>/Enable'
   */
  if (GasCoh_DiagEn > 0) {
    GasPosMgm_DWork.Sensor_coherence_MODE = true;

    /* S-Function (DiagMgm_SetDiagState): '<S29>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S27>/DIAG_COH_VGASPOS'
     */
    DiagMgm_SetDiagState( DIAG_COH_VGASPOS, rtb_Compare_oj4h,
                         &GasPosMgm_B.DiagMgm_SetDiagState);
  } else {
    if (GasPosMgm_DWork.Sensor_coherence_MODE) {
      /* Disable for Outport: '<S27>/stdiag_coh' */
      GasPosMgm_B.DiagMgm_SetDiagState = 0U;
      GasPosMgm_DWork.Sensor_coherence_MODE = false;
    }
  }

  /* End of Outputs for SubSystem: '<S18>/Sensor_coherence' */

  /* Update for Memory: '<S18>/Memory1' */
  GasPosMgm_DWork.Memory1_PreviousInput_mxi5 = rtb_Compare;

  /* End of Outputs for SubSystem: '<S13>/Coherence_diag' */

  /* Outputs for Atomic SubSystem: '<S13>/Switch Coherence DiagTest' */
  /* Outputs for Atomic SubSystem: '<S22>/IdleSW_VGasPos1_Func_Diag' */
  /* Inport: '<Root>/VGasPos1' */
  GasPo_IdleSW_VGasPos1_Func_Diag(StIdleSwitch, VGasPos1,
    &GasPosMgm_B.IdleSW_VGasPos1_Func_Diag);

  /* End of Outputs for SubSystem: '<S22>/IdleSW_VGasPos1_Func_Diag' */

  /* Outputs for Atomic SubSystem: '<S22>/IdleSW_VGasPos2_Func_Diag' */
  /* Inport: '<Root>/VGasPos2' */
  GasPo_IdleSW_VGasPos2_Func_Diag(StIdleSwitch, VGasPos2,
    &GasPosMgm_B.IdleSW_VGasPos2_Func_Diag);

  /* End of Outputs for SubSystem: '<S22>/IdleSW_VGasPos2_Func_Diag' */

  /* Chart: '<S22>/Chart' */
  /* Gateway: GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/Chart */
  /* During: GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/Chart */
  /* Entry Internal: GasPosMgm/T10ms/dbw/Switch Coherence DiagTest/Chart */
  /* Transition: '<S38>:2' */
  if (GasPosMgm_B.IdleSW_VGasPos1_Func_Diag.LogicalOperator1 != 0) {
    /* Transition: '<S38>:4' */
    rtb_Compare = 1U;
    if (GasPosMgm_B.IdleSW_VGasPos2_Func_Diag.LogicalOperator1 != 0) {
      /* Transition: '<S38>:12' */
      if (GasPosMgm_B.IdleSW_VGasPos1_Func_Diag.Switch3 >
          GasPosMgm_B.IdleSW_VGasPos2_Func_Diag.Switch3) {
        rtb_Compare_oj4h = GasPosMgm_B.IdleSW_VGasPos1_Func_Diag.Switch3;
      } else {
        rtb_Compare_oj4h = GasPosMgm_B.IdleSW_VGasPos2_Func_Diag.Switch3;
      }
    } else {
      /* Transition: '<S38>:14' */
      rtb_Compare_oj4h = GasPosMgm_B.IdleSW_VGasPos1_Func_Diag.Switch3;
    }
  } else {
    /* Transition: '<S38>:6' */
    if (GasPosMgm_B.IdleSW_VGasPos2_Func_Diag.LogicalOperator1 != 0) {
      /* Transition: '<S38>:8' */
      rtb_Compare = 1U;
      rtb_Compare_oj4h = GasPosMgm_B.IdleSW_VGasPos2_Func_Diag.Switch3;
    } else {
      /* Transition: '<S38>:10' */
      rtb_Compare = 0U;
      rtb_Compare_oj4h = NO_PT_FAULT;
    }
  }

  /* End of Chart: '<S22>/Chart' */

  /* Logic: '<S22>/Logical Operator1' incorporates:
   *  Constant: '<S22>/NO_FAULT'
   *  Logic: '<S22>/Logical Operator'
   *  RelationalOperator: '<S22>/Relational Operator1'
   *  RelationalOperator: '<S22>/Relational Operator2'
   *  RelationalOperator: '<S22>/Relational Operator8'
   */
  GasSwCoh_DiagEn = (uint8_T)((rtb_Compare != 0) && ((rtb_DiagMgm_SetDiagState ==
    NO_FAULT) && (rtb_DiagMgm_SetDiagState_npjx == NO_FAULT) &&
    (rtb_DiagMgm_SetDiagState_pdv3 == NO_FAULT)));

  /* Outputs for Enabled SubSystem: '<S22>/Diag_GasSW_coh' incorporates:
   *  EnablePort: '<S39>/Enable'
   */
  if (GasSwCoh_DiagEn > 0) {
    /* S-Function (DiagMgm_SetDiagState): '<S43>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S39>/DIAG_VGASSWCOH'
     */
    DiagMgm_SetDiagState( DIAG_VGASSWCOH, rtb_Compare_oj4h, (&(StDiagGasSwCoh)));
  }

  /* End of Outputs for SubSystem: '<S22>/Diag_GasSW_coh' */

  /* Outputs for Atomic SubSystem: '<S22>/FlgGasZero_Calc' */
  /* RelationalOperator: '<S44>/Compare' */
  rtb_Compare = (uint8_T)(GasPosMgm_B.vtgaspos[0] == 0);

  /* RelationalOperator: '<S45>/Compare' */
  rtb_Compare_oj4h = (uint8_T)(GasPosMgm_B.vtgaspos[1] == 0);

  /* Logic: '<S40>/Logical Operator1' incorporates:
   *  Constant: '<S40>/USE_1ST_SENS'
   *  Constant: '<S40>/USE_2ND_SENS'
   *  DataStoreRead: '<S40>/Data Store Read2'
   *  Logic: '<S40>/Logical Operator2'
   *  Logic: '<S40>/Logical Operator3'
   *  Logic: '<S40>/Logical Operator4'
   *  RelationalOperator: '<S40>/Relational Operator3'
   *  RelationalOperator: '<S40>/Relational Operator4'
   */
  FlgGasZero = (uint8_T)(((RecGasType == ((uint8_T)USE_1ST_SENS)) &&
    (rtb_Compare != 0)) || ((RecGasType == ((uint8_T)USE_2ND_SENS)) &&
    (rtb_Compare_oj4h != 0)) || ((rtb_Compare != 0) && (rtb_Compare_oj4h != 0)));

  /* End of Outputs for SubSystem: '<S22>/FlgGasZero_Calc' */
  /* End of Outputs for SubSystem: '<S13>/Switch Coherence DiagTest' */

  /* Chart: '<S13>/Calc_Recovery' */
  /* Gateway: GasPosMgm/T10ms/dbw/Calc_Recovery */
  /* During: GasPosMgm/T10ms/dbw/Calc_Recovery */
  /* Entry Internal: GasPosMgm/T10ms/dbw/Calc_Recovery */
  /* Transition: '<S16>:3' */
  if (FORCEGASSENS == 1) {
    /* Transition: '<S16>:29' */
    if (rtb_DiagMgm_SetDiagState_npjx != FAULT) {
      /* Transition: '<S16>:31' */
      RecGasType_tmp = ((uint8_T)USE_1ST_SENS);
      RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
    } else {
      /* Transition: '<S16>:37' */
      RecGasType_tmp = ((uint8_T)WAIT_IDLE);
      RecLimTorqueGas_tmp = ((uint8_T)FORCE_IDLE);
    }
  } else {
    /* Transition: '<S16>:32' */
    if (FORCEGASSENS == 2) {
      /* Transition: '<S16>:38' */
      if (rtb_DiagMgm_SetDiagState_pdv3 != FAULT) {
        /* Transition: '<S16>:252' */
        RecGasType_tmp = ((uint8_T)USE_2ND_SENS);
        RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
      } else {
        /* Transition: '<S16>:253' */
        RecGasType_tmp = ((uint8_T)WAIT_IDLE);
        RecLimTorqueGas_tmp = ((uint8_T)FORCE_IDLE);
      }
    } else {
      /* Transition: '<S16>:45' */
      if (rtb_DiagMgm_SetDiagState == NO_FAULT) {
        /* Transition: '<S16>:257' */
        /*  VGasIDLSw diag */
        if (rtb_DiagMgm_SetDiagState_npjx == NO_FAULT) {
          /* Transition: '<S16>:46' */
          /*  VGasPos1 diag */
          if (rtb_DiagMgm_SetDiagState_pdv3 == NO_FAULT) {
            /* Transition: '<S16>:48' */
            /*  VGasPos2 diag */
            if (GasPosMgm_B.DiagMgm_SetDiagState == NO_FAULT) {
              /* Transition: '<S16>:51' */
              /*  VGasPos1/2 coherence */
              if (StDiagGasSwCoh == NO_FAULT) {
                /* Transition: '<S16>:133' */
                /*  GasPos/StIdleSwitch coherence */
                if (DEFMASTSENS == 1) {
                  /* Transition: '<S16>:55' */
                  RecGasType_tmp = ((uint8_T)USE_1ST_SENS);
                  RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
                } else {
                  /* Transition: '<S16>:57' */
                  RecGasType_tmp = ((uint8_T)USE_2ND_SENS);
                  RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
                }
              } else {
                /* Transition: '<S16>:193' */
                if (StDiagGasSwCoh == FAULT_FILTERING) {
                  /* Transition: '<S16>:134' */
                  RecGasType_tmp = ((uint8_T)MIN_SENS);
                  RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
                } else {
                  /* Transition: '<S16>:136' */
                  RecGasType_tmp = ((uint8_T)WAIT_IDLE);
                  RecLimTorqueGas_tmp = ((uint8_T)FORCE_IDLE);
                }
              }
            } else {
              /* Transition: '<S16>:69' */
              if (GasPosMgm_B.DiagMgm_SetDiagState == FAULT_FILTERING) {
                /* Transition: '<S16>:70' */
                RecGasType_tmp = ((uint8_T)MIN_SENS);
                RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
              } else {
                /* Transition: '<S16>:270' */
                if (StDiagGasSwCoh == FAULT) {
                  /* Transition: '<S16>:274' */
                  RecGasType_tmp = ((uint8_T)WAIT_IDLE);
                  RecLimTorqueGas_tmp = ((uint8_T)FORCE_IDLE);
                } else {
                  /* Transition: '<S16>:74' */
                  RecGasType_tmp = ((uint8_T)MIN_SENS);
                  RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);
                }
              }
            }
          } else {
            /* Transition: '<S16>:60' */
            if (rtb_DiagMgm_SetDiagState_pdv3 == FAULT_FILTERING) {
              /* Transition: '<S16>:62' */
              RecGasType_tmp = ((uint8_T)USE_1ST_SENS);
              RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
            } else {
              /* Transition: '<S16>:77' */
              RecGasType_tmp = ((uint8_T)USE_1ST_SENS);
              RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);
            }
          }
        } else {
          /* Transition: '<S16>:176' */
          if (rtb_DiagMgm_SetDiagState_npjx == FAULT_FILTERING) {
            /* Transition: '<S16>:84' */
            if (rtb_DiagMgm_SetDiagState_pdv3 == NO_FAULT) {
              /* Transition: '<S16>:89' */
              RecGasType_tmp = ((uint8_T)USE_2ND_SENS);
              RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
            } else {
              /* Transition: '<S16>:212' */
              if (rtb_DiagMgm_SetDiagState_pdv3 == FAULT_FILTERING) {
                /* Transition: '<S16>:209' */
                RecGasType_tmp = ((uint8_T)MIN_SENS);
                RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
              } else {
                /* Transition: '<S16>:91' */
                RecGasType_tmp = ((uint8_T)MIN_SENS);
                RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);
              }
            }
          } else {
            /* Transition: '<S16>:97' */
            guard1 = false;
            if (rtb_DiagMgm_SetDiagState_npjx == FAULT) {
              /* Transition: '<S16>:98' */
              if (rtb_DiagMgm_SetDiagState_pdv3 == NO_FAULT) {
                /* Transition: '<S16>:100' */
                RecGasType_tmp = ((uint8_T)USE_2ND_SENS);
                RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);
              } else {
                /* Transition: '<S16>:104' */
                if (rtb_DiagMgm_SetDiagState_pdv3 == FAULT_FILTERING) {
                  /* Transition: '<S16>:102' */
                  RecGasType_tmp = ((uint8_T)MIN_SENS);
                  RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);
                } else if (rtb_DiagMgm_SetDiagState_pdv3 == FAULT) {
                  /* Transition: '<S16>:107' */
                  RecGasType_tmp = ((uint8_T)WAIT_IDLE);
                  RecLimTorqueGas_tmp = ((uint8_T)FORCE_IDLE);
                } else {
                  guard1 = true;
                }
              }
            } else {
              guard1 = true;
            }

            if (guard1) {
              /* Transition: '<S16>:181' */
              RecGasType_tmp = MAX_uint8_T;
              RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);

              /*  IMPOSSIBLE!! */
            }
          }
        }
      } else {
        /* Transition: '<S16>:251' */
        if (rtb_DiagMgm_SetDiagState == FAULT_FILTERING) {
          /* Transition: '<S16>:246' */
          if (DEFMASTSENS == 1) {
            /* Transition: '<S16>:241' */
            RecGasType_tmp = ((uint8_T)USE_1ST_SENS);
            RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
          } else {
            /* Transition: '<S16>:242' */
            RecGasType_tmp = ((uint8_T)USE_2ND_SENS);
            RecLimTorqueGas_tmp = ((uint8_T)NO_TRQ_LIM);
          }
        } else {
          /* Transition: '<S16>:243' */
          if (rtb_DiagMgm_SetDiagState == FAULT) {
            /* Transition: '<S16>:244' */
            RecGasType_tmp = ((uint8_T)WAIT_IDLE);
            RecLimTorqueGas_tmp = ((uint8_T)FORCE_IDLE);
          } else {
            /* Transition: '<S16>:245' */
            RecGasType_tmp = MAX_uint8_T;
            RecLimTorqueGas_tmp = ((uint8_T)HEAVY_TRQ_LIM);

            /*  IMPOSSIBLE!! */
          }
        }
      }
    }
  }

  /* End of Chart: '<S13>/Calc_Recovery' */

  /* Chart: '<S13>/Calc_Freeze_condition' */
  /* Gateway: GasPosMgm/T10ms/dbw/Calc_Freeze_condition */
  /* During: GasPosMgm/T10ms/dbw/Calc_Freeze_condition */
  if (GasPosMgm_DWork.bitsForTID0.is_active_c2_GasPosMgm == 0U) {
    /* Entry: GasPosMgm/T10ms/dbw/Calc_Freeze_condition */
    GasPosMgm_DWork.bitsForTID0.is_active_c2_GasPosMgm = 1U;

    /* Entry Internal: GasPosMgm/T10ms/dbw/Calc_Freeze_condition */
    /* Transition: '<S15>:3' */
    RecLimTorqueGas = RecLimTorqueGas_tmp;
    RecGasType = RecGasType_tmp;
    FlgFreezeRecGas = 0U;
    GasPosMgm_DWork.bitsForTID0.is_c2_GasPosMgm = GasPosMgm_IN_NO_FREEZE;
  } else if (GasPosMgm_DWork.bitsForTID0.is_c2_GasPosMgm == GasPosMgm_IN_FREEZE)
  {
    /* During 'FREEZE': '<S15>:2' */
    if ((FlgGasZero == 1) || (RecLimTorqueGas_tmp > RecLimTorqueGas)) {
      /* Transition: '<S15>:5' */
      RecLimTorqueGas = RecLimTorqueGas_tmp;
      RecGasType = RecGasType_tmp;
      FlgFreezeRecGas = 0U;
      GasPosMgm_DWork.bitsForTID0.is_c2_GasPosMgm = GasPosMgm_IN_NO_FREEZE;
    }
  } else {
    /* During 'NO_FREEZE': '<S15>:1' */
    /* Transition: '<S15>:6' */
    if ((FlgGasZero != 1) && (RecLimTorqueGas_tmp < RecLimTorqueGas)) {
      /* Transition: '<S15>:4' */
      FlgFreezeRecGas = 1U;
      GasPosMgm_DWork.bitsForTID0.is_c2_GasPosMgm = GasPosMgm_IN_FREEZE;
    } else {
      /* Transition: '<S15>:7' */
      RecLimTorqueGas = RecLimTorqueGas_tmp;
      RecGasType = RecGasType_tmp;
    }
  }

  /* End of Chart: '<S13>/Calc_Freeze_condition' */

  /* Logic: '<S21>/Logical Operator1' incorporates:
   *  Constant: '<S21>/ENSTOPCREEPING'
   *  Constant: '<S34>/Constant'
   *  Constant: '<S35>/Constant'
   *  Constant: '<S36>/Constant'
   *  Constant: '<S37>/Constant'
   *  Inport: '<Root>/FlgVehStop'
   *  Inport: '<Root>/GearPosClu'
   *  Inport: '<Root>/ParkBrakeSignal'
   *  Inport: '<Root>/RiderPresence'
   *  Logic: '<S21>/Logical Operator'
   *  RelationalOperator: '<S34>/Compare'
   *  RelationalOperator: '<S35>/Compare'
   *  RelationalOperator: '<S36>/Compare'
   *  RelationalOperator: '<S37>/Compare'
   */
  GasPosMgm_B.LogicalOperator1 = ((ENSTOPCREEPING != 0) && (GearPosClu != 0) &&
    (ParkBrakeSignal != 0) && ((RiderPresence == 0) || (FlgVehStop != 0)));

  /* Chart: '<S13>/Calc_Final_GasPos' */
  GasPosMgm_Calc_Final_GasPos();

  /* DataTypeConversion: '<S13>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S13>/Data Store Write5'
   */
  GasPos0 = (uint16_T)((uint32_T)GasPosMgm_B.tmpGasPos >> 3);

  /* SignalConversion: '<S13>/Signal Conversion' */
  GasPos1 = GasPosMgm_B.vtgaspos[0];

  /* SignalConversion: '<S13>/Signal Conversion1' */
  GasPos2 = GasPosMgm_B.vtgaspos[1];
}

/* Enable for function-call system: '<S1>/T10ms' */
void GasPosMgm_T10ms_Enable(void)
{
  /* SystemReset for Function Call SubSystem: '<S11>/dbw' */
  GasPosMgm_dbw_Reset();

  /* End of SystemReset for SubSystem: '<S11>/dbw' */

  /* Enable for Chart: '<S11>/Choose' incorporates:
   *  SubSystem: '<S11>/dbw'
   */
  GasPosMgm_dbw_Enable();
}

/* Output and update for function-call system: '<S1>/T10ms' */
void GasPosMgm_T10ms(void)
{
  /* Chart: '<S11>/Choose' incorporates:
   *  Inport: '<Root>/AngThrottle'
   */
  /* Gateway: GasPosMgm/T10ms/Choose */
  /* During: GasPosMgm/T10ms/Choose */
  /* Entry Internal: GasPosMgm/T10ms/Choose */
  /* Transition: '<S12>:1' */
  if (DBW == 0) {
    /* Transition: '<S12>:2' */
    RecLimTorqueGas = ((uint8_T)NO_TRQ_LIM);
    GasPos0 = (uint16_T)(AngThrottle << 1);
  } else {
    /* Outputs for Function Call SubSystem: '<S11>/dbw' */
    /* Transition: '<S12>:3' */
    /* Event: '<S12>:14' */
    GasPosMgm_dbw();

    /* End of Outputs for SubSystem: '<S11>/dbw' */
  }

  /* End of Chart: '<S11>/Choose' */
}

/* Output and update for function-call system: '<S1>/Init' */
void GasPosMgm_Init(void)
{
  /* DataStoreWrite: '<S10>/Data Store Write2' incorporates:
   *  Constant: '<S10>/DEFMASTSENS'
   */
  IndUsedSens = DEFMASTSENS;

  /* Switch: '<S10>/Switch' incorporates:
   *  Constant: '<S10>/DEFMASTSENS'
   *  Constant: '<S10>/ONE'
   *  Constant: '<S10>/USE_1ST_SENS'
   *  Constant: '<S10>/USE_2ND_SENS'
   *  DataStoreWrite: '<S10>/Data Store Write4'
   *  RelationalOperator: '<S10>/Relational Operator'
   */
  if (DEFMASTSENS == 1) {
    RecGasType = ((uint8_T)USE_1ST_SENS);
  } else {
    RecGasType = ((uint8_T)USE_2ND_SENS);
  }

  /* End of Switch: '<S10>/Switch' */

  /* DataStoreWrite: '<S10>/Data Store Write' incorporates:
   *  Constant: '<S10>/ZERO'
   */
  GasPos0 = 0U;

  /* DataStoreWrite: '<S10>/Data Store Write1' incorporates:
   *  Constant: '<S10>/NO_TRQ_LIM'
   */
  RecLimTorqueGas_Freeze = ((uint8_T)NO_TRQ_LIM);

  /* DataStoreWrite: '<S10>/Data Store Write3' incorporates:
   *  Constant: '<S10>/NO_TRQ_LIM'
   */
  RecLimTorqueGas = ((uint8_T)NO_TRQ_LIM);

  /* DataStoreWrite: '<S10>/Data Store Write5' incorporates:
   *  Constant: '<S10>/GAS_IDLE'
   */
  StIdleSwitch = ((uint8_T)GAS_IDLE);

  /* Constant: '<S10>/ID_SPEED_LIM_CTRL' */
  IDGasPosMgm = ID_SPEED_LIM_CTRL;
}

/* Model step function */
void GasPosMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/GasPosMgm' */

  /* Outputs for Triggered SubSystem: '<S1>/Call_10ms' incorporates:
   *  TriggerPort: '<S8>/ev_10ms'
   */
  /* Inport: '<Root>/ev_10ms' */
  if ((GasPosMgm_U.ev_10ms > 0) && (GasPosMgm_PrevZCSigState.Call_10ms_Trig_ZCE
       != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S8>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    GasPosMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S8>/Function-Call Generator' */
  }

  GasPosMgm_PrevZCSigState.Call_10ms_Trig_ZCE = (ZCSigState)(GasPosMgm_U.ev_10ms
    > 0);

  /* End of Inport: '<Root>/ev_10ms' */
  /* End of Outputs for SubSystem: '<S1>/Call_10ms' */

  /* Outputs for Triggered SubSystem: '<S1>/Call_Reset' incorporates:
   *  TriggerPort: '<S9>/ev_PowerOn'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((GasPosMgm_U.ev_PowerOn > 0) &&
      (GasPosMgm_PrevZCSigState.Call_Reset_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S9>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    GasPosMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S9>/Function-Call Generator' */
  }

  GasPosMgm_PrevZCSigState.Call_Reset_Trig_ZCE = (ZCSigState)
    (GasPosMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/Call_Reset' */

  /* End of Outputs for SubSystem: '<Root>/GasPosMgm' */
}

/* Model initialize function */
void GasPosMgm_initialize(void)
{
  /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_1' */
  RecLimTorqueGas = ((uint8_T)NO_TRQ_LIM);

  /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_3' */
  RecLimTorqueGas_Freeze = ((uint8_T)NO_TRQ_LIM);

  /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_4' */
  StIdleSwitch = ((uint8_T)NO_TRQ_LIM);
  GasPosMgm_PrevZCSigState.Call_10ms_Trig_ZCE = POS_ZCSIG;
  GasPosMgm_PrevZCSigState.Call_Reset_Trig_ZCE = POS_ZCSIG;

  /* Enable for Atomic SubSystem: '<Root>/GasPosMgm' */

  /* Enable for Triggered SubSystem: '<S1>/Call_10ms' */
  /* Enable for S-Function (fcncallgen): '<S8>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  GasPosMgm_T10ms_Enable();

  /* End of Enable for S-Function (fcncallgen): '<S8>/Function-Call Generator' */
  /* End of Enable for SubSystem: '<S1>/Call_10ms' */

  /* End of Enable for SubSystem: '<Root>/GasPosMgm' */
}

/* user code (bottom of source file) */
/* System '<Root>/GasPosMgm' */
#else

extern uint16_T AngThrottle;
uint8_T RecLimTorqueGas;
uint16_T GasPos;
uint8_T RecLimTorqueGas_Freeze;
uint8_T IndUsedSens;
void GasPosMgm_stub(void);
void GasPosMgm_Init(void)
{
  GasPosMgm_stub();
}

void GasPosMgm_T10ms(void)
{
  GasPosMgm_stub();
}

void GasPosMgm_stub(void)
{
  RecLimTorqueGas = NO_TRQ_LIM;
  RecLimTorqueGas_Freeze = NO_TRQ_LIM;
  IndUsedSens = 0;
  GasPos = AngThrottle;
}

#endif                                 // _BUILD_GASPOSMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
