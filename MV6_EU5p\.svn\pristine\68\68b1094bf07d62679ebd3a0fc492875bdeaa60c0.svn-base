/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "sys.h"
#include "typedefs.h"
#include "pio.h"

/* Tasks */
#include "OS_api.h"
#include "tasksdefs.h"


#ifdef _BUILD_PIOTEST_

//#define ETPU_ANGLE_TESTING  //to enalble  etpu angle testing
#define EMIOS_IRQ    //to enable PIN based on EMIOS

void  EMIOS_OutCfg_test(uint8_T pid);
void  ETPU_InCfg_test(uint8_T pid);
void TransitionManager(void);


#define PIOTEST_EMIOS_CH EMIOS_UC8 //EMIOS_UC8 //EMIOS_UC2
#define PIOPIN_EMIOS_CH  EMIOS_UC2 //EMIOS_UC8 //EMIOS_UC2
#define WAIT5s           50 
#define PWMIN_ETPU_CH    ETPUA_UC23//ETPUA_UC8
#define PIN_ETPU_CH      ETPUA_UC8
#define PIN_EMIOS_CH     EMIOS_UC10 //EMIOS_UC4
#define PIOTEST_TIMEOUT  250000

//#define PIOTEST_PWMOUT_CH   ETPUA_UC2
#define PIOTEST_PWMOUT_CH  PIOTEST_EMIOS_CH //PIOTEST_EMIOS_CH

#define EMIOS_CH2_PIN    181
#define EMIOS_CH4_PIN    183
#define EMIOS_CH8_PIN    187
#define EMIOS_CH9_PIN    188
#define EMIOS_CH10_PIN   189
#define EMIOS_CH12_PIN   191
#define EMIOS_CH14_PIN   193


#define ETPU_CH8_PIN     122
#define ETPU_CH2_PIN     116
#define ETPU_CH5_PIN     119
#define ETPU_CH9_PIN     123

#define ETPU_CH23_PIN    137

//#define PWMOUT_PIN         ETPU_CH2_PIN 
#define PWMOUT_PIN EMIOS_CH8_PIN //EMIOS_CH8_PIN 
#define PULSEINEMIOS_PIN   EMIOS_CH10_PIN //EMIOS_CH4_PIN
#define PWMIN_PIN          ETPU_CH23_PIN //ETPU_CH8_PIN 
#define PULSEIN_PIN        ETPU_CH8_PIN

#ifdef ETPU_ANGLE_TESTING
#define PIOTEST_PERIOD  (180*16)  /* 4 periodi per giro motore */
#else
#define PIOTEST_PERIOD   10000     //10 ms
#endif

#define PIOTEST_DUTY     40*256    //40% 

t_DutyCycle testDuty  = PIOTEST_DUTY;
t_Period testPeriod = PIOTEST_PERIOD;
uint8_t testupdate = 0;
uint8_t testupdatePer = 0;
uint8_t testupdateDuty = 0;

t_DutyCycle InDuty = 0;
t_Period InPeriod = 0;

uint32_t PInTRising = 0;
uint32_t PInTFalling = 0;
uint32_t PInTDuration = 0;
t_Distance distance = 0; 

#define PWMINBUFF_TESTPER    10000  //5 ms
#define PWMBUFF_ETPU_OUT_CH  ETPUA_UC5 
#define PWMBUFF_ETPU_IN_CH   ETPUA_UC9 

#define DUTY_BUFF_SIZE 16

t_DutyCycle dutyBuff[DUTY_BUFF_SIZE+2] = {10*256, 20*256, 30*256, 40*256, 50*256, 60*256, 70*256, 10*256, 20*256, 30*256, 
                                        40*256, 50*256, 60*256, 70*256, 80*256, 90*256 , 80*256, 10*256};//, 30*256, 50*256 };

t_Period PeriodBuff[16];
t_DutyCycle dutyBuffIn[16+2];

int16_t PIOTEST_Init(void)
{
    int16_t retval = NO_ERROR;

    retval = PIO_Enable();

    return retval;
}


int16_t PIOTEST_PwmInConfig(void)
{   
    int16_t retval = NO_ERROR;

    retval = PIO_PwmInConfig(PWMIN_ETPU_CH, (t_ActiveEdge)ACTIVE_HIGH, PIOTEST_TIMEOUT);

    ETPU_InCfg_test(PWMIN_PIN);

    if (retval == NO_ERROR)
        retval = PIO_PwmInEnable(PWMIN_ETPU_CH);

    return retval;
}

int16_t PIOTEST_PwmInGetDutyPeriod_100ms(void)
{
    int16_t retval = NO_ERROR;
    t_DutyCycle dutyCycle;
    static uint8_t count = 0; 

    /* called every sec */
    if (count == 10)
    {
        retval = PIO_PwmInGetDutyCycle(PWMIN_ETPU_CH, &InDuty);
        if (retval != NO_ERROR)
            return retval;
        
        InDuty = InDuty;
        
        retval = PIO_PwmInGetPeriod(PWMIN_ETPU_CH, &InPeriod);
        count = 0;

        //retval = PIO_PwmInGetBuffPeriod(PWMIN_ETPU_CH, PeriodBuff);
    }
    count++;
    
    return retval;
}

void FuncPIOChannel_triggered(void) 
{
    TransitionManager();

    TerminateTask();
}


void PIOTEST_PwmInBufferedConfig(void)
{
    int16_t retval = NO_ERROR;

    /* configure etpu ch5 as output channel */
    retval = PIO_PwmOutConfig(PWMBUFF_ETPU_OUT_CH, (t_ActiveEdge)ACTIVE_HIGH, PWM_MATCH_TIME, testDuty, PWMINBUFF_TESTPER, 0);

    EMIOS_OutCfg_test(ETPU_CH5_PIN);
    
    if (retval == NO_ERROR)
        retval = PIO_PwmOutEnable(PWMBUFF_ETPU_OUT_CH); 
    
    /* configure etpu ch9 as input channel */
    retval = PIO_PwmInConfig(PWMBUFF_ETPU_IN_CH, (t_ActiveEdge)ACTIVE_HIGH, PIOTEST_TIMEOUT);

    ETPU_InCfg_test(ETPU_CH9_PIN);

    if (retval == NO_ERROR)
        retval = PIO_PwmInEnable(PWMBUFF_ETPU_IN_CH);

    
}

uint8_t buffSize = 0;

void PIOTEST_PwmInBuffered_10ms(void)
{
    static int8_t count = 0;
    /* int8_t i; */

    PIO_PwmOutSetDutyCicle(PWMBUFF_ETPU_OUT_CH, dutyBuff[count++]);
        
    if (count == DUTY_BUFF_SIZE+2)
        count = 0;
    
    PIO_PwmInGetBuffDuty(PWMBUFF_ETPU_IN_CH, dutyBuffIn, &buffSize);
}
#ifndef ETPU_ANGLE_TESTING

int16_t PIOTEST_PwmOutConfig(void)
{
    int16_t retval = NO_ERROR;

    retval = PIO_PwmOutConfig(PIOTEST_PWMOUT_CH, (t_ActiveEdge)ACTIVE_HIGH, PWM_MATCH_TIME, testDuty, testPeriod, 0);

    //configure PIN of EMIOS ch 8 
    EMIOS_OutCfg_test(PWMOUT_PIN);
    
    if (retval == NO_ERROR)
        retval = PIO_PwmOutEnable(PIOTEST_PWMOUT_CH);
#if 0
    retval = PIO_PwmOutConfig(EMIOS_UC11, (t_ActiveEdge)ACTIVE_HIGH, PWM_MATCH_TIME, testDuty, testPeriod, 0);

    //configure PIN of EMIOS ch 8 
    EMIOS_OutCfg_test(190);
   
    if (retval == NO_ERROR)
        retval = PIO_PwmOutEnable(EMIOS_UC11);
#endif

    return retval;
}

int16_t PIOTEST_PwmOutSetDutyPeriod_100ms(void)
{
    int16_t retval = NO_ERROR;

    if(testupdatePer)
    {
        PIO_PwmOutSetPeriod(PIOTEST_PWMOUT_CH, testPeriod);
        //PIO_PwmOutSetPeriod(EMIOS_UC11, testPeriod);
        testupdatePer = 0;
    }    
    if(testupdateDuty)
    {
        PIO_PwmOutSetDutyCicle(PIOTEST_PWMOUT_CH, testDuty);
    }
    
    return retval;
}

#ifndef EMIOS_IRQ
int16_t PIOTEST_PinConfig(void)
{   
    int16_t retval = NO_ERROR;

    retval = PIO_PinConfig(PIN_ETPU_CH, (t_BusSelect)TIME, BOTH_EDGE, RISING_EDGE);

    PIO_PinSetInterruptHandler(PIN_ETPU_CH, PIOChannel_triggeredID);
    PIO_PinEnableInterrupt(PIN_ETPU_CH);
    
    ETPU_InCfg_test(PULSEIN_PIN);

    if (retval == NO_ERROR)
        retval = PIO_PinEnable(PIN_ETPU_CH);

    return retval;

}
#else
int16_t PIOTEST_PinConfig(void)
{   
    int16_t retval = NO_ERROR;

    return retval;
}
#endif

int16_t PIOTEST_PinEMIOS()
{
    int16_t retval = NO_ERROR;

    retval = PIO_PinConfig(PIN_EMIOS_CH, (t_BusSelect)TIME, BOTH_EDGE, RISING_EDGE);

    PIO_PinSetInterruptHandler(PIN_EMIOS_CH, PIOChannel_triggeredID);
    PIO_PinEnableInterrupt(PIN_EMIOS_CH);


    ETPU_InCfg_test(PULSEINEMIOS_PIN);

    if (retval == NO_ERROR)
        retval = PIO_PinEnable(PIN_EMIOS_CH);

    return retval;

}

void TransitionManager(void)
{
    int16_T error;

#ifdef EMIOS_IRQ
    error = PIO_PinGetDistance(PIN_EMIOS_CH, &PInTDuration);

    //PIO_PinEnable(PIN_EMIOS_CH);
    
#else
    t_EdgePolarity edgeType;
      
    error = PIO_PinGetLastTransition(PIN_ETPU_CH, &edgeType);

    if (error == NO_ERROR)
    {
      if(edgeType == RISING_EDGE) 
      {
          PIO_PinGetLastEdgeTime(PIN_ETPU_CH, &PInTRising);
      } 
      else
      {
          PIO_PinGetLastEdgeTime(PIN_ETPU_CH, &PInTFalling);
          if ((int32_t)PInTFalling - (int32_t)PInTRising >= 0)
              PInTDuration = PInTFalling - PInTRising;
          else
              PInTDuration = PInTFalling - PInTRising + 0x01000000;
            
      }        
    }  
    PIO_PinEnable(PIN_ETPU_CH);
#endif
}

#else

int16_t PIOTEST_PwmOutConfig(void)
{
    int16_t retval = NO_ERROR;

    retval = PIO_PwmOutConfig(PIOTEST_PWMOUT_CH, (t_ActiveEdge)ACTIVE_HIGH, PWM_MATCH_ANGLE, testDuty, testPeriod, 0);

    //configure PIN of ETPU ch 2
    EMIOS_OutCfg_test(PWMOUT_PIN);
    
    if (retval == NO_ERROR)
        retval = PIO_PwmOutEnable(PIOTEST_PWMOUT_CH);

    return retval;
}

int16_t PIOTEST_PwmOutSetDutyPeriod_100ms(void)
{
    int16_t retval = NO_ERROR;

    if(testupdate)
    {
        PIO_PwmOutSetPeriod(PIOTEST_PWMOUT_CH, testPeriod);
        PIO_PwmOutSetDutyCicle(PIOTEST_PWMOUT_CH, testDuty);
    }
    
    return retval;
}

int16_t PIOTEST_PinConfig(void)
{   
    int16_t retval = NO_ERROR;

    retval = PIO_PinConfig(PIN_ETPU_CH, (t_BusSelect)ANGLE, BOTH_EDGE, RISING_EDGE);

    PIO_PinSetInterruptHandler(PIN_ETPU_CH, PIOChannel_triggeredID);
    PIO_PinEnableInterrupt(PIN_ETPU_CH);
    
    ETPU_InCfg_test(PULSEIN_PIN);

    if (retval == NO_ERROR)
        retval = PIO_PinEnable(PIN_ETPU_CH);

    return retval;
}

void TransitionManager(void)
{

    t_EdgePolarity edgeType;
    int16_T error;
      
    error = PIO_PinGetLastTransition(PIN_ETPU_CH, &edgeType);

    if (error == NO_ERROR)
    {
        if(edgeType == RISING_EDGE) 
        {
          PIO_PinGetLastEdgeAngle(PIN_ETPU_CH, &PInTRising);
        } 
        else
        {
          PIO_PinGetLastEdgeAngle(PIN_ETPU_CH, &PInTFalling);
          if ((int32_t)PInTFalling - (int32_t)PInTRising >= 0)
              PInTDuration = PInTFalling - PInTRising;
          else
              PInTDuration = PInTFalling - PInTRising + 0x01000000;
        }        
    }  
    PIO_PinEnable(PIN_ETPU_CH);
}


#endif

void  EMIOS_OutCfg_test(uint8_T pid)
{
    /* configure pin pid as an output pin driven by EMIOS */
    SIU.PCR[pid].B.PA   = 1;
    SIU.PCR[pid].B.IBE  = 0;
    SIU.PCR[pid].B.OBE  = 1;
}   /* end EMIOS_OutCfg(.) */

void  ETPU_InCfg_test(uint8_T pid)
{
    /* configure pin pid as an input pin driven */
    SIU.PCR[pid].B.PA   = 1;
    SIU.PCR[pid].B.IBE  = 1;
    SIU.PCR[pid].B.OBE  = 0;
}   /* end EMIOS_OutCfg(.) */

#endif // _BUILD_PIOTEST_

