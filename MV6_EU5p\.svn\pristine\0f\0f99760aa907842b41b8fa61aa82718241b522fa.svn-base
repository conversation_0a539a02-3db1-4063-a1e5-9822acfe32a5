/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL$ */
/* $Description:  $ */
/* $Revision$ */
/* $Date$ */
/* $Author$ */
/*****************************************************************************************************************/
/*
 * File: exhvalmgm_eep.c
 *
 * Real-Time Workshop code generated for Simulink model ExhValMgm.
 *
 * Model version                        : 1.902
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Thu May 22 10:37:32 2014
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Thu May 22 10:37:33 2014
 */
#if defined (_BUILD_EXHVALMGM_) || defined (_BUILD_EXHVALMGM_SBS_)
#include "rtwtypes.h"
#include "exhvalmgm_eep.h"

const uint8_T FlgSelfExhLMSOnce = 0;             /* flag to indicate that the self-learning of LMS has been done at least once */
const uint8_T FlgSelfExhUMSOnce = 0;             /* flag to indicate that the self-learning of UMS has been done at least once */
const uint16_T VAngExhClosed = VANG_EXH_CLOSED_DEF;
const uint16_T VAngExhOpen   = VANG_EXH_OPEN_DEF;
const uint16_T CntSelfExhTripEnable = 0;
const uint8_T  FlgLMSTripEnable = 1;
const uint32_T FlgUMSTripEnable = 1;

#endif /* _BUILD_EXHVALMGM_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
