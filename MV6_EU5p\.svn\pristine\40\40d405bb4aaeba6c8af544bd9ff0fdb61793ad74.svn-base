/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_GEARSHIFTMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//CmeDriver breakpoint vector for CutOff Gain management [Nm]
CALQUAL int16_T BKCMEGEARSHIFT[8] = 
{
 -64, 0, 320, 640, 960, 1600, 2240, 2880
};
//BK: VTFLGQSLOW [Rpm]
CALQUAL uint16_T BKFLGQSLOW[5] = 
{
   1000u,   3000u,   6000u,   8000u,  12000u
};
//GearPosQShift brakepoint [Counter]
CALQUAL uint8_T BKGEARPOS[7] = 
{
  0u,  1u,  2u,  3u,  4u,  5u,  6u
};
//Rpm breakpoint vector for CutOff Gain management [Rpm]
CALQUAL uint16_T BKRPMGEARSHIFT[8] = 
{
      0u,   1000u,   3000u,   5000u,   8000u,  11000u,  14000u,  17000u
};
//QuickShift cutoff offset brakepoint [rpm]
CALQUAL uint16_T BKRPMQSCTFOFFSET[5] = 
{
   3000u,   7000u,  10000u,  13000u,  17000u
};
//GasPosCC brakepoint [%]
CALQUAL uint16_T BKRPMQSGASPOS[2] = 
{
 0u, 640u
};
//Absolute difference between CmeDriverI and CmeQsIFilt to exit shifting Cme [%]
CALQUAL uint16_T CMEQSIDIFF = 32u;   //(   1.00000*32)
//Absolute difference between CmeDriverP and CmeQsDnPFilt to exit shifting Cme [%]
CALQUAL uint16_T CMEQSPDIFF = 32u;   //(   1.00000*32)
//If 1 Quick shifting is enabled [flag]
CALQUAL uint8_T ENQUICKSHIFT =  1u;   // 1
//Quick shift maximum duration [ms]
CALQUAL uint8_T MINTIMEQSHIFT =   30u;   //  30
//counter [counter]
CALQUAL uint8_T NUMQSDBLCTF =    1u;   //   1
//Cme threshold for down [Nm]
CALQUAL int16_T QSDNCMEDRIVERMAX = 3840;   //( 120.00000*32)
//K Filter gain [gain]
CALQUAL uint16_T QSGNFILTTOTIME = 64u;   //(0.5000000*128)
//Min gear QS [counter]
CALQUAL uint8_T QSMINGEARPOS =  2u;   // 2
//Rpm Hysteresis [rpm]
CALQUAL uint16_T QSRPMMAXHYS =      0u;   //     0
//Cme threshold for up [Nm]
CALQUAL int16_T QSUPCMEDRIVERMIN = 0;   //(   0.00000*32)
//QS ver 2 [flag]
CALQUAL uint8_T SELQS2 =  1u;   // 1
//Quick shift CmeQsI application period [ms]
CALQUAL uint8_T TBCMEQSDNIPERIOD[8*7] = 
{
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u
};
//Quick shift CmeQsI application period [ms]
CALQUAL uint8_T TBCMEQSUPIPERIOD[8*7] = 
{
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u
};
//Quick shift Cut Off period offset [ms]
CALQUAL int8_T TBQSCTFDNOFFSET[5*7] = 
{
   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    0,    0,    0,    0,    0,    0,    0,
    1,    1,    1,    1,    1,    1,    1,
    2,    2,    2,    2,    2,    2,    2,
    3,    3,    3,    3,    3,    3,    3
};
//Quick shift Cut Off period [ms]
CALQUAL uint8_T TBQSCTFDNPERIOD[8*7] = 
{
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u
};
//Quick shift Cut Off period offset [ms]
CALQUAL int8_T TBQSCTFUPOFFSET[5*7] = 
{
   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    0,    0,    0,    0,    0,    0,    0,
    1,    1,    1,    1,    1,    1,    1,
    2,    2,    2,    2,    2,    2,    2,
    3,    3,    3,    3,    3,    3,    3
};
//Quick shift Cut Off period [ms]
CALQUAL uint8_T TBQSCTFUPPERIOD[8*7] = 
{
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    2u,    2u,    2u,    2u,    2u,    2u,    2u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u,
    3u,    3u,    3u,    3u,    3u,    3u,    3u
};
//Rpm threshold [rpm]
CALQUAL uint16_T TBRPMQSHIFTDNMAX[7*2] = 
{
  20000u,  20000u,
  10000u,  10000u,
  10000u,  10000u,
  10000u,  10000u,
  10000u,  10000u,
  10000u,  10000u,
  10000u,  10000u
};
//CmeI for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPDNI[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeI for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPDNICTF[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeP for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPDNP[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeP for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPDNPCTF[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeI for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPUPI[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeI for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPUPICTF[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeP for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPUPP[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeP for quick Blip gear shifting [Nm]
CALQUAL int16_T VTCMEQSBLPUPPCTF[8] = 
{
 320, 320, 320, 320, 320, 320, 320, 320
};
//CmeI for quick Up gear shifting [Nm]
CALQUAL int16_T VTCMEQSCTFI[8] = 
{
 -64, 0, 320, 640, 960, 1600, 2240, 2880
};
//FlgQSLow [rpm]
CALQUAL int16_T VTFLGQSLOW[5] = 
{
 0, 0, 0, 0, 0
};
//Gsin CmeQs [gain]
CALQUAL int16_T VTGNCMEQSBLPDN[7] = 
{
 256, 256, 256, 256, 256, 256, 256
};
//Gsin CmeQs [gain]
CALQUAL int16_T VTGNCMEQSBLPUP[7] = 
{
 256, 256, 256, 256, 256, 256, 256
};
//GearShift Gain Down FOF constant [K]
CALQUAL uint16_T VTQSBLPDNFOFK[8] = 
{
 1638u, 3277u, 4915u, 6554u, 8192u, 9830u, 11469u, 13107u
};
//Quick shift Cme increment duration [ms]
CALQUAL uint8_T VTQSBLPDNTIME[7] = 
{
    0u,    0u,   20u,   10u,   10u,   10u,   10u
};
//Quick shift Cme increment duration [ms]
CALQUAL uint8_T VTQSBLPDNTIMECTF[7] = 
{
    0u,    0u,   10u,   10u,   10u,   10u,   10u
};
//Quick shift Cme increment duration [ms]
CALQUAL uint8_T VTQSBLPDNTOTIME[7] = 
{
    0u,    0u,   20u,   10u,   10u,   10u,   10u
};
//GearShift Gain Down FOF constant [K]
CALQUAL uint16_T VTQSBLPUPFOFK[8] = 
{
 1638u, 3277u, 4915u, 6554u, 8192u, 9830u, 11469u, 13107u
};
//Quick shift Cme increment duration [ms]
CALQUAL uint8_T VTQSBLPUPTIME[7] = 
{
    0u,    0u,   10u,   10u,   10u,   10u,   10u
};
//Quick shift Cme increment duration [ms]
CALQUAL uint8_T VTQSBLPUPTIMECTF[7] = 
{
    0u,    0u,   10u,   10u,   10u,   10u,   10u
};
//Quick shift Cme increment duration [ms]
CALQUAL uint8_T VTQSBLPUPTOTIME[7] = 
{
    0u,    0u,   20u,   10u,   10u,   10u,   10u
};
//GearShift Gain UP FOF constant [K]
CALQUAL uint16_T VTQSCTFDNFOFK[8] = 
{
 1638u, 3277u, 4915u, 6554u, 8192u, 9830u, 11469u, 13107u
};
//GearShift Gain UP FOF constant [K]
CALQUAL uint16_T VTQSCTFUPFOFK[8] = 
{
 1638u, 3277u, 4915u, 6554u, 8192u, 9830u, 11469u, 13107u
};
//Time [mS]
CALQUAL uint8_T VTQSDNDBLCTFTIME[7] = 
{
   10u,   10u,   10u,   10u,   10u,   10u,   10u
};
//Time [mS]
CALQUAL uint8_T VTQSDNDBLCTFTOTIME[7] = 
{
   50u,   50u,   50u,   50u,   50u,   50u,   50u
};
//Time [mS]
CALQUAL uint8_T VTQSUPDBLCTFTIME[7] = 
{
   10u,   10u,   10u,   10u,   10u,   10u,   10u
};
//Time [mS]
CALQUAL uint8_T VTQSUPDBLCTFTOTIME[7] = 
{
   50u,   50u,   50u,   50u,   50u,   50u,   50u
};
//Minimum Rpm value to enable quick up shifting [rpm]
CALQUAL uint16_T VTRPMQSHIFTUPMIN[7] = 
{
  20000u,   3000u,   3000u,   3000u,   3000u,   3000u,   3000u
};

#endif /* _BUILD_GEARSHIFTMGM_ */

