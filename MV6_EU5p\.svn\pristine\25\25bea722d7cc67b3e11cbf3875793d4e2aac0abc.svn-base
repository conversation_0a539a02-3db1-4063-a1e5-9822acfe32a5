/*
 * File: GearPosCluMgm_private.h
 *
 * Code generated for Simulink model 'GearPosCluMgm'.
 *
 * Model version                  : 1.323
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Jun 12 09:40:12 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_GearPosCluMgm_private_h_
#define RTW_HEADER_GearPosCluMgm_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "GearPosCluMgm.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "digitalin.h"
#include "PTrain_Diag.h"
#include "diagflags_out.h"
#include "gear_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint16_T THRCLUTCHRB;           /* Variable: THRCLUTCHRB
                                        * Referenced by: '<S9>/Constant3'
                                        * Speed thr stab
                                        */
extern uint8_T ENRBGEAR;               /* Variable: ENRBGEAR
                                        * Referenced by: '<S10>/LocalGear_calc'
                                        * Enable Recovery gear
                                        */
extern uint8_T RCGEAR;                 /* Variable: RCGEAR
                                        * Referenced by: '<S10>/LocalGear_calc'
                                        * Recovery gear
                                        */
extern uint8_T TIMCLUTCHRB;            /* Variable: TIMCLUTCHRB
                                        * Referenced by: '<S9>/Constant4'
                                        * Speed tim stab
                                        */
extern void GearPosCluMgm_T10ms(void);
extern void GearPosCluMgm_Init(void);
extern void GearPosCluMgm_Off(void);

#endif                                 /* RTW_HEADER_GearPosCluMgm_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
