/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_SPI_
#include "OS_api.h"
#include "tasksdefs.h"

#include "typedefs.h"
#include "spi.h"
#include "dma.h"
#include "task.h"
#include "events.h"

uint16_T dbCntMF_TLE;
uint16_T dbCntMF_Gain;
uint16_T dbCntMF_HB1;
uint16_T dbCntMF_HB2;
static volatile uint8_T spiResFlagCH[4];

/* local functions */
spi_error_t SPI_InitCh (uint8_t Channel,uint8_t Slave,uint8_t BR_scaler,uint8_t PBR_scaler, uint8_t DBR_value);


/* Local Buffer for TX and RX */
#if SPI_CH_A_EN 
static vuint32_t DSPI_TX_BUFFER_A [TX_BUFFER_SIZE];
static vuint32_t DSPI_RX_BUFFER_A [RX_BUFFER_SIZE];
static TaskType  SPI_ExTxDoneChAIrqVect[SPI_NUM_OF_CS] = { 0 };
#endif 

#if SPI_CH_B_EN 
static vuint32_t DSPI_TX_BUFFER_B [TX_BUFFER_SIZE];
static vuint32_t DSPI_RX_BUFFER_B [RX_BUFFER_SIZE];
static TaskType  SPI_ExTxDoneChBIrqVect[SPI_NUM_OF_CS] = { 0 };
#endif

#if SPI_CH_C_EN 
static vuint32_t DSPI_TX_BUFFER_C [TX_BUFFER_SIZE];
static vuint32_t DSPI_RX_BUFFER_C [RX_BUFFER_SIZE];
static TaskType  SPI_ExTxDoneChCIrqVect[SPI_NUM_OF_CS] = { 0 };
#endif

#if SPI_CH_D_EN 
static vuint32_t DSPI_TX_BUFFER_D [TX_BUFFER_SIZE];
static vuint32_t DSPI_RX_BUFFER_D [RX_BUFFER_SIZE];
static TaskType  SPI_ExTxDoneChDIrqVect[SPI_NUM_OF_CS] = { 0 };
#endif
 

static vuint32_t *DSPI_TX_BUFFER_CH [4];
static vuint32_t *DSPI_RX_BUFFER_CH [4];
static vuint8_t SPI_LastPCS[4];






const uint8_t SPI_DMA_Ch[] =
{
 0,  
 0,  
 DSPIB_SR_TFFF_DMA_Ch, 
 DSPIB_SR_RFDF_DMA_Ch, 
 DSPIC_SR_TFFF_DMA_Ch, 
 DSPIC_SR_RFDF_DMA_Ch, 
 0, 
 0
};


/* Pointer to the SPI  registers  */

const DSPI_ptr_t DSPI_ptr_array[]=
{
     (DSPI_ptr_t)0xFFF90000,
     (DSPI_ptr_t)0xFFF94000,
     (DSPI_ptr_t)0xFFF98000,
     (DSPI_ptr_t)0xFFF9C000
};



/* Pointer to the SPI Push Registers */
const vuint32_t DSPI_PUSHR[]=
{
    0,  
    DSPIB_PUSHR,
    DSPIC_PUSHR,
    0
};



/* Pointer to the SPI Pop Registers */
const vuint32_t DSPI_POPR[]=
{
    0,  
    DSPIB_POPR,
    DSPIC_POPR,
    0
};

const vuint8_t PCS_CONT[]=
{
    0,  
    SPI_CH_B_PCS_CONT,
    SPI_CH_C_PCS_CONT,
    0
};

#define     SPI_CH_FREE     0
#define     SPI_CH_BUSY     1
    

/* Global variables for the Driver Status */
uint16_t SPI_ConfigStatus=0;
uint8_t SPI_ChannelStatus [4]={0,0,0,0};


/**********************************************/
/*            SPI Prototypes for              */ 
/*         Tx Done Exceptions                 */
/* This methods are extern cause are user     */
/* defined.                                   */
/**********************************************/


/**********************************************/
/*            SPI Exported Methods            */ 
/**********************************************/

  
/**********************************************
Solinas 12/04/2005
void SPI_Config (...)
It uses the configuration parameters indicated
into the file spi.cfg
**********************************************/

spi_error_t SPI_Config (void) {

  spi_error_t retValue = NO_ERROR;

  if(SPI_ConfigStatus==0) 
  {
      uint8_t i;
      SPI_ConfigStatus = 1;      
      for (i=0;i<4;i++)
      {
        DSPI_TX_BUFFER_CH[i]=NULL;
      }
#if SPI_CH_A_EN 
      DSPI_TX_BUFFER_CH [0]=DSPI_TX_BUFFER_A;
      DSPI_RX_BUFFER_CH [0]=DSPI_RX_BUFFER_A;
      SPI_ConfigCh (SPI_CH_A,SPI_CH_A_MODE,SPI_CH_A_CNTS_SCK,SPI_CH_A_PCSn);
            
            
            /* Set Clock and Transfer Attributes */
#if PCSA0_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_A,0,SPI_CH_A0_FMSZ,SPI_CH_A0_CPOL,SPI_CH_A0_CPHA,
                                    SPI_CH_A0_CSSCK,SPI_CH_A0_DT,SPI_CH_A0_ASC,SPI_CH_A0_LSBFE);
      SPI_InitCh (SPI_CH_A,0,SPI_CH_A0_BR,SPI_CH_A0_BRP, SPI_CH_A0_DBR);                              
#endif
#if PCSA1_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_A,1,SPI_CH_A1_FMSZ,SPI_CH_A1_CPOL,SPI_CH_A1_CPHA,
                                    SPI_CH_A1_CSSCK,SPI_CH_A1_DT,SPI_CH_A1_ASC,SPI_CH_A1_LSBFE);
      SPI_InitCh (SPI_CH_A,1,SPI_CH_A1_BR,SPI_CH_A1_BRP, SPI_CH_A1_DBR);                              
#endif
#if PCSA2_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_A,2,SPI_CH_A2_FMSZ,SPI_CH_A2_CPOL,SPI_CH_A2_CPHA,
                                    SPI_CH_A2_CSSCK,SPI_CH_A2_DT,SPI_CH_A2_ASC,SPI_CH_A2_LSBFE);
      SPI_InitCh (SPI_CH_A,2,SPI_CH_A2_BR,SPI_CH_A2_BRP, SPI_CH_A2_DBR);                              
#endif
#if PCSA3_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_A,3,SPI_CH_A3_FMSZ,SPI_CH_A3_CPOL,SPI_CH_A3_CPHA,
                                    SPI_CH_A3_CSSCK,SPI_CH_A3_DT,SPI_CH_A3_ASC,SPI_CH_A3_LSBFE);
      SPI_InitCh (SPI_CH_A,3,SPI_CH_A3_BR,SPI_CH_A3_BRP, SPI_CH_A3_DBR);                              
#endif
#if PCSA4_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_A,4,SPI_CH_A4_FMSZ,SPI_CH_A4_CPOL,SPI_CH_A4_CPHA,
                                    SPI_CH_A4_CSSCK,SPI_CH_A4_DT,SPI_CH_A4_ASC,SPI_CH_A4_LSBFE);
      SPI_InitCh (SPI_CH_A,4,SPI_CH_A4_BR,SPI_CH_A4_BRP, SPI_CH_A4_DBR);                              
#endif
#if PCSA5_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_A,5,SPI_CH_A5_FMSZ,SPI_CH_A5_CPOL,SPI_CH_A5_CPHA,
                                    SPI_CH_A5_CSSCK,SPI_CH_A5_DT,SPI_CH_A5_ASC,SPI_CH_A5_LSBFE);
      SPI_InitCh (SPI_CH_A,5,SPI_CH_A5_BR,SPI_CH_A5_BRP, SPI_CH_A5_DBR);                              
#endif
                              
            /* SIU Configuration */

#if SPI_CH_A_EXTERNAL
      SPI_ConfigSiuChA(SPI_CH_A_MODE);
#endif
#endif 
      
#if SPI_CH_B_EN
      DSPI_TX_BUFFER_CH [1]=DSPI_TX_BUFFER_B; 
      DSPI_RX_BUFFER_CH [1]=DSPI_RX_BUFFER_B;
      SPI_ConfigCh (SPI_CH_B,SPI_CH_B_MODE,SPI_CH_B_CNTS_SCK,SPI_CH_B_PCSn);
      
        /* Set Clock and Transfer Attributes */
#if PCSB0_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_B,0,SPI_CH_B0_FMSZ,SPI_CH_B0_CPOL,SPI_CH_B0_CPHA,
                                    SPI_CH_B0_CSSCK,SPI_CH_B0_DT,SPI_CH_B0_ASC,SPI_CH_B0_LSBFE);
      SPI_InitCh (SPI_CH_B,0,SPI_CH_B0_BR,SPI_CH_B0_BRP, SPI_CH_B0_DBR);                              
#endif
#if PCSB1_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_B,1,SPI_CH_B1_FMSZ,SPI_CH_B1_CPOL,SPI_CH_B1_CPHA,
                                    SPI_CH_B1_CSSCK,SPI_CH_B1_DT,SPI_CH_B1_ASC,SPI_CH_B1_LSBFE);
      SPI_InitCh (SPI_CH_B,1,SPI_CH_B1_BR,SPI_CH_B1_BRP, SPI_CH_B1_DBR);                              
#endif
#if PCSB2_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_B,2,SPI_CH_B2_FMSZ,SPI_CH_B2_CPOL,SPI_CH_B2_CPHA,
                                    SPI_CH_B2_CSSCK,SPI_CH_B2_DT,SPI_CH_B2_ASC,SPI_CH_B2_LSBFE);
      SPI_InitCh (SPI_CH_B,2,SPI_CH_B2_BR,SPI_CH_B2_BRP, SPI_CH_B2_DBR);                              
#endif
#if PCSB3_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_B,3,SPI_CH_B3_FMSZ,SPI_CH_B3_CPOL,SPI_CH_B3_CPHA,
                                    SPI_CH_B3_CSSCK,SPI_CH_B3_DT,SPI_CH_B3_ASC,SPI_CH_B3_LSBFE);
      SPI_InitCh (SPI_CH_B,3,SPI_CH_B3_BR,SPI_CH_B3_BRP, SPI_CH_B3_DBR);                              
#endif
#if PCSB4_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_B,4,SPI_CH_B4_FMSZ,SPI_CH_B4_CPOL,SPI_CH_B4_CPHA,
                                    SPI_CH_B4_CSSCK,SPI_CH_B4_DT,SPI_CH_B4_ASC,SPI_CH_B4_LSBFE);
      SPI_InitCh (SPI_CH_B,4,SPI_CH_B4_BR,SPI_CH_B4_BRP, SPI_CH_B4_DBR);                              
#endif
#if PCSB5_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_B,5,SPI_CH_B5_FMSZ,SPI_CH_B5_CPOL,SPI_CH_B5_CPHA,
                                    SPI_CH_B5_CSSCK,SPI_CH_B5_DT,SPI_CH_B5_ASC,SPI_CH_B5_LSBFE);
      SPI_InitCh (SPI_CH_B,5,SPI_CH_B5_BR,SPI_CH_B5_BRP, SPI_CH_B5_DBR);                              
#endif
                              
            /* SIU Configuration */
      
      
      
#if SPI_CH_B_EXTERNAL
      SPI_ConfigSiuChB(SPI_CH_B_MODE);
#endif
#endif 
      
#if SPI_CH_C_EN
      DSPI_TX_BUFFER_CH [2]=DSPI_TX_BUFFER_C; 
      DSPI_RX_BUFFER_CH [2]=DSPI_RX_BUFFER_C;
      SPI_ConfigCh (SPI_CH_C,SPI_CH_C_MODE,SPI_CH_C_CNTS_SCK,SPI_CH_C_PCSn);
      
      /* Set Clock and Transfer Attributes */
#if PCSC0_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_C,0,SPI_CH_C0_FMSZ,SPI_CH_C0_CPOL,SPI_CH_C0_CPHA,
                                    SPI_CH_C0_CSSCK,SPI_CH_C0_DT,SPI_CH_C0_ASC,SPI_CH_C0_LSBFE);
      SPI_InitCh (SPI_CH_C,0,SPI_CH_C0_BR,SPI_CH_C0_BRP, SPI_CH_C0_DBR);                              
#endif
#if PCSC1_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_C,1,SPI_CH_C1_FMSZ,SPI_CH_C1_CPOL,SPI_CH_C1_CPHA,
                                    SPI_CH_C1_CSSCK,SPI_CH_C1_DT,SPI_CH_C1_ASC,SPI_CH_C1_LSBFE);
      SPI_InitCh (SPI_CH_C,1,SPI_CH_C1_BR,SPI_CH_C1_BRP, SPI_CH_C1_DBR);
#endif
#if PCSC2_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_C,2,SPI_CH_C2_FMSZ,SPI_CH_C2_CPOL,SPI_CH_C2_CPHA,
                                    SPI_CH_C2_CSSCK,SPI_CH_C2_DT,SPI_CH_C2_ASC,SPI_CH_C2_LSBFE);
      SPI_InitCh (SPI_CH_C,2,SPI_CH_C2_BR,SPI_CH_C2_BRP, SPI_CH_C2_DBR);                              
#endif
#if PCSC3_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_C,3,SPI_CH_C3_FMSZ,SPI_CH_C3_CPOL,SPI_CH_C3_CPHA,
                                    SPI_CH_C3_CSSCK,SPI_CH_C3_DT,SPI_CH_C3_ASC,SPI_CH_C3_LSBFE);
      SPI_InitCh (SPI_CH_C,3,SPI_CH_C3_BR,SPI_CH_C3_BRP, SPI_CH_C3_DBR);                              
#endif
#if PCSC4_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_C,4,SPI_CH_C4_FMSZ,SPI_CH_C4_CPOL,SPI_CH_C4_CPHA,
                                    SPI_CH_C4_CSSCK,SPI_CH_C4_DT,SPI_CH_C4_ASC,SPI_CH_C4_LSBFE);
      SPI_InitCh (SPI_CH_C,4,SPI_CH_C4_BR,SPI_CH_C4_BRP, SPI_CH_C4_DBR);                              
#endif
#if PCSC5_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_C,5,SPI_CH_C5_FMSZ,SPI_CH_C5_CPOL,SPI_CH_C5_CPHA,
                                    SPI_CH_C5_CSSCK,SPI_CH_C5_DT,SPI_CH_C5_ASC,SPI_CH_C5_LSBFE);
      SPI_InitCh (SPI_CH_C,5,SPI_CH_C5_BR,SPI_CH_C5_BRP, SPI_CH_C5_DBR);                              
#endif
                              
            /* SIU Configuration */
#if SPI_CH_C_EXTERNAL
      SPI_ConfigSiuChC(SPI_CH_C_MODE);
#endif
#endif 
      
      
      
#if SPI_CH_D_EN
      DSPI_TX_BUFFER_CH [3]=DSPI_TX_BUFFER_D; 
      DSPI_RX_BUFFER_CH [3]=DSPI_RX_BUFFER_D;
      SPI_ConfigCh (SPI_CH_D,SPI_CH_D_MODE,SPI_CH_D_CNTS_SCK,SPI_CH_D_PCSn);
      
        /* Set Clock and Transfer Attributes */
#if PCSD0_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_D,0,SPI_CH_D0_FMSZ,SPI_CH_D0_CPOL,SPI_CH_D0_CPHA,
                                    SPI_CH_D0_CSSCK,SPI_CH_D0_DT,SPI_CH_D0_ASC,SPI_CH_D0_LSBFE);
      SPI_InitCh (SPI_CH_D,0,SPI_CH_D0_BR,SPI_CH_D0_BRP, SPI_CH_D0_DBR);                              
#endif
#if PCSD1_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_D,1,SPI_CH_D1_FMSZ,SPI_CH_D1_CPOL,SPI_CH_D1_CPHA,
                                    SPI_CH_D1_CSSCK,SPI_CH_D1_DT,SPI_CH_D1_ASC,SPI_CH_D1_LSBFE);
      SPI_InitCh (SPI_CH_D,1,SPI_CH_D1_BR,SPI_CH_D1_BRP, SPI_CH_D1_DBR);                              
#endif
#if PCSD2_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_D,2,SPI_CH_D2_FMSZ,SPI_CH_D2_CPOL,SPI_CH_D2_CPHA,
                                    SPI_CH_D2_CSSCK,SPI_CH_D2_DT,SPI_CH_D2_ASC,SPI_CH_D2_LSBFE);
      SPI_InitCh (SPI_CH_D,2,SPI_CH_D2_BR,SPI_CH_D2_BRP, SPI_CH_D2_DBR);                              
#endif
#if PCSD3_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_D,3,SPI_CH_D3_FMSZ,SPI_CH_D3_CPOL,SPI_CH_D3_CPHA,
                                    SPI_CH_D3_CSSCK,SPI_CH_D3_DT,SPI_CH_D3_ASC,SPI_CH_D3_LSBFE);
      SPI_InitCh (SPI_CH_D,3,SPI_CH_D3_BR,SPI_CH_D3_BRP, SPI_CH_D3_DBR);                              
#endif
#if PCSD4_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_D,4,SPI_CH_D4_FMSZ,SPI_CH_D4_CPOL,SPI_CH_D4_CPHA,
                                    SPI_CH_D4_CSSCK,SPI_CH_D4_DT,SPI_CH_D4_ASC,SPI_CH_D4_LSBFE);
      SPI_InitCh (SPI_CH_D,4,SPI_CH_D4_BR,SPI_CH_D4_BRP, SPI_CH_D4_DBR);                              
#endif
#if PCSD5_ENABLE
      SPI_ConfigClockTransferParam (SPI_CH_D,5,SPI_CH_D5_FMSZ,SPI_CH_D5_CPOL,SPI_CH_D5_CPHA,
                                    SPI_CH_D5_CSSCK,SPI_CH_D5_DT,SPI_CH_D5_ASC,SPI_CH_D5_LSBFE);
      SPI_InitCh (SPI_CH_D,5,SPI_CH_D5_BR,SPI_CH_D5_BRP, SPI_CH_D5_DBR);                              
#endif
                              
            /* SIU Configuration */
            
#if SPI_CH_D_EXTERNAL
      SPI_ConfigSiuChD(SPI_CH_D_MODE);
#endif
#endif 
  
    /*SIU.DISR.R = 0x00000000;*/
    /* Solinas : No serial or parallel chaining, data
                                         is taken from the pins */
    /*Change this register for internal connections" */
  /*     Solinas For Example: */
    SIU.DISR.R = SPI_INTERNAL_CONNECTION;   
  } 
  else
  {
    retValue = PERIPHERAL_ALREADY_CONFIGURED; 
  }
  return retValue;
}


/**********************************************
Solinas 30/03/2005
void SPI_InitCh (Channel,Slave, Scalers)
This method change the BaudRate for the channel
<Channel>.
The SPI baud rate is calculated as
FSYS / (BaudRate_scaler*BaudRatePre_scaler)
Use this function only with the macro defined in spi.h
example:
SPI_set_baud_rate (SPI_CH_A,BR_SCALER_32,BRP_SCALER_2)
if FSYS=80 Mhz
Baud Rate =80/(32*2)=  1,25 Mbit/sec
**********************************************/

spi_error_t SPI_InitCh (uint8_t Channel,uint8_t Slave,uint8_t BR_scaler,uint8_t PBR_scaler,uint8_t DBR_value) 
{
    DSPI_ptr_t DSPI_ptr=DSPI_ptr_array[Channel];
    DSPI_ptr->CTAR[Slave].B.BR = BR_scaler;
    DSPI_ptr->CTAR[Slave].B.PBR = PBR_scaler;
    DSPI_ptr->CTAR[Slave].B.DBR = DBR_value;
    SPI_ChannelStatus[Channel] = 1;
    return NO_ERROR;
}



/**********************************************
Solinas 30/03/2005
void SPI_RxTxBuffers (...)
PCSn = Pheripheral chip select

**********************************************/

spi_error_t SPI_RxTxBuffer (uint8_t Channel,uint16_t *tx_buffer,uint8_t tx_size,uint8_t CTAS) { 
    int16_t ret;
    int8_t PCSn;

    if (SPI_ChannelStatus[Channel] != 0) /* initialized? */
    {
      if ((tx_size>0)&&(tx_size<=TX_BUFFER_SIZE))
      {
        DSPI_ptr_array[Channel]->MCR.R |= 0x00000001; /*Halt Bit is set */    
        DSPI_ptr_array[Channel]->RSER.R = 0x0;  
        DSPI_ptr_array[Channel]->SR.B.RFOF = 0; /* clean an eventual overflow condition */
        PCSn=1<<CTAS;
        SPI_LastPCS[Channel]=CTAS;
        ret = SPI_Write(Channel,tx_buffer,tx_size,PCSn,CTAS);
        if (ret == NO_ERROR)
        {
            DSPI_ptr_array[Channel]->SR.R  =  0x90000000; /* Clear EOQF */
            DSPI_ptr_array[Channel]->RSER.R |= 0x03030000;/* Setup TFFF to cause DMA transfer*/
                                                          /* Setup RFDF to cause DMA transfer*/
            DSPI_ptr_array[Channel]->MCR.R &= 0xFFFFFFFE; /* Halt Bit is clear */
        }
      }
      else
      {
        ret = SPI_WRONG_SIZE;
      }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED;
    }
    return ret;
}




/**********************************************
Solinas 21/04/2005
void SPI_GetRxData (...)

**********************************************/

spi_error_t SPI_GetRxData (uint8_t Channel,uint16_t *rx_buffer,uint8_t rx_size) 
{ 
    spi_error_t ret = NO_ERROR;
    if (SPI_ChannelStatus[Channel] != 0)  /* initialized? */
    {
        if ((rx_size>0)&&(rx_size<=RX_BUFFER_SIZE))
        {
            uint8_t cnt;
            vuint32_t *DSPI_RX_BUFFER;
            DSPI_RX_BUFFER=DSPI_RX_BUFFER_CH[Channel];
            for (cnt=0;cnt<rx_size;cnt++)
            {
                 rx_buffer[cnt]=(uint16_t) DSPI_RX_BUFFER[cnt];
            }
        }
        else
        {
            ret = SPI_WRONG_SIZE;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED; 
    }
    return ret;
}


/**********************************************
Solinas 21/04/2005
void SPI_GetChannelStatus (...)
**********************************************/

spi_error_t SPI_GetChannelStatus (uint8_t Channel) 
{ 
    spi_error_t ret;

    if (SPI_ChannelStatus[Channel] != 0) /* initialized? */
    {
        DSPI_ptr_t DSPI_ptr;
        DSPI_ptr=DSPI_ptr_array[Channel];   
        if ((DSPI_ptr->SR.R & 0x40000000)==0 ) 
        {
           ret = SPI_STOPPED;
        }
        else
        {
           ret = SPI_RUNNING;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED; 
    }
    return ret;
}



/**********************************************/
/*            SPI Internal Methods            */ 
/**********************************************/



/**********************************************************/
/* SPI_ConfigCh ()                                        */
/* This method configure the channel "Channel"            */
/* with the following parameters                          */
/* SPI_mode      :      MASTER/SLAVE                      */
/* Continuous_SCK: Continuous clock 0 Disable 1 Enable    */
/* PCSn          : Mask for the Chip selects.             */
/*                 0 Inactive state is low                */
/*                 1 Inactive state is high               */
/**********************************************************/

void SPI_ConfigCh (uint8_t Channel,bool SPI_Mode,bool Continuous_SCK,uint8_t PCSn)
{

    DSPI_ptr_t DSPI_ptr=DSPI_ptr_array[Channel];
    /* SPI_DmaConfig(Channel); */
  
    DSPI_ptr->MCR.R=0x00000001;
    DSPI_ptr->MCR.B.MSTR=SPI_Mode;
    DSPI_ptr->MCR.B.CONT_SCKE=Continuous_SCK;
    DSPI_ptr->MCR.R=(DSPI_ptr->MCR.R & PCSIS_MASK_N)|(PCSn<<16);

    DSPI_ptr_array[Channel]->RSER.R =0x0;
    
    switch (Channel)  {
     #if SPI_CH_A_EN
      case SPI_CH_A: {
        SPI_VectorInitA();
      }
       
      break;
     #endif
     #if SPI_CH_B_EN 
      case SPI_CH_B:  {
        SPI_VectorInitB();
      }
       
      break;
     #endif
     #if SPI_CH_C_EN
      case SPI_CH_C:  {
        SPI_VectorInitC();
      }  
      break;
     #endif
     #if SPI_CH_D_EN 
      case SPI_CH_D:  {
        SPI_VectorInitD();
      }  
      break;        
     #endif                     
            
       default:
      break;
    }
    
}


/**********************************************/
/* SPI_ConfigClockTransferParam               */
/* This method configure the channel "Channel"*/
/* with the following parameters                            */
/* Frame Bit Size: FRAMESIZE_x with x=[4..16] */   
/* Clock Polarity: 0 Inactive state is low      */
/* Clock Polarity: 1 Inactive state is high   */
/**********************************************/

void SPI_ConfigClockTransferParam (uint8_t Channel,uint8_t ChipSelect,uint8_t Frame_Bit_Size,
                                   bool Clock_Polarity,bool Clock_Phase,uint8_t CSSCK_Delay,
                                   uint8_t DT_Delay,uint8_t ASC_Delay,bool LSBFE)
{

    DSPI_ptr_t DSPI_ptr=DSPI_ptr_array[Channel];

    DSPI_ptr->CTAR[ChipSelect].B.FMSZ   = Frame_Bit_Size ;
    DSPI_ptr->CTAR[ChipSelect].B.CPOL   = Clock_Polarity ;
    DSPI_ptr->CTAR[ChipSelect].B.CPHA   = Clock_Phase    ;
    DSPI_ptr->CTAR[ChipSelect].B.CSSCK  = CSSCK_Delay    ;
    DSPI_ptr->CTAR[ChipSelect].B.DT     = DT_Delay       ;
    DSPI_ptr->CTAR[ChipSelect].B.ASC    = ASC_Delay      ;
    DSPI_ptr->CTAR[ChipSelect].B.LSBFE  = LSBFE          ;

}





/**********************************************
Solinas 30/03/2005
int16_t SPI_Write (...)
PCSn = Pheripheral chip select
0 Negate the PCSn Signal
1 Assert the PCSn Signal
E.g. PCS=0x5 Assert the PCS2 and the PCS0
**********************************************/
int16_t SPI_Write(uint8_t Channel,uint16_t *txbuffer,uint8_t word_number,uint8_t PCSn,uint8_t CTAS )
{

    uint8_t index;
    uint8_t end;
    uint8_t DMA_Tx_Ch;
    uint8_t DMA_Rx_Ch;
    vuint32_t *DSPI_RX_BUFFER;
    vuint32_t *DSPI_TX_BUFFER;
    vuint32_t contPCS;
    DSPI_ptr_t DSPI_ptr;
    int16_t dmaRet;

    DMA_Tx_Ch=SPI_DMA_Ch[Channel*2];
    DSPI_ptr=DSPI_ptr_array[Channel];
    DSPI_TX_BUFFER=DSPI_TX_BUFFER_CH[Channel];  
    
    DMA_Rx_Ch=SPI_DMA_Ch[(Channel*2)+1];
    DSPI_RX_BUFFER=DSPI_RX_BUFFER_CH[Channel];  

    EDMA.CERQR.R = DMA_Tx_Ch;
    EDMA.CERQR.R = DMA_Rx_Ch;
    
    /*  Added 23/03/2006 */
    /* int16_t DMA_Init(uint16_t channelNumber, 
                 uint32_t *dest, uint16_t destOffset, 
                 uint32_t *src,  uint16_t srcOffset,
                 uint16_t innerLoopSize, uint16_t outerLoopSize,
                 uint16_t interruptEnabled)
    */

    dmaRet = DMA_Init (DMA_Rx_Ch, 
                (uint32_t *) DSPI_RX_BUFFER,  4,
                (uint32_t *) (DSPI_POPR[Channel]), 0, 
                4, word_number,
                1);
    if (dmaRet == NO_ERROR)
    {
        dmaRet = dmaRet | (DMA_Init (DMA_Tx_Ch, 
                           (uint32_t *)(DSPI_PUSHR[Channel]), 0, 
                           (uint32_t *) DSPI_TX_BUFFER,  4,
                           4, word_number,
                0));                 
    /* 
        EDMA.TCD[DMA_Tx_Ch].B.SADDR = (vuint32_t) &DSPI_TX_BUFFER[0]; // Start Address
        EDMA.TCD[DMA_Tx_Ch].B.DADDR = DSPI_PUSHR[Channel]; // Destination address
    */
    /*  Solinas:14/03/2005
     *  Added the following step:
     *       
             8. Flush TX FIFO by writing a '1' to the CLR_TXF bit in the DSPIx_MCR,
             9. Clear Transfer Count
    */

        if (dmaRet == NO_ERROR)
        {
        /* tmp_data=DSPI_POPR[Channel]; */
            DSPI_ptr->MCR.R |= 0x00000c00;
            DSPI_ptr->TCR.R = 0x00000000;
            end=word_number-1;
            contPCS=(PCS_CONT[Channel]&PCSn)?0x80000000:0x00000000;
          
            /* Solinas: Prepare the buffer  adding  EOQ=0 PCSn=active Non continuos SCK |Data */
            for (index=0;index<=end;index++)
            {
                DSPI_TX_BUFFER[index]=contPCS|(CTAS<<28)|(PCSn<<16)|(txbuffer[index]&0x0000ffff);
            }

            /* Solinas: Clear  Transfer Counter in the first tx */
            DSPI_TX_BUFFER[0]|=0x04000000;
            /* Solinas: EOQ=1 for the last word */
            DSPI_TX_BUFFER[end]|=0x08000000;
            DSPI_TX_BUFFER[end]&=0x7FFFFFFF;
            DMA_Enable(DMA_Rx_Ch);
            DMA_Enable(DMA_Tx_Ch);
        }
    }
    return dmaRet;
}


/**********************************************
Solinas 30/03/2005
void SPI_Read (...)
PCSn = Pheripheral chip select
0 Negate the PCSn Signal
1 Assert the PCSn Signal
E.g. PCS=0x5 Assert the PCS2 and the PCS0
**********************************************/


void SPI_Read (uint8_t Channel)
{
    uint8_t DMA_Rx_Ch;
    DMA_Rx_Ch=SPI_DMA_Ch[(Channel*2)+1];
    DMA_Enable(DMA_Rx_Ch);
}


void SPI_Disable (uint8_t Channel)
{
    uint8_t DMA_Tx_Ch;
    uint8_t DMA_Rx_Ch;

    DSPI_ptr_array[Channel]->MCR.B.HALT = 1;
    
    DMA_Tx_Ch=SPI_DMA_Ch[Channel*2];
    DMA_Rx_Ch=SPI_DMA_Ch[(Channel*2)+1];
    DMA_Disable(DMA_Tx_Ch);
    DSPI_ptr_array[Channel]->RSER.R = 0x0;  
    DSPI_ptr_array[Channel]->SR.B.RFOF = 0; /* clean an eventual overflow condition */
    DMA_Disable(DMA_Rx_Ch);
    DSPI_ptr_array[Channel]->MCR.B.CLR_TXF = 1;
    DSPI_ptr_array[Channel]->MCR.B.CLR_RXF = 1;
}

/*******************************************************************/

int16_t SPI_VectorInitA(void)
{ 
    
   #if SPI_CH_A_EN
    {
        uint8_t *tmp;
        uint16_t i;

        tmp = (uint8_t *)SPI_ExTxDoneChAIrqVect;
        for(i=0;i<sizeof(SPI_ExTxDoneChAIrqVect);i++)
        {
            *tmp++=0;
        }
    }
   #endif
    return NO_ERROR;
}
/*******************************************************************/
int16_t SPI_VectorInitB(void)
{ 
   #if SPI_CH_B_EN
    {
        uint8_t *tmp;
        uint16_t i;

        tmp = (uint8_t *)SPI_ExTxDoneChBIrqVect;
        for(i=0;i<sizeof(SPI_ExTxDoneChBIrqVect);i++)
        {
            *tmp=0;
	     tmp++;
        }
    }
   #endif
    return NO_ERROR;
}
/*****************************************************************/
int16_t SPI_VectorInitC(void)
{ 
   #if SPI_CH_C_EN
    {
        uint8_t *tmp;
        uint16_t i;

        tmp = (uint8_t *)SPI_ExTxDoneChCIrqVect;
        for(i=0;i<sizeof(SPI_ExTxDoneChCIrqVect);i++)
        {
            *tmp=0;
	     tmp++;
        }
    }
   #endif
    return NO_ERROR;
}
/***************************************************************/
int16_t SPI_VectorInitD(void)
{ 
   #if SPI_CH_D_EN
    {
        uint8_t *tmp;
        uint16_t i;

        tmp = (uint8_t *)SPI_ExTxDoneChDIrqVect;
        for(i=0;i<sizeof(SPI_ExTxDoneChDIrqVect);i++)
        {
            *tmp++=0;
        }
    }
   #endif
    return NO_ERROR;
}


/************************************************************************************/

int16_t SPI_SetInterruptHandler(uint8_t Channel,uint8_t _cs, TaskType _isrFunction){

  int16_t returnCode=NO_ERROR;
  
  if (SPI_ChannelStatus[Channel] != 0) /* initialized? */ 
  {
  
    switch (Channel)  {
     #if SPI_CH_A_EN
      case SPI_CH_A: {
        SPI_ExTxDoneChAIrqVect[_cs] = _isrFunction;
      }
       
      break;
     #endif
     #if SPI_CH_B_EN 
      case SPI_CH_B:  {
        SPI_ExTxDoneChBIrqVect[_cs] = _isrFunction;
      }
       
      break;
     #endif
     #if SPI_CH_C_EN
      case SPI_CH_C:  {
        SPI_ExTxDoneChCIrqVect[_cs] = _isrFunction;
      }  
      break;
     #endif
     #if SPI_CH_D_EN 
      case SPI_CH_D:  {
        SPI_ExTxDoneChDIrqVect[_cs] = _isrFunction;
      }  
      break;        
     #endif                     
        default:    
          returnCode = (SPI_ERROR);
      break;
        }
  }
  else
  {
    returnCode = PERIPHERAL_NOT_INITIALIZED;
  }
  
  return returnCode;
}


/**********************************************/
/*            SPI Interrupt Service Routine   */ 
/**********************************************/

void SPI_B_EOQ_ISR (void) 
{
#ifdef _BUILD_SPI_
#if SPI_CH_B_EN 

  EDMA.CIRQR.R = DMA_DSPIB_SR_RFDF;

  DSPI_B.RSER.R&=(~(0x03030000));
  ActivateTask (SPI_ExTxDoneChBIrqVect[SPI_LastPCS[SPI_CH_B]]);
#endif
#endif
}

void SPI_C_EOQ_ISR (void) 
{
#ifdef _BUILD_SPI_
#if SPI_CH_C_EN 
  EDMA.CIRQR.R = DMA_DSPIC_SR_RFDF;
  
  DSPI_C.RSER.R&=(~(0x03030000));

  ActivateTask (SPI_ExTxDoneChCIrqVect[SPI_LastPCS[SPI_CH_C]]);
#endif
#endif
}


/*#############################*/
/*#          SPI RES          #*/
/*#############################*/
void SPIRes_Init (void)
{
    #if 0
    uint8_T i;
    i = 0;
    do
    {
        TXSpiCBuffer[i] = 0xA55A;
        RXSpiCBuffer[i] = 0xA55A;
        i++;
    } while (i < SPIC_TXRX_BUFFER_SIZE);
    #endif
    spiResFlagCH[0] = SPI_FREE;
    spiResFlagCH[1] = SPI_FREE;
    spiResFlagCH[2] = SPI_FREE;
    spiResFlagCH[3] = SPI_FREE;
}

void SetSPIResFlag (uint8_T channel)
{
    spiResFlagCH[channel] = SPI_BUSY;    
}

void ResetSPIResFlag (uint8_T channel)
{
    spiResFlagCH[channel] = SPI_FREE;    
}

uint8_T GetSPIResFlag (uint8_T channel)
{
    return spiResFlagCH[channel];
}

#else

void SPIRes_Init (void)
{
    /* Non fare niente. */
}

void SetSPIResFlag (uint8_T channel)
{
    /* Non fare niente. */    
}

void ResetSPIResFlag (uint8_T channel)
{
    /* Non fare niente. */     
}

uint8_T GetSPIResFlag (uint8_T channel)
{
    return PERIPHERAL_NOT_PRESENT;
}

#endif /*_BUILD_SPI_*/



