#ifndef _ADC_H_
#define _ADC_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "eQADC_messages.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* CFIFO Sampling mode */
#define VARIABLE_RATE_A     0    /* CFIFO0 */
#define ANGULAR_SAMPLING    1    /* CFIFO1 */
#define _450_MICROSEC       2    /* CFIFO2 */
#define _2_MILLISEC         3    /* CFIFO3 */
#define _10_MILLISEC        4    /* CFIFO4 */
#define SOFTWARE_TRIGGERED  5    /* CFIFO5 */
    
/* ADC error messages */
#define   ADC_CHAN_NOT_TRIGGERED    (-1)


/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
 extern bool ADC_ConfigStatus;


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/

/* The interrupt service routine(s) must be implemented by user. */

/*--------------------------------------------------------------------------*
 * CFIFO_Underflow - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void CFIFO_Underflow(void);

/*--------------------------------------------------------------------------*
 * EndOfAcq_A - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void EndOfAcq_A(void);

/*--------------------------------------------------------------------------*
 * EndOfAcq_B - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void EndOfAcq_B(void);

/*--------------------------------------------------------------------------*
 * ALLFIFOFault_Isr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ALLFIFOFault_Isr(void);

/*--------------------------------------------------------------------------*
 * EndOfAcquisition_450u_Isr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void EndOfAcquisition_450u_Isr(void);

/*--------------------------------------------------------------------------*
 * EndOfAcquisition_10m_Isr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void EndOfAcquisition_10m_Isr(void);

/*--------------------------------------------------------------------------*
 * EndOfAcquisition_2m_Isr - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void EndOfAcquisition_2m_Isr(void);

/*--------------------------------------------------------------------------*
 * EndOfAngAcq - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void EndOfAngAcq(void);

/*************END OF ADC ISR PROTOTYPES*********************/

/*
** ===================================================================
**     Method      :  ADC_Config
**
**     Description :
**         This method initializes the internal ADC registers of the 
**         eQADC module according to this Peripheral configuration 
**         file.
**         Call this method in user code to initialize the module.
**     Parameters  : None
**     Returns     : Error code
** ===================================================================
*/
int16_t ADC_Config(void);

/*
** ===================================================================
**     Method      :  ADC_Init
**
**     Description :
**         This method initializes the required eQADC module Channel 
**         according to this Peripheral configuration file.
**         Call this method in user code to initialize the channel 
**         providing the required sampling mode.and, eventually, the 
**         destination buffer for the conversion.
**     Parameters  : channelNumber
**                   convDest
**                   destSize
**                   samplingMode
**     Returns     : Error code
** ===================================================================
*/
int16_t ADC_Init(uint8_t channelNumber, 
                 uint16_t *convDest, 
                 uint16_t *angularStamps, 
                 uint16_t destSize, 
                 uint8_t samplingMode); 
/*
** ===================================================================
**     Method      :  ADC_Enable
**
**     Description :
**         This method enable the acquisitions initialized with the  
**         provided sampling mode according to the settings.
**         Call this method in user code to start samples acquisition
**         with the required rate.
**     Parameters  : Channel Number
**     Returns     : Error code
** ===================================================================
*/
int16_t ADC_Enable(uint16_t samplingMode);


/*
** ===================================================================
**     Method      :  ADC_GetSampleRes
**
**     Description :
**         This method enable the acquisitions initialized with the  
**         provided sampling mode according to the settings.
**         Call this method in user code to start samples acquisition
**         with the required rate.
**     Parameters  : 
**        input parameters:
**            channelNumber: number of analog channel to sample
**            res: resolution in bits of the result (0 to 16 bits)
**        output parameters: 
**            convDest: pointer to the output variable, containing the 
**                      analog value sampled, converted and shifted
**     Returns     : Error code
** ===================================================================
*/
int16_t ADC_GetSampleRes(uint16_t channelNumber,
                         uint16_t *convDest, 
                         uint8_t res);

/*
** ===================================================================
**     Method      :  ADC_GetSampleResSoftTrig
**
**     Description :
**         
**     Parameters  : Channel Number
**     Returns     : Error code
** ===================================================================
*/
int16_t ADC_GetSampleResSoftTrig(uint16_t channelNumber,
                                 uint16_t *convDest, 
                                 uint8_t res);

/*
** ===================================================================
**     Method      :  ADC_CalibrationSet
**
**     Description :
**         
**     Parameters  : Channel Number
**     Returns     : Error code
** ===================================================================
*/
int16_t ADC_CalibrationSet(uint16_t gainCC,
                           uint16_t offsetCC);

#endif /* _ADC_H_ */
