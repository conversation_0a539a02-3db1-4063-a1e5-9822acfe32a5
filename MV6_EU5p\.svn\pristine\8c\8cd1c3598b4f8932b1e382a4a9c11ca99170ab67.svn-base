
/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "typedefs.h"
#include "adc.h"
#include "analogin_MV_AGUSTA_M1.h"

typedef struct
{
    uint8_T anch;
    uint8_T samprate;
} ANCH_STRUCT ;

const ANCH_STRUCT ANTAB_CFG0[N_ANCH] =
{
/*  0 */{SPARKPEAK_A,       _450_MICROSEC}, /* AN_1 */
        {IDN_ANG_THROTTLE,  _450_MICROSEC}, /* AN_2 */
        {IDN_ANG_THROTTLE2, _450_MICROSEC}, /* AN_3 */
        {IA_BRAKE_LAMP,     _2_MILLISEC},   /* AN_4 */
        {IA_SHIFT_UP,       _2_MILLISEC},   /* AN_5 */
#ifndef MAP_ANGLE_BASED
        {IDN_MAP_SIGNAL,    _450_MICROSEC}, /* AN_6 */
#endif
        {IA_BRAKE_SWITCH,   _2_MILLISEC},   /* AN_7 */
        {IDN_GAS_POSITION,  _2_MILLISEC},   /* AN_8 */
        {IDN_GAS_POSITION2, _2_MILLISEC},   /* AN_9 */
        {IDN_LAM1_P,        _2_MILLISEC},   /* AN_10 */
#ifdef IDN_GAS_IDLE_SW
/* 10 */{IDN_GAS_IDLE_SW,   _450_MICROSEC}, /* AN_11 */
#else
#ifdef IDN_LAM2_P
/* 10 */{IDN_LAM2_P,        _2_MILLISEC},   /* AN_11 */
#endif
#endif
        {IA_DIAG_IGN_1,     _450_MICROSEC}, /* AN_12 VERIFICARE CODA!! */
        {IA_DIAG_IGN_2,     _450_MICROSEC}, /* AN_13 VERIFICARE CODA!! */
        {IA_DIAG_IGN_3,     _450_MICROSEC}, /* AN_14 VERIFICARE CODA!! */
        {IA_DIAG_IGN_4,     _450_MICROSEC}, /* AN_15 VERIFICARE CODA!! */
        {IDN_BATTERY,       _2_MILLISEC},   /* AN_16 */
        {IDN_V_SENS1,       _2_MILLISEC},   /* AN_17 */
        {IDN_V_SENS2,       _2_MILLISEC},   /* AN_18 */
        {IDN_T_AIR,         _2_MILLISEC},  /* AN_21 */
        {IDN_T_WATER,       _2_MILLISEC},  /* AN_22 */
/* 20 */{IDN_T_WATER_2,     _2_MILLISEC},  /* AN_23 VERIFICARE CODA!! */
        {IA_GEAR_POS,       _2_MILLISEC},  /* AN_24 VERIFICARE CODA!! */
#ifdef _SALA_PROVA_            
        {IDN_LAMBDA_OBJ,    _2_MILLISEC}, 
#else            
        {IA_EXH_FBK,        _2_MILLISEC},  /* AN_25 VERIFICARE CODA!! */
#endif            
        {IA_CMD_STARTER,    _2_MILLISEC},  /* AN_27 VERIFICARE CODA!! */
        {IA_DIAG_STARTER,   _2_MILLISEC},  /* AN_28 VERIFICARE CODA!! */
        {IA_CLUTCH_SW,      _2_MILLISEC},  /* AN_31 */
        {IA_SIDE_STAND,     _2_MILLISEC},  /* AN_32 */
        {IA_RUN_STOP,       _2_MILLISEC},  /* AN_33 */
        {IA_START_SW,       _2_MILLISEC},  /* AN_34 */
        {IA_TIP_OVER,       _2_MILLISEC},  /* AN_35 */
/* 30 */{IDN_VREF_H,        _2_MILLISEC},  /* AN_40 */
        {IDN_VREF_L,        _2_MILLISEC},  /* AN_41 */
        {IDN_VREF_HALF,     _2_MILLISEC},  /* AN_42 */
        {IDN_VREF_75,       SOFTWARE_TRIGGERED},  /* AN_43 */
        {IDN_VREF_25,       SOFTWARE_TRIGGERED},  /* AN_44 */
        {BANDGAP,           _2_MILLISEC},  /* AN_45 */
        {TEMP_SENSOR,       _2_MILLISEC},  /* AN_128 */
};

const ANCH_STRUCT ANTAB_CFG1[N_ANCH] =
{
/*  0 */{SPARKPEAK_A,       _450_MICROSEC}, /* AN_1 */
        {IDN_ANG_THROTTLE,  _450_MICROSEC}, /* AN_2 */
        {IDN_ANG_THROTTLE2, _450_MICROSEC}, /* AN_3 */
        {IA_BRAKE_LAMP,     _2_MILLISEC},   /* AN_4 */
        {IA_SHIFT_UP,       _2_MILLISEC},   /* AN_5 */
#ifndef MAP_ANGLE_BASED
        {IDN_MAP_SIGNAL,    _450_MICROSEC}, /* AN_6 */
#endif
        {IA_BRAKE_SWITCH,   _2_MILLISEC},   /* AN_7 */
        {IDN_GAS_POSITION,  _450_MICROSEC}, /* AN_8 */
        {IDN_GAS_POSITION2, _2_MILLISEC},   /* AN_9 */
        {IDN_LAM1_P,        _2_MILLISEC},   /* AN_10 */
#ifdef IDN_GAS_IDLE_SW
/* 10 */{IDN_GAS_IDLE_SW,   _2_MILLISEC},   /* AN_11 */
#else
#ifdef IDN_LAM2_P
/* 10 */{IDN_LAM2_P,        _2_MILLISEC},   /* AN_11 */
#endif
#endif
        {IA_DIAG_IGN_1,     _450_MICROSEC}, /* AN_12 VERIFICARE CODA!! */
        {IA_DIAG_IGN_2,     _450_MICROSEC}, /* AN_13 VERIFICARE CODA!! */
        {IA_DIAG_IGN_3,     _450_MICROSEC}, /* AN_14 VERIFICARE CODA!! */
        {IA_DIAG_IGN_4,     _450_MICROSEC}, /* AN_15 VERIFICARE CODA!! */
        {IDN_BATTERY,       _2_MILLISEC},   /* AN_16 */
        {IDN_V_SENS1,       _2_MILLISEC},   /* AN_17 */
        {IDN_V_SENS2,       _2_MILLISEC},   /* AN_18 */
        {IDN_T_AIR,         _2_MILLISEC},  /* AN_21 */
        {IDN_T_WATER,       _2_MILLISEC},  /* AN_22 */
/* 20 */{IDN_T_WATER_2,     _2_MILLISEC},  /* AN_23 VERIFICARE CODA!! */
        {IA_GEAR_POS,       _2_MILLISEC},  /* AN_24 VERIFICARE CODA!! */
#ifdef _SALA_PROVA_            
        {IDN_LAMBDA_OBJ,    _2_MILLISEC}, 
#else            
        {IA_EXH_FBK,        _2_MILLISEC},  /* AN_25 VERIFICARE CODA!! */
#endif            
        {IA_CMD_STARTER,    _2_MILLISEC},  /* AN_27 VERIFICARE CODA!! */
        {IA_DIAG_STARTER,   _2_MILLISEC},  /* AN_28 VERIFICARE CODA!! */
        {IA_CLUTCH_SW,      _2_MILLISEC},  /* AN_31 */
        {IA_SIDE_STAND,     _2_MILLISEC},  /* AN_32 */
        {IA_RUN_STOP,       _2_MILLISEC},  /* AN_33 */
        {IA_START_SW,       _2_MILLISEC},  /* AN_34 */
        {IA_TIP_OVER,       _2_MILLISEC},  /* AN_35 */
/* 30 */{IDN_VREF_H,        _2_MILLISEC},  /* AN_40 */
        {IDN_VREF_L,        _2_MILLISEC},  /* AN_41 */
        {IDN_VREF_HALF,     _2_MILLISEC},  /* AN_42 */
        {IDN_VREF_75,       SOFTWARE_TRIGGERED},  /* AN_43 */
        {IDN_VREF_25,       SOFTWARE_TRIGGERED},  /* AN_44 */
        {BANDGAP,           _2_MILLISEC},  /* AN_45 */
        {TEMP_SENSOR,       _2_MILLISEC},  /* AN_128 */
};

