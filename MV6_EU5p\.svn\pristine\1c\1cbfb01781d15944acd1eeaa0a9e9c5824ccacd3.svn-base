#ifndef _OS_HOOKS_H_
#define _OS_HOOKS_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "OS_api.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint32_t currentTaskStkPtr;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ErrorHook - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ErrorHook(StatusType error);

/*--------------------------------------------------------------------------*
 * PreTaskHook - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void PreTaskHook( void );

/*--------------------------------------------------------------------------*
 * PostTaskHook - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void PostTaskHook( void );

/*--------------------------------------------------------------------------*
 * ShutdownHook - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void StartupHook( void );

/*--------------------------------------------------------------------------*
 * ShutdownHook - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void ShutdownHook( StatusType error);

#endif /* _OS_HOOKS_H_ */
