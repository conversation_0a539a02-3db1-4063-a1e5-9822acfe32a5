/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#include "vsrammgm.h"
#include "vsram.h"
 
#ifdef _BUILD_VSRAMMGM_

int8_t VsramState;

/*
** ===================================================================
**     Method      :  VSRAMMGM_Update(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/

int16_t VSRAMMGM_Update(void)
{
  int16_t result = NO_ERROR;
  uint32_t newCheckSum;

  VSRAMMGM_StoreToSharedMemory();
  
  result = VSRAM_ComputeCheckSum(&newCheckSum);
  
  if (result == NO_ERROR) {
     result = VSRAM_WriteCheckSum(newCheckSum);
  }
  
  return result;
}

/*
** ===================================================================
**     Method      :  VSRAMMGM_Verify(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/

int16_t VSRAMMGM_Verify(void)
{
  int16_t result = NO_ERROR;
  uint32_t oldCheckSum;
  uint32_t newCheckSum;
  
  result = VSRAM_ComputeCheckSum(&newCheckSum);
  
  if (result == NO_ERROR) {
    result = VSRAM_ReadCheckSum(&oldCheckSum);
    
    if (oldCheckSum != newCheckSum)
    {
      result = VSRAM_CHECKSUM_ERROR;
    }
    else
    {
      VSRAMMGM_LoadFromSharedMemory();  
    }
  }
  return result;
}

/*
** ===================================================================
**     Method      :  VSRAMMGM_Init(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : void
** ===================================================================
*/
int16_t VSRAMMGM_Init(uint32_t vsramInitWord)
{
  int16_t result= NO_ERROR;
  
  result = VSRAM_InitAllMemory(vsramInitWord);
  
  return result;

}


#endif 	 /* _BUILD_VSRAMMGM_ */
