/*
 * File: sabasic_mgm.h
 *
 * Code generated for Simulink model 'SABasicMgm'.
 *
 * Model version                  : 1.2256
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Mar 24 07:00:43 2021
 */

#ifndef RTW_HEADER_sabasic_mgm_h_
#define RTW_HEADER_sabasic_mgm_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint16_T AveSAEff;

/* Average SA efficiency not limited */
extern int16_T DeltaSAIdle;

/* Output BKEFFSA */
extern int16_T DeltaSAbase;

/* Output BKEFFSA */
extern uint16_T EffSAIdleNoLim;

/* Idle SA efficiency for single cylinder not limited */
extern uint16_T EffSAbase;

/* Basic SA Efficiency */
extern uint16_T EffSAbaseNoLim;

/* Basic SA efficiency for single cylinder not limited */
extern uint16_T EffSAmax;

/* Basic SA Efficiency */
extern uint16_T EffSAmin;

/* Minimum SA Efficiency */
extern uint8_T FlgSAIdleSel;

/* Indicates when the idle spark advance map is selected */
extern uint8_T FlgUpdateSA;

/* Spark Advance updating at HTDC is enabled (=1) */
extern uint32_T IDSAbasicMgm;

/* ID Version */
extern uint16_T KFiltSABasic;

/* Output of VTKFILTSABASIC */
extern uint16_T LoadCyl;

/* Load by cylinder */
extern uint16_T LoadObjSat;

/* Load by cylinder */
extern uint16_T MaxEffSASt;

/* Max Eff SA St */
extern uint8_T SABasCylIdx;

/* cylinder index */
extern int16_T SAIdle;

/* Spark Advance in idle state */
extern int16_T SAIdleOff;

/* Idle Spark Advance offset during cutoff (optional) */
extern int16_T SAbase;

/* Basic Spark Advance */
extern int16_T SAmax;

/* Maximum Spark Advance */
extern int16_T SAmin;

/* Minimum Spark Advance */
extern int16_T SAopt;

/* Optimum Spark Advance */
extern int16_T SAstart;

/* Spark Advance during crank */
extern int16_T SAtemp;

/* Spark Advance offset for temperature */
extern uint8_T SelEffSABase;

/* Select SABase strategy */
extern uint16_T SumSAEff;

/* Sum of the efficiencies of all cylinders */
extern int16_T saidlepoff;

/* SAIdle + SAIdleOff */
#endif                                 /* RTW_HEADER_sabasic_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
