/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Ferrari/HPUE/Appl/trunk/tree/BIOS/COMMON/foc_util             $ */
/* $Revision: 2223   $                                                                                             */
/* $Date: 2011-09-05 12:30:00 +0200 (lun, 05 set 2011)   $                                                         */
/* $Author: GelmettiA_SVN           $                                                                              */
/*******************************************************************************************************************/

#ifdef _BUILD_GEARMGM_ 

#include "rtwtypes.h"
#pragma ghs section rodata=".calib" 

//Convertion value for wheel speed [counter]
CALQUAL uint16_T CONVFACTOR = 16977u;   //( 265.265625*64)
//half-amplitude of voltage band of each gear ratio [mV]
CALQUAL int16_T DELTAGEARV =   110;   //  110
//delta gear ratio [ratio]
CALQUAL uint16_T DELTPERCGR = 205u;   //( 0.10009765625*2048)
//Enable QS double blip [flag]
CALQUAL uint8_T ENDOUBLEBLIP =  0u;   // 0
//Enable recheck diagnosis in no fault case [flag]
CALQUAL uint8_T ENGPDIAGRECHECK =    1u;   //   1
//flag GR one trip [flag]
CALQUAL uint8_T FLGONETRIPGR =  1u;   // 1
//GearPosRec debounce [ms]
CALQUAL uint8_T GEARPOSRECDEB =   30u;   //  30
//GearPosRec default value [counter]
CALQUAL uint8_T GEARPOSRECDEFAULT =  2u;   // 2
//Rpm k filter value [counter]
CALQUAL uint16_T KFILTGEARRPM = 1147u;   //(0.07000732421875*16384)
//Numver of gears not valid [counter]
CALQUAL uint8_T NUMGEARPLAUSN =    4u;   //   4
//Numver of gears valid [counter]
CALQUAL uint8_T NUMGEARPLAUSV =    3u;   //   3
//CmeEst threshold [Nm]
CALQUAL int16_T THCALCGR = 768;   //(  24.00000*32)
//Min perc to confirm new Gear ratio [ratio]
CALQUAL uint16_T THPERCGRUPD = 41u;   //( 0.02001953125*2048)
//blip threshold [mV]
CALQUAL uint16_T THRDBLIPHI = 584u;   //(2851.5625000*0.2048)
//blip threshold [mV]
CALQUAL uint16_T THRDBLIPLOW = 440u;   //(2148.4375000*0.2048)
//VehSpeed above threshold to enable plausibility diagnosis [Km/h]
CALQUAL uint16_T THRVSGEARDIAG = 160u;   //( 10.0000*16)
//blip confirmed time [ms]
CALQUAL uint8_T TIMDBLIP =    8u;   //   8
//Diagnosis counter [ms]
CALQUAL uint16_T TIMGEARDIAG =    300u;   //   300
//tim observer GR [ms]
CALQUAL uint16_T TIMGROBSERVER =   100u;   //  100
//Neutral filtering [ms]
CALQUAL uint8_T TIMNFILT =    6u;   //   6
//delta for CL [mV]
CALQUAL int16_T VTDGEARDNCLV[7] = 
{
    40,    40,    40,    40,    40,    40,    40
};
//delta for CL [mV]
CALQUAL int16_T VTDGEARUPCLV[7] = 
{
    40,    40,    40,    40,    40,    40,    40
};
//gear ratio [gain]
CALQUAL int16_T VTGEARRATIO[7] = 
{
 0, 205, 410, 614, 819, 1024, 1229
};
//Typical values of each gear ratio [mV]
CALQUAL int16_T VTGEARV[7] = 
{
  4900,  4200,  3500,  2700,  2000,  1500,  1200
};
//gear ratio threshold [gain]
CALQUAL int16_T VTTHGRMAX[7] = 
{
 0, 225, 430, 635, 840, 1044, 1249
};
//gear ratio threshold [gain]
CALQUAL int16_T VTTHGRMIN[7] = 
{
 0, 205, 410, 614, 819, 1024, 1229
};

#endif /* _BUILD_GEARMGM_ */

