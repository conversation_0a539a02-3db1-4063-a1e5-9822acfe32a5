/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_CCP_



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ccp.h"
#include "..\include\ccp_can_interface.h"
#include "Flash_out.h"   /* needed by the 'PSSD_CONFIG' type */
#include "can.h"
#include "sys.h"
    
#include "mpc5500_spr_macros.h"
#include "canmgm.h"
#ifdef _BUILD_SPICAN_
#include "spican.h"
#endif
#include "tasksdefs.h"
    
#include "os_api.h"
#include "OS_resources.h"
#include "eemgm.h"
    
#include "vcalib.h"
#include "secure.h"
#include "utils.h"
#include "mathlib.h"
#include "timing.h"
#ifdef  _BUILD_SAF3MGM_
#include "digio.h"
#include "saf3_mgm.h"
#endif

/******************************************************************************/
/* Version check                                                              */
/******************************************************************************/
#if( CCP_DRIVER_VERSION != 142)
 #error "Source and Header file of CCP-Module inconsistent!"
#endif
#if( CCP_DRIVER_BUGFIX_VERSION != 0)
 #error "Source and Header file of CCP-Module inconsistent!"
#endif

#if( CCP_DRIVER_VERSION > 255)
 #error "Version decreased in CCP-Module"
#endif


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*/
/* ROM */
/*--------------------------------------------------------------------------*/

/*
   Identification
   Must be 0 terminated !!

   This string is used by CANape as the ASAP2 database filename
   The extension .A2L or .DB is added by CANape
*/

 const uint8_t ccpStationId[] = CCP_STATION_ID;

/*--------------------------------------------------------------------------*/
/* RAM */
/*--------------------------------------------------------------------------*/

/*
   The following structure containes all RAM locations needed by the CCP drive
*/

/* ##Hp - rename struct ccp */
struct CCP ccp;


/* Externals */
extern uint32_t comm_protocol;
extern uint8_T  CntTaskCCPChk;
extern uint32_T TaskTimerCCPChkms;

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* CCP internal programming status defs*/
#define CCP_IDLE     0
#define CCP_APPL     1
#define CCP_BOOT     2
#define CCP_CALIB    3
    
#define RAM_WORD_SIZE     4

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
#define ccpSetMTA(n,p) ccp.MTA[n] = p

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* data type and struct definition for crc calculation */
typedef struct 
{
    uint32_t CRCStartAddress; // start crc address
    uint16_t CRCsize;   
} taskCCPCRCParams_t;

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ccpQueueInit - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpQueueInit(void);

/*--------------------------------------------------------------------------*
 * ccpQueueWrite - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_t ccpQueueWrite(ccpMsg_t *msg);
    

/*--------------------------------------------------------------------------*
 * ccpClearDaqList - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint8_t ccpClearDaqList( uint8_t daq );


/*--------------------------------------------------------------------------*
 * ccpPrepareDaq - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint8_t ccpPrepareDaq(  uint8_t daq,
                                uint8_t last, 
                                uint8_t eventChannel, 
                                uint16_t prescaler );


/*--------------------------------------------------------------------------*
 *ccpStartDaq- Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint8_t ccpStartDaq( uint8_t daq ); 


/*--------------------------------------------------------------------------*
 * ccpStartAllPreparedDaq - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpStartAllPreparedDaq(void); 


/*--------------------------------------------------------------------------*
 * ccpQueueInit - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpStopDaq ( uint8_t daq );


#ifndef CCP_SEND_SINGLE
/*--------------------------------------------------------------------------*
 * ccpSampleAndTransmitDtm - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int ccpSampleAndTransmitDtm( uint8_t pid, 
                                     uint8_t daq, 
                                     uint8_t odt );
#else
/*--------------------------------------------------------------------------*
 * ccpSampleAndSendNextDtm - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpSampleAndSendNextDtm( void );

#endif

/*--------------------------------------------------------------------------*
 * ccpSendCrm - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpSendCrm( void );


#ifdef CCP_DAQ
#ifndef CCP_SEND_QUEUE
/*--------------------------------------------------------------------------*
 * ccpSendDtm - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpSendDtm(void); 

#endif /* CCP_SEND_QUEUE*/
#endif   /*CCP_DAQ*/


/*--------------------------------------------------------------------------*/
/* Handle MTAs (Memory-Transfer-Address) */
/*--------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------*
 * ccpSampleAndSendNextDtm - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static uint8_t ccpWriteMTA( uint8_t n,
                             uint8_t size,
                             uint8_t* d ); 

/*--------------------------------------------------------------------------*
 * ccpReadMTA - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpReadMTA( uint8_t n,
                         uint8_t size,
                         uint8_t* d );

/*--------------------------------------------------------------------------*
 * ccpCommand - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ccpCommand( uint8_t * com ); 

/*--------------------------------------------------------------------------*
 * CalculateChecksum - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void CalculateChecksum(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static uint32_t ccp_LastMTA;
static uint8_t Flgrespdelay;
static BlockDescription ccpInvalidateRegTag;
/* used for flashing operations */
static uint32_t dest;
static uint32_t size;
static uint32_t source;

static taskCCPCRCParams_t CCP_CRCParams;

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/    
/*--------------------------------------------------------------------------*
 * ccpInit - Initialization 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ccpInit( void ) 
{
    /* Initialize all CCP variables to zero */
    uint8_t * p;
    uint8_t * pl;
    p = (uint8_t *)&ccp;
    pl = p+sizeof(ccp);
    while (p<pl) 
    {
        *p = 0;
        p++;
    }

    /* initialize ccpInvalidateRegTag */
    p = (uint8_t *)&ccpInvalidateRegTag;
    pl = p+sizeof(BlockDescription);
    while (p<pl) 
    {
        *p = 0;
        p++;
    }

    ccp_LastMTA = 0;
    buffsel=CCP_START_OF_RANGE;
}

/*--------------------------------------------------------------------------*
 * CCP_ActivityMonitoring - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CCP_ActivityMonitoring(void)
{
    uint16_t res=CAN_RX_BUFFER_EMPTY;
    struct CAN_buff * iptr;
    uint8_t  Data[8];
    int8_t   i;

#if (CCP_CAN == SPICAN_CH)
    GetResource(RES_SPI_CHC);
    res=SPICAN_RxData(&iptr);
    ReleaseResource(RES_SPI_CHC);
#else
    res=CAN_RxData(CCP_CAN,CCP_RXCH,&iptr);
#endif
    
    if((res != CAN_RX_BUFFER_EMPTY) && (res != CAN_BUSOFF))
    {
        for(i=0;i<8;i++)
        {
            Data[i] = (iptr->b)[i];
        }
        ccpCommand(Data);
        if (!ccpTxCrmPossible())
        {
            ccpSendCallBack();
        }
    }
    if (res == CAN_BUSOFF)
    {
#if (CCP_CAN != SPICAN_CH)
        CAN_BusOffRecovery(CCP_CAN);
#endif
    }
} 


/*--------------------------------------------------------------------------*
 * ccpStopAllDaq -  Stop all DAQs  
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ccpStopAllDaq( void )
{
    uint8_t q;

    for (q=0;q<CCP_MAX_DAQ;q++)
    {
        ccp.DaqList[q].flags = 0;
    }
    ccp.SessionStatus &= ~SS_RUN;
}

/*--------------------------------------------------------------------------*
 * ccpDaq -  Data aquisition 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void ccpDaq( uint8_t eventChannel )
{
    uint8_t q,o;
    uint8_t dummy = 0; //for MISRA 14.5
    uint8_t control_flow = 1; //for MISRA 14.9
#ifndef CCP_SEND_SINGLE
    uint8_t j;
#endif

    //  DISABLE_INTERRUPT;

    if (!(ccp.SessionStatus&SS_RUN)) 
    {
        control_flow=0;
    }
    if(control_flow!=0)
    {
        o=0;

        for (q=0; q<CCP_MAX_DAQ;q++)
        {

            if (!(ccp.DaqList[q].flags&DAQ_FLAG_START))
            {
                /* continue; */
                dummy++;
            }

            else if (ccp.DaqList[q].eventChannel!=eventChannel)
            {
                /* continue; */
                dummy++;
            }
            else
            {
                ccp.DaqList[q].cycle--;
                if (/*--*/ccp.DaqList[q].cycle!=0)
                {
                    /* continue;  */
                    dummy++;
                }
                else
                {
                    ccp.DaqList[q].cycle = ccp.DaqList[q].prescaler;

#ifdef CCP_SEND_SINGLE

                    /* Just mark DAQ for transmission */
                    ccp.DaqList[q].flags |= DAQ_FLAG_SEND;

#else

                    /* Check that the current queue space fits a complete cycle */
#if defined(CCP_SEND_QUEUE) && defined(CCP_SEND_QUEUE_OVERRUN_INDICATION)
                    if (CCP_SEND_QUEUE_SIZE-ccp.Queue.len<=ccp.DaqList[q].last) 
                    {
                        ccp.DaqList[q].flags |= DAQ_FLAG_OVERRUN;
                        /* continue;  */ /* Skip this DAQ list on overrun */
                    }
                    else
                    {
#endif

                        /* Use BIT7 of PID to indicate overruns (CANape special feature) */
#ifdef CCP_SEND_QUEUE_OVERRUN_INDICATION
                        for (j=0;j<=ccp.DaqList[q].last;j++) 
                        {
                            if (!ccpSampleAndTransmitDtm((o+j)|(ccp.DaqList[q].flags&DAQ_FLAG_OVERRUN),q,j)) 
                            {
                                ccp.DaqList[q].flags |= DAQ_FLAG_OVERRUN;
                            } 
                            else 
                            {
                                ccp.DaqList[q].flags &= ~DAQ_FLAG_OVERRUN;
                            }
                        } /* j */

#else    /*CCP_SEND_QUEUE_OVERRUN_INDICATION*/

                        for (j=0;j<=ccp.DaqList[q].last;j++) 
                        {
                            ccpSampleAndTransmitDtm(o+j,q,j);
                        } /* j */

#endif   /*CCP_SEND_QUEUE_OVERRUN_INDICATION*/
#if defined(CCP_SEND_QUEUE) && defined(CCP_SEND_QUEUE_OVERRUN_INDICATION)
                    }
#endif
#endif   /* CCP_SEND_SINGLE*/
                }
            }

            o+=CCP_MAX_ODT;

        } /* q */

        /* Check for the next ODT to send */
#ifdef CCP_SEND_SINGLE
        ccpSampleAndSendNextDtm();
#endif
        //  ENABLE_INTERRUPT;
    }

    return;  
}


/*--------------------------------------------------------------------------*
 * ccpGetConnectionStatus -  Get connection status 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void  ccpGetConnectionStatus(uint8_t *ccpConnectionStatus) 
{
    if ((ccp.SessionStatus & SS_CONNECTED) == SS_CONNECTED)
    {
        *ccpConnectionStatus = TRUE;
    }
    else
    {
        *ccpConnectionStatus = FALSE;
    }
}


/*--------------------------------------------------------------------------*
 * ccpSendCallBack - Send notification callback; ccpSend must not fail, 
 *                          when called from this function 
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_t  ccpSendCallBack( void )
{
    uint8_t returncode=0;
#if (CCP_CAN == SPICAN_CH)
    {
        int i;
        int y=0;
        for(i=0; (i<3) && (y==0);i++)
        {
            if ((txBufFull[i] == FALSE))
            {
                y=1;
                /* break; */
            }
        }
        if(y==0)
        {
            returncode=CAN_TX_BUSY;//return CAN_TX_BUSY; mod to misra 14.9
        }
    }
#endif  
    if(returncode==0)
    {
        /* Clear all pending flags, except for CCP_CMD_PENDING */
        ccp.SendStatus &= ~CCP_SEND_PENDING;

        /* Send a CRM message */
        if (ccp.SendStatus&CCP_CRM_REQUEST)
        {
            ccp.SendStatus &= ~CCP_CRM_REQUEST;
            ccpSendCrm();

            ActivateTask(CCPBackgroundTaskID);

            //RST_PORT_BIT(2); /* Timingtest */
            returncode=1;//return 1; mod to misra 14.9
        }

        if(returncode==0)
        {  
            /* Send a DAQ message */
#ifdef CCP_DAQ
            if (ccp.SessionStatus&SS_RUN)
            {

                /* Send a  DAQ message (DTM) from the queue */
#ifdef CCP_SEND_QUEUE
                {
                    if (ccp.Queue.len) 
                    {
                        ccp.SendStatus |= CCP_DTM_PENDING;

                        if(ccpSend((uint8_t *)&ccp.Queue.msg[ccp.Queue.rp])==NO_ERROR)
                        {
                            ccp.Queue.rp++;
                            if (ccp.Queue.rp>=CCP_SEND_QUEUE_SIZE) 
                            {
                                ccp.Queue.rp = 0;
                            }
                            ccp.Queue.len--;
                            
                        }

                        returncode=1;//return 1;misra14.9
                    }
                }
                /* Send a pending DAQ message (DTM) */
#else
                if(returncode==0)
                {  
                    if (ccp.SendStatus&CCP_DTM_REQUEST) 
                    {
                        ccp.SendStatus &= ~CCP_DTM_REQUEST;
                        ccpSendDtm();
                        returncode=1;//return 1;misra 14.9
                    }
                }

#endif /*CCP_SEND_QUEUE*/

        }

#endif  /*CCP_DAQ*/
        }
    }

    return returncode;
}


/*--------------------------------------------------------------------------*
 * FuncTaskCheckSum - Function Description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncTaskCheckSum(void)
{
    CalculateChecksum();
    TerminateTask();
}

 
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
#ifdef CCP_SEND_QUEUE
/*--------------------------------------------------------------------------*
 * ccpQueueInit - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpQueueInit(void)
{
    ccp.Queue.len = 0;
    ccp.Queue.rp = 0;
}

/*--------------------------------------------------------------------------*
 * ccpQueueWrite - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_t ccpQueueWrite(ccpMsg_t *msg)
{
    uint8_t returncode=1; //misra 14.9 
    if (ccp.Queue.len>=CCP_SEND_QUEUE_SIZE)
    {
        returncode= 0;
    }
    if(returncode!=0)
    {
        ccp.Queue.msg[(ccp.Queue.rp+ccp.Queue.len)%CCP_SEND_QUEUE_SIZE] = *msg;
        ccp.Queue.len++;
    }
    return returncode;
}    
#endif

/*--------------------------------------------------------------------------*
 * ccpClearDaqList -  Clear DAQ list
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_t ccpClearDaqList( uint8_t daq )
{
    uint8_t returncode=CCP_MAX_ODT; //misra 14.9 
    uint8_t * p;
    uint8_t * pl;

    if (daq>=CCP_MAX_DAQ)
    {
        returncode= 0;
    }
    if(returncode==CCP_MAX_ODT)
    {
        /* Clear this daq list to zero */
        p = (uint8_t *)&ccp.DaqList[daq];
        pl = p+sizeof(ccpDaqList_t);
        while (p<pl) 
        {
            *p = 0;
		    p++;
        }
        /* Not DAQ list specific */
        ccp.SessionStatus |= SS_DAQ;
#ifdef CCP_SEND_SINGLE
        ccp.CurrentDaq = 0;
        ccp.CurrentOdt = 0;
 #endif
 #ifdef CCP_SEND_QUEUE
        ccpQueueInit();
 #endif
    }
    return returncode;
}

/*--------------------------------------------------------------------------*
 * ccpPrepareDaq - Prepare DAQ  
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_t ccpPrepareDaq(  uint8_t daq,
                                uint8_t last, 
                                uint8_t eventChannel, 
                                uint16_t prescaler )
{
    uint8_t returncode=1;  //misra 14.9 
    if (daq>=CCP_MAX_DAQ)
    {
        returncode= 0;
    } 
    if(returncode!=0)
    {
        ccp.DaqList[daq].eventChannel = eventChannel;
        if (prescaler==0)
        {
            prescaler = 1;
        }
        ccp.DaqList[daq].prescaler = prescaler;
        ccp.DaqList[daq].cycle = 1;
        ccp.DaqList[daq].last = last;
        ccp.DaqList[daq].flags = DAQ_FLAG_PREPARED;
    }
    return returncode;
}

/*--------------------------------------------------------------------------*
 * ccpStartDaq - Start DAQ 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_t ccpStartDaq( uint8_t daq ) 
{
    uint8_t returncode=1; //misra 14.9  

    if (daq>=CCP_MAX_DAQ)
    {
        returncode= 0;
    }   
    if(returncode!=0)
    {
        ccp.DaqList[daq].flags = DAQ_FLAG_START;
        ccp.SessionStatus |= SS_RUN;

#ifdef CCP_TIMESTAMPING
        ccpClearTimestamp();
#endif
    }
    return returncode;
}

/*--------------------------------------------------------------------------*
 * ccpStartAllPreparedDaq -  Start all prepared DAQs 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpStartAllPreparedDaq(void) 
{

    uint8_t q;

    for (q=0;q<CCP_MAX_DAQ;q++)
    {
        if (ccp.DaqList[q].flags==DAQ_FLAG_PREPARED)
        {
            ccp.DaqList[q].flags = DAQ_FLAG_START;
        }    
    }
    ccp.SessionStatus |= SS_RUN;

#ifdef CCP_TIMESTAMPING
    ccpClearTimestamp();
#endif
}

/*--------------------------------------------------------------------------*
 * ccpStopDaq -   Stop DAQ 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpStopDaq ( uint8_t daq )
{
    uint8_t i;
    uint8_t control_flow=1;//misra 14.7

    if (daq>=CCP_MAX_DAQ)
    {
        control_flow=0;
    }
    if(control_flow!=0)
    {
        ccp.DaqList[daq].flags = 0;

        /* check if all DAQ lists are stopped */
        for (i=0;(i<CCP_MAX_DAQ) && (control_flow==1);i++)
        {
            if (ccp.DaqList[i].flags&DAQ_FLAG_START)
            {
                control_flow=0;
            }
        }
    }   
    if(control_flow!=0)
    {
        ccp.SessionStatus &= ~SS_RUN;
    }

    return;
}

#ifndef CCP_SEND_SINGLE

/*--------------------------------------------------------------------------*
 * ccpSampleAndTransmitDtm - Sample and transmit a DTM  
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int ccpSampleAndTransmitDtm( uint8_t pid, 
                                    uint8_t daq, 
                                    uint8_t odt )
{
    int returncode=1;//misra 14.9 
#ifdef CCP_SEND_QUEUE
    uint8_t dtm[8];
#else
    #define dtm ccp.Dtm
#endif
#ifdef CCP_DAQ_BASE_ADDR
    uint8_t * p;
#else
    CCP_DAQBYTEPTR p;
#endif
#ifdef CCP_ODT_ENTRY_SIZE
    uint8_t s;
    uint8_t *d,*dl;
    ccpOdtEntry_t *e,*el;
    CCP_BYTE *pe_siz;
#else
    uint8_t i;
    ccpOdtEntry_t *e;
#endif

    Flgrespdelay= FALSE;

    /* PID */
    dtm[0] = pid;

    /* Assure data consistency */
    //DISABLE_INTERRUPT;

    /* Sample */
#ifdef CCP_ODT_ENTRY_SIZE

    e = &ccp.DaqList[daq].odt[odt][0];
    pe_siz = &ccp.DaqList[daq].odt_siz[odt][0];
    el = e+8;
    d = &dtm[1];
    dl = d+7;
    while (d<dl && e<el && e->ptr) 
    {
#ifdef CCP_DAQ_BASE_ADDR
        p = (uint8_t *)( e->ptr ) + CCP_DAQ_BASE_ADDR;
#else
        if(ccpSecured == 0)
        {   /* access to all memory is granted */
            p = e->ptr;
        }
        else
        {
            uint32_t tmp;
            extern uint32_t __SRAM_START_ADDR;
            extern uint32_t __SRAM_END_ADDR;
            extern uint32_t __3CALIB_ROM_START;
            extern uint32_t __3CALIB_ROM_END;

            tmp = (uint32_t)e->ptr;
            if(((tmp>=(uint32_t)&__SRAM_START_ADDR)&&(tmp<(uint32_t)&__SRAM_END_ADDR)))
            {   
                /* access to ram memory is granted */
                p = (uint8_t *)tmp;
            } 
            else if((tmp>=(uint32_t)&__3CALIB_ROM_START)&&(tmp<(uint32_t)&__3CALIB_ROM_END)&&(ccpCalibSecured == 0))
            {   
                /* access to calib memory is granted */
                p = (uint8_t *)tmp;
            }
            else
            {   
                /*  access to memory are remapped to the first ram address*/
                p = (uint8_t *)0x40000000;
            }
        }

        //////////////////////

#endif
        //      s = e->siz;
        s = *pe_siz; 
#ifdef CCP_DOUBLE_FLOAT
        if (s==8) 
        {
            *(float*)d = (float)(*(double*)p);
            s = 4;
        }
        else
#endif
        if (s==4) 
        {
            *(uint32_t *)d = *(uint32_t *)p;
        } 
        else if (s==2) 
        {
            *(uint16_t *)d = *(uint16_t *)p;
        } 
        else 
        {
            *d = *p;
        }
            d += s;
            e++;
            pe_siz++;
    }

#else

    e =  &ccp.DaqList[daq].odt[odt][0];
    for (i=1;i<8;i++) 
    {
#ifdef CCP_DAQ_BASE_ADDR
        p = (uint8_t +)( (e++)->ptr ) + CCP_DAQ_BASE_ADDR;
#else
        p = (e++)->ptr;
#endif
        if (p) 
        {
            dtm[i] = *p;
        }
    }

#endif

    /* Optional for CANape: Put a timestamp in the first ODT (Byte6+7) of each DAQ */
#ifdef CCP_TIMESTAMPING
    if (odt==0) 
    {
        *(uint16_t *)&dtm[6] = ccpGetTimestamp();
    }
#endif

    /* Queue or transmit the DTM */
#ifdef CCP_SEND_QUEUE

    if (ccp.SendStatus&CCP_SEND_PENDING) 
    {
        if (!ccpQueueWrite((ccpMsg_t*)dtm)) 
        {
            /* Overun */
            // ENABLE_INTERRUPT;
            returncode = 0;
        }
    }
    else
    {
        ccp.SendStatus |= CCP_DTM_PENDING;
        ccpSend(dtm);
    }

#else   /* CCP_SEND_QUEUE*/
    if(returncode!=0)
    {
        if (ccp.SendStatus&CCP_DTM_REQUEST) 
        {
            /* Overun */
            //  ENABLE_INTERRUPT;
            returncode = 0;
        }
    }
    if(returncode!=0)
    {
        if (ccp.SendStatus&CCP_SEND_PENDING) 
        {
            ccp.SendStatus |= CCP_DTM_REQUEST;
        } 
        else 
        {
            ccp.SendStatus |= CCP_DTM_PENDING;
            ccpSend(dtm);
        }
    }
#endif

    //ENABLE_INTERRUPT;
    return returncode;

}
#else
/*--------------------------------------------------------------------------*
 * ccpSampleAndSendNextDtm - Sample and transmit the next DTM in SEND_SINGLE mode  
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpSampleAndSendNextDtm( void )
{

    uint8_t i,j;
    uint8_t * p;
    ccpOdtEntry_t *e;

    /* Request for DTM transmission pending */
    if (ccp.SendStatus&CCP_DTM_REQUEST) return;

    /* Find a DAQ list marked for transmission */
    for (i=0;i<CCP_MAX_DAQ;i++) 
    {

        if (ccp.DaqList[ccp.CurrentDaq].flags&DAQ_FLAG_SEND) 
        {

            /* PID */
            ccp.Dtm[0] = ccp.CurrentDaq*CCP_MAX_ODT+ccp.CurrentOdt;

            /* Sample */
            e =  &ccp.DaqList[ccp.CurrentDaq].odt[ccp.CurrentOdt][0];
            for (j=1;j<8;j++) 
            {
                p = (e++)->ptr;
                if (p) ccp.Dtm[j] = *p;
            }

            /* Send */
            ccpSendDtm();

            /* Increment ODT */
            if (++ccp.CurrentOdt>ccp.DaqList[ccp.CurrentDaq].last) 
            {
                /* DAQ list done */
                ccp.CurrentOdt = 0;
                ccp.DaqList[ccp.CurrentDaq].flags &= ~DAQ_FLAG_SEND;

                /* Increment DAQ */
                if (++ccp.CurrentDaq>=CCP_MAX_DAQ)
                {
                    ccp.CurrentDaq = 0;
                }
            }

            break;

        } 
        else 
        {

            /* Increment DAQ */
            if (++ccp.CurrentDaq>=CCP_MAX_DAQ) 
            {  
                ccp.CurrentDaq = 0;
            }

        }
    }
}
#endif

/*--------------------------------------------------------------------------*
 * ccpSendCrm - Send a CRM, if no other message is pending  
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpSendCrm( void )
{

    //DISABLE_INTERRUPT;

    if (ccp.SendStatus&CCP_SEND_PENDING)
    {
        ccp.SendStatus |= CCP_CRM_REQUEST;
    }
    else
    {
        if(ccpSend(ccp.Crm)== NO_ERROR)
        {
            ccp.SendStatus |= CCP_CRM_PENDING;
        }
        else
        {
            ccp.SendStatus |= CCP_CRM_REQUEST;
        }
    }
    //ENABLE_INTERRUPT;
}

#ifdef CCP_DAQ
#ifndef CCP_SEND_QUEUE
/*--------------------------------------------------------------------------*
 * ccpSendDtm - Send a DTM, if no other message is pending  
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpSendDtm(void) 
{

    //DISABLE_INTERRUPT;

    if (ccp.SendStatus&CCP_SEND_PENDING) 
    {
        ccp.SendStatus |= CCP_DTM_REQUEST;
    }
    else
    {
        if(ccpSend(ccp.Dtm) == NO_ERROR)
        {
            ccp.SendStatus |= CCP_DTM_PENDING;
        }
        else
        {
            ccp.SendStatus |= CCP_DTM_REQUEST;
        }
    }

    //ENABLE_INTERRUPT;
}
#endif /* CCP_SEND_QUEUE*/
#endif   /*CCP_DAQ*/


/*--------------------------------------------------------------------------*/
/* Handle MTAs (Memory-Transfer-Address) */
/*--------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------*
 * ccpWriteMTA - Write n bytes 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_t ccpWriteMTA( uint8_t n,
                            uint8_t size,
                            uint8_t* d ) 
{

    uint8_t temp;
    /* EEPROM write access */
#ifdef CCP_WRITE_EEPROM
    CCP_BYTE r = ccpCheckWriteEEPROM(ccp.MTA[n],size,d);
    if (r) 
    { 
        /* EEPROM write access */
        ccp.MTA[n] += size;
        return r;
    }
#endif

    /* Checked ram memory write access */
#ifdef CCP_WRITE_PROTECTION
    if (!ccpCheckWriteAccess(ccp.MTA[n],size))
    {
        ccp.MTA[n] += size;
        return CCP_WRITE_DENIED;
    }
#endif

    while (size>0)
    {
        temp = *d;
        size--;

#ifdef _BUILD_VCALIB_
        /*
        * check each memory virtual address before accessing
        * if a vcalib memory is busy due to internal synchronizations
        * ccpWriteMTA returns CCP_WRITE_PENDING
        */
        if (VCALIB_Switch_no_blocking((uint32_t) ccp.MTA[n]) != NO_ERROR)
        {
            return CCP_WRITE_PENDING;
        }
#endif

        *ccp.MTA[n] = temp;
        ccp.MTA[n]++;
        d++;
    }

    return CCP_WRITE_OK;
}

/*--------------------------------------------------------------------------*
 * ccpReadMTA - Read n bytes 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpReadMTA( uint8_t n, uint8_t size, uint8_t* d )
{

    /* EEPROM read access */
    uint8_t *p;
    uint8_t i;
#if (CCP_CAN == SPICAN_CH)
    uint32_t tmp;

    tmp = (uint32_t)ccp.MTA[n];
    if((tmp&0xFFFF0000)!=0x40000000)
    {
        tmp>>=8;
        tmp=0x40000000|(tmp&0x0000ffff);
    }
    p = (uint8_t *)tmp;
#else

    if(ccpSecured == 0)
    {
        p = ccp.MTA[n];
    }
    else
    {
        uint32_t tmp;
        extern uint32_t __SRAM_START_ADDR;
        extern uint32_t __SRAM_END_ADDR;
        extern uint32_t __3CALIB_ROM_START;
        extern uint32_t __3CALIB_ROM_END;

        tmp = (uint32_t)ccp.MTA[n];
        if(((tmp>=(uint32_t)&__SRAM_START_ADDR)&&(tmp<(uint32_t)&__SRAM_END_ADDR)))
        {  
            /* access to ram memory is granted */
            p = (uint8_t *)tmp;
        } 
        else if((tmp>=(uint32_t)&__3CALIB_ROM_START)&&(tmp<(uint32_t)&__3CALIB_ROM_END)&&(ccpCalibSecured == 0))
        {   
            /* access to calib memory is granted */
            p = (uint8_t *)tmp;
        }
        else
        {   
            /* access to memory are remapped to the first ram address*/
            p = (uint8_t *)0x40000000;
        }
    }

#endif

    for(i=0; i< size; i++)
    {
        ccp.Crm[i+3]= *p;
        p++;
    }

    //  #ifdef CCP_READ_EEPROM
    //    if (ccpCheckReadEEPROM(ccp.MTA[n],size,d)) {
    ccp.MTA[n] += size;
#ifdef __MWERKS__
    d;
#endif /* __MWERKS__ */

    return;
}

/*--------------------------------------------------------------------------*
 * ccpCommand - Command Processor 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void ccpCommand( uint8_t * com ) 
{
    uint8_t i;
    uint8_t cmd = com[0];
    uint8_t ctr = com[1];
    //uint16_t * stationAddr = (*(uint16_t*)&com[2]); /* Has to be Intel-Format ! */
    uint8_t disconnectCmd = com[2];
    uint8_t control_flow=1;
    //uint16_t * disconnectStationAddr = (*(uint16_t*)&com[4]);
    //uint8_t mta;
    CCP_BYTE r;
    CCP_DWORD s;

    Flgrespdelay= FALSE;

    /* Handle CONNECT or TEST command */
    if (cmd==CC_CONNECT||cmd==CC_TEST) 
    {

        /* This station */

        if ((*(uint16_t*)&com[2])== CCP_STATION_ADDR|| (*(uint16_t*)&com[2]) == CCP_BROADCAST_STATION_ADDR) 
        {
            /* This station */
            if (cmd==CC_CONNECT) 
            {
#ifdef CCP_DAQ
                if (!(ccp.SessionStatus&SS_TMP_DISCONNECTED)) 
                {
                    ccpStopAllDaq();
                    ccp.SendStatus = 0; /* Clear all transmission flags */
                }
#endif
                ccp.SessionStatus |= SS_CONNECTED;
                ccp.SessionStatus &= ~SS_TMP_DISCONNECTED;
            }


            /* Responce */
            /* Station addresses in Intel Format */
            ccp.Crm[0] = 0xFF;
            ccp.Crm[1] = CRC_OK;
            ccp.Crm[2] = ctr;
            ccp.Crm[3] = 0x00;
            *(uint16_t *)&ccp.Crm[4] = CCP_STATION_ADDR;
            *(uint16_t*)&ccp.Crm[6] = CCP_BROADCAST_STATION_ADDR;

            /* responce */
            /* |||| */
            }

        /* Another station */
        else 
        {

            /* If connected, temporary disconnect */
            if (ccp.SessionStatus&SS_CONNECTED) 
            {
                ccp.SessionStatus &= ~SS_CONNECTED;
                ccp.SessionStatus |= SS_TMP_DISCONNECTED;
            }
            /* no responce */
            control_flow=0;//return; mod to 14.7 Rules Misra 2004
        }
    }     


    /* Handle other commands only if connected */
    else if (ccp.SessionStatus&SS_CONNECTED) 
    {
        /* prepare the responce */
        ccp.Crm[0] = 0xFF;
        ccp.Crm[1] = CRC_OK;
        ccp.Crm[2] = ctr;
        for (i=3;i<8;i++)
        {
            ccp.Crm[i] = 0x00;
        }
        switch (cmd)
        {

            case CC_DISCONNECT:
                ccp.SessionStatus &= ~SS_CONNECTED;
                if (disconnectCmd==0x00)
                { /* Temporary */
                    ccp.SessionStatus |= SS_TMP_DISCONNECTED;
                }
                else
                {           /* End of session */

#ifdef CCP_DAQ
                    ccpStopAllDaq();
#endif
#ifdef CCP_SEED_KEY
                    ccp.ProtectionStatus = 0; /* Clear Protection Status */
#endif
                }
                break;

            case CC_EXCHANGE_ID: /* Exchange Station Identifications */                
                /*uint8_t ccpStationId[8];*/
                //uint8_t masterId = com[2];
                for(i=0; ccpStationId[i]!=0;i++)
                {
                }
                ccp.Crm[3] = i; /* Lenght of slave device identifier */
                ccp.Crm[4] = 0;
                /* Build the Resource Availability and Protection Mask */
                ccp.Crm[5] = PL_CAL; /* Default: Calibration available */
                ccp.Crm[6] = 0;      /* Default: No Protection */
#ifdef CCP_SEED_KEY
                ccp.Crm[6] |= PL_CAL;   /* Protected Calibration */
#endif
#ifdef CCP_DAQ
                ccp.Crm[5] |= PL_DAQ;     /* Data Acquisition */
#ifdef CCP_SEED_KEY
                ccp.Crm[6] |= PL_DAQ;   /* Protected Data Acquisition */
#endif
#endif
#if defined(CCP_PROGRAM) || defined(CCP_BOOTLOADER_DOWNLOAD)
                ccp.Crm[5] |= PL_PGM;     /* Flash Programming */
#ifdef CCP_SEED_KEY
                ccp.Crm[6] |= PL_PGM;   /* Protected Flash Programming */
#endif
#endif
                ccp.Crm[7] = CCP_DRIVER_VERSION; /* Driver version number */
                ccpSetMTA(0,(uint8_t *)ccpStationId);

                break;

#ifdef CCP_SEED_KEY

            case CC_GET_SEED: /* Get Seed for Key */
                //uint8_t privilegeLevel = com[2];
                ccp.Crm[3] = 0; /* Protection Status: No key required */
                *(CCP_DWORD*)&ccp.Crm[4] = 0;
#ifdef CCP_SEED_KEY
                /* Keys required for CAL or PGM */
                switch (privilegeLevel) 
                {
                    case PL_CAL:
                        ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_CAL)); /* Protection Status */
                        *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_CAL);
                        break;
                    case PL_PGM:
                        ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_PGM)); /* Protection Status */
                        *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_PGM);
                        break;
                    case PL_DAQ:
                        ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_DAQ)); /* Protection Status */
                        *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_DAQ);
                        break;
                    default:
                    ccp.Crm[1] = CRC_CMD_SYNTAX;
                /* Error */
                }
#endif /* CCP_SEED_KEY riga 623*/

                break;

            case CC_UNLOCK: /* Unlock Protection */

                //uint8_t key = com[2]; /* Key may be up to 6 Bytes */
                /* Check key */
                ccp.ProtectionStatus |= ccpUnlock(&com[2]); /* Reset the appropriate resource protection mask bit */
                ccp.Crm[3] = ccp.ProtectionStatus; /* Current Protection Status */

            break;

#endif /* CCP_SEED_KEY riga 616*/

            case CC_SET_MTA: /* Set transfer address */
                //mta = com[2];
                //addrExt = com[3];
                //addr = (*(CCP_DWORD*)&com[4]);
                if (com[2] < CCP_MAX_MTA-1)
                {
                    ccpSetMTA(com[2],(uint8_t *)ccpGetPointer(com[3],(*(CCP_DWORD*)&com[4])));
                }
                else
                {
                    ccp.Crm[1] = CRC_OUT_OF_RANGE;
                }
                break;

            case CC_DNLOAD: /* Download */

                //size = com[2];
#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_CAL))
                {
                    ccp.Crm[1] = CRC_ACCESS_DENIED;
                    r = 0;
                }
                else  /*CCP_SEED_KEY*/
#endif
                r = ccpWriteMTA(0,com[2],&com[3]);
                // tempAddr= ccp.MTA;
                *(uint32_t *)&ccp.Crm[4] = (uint32_t )ccp.MTA[0];
                if (r==CCP_WRITE_PENDING)
                {
                    control_flow=0;
                }//  return; /* EEPROM write pending */mod to misra 14.7
                else
                {
                    if (r==CCP_WRITE_DENIED||r==CCP_WRITE_ERROR) 
                    {
                        ccp.Crm[1] = CRC_ACCESS_DENIED; /* No write access */
                    }

                    }
                break;


            case CC_DNLOAD6: /* Download */
#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_CAL))
                {
                    ccp.Crm[1] = CRC_ACCESS_DENIED;
                    r = 0;
                }
                else
#endif
                r = ccpWriteMTA(0,6,&com[2]);
                *(uint32_t *)&ccp.Crm[4] = (uint32_t )ccp.MTA[0];         /* added by Soro & Ibba : TBC*/
#ifdef CCP_STANDARD
                ccpGetMTA0((CCP_BYTE*)&ccp.Crm[3],(CCP_DWORD*)&ccp.Crm[4]);
#endif
                if (r==CCP_WRITE_PENDING)
                {
                    control_flow=0;
                }//  return; /* EEPROM write pending */mod to misra 14.7
                else
                {
                    if (r==CCP_WRITE_DENIED||r==CCP_WRITE_ERROR) 
                    {
                        ccp.Crm[1] = CRC_ACCESS_DENIED;/* No write access */
                    } 
                }
                break;
            case CC_UPLOAD: /* Upload */
                {
                uint8_t size= com[2];
                ccpReadMTA(0,size,&ccp.Crm[3]);
                }
                break;

            case CC_SHORT_UPLOAD: /* Upload with Address */

                //size = com[2];
                //addrExt = com[3];
                //addr = (*(CCP_DWORD*)&com[4]);
#if (CCP_CAN == SPICAN_CH)
                GetResource(RES_SPI_CHC);
#endif
                ccpSetMTA(CCP_INTERNAL_MTA,(uint8_t *) ccpGetPointer(com[3],(*(CCP_DWORD*)&com[4])));
                ccpReadMTA(CCP_INTERNAL_MTA,com[2],&ccp.Crm[3]);
#if (CCP_CAN == SPICAN_CH)
                ReleaseResource(RES_SPI_CHC);
#endif
                break;

            case CC_GET_DAQ_SIZE: /* Return the size of a DAQ list and clear */

                //uint8_t daqList = com[2];
                //uint32_t * daqId = (*(CCP_DWORD*)&com[4]);
#ifdef CCP_DAQ
                ccpStopDaq((*(CCP_DWORD*)&com[4])); /* Stop this daq list */
                ccp.Crm[3] = ccpClearDaqList(com[2]); /* Number of  ODTs */
                ccp.Crm[4] = com[2]*CCP_MAX_ODT; /* PID of the first ODT */
#else
                ccp.Crm[3] = 0;
                ccp.Crm[4] = 0;
#endif

                break;

#ifdef CCP_DAQ

            case CC_SET_DAQ_PTR: /* Set DAQ pointer */

                //uint8_t comDaq = com[2];
                //uint8_t comOdt = com[3];
                //uint8_t comIdx = com[4];

                if ((com[2]>=CCP_MAX_DAQ)||(com[3]>=CCP_MAX_ODT)||(com[4]>7))
                {
                ccp.Crm[1] = CRC_CMD_SYNTAX;
                ccp.DaqListPtr = 0;
                }
                else
                {
                ccp.DaqListPtr = &ccp.DaqList[com[2]].odt[com[3]][com[4]];
                ccp.DaqListSizPtr = &ccp.DaqList[com[2]].odt_siz[com[3]][com[4]];
                }

                break;

            case CC_WRITE_DAQ: /* Write DAQ entry */

                //uint8_t writeDaqSize = com[2];
                //uint8_t writeDaqAddrExt = com[3];
                //uint32_t * writeDaqAddr = (*(CCP_DWORD*)&com[4]);
                if (
#ifdef CCP_ODT_ENTRY_SIZE
#ifdef CCP_DOUBLE_FLOAT
                (com[2]!=8) &&
#endif
                ( com[2]!=1 && com[2]!=2 && com[2]!=4)
#else
                com[2]!=1
#endif
                || ccp.DaqListPtr==0) 
                {
                ccp.Crm[1] = CRC_CMD_SYNTAX;
                } 
                else
                {
#ifdef CCP_DAQ_BASE_ADDR
                ccp.DaqListPtr->ptr = ccpGetDaqPointer(com[3],(*(CCP_DWORD*)&com[4]));
#else
                ccp.DaqListPtr->ptr = (CCP_DAQBYTEPTR)ccpGetPointer(com[3],(*(CCP_DWORD*)&com[4]));
#endif
#ifdef CCP_ODT_ENTRY_SIZE
                //ccp.DaqListPtr->siz = com[2];
                *ccp.DaqListSizPtr = com[2];
#endif
                }

                break;

            case CC_START_STOP: /* Cyclic aquisition start/stop */

                /*        uint8_t ssCmd = com[2];  @/@*$ Start or Stop $*@/@
                -         uint8_t ssDaq = com[3];  @/@*$ DAQ list $*@/@
                -         uint8_t ssLast = com[4];  @/@*$ Last ODT to send $*@/@
                -         uint8_t ssEventChannel = com[5];  @/@*$ Event Channel Number $*@/@
                -         uint16_t ssPrescaler = (*(CCP_WORD*)&com[6]); @/@*$ Prescaler $*@/@*/

#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_DAQ))
                {
                ccp.Crm[1] = CRC_ACCESS_DENIED;                /****?????????****/
                }
                else
#endif
                if (!(ccp.SessionStatus&SS_DAQ))
                { /* Not initialized */
                ccp.Crm[1] = CRC_DAQ_INIT_REQUEST;
                }
                else
                {
                    switch (com[2])
                    {
                        case 0: /* stop */
                            ccpStopDaq(com[3]);
                            break;
                        case 1: /* start */
                            ccpPrepareDaq(com[3],com[4],com[5],(*(CCP_WORD*)&com[6]));
                            ccpStartDaq(com[3]);
                            break;
                        case 2: /* prepare */
                            ccpPrepareDaq(com[3],com[4],com[5],(*(CCP_WORD*)&com[6]));
                            break;
                        default:
                            ccp.Crm[1] = CRC_CMD_SYNTAX;
                            break;
                    }
                }
                break;
                
            case CC_START_STOP_ALL: /* Cyclic aquisition start/stop */
                //uint8_t ssCmd = com[2];  /* Start or Stop */

#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_DAQ))
                ccp.Crm[1] = CRC_ACCESS_DENIED;
                else
#endif
                if (!(ccp.SessionStatus&SS_DAQ))
                { /* Not initialized */
                ccp.Crm[1] = CRC_DAQ_INIT_REQUEST;
                }
                else
                {
                    switch (com[2])
                    {
                        case 0: /* Stop */
                            ccpStopAllDaq();
                            break;
                        case 1: /* Start */
                            ccpStartAllPreparedDaq();
                            break;
                        default:
                            ccp.Crm[1] = CRC_CMD_SYNTAX;
                            break;
                    }
                }
                break;


#endif /*CCP_DAQ*/


#ifdef CCP_CHECKSUM

            case CC_BUILD_CHKSUM: /* Build Checksum */
            {
                /* Initialize Responce */
                ccp.Crm[3] = sizeof(CCP_CHECKSUM_TYPE); /* Checksum Size */
#ifdef CCP_CHECKSUM_CCITT               /* Checksum */
                *(CCP_DWORD*)&ccp.Crm[4] = 0xFFFFFFFF;
#else
                *(CCP_DWORD*)&ccp.Crm[4] = 0;
#endif
                ccp.MTA[CCP_INTERNAL_MTA] = ccp.MTA[0];        /* MTA[0] is not affected */
#ifdef CCP_MOTOROLA
                s = (*(CCP_WORD*)&com[4]) | ((*(CCP_WORD*)&com[2])<<16);
#else
                s = (*(CCP_WORD*)&com[2]) | ((*(CCP_WORD*)&com[4])<<16);
#endif
                if (s&0xffff0000) 
                {
                    ccp.Crm[1] = CRC_OUT_OF_RANGE; /* Range, max. 64K-1 on <32Bit CPUs */
                }
                ccp.CheckSumSize = (CCP_CHECKSUM_TYPE)s;

                if (((uint32_t )ccp.MTA[0] >= (uint32_t )(&__FLASH_START)) &&
                (((uint32_t )ccp.MTA[0]+s) <= (uint32_t )(&__FLASH_END)))
                {
                    //sum16 = update_crc16(sum16, (uint8_t*)ccp.MTA[0], s, UTILS_CRC_FORWARD);
                    CCP_CRCParams.CRCStartAddress=(uint32_t )ccp.MTA[0];
                    CCP_CRCParams.CRCsize=ccp.CheckSumSize;
                    Flgrespdelay= TRUE;
                    ActivateTask(TaskCheckSumID);
                }

                if ((((uint32_t )ccp.MTA[0] >= (uint32_t)(&__SRAM_START_ADDR)) && ((uint32_t )ccp.MTA[0]+s <= (uint32_t)(&__SRAM_END_ADDR)))
#if defined( _BUILD_DEVELOPMENT_ ) || defined( _BUILD_VCALIB_ )
                || (((uint32_t )ccp.MTA[0] >= (uint32_t)(&__CALIB_RAM_START)) && ((uint32_t )ccp.MTA[0]+s <= (uint32_t)(&__CALIB_RAM_END)))
#endif
                )
                {
                    //sum16 = update_crc16(sum16, (uint8_t*)ccp.MTA[0], s, UTILS_CRC_FORWARD);

                    CCP_CRCParams.CRCStartAddress=(uint32_t )ccp.MTA[0];
                    CCP_CRCParams.CRCsize=ccp.CheckSumSize;
                    Flgrespdelay= TRUE;
                    ActivateTask(TaskCheckSumID);
                }


                /* ccp.Crm[4]= (sum16 >> 8 ) & 0x00ff;
                ccp.Crm[5]= (sum16      ) & 0x00ff;
                ccp.Crm[6]= 0xff;
                ccp.Crm[7]= 0xff;*/
                }
                break;

#endif /* CCP_CHECKSUM */

            /* Flash Programming */
#ifdef CCP_PROGRAM 

            case CC_CLEAR_MEMORY: /* Clear Memory */
            {
#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_PGM))
                ccp.Crm[1] = CRC_ACCESS_DENIED;
                else
#endif

#ifdef CCP_BOOTLOADER
                /* Transfer control to the CCP bootloader */
                ccpBootLoaderStartup(&ccp,com); /* Never returns */
#else
                /* Clear flash sector */
                /* Clear flash sector */
#ifdef CCP_MOTOROLA
                s = (*(uint16_t*)&com[4]) | ((*(CCP_WORD*)&com[2])<<16);
#else
                s = (*(uint16_t*)&com[2]) | ((*(CCP_WORD*)&com[4])<<16);
#endif /* CCP_MOTOROLA */

                ccp_LastMTA=(uint32_t )ccp.MTA[0];

#endif /* CCP_BOOTLOADER */



                /* CCP region Programming state machine */
                {
                    if(ccp_LastMTA == (uint32_t)(&__BOOT_START))
                    {
#ifdef  _BUILD_SAF3MGM_
                        Saf3Mgm_ResetSMP(); /* disable SMP to avoid an unwanted Safety 3 reset */
#endif
                        DisableAllInterrupts();
                        SYS_DisableWatchdog();

                        ccp_LastMTA = (uint32_t)(&__BACKUP_START);

                        //                if (ccpFlashErase(ccp_LastMTA, s))
                        if (FLASH_Erase(ccp_LastMTA, s,(void(*)(void))NULL_CALLBACK))
                        {
                            ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
                        }

                        dest = (uint32_t)(&__CALIB_ROM_START)+(uint32_t)(&__CALIB_ROM_SIZE)-sizeof(BlockDescription);
                        ccpInvalidateRegTag = *((BlockDescription*) dest);
                        ccpInvalidateRegTag.validMemoryRegion = 0;
                        source = (uint32_t)&ccpInvalidateRegTag;
                        size = sizeof(BlockDescription);

                        FLASH_Program(dest, size, source);
                        FLASH_ProgramVerify(dest, size, source);

                        dest = (uint32_t)(&__APP_START)+(uint32_t)(&__APP_SIZE)-sizeof(BlockDescription);
                        ccpInvalidateRegTag = *((BlockDescription*) dest);
                        ccpInvalidateRegTag.validMemoryRegion = 0;
                        source = (uint32_t)&ccpInvalidateRegTag;
                        size = sizeof(BlockDescription);

                        FLASH_Program(dest, size, source);
                        FLASH_ProgramVerify(dest, size, source);

                        ccpSendCrm();

#if (CCP_CAN == SPICAN_CH)
                        if (!ccpTxCrmPossible())
                        {
                            ccpSendCallBack();
                        }
                        Delay_ms(10);
                        SPICAN_RTSMessage(spican_bufSel);
#else
                        while (Can_GetTxStatus(CCP_CAN)==CAN_TX_BUSY) 
                        {
                        }
#endif
                        comm_protocol = 3;
                        VSRAMMGM_Update();
#if (CCP_CAN == SPICAN_CH)
                        Delay_ms(2);
#else
                        Delay_ms(1);
#endif
                        ShutdownOS(E_NO_ERROR);
                    }

                    else if(ccp_LastMTA == (uint32_t)(&__CALIB_ROM_START))
                    {
                        dest = (uint32_t)(&__CALIB_ROM_START)+(uint32_t)(&__CALIB_ROM_SIZE)-sizeof(BlockDescription);
                        ccpInvalidateRegTag = *((BlockDescription*) dest);
                        ccpInvalidateRegTag.validMemoryRegion = 0;
                        source = (uint32_t)&ccpInvalidateRegTag;
                        size = sizeof(BlockDescription);

#ifdef  _BUILD_SAF3MGM_
                        Saf3Mgm_ResetSMP(); /* disable SMP to avoid an unwanted Safety 3 reset */
#endif
                        DisableAllInterrupts();
                        SYS_DisableWatchdog();

                        //!! ap: just invalidate application region. erase and program performed in boot mode
                        FLASH_Program(dest, size, source);
                        FLASH_ProgramVerify(dest, size, source);

                        // EnableAllInterrupts();

                        ccpSendCrm();

#if (CCP_CAN == SPICAN_CH)
                        if (!ccpTxCrmPossible())
                        {
                        ccpSendCallBack();
                        }
                        Delay_ms(10);
                        SPICAN_RTSMessage(spican_bufSel);
#else
                        while (Can_GetTxStatus(CCP_CAN)==CAN_TX_BUSY) 
                        {
                        }
#endif

                        comm_protocol = 3;
                        VSRAMMGM_Update();

#if (CCP_CAN == SPICAN_CH)
                        Delay_ms(2);
#else
                        Delay_ms(1);
#endif
                        ShutdownOS(E_NO_ERROR);
                    }

                    else if(ccp_LastMTA == (uint32_t)(&__APP_START))
                    {
                        dest = (uint32_t)(&__APP_START)+(uint32_t)(&__APP_SIZE)-sizeof(BlockDescription);
                        ccpInvalidateRegTag = *((BlockDescription*) dest);
                        ccpInvalidateRegTag.validMemoryRegion = 0;
                        source = (uint32_t)&ccpInvalidateRegTag;
                        size = sizeof(BlockDescription);

#ifdef  _BUILD_SAF3MGM_
                        Saf3Mgm_ResetSMP(); /* disable SMP to avoid an unwanted Safety 3 reset */
#endif
                        DisableAllInterrupts();
                        SYS_DisableWatchdog();

                        //!! ap: just invalidate application region. erase and program performed in boot mode
                        FLASH_Program(dest, size, source);
                        FLASH_ProgramVerify(dest, size, source);

                        ccpSendCrm();

#if (CCP_CAN == SPICAN_CH)
                        if (!ccpTxCrmPossible())
                        {
                            ccpSendCallBack();
                        }
                        Delay_ms(10);
                        SPICAN_RTSMessage(spican_bufSel);
#else
                        while (Can_GetTxStatus(CCP_CAN)==CAN_TX_BUSY) 
                        {
                        }
#endif

                        comm_protocol = 3;
                        VSRAMMGM_Update();
#if (CCP_CAN == SPICAN_CH)
                        Delay_ms(2);
#else
                        Delay_ms(1);
#endif
                        ShutdownOS(E_NO_ERROR);
                    } 
                    else
                    {
                        ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
                    }
                }
            }
            break;

            /* Flash Programming */
#ifndef CCP_BOOTLOADER

            // case CC_PROGRAM: /* Program */
            // case CC_PROGRAM6: /* Program */

#endif /* !CCP_BOOTLOADER */
#endif /* CCP_PROGRAM */

#ifdef CCP_CALPAGE

            case CC_SET_CAL_PAGE: /* Select Calibration Page */

                ccpSetCalPage((CCP_DWORD)ccp.MTA[0]);

                break;

            case CC_GET_CAL_PAGE: /* Get Active Calibration Page */

                ccp.Crm[3] = 0; /* Address Extension */
                *(CCP_DWORD*)&ccp.Crm[4] = ccpGetCalPage(); /* Address */

                break;

#endif /* CCP_CALPAGE */

#ifdef CCP_SET_SESSION_STATUS

            case CC_SET_S_STATUS: /* Set Session Status */
                /* Set Resume and Store mode in SessionStatus */
                ccp.SessionStatus &= ~(SS_STORE|SS_RESUME);
                ccp.SessionStatus |= (com[2]&(SS_STORE|SS_RESUME));

                /* Save as UserSessionStatus */
                ccp.UserSessionStatus = com[2];
                break;

            case CC_GET_S_STATUS: /* Get Session Status */

                ccp.Crm[3] = ccp.UserSessionStatus;
                ccp.Crm[4] = 0; /* No additional status */

                break;

#endif /* CCP_SET_SESSION_STATUS */

            case CC_GET_CCP_VERSION: /* Get Version */

                ccp.Crm[3] = CCP_VERSION_MAJOR;
                ccp.Crm[4] = CCP_VERSION_MINOR;

                break;

            default: /* unknown */

                ccp.Crm[1] = CRC_CMD_UNKNOWN;
                break;

            } /* switch */
        /* Responce */
        /* |||| */
        }

    /* Not connected */
    else
    {
        /* No responce */
        control_flow=0;//return;mod to misra14.7
    }

    if(control_flow!=0)
    {
        if(!Flgrespdelay)
        {
            ccpSendCrm();
        }
    }

    return;
}



/*--------------------------------------------------------------------------*
 * CalculateChecksum - Command Processor 
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CalculateChecksum(void)
{   
    uint16_t sum16; /* For CRC16 */
#ifdef _TEST_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
    
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskCCPChk++;

    /* Initialize Responce */
    sum16 = 0xffff; 

    sum16 = update_crc16(sum16, (uint8_t*)CCP_CRCParams.CRCStartAddress, CCP_CRCParams.CRCsize, UTILS_CRC_FORWARD);
    ccp.Crm[4]= (sum16 >> 8 ) & 0x00ff;
    ccp.Crm[5]= (sum16 ) & 0x00ff;
    ccp.Crm[6]= 0xff;
    ccp.Crm[7]= 0xff;


    ccpSendCrm();
    Flgrespdelay=FALSE;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerCCPChkms)
        {
            TaskTimerCCPChkms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

#endif /*_BUILD_CCP_*/
