#**************************************************************************/
#* FILE NAME: intc_sw_handlers.s            COPYRIGHT (c) Freescale 2004  */
#*                                                All Rights Reserved     */
#* DESCRIPTION:                                                           */
#*        This file creates prolog, epilog for C ISR and enables nested   */
#*        interrupts. This file starts in memory at the beginning of the  */
#*        ".xcptn" section designated by the label "IVOR4Handler".        */
#* WARNING:  This stack frame does not save the SPE s Accumulator, which  */
#*           is required if SPE instructions are used in ISRs.   If SPE   */
#*           instructions are used, the stack frame must include the      */
#*           accumulator, and prologue and epilogue must be modified.     */
#=========================================================================*/
#*                                                                        */
#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
#* ---   -----------   ----------   ---------------------                 */
#* 1.0:  S. Mihalik    23/Apr/04     Initial version                      */
#* 1.1:  B. Terry      29/Jul/04    Modified read of IACKR using          */
#*                                  pointer to determine vector  address. */
#* 1.2   G. Jackson    30/Jul/04    Added ".xcptn" section designation    */
#*                                   for placement into mpc5500cfg.       */
#* 1.3   G. Jackson    12/Oct/04    Green Hills now does not require      */
#*                                    quotation marks around the section  */
#*                                  Added syntax to generate symbols for  */
#*                                    debug.                              */
#**************************************************************************/

# STACK FRAME DESIGN for IVOR 4: Depth: 20 words (0x50, or 80 bytes)
#            ************* ______________
#   0x4C     *  GPR12    *    ^
#   0x48     *  GPR11    *    |
#   0x44     *  GPR10    *    |
#   0x40     *  GPR9     *    |
#   0x3C     *  GPR8     *    |
#   0x38     *  GPR7     *  GPRs (32 bit)
#   0x34     *  GPR6     *    |
#   0x30     *  GPR5     *    |
#   0x2C     *  GPR4     *    |
#   0x28     *  GPR3     *    |
#   0x24     *  GPR0     * ___v__________
#   0x20     *  CR       * __CR__________
#   0x1C     *  XER      *    ^
#   0x18     *  CTR      *    |
#   0x14     *  LR       * locals for 16 B alignment
#   0x10     *  SRR1	   *    |
#   0x0C     *  SRR0     *____v__________
#   0x08     *  resvd-LR * Reserved for calling function
#   0x04     *  padding  *  
#   0x00     *  SP       * Backchain (same as gpr1 in GPRs)
#            ************* 
    .equ __GRNHS__,  1  // Designation for the Green Hills compiler
    .equ __PEGNU__,  0  // Designation for the P&E Micro Gnu compiler
    .equ __DIABCC__, 0  // Designation for the Wind River compiler
    .equ __CWWRKS__, 0  // Designation for the Metrowerks CodeWarrior compiler

    .include "mpc5500_usrdefs.inc"

    .if __CWWRKS__
    .include "wdt.cfg"
    .else
    .equ WDT_PSR_INTH,  1
    .endif

    .globl   IVOR0Handler
    .globl   IVOR1Handler
    .globl   IVOR2Handler
    .globl   IVOR3Handler
    .globl   IVOR4Handler
    .globl   IVOR5Handler
    .globl   IVOR6Handler
    .globl   IVOR7Handler
    .globl   IVOR8Handler
    .globl   IVOR9Handler
    .globl   IVOR10Handler
    .globl   IVOR11Handler
    .globl   IVOR12Handler
    .globl   IVOR13Handler
    .globl   IVOR14Handler
    .globl   IVOR15Handler
    .globl   IVOR32Handler
    .globl   IVOR33Handler
    .globl   IVOR34Handler

    .extern  WDT_ISR		          #  defined in WDT_Events.c file
#    .extern  IVOR2_UserFunction       # will be assigned to an Ivor2 manager function in appManager
    .extern  IVOR2_Manager  #  defined in mboot.c file
    .extern  IVOR10_Manager				#  defined in mboot.c file
    .extern  IVOR11_Manager       #  defined in mboot.c file
    .extern  IVOR_Common_Manager  #  defined in mboot.c file

		# The following variables are user facilities to detect the type of Ivor
		# occurred and its own info
    .extern  IvorIndex						#  defined in sys.c file
    .extern  CSRR0_Value					#  defined in sys.c file
    .extern  CSRR1_Value					#	 defined in sys.c file
    .extern  SRR0_Value						#	 defined in sys.c file
    .extern  SRR1_Value						#	 defined in sys.c file
    .extern  SPR_ESRValue					#	 defined in sys.c file
    .extern  SPR_DEARValue				#	 defined in sys.c file
    .extern  SPR_MCSRValue				#	 defined in sys.c file
    
    .if __CWWRKS__
    .function "IVOR4Handler",prolog4,IVOR5Handler-prolog4 
    .function "IVOR10Handler",prolog10,IVOR11Handler-prolog10 
    .endif
    
#################################################
#       This is the start of the .xcptn section.

    .if __PEGNU__
    .section ".xcptn","ax"        # The "ax" is required to generate "non-text" code
    .align 4                      # Align IVOR handlers on a 16 byte boundary 
    .endif

    .if __GRNHS__ 
    .section .xcptn,ax            # The "ax" generates symbols for debug
    .align 4                      # Align IVOR handlers on a 16 byte boundary 
    .endif

    .if __DIABCC__
    .section .xcptn,c             # The "c" generates symbols for debug
    .align 4                      # Align IVOR handlers on a 16 byte boundary 
    .endif

    .if __CWWRKS__
    .section .xcptn               # The "text" can only generate symbols for
#                                 #  one user defined section.
#                                 # Align IVOR handlers on a 16 byte boundary 
    .align 16                     # CodeWarrior requires an .align 16
#                                 # Other compilers require an .align 4 for (2**4)
    .endif

    .equ  INTC_IACKR, 0xfff48010  # Interrupt Acknowledge Register address
    .equ  INTC_EOIR,  0xfff48018  # End Of Interrupt Register address
    .equ  INTC_CPR,   0xfff48008
    .equ  IVOR10_PRI, 0xB
		
##################################
IVOR0Handler:
	  b  IVOR0Handler


##################################    
#    .if __CWWRKS__ 
#                                 # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
#                                 # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR1Handler:
prolog1:                          # PROLOGUE 
    stwu    r1, -0x58 (r1)        # create stack frame and store back chain
    stw     r0, 0x24 (r1)         # save working registers R0
    stw     r3, 0x28 (r1)         # store a working register
    mfLR    r0                    # store LR (Store now since LR will be used)
    stw     r0, 0x14 (r1)        

    li      r0, 1                 # load in R0 IVOR index
    lis     r3,IvorIndex@ha
    addi    r3,r3,IvorIndex@l
    stb     r0,0(r3)

    mfCSRR1 r0                    # get CSRR1 
    stw     r0, 0x10 (r1)					# save it on the stack
    lis     r3, CSRR1_Value@h
    ori     r3, r3, CSRR1_Value@l
    stw     r0, 0(r3)							

    mfCSRR0 r0                    # get CSRR0 
    stw     r0, 0x0C (r1)         # save it on the stack
    lis     r3, CSRR0_Value@h
    ori     r3, r3, CSRR0_Value@l
    stw     r0, 0(r3)							 
    mfCR    r0                    # get CR
    stw     r0,  0x20 (r1)		    # save it on the stack
					  
    mfspr   r0, 61	              # get DEAR; SPR_DEAR=61
    lis     r3, SPR_DEARValue@h
    ori     r3, r3, SPR_DEARValue@l
    stw     r0, 0(r3)

    mfspr   r0, 572	              # get MCSR; SPR_MCSR=572
    lis     r3, SPR_MCSRValue@h
    ori     r3, r3, SPR_MCSRValue@l
    stw     r0, 0(r3)

    bl      HandlerSaveContext_ph2 # branch to routine that save gprs contents

    lis     r3, IVOR_Common_Manager@ha
    addi    r3, r3, IVOR_Common_Manager@l
    mtLR    r3                    # store ISR address to LR to use for branching later
    blrl                          # branch to ISR, but return here
epilog1:                          # EPILOGUE
#                                 # STEP 6 :  RESTORE CONTEXT
    mbar 0                        # ensure interrupt flag has finished clearing

    bl      HandlerRestoreContext_ph1

    lwz     r0, 0x20 (r1)         # Restore CR
    mtcrf   0xff, r0
    lwz     r3,  0x28 (r1)	      # restore R3 gprs register
    lwz     r4,  0x2C (r1)	      # restore R4 gprs register
    lwz     r0,  0x0C (r1)        # Restore CSRR0
    mtCSRR0 r0
    lwz     r0,  0x10 (r1)        # Restore CSRR1
    mtCSRR1 r0
    lwz     r0,  0x14 (r1)        # restore LR
    mtLR    r0									 
    lwz     r0,  0x24 (r1)        # restore working register
    addi    r1,  r1, 0x58         # restore space on stack
    rfci                          # restores machine state, including reenabling
                                  # critical interrupts MSR[CE].


##################################    
#    .if __CWWRKS__ 
#                                 # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
#                                 # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR2Handler:
#@Eluzzu: added for new Ivor2 management
    .extern Ivor2_ReturnJumpingFaultInstr # Shared between Ivor2 handler and 
                                          # checkFaultyAddrFromLastIvor2 function
                                          # to make the Ivor2 return to the address
                                          # of the faulty instruction + 4,
                                          # to not execute it again.

    stwu   r1, -0x58 (r1)         # Create stack frame and store back chain
    stw    r0, 0x24 (r1)          # Save working registers R0
    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
    stw    r0, 0x14 (r1)
    stw    r3, 0x28 (r1)          # Store a working register R3


    lis    r0, 0x02000200@h         # patch for SPE functionalities (apu instruction on vectors)
    ori    r0,r0,0x02000200@l
    mtmsr  r0


  ### HandlerSaveContext_ph1 CUSTOMIZATION to change SRR0 to the next address ###

    mfSRR1 r0                     # get SRR1 
    stw    r0, 0x10 (r1)                    # save it on the stack

    
    lis     r3,    Ivor2_ReturnJumpingFaultInstr@h
    ori     r3,r3, Ivor2_ReturnJumpingFaultInstr@l
    lbz     r0, 0x0(r3)                     # Get an adder for the SRRO address to accomplish
    mr      r3, r0                          # ivor2 behaviour explained in the following comment
    mfSRR0  r0                    # get SRR0
    evaddw  r3, r0, r3                      #@Eluzzu: adding 4 in case of EEPROM probably corrupted,
                                            # because we have to continue after interrupt WITHOUT
                                            # repeat the faulty instruction. The saved SRR0
                                            # will be the value of the PC when IVOR2 returns.
                                            # It is incremented here by 4(an instruction)
                                            # and restored when IVOR2_Manager ends by using
                                            # the HandlerRestoreContext_ph2 subroutine.
    stw    r3, 0x0C (r1) # save it on the stack

    mfCR   r0                     # get CR
    stw    r0, 0x20 (r1)                    # save it on the stack

  ######### end of HandlerSaveContext_ph1 CUSTOMIZATION

    bl     HandlerSaveContext_ph2    
    
    li   r4,2  
    lis  r3,IvorIndex@ha
    addi r3,r3,IvorIndex@l
    stb  r4,0(r3)

    mfSRR1 r4                     # get SRR1 
    lis  r3,SRR1_Value@h
    ori  r3,r3,SRR1_Value@l
    stw  r4,0(r3)    

    mfSRR0 r4                     # get SRR0 
    lis  r3,SRR0_Value@h
    ori  r3,r3,SRR0_Value@l
    stw  r4,0(r3)

    mfspr r4,62	                  # get ESR; SPR_ESR=62
    lis   r3,SPR_ESRValue@h
    ori   r3,r3,SPR_ESRValue@l
    stw   r4,0(r3)

    mfspr r4,61	                  # get ESR; SPR_DEAR=61
    lis   r3,SPR_DEARValue@h
    ori   r3,r3,SPR_DEARValue@l
    stw   r4,0(r3)

   
    lis  r3,IVOR2_Manager@ha
    addi r3,r3,IVOR2_Manager@l
    mtlr r3
    blrl

    mbar 0                          #sinchronization
                                  
    bl     HandlerRestoreContext_ph1
    bl     HandlerRestoreContext_ph2

    lwz    r0,  0x14 (r1)         # Restore LR
    mtLR   r0
    lwz    r0,  0x24 (r1)         # Restore working register
    addi   r1,  r1, 0x58          # Restore space on stack
    
    rfi
 

##################################    
#    .if __CWWRKS__ 
#                                 # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
#                                 # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR3Handler:
    li   r4,3  
    b    NCI_Handler

	 

##################################    
#    .if __CWWRKS__ 
#                                 # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
#                                 # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR4Handler:
prolog4:                          # PROLOGUE 
    stwu   r1, -0x58 (r1)         # Create stack frame and store back chain
    stw    r0, 0x24 (r1)          # Save working registers R0
    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
    stw    r0, 0x14 (r1)
    stw    r3, 0x28 (r1)          # Store a working register R3

    bl     HandlerSaveContext_ph1
#    mfSRR1 r0                     # get SRR1 
#    stw    r0, 0x10 (r1)					# save it on the stack
#    mfSRR0 r0                     # get SRR0
#    stw    r0, 0x0C (r1)					# save it on the stack
#    mfCR   r0                     # get CR
#    stw    r0, 0x20 (r1)					# save it on the stack
##########################

    lis    r3, INTC_IACKR@h       # Store address of IACKR in r3
    ori    r3, r3, INTC_IACKR@l
    lwz    r3, 0(r3)              # Store contents of IACKR in r3 (this is vector table
                                  # address)
    lwz    r0, 0(r3)              # Read ISR address from ISR Vector Table address
    stw    r0, 0x08(r1)

    lis    r0, 0x02008200@h			# patch for SPE functionalities
    ori   r0,r0,0x02008200@l
    mtmsr  r0
#    wrteei 1                      # Set MSR[EE]=1 (must wait a couple clocks after reading IACKR)

    bl     HandlerSaveContext_ph2     
#    stw    r12, 0x4C (r1)         # store rest of gprs
#    stw    r11, 0x48 (r1)	        #    |
#    stw    r10, 0x44 (r1)	        #    |
#    stw    r9,  0x40 (r1)	        #    |
#    stw    r8,  0x3C (r1)	        #    |
#    stw    r7,  0x38 (r1)	        #    |
#    stw    r6,  0x34 (r1)	        #    |
#    stw    r5,  0x30 (r1)	        #    |
#    stw    r4,  0x2c (r1)  	      #____v____
#    mfXER  r0                     # get XER
#    stw    r0,  0x1C (r1)	        # save it on the stack
#    mfCTR  r0                     # get CTR
#    stw    r0,  0x18 (r1)	        # save it on the stack
###########################
	  lwz    r0,  0x8(r1)
	  mtLR   r0                     # Store ISR address to LR to use for branching later
    blrl                          # Branch to ISR, but return here
epilog4:                          # EPILOGUE
                                  # STEP 6 :  RESTORE CONTEXT
    mbar 0                        # Ensure interrupt flag has finished clearing
                                  # before writing to INTC_EIOR
    bl     HandlerRestoreContext_ph1
#    lwz    r0, 0x18 (r1)          # get CTR off stack
#    mtCTR  r0			                # restore it
#    lwz    r0, 0x1C (r1)          # get XER off stack
#    mtXER  r0			                # restore it
#    lwz    r5,  0x30 (r1)	        # restore rest of gprs
#    lwz    r6,  0x34 (r1)	        #    |
#    lwz    r7,  0x38 (r1)	        #    |
#    lwz    r8,  0x3C (r1)	        #    |
#    lwz    r9,  0x40 (r1)	        #    |
#    lwz    r10, 0x44 (r1)	        #    |
#    lwz    r11, 0x48 (r1)	        #    |
#    lwz    r12, 0x4C (r1)	        #____v____
###########################
    wrteei 0                      # Disable interrupts

    li     r3,0		
    lis    r4, INTC_EOIR@ha       # Load upper half of EIOR address to r4
    addi   r4, r4, INTC_EOIR@l    # Load lower half of EIOR address to R4
    stw    r3, 0(r4)              # Write 0 to INTC_EOIR, address 0xFFF4 8018

    bl     HandlerRestoreContext_ph2
#    lwz    r0, 0x20 (r1)          # Restore CR
#    mtcrf  0xff, r0
#    lwz    r3,  0x28 (r1)	        # restore R3 gprs register
#    lwz    r4,  0x2C (r1)	        # restore R4 gprs register
#    lwz    r0,  0x0C (r1)         # Restore SRR0
#    mtSRR0 r0
#    lwz    r0,  0x10 (r1)         # Restore SRR1
#    mtSRR1 r0
###########################
    lwz    r0,  0x14 (r1)         # Restore LR
    mtLR   r0
    lwz    r0,  0x24 (r1)         # Restore working register
    addi   r1,  r1, 0x58          # Restore space on stack
    rfi                           # End of Interrupt - re-enables interrupts.


##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR5Handler:
    li   r4,5  
    b    NCI_Handler
	 
##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR6Handler:
    li   r4,6  
    b    NCI_Handler
	 
##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR7Handler:
    li   r4,7  
    b    NCI_Handler


##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR8Handler:
    li   r4,8  
    b    NCI_Handler
	 

##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR9Handler:
	  b  IVOR9Handler

##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR10Handler:
prolog10:                         # PROLOGUE 
    stwu   r1, -0x58 (r1)         # Create stack frame and store back chain
    stw    r0, 0x24 (r1)          # Save working registers R0
    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
    stw    r0, 0x14 (r1)
    stw    r3, 0x28 (r1)          # Store a working register R3

	  lis    r3, 0x0800
    mtspr  336, r3

    bl     HandlerSaveContext_ph1
	  bl     Check_CPR_priority

    lis    r0, 0x02008200@h			# patch for SPE functionalities
    ori   r0,r0,0x02008200@l
    mtmsr  r0

#	  wrteei 1                      # Set MSR[EE]=1 (must wait a couple clocks after reading IACKR)
    bl     HandlerSaveContext_ph2

    lis    r3,IVOR10_Manager@ha
    addi   r3,r3,IVOR10_Manager@l
    mtLR   r3                     # Store ISR address to LR to use for branching later
    blrl                          # Branch to ISR, but return here
epilog10:                         # EPILOGUE
                                  # STEP 6 :  RESTORE CONTEXT
    mbar 0                        # Ensure interrupt flag has finished clearing
                                  # before writing to INTC_EIOR
    bl     HandlerRestoreContext_ph1
    wrteei 0                      # Disable interrupts
	  bl     Restore_CPR
    bl     HandlerRestoreContext_ph2
    lwz    r0,  0x14 (r1)         # Restore LR
    mtLR   r0
    lwz    r0,  0x24 (r1)         # Restore working register
    addi   r1,  r1, 0x58          # Restore space on stack
    rfi                           # End of Interrupt - re-enables interrupts.

	

##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR11Handler:
prolog11:                         # PROLOGUE 
    stwu   r1, -0x58 (r1)         # Create stack frame and store back chain
    stw    r0, 0x24 (r1)          # Save working registers R0
    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
    stw    r0, 0x14 (r1)
    stw    r3, 0x28 (r1)          # Store a working register R3

    lis    r3,  0xC000            # prevent WDT interrupt by writing 1 to 
    mtspr  336, r3                # [ENW,WIS] bit fields in TSR register
    lis    r3,  0x0400            # clear Fixed interval interrupt by writing 1
    mtspr  336, r7                # to [FIS] bit field in TSR register

    bl     HandlerSaveContext_ph1
	  bl     Check_CPR_priority
	  wrteei 1                      # Set MSR[EE]=1 (must wait a couple clocks after reading IACKR)
    bl     HandlerSaveContext_ph2

    lis    r3,IVOR11_Manager@ha
    addi   r3,r3,IVOR11_Manager@l
    mtLR   r3                     # Store ISR address to LR to use for branching later
    blrl                          # Branch to ISR, but return here
epilog11:                         # EPILOGUE
                                  # STEP 6 :  RESTORE CONTEXT
    mbar 0                        # Ensure interrupt flag has finished clearing
                                  # before writing to INTC_EIOR
    bl     HandlerRestoreContext_ph1
    wrteei 0                      # Disable interrupts
	  bl     Restore_CPR
    bl     HandlerRestoreContext_ph2
    lwz    r0,  0x14 (r1)         # Restore LR
    mtLR   r0
    lwz    r0,  0x24 (r1)         # Restore working register
    addi   r1,  r1, 0x58          # Restore space on stack
    rfi                           # End of Interrupt - re-enables interrupts.


    .if __CWWRKS__ 
    .ifdef  WDT_PSR_INTH          # defined in WDT.cfg file                       
    .else
    .if  WDT_PSR_INTH          # defined in WDT.cfg file                       
    .endif

##################################    

#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif

IVOR12Handler:
    li   r4,12     
    lis  r3,IvorIndex@ha
    addi r3,r3,IvorIndex@l
    stb  r4,0(r3)

    mfcsrr1 r4                     # get CSRR1 
    lis  r3,CSRR1_Value@h					 
    ori  r3,r3,CSRR1_Value@l
    stw  r4,0(r3)								    

    mfcsrr0 r4                     # get CSRR0 
    lis  r3,CSRR0_Value@h
    ori  r3,r3,CSRR0_Value@l
    stw  r4,0(r3)

    lis    r3, 0x02000200@h			# patch for SPE functionalities
    ori    r3,r3,0x02000200@l
    mtmsr  r3

    lis  r3,IVOR_Common_Manager@ha
    addi r3,r3,IVOR_Common_Manager@l
    mtlr r3
    blrl

#prolog12:                         # PROLOGUE		 
                                  # save context (might be meaningless here)
#     stwu r1, -16(r1)             # allocate 16 bytes on stack
#     stw r6, 4(r1)                # save r6 on stack so it can be used in the handler
#     mfcsrr0 r6                   # get CSRR0
#     stw r6, 8(r1)                # save it on the stack
#     mfcsrr1 r6                   # get CSRR1
#     stw r6, 12(r1)               # save it on the stack

#     lis  r6,WDT_ISR@ha
#     addi r6,r6,WDT_ISR@l
#     mtlr r6
#     blrl

	 
#epilog12:                         # EPILOGUE
# restore context
#     lwz r6, 12(r1)               # get CSRR1 off stack
#     mtcsrr1 r6                   # restore it
#     lwz r6, 8(r1)                # get CSRR0 off stack
#     mtcsrr0 r6                   # restore it
#     lwz r6, 4(r1)                # get r6 off stack
#     addi r1, r1, 16              # restore stack pointer
# return from critical interrupt -
#     rfci                         # restores machine state, including reenabling
                                  # critical interrupts MSR[CE]. 
    
    .endif #WDT_PSR_INTH      


##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR13Handler:
    li   r4,13  
    b    NCI_Handler

##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR14Handler:
    li   r4,14  
    b    NCI_Handler
    
##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR15Handler:
	  b  IVOR15Handler
	 
##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR32Handler:
    li   r4,32  
    b    NCI_Handler


##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR33Handler:
    li   r4,33  
    b    NCI_Handler
    
##################################    
#    .if __CWWRKS__ 
                                  # Align IVOR handlers on a 16 byte boundary 
    .align 4                      # CodeWarrior requires an .align 16
                                  # Other compilers require an .align 4 for (2**4)
#    .endif
IVOR34Handler:
    li   r4,34  
    b  NCI_Handler



##################################    
HandlerSaveContext_ph1:
    mfSRR1 r0                     # get SRR1 
    stw    r0, 0x10 (r1)					# save it on the stack
    mfSRR0 r0                     # get SRR0
    stw    r0, 0x0C (r1)					# save it on the stack
    mfCR   r0                     # get CR
    stw    r0, 0x20 (r1)					# save it on the stack
    blr


##################################    
HandlerSaveContext_ph2:
    stw    r12, 0x4C (r1)         # store rest of gprs
    stw    r11, 0x48 (r1)	        #    |
    evstwwe r12, 0x50 (r1)
    evstwwe r11, 0x54 (r1)
    stw    r10, 0x44 (r1)	        #    |
    stw    r9,  0x40 (r1)	        #    |
    stw    r8,  0x3C (r1)	        #    |
    stw    r7,  0x38 (r1)	        #    |
    stw    r6,  0x34 (r1)	        #    |
    stw    r5,  0x30 (r1)	        #    |
    stw    r4,  0x2c (r1)  	      #____v____
    mfXER  r0                     # get XER
    stw    r0,  0x1C (r1)	        # save it on the stack
    mfCTR  r0                     # get CTR
    stw    r0,  0x18 (r1)	        # save it on the stack
    blr


##################################    
HandlerRestoreContext_ph1:
    lwz    r0, 0x18 (r1)          # get CTR off stack
    mtCTR  r0			                # restore it
    lwz    r0, 0x1C (r1)          # get XER off stack
    mtXER  r0			                # restore it
    lwz    r5,  0x30 (r1)	        # restore rest of gprs
    lwz    r6,  0x34 (r1)	        #    |
    lwz    r7,  0x38 (r1)	        #    |
    lwz    r8,  0x3C (r1)	        #    |
    lwz    r9,  0x40 (r1)	        #    |
    lwz    r10, 0x44 (r1)	        #    |
    evlwwsplat r11, 0x54 (r1)
    evlwwsplat r12, 0x50 (r1)
    lwz    r11, 0x48 (r1)	        #    |
    lwz    r12, 0x4C (r1)	        #____v____
    blr

##################################    
HandlerRestoreContext_ph2:
    lwz    r0, 0x20 (r1)          # Restore CR
    mtcrf  0xff, r0
    lwz    r3,  0x28 (r1)	        # restore R3 gprs register
    lwz    r4,  0x2C (r1)	        # restore R4 gprs register
    lwz    r0,  0x0C (r1)         # Restore SRR0
    mtSRR0 r0
    lwz    r0,  0x10 (r1)         # Restore SRR1
    mtSRR1 r0
    blr

##################################    
Check_CPR_priority:
    lis    r3, INTC_CPR@h         # load upper half of CPR address in R3
    ori    r3, r3, INTC_CPR@l	    # load lower half of CPR address in R3
    lwz    r0, 0(r3)              # load contents of CPR in R0
    li     r3,IVOR10_PRI          # load current IVOR priority threshold in R3
    cmplw  r0,r3                  # compare threshold priority with the current one
    bgt   unchange_CPR 		        # if current_pri >= threshold_pri then unchange CPR value
    stw    r0, 0x08 (r1)          # store CPR priority
    lis    r3, INTC_CPR@h      	  # load upper half of CPR address in R3
    ori    r3, r3, INTC_CPR@l	    # load lower half of CPR address in R3
    li     r0, IVOR10_PRI	        # load current IVOR priority threshold in R0
    stw    r0, 0(r3)              # store contents of R0 in CPR register
unchange_CPR:
    blr
  

##################################    
Restore_CPR:  
    lis    r3, INTC_CPR@h         # load upper half of CPR address in R3
    ori    r3, r3, INTC_CPR@l	    # load lower half of CPR address in R3
    lwz    r0, 0(r3)              # load contents of CPR in R0
    li     r3,IVOR10_PRI	        # load IVOR priority threshold in R3
    cmplw  r0,r3
    bgt    CPR_not_changed 
    lwz    r0,0x08(r1)            # restore saved priority in R0
    lis    r3, INTC_CPR@h         # load upper half of CPR address in R3
    ori    r3, r3, INTC_CPR@l	    # load lower half of CPR address in R3
    stw    r0, 0(r3)              # store contents of R0 in CPR register
CPR_not_changed:
    blr	



##################################    
NCI_Handler:		                  # Non Recoverable Interrupt handler body 
    lis  r3,IvorIndex@ha
    addi r3,r3,IvorIndex@l
    stb  r4,0(r3)

    mfSRR1 r4                     # get SRR1 
    lis  r3,SRR1_Value@h					 
    ori  r3,r3,SRR1_Value@l
    stw  r4,0(r3)								    

    mfSRR0 r4                     # get SRR0 
    lis  r3,SRR0_Value@h
    ori  r3,r3,SRR0_Value@l
    stw  r4,0(r3)

    mfspr r4,62	                  # get ESR; SPR_ESR=62
    lis   r3,SPR_ESRValue@h
    ori   r3,r3,SPR_ESRValue@l
    stw   r4,0(r3)

    mfspr r4,61	                  # get ESR; SPR_DEAR=61
    lis   r3,SPR_DEARValue@h
    ori   r3,r3,SPR_DEARValue@l
    stw   r4,0(r3)

    lis    r3, 0x02000200@h			# patch for SPE functionalities
    ori    r3,r3,0x02000200@l
    mtmsr  r3

    lis  r3,IVOR_Common_Manager@ha
    addi r3,r3,IVOR_Common_Manager@l
    mtlr r3
    blrl
#end NCI_Handler

