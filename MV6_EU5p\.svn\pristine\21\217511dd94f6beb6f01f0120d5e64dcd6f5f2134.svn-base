/*
 * File: MisfOBD2_private.h
 *
 * Code generated for Simulink model 'MisfOBD2'.
 *
 * Model version                  : 1.588
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Aug 19 10:02:22 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (26), Warnings (6), Error (0)
 */

#ifndef RTW_HEADER_MisfOBD2_private_h_
#define RTW_HEADER_MisfOBD2_private_h_
#include "rtwtypes.h"
#include "MisfOBD2.h"

/* Includes for objects with custom storage classes. */
#include "ion_misf.h"
#include "diagmgm_out.h"
#include "engflag.h"
#include "ETPU_EngineDefs.h"
#include "dtc.h"
#include "syncmgm.h"
#include "rpm_limiter.h"
#include "Engflag.h"
#include "canmgm.h"
#include "loadmgm.h"
#include "Patm_model.h"
#include "Ion_misf.h"
#include "FOInjCtfMgm_out.h"
#include "Temp_mgm.h"
#include "idxctfctrl_out.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKSTEPEMISSOBD2[5];     /* Variable: BKSTEPEMISSOBD2
                                        * Referenced by: '<S12>/BKSTEPEMISSOBD2'
                                        * Step emission breakpoint
                                        */
extern int16_T VTSTEPEMISSOBD2[5];     /* Variable: VTSTEPEMISSOBD2
                                        * Referenced by: '<S12>/VTSTEPEMISSOBD2'
                                        * Step emission vector
                                        */
extern int16_T BKNREVOBD2EMISS[4];     /* Variable: BKNREVOBD2EMISS
                                        * Referenced by: '<S49>/BKNREVOBD2EMISS'
                                        * Emission window breakpoint
                                        */
extern int16_T THRTAIRMISFOBD2;        /* Variable: THRTAIRMISFOBD2
                                        * Referenced by: '<S46>/THRTAIRMISFOBD2'
                                        * Temperature to enable Misfire emissions
                                        */
extern int16_T BKENCMICATOBD2[7];      /* Variable: BKENCMICATOBD2
                                        * Referenced by: '<S49>/BKENCMICATOBD2'
                                        * Enable Emission window breakpoint
                                        */
extern uint16_T BKLOADMISFOBD2[6];     /* Variable: BKLOADMISFOBD2
                                        * Referenced by: '<S41>/BKLOADMISFOBD2'
                                        * Load breakpoint
                                        */
extern uint16_T BKENRPMCATOBD2[7];     /* Variable: BKENRPMCATOBD2
                                        * Referenced by: '<S49>/BKENRPMCATOBD2'
                                        * Enable Emission window breakpoint
                                        */
extern uint16_T BKRPMMISFOBD2[6];      /* Variable: BKRPMMISFOBD2
                                        * Referenced by: '<S41>/BKRPMMISFOBD2'
                                        * Rpm breakpoint
                                        */
extern uint16_T NREVOBD2CAT;           /* Variable: NREVOBD2CAT
                                        * Referenced by: '<S48>/NREVOBD2CAT'
                                        * Window CAT counter
                                        */
extern uint16_T TBMISFCATOBD2[36];     /* Variable: TBMISFCATOBD2
                                        * Referenced by: '<S11>/TBMISFCATOBD2'
                                        * CAT threshold
                                        */
extern uint16_T TBMISFEMISSOBD2[36];   /* Variable: TBMISFEMISSOBD2
                                        * Referenced by: '<S12>/TBMISFEMISSOBD2'
                                        * Emissions threshold
                                        */
extern uint16_T THREMISSOBD2;          /* Variable: THREMISSOBD2
                                        * Referenced by: '<S12>/Calc_EmissFlg'
                                        * Misfire threshold emissions
                                        */
extern uint16_T THRPRESMISFOBD2;       /* Variable: THRPRESMISFOBD2
                                        * Referenced by: '<S46>/THRPRESMISFOBD2'
                                        * Pressure to enable Misfire emissions
                                        */
extern uint16_T VTNREVOBD2EMISS[4];    /* Variable: VTNREVOBD2EMISS
                                        * Referenced by: '<S48>/VTNREVOBD2EMISS'
                                        * window emission vector
                                        */
extern uint8_T CYLPERCMISFOBD2;        /* Variable: CYLPERCMISFOBD2
                                        * Referenced by:
                                        *   '<S11>/Calc_CylMisfire'
                                        *   '<S12>/Calc_CylMisfire'
                                        * Misfire cylinder threshold percentage
                                        */
extern uint8_T NFILTMISFOBD2;          /* Variable: NFILTMISFOBD2
                                        * Referenced by: '<S9>/Misfire_Filter'
                                        * Filter misfire number
                                        */
extern uint8_T TBENMISFCATOBD2[49];    /* Variable: TBENMISFCATOBD2
                                        * Referenced by: '<S47>/TBENMISFCATOBD2'
                                        * Enable Misfire CAT OBD2
                                        */
extern uint8_T TBENMISFEMISSOBD2[49];  /* Variable: TBENMISFEMISSOBD2
                                        * Referenced by: '<S47>/TBENMISFEMISSOBD2'
                                        * Enable Misfire Emissions OBD2
                                        */
extern uint8_T TESTMISFOBD2;           /* Variable: TESTMISFOBD2
                                        * Referenced by: '<S9>/TESTMISFOBD2'
                                        * Enable Test misfire
                                        */

#ifdef __cplusplus

extern "C" {

#endif

  extern void MisfOBD2_Return_Addr_U16_Start_wrapper(void);
  extern void MisfOBD2_Return_Addr_U16_Outputs_wrapper(const uint16_T *in,
    const uint16_T *diag,
    uint32_T *addr);
  extern void MisfOBD2_Return_Addr_U16_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

#ifdef __cplusplus

extern "C" {

#endif

  extern void fc_MisfOBD2_Update_Start_wrapper(void);
  extern void fc_MisfOBD2_Update_Outputs_wrapper(const uint16_T *val,
    const uint32_T *addr,
    uint16_T *pCode);
  extern void fc_MisfOBD2_Update_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

extern void MisfOBD2_Cyl_Counter_Init(uint16_T *rty_cntMisfCAT, uint16_T
  *rty_cntMisfEmiss, rtDW_Cyl_Counter_MisfOBD2_T *localDW);
extern void MisfOBD2_Cyl_Counter(boolean_T rtu_misfEv, uint8_T rtu_resetCAT,
  uint8_T rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_Cyl_Counter_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_1_Init(uint16_T *rty_cntMisfCAT, uint16_T
  *rty_cntMisfEmiss, rtDW_fc_1_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_1(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
  rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_1_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_2_Init(uint16_T *rty_cntMisfCAT, uint16_T
  *rty_cntMisfEmiss, rtDW_fc_2_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_2(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
  rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_2_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_3_Init(uint16_T *rty_cntMisfCAT, uint16_T
  *rty_cntMisfEmiss, rtDW_fc_3_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_3(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
  rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_3_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_4_Init(uint16_T *rty_cntMisfCAT, uint16_T
  *rty_cntMisfEmiss, rtDW_fc_4_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_4(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
  rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_4_MisfOBD2_T *localDW);
extern void MisfOBD2_Sum_Init(uint16_T *rty_sum);
extern void MisfOBD2_Sum(const uint16_T rtu_in[4], uint16_T *rty_sum);
extern void MisfOBD2_fc_ThrCAT_Init(int8_T *rty_MisfCylCAT);
extern void MisfOBD2_fc_ThrCAT(const uint16_T rtu_vtCntMisfCAT[4], uint16_T
  rtu_index_rpm, uint16_T rtu_ratio_rpm, uint16_T rtu_index_load, uint16_T
  rtu_ratio_load, uint8_T rtu_resetCAT, uint8_T rtu_resSync, uint16_T
  rty_VtCntMisfCAT[4], uint8_T *rty_FlgCatMisfOBD2, int8_T *rty_MisfCylCAT,
  uint16_T *rty_SumCatMisfOBD2, rtDW_fc_ThrCAT_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_ThrEmiss_Init(uint8_T *rty_FlgEmissMisfOBD2, int8_T
  *rty_MisfCylEmiss, uint16_T *rty_CntEmissOBD2);
extern void MisfOBD2_fc_ThrEmiss(const uint16_T rtu_vtCntMisfEmiss[4], uint16_T
  rtu_index_rpm, uint16_T rtu_ratio_rpm, uint16_T rtu_index_load, uint16_T
  rtu_ratio_load, uint8_T rtu_resSync, uint32_T *rty_cntClearDiag, uint16_T
  rty_VtCntMisfEmiss[4], uint8_T *rty_FlgEmissMisfOBD2, int8_T *rty_MisfCylEmiss,
  uint16_T *rty_CntEmissOBD2, uint16_T *rty_SumEmissMisfOBD2,
  rtDW_fc_ThrEmiss_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_revolution_Init(uint8_T *rty_resetEmiss, uint8_T
  *rty_resetCAT, uint16_T *rty_NRevCAT, uint16_T *rty_NRevEmiss, int8_T *rty_6,
  uint8_T *rty_10, int8_T *rty_11, uint16_T *rty_12);
extern void MisfOBD2_fc_revolution(uint16_T rtu_NRevOBD2Cat, uint16_T
  rtu_NRevOBD2Emiss, boolean_T rtu_EnMisfCATOBD2, boolean_T rtu_EnMisfEmissOBD,
  uint16_T rtu_Rpm, uint16_T rtu_Load, uint8_T rtu_resSync, const uint16_T
  rtu_7[4], const uint16_T rtu_8[4], uint8_T *rty_resetEmiss, uint8_T
  *rty_resetCAT, uint16_T *rty_NRevCAT, uint16_T *rty_NRevEmiss, uint16_T rty_4
  [4], uint8_T *rty_5, int8_T *rty_6, uint16_T *rty_7, uint32_T *rty_8, uint16_T
  rty_9[4], uint8_T *rty_10, int8_T *rty_11, uint16_T *rty_12, uint16_T *rty_13,
  rtDW_fc_revolution_MisfOBD2_T *localDW);
extern void MisfOBD2_EnTrig_Init(uint8_T *rty_flg, rtDW_EnTrig_MisfOBD2_T
  *localDW);
extern void MisfOBD2_EnTrig(uint8_T rtu_in, uint8_T rtu_resSync, uint8_T
  *rty_flg, rtDW_EnTrig_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_write_PCode(const uint16_T rtu_DiagToDtcTab[92],
  uint16_T rtu_pCode, rtDW_fc_write_PCode_MisfOBD2_T *localDW);
extern void MisfOBD2_fc_globalCyl(uint8_T rtu_ptFault);
extern void MisfOBD2_Init(void);
extern void MisfOBD2_PreTdc_Init(void);
extern void MisfOBD2_PreTdc(void);
extern void MisfOBD2_T10ms_Init(void);
extern void MisfOBD2_T10ms(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint16_T EECntMisf;             /* '<S1>/Data Store Memory1' */

/* Misfire counter */
extern uint16_T EEVtCntMisfCyl[4];     /* '<S1>/Data Store Memory' */

/* Misfire counter */
#endif                                 /* RTW_HEADER_MisfOBD2_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
