/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "typedefs.h"
#ifdef  MATLAB_MEX_FILE
#define _BUILD_THRPOSMGM_
#define IDN_ANG_THROTTLE
#define IDN_ANG_THROTTLE2
#define _BUILD_DIAGMGM_
#else
#include "analogin.h"
#endif
#include "thrposmgm.h"
#include "diagmgm_out.h"
#include "mathlib.h"

//OUTPUTS VARAIBLES
uint16_T AngThrottle;                     // 2^-4
uint16_T AngThrottle0;                      // 2^-4
uint8_T  RecThrReq;                     // 2^0
uint8_T  recthrreq_freeze;

#ifdef _BUILD_THRPOSMGM_

//TUNING PARAMETERS 
extern int16_T  ANGTHRGAIN;             // 2^-11
extern int16_T  ANGTHROFFSET;       // 5000/1024
extern int16_T  ANGTHRGAIN2;        // 2^-11
extern uint16_T VANGTHRMIN;             // 5000/1024
extern uint16_T VANGTHRMAX;             // 5000/1024
extern uint16_T VANGTHRCOH;             // 5000/1024
extern uint8_T  FORCEANGTHROTTLE;   // 2^0

//LOCAL VARAIBLES
uint16_T AngThrottle1;              // 2^-4
uint16_T AngThrottle2;                      // 2^-4
uint16_T AngThrottle10;                     // 2^-4
uint16_T AngThrottle20;                     // 2^-4
uint16_T VAngThrottle;                      // 5000/1024
int16_T  DAngThrottle;              // 2^-4
uint8_T  StThrRec;                      // 2^0
uint16_T AngThrottle_old;                   // 2^-4
uint8_T  StThrActive;                   // 2^0

// Internal Functions
void ThrPosMgm_ResetVars(void);
void ThrPosMgm_Conversion(void);
void ThrPosMgm_Diagnosis(void);
void ThrPosMgm_GasPos(void);
void ThrPosMgm_GetAngThrottle(uint16_T *AngThrottle, uint16_T VAngThrottle, int16_T ANGTHROFFSET, int16_T ANGTHRGAIN);
void ThrPosMgm_Selector(uint16_T *output, uint16_T input1, uint16_T input2, uint8_T selector);

//Funzioni chiamate dai Task
void ThrPosMgm_Init(void)
{
    ThrPosMgm_ResetVars();
    
    ThrPosMgm_T5ms();
}

void ThrPosMgm_T5ms(void)
{
  ThrPosMgm_Conversion();
    
    ThrPosMgm_Diagnosis();
    
    ThrPosMgm_GasPos();     
}

void ThrPosMgm_ResetVars(void)
{
    AngThrottle = 0;
    DAngThrottle = 0;
    AngThrottle0 = 0;
    AngThrottle1 = 0;
    AngThrottle2 = 0;
    AngThrottle10 = 0;
    AngThrottle20 = 0;
    VAngThrottle = 0;   
    AngThrottle_old = MAX_uint16_T;
    StThrRec = 0;
    StThrActive = USE_THR_1;
    RecThrReq = NO_THRREC;
    recthrreq_freeze = NO_THRREC;
}

void ThrPosMgm_Conversion(void)
{
#if defined(IDN_ANG_THROTTLE) && !defined(IDN_ANG_THROTTLE2)    
  ThrPosMgm_GetAngThrottle(&AngThrottle1, VAngThrottle1, ANGTHROFFSET, ANGTHRGAIN);

#elif defined(IDN_ANG_THROTTLE) && defined(IDN_ANG_THROTTLE2) 
  ThrPosMgm_GetAngThrottle(&AngThrottle1, VAngThrottle1, VAngThrMin1, ANGTHRGAIN);
  ThrPosMgm_GetAngThrottle(&AngThrottle2, VAngThrottle2, VAngThrMin2, ANGTHRGAIN2);

  ThrPosMgm_GetAngThrottle(&AngThrottle10, VAngThrLh1, VAngThrMin1, ANGTHRGAIN);
  ThrPosMgm_GetAngThrottle(&AngThrottle20, VAngThrLh2, VAngThrMin2, ANGTHRGAIN2);
#endif
}

void ThrPosMgm_Diagnosis(void)
{
  uint8_T PtFaultVAngThrottle1 = NO_PT_FAULT;
  uint8_T PtFaultVAngThrottle2 = NO_PT_FAULT;
  uint8_T PtFaultCohVAngThr    = NO_PT_FAULT;
  uint8_T StDiagVAngThrottle1  = NO_FAULT;
  uint8_T StDiagVAngThrottle2  = NO_FAULT;
  uint8_T StDiagCohVAngThr     = NO_FAULT;
  
#if defined(EN_DIAG_VANGTHR_1) && defined(IDN_ANG_THROTTLE)
    DiagMgm_RangeCheck_U16(&PtFaultVAngThrottle1, VAngThrottle1, VANGTHRMIN, VANGTHRMAX, CC_TO_GND, CC_TO_VCC);

  DiagMgm_SetDiagState(DIAG_VANGTHR_1, PtFaultVAngThrottle1, &StDiagVAngThrottle1);
#endif

#if defined(EN_DIAG_VANGTHR_2) && defined(IDN_ANG_THROTTLE2)    
    DiagMgm_RangeCheck_U16(&PtFaultVAngThrottle2, VAngThrottle2, VANGTHRMIN, VANGTHRMAX, CC_TO_GND, CC_TO_VCC);
    
  DiagMgm_SetDiagState(DIAG_VANGTHR_2, PtFaultVAngThrottle2, &StDiagVAngThrottle2);
#endif

#if defined(EN_DIAG_COH_VANGTHR) && defined(IDN_ANG_THROTTLE) && defined(IDN_ANG_THROTTLE2)
    // Fault Puntuale
    if(sign(ANGTHRGAIN) == sign(ANGTHRGAIN2))
    {  
        uint16_T     tmp;
        //if(abs(VAngThrottle1 - VAngThrottle2) > VANGTHRCOH)  
        tmp=    (uint16_T) GenAbs((int32_T)(VAngThrottle1 - VAngThrottle2), INT32_TYPE);
        if(tmp > VANGTHRCOH)
		{	
			PtFaultCohVAngThr = SIG_NOT_PLAUSIBLE;
		}	
    }
    else
    {  
        if(abs(VAngThrottle1 - (MAX_VANGTHROTTLE - VAngThrottle2)) > VANGTHRCOH) 
        {
			PtFaultCohVAngThr = SIG_NOT_PLAUSIBLE;
		}	
    }
    
  DiagMgm_SetDiagState(DIAG_COH_VANGTHR, PtFaultCohVAngThr, &StDiagCohVAngThr);
#endif

    ThrPosMgm_RecoveryTable(StDiagVAngThrottle1, StDiagVAngThrottle2, StDiagCohVAngThr);
    }
    
void ThrPosMgm_GasPos(void) 
{    
    // la prima volta impongo DAngThrottle = 0
    if(AngThrottle_old != MAX_uint16_T)
	{	
        AngThrottle_old = AngThrottle;
	}	

#if (USE_ANGTHROTTLE_CAN == 1)
  // In caso di angolo farfalla da CAN
  VAngThrottle = VAngThrottle1;
  AngThrottle0 = AngThrottle10;
  if(VDAngThrottleCAN)
  {	  
      AngThrottle = AngThrottleCAN;
  }	  
#else
  ThrPosMgm_Selector(&VAngThrottle, VAngThrottle1, VAngThrottle2, StThrActive);
  ThrPosMgm_Selector(&AngThrottle, AngThrottle1, AngThrottle2, StThrActive);
  ThrPosMgm_Selector(&AngThrottle0, AngThrottle10, AngThrottle20, StThrActive);
#endif

    // la prima volta impongo DAngThrottle = 0
    if(AngThrottle_old == MAX_uint16_T)
	{	
        AngThrottle_old = AngThrottle;
	}	
    DAngThrottle = AngThrottle - AngThrottle_old;
}

void ThrPosMgm_GetAngThrottle(uint16_T *AngThrottle, uint16_T VAngThrottle, int16_T ANGTHROFFSET, int16_T ANGTHRGAIN) 
{  
    int32_T tmpAngThrottle;
    
    tmpAngThrottle = ((((((int32_T)VAngThrottle - (int32_T)ANGTHROFFSET) * 5000) >> 10) * ANGTHRGAIN) >> 7);

    if(tmpAngThrottle < 0)
	{	
        *AngThrottle = 0;
	}	
    else if(tmpAngThrottle > MAX_PERC)
	{	
      *AngThrottle = MAX_PERC;
	}  
    else
	{	
        *AngThrottle = (uint16_T)tmpAngThrottle;
	}	
}

void ThrPosMgm_Selector(uint16_T *output, uint16_T input1, uint16_T input2, uint8_T selector)
{
    switch(selector)
    {
        case USE_THR_1:
        {
            *output = input1;
            break;
        }
        case USE_THR_2:
        {
            *output = input2;
            break;
        }
        case USE_THR1_OLD:
        case USE_THR2_OLD:
        default:
        {
            // mantiene il vecchio valore in caso di USE_THR_LAST
            break;
        }
    }
}

void ThrPosMgm_RecoveryTable(uint8_T StDiagVAngThr1, uint8_T StDiagVAngThr2, uint8_T StDiagCohVAngThr)
{
    uint8_T tmpCond_0;
    uint8_T tmpCond_1;
    uint8_T tmpCond_2;
    uint8_T tmpCond_3;
    uint8_T tmpCond_4;
    uint8_T tmpCond_5;
    uint8_T tmpCond_6;
    uint8_T tmpCond_7;
    uint8_T tmpCond_8;
    uint8_T tmpCond_9;
    uint8_T tmpCond_10;
    uint8_T tmpCond_11;
    uint8_T tmpCond_12;
    
    // Diagnosi StDiagVAngThr1
    tmpCond_0 = (StDiagVAngThr1 == NO_FAULT); 
    tmpCond_1 = (StDiagVAngThr1 == FAULT_FILTERING);
    tmpCond_2 = (StDiagVAngThr1 == FAULT);
    // Diagnosi StDiagVAngThr2  
    tmpCond_3 = (StDiagVAngThr2 == NO_FAULT); 
    tmpCond_4 = (StDiagVAngThr2 == FAULT_FILTERING);
    tmpCond_5 = (StDiagVAngThr2 == FAULT);
    // Diagnosi StDiagCohVAngThr        
    tmpCond_6 = (StDiagCohVAngThr == NO_FAULT);
    tmpCond_7 = (StDiagCohVAngThr == FAULT_FILTERING);
    tmpCond_8 = (StDiagCohVAngThr == FAULT);
    // Test vari            
    tmpCond_9 = !(StThrActive & 0x01);  //(StThrActive == USE_THR_1) || (StThrActive == USE_THR1_OLD);
    tmpCond_10 = (AngThrottle1 >= AngThrottle2);
    tmpCond_11 = FlgPresCoh1;
    tmpCond_12 = FlgPresCoh2;
    
  if(FORCEANGTHROTTLE == 1) 
  {
        // FORCEANGTHROTTLE 1
        StThrRec = 0;
        StThrActive = USE_THR_1;
        RecThrReq = NO_THRREC;
  } 
  else if(FORCEANGTHROTTLE == 2) 
  {
        // FORCEANGTHROTTLE 2
        StThrRec = 0;
        StThrActive = USE_THR_2;
        RecThrReq = NO_THRREC;
  }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_6 && tmpCond_9)
    {
        // NO_FAULT NO_FAULT NO_FAULT USE_THR_1
        StThrRec = 0;
        StThrActive = USE_THR_1;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_6 && !tmpCond_9)
    {
        // NO_FAULT NO_FAULT NO_FAULT USE_THR_2
        StThrRec = 1;
        StThrActive = USE_THR_2;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_7 && tmpCond_10)
    {
        // NO_FAULT NO_FAULT FAULT_FILTERING (AngThrottle1 >= AngThrottle2)
        StThrRec = 2;
        StThrActive = USE_THR_1;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_7 && !tmpCond_10)
    {
        // NO_FAULT NO_FAULT FAULT_FILTERING !(AngThrottle1 >= AngThrottle2)
        StThrRec = 3;
        StThrActive = USE_THR_2;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_8 && tmpCond_11 & !tmpCond_12)
    {
        // NO_FAULT NO_FAULT FAULT FlgPresCoh1 !FlgPresCoh2
        StThrRec = 4;
        StThrActive = USE_THR_1;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_8 && !tmpCond_11 & tmpCond_12)
    {
        // NO_FAULT NO_FAULT FAULT !FlgPresCoh1 FlgPresCoh2
        StThrRec = 5;
        StThrActive = USE_THR_2;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_8 && tmpCond_9 && tmpCond_11 & tmpCond_12)
    {
        // NO_FAULT NO_FAULT FAULT USE_THR_1 FlgPresCoh1 FlgPresCoh2
        StThrRec = 6;
        StThrActive = USE_THR_1;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_8 && !tmpCond_9 && tmpCond_11 & tmpCond_12)
    {
        // NO_FAULT NO_FAULT FAULT USE_THR_2 FlgPresCoh1 FlgPresCoh2
        StThrRec = 7;
        StThrActive = USE_THR_2;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_8 && tmpCond_9 && !tmpCond_11 & !tmpCond_12)
    {
        // NO_FAULT NO_FAULT FAULT USE_THR_1 !FlgPresCoh1 !FlgPresCoh2
        StThrRec = 8;
        StThrActive = USE_THR_1;
        RecThrReq = DBWOFF_THRREC;
    }
    else if(tmpCond_0 && tmpCond_3 && tmpCond_8 && !tmpCond_9 && !tmpCond_11 & !tmpCond_12)
    {
        // NO_FAULT NO_FAULT FAULT USE_THR_2 !FlgPresCoh1 !FlgPresCoh2
        StThrRec = 9;
        StThrActive = USE_THR_2;
        RecThrReq = DBWOFF_THRREC;
    }
    else if(tmpCond_0 && tmpCond_4)
    {
        // NO_FAULT FAULT_FILTERING
        StThrRec = 10;
        StThrActive = USE_THR_1;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_0 && tmpCond_5 && tmpCond_11)
    {
        // NO_FAULT FAULT FlgPresCoh1
        StThrRec = 11;
        StThrActive = USE_THR_1;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_0 && tmpCond_5 && !tmpCond_11)
    {
        // NO_FAULT FAULT !FlgPresCoh1
        StThrRec = 12;
        StThrActive = USE_THR_1;
        RecThrReq = DBWOFF_THRREC;
    }
    else if(tmpCond_1 && tmpCond_3)
    {
        // FAULT_FILTERING NO_FAULT 
        StThrRec = 13;
        StThrActive = USE_THR_2;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_2 && tmpCond_3 && tmpCond_12)
    {
        // FAULT NO_FAULT FlgPresCoh2
        StThrRec = 14;
        StThrActive = USE_THR_2;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_2 &&  tmpCond_3 && !tmpCond_12)
    {
        // FAULT NO_FAULT !FlgPresCoh2
        StThrRec = 15;
        StThrActive = USE_THR_2;
        RecThrReq = DBWOFF_THRREC;
    }   
    else if(tmpCond_1 && tmpCond_4 && tmpCond_9)
    {
        // FAULT_FILTERING FAULT_FILTERING USE_THR_1 
        StThrRec = 16;
        StThrActive = USE_THR1_OLD;
        RecThrReq = NO_THRREC;
    }
    else if(tmpCond_1 && tmpCond_4 && !tmpCond_9)
    {
        // FAULT_FILTERING FAULT_FILTERING USE_THR_2 
        StThrRec = 17;
        StThrActive = USE_THR2_OLD;
        RecThrReq = NO_THRREC;
  }
    else if(tmpCond_1 && tmpCond_5)
    {
        // FAULT_FILTERING FAULT 
        StThrRec = 18;
        StThrActive = USE_THR1_OLD;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_2 && tmpCond_4)
    {  
        // FAULT FAULT_FILTERING 
        StThrRec = 19;
        StThrActive = USE_THR2_OLD;
        RecThrReq = TRQ_THRREC;
    }
    else if(tmpCond_2 && tmpCond_5 && tmpCond_9)
    {
        // FAULT FAULT USE_THR_1
        StThrRec = 20;
        StThrActive = USE_THR_1;
        RecThrReq = DBWOFF_THRREC;
    }
    else
    if(tmpCond_2 && tmpCond_5 && !tmpCond_9)
    {  
        // FAULT FAULT USE_THR_2
        StThrRec = 21;
        StThrActive = USE_THR_2;
        RecThrReq = DBWOFF_THRREC;
    }
    else /* MISRA 14.10 */
    {
    }
    if ( RecThrReq != NO_THRREC )
    {
        recthrreq_freeze = RecThrReq;
    }
}

#endif // _BUILD_THRPOSMGM_
