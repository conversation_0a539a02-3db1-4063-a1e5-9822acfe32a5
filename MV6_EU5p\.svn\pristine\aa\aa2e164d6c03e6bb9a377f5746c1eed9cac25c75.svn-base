/*
 * File: AFCtrl_data.c
 *
 * Code generated for Simulink model 'AFCtrl'.
 *
 * Model version                  : 1.850
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Oct 16 10:25:53 2020
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "AFCtrl.h"
#include "AFCtrl_private.h"

/* Invariant block signals (default storage) */
const ConstBlockIO_AFCtrl AFCtrl_ConstB = {
  0U,                                  /* '<S30>/Gateway Out32' */
  0                                    /* '<S134>/Data Type Conversion' */
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
