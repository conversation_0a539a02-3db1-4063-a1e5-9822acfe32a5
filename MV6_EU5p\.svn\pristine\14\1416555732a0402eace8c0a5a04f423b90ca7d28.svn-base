/*
 * File: FOInjCtfMgm_types.h
 *
 * Code generated for Simulink model 'FOInjCtfMgm'.
 *
 * Model version                  : 1.360
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : <PERSON><PERSON> Jan  5 15:31:29 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (30), Warnings (2), Error (0)
 */

#ifndef RTW_HEADER_FOInjCtfMgm_types_h_
#define RTW_HEADER_FOInjCtfMgm_types_h_
#endif                                 /* RTW_HEADER_FOInjCtfMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
