/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Device_Drivers/DBWMGM/main_trunk/DbwMgm_ert_rtw/DbwMgm.c $ */
/* $Description:  $ */
/* $Revision: 5140 $ */
/* $Date: 2012-10-29 12:10:55 +0100 (lun, 29 ott 2012) $ */
/* $Author: lanal $ */
/*****************************************************************************************************************/
/*
 * File: DbwMgm.c
 *
 * Real-Time Workshop code generated for Simulink model DbwMgm.
 *
 * Model version                        : 1.705
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Wed Oct 24 13:36:13 2012
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Wed Oct 24 13:36:15 2012
 */
#include "DbwMgm.h"
#include "DbwMgm_private.h"

/* user code (top of source file) */
/* System '<Root>/DbwMgm' */
#ifdef _BUILD_DBWMGM_

/* Exported block signals */
uint32_T DbwMgmTimer;                  /* '<Root>/DbwMgmTimer' */

/* Block signals (auto storage) */
BlockIO_DbwMgm DbwMgm_B;

/* Block states (auto storage) */
D_Work_DbwMgm DbwMgm_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_DbwMgm DbwMgm_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_DbwMgm DbwMgm_U;
int16_T AbsAngThrErr;                  /* Absolute throttle angle error integrated for diagnosis */
int16_T AngIntErr;                     /* Integral term of throttle angle error */
int16_T AngThr;                        /* Measured throttle angle referred to limp home */
int16_T AngThrCorrObjF;                /* Throttle angle target filtered */
int32_T AngThrCorrObjFHiR;             /* Throttle angle target filtered - High resolution variable */
int16_T AngThrDiag;                    /* Throttle angle output for diagnosis */
int16_T AngThrDiagErr;                 /* Throttle angle Error for diagnosis */
int16_T AngThrErr;                     /* Throttle angle error at current time step */
int16_T AngThrErr1;                    /* Throttle angle error at time step -1 */
int16_T AngThrErr2;                    /* Throttle angle error at time step -2 */
int16_T AngThrErr3;                    /* Throttle angle error at time step -3 */
uint8_T AngThrFilReset;                /* AngThrObj filter reset flag */
int16_T AngThrObj;                     /* Throttle angle target referred to limp home */
int16_T AngThrObjNoFilt;               /* Throttle angle target referred to limp home before filter and rate limiter */
int16_T AngThrottleTgt;                /* Absolute throttle angle target */
uint8_T FlgAngThrObjSat;               /* Target throttle angle saturation flag */
uint8_T FlgDisableDbw;                 /* Disable Dbw due to Rpm flag */
uint8_T FlgEnHBridge;                  /* Enable H-Bridge flag */
uint8_T FlgRpmDisable;                 /* Disable Dbw controller flag */
uint16_T GainKPDBW;                    /* PID regulator proportional gain factor */
int16_T KDyna;                         /* DBW dynamic constant used for diagnosis */
int16_T MaxAngSlope;                   /* Max. throttle angle target slope */
int16_T MinAngErr;                     /* Dead Band on throttle angle error */
int16_T MinAngSlope;                   /* Min. throttle angle target slope */
uint8_T StDbwCtrl;                     /* Dbw controller status */
int16_T VDbwFeedFwd;                   /* Dbw output feedforward voltage */
int16_T VDbwOut;                       /* Dbw output command voltage */
int16_T VDbwPID;                       /* DBW PID regulator output voltage at current time step */
int16_T VDbwPID1;                      /* Dbw PID regulator output voltage at time step -1 */
int32_T VDbwPIDNoSat;                  /* Current DBW PID regulator output voltage not satured */

/* Output and update for function-call system: '<S1>/Init' */
void DbwMgm_Init(void)
{
  /* Switch: '<S15>/Switch' incorporates:
   *  Constant: '<S15>/DBW_SELF_LEARNING'
   *  Constant: '<S15>/DBW_TEST_DIS_LOAD'
   *  DataStoreWrite: '<S15>/Data Store Write8'
   *  Inport: '<Root>/S3FlgTestDisLoads'
   */
  if (S3FlgTestDisLoads != 0) {
    StDbwCtrl = DBW_TEST_DIS_LOAD;
  } else {
    StDbwCtrl = DBW_SELF_LEARNING;
  }

  /* DataStoreWrite: '<S15>/Data Store Write' incorporates:
   *  Constant: '<S15>/Constant'
   */
  VDbwOut = 0;

  /* DataStoreWrite: '<S15>/Data Store Write1' incorporates:
   *  Constant: '<S15>/Constant1'
   */
  AngThrObj = 0;

  /* DataStoreWrite: '<S15>/Data Store Write11' incorporates:
   *  Constant: '<S15>/Constant11'
   */
  FlgEnHBridge = 0U;

  /* DataStoreWrite: '<S15>/Data Store Write12' incorporates:
   *  Constant: '<S15>/Constant12'
   */
  AngIntErr = 0;

  /* DataStoreWrite: '<S15>/Data Store Write13' incorporates:
   *  Constant: '<S15>/Constant13'
   */
  AngThrFilReset = 1U;

  /* DataStoreWrite: '<S15>/Data Store Write14' incorporates:
   *  Constant: '<S15>/Constant14'
   */
  AngThrErr2 = 0;

  /* DataStoreWrite: '<S15>/Data Store Write3' incorporates:
   *  Constant: '<S15>/Constant3'
   */
  AngThr = 0;

  /* DataStoreWrite: '<S15>/Data Store Write4' incorporates:
   *  Constant: '<S15>/Constant4'
   */
  AngThrErr1 = 0;

  /* DataStoreWrite: '<S15>/Data Store Write5' incorporates:
   *  Constant: '<S15>/Constant5'
   */
  VDbwFeedFwd = 0;

  /* DataStoreWrite: '<S15>/Data Store Write6' incorporates:
   *  Constant: '<S15>/Constant6'
   */
  VDbwPID1 = 0;
}

/* Output and update for function-call system: '<S16>/ForceLH' */
void DbwMgm_ForceLH(void)
{
  {
    int16_T rtmin;
    int16_T rtmax;

    /* Switch: '<S23>/Switch' incorporates:
     *  Constant: '<S23>/Constant'
     *  DataStoreRead: '<S23>/Data Store Read'
     *  RelationalOperator: '<S23>/Relational Operator'
     */
    if (VDbwOut <= 0) {
      /* MinMax: '<S23>/MinMax' incorporates:
       *  Constant: '<S23>/Constant1'
       *  Constant: '<S23>/VDBWOUTINC'
       *  DataStoreRead: '<S23>/Data Store Read'
       *  Sum: '<S23>/Add'
       */
      rtmax = 0;
      rtmin = (int16_T)(VDBWOUTINC + VDbwOut);
      if (rtmin < 0) {
        rtmax = rtmin;
      }

      VDbwOut = rtmax;
    } else {
      /* MinMax: '<S23>/MinMax1' incorporates:
       *  Constant: '<S23>/Constant2'
       *  Constant: '<S23>/VDBWOUTDEC'
       *  DataStoreRead: '<S23>/Data Store Read'
       *  Sum: '<S23>/Add1'
       */
      rtmax = (int16_T)(VDbwOut - VDBWOUTDEC);
      if (0 > rtmax) {
        rtmax = 0;
      }

      VDbwOut = rtmax;
    }
  }
}

/* Output and update for function-call system: '<S16>/ActiveDiag' */
void DbwMgm_ActiveDiag(void)
{
  /* DataStoreWrite: '<S19>/Data Store Write10' incorporates:
   *  Inport: '<Root>/VDbwActiveDiag'
   */
  VDbwOut = VDbwActiveDiag;

  /* Sum: '<S19>/Add1' incorporates:
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/AngThrottle0'
   */
  AngThrObj = (int16_T)(AngThrottle - AngThrottle0);

  /* DataStoreWrite: '<S19>/Data Store Write3' */
  AngThr = AngThrObj;

  /* DataStoreWrite: '<S19>/Data Store Write12' incorporates:
   *  Constant: '<S19>/Constant12'
   */
  AngIntErr = 0;

  /* DataStoreWrite: '<S19>/Data Store Write6' incorporates:
   *  Constant: '<S19>/Constant6'
   */
  VDbwPID1 = 0;
}

/* Output and update for function-call system: '<S16>/SelfLearning' */
void DbwMgm_SelfLearning(void)
{
  /* DataStoreWrite: '<S25>/Data Store Write10' incorporates:
   *  Inport: '<Root>/VDbwIn'
   */
  VDbwOut = VDbwIn;

  /* Sum: '<S25>/Add1' incorporates:
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/AngThrottle0'
   */
  AngThrObj = (int16_T)(AngThrottle - AngThrottle0);

  /* DataStoreWrite: '<S25>/Data Store Write3' */
  AngThr = AngThrObj;

  /* DataStoreWrite: '<S25>/Data Store Write12' incorporates:
   *  Constant: '<S25>/Constant12'
   */
  AngIntErr = 0;

  /* DataStoreWrite: '<S25>/Data Store Write6' incorporates:
   *  Constant: '<S25>/Constant6'
   */
  VDbwPID1 = 0;
}

/* Output and update for function-call system: '<S16>/Disabled' */
void DbwMgm_Disabled(void)
{
  /* Sum: '<S22>/Add1' incorporates:
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/AngThrottle0'
   */
  AngThr = (int16_T)(AngThrottle - AngThrottle0);

  /* Switch: '<S22>/Switch' incorporates:
   *  Constant: '<S22>/Constant1'
   *  Constant: '<S22>/DBWDISDIAGCALC'
   */
  if (DBWDISDIAGCALC != 0) {
    AngThrObj = 0;
  } else {
    AngThrObj = AngThr;
  }

  /* Sum: '<S22>/Add2' */
  AngThrErr1 = (int16_T)(AngThrObj - AngThr);

  /* DataStoreWrite: '<S22>/Data Store Write7' */
  AngThrErr2 = AngThrErr1;

  /* DataStoreWrite: '<S22>/Data Store Write10' incorporates:
   *  Constant: '<S22>/Constant'
   */
  VDbwOut = 0;

  /* DataStoreWrite: '<S22>/Data Store Write12' incorporates:
   *  Constant: '<S22>/Constant12'
   */
  AngIntErr = 0;

  /* DataStoreWrite: '<S22>/Data Store Write13' incorporates:
   *  Constant: '<S22>/Constant2'
   */
  AngThrFilReset = 1U;

  /* DataStoreWrite: '<S22>/Data Store Write5' incorporates:
   *  Constant: '<S22>/Constant4'
   */
  VDbwFeedFwd = 0;

  /* DataStoreWrite: '<S22>/Data Store Write6' incorporates:
   *  Constant: '<S22>/Constant6'
   */
  VDbwPID1 = 0;
}

/* Output and update for function-call system: '<S16>/TestDisLoads' */
void DbwMgm_TestDisLoads(void)
{
  /* DataStoreWrite: '<S27>/Data Store Write10' incorporates:
   *  Inport: '<Root>/S3VDbwDisLoads'
   */
  VDbwOut = S3VDbwDisLoads;

  /* Sum: '<S27>/Add1' incorporates:
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/AngThrottle0'
   */
  AngThrObj = (int16_T)(AngThrottle - AngThrottle0);

  /* DataStoreWrite: '<S27>/Data Store Write3' */
  AngThr = AngThrObj;

  /* DataStoreWrite: '<S27>/Data Store Write12' incorporates:
   *  Constant: '<S27>/Constant12'
   */
  AngIntErr = 0;

  /* DataStoreWrite: '<S27>/Data Store Write6' incorporates:
   *  Constant: '<S27>/Constant6'
   */
  VDbwPID1 = 0;
}

/* Output and update for atomic system: '<S20>/ErrorPositionDiagnosis' */
void DbwMgm_ErrorPositionDiagnosis(void)
{
  /* local block i/o variables */
  uint8_T rtb_StDiagDbwMgm;

  {
    int16_T rtb_AngIntErr;
    int16_T rtb_MinMax_oy0k;

    /* Abs: '<S28>/Abs' */
    if (AngThrDiagErr < 0) {
      rtb_AngIntErr = (int16_T)(uint32_T)(-AngThrDiagErr);
    } else {
      rtb_AngIntErr = AngThrDiagErr;
    }

    /* Abs: '<S28>/Abs1' */
    if (AngThrErr < 0) {
      rtb_MinMax_oy0k = (int16_T)(uint32_T)(-AngThrErr);
    } else {
      rtb_MinMax_oy0k = AngThrErr;
    }

    /* MinMax: '<S28>/MinMax' */
    if (rtb_MinMax_oy0k < rtb_AngIntErr) {
      rtb_AngIntErr = rtb_MinMax_oy0k;
    }

    AbsAngThrErr = rtb_AngIntErr;

    /* MinMax: '<S32>/MinMax' incorporates:
     *  Constant: '<S32>/MAXANGTHRERR'
     *  Constant: '<S32>/min_integral_error'
     *  DataStoreRead: '<S32>/Data Store Read1'
     *  Sum: '<S32>/Add'
     *  Sum: '<S32>/Add1'
     */
    rtb_AngIntErr = (int16_T)((int16_T)(AbsAngThrErr - MAXANGTHRERR) + AngIntErr);
    if (0 > rtb_AngIntErr) {
      rtb_AngIntErr = 0;
    }

    /* MinMax: '<S32>/MinMax1' incorporates:
     *  Constant: '<S32>/MAXANGINTERR'
     */
    rtb_MinMax_oy0k = MAXANGINTERR;
    if (rtb_AngIntErr < MAXANGINTERR) {
      rtb_MinMax_oy0k = rtb_AngIntErr;
    }

    /* DataStoreWrite: '<S32>/Data Store Write' */
    AngIntErr = rtb_MinMax_oy0k;

    /* Switch: '<S33>/Switch' incorporates:
     *  Constant: '<S33>/MAXANGINTERR'
     *  Constant: '<S33>/NO_PT_FAULT'
     *  Constant: '<S33>/SIG_NOT_PLAUSIBLE'
     *  RelationalOperator: '<S33>/Relational Operator'
     */
    if (rtb_MinMax_oy0k >= MAXANGINTERR) {
      rtb_StDiagDbwMgm = SIG_NOT_PLAUSIBLE;
    } else {
      rtb_StDiagDbwMgm = NO_PT_FAULT;
    }

    /* S-Function (DiagMgm_SetDiagState): '<S34>/DiagMgm_SetDiagState1' incorporates:
     *  Constant: '<S33>/DIAG_DWB_CONTROL'
     */
    DiagMgm_SetDiagState( DIAG_DBW_CONTROL, rtb_StDiagDbwMgm, &rtb_StDiagDbwMgm);
  }
}

/* Output and update for atomic system: '<S35>/minangerr_select' */
void DbwMgm_minangerr_select(int16_T rtu_In2, int16_T rtu_In3, int16_T rtu_In1,
  int16_T rtu_In4, rtB_minangerr_select_DbwMgm *localB)
{
  {
    int16_T rtb_Add2;

    /* Sum: '<S40>/Add2' */
    rtb_Add2 = (int16_T)(rtu_In2 - rtu_In3);

    /* Switch: '<S40>/Switch' incorporates:
     *  Constant: '<S43>/MINANGTHRERR'
     *  Gain: '<S40>/Gain1'
     *  Logic: '<S40>/Logical Operator'
     *  Product: '<S43>/Divide'
     *  Product: '<S43>/Product'
     *  RelationalOperator: '<S40>/Relational Operator'
     *  RelationalOperator: '<S40>/Relational Operator1'
     *  Sum: '<S43>/Add3'
     */
    if ((rtu_In1 > rtb_Add2) && (rtb_Add2 > (int16_T)(-rtu_In1))) {
      /* Abs: '<S43>/Abs' */
      if (rtb_Add2 < 0) {
        rtb_Add2 = (int16_T)(-rtb_Add2);
      }

      localB->Switch = (int16_T)((int16_T)(rtu_In1 - rtb_Add2) * MINANGTHRERR /
        rtu_In1);
    } else {
      localB->Switch = rtu_In4;
    }
  }
}

/* Output and update for atomic system:
 *   '<S35>/minangerr_select1'
 *   '<S35>/minangerr_select2'
 */
void DbwMgm_minangerr_select1(int16_T rtu_In2, int16_T rtu_In3, int16_T rtu_In1,
  int16_T rtu_In4, rtB_minangerr_select1_DbwMgm *localB)
{
  {
    int16_T rtb_Add2_iva4;

    /* Sum: '<S41>/Add2' */
    rtb_Add2_iva4 = (int16_T)(rtu_In2 - rtu_In3);

    /* Switch: '<S41>/Switch' incorporates:
     *  Constant: '<S44>/MINANGTHRERR'
     *  Gain: '<S41>/Gain1'
     *  Logic: '<S41>/Logical Operator'
     *  Product: '<S44>/Divide'
     *  Product: '<S44>/Product'
     *  RelationalOperator: '<S41>/Relational Operator'
     *  RelationalOperator: '<S41>/Relational Operator1'
     *  Sum: '<S44>/Add3'
     */
    if ((rtu_In1 > rtb_Add2_iva4) && (rtb_Add2_iva4 > (int16_T)(-rtu_In1))) {
      /* Abs: '<S44>/Abs' */
      if (rtb_Add2_iva4 < 0) {
        rtb_Add2_iva4 = (int16_T)(-rtb_Add2_iva4);
      }

      localB->Switch = (int16_T)((int16_T)(rtu_In1 - rtb_Add2_iva4) *
        MINANGTHRERR / rtu_In1);
    } else {
      localB->Switch = rtu_In4;
    }
  }
}

/* Output and update for atomic system: '<S20>/PID_Regulator' */
void DbwMgm_PID_Regulator(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_S16_S16;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  int16_T rtb_SignalConversion1;
  int16_T rtb_SignalConversion2;
  int16_T rtb_SignalConversion3;
  uint16_T rtb_LookUp_IR_U16;

  {
    uint16_T rtmin;
    int16_T rtb_DataTypeConversion3;
    int16_T rtb_Add1_bpcg;
    int16_T rtb_Gain2;
    int32_T tmp;

    /* S-Function (LookUp_S16_S16): '<S37>/LookUp_S16_S16' incorporates:
     *  Constant: '<S29>/VTDBWFEEDFWD'
     *  Constant: '<S29>/BKANGTHROBJ'
     *  Constant: '<S29>/BKANGTHROBJ_dim'
     */
    LookUp_S16_S16( &rtb_LookUp_S16_S16, &VTDBWFEEDFWD[0],
                   DbwMgm_B.AngThrObj_gm0w, &BKANGTHROBJ[0], BKANGTHROBJ_dim);

    /* DataStoreWrite: '<S29>/Data Store Write1' */
    VDbwFeedFwd = rtb_LookUp_S16_S16;

    /* MinMax: '<S29>/MinMax' incorporates:
     *  Constant: '<S29>/MAXVDBWOUT'
     *  Inport: '<Root>/VBattery'
     */
    rtmin = MAXVDBWOUT;
    if (VBattery < MAXVDBWOUT) {
      rtmin = VBattery;
    }

    rtb_LookUp_IR_U16 = rtmin;

    /* DataTypeConversion: '<S29>/Data Type Conversion3' */
    rtb_DataTypeConversion3 = (int16_T)((int16_T)rtb_LookUp_IR_U16 << 6U);

    /* Sum: '<S35>/Add1' */
    rtb_Add1_bpcg = (int16_T)(DbwMgm_B.AngThrObj_gm0w - DbwMgm_B.AngThr_dq42);

    /* SignalConversion: '<S35>/Signal Conversion1' */
    rtb_SignalConversion1 = AngThrObjNoFilt;

    /* SignalConversion: '<S35>/Signal Conversion2' */
    rtb_SignalConversion2 = DbwMgm_B.AngThrObj_gm0w;

    /* SignalConversion: '<S35>/Signal Conversion3' */
    rtb_SignalConversion3 = DbwMgm_B.AngThrObj_gm0w;

    /* Outputs for atomic SubSystem: '<S35>/minangerr_select2' */
    DbwMgm_minangerr_select1(rtb_SignalConversion3, ANGTHRMINDB, MAXANGMINDB, 0,
      &DbwMgm_B.minangerr_select2);

    /* end of Outputs for SubSystem: '<S35>/minangerr_select2' */

    /* Outputs for atomic SubSystem: '<S35>/minangerr_select1' */
    DbwMgm_minangerr_select1(rtb_SignalConversion2, ANGTHRMAXDB, MAXANGMAXDB,
      DbwMgm_B.minangerr_select2.Switch, &DbwMgm_B.minangerr_select1);

    /* end of Outputs for SubSystem: '<S35>/minangerr_select1' */

    /* Outputs for atomic SubSystem: '<S35>/minangerr_select' */
    DbwMgm_minangerr_select(rtb_SignalConversion1, ANGTHRLHDB, MAXANGLHDB,
      DbwMgm_B.minangerr_select1.Switch, &DbwMgm_B.minangerr_select);

    /* end of Outputs for SubSystem: '<S35>/minangerr_select' */

    /* SignalConversion: '<S35>/Signal Conversion' */
    MinAngErr = DbwMgm_B.minangerr_select.Switch;

    /* Switch: '<S39>/Switch' incorporates:
     *  RelationalOperator: '<S39>/u_GTE_up'
     */
    if (rtb_Add1_bpcg >= MinAngErr) {
      rtb_Gain2 = MinAngErr;
    } else {
      /* Gain: '<S35>/Gain2' */
      rtb_Gain2 = (int16_T)(-MinAngErr);

      /* Switch: '<S39>/Switch1' incorporates:
       *  RelationalOperator: '<S39>/u_GT_lo'
       */
      if (rtb_Add1_bpcg > rtb_Gain2) {
        rtb_Gain2 = rtb_Add1_bpcg;
      }
    }

    /* Sum: '<S39>/Diff' */
    AngThrErr = (int16_T)(rtb_Add1_bpcg - rtb_Gain2);

    /* S-Function (PreLookUpIdSearch_S16): '<S47>/PreLookUpIdSearch_S16' incorporates:
     *  Constant: '<S29>/BKANGTHRERR'
     *  Constant: '<S36>/BKANGTHRERR_dim'
     */
    PreLookUpIdSearch_S16( &rtb_LookUp_IR_U16, &rtb_PreLookUpIdSearch_S16_o2,
                          AngThrErr, &BKANGTHRERR[0], BKANGTHRERR_dim);

    /* S-Function (LookUp_IR_U16): '<S46>/LookUp_IR_U16' incorporates:
     *  Constant: '<S29>/VTGAINKPDBW'
     *  Constant: '<S36>/BKANGTHRERR_dim'
     */
    LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTGAINKPDBW[0], rtb_LookUp_IR_U16,
                  rtb_PreLookUpIdSearch_S16_o2, BKANGTHRERR_dim);

    /* DataTypeConversion: '<S48>/Conversion' */
    GainKPDBW = rtb_LookUp_IR_U16;

    /* Sum: '<S29>/Sum5' incorporates:
     *  Constant: '<S29>/GAINKDDBW'
     *  Constant: '<S29>/GAINKIDBW'
     *  DataStoreRead: '<S29>/Data Store Read'
     *  DataStoreRead: '<S29>/Data Store Read1'
     *  DataStoreRead: '<S29>/Data Store Read2'
     *  Gain: '<S29>/Gain2'
     *  Product: '<S29>/Product'
     *  Product: '<S29>/Product1'
     *  Product: '<S29>/Product2'
     *  Sum: '<S29>/Sum2'
     *  Sum: '<S29>/Sum4'
     *  Sum: '<S29>/Sum6'
     *  Sum: '<S29>/Sum7'
     */
    VDbwPIDNoSat = ((((((AngThrErr * ((GAINKIDBW + GainKPDBW) + GAINKDDBW) >> 1)
                        - (AngThrErr1 * (GainKPDBW + (uint16_T)(GAINKDDBW << 1U))
      >> 1)) >> 1) + (AngThrErr2 * GAINKDDBW >> 2)) >> 1) + (VDbwPID1 << 3)) >>
      3;

    /* DataTypeConversion: '<S29>/Data Type Conversion1' incorporates:
     *  Sum: '<S29>/Sum1'
     */
    tmp = rtb_LookUp_S16_S16 + VDbwPIDNoSat;
    if (tmp > 32767) {
      rtb_Add1_bpcg = MAX_int16_T;
    } else if (tmp <= -32768) {
      rtb_Add1_bpcg = MIN_int16_T;
    } else {
      rtb_Add1_bpcg = (int16_T)tmp;
    }

    VDbwOut = rtb_Add1_bpcg;

    /* Switch: '<S38>/Switch2' incorporates:
     *  RelationalOperator: '<S38>/LowerRelop1'
     */
    if (VDbwOut > rtb_DataTypeConversion3) {
      VDbwOut = rtb_DataTypeConversion3;
    } else {
      /* Gain: '<S29>/Gain' */
      rtb_Gain2 = (int16_T)(-rtb_DataTypeConversion3);

      /* Switch: '<S38>/Switch' incorporates:
       *  RelationalOperator: '<S38>/UpperRelop'
       */
      if (VDbwOut < rtb_Gain2) {
        VDbwOut = rtb_Gain2;
      }
    }

    /* Sum: '<S29>/Sum3' */
    VDbwPID = (int16_T)(VDbwOut - rtb_LookUp_S16_S16);

    /* DataStoreWrite: '<S29>/Data Store Write2' */
    VDbwPID1 = VDbwPID;

    /* DataStoreWrite: '<S29>/Data Store Write4' incorporates:
     *  DataStoreRead: '<S29>/Data Store Read2'
     */
    AngThrErr3 = AngThrErr2;

    /* DataStoreWrite: '<S29>/Data Store Write5' incorporates:
     *  DataStoreRead: '<S29>/Data Store Read1'
     */
    AngThrErr2 = AngThrErr1;

    /* DataStoreWrite: '<S35>/Data Store Write4' */
    AngThrErr1 = AngThrErr;
  }
}

/* Output and update for atomic system: '<S20>/SysDynamics' */
void DbwMgm_SysDynamics(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int16_T rtb_LookUp_IR_S16;
  int16_T rtb_LookUp_IR_S16_gwnm;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_Conversion1;
  int16_T rtb_Switch_cm55;
  int16_T rtb_UnitDelay;
  uint8_T rtb_DataStoreRead_lquj;

  /* S-Function (sfix_udelay): '<S30>/Integer Delay'
   *
   * Regarding '<S30>/Integer Delay':
   * Integer/Tapped Delay Block: '<S30>/Integer Delay'
   */
  rtb_UnitDelay = DbwMgm_DWork.IntegerDelay_DWORK1[0];

  /* S-Function (PreLookUpIdSearch_U16): '<S53>/PreLookUpIdSearch_U16' incorporates:
   *   Inport: '<Root>/VBattery'
   *  Constant: '<S30>/BKVBATANGSLOPE'
   *  Constant: '<S30>/BKVBATANGSLOPE_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, VBattery,
                        &BKVBATANGSLOPE[0], BKVBATANGSLOPE_dim);

  /* Switch: '<S30>/Switch1' incorporates:
   *  Sum: '<S30>/Add1'
   */
  if ((int16_T)(DbwMgm_B.AngThrObj_gm0w - DbwMgm_B.AngThr_dq42) >= 0) {
    /* S-Function (LookUp_IR_S16): '<S52>/LookUp_IR_S16' incorporates:
     *  Constant: '<S30>/VTKDBWDYNAPOS'
     *  Constant: '<S30>/BKVBATANGSLOPE_dim'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTKDBWDYNAPOS[0],
                  rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                  BKVBATANGSLOPE_dim);
    KDyna = rtb_LookUp_IR_S16;
  } else {
    /* S-Function (LookUp_IR_S16): '<S51>/LookUp_IR_S16' incorporates:
     *  Constant: '<S30>/VTKDBWDYNANEG'
     *  Constant: '<S30>/BKVBATANGSLOPE_dim'
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16_gwnm, &VTKDBWDYNANEG[0],
                  rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                  BKVBATANGSLOPE_dim);
    KDyna = rtb_LookUp_IR_S16_gwnm;
  }

  /* DataTypeConversion: '<S50>/Conversion1' */
  rtb_Conversion1 = (uint16_T)KDyna;

  /* DataStoreRead: '<S30>/Data Store Read' */
  rtb_DataStoreRead_lquj = AngThrFilReset;

  /* DataStoreRead: '<S30>/Data Store Read1' */
  rtb_FOF_Reset_S16_FXP_o2 = DbwMgm_DWork.AngThrDiagHiR;

  /* S-Function (FOF_Reset_S16_FXP): '<S50>/FOF_Reset_S16_FXP' */
  FOF_Reset_S16_FXP( &rtb_UnitDelay, &rtb_FOF_Reset_S16_FXP_o2, rtb_UnitDelay,
                    rtb_Conversion1, DbwMgm_B.AngThrObj_gm0w,
                    rtb_DataStoreRead_lquj, rtb_FOF_Reset_S16_FXP_o2);

  /* DataStoreWrite: '<S30>/Data Store Write1' */
  DbwMgm_DWork.AngThrDiagHiR = rtb_FOF_Reset_S16_FXP_o2;

  /* DataTypeConversion: '<S54>/Conversion' */
  AngThrDiag = rtb_UnitDelay;

  /* Sum: '<S30>/Add' */
  AngThrDiagErr = (int16_T)(AngThrDiag - DbwMgm_B.AngThr_dq42);

  /* UnitDelay: '<S30>/Unit Delay' */
  rtb_UnitDelay = DbwMgm_DWork.UnitDelay_DSTATE;

  /* Switch: '<S30>/Switch' incorporates:
   *  Constant: '<S30>/DBWDELAY'
   */
  if (DBWDELAY > 0) {
    rtb_Switch_cm55 = rtb_UnitDelay;
  } else {
    rtb_Switch_cm55 = DbwMgm_B.AngThrObj_gm0w;
  }

  /* Integer/Tapped Delay Block: '<S30>/Integer Delay'
   */
  DbwMgm_DWork.IntegerDelay_DWORK1[0] = DbwMgm_DWork.IntegerDelay_DWORK1[1];
  DbwMgm_DWork.IntegerDelay_DWORK1[1] = rtb_Switch_cm55;

  /* Update for UnitDelay: '<S30>/Unit Delay' */
  DbwMgm_DWork.UnitDelay_DSTATE = DbwMgm_B.AngThrObj_gm0w;
}

/* Output and update for atomic system: '<S20>/Target_Calculation' */
void DbwMgm_Target_Calculation(void)
{
  /* local block i/o variables */
  int32_T rtb_Switch1_devv;
  int16_T rtb_LookUp_IR_S16_d0o5;
  int16_T rtb_LookUp_IR_S16_fjs4;
  int16_T rtb_LookUp_IR_S16_kwph;
  int16_T rtb_LookUp_IR_S16_nybq;
  int16_T rtb_DataStoreRead_iawr;
  uint16_T rtb_Conversion1_lbup;
  uint16_T rtb_PreLookUpIdSearch_U16__bnnq;
  uint16_T rtb_PreLookUpIdSearch_U16__dmob;
  uint8_T rtb_DataStoreRead_oo2x;

  {
    boolean_T rtb_LowerRelop1_cuf0;
    int16_T rtb_Add1_orrl;
    uint16_T rtb_Switch_bqnm;
    int16_T rtb_Switch_psre;

    /* Switch: '<S62>/Switch2' incorporates:
     *  Constant: '<S58>/MAXANGOBJ'
     *  Inport: '<Root>/AngThrCorrObj'
     *  RelationalOperator: '<S62>/LowerRelop1'
     */
    if (AngThrCorrObj > MAXANGOBJ) {
      rtb_Switch_bqnm = MAXANGOBJ;
    } else {
      /* Switch: '<S62>/Switch' incorporates:
       *  Constant: '<S58>/MINANGOBJ'
       *  Inport: '<Root>/AngThrCorrObj'
       *  RelationalOperator: '<S62>/UpperRelop'
       */
      if (AngThrCorrObj < MINANGOBJ) {
        rtb_Switch_bqnm = MINANGOBJ;
      } else {
        rtb_Switch_bqnm = AngThrCorrObj;
      }
    }

    /* Sum: '<S58>/Add3' incorporates:
     *  Inport: '<Root>/AngThrottle0'
     */
    AngThrObjNoFilt = (int16_T)(rtb_Switch_bqnm - AngThrottle0);

    /* DataTypeConversion: '<S60>/Conversion1' incorporates:
     *  Constant: '<S57>/KFILTANGTHROBJ'
     */
    rtb_Conversion1_lbup = (uint16_T)KFILTANGTHROBJ;

    /* Sum: '<S57>/Add1' incorporates:
     *  Inport: '<Root>/AngThrottle'
     *  Inport: '<Root>/AngThrottle0'
     */
    DbwMgm_B.AngThr_dq42 = (int16_T)(AngThrottle - AngThrottle0);

    /* DataStoreRead: '<S57>/Data Store Read' */
    rtb_DataStoreRead_oo2x = AngThrFilReset;

    /* DataStoreRead: '<S57>/Data Store Read1' */
    rtb_Switch1_devv = AngThrCorrObjFHiR;

    /* S-Function (FOF_Reset_S16_FXP): '<S60>/FOF_Reset_S16_FXP' */
    FOF_Reset_S16_FXP( &rtb_DataStoreRead_iawr, &rtb_Switch1_devv,
                      AngThrObjNoFilt, rtb_Conversion1_lbup,
                      DbwMgm_B.AngThr_dq42, rtb_DataStoreRead_oo2x,
                      rtb_Switch1_devv);

    /* DataTypeConversion: '<S61>/Conversion' */
    AngThrCorrObjF = rtb_DataStoreRead_iawr;

    /* DataStoreRead: '<S59>/Data Store Read' */
    rtb_DataStoreRead_iawr = AngThrObj;

    /* Sum: '<S59>/Add1' */
    rtb_Add1_orrl = (int16_T)(AngThrCorrObjF - rtb_DataStoreRead_iawr);

    /* S-Function (PreLookUpIdSearch_U16): '<S67>/PreLookUpIdSearch_U16' incorporates:
     *   Inport: '<Root>/VBattery'
     *  Constant: '<S59>/BKVBATANGSLOPE'
     *  Constant: '<S59>/BKVBATANGSLOPE_dim'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16__bnnq,
                          &rtb_PreLookUpIdSearch_U16__dmob, VBattery,
                          &BKVBATANGSLOPE[0], BKVBATANGSLOPE_dim);

    /* Sum: '<S59>/Add5' incorporates:
     *  Inport: '<Root>/AngThrottle0'
     */
    AngThrottleTgt = (int16_T)(AngThrottle0 + rtb_DataStoreRead_iawr);

    /* Switch: '<S59>/Switch2' incorporates:
     *  Constant: '<S59>/HITHRANGSLOW'
     *  RelationalOperator: '<S59>/Relational Operator2'
     */
    if (HITHRANGSLOW > AngThrottleTgt) {
      /* S-Function (LookUp_IR_S16): '<S64>/LookUp_IR_S16' incorporates:
       *  Constant: '<S59>/VTMAXANGSLOPEPOS'
       *  Constant: '<S59>/BKVBATANGSLOPE_dim'
       */
      LookUp_IR_S16( &rtb_LookUp_IR_S16_kwph, &VTMAXANGSLOPEPOS[0],
                    rtb_PreLookUpIdSearch_U16__bnnq,
                    rtb_PreLookUpIdSearch_U16__dmob, BKVBATANGSLOPE_dim);
      MaxAngSlope = rtb_LookUp_IR_S16_kwph;
    } else {
      /* S-Function (LookUp_IR_S16): '<S66>/LookUp_IR_S16' incorporates:
       *  Constant: '<S59>/VTMAXANGSLOPE'
       *  Constant: '<S59>/BKVBATANGSLOPE_dim'
       */
      LookUp_IR_S16( &rtb_LookUp_IR_S16_nybq, &VTMAXANGSLOPE[0],
                    rtb_PreLookUpIdSearch_U16__bnnq,
                    rtb_PreLookUpIdSearch_U16__dmob, BKVBATANGSLOPE_dim);
      MaxAngSlope = rtb_LookUp_IR_S16_nybq;
    }

    /* RelationalOperator: '<S68>/LowerRelop1' */
    rtb_LowerRelop1_cuf0 = (rtb_Add1_orrl > MaxAngSlope);

    /* Switch: '<S59>/Switch' incorporates:
     *  Constant: '<S59>/LOTHRANGSLOW'
     *  RelationalOperator: '<S59>/Relational Operator'
     */
    if (AngThrottleTgt > LOTHRANGSLOW) {
      /* S-Function (LookUp_IR_S16): '<S63>/LookUp_IR_S16' incorporates:
       *  Constant: '<S59>/VTMAXANGSLOPENEG'
       *  Constant: '<S59>/BKVBATANGSLOPE_dim'
       */
      LookUp_IR_S16( &rtb_LookUp_IR_S16_d0o5, &VTMAXANGSLOPENEG[0],
                    rtb_PreLookUpIdSearch_U16__bnnq,
                    rtb_PreLookUpIdSearch_U16__dmob, BKVBATANGSLOPE_dim);
      MinAngSlope = rtb_LookUp_IR_S16_d0o5;
    } else {
      /* S-Function (LookUp_IR_S16): '<S65>/LookUp_IR_S16' incorporates:
       *  Constant: '<S59>/VTMINANGSLOPE'
       *  Constant: '<S59>/BKVBATANGSLOPE_dim'
       */
      LookUp_IR_S16( &rtb_LookUp_IR_S16_fjs4, &VTMINANGSLOPE[0],
                    rtb_PreLookUpIdSearch_U16__bnnq,
                    rtb_PreLookUpIdSearch_U16__dmob, BKVBATANGSLOPE_dim);
      MinAngSlope = rtb_LookUp_IR_S16_fjs4;
    }

    /* Switch: '<S68>/Switch2' */
    if (rtb_LowerRelop1_cuf0) {
      rtb_Switch_psre = MaxAngSlope;
    } else {
      /* Switch: '<S68>/Switch' incorporates:
       *  RelationalOperator: '<S68>/UpperRelop'
       */
      if (rtb_Add1_orrl < MinAngSlope) {
        rtb_Switch_psre = MinAngSlope;
      } else {
        rtb_Switch_psre = rtb_Add1_orrl;
      }
    }

    /* Sum: '<S59>/Add2' */
    DbwMgm_B.AngThrObj_gm0w = (int16_T)(rtb_Switch_psre + rtb_DataStoreRead_iawr);

    /* Switch: '<S59>/Switch1' incorporates:
     *  DataTypeConversion: '<S59>/Data Type Conversion'
     *  RelationalOperator: '<S59>/Relational Operator1'
     */
    if (rtb_Add1_orrl == rtb_Switch_psre) {
    } else {
      rtb_Switch1_devv = DbwMgm_B.AngThrObj_gm0w << 14;
    }

    /* DataStoreWrite: '<S59>/Data Store Write12' */
    AngThrCorrObjFHiR = rtb_Switch1_devv;

    /* RelationalOperator: '<S58>/Relational Operator' incorporates:
     *  Inport: '<Root>/AngThrCorrObj'
     */
    FlgAngThrObjSat = (rtb_Switch_bqnm != AngThrCorrObj);
  }
}

/* Output and update for function-call system: '<S16>/ClosedLoop' */
void DbwMgm_ClosedLoop(void)
{
  /* Outputs for atomic SubSystem: '<S20>/Target_Calculation' */
  DbwMgm_Target_Calculation();

  /* end of Outputs for SubSystem: '<S20>/Target_Calculation' */

  /* DataStoreWrite: '<S20>/Data Store Write1' */
  AngThrObj = DbwMgm_B.AngThrObj_gm0w;

  /* DataStoreWrite: '<S20>/Data Store Write3' */
  AngThr = DbwMgm_B.AngThr_dq42;

  /* DataStoreWrite: '<S20>/Data Store Write13' incorporates:
   *  Constant: '<S20>/Constant2'
   */
  AngThrFilReset = 0U;

  /* DataStoreWrite: '<S20>/Data Store Write11' incorporates:
   *  Constant: '<S20>/Constant7'
   */
  FlgEnHBridge = 1U;

  /* Outputs for atomic SubSystem: '<S20>/SysDynamics' */
  DbwMgm_SysDynamics();

  /* end of Outputs for SubSystem: '<S20>/SysDynamics' */

  /* Outputs for atomic SubSystem: '<S20>/PID_Regulator' */
  DbwMgm_PID_Regulator();

  /* end of Outputs for SubSystem: '<S20>/PID_Regulator' */

  /* Outputs for atomic SubSystem: '<S20>/ErrorPositionDiagnosis' */
  DbwMgm_ErrorPositionDiagnosis();

  /* end of Outputs for SubSystem: '<S20>/ErrorPositionDiagnosis' */
}

/* Initial conditions for function-call system: '<S16>/TestCond' */
void DbwMgm_TestCond_Init(void)
{
  /* InitializeConditions for UnitDelay: '<S80>/FixPt Unit Delay2' */
  DbwMgm_DWork.FixPtUnitDelay2_DSTATE = 1U;
}

/* Output and update for function-call system: '<S16>/TestCond' */
void DbwMgm_TestCond(void)
{
  /* Switch: '<S78>/Switch' incorporates:
   *  Inport: '<Root>/DbwMgmTimer'
   *  Inport: '<Root>/Rpm'
   */
  if (Rpm != 0) {
    DbwMgm_DWork.FixPtUnitDelay1_DSTATE = DbwMgmTimer;
  } else {
    /* Switch: '<S80>/Init' incorporates:
     *  Inport: '<Root>/DbwMgmTimer'
     *  UnitDelay: '<S80>/FixPt Unit Delay1'
     *  UnitDelay: '<S80>/FixPt Unit Delay2'
     */
    if (DbwMgm_DWork.FixPtUnitDelay2_DSTATE != 0) {
      DbwMgm_DWork.FixPtUnitDelay1_DSTATE = DbwMgmTimer;
    }
  }

  /* Logic: '<S78>/Logical Operator4' incorporates:
   *  Constant: '<S78>/DBWTIMEOUT'
   *  Constant: '<S78>/MAX_UINT16'
   *  Inport: '<Root>/DbwMgmTimer'
   *  Inport: '<Root>/Rpm'
   *  RelationalOperator: '<S78>/Relational Operator'
   *  RelationalOperator: '<S78>/Relational Operator1'
   *  RelationalOperator: '<S79>/Compare'
   *  Sum: '<S78>/Add'
   */
  FlgRpmDisable = (((Rpm == ((uint16_T)0U)) != 0) && (DbwMgmTimer -
    DbwMgm_DWork.FixPtUnitDelay1_DSTATE > (uint32_T)DBWTIMEOUT) && (DBWTIMEOUT
    != 65535));

  /* Logic: '<S26>/Logical Operator3' incorporates:
   *  Constant: '<S26>/FORCEDBWVOUT'
   *  Constant: '<S26>/REC_DBW_OFF'
   *  Constant: '<S26>/REC_FORCE_LH'
   *  Constant: '<S26>/VDBWOUTDEC'
   *  Constant: '<S26>/VDBWOUTINC'
   *  Constant: '<S73>/Constant'
   *  DataStoreRead: '<S26>/Data Store Read'
   *  Gain: '<S26>/Gain'
   *  Inport: '<Root>/FlgEnaDbwTimeHist'
   *  Inport: '<Root>/FlgSelfLearning'
   *  Inport: '<Root>/KeySignal'
   *  Inport: '<Root>/S2FlgDisL2'
   *  Inport: '<Root>/S3FlgDisL2'
   *  Inport: '<Root>/S3FlgTestDisLoads'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S26>/Logical Operator'
   *  Logic: '<S26>/Logical Operator5'
   *  RelationalOperator: '<S26>/Relational Operator'
   *  RelationalOperator: '<S26>/Relational Operator1'
   *  RelationalOperator: '<S73>/Compare'
   *  RelationalOperator: '<S74>/Compare'
   *  RelationalOperator: '<S75>/Compare'
   *  RelationalOperator: '<S76>/Compare'
   *  RelationalOperator: '<S77>/Compare'
   *  Selector: '<S26>/Selector'
   *  Selector: '<S26>/Selector1'
   */
  FlgDisableDbw = ((KeySignal == ((uint8_T)0U)) || ((FlgSelfLearning ==
    ((uint8_T)0U)) && (S3FlgTestDisLoads == ((uint8_T)0U)) && (FLGFORCEDBWOUT ==
    0) && (FlgEnaDbwTimeHist == ((uint8_T)0U)) && (FlgRpmDisable != 0)) ||
                   (VtRec[(int32_T)REC_DBW_OFF] != 0) || ((VtRec[(int32_T)
    REC_FORCE_LH] != 0) && (VDbwOut <= VDBWOUTDEC) && (VDbwOut >= (int16_T)
    (-VDBWOUTINC))) || (S2FlgDisL2 != 0) || (S3FlgDisL2 != 0));

  /* Update for UnitDelay: '<S80>/FixPt Unit Delay2' incorporates:
   *  Constant: '<S80>/FixPt Constant'
   */
  DbwMgm_DWork.FixPtUnitDelay2_DSTATE = 0U;
}

/* Output and update for function-call system: '<S16>/HBLogicEnable' */
void DbwMgm_HBLogicEnable(void)
{
  /* Switch: '<S24>/Switch1' incorporates:
   *  Constant: '<S24>/Constant3'
   *  Constant: '<S24>/Constant7'
   *  DataStoreRead: '<S24>/Data Store Read'
   *  DataStoreWrite: '<S24>/Data Store Write11'
   */
  if (VDbwOut != 0) {
    FlgEnHBridge = 1U;
  } else {
    FlgEnHBridge = 0U;
  }
}

/* Initial conditions for function-call system: '<S1>/T5ms' */
void DbwMgm_T5ms_Init(void)
{
  /* InitializeConditions for Stateflow: '<S16>/DbwMgm' incorporates:
   *  InitializeConditions for SubSystem: '<S16>/ActiveDiag'
   *  InitializeConditions for SubSystem: '<S16>/ClosedLoop'
   *  InitializeConditions for SubSystem: '<S16>/Disabled'
   *  InitializeConditions for SubSystem: '<S16>/ForceLH'
   *  InitializeConditions for SubSystem: '<S16>/HBLogicEnable'
   *  InitializeConditions for SubSystem: '<S16>/SelfLearning'
   *  InitializeConditions for SubSystem: '<S16>/TestCond'
   *  InitializeConditions for SubSystem: '<S16>/TestDisLoads'
   */
  DbwMgm_TestCond_Init();
}

/* Output and update for function-call system: '<S1>/T5ms' */
void DbwMgm_T5ms(void)
{
  {
    boolean_T sf_guard;

    {
      /* user code (Output function Header) */
      /* System '<S1>/T5ms' */
      uint64_T ticksTimer;
      uint64_T msTimer;
      TIMING_GetAbsTimer(&(ticksTimer));
      TIMING_TicksToMilliSeconds(ticksTimer, &msTimer);
      DbwMgmTimer = (uint32_T)(msTimer & 0x00000000FFFFFFFF);

      /* Stateflow: '<S16>/DbwMgm' incorporates:
       *  Inport: '<Root>/FlgDbwActiveDiag'
       *  Inport: '<Root>/FlgSelfLearning'
       *  Inport: '<Root>/S3FlgAllowStart'
       *  Inport: '<Root>/S3FlgTestDisLoads'
       *  Inport: '<Root>/VtRec'
       *  SubSystem: '<S16>/ActiveDiag'
       *  SubSystem: '<S16>/ClosedLoop'
       *  SubSystem: '<S16>/Disabled'
       *  SubSystem: '<S16>/ForceLH'
       *  SubSystem: '<S16>/HBLogicEnable'
       *  SubSystem: '<S16>/SelfLearning'
       *  SubSystem: '<S16>/TestCond'
       *  SubSystem: '<S16>/TestDisLoads'
       */
      /* Gateway: DbwMgm/T5ms/DbwMgm */
      /* During: DbwMgm/T5ms/DbwMgm */
      /* Transition: '<S21>:52' */
      if ((S3FlgAllowStart != 0) || (S3DBWTESTMOV == 1)) {
        /* Transition: '<S21>:7' */
        /* Event: '<S21>:48' */
        DbwMgm_TestCond();
        if (FLGFORCEDBWOUT != 0) {
          /* Transition: '<S21>:1' */
          VDbwOut = FORCEDBWVOUT;
          FlgEnHBridge = 1U;
        } else {
          /* Transition: '<S21>:13' */
          sf_guard = false;
          if (FlgDbwActiveDiag != 0) {
            /* Transition: '<S21>:3' */
            StDbwCtrl = DBW_ACTIVE_DIAG;

            /* Event: '<S21>:43' */
            DbwMgm_ActiveDiag();
            sf_guard = true;
          } else {
            /* Transition: '<S21>:4' */
            if (FlgDisableDbw != 0) {
              /* Transition: '<S21>:8' */
              StDbwCtrl = DBW_DISABLED;

              /* Event: '<S21>:45' */
              DbwMgm_Disabled();
              sf_guard = true;
            } else {
              /* Transition: '<S21>:10' */
              if (S3FlgTestDisLoads != 0) {
                /* Transition: '<S21>:9' */
                StDbwCtrl = DBW_TEST_DIS_LOAD;

                /* Event: '<S21>:46' */
                DbwMgm_TestDisLoads();
                sf_guard = true;
              } else {
                /* Transition: '<S21>:6' */
                if (FlgSelfLearning != 0) {
                  /* Transition: '<S21>:5' */
                  StDbwCtrl = DBW_SELF_LEARNING;

                  /* Event: '<S21>:44' */
                  DbwMgm_SelfLearning();
                  sf_guard = true;
                } else {
                  /* Transition: '<S21>:11' */
                  if (VtRec[(int32_T)REC_FORCE_LH] != 0) {
                    /* Transition: '<S21>:12' */
                    StDbwCtrl = DBW_FORCE_LH;

                    /* Event: '<S21>:42' */
                    DbwMgm_ForceLH();
                    sf_guard = true;
                  } else {
                    /* Transition: '<S21>:2' */
                    StDbwCtrl = DBW_CLOSED_LOOP;

                    /* Event: '<S21>:47' */
                    DbwMgm_ClosedLoop();
                  }
                }
              }
            }
          }

          if (sf_guard) {
            /* Transition: '<S21>:14' */
            /* Event: '<S21>:49' */
            DbwMgm_HBLogicEnable();
          }
        }
      } else {
        /* Transition: '<S21>:53' */
        StDbwCtrl = DBW_DISABLED;

        /* Event: '<S21>:45' */
        DbwMgm_Disabled();
      }
    }
  }
}

/* Model step function */
void DbwMgm_step(void)
{
  /* Outputs for atomic SubSystem: '<Root>/DbwMgm' */

  /* Outputs for trigger SubSystem: '<S1>/fc_DbwMgm_Init' incorporates:
   *  Inport: '<Root>/ev_PowerOn'
   *  TriggerPort: '<S17>/Trigger'
   */
  if ((DbwMgm_U.ev_PowerOn > 0) &&
      (DbwMgm_PrevZCSigState.fc_DbwMgm_Init_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S17>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    DbwMgm_Init();
  }

  DbwMgm_PrevZCSigState.fc_DbwMgm_Init_Trig_ZCE = DbwMgm_U.ev_PowerOn > 0 ?
    POS_ZCSIG : ZERO_ZCSIG;

  /* end of Outputs for SubSystem: '<S1>/fc_DbwMgm_Init' */

  /* Outputs for trigger SubSystem: '<S1>/fc_DbwMgm_T5ms' incorporates:
   *  Inport: '<Root>/ev_T5ms'
   *  TriggerPort: '<S18>/Trigger'
   */
  if ((DbwMgm_U.ev_T5ms > 0) && (DbwMgm_PrevZCSigState.fc_DbwMgm_T5ms_Trig_ZCE
       != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S18>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T5ms'
     */
    DbwMgm_T5ms();
  }

  DbwMgm_PrevZCSigState.fc_DbwMgm_T5ms_Trig_ZCE = DbwMgm_U.ev_T5ms > 0 ?
    POS_ZCSIG : ZERO_ZCSIG;

  /* end of Outputs for SubSystem: '<S1>/fc_DbwMgm_T5ms' */

  /* end of Outputs for SubSystem: '<Root>/DbwMgm' */
}

/* Model initialize function */
void DbwMgm_initialize(void)
{
  DbwMgm_PrevZCSigState.fc_DbwMgm_T5ms_Trig_ZCE = POS_ZCSIG;
  DbwMgm_PrevZCSigState.fc_DbwMgm_Init_Trig_ZCE = POS_ZCSIG;

  /* InitializeConditions for atomic SubSystem: '<Root>/DbwMgm' */

  /* InitializeConditions for trigger SubSystem: '<S1>/fc_DbwMgm_T5ms' */

  /* InitializeConditions for S-Function (fcncallgen): '<S18>/Function-Call Generator' incorporates:
   *  InitializeConditions for SubSystem: '<S1>/T5ms'
   */
  DbwMgm_T5ms_Init();

  /* end of InitializeConditions for SubSystem: '<S1>/fc_DbwMgm_T5ms' */

  /* end of InitializeConditions for SubSystem: '<Root>/DbwMgm' */
}

/* user code (bottom of source file) */
/* System '<Root>/DbwMgm' */
#else                                  /* _BUILD_DBWMLM_ not defined */
#endif                                 /* _BUILD_DBWMLM_  */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
