COMPILER = <COMPILERFULLPATH>
COMMAND = n=..\etpu\include
COMMAND  = +delf
COMMAND  = -d<TARGET>

#include "..\common\ETPU_EngineTypes.h"
#include "..\etpu\include\build_opt.h"

OBJECT   = ..\etpu\src\ETPU_globals.obj
OBJECT   = ..\etpu\src\ETPU_PHASE.obj
OBJECT   = ..\etpu\src\sparkHandler.obj
OBJECT   = ..\etpu\src\angleClock.obj
OBJECT   = ..\etpu\src\ETPU_PULSE.obj
OBJECT   = ..\etpu\src\angleExGenerator.obj
OBJECT   = ..\etpu\src\ETPU_PWM.obj
OBJECT   = ..\etpu\src\ion_trigger.obj
OBJECT   = ..\etpu\src\adc_trigger.obj
OBJECT   = ..\etpu\src\ETPU_Delay.obj
OBJECT   = ..\etpu\src\FastLinkedChan_in.obj
OBJECT   = ..\etpu\src\FastLinkedChan_out.obj
OBJECT   = ..\etpu\src\FastLinkedChan_dir.obj
OBJECT   = ..\etpu\src\ETPU_UART.obj
OBJECT   = ..\etpu\src\ETPU_PWM_in.obj
OBJECT   = ..\etpu\src\ETPU_angTrigger.obj
OBJECT   = ..\etpu\src\autofilesGenerator.obj

HEX      = .\synch
LISTING  = .\synch
ERROR    = .\synch
MAP      = .\synch