#ifndef _EVENTS_H_
#define _EVENTS_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint16_t FIFOErrCnt;
extern uint16_t FIFOErrCnt_TORF;
extern uint16_t FIFOErrCnt_RFOF;
extern uint16_t FIFOErrCnt_CFUF;
extern uint16_T CntADC450uIrqRec;
extern uint16_T CntADC2mIrqRec;
extern uint16_T CntADC10mIrqRec;

extern void (*ExTxDoneChA)(void);
extern void (*ExRxDoneChA)(void);

#if (TARGET_TYPE == MPC5554)
extern void (*ExTxDoneChB)(void);
extern void (*ExRxDoneChB)(void);
#endif 

extern void (*ExTxDoneChC)(void);
extern void (*ExRxDoneChC)(void);

extern bool CAN_ConfigStatus;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
void FuncCAN_ExTxDoneChA (void);
void FuncCAN_ExRxDoneChA (void);

#if (TARGET_TYPE == MPC5554)
void FuncCAN_ExTxDoneChB (void);
void FuncCAN_ExRxDoneChB (void);
#endif
void FuncCAN_ExTxDoneChC (void);
void FuncCAN_ExRxDoneChC (void);

void CAN_ExTxDoneChA (void);
void CAN_ExRxDoneChA (void);   
#if (TARGET_TYPE == MPC5554)
void CAN_ExTxDoneChB (void);
void CAN_ExRxDoneChB (void); 
#endif 
void CAN_ExTxDoneChC (void);
void CAN_ExRxDoneChC (void); 


void CAN_CHA_MB0(void);
void CAN_CHA_MB1(void);
void CAN_CHA_MB2(void);
void CAN_CHA_MB3(void);
void CAN_CHA_MB4(void);
void CAN_CHA_MB5(void);
void CAN_CHA_MB6(void);
void CAN_CHA_MB7(void);
void CAN_CHA_MB8(void);
void CAN_CHA_MB9(void);
void CAN_CHA_MB10(void);
void CAN_CHA_MB11(void);
void CAN_CHA_MB12(void);
void CAN_CHA_MB13(void);
void CAN_CHA_MB14(void);
void CAN_CHA_MB15(void);
void CAN_CHA_MB16_31(void);
void CAN_CHA_MB32_63(void);
#if (TARGET_TYPE == MPC5554)
void CAN_CHB_MB0(void);
void CAN_CHB_MB1(void);
void CAN_CHB_MB2(void);
void CAN_CHB_MB3(void);
void CAN_CHB_MB4(void);
void CAN_CHB_MB5(void);
void CAN_CHB_MB6(void);
void CAN_CHB_MB7(void);
void CAN_CHB_MB8(void);
void CAN_CHB_MB9(void);
void CAN_CHB_MB10(void);
void CAN_CHB_MB11(void);
void CAN_CHB_MB12(void);
void CAN_CHB_MB13(void);
void CAN_CHB_MB14(void);
void CAN_CHB_MB15(void);
void CAN_CHB_MB16_31(void);
void CAN_CHB_MB32_63(void);
#endif
void CAN_CHC_MB0(void);
void CAN_CHC_MB1(void);
void CAN_CHC_MB2(void);
void CAN_CHC_MB3(void);
void CAN_CHC_MB4(void);
void CAN_CHC_MB5(void);
void CAN_CHC_MB6(void);
void CAN_CHC_MB7(void);
void CAN_CHC_MB8(void);
void CAN_CHC_MB9(void);
void CAN_CHC_MB10(void);
void CAN_CHC_MB11(void);
void CAN_CHC_MB12(void);
void CAN_CHC_MB13(void);
void CAN_CHC_MB14(void);
void CAN_CHC_MB15(void);
void CAN_CHC_MB16_31(void);
void CAN_CHC_MB32_63(void);


void SPI_A_EOQ_ISR (void) ;

void SPI_B_EOQ_ISR (void) ;

void SPI_C_EOQ_ISR (void) ;

void SPI_D_EOQ_ISR (void) ;


#endif
