/*
 * File: CmiSatMgm_types.h
 *
 * Code generated for Simulink model 'CmiSatMgm'.
 *
 * Model version                  : 1.285
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu May 16 16:15:34 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_CmiSatMgm_types_h_
#define RTW_HEADER_CmiSatMgm_types_h_
#endif                                 /* RTW_HEADER_CmiSatMgm_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
