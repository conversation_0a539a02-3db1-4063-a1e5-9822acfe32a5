#**************************************************************************/
#* FILE NAME: mpc5500_asmcfg.s              COPYRIGHT (c) Freescale 2004  */
#*                                                All Rights Reserved     */
#* DESCRIPTION:                                                           */
#* This file contains functions for the MPC5500 assembly configuration.   */
#=========================================================================*/
#*                                                                        */
#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
#* ---   -----------   ----------   ---------------------                 */
#* 0.1   G. <PERSON>    26/Mar/04    Initial version                       */ 
#* 0.2   G<PERSON>    29/Apr/04    Made compiler names unique for        */
#*                                    assembly configuration.             */
#*                                  Single designation for rcw values.    */
#* 0.3   G. <PERSON>    13/May/04    Changed definition of FMPLL_SYNCR     */
#*                                    register settings.                  */
#* 0.4   G. Jackson    15/May/04    Removed msync and isync from tlbwe    */
#*                                    commands.                           */
#* 0.5   G. Jackson    25/May/04    Changed __OPCOUNT to __SRAM_LOAD_SIZE */
#*                                  Changed __SRAM_OPCODE to __SRAM_LOAD  */
#*                                  Changed cfg_OPCODE to cfg_SRAM_LOAD   */
#*                                  Changed OPCNT_OFFSET to IP_ADVANCE    */
#* 0.6   G. Jackson    12/Jun/04    Changed TLB entries to work with      */
#*                                  MPC5554 Rev. 0.3 and later for the    */
#*                                  BAM, PBRIDGE_B, and Internal FLASH.   */
#* 0.7   G. Jackson    30/Jun/04    Added entries for RCHW (RCHW_VAL)     */
#* 0.8   G. Jackson    05/Aug/04    Added cfg_PNTRS for R13 and R2        */
#* 0.9   G. Jackson    18/Aug/04    Added cfg_ROMCPY for .data and .sdata */
#* 0.91  G. Jackson    20/Sep/04    cfg_ROMCPY changed to load by bytes.  */
#* 0.92  G. Jackson    11/Oct/04    L1CSR0 checks added for complete      */
#*                                    cache operation.                    */
#* 1.0   G. Jackson    12/Oct/04    Green Hills now does not require      */
#*                                    quotation marks around the section  */
#*                                  Added syntax to generate symbols for  */
#*                                    debug.                              */
#**************************************************************************/
    .equ __GRNHS__,  1  // Designation for the Green Hills compiler
    .equ __PEGNU__,  0  // Designation for the P&E Micro Gnu compiler
    .equ __DIABCC__, 0  // Designation for the Wind River compiler
    .equ __CWWRKS__, 0  // Designation for the Metrowerks CodeWarrior compiler
    
    .include "mpc5500_usrdefs.inc"

    .globl cfg_MMU         

         
 
 	
#################################################
#       This is the start of the .init section.

    .if __PEGNU__
    .section ".init","ax" # The "ax" is required to generate "non-text" code
    .endif

    .if __GRNHS__
    .section .init,ax     # The "ax" generates symbols for debug
    .endif

    .if __DIABCC__
    .section .init,c      # The "c" generates symbols for debug
    .endif
    
     .if __CWWRKS__
    .section .init,text   # The "text" generates symbols for debug
    .endif

#*********************************************************************/
#*************************************************************************/
#                        MMU Functions                                   */
#*************************************************************************/

#*****************************************************************************/
# FUNCTION     : cfg_MMU                                                     */
# PURPOSE      : This function modifies the MMU TLB (translation lookaside   */
#                 buffer) table by writing to the appropriate MAS registers. */
# INPUT NOTES  : Requires SPRs defined and a data table for the TLB entries  */
#                mmu_tlb0 through mmu_tlb11, mmu_tlb15 from                  */
#                mpc5500_usrdefs.inc.                                        */
# RETURN NOTES : None                                                        */
# WARNING      : Registers used: R3,R5. Commands "msync" and "isync" are not */
#                required around the tlbwe since we are at configuration and */
#                 other background operations cannot be active.              */
#*****************************************************************************/

cfg_MMU:

#***************************************************/
#     setup MMU                                    */
#***************************************************/

# Change the TLB0 PBRIDGE_B size to 1M.    
    lis   r3, mmu_tlb0@h     # base address of MAS Constants
    ori   r3,r3, mmu_tlb0@l
    lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Change the TLB1 Flash (1) size to 1M. Split into 5 areas.    
    lis   r3, mmu_tlb1@h     # base address of MAS Constants
    ori   r3,r3, mmu_tlb1@l
    lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    msync                    # synchronize for running out of Flash
    tlbwe                    # Write the entry to the TLB 
    isync                    # synchronize for running out of Flash 

# Change the TLB2 SRAM size to 64K.    
    lis   r3, mmu_tlb2@h     # base address of MAS Constants
    ori   r3,r3, mmu_tlb2@l
    lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Change the TLB3 PBRIDGE_A size to 1M.    
    lis   r3, mmu_tlb3@h     # base address of MAS Constants
    ori   r3,r3, mmu_tlb3@l
    lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Change the TLB4 SRAM size to 80K    
    lis   r3, mmu_tlb4@h     # base address of MAS Constants
    ori   r3,r3, mmu_tlb4@l
    lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Change the TLB5 SRAM size to 96K    
    lis   r3, mmu_tlb5@h     # base address of MAS Constants
    ori   r3,r3, mmu_tlb5@l
    lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

    blr
# End of cfg_MMU 

##*************************************************************************/




##*************************************************************************/
# FUNCTION     : MMU DATA Tables                                          */
# PURPOSE      : This defines the MMU data tables for the TLB entries     */
#                which are set in the file mpc5500_asmcfg.s               */
# INPUT NOTES  : Requires that the TLB settings be in MPC5500_defs.inc    */
# RETURN NOTES : mmu_tlb0 [TLB0_MAS[0:3] through mmu_tlb11 [TLB0_MAS[0:3] */
# WARNING      : Registers used: none. Section is: .rodata                */
##*************************************************************************/


# Solinas : 07/04/2005 Section declaration for MMU TLBs :
    
    .if __PEGNU__
    .section ".rodata" 
    .endif

    .if __CWWRKS__ | __DIABCC__ | __GRNHS__
    .section .rodata
    .endif
        
    
#*************************************************************************/
#* DESCRIPTION:                                                          */  
#* This table contains definitions for the MPC5554 MMU TLB entries.      */
#* The bit definitions used in the TLB defines are located below.        */  
#* The second half of the file is the TLB setup code in mpc5500_asmcfg.s */
#*************************************************************************/ 

#*** TLB DEFINES ***/

#** TLB entry 0 - PBRIDGE_B set to 1M **
mmu_tlb0:
# TLB1_MAS0
    .long ( TLB_SELECT | TLB_ENTRY0 )
# TLB1_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_1M )
# TLB1_MAS2
    .long ( PBRIDGEB_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_GUARDED | PAGE_BIG_ENDIAN )
# TLB1_MAS3
    .long ( PBRIDGEB_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 1 - Split Internal FLASH (1) set to 1MB **
mmu_tlb1:
# TLB1_MAS0
    .long ( TLB_SELECT | TLB_ENTRY1 )
# TLB1_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_256M )
# TLB1_MAS2
    .long ( FLASH_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB1_MAS3
    .long ( FLASH_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 3 - Internal SRAM reduce from 1M space to 64K only **
mmu_tlb2:
# TLB2_MAS0
    .long ( TLB_SELECT | TLB_ENTRY2 )
# TLB2_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_64K )
# TLB2_MAS2
    .long ( SRAM_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB2_MAS3
    .long ( SRAM_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 3 - PBRIDGE_A set to 1MB **
mmu_tlb3:
# TLB3_MAS0
    .long ( TLB_SELECT | TLB_ENTRY3 )
# TLB3_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_1M )
# TLB3_MAS2
    .long ( PBRIDGEA_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB3_MAS3
    .long ( PBRIDGEA_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 4 - Internal SRAM  from 64K space to 80K 
mmu_tlb4:
# TLB4_MAS0
    .long ( TLB_SELECT | TLB_ENTRY4 )
# TLB4_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_16K )
# TLB2_MAS2
    .long ( SRAM_BASE_ADDR1 | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB2_MAS3
    .long ( SRAM_BASE_ADDR1 | READWRITEEXECUTE )

#** TLB entry 5 - Internal SRAM  from 64K space to 80K 
mmu_tlb5:
# TLB4_MAS0
    .long ( TLB_SELECT | TLB_ENTRY5 )
# TLB4_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_16K )
# TLB2_MAS2
    .long ( SRAM_BASE_ADDR2 | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB2_MAS3
    .long ( SRAM_BASE_ADDR2 | READWRITEEXECUTE )


