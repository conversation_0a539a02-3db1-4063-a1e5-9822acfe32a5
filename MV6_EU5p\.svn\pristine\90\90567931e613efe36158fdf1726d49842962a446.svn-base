/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/SAF2#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1638   $                                                                                          */
/* $Date:: 2009-09-08 09:52:41 +0200 (mar, 08 set 2009)   $                                                      */
/* $Author:: gelmettia               $                                                                       */
/*****************************************************************************************************************/


#include "sys.h"

#include "hbridge.h"
#include "rtlibsrc.h"
#include "recmgm.h"
#include "saf2mgm.h"
#include <string.h>
#include "diagmgm_out.h"
#include "timing.h"
#include "tasksdefs.h"
#include "GasPos_Mgm.h"
#include "digio.h"
#include "sys.h"
#include "mathlib.h"
#include "trq_est.h"
#include "trq_driver.h"
#include "trq_saflim.h"
#include "af_ctrl.h"
#include "rpm_limiter.h"
#include "idle_mgm.h"
#include "temp_mgm.h"
#include "eemgm.h"
#include "air_mgm.h"
#include "airdiag_mgm.h"
#include "ion_lambda.h"
#include "loadmgm.h"
#include "sparkmgm.h"
#include "cpumgm.h"
#include "sabasic_mgm.h"
#include "ion_lambda.h"
#include "thrposmgm.h"
#include "injcmd.h"
#include "vspeed_mgm.h"
#include "vspeed_ctrl.h"
#include "rpm_limiter.h"
#include "ionacq.h"
#include "patm_model.h"
#include "trq_drivmgm.h"
#include "analogin.h"
#include "diagcanmgm.h"
#include "dbw_mgm.h"
#include "Canmgm.h"

#include "saf2mgm_eep.h"

#ifdef  _BUILD_VSRAMMGM_
#include "vsrammgm.h"
#endif /* _BUILD_VSRAMMGM_ */ 


#ifdef _BUILD_SAF2MGM_

//Extern calib params defs

extern int16_T S2ACOEFR0A0;
extern int16_T S2ACOEFR0A1;
extern int16_T S2ACOEFR0A2;
extern int16_T S2ACOEFR0A3;
extern int16_T S2ACOEFR1A0;
extern int16_T S2ACOEFR1A1;
extern int16_T S2ACOEFR1A2;
extern int16_T S2ACOEFR2A0;
extern int16_T S2ACOEFR2A1;
extern int16_T S2ACOEFR3A0;
extern uint16_T S2AKFILTPRES;
extern uint16_T S2APOW2TEMP;
extern uint16_T S2APRESATMMAX;
extern uint16_T S2APRESATMMIN;
extern int16_T S2ATAIRMAX;
extern int16_T S2ATAIRMIN;
extern uint16_T S2ATGASPOS;
extern uint16_T S2ATHRGASPOS;
extern uint8_T S2ASTCONFGAS;
extern uint16_T S2ATHRPRES;
extern uint16_T S2ATHRVGASH;
extern uint16_T S2ATHRVGASL;
extern uint16_T S2ATHRPRESSTAB;
extern uint16_T S2ATPRES;
extern int16_T S2ATWATERINC;
extern int16_T S2ATWATERMAX;
extern int16_T S2BTHRDELTALAMCL;
extern uint16_T S2BTVALERR;
extern int16_T S2BTHRLAMERR;
extern int16_T S2DCOEFG0R0;
extern int16_T S2DCOEFG0R1;
extern int16_T S2DCOEFG0R2;
extern int16_T S2DCOEFG1R0;
extern int16_T S2DCOEFG1R1;
extern int16_T S2DCOEFG1R2;
extern int16_T S2DCOEFG2R0;
extern int16_T S2DCOEFG2R1;
extern uint16_T S2DELAYRESTART;
extern int16_T S2DTHRTRQ;
extern uint16_T S2DTTRQ;
extern int16_T S2ECOEFG0R0;
extern int16_T S2ECOEFG0R1;
extern int16_T S2ECOEFG0R2;
extern int16_T S2ECOEFG1R0;
extern int16_T S2ECOEFG1R1;
extern int16_T S2ECOEFG1R2;
extern int16_T S2ECOEFG2R0;
extern int16_T S2ECOEFG2R1;
extern uint16_T S2ENSAF2;
extern uint16_T S2ETCANREQ;
extern int16_T S2ETHRCANREQ;
extern uint16_T S2FTHRPMIDLE;
extern uint16_T S2FTVALERR;
extern int16_T S2FCMEINIDLE;
extern int16_T S2AGASPOSGAIN[];
extern int16_T S2AGASPOSOFFSET[];
extern int16_T S2GCOEFCMIQ0R0;
extern int16_T S2GCOEFCMIQ0R1;
extern int16_T S2GCOEFCMIQ0R2;
extern int16_T S2GCOEFCMIQ0R3;
extern int16_T S2GCOEFCMIQ1R0;
extern int16_T S2GCOEFCMIQ1R1;
extern int16_T S2GCOEFCMIQ1R2;
extern int16_T S2GCOEFCMIQ1R3;
extern int16_T S2GCOEFDSA0;
extern int16_T S2GCOEFDSA1;
extern int16_T S2GCOEFDSA2;
extern int16_T S2GCOEFLAM0;
extern int16_T S2GCOEFLAM1;
extern int16_T S2GCOEFLAM2;
extern int16_T S2GCOEFPATM0;
extern int16_T S2GCOEFPATM1;
extern int16_T S2GCOEFPATM2;
extern int16_T S2GCOEFQAIRP0R0;
extern int16_T S2GCOEFQAIRP0R2;
extern int16_T S2GCOEFQAIRP0R3;
extern int16_T S2GCOEFQAIRP1R0;
extern int16_T S2GCOEFQAIRP1R1;
extern int16_T S2GCOEFQAIRP1R2;
extern int16_T S2GCOEFQAIRP2R0;
extern int16_T S2GCOEFQAIRP2R1;
extern int16_T S2GCOEFQAIRP3R0;
extern int16_T S2GCOEFSAOPTL0R0;
extern int16_T S2GCOEFSAOPTL0R1;
extern int16_T S2GCOEFSAOPTL0R2;
extern int16_T S2GCOEFSAOPTL0R3;
extern int16_T S2GCOEFSAOPTL1R0;
extern int16_T S2GCOEFSAOPTL1R1;
extern int16_T S2GCOEFSAOPTL1R2;
extern int16_T S2GCOEFSAOPTL2R0;
extern int16_T S2GCOEFSAOPTL2R1;
extern int16_T S2GCOEFSAOPTL3R0;
extern int16_T S2GCOEFTAIR0;
extern int16_T S2GCOEFTAIR1;
extern int16_T S2GCOEFTAIR2;
extern int16_T S2GCOEFTAIR3;
extern int16_T S2GCOEFTW0R0;
extern int16_T S2GCOEFTW1R0;
extern int16_T S2GCOEFTW1R1;
extern int16_T S2GCOEFTW2R0;
extern int16_T S2GCOEFTW2R1;
extern uint16_T S2GLAMOBJMAX;
extern uint16_T S2GLAMOBJMIN;
extern int16_T S2GSAMAX;
extern int16_T S2GSAMIN;
extern uint16_T S2GTCMIEST;
extern uint16_T S2GTEFFCTF;
extern uint16_T S2GTEFFLAM;
extern uint16_T S2GTEFFSA;
extern int16_T S2GTHRCMIEST;
extern int16_T S2GTHREFFCTF;
extern int16_T S2GTHREFFLAM;
extern int16_T S2GTHREFFSA;
extern uint16_T S2GTHRQAIR;
extern int16_T S2GTHRSAOPT;
extern uint16_T S2GTQAIR;
extern uint16_T S2GTSAOPT;
extern int16_T S2HCOEFCMFR0P0;
extern int16_T S2HCOEFCMFR0P1;
extern int16_T S2HCOEFCMFR0P2;
extern int16_T S2HCOEFCMFR0P3;
extern int16_T S2HCOEFCMFR1P0;
extern int16_T S2HCOEFCMFR1P1;
extern int16_T S2HCOEFCMFR1P2;
extern int16_T S2HCOEFCMFR2P0;
extern int16_T S2HCOEFCMFR2P1;
extern int16_T S2HCOEFCMFR3P0;
extern int16_T S2HCOEFFCMFTWR0T0;
extern int16_T S2HCOEFFCMFTWR0T1;
extern int16_T S2HCOEFFCMFTWR0T2;
extern int16_T S2HCOEFFCMFTWR1T1;
extern int16_T S2HCOEFFCMFTWR1T2;
extern uint16_T S2HTCMF;
extern uint16_T S2HTCMIP;
extern int16_T S2HTHRCMF;
extern int16_T S2HTHRCMIP;
extern uint16_T S2ITCMI;
extern uint16_T S2IGNTHRCMI;
extern uint16_T S2IMINTHRCMI;
extern uint16_T S2IMINTHRCMIMIN;
extern uint16_T S2LCMEMAXH;
extern uint16_T S2LCMEMAXS;
extern uint16_T S2LTTRQRED;
extern uint16_T S2LVALENGOFF;
extern uint16_T S2LVALGASPOS;
extern uint16_T S2LVALLIM;
extern int16_t  S2LTHRTRQRED;
extern uint16_T S2LTHRMAXLH;
extern uint16_T S2LVALLLH;
extern uint16_T S2MAXRESTART;
extern uint16_T S2RPMSTARTS2;
extern uint16_T S2RPMSTOPS2;
extern uint8_T S2THS2ERRCNT;
extern uint8_T S2PERFENABLE;
extern uint8_t   S2USECMECAN;
extern uint8_T S2STOREDEL;
extern uint16_t S2INCRESTARTCNT;

/* Min-Max values for polynomial input */
/* A MODULE */
extern uint16_T S2ATBPRESANGMIN;
extern uint16_T S2ATBPRESANGMAX;
extern uint16_T S2AGTBPRESRPMMIN;
extern uint16_T S2AGTBPRESRPMMAX;

/* D MODULE */
extern uint16_T S2DTBCMERPMMIN;
extern uint16_T S2DTBCMERPMMAX;
extern uint16_T S2DTBCMEGASMIN;
extern uint16_T S2DTBCMEGASMAX;

/* E MODULE */
extern uint16_T S2ETBCMERPMMIN;
extern uint16_T S2ETBCMERPMMAX;
extern uint16_T S2ETBCMEGASMIN;
extern uint16_T S2ETBCMEGASMAX;

/* G MODULE */
extern uint16_T S2GHBKCMRPMFMIN;
extern uint16_T S2GHBKCMRPMFMAX;

extern int16_T S2GBKEFFSAMIN;
extern int16_T S2GBKEFFSAMAX;

extern uint16_T S2GBKPATMCORRMIN;
extern uint16_T S2GBKPATMCORRMAX;

extern uint16_T S2GTBQACPRESMIN;
extern uint16_T S2GTBQACPRESMAX;
extern uint16_T S2GTBQACRPMMIN;
extern uint16_T S2GTBQACRPMMAX;

extern uint16_T S2GTBSARPMMIN;
extern uint16_T S2GTBSARPMMAX;
extern uint16_T S2GTBSALOADMIN;
extern uint16_T S2GTBSALOADMAX;

extern int16_T S2GTBSATAIRMIN;
extern int16_T S2GTBSATAIRMAX;
extern int16_T S2GTBSATWATMIN;
extern int16_T S2GTBSATWATMAX;

extern int16_T S2GBKWATERCORRMIN;
extern int16_T S2GBKWATERCORRMAX;

extern int16_T S2GBKAIRCORRMIN;
extern int16_T S2GBKAIRCORRMAX;

extern uint16_T S2GBKLAMEFFMIN;
extern uint16_T S2GBKLAMEFFMAX;

/* H MODULE */
extern uint16_T S2HTBCMFPRESMIN;
extern uint16_T S2HTBCMFPRESMAX;

extern int16_T S2HTBCMFTWATMIN;
extern int16_T S2HTBCMFTWATMAX;

/* Scalar variable definitions */
uint8_t   S2State = S2_STATE_NONE;
uint16_t  S2ExeCode;

extern uint8_T  KeySignal;
extern uint32_t      S2RestartCnt;        //must be stored in  EERAM

/* Scalar variable definitions for S2 Modules */
uint16_t  S2AEstPresIntake;
uint16_t  S2AEstGasPos;
int32_t   S2AEstPresIntakeHR;
int16_t   S2AEstTWater;
int16_t   S2DEstCmeGasRpm;
int16_t   S2EEstCmeTargCAN;
int16_t   S2GEstCmiEst;
int16_t   S2GEstCmiEstMax;
uint16_t  S2GEstEffCutoff;
uint16_t  S2GEstEffLambda;
uint16_t  S2GEstQAirAvg;
int16_t   S2HEstCmfP;
int16_t   S2HEstCmiDriverP;
uint16_t  S2AEstPresPoly;

uint16_t  S2GTBQACPoly;
uint16_t  S2GVTAIRCORRPoly;
uint16_t  S2GTBWATERCORRPoly;
uint16_t  S2GVTPATMCORRPoly;

uint16_t  S2GEstQAirBase;

int16_t   S2GTBSAOPTPoly;

int16_t   S2GEstSAopt;
uint16_t  S2GEstEffSAreal;

int16_t   S2GCOEFCMIPoly;

int16_T   S2HTBCMFTWPoly;         
int16_T   S2HTBCMFPoly;       

int32_t   S2AEstTWater_HR;

/* extra scalar variables */

uint8_t   S2FlgDisL1;
uint8_t   S2FlgDisL2;
uint8_t   S2FlgExec;
int16_t   S2HTWaterMax; //OK modulo H

uint32_t  S2MgmStartTime;

uint32_t  AModule_timer_old1;
uint32_t  AModule_timer_old2;
uint32_t  BModule_timer_old1;
uint32_t  BModule_timer_old2;
uint32_t  DModule_timer_old;
uint32_t  EModule_timer_old;
uint32_t  FModule_timer_old;
uint32_t  GAirModule_timer_old;
uint32_t  GModule_SAopt_timer_old;
uint32_t  GModule_EFFSA_timer_old;
uint32_t  GModule_EFFLAM_timer_old;
uint32_t  GModule_EFFCUTOFF_timer_old;
uint32_t  G_CmiEst_timer_old;
uint32_t  H_CmiDrvP_timer_old;
uint32_t  H_CmfP_timer_old;
uint32_t  I_TorqueComp_timer_old;
uint32_t  L_RecoveriesVal_timer_old1;
uint32_t  L_RecoveriesVal_timer_old2;
uint32_t  L_RecoveriesVal_timer_old3;
uint32_t  L_RecoveriesVal_timer_old4;
uint32_t  L_RecoveriesVal_timer_old5;
uint32_t  L_RecoveriesVal_timer_old6;

uint16_t  S2PerfTime;


/* Vector variable definitions */
uint8_t   S2ErrCnt[SIZE_S2PUNERR];
uint8_t   S2PunErr[SIZE_S2PUNERR];
uint16_t  S2Result[SIZE_S2PUNERR];
uint16_t  S2ResultNeg[SIZE_S2PUNERR];

extern uint8_t   S2ValErr[SIZE_S2PUNERR]; //Stored in  EERAM

/* Modules Scalar Parameters */


/* Scalar outputs */
uint8_t S2FlgDisLRelay = FALSE;
uint8_t S2ForceShtDwn = FALSE;
uint8_t S2KeyOffFlag = FALSE;


//--------------------------------------------
 
/* Local variables */
Saf2InputBufCfg      S210msBuf,Saf2NegInputBuf;

/* Prototypes */
void Saf2Mgm_ENLoads(void);
void Saf2Mgm_DISLoads(void);
void SafMgm_ResErr(void);

//A_Sensors_Val Sub-Routines
static uint16_t TBPRESINTAKE_Poly (void);

//D_DriverTorqueRequest_Val Sub-Routines
static int16_t TBCMEDRIV_Poly (void);

//E_ExternalTorqueRequest_Val Sub-Routines
static int16_t Estimated_Driver_Torque(void);

//G_GeneratedTorque_Val Sub-Routines
static uint16_t TBQAC_polynomial(uint16_t PrIn);
static uint16_t VTAIRCORR_polynomial(void);
static uint16_t TBWATERCORR_polynomial(void);
static uint16_t PATMCORR_polynomial(void);
static int16_t  TBSAOPT_polynomial(void);
/* static int16_t  TBSATEMP_polynomial(void); */
static uint16_t EFFSA_polynomial(void);
static uint16_t EFFLam_polynomial(void);
static int16_t CmiEstimation(void);


//H_RequestedTorque_Val Sub-Routines
static int16_t TBCMF_polynomial(void);
static int16_t TBCMFTW_polynomial(void);
static int16_t S2TWaterModel(void);

static void A_Sensors_Val(void);
static void B_Actuactors_Val(void);    
static void C_CAN_Val(void);
static void D_DriverTorqueRequest_Val(void);
static void E_ExternalTorqueRequest_Val(void);
static void F_Idle_Val(void);
static void G_GeneratedTorque_Val(void);
static void H_RequestedTorque_Val(void);
static void I_TorqueCompare(void);
static void L_Recoveries_Val(void);
static void M_Error_Counter(void);

static void Saf2Mgm_UpdateBufferT10ms(void);
static void CheckInputData(void);
static void Saf2Mgm_Safety_Engine_Stop(void);
static void Saf2ResetModuleTimer(uint32_t *module_timer);


#ifdef _BUILD_S2_STORE_RESULTS_

static void A1_Store(void);
static void A2_Store(void);
static void D1_Store(void);
static void E1_Store(void);
static void G1_Store(void);
static void G2_Store(void);
static void G3_Store(void);
static void G4_Store(void);
static void G5_Store(void);
static void G6_Store(void);
static void H1_Store(void);
static void H2_Store(void);
static void S2DelStoredResults(void);
static void S2StoreFrz(uint8_t ind);


static void (*ptrS2StoreFunction[SIZE_S2STORE])(void) = 
{
    A1_Store,
    A2_Store,   
    D1_Store,
    E1_Store,
    G1_Store,
    G2_Store,
    G3_Store,
    G4_Store,
    G5_Store,
    G6_Store,
    H1_Store,
    H2_Store
};

#define S2_STORE_RESULTS(x) if (S2FlgExec == TRUE)  { ptrS2StoreFunction[x](); }
#define S2_DEL_STORED_RESULTS()   S2DelStoredResults()
#define S2_INC_COUNTER(x) { if (S2FlgExec == TRUE)  {EES2FrzNEvents[x]++; }}

static void A1_Store(void)
{
   if (abs_delta(S2AEstPresIntake,S210msBuf.PresIntake_10ms) > 
       abs_delta(EES2AEstPresIntake,EEPresIntake_10ms)) 
   {
      EES2AEstPresIntake = S2AEstPresIntake;
      EEPresIntake_10ms = S210msBuf.PresIntake_10ms;

      S2StoreFrz(STORE_A1);
   } 
}

static void A2_Store(void)
{
    if ((S210msBuf.GasPos_10ms + EES2AEstGasPos) >
        (EEGasPos_10ms + S2AEstGasPos))
    {
      EEGasPos_10ms = S210msBuf.GasPos_10ms;
      EES2AEstGasPos = S2AEstGasPos;

      S2StoreFrz(STORE_A2);
    } 

}
static void D1_Store(void)
{
    if (
        abs_delta(S2DEstCmeGasRpm , S210msBuf.CmeGasRpm_10ms) > 
        abs_delta(EES2DEstCmeGasRpm , EECmeGasRpm_10ms)
       ) 
    {
        EES2DEstCmeGasRpm = S2DEstCmeGasRpm;
        EECmeGasRpm_10ms = S210msBuf.CmeGasRpm_10ms;

        S2StoreFrz(STORE_D1);
    } 
}
static void E1_Store(void)
{
    if (
        (S2EEstCmeTargCAN + EECmeDriverCANF_10ms ) > 
        (EES2EEstCmeTargCAN + S210msBuf.CmeDriverCANF_10ms)
       ) 
    {
        EES2EEstCmeTargCAN = S2EEstCmeTargCAN;
        EECmeDriverCANF_10ms = S210msBuf.CmeDriverCANF_10ms;

        S2StoreFrz(STORE_E1);
    } 
}
static void G1_Store(void)
{
    if (
        (S2GEstQAirAvg  + EEQAirAvg_10ms) >
        (EES2GEstQAirAvg + S210msBuf.QAirBaseAvg_10ms )
       ) 
    {
        EES2GEstQAirAvg = S2GEstQAirAvg;            
        EEQAirAvg_10ms = S210msBuf.QAirBaseAvg_10ms;            

        S2StoreFrz(STORE_G1);
    } 
}
static void G2_Store(void)
{
    if (
        abs_delta(S210msBuf.SAopt_10ms, S2GEstSAopt) >
        abs_delta(EESAopt_10ms, EES2GEstSAopt)
       ) 
    {
        EESAopt_10ms = S210msBuf.SAopt_10ms;
        EES2GEstSAopt = S2GEstSAopt;

        S2StoreFrz(STORE_G2);
    }
}
static void G3_Store(void)
{
    if (
        abs_delta(S210msBuf.EffSAReal_10ms, S2GEstEffSAreal) > 
        abs_delta(EEEffSAReal_10ms, EES2GEstEffSAreal)
       ) 
    {
        EES2GEstEffSAreal = S2GEstEffSAreal;
        EEEffSAReal_10ms = S210msBuf.EffSAReal_10ms;
        
        S2StoreFrz(STORE_G3);
    } 

}
static void G4_Store(void)
{
    if (
        abs_delta(S210msBuf.EffLambda_10ms, S2GEstEffLambda) > 
        abs_delta(EEEffLambda_10ms, EES2GEstEffLambda) 
       ) 
    {
        EEEffLambda_10ms = S210msBuf.EffLambda_10ms ;
        EES2GEstEffLambda = S2GEstEffLambda;

        S2StoreFrz(STORE_G4);
    } 

}
static void G5_Store(void)
{
    if (
        abs_delta(S210msBuf.EffCutoff_10ms, S2GEstEffCutoff) > 
        abs_delta(EEEffCutoff_10ms, EES2GEstEffCutoff) 
       ) 
    {
        EEEffCutoff_10ms = S210msBuf.EffCutoff_10ms;
        EES2GEstEffCutoff = S2GEstEffCutoff;

        S2StoreFrz(STORE_G5);
    } 

}
static void G6_Store(void)
{
    if (
        (S2GEstCmiEst + EECmiEst_10ms) > 
        (EES2GEstCmiEst + S210msBuf.CmiEst_10ms) 
       ) 
    {
        EES2GEstCmiEst = S2GEstCmiEst;
        EECmiEst_10ms = S210msBuf.CmiEst_10ms;      

        S2StoreFrz(STORE_G6);
    } 
}
static void H1_Store(void)
{
    if (
        (S210msBuf.CmiDriverP_10ms + EES2HEstCmiDriverP) >
        (EECmiDriverP_10ms + S2HEstCmiDriverP) 
       )        
    {
        EECmiDriverP_10ms = S210msBuf.CmiDriverP_10ms;
        EES2HEstCmiDriverP = S2HEstCmiDriverP;

        S2StoreFrz(STORE_H1);
    } 
}
static void H2_Store(void)
{
    if (
        (S210msBuf.CmfP_10ms  + EES2HEstCmfP) >
        (EECmfP_10ms + S2HEstCmfP)
       )
    {
        EECmfP_10ms = S210msBuf.CmfP_10ms;
        EES2HEstCmfP = S2HEstCmfP;

        S2StoreFrz(STORE_H2);
    } 
}

static void S2DelStoredResults(void)
{
    uint8_t i;
    
    if (S2STOREDEL != 0)
    {
        S2STOREDEL = 0;

        EES2AEstPresIntake = 0;
        EEPresIntake_10ms = 0;

        EEGasPos_10ms = 0;
        EES2AEstGasPos = 0;

        EES2DEstCmeGasRpm = 0;
        EECmeGasRpm_10ms = 0;

        EES2EEstCmeTargCAN = 0;
        EECmeDriverCANF_10ms = 0;

        EES2GEstQAirAvg = 0;            
        EEQAirAvg_10ms = 0;            

        EESAopt_10ms = 0;
        EES2GEstSAopt = 0;

        EES2GEstEffSAreal = 0;
        EEEffSAReal_10ms = 0;

        EEEffLambda_10ms = 0;
        EES2GEstEffLambda = 0;

        EEEffCutoff_10ms = 0;
        EES2GEstEffCutoff = 0;

        EES2GEstCmiEst = 0;
        EECmiEst_10ms = 0;      

        EECmiDriverP_10ms = 0;
        EES2HEstCmiDriverP = 0;

        EECmfP_10ms = 0;
        EES2HEstCmfP = 0;

        for(i = 0; i < SIZE_S2STORE; i++)
        {
            EES2FrzRpm[i] = 0;
            EES2FrzLoad[i] = 0;
            EES2FrzPresIntake[i] = 0;
            EES2FrzGasPos[i] = 0;
            EES2FrzAngThrottle[i] = 0;
            EES2FrzTWater[i] = 0;       
            EES2FrzNEvents[i] = 0;       
        }
    }   
}

static void S2StoreFrz(uint8_t ind)
{
    EES2FrzRpm[ind] = Rpm;
    EES2FrzLoad[ind] = Load;
    EES2FrzPresIntake[ind] = PresIntake;
    EES2FrzGasPos[ind] = GasPosCC;
    EES2FrzAngThrottle[ind] = AngThrottle;
    EES2FrzTWater[ind] = TWater;             
}

#else

#define S2_STORE_RESULTS(x) {}
#define S2_DEL_STORED_RESULTS()
#define S2_INC_COUNTER(x) {}

#endif

void (*ptrS2function[SIZE_S2PUNERR])(void) = 
{
    A_Sensors_Val,
    B_Actuactors_Val,   
    C_CAN_Val,
    D_DriverTorqueRequest_Val,
    E_ExternalTorqueRequest_Val,
    F_Idle_Val,
    G_GeneratedTorque_Val,
    H_RequestedTorque_Val,
    I_TorqueCompare,
    L_Recoveries_Val,
    M_Error_Counter,
};

//----------------------------------------------------------
//  Update Buffer T10msec Routine 
//----------------------------------------------------------
static void Saf2Mgm_UpdateBufferT10ms(void)
{
    register uint8_t  index;

    uint8_t *ptrBuffElem    = (uint8_t *)&(S210msBuf);
    uint8_t *ptrBuffNegElem = (uint8_t *)&(Saf2NegInputBuf);
    
    DisableAllInterrupts();
  
    S210msBuf.Rpm_10ms=             Rpm;
    S210msBuf.PresIntake_10ms=      PresIntake;
    S210msBuf.FlgEOL_10ms=          FlgEOL;
    S210msBuf.AngThrottle_10ms=     AngThrottle;
    S210msBuf.StThrRec_10ms=        StThrRec;
    S210msBuf.LamObj_10ms=          LamObj;
    S210msBuf.CmiPotEst_10ms=       CmiPotEst[0];
    S210msBuf.TWater_10ms=          TWater;
    S210msBuf.IdleFlg_10ms=         IdleFlg;
    S210msBuf.GasPos_10ms=          GasPosCC;
    S210msBuf.TAir_10ms=            TAir;
    S210msBuf.Load_10ms=            Load;
    S210msBuf.PresAtm_10ms=         PresAtm;
    S210msBuf.QAirBaseAvg_10ms=     QAirBaseAvg;
    S210msBuf.EffLambda_10ms=       EffLambda;
    S210msBuf.CmiSafP_10ms=         CmiSafP;
    S210msBuf.SAopt_10ms=           SAopt;
    S210msBuf.SAout_10ms=           SAout;
    S210msBuf.EffCutoff_10ms=       EffCutoff;
    S210msBuf.CutOffFlg_10ms=       CutoffFlg;
    S210msBuf.EffSAReal_10ms=       EffSAReal;
    S210msBuf.CmeDriver_10ms=       CmeDriver;
    S210msBuf.CmeGasRpm_10ms=       CmeGasRpm;
    S210msBuf.IdleReqFlg_10ms=      IdleReqFlg;
    S210msBuf.CmeDriverP_10ms=      CmeDriverP;
    S210msBuf.CmiTargetP_10ms=      CmiTargetP;
    S210msBuf.CmiDriverP_10ms=      CmiDriverP;
    S210msBuf.CmfP_10ms=            CmfP;
    S210msBuf.CmiEst_10ms=          CmiEst;
    S210msBuf.CmiIdleP_10ms=        CmiIdleP;
    S210msBuf.CmeDriverCANF_10ms=   CmeDriverCANF;
    
    S210msBuf.VtRec_NO_GAS_10ms=    VtRec[REC_NO_GAS];
    S210msBuf.VtRec_ENG_OFF_10ms=   VtRec[REC_ENG_OFF];
    S210msBuf.VtRec_SL_T_R_10ms=    VtRec[REC_SLIGHT_TRQ_RED];
    S210msBuf.VtRec_HV_T_R_10ms=    VtRec[REC_HEAVY_TRQ_RED];
    S210msBuf.VtRec_DBW_OFF_10ms=   VtRec[REC_DBW_OFF];
    S210msBuf.VtRec_FORCE_LH_10ms=  VtRec[REC_FORCE_LH];
    S210msBuf.VtRec_LIMIT_RPM_10ms= VtRec[REC_LIMIT_RPM];
    S210msBuf.VtRec_REC_LIMIT_VEH_SPEED_10ms= VtRec[REC_LIMIT_VEH_SPEED];

    S210msBuf.FlgEnHBridge_10ms=          FlgEnHBridge;
    S210msBuf.VGasPos1_10ms=        VGasPos1;
    S210msBuf.VGasPos2_10ms=        VGasPos2;
    S210msBuf.VGasPos3_10ms=        VGasPos3;
    S210msBuf.LoadCmd_10ms=         LoadCmd;
    S210msBuf.DeltaLamCL_10ms=      DeltaLamCL;
    S210msBuf.LambdaError_10ms=     LambdaError;
    S210msBuf.InjTimePrgCyl_10ms=   InjTimePrgCyl;
    S210msBuf.CtfLimiterFlg_10ms=   CtfLimiterFlg;
    S210msBuf.CtfVSpeedLimFlg_10ms= CtfVSpeedLimFlg;
    S210msBuf.CmiSpeedP_10ms=       CmiSpeedP;
    S210msBuf.RpmMaxCorr_10ms=      RpmMaxCorr;
    S210msBuf.CmiReqP_10ms=         CmiReqP;
    S210msBuf.VehSpeed_10ms=        VehSpeed;
    S210msBuf.VehSpeedLimCAN_10ms=  VehSpeedLimCAN;
    S210msBuf.CmiTargetPMin_10ms=   CmiTargetPMin;
    

    for (index=0;index<sizeof(S210msBuf);index+=1)
    {
        *(ptrBuffNegElem) = ~(*ptrBuffElem);
        ptrBuffElem++;
        ptrBuffNegElem++;
        
    }

    S210msBuf = S210msBuf;
    EnableAllInterrupts();
}


//----------------------------------------------------------
//  Check input data routine
//----------------------------------------------------------
static void CheckInputData(void){

    register uint8_t  index;

    uint8_t *ptrBuffElem    = (uint8_t *)&(S210msBuf);
    uint8_t *ptrBuffNegElem = (uint8_t *)&(Saf2NegInputBuf);

    uint8_t temp1,temp2;

    S2PunErr[IND_BUF] = NO_ERROR;
   
    for (index=0;index<sizeof(S210msBuf);index+=1) 
    {
      temp1 = *ptrBuffElem;
      temp2 = ~(*ptrBuffNegElem);
      if (temp1 != temp2) 
      {
        S2PunErr[IND_BUF] = S2ERR_BUFFERIN;
        break;
      }

      ptrBuffElem++;
      ptrBuffNegElem++;
    }    
}

//----------------------------------------------------------
//  COntrol Flow Routine
//----------------------------------------------------------
void Saf2Mgm_CrtlFlow(void)
{
  uint8_t i = 0;

  CheckInputData();
  S2ExeCode|=S2_EXECODE_BUF;

  if (S2PunErr[IND_BUF]!=NO_ERROR)
  {
    //Corrupt buffer
    M_Error_Counter();
    S2ExeCode|=S2_EXECODE_ALL;
  } 
  else 
  {
    TIMING_GetAbsMsecTimer(&S2MgmStartTime); //abstime

    for (i=0; i<SIZE_S2PUNERR; i++)
    {
      if (S2ENSAF2&(1 << i))
      {
        ptrS2function[i]();
      }
      else
      {
        S2PunErr[i] = NO_ERROR;
        S2ValErr[i] = NO_ERROR;
        S2ErrCnt[i] = 0;
      }
      S2ExeCode|=(1 << i);
    }
  }
}

//----------------------------------------------------------
//  Start Engine Routine
//----------------------------------------------------------
void Saf2Mgm_EngineStart(void)
{
    S2AEstPresIntake = S210msBuf.PresIntake_10ms;
    S2AEstPresIntakeHR = S210msBuf.PresIntake_10ms << 14;

    S2AEstTWater = TWaterCrk;
    S2AEstTWater_HR = TWaterCrk << 12;

    TIMING_GetAbsMsecTimer(&S2MgmStartTime);

    AModule_timer_old1 = S2MgmStartTime;
    AModule_timer_old2 = S2MgmStartTime;
    BModule_timer_old1 = S2MgmStartTime;
    BModule_timer_old2 = S2MgmStartTime;
    DModule_timer_old = S2MgmStartTime;
    EModule_timer_old = S2MgmStartTime;
    FModule_timer_old = S2MgmStartTime;
    GAirModule_timer_old = S2MgmStartTime;
    GModule_SAopt_timer_old = S2MgmStartTime;
    GModule_EFFSA_timer_old = S2MgmStartTime;
    GModule_EFFLAM_timer_old = S2MgmStartTime;
    GModule_EFFCUTOFF_timer_old = S2MgmStartTime;
    G_CmiEst_timer_old = S2MgmStartTime;
    H_CmiDrvP_timer_old = S2MgmStartTime;
    H_CmfP_timer_old = S2MgmStartTime;
    I_TorqueComp_timer_old = S2MgmStartTime;
    L_RecoveriesVal_timer_old1= S2MgmStartTime;
    L_RecoveriesVal_timer_old2= S2MgmStartTime;
    L_RecoveriesVal_timer_old3= S2MgmStartTime;
    L_RecoveriesVal_timer_old4= S2MgmStartTime;
    L_RecoveriesVal_timer_old5= S2MgmStartTime;
    L_RecoveriesVal_timer_old6= S2MgmStartTime;
}

//----------------------------------------------------------
//  safety Engine Stop Routine
//----------------------------------------------------------
static void Saf2Mgm_Safety_Engine_Stop(void)
{
//------ S2_WAIT_KEYOFF -------

    //CALL DISABLE LOADS FUNCTION
    Saf2Mgm_DISLoads();
    //CALL RESET SMP FUNCTION

    S2KeyOffFlag = TRUE;


//------ S2_WAIT_KEYON -------
    if (!KeySignal) 
    {
        S2ForceShtDwn = TRUE;

      EEMGM_SetEventID(EE_BEFORE_SAF23_PWROFF);
    }
}
//----------------------------------------------------------
//  Safety 2 reset module timer routine
//----------------------------------------------------------
static void Saf2ResetModuleTimer(uint32_t *module_timer)
{
    if (S2FlgExec) 
    {
        *module_timer = S2MgmStartTime;
    }
}


//----------------------------------------------------------
//  Task 50ms Routine         
//----------------------------------------------------------
void FuncTask50ms(void)
{
//    TerminateTask();  // term. toni
}

void Saf2Task50ms(void)
{
    uint8_t  ind=0;
    uint64_t timer;
    static uint64_t timer_restart = 0;
    uint64_t perf_timer = 0;
    uint64_t perf_done_timer = 0;
    uint64_t perf_diff_timer = 0;
    uint8_T PtFaultSafety2 = NO_PT_FAULT;
    uint8_T StPtFaultSafety2 = NO_FAULT;
    uint8_T stdiagramsaf2 = NO_FAULT;

    TIMING_GetAbsTimer(&perf_timer);

    S2_DEL_STORED_RESULTS();
    
    switch (S2State) 
    {
        case S2_STATE_ENG_STOPPED:
            if (Rpm > S2RPMSTARTS2 && CntAbsTdc > 1 && InjEnable == 1) 
            {
                Saf2Mgm_EngineStart();
                S2State = S2_STATE_ENABLE;
            }  
        break;
        case S2_STATE_ENABLE:
            if (Rpm <= S2RPMSTOPS2 || InjEnable == 0) 
            {
                S2State = S2_STATE_ENG_STOPPED;
                SafMgm_ResErr();
            }
            else 
            {
                S2ExeCode = 0;
                S2FlgExec = TRUE;   /* flag essenziale per la safety 23 */
                Saf2Mgm_CrtlFlow();
                S2FlgExec = FALSE;
                PtFaultSafety2 = NO_PT_FAULT;
                for (ind=0;ind<SIZE_S2PUNERR;ind++) 
                {
                    if (S2ValErr[ind]>0)
                    {
                        /* Lurida porcata */
                        if (ind == IND_BUF)
                        {
                            DiagMgm_SetDiagState(DIAG_RAM,CIRCUIT_MALFUNCTION,&stdiagramsaf2);
                        }
                        /* Fine lurida porcata */
                        /* A simple way to track which module causes the reset first */
                        PtFaultSafety2 = SAFETY_2_FAULT;
                        S2PtFault = ind*10+S2ValErr[ind];
                        break; 
                    }
                  }
                if (PtFaultSafety2 != NO_PT_FAULT) 
                {
                    S2RestartCnt += (uint32_t) S2INCRESTARTCNT;
                    if (0x01 != S2PERFENABLE)
                    {
                        if (S2RestartCnt < (uint32_t) S2MAXRESTART) 
                        {
                            S2State = S2_STATE_DELAY_RESTART;
                            TIMING_GetAbsTimer(&timer_restart);
                        } 
                        else 
                        {
                            S2State = S2_STATE_WAIT_KEY_OFF;
                            S2RestartCnt = 0;
                        }
                    }
                }
                else if (S2RestartCnt>=1)
                {
                    S2RestartCnt--;
                }
		else
		{
		    /* Non fare niente. */
		}
                DiagMgm_SetDiagState(DIAG_SAFETY_2,PtFaultSafety2,&StPtFaultSafety2);
            }
        break;  /* End  S2State =  S2_STATE_ENABLE */
        case S2_STATE_DELAY_RESTART:
            //Delayed_Restart

            //CALL DISABLE LOADS FUNCTION
            Saf2Mgm_DISLoads();

            TIMING_GetAbsTimer(&timer);
            TIMING_TicksToMilliSeconds(timer - timer_restart, &timer);

            if (timer>S2DELAYRESTART) 
            {
                S2State = S2_STATE_RESTART_PROG;
            }
        break;
        case S2_STATE_RESTART_PROG:
            //Restart_Program
#ifdef  _BUILD_VSRAMMGM_
            VSRAMMGM_Update();
#endif /* _BUILD_VSRAMMGM_ */ 
            //CALL SHUTDOWN FUNCTION
          EEMGM_SetEventID(EE_BEFORE_SW_RESET_SAF2);
          ShutdownOS(E_OS_SYS_SAF2_SHTDWN); /*fc_shutdown*/
        break; 
        case S2_STATE_WAIT_KEY_OFF:
            Saf2Mgm_Safety_Engine_Stop();
        break;
	default:
	break;
    }

    TIMING_GetAbsTimer(&perf_done_timer);
    TIMING_TicksToMicroSeconds(perf_done_timer - perf_timer, &perf_diff_timer);

    if (perf_diff_timer < 65535)
    {
        S2PerfTime = (uint16_T) perf_diff_timer;
    }
    else
    {
        S2PerfTime = 65535;
    }
}

//----------------------------------------------------------
//  SAF2 Modules
//----------------------------------------------------------
static void M_Error_Counter(void)
{
    uint16_t  ind;
  
    for (ind=0;ind<SIZE_S2PUNERR;ind++) 
    {  
        if (S2PunErr[ind]>0) 
        {      
            S2ErrCnt[ind]++;
            
            if (S2ErrCnt[ind]>S2THS2ERRCNT)   
            {
                S2ValErr[ind] = S2PunErr[ind];
                S2ErrCnt[ind] = S2THS2ERRCNT+1;
            }      
        } 
        else 
        {
            S2ErrCnt[ind]=0;
        }
    }    
}

/* S2 Modules TBD */
static void A_Sensors_Val(void)
{
   uint16_t s2gasposmedian;
   uint16_t vtgaspos[3];
   uint16_t vtvgaspos[3];
   int32_t  tmpgaspos;
   uint16_t tmp;
   uint8_t  i=0,k=0;
   uint16_t s2athrpres;

   S2PunErr[IND_A] = NO_ERROR;
   
   // A1: PresIntake Validation
   S2AEstPresPoly = TBPRESINTAKE_Poly();
   
   FOF_Reset_S16_FXP( (int16_t *)&S2AEstPresIntake, &S2AEstPresIntakeHR, (int16_t)S2AEstPresPoly, S2AKFILTPRES, (int16_t)S2AEstPresPoly, 0, S2AEstPresIntakeHR);
      
   S2_STORE_RESULTS(STORE_A1)

   if ( (abs_delta(S2AEstPresIntake, S2AEstPresPoly) < S2ATHRPRESSTAB) && (S210msBuf.StThrRec_10ms<=1) )
   {
    s2athrpres = S2ATHRPRES;
   }
   else
   {
    s2athrpres = S2_DPRES_MAX;
   }

   if (abs_delta(S2AEstPresIntake, S210msBuf.PresIntake_10ms) > 
       s2athrpres) 
   {
       if (S2MgmStartTime - AModule_timer_old1 > (uint32_t)S2ATPRES) 
       {
         S2PunErr[IND_A] = S2ERR_A_PRESINTAKE;
       }
       
       S2_INC_COUNTER(STORE_A1)
   } 
   else 
   {
      Saf2ResetModuleTimer(&AModule_timer_old1);
   }
   
   S2Result[IND_A] = S2AEstPresPoly;
   S2ResultNeg[IND_A] = ~(S2Result[IND_A]);
   
   // A2: Gas Position Validation
   vtvgaspos[0] =  S210msBuf.VGasPos1_10ms;
   vtvgaspos[1] =  S210msBuf.VGasPos2_10ms;
   vtvgaspos[2] =  S210msBuf.VGasPos3_10ms;
   
    for(i=0;i<S2ASTCONFGAS;i++) 
    {      
        if ((vtvgaspos[i]>S2ATHRVGASL) && (vtvgaspos[i]<S2ATHRVGASH)) 
        {
            tmpgaspos = (uint32_t)(vtvgaspos[i]) - (uint32_t)(S2AGASPOSOFFSET[i]);
            tmpgaspos = ((tmpgaspos * S2AGASPOSGAIN[i]>> 8) * 625) >> 9;
            if (tmpgaspos > S2_MAX_PERC) 
            {
                tmpgaspos = S2_MAX_PERC;
            } 
            else if (tmpgaspos < 0) 
            {
                tmpgaspos = 0;
            }
	    else
	    {
		/* Non fare niente. */
	    }
            vtgaspos[k] = (uint16_t) tmpgaspos;
            k++;
        }
    }
   
   switch(k) {
    case 1:
      s2gasposmedian = vtgaspos[0];
    break;
    case 2:
      if (vtgaspos[1] < vtgaspos[0]) 
      {
        s2gasposmedian = vtgaspos[1];
      } 
      else 
      {
        s2gasposmedian = vtgaspos[0];
      }
    break;
    case 3:
      if (vtgaspos[1] < vtgaspos[0]) 
      {
        tmp = vtgaspos[0];
        vtgaspos[0] = vtgaspos[1];
        vtgaspos[1] = tmp;
      }
      if (vtgaspos[2] < vtgaspos[0]) 
      {
        tmp = vtgaspos[0];
        vtgaspos[0] = vtgaspos[2];
        vtgaspos[2] = tmp;  
      }
      if (vtgaspos[2] < vtgaspos[1]) 
      {
        tmp = vtgaspos[1];
        vtgaspos[1] = vtgaspos[2];
        vtgaspos[2] = tmp;  
      }
      s2gasposmedian = vtgaspos[1];
    break;
    default:
      s2gasposmedian = 0;
    break;
    
   }

   S2AEstGasPos = s2gasposmedian;
 
   /* Gas Position Validation Error detect */

    S2_STORE_RESULTS(STORE_A2)

   /* Calcola soglia in base al flag FlgEOL */
   if (S210msBuf.FlgEOL_10ms == 1)
   {
    tmp = S2_MAX_PERC;
   }
   else
   {
    tmp = S2ATHRGASPOS;
   }

    if (S210msBuf.GasPos_10ms  > ( tmp + S2AEstGasPos)) 
    {
        if (S2MgmStartTime - AModule_timer_old2 > (uint32_t)S2ATGASPOS) 
        {
          S2PunErr[IND_A] = S2ERR_A_GASPOS;
        }
        
        S2_INC_COUNTER(STORE_A2)
    } 
    else 
    {
      Saf2ResetModuleTimer(&AModule_timer_old2);
    }
          
    S2AEstTWater = S2TWaterModel();

   // A3: TAir Validation
   if ( (S210msBuf.TAir_10ms > S2ATAIRMAX) || 
      (S210msBuf.TAir_10ms < S2ATAIRMIN)     ) 
   {
        S2PunErr[IND_A] = S2ERR_A_TAIR;
   } 
   
   // A4: PresAtm Validation
   if ( (S210msBuf.PresAtm_10ms > S2APRESATMMAX) || 
      (S210msBuf.PresAtm_10ms < S2APRESATMMIN)     ) 
   {
    S2PunErr[IND_A] = S2ERR_A_PRESATM;
   } 
}

static int16_t S2TWaterModel(void)
{
    int16_t ans; // 2^-4
    int64_t temp; 
    
    //****Coolant Temperature Model
    temp = ((int64_t)S210msBuf.Rpm_10ms * (int64_t)S210msBuf.CmiPotEst_10ms) >> 5; // 2^-5 >> 5 = 2^0
    temp = ((temp * (int64_t)S2APOW2TEMP) >> 18);  // 2^0 * 2^-34 = 2^-34 >> 18 = 2^-16
    temp = (temp + (int64_t)S2ATWATERINC);  // 2^-16
    
    temp = ((int64_t)S2AEstTWater_HR + temp); // 2^-16

    if (temp  > (int64_t)S2ATWATERMAX << 12) 
    {
        S2AEstTWater_HR = (int32_t)S2ATWATERMAX << 12;
    } 
    else 
    {
        S2AEstTWater_HR = (int32_t) temp;
    }

    ans = (int16_t)(S2AEstTWater_HR >> 12);
    
    return (ans);
}

static void B_Actuactors_Val(void)
{
    uint8_t resflg=0;

    S2PunErr[IND_B] = NO_ERROR;

    if ( ( (S210msBuf.LambdaError_10ms > S2BTHRLAMERR) && (S210msBuf.DeltaLamCL_10ms < -S2BTHRDELTALAMCL) ) ||
         ( (S210msBuf.LambdaError_10ms < -S2BTHRLAMERR) && (S210msBuf.DeltaLamCL_10ms > S2BTHRDELTALAMCL) )  )
    {
        if ( (S2MgmStartTime - BModule_timer_old1) >= (uint32_t)S2BTVALERR ) 
        {
            S2PunErr[IND_B] = S2ERR_B_ENERLAM;
        }
        resflg = 1;
    } 
    else 
    {
        Saf2ResetModuleTimer(&BModule_timer_old1);
    }

    if ( 1==S210msBuf.CutOffFlg_10ms && S210msBuf.InjTimePrgCyl_10ms>0 )
    {
        if ( (S2MgmStartTime - BModule_timer_old2) >= (uint32_t)S2BTVALERR ) 
        {
            S2PunErr[IND_B] = S2ERR_B_CUTOFF;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&BModule_timer_old2);
    }
    
    S2Result[IND_B] = resflg;
    S2ResultNeg[IND_B] = ~(S2Result[IND_B]);
    
}  

static void C_CAN_Val(void)
{
    S2Result[IND_C] = 1;
    S2ResultNeg[IND_C] = ~(S2Result[IND_C]);
    S2PunErr[IND_C] = NO_ERROR;
}

/* Manca la TrqSource */

static void D_DriverTorqueRequest_Val(void)
{
    S2PunErr[IND_D] = NO_ERROR;
  
    if (S2USECMECAN == GAS) 
    {
        S2DEstCmeGasRpm = TBCMEDRIV_Poly();
    
        S2_STORE_RESULTS(STORE_D1)
        
        if (abs_delta(S2DEstCmeGasRpm, S210msBuf.CmeGasRpm_10ms) > S2DTHRTRQ) 
        {
            if (S2MgmStartTime - DModule_timer_old > (uint32_t)S2DTTRQ) 
            {
                S2PunErr[IND_D] = S2ERR_D_DRIVREQ;
            }
            
            S2_INC_COUNTER(STORE_D1)
        } 
        else 
        {
            Saf2ResetModuleTimer(&DModule_timer_old);
        }    
    }  

    S2Result[IND_D] = S2DEstCmeGasRpm;
    S2ResultNeg[IND_D] = ~(S2Result[IND_D]);

}

/* Manca la TrqSource */

static void E_ExternalTorqueRequest_Val(void)
{
    S2PunErr[IND_E] = NO_ERROR;
    
    if (S2USECMECAN == CAN) 
    {    
        S2EEstCmeTargCAN = Estimated_Driver_Torque();
        
        //E Module Error detect
      
        S2_STORE_RESULTS(STORE_E1)
        
        if ((S2EEstCmeTargCAN - S210msBuf.CmeDriverCANF_10ms) > S2ETHRCANREQ) 
        {
            if (S2MgmStartTime - EModule_timer_old > (uint32_t)S2ETCANREQ) 
            {
                S2PunErr[IND_E] = S2ERR_E_CANREQ;
            } 
            
            S2_INC_COUNTER(STORE_E1)
        } 
        else 
        {
            Saf2ResetModuleTimer(&EModule_timer_old);
        }    
    }
    
    S2Result[IND_E] = S2EEstCmeTargCAN;
    S2ResultNeg[IND_E] = ~(S2Result[IND_E]);
    
}

static void F_Idle_Val(void)
{
    uint8_t  ResFlg = 0;
  
    S2PunErr[IND_F] = NO_ERROR;

    if (S210msBuf.CmeDriver_10ms < S2FCMEINIDLE && 0==S210msBuf.IdleReqFlg_10ms)
    {
        S2PunErr[IND_F] = S2ERR_F_WRONG_IDLEREQ;
    }
        
   
    if ( S210msBuf.IdleReqFlg_10ms && S210msBuf.IdleFlg_10ms && (S210msBuf.Rpm_10ms > S2FTHRPMIDLE) ) 
    {
        if ( ((S2MgmStartTime - FModule_timer_old) >= (uint32_t)S2FTVALERR) &&
              (S210msBuf.CmiIdleP_10ms >= S210msBuf.CmiSafP_10ms)) 
        {
            S2PunErr[IND_F] = S2ERR_F_TOOFAST_IDLE;
        }
        
        ResFlg = 1;
    } 
    else 
    {
        Saf2ResetModuleTimer(&FModule_timer_old);
    }
    
    S2Result[IND_F] = ResFlg;
    S2ResultNeg[IND_F] = ~(S2Result[IND_F]);
}


static void G_GeneratedTorque_Val(void)
{    
    uint64_t temp_qac, temp_corr;
    
  // G1: QAirAvg_Validation********************
    S2PunErr[IND_G] = NO_ERROR;
    //QAirAvg_Model
    S2GTBQACPoly = TBQAC_polynomial(S210msBuf.PresIntake_10ms);
    
    S2GVTAIRCORRPoly = VTAIRCORR_polynomial();
    S2GTBWATERCORRPoly = TBWATERCORR_polynomial();
    S2GVTPATMCORRPoly = PATMCORR_polynomial();

    temp_corr = (uint64_t)S2GVTAIRCORRPoly * (uint64_t)S2GTBWATERCORRPoly *
                  (uint64_t)(S210msBuf.PresIntake_10ms) * (uint64_t)(S2GVTPATMCORRPoly);
    temp_qac = temp_corr * (uint64_t)(S2GTBQACPoly);

    S2GEstQAirBase = (uint16_t)(temp_qac >> 42);
    
    S2GEstQAirAvg = S2GEstQAirBase;
    
    //G_Air_Error_Mgm
    
    S2_STORE_RESULTS(STORE_G1)
  
    if (S2GEstQAirAvg  > (S2GTHRQAIR + S210msBuf.QAirBaseAvg_10ms)) 
    {
        if (S2MgmStartTime - GAirModule_timer_old > (uint32_t)S2GTQAIR) 
        {
            S2PunErr[IND_G] = S2ERR_G_QAIR;
        }
        S2_INC_COUNTER(STORE_G1)
    } 
    else 
    {
        Saf2ResetModuleTimer(&GAirModule_timer_old);
    }   
    
  // G2: EffSAReal_Validation********************
  
    //Func_Range_Detect + GAout_Error_Mgm
    if ((S210msBuf.SAout_10ms > S2GSAMAX) || (S210msBuf.SAout_10ms < S2GSAMIN)) 
    {
        S2PunErr[IND_G] = S2ERR_G_SAOUT;
    }
    
    /* SAopt_Model */
    S2GTBSAOPTPoly = TBSAOPT_polynomial();
    /* S2GTBSATEMPPoly = TBSATEMP_polynomial(); */

    S2GEstSAopt = S2GTBSAOPTPoly;
  
    //G_SAopt_Error_Mgm
    S2_STORE_RESULTS(STORE_G2)
    
    if (abs_delta(S210msBuf.SAopt_10ms, S2GEstSAopt) > S2GTHRSAOPT) 
    {
        if (S2MgmStartTime - GModule_SAopt_timer_old > (uint32_t)S2GTSAOPT) 
        {
            S2PunErr[IND_G] = S2ERR_G_SAOPT;
        }
        
        S2_INC_COUNTER(STORE_G2)
    } 
    else 
    {
        Saf2ResetModuleTimer(&GModule_SAopt_timer_old);
    }
                 
    //EFFSAReal_Model
    
    S2GEstEffSAreal = EFFSA_polynomial();

    //G_EFFSA_Error_Mgm

    S2_STORE_RESULTS(STORE_G3)

    if (abs_delta(S210msBuf.EffSAReal_10ms, S2GEstEffSAreal) > S2GTHREFFSA) 
    {
        if (S2MgmStartTime - GModule_EFFSA_timer_old > (uint32_t)S2GTEFFSA) 
        {
            S2PunErr[IND_G] = S2ERR_G_EFFSA;
        }
        
        S2_INC_COUNTER(STORE_G3)
    } 
    else 
    {
        Saf2ResetModuleTimer(&GModule_EFFSA_timer_old);
    }
  
  // G3: EffLambda_Validation********************
    
    //Func_Range_Detect + G_LamObj_Error_Mgm
    if ( (S210msBuf.LamObj_10ms > S2GLAMOBJMAX) ||
        (S210msBuf.LamObj_10ms < S2GLAMOBJMIN)   )
    {      
      S2PunErr[IND_G] = S2ERR_G_LAMOBJ;
    }
       
     //EFFLam_Model
    S2GEstEffLambda = EFFLam_polynomial();

     //G_EffLam_Error_Mgm  

    S2_STORE_RESULTS(STORE_G4)

    if (abs_delta(S210msBuf.EffLambda_10ms, S2GEstEffLambda) > S2GTHREFFLAM) 
    {
        if (S2MgmStartTime - GModule_EFFLAM_timer_old > (uint32_t)S2GTEFFLAM) 
        {
            S2PunErr[IND_G] = S2ERR_G_EFFLAM;
        }
        
        S2_INC_COUNTER(STORE_G4)
    } 
    else 
    {
        Saf2ResetModuleTimer(&GModule_EFFLAM_timer_old);
    }
     
  // G4: EffCutoff_Validation********************
    if (S210msBuf.CutOffFlg_10ms == TRUE)
    {
        S2GEstEffCutoff = 0;
    }
    else
    {
        S2GEstEffCutoff = MAXEFF;
    }
    
    //G_EffCutOff_Error_Mgm

    S2_STORE_RESULTS(STORE_G5)

    if (abs_delta(S210msBuf.EffCutoff_10ms, S2GEstEffCutoff) > S2GTHREFFCTF) 
    {
        if (S2MgmStartTime - GModule_EFFCUTOFF_timer_old > (uint32_t)S2GTEFFCTF) 
        {
            S2PunErr[IND_G] = S2ERR_G_EFFCTF;
        }
        
        S2_INC_COUNTER(STORE_G5)
    } 
    else 
    {
        Saf2ResetModuleTimer(&GModule_EFFCUTOFF_timer_old);
    }
    
    //CmiEstimation and Error Mgm********************
    S2GEstCmiEst = CmiEstimation();
        
    //G_CmiEst_Error_Mgm

    S2_STORE_RESULTS(STORE_G6)
  
    if ((S2GEstCmiEst - S210msBuf.CmiEst_10ms) > S2GTHRCMIEST) 
    {
        if (S2MgmStartTime - G_CmiEst_timer_old > (uint32_t)S2GTCMIEST) 
        {
          S2PunErr[IND_G] = S2ERR_G_CMIEST;
        }
        
        S2_INC_COUNTER(STORE_G6)
    } 
    else 
    {
        Saf2ResetModuleTimer(&G_CmiEst_timer_old);
    }   
    
    if (S2GEstCmiEst < S210msBuf.CmiEst_10ms) 
    {
      S2GEstCmiEstMax = S210msBuf.CmiEst_10ms;  
    }
    else
    {
      S2GEstCmiEstMax = S2GEstCmiEst;
    }
    
    S2Result[IND_G] = S2GEstCmiEstMax;
    S2ResultNeg[IND_G] = ~(S2Result[IND_G]);
}

static void H_RequestedTorque_Val(void) 
{ 
    uint16_t H_out;
  
    /* H1: CmeDriver_Validation********************/
    
    S2PunErr[IND_H] = NO_ERROR;

    if (S2USECMECAN == GAS) 
    {
      
        if (S210msBuf.CmeDriver_10ms > S210msBuf.CmeGasRpm_10ms) 
        {
            S2PunErr[IND_H] = S2ERR_H_CMEDRIV;
        }
      
    }     
    else if (S2USECMECAN == CAN) 
    {
      
        if (S210msBuf.CmeDriver_10ms > S210msBuf.CmeDriverCANF_10ms ) 
        {
            S2PunErr[IND_H] = S2ERR_H_CMEDRIV;
        }
      
    } 
    else 
    {
        S2PunErr[IND_H] = S2ERR_H_WRONG_TRQ;
    }
    
  
    // H2: CmiDriverP_Validation******************** OK
  
    //CmiDriverP_Model
    S2HEstCmiDriverP = S210msBuf.CmeDriverP_10ms + S210msBuf.CmfP_10ms;
    //CmiDriverP_Error_Mgm

    S2_STORE_RESULTS(STORE_H1)

    if (S210msBuf.CmiDriverP_10ms - S2HEstCmiDriverP > S2HTHRCMIP ) 
    {
        if (S2MgmStartTime - H_CmiDrvP_timer_old > (uint32_t)S2HTCMIP) 
        {
            S2PunErr[IND_H] = S2ERR_H_CMIDRIV;
        }
        
        S2_INC_COUNTER(STORE_H1)
    } 
    else 
    {
        Saf2ResetModuleTimer(&H_CmiDrvP_timer_old);
    }

         

    if (S2HEstCmiDriverP < 0)
    {
        H_out = 0;
    }
    else
    {
        H_out = (uint16_t)S2HEstCmiDriverP;
    }

    S2Result[IND_H] = H_out;
    S2ResultNeg[IND_H] = ~(S2Result[IND_H]);
    
  // H3: CmfP_Validation********************
  
    if (S2AEstTWater > S210msBuf.TWater_10ms) 
    {
      S2HTWaterMax = S2AEstTWater;
    } 
    else 
    {
      S2HTWaterMax = S210msBuf.TWater_10ms;
    }
    //CmfP_Model

    S2HTBCMFPoly = TBCMF_polynomial();

    S2HTBCMFTWPoly = TBCMFTW_polynomial();
    
    S2HEstCmfP = S2HTBCMFPoly + S2HTBCMFTWPoly;

    //CmfP_Error_Mgm

    S2_STORE_RESULTS(STORE_H2)

    if (S210msBuf.CmfP_10ms - S2HEstCmfP > S2HTHRCMF ) 
    {
        if (S2MgmStartTime - H_CmfP_timer_old > (uint32_t)S2HTCMF) 
        {
            S2PunErr[IND_H] = S2ERR_H_CMF;
        }
        S2_INC_COUNTER(STORE_H2)
    } 
    else 
    {
        Saf2ResetModuleTimer(&H_CmfP_timer_old);
    }        
  
  // H4: CmiTargetP_Validation + CmiTargetP_Error_Mgm********************
    if (( (S210msBuf.CmiTargetP_10ms > S210msBuf.CmiIdleP_10ms) &&
          (S210msBuf.CmiTargetP_10ms > S210msBuf.CmiTargetPMin_10ms) ) ||
        (S210msBuf.CmiSafP_10ms > S210msBuf.CmiDriverP_10ms) ) 
    {
        S2PunErr[IND_H] = S2ERR_H_CMI;
    }
  
}

static void I_TorqueCompare(void)
{
    uint16_t    relerrthr;
    uint16_T    minthr;

    //TorqueCompare_Error_Mgm  
    S2PunErr[IND_I] = NO_ERROR;

    if (S210msBuf.CmiTargetP_10ms <= S210msBuf.CmiTargetPMin_10ms)
    {
        minthr = max(S2IMINTHRCMIMIN,S2IMINTHRCMI);
    }
    else
    {
        minthr = S2IMINTHRCMI;
    }

    relerrthr = (uint16_t) ((uint32_t) (S2IGNTHRCMI * S210msBuf.CmiTargetP_10ms)>>14);
    if (relerrthr<minthr)
    {
        relerrthr = minthr;
    }

    if ( S2GEstCmiEstMax > ((int16_t)relerrthr + S210msBuf.CmiTargetP_10ms) ) 
    {
        if (S2MgmStartTime - I_TorqueComp_timer_old > (uint32_t)S2ITCMI) 
        {
          S2PunErr[IND_I] = S2ERR_I_TOOHIGH_TORQUE;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&I_TorqueComp_timer_old);
    }
}

static void L_Recoveries_Val(void)
{
    int16_t s2cmimax;

    S2PunErr[IND_L] = NO_ERROR;

    // L1: REC_NO_GAS__Validation
    if (S210msBuf.VtRec_NO_GAS_10ms != 0) 
    {
        if ( (S210msBuf.GasPos_10ms != 0) && 
             (S2MgmStartTime - L_RecoveriesVal_timer_old1 > (uint32_t)S2LVALGASPOS) ) 
        {
            S2PunErr[IND_L] = S2ERR_L_PEDAL_NOT_0;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&L_RecoveriesVal_timer_old1);
    }

    /*  L2: REC_DBW_OFF Validation */
    if (S210msBuf.VtRec_DBW_OFF_10ms != 0 && S210msBuf.FlgEnHBridge_10ms == 1)
    {
    S2PunErr[IND_L] = S2ERR_L_LIMPHOME;
    }
    
    /*  L2: REC_DBW_OFF Validation */
    if (S210msBuf.VtRec_FORCE_LH_10ms != 0 &&
        ((S210msBuf.FlgEnHBridge_10ms == 1) || (S210msBuf.AngThrottle_10ms > S2LTHRMAXLH)))
    {
        if (S2MgmStartTime - L_RecoveriesVal_timer_old6 > (uint32_t)S2LVALLLH)
        {
            S2PunErr[IND_L] = S2ERR_L_LIMPHOME;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&L_RecoveriesVal_timer_old6);
    }

    /* L3: REC_ENG_OFF_Validation */

    if (S210msBuf.VtRec_ENG_OFF_10ms != 0) 
    {
        if ( (S210msBuf.LoadCmd_10ms == LOAD_OFF) && 
            (S2MgmStartTime - L_RecoveriesVal_timer_old2 > (uint32_t)S2LVALENGOFF )) 
        {
            S2PunErr[IND_L] = S2ERR_L_LOADCMD_NOT_0;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&L_RecoveriesVal_timer_old2);
    }

    // L4: REC_x_TRQ_RED_Validation
    if (S210msBuf.VtRec_SL_T_R_10ms != 0) 
    {
        s2cmimax = S2LCMEMAXS + S210msBuf.CmfP_10ms;
    } 
    else if (S210msBuf.VtRec_HV_T_R_10ms != 0) 
    {
        s2cmimax = S2LCMEMAXH + S210msBuf.CmfP_10ms;
    } 
    else 
    {
        s2cmimax = CME_MAX;
    }

    if (S210msBuf.CmiSafP_10ms > s2cmimax + S2LTHRTRQRED) 
    {
        if (S2MgmStartTime - L_RecoveriesVal_timer_old3 > (uint32_t)S2LTTRQRED) 
        {
            S2PunErr[IND_L] = S2ERR_L_TRQ_RED;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&L_RecoveriesVal_timer_old3);
    }

    // L5: REC_LIMIT_RPM__Validation
    if ( (1==S210msBuf.CtfLimiterFlg_10ms && 0==S210msBuf.CutOffFlg_10ms) ||
          (S210msBuf.Rpm_10ms > S210msBuf.RpmMaxCorr_10ms && S210msBuf.CmiTargetP_10ms >= S210msBuf.CmiIdleP_10ms && S210msBuf.CmiTargetP_10ms > S210msBuf.CmiTargetPMin_10ms) ) 
    {
        if ( (S2MgmStartTime - L_RecoveriesVal_timer_old4) > (uint32_t)S2LVALLIM ) 
        {
            S2PunErr[IND_L] = S2ERR_L_RPMLIM;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&L_RecoveriesVal_timer_old4);
    }

    // L6: REC_LIMIT_VEH_SPEED__Validation
    if ( (1==S210msBuf.CtfVSpeedLimFlg_10ms && 0==S210msBuf.CutOffFlg_10ms) ||
          (S210msBuf.VehSpeed_10ms> S210msBuf.VehSpeedLimCAN_10ms && S210msBuf.CmiSpeedP_10ms >= S210msBuf.CmiReqP_10ms && 1==S210msBuf.VtRec_REC_LIMIT_VEH_SPEED_10ms) ) 
    {
        if ( (S2MgmStartTime - L_RecoveriesVal_timer_old5) > (uint32_t)S2LVALLIM ) 
        {
            S2PunErr[IND_L] = S2ERR_L_VSPEEDLIM;
        }
    } 
    else 
    {
        Saf2ResetModuleTimer(&L_RecoveriesVal_timer_old5);
    }
}

//A_Sensors_Val Sub-Routines
static uint16_t TBPRESINTAKE_Poly (void) 
{  
  
  /* local block i/o variables*/
  uint16_T rtb_Enforceupperlimit_a;

  {
    int16_T rtb_s16_tmp;
    uint16_T rtb_u16_tmp;

    rtb_u16_tmp = rt_MAX(S2ATBPRESANGMIN, S210msBuf.AngThrottle_10ms);
    rtb_Enforceupperlimit_a = rt_MIN(rtb_u16_tmp, S2ATBPRESANGMAX);

    rtb_u16_tmp = rt_MAX(S2AGTBPRESRPMMIN, S210msBuf.Rpm_10ms);

    rtb_u16_tmp = rt_MIN(rtb_u16_tmp, S2AGTBPRESRPMMAX);

    rtb_s16_tmp = (int16_T)((((((((S2ACOEFR1A2 * rtb_u16_tmp >> 11) *
      rtb_Enforceupperlimit_a >> 11) >> 2) + ((S2ACOEFR0A2 *
      rtb_Enforceupperlimit_a >> 8) + (S2ACOEFR0A1 << 2U))) +
      (((uint16_T)((uint32_T)rtb_Enforceupperlimit_a *
      (uint32_T)rtb_Enforceupperlimit_a >> 6) * S2ACOEFR0A3 >> 11) >> 3)) *
      rtb_Enforceupperlimit_a >> 11) + ((((((S2ACOEFR2A0 * rtb_u16_tmp >> 11) +
      (S2ACOEFR1A0 << 2U)) + ((S2ACOEFR2A1 * rtb_Enforceupperlimit_a >> 7) *
      rtb_u16_tmp >> 15)) >> 1) + (((uint16_T)((uint32_T)rtb_u16_tmp *
      (uint32_T)rtb_u16_tmp >> 12) * S2ACOEFR3A0 >> 13) >> 1)) * rtb_u16_tmp >>
      13)) + ((((uint16_T)((uint32_T)rtb_Enforceupperlimit_a *
      (uint32_T)rtb_u16_tmp >> 13) * S2ACOEFR1A1 >> 9) >> 1) + S2ACOEFR0A0));
    if(rtb_s16_tmp <= 0) {
      rtb_u16_tmp = 0U;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_s16_tmp;
    }

    return(rtb_u16_tmp);
  }
}

//D_DriverTorqueRequest_Val Sub-Routines
static int16_t TBCMEDRIV_Poly (void) 
{
    uint16_T rtb_Switch;
    uint16_T rtb_Switch_o;
    uint16_T rtb_Product12;
    uint16_T rtb_Product6;

    int32_T tmp;
    int16_T tmp_0;

    if (S210msBuf.GasPos_10ms > S2DTBCMEGASMAX)
    {
        rtb_Switch_o = S2DTBCMEGASMAX;
    }
    else
    {
        if (S210msBuf.GasPos_10ms < S2DTBCMEGASMIN)
        {
            rtb_Switch_o = S2DTBCMEGASMIN;
        }
        else
        {
            rtb_Switch_o = S210msBuf.GasPos_10ms;
        }
    }

    if (S210msBuf.Rpm_10ms > S2DTBCMERPMMAX)
    {
        rtb_Switch = S2DTBCMERPMMAX;
    }
    else
    {
        if (S210msBuf.Rpm_10ms < S2DTBCMERPMMIN)
        {
            rtb_Switch = S2DTBCMERPMMIN;
        }
        else
        {
            rtb_Switch = S210msBuf.Rpm_10ms;
        }
    }

    rtb_Product6 = (uint16_T)((uint32_T)rtb_Switch * (uint32_T)rtb_Switch_o >> 9);

    rtb_Product12 = (uint16_T)((uint32_T)rtb_Switch * (uint32_T)rtb_Switch >> 12);

    tmp = (((((((((((uint16_T)((uint32_T)rtb_Switch_o * (uint32_T)rtb_Switch_o >>
    6) * S2DCOEFG2R0 >> 1) + (S2DCOEFG0R2 * rtb_Product12 >> 2)) >> 2) +
    (((S2DCOEFG0R1 * rtb_Switch >> 1) + (S2DCOEFG1R1 * rtb_Product6
    >> 2)) >> 1)) >> 3) + (S2DCOEFG1R0 * rtb_Switch_o >> 1)) >> 3) + ((int16_T)
    (((int16_T)(S2DCOEFG2R1 * rtb_Product6 >> 16) + (int16_T)
    (S2DCOEFG1R2 * rtb_Product12 >> 18)) >> 1) * rtb_Switch_o >> 1))
    >> 1) + (S2DCOEFG0R0 << 6U)) >> 6;
    
    if (tmp > 32767)
    {
        tmp_0 = MAX_int16_T;
    }
    else if (tmp <= -32768)
    {
        tmp_0 = MIN_int16_T;
    }
    else
    {
        tmp_0 = (int16_T)tmp;
    }

    return(tmp_0);
}

//E_ExternalTorqueRequest_Val Sub-Routines
  
static int16_t Estimated_Driver_Torque(void) 
{
    uint16_T rtb_u16_max;
    uint16_T rtb_u16_min;
    int32_T rtb_s32_tmp;
    int16_T rtb_s16_tmp;

    rtb_u16_max = rt_MAX(S2ETBCMERPMMIN, S210msBuf.Rpm_10ms);

    rtb_u16_min = rt_MIN(rtb_u16_max, S2ETBCMERPMMAX);

    rtb_u16_max = rt_MAX(S2ETBCMEGASMIN, S210msBuf.GasPos_10ms);
    rtb_u16_max = rt_MIN(rtb_u16_max, S2ETBCMEGASMAX);

    rtb_s32_tmp = (((((((((S2ECOEFG2R1 * rtb_u16_min >> 12) >> 1) + (S2ECOEFG2R0
      >> 1)) * (uint16_T)((uint32_T)rtb_u16_max * (uint32_T)rtb_u16_max >> 6) >>
      2) + ((((S2ECOEFG1R2 * rtb_u16_max >> 10) >> 1) + (S2ECOEFG0R2 >> 1)) *
      (uint16_T)((uint32_T)rtb_u16_min * (uint32_T)rtb_u16_min >> 12) >> 3)) >>
      5) + ((((((S2ECOEFG1R1 * rtb_u16_min >> 14) >> 1) + (S2ECOEFG1R0 >> 1)) *
      rtb_u16_max >> 1) + (S2ECOEFG0R1 * rtb_u16_min >> 6)) >> 1)) >> 1) +
      ((S2ECOEFG0R0 << 8U) >> 1)) >> 7;
    if(rtb_s32_tmp > 32767) {
      rtb_s16_tmp = MAX_int16_T;
    } else if(rtb_s32_tmp <= -32768) {
      rtb_s16_tmp = MIN_int16_T;
    } else {
      rtb_s16_tmp = (int16_T)rtb_s32_tmp;
    }

    return(rtb_s16_tmp);
    
}

//G_GeneratedTorque_Val Sub-Routines
static uint16_t TBQAC_polynomial(uint16_t PrIn) 
{
    /* local block i/o variables*/
    int16_T rtb_DataTypeConversion1;
    int16_T rtb_DataTypeConversion2;

    int32_T rtb_s32_tmp;
    uint16_T rtb_u16_tmp;

    rtb_u16_tmp = rt_MAX(S2GTBQACPRESMIN, PrIn);

    rtb_DataTypeConversion1 = (int16_T)rt_MIN(rtb_u16_tmp, S2GTBQACPRESMAX);

    rtb_u16_tmp = rt_MAX(S2GTBQACRPMMIN, S210msBuf.Rpm_10ms);

    rtb_DataTypeConversion2 = (int16_T)rt_MIN(rtb_u16_tmp, S2GTBQACRPMMAX);

    rtb_s32_tmp = ((((((((((((S2GCOEFQAIRP1R2 * rtb_DataTypeConversion1 >> 11)
      >> 1) + (S2GCOEFQAIRP0R2 >> 1)) >> 1) + ((S2GCOEFQAIRP0R3 *
      rtb_DataTypeConversion2 >> 14) >> 1)) * (rtb_DataTypeConversion2 *
      rtb_DataTypeConversion2 >> 12) >> 1) + ((rtb_DataTypeConversion1 *
      rtb_DataTypeConversion2 >> 9) * S2GCOEFQAIRP1R1 >> 1)) >> 2) +
      ((((((S2GCOEFQAIRP2R1 * rtb_DataTypeConversion2 >> 14) >> 1) +
      ((S2GCOEFQAIRP3R0 * rtb_DataTypeConversion1 >> 11) >> 1)) >> 1) +
      (S2GCOEFQAIRP2R0 >> 1)) * (rtb_DataTypeConversion1 *
      rtb_DataTypeConversion1 >> 6) >> 1)) >> 3) + (S2GCOEFQAIRP1R0 *
      rtb_DataTypeConversion1 >> 1)) >> 8) + S2GCOEFQAIRP0R0;
    if(rtb_s32_tmp <= 0) {
      rtb_u16_tmp = 0U;
    } else if(rtb_s32_tmp > 65535) {
      rtb_u16_tmp = MAX_uint16_T;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_s32_tmp;
    }


    return(rtb_u16_tmp);      
}
    
static uint16_t VTAIRCORR_polynomial(void) 
{  
    int16_T rtb_s16_rtmax;
    uint16_T rtb_u16_tmp;

    rtb_s16_rtmax = rt_MAX(S2GBKAIRCORRMIN, S210msBuf.TAir_10ms);
    rtb_s16_rtmax = rt_MIN(rtb_s16_rtmax, S2GBKAIRCORRMAX);

    rtb_s16_rtmax = (int16_T)(((((((rtb_s16_rtmax * rtb_s16_rtmax >> 9) *
      S2GCOEFTAIR3 >> 9) >> 5) + ((S2GCOEFTAIR2 * rtb_s16_rtmax >> 10) >> 1)) *
      rtb_s16_rtmax >> 11) + (S2GCOEFTAIR1 * rtb_s16_rtmax >> 9)) +
      S2GCOEFTAIR0);
    if(rtb_s16_rtmax <= 0) {
      rtb_u16_tmp = 0U;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_s16_rtmax;
    }
    return(rtb_u16_tmp);
}


static uint16_t TBWATERCORR_polynomial(void) 
{
    /* local block i/o variables*/
    int16_T rtb_Product8;

    int16_T rtb_s16_rtmax;
    uint16_T rtb_u16_tmp;

    rtb_s16_rtmax = rt_MAX(S2GBKWATERCORRMIN, S210msBuf.TWater_10ms);
    rtb_s16_rtmax = rt_MIN(rtb_s16_rtmax, S2GBKWATERCORRMAX);

    rtb_Product8 = (int16_T)(rtb_s16_rtmax * rtb_s16_rtmax >> 13);

    rtb_u16_tmp = rt_MAX(S2AGTBPRESRPMMIN, S210msBuf.Rpm_10ms);

    rtb_u16_tmp = rt_MIN(rtb_u16_tmp, S2AGTBPRESRPMMAX);

    rtb_s16_rtmax = (int16_T)((((((rtb_s16_rtmax * rtb_u16_tmp >> 14) *
      S2GCOEFTW1R1 >> 8) + ((rtb_Product8 * rtb_u16_tmp >> 10) * S2GCOEFTW2R1 >>
      9)) + ((S2GCOEFTW1R0 * rtb_s16_rtmax >> 11) + (S2GCOEFTW2R0 * rtb_Product8
      >> 7))) >> 2) + S2GCOEFTW0R0);
    if(rtb_s16_rtmax <= 0) {
      rtb_u16_tmp = 0U;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_s16_rtmax;
    }

    return(rtb_u16_tmp);
}
  

static uint16_t PATMCORR_polynomial(void) 
{
    uint16_T rtb_u16_max;
    int16_T rtb_s16_tmp;

    rtb_u16_max = rt_MAX(S2GBKPATMCORRMIN, S210msBuf.PresAtm_10ms);

    rtb_u16_max = rt_MIN(rtb_u16_max, S2GBKPATMCORRMAX);

    rtb_s16_tmp = (int16_T)((((int16_T)((uint32_T)rtb_u16_max *
      (uint32_T)rtb_u16_max >> 6) * S2GCOEFPATM2 >> 20) + ((S2GCOEFPATM1 *
      rtb_u16_max >> 12) >> 3)) + (S2GCOEFPATM0 >> 4));
    if(rtb_s16_tmp <= 0) {
      rtb_u16_max = 0U;
    } else if(rtb_s16_tmp > 4095) {
      rtb_u16_max = MAX_uint16_T;
    } else {
      rtb_u16_max = (uint16_T)((uint16_T)rtb_s16_tmp << 4);
    }
    return(rtb_u16_max);
}

static int16_t TBSAOPT_polynomial(void) 
{
    int16_T rtb_s16_rtmin;
    int32_T rtb_s32_tmp;
    int16_T rtb_s16_tmp;

    rtb_s16_tmp = (int16_T)S2GTBSARPMMIN;
    rtb_s16_tmp = rt_MAX(rtb_s16_tmp, S210msBuf.Rpm_10ms);

    rtb_s16_rtmin = rt_MIN(rtb_s16_tmp, (int16_T)S2GTBSARPMMAX);

    rtb_s16_tmp = (int16_T)(S2GTBSALOADMIN << 3U);

    rtb_s16_tmp = rt_MAX(rtb_s16_tmp, S210msBuf.Load_10ms);
    rtb_s16_tmp = rt_MIN(rtb_s16_tmp, (int16_T)(S2GTBSALOADMAX << 3U));

    rtb_s32_tmp = (((((((((((S2GCOEFSAOPTL2R1 * rtb_s16_tmp >> 10) >> 2) +
      ((S2GCOEFSAOPTL1R2 * rtb_s16_rtmin >> 11) >> 1)) * rtb_s16_tmp >> 14) >>
      1) + ((S2GCOEFSAOPTL0R2 * rtb_s16_rtmin >> 11) >> 1)) >> 1) +
      (((rtb_s16_rtmin * rtb_s16_rtmin >> 12) * S2GCOEFSAOPTL0R3 >> 13) >> 3)) *
      rtb_s16_rtmin >> 1) + (((((((((S2GCOEFSAOPTL2R0 * rtb_s16_tmp >> 11) >> 1)
      + (rtb_s16_rtmin * S2GCOEFSAOPTL1R1 >> 14)) >> 1) + (((rtb_s16_tmp *
      rtb_s16_tmp >> 12) * S2GCOEFSAOPTL3R0 >> 12) >> 3)) * rtb_s16_tmp << 4U)
      >> 1) + (((rtb_s16_rtmin * S2GCOEFSAOPTL0R1 << 5U) >> 1) +
      ((S2GCOEFSAOPTL1R0 * rtb_s16_tmp << 5U) >> 5))) >> 4)) >> 12) +
      S2GCOEFSAOPTL0R0;
    if(rtb_s32_tmp > 32767) {
      rtb_s16_tmp = MAX_int16_T;
    } else if(rtb_s32_tmp <= -32768) {
      rtb_s16_tmp = MIN_int16_T;
    } else {
      rtb_s16_tmp = (int16_T)rtb_s32_tmp;
    }

    return(rtb_s16_tmp);
}

static uint16_t EFFSA_polynomial(void) 
{
    int16_T rtb_Enforceupperlimit;


    uint16_T rtb_u16_tmp;

    rtb_Enforceupperlimit = (int16_T)(S210msBuf.SAout_10ms -
      S210msBuf.SAopt_10ms);

    rtb_Enforceupperlimit = rt_MAX(S2GBKEFFSAMIN, rtb_Enforceupperlimit);
    rtb_Enforceupperlimit = rt_MIN(rtb_Enforceupperlimit, S2GBKEFFSAMAX);

    rtb_Enforceupperlimit = (int16_T)(((((S2GCOEFDSA2 * rtb_Enforceupperlimit >>
      6) >> 1) + (S2GCOEFDSA1 >> 3)) * rtb_Enforceupperlimit >> 8) +
      S2GCOEFDSA0);
    if(rtb_Enforceupperlimit <= 0) {
      rtb_u16_tmp = 0U;
    } else {
      rtb_u16_tmp = (uint16_T)rtb_Enforceupperlimit;
    }

    return(rtb_u16_tmp);
}
  
static uint16_t EFFLam_polynomial(void) 
{
    uint16_T rtb_u16_rtmax;
    int32_T rtb_s32_tmp;

    rtb_u16_rtmax = rt_MAX(S2GBKLAMEFFMIN, S210msBuf.LamObj_10ms);
    rtb_u16_rtmax = rt_MIN(rtb_u16_rtmax, S2GBKLAMEFFMAX);

    rtb_s32_tmp = (((((S2GCOEFLAM2 * rtb_u16_rtmax >> 8) >> 3) + S2GCOEFLAM1) *
      rtb_u16_rtmax << 3U) + (S2GCOEFLAM0 << 6U)) >> 6;
    if(rtb_s32_tmp <= 0) {
      rtb_u16_rtmax = 0U;
    } else if(rtb_s32_tmp > 65535) {
      rtb_u16_rtmax = MAX_uint16_T;
    } else {
      rtb_u16_rtmax = (uint16_T)rtb_s32_tmp;
    }
    
    return(rtb_u16_rtmax);
}

static int16_t CmiEstimation(void) 
{
  /* local block i/o variables*/
  int16_T rtb_DataTypeConversion1;
  int16_T rtb_Product10;
  int16_T ans;
  uint16_T rtb_u16_max;

  rtb_u16_max = rt_MAX(S2GHBKCMRPMFMIN, S210msBuf.Rpm_10ms);

  rtb_DataTypeConversion1 = (int16_T)rt_MIN(rtb_u16_max, S2GHBKCMRPMFMAX);

  rtb_Product10 = (int16_T)(rtb_DataTypeConversion1 * rtb_DataTypeConversion1
      >> 12);

  S2GCOEFCMIPoly = (int16_T)((((((((S2GCOEFCMIQ1R3 * rtb_DataTypeConversion1
      >> 14) >> 2) + (S2GCOEFCMIQ1R2 >> 1)) * rtb_Product10 >> 16) >> 1) +
      (((S2GCOEFCMIQ1R1 * rtb_DataTypeConversion1 >> 14) + (S2GCOEFCMIQ1R0 >>
      2)) >> 3)) * (S210msBuf.QAirBaseAvg_10ms >> 1) >> 9) + (((((S2GCOEFCMIQ0R3 *
    rtb_DataTypeConversion1 >> 14) >> 1) + (S2GCOEFCMIQ0R2 >> 1)) *
      rtb_Product10 >> 15) + (((S2GCOEFCMIQ0R1 * rtb_DataTypeConversion1 >> 14)
      >> 2) + S2GCOEFCMIQ0R0)));

  ans = (int16_T)(((S2GCOEFCMIPoly * S210msBuf.EffSAReal_10ms >> 14)
      * S210msBuf.EffLambda_10ms >> 14) * S210msBuf.EffCutoff_10ms >> 14);

    return(ans);
}
  
//H_RequestedTorque_Val Sub-Routines
static int16_t TBCMF_polynomial(void) 
{
    uint16_T rtb_u16_max;
    uint16_T rtb_u16_min;
    int16_T ans;
        
    rtb_u16_max = rt_MAX(S2GHBKCMRPMFMIN, S210msBuf.Rpm_10ms);

    rtb_u16_min = rt_MIN(rtb_u16_max, S2GHBKCMRPMFMAX);

    rtb_u16_max = rt_MAX(S2HTBCMFPRESMIN, S210msBuf.PresIntake_10ms);

    rtb_u16_max = rt_MIN(rtb_u16_max, S2HTBCMFPRESMAX);

    ans = (int16_T)((((((((((((((S2HCOEFCMFR1P2 * rtb_u16_min >> 14) >>
      2) + (S2HCOEFCMFR0P3 * rtb_u16_max >> 11)) >> 1) + (S2HCOEFCMFR0P2 >> 1))
      * (int16_T)((uint32_T)rtb_u16_max * (uint32_T)rtb_u16_max >> 6) +
      (S2HCOEFCMFR0P1 * rtb_u16_max << 3U)) >> 7) + ((((((S2HCOEFCMFR3P0 *
      rtb_u16_min >> 15) >> 1) + (S2HCOEFCMFR2P1 * rtb_u16_max >> 11)) >> 1) +
      (S2HCOEFCMFR2P0 >> 4)) * (int16_T)((uint32_T)rtb_u16_min *
      (uint32_T)rtb_u16_min >> 20) >> 1)) >> 1) +
      ((int16_T)((uint32_T)rtb_u16_min * (uint32_T)rtb_u16_max >> 9) *
      S2HCOEFCMFR1P1 >> 14)) >> 1) + (S2HCOEFCMFR1P0 * rtb_u16_min >> 12)) >> 5)
      + S2HCOEFCMFR0P0);

    return(ans);
}

  
static int16_t TBCMFTW_polynomial(void) 
{
    int16_T ans;

    uint16_T rtb_u16_max;
    int16_T rtb_s16_rtmax;

    rtb_u16_max = rt_MAX(S2GHBKCMRPMFMIN, S210msBuf.Rpm_10ms);

    rtb_s16_rtmax = rt_MAX(S2HTBCMFTWATMIN, S2HTWaterMax);
    rtb_s16_rtmax = rt_MIN(rtb_s16_rtmax, S2HTBCMFTWATMAX);

    ans = (int16_T)((((((S2HCOEFFCMFTWR1T2 * rtb_s16_rtmax >> 19) +
      (S2HCOEFFCMFTWR1T1 >> 8)) * ((int16_T)rt_MIN(rtb_u16_max, S2GHBKCMRPMFMAX)
         * rtb_s16_rtmax >> 10) >> 1) + (((S2HCOEFFCMFTWR0T2 * rtb_s16_rtmax >>
          14) + (S2HCOEFFCMFTWR0T1 >> 2)) * rtb_s16_rtmax >> 2)) >> 10) +
     S2HCOEFFCMFTWR0T0);

    return(ans);
}

extern uint8_t   PowerOnNumber;

/**************** funzioni provenienti dal MAIN *************/
void Saf2Mgm_Init(void) 
{  
  if(PowerOnType == PWON_NORMAL)
  {  
    S2RestartCnt = 0;
  }
  else if (ResetType == SOFTWARESYSTEMRESET && VsramState != NO_ERROR) /* Safety 2 reset has just happened and the VSRAM is corrupt */
  {
    int32_t tmp;
    tmp = S2MAXRESTART - S2INCRESTARTCNT;
    if (tmp>0)
    {
        S2RestartCnt = (uint32_t)tmp;
    }
    else
    {
        S2RestartCnt = 0;
    }
  }
  else
  {
      /* Non fare niente. */
  }
  SafMgm_ResErr();
  S2State = S2_STATE_ENG_STOPPED;
}

void Saf2Mgm_T10ms(void) 
{ 
    static uint8_t prescaler = 0;
    if(++prescaler >= 5)
    {
        Saf2Mgm_UpdateBufferT10ms();
        Saf2Task50ms();
        prescaler =0;
    }

}

void Saf2Mgm_PowerOff(void) 
{  

}

void Saf2Mgm_DISLoads(void) 
{
    S2FlgDisLRelay= TRUE;
    S2FlgDisL1 = TRUE;
    S2FlgDisL2 = TRUE;
    DIGIO_OutSet(ODE_INJ_ENABLE,OUT_INJ_OFF);
}

void SafMgm_ResErr(void)
{
  S2ExeCode      = 0;
  S2ForceShtDwn  = FALSE;
  S2FlgDisL1     = 0;
  S2FlgDisL2     = 0;
  S2FlgDisLRelay = 0;
  memset(S2PunErr,0,sizeof(S2PunErr));  
  memset(S2ErrCnt,0,sizeof(S2ErrCnt));
  memset(S2ValErr,0,sizeof(S2ValErr));
  memset(S2Result,0,sizeof(S2Result));  
  memset(S2ResultNeg,0,sizeof(S2ResultNeg));
  S2FlgExec  = FALSE;
}

/************************************************************/

#else     /* _BUILD_SAF2MGM_ */

uint8_t S2FlgDisL1 = FALSE;
uint8_t S2FlgDisL2 = FALSE;
uint8_t S2FlgDisLRelay = FALSE;
uint8_t S2KeyOffFlag = FALSE;
uint8_t S2ForceShtDwn = FALSE;

#endif /* _BUILD_SAF2MGM_ */
