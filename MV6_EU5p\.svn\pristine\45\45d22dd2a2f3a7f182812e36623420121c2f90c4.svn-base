/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/MV1/tree/APPLICATION/IMMO/sr#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1641   $                                                                                          */
/* $Date:: 2009-09-08 13:36:15 +0200 (mar, 08 set 2009)   $                                                      */
/* $Author:: gelmettia               $                                                                       */
/*****************************************************************************************************************/


 #ifdef   _BUILD_IMMO_ 
 #include "..\..\..\BIOS\LIN\inc\linbase.h"

 #include "..\..\..\DD\LINMGM\include\immo_task.h"
 #include "..\..\..\BIOS\SYS\auto\etpu_UART_auto.h"
 #include "..\include\IMMOapp.h"
 #include "IMMO_app.h"
 #include "..\..\..\DD\LINMGM\include\ImmoE2mgm.h" 
 #include <string.h>
#include "digitalin.h" 
 
 
 
 /*This Variable are used only for the Led Management*/
 uint8_t toggleTime;
 uint8_t ledSigCounter;
 uint8_t ledSig1State;
 uint8_t ledSig2State;
 uint8_t ledSig3State;
 uint8_t ledClk;
     

    /***********************************************************************
    *
    *    DESCRIPTION: IMMOBILIZER Led MGM
    *	                Call this function in the 100 ms task			   
    *                              ______                   _______________
    *                 Signal 1:   |      |________  oppure |               |
    *                               0.7    1.3                  2.0 
    *                                     __       __       __                 
    *                 Signal 2:   _______|  |_____|  |_____|  | Number of Key
    *                             0.5     0.1 0.3 0.1 0.3  0.1 
    *                                      ____      ____      ____
    *                 Signal 3:   ________|    |____|    |____|    |____ error
    *                               2      0.5   0.5  0.5  0.5  0.5   2s
    *                                             ___________ 
    *                 Signal 4:   _________ .. or             ..
    *                             ENGINE EN       ENGINE DIS 
    *
    *    AUTHOR:	     Solinas - Luca G.
    *
    *    HISTORY:     22/05/07 First Version
    *									3/06/07 Piaggio System Integration (Luca G)
    *                 6/06/07 Change LedOn LedOff API
    *                 12/06/07 add (ImmoE2KeyNumber==0) in IMMO_LedMgm
    ***********************************************************************/
     void IMMO_LedMgm (void)
    {
     uint8_t res;
     
     if(KeySignal)
     {
        
       if (ImmoCtxState==IMMO_END) 
       { 
         switch (ImmoCtxLedState) 
         {
         case IMMO_LED_SIG1    :   res=IMMO_LedSig1();
                              if (res==IMMO_LED_END)
                              {
                              /* The Signal2 is done only in presence of Master Key */
                              if ((memcmp((char *)ImmoE2KeyStored[0],(char *)ImmoCtxLastKey,6)==0) && (ImmoE2KeyNumber!=0)) 
                                              {
                                               ImmoCtxLedState=IMMO_LED_SIG2;
                                               ledClk=0;
                                               ledSigCounter=0;
                                              } 
                              else       {
                                         if (ImmoCtxError!=IMMO_NOERROR) 
                                           ImmoCtxLedState=IMMO_LED_SIG3;
                                         else  ImmoCtxLedState=IMMO_LED_SIG4; 
                                         ledClk=0;
                                         ledSigCounter=0;
                                         }            
                              }
                              break;
         case IMMO_LED_SIG2    :   res=IMMO_LedSig2();
                              if (res==IMMO_LED_END)
                              {
                                 //if (ImmoCtxError!=IMMO_NOERROR) 				 //l.g.
                                 //          ImmoCtxLedState=IMMO_LED_SIG3; //l.g.
                                 //else  																		 //l.g.
                                 //     ImmoCtxLedState=IMMO_LED_SIG4; 		 //l.g.
                                 
                                 ImmoCtxLedState=IMMO_LED_SIG3;						 //l.g.
                                 ledClk=0;
                                 ledSigCounter=0;
                              }
                              break;
         case IMMO_LED_SIG3    :   res=IMMO_LedSig3();
                              if (res==IMMO_LED_END)
                              {
                                  ImmoCtxLedState=IMMO_LED_SIG4;
                                  ledClk=0;
                                  ledSigCounter=0;
                              }
                              break;
         case IMMO_LED_SIG4  :
                              if(IMMO_CheckEngine())  //l.g.
                                ImmoLed_OFF();
                              else
                                ImmoLed_ON();
                              ImmoCtxLedState=IMMO_LED_IDLE;
                              break;
         case IMMO_LED_IDLE  :break;                     
         }
       } 
     }
       else
      {
      
         /*LED FSM Initialization*/
        ImmoCtxLedState=IMMO_LED_SIG1;
        ledClk      =0;
        ledSig1State=0;
        ledSig2State=0;
        ledSig3State=0;
        ImmoLed_OFF();
        }
       
    }
    /***********************************************************************
    *
    *    DESCRIPTION: IMMOBILIZER Signal 1
    *	                             ______                   _______________
    *                 Signal 1:   |      |________  oppure |               |
    *                               0.7    1.3                  2.0 
    *
    *     AUTHOR:	     Solinas - Luca G
    *
    *     HISTORY:     22/05/07 First Version
    *									3/06/07 Piaggio System Integration (Luca G)
    *                 6/06/07 Change LedOn LedOff API (Luca G)                
    ***********************************************************************/
    uint8_t IMMO_LedSig1 (void)
    {
     uint8_t res;
     ledClk++;
     switch (ledSig1State) 
     {   
          case 0:              /* Start 0.7 sec*/  
            ImmoLed_ON();
            res=IMMO_LED_RUNNING;
            if (ledClk==7) ledSig1State=1;
            break;
          case 1:              /*       1.3 sec*/
            if(ImmoE2KeyNumber==0)	 //l.g.
            		 ImmoLed_ON();
            else
            		 ImmoLed_OFF();
            
            res=IMMO_LED_RUNNING;
            if (ledClk==20) ledSig1State=2;
            break; 
          case 2:              /*       End    */
            ImmoLed_OFF();
            res=IMMO_LED_END;
            break;
     }
     return res;
    }
   
    /***********************************************************************
    *
    *    DESCRIPTION: IMMOBILIZER Led Signal 2
    *	                Call this function in the 100 ms task			   
    *
    *                                     __       __       __                 
    *                 Signal 2:   _______|  |_____|  |_____|  | Number of Key
    *                             0.5     0.1 0.3 0.1 0.3  0.1 
    *
    *    AUTHOR:	     Solinas	 Luca G
    *
    *    HISTORY:     22/05/07 First Version
    *									3/06/07 Piaggio System Integration (Luca G) 
    *                 6/06/07 Change LedOn LedOff API (Luca G)                 
    ***********************************************************************/
    uint8_t IMMO_LedSig2 (void)
    {
     uint8_t res;
     ledClk++;
     switch (ledSig2State) 
     {   
          case 0:              /* Start 0.5 sec*/  
            ImmoLed_OFF();
            res=IMMO_LED_RUNNING;
            if (ledClk==5) {
                             ledSig2State=1;
                             ledClk=0;
                           } 
            break;
          case 1:              /*       0.1 sec*/
            ImmoLed_ON();
            res=IMMO_LED_RUNNING;
            if (ledClk==1) ledSig2State=2;
            break; 
          case 2:              /*       0.3 sec */
            ImmoLed_OFF();
            res=IMMO_LED_RUNNING;
            if (ledClk==4) {       
                           ledSigCounter++;
                           if (ledSigCounter<ImmoE2KeyNumber) 
                           {
                            ledSig2State=1;
                            ledClk=0;
                           } else 
                           ledSig2State=3;
                          }
            break;
          case 3:              /*       End    */
            ImmoLed_OFF();
            res=IMMO_LED_END;
            break;
            
     }
     return res;
      
    }

    /***********************************************************************
    *
    *    DESCRIPTION: IMMOBILIZER Led Signal 3
    *	                Call this function in the 100 ms task			   
    *                                      ____      ____      ____
    *                 Signal 3:   ________|    |____|    |____|    |____ 
    *                               2      0.5   0.5  0.5  0.5  0.5   2s
    *
    *                 The number of pulse represent the Error Type
    *                 0: Tutto ok
    *                 1: Mancanza Linea Seriale ()
    *                 2: Chiave o Antenna non funzionanti
    *                 3: Chiave sconosciuta
    *                 4: Accensione programmata e Immo Vergine 
    *                    (Non applicabile)
    *
    *
    *    AUTHOR:	     Solinas  Luca G.
    *
    *    HISTORY:     22/05/07 First Version
    *									3/06/07 Piaggio System Integration (Luca G) 
    *                 6/06/07 Change LedOn LedOff API (Luca G)                
    ***********************************************************************/
    uint8_t IMMO_LedSig3 (void)
    {
     uint8_t res;
     ledClk++;
     switch (ledSig3State) 
     {   
          case 0:              /* Start 2 sec*/  
            ImmoLed_OFF();
            res=IMMO_LED_RUNNING;
            if (ledClk==20) {
                             if (ImmoCtxError==IMMO_NOERROR) ledSig3State=4;
                             else {
                                  if (ImmoCtxError==IMMO_KEYNOTVALID) ledSigCounter=3;
                                  else { 
											if (ImmoCtxError==IMMO_XCVR_ERROR) ledSigCounter=1;                        
                                  else ledSigCounter=2;
										}
                                  ledSig3State=1;      
                                  }                      
                             ledClk=0;
                           } 
            break;
          case 1:              /*       0.5 sec*/
            ImmoLed_ON();
            res=IMMO_LED_RUNNING;
            if (ledClk==5) ledSig3State=2;
            break; 
          case 2:              /*       0.5 sec */
            ImmoLed_OFF();
            res=IMMO_LED_RUNNING;
            if (ledClk==10) {       
                           ledSigCounter--;
                           if (ledSigCounter>0) 
                           {
                            ledSig3State=1;
                            ledClk=0;
                           } else 
                           { ledClk=0;
                             ledSig3State=3;
                           }  
                          }
            break;
          case 3:                    /*       1.5 Sec =2-0.5 */
            if (ledClk==15) ledSig3State=4;
            ImmoLed_OFF();
            res=IMMO_LED_RUNNING;
            
            break;
          case 4:res=IMMO_LED_END;   
            
     }
     return res;
    }
    
    
    
    
 uint8_t Get_LEDState(void){
    return ImmoCtxLedState;
 }   
 void Set_LEDState(uint8_t state){
    ImmoCtxLedState=state;
 }    
    
 #else
 /*stubs section*/   
#include "typedefs.h"
 void Set_LEDState(uint8_t state)
 {
 
 }    
 #endif
