/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/KLIN#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1394   $                                                                                          */
/* $Date:: 2009-06-24 17:47:22 +0200 (mer, 24 giu 2009)   $                                                      */
/* $Author:: GelmettiA               $                                                                       */
/*****************************************************************************************************************/


#ifdef _BUILD_KLINE_

#include "kline.h"
#include "diagcanmgm.h"
#include "kline_timer.h"
#include "kline_init.h"
#include "kline_utils.h"
#include "sci.h"


extern klineDrv_t klineDrv;
extern uint32_t  kwp_baud;

void klineTickTimer(void)
{
/****************************************/
    if(klineDrv.fivebaud_first_time > 0)
    { 
        klineDrv.fivebaud_first_time--;
        if(klineDrv.fivebaud_first_time == 0)
        {
            klineInit5BaudAddr();
        }
    }

    if(klineDrv.fivebaud_last_time > 0)
    { 
        klineDrv.fivebaud_last_time--;
        if(klineDrv.fivebaud_last_time == 0)
        {
              klineInitLL();
              klineStartTimer(KLINE_TIMER_5BAUD_W1);
              klineDrv.init = KLINE_INIT_55;

        }
    }

/****************************************/

    if(klineDrv.fivebaud_bittime > 0)
    { 
        klineDrv.fivebaud_bittime--;
        if(klineDrv.fivebaud_bittime == 0)
        {
            klineInit5BaudAddr();
        }
    }

    if(klineDrv.fivebaud_w1 > 0)
    { 
        klineDrv.fivebaud_w1--;
        if(klineDrv.fivebaud_w1 == 0)
        {
            klineWriteLL(KLINE_5BAUD_SYNCH);
        }
    }

    if(klineDrv.fivebaud_w2 > 0)
    { 
        klineDrv.fivebaud_w2--;
        if(klineDrv.fivebaud_w2 == 0)
        {
            klineWriteLL(KLINE_5BAUD_KB1);
        }
    }

    if(klineDrv.fivebaud_w3 > 0)
    { 
        klineDrv.fivebaud_w3--;
        if(klineDrv.fivebaud_w3 == 0)
        {
            klineWriteLL(KLINE_5BAUD_KB2);
        }
    }

    if(klineDrv.fivebaud_w4 > 0)
    { 
        klineDrv.fivebaud_w4--;
        
        if((klineDrv.init == KLINE_INIT_NADDR) &&(KLINE_TIME_5BAUD_W4+1-klineDrv.fivebaud_w4) >= KLINE_TIME_5BAUD_W4MIN)
        {
           klineWriteLL(KLINE_5BAUD_NADDR);
           klineStopTimer(KLINE_TIMER_5BAUD_W4);
           klineDrv.init = KLINE_INIT_NADDR;
           
        }
        else
        {
            if(klineDrv.fivebaud_w4 == 0)
            {
                klineDrv.error = KLINE_ERROR_TIMEOUT;
                klineStopTimer(KLINE_TIMER_5BAUD_W4);
                klineInitReset();
                
            }
        }
            
    }

    if(klineDrv.fast_p1min > 0)
    { 
        klineDrv.fast_p1min--;
        if(klineDrv.fast_p1min == 0)
        {
            klineWriteLL(klineDrv.data_tx[klineDrv.sent_bytes]);
            if(klineDrv.data_tx_len>klineDrv.sent_bytes)
        {
                klineDrv.sent_bytes++;
        }
    }
    }

    if(klineDrv.fast_p1max > 0)
    { 
        klineDrv.fast_p1max--;
        if(klineDrv.fast_p1max == 0)
        {   // timeout in fase di trasmissione
            klineInitReset();
            klineDrv.error = KLINE_ERROR_TIMEOUT;
        }
    }



    if(klineDrv.fast_p2min > 0)
    { 
        klineDrv.fast_p2min--;
        if(klineDrv.fast_p2min == 0)
        {
            klineWriteLL(klineDrv.data_tx[0]);
            if(klineDrv.data_tx_len>klineDrv.sent_bytes)
            {
                klineDrv.sent_bytes++;
            }
        }
    }


    // per  KLINE_TIME_FAST_P3MAX resettare il sistema e aspettare un nuovo pattern
    if(klineDrv.fast_p3max > 0)
    { 
        klineDrv.fast_p3max--;
        if(klineDrv.fast_p3max == 0)
        {

            if(kwp_baud!=0)
            {
             
               SCI_Config();
               klineResetTiming();
               klineNewbaudLL(SCI_10400_BR);
            }
            klineInitReset();
            klineDrv.error = KLINE_ERROR_TIMEOUT_P3;
        }
    }

    // per  KLINE_TIME_FAST_P4MAX resettare il sistema e aspettare un nuovo pattern
    if(klineDrv.fast_p4max > 0)
    { 
        klineDrv.fast_p4max--;
        if(klineDrv.fast_p4max == 0)
        {
            if(klineDrv.init != KLINE_INIT_DONE)
            { // timeout in fase di inizializzazione
                klineInitReset();
                klineDrv.error = KLINE_ERROR_TIMEOUT;
            }
            else
            { // timeout di ricezione bytes
                klineDrv.init = KLINE_INIT_DONE;
                klineDrv.error = KLINE_ERROR_TIMEOUT;
                klineStartTimer(KLINE_TIMER_FAST_P3MAX);
            }
        }
    }
}


/*********************************************************************************************/
/* FUNCTION     : void klineStartTimer(void)                                                 */
/*                                                                                           */
/* PURPOSE      : This function performs                                                     */
/*                                                                                           */
/* INPUTS NOTES : KLINE module                                                               */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/*                                                                                           */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
void klineStartTimer(klineTimers_t timer)
{
    // faccio partire timer
    switch(timer)
    {
/*******************************************/
        case KLINE_TIMER_5BAUD_FIRST:
        klineDrv.fivebaud_first_time = KLINE_TIME_5BAUD_FIRST;
        break;
        case KLINE_TIMER_5BAUD_LAST:
        klineDrv.fivebaud_last_time = KLINE_TIME_5BAUD_LAST;
        break;
/*******************************************/
        case KLINE_TIMER_5BAUD:
        klineDrv.fivebaud_bittime = KLINE_TIME_5BAUD;
        break;
    
        case KLINE_TIMER_5BAUD_W1:
        klineDrv.fivebaud_w1 = KLINE_TIME_5BAUD_W1;
        break;
    
        case KLINE_TIMER_5BAUD_W2:
        klineDrv.fivebaud_w2 = KLINE_TIME_5BAUD_W2+1;
        break;
    
        case KLINE_TIMER_5BAUD_W3:
        klineDrv.fivebaud_w3 = KLINE_TIME_5BAUD_W3;
        break;
    
        case KLINE_TIMER_5BAUD_W4:
        klineDrv.fivebaud_w4 = KLINE_TIME_5BAUD_W4+2;
        break;
    
        case KLINE_TIMER_FAST_P1:
        klineDrv.fast_p1min = klineDrv.p1min;
        klineDrv.fast_p1max = KLINE_TIME_FAST_P1MAX;
        break;
    
        case KLINE_TIMER_FAST_P2:
        klineDrv.fast_p2min = klineDrv.p2min;
        break;
    
        case KLINE_TIMER_FAST_P3MAX:
        klineDrv.fast_p3max = klineDrv.p3max;
        break;
    
        case KLINE_TIMER_FAST_P4MAX:
        klineDrv.fast_p4max = KLINE_TIME_FAST_P4MAX;
        break;
    
        default:
        break;
    }
}


/*********************************************************************************************/
/* FUNCTION     : void klineStopTimer(void)                                                  */
/*                                                                                           */
/* PURPOSE      : This function performs                                                     */
/*                                                                                           */
/* INPUTS NOTES : KLINE module                                                               */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/*                                                                                           */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
void klineStopTimer(klineTimers_t timer)
{
    // faccio partire timer
    switch(timer)
    {
        case KLINE_TIMER_5BAUD:
        klineDrv.fivebaud_bittime = 0U;
        break;
    
        case KLINE_TIMER_5BAUD_W1:
        klineDrv.fivebaud_w1 = 0U;
        break;
    
        case KLINE_TIMER_5BAUD_W2:
        klineDrv.fivebaud_w2 = 0U;
        break;
    
        case KLINE_TIMER_5BAUD_W3:
        klineDrv.fivebaud_w3 = 0U;
        break;
    
        case KLINE_TIMER_5BAUD_W4:
        klineDrv.fivebaud_w4 = 0U;
        break;
    
        case KLINE_TIMER_5BAUD_W4MAX:
        klineDrv.fivebaud_w4max = 0U;
        break;
    
        case KLINE_TIMER_FAST_P1:
        klineDrv.fast_p1min = 0U;
        klineDrv.fast_p1max = 0U;
        break;
    
        case KLINE_TIMER_FAST_P2:
        klineDrv.fast_p2min = 0U;
        break;
    
        case KLINE_TIMER_FAST_P3MAX:
        klineDrv.fast_p3max = 0U;
        break;
    
        case KLINE_TIMER_FAST_P4MAX:
        klineDrv.fast_p4max = 0U;
        break;
    
        default:
        break;
    }
}


/*********************************************************************************************/
/* FUNCTION     : void klineUpdateTiming(void)                                               */
/*                                                                                           */
/* PURPOSE      : This function performs                                                     */
/*                                                                                           */
/* INPUTS NOTES : KLINE module                                                               */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/*                                                                                           */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/

void klineUpdateTiming(void) 
{
// Quello che perdo con l'operazione di shift rientra nella tolleranza

    kwp_timing.tempP2min = klineDrv.p2min = klineDrv.data_rx[klineDrv.sid_pos+2] >> 1 + 1;       
    kwp_timing.tempP2max = klineDrv.p2max = klineDrv.data_rx[klineDrv.sid_pos+3] * KLINE_CONVERT_P2MAX;        
    kwp_timing.tempP3min = klineDrv.p3min = klineDrv.data_rx[klineDrv.sid_pos+4] >> 1;       
    kwp_timing.tempP3max = klineDrv.p3max = klineDrv.data_rx[klineDrv.sid_pos+5] * KLINE_CONVERT_P3MAX;               
    kwp_timing.tempP4min = klineDrv.p4min = klineDrv.data_rx[klineDrv.sid_pos+6] >> 1;        
            
}

/*********************************************************************************************/
/* FUNCTION     : void klineGetTiming(uint8_t *buff)                                         */
/*                                                                                           */
/* PURPOSE      : This function performs                                                     */
/*                                                                                           */
/* INPUTS NOTES : KLINE module                                                               */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/*                                                                                           */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/

void klineGetTiming(uint16_t *buff) 
{

  klineDrv.p1min = kwp_timing.tempP1min;       
  klineDrv.p2min = kwp_timing.tempP2min;       
  klineDrv.p2max = kwp_timing.tempP2max;        
  klineDrv.p3min = kwp_timing.tempP3min;     
  klineDrv.p3max = kwp_timing.tempP3max;           
  klineDrv.p4min = kwp_timing.tempP4min;        


// Quello che perdo con l'operazione di shift rientra nella tolleranza
    buff[0] = (uint16_t)((klineDrv.p2min - 1) << 1);        
    buff[1] = (uint16_t)(klineDrv.p2max / KLINE_GET_P2MAX);        
    buff[2] = (uint16_t)(klineDrv.p3min << 1);        
    buff[3] = (uint16_t)(klineDrv.p3max / KLINE_GET_P3MAX);        
    buff[4] = (uint16_t)(klineDrv.p4min << 1);        
}


uint16_t kline_time_prog_2min = KLINE_TIME_PROG_P2MIN ;        
uint16_t kline_time_prog_2max = KLINE_TIME_PROG_P2MAX ;        
uint16_t kline_time_prog_3min = KLINE_TIME_PROG_P3MIN ;        
uint16_t kline_time_prog_3max = KLINE_TIME_PROG_P3MAX ;        
uint16_t kline_time_prog_4min = KLINE_TIME_PROG_P4MIN ;        


void klineGetTimingLimits(uint16_t *buff) 
{


// Quello che perdo con l'operazione di shift rientra nella tolleranza
    buff[0] = (uint16_t)(kline_time_prog_2min << 1);        
    buff[1] = (uint16_t)(kline_time_prog_2max / KLINE_GET_P2MAX);        
    buff[2] = (uint16_t)(kline_time_prog_3min << 1);        
    buff[3] = (uint16_t)(kline_time_prog_3max / KLINE_GET_P3MAX);        
    buff[4] = (uint16_t)(kline_time_prog_4min << 1);        
}

/*********************************************************************************************/
/* FUNCTION     : void klineResetTiming(void)                                               */
/*                                                                                           */
/* PURPOSE      : This function performs                                                     */
/*                                                                                           */
/* INPUTS NOTES : KLINE module                                                               */
/*                                                                                           */
/* RETURN NOTES : none.                                                                      */
/*                                                                                           */
/* WARNING      :                                                                            */
/*                                                                                           */
/*********************************************************************************************/
void klineResetTiming(void) 
{

    kwp_timing.tempP1min = klineDrv.p1min = KLINE_TIME_FAST_P1MIN;      
    kwp_timing.tempP2min = klineDrv.p2min = KLINE_TIME_FAST_P2MIN + 1;        
    kwp_timing.tempP2max = klineDrv.p2max = KLINE_TIME_FAST_P2MAX;        
    kwp_timing.tempP3min = klineDrv.p3min = KLINE_TIME_FAST_P3MIN;       
    kwp_timing.tempP3max = klineDrv.p3max = KLINE_TIME_FAST_P3MAX;          
    kwp_timing.tempP4min = klineDrv.p4min = KLINE_TIME_FAST_P4MIN;        
     
}
#else
/*tubs section */
#include "kline.h"
#include "diagcanmgm.h"
#include "kline_timer.h"
#include "kline_init.h"
#include "kline_utils.h"
kwpTimingStruct kwp_timing = {0,0,0,0,0,0};




void klineStartTimer(klineTimers_t timer)
{

}

void klineStopTimer(klineTimers_t timer)
{

}

void klineUpdateTiming(void)
{ 

}
void klineResetTiming(void) 
{

}
void klineTickTimer(void)
{

}
void klineGetTiming(uint16_t *buff) 
{
}

void klineGetTimingLimits(uint16_t *buff) 
{

}


#endif // _BUILD_KLINE_




