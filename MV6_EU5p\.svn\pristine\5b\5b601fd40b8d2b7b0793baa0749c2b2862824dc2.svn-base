/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/


#pragma ETPU_function PULSE, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

#include "..\..\common\ETPU_Shared.h"
#include "..\include\etpuc.h"
#include "..\include\eTPUC_common.h"
#include "..\..\common\ETPU_SharedTypes.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

// pulse state machine: states
#define PULSE_CLOSE  (0x000000) // pulse is close
#define PULSE_INIT   (0x000001) // transitory state
#define PULSE_OPEN   (0x000002) // pulse has been programmed
#define PULSE_LOCK   (0x000003) // transitory state
#define PULSE_ON     (0x000004) // pulse out is ON
#define PULSE_OFF    (0x000005) // pulse out is OFF

#define LATENCY   (3)

#define STATUS       (0) // status of the pulse
#define PROGRAM      (1) // program angle/time
#define REFERENCE    (2) // reference angle/time
#define FLAGS        (3) // status of the channel
#define ONMODE       (4) // start mode (0=time, 1=angle)
#define OFFMODE      (5) // stop mode (0=time, 1=angle)
#define PULSEIDX     (6) // current pulse index
#define NUMPULSES    (9) // number of puleses
#define POLARITY     (8) // pin polarity
#define SATURATION   (9) // saturation limit (angle or time)
#define PRGSTART    (10) // programmed start base param
#define PRGSTOP     (13) // programmed stop base param
#define TRANSANGLE  (16) // last transition angle
#define TRANSTIME   (17) // last transition time
#define PREDICTEN   (18) // enable prediction mode (only in A-A mode)
#define NOSTRICTADJ (19) // disable strict adjust mode

extern unsigned int espToothPeriod; // extern current tooth period
extern unsigned int event_divider;  // divider for frequency of prediction wakeup

#ifdef _BUILD_ETPUPULSE_

#define _BUILD_PREDICTION_FEATURE_

#ifdef _BUILD_PREDICTION_FEATURE_
unsigned int adda(unsigned int a, unsigned int b);
unsigned int adda(unsigned int a, unsigned int b)
{
    unsigned int c;
    unsigned int k = 360*N_CYCLE*TICKS_PER_DEGREE; // fault compiler

    c = a + b;
    if (c >= k)
    {
        c = c - k;
    }

    return c;
}

unsigned int suba(unsigned int a, unsigned int b);
unsigned int suba(unsigned int a, unsigned int b)
{
    unsigned int c;
    unsigned int k = 360*N_CYCLE*TICKS_PER_DEGREE; // fault compiler

    if (a < b)
    {
        a = a + k;
    }
    c = a - b;

    return c;
}

// time to angle prediction
unsigned int preda(unsigned int time);
unsigned int preda(unsigned int time)
{
    unsigned int angle;

#if 0
    // no half espToothPeriod rounding
    angle = TICKS_PER_TOOTH*time/espToothPeriod; // ok
#else
    // reduce quantization rounding effects for low rpm
    angle = TICKS_PER_TOOTH*time;

    if(event_divider != 2)
    {
      angle = angle + espToothPeriod/2; // round
    }
    angle = angle/espToothPeriod; // ok
#endif

    return angle;
}

#if 0
// angle to time prediction
unsigned int predt(unsigned int angle);
unsigned int predt(unsigned int angle)
{
    unsigned int time;

    time = espToothPeriod*angle/TICKS_PER_TOOTH;

    return time;
}
#endif
#endif /* _BUILD_PREDICTION_FEATURE_ */

// median test:
// return 1: if x falls in [left, right]
// return 0: otherwise
//
//   (1)   /      X (2) *|            case (1) L<R
// R      +            / |                     L<X<R      => 1
//       /        L   +  |
// X    *            /   |   /        case (2) L>R
//     /                 |  /                  X>L or X<R => 1
// L  +           R      | +
//   /                   |/
//    |  | |          | |  |
//    l  x r          l x  r
// median test:
unsigned int median(unsigned int left, unsigned int x, unsigned int right);
unsigned int median(unsigned int left, unsigned int x, unsigned int right)
{
    unsigned int res;

    if (right < left)
    {
        res = (x > left) || (x < right);
    }
    else
    {
        res = (x > left) && (x < right);
    }

    return res;
}

void close_all(unsigned int *h);
void close_all(unsigned int *h)
{
    if (h[8])
    {
        SetPinLow(); /* set out pin not active */
    }
    else
    {
        SetPinHigh();
    }

    ClearLSRLatch();
    ClearMatchALatch();
    ClearMatchBLatch();
    ClearTransLatch();
    DisableMatchDetection();
}

void setup_matches(unsigned int *h);
void setup_matches(unsigned int *h)
{
    unsigned int current_time;
    unsigned int current_angle;
    unsigned int matchA;
    unsigned int matchB;
    unsigned int event;
    unsigned int event_tmp;
    unsigned int testa;
    unsigned int testb;
    unsigned int index;
    unsigned int pulseChanFlags;
    unsigned int update;

    current_time = GetCapRegA();
    current_angle = GetCapRegB();

    index = h[6]; // idx

    switch (h[0]) // status
    {
        // setup matches for next PULSE_OFF
        // previous state was PULSE_OPEN
        // current state now is PULSE_ON
        // case PULSE_OPEN:
        case PULSE_LOCK:

        // setup matches for next PULSE_OFF
        // previous state was PULSE_OFF
        // current state now is PULSE_ON
        case PULSE_OFF:

            h[0] = PULSE_ON;

            event = h[13+index];

            h[16] = current_time;
            h[17] = current_angle;

            if (h[5]) // close angle
            {
                matchA = event; // stop angle event

                // time saturation: time duration
                matchB = current_time + h[9];
            }
            else // close time
            {
                // relative angle saturation: angle duration
                // matchA = adda(current_angle, h[9]);

                // absolute angle saturation
                matchA = h[9];
                matchB = current_time + event; // stop time event
            }

            // setup matches for stop pulse
            SetupMatch_A(matchA, Mtcr2_Ctcr1_eq, PinToggle); // angle
            SetupMatch_B(matchB, Mtcr1_Ctcr2_eq, PinToggle); // time
        break;

        // previous state was PULSE_ON
        // current state now is PULSE_OFF
        // setup matches for next PULSE_ON (or PULSE_CLOSE)
        case PULSE_ON:

            index++; // increase pulse index
            h[6] = index;

            h[16] = current_time;
            h[17] = current_angle;

        // previous state was PULSE_INIT
        // current state now is PULSE_OPEN
        // setup matches for next PULSE_ON
        case PULSE_INIT:

            testa = h[0];
            testa++;
            h[0] = testa; // status: move to next state

        // wake-up setup
        case PULSE_OPEN:

            update = 1;

            // if (h[6] >= h[7]) // fault complier!!! // idx = max_idx
            testa = h[6];
            testb = h[7];
            if (testa >= testb) // idx = max_idx
            {
                // last pulse reached pulse has completed, close down the pulse
                update = 0;
            }
#if 1
            // manage absolute angle saturation
            // if saturation occurs close down the pulse even if
            // it has not completed
            if (!h[5])
            {
                if (h[0] != PULSE_OPEN)
                {
                    // close all in case of angle saturation
                    if (current_angle == h[9])
                    {
                        update = 0;
                    }

                }
            }
#endif
            if (update)
            {
                event = h[10+index];

                matchA = event + h[2]; // start match event
                matchB = 360*N_CYCLE*TICKS_PER_DEGREE; // never

#ifdef _BUILD_PREDICTION_FEATURE_
                if (h[18])
                {
                    testa = (h[0] == PULSE_OPEN);
                    testb = (h[0] == PULSE_OFF);
                    // testa = testa || testb; // fault
                    testa = testa | testb;
                    if (testa)
                    {
                        // if (h[5])
                        {
                        testa = h[13+index]; // stop angle
#if 1
                        // PREDICTION: convert on periode to angle
                        // matchA is an angle
                        // matchB is an angle

                        event = preda(event); // ok
                        matchA = suba(testa, event); // predicted open

                        // allow prediction only in open state
                        if (h[0] == PULSE_OPEN)
                        {
                          event = suba(matchA, tcr2);
                          
                          if(event_divider != 2)
                          {
                            event_tmp = event ;                            
                            event = event/event_divider;  
                            if ((event_tmp-event) > 5)
                            {
                                matchB = adda(event, tcr2); // wake-up
                            }
                          
                          }
                          else
                          {
                              event = event/2; // half angle distance       
                              if (event > TICKS_PER_TOOTH)
                              {
                                  matchB = adda(event, tcr2); // wake-up
                              }
                          }                 
                        }
#else
                        // PREDICTION: convert stop angle to time
                        // matchA is a time
                        // matchB is a time
                        testb = suba(testa, tcr2);
                        testb = espToothPeriod*testb/TICKS_PER_TOOTH;
                        testb = testb - event;
                        matchA = tcr1 + testb; // on

                        testb = testb/2; // half time distance
                        testb = LATENCY; // add latency

                        if (testb > espToothPeriod)
                        {
                            matchB = testb + tcr1; // wake-up
                        }
#endif
                    }
                }
            }
#endif /* _BUILD_PREDICTION_FEATURE_ */

                // setup matches for start pulse
                if (h[4]) // open angle
                {
                    SetupMatch_A(matchA, Mtcr2_Ctcr1_eq, PinToggle); // angle
                    SetupMatch_B(matchB, Mtcr2_Ctcr2_eq, NoChange); // wake-up match angle
                    // SetupMatch_A(matchA, Mtcr1_Ctcr2_eq, PinToggle); // angle
                    // SetupMatch_B(matchB, Mtcr1_Ctcr2_eq, NoChange); // wake-up match angle
                }
                else // open time
                {
                    // SetupMatch_A(matchB, Mtcr2_Ctcr1_eq, NoChange); // wake-up match angle
                    SetupMatch_B(matchA, Mtcr1_Ctcr2_eq, PinToggle); // time
                }
            }
            else
            {
                testa = PULSE_CLOSE;
                h[0] = testa;
                close_all(h);
            }
        break;
    }

    pulseChanFlags = h[3];

    // raise an interrupt request in case of PULSE_ON transition
    if (h[0] == PULSE_ON)
    {
        if (pulseChanFlags & (EXCEPTIONS_ENABLED))
        {
            pulseChanFlags = pulseChanFlags | OPEN_PULSE_EXCEPTION;
            h[3] = pulseChanFlags;
            SetChannelInterrupt(); /* interrupt request: the HOST should ask for tcr1 value */
        }
    }

    if (h[0] == PULSE_CLOSE)
    {
        pulseChanFlags = pulseChanFlags & (~INIT_STATUS_MASK);

        if (pulseChanFlags & (EXCEPTIONS_ENABLED))
        {
            pulseChanFlags = pulseChanFlags | CLOSE_PULSE_EXCEPTION;
            h[3] = pulseChanFlags;
            SetChannelInterrupt(); /* interrupt request: the HOST should ask for tcr1 value */
        }
    }
}
#endif

#pragma library;
#pragma option +l;  /* List the library */
#pragma option v;

/********************************************************************************
* FUNCTION: PULSE                                                               *
* PURPOSE:  This function manages the pulses                                    *
*                                                                               *
*                                                                               *
* INPUTS NOTES:                                   *
* RETURNS NOTES:      *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/

        /* mathematically
        ** on_angle  =               on_a  * Start[i] + 0;
        ** on_time   = CurrentTime + on_t  * Start[i] + 0;
        ** off_angle =               off_a * Stop[i]  + off_sa * Saturation;
        ** off_time  = CurrentTime + off_t * Stop[i]  + off_st * Saturation;

        ** where, trivial rules are:
        **         on_a
        **         on_t = !on_a
        **         off_a
        **         off_sa = !off_a
        **         off_t = !off_a
        **         off_st = !off_t = off_a

        ** on  = on_a*Start[i];
        ** on  = CurrentTime + !on_a*Start[i];
        ** off = off_a*Stop[i] + !off_s*Saturation;
        ** off = CurrentTime + !off_a*Stop[i] + off_a*Saturation;
        */

void PULSE(
    unsigned int handle[25],
    unsigned int pulseStartAdj[3],
    unsigned int pulseStopAdj[3],
    unsigned int dummy)
{
#ifdef _BUILD_ETPUPULSE_

    if (HSR_INIT_PULSE)   /* init */
    {
        // init possible only in close status
        if (handle[0] == PULSE_CLOSE)
        {
            unsigned int first;
            unsigned int adjust;
            unsigned int current;
            unsigned int update;
            unsigned int index;
            unsigned int pulseChanFlags;
            unsigned int testa;

            handle[6] = 0; // reset idx

            // setup for pulse open
            if (handle[4]) // angle mode
            {
                handle[1] = tcr2; // save programming angle
                handle[2] = 0; // reference is angle 0
            }
            else
            {
                handle[1] = tcr1; // save programming time
                handle[2] = zeroToothTime; // reference is the time of angle 0
            }

#if 0
            // meglio tenere questa verifica non attiva: abbastanza
            // complesso gestire il tutto con le chiusure a tempo
            //!!! da verificare bene nel caso di t-a predittivo
            // verify if current setup is congruent
            if (handle[18]) // if prediction mode enabled
            {
                testa = handle[10];
                testa = preda(testa); // predict aperture angle
                first = handle[13];
                first = suba(first, testa); // start angle prediction
                // index = 13 + handle[7] - 1;
                // adjust = handle[index]; // take the max stop
            }
            else
            {
                first = handle[10];
                first += handle[2]; // add reference
                // index = 13 + handle[7] - 1;
                // adjust = handle[index]; // take the max stop
            }
                index = 13 + handle[7] - 1; // !!! nel modo at non va bene
                adjust = handle[index]; // take the max stop

            current = handle[1] + LATENCY;

            // current time/angle must be outside first...adjust range
            // therefore inside complement adjust...first
            update = median(adjust, current, first);

            if (update)
#endif
            {
                pulseChanFlags = handle[3];
                if (handle[8]) /* active high */
                {
                    SetPinLow();
                }
                else /* active low */
                {
                    SetPinHigh();
                }

                SetChannelMode(em_b_dt);

                pulseChanFlags = pulseChanFlags | INIT_STATUS_MASK;
                handle[3] = pulseChanFlags;

                handle[0] = PULSE_INIT;
                if(handle[5] == 0)
                {
                    dummy = handle[13];
                    dummy += tcr1;
                }
                
                setup_matches(handle); // saturation is meaningless
            }
        }
    }
    else if (HSR_FORCE_CLOSE_PULSE)   /* Required in case of sync lost */
    {
#if 1
        unsigned int pulseChanFlags;
        handle[0] = PULSE_CLOSE;
        pulseChanFlags = handle[3];
        if (pulseChanFlags & INIT_STATUS_MASK)
        {
            close_all(handle);
            pulseChanFlags &= (~INIT_STATUS_MASK);
        }
        handle[3] = pulseChanFlags;
#endif
    }
    else if (HSR_FORCE_INIT_PULSE)   /* Required in case of sync lost */
    {
#if 1
        if (handle[8]) /* active high */
		{
				SetPinLow();
		}
		else /* active low */
		{
				SetPinHigh();
		}
#endif
    }
    else if (HSR_ADJUST_PULSE)
    {
    // adjust consideration in case of time mode
    // 1. bios and dd may use time instead of angle. in this case
    //    time are referred to the zero tooth 
    // 2. zeroToothTime here is internally used a reference time to attach time
    //    values coming from bios level. zeroToothTime may vary from init to ajust commands
    // Init and ajust command shall refer to the same reference time, the zeroToothTime
    // get in the init command.
#if 1
        unsigned int first;
        unsigned int adjust;
        unsigned int current;
        unsigned int reference;
        unsigned int update;
        unsigned int index;
        unsigned int testa;
        unsigned int testb;

        // here used also to inform state machine
        // from wich status to start
        update = 0;

#if 1
        // adjust allowed only in OPEN state in adjust strict mode
        if (handle[0] == PULSE_OPEN)
        {
            adjust = pulseStartAdj[0];
            if (handle[18]) // if prediction mode enabled
            {
                testa = preda(adjust); // predict open angle
                adjust = pulseStopAdj[0];
                adjust = suba(adjust, testa); // start angle prediction
            }

            // PULSE_INIT is the entry point to setup
            // new matches due to requested correction
            update = PULSE_INIT;
            first = handle[1];
        }
#endif

#if 1
        // adjust allowed also in ON state in adjust not strict mode
        //
        //           current
        //    +---------x-------+.....
        //    |             .   |    .
        //    |             .   |    .
        //    |             .   |    .
        // ---+                 +------------
        //  pulseStart       pulseStop
        //
        // condition 1: pulseStartAdj == pulseStart
        // condition 2: current < pulseStopAdj
        // 
        // else
        if (handle[0] == PULSE_ON)
        {
            if (handle[19]) // no strict mode: extend correction
            {
                index = handle[6];
                first = handle[index + 10]; // equal to last match
                adjust = pulseStartAdj[index];
                if (first == adjust)
                {
                    int timeNow;

                    timeNow = tcr1 + 20;
                    if(handle[5] == 0)
                    {
                        if(timeNow < dummy)
                        {
                            adjust = pulseStopAdj[index]; // stop
                            // PULSE_OFF is the entry point to setup
                            // new matches due to requested correction
                            update = PULSE_OFF;
                        }
                    }
                }
            }
        }
#endif

#if 1
        if (update) // update is allowed
        {
            if (handle[4]) // open angle
            {
                // following sum is very dangerous!!! use adda
                testa = tcr2 + LATENCY; // get current angle
                // testa = adda(tcr2, LATENCY);
                // reference = 0; // update reference angle, the same...
            }
            else // open time
            {
                testa = tcr1 + LATENCY; // get current time
                // reference = zeroToothTime; // update reference time
            }
            adjust = adjust + handle[2]; // attach the reference

            // check if correction is allowed
            // correction must be inside first...adjust range
            // first has already passed, but use it to know angular
            // position, if we are across a wraparound
            update = update * median(first, testa, adjust);

            if (update) // update is allowed
            {
                handle[0] = update; // init status (PULSE_INIT or PULSE_OFF)

                // update program and reference time/angle to each current values
                handle[1] = testa; // program angle/time
                // handle[2] = reference; // reference angle/time

                // synchronize all
                // we assume all these are congruent!!!
                handle[10] = pulseStartAdj[0];
                handle[11] = pulseStartAdj[1];
                handle[12] = pulseStartAdj[2];
                handle[13] = pulseStopAdj[0];
                handle[14] = pulseStopAdj[1];
                handle[15] = pulseStopAdj[2];
                setup_matches(handle);
            }
        }
#endif
#endif
    }
    else if (MatchA) // angle match
    {
        unsigned int testa;

        ClearMatchALatch();

        // engage state machine on first match
        if (handle[0] == PULSE_OPEN)
        {
            handle[0] = PULSE_LOCK;
        }

        setup_matches(handle);
    }
    else if (MatchB) // time match, and wake-up angle match
    {
        unsigned int testa;

        ClearMatchBLatch();

        // engage state machine on first match
        if (handle[0] == PULSE_OPEN)
        {
            // in case of prediction mode (angle)
            // do not engage the state machine if MatchB occurs
            // wake-up angle match: keep staying in OPEN state
            if (handle[18] == 0)
            {
                handle[0] = PULSE_LOCK;
            }
        }

        setup_matches(handle);
    }
    else
    {
    }
#endif /* _BUILD_ETPUPULSE_ */
}
#pragma endlibrary;

