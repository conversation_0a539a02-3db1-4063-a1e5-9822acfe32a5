/*
 * File: C:\localmodules\local_AirMgm_PI6\slprj\ert\_sharedutils\mul_wide_su32.c
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2438
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Thu Feb 14 08:15:05 2019
 */

#include "rtwtypes.h"
#include "rtw_shared_utils.h"

void mul_wide_su32(int32_T in0, uint32_T in1, uint32_T *ptrOutBitsHi, uint32_T
                   *ptrOutBitsLo)
{
  uint32_T outBitsLo;
  uint32_T absIn;
  uint32_T in0Hi;
  uint32_T in1Lo;
  uint32_T in1Hi;
  uint32_T productHiLo;
  uint32_T productLoHi;
  absIn = (uint32_T)((in0 < 0) ? (-in0) : in0);
  in0Hi = (absIn >> 16U);
  absIn &= 65535U;
  in1Hi = (in1 >> 16U);
  in1Lo = in1 & 65535U;
  productHiLo = in0Hi * in1Lo;
  productLoHi = absIn * in1Hi;
  absIn *= in1Lo;
  in1Lo = 0U;
  outBitsLo = (productLoHi << 16U) + absIn;
  if (outBitsLo < absIn) {
    in1Lo = 1U;
  }

  absIn = outBitsLo;
  outBitsLo += (productHiLo << 16U);
  if (outBitsLo < absIn) {
    in1Lo++;
  }

  absIn = (((productLoHi >> 16U) + (productHiLo >> 16U)) + (in0Hi * in1Hi)) +
    in1Lo;
  if (!((in1 == 0U) || (in0 >= 0))) {
    absIn = ~absIn;
    outBitsLo = ~outBitsLo;
    outBitsLo++;
    if (outBitsLo == 0U) {
      absIn++;
    }
  }

  *ptrOutBitsHi = absIn;
  *ptrOutBitsLo = outBitsLo;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
