/*
 * File: MisfOBD2.c
 *
 * Code generated for Simulink model 'MisfOBD2'.
 *
 * Model version                  : 1.588
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Aug 19 10:02:22 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (26), Warnings (6), Error (0)
 */

#include "MisfOBD2.h"
#include "MisfOBD2_private.h"

/* Named constants for Chart: '<S50>/EnTrig' */
#define MisfOBD2_IN_NO_ACTIVE_CHILD_mrh ((uint8_T)0U)
#define MisfOBD2_IN_OFF                ((uint8_T)1U)
#define MisfOBD2_IN_ON                 ((uint8_T)2U)

/* Named constants for Chart: '<S50>/Select_Diag' */
#define MisfOBD2_IN_MIL_FIXED          ((uint8_T)1U)
#define MisfOBD2_IN_MIL_ONLY_FLASHING  ((uint8_T)2U)
#define MisfOBD2_IN_NO_ACTIVE_CHILD_ck1 ((uint8_T)0U)

/* Named constants for Chart: '<S50>/res_Sync' */
#define MisfOBD2_IN_ACK                ((uint8_T)1U)
#define MisfOBD2_IN_REQ                ((uint8_T)2U)

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_MISFOBD2_

/* Block signals and states (default storage) */
D_Work_MisfOBD2_T MisfOBD2_DWork;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint16_T CntEmissOBD2;

/* Emission misfire counter window */
uint8_T EnMisfCATOBD2;

/* CAT misfire enable */
uint8_T EnMisfEmissOBD2;

/* Emissions misfire enable */
uint8_T EnMisfOBD2;

/* misfire enable */
uint8_T FlgCatMisfOBD2;

/* Flg Catalyst damage OBD2 */
uint8_T FlgEmissMisfOBD2;

/* Flg Emissions damage OBD2 */
uint8_T FlgMisfDiagOn;

/* Flg Misfire driving cycle exec */
uint8_T FlgOBD2MILFlashing;

/* MIL in flashing mode */
uint32_T IDMisfOBD2;

/* ID Version */
int8_T MisfCylCAT;

/* Cylinder in misfire */
int8_T MisfCylEmiss;

/* Cylinder in misfire */
uint16_T NRevCAT;

/* window counter */
uint16_T NRevEmiss;

/* window counter */
uint16_T NRevOBD2Emiss;

/* Emission misfire window */
uint16_T SumCatMisfOBD2;

/* threshold counter */
uint16_T SumEmissMisfOBD2;

/* threshold counter */
uint16_T VtCntMisfCAT[4];

/* Misfire counter */
uint16_T VtCntMisfEmiss[4];

/* Misfire counter */

/* Output and update for function-call system: '<S1>/Init' */
void MisfOBD2_Init(void)
{
  int32_T i;

  {
    /* user code (Output function Header for TID1) */

    /* System '<S1>/Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    MisfOBD2_initialize();

    /* Constant: '<S3>/ID_MISF_OBD2' */
    IDMisfOBD2 = ID_MISF_OBD2;

    /* Constant: '<S3>/ZERO' */
    FlgCatMisfOBD2 = 0U;

    /* Constant: '<S3>/ZERO1' */
    MisfCylCAT = 0;

    /* Constant: '<S3>/ZERO10' */
    NRevCAT = 0U;

    /* Constant: '<S3>/ZERO11' */
    NRevEmiss = 0U;

    /* Constant: '<S3>/ZERO12' */
    SumCatMisfOBD2 = 0U;

    /* Constant: '<S3>/ZERO13' */
    SumEmissMisfOBD2 = 0U;

    /* Constant: '<S3>/ZERO14' */
    FlgMisfDiagOn = 0U;

    /* Constant: '<S3>/ZERO3' */
    CntEmissOBD2 = 0U;
    for (i = 0; i < 4; i++) {
      /* Constant: '<S3>/ZERO2' */
      VtCntMisfEmiss[(i)] = 0U;

      /* Constant: '<S3>/ZERO4' incorporates:
       *  Constant: '<S3>/ZERO2'
       */
      VtCntMisfCAT[(i)] = 0U;
    }

    /* Constant: '<S3>/ZERO5' */
    FlgEmissMisfOBD2 = 0U;

    /* Constant: '<S3>/ZERO6' */
    EnMisfCATOBD2 = 0U;

    /* Constant: '<S3>/ZERO7' */
    EnMisfEmissOBD2 = 0U;

    /* Constant: '<S3>/ZERO8' */
    NRevOBD2Emiss = 0U;

    /* Constant: '<S3>/ZERO9' */
    MisfCylEmiss = 0;

    /* user code (Output function Trailer for TID1) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/*
 * System initialize for atomic system:
 *    '<S14>/Cyl_Counter'
 *    '<S15>/Cyl_Counter'
 *    '<S16>/Cyl_Counter'
 *    '<S17>/Cyl_Counter'
 */
void MisfOBD2_Cyl_Counter_Init(uint16_T *rty_cntMisfCAT, uint16_T
  *rty_cntMisfEmiss, rtDW_Cyl_Counter_MisfOBD2_T *localDW)
{
  localDW->is_active_c4_MisfOBD2 = 0U;
  *rty_cntMisfCAT = 0U;
  *rty_cntMisfEmiss = 0U;
}

/*
 * Output and update for atomic system:
 *    '<S14>/Cyl_Counter'
 *    '<S15>/Cyl_Counter'
 *    '<S16>/Cyl_Counter'
 *    '<S17>/Cyl_Counter'
 */
void MisfOBD2_Cyl_Counter(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
  rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_Cyl_Counter_MisfOBD2_T *localDW)
{
  boolean_T guard1 = false;

  /* Chart: '<S14>/Cyl_Counter' */
  /* Gateway: MisfOBD2/PreTdc/Counter_Misf/fc_1/Cyl_Counter */
  /* During: MisfOBD2/PreTdc/Counter_Misf/fc_1/Cyl_Counter */
  if (((uint32_T)localDW->is_active_c4_MisfOBD2) == 0U) {
    /* Entry: MisfOBD2/PreTdc/Counter_Misf/fc_1/Cyl_Counter */
    localDW->is_active_c4_MisfOBD2 = 1U;

    /* Entry Internal: MisfOBD2/PreTdc/Counter_Misf/fc_1/Cyl_Counter */
    /* Entry Internal 'CAT': '<S18>:20' */
    /* Transition: '<S18>:43' */
    if (rtu_misfEv) {
      /* Transition: '<S18>:51' */
      *rty_cntMisfCAT = (uint16_T)((int32_T)(((int32_T)(*rty_cntMisfCAT)) + 1));
    } else {
      /* Transition: '<S18>:50' */
    }

    /* Entry Internal 'EMISSIONS': '<S18>:37' */
    /* Transition: '<S18>:55' */
    if (rtu_misfEv) {
      /* Transition: '<S18>:54' */
      *rty_cntMisfEmiss = (uint16_T)((int32_T)(((int32_T)(*rty_cntMisfEmiss)) +
        1));
    } else {
      /* Transition: '<S18>:52' */
    }
  } else {
    /* During 'CAT': '<S18>:20' */
    /* During 'ST_CAT': '<S18>:41' */
    /* Transition: '<S18>:42' */
    guard1 = false;
    if (((int32_T)rtu_resetCAT) == 1) {
      /* Transition: '<S18>:4' */
      *rty_cntMisfCAT = 0U;

      /* Transition: '<S18>:8' */
      guard1 = true;
    } else {
      /* Transition: '<S18>:6' */
      if (((int32_T)rtu_resetCAT) == 0) {
        /* Transition: '<S18>:9' */
        guard1 = true;
      } else {
        /* Transition: '<S18>:57' */
        /* Transition: '<S18>:58' */
        /* Transition: '<S18>:15' */
      }
    }

    if (guard1) {
      if (rtu_misfEv) {
        /* Transition: '<S18>:13' */
        *rty_cntMisfCAT = (uint16_T)((int32_T)(((int32_T)(*rty_cntMisfCAT)) + 1));

        /* Transition: '<S18>:16' */
      } else {
        /* Transition: '<S18>:12' */
        /* Transition: '<S18>:15' */
      }
    }

    /* Transition: '<S18>:48' */
    /* During 'EMISSIONS': '<S18>:37' */
    /* During 'ST_EMISS': '<S18>:44' */
    /* Transition: '<S18>:45' */
    guard1 = false;
    if (((int32_T)rtu_resetEmiss) == 1) {
      /* Transition: '<S18>:26' */
      *rty_cntMisfEmiss = 0U;

      /* Transition: '<S18>:25' */
      guard1 = true;
    } else {
      /* Transition: '<S18>:27' */
      if (((int32_T)rtu_resetEmiss) == 0) {
        /* Transition: '<S18>:29' */
        guard1 = true;
      } else {
        /* Transition: '<S18>:60' */
        /* Transition: '<S18>:61' */
        /* Transition: '<S18>:32' */
      }
    }

    if (guard1) {
      if (rtu_misfEv) {
        /* Transition: '<S18>:35' */
        *rty_cntMisfEmiss = (uint16_T)((int32_T)(((int32_T)(*rty_cntMisfEmiss))
          + 1));

        /* Transition: '<S18>:34' */
      } else {
        /* Transition: '<S18>:36' */
        /* Transition: '<S18>:32' */
      }
    }

    /* Transition: '<S18>:47' */
  }

  /* End of Chart: '<S14>/Cyl_Counter' */
}

/* System initialize for function-call system: '<S8>/fc_1' */
void MisfOBD2_fc_1_Init(uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_1_MisfOBD2_T *localDW)
{
  /* SystemInitialize for Chart: '<S14>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter_Init(rty_cntMisfCAT, rty_cntMisfEmiss,
    &localDW->sf_Cyl_Counter);
}

/* Output and update for function-call system: '<S8>/fc_1' */
void MisfOBD2_fc_1(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
                   rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T
                   *rty_cntMisfEmiss, rtDW_fc_1_MisfOBD2_T *localDW)
{
  /* Chart: '<S14>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter(rtu_misfEv, rtu_resetCAT, rtu_resetEmiss, rty_cntMisfCAT,
                       rty_cntMisfEmiss, &localDW->sf_Cyl_Counter);
}

/* System initialize for function-call system: '<S8>/fc_2' */
void MisfOBD2_fc_2_Init(uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_2_MisfOBD2_T *localDW)
{
  /* SystemInitialize for Chart: '<S15>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter_Init(rty_cntMisfCAT, rty_cntMisfEmiss,
    &localDW->sf_Cyl_Counter);
}

/* Output and update for function-call system: '<S8>/fc_2' */
void MisfOBD2_fc_2(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
                   rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T
                   *rty_cntMisfEmiss, rtDW_fc_2_MisfOBD2_T *localDW)
{
  /* Chart: '<S15>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter(rtu_misfEv, rtu_resetCAT, rtu_resetEmiss, rty_cntMisfCAT,
                       rty_cntMisfEmiss, &localDW->sf_Cyl_Counter);
}

/* System initialize for function-call system: '<S8>/fc_3' */
void MisfOBD2_fc_3_Init(uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_3_MisfOBD2_T *localDW)
{
  /* SystemInitialize for Chart: '<S16>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter_Init(rty_cntMisfCAT, rty_cntMisfEmiss,
    &localDW->sf_Cyl_Counter);
}

/* Output and update for function-call system: '<S8>/fc_3' */
void MisfOBD2_fc_3(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
                   rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T
                   *rty_cntMisfEmiss, rtDW_fc_3_MisfOBD2_T *localDW)
{
  /* Chart: '<S16>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter(rtu_misfEv, rtu_resetCAT, rtu_resetEmiss, rty_cntMisfCAT,
                       rty_cntMisfEmiss, &localDW->sf_Cyl_Counter);
}

/* System initialize for function-call system: '<S8>/fc_4' */
void MisfOBD2_fc_4_Init(uint16_T *rty_cntMisfCAT, uint16_T *rty_cntMisfEmiss,
  rtDW_fc_4_MisfOBD2_T *localDW)
{
  /* SystemInitialize for Chart: '<S17>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter_Init(rty_cntMisfCAT, rty_cntMisfEmiss,
    &localDW->sf_Cyl_Counter);
}

/* Output and update for function-call system: '<S8>/fc_4' */
void MisfOBD2_fc_4(boolean_T rtu_misfEv, uint8_T rtu_resetCAT, uint8_T
                   rtu_resetEmiss, uint16_T *rty_cntMisfCAT, uint16_T
                   *rty_cntMisfEmiss, rtDW_fc_4_MisfOBD2_T *localDW)
{
  /* Chart: '<S17>/Cyl_Counter' */
  MisfOBD2_Cyl_Counter(rtu_misfEv, rtu_resetCAT, rtu_resetEmiss, rty_cntMisfCAT,
                       rty_cntMisfEmiss, &localDW->sf_Cyl_Counter);
}

/*
 * System initialize for atomic system:
 *    '<S11>/Sum'
 *    '<S12>/Sum'
 */
void MisfOBD2_Sum_Init(uint16_T *rty_sum)
{
  *rty_sum = 0U;
}

/*
 * Output and update for atomic system:
 *    '<S11>/Sum'
 *    '<S12>/Sum'
 */
void MisfOBD2_Sum(const uint16_T rtu_in[4], uint16_T *rty_sum)
{
  uint8_T i;

  /* Chart: '<S11>/Sum' */
  /* Gateway: MisfOBD2/PreTdc/fc_ThrCAT/Sum */
  /* During: MisfOBD2/PreTdc/fc_ThrCAT/Sum */
  /* Entry Internal: MisfOBD2/PreTdc/fc_ThrCAT/Sum */
  /* Transition: '<S29>:2' */
  i = 0U;
  *rty_sum = 0U;
  while (i < N_CYLINDER) {
    /* Transition: '<S29>:7' */
    *rty_sum += rtu_in[i];
    i = (uint8_T)((int32_T)(((int32_T)i) + 1));
  }

  /* End of Chart: '<S11>/Sum' */
  /* Transition: '<S29>:4' */
}

/* System initialize for function-call system: '<S5>/fc_ThrCAT' */
void MisfOBD2_fc_ThrCAT_Init(int8_T *rty_MisfCylCAT)
{
  uint16_T rtb_sum;

  /* SystemInitialize for Chart: '<S11>/Calc_CylMisfire' */
  *rty_MisfCylCAT = 0;

  /* SystemInitialize for Chart: '<S11>/Sum' */
  MisfOBD2_Sum_Init(&rtb_sum);
}

/* Output and update for function-call system: '<S5>/fc_ThrCAT' */
void MisfOBD2_fc_ThrCAT(const uint16_T rtu_vtCntMisfCAT[4], uint16_T
  rtu_index_rpm, uint16_T rtu_ratio_rpm, uint16_T rtu_index_load, uint16_T
  rtu_ratio_load, uint8_T rtu_resetCAT, uint8_T rtu_resSync, uint16_T
  rty_VtCntMisfCAT[4], uint8_T *rty_FlgCatMisfOBD2, int8_T *rty_MisfCylCAT,
  uint16_T *rty_SumCatMisfOBD2, rtDW_fc_ThrCAT_MisfOBD2_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_IR_U16;
  uint32_T perc;
  uint8_T rtb_Memory1;
  uint16_T rtb_sum;
  uint8_T rtb_Conversion6;
  int32_T i;
  uint8_T Memory_PreviousInput;
  uint8_T Memory1_PreviousInput;

  /* Memory: '<S11>/Memory1' */
  Memory1_PreviousInput = localDW->Memory1_PreviousInput;

  /* Memory: '<S11>/Memory' */
  Memory_PreviousInput = localDW->Memory_PreviousInput;

  /* Inport: '<S11>/vtCntMisfCAT' */
  for (i = 0; i < 4; i++) {
    rty_VtCntMisfCAT[i] = rtu_vtCntMisfCAT[i];
  }

  /* End of Inport: '<S11>/vtCntMisfCAT' */

  /* Chart: '<S11>/Sum' */
  MisfOBD2_Sum(rty_VtCntMisfCAT, &rtb_sum);

  /* Switch: '<S11>/Switch' incorporates:
   *  Constant: '<S11>/Constant'
   */
  if (((int32_T)rtu_resSync) != 0) {
    rtb_sum = 0U;
  }

  /* End of Switch: '<S11>/Switch' */

  /* DataTypeConversion: '<S28>/Conversion6' incorporates:
   *  Constant: '<S11>/BKRPMMISFOBD2_dim'
   */
  rtb_Conversion6 = (uint8_T)BKRPMMISFOBD2_dim;

  /* DataTypeConversion: '<S28>/Conversion7' incorporates:
   *  Constant: '<S11>/BKLOADMISFOBD2_dim'
   */
  rtb_Memory1 = (uint8_T)BKLOADMISFOBD2_dim;

  /* S-Function (Look2D_IR_U16): '<S28>/Look2D_IR_U16' incorporates:
   *  Constant: '<S11>/TBMISFCATOBD2'
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBMISFCATOBD2[0], rtu_index_rpm,
                rtu_ratio_rpm, rtb_Conversion6, rtu_index_load, rtu_ratio_load,
                rtb_Memory1);

  /* Memory: '<S11>/Memory' */
  rtb_Memory1 = Memory_PreviousInput;

  /* RelationalOperator: '<S11>/Relational Operator2' */
  Memory_PreviousInput = (uint8_T)((rtb_sum >= ((uint16_T)rtb_Memory1)) ? 1 : 0);

  /* Memory: '<S11>/Memory1' */
  rtb_Memory1 = Memory1_PreviousInput;

  /* Logic: '<S11>/Logical Operator' incorporates:
   *  Logic: '<S11>/Logical Operator1'
   *  RelationalOperator: '<S11>/Relational Operator1'
   */
  Memory1_PreviousInput = (uint8_T)(((rtb_sum >= rtb_Look2D_IR_U16) ||
    ((((int32_T)Memory_PreviousInput) != 0) && (((int32_T)rtb_Memory1) != 0))) ?
    1 : 0);

  /* Chart: '<S11>/Calc_CylMisfire' */
  /* Gateway: MisfOBD2/PreTdc/fc_ThrCAT/Calc_CylMisfire */
  /* During: MisfOBD2/PreTdc/fc_ThrCAT/Calc_CylMisfire */
  /* Entry Internal: MisfOBD2/PreTdc/fc_ThrCAT/Calc_CylMisfire */
  /* Transition: '<S25>:2' */
  rtb_Conversion6 = 0U;
  *rty_MisfCylCAT = -1;
  perc = ((uint32_T)rtb_sum) * ((uint32_T)CYLPERCMISFOBD2);
  while (rtb_Conversion6 < N_CYLINDER) {
    /* Transition: '<S25>:5' */
    if (((((uint32_T)rty_VtCntMisfCAT[rtb_Conversion6]) * ((uint32_T)((uint8_T)
            PERC_MISF_SCALE))) > perc) && (((int32_T)Memory1_PreviousInput) != 0))
    {
      /* Transition: '<S25>:9' */
      *rty_MisfCylCAT = (int8_T)rtb_Conversion6;
    } else {
      /* Transition: '<S25>:10' */
    }

    /* Transition: '<S25>:8' */
    rtb_Conversion6 = (uint8_T)((int32_T)(((int32_T)rtb_Conversion6) + 1));
  }

  /* End of Chart: '<S11>/Calc_CylMisfire' */

  /* Outputs for Enabled SubSystem: '<S11>/Latch_Status' incorporates:
   *  EnablePort: '<S27>/Enable'
   */
  /* Logic: '<S11>/Logical Operator2' incorporates:
   *  Constant: '<S26>/Constant'
   *  Inport: '<S27>/flg'
   *  RelationalOperator: '<S26>/Compare'
   */
  /* Transition: '<S25>:4' */
  if ((((int32_T)Memory1_PreviousInput) != 0) || (((int32_T)rtu_resetCAT) == 1))
  {
    *rty_FlgCatMisfOBD2 = Memory1_PreviousInput;
  }

  /* End of Logic: '<S11>/Logical Operator2' */
  /* End of Outputs for SubSystem: '<S11>/Latch_Status' */

  /* DataTypeConversion: '<S11>/Data Type Conversion1' */
  *rty_SumCatMisfOBD2 = rtb_sum;

  /* Memory: '<S11>/Memory' */
  localDW->Memory_PreviousInput = Memory_PreviousInput;

  /* Memory: '<S11>/Memory1' */
  localDW->Memory1_PreviousInput = Memory1_PreviousInput;
}

/* System initialize for function-call system: '<S5>/fc_ThrEmiss' */
void MisfOBD2_fc_ThrEmiss_Init(uint8_T *rty_FlgEmissMisfOBD2, int8_T
  *rty_MisfCylEmiss, uint16_T *rty_CntEmissOBD2)
{
  uint16_T rtb_sum;

  /* SystemInitialize for Chart: '<S12>/Calc_CylMisfire' */
  *rty_MisfCylEmiss = 0;

  /* SystemInitialize for Chart: '<S12>/Calc_EmissFlg' */
  *rty_FlgEmissMisfOBD2 = 0U;
  *rty_CntEmissOBD2 = 0U;

  /* SystemInitialize for Chart: '<S12>/Sum' */
  MisfOBD2_Sum_Init(&rtb_sum);
}

/* Output and update for function-call system: '<S5>/fc_ThrEmiss' */
void MisfOBD2_fc_ThrEmiss(const uint16_T rtu_vtCntMisfEmiss[4], uint16_T
  rtu_index_rpm, uint16_T rtu_ratio_rpm, uint16_T rtu_index_load, uint16_T
  rtu_ratio_load, uint8_T rtu_resSync, uint32_T *rty_cntClearDiag, uint16_T
  rty_VtCntMisfEmiss[4], uint8_T *rty_FlgEmissMisfOBD2, int8_T *rty_MisfCylEmiss,
  uint16_T *rty_CntEmissOBD2, uint16_T *rty_SumEmissMisfOBD2,
  rtDW_fc_ThrEmiss_MisfOBD2_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_IR_U16_bvs;
  int16_T rtb_LookUp_S16_S16;
  uint16_T tmp;
  uint32_T perc;
  uint16_T rtb_sum;
  uint8_T rtb_Conversion6;
  uint8_T rtb_Conversion3;
  int16_T rtb_Add;
  int32_T i;

  /* Inport: '<S12>/vtCntMisfEmiss' */
  for (i = 0; i < 4; i++) {
    rty_VtCntMisfEmiss[i] = rtu_vtCntMisfEmiss[i];
  }

  /* End of Inport: '<S12>/vtCntMisfEmiss' */

  /* Chart: '<S12>/Sum' */
  MisfOBD2_Sum(rty_VtCntMisfEmiss, &rtb_sum);

  /* DataTypeConversion: '<S35>/Conversion6' incorporates:
   *  Constant: '<S12>/BKRPMMISFOBD2_dim'
   */
  rtb_Conversion6 = (uint8_T)BKRPMMISFOBD2_dim;

  /* DataTypeConversion: '<S35>/Conversion7' incorporates:
   *  Constant: '<S12>/BKLOADMISFOBD2_dim'
   */
  rtb_Conversion3 = (uint8_T)BKLOADMISFOBD2_dim;

  /* S-Function (Look2D_IR_U16): '<S35>/Look2D_IR_U16' incorporates:
   *  Constant: '<S12>/TBMISFEMISSOBD2'
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16_bvs, &TBMISFEMISSOBD2[0], rtu_index_rpm,
                rtu_ratio_rpm, rtb_Conversion6, rtu_index_load, rtu_ratio_load,
                rtb_Conversion3);

  /* Sum: '<S12>/Add' */
  rtb_Add = (int16_T)(((int16_T)rtb_sum) - ((int16_T)rtb_Look2D_IR_U16_bvs));

  /* DataTypeConversion: '<S36>/Conversion3' incorporates:
   *  Constant: '<S12>/BKSTEPEMISSOBD2_dim'
   */
  rtb_Conversion3 = (uint8_T)BKSTEPEMISSOBD2_dim;

  /* S-Function (LookUp_S16_S16): '<S36>/LookUp_S16_S16' incorporates:
   *  Constant: '<S12>/BKSTEPEMISSOBD2'
   *  Constant: '<S12>/VTSTEPEMISSOBD2'
   */
  LookUp_S16_S16( &rtb_LookUp_S16_S16, &VTSTEPEMISSOBD2[0], rtb_Add,
                 &BKSTEPEMISSOBD2[0], rtb_Conversion3);

  /* Chart: '<S12>/Calc_EmissFlg' */
  /* Gateway: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_EmissFlg */
  /* During: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_EmissFlg */
  /* Entry Internal: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_EmissFlg */
  /* Transition: '<S33>:24' */
  if (((int32_T)rtu_resSync) != 0) {
    /* Transition: '<S33>:21' */
    *rty_FlgEmissMisfOBD2 = 0U;
    *rty_CntEmissOBD2 = 0U;
  } else {
    /* Transition: '<S33>:25' */
  }

  /* Transition: '<S33>:4' */
  i = ((int32_T)(*rty_CntEmissOBD2)) + ((int32_T)rtb_LookUp_S16_S16);
  if (i < 0) {
    i = 0;
  } else {
    if (i > 65535) {
      i = 65535;
    }
  }

  *rty_CntEmissOBD2 = (uint16_T)i;
  if ((*rty_CntEmissOBD2) >= THREMISSOBD2) {
    /* Transition: '<S33>:6' */
    *rty_CntEmissOBD2 = THREMISSOBD2;
    *rty_FlgEmissMisfOBD2 = 1U;
  } else {
    /* Transition: '<S33>:7' */
  }

  /* End of Chart: '<S12>/Calc_EmissFlg' */

  /* Chart: '<S31>/Calc_Misfire_Counters' */
  /* Gateway: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_Counters/Calc_Misfire_Counters */
  /* During: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_Counters/Calc_Misfire_Counters */
  /* Entry Internal: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_Counters/Calc_Misfire_Counters */
  /* Transition: '<S38>:2' */
  rtb_Conversion6 = 0U;
  tmp = 0U;
  while (rtb_Conversion6 < N_CYLINDER) {
    /* Transition: '<S38>:6' */
    EEVtCntMisfCyl[(rtb_Conversion6)] = (uint16_T)(EEVtCntMisfCyl
      [(rtb_Conversion6)] + rty_VtCntMisfEmiss[rtb_Conversion6]);
    tmp += EEVtCntMisfCyl[(rtb_Conversion6)];
    rtb_Conversion6 = (uint8_T)((int32_T)(((int32_T)rtb_Conversion6) + 1));
  }

  /* Transition: '<S38>:14' */
  EECntMisf = tmp;

  /* End of Chart: '<S31>/Calc_Misfire_Counters' */

  /* Chart: '<S12>/Calc_CylMisfire' */
  /* Gateway: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_CylMisfire */
  /* During: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_CylMisfire */
  /* Entry Internal: MisfOBD2/PreTdc/fc_ThrEmiss/Calc_CylMisfire */
  /* Transition: '<S32>:2' */
  rtb_Conversion6 = 0U;
  *rty_MisfCylEmiss = -1;
  perc = ((uint32_T)rtb_sum) * ((uint32_T)CYLPERCMISFOBD2);
  while (rtb_Conversion6 < N_CYLINDER) {
    /* Transition: '<S32>:5' */
    if (((((uint32_T)rty_VtCntMisfEmiss[rtb_Conversion6]) * ((uint32_T)((uint8_T)
            PERC_MISF_SCALE))) >= perc) && (rtb_LookUp_S16_S16 >= 0)) {
      /* Transition: '<S32>:9' */
      *rty_MisfCylEmiss = (int8_T)rtb_Conversion6;
    } else {
      /* Transition: '<S32>:10' */
    }

    /* Transition: '<S32>:8' */
    rtb_Conversion6 = (uint8_T)((int32_T)(((int32_T)rtb_Conversion6) + 1));
  }

  /* End of Chart: '<S12>/Calc_CylMisfire' */

  /* Sum: '<S12>/Add1' incorporates:
   *  Constant: '<S34>/Constant'
   *  Logic: '<S12>/Logical Operator'
   *  Logic: '<S12>/Logical Operator1'
   *  Memory: '<S12>/Memory8'
   *  RelationalOperator: '<S34>/Compare'
   */
  /* Transition: '<S32>:4' */
  *rty_cntClearDiag = ((uint32_T)(((((int32_T)(*rty_CntEmissOBD2)) == 0) &&
    (((int32_T)rtu_resSync) == 0)) ? 1 : 0)) + localDW->Memory8_PreviousInput;

  /* DataTypeConversion: '<S12>/Data Type Conversion1' */
  *rty_SumEmissMisfOBD2 = rtb_sum;

  /* Update for Memory: '<S12>/Memory8' */
  localDW->Memory8_PreviousInput = *rty_cntClearDiag;
}

/* System initialize for function-call system: '<S5>/fc_revolution' */
void MisfOBD2_fc_revolution_Init(uint8_T *rty_resetEmiss, uint8_T *rty_resetCAT,
  uint16_T *rty_NRevCAT, uint16_T *rty_NRevEmiss, int8_T *rty_6, uint8_T *rty_10,
  int8_T *rty_11, uint16_T *rty_12)
{
  *rty_resetCAT = 0U;
  *rty_NRevCAT = 0U;

  /* SystemInitialize for Chart: '<S13>/OBD2_CAT' incorporates:
   *  SubSystem: '<S5>/fc_ThrCAT'
   */
  MisfOBD2_fc_ThrCAT_Init(rty_6);
  *rty_resetEmiss = 0U;
  *rty_NRevEmiss = 0U;

  /* SystemInitialize for Chart: '<S13>/OBD2_EMISSIONS' incorporates:
   *  SubSystem: '<S5>/fc_ThrEmiss'
   */
  MisfOBD2_fc_ThrEmiss_Init(rty_10, rty_11, rty_12);
}

/* Output and update for function-call system: '<S5>/fc_revolution' */
void MisfOBD2_fc_revolution(uint16_T rtu_NRevOBD2Cat, uint16_T rtu_NRevOBD2Emiss,
  boolean_T rtu_EnMisfCATOBD2, boolean_T rtu_EnMisfEmissOBD, uint16_T rtu_Rpm,
  uint16_T rtu_Load, uint8_T rtu_resSync, const uint16_T rtu_7[4], const
  uint16_T rtu_8[4], uint8_T *rty_resetEmiss, uint8_T *rty_resetCAT, uint16_T
  *rty_NRevCAT, uint16_T *rty_NRevEmiss, uint16_T rty_4[4], uint8_T *rty_5,
  int8_T *rty_6, uint16_T *rty_7, uint32_T *rty_8, uint16_T rty_9[4], uint8_T
  *rty_10, int8_T *rty_11, uint16_T *rty_12, uint16_T *rty_13,
  rtDW_fc_revolution_MisfOBD2_T *localDW)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o_hhg;
  uint8_T rtb_DataTypeConversion8;

  /* DataTypeConversion: '<S45>/Data Type Conversion4' */
  localDW->DataTypeConversion1_bti = rtu_Rpm;

  /* DataTypeConversion: '<S45>/Data Type Conversion8' incorporates:
   *  Constant: '<S41>/BKRPMMISFOBD2_dim'
   */
  rtb_DataTypeConversion8 = (uint8_T)BKRPMMISFOBD2_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S45>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S41>/BKRPMMISFOBD2'
   */
  PreLookUpIdSearch_U16( &localDW->PreLookUpIdSearch_U16_o1,
                        &localDW->DataTypeConversion1_bti,
                        localDW->DataTypeConversion1_bti, &BKRPMMISFOBD2[0],
                        rtb_DataTypeConversion8);

  /* DataTypeConversion: '<S44>/Data Type Conversion8' incorporates:
   *  Constant: '<S41>/BKLOADMISFOBD2_dim'
   */
  rtb_DataTypeConversion8 = (uint8_T)BKLOADMISFOBD2_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S44>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S41>/BKLOADMISFOBD2'
   */
  PreLookUpIdSearch_U16( &localDW->PreLookUpIdSearch_U16_o1_go0,
                        &rtb_PreLookUpIdSearch_U16_o_hhg, rtu_Load,
                        &BKLOADMISFOBD2[0], rtb_DataTypeConversion8);

  /* Chart: '<S13>/OBD2_CAT' incorporates:
   *  DataTypeConversion: '<S44>/Data Type Conversion1'
   *  DataTypeConversion: '<S45>/Data Type Conversion1'
   */
  /* Gateway: MisfOBD2/PreTdc/fc_revolution/OBD2_CAT */
  /* During: MisfOBD2/PreTdc/fc_revolution/OBD2_CAT */
  /* Entry Internal: MisfOBD2/PreTdc/fc_revolution/OBD2_CAT */
  /* Transition: '<S42>:46' */
  if ((((int32_T)rtu_resSync) != 0) || (!rtu_EnMisfCATOBD2)) {
    /* Transition: '<S42>:43' */
    *rty_NRevCAT = 0U;
    *rty_resetCAT = 1U;

    /* Outputs for Function Call SubSystem: '<S5>/fc_ThrCAT' */
    /* Event: '<S42>:12' */
    MisfOBD2_fc_ThrCAT(rtu_7, localDW->PreLookUpIdSearch_U16_o1,
                       localDW->DataTypeConversion1_bti,
                       localDW->PreLookUpIdSearch_U16_o1_go0,
                       rtb_PreLookUpIdSearch_U16_o_hhg, *rty_resetCAT,
                       rtu_resSync, rty_4, rty_5, rty_6, rty_7,
                       &localDW->fc_ThrCAT);

    /* End of Outputs for SubSystem: '<S5>/fc_ThrCAT' */
    /* Transition: '<S42>:42' */
    /* Transition: '<S42>:45' */
  } else {
    /* Transition: '<S42>:40' */
    /* Transition: '<S42>:28' */
    if ((*rty_NRevCAT) >= rtu_NRevOBD2Cat) {
      /* Transition: '<S42>:4' */
      *rty_NRevCAT = 0U;
      *rty_resetCAT = 1U;

      /* Transition: '<S42>:8' */
    } else {
      /* Transition: '<S42>:6' */
      *rty_NRevCAT = (uint16_T)((int32_T)(((int32_T)(*rty_NRevCAT)) + 1));
      *rty_resetCAT = 0U;

      /* Transition: '<S42>:9' */
    }

    /* Outputs for Function Call SubSystem: '<S5>/fc_ThrCAT' */
    /* Transition: '<S42>:25' */
    /* Event: '<S42>:12' */
    MisfOBD2_fc_ThrCAT(rtu_7, localDW->PreLookUpIdSearch_U16_o1,
                       localDW->DataTypeConversion1_bti,
                       localDW->PreLookUpIdSearch_U16_o1_go0,
                       rtb_PreLookUpIdSearch_U16_o_hhg, *rty_resetCAT, 0, rty_4,
                       rty_5, rty_6, rty_7, &localDW->fc_ThrCAT);

    /* End of Outputs for SubSystem: '<S5>/fc_ThrCAT' */
  }

  /* End of Chart: '<S13>/OBD2_CAT' */

  /* Chart: '<S13>/OBD2_EMISSIONS' incorporates:
   *  DataTypeConversion: '<S44>/Data Type Conversion1'
   *  DataTypeConversion: '<S45>/Data Type Conversion1'
   */
  /* Gateway: MisfOBD2/PreTdc/fc_revolution/OBD2_EMISSIONS */
  /* During: MisfOBD2/PreTdc/fc_revolution/OBD2_EMISSIONS */
  /* Entry Internal: MisfOBD2/PreTdc/fc_revolution/OBD2_EMISSIONS */
  /* Transition: '<S43>:74' */
  if (((int32_T)rtu_resSync) != 0) {
    /* Transition: '<S43>:70' */
    *rty_NRevEmiss = 0U;
    *rty_resetEmiss = 1U;

    /* Outputs for Function Call SubSystem: '<S5>/fc_ThrEmiss' */
    /* Event: '<S43>:59' */
    MisfOBD2_fc_ThrEmiss(rtu_8, localDW->PreLookUpIdSearch_U16_o1,
                         localDW->DataTypeConversion1_bti,
                         localDW->PreLookUpIdSearch_U16_o1_go0,
                         rtb_PreLookUpIdSearch_U16_o_hhg, rtu_resSync, rty_8,
                         rty_9, rty_10, rty_11, rty_12, rty_13,
                         &localDW->fc_ThrEmiss);

    /* End of Outputs for SubSystem: '<S5>/fc_ThrEmiss' */
    /* Transition: '<S43>:71' */
    /* Transition: '<S43>:68' */
  } else {
    /* Transition: '<S43>:73' */
    if (!rtu_EnMisfEmissOBD) {
      /* Transition: '<S43>:67' */
      *rty_resetEmiss = 2U;

      /* Transition: '<S43>:68' */
    } else {
      /* Transition: '<S43>:65' */
      if ((*rty_NRevEmiss) >= rtu_NRevOBD2Emiss) {
        /* Transition: '<S43>:50' */
        *rty_NRevEmiss = 0U;
        *rty_resetEmiss = 1U;

        /* Outputs for Function Call SubSystem: '<S5>/fc_ThrEmiss' */
        /* Event: '<S43>:59' */
        MisfOBD2_fc_ThrEmiss(rtu_8, localDW->PreLookUpIdSearch_U16_o1,
                             localDW->DataTypeConversion1_bti,
                             localDW->PreLookUpIdSearch_U16_o1_go0,
                             rtb_PreLookUpIdSearch_U16_o_hhg, 0, rty_8, rty_9,
                             rty_10, rty_11, rty_12, rty_13,
                             &localDW->fc_ThrEmiss);

        /* End of Outputs for SubSystem: '<S5>/fc_ThrEmiss' */
        /* Transition: '<S43>:53' */
      } else {
        /* Transition: '<S43>:54' */
        *rty_NRevEmiss = (uint16_T)((int32_T)(((int32_T)(*rty_NRevEmiss)) + 1));
        *rty_resetEmiss = 0U;

        /* Transition: '<S43>:49' */
      }

      /* Transition: '<S43>:62' */
    }
  }

  /* End of Chart: '<S13>/OBD2_EMISSIONS' */

  /* DataTypeConversion: '<S44>/Data Type Conversion1' */
  localDW->DataTypeConversion1 = rtb_PreLookUpIdSearch_U16_o_hhg;
}

/* System initialize for function-call system: '<S1>/PreTdc' */
void MisfOBD2_PreTdc_Init(void)
{
  int32_T i;
  MisfOBD2_DWork.ackSync = 0U;

  /* SystemInitialize for Chart: '<S5>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S8>/fc_1'
   */
  MisfOBD2_fc_1_Init(&MisfOBD2_DWork.cntMisfCAT_jbl,
                     &MisfOBD2_DWork.cntMisfEmiss_cud, &MisfOBD2_DWork.fc_1);

  /* SystemInitialize for Chart: '<S5>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S8>/fc_2'
   */
  MisfOBD2_fc_2_Init(&MisfOBD2_DWork.cntMisfCAT_fz1,
                     &MisfOBD2_DWork.cntMisfEmiss_kyl, &MisfOBD2_DWork.fc_2);

  /* SystemInitialize for Chart: '<S5>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S8>/fc_3'
   */
  MisfOBD2_fc_3_Init(&MisfOBD2_DWork.cntMisfCAT_e5z,
                     &MisfOBD2_DWork.cntMisfEmiss_hs5, &MisfOBD2_DWork.fc_3);

  /* SystemInitialize for Chart: '<S5>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S8>/fc_4'
   */
  MisfOBD2_fc_4_Init(&MisfOBD2_DWork.cntMisfCAT, &MisfOBD2_DWork.cntMisfEmiss,
                     &MisfOBD2_DWork.fc_4);

  /* SystemInitialize for Chart: '<S5>/Trigger_Cylinder' incorporates:
   *  SubSystem: '<S5>/fc_revolution'
   */
  MisfOBD2_fc_revolution_Init(&MisfOBD2_DWork.resetEmiss,
    &MisfOBD2_DWork.resetCAT, &MisfOBD2_DWork.NRevCAT_fez,
    &MisfOBD2_DWork.NRevEmiss_ebn, &MisfOBD2_DWork.MisfCylCAT_ptp,
    &MisfOBD2_DWork.FlgEmissMisfOBD2_ot3, &MisfOBD2_DWork.MisfCylEmiss_ne2,
    &MisfOBD2_DWork.CntEmissOBD2_p22);

  /* SystemInitialize for Chart: '<S9>/Misfire_Filter' */
  for (i = 0; i < 4; i++) {
    MisfOBD2_DWork.cntFiltMisfOBD2[i] = 0U;
  }

  /* End of SystemInitialize for Chart: '<S9>/Misfire_Filter' */
}

/* Output and update for function-call system: '<S1>/PreTdc' */
void MisfOBD2_PreTdc(void)
{
  uint8_T rtb_evFilter;
  uint16_T VectorConcatenate1[4];
  uint16_T VectorConcatenate[4];
  boolean_T LogicalOperator1;
  int32_T i;
  uint8_T AbsPreTdc_0;
  uint16_T Load_0;
  uint16_T Rpm_0;

  /* Inport: '<Root>/Rpm' */
  Rpm_0 = Rpm;

  /* Inport: '<Root>/Load' */
  Load_0 = Load;

  /* Inport: '<Root>/AbsPreTdc' */
  AbsPreTdc_0 = AbsPreTdc;

  /* Memory: '<S8>/Memory7' */
  VectorConcatenate1[0] = MisfOBD2_DWork.Memory7_PreviousInput;

  /* Memory: '<S8>/Memory6' */
  VectorConcatenate1[1] = MisfOBD2_DWork.Memory6_PreviousInput;

  /* Memory: '<S8>/Memory5' */
  VectorConcatenate1[2] = MisfOBD2_DWork.Memory5_PreviousInput;

  /* Memory: '<S8>/Memory4' */
  VectorConcatenate1[3] = MisfOBD2_DWork.Memory4_PreviousInput;

  /* Memory: '<S8>/Memory' */
  VectorConcatenate[0] = MisfOBD2_DWork.Memory_PreviousInput;

  /* Memory: '<S8>/Memory1' */
  VectorConcatenate[1] = MisfOBD2_DWork.Memory1_PreviousInput;

  /* Memory: '<S8>/Memory2' */
  VectorConcatenate[2] = MisfOBD2_DWork.Memory2_PreviousInput;

  /* Memory: '<S8>/Memory3' */
  VectorConcatenate[3] = MisfOBD2_DWork.Memory3_PreviousInput;

  /* Chart: '<S9>/Misfire_Filter' incorporates:
   *  Inport: '<Root>/AbsPreTdc'
   *  Inport: '<Root>/VtIdxCtfFlgBuff'
   *  Selector: '<S9>/Selector2'
   */
  /* Gateway: MisfOBD2/PreTdc/FOMisf/Misfire_Filter */
  /* During: MisfOBD2/PreTdc/FOMisf/Misfire_Filter */
  /* Entry Internal: MisfOBD2/PreTdc/FOMisf/Misfire_Filter */
  /* Transition: '<S24>:21' */
  if (((int32_T)VtIdxCtfFlgBuff[(AbsPreTdc_0)]) != 0) {
    /* Transition: '<S24>:5' */
    MisfOBD2_DWork.cntFiltMisfOBD2[AbsPreTdc_0] = NFILTMISFOBD2;
  } else {
    /* Transition: '<S24>:22' */
  }

  if (((int32_T)MisfOBD2_DWork.cntFiltMisfOBD2[AbsPreTdc_0]) != 0) {
    /* Transition: '<S24>:6' */
    i = ((int32_T)MisfOBD2_DWork.cntFiltMisfOBD2[AbsPreTdc_0]) - 1;
    if (i < 0) {
      i = 0;
    }

    MisfOBD2_DWork.cntFiltMisfOBD2[AbsPreTdc_0] = (uint8_T)i;
    rtb_evFilter = 0U;
  } else {
    /* Transition: '<S24>:11' */
    rtb_evFilter = 1U;
  }

  /* End of Chart: '<S9>/Misfire_Filter' */

  /* Switch: '<S9>/Switch' incorporates:
   *  Constant: '<S22>/Constant'
   *  Constant: '<S23>/Constant'
   *  Constant: '<S9>/TESTMISFOBD2'
   *  Inport: '<Root>/AbsPreTdc'
   *  Inport: '<Root>/StMisf'
   *  Inport: '<Root>/SymFOInjCutoff'
   *  Logic: '<S9>/Logical Operator'
   *  Logic: '<S9>/Logical Operator2'
   *  RelationalOperator: '<S22>/Compare'
   *  RelationalOperator: '<S23>/Compare'
   *  Selector: '<S9>/Selector1'
   *  Selector: '<S9>/Selector3'
   */
  if (((int32_T)TESTMISFOBD2) != 0) {
    LogicalOperator1 = (((int32_T)SymFOInjCutoff[(AbsPreTdc_0)]) != 0);
  } else {
    LogicalOperator1 = ((((int32_T)SymFOInjCutoff[(AbsPreTdc_0)]) != 0) ||
                        ((StMisf[(AbsPreTdc_0)] == BAD_COMB) || (StMisf
      [(AbsPreTdc_0)] == MISF)));
  }

  /* End of Switch: '<S9>/Switch' */

  /* Logic: '<S9>/Logical Operator1' */
  LogicalOperator1 = ((((int32_T)rtb_evFilter) != 0) && LogicalOperator1);

  /* Chart: '<S5>/Trigger_Cylinder' incorporates:
   *  Inport: '<Root>/AbsPreTdc'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/Rpm'
   */
  /* Gateway: MisfOBD2/PreTdc/Trigger_Cylinder */
  /* During: MisfOBD2/PreTdc/Trigger_Cylinder */
  /* Entry Internal: MisfOBD2/PreTdc/Trigger_Cylinder */
  /* Transition: '<S10>:34' */
  if (((int32_T)MisfOBD2_DWork.resSync) != 0) {
    /* Outputs for Function Call SubSystem: '<S5>/fc_revolution' */
    /* Transition: '<S10>:35' */
    /* Event: '<S10>:25' */
    MisfOBD2_fc_revolution(MisfOBD2_DWork.NRevOBD2Cat,
      MisfOBD2_DWork.NRevOBD2Emiss_kdt, MisfOBD2_DWork.EnMisfCATOBD2_eas,
      MisfOBD2_DWork.EnMisfEmissOBD2_ekh, Rpm_0, Load_0, MisfOBD2_DWork.resSync,
      VectorConcatenate, VectorConcatenate1, &MisfOBD2_DWork.resetEmiss,
      &MisfOBD2_DWork.resetCAT, &MisfOBD2_DWork.NRevCAT_fez,
      &MisfOBD2_DWork.NRevEmiss_ebn, MisfOBD2_DWork.vtCntMisfCAT,
      &MisfOBD2_DWork.flg, &MisfOBD2_DWork.MisfCylCAT_ptp,
      &MisfOBD2_DWork.DataTypeConversion1_ey0, &MisfOBD2_DWork.Add1,
      MisfOBD2_DWork.vtCntMisfEmiss, &MisfOBD2_DWork.FlgEmissMisfOBD2_ot3,
      &MisfOBD2_DWork.MisfCylEmiss_ne2, &MisfOBD2_DWork.CntEmissOBD2_p22,
      &MisfOBD2_DWork.DataTypeConversion1, &MisfOBD2_DWork.fc_revolution);

    /* End of Outputs for SubSystem: '<S5>/fc_revolution' */

    /* Outputs for Function Call SubSystem: '<S8>/fc_1' */
    /* Event: '<S10>:20' */
    MisfOBD2_fc_1(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                  MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT_jbl,
                  &MisfOBD2_DWork.cntMisfEmiss_cud, &MisfOBD2_DWork.fc_1);

    /* End of Outputs for SubSystem: '<S8>/fc_1' */

    /* Outputs for Function Call SubSystem: '<S8>/fc_2' */
    /* Event: '<S10>:21' */
    MisfOBD2_fc_2(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                  MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT_fz1,
                  &MisfOBD2_DWork.cntMisfEmiss_kyl, &MisfOBD2_DWork.fc_2);

    /* End of Outputs for SubSystem: '<S8>/fc_2' */

    /* Outputs for Function Call SubSystem: '<S8>/fc_3' */
    /* Event: '<S10>:22' */
    MisfOBD2_fc_3(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                  MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT_e5z,
                  &MisfOBD2_DWork.cntMisfEmiss_hs5, &MisfOBD2_DWork.fc_3);

    /* End of Outputs for SubSystem: '<S8>/fc_3' */

    /* Outputs for Function Call SubSystem: '<S8>/fc_4' */
    /* Event: '<S10>:23' */
    MisfOBD2_fc_4(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                  MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT,
                  &MisfOBD2_DWork.cntMisfEmiss, &MisfOBD2_DWork.fc_4);

    /* End of Outputs for SubSystem: '<S8>/fc_4' */
    MisfOBD2_DWork.ackSync = 1U;

    /* Transition: '<S10>:36' */
    /* Transition: '<S10>:17' */
    /* Transition: '<S10>:18' */
    /* Transition: '<S10>:19' */
    /* Transition: '<S10>:30' */
  } else {
    /* Transition: '<S10>:33' */
    MisfOBD2_DWork.ackSync = 0U;
    switch (AbsPreTdc_0) {
     case 0:
      /* Outputs for Function Call SubSystem: '<S5>/fc_revolution' */
      /* Transition: '<S10>:6' */
      /* Event: '<S10>:25' */
      MisfOBD2_fc_revolution(MisfOBD2_DWork.NRevOBD2Cat,
        MisfOBD2_DWork.NRevOBD2Emiss_kdt, MisfOBD2_DWork.EnMisfCATOBD2_eas,
        MisfOBD2_DWork.EnMisfEmissOBD2_ekh, Rpm_0, Load_0,
        MisfOBD2_DWork.resSync, VectorConcatenate, VectorConcatenate1,
        &MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.resetCAT,
        &MisfOBD2_DWork.NRevCAT_fez, &MisfOBD2_DWork.NRevEmiss_ebn,
        MisfOBD2_DWork.vtCntMisfCAT, &MisfOBD2_DWork.flg,
        &MisfOBD2_DWork.MisfCylCAT_ptp, &MisfOBD2_DWork.DataTypeConversion1_ey0,
        &MisfOBD2_DWork.Add1, MisfOBD2_DWork.vtCntMisfEmiss,
        &MisfOBD2_DWork.FlgEmissMisfOBD2_ot3, &MisfOBD2_DWork.MisfCylEmiss_ne2,
        &MisfOBD2_DWork.CntEmissOBD2_p22, &MisfOBD2_DWork.DataTypeConversion1,
        &MisfOBD2_DWork.fc_revolution);

      /* End of Outputs for SubSystem: '<S5>/fc_revolution' */

      /* Outputs for Function Call SubSystem: '<S8>/fc_1' */
      /* Event: '<S10>:20' */
      MisfOBD2_fc_1(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                    MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT_jbl,
                    &MisfOBD2_DWork.cntMisfEmiss_cud, &MisfOBD2_DWork.fc_1);

      /* End of Outputs for SubSystem: '<S8>/fc_1' */
      /* Transition: '<S10>:17' */
      /* Transition: '<S10>:18' */
      /* Transition: '<S10>:19' */
      /* Transition: '<S10>:30' */
      break;

     case 1:
      /* Outputs for Function Call SubSystem: '<S8>/fc_2' */
      /* Transition: '<S10>:7' */
      /* Transition: '<S10>:8' */
      /* Event: '<S10>:21' */
      MisfOBD2_fc_2(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                    MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT_fz1,
                    &MisfOBD2_DWork.cntMisfEmiss_kyl, &MisfOBD2_DWork.fc_2);

      /* End of Outputs for SubSystem: '<S8>/fc_2' */
      /* Transition: '<S10>:18' */
      /* Transition: '<S10>:19' */
      /* Transition: '<S10>:30' */
      break;

     case 2:
      /* Outputs for Function Call SubSystem: '<S8>/fc_3' */
      /* Transition: '<S10>:10' */
      /* Transition: '<S10>:12' */
      /* Event: '<S10>:22' */
      MisfOBD2_fc_3(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                    MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT_e5z,
                    &MisfOBD2_DWork.cntMisfEmiss_hs5, &MisfOBD2_DWork.fc_3);

      /* End of Outputs for SubSystem: '<S8>/fc_3' */
      /* Transition: '<S10>:19' */
      /* Transition: '<S10>:30' */
      break;

     case 3:
      /* Outputs for Function Call SubSystem: '<S8>/fc_4' */
      /* Transition: '<S10>:14' */
      /* Transition: '<S10>:16' */
      /* Event: '<S10>:23' */
      MisfOBD2_fc_4(LogicalOperator1, MisfOBD2_DWork.resetCAT,
                    MisfOBD2_DWork.resetEmiss, &MisfOBD2_DWork.cntMisfCAT,
                    &MisfOBD2_DWork.cntMisfEmiss, &MisfOBD2_DWork.fc_4);

      /* End of Outputs for SubSystem: '<S8>/fc_4' */
      /* Transition: '<S10>:30' */
      break;

     default:
      /* Transition: '<S10>:28' */
      /* Transition: '<S10>:29' */
      break;
    }
  }

  /* End of Chart: '<S5>/Trigger_Cylinder' */

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  FlgCatMisfOBD2 = MisfOBD2_DWork.flg;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  SumEmissMisfOBD2 = MisfOBD2_DWork.DataTypeConversion1;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  MisfCylCAT = MisfOBD2_DWork.MisfCylCAT_ptp;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  SumCatMisfOBD2 = MisfOBD2_DWork.DataTypeConversion1_ey0;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  NRevCAT = MisfOBD2_DWork.NRevCAT_fez;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  NRevEmiss = MisfOBD2_DWork.NRevEmiss_ebn;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  for (i = 0; i < 4; i++) {
    VtCntMisfCAT[(i)] = MisfOBD2_DWork.vtCntMisfCAT[i];
    VtCntMisfEmiss[(i)] = MisfOBD2_DWork.vtCntMisfEmiss[i];
  }

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  FlgEmissMisfOBD2 = MisfOBD2_DWork.FlgEmissMisfOBD2_ot3;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  MisfCylEmiss = MisfOBD2_DWork.MisfCylEmiss_ne2;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  CntEmissOBD2 = MisfOBD2_DWork.CntEmissOBD2_p22;

  /* SignalConversion generated from: '<S5>/Bus_PreTdc' */
  EnMisfOBD2 = MisfOBD2_DWork.EnMisfOBD2_brz;

  /* user code (Output function Trailer for TID2) */

  /* System '<S1>/PreTdc' */

  /* PILOTAGGIO USCITE - 10ms */

  /* Update for Memory: '<S8>/Memory7' */
  MisfOBD2_DWork.Memory7_PreviousInput = MisfOBD2_DWork.cntMisfEmiss_cud;

  /* Update for Memory: '<S8>/Memory6' */
  MisfOBD2_DWork.Memory6_PreviousInput = MisfOBD2_DWork.cntMisfEmiss_kyl;

  /* Update for Memory: '<S8>/Memory5' */
  MisfOBD2_DWork.Memory5_PreviousInput = MisfOBD2_DWork.cntMisfEmiss_hs5;

  /* Update for Memory: '<S8>/Memory4' */
  MisfOBD2_DWork.Memory4_PreviousInput = MisfOBD2_DWork.cntMisfEmiss;

  /* Update for Memory: '<S8>/Memory' */
  MisfOBD2_DWork.Memory_PreviousInput = MisfOBD2_DWork.cntMisfCAT_jbl;

  /* Update for Memory: '<S8>/Memory1' */
  MisfOBD2_DWork.Memory1_PreviousInput = MisfOBD2_DWork.cntMisfCAT_fz1;

  /* Update for Memory: '<S8>/Memory2' */
  MisfOBD2_DWork.Memory2_PreviousInput = MisfOBD2_DWork.cntMisfCAT_e5z;

  /* Update for Memory: '<S8>/Memory3' */
  MisfOBD2_DWork.Memory3_PreviousInput = MisfOBD2_DWork.cntMisfCAT;
}

/*
 * System initialize for atomic system:
 *    '<S50>/EnTrig'
 *    '<S50>/EnTrig1'
 */
void MisfOBD2_EnTrig_Init(uint8_T *rty_flg, rtDW_EnTrig_MisfOBD2_T *localDW)
{
  localDW->is_active_c14_MisfOBD2 = 0U;
  localDW->is_c14_MisfOBD2 = MisfOBD2_IN_NO_ACTIVE_CHILD_mrh;
  *rty_flg = 0U;
}

/*
 * Output and update for atomic system:
 *    '<S50>/EnTrig'
 *    '<S50>/EnTrig1'
 */
void MisfOBD2_EnTrig(uint8_T rtu_in, uint8_T rtu_resSync, uint8_T *rty_flg,
                     rtDW_EnTrig_MisfOBD2_T *localDW)
{
  uint8_T is_c14_MisfOBD2;
  is_c14_MisfOBD2 = localDW->is_c14_MisfOBD2;

  /* Chart: '<S50>/EnTrig' */
  /* Gateway: MisfOBD2/T10ms/Diag_OBD2/EnTrig */
  /* During: MisfOBD2/T10ms/Diag_OBD2/EnTrig */
  if (((uint32_T)localDW->is_active_c14_MisfOBD2) == 0U) {
    /* Entry: MisfOBD2/T10ms/Diag_OBD2/EnTrig */
    localDW->is_active_c14_MisfOBD2 = 1U;

    /* Entry Internal: MisfOBD2/T10ms/Diag_OBD2/EnTrig */
    /* Transition: '<S58>:7' */
    *rty_flg = 0U;
    is_c14_MisfOBD2 = MisfOBD2_IN_OFF;
  } else if (((uint32_T)is_c14_MisfOBD2) == MisfOBD2_IN_OFF) {
    /* During 'OFF': '<S58>:5' */
    /* Transition: '<S58>:12' */
    if (((int32_T)rtu_resSync) == 0) {
      /* Transition: '<S58>:13' */
      *rty_flg = rtu_in;
      is_c14_MisfOBD2 = MisfOBD2_IN_ON;
    } else {
      /* Transition: '<S58>:15' */
      *rty_flg = 0U;
    }
  } else {
    /* During 'ON': '<S58>:6' */
    /* Transition: '<S58>:10' */
    if (((int32_T)rtu_resSync) != 0) {
      /* Transition: '<S58>:9' */
      *rty_flg = 0U;
      is_c14_MisfOBD2 = MisfOBD2_IN_OFF;
    } else {
      /* Transition: '<S58>:14' */
      *rty_flg = rtu_in;
    }
  }

  /* End of Chart: '<S50>/EnTrig' */
  localDW->is_c14_MisfOBD2 = is_c14_MisfOBD2;
}

/* Output and update for function-call system: '<S50>/fc_write_PCode' */
void MisfOBD2_fc_write_PCode(const uint16_T rtu_DiagToDtcTab[92], uint16_T
  rtu_pCode, rtDW_fc_write_PCode_MisfOBD2_T *localDW)
{
  /* DataTypeConversion: '<S62>/Data Type Conversion' incorporates:
   *  Constant: '<S62>/DIAG_MISF_RND'
   */
  localDW->DataTypeConversion = (uint16_T)DIAG_MISF_RND;

  /* S-Function (MisfOBD2_Return_Addr_U16): '<S62>/C//C++ Code Block' */
  MisfOBD2_Return_Addr_U16_Outputs_wrapper(&rtu_DiagToDtcTab[0],
    &localDW->DataTypeConversion, &localDW->CCCodeBlock);

  /* S-Function (fc_MisfOBD2_Update): '<S62>/C//C++ Code Block1' */
  fc_MisfOBD2_Update_Outputs_wrapper(&rtu_pCode, &localDW->CCCodeBlock,
    &localDW->pCode);
}

/* Output and update for function-call system: '<S50>/fc_globalCyl' */
void MisfOBD2_fc_globalCyl(uint8_T rtu_ptFault)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;

  /* S-Function (DiagMgm_SetDiagState): '<S65>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S61>/DIAG_MISF_RND'
   */
  DiagMgm_SetDiagState( DIAG_MISF_RND, rtu_ptFault, &rtb_DiagMgm_SetDiagState);
}

/* System initialize for function-call system: '<S1>/T10ms' */
void MisfOBD2_T10ms_Init(void)
{
  uint8_T rtb_flg;

  /* SystemInitialize for Chart: '<S50>/Select_Diag' */
  MisfOBD2_DWork.is_active_c16_MisfOBD2 = 0U;
  MisfOBD2_DWork.is_c16_MisfOBD2 = MisfOBD2_IN_NO_ACTIVE_CHILD_ck1;
  MisfOBD2_DWork.oldCntClearDiag = 0U;
  MisfOBD2_DWork.oldFlgCatMisfOBD2 = 0U;
  MisfOBD2_DWork.FlgMisfDiagOn_nn4 = 0U;
  MisfOBD2_DWork.pCode = 0U;
  MisfOBD2_DWork.ptFault = 0U;

  /* SystemInitialize for Chart: '<S50>/res_Count' */
  MisfOBD2_DWork.oldDrivingCycle = 0U;

  /* SystemInitialize for Chart: '<S50>/EnTrig' */
  MisfOBD2_EnTrig_Init(&rtb_flg, &MisfOBD2_DWork.sf_EnTrig);

  /* SystemInitialize for Chart: '<S50>/EnTrig1' */
  MisfOBD2_EnTrig_Init(&rtb_flg, &MisfOBD2_DWork.sf_EnTrig1);

  /* SystemInitialize for Chart: '<S50>/res_Sync' */
  MisfOBD2_DWork.is_active_c17_MisfOBD2 = 0U;
  MisfOBD2_DWork.is_c17_MisfOBD2 = MisfOBD2_IN_NO_ACTIVE_CHILD_ck1;
  MisfOBD2_DWork.resSync_htn = 0U;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void MisfOBD2_T10ms(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  int16_T rtb_LookUp_IR_S16;
  int8_T cyl;
  boolean_T rtb_LogicalOperator_k3t;
  uint8_T rtb_flg_jos;
  uint8_T rtb_flg;
  uint16_T rtb_Look2D_IR_U8;
  int16_T rtb_Conversion1[4];
  int32_T i;
  uint8_T DrivingCycle_0;
  uint8_T FlgMisfDiagOn_nn4;
  uint8_T is_c17_MisfOBD2;
  uint8_T is_c16_MisfOBD2;
  uint8_T oldCntClearDiag;
  uint8_T oldFlgCatMisfOBD2;
  boolean_T guard1 = false;
  oldFlgCatMisfOBD2 = MisfOBD2_DWork.oldFlgCatMisfOBD2;
  oldCntClearDiag = MisfOBD2_DWork.oldCntClearDiag;
  is_c16_MisfOBD2 = MisfOBD2_DWork.is_c16_MisfOBD2;
  is_c17_MisfOBD2 = MisfOBD2_DWork.is_c17_MisfOBD2;
  FlgMisfDiagOn_nn4 = MisfOBD2_DWork.FlgMisfDiagOn_nn4;

  /* Inport: '<Root>/DrivingCycle' */
  DrivingCycle_0 = DrivingCycle;

  /* Chart: '<S50>/res_Sync' incorporates:
   *  Inport: '<Root>/DrivingCycle'
   */
  /* Gateway: MisfOBD2/T10ms/Diag_OBD2/res_Sync */
  /* During: MisfOBD2/T10ms/Diag_OBD2/res_Sync */
  if (((uint32_T)MisfOBD2_DWork.is_active_c17_MisfOBD2) == 0U) {
    /* Entry: MisfOBD2/T10ms/Diag_OBD2/res_Sync */
    MisfOBD2_DWork.is_active_c17_MisfOBD2 = 1U;

    /* Entry Internal: MisfOBD2/T10ms/Diag_OBD2/res_Sync */
    /* Transition: '<S64>:2' */
    MisfOBD2_DWork.resSync_htn = 0U;
    is_c17_MisfOBD2 = MisfOBD2_IN_REQ;
  } else if (((uint32_T)is_c17_MisfOBD2) == MisfOBD2_IN_ACK) {
    /* During 'ACK': '<S64>:3' */
    /* Transition: '<S64>:9' */
    if ((((int32_T)MisfOBD2_DWork.ackSync) != 0) && (DrivingCycle_0 != DRVC_OFF))
    {
      /* Transition: '<S64>:10' */
      MisfOBD2_DWork.resSync_htn = 0U;
      is_c17_MisfOBD2 = MisfOBD2_IN_REQ;
    } else {
      /* Transition: '<S64>:11' */
    }
  } else {
    /* During 'REQ': '<S64>:1' */
    /* Transition: '<S64>:5' */
    if (DrivingCycle_0 == DRVC_OFF) {
      /* Transition: '<S64>:6' */
      MisfOBD2_DWork.resSync_htn = 1U;
      is_c17_MisfOBD2 = MisfOBD2_IN_ACK;
    } else {
      /* Transition: '<S64>:7' */
    }
  }

  /* End of Chart: '<S50>/res_Sync' */

  /* Chart: '<S50>/EnTrig1' */
  MisfOBD2_EnTrig(MisfOBD2_DWork.flg, MisfOBD2_DWork.resSync_htn, &rtb_flg,
                  &MisfOBD2_DWork.sf_EnTrig1);

  /* SignalConversion generated from: '<S6>/Bus_T10msTP' incorporates:
   *  Switch: '<S50>/Switch3'
   */
  FlgOBD2MILFlashing = (uint8_T)((((int32_T)rtb_flg) != 0) ? 1 : 0);

  /* Chart: '<S50>/EnTrig' */
  MisfOBD2_EnTrig(MisfOBD2_DWork.FlgEmissMisfOBD2_ot3,
                  MisfOBD2_DWork.resSync_htn, &rtb_flg_jos,
                  &MisfOBD2_DWork.sf_EnTrig);

  /* Chart: '<S50>/Select_Diag' incorporates:
   *  Inport: '<Root>/DiagToDtcTab'
   */
  /* Gateway: MisfOBD2/T10ms/Diag_OBD2/Select_Diag */
  /* During: MisfOBD2/T10ms/Diag_OBD2/Select_Diag */
  if (((uint32_T)MisfOBD2_DWork.is_active_c16_MisfOBD2) == 0U) {
    /* Entry: MisfOBD2/T10ms/Diag_OBD2/Select_Diag */
    MisfOBD2_DWork.is_active_c16_MisfOBD2 = 1U;

    /* Entry Internal: MisfOBD2/T10ms/Diag_OBD2/Select_Diag */
    /* Transition: '<S60>:14' */
    MisfOBD2_DWork.pCode = P0300;
    FlgMisfDiagOn_nn4 = 0U;
    oldCntClearDiag = (uint8_T)MisfOBD2_DWork.Add1;
    oldFlgCatMisfOBD2 = rtb_flg;
    is_c16_MisfOBD2 = MisfOBD2_IN_MIL_ONLY_FLASHING;
  } else if (((uint32_T)is_c16_MisfOBD2) == MisfOBD2_IN_MIL_FIXED) {
    /* During 'MIL_FIXED': '<S60>:15' */
    /* Transition: '<S60>:94' */
    guard1 = false;
    if (((int32_T)rtb_flg) != 0) {
      /* Transition: '<S60>:105' */
      MisfOBD2_DWork.ptFault = DANGER;
      cyl = MisfOBD2_DWork.MisfCylCAT_ptp;

      /* Transition: '<S60>:122' */
      guard1 = true;
    } else {
      /* Transition: '<S60>:101' */
      if (((int32_T)rtb_flg_jos) != 0) {
        /* Transition: '<S60>:97' */
        MisfOBD2_DWork.ptFault = CONTINUOUS;
        cyl = MisfOBD2_DWork.MisfCylEmiss_ne2;
        guard1 = true;
      } else {
        /* Transition: '<S60>:123' */
        FlgMisfDiagOn_nn4 = 0U;
        oldCntClearDiag = (uint8_T)MisfOBD2_DWork.Add1;
        oldFlgCatMisfOBD2 = 0U;
        is_c16_MisfOBD2 = MisfOBD2_IN_MIL_ONLY_FLASHING;
      }
    }

    if (guard1) {
      if (cyl < 0) {
        /* Transition: '<S60>:112' */
        MisfOBD2_DWork.pCode = P0300;

        /* Transition: '<S60>:113' */
        /* Transition: '<S60>:109' */
        /* Transition: '<S60>:102' */
        /* Transition: '<S60>:99' */
      } else {
        /* Transition: '<S60>:95' */
        switch (cyl) {
         case 0:
          /* Transition: '<S60>:100' */
          MisfOBD2_DWork.pCode = P0301;

          /* Transition: '<S60>:109' */
          /* Transition: '<S60>:102' */
          /* Transition: '<S60>:99' */
          break;

         case 1:
          /* Transition: '<S60>:98' */
          /* Transition: '<S60>:106' */
          MisfOBD2_DWork.pCode = P0302;

          /* Transition: '<S60>:102' */
          /* Transition: '<S60>:99' */
          break;

         case 2:
          /* Transition: '<S60>:118' */
          /* Transition: '<S60>:115' */
          MisfOBD2_DWork.pCode = P0303;

          /* Transition: '<S60>:99' */
          break;

         default:
          /* Transition: '<S60>:96' */
          /* Transition: '<S60>:93' */
          MisfOBD2_DWork.pCode = P0304;
          break;
        }
      }

      /* Outputs for Function Call SubSystem: '<S50>/fc_write_PCode' */
      /* Transition: '<S60>:104' */
      /* Event: '<S60>:126' */
      MisfOBD2_fc_write_PCode((&(DiagToDtcTab[0])), MisfOBD2_DWork.pCode,
        &MisfOBD2_DWork.fc_write_PCode);

      /* End of Outputs for SubSystem: '<S50>/fc_write_PCode' */

      /* Outputs for Function Call SubSystem: '<S50>/fc_globalCyl' */
      /* Event: '<S60>:125' */
      MisfOBD2_fc_globalCyl(MisfOBD2_DWork.ptFault);

      /* End of Outputs for SubSystem: '<S50>/fc_globalCyl' */
      FlgMisfDiagOn_nn4 = 1U;
    }
  } else {
    /* During 'MIL_ONLY_FLASHING': '<S60>:13' */
    /* Transition: '<S60>:17' */
    if (((int32_T)rtb_flg_jos) != 0) {
      /* Transition: '<S60>:18' */
      /* Transition: '<S60>:139' */
      is_c16_MisfOBD2 = MisfOBD2_IN_MIL_FIXED;
    } else {
      /* Transition: '<S60>:20' */
      if (((int32_T)MisfOBD2_DWork.resSync_htn) != 0) {
        /* Transition: '<S60>:152' */
        FlgMisfDiagOn_nn4 = 0U;
        oldCntClearDiag = (uint8_T)MisfOBD2_DWork.Add1;
        oldFlgCatMisfOBD2 = rtb_flg;

        /* Transition: '<S60>:156' */
      } else {
        /* Transition: '<S60>:24' */
        if (((int32_T)rtb_flg) != 0) {
          /* Transition: '<S60>:22' */
          oldCntClearDiag = (uint8_T)MisfOBD2_DWork.Add1;
          oldFlgCatMisfOBD2 = rtb_flg;
          if (MisfOBD2_DWork.MisfCylCAT_ptp < 0) {
            /* Transition: '<S60>:35' */
            MisfOBD2_DWork.pCode = P0300;

            /* Transition: '<S60>:39' */
            /* Transition: '<S60>:46' */
            /* Transition: '<S60>:49' */
            /* Transition: '<S60>:54' */
          } else {
            /* Transition: '<S60>:37' */
            switch (MisfOBD2_DWork.MisfCylCAT_ptp) {
             case 0:
              /* Transition: '<S60>:45' */
              MisfOBD2_DWork.pCode = P0301;

              /* Transition: '<S60>:46' */
              /* Transition: '<S60>:49' */
              /* Transition: '<S60>:54' */
              break;

             case 1:
              /* Transition: '<S60>:42' */
              /* Transition: '<S60>:51' */
              MisfOBD2_DWork.pCode = P0302;

              /* Transition: '<S60>:49' */
              /* Transition: '<S60>:54' */
              break;

             case 2:
              /* Transition: '<S60>:47' */
              /* Transition: '<S60>:56' */
              MisfOBD2_DWork.pCode = P0303;

              /* Transition: '<S60>:54' */
              break;

             default:
              /* Transition: '<S60>:52' */
              /* Transition: '<S60>:57' */
              MisfOBD2_DWork.pCode = P0304;
              break;
            }
          }

          /* Outputs for Function Call SubSystem: '<S50>/fc_write_PCode' */
          /* Transition: '<S60>:59' */
          /* Event: '<S60>:126' */
          MisfOBD2_fc_write_PCode((&(DiagToDtcTab[0])), MisfOBD2_DWork.pCode,
            &MisfOBD2_DWork.fc_write_PCode);

          /* End of Outputs for SubSystem: '<S50>/fc_write_PCode' */
          MisfOBD2_DWork.ptFault = DANGER;

          /* Outputs for Function Call SubSystem: '<S50>/fc_globalCyl' */
          /* Event: '<S60>:125' */
          MisfOBD2_fc_globalCyl(DANGER);

          /* End of Outputs for SubSystem: '<S50>/fc_globalCyl' */
          FlgMisfDiagOn_nn4 = 1U;
        } else {
          /* Transition: '<S60>:29' */
          if ((MisfOBD2_DWork.Add1 != ((uint32_T)oldCntClearDiag)) || (0 !=
               ((int32_T)oldFlgCatMisfOBD2))) {
            /* Transition: '<S60>:148' */
            oldCntClearDiag = (uint8_T)MisfOBD2_DWork.Add1;
            oldFlgCatMisfOBD2 = 0U;
            MisfOBD2_DWork.ptFault = NO_PT_FAULT;

            /* Outputs for Function Call SubSystem: '<S50>/fc_globalCyl' */
            /* Event: '<S60>:125' */
            MisfOBD2_fc_globalCyl(NO_PT_FAULT);

            /* End of Outputs for SubSystem: '<S50>/fc_globalCyl' */
            FlgMisfDiagOn_nn4 = 1U;
          } else {
            /* Transition: '<S60>:147' */
          }

          /* Transition: '<S60>:60' */
        }

        /* Transition: '<S60>:61' */
      }

      /* Transition: '<S60>:154' */
    }
  }

  /* End of Chart: '<S50>/Select_Diag' */

  /* SignalConversion generated from: '<S6>/Bus_T10msTP' */
  FlgMisfDiagOn = FlgMisfDiagOn_nn4;

  /* Logic: '<S46>/Logical Operator' incorporates:
   *  Constant: '<S46>/THRPRESMISFOBD2'
   *  Constant: '<S46>/THRTAIRMISFOBD2'
   *  Inport: '<Root>/EndStartFlg'
   *  Inport: '<Root>/FlgLowFuel'
   *  Inport: '<Root>/PresAtm'
   *  Inport: '<Root>/TAirCrk'
   *  Logic: '<S46>/Logical Operator1'
   *  RelationalOperator: '<S46>/Relational Operator1'
   *  RelationalOperator: '<S46>/Relational Operator2'
   */
  rtb_LogicalOperator_k3t = ((((((int32_T)EndStartFlg) != 0) && (PresAtm >
    THRPRESMISFOBD2)) && (TAirCrk > THRTAIRMISFOBD2)) && (FlgLowFuel == 0));

  /* DataTypeConversion: '<S57>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_Look2D_IR_U8 = Rpm;

  /* DataTypeConversion: '<S57>/Data Type Conversion8' incorporates:
   *  Constant: '<S49>/BKENRPMCATOBD2_dim'
   */
  rtb_flg_jos = (uint8_T)BKENRPMCATOBD2_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S57>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S49>/BKENRPMCATOBD2'
   */
  PreLookUpIdSearch_U16( &rtb_Look2D_IR_U8, &rtb_PreLookUpIdSearch_U16_o2,
                        rtb_Look2D_IR_U8, &BKENRPMCATOBD2[0], rtb_flg_jos);

  /* DataTypeConversion: '<S52>/Conversion6' incorporates:
   *  Constant: '<S47>/BKENRPMCATOBD2_dim'
   */
  rtb_flg_jos = (uint8_T)BKENRPMCATOBD2_dim;

  /* DataTypeConversion: '<S56>/Data Type Conversion8' incorporates:
   *  Constant: '<S49>/BKENCMICATOBD2_dim'
   */
  rtb_flg = (uint8_T)BKENCMICATOBD2_dim;

  /* S-Function (PreLookUpIdSearch_S16): '<S56>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S49>/BKENCMICATOBD2'
   *  Inport: '<Root>/CmiTargetP'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, CmiTargetP,
                        &BKENCMICATOBD2[0], rtb_flg);

  /* DataTypeConversion: '<S52>/Conversion7' incorporates:
   *  Constant: '<S47>/BKENCMICATOBD2_dim'
   */
  rtb_flg = (uint8_T)BKENCMICATOBD2_dim;

  /* S-Function (Look2D_IR_U8): '<S52>/Look2D_IR_U8' incorporates:
   *  Constant: '<S47>/TBENMISFCATOBD2'
   */
  Look2D_IR_U8( (&(NRevOBD2Emiss)), &TBENMISFCATOBD2[0], rtb_Look2D_IR_U8,
               rtb_PreLookUpIdSearch_U16_o2, rtb_flg_jos,
               rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
               rtb_flg);

  /* Logic: '<S47>/Logical Operator' */
  MisfOBD2_DWork.EnMisfCATOBD2_eas = (rtb_LogicalOperator_k3t && (((int32_T)
    NRevOBD2Emiss) != 0));

  /* SignalConversion generated from: '<S6>/Bus_T10msTP' incorporates:
   *  DataTypeConversion: '<S6>/Data Type Conversion2'
   */
  EnMisfCATOBD2 = (uint8_T)(MisfOBD2_DWork.EnMisfCATOBD2_eas ? ((uint8_T)1) :
    ((uint8_T)0));

  /* DataTypeConversion: '<S51>/Conversion6' incorporates:
   *  Constant: '<S47>/BKENRPMCATOBD2_dim'
   */
  rtb_flg = (uint8_T)BKENRPMCATOBD2_dim;

  /* DataTypeConversion: '<S51>/Conversion7' incorporates:
   *  Constant: '<S47>/BKENCMICATOBD2_dim'
   */
  rtb_flg_jos = (uint8_T)BKENCMICATOBD2_dim;

  /* S-Function (Look2D_IR_U8): '<S51>/Look2D_IR_U8' incorporates:
   *  Constant: '<S47>/TBENMISFEMISSOBD2'
   */
  Look2D_IR_U8( &rtb_Look2D_IR_U8, &TBENMISFEMISSOBD2[0], rtb_Look2D_IR_U8,
               rtb_PreLookUpIdSearch_U16_o2, rtb_flg,
               rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
               rtb_flg_jos);

  /* Logic: '<S47>/Logical Operator1' */
  MisfOBD2_DWork.EnMisfEmissOBD2_ekh = (rtb_LogicalOperator_k3t && (((int32_T)
    rtb_Look2D_IR_U8) != 0));

  /* SignalConversion generated from: '<S6>/Bus_T10msTP' incorporates:
   *  DataTypeConversion: '<S6>/Data Type Conversion1'
   */
  EnMisfEmissOBD2 = (uint8_T)(MisfOBD2_DWork.EnMisfEmissOBD2_ekh ? ((uint8_T)1) :
    ((uint8_T)0));

  /* DataTypeConversion: '<S53>/Conversion1' incorporates:
   *  Constant: '<S48>/VTNREVOBD2EMISS'
   */
  for (i = 0; i < 4; i++) {
    rtb_Conversion1[i] = (int16_T)VTNREVOBD2EMISS[i];
  }

  /* End of DataTypeConversion: '<S53>/Conversion1' */

  /* DataTypeConversion: '<S55>/Data Type Conversion8' incorporates:
   *  Constant: '<S49>/BKNREVOBD2EMISS_dim'
   */
  rtb_flg = (uint8_T)BKNREVOBD2EMISS_dim;

  /* S-Function (PreLookUpIdSearch_S16): '<S55>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S49>/BKNREVOBD2EMISS'
   *  Inport: '<Root>/TWaterCrk'
   */
  PreLookUpIdSearch_S16( (&(NRevOBD2Emiss)), &rtb_PreLookUpIdSearch_S16_o2,
                        TWaterCrk, &BKNREVOBD2EMISS[0], rtb_flg);

  /* DataTypeConversion: '<S53>/Conversion3' incorporates:
   *  Constant: '<S48>/BKNREVOBD2EMISS_dim'
   */
  rtb_flg = (uint8_T)BKNREVOBD2EMISS_dim;

  /* S-Function (LookUp_IR_S16): '<S53>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &rtb_Conversion1[0], NRevOBD2Emiss,
                rtb_PreLookUpIdSearch_S16_o2, rtb_flg);

  /* DataTypeConversion: '<S54>/Conversion' */
  NRevOBD2Emiss = (uint16_T)rtb_LookUp_IR_S16;

  /* SignalConversion generated from: '<S6>/Bus_T10ms' incorporates:
   *  Logic: '<S47>/Logical Operator2'
   */
  MisfOBD2_DWork.EnMisfOBD2_brz = (uint8_T)(((MisfOBD2_DWork.EnMisfCATOBD2_eas) ||
    (MisfOBD2_DWork.EnMisfEmissOBD2_ekh)) ? 1 : 0);

  /* SignalConversion generated from: '<S6>/Bus_T10ms' */
  MisfOBD2_DWork.resSync = MisfOBD2_DWork.resSync_htn;

  /* SignalConversion generated from: '<S6>/Bus_T10ms' incorporates:
   *  Constant: '<S48>/NREVOBD2CAT'
   */
  MisfOBD2_DWork.NRevOBD2Cat = NREVOBD2CAT;

  /* SignalConversion generated from: '<S6>/Bus_T10ms' */
  MisfOBD2_DWork.NRevOBD2Emiss_kdt = NRevOBD2Emiss;

  /* Chart: '<S50>/res_Count' incorporates:
   *  Inport: '<Root>/DrivingCycle'
   */
  /* Gateway: MisfOBD2/T10ms/Diag_OBD2/res_Count */
  /* During: MisfOBD2/T10ms/Diag_OBD2/res_Count */
  /* Entry Internal: MisfOBD2/T10ms/Diag_OBD2/res_Count */
  /* Transition: '<S63>:2' */
  if ((DrivingCycle_0 == DRVC_START) && (MisfOBD2_DWork.oldDrivingCycle ==
       DRVC_OFF)) {
    /* Transition: '<S63>:6' */
    for (rtb_flg = 0U; rtb_flg < N_CYLINDER; rtb_flg = (uint8_T)((int32_T)
          (((int32_T)rtb_flg) + 1))) {
      /* Transition: '<S63>:16' */
      EEVtCntMisfCyl[(rtb_flg)] = 0U;
      EECntMisf = 0U;
    }

    /* Transition: '<S63>:15' */
  } else {
    /* Transition: '<S63>:5' */
  }

  /* Transition: '<S63>:22' */
  MisfOBD2_DWork.oldDrivingCycle = DrivingCycle_0;

  /* End of Chart: '<S50>/res_Count' */
  /* user code (Output function Trailer for TID3) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
  MisfOBD2_DWork.FlgMisfDiagOn_nn4 = FlgMisfDiagOn_nn4;
  MisfOBD2_DWork.is_c17_MisfOBD2 = is_c17_MisfOBD2;
  MisfOBD2_DWork.is_c16_MisfOBD2 = is_c16_MisfOBD2;
  MisfOBD2_DWork.oldCntClearDiag = oldCntClearDiag;
  MisfOBD2_DWork.oldFlgCatMisfOBD2 = oldFlgCatMisfOBD2;
}

/* Model step function */
void Trig_MisfOBD2_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S1>/Init'
   */
  MisfOBD2_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_MisfOBD2_PreTdc(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' incorporates:
   *  SubSystem: '<S1>/PreTdc'
   */
  MisfOBD2_PreTdc();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' */
}

/* Model step function */
void Trig_MisfOBD2_T10(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  MisfOBD2_T10ms();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */
}

/* Model initialize function */
void MisfOBD2_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  IDMisfOBD2 = 0U;
  NRevCAT = 0U;
  NRevEmiss = 0U;
  SumCatMisfOBD2 = 0U;
  SumEmissMisfOBD2 = 0U;

  {
    int32_T i;
    for (i = 0; i < 4; i++) {
      VtCntMisfCAT[i] = 0U;
    }
  }

  {
    int32_T i;
    for (i = 0; i < 4; i++) {
      VtCntMisfEmiss[i] = 0U;
    }
  }

  CntEmissOBD2 = 0U;
  NRevOBD2Emiss = 0U;
  FlgCatMisfOBD2 = 0U;
  FlgEmissMisfOBD2 = 0U;
  FlgMisfDiagOn = 0U;
  EnMisfEmissOBD2 = 0U;
  EnMisfCATOBD2 = 0U;
  FlgOBD2MILFlashing = 0U;
  EnMisfOBD2 = 0U;
  MisfCylCAT = 0;
  MisfCylEmiss = 0;

  /* states (dwork) */
  (void) memset((void *)&MisfOBD2_DWork, 0,
                sizeof(D_Work_MisfOBD2_T));

  {
    int32_T i;

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' incorporates:
     *  SubSystem: '<S1>/PreTdc'
     */
    MisfOBD2_PreTdc_Init();

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_PreTdc' */
    /* SystemInitialize for Merge: '<S4>/Merge5' */
    MisfCylCAT = 0;

    /* SystemInitialize for Merge: '<S4>/Merge6' */
    MisfCylEmiss = 0;

    /* SystemInitialize for Merge: '<S4>/Merge2' */
    FlgCatMisfOBD2 = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge1' */
    FlgEmissMisfOBD2 = 0U;

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    MisfOBD2_T10ms_Init();

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_T10ms' */

    /* SystemInitialize for Merge: '<S4>/Merge15' */
    FlgMisfDiagOn = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge10' */
    EnMisfEmissOBD2 = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge11' */
    NRevCAT = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge12' */
    NRevEmiss = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge13' */
    SumCatMisfOBD2 = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge14' */
    SumEmissMisfOBD2 = 0U;
    for (i = 0; i < 4; i++) {
      /* SystemInitialize for Merge: '<S4>/Merge3' */
      VtCntMisfCAT[(i)] = 0U;

      /* SystemInitialize for Merge: '<S4>/Merge4' incorporates:
       *  Merge: '<S4>/Merge3'
       */
      VtCntMisfEmiss[(i)] = 0U;
    }

    /* SystemInitialize for Merge: '<S4>/Merge7' */
    CntEmissOBD2 = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge8' */
    NRevOBD2Emiss = 0U;

    /* SystemInitialize for Merge: '<S4>/Merge9' */
    EnMisfCATOBD2 = 0U;
  }
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_MISFOBD2_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
