/*
 * File: FOInjCtfMgm_out.h
 *
 * Code generated for Simulink model 'FOInjCtfMgm'.
 *
 * Model version                  : 1.332
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Mar 16 16:34:41 2020
 */

#ifndef RTW_HEADER_FOInjCtfMgm_out_h_
#define RTW_HEADER_FOInjCtfMgm_out_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define IGN_CTF                        1U

/* IGN_CTF */
#define IGN_INJ_CTF                    3U

/* IGN_INJ_CTF */
#define INJ_CTF                        2U

/* INJ_CTF */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint32_T IDFOInjCtfMgm;

/* ID Version */
extern uint8_T IdxFOInjCutoff;

/* Idx */
extern uint8_T SymFOInjCutoff[4];

/* Cutoff FO symulaterd */
extern uint16_T TimesFOInjCutoff;

/* counter */
extern uint8_T VtFOInjCutoff[4];

/* Cutoff FO Inj */
#endif                                 /* RTW_HEADER_FOInjCtfMgm_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
