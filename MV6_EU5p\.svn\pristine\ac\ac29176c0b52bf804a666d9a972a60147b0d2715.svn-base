;
; Example script for programming of MPC5633M internal flash.
;
; Internal Flash Memory 1.0 MByte:
;   Low address range   256 kByte (16, 48, 48, 16, 64, 64 kByte)
;                       0x00000000--0x0003ffff
;   Mid address range   256 kByte (2 * 128 kByte)
;                       0x00040000--0x0007ffff
;   High address range  512 kByte (2 * 256 kByte)
;                       0x00080000--0x000fffff
;   Shadow row          32 kByte
;                       0x00ff8000--0x00ffffff
; 
; Internal SRAM         48 kByte
;                       0x40000000--0x4000bfff 
;
; Flash register base address is 0xc3f88000
;
; NOTES:
; 
;   Flash register base address has to be 0xc3f88000, because target
;   program does currently not support another base address.
;
; wrd - 22.02.2008
;

&flashbase=0x00000000
&shadowbase=0x00ffc000
&rambase=0x40000000
&flashsize=0x000100000

; Optimize flash programming time by switching on PLL
&optimize=0

;========================================================================
; CPU setup


SYStem.RESet
SYStem.BdmClock 4.0MHz
SYStem.CPU SPC563M
sys.o NOJTAGRDY ON	
SYStem.Up

if &optimize==1
(
  ; TBD
  ; setup PLL
  ; Data.Set ASD:0xC3F80000 %LONG 0x06000000
  ; set JTAG clock to 25 MHz
  ; SYStem.BdmClock 25MHz
)

; initialize internal SRAM
Data.Set EA:0x40000000--0x4000bfff %quad 0

; setup MMU for flash, RAM and register access
MMU.TLB1.SET 0x1 0xC0000500 0xFFF0000A 0xFFF0003F
MMU.TLB1.SET 0x2 0xC0000700 0x20000000 0x2000003F
MMU.TLB1.SET 0x3 0xC0000400 0x40000008 0x4000003F
MMU.TLB1.SET 0x4 0xC0000500 0xC3F00008 0xC3F0003F
MMU.TLB1.SET 0x5 0xC0000700 0x00000000 0x0000003F

;========================================================================
; Flash declaration

FLASH.RESet

FLASH.Create 1. (&flashbase+0x00000000)++0x03fff TARGET Quad 0. ; L0
FLASH.Create 1. (&flashbase+0x00004000)++0x0bfff TARGET Quad 1. ; L1 
FLASH.Create 1. (&flashbase+0x00010000)++0x0bfff TARGET Quad 2. ; L2 
FLASH.Create 1. (&flashbase+0x0001c000)++0x03fff TARGET Quad 3. ; L3
FLASH.Create 1. (&flashbase+0x00020000)++0x0ffff TARGET Quad 4. ; L4
FLASH.Create 1. (&flashbase+0x00030000)++0x0ffff TARGET Quad 5. ; L5
FLASH.Create 2. (&flashbase+0x00040000)++0x1ffff TARGET Quad 0. ; M0
FLASH.Create 2. (&flashbase+0x00060000)++0x1ffff TARGET Quad 1. ; M1
&flashaddr=&flashbase+0x00080000
&Hx=0.
while &flashaddr<(&flashbase+&flashsize)
(
  FLASH.Create 3. &flashaddr++0x3ffff TARGET Quad &Hx ; H0..H3
  &flashaddr=&flashaddr+0x40000
  &Hx=&Hx+1.
)
; Shadow row
FLASH.Create 4. (&shadowbase+0x00000000)++0x3fff NOP Quad

FLASH.TARGET E:&rambase E:&rambase+0x2000 0x1000 ~~/demo/powerpc/flash/quad/c90fl.bin /STACKSIZE 0x0200

;========================================================================
; Flash programming example
;
; Flash programming speed is about three times faster when memory class E:
; is used for data buffer (DUALPORT memory access). For DUALPORT access it
; is required to setup MemAccess NEXUS for both, NEXUS and JTAG debugger. 

DIALOG.YESNO "Program flash memory?"
entry &progflash

if &progflash 
(
  FLASH.ERASE ALL
  FLASH.Auto ALL
  ;FLASH.ERASE 0x30000--0x9FFFF
  ;FLASH.Auto 0x30000--0x9FFFF

  ; optional set unused flash memory to 0xFF 
  ;Data.Set E:0x00--0xfffff %Quad 0xFFFFFFFFFFFFFFFF
  ;Data.LOAD.binary ..\GHS\bin\MV_L3_563M\MV_L3_563M-merge.bin
  ;Data.LOAD.binary ..\GHS\bin\MV_L3_Dev_563M\MV_L3_Dev_563M-appl.bin 0x30000--0x9FFFF
  ;Data.LOAD.binary ..\GHS\bin\MV_L4_Dev_563M\MV_L4_Dev_563M-appl.bin 0x30000--0x9FFFF
  FLASH.Auto OFF
)
;Data.LOAD.elf    ..\GHS\bin\MV_L3_563M\MV_L3_563M.elf /GHILLS /NOCODE
;Data.LOAD.elf   ..\GHS\bin\MV_L3_Dev_563M\MV_L3_Dev_563M.elf 0x30000--0x9FFFF /GHILLS /NOCODE
;Data.LOAD.elf   ..\GHS\bin\MV_L4_Dev_563M\MV_L4_Dev_563M.elf 0x30000--0x9FFFF /GHILLS /NOCODE
;FLASH.ERASE 0x30000--0x9FFFF

DIALOG.YESNO "Erase EEPROM?"
entry &progflash

if &progflash
(
  FLASH.ERASE 0x4000--0x17FFF
)


;set JTAG clock back to default
SYStem.BdmClock 4MHz

;attach debugger to cores ====================================================
; PPC
sys.down
sys.bdmclock 4.M
sys.o.DTM off	;no datatrace
sys.o stall off	

sys.o NEXUS MDO4	

;PPC
sys.u
do .\sram_init_5633.cmm
do .\errata_e_5200.cmm
do .\win


ENDDO











