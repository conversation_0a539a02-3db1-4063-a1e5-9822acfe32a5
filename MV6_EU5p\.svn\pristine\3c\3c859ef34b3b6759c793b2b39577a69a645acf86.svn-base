/*****************************************************************************************************************/
/* $HeadURL:: https://***********/svn/Rep_Bo/EM/appl_calib/branches/MV7/tree/DD/HEATGRIPDRIVEMGM/Hea#$   */
/* $ Description:                                                                                                */
/* $Revision:: 9603   $                                                                                          */
/* $Date:: 2019-06-27 15:12:55 +0200 (gio, 27 giu 2019)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

/****************************************************************************
 ****************************************************************************
 *
 *                              GasPosFiltMgm_calib.c
 *
 * Author(s): Lana L.
 *
 *
 * Implementation notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifdef _BUILD_GASPOSFILTMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "gasposfilt_mgm.h"

/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/
#pragma ghs section rodata=".calib"

const uint8_T GASPOSFILTDEEP = 29;
const uint8_T GASPOSFILTDEEPSHORT = 5;
const uint8_T GASPOSFILTDECIM = 0;
const uint8_T TIMGASSUPHILL = 6;
const uint16_T THRGASATTACH = (uint16_T)(2.5f * 32.0f);
const uint16_T MINGASATTACH = (uint16_T)(3.0f * 32.0f);

#endif

/****************************************************************************
 ****************************************************************************/

