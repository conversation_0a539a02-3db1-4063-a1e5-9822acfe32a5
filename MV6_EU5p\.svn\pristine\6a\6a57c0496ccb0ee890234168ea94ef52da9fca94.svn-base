/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function angTrigger, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

//#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_SharedTypes.h"
#include "..\..\common\ETPU_Shared.h"
#include "..\include\sparkHandler.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

#pragma library;
#pragma option +l;  // List the library
#pragma option v;

/********************************************************************************
* FUNCTION: angTrigger                                                          *

* PURPOSE:  This function generates triggers in TIME Mode or in ANGLE Mode      *
*                                                                               *
* INPUTS NOTES: This function has 5 parameters                                  *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
void angTrigger(unsigned int trigStartAngle[ANG_ACQ_BUF_SIZE], unsigned int index, unsigned int trigMode,
                unsigned int trigPeriod, unsigned int trigNum, unsigned int indexRestart)
{
#ifdef _BUILD_ANGTRIGGER_
    unsigned int currentTCR;
    unsigned int currentTCRB;

    if (HSR_INIT_ANGTRIG)   // Required to initialize
    {
    
        EnableOutputBuffer();
        SetChannelMode(m2_st);
        
        index = 0;
        if (trigMode == 1) /* TIME_TRIGGER_MODE */
        {
            currentTCR = tcr1;
            SetPinLow();
            
            SetupMatch_A(currentTCR + trigPeriod - 5, Mtcr1_Ctcr1_ge, PinHigh);
            SetupMatch_B(currentTCR + trigPeriod, Mtcr1_Ctcr1_ge, PinLow);
            
        }
        else                  /* ANGLE_TRIGGER_MODE */
        {
            currentTCR = trigStartAngle[index];
            currentTCRB = currentTCR + 1;
            if (currentTCRB >= (360*N_CYCLE*TICKS_PER_DEGREE))
                currentTCRB -= (360*N_CYCLE*TICKS_PER_DEGREE);
            SetPinLow();
            /*Program match for first trigger */
            SetupMatch_A(currentTCR , Mtcr2_Ctcr2_eq, PinHigh);
            SetupMatch_B(currentTCRB, Mtcr2_Ctcr2_eq, PinLow);
        }
        
        index = 1;
    }
    else if (HSR_DISABLE_ANGTRIG)
    {
    
        if (indexRestart)
        {
            trigMode = 1;
            /* generate remaning trigger */
            if (index <= trigNum)
            {
                currentTCR = tcr1;
            
                if(!(IsCurrentInputPinHigh()))
                    SetupMatch_A(currentTCR + trigPeriod - 5, Mtcr1_Ctcr1_ge, PinHigh);
                SetupMatch_B(currentTCR + trigPeriod, Mtcr1_Ctcr1_ge, PinLow);
            }
            /*else
            {
                ClearLSRLatch();
                DisableMatchDetection();
                ClearMatchBLatch();
                ClearTransLatch();
            }*/

            ClearLSRLatch();
            ClearMatchBLatch();
            ClearMatchALatch();
            ClearTransLatch();
        }
        else
        {
            SetPinLow();
            ClearLSRLatch();
            DisableMatchDetection();
            ClearMatchBLatch();
            ClearTransLatch();
        }
    }
    else if (MatchB)
    {    
        if (trigMode == 1)  /* TIME_TRIGGER_MODE */
        {
            if (index <= (trigNum-1))
            {
                currentTCR = GetCapRegB();

                SetupMatch_A(currentTCR + trigPeriod - 5, Mtcr1_Ctcr1_ge, PinHigh);
                SetupMatch_B(currentTCR + trigPeriod, Mtcr1_Ctcr1_ge, PinLow);
            }
            else
            {
                ClearLSRLatch();
                DisableMatchDetection();
                ClearMatchBLatch();
                ClearTransLatch();
            }
            index++;
        }
        else                  /* ANGLE_TRIGGER_MODE */
        {
            if (index <= (trigNum-1))
            {
                ClearMatchBLatch();
                currentTCR = trigStartAngle[index];
                currentTCRB = currentTCR + 1;
                if (currentTCRB >= (360*N_CYCLE*TICKS_PER_DEGREE))
                    currentTCRB -= (360*N_CYCLE*TICKS_PER_DEGREE);
                SetupMatch_A(currentTCR , Mtcr2_Ctcr2_eq, PinHigh);
                SetupMatch_B(currentTCRB, Mtcr2_Ctcr2_eq, PinLow);
            }
            else
            {
                ClearLSRLatch();
                DisableMatchDetection();
                ClearMatchBLatch();
                ClearTransLatch();
            }
            index++;
        }
    }
    else if (LinkServiceRequest) /* when loose synchronization */
    {
        trigMode = 1;
        /* generate remaning trigger */
        if (index <= trigNum)
        {
            currentTCR = tcr1;
        
            if(!(IsCurrentInputPinHigh()))
                SetupMatch_A(currentTCR + trigPeriod - 5, Mtcr1_Ctcr1_ge, PinHigh);
            SetupMatch_B(currentTCR + trigPeriod, Mtcr1_Ctcr1_ge, PinLow);
        }
        /*else
        {
            ClearLSRLatch();
            DisableMatchDetection();
            ClearMatchBLatch();
            ClearTransLatch();
        }*/

        ClearLinkServiceRequestEvent();
        ClearLSRLatch();
        ClearMatchBLatch();
        ClearMatchALatch();
        ClearTransLatch();
    }
    else
    {
        // This else statement is used to catch all unspecified entry table conditions
        // Clear all possible event sources
        // And set the unexpected event error indicator
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
    }
#endif /* _BUILD_ANGTRIGGER_ */

}

#pragma endlibrary;


