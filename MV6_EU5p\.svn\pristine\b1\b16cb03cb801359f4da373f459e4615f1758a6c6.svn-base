/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function DELAY, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

//#include "..\..\common\ETPU_EngineDefs.h"
//#include "..\..\common\ETPU_SharedTypes.h"
#include "..\..\common\ETPU_Shared.h"
#include "..\include\sparkHandler.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/

#pragma library;
#pragma option +l;  // List the library
#pragma option v;


/********************************************************************************
* FUNCTION: DELAY       			                                                *
* PURPOSE:  This function rise an ISR to the host at high to low or low to high *
* input pin transition															*
* CrankAngle parameter.															*
*                                                                               *
* INPUTS NOTES: This function has 2 parameters                                  *
* RETURNS NOTES: N/A                                                            *
*                                                                               *
* WARNING:                                                                      *
********************************************************************************/
void DELAY(unsigned int etpuDelay)
{
#ifdef _BUILD_ETPUDELAY_
	unsigned int currentTCR;

    if (HSR_INIT_DELAY)   // Required to initialize
	{
		currentTCR = tcr1;
        SetChannelMode(sm_st);

		SetupMatch_A(etpuDelay + currentTCR, Mtcr1_Ctcr1_eq, NoChange);

	}
	else
	if (MatchA)
	{
		SetChannelInterrupt(); // interrupt request to HOST

		// Clear all registers
		ClearLSRLatch();
		ClearMatchALatch();
		ClearMatchBLatch();
		ClearTransLatch();
	}
	else
	{
		//This else statement is used to catch all unspecified entry table conditions
		// Clear all possible event sources
		// And set the unexpected event error indicator
		ClearLSRLatch();
		ClearMatchALatch();
		ClearMatchBLatch();
		ClearTransLatch();
	};
#endif /* _BUILD_ETPUDELAY_ */
}

#pragma endlibrary;

