/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Application/THROTTLEMODEL/main_trunk/ThrottleModel_ert_rtw/ThrottleModel.h $ */
/* $Description:  $ */
/* $Revision: 5834 $ */
/* $Date: 2014-07-15 18:19:18 +0200 (mar, 15 lug 2014) $ */
/* $Author: LanaL $ */
/*****************************************************************************************************************/
/*
 * File: ThrottleModel.h
 *
 * Real-Time Workshop code generated for Simulink model ThrottleModel.
 *
 * Model version                        : 1.686
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Tue Jul 15 18:16:27 2014
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Tue Jul 15 18:16:28 2014
 */
#ifndef RTW_HEADER_ThrottleModel_h_
#define RTW_HEADER_ThrottleModel_h_
#ifndef ThrottleModel_COMMON_INCLUDES_
# define ThrottleModel_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* ThrottleModel_COMMON_INCLUDES_ */

#include "ThrottleModel_types.h"

/* Includes for objects with custom storage classes. */
#include "throttle_model.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
# define rtmGetErrorStatus(rtm)        ((void*) 0)
#endif

#ifndef rtmSetErrorStatus
# define rtmSetErrorStatus(rtm, val)   ((void) 0)
#endif

#ifndef rtmGetStopRequested
# define rtmGetStopRequested(rtm)      ((void*) 0)
#endif

#define BKPRESANGTHRTARG_dim           16U
#define BKRPMANGTHRTARG_dim            28U

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState fc_ThrottleModel_Init_Trig_ZCE[2];/* '<S1>/fc_ThrottleModel_Init' */
  ZCSigState fc_ThrottleModel_Calc_Trig_ZCE;/* '<S1>/fc_ThrottleModel_Calc' */
} PrevZCSigStates_ThrottleModel;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
  uint8_T ev_10ms;                     /* '<Root>/ev_10ms' */
} ExternalInputs_ThrottleModel;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_ThrottleModel ThrottleModel_U;

/* Model entry point functions */
extern void ThrottleModel_initialize(void);
extern void ThrottleModel_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('throttlemodel_gen/ThrottleModel')    - opens subsystem throttlemodel_gen/ThrottleModel
 * hilite_system('throttlemodel_gen/ThrottleModel/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : throttlemodel_gen
 * '<S1>'   : throttlemodel_gen/ThrottleModel
 * '<S2>'   : throttlemodel_gen/ThrottleModel/Init
 * '<S3>'   : throttlemodel_gen/ThrottleModel/T10ms
 * '<S4>'   : throttlemodel_gen/ThrottleModel/fc_ThrottleModel_Calc
 * '<S5>'   : throttlemodel_gen/ThrottleModel/fc_ThrottleModel_Init
 * '<S6>'   : throttlemodel_gen/ThrottleModel/T10ms/Sec2Ang_Calc
 * '<S7>'   : throttlemodel_gen/ThrottleModel/T10ms/Sec2Ang_Calc/Look2D_U16_U16_U16
 * '<S8>'   : throttlemodel_gen/ThrottleModel/T10ms/Sec2Ang_Calc/Look2D_U16_U16_U16/Data Type Conversion Inherited1
 */

/*
 * Requirements for '<Root>' : ThrottleModel
 */
#endif                                 /* RTW_HEADER_ThrottleModel_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
