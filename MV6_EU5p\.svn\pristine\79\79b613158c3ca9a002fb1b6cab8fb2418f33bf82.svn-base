/** ###################################################################
**     
**     
**     
**     
**     Programmer      :  Solinas
**     Date Last Modify:  28/02/2005     
**     
**     
**     Description  :
**         SPR_Macros.h - 
**         contains definitions register access macros 
**         for the special purpouse register.
**         It is based on the Processor Expert PE_Types.h
**         with minor syntax changes
** ###################################################################*/


#ifndef __SPR_Macros_H
#define __SPR_Macros_H


#include "mpc5500_spr.h"

/**********************************************************/
/* High level special registers macros                    */
/**********************************************************/
#define setSpecReg32(regName, regValue) setSpecReg32##regName(regValue)
#define getSpecReg32(regName) getSpecReg32##regName()

/* ========================================================================= */
/* ### asm functions					 */
/* ========================================================================= */

asm void setSpecReg32TBU(data)
{
%reg data
    mtspr SPR_TBU,data
}
asm void setSpecReg32TBL(data)
{
%reg data
   mtspr SPR_TBL,data
}


/* Machine State Register */
asm void setSpecReg32MSR(data)
{
%reg data
   mtmsr data
}

asm uint32_t getSpecReg32MSR(void)
{
   mfmsr r3
}

/* Critical Save/Restore Register 0 */
asm void setSpecReg32SPR_CSRR0(data)
{
%reg data
   mtspr SPR_CSRR0,data
}

asm uint32_t getSpecReg32SPR_CSRR0(void)
{
    mfspr r3,SPR_CSRR0
}

/* Critical Save/Restore Register 1 */
asm void setSpecReg32SPR_CSRR1(data)
{
%reg data
   mtspr SPR_CSRR1,data
}

asm uint32_t getSpecReg32SPR_CSRR1(void)
{

   mfspr r3,SPR_CSRR1
   
}

/* Count Register */
asm void setSpecReg32SPR_CTR(data)
{
%reg data
  mtspr SPR_CTR,data
}

asm uint32_t getSpecReg32SPR_CTR(void)
{

  mfspr r3, SPR_CTR
  
}

/* Data Address Compare 1 */
asm void setSpecReg32SPR_DAC1(data)
{
%reg data
  mtspr SPR_DAC1,data
}

asm uint32_t getSpecReg32SPR_DAC1(void)
{

  mfspr r3, SPR_DAC1
  
}

/* Data Address Compare 2 */
asm void setSpecReg32SPR_DAC2(data)
{
%reg data
  mtspr SPR_DAC2,data
}

asm uint32_t getSpecReg32SPR_DAC2(void)
{

  mfspr r3, SPR_DAC2
  
}

/* Debug Counter register */
asm void setSpecReg32SPR_DBCNT(data)
{
%reg data
  mtspr SPR_DBCNT,data
}

asm uint32_t getSpecReg32SPR_DBCNT(void)
{

  mfspr r3, SPR_DBCNT
  
}

/* Debug Control Register 0 */
asm void setSpecReg32SPR_DBCR0(data)
{
%reg data
  mtspr SPR_DBCR0,data
}

asm uint32_t getSpecReg32SPR_DBCR0(void)
{

  mfspr r3, SPR_DBCR0
  
}

/* Debug Control Register 1 */
asm void setSpecReg32SPR_DBCR1(data)
{
%reg data
  mtspr SPR_DBCR1,data
}

asm uint32_t getSpecReg32SPR_DBCR1(void)
{

  mfspr r3, SPR_DBCR1
  
}

/* Debug Control Register 2 */
asm void setSpecReg32SPR_DBCR2(data)
{
%reg data
  mtspr SPR_DBCR2,data
}

asm uint32_t getSpecReg32SPR_DBCR2(void)
{
;
  mfspr r3, SPR_DBCR2
  
}

/* Debug Control Register 3 */
asm void setSpecReg32SPR_DBCR3(data)
{
%reg data
  mtspr SPR_DBCR3,data
}

asm uint32_t getSpecReg32SPR_DBCR3(void)
{

  mfspr r3, SPR_DBCR3
  
}

/* Debug Status Register */
asm void setSpecReg32SPR_DBSR(data)
{
%reg data
  mtspr SPR_DBSR,data
}

asm uint32_t getSpecReg32SPR_DBSR(void)
{

  mfspr r3, SPR_DBSR
  
}

/* Data Exception Address Register */
asm void setSpecReg32SPR_DEAR(data)
{
%reg data
  mtspr SPR_DEAR,data
}

asm uint32_t getSpecReg32SPR_DEAR(void)
{

  mfspr r3, SPR_DEAR
  
}

/* Decrementer */
asm void setSpecReg32SPR_DEC(data)
{
%con data
  mtspr SPR_DEC,data
}

asm uint32_t getSpecReg32SPR_DEC(void)
{

  mfspr r3, SPR_DEC
  
}

/* Decrementer Auto-Reload */
asm void setSpecReg32SPR_DECAR(data)
{
%con data
  mtspr SPR_DECAR,data
}

asm uint32_t getSpecReg32SPR_DECAR(void)
{

  mfspr r3, SPR_DECAR
  
}

/* Debug Save/Restore Register 0 */
asm void setSpecReg32SPR_DSRR0(data)
{
%reg data
  mtspr SPR_DSRR0,data
}

asm uint32_t getSpecReg32SPR_DSRR0(void)
{

  mfspr r3, SPR_DSRR0
  
}

/* Debug Save/Restore Register 1 */
asm void setSpecReg32SPR_DSRR1(data)
{
%reg data
  mtspr SPR_DSRR1,data
}

asm uint32_t getSpecReg32SPR_DSRR1(void)
{

  mfspr r3, SPR_DSRR1
  
}

/* Exception Syndrome Register */
asm void setSpecReg32SPR_ESR(data)
{
%reg data
  mtspr SPR_ESR,data
}

asm uint32_t getSpecReg32SPR_ESR(void)
{

  mfspr r3, SPR_ESR
  
}

/* Hardware Implementation Dependent Register 0 */
asm void setSpecReg32SPR_HID0(data)
{
%reg data
  mtspr SPR_HID0,data
}

asm uint32_t getSpecReg32SPR_HID0(void)
{
/* register r3; */
/*#pragma asm   */
  mfspr r3, SPR_HID0
/*#pragma endasm */  
}

//asm uint32_t getSpecReg32SPR_HID0(void)
//{
//;
//    mfspr r3, SPR_HID0
//  
//}

/* Hardware Implementation Dependent Register 1 */
asm void setSpecReg32SPR_HID1(data)
{
%reg%reg data
  mtspr SPR_HID1,data
}

asm uint32_t getSpecReg32SPR_HID1(void)
{

  mfspr r3, SPR_HID1
  
}

/* Instruction Address Compare 1 */
asm void setSpecReg32SPR_IAC1(data)
{
%reg data
  mtspr SPR_IAC1,data
}

asm uint32_t getSpecReg32SPR_IAC1(void)
{

  mfspr r3, SPR_IAC1
  
}

/* Instruction Address Compare 2 */
asm void setSpecReg32SPR_IAC2(data)
{
%reg data
  mtspr SPR_IAC2,data
}

asm uint32_t getSpecReg32SPR_IAC2(void)
{

  mfspr r3, SPR_IAC2
  
}

/* Instruction Address Compare 3 */
asm void setSpecReg32SPR_IAC3(data)
{
%reg data
  mtspr SPR_IAC3,data
}

asm uint32_t getSpecReg32SPR_IAC3(void)
{

  mfspr r3, SPR_IAC3
  
}

/* Instruction Address Compare 4 */
asm void setSpecReg32SPR_IAC4(data)
{
%reg data
  mtspr SPR_IAC4,data
}

asm uint32_t getSpecReg32SPR_IAC4(void)
{

  mfspr r3, SPR_IAC4
  
}

/* Interrupt Vector Offset Register 0 */
asm void setSpecReg32SPR_IVOR0(data)
{
%reg data
  mtspr SPR_IVOR0,data
}

asm uint32_t getSpecReg32SPR_IVOR0(void)
{

  mfspr r3, SPR_IVOR0
  
}

/* Interrupt Vector Offset Register 1 */
asm void setSpecReg32SPR_IVOR1(data)
{
%reg data
  mtspr SPR_IVOR1,data
}

asm uint32_t getSpecReg32SPR_IVOR1(void)
{

  mfspr r3, SPR_IVOR1
  
}

/* Interrupt Vector Offset Register 2 */
asm void setSpecReg32SPR_IVOR2(data)
{
%reg data
  mtspr SPR_IVOR2,data
}

asm uint32_t getSpecReg32SPR_IVOR2(void)
{

  mfspr r3, SPR_IVOR2
  
}

/* Interrupt Vector Offset Register 3 */
asm void setSpecReg32SPR_IVOR3(data)
{
%reg data
  mtspr SPR_IVOR3,data
}

asm uint32_t getSpecReg32SPR_IVOR3(void)
{

  mfspr r3, SPR_IVOR3
  
}

/* Interrupt Vector Offset Register 4 */
asm void setSpecReg32SPR_IVOR4(data)
{
%reg data
  mtspr SPR_IVOR4,data
}

asm uint32_t getSpecReg32SPR_IVOR4(void)
{

  mfspr r3, SPR_IVOR4
  
}

/* Interrupt Vector Offset Register 5 */
asm void setSpecReg32SPR_IVOR5(data)
{
%reg data
   mtspr SPR_IVOR5,data
}

asm uint32_t getSpecReg32SPR_IVOR5(void)
{

  mfspr r3, SPR_IVOR5
  
}

/* Interrupt Vector Offset Register 6 */
asm void setSpecReg32SPR_IVOR6(data)
{
%reg data
  mtspr SPR_IVOR6,data
}

asm uint32_t getSpecReg32SPR_IVOR6(void)
{

  mfspr r3, SPR_IVOR6
  
}

/* Interrupt Vector Offset Register 7 */
asm void setSpecReg32SPR_IVOR7(data)
{
%reg data
  mtspr SPR_IVOR7,data
}

asm uint32_t getSpecReg32SPR_IVOR7(void)
{

  mfspr r3, SPR_IVOR7
  
}

/* Interrupt Vector Offset Register 8 */
asm void setSpecReg32SPR_IVOR8(data)
{
%reg data
  mtspr SPR_IVOR8,data
}

asm uint32_t getSpecReg32SPR_IVOR8(void)
{

  mfspr r3, SPR_IVOR8
  
}

/* Interrupt Vector Offset Register 9 */
asm void setSpecReg32SPR_IVOR9(data)
{
%reg data
  mtspr SPR_IVOR9,data
}

asm uint32_t getSpecReg32SPR_IVOR9(void)
{

  mfspr r3, SPR_IVOR9
  
}

/* Interrupt Vector Offset Register 10 */
asm void setSpecReg32SPR_IVOR10(data)
{
%reg data
  mtspr SPR_IVOR10,data
}

asm uint32_t getSpecReg32SPR_IVOR10(void)
{

  mfspr r3, SPR_IVOR10
  
}

/* Interrupt Vector Offset Register 11 */
asm void setSpecReg32SPR_IVOR11(data)
{
%reg data
  mtspr SPR_IVOR11,data
}

asm uint32_t getSpecReg32SPR_IVOR11(void)
{

  mfspr r3, SPR_IVOR11
  
}

/* Interrupt Vector Offset Register 12 */
asm void setSpecReg32SPR_IVOR12(data)
{
%reg data
  mtspr SPR_IVOR12,data
}

asm uint32_t getSpecReg32SPR_IVOR12(void)
{

  mfspr r3, SPR_IVOR12
  
}

/* Interrupt Vector Offset Register 13 */
asm void setSpecReg32SPR_IVOR13(data)
{
%reg data
  mtspr SPR_IVOR13,data
}

asm uint32_t getSpecReg32SPR_IVOR13(void)
{

  mfspr r3, SPR_IVOR13
  
}

/* Interrupt Vector Offset Register 14 */
asm void setSpecReg32SPR_IVOR14(data)
{
%reg data
  mtspr SPR_IVOR14,data
}

asm uint32_t getSpecReg32SPR_IVOR14(void)
{

  mfspr r3, SPR_IVOR14
  
}

/* Interrupt Vector Offset Register 15 */
asm void setSpecReg32SPR_IVOR15(data)
{
%reg data
  mtspr SPR_IVOR15,data
}

asm uint32_t getSpecReg32SPR_IVOR15(void)
{

  mfspr r3, SPR_IVOR15
  
}

/* Interrupt Vector Offset Register 32 */
asm void setSpecReg32SPR_IVOR32(data)
{
%reg data
  mtspr SPR_IVOR32,data
}

asm uint32_t getSpecReg32SPR_IVOR32(void)
{

  mfspr r3, SPR_IVOR32
  
}

/* Interrupt Vector Offset Register 33 */
asm void setSpecReg32SPR_IVOR33(data)
{
%reg data
  mtspr SPR_IVOR33,data
}

asm uint32_t getSpecReg32SPR_IVOR33(void)
{

  mfspr r3, SPR_IVOR33
  
}

/* Interrupt Vector Offset Register 34 */
asm void setSpecReg32SPR_IVOR34(data)
{
%reg data
  mtspr SPR_IVOR34,data
}

asm uint32_t getSpecReg32SPR_IVOR34(void)
{

  mfspr r3, SPR_IVOR34
  
}

/* Interrupt Vector Prefix Register */
asm void setSpecReg32SPR_IVPR(data)
{
%reg data
  mtspr SPR_IVPR,data
}

asm uint32_t getSpecReg32SPR_IVPR(void)
{

  mfspr r3, SPR_IVPR
  
}

/* Link Register */
asm void setSpecReg32SPR_LR(data)
{
%reg data
  mtspr SPR_LR,data
}

asm uint32_t getSpecReg32SPR_LR(void)
{

  mfspr r3, SPR_LR
  
}

/* L1 Cache Control and Status Register 0 */
asm void setSpecReg32SPR_L1CSR0(data)
{
%reg data
    msync
    isync
    mtspr SPR_L1CSR0,data
}

asm uint32_t getSpecReg32SPR_L1CSR0(void)
{

  mfspr r3, SPR_L1CSR0
  
}

/* L1 Cache Flush and Invalidate Control Register 0 */
asm void setSpecReg32SPR_L1FINV0(data)
{
%reg data
  mtspr SPR_L1FINV0,data
}

asm uint32_t getSpecReg32SPR_L1FINV0(void)
{

  mfspr r3, SPR_L1FINV0
  
}

/* MMU Assist Register 0 */
asm void setSpecReg32SPR_MAS0(data)
{
%reg data
  mtspr SPR_MAS0,data
}

asm uint32_t getSpecReg32SPR_MAS0(void)
{

  mfspr r3, SPR_MAS0
  
}

/* MMU Assist Register 1 */
asm void setSpecReg32SPR_MAS1(data)
{
%reg data
  mtspr SPR_MAS1,data
}

asm uint32_t getSpecReg32SPR_MAS1(void)
{

  mfspr r3, SPR_MAS1
  
}

/* MMU Assist Register 2 */
asm void setSpecReg32SPR_MAS2(data)
{
%reg data
  mtspr SPR_MAS2,data
}

asm uint32_t getSpecReg32SPR_MAS2(void)
{

  mfspr r3, SPR_MAS2
  
}

/* MMU Assist Register 3 */
asm void setSpecReg32SPR_MAS3(data)
{
%reg data
  mtspr SPR_MAS3,data
}

asm uint32_t getSpecReg32SPR_MAS3(void)
{

  mfspr r3, SPR_MAS3
  
}

/* MMU Assist Register 4 */
asm void setSpecReg32SPR_MAS4(data)
{
%reg data
  mtspr SPR_MAS4,data
}

asm uint32_t getSpecReg32SPR_MAS4(void)
{

  mfspr r3, SPR_MAS4
  
}

/* MMU Assist Register 6 */
asm void setSpecReg32SPR_MAS6(data)
{
%reg data
  mtspr SPR_MAS6,data
}

asm uint32_t getSpecReg32SPR_MAS6(void)
{

  mfspr r3, SPR_MAS6
  
}

/* Machine Check Syndrome Register */
asm void setSpecReg32SPR_MCSR(data)
{
%reg data
  mtspr SPR_MCSR,data
}

asm uint32_t getSpecReg32SPR_MCSR(void)
{

  mfspr r3, SPR_MCSR
  
}

/* MMU Control and Status Register 0 */
asm void setSpecReg32SPR_MMUCSR(data)
{
%reg data
  mtspr SPR_MMUCSR0,data
}

asm uint32_t getSpecReg32SPR_MMUCSR(void)
{

  mfspr r3, SPR_MMUCSR0
  
}

/* Process ID Register */
asm void setSpecReg32SPR_PID0(data)
{
%reg data
  mtspr SPR_PID0,data
}

asm uint32_t getSpecReg32SPR_PID0(void)
{

  mfspr r3, SPR_PID0
  
}

/* SPE APU Status and Control Register */
asm void setSpecReg32SPR_SPEFSCR(data)
{
%reg data
  mtspr SPR_SPEFSCR,data
}

asm uint32_t getSpecReg32SPR_SPEFSCR(void)
{

  mfspr r3, SPR_SPEFSCR
  
}

/* SPR General 0 */
asm void setSpecReg32SPR_SPRG0(data)
{
%reg data
  mtspr SPR_SPRG0,data
}

asm uint32_t getSpecReg32SPR_SPRG0(void)
{

  mfspr r3, SPR_SPRG0
  
}

/* SPR General 1 */
asm void setSpecReg32SPR_SPRG1(data)
{
%reg data
  mtspr SPR_SPRG1,data
}

asm uint32_t getSpecReg32SPR_SPRG1(void)
{

  mfspr r3, SPR_SPRG1
  
}

/* SPR General 2 */
asm void setSpecReg32SPR_SPRG2(data)
{
%reg data
  mtspr SPR_SPRG2,data
}

asm uint32_t getSpecReg32SPR_SPRG2(void)
{

  mfspr r3, SPR_SPRG2
  
}

/* SPR General 3 */
asm void setSpecReg32SPR_SPRG3(data)
{
%reg data
  mtspr SPR_SPRG3,data
}

asm uint32_t getSpecReg32SPR_SPRG3(void)
{

  mfspr r3, SPR_SPRG3
  
}

/* Save/Restore Register 0 */
asm void setSpecReg32SPR_SRR0(data)
{
%reg data
  mtspr SPR_SRR0,data
}

asm uint32_t getSpecReg32SPR_SRR0(void)
{

  mfspr r3, SPR_SRR0
  
}

/* Save/Restore Register 1 */
asm void setSpecReg32SPR_SRR1(data)
{
%reg data
  mtspr SPR_SRR1,data
}

asm uint32_t getSpecReg32SPR_SRR1(void)
{

  mfspr r3, SPR_SRR1
  
}

/* Timer Control Register */
asm void setSpecReg32SPR_TCR(data) // OK!!
{
%reg data
  mtspr SPR_TCR,data
}

asm uint32_t getSpecReg32SPR_TCR(void) // OK!!
{

  mfspr r3, SPR_TCR
  
}

/* Timer Status Register */
asm void setSpecReg32SPR_TSR(data)
{
%reg data
  mtspr SPR_TSR,data
}

asm uint32_t getSpecReg32SPR_TSR(void)
{

  mfspr r3, SPR_TSR
  
}

/* User SPR General 0 */
asm void setSpecReg32SPR_USPRG0(data)
{
%reg data
  mtspr SPR_USPRG0,data
}

asm uint32_t getSpecReg32SPR_USPRG0(void)
{

  mfspr r3, SPR_USPRG0
  
}

/* Integer Exception Register */
asm void setSpecReg32SPR_XER(data)
{
%reg data
  mtspr SPR_XER,data
}

asm uint32_t getSpecReg32SPR_XER(void)
{

  mfspr r3, SPR_XER
  
}

/* L1 cache config register 0 - Read-only */
asm uint32_t getSpecReg32SPR_L1CFG0(void)
{

   mfspr r3,SPR_L1CFG0
   
}

/* MMU configuration register - Read-only */
asm uint32_t getSpecReg32SPR_MMUCFG(void)
{

   mfspr r3,SPR_MMUCFG
   
}

/* Processor ID Register */
asm uint32_t getSpecReg32SPR_PIR(void)
{

  mfspr r3, SPR_PIR
  
}

/* Processor Version Register */
asm uint32_t getSpecReg32SPR_PVR(void)
{

  mfspr r3, SPR_PVR
  
}

/* System Version Register */
asm uint32_t getSpecReg32SPR_SVR(void)
{

  mfspr r3, SPR_SVR
  
}

/* TLB0 Configuration Register */
asm uint32_t getSpecReg32SPR_TLB0CFG(void)
{

  mfspr r3, SPR_TLB0CFG
  
}

/* TLB1 Configuration Register */
asm uint32_t getSpecReg32SPR_TLB1CFG(void)
{

  mfspr r3, SPR_TLB1CFG
  
}

/* SPR General 4 - Read/write with different index */
asm void setSpecReg32SPR_SPRG4(data)
{
%reg data
   mtspr SPR_SPRG4,data
}

asm uint32_t getSpecReg32SPR_SPRG4(void)
{

   mfspr r3,SPR_SPRG4
   
}

/* SPR General 5 - Read/write with different index */
asm void setSpecReg32SPR_SPRG5(data)
{
%reg data
   mtspr SPR_SPRG5,data
}

asm uint32_t getSpecReg32SPR_SPRG5(void)
{

   mfspr r3,SPR_SPRG5
   
}

/* SPR General 6 */
asm uint32_t getSpecReg32SPR_SPRG6(void)
{

  mfspr r3, SPR_SPRG6
  
}

/* SPR General 7 */
asm uint32_t getSpecReg32SPR_SPRG7(void)
{

  mfspr r3, SPR_SPRG7
  
}

/* Time Base Lower */
asm uint32_t getSpecReg32SPR_TBL(void)
{

  mfspr r3, SPR_TBL
  
}

/* Time Base Upper */
asm uint32_t getSpecReg32SPR_TBU(void)
{

  mfspr r3, SPR_TBU
  
}

#endif /* __SPR_Macros_H */

/*
** ###################################################################
**
**     This file was created by UNIS Processor Expert 1.02 [03.59]
**     for the Freescale MPC5500 series of microcontrollers.
**
** ###################################################################
*/
