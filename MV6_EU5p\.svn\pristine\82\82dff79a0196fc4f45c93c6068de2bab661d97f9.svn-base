/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _SAF3_CTRL_H_
#define _SAF3_CTRL_H_

/** include files **/
#include "typedefs.h"

/** public data **/
extern uint8_t  S3FlgDisLRelay;
extern uint8_t  S3FlgDisL1;
extern uint8_t  S3FlgDisL2;
extern uint8_t  S3FlgAllowStart;
extern int16_t  S3VDbwDisLoads;
extern uint8_t  S3FlgTestDisLoads;
extern uint8_t  S3ForceShtDwn;
extern uint8_t  S3FlgKeyOffSent;
extern uint16_t S3SMPFwVer;

/** public functions **/
extern void Saf3Mgm_T5ms(void);
extern void Saf3Mgm_T10ms(void);
extern void Saf3Mgm_T100ms(void);
extern void Saf3Mgm_Init(void); 

/** private functions **/

/** macros **/
#define Saf3Mgm_ResetSMP()  DIGIO_OutSet(RESET_SMP,RESET_SMP_LVL)
#define Saf3Mgm_RunSMP()    DIGIO_OutSet(RESET_SMP,RUN_SMP_LVL)

#endif // _SAF3_CTRL_H_
