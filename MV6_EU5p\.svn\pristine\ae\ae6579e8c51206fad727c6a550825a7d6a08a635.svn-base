/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#include "mpc5500_spr_macros.h"   // needed to use the getSpecReg32SPR_L1CSR0 and set methods
                                  //  (used by the SYS_CacheDisable nad SYS_CacheEnable)
#include "sys.h"      // needed to use the SYS_CacheDisable and SYS_CacheEnable 
#include "ivor.h"



extern vuint32_t __IV_ADDR;           /* Defined in the linker file          */

void IVOR0Handler (void);
void IVOR1Handler (void);
void IVOR2Handler (void);
void IVOR3Handler (void);
void IVOR4Handler (void);
void IVOR5Handler (void);
void IVOR6Handler (void);
void IVOR7Handler (void);
void IVOR8Handler (void);
void IVOR9Handler (void);
void IVOR10Handler (void);
void IVOR11Handler (void);
void IVOR12Handler (void);
void IVOR13Handler (void);
void IVOR14Handler (void);
void IVOR15Handler (void);
void IVOR32Handler (void);
void IVOR33Handler (void);
void IVOR34Handler (void);

/** external data **/
void (*IVOR10_UserFunction) (void);
void (*IVOR11_UserFunction) (void);
void (*IVOR_Common_ManagerUserFunction) (void);
void (*IVOR2_UserFunction) (void);
void IVOR2_Manager(void);

/* ------------------------------------------------------------------------- */
/* SYS/IVOR's variables definition                                                 */
/* ------------------------------------------------------------------------- */
uint8_t  IvorIndex;
uint32_t SRR0_Value;
uint32_t SRR1_Value;
uint32_t CSRR0_Value;
uint32_t CSRR1_Value;
uint32_t SPR_ESRValue;
uint32_t SPR_DEARValue;
uint32_t SPR_MCSRValue;

/** local definitions **/

/** internal functions **/
void IVOR10_Manager(void);
void IVOR11_Manager(void);
void IVOR_Common_Manager(void);


#ifdef __MWERKS__

inline void setSpecReg32SPEC_REG_IVOR0(uint32_t register data)
{
  asm {mtspr SPR_IVOR0,data}
}

inline void setSpecReg32SPEC_REG_IVOR1(uint32_t register data)
{
  asm {mtspr SPR_IVOR1,data}
}

inline void setSpecReg32SPEC_REG_IVOR2(uint32_t register data)
{
  asm {mtspr SPR_IVOR2,data}
}

inline void setSpecReg32SPEC_REG_IVOR3(uint32_t register data)
{
  asm {mtspr SPR_IVOR3,data}
}

inline void setSpecReg32SPEC_REG_IVOR4(uint32_t register data)
{
  asm {mtspr SPR_IVOR4,data}
}

inline void setSpecReg32SPEC_REG_IVOR5(uint32_t register data)
{
  asm {mtspr SPR_IVOR5,data}
}

inline void setSpecReg32SPEC_REG_IVOR6(uint32_t register data)
{
  asm {mtspr SPR_IVOR6,data}
}

inline void setSpecReg32SPEC_REG_IVOR7(uint32_t register data)
{
  asm {mtspr SPR_IVOR7,data}
}

inline void setSpecReg32SPEC_REG_IVOR8(uint32_t register data)
{
  asm {mtspr SPR_IVOR8,data}
}

inline void setSpecReg32SPEC_REG_IVOR9(uint32_t register data)
{
  asm {mtspr SPR_IVOR9,data}
}

inline void setSpecReg32SPEC_REG_IVOR10(uint32_t register data)
{
  asm {mtspr SPR_IVOR10,data}
}

inline void setSpecReg32SPEC_REG_IVOR11(uint32_t register data)
{
  asm {mtspr SPR_IVOR11,data}
}

inline void setSpecReg32SPEC_REG_IVOR12(uint32_t register data)
{
  asm {mtspr SPR_IVOR12,data}
}

inline void setSpecReg32SPEC_REG_IVOR13(uint32_t register data)
{
  asm {mtspr SPR_IVOR13,data}
}

inline void setSpecReg32SPEC_REG_IVOR14(uint32_t register data)
{
  asm {mtspr SPR_IVOR14,data}
}

inline void setSpecReg32SPEC_REG_IVOR15(uint32_t register data)
{
  asm {mtspr SPR_IVOR15,data}
}

inline void setSpecReg32SPEC_REG_IVOR32(uint32_t register data)
{
  asm {mtspr SPR_IVOR32,data}
}

inline void setSpecReg32SPEC_REG_IVOR33(uint32_t register data)
{
  asm {mtspr SPR_IVOR33,data}
}

inline void setSpecReg32SPEC_REG_IVOR34(uint32_t register data)
{
  asm {mtspr SPR_IVOR34,data}
}

#else /* __MWERKS__ */

inline void setSpecReg32SPEC_REG_IVOR0(data)
{
__MTSPR(SPR_IVOR0,data);
}

inline void setSpecReg32SPEC_REG_IVOR1(data)
{
__MTSPR(SPR_IVOR1,data);
}

inline void setSpecReg32SPEC_REG_IVOR2(data)
{
__MTSPR(SPR_IVOR2,data);
}

inline void setSpecReg32SPEC_REG_IVOR3(data)
{
__MTSPR(SPR_IVOR3,data);
}

inline void setSpecReg32SPEC_REG_IVOR4(data)
{
__MTSPR(SPR_IVOR4,data);
}

inline void setSpecReg32SPEC_REG_IVOR5(data)
{
__MTSPR(SPR_IVOR5,data);
}

inline void setSpecReg32SPEC_REG_IVOR6(data)
{
__MTSPR(SPR_IVOR6,data);
}

inline void setSpecReg32SPEC_REG_IVOR7(data)
{
__MTSPR(SPR_IVOR7,data);
}

inline void setSpecReg32SPEC_REG_IVOR8(data)
{
__MTSPR(SPR_IVOR8,data);
}

inline void setSpecReg32SPEC_REG_IVOR9(data)
{
__MTSPR(SPR_IVOR9,data);
}

inline void setSpecReg32SPEC_REG_IVOR10(data)
{
__MTSPR(SPR_IVOR10,data);
}

inline void setSpecReg32SPEC_REG_IVOR11(data)
{
__MTSPR(SPR_IVOR11,data);
}

inline void setSpecReg32SPEC_REG_IVOR12(data)
{
__MTSPR(SPR_IVOR12,data);
}

inline void setSpecReg32SPEC_REG_IVOR13(data)
{
__MTSPR(SPR_IVOR13,data);
}

inline void setSpecReg32SPEC_REG_IVOR14(data)
{
__MTSPR(SPR_IVOR14,data);
}

inline void setSpecReg32SPEC_REG_IVOR15(data)
{
__MTSPR(SPR_IVOR15,data);
}

inline void setSpecReg32SPEC_REG_IVOR32(data)
{
__MTSPR(SPR_IVOR32,data);
}

inline void setSpecReg32SPEC_REG_IVOR33(data)
{
__MTSPR(SPR_IVOR33,data);
}

inline void setSpecReg32SPEC_REG_IVOR34(data)
{
__MTSPR(SPR_IVOR34,data);
}

#endif /* __MWERKS__ */

/* ------------------------------------------------------------------------- */
void InitIVPR(void) 
{
#ifdef __MWERKS__   
  asm ("lis r5, __IV_ADDR@h ");   /* IVPR = address base used with IVOR's */
  asm ("mtIVPR r5 ");
#else
  __asm ("lis r5, __IV_ADDR@h ");   /* IVPR = address base used with IVOR's */
  __asm ("mtIVPR r5 ");
#endif
}

/* ------------------------------------------------------------------------- */
void InitIVOR0Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR0((uint32_t)IVOR0Handler & 0x0000FFFF);
}

/* ------------------------------------------------------------------------- */
void InitIVOR1Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR1((uint32_t)IVOR1Handler & 0x0000FFFF);
}

/* ------------------------------------------------------------------------- */
void InitIVOR2Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR2((uint32_t)IVOR2Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR3Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR3((uint32_t)IVOR3Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR4Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR4((uint32_t)IVOR4Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR5Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR5((uint32_t)IVOR5Handler & 0x0000FFFF);
}

/* ------------------------------------------------------------------------- */
void InitIVOR6Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR6((uint32_t)IVOR6Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR7Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR7((uint32_t)IVOR7Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR8Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR8((uint32_t)IVOR8Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR9Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR9((uint32_t)IVOR9Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR10Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR10((uint32_t)IVOR10Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR11Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR11((uint32_t)IVOR11Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR12Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR12((uint32_t)IVOR12Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR13Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR13((uint32_t)IVOR13Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR14Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR14((uint32_t)IVOR14Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR15Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR15((uint32_t)IVOR15Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR32Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR32((uint32_t)IVOR32Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR33Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR33((uint32_t)IVOR33Handler & 0x0000FFFF);
}
/* ------------------------------------------------------------------------- */
void InitIVOR34Handler(void) 
{
      setSpecReg32SPEC_REG_IVOR34((uint32_t)IVOR34Handler & 0x0000FFFF);
}

void IVOR_Config(void)
{
  InitIVPR();
  InitIVOR0Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR1Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR2Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR3Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR4Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR5Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR6Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR7Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR8Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR9Handler();             /* Initialize interrupt handler, load IVPR */
  InitIVOR10Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR11Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR12Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR13Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR14Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR15Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR32Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR33Handler();            /* Initialize interrupt handler, load IVPR */
  InitIVOR34Handler();            /* Initialize interrupt handler, load IVPR */
}


void IVOR10_Manager(void)
{
   if (IVOR10_UserFunction)
   {
      IVOR10_UserFunction();
   }
}

void IVOR11_Manager(void)
{
/* Stub function */
   if (IVOR11_UserFunction)
   {
      IVOR11_UserFunction();
   }
}

void IVOR2_Manager(void)
{
  if (IVOR2_UserFunction)
  {
    IVOR2_UserFunction();
  }
}

void IVOR_Common_Manager(void)
{
  if (IVOR_Common_ManagerUserFunction)
  {
    IVOR_Common_ManagerUserFunction();
  }
}

int16_t SYS_GetMCSRExceptionType(uint32_t* ExcepType)
{
    int16_t ReturnCode = IVOR1_RECOVERABLE;
    
    if (SPR_MCSRValue & SPR_MCSR_FIELD_BUS_WRERR)
    {
        *ExcepType = IVOR1_BUS_ERR;
        ReturnCode = IVOR1_UNRECOVERABLE;     
    }
    else
    if (SPR_MCSRValue & SPR_MCSR_FIELD_EXCP_ERR)
    {
      *ExcepType = IVOR1_EXCP_ERR;
      ReturnCode = IVOR1_RECOVERABLE;     
    }
    else
    if (SPR_MCSRValue & SPR_MCSR_FIELD_CPERR)
    {
        *ExcepType = IVOR1_CACHE_PARITY_ERR;
        ReturnCode = IVOR1_RECOVERABLE;     
    }
    else
    if (SPR_MCSRValue & SPR_MCSR_FIELD_CP_PERR)
    {
        *ExcepType = IVOR1_CACHE_PUSH_PARITY_ERR;
        ReturnCode = IVOR1_UNRECOVERABLE;     
    }
    else
    if (SPR_MCSRValue & SPR_MCSR_FIELD_MCP)
    {
         *ExcepType = IVOR1_MC_INPUT_PIN;
         ReturnCode = IVOR1_UNRECOVERABLE;  //Worst case; no risk   
    }
    else
    {
    }
    return  ReturnCode;
}


int16_t SYS_GetESRExceptionType(uint8_t IvorType,uint32_t* ExcepType)
{
    int16_t ReturnCode = NO_ERROR;

    switch (IvorType)
    {
//IVOR2
        case (IVOR2_INDX):
            if (SPR_ESRValue & SPR_ESR_FIELD_BO)
            {
               *ExcepType = IVOR2_BYTE_ORDER;
            }
            else 
            if  (SPR_ESRValue & SPR_ESR_FIELD_XTE)
            {
               *ExcepType = IVOR2_EXTERN_TERMIN_ERR;
            }
            else
            if ((SPR_ESRValue == (SPR_ESR_FIELD_DLK | SPR_ESR_FIELD_ST)) ||\
                (SPR_ESRValue == (SPR_ESR_FIELD_ILK | SPR_ESR_FIELD_ST)) ||\
                (SPR_ESRValue == SPR_ESR_FIELD_ST) ||\
                (SPR_ESRValue == SPR_ESR_FIELD_DLK) ||\
                (SPR_ESRValue == SPR_ESR_FIELD_ILK)) 
            {
               *ExcepType = IVOR2_CACHE_LOCK;
            }
            else
            if ((SPR_ESRValue == SPR_ESR_FIELD_ST) ||\
                (SPR_ESRValue == SPR_ESR_FIELD_SPE) ||\
                (SPR_ESRValue == (SPR_ESR_FIELD_SPE | SPR_ESR_FIELD_ST))||\
                (SPR_ESRValue == 0x0))
            {
                *ExcepType = IVOR2_ACCESS_EXECPT;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
//IVOR3        
        case (IVOR3_INDX):
            if ((SPR_ESRValue & SPR_ESR_FIELD_XTE) || (SPR_ESRValue==0x0))
            {
                *ExcepType = IVOR3_INSTRC_STOR;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
//IVOR5
        case (IVOR5_INDX):
            if ((SPR_ESRValue == SPR_ESR_FIELD_ST) ||\
                (SPR_ESRValue == SPR_ESR_FIELD_SPE) ||\
                (SPR_ESRValue == (SPR_ESR_FIELD_SPE | SPR_ESR_FIELD_ST))||\
                (SPR_ESRValue == 0x0))
            {
                *ExcepType = IVOR5_ALLIGN_INTERR;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
//IVOR6         
        case (IVOR6_INDX):
            if (SPR_ESRValue & SPR_ESR_FIELD_PIL)
            {
                *ExcepType = IVOR6_ILLEGAL;
            }
            else
            if (SPR_ESRValue & SPR_ESR_FIELD_PPR)
            {
                *ExcepType = IVOR6_PRIVILEGED;
            }
            else
            if (SPR_ESRValue & SPR_ESR_FIELD_PTR)
            {
                *ExcepType = IVOR6_TRAP;
            }
            else
            if (SPR_ESRValue & SPR_ESR_FIELD_PUO)
            {
                *ExcepType = IVOR6_UNIMPLEMENT;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;
            }
        break;
//IVOR13        
        case (IVOR13_INDX):
            if ((SPR_ESRValue == SPR_ESR_FIELD_ST) ||\
                (SPR_ESRValue == SPR_ESR_FIELD_SPE) ||\
                (SPR_ESRValue == (SPR_ESR_FIELD_SPE | SPR_ESR_FIELD_ST))||\
                (SPR_ESRValue == 0x0))
            {
                *ExcepType = IVOR13_DATA_TLB_ERR;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
//IVOR32
        case (IVOR32_INDX):
            if (SPR_ESRValue == SPR_ESR_FIELD_SPE)
            {
                *ExcepType = IVOR32_SPE_APU_UNAVAIL;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
//IVOR33
        case (IVOR33_INDX):
            if (SPR_ESRValue == SPR_ESR_FIELD_SPE)
            {
                *ExcepType = IVOR33_SPE_FP_DATA_INTERR;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
//IVOR34
        case (IVOR34_INDX):
            if (SPR_ESRValue == SPR_ESR_FIELD_SPE)
            {
                *ExcepType = IVOR34_SPE_FP_ROUND_INTERR;
            }
            else
            {
                ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;     
            }
        break;
         
        default:
            ReturnCode = SYS_GetESRExceptionType_FCN_ERROR;
        break;
    }
    return ReturnCode;     
}

 
