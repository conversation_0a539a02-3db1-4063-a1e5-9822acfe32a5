clear all;
clc;

% load('pattern_segnali');
load('tractionmv_f4_ppc_2015_05_29_13_41');
clearvars -except AxCAN AyCAN AzCAN ButtonCAN ClutchCAN CMEDriverCAN CMEEstMinCAN ...
                  FlagBrakeCAN GasPosCAN GearPosCAN GxCAN GyCAN GzCAN PitchCAN ...
                  RollCAN rpmCAN SelectorCAN VFrontCAN VRearCAN Longitude...
                  Latitude Satellites Second;
var_list = who;
var_list = var_list';

n_vars = length(var_list);
n_samples = length(eval(var_list{1}));

% Sample time dei diversi segnali inviati sul CAN
t_sample = 10*ones(1,n_vars);

% Definizione formato righe file csv
formatSpec1 = [];
formatSpec2 = [];
formatSpec3 = [];

for id=1:(n_vars-1)
    formatSpec1 = [formatSpec1 '%s,'];
    formatSpec2 = [formatSpec2 '%d,'];
    formatSpec3 = [formatSpec3 '%f,'];
end
formatSpec1 = [formatSpec1 '%s\n'];
formatSpec2 = [formatSpec2 '%d\n'];
formatSpec3 = [formatSpec3 '%f\n'];

% Print prime 2 righe - COSTANTI
fileID = fopen('pattern_segnali.csv','w');
fprintf(fileID,formatSpec1,var_list{1,:});
fprintf(fileID,formatSpec2,t_sample(1,:));

% Print altre righe
for idrow=1:(n_samples/10)
    vect2write = [];
    for idvar=1:n_vars
        vect2write = [vect2write eval([var_list{idvar} '(idrow)'])];       
    end
    
    fprintf(fileID,formatSpec3,vect2write);
end

fclose(fileID);