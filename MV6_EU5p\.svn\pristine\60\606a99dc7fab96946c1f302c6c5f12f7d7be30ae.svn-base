/*
 * File: lambdamgm.c
 *
 * Code generated for Simulink model 'lambdamgm'.
 *
 * Model version                  : 1.1171
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Jul  5 09:54:45 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#include "lambdamgm.h"
#include "lambdamgm_private.h"

/* Named constants for Chart: '<S17>/Calc_Transitions' */
#define lambdamgm_IN_DIAGNOSABLE       ((uint8_T)1U)
#define lambdamgm_IN_DONE              ((uint8_T)1U)
#define lambdamgm_IN_END               ((uint8_T)2U)
#define lambdamgm_IN_GO_LEAN           ((uint8_T)1U)
#define lambdamgm_IN_GO_RICH           ((uint8_T)2U)
#define lambdamgm_IN_IN_LEAN           ((uint8_T)3U)
#define lambdamgm_IN_IN_RICH           ((uint8_T)4U)
#define lambdamgm_IN_L2R_TR_TIME       ((uint8_T)1U)
#define lambdamgm_IN_LAM_OBD2_DIAG     ((uint8_T)2U)
#define lambdamgm_IN_LEAN              ((uint8_T)2U)
#define lambdamgm_IN_NORMAL_OBJ        ((uint8_T)3U)
#define lambdamgm_IN_NO_ACTIVE_CHILD   ((uint8_T)0U)
#define lambdamgm_IN_R2L_TR_TIME       ((uint8_T)3U)
#define lambdamgm_IN_RICH              ((uint8_T)4U)

/* Named constants for Chart: '<S50>/Chart' */
#define lambdamgm_IN_GO                ((uint8_T)1U)
#define lambdamgm_IN_STOP              ((uint8_T)2U)

/* Named constants for Chart: '<S9>/Chart' */
#define lambdamgm_IN_DIAG_CAT          ((uint8_T)1U)
#define lambdamgm_IN_INIT_CAT          ((uint8_T)2U)
#define lambdamgm_IN_READY_CAT         ((uint8_T)1U)
#define lambdamgm_IN_WAIT_COND_CAT     ((uint8_T)2U)
#define lambdamgm_IN_WAIT_READY_CAT    ((uint8_T)3U)
#define lambdamgm_IN_WAIT_TDC_CAT      ((uint8_T)3U)

/* Named constants for Chart: '<S4>/VLambdaState_Calc' */
#define lambdamgm_IN_Cutoff            ((uint8_T)1U)
#define lambdamgm_IN_NO_OSC            ((uint8_T)1U)
#define lambdamgm_IN_Normal            ((uint8_T)2U)
#define lambdamgm_IN_OSC               ((uint8_T)2U)
#define lambdamgm_IN_VLAM2INIT         ((uint8_T)1U)
#define lambdamgm_IN_VLAM2POOR         ((uint8_T)1U)
#define lambdamgm_IN_VLAM2RDY          ((uint8_T)2U)
#define lambdamgm_IN_VLAM2RICH         ((uint8_T)2U)
#define lambdamgm_IN_VLAMINIT          ((uint8_T)1U)
#define lambdamgm_IN_VLAMPOOR          ((uint8_T)1U)
#define lambdamgm_IN_VLAMRDY           ((uint8_T)2U)
#define lambdamgm_IN_VLAMRICH          ((uint8_T)2U)
#define lambdamgm_IN_Wait              ((uint8_T)3U)

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_LAMBDAMGM_

uint32_T LambdaMgmTimer;

/* Block signals and states (default storage) */
D_Work_lambdamgm_T lambdamgm_DWork;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint32_T AbsCntTransLam;

/* Abs counter of lambda sensor transition */
uint8_T CatDiagCond;

/* Enable cat functional diagnosis */
uint16_T CntLam1Trs;

/* Transition lambda counter */
uint8_T CntLam2Liveness;

/* Lambda 2 liveness activities */
uint16_T CntLam2Trs;

/* Transition lambda 2 counter */
uint16_T CntLam2TrsMax;

/* Transition lambda 2 counter max */
uint16_T CntLam2TrsMin;

/* Transition lambda 2 counter min */
uint16_T CntLamFunDiag;

/* Abs counter of lambda functional diag */
uint8_T CntLamLiveness;

/* Lambda liveness activities */
uint16_T CntLamNoDiag;

/* CntLamNoDiag */
uint16_T CntLamWatchdog;

/* Lambda Watchdog */
uint16_T CntMinMaxLam2;

/* Lambda 2 functional counter diagnosis */
uint16_T CntOBD2LamL2R;

/* Time lambda transition L2R */
uint16_T CntOBD2LamL2RMax;

/* Time lambda transition L2R max */
uint16_T CntOBD2LamL2RMin;

/* Time lambda transition L2R min */
uint16_T CntOBD2LamR2L;

/* Time lambda transition R2L */
uint16_T CntOBD2LamR2LMax;

/* Time lambda transition R2L max */
uint16_T CntOBD2LamR2LMin;

/* Time lambda transition R2L min */
uint8_T CntOBD2TstL2R;

/* Number of diagnosis L2R test */
uint8_T CntOBD2TstR2L;

/* Number of diagnosis R2L test */
uint8_T FlgCatDiagOn;

/* Cat diagnosis flag ack */
uint8_T FlgLam2Ready;

/* Flag to indicate lambda 2 closed loop ready */
uint8_T FlgLamNotCoherent;

/* Flag to indicate lambda not coherent */
uint8_T FlgLamReady;

/* Flag to indicate lambda closed loop ready */
uint8_T FlgO22DiagOn;

/* Flag diagnosis lambda 2 on */
uint8_T FlgO2DiagOn;

/* Flag diagnosis lambda on */
uint8_T FlgVeryRichPoor;

/* Flag to indicate very rich-poor state */
uint16_T FreqOscLambda;

/* Average lambda1 sensor oscillation frequency */
uint32_T IDLambdaMgm;

/* ID Version */
uint8_T Lam2DiagCond;

/* Enable Lambda 2 functional diagnosis */
uint8_T LamDiagCond;

/* Enable lambda functional diagnosis */
uint8_T LamOBD2End;

/* End of lambda functional diagnosis */
uint16_T LamObjOBD2;

/* Force lambda Obj for diagnosis OBD2 */
uint32_T PerOscLambda;

/* Lambda sensor oscillation period */
uint32_T PerOscLambdaMax;

/* Lambda sensor oscillation period max */
uint32_T PerOscLambdaMin;

/* Lambda sensor oscillation period min */
uint32_T RatioCatDiag;

/* Ratio cat diagnosis */
uint8_T StCatDiag;

/* Cat diagnosis status */
uint8_T StLamFuncDiag;

/* Substate for lambda functional diag */
uint8_T StLamOBD2DIag;

/* OBD2 Transition lambda status */
uint16_T ThrVLam2P2R;

/* Poor to rich Vlambda 2 threshold */
uint16_T ThrVLam2R2P;

/* Rich to poor Vlambda 2 threshold */
uint16_T ThrVLamP2R;

/* Poor to rich Vlambda threshold */
uint16_T ThrVLamR2P;

/* Rich to poor Vlambda threshold */
uint16_T VLambda;

/* Lambda voltage */
uint16_T VLambda0;

/* Lambda voltage */
uint16_T VLambda2;

/* Lambda 2 voltage */
uint16_T VLambda20;

/* Lambda 2 voltage */
uint16_T VLambdaCrk;

/* Lambda voltage corrected during crank */
uint8_T VLambdaState;

/* VLambda state */
uint8_T VLambdaState2;

/* VLambda 2 state */
uint16_T VMaxLam2;

/* Lambda 2 voltage max */
uint16_T VMinLam2;

/* Lambda 2 voltage min */

/* Forward declaration for local functions */
static void lambdam_enter_internal_VLAMPOOR(void);
static void lambdamgm_VLAMRDY(void);

/* System initialize for function-call system: '<S4>/cal_freqosclam' */
void lambdamgm_cal_freqosclam_Init(void)
{
  /* InitializeConditions for Memory: '<S12>/INIT' */
  lambdamgm_DWork.INIT_PreviousInput = 1048576U;

  /* InitializeConditions for Memory: '<S12>/Memory1' */
  lambdamgm_DWork.Memory1_PreviousInput_nnq = 65535U;
}

/* Output and update for function-call system: '<S4>/cal_freqosclam' */
void lambdamgm_cal_freqosclam(void)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int16_T rtb_Conversion2;
  int32_T rtb_Conversion4;
  int16_T rtb_Conversion5;
  uint32_T u1;

  /* Product: '<S12>/Divide' */
  u1 = 1024000U / PerOscLambda;
  if (u1 > 65535U) {
    u1 = 65535U;
  }

  /* DataTypeConversion: '<S86>/Conversion2' incorporates:
   *  Product: '<S12>/Divide'
   */
  rtb_Conversion2 = (int16_T)u1;

  /* DataTypeConversion: '<S86>/Conversion4' incorporates:
   *  Memory: '<S12>/INIT'
   */
  rtb_Conversion4 = (int32_T)lambdamgm_DWork.INIT_PreviousInput;

  /* DataTypeConversion: '<S86>/Conversion5' incorporates:
   *  Product: '<S12>/Divide'
   */
  rtb_Conversion5 = (int16_T)u1;

  /* S-Function (FOF_Reset_S16_FXP): '<S86>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S12>/KFFREQLAM'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_Conversion5, KFFREQLAM, rtb_Conversion2,
                    lambdamgm_DWork.flgresfiltfreq, rtb_Conversion4);

  /* DataTypeConversion: '<S87>/Conversion' */
  FreqOscLambda = (uint16_T)rtb_FOF_Reset_S16_FXP_o1;

  /* Memory: '<S12>/Memory' */
  u1 = lambdamgm_DWork.Memory_PreviousInput_izo;

  /* MinMax: '<S12>/Max' */
  if (PerOscLambda > u1) {
    PerOscLambdaMax = PerOscLambda;
  } else {
    PerOscLambdaMax = u1;
  }

  /* End of MinMax: '<S12>/Max' */

  /* MinMax: '<S12>/Max1' incorporates:
   *  Memory: '<S12>/Memory1'
   */
  if (PerOscLambda < lambdamgm_DWork.Memory1_PreviousInput_nnq) {
    PerOscLambdaMin = PerOscLambda;
  } else {
    PerOscLambdaMin = lambdamgm_DWork.Memory1_PreviousInput_nnq;
  }

  /* End of MinMax: '<S12>/Max1' */

  /* Update for Memory: '<S12>/INIT' incorporates:
   *  DataTypeConversion: '<S86>/Conversion7'
   */
  lambdamgm_DWork.INIT_PreviousInput = (uint32_T)rtb_FOF_Reset_S16_FXP_o2;

  /* Update for Memory: '<S12>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_izo = PerOscLambdaMax;

  /* Update for Memory: '<S12>/Memory1' */
  lambdamgm_DWork.Memory1_PreviousInput_nnq = PerOscLambdaMin;
}

/* Function for Chart: '<S4>/VLambdaState_Calc' */
static void lambdam_enter_internal_VLAMPOOR(void)
{
  uint32_T qY;
  int32_T tmp;

  /* Entry Internal 'VLAMPOOR': '<S11>:20' */
  /* Transition: '<S11>:314' */
  if (((int32_T)lambdamgm_DWork.setLamLiveness) != 0) {
    /* Transition: '<S11>:312' */
    lambdamgm_DWork.setLamLiveness = 0U;
    tmp = ((int32_T)CntLamLiveness) + 1;
    if (tmp > 255) {
      tmp = 255;
    }

    CntLamLiveness = (uint8_T)tmp;
    if (((int32_T)CntLamLiveness) > 250) {
      /* Transition: '<S11>:309' */
      CntLamLiveness = 0U;
    } else {
      /* Transition: '<S11>:310' */
    }
  } else {
    /* Transition: '<S11>:313' */
  }

  if (AbsCntTransLam >= ((uint32_T)THRR2PRESDIAG)) {
    /* Transition: '<S11>:221' */
    lambdamgm_DWork.tmpDiag = 2U;

    /*  NO_FAULT */
    AbsCntTransLam = 0U;
    lambdamgm_DWork.flgresfiltfreq = 0U;
  } else {
    /* Transition: '<S11>:222' */
  }

  /* Inport: '<Root>/LambdaMgmTimer' */
  /* Transition: '<S11>:223' */
  qY = LambdaMgmTimer - /*MW:OvSatOk*/ lambdamgm_DWork.t_entry_poor;
  if (qY > LambdaMgmTimer) {
    qY = 0U;
  }

  PerOscLambda = qY;

  /* Inport: '<Root>/LambdaMgmTimer' */
  lambdamgm_DWork.t_entry_poor = LambdaMgmTimer;
  CntLamWatchdog = 0U;
  qY = AbsCntTransLam + /*MW:OvSatOk*/ 1U;
  if (qY < AbsCntTransLam) {
    qY = MAX_uint32_T;
  }

  AbsCntTransLam = qY;

  /* Outputs for Function Call SubSystem: '<S4>/cal_freqosclam' */
  /* Event: '<S11>:128' */
  lambdamgm_cal_freqosclam();

  /* End of Outputs for SubSystem: '<S4>/cal_freqosclam' */
  /* Entry 'Normal': '<S11>:27' */
  StLamFuncDiag = ((uint8_T)VLAMPOOR_NORMAL);
}

/* Function for Chart: '<S4>/VLambdaState_Calc' */
static void lambdamgm_VLAMRDY(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState_ftl;
  int32_T tmp;
  boolean_T guard1 = false;

  /* Inport: '<Root>/Rpm' */
  /* During 'VLAMRDY': '<S11>:14' */
  if (((int32_T)Rpm) == 0) {
    /* Transition: '<S11>:6' */
    /* Exit Internal 'VLAMRDY': '<S11>:14' */
    /* Exit Internal 'DIAG_FREQ': '<S11>:55' */
    lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_NO_ACTIVE_CHILD;

    /* Exit Internal 'Lam_Trans': '<S11>:17' */
    /* Exit Internal 'VLAMPOOR': '<S11>:20' */
    /* Exit Internal 'VLAMRICH': '<S11>:35' */
    /* Exit Internal 'LAM_CUTOFF_DIAG': '<S11>:41' */
    lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_NO_ACTIVE_CHILD;
    lambdamgm_DWork.is_Lam_Trans = lambdamgm_IN_NO_ACTIVE_CHILD;
    lambdamgm_DWork.is_VLambdaState = lambdamgm_IN_VLAMINIT;

    /* Entry 'VLAMINIT': '<S11>:8' */
    VLambdaState = ((uint8_T)VLAMINIT);
    StLamFuncDiag = ((uint8_T)VLAMPOOR_INIT);
    CntLamFunDiag = 0U;
    CntLamWatchdog = 0U;
    FlgLamReady = 0U;

    /* Inport: '<Root>/CntTdcCrk' */
    lambdamgm_DWork.oldTdcCrk = CntTdcCrk;
    lambdamgm_DWork.setLamLiveness = 0U;
    CntLamLiveness = 0U;
  } else {
    /* During 'Lam_Trans': '<S11>:17' */
    if (((uint32_T)lambdamgm_DWork.is_Lam_Trans) == lambdamgm_IN_VLAMPOOR) {
      VLambdaState = ((uint8_T)VLAMPOOR);

      /* During 'VLAMPOOR': '<S11>:20' */
      if (((uint32_T)VLambda) > (((uint32_T)ThrVLamP2R) << ((uint32_T)4))) {
        /* Transition: '<S11>:19' */
        /* Exit Internal 'VLAMPOOR': '<S11>:20' */
        lambdamgm_DWork.is_Lam_Trans = lambdamgm_IN_VLAMRICH;

        /* Entry 'VLAMRICH': '<S11>:35' */
        VLambdaState = ((uint8_T)VLAMRICH);
        lambdamgm_DWork.setLamLiveness = 1U;

        /* Entry Internal 'VLAMRICH': '<S11>:35' */
        /* Entry Internal 'LAM_CUTOFF_DIAG': '<S11>:41' */
        /* Transition: '<S11>:44' */
        CntLamWatchdog = 0U;
        lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_Normal;

        /* Entry 'Normal': '<S11>:52' */
        StLamFuncDiag = ((uint8_T)VLAMRICH_NORMAL);
      } else {
        StLamFuncDiag = ((uint8_T)VLAMPOOR_NORMAL);

        /* Inport: '<Root>/LamObj' */
        /* During 'Normal': '<S11>:27' */
        /* Transition: '<S11>:31' */
        if (LamObj <= ((uint16_T)STOICHIOM_RATIO)) {
          /* Inport: '<Root>/IdxCtfFlg' */
          /* Transition: '<S11>:32' */
          if (((int32_T)IdxCtfFlg) == 0) {
            /* Transition: '<S11>:30' */
            FlgLamNotCoherent = 1U;
          } else {
            /* Transition: '<S11>:34' */
            FlgLamNotCoherent = 0U;
            CntLamWatchdog = 0U;
          }
        } else {
          /* Transition: '<S11>:33' */
          FlgLamNotCoherent = 0U;
        }
      }
    } else {
      VLambdaState = ((uint8_T)VLAMRICH);

      /* During 'VLAMRICH': '<S11>:35' */
      if (((uint32_T)VLambda) < (((uint32_T)ThrVLamR2P) << ((uint32_T)4))) {
        /* Transition: '<S11>:18' */
        /* Exit Internal 'VLAMRICH': '<S11>:35' */
        /* Exit Internal 'LAM_CUTOFF_DIAG': '<S11>:41' */
        lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_NO_ACTIVE_CHILD;
        lambdamgm_DWork.is_Lam_Trans = lambdamgm_IN_VLAMPOOR;

        /* Entry 'VLAMPOOR': '<S11>:20' */
        VLambdaState = ((uint8_T)VLAMPOOR);
        lambdam_enter_internal_VLAMPOOR();
      } else {
        /* Inport: '<Root>/LamObj' */
        /* During 'LAM_RICH_COHERENCE': '<S11>:36' */
        /* Transition: '<S11>:38' */
        if (LamObj >= ((uint16_T)STOICHIOM_RATIO)) {
          /* Transition: '<S11>:39' */
          FlgLamNotCoherent = 1U;
        } else {
          /* Transition: '<S11>:40' */
          FlgLamNotCoherent = 0U;
        }

        /* During 'LAM_CUTOFF_DIAG': '<S11>:41' */
        switch (lambdamgm_DWork.is_LAM_CUTOFF_DIAG) {
         case lambdamgm_IN_Cutoff:
          StLamFuncDiag = ((uint8_T)VLAMRICH_CUTOFF);

          /* Inport: '<Root>/IdxCtfFlg' */
          /* During 'Cutoff': '<S11>:53' */
          /* Transition: '<S11>:47' */
          if (((int32_T)IdxCtfFlg) == 0) {
            /* Transition: '<S11>:46' */
            lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_Normal;

            /* Entry 'Normal': '<S11>:52' */
            StLamFuncDiag = ((uint8_T)VLAMRICH_NORMAL);
          } else {
            /* Transition: '<S11>:48' */
            if (CntLamFunDiag > TIMCTOFFLAMRICH) {
              /* Transition: '<S11>:51' */
              PerOscLambda = ((uint16_T)MAX_LAM_PERIOD);
              lambdamgm_DWork.flgresfiltfreq = 1U;

              /* Outputs for Function Call SubSystem: '<S4>/cal_freqosclam' */
              /* Event: '<S11>:128' */
              lambdamgm_cal_freqosclam();

              /* End of Outputs for SubSystem: '<S4>/cal_freqosclam' */
              AbsCntTransLam = 1U;
              FlgLamReady = 0U;
              lambdamgm_DWork.flgCFTrigDiag = 1U;
              lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_Wait;

              /* Entry 'Wait': '<S11>:54' */
              StLamFuncDiag = ((uint8_T)VLAMRICH_WAIT);
            } else {
              /* Transition: '<S11>:50' */
              tmp = ((int32_T)CntLamFunDiag) + 1;
              if (tmp > 65535) {
                tmp = 65535;
              }

              CntLamFunDiag = (uint16_T)tmp;
            }
          }
          break;

         case lambdamgm_IN_Normal:
          StLamFuncDiag = ((uint8_T)VLAMRICH_NORMAL);

          /* Inport: '<Root>/IdxCtfFlg' */
          /* During 'Normal': '<S11>:52' */
          /* Transition: '<S11>:307' */
          if (((int32_T)IdxCtfFlg) != 0) {
            /* Transition: '<S11>:45' */
            CntLamFunDiag = 0U;
            lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_Cutoff;

            /* Entry 'Cutoff': '<S11>:53' */
            StLamFuncDiag = ((uint8_T)VLAMRICH_CUTOFF);
          } else {
            /* Transition: '<S11>:305' */
          }
          break;

         default:
          StLamFuncDiag = ((uint8_T)VLAMRICH_WAIT);

          /* Inport: '<Root>/IdxCtfFlg' */
          /* During 'Wait': '<S11>:54' */
          /* Transition: '<S11>:302' */
          if (((int32_T)IdxCtfFlg) == 0) {
            /* Transition: '<S11>:49' */
            lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_Normal;

            /* Entry 'Normal': '<S11>:52' */
            StLamFuncDiag = ((uint8_T)VLAMRICH_NORMAL);
          } else {
            /* Transition: '<S11>:303' */
          }
          break;
        }
      }
    }

    /* During 'DIAG_FREQ': '<S11>:55' */
    if (((uint32_T)lambdamgm_DWork.is_DIAG_FREQ) == lambdamgm_IN_NO_OSC) {
      /* During 'NO_OSC': '<S11>:64' */
      /* Transition: '<S11>:241' */
      guard1 = false;
      if (VLambdaState == ((uint8_T)VLAMRICH)) {
        /* Transition: '<S11>:264' */
        lambdamgm_DWork.tmpDiag = 1U;

        /* FAULT */
        lambdamgm_DWork.ptfault_lambda = LAMBDA_TOO_RICH;
        guard1 = true;
      } else {
        /* Transition: '<S11>:133' */
        if (((int32_T)FlgLowFuel) != 0) {
          /* Transition: '<S11>:134' */
          /* Transition: '<S11>:234' */
          /* Transition: '<S11>:236' */
        } else {
          /* Transition: '<S11>:265' */
          lambdamgm_DWork.tmpDiag = 1U;

          /* FAULT */
          lambdamgm_DWork.ptfault_lambda = LAMBDA_TOO_POOR;
          guard1 = true;
        }
      }

      if (guard1) {
        if (((int32_T)lambdamgm_DWork.trigLDInc) == 1) {
          /* Transition: '<S11>:266' */
          lambdamgm_DWork.FlgO2DiagOn_o2g = 1U;

          /* Outputs for Function Call SubSystem: '<S4>/Diag_Func' */
          /* S-Function (DiagMgm_SetDiagState): '<S79>/DiagMgm_SetDiagState' incorporates:
           *  Constant: '<S6>/DIAG_LAM_FUNC'
           */
          /* Event: '<S11>:205' */
          DiagMgm_SetDiagState( DIAG_LAM_FUNC, lambdamgm_DWork.ptfault_lambda,
                               &rtb_DiagMgm_SetDiagState_ftl);

          /* End of Outputs for SubSystem: '<S4>/Diag_Func' */
          lambdamgm_DWork.trigLDInc = 0U;

          /* Transition: '<S11>:293' */
        } else {
          /* Transition: '<S11>:267' */
          /* Transition: '<S11>:236' */
        }
      }

      /* Transition: '<S11>:289' */
      if (((int32_T)lambdamgm_DWork.flgresfiltfreq) == 0) {
        /* Transition: '<S11>:290' */
        FlgLamReady = 1U;
        lambdamgm_DWork.flgCFTrigDiag = 0U;
        CntLamWatchdog = 0U;
        lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_OSC;
      } else {
        /* Transition: '<S11>:246' */
      }
    } else {
      /* During 'OSC': '<S11>:65' */
      if (((int32_T)lambdamgm_DWork.flgCFTrigDiag) == 1) {
        /* Transition: '<S11>:250' */
        lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_NO_OSC;
      } else {
        /* Transition: '<S11>:69' */
        if (CntLamWatchdog > TIMNOSCLAM) {
          /* Transition: '<S11>:258' */
          if (((int32_T)FlgLamNotCoherent) == 1) {
            /* Transition: '<S11>:259' */
            PerOscLambda = ((uint16_T)MAX_LAM_PERIOD);
            lambdamgm_DWork.flgresfiltfreq = 1U;

            /* Outputs for Function Call SubSystem: '<S4>/cal_freqosclam' */
            /* Event: '<S11>:128' */
            lambdamgm_cal_freqosclam();

            /* End of Outputs for SubSystem: '<S4>/cal_freqosclam' */
            AbsCntTransLam = 1U;
            FlgLamReady = 0U;
            lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_NO_OSC;
          } else {
            /* Transition: '<S11>:257' */
          }
        } else {
          /* Inport: '<Root>/FlgClosedLoopOnOff' */
          /* Transition: '<S11>:72' */
          if (((int32_T)FlgClosedLoopOnOff) == 0) {
            /* Transition: '<S11>:263' */
          } else {
            /* Transition: '<S11>:299' */
            tmp = ((int32_T)CntLamWatchdog) + 1;
            if (tmp > 65535) {
              tmp = 65535;
            }

            CntLamWatchdog = (uint16_T)tmp;
          }

          /* End of Inport: '<Root>/FlgClosedLoopOnOff' */
        }
      }
    }
  }

  /* End of Inport: '<Root>/Rpm' */
}

/* System initialize for atomic system: '<S3>/Lambda_T5ms' */
void lambdamgm_Lambda_T5ms_Init(void)
{
  lambdamgm_DWork.is_VLambdaState = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_Lam_Trans = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_VLambdaState2 = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_Lam_Trans_hpo = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_active_c1_lambdamgm = 0U;
  lambdamgm_DWork.t_entry_poor = 0U;
  lambdamgm_DWork.tmpDiag = 0U;
  lambdamgm_DWork.trigLDDec = 0U;
  lambdamgm_DWork.trigLDInc = 0U;
  lambdamgm_DWork.flgCFTrigDiag = 0U;
  lambdamgm_DWork.oldTdcCrk = 0U;
  lambdamgm_DWork.oldTdcCrk2 = 0U;
  lambdamgm_DWork.setLam2Liveness = 0U;
  lambdamgm_DWork.setLamLiveness = 0U;
  lambdamgm_DWork.trigLD2Inc = 0U;
  VLambdaState = 0U;
  CntLamFunDiag = 0U;
  StLamFuncDiag = 0U;
  AbsCntTransLam = 0U;
  lambdamgm_DWork.ptfault_lambda = 0U;
  CntLamNoDiag = 0U;
  PerOscLambda = 0U;
  lambdamgm_DWork.flgresfiltfreq = 0U;
  FlgLamNotCoherent = 0U;
  CntLamWatchdog = 0U;
  FlgVeryRichPoor = 0U;
  lambdamgm_DWork.FlgO2DiagOn_o2g = 0U;
  FlgLamReady = 0U;
  FlgLam2Ready = 0U;
  VLambdaState2 = 0U;
  CntLamLiveness = 0U;
  CntLam2Liveness = 0U;
  FlgO22DiagOn = 0U;
  CntMinMaxLam2 = 0U;
  lambdamgm_DWork.pt_fault_l2 = 0U;
  VMaxLam2 = 0U;
  VMinLam2 = 0U;

  /* SystemInitialize for Chart: '<S4>/VLambdaState_Calc' incorporates:
   *  SubSystem: '<S4>/cal_freqosclam'
   */
  lambdamgm_cal_freqosclam_Init();

  /* SystemInitialize for Chart: '<S17>/Calc_Transitions' */
  lambdamgm_DWork.is_LAM_DIAG_OBD2 = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_LAM_DISTURBANCE = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_active_c3_lambdamgm = 0U;
  lambdamgm_DWork.cnt = 0U;
  lambdamgm_DWork.cnt2 = 0U;
  lambdamgm_DWork.flgDone = 0U;
  lambdamgm_DWork.wdt = 0U;
  lambdamgm_DWork.end = 0U;
  lambdamgm_DWork.wdtlr = 0U;
  lambdamgm_DWork.endL2R = 0U;
  lambdamgm_DWork.endR2L = 0U;
  CntOBD2LamL2RMax = 0U;
  CntOBD2LamL2RMin = 0U;
  CntOBD2LamR2LMax = 0U;
  CntOBD2LamR2LMin = 0U;
  CntOBD2LamL2R = 0U;
  CntOBD2TstL2R = 0U;
  CntOBD2LamR2L = 0U;
  CntOBD2TstR2L = 0U;
  LamObjOBD2 = 0U;
  StLamOBD2DIag = 0U;
  lambdamgm_DWork.LamOBD2End_abf = 0U;
  lambdamgm_DWork.FlgO2DiagOn_dta = 0U;
  lambdamgm_DWork.ptFault_rich = 0U;
  lambdamgm_DWork.ptFault_lean = 0U;

  /* SystemInitialize for Chart: '<S50>/Chart' */
  lambdamgm_DWork.is_active_c2_lambdamgm = 0U;
  lambdamgm_DWork.is_c2_lambdamgm = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.offCntTdcCrk_iga = 0U;
  lambdamgm_DWork.out_ns4 = 0U;

  /* SystemInitialize for Chart: '<S51>/Chart' */
  lambdamgm_DWork.is_active_c5_lambdamgm = 0U;
  lambdamgm_DWork.is_c5_lambdamgm = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.offCntTdcCrk = 0U;
  lambdamgm_DWork.out = 0U;

  /* SystemInitialize for Chart: '<S9>/Chart' */
  lambdamgm_DWork.is_DIAG_CAT = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.is_active_c4_lambdamgm = 0U;
  lambdamgm_DWork.is_c4_lambdamgm = lambdamgm_IN_NO_ACTIVE_CHILD;
  lambdamgm_DWork.oldCntLam2Liveness = 0U;
  lambdamgm_DWork.oldCntLamLiveness = 0U;
  lambdamgm_DWork.tmpCntAbsTdc = 0U;
  StCatDiag = 0U;
  FlgCatDiagOn = 0U;
  CntLam1Trs = 0U;
  CntLam2Trs = 0U;
  RatioCatDiag = 0U;
  CntLam2TrsMax = 0U;
  CntLam2TrsMin = 0U;
  CatDiagCond = 0U;
  lambdamgm_DWork.ptfault_cat = 0U;
}

/* Output and update for atomic system: '<S3>/Lambda_T5ms' */
void lambdamgm_Lambda_T5ms(void)
{
  /* local block i/o variables */
  int32_T rtb_RateLimiter_S32;
  int32_T rtb_RateLimiter_S32_dg2;
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_SteadyStateDetect_o3_kfe;
  uint16_T rtb_SteadyStateDetect_o4_bue;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o_f3m;
  uint16_T rtb_PreLookUpIdSearch_U16_o_h1p;
  uint16_T rtb_Look2D_IR_U8;
  uint16_T rtb_PreLookUpIdSearch_U16_o_onj;
  uint16_T rtb_PreLookUpIdSearch_U16_o_a22;
  uint16_T rtb_PreLookUpIdSearch_U16_o_m12;
  uint16_T rtb_PreLookUpIdSearch_U16_o_lpc;
  uint16_T rtb_Look2D_IR_U8_cwx;
  uint16_T rtb_LookUp_U8_S16;
  uint16_T rtb_LookUp_U8_S16_mvt;
  int16_T rtb_Look2D_S8_U16_S16;
  int16_T rtb_Look2D_S8_U16_S16_pof;
  uint8_T rtb_SteadyStateDetect_o2;
  uint8_T rtb_SteadyStateDetect_o2_orv;
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T rtb_DiagMgm_SetDiagState_ftl;
  uint8_T rtb_DiagMgm_SetDiagState_h05;
  uint8_T rtb_DiagMgm_SetDiagState_hip;
  uint8_T rtb_DiagMgm_SetDiagState_aih;
  uint8_T rtb_SteadyStateDetect_o1;
  uint8_T rtb_SteadyStateDetect_o1_cui;
  uint8_T rtb_DiagMgm_SetDiagState_h1f;
  uint8_T rtb_DiagMgm_SetDiagState_k3o;
  uint32_T rtb_Product;
  boolean_T rtb_LogicalOperator;
  boolean_T rtb_LogicalOperator_bcs;
  boolean_T rtb_LogicalOperator1_cyl;
  boolean_T rtb_LogicalOperator1_amu;
  boolean_T rtb_Compare;
  boolean_T rtb_Compare_lix;
  uint32_T rtb_Switch1;
  boolean_T rtb_Compare_lc0;
  int16_T rtb_Switch_eoa;
  int16_T rtb_Add_m3n;
  int16_T rtb_Add_dvn;
  int32_T rtb_DataTypeConversion;
  int32_T rtb_Switch1_flw;
  uint16_T rtb_MultiportSwitch;
  int16_T thrvlamr2p_crank;
  uint8_T rtb_Conversion4;
  uint8_T rtb_Memory_mlt;
  uint16_T rtb_Memory1_b3r;
  uint16_T rtb_Memory_a11;
  uint32_T qY;

  /* DataTypeConversion: '<S13>/Data Type Conversion1' incorporates:
   *  Inport: '<Root>/VLamM'
   *  Inport: '<Root>/VLamP'
   *  Sum: '<S13>/Add2'
   */
  rtb_DataTypeConversion = ((((int32_T)((int16_T)((int32_T)(((int32_T)VLamP) -
    ((int32_T)VLamM))))) * 625) >> ((uint32_T)3));
  if (rtb_DataTypeConversion < 0) {
    rtb_DataTypeConversion = 0;
  } else {
    if (rtb_DataTypeConversion > 65535) {
      rtb_DataTypeConversion = 65535;
    }
  }

  VLambda0 = (uint16_T)rtb_DataTypeConversion;

  /* End of DataTypeConversion: '<S13>/Data Type Conversion1' */

  /* DataTypeConversion: '<S37>/Data Type Conversion' */
  rtb_DataTypeConversion = (int32_T)VLambda0;

  /* Logic: '<S71>/Logical Operator' incorporates:
   *  Constant: '<S71>/FOVLAMRATE'
   *  Logic: '<S71>/Logical Operator1'
   *  Memory: '<S4>/Memory'
   *  Memory: '<S71>/Memory'
   */
  rtb_LogicalOperator = ((((int32_T)FOVLAMRATE) != 0) && ((((int32_T)
    lambdamgm_DWork.Memory_PreviousInput_d3k) != 0) ||
    (lambdamgm_DWork.Memory_PreviousInput_nqz)));

  /* Switch: '<S37>/Switch1' incorporates:
   *  Memory: '<S37>/Memory'
   */
  if (rtb_LogicalOperator) {
    rtb_Switch1_flw = lambdamgm_DWork.Memory_PreviousInput_lku;
  } else {
    rtb_Switch1_flw = rtb_DataTypeConversion;
  }

  /* End of Switch: '<S37>/Switch1' */

  /* S-Function (RateLimiter_S32): '<S72>/RateLimiter_S32' incorporates:
   *  Constant: '<S37>/RTVLAMMAX'
   *  Constant: '<S37>/RTVLAMMIN'
   */
  RateLimiter_S32( &rtb_RateLimiter_S32, rtb_DataTypeConversion, rtb_Switch1_flw,
                  RTVLAMMIN, RTVLAMMAX);

  /* Switch: '<S37>/Switch' incorporates:
   *  DataTypeConversion: '<S37>/Data Type Conversion1'
   */
  if (rtb_LogicalOperator) {
    VLambda = (uint16_T)rtb_RateLimiter_S32;
  } else {
    VLambda = VLambda0;
  }

  /* End of Switch: '<S37>/Switch' */

  /* MultiPortSwitch: '<S10>/Multiport Switch' incorporates:
   *  Constant: '<S10>/SELLAMMGMLOAD'
   *  Inport: '<Root>/AngThrCorr'
   *  Inport: '<Root>/AngThrottle'
   *  Inport: '<Root>/Load'
   */
  switch (SELLAMMGMLOAD) {
   case 0:
    rtb_MultiportSwitch = (uint16_T)(AngThrottle << ((uint32_T)3));
    break;

   case 1:
    rtb_MultiportSwitch = (uint16_T)(AngThrCorr << ((uint32_T)3));
    break;

   default:
    rtb_MultiportSwitch = Load;
    break;
  }

  /* End of MultiPortSwitch: '<S10>/Multiport Switch' */

  /* S-Function (PreLookUpIdSearch_U16): '<S25>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S21>/BKLAMMGMLOAD'
   *  Constant: '<S21>/BKLAMMGMLOAD_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, rtb_MultiportSwitch,
                        &BKLAMMGMLOAD[0], ((uint8_T)BKLAMMGMLOAD_dim));

  /* S-Function (PreLookUpIdSearch_U16): '<S24>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S21>/BKLAMMGMRPM'
   *  Constant: '<S21>/BKLAMMGMRPM_dim'
   *  Inport: '<Root>/Rpm'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_f3m,
                        &rtb_PreLookUpIdSearch_U16_o_h1p, Rpm, &BKLAMMGMRPM[0],
                        ((uint8_T)BKLAMMGMRPM_dim));

  /* S-Function (Look2D_IR_U8): '<S27>/Look2D_IR_U8' incorporates:
   *  Constant: '<S15>/TBTHRVLAMR2P'
   *  Constant: '<S23>/BKLAMMGMLOAD_dim'
   *  Constant: '<S23>/BKLAMMGMRPM_dim'
   */
  Look2D_IR_U8( &rtb_Look2D_IR_U8, &TBTHRVLAMR2P[0],
               rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
               ((uint8_T)BKLAMMGMLOAD_dim), rtb_PreLookUpIdSearch_U16_o_f3m,
               rtb_PreLookUpIdSearch_U16_o_h1p, ((uint8_T)BKLAMMGMRPM_dim));

  /* S-Function (Look2D_S8_U16_S16): '<S26>/Look2D_S8_U16_S16' incorporates:
   *  Constant: '<S15>/BKTHVLSTWAT'
   *  Constant: '<S15>/BKTHVLTDCCRK'
   *  Constant: '<S15>/TBOFFSVLAMCRK'
   *  Constant: '<S22>/BKTHVLSTWAT_dim'
   *  Constant: '<S22>/BKTHVLTDCCRK_dim'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/TWaterCrk'
   */
  Look2D_S8_U16_S16( &rtb_Look2D_S8_U16_S16, &TBOFFSVLAMCRK[0], CntTdcCrk,
                    &BKTHVLTDCCRK[0], ((uint8_T)BKTHVLTDCCRK_dim), TWaterCrk,
                    &BKTHVLSTWAT[0], ((uint8_T)BKTHVLSTWAT_dim));

  /* DataTypeConversion: '<S22>/Data Type Conversion' */
  thrvlamr2p_crank = (int16_T)(rtb_Look2D_S8_U16_S16 >> ((uint32_T)6));

  /* Sum: '<S5>/Add1' incorporates:
   *  DataTypeConversion: '<S23>/Data Type Conversion'
   */
  ThrVLamR2P = (uint16_T)((int32_T)(((int32_T)((uint32_T)(((uint32_T)
    rtb_Look2D_IR_U8) >> ((uint32_T)6)))) + ((int32_T)thrvlamr2p_crank)));

  /* Sum: '<S5>/Add' incorporates:
   *  Constant: '<S5>/THRVLAMHYST'
   */
  ThrVLamP2R = (uint16_T)(((uint32_T)THRVLAMHYST) + ((uint32_T)ThrVLamR2P));

  /* S-Function (PreLookUpIdSearch_U16): '<S32>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S28>/BKLAMMGMLOAD'
   *  Constant: '<S28>/BKLAMMGMLOAD_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_onj,
                        &rtb_PreLookUpIdSearch_U16_o_a22, rtb_MultiportSwitch,
                        &BKLAMMGMLOAD[0], ((uint8_T)BKLAMMGMLOAD_dim));

  /* S-Function (PreLookUpIdSearch_U16): '<S31>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S28>/BKLAMMGMRPM'
   *  Constant: '<S28>/BKLAMMGMRPM_dim'
   *  Inport: '<Root>/Rpm'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_m12,
                        &rtb_PreLookUpIdSearch_U16_o_lpc, Rpm, &BKLAMMGMRPM[0],
                        ((uint8_T)BKLAMMGMRPM_dim));

  /* S-Function (Look2D_IR_U8): '<S34>/Look2D_IR_U8' incorporates:
   *  Constant: '<S16>/TBTHRVLAM2R2P'
   *  Constant: '<S30>/BKLAMMGMLOAD_dim'
   *  Constant: '<S30>/BKLAMMGMRPM_dim'
   */
  Look2D_IR_U8( &rtb_Look2D_IR_U8_cwx, &TBTHRVLAM2R2P[0],
               rtb_PreLookUpIdSearch_U16_o_onj, rtb_PreLookUpIdSearch_U16_o_a22,
               ((uint8_T)BKLAMMGMLOAD_dim), rtb_PreLookUpIdSearch_U16_o_m12,
               rtb_PreLookUpIdSearch_U16_o_lpc, ((uint8_T)BKLAMMGMRPM_dim));

  /* S-Function (Look2D_S8_U16_S16): '<S33>/Look2D_S8_U16_S16' incorporates:
   *  Constant: '<S16>/BKTHVLSTWAT'
   *  Constant: '<S16>/BKTHVLTDCCRK'
   *  Constant: '<S16>/TBOFFSVLAM2CRK'
   *  Constant: '<S29>/BKTHVLSTWAT_dim'
   *  Constant: '<S29>/BKTHVLTDCCRK_dim'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/TWaterCrk'
   */
  Look2D_S8_U16_S16( &rtb_Look2D_S8_U16_S16_pof, &TBOFFSVLAM2CRK[0], CntTdcCrk,
                    &BKTHVLTDCCRK[0], ((uint8_T)BKTHVLTDCCRK_dim), TWaterCrk,
                    &BKTHVLSTWAT[0], ((uint8_T)BKTHVLSTWAT_dim));

  /* Sum: '<S5>/Add3' incorporates:
   *  DataTypeConversion: '<S29>/Data Type Conversion'
   *  DataTypeConversion: '<S30>/Data Type Conversion'
   */
  ThrVLam2R2P = (uint16_T)((int32_T)(((int32_T)((uint32_T)(((uint32_T)
    rtb_Look2D_IR_U8_cwx) >> ((uint32_T)6)))) + (((int32_T)
    rtb_Look2D_S8_U16_S16_pof) >> ((uint32_T)6))));

  /* Sum: '<S5>/Add4' incorporates:
   *  Constant: '<S5>/THRVLAM2HYST'
   */
  ThrVLam2P2R = (uint16_T)(((uint32_T)ThrVLam2R2P) + ((uint32_T)THRVLAM2HYST));

  /* DataTypeConversion: '<S14>/Data Type Conversion1' incorporates:
   *  Inport: '<Root>/VLam2M'
   *  Inport: '<Root>/VLam2P'
   *  Sum: '<S14>/Add2'
   */
  rtb_DataTypeConversion = ((((int32_T)((int16_T)((int32_T)(((int32_T)VLam2P) -
    ((int32_T)VLam2M))))) * 625) >> ((uint32_T)3));
  if (rtb_DataTypeConversion < 0) {
    rtb_DataTypeConversion = 0;
  } else {
    if (rtb_DataTypeConversion > 65535) {
      rtb_DataTypeConversion = 65535;
    }
  }

  VLambda20 = (uint16_T)rtb_DataTypeConversion;

  /* End of DataTypeConversion: '<S14>/Data Type Conversion1' */

  /* DataTypeConversion: '<S18>/Data Type Conversion' */
  rtb_DataTypeConversion = (int32_T)VLambda20;

  /* Logic: '<S76>/Logical Operator' incorporates:
   *  Constant: '<S76>/FOVLAM2RATE'
   *  Logic: '<S76>/Logical Operator1'
   *  Memory: '<S4>/Memory1'
   *  Memory: '<S76>/Memory'
   */
  rtb_LogicalOperator_bcs = ((((int32_T)FOVLAM2RATE) != 0) && ((((int32_T)
    lambdamgm_DWork.Memory1_PreviousInput_jee) != 0) ||
    (lambdamgm_DWork.Memory_PreviousInput_dy5)));

  /* Switch: '<S18>/Switch1' incorporates:
   *  Memory: '<S18>/Memory'
   */
  if (rtb_LogicalOperator_bcs) {
    rtb_Switch1_flw = lambdamgm_DWork.Memory_PreviousInput_gja;
  } else {
    rtb_Switch1_flw = rtb_DataTypeConversion;
  }

  /* End of Switch: '<S18>/Switch1' */

  /* S-Function (RateLimiter_S32): '<S77>/RateLimiter_S32' incorporates:
   *  Constant: '<S18>/RTVLAM2MAX'
   *  Constant: '<S18>/RTVLAM2MIN'
   */
  RateLimiter_S32( &rtb_RateLimiter_S32_dg2, rtb_DataTypeConversion,
                  rtb_Switch1_flw, RTVLAM2MIN, RTVLAM2MAX);

  /* Switch: '<S18>/Switch' incorporates:
   *  DataTypeConversion: '<S18>/Data Type Conversion1'
   */
  if (rtb_LogicalOperator_bcs) {
    VLambda2 = (uint16_T)rtb_RateLimiter_S32_dg2;
  } else {
    VLambda2 = VLambda20;
  }

  /* End of Switch: '<S18>/Switch' */

  /* DataTypeConversion: '<S68>/Conversion4' incorporates:
   *  Constant: '<S67>/Constant'
   *  Inport: '<Root>/Rpm'
   *  RelationalOperator: '<S67>/Compare'
   */
  rtb_Conversion4 = (uint8_T)((((int32_T)Rpm) == 0) ? 1 : 0);

  /* Memory: '<S56>/Memory' */
  rtb_Memory_mlt = lambdamgm_DWork.Memory_PreviousInput_mhv;

  /* Memory: '<S68>/Memory1' */
  rtb_Memory1_b3r = lambdamgm_DWork.Memory1_PreviousInput_nvw;

  /* Memory: '<S68>/Memory' */
  rtb_Memory_a11 = lambdamgm_DWork.Memory_PreviousInput_bxf;

  /* S-Function (SteadyStateDetect): '<S68>/SteadyStateDetect' incorporates:
   *  Constant: '<S56>/THRLAMOBD2RPMSTAB'
   *  Constant: '<S56>/TIMLAMOBD2RPMSTAB'
   *  Inport: '<Root>/Rpm'
   */
  SteadyStateDetect( &rtb_SteadyStateDetect_o1, &rtb_SteadyStateDetect_o2,
                    &rtb_SteadyStateDetect_o3, &rtb_SteadyStateDetect_o4, Rpm,
                    rtb_Conversion4, THRLAMOBD2RPMSTAB, TIMLAMOBD2RPMSTAB,
                    rtb_Memory_mlt, rtb_Memory1_b3r, rtb_Memory_a11);

  /* Logic: '<S55>/Logical Operator1' incorporates:
   *  Memory: '<S55>/Memory1'
   */
  rtb_LogicalOperator1_cyl = ((lambdamgm_DWork.Memory1_PreviousInput_pf2) &&
    (((int32_T)rtb_SteadyStateDetect_o1) != 0));

  /* DataTypeConversion: '<S64>/Conversion4' incorporates:
   *  Constant: '<S63>/Constant'
   *  Inport: '<Root>/Rpm'
   *  RelationalOperator: '<S63>/Compare'
   */
  rtb_Conversion4 = (uint8_T)((((int32_T)Rpm) == 0) ? 1 : 0);

  /* Memory: '<S54>/Memory' */
  rtb_Memory_mlt = lambdamgm_DWork.Memory_PreviousInput_cqm;

  /* Memory: '<S64>/Memory1' */
  rtb_Memory1_b3r = lambdamgm_DWork.Memory1_PreviousInput_mg1;

  /* Memory: '<S64>/Memory' */
  rtb_Memory_a11 = lambdamgm_DWork.Memory_PreviousInput_gnc;

  /* S-Function (SteadyStateDetect): '<S64>/SteadyStateDetect' incorporates:
   *  Constant: '<S54>/THRLAMOBD2LOADSTAB'
   *  Constant: '<S54>/TIMLAMOBD2LOADSTAB'
   */
  SteadyStateDetect( &rtb_SteadyStateDetect_o1_cui,
                    &rtb_SteadyStateDetect_o2_orv, &rtb_SteadyStateDetect_o3_kfe,
                    &rtb_SteadyStateDetect_o4_bue, rtb_MultiportSwitch,
                    rtb_Conversion4, THRLAMOBD2LOADSTAB, TIMLAMOBD2LOADSTAB,
                    rtb_Memory_mlt, rtb_Memory1_b3r, rtb_Memory_a11);

  /* Logic: '<S53>/Logical Operator1' incorporates:
   *  Memory: '<S53>/Memory1'
   */
  rtb_LogicalOperator1_amu = ((lambdamgm_DWork.Memory1_PreviousInput_pwi) &&
    (((int32_T)rtb_SteadyStateDetect_o1_cui) != 0));

  /* Switch: '<S57>/Switch' incorporates:
   *  Constant: '<S57>/Constant'
   *  Constant: '<S57>/LAMOBD2TWHYS'
   *  Memory: '<S57>/Memory'
   */
  if (lambdamgm_DWork.Memory_PreviousInput_idy) {
    rtb_Switch_eoa = LAMOBD2TWHYS;
  } else {
    rtb_Switch_eoa = 0;
  }

  /* End of Switch: '<S57>/Switch' */

  /* Sum: '<S57>/Add' incorporates:
   *  Constant: '<S57>/LAMOBD2TWMAX'
   */
  rtb_Add_m3n = (int16_T)(LAMOBD2TWMAX + rtb_Switch_eoa);

  /* Switch: '<S70>/Switch' incorporates:
   *  Inport: '<Root>/TWater'
   *  RelationalOperator: '<S70>/u_GTE_up'
   */
  if (TWater >= rtb_Add_m3n) {
  } else {
    /* Sum: '<S57>/Add1' incorporates:
     *  Constant: '<S57>/LAMOBD2TWMIN'
     */
    rtb_Switch_eoa = (int16_T)(LAMOBD2TWMIN - rtb_Switch_eoa);

    /* Switch: '<S70>/Switch1' incorporates:
     *  RelationalOperator: '<S70>/u_GT_lo'
     */
    if (TWater > rtb_Switch_eoa) {
      rtb_Add_m3n = TWater;
    } else {
      rtb_Add_m3n = rtb_Switch_eoa;
    }

    /* End of Switch: '<S70>/Switch1' */
  }

  /* End of Switch: '<S70>/Switch' */

  /* RelationalOperator: '<S69>/Compare' incorporates:
   *  Inport: '<Root>/TWater'
   *  Sum: '<S70>/Diff'
   */
  rtb_Compare = (((int16_T)(TWater - rtb_Add_m3n)) == 0);

  /* RelationalOperator: '<S48>/Compare' incorporates:
   *  Constant: '<S48>/Constant'
   *  Inport: '<Root>/FlgLowFuel'
   */
  rtb_Compare_lix = (((int32_T)FlgLowFuel) == 0);

  /* Switch: '<S14>/Switch' incorporates:
   *  Constant: '<S14>/CC_TO_VBAT'
   *  Constant: '<S14>/LAM_HEAT_D'
   *  Constant: '<S14>/NO_PT_FAULT'
   *  Constant: '<S14>/VLAM2MAX'
   *  Constant: '<S14>/VLAM2OCMIN'
   *  Inport: '<Root>/StLamHeater2'
   *  Inport: '<Root>/VLam2M'
   *  Inport: '<Root>/VLam2P'
   *  Logic: '<S14>/Logical Operator'
   *  Logic: '<S14>/Logical Operator1'
   *  Logic: '<S14>/Logical Operator2'
   *  RelationalOperator: '<S14>/Relational Operator'
   *  RelationalOperator: '<S14>/Relational Operator1'
   *  RelationalOperator: '<S14>/Relational Operator2'
   *  RelationalOperator: '<S14>/Relational Operator3'
   *  RelationalOperator: '<S14>/Relational Operator4'
   *  Switch: '<S14>/Switch1'
   */
  if ((VLam2P > VLAM2MAX) || (VLam2M > VLAM2MAX)) {
    rtb_Conversion4 = CC_TO_VBAT;
  } else if (((VLam2P > VLAM2OCMIN) || (VLam2M > VLAM2OCMIN)) && (StLamHeater2 ==
              LAM_HEAT_D)) {
    /* Switch: '<S14>/Switch1' incorporates:
     *  Constant: '<S14>/OPEN_CIRCUIT'
     */
    rtb_Conversion4 = OPEN_CIRCUIT;
  } else {
    rtb_Conversion4 = NO_PT_FAULT;
  }

  /* End of Switch: '<S14>/Switch' */

  /* S-Function (DiagMgm_SetDiagState): '<S20>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S14>/DIAG_LAM2_EL'
   */
  DiagMgm_SetDiagState( DIAG_LAM2_EL, rtb_Conversion4,
                       &rtb_DiagMgm_SetDiagState_h1f);

  /* Chart: '<S51>/Chart' incorporates:
   *  Inport: '<Root>/CntTdcCrk'
   *  Memory: '<S4>/Memory1'
   */
  /* Gateway: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc1/Chart */
  /* During: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc1/Chart */
  if (((uint32_T)lambdamgm_DWork.is_active_c5_lambdamgm) == 0U) {
    /* Entry: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc1/Chart */
    lambdamgm_DWork.is_active_c5_lambdamgm = 1U;

    /* Entry Internal: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc1/Chart */
    /* Transition: '<S59>:2' */
    lambdamgm_DWork.is_c5_lambdamgm = lambdamgm_IN_STOP;
  } else if (((uint32_T)lambdamgm_DWork.is_c5_lambdamgm) == lambdamgm_IN_GO) {
    /* During 'GO': '<S59>:3' */
    /* Transition: '<S59>:7' */
    if (((int32_T)CntTdcCrk) == 0) {
      /* Transition: '<S59>:8' */
      lambdamgm_DWork.is_c5_lambdamgm = lambdamgm_IN_STOP;
    } else {
      /* Transition: '<S59>:11' */
      rtb_DataTypeConversion = ((int32_T)CntTdcCrk) - ((int32_T)
        lambdamgm_DWork.offCntTdcCrk);
      if (rtb_DataTypeConversion < 0) {
        rtb_DataTypeConversion = 0;
      }

      lambdamgm_DWork.out = (uint16_T)rtb_DataTypeConversion;
    }
  } else {
    /* During 'STOP': '<S59>:1' */
    /* Transition: '<S59>:9' */
    if (((int32_T)lambdamgm_DWork.Memory1_PreviousInput_jee) != 0) {
      /* Transition: '<S59>:5' */
      lambdamgm_DWork.offCntTdcCrk = CntTdcCrk;
      lambdamgm_DWork.is_c5_lambdamgm = lambdamgm_IN_GO;
    } else {
      /* Transition: '<S59>:10' */
    }
  }

  /* End of Chart: '<S51>/Chart' */

  /* Logic: '<S36>/Logical Operator1' incorporates:
   *  Constant: '<S44>/Constant'
   *  Constant: '<S45>/Constant'
   *  Constant: '<S46>/Constant'
   *  Constant: '<S47>/Constant'
   *  Inport: '<Root>/StLamHeater2'
   *  Logic: '<S36>/Logical Operator'
   *  Memory: '<S4>/Memory1'
   *  RelationalOperator: '<S44>/Compare'
   *  RelationalOperator: '<S45>/Compare'
   *  RelationalOperator: '<S46>/Compare'
   *  RelationalOperator: '<S47>/Compare'
   */
  rtb_Compare_lc0 = ((rtb_LogicalOperator1_cyl && rtb_LogicalOperator1_amu) &&
                     rtb_Compare);
  Lam2DiagCond = (uint8_T)((((((rtb_Compare_lc0 && (StLamHeater2 == LAM_HEAT_D))
    && rtb_Compare_lix) && (rtb_DiagMgm_SetDiagState_h1f != FAULT)) &&
    (lambdamgm_DWork.out > LAMOBD2TDCCRK)) && (((int32_T)
    lambdamgm_DWork.Memory1_PreviousInput_jee) != 0)) ? 1 : 0);

  /* Chart: '<S4>/VLambdaState_Calc' incorporates:
   *  DataTypeConversion: '<S8>/Data Type Conversion'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/DrivingCycle'
   *  Inport: '<Root>/FlgClosedLoopOnOff'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/StLamHeater'
   *  Inport: '<Root>/StLamHeater2'
   */
  /* Gateway: lambdamgm/T5ms/Lambda_T5ms/VLambdaState_Calc */
  /* During: lambdamgm/T5ms/Lambda_T5ms/VLambdaState_Calc */
  if (((uint32_T)lambdamgm_DWork.is_active_c1_lambdamgm) == 0U) {
    /* Entry: lambdamgm/T5ms/Lambda_T5ms/VLambdaState_Calc */
    lambdamgm_DWork.is_active_c1_lambdamgm = 1U;

    /* Entry Internal: lambdamgm/T5ms/Lambda_T5ms/VLambdaState_Calc */
    /* Entry Internal 'VLambdaState': '<S11>:1' */
    /* Transition: '<S11>:2' */
    PerOscLambda = ((uint16_T)MAX_LAM_PERIOD);
    lambdamgm_DWork.flgresfiltfreq = 1U;
    lambdamgm_DWork.tmpDiag = 0U;
    lambdamgm_DWork.trigLDInc = 1U;
    lambdamgm_DWork.trigLDDec = 0U;
    lambdamgm_DWork.flgCFTrigDiag = 0U;
    FlgLamNotCoherent = 0U;
    lambdamgm_DWork.is_VLambdaState = lambdamgm_IN_VLAMINIT;

    /* Entry 'VLAMINIT': '<S11>:8' */
    VLambdaState = ((uint8_T)VLAMINIT);
    StLamFuncDiag = ((uint8_T)VLAMPOOR_INIT);
    CntLamFunDiag = 0U;
    CntLamWatchdog = 0U;
    FlgLamReady = 0U;
    lambdamgm_DWork.oldTdcCrk = CntTdcCrk;
    lambdamgm_DWork.setLamLiveness = 0U;
    CntLamLiveness = 0U;

    /* Entry Internal 'VLambdaState2': '<S11>:316' */
    /* Transition: '<S11>:319' */
    lambdamgm_DWork.trigLD2Inc = 1U;
    lambdamgm_DWork.is_VLambdaState2 = lambdamgm_IN_VLAM2INIT;

    /* Entry 'VLAM2INIT': '<S11>:323' */
    VLambdaState2 = ((uint8_T)VLAMINIT);
    CntLam2Liveness = 0U;
    FlgLam2Ready = 0U;
    lambdamgm_DWork.setLam2Liveness = 0U;
    lambdamgm_DWork.oldTdcCrk2 = CntTdcCrk;
  } else {
    /* During 'VLambdaState': '<S11>:1' */
    if (((uint32_T)lambdamgm_DWork.is_VLambdaState) == lambdamgm_IN_VLAMINIT) {
      VLambdaState = ((uint8_T)VLAMINIT);
      StLamFuncDiag = ((uint8_T)VLAMPOOR_INIT);

      /* Outputs for Function Call SubSystem: '<S4>/Interp_lready' */
      /* S-Function (LookUp_U8_S16): '<S82>/LookUp_U8_S16' incorporates:
       *  Constant: '<S8>/BKTHVLSTWAT'
       *  Constant: '<S8>/BKTHVLSTWAT_dim'
       *  Constant: '<S8>/VTTDCLAMBDAREADY'
       *  Inport: '<Root>/TWaterCrk'
       */
      /* During 'VLAMINIT': '<S11>:8' */
      /* Transition: '<S11>:11' */
      /* Event: '<S11>:129' */
      LookUp_U8_S16( &rtb_LookUp_U8_S16, &VTTDCLAMBDAREADY[0], TWaterCrk,
                    &BKTHVLSTWAT[0], ((uint8_T)BKTHVLSTWAT_dim));

      /* S-Function (LookUp_U8_S16): '<S81>/LookUp_U8_S16' incorporates:
       *  Constant: '<S8>/BKTHVLSTWAT'
       *  Constant: '<S8>/BKTHVLSTWAT_dim'
       *  Constant: '<S8>/VTTDCLAMBDAREADY2'
       *  Inport: '<Root>/TWaterCrk'
       */
      LookUp_U8_S16( &rtb_LookUp_U8_S16_mvt, &VTTDCLAMBDAREADY2[0], TWaterCrk,
                    &BKTHVLSTWAT[0], ((uint8_T)BKTHVLSTWAT_dim));

      /* DataTypeConversion: '<S8>/Data Type Conversion1' */
      lambdamgm_DWork.DataTypeConversion1 = (uint16_T)(((uint32_T)
        rtb_LookUp_U8_S16_mvt) >> ((uint32_T)4));

      /* End of Outputs for SubSystem: '<S4>/Interp_lready' */
      if (((uint32_T)VLambda) > (((uint32_T)ThrVLamP2R) << ((uint32_T)4))) {
        /* Transition: '<S11>:5' */
        FlgLamReady = 1U;
        AbsCntTransLam = 1U;
        lambdamgm_DWork.is_VLambdaState = lambdamgm_IN_VLAMRDY;
        lambdamgm_DWork.is_Lam_Trans = lambdamgm_IN_VLAMRICH;

        /* Entry 'VLAMRICH': '<S11>:35' */
        VLambdaState = ((uint8_T)VLAMRICH);
        lambdamgm_DWork.setLamLiveness = 1U;

        /* Entry Internal 'VLAMRICH': '<S11>:35' */
        /* Entry Internal 'LAM_CUTOFF_DIAG': '<S11>:41' */
        /* Transition: '<S11>:44' */
        CntLamWatchdog = 0U;
        lambdamgm_DWork.is_LAM_CUTOFF_DIAG = lambdamgm_IN_Normal;

        /* Entry 'Normal': '<S11>:52' */
        StLamFuncDiag = ((uint8_T)VLAMRICH_NORMAL);

        /* Entry Internal 'DIAG_FREQ': '<S11>:55' */
        /* Transition: '<S11>:58' */
        lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_OSC;
      } else {
        /* Transition: '<S11>:12' */
        rtb_DataTypeConversion = ((int32_T)CntLamFunDiag) + 1;
        if (rtb_DataTypeConversion > 65535) {
          rtb_DataTypeConversion = 65535;
        }

        CntLamFunDiag = (uint16_T)rtb_DataTypeConversion;

        /* Outputs for Function Call SubSystem: '<S4>/Interp_lready' */
        if (((((int32_T)CntTdcCrk) - ((int32_T)lambdamgm_DWork.oldTdcCrk)) >
             ((int32_T)((uint32_T)(((uint32_T)rtb_LookUp_U8_S16) >> ((uint32_T)4)))))
            || (StLamHeater == LAM_HEAT_D)) {
          /* Transition: '<S11>:7' */
          FlgLamReady = 1U;
          AbsCntTransLam = 0U;
          lambdamgm_DWork.is_VLambdaState = lambdamgm_IN_VLAMRDY;
          lambdamgm_DWork.is_Lam_Trans = lambdamgm_IN_VLAMPOOR;

          /* Entry 'VLAMPOOR': '<S11>:20' */
          VLambdaState = ((uint8_T)VLAMPOOR);
          lambdam_enter_internal_VLAMPOOR();

          /* Entry Internal 'DIAG_FREQ': '<S11>:55' */
          /* Transition: '<S11>:58' */
          lambdamgm_DWork.is_DIAG_FREQ = lambdamgm_IN_OSC;
        } else {
          /* Transition: '<S11>:13' */
        }

        /* End of Outputs for SubSystem: '<S4>/Interp_lready' */
      }
    } else {
      lambdamgm_VLAMRDY();
    }

    /* During 'VLambdaState2': '<S11>:316' */
    if (((uint32_T)lambdamgm_DWork.is_VLambdaState2) == lambdamgm_IN_VLAM2INIT)
    {
      VLambdaState2 = ((uint8_T)VLAMINIT);

      /* During 'VLAM2INIT': '<S11>:323' */
      /* Transition: '<S11>:326' */
      if (((uint32_T)VLambda2) > (((uint32_T)ThrVLam2P2R) << ((uint32_T)4))) {
        /* Transition: '<S11>:321' */
        FlgLam2Ready = 1U;
        VMinLam2 = ((uint16_T)MAX_LAM_V);
        VMaxLam2 = 0U;
        CntMinMaxLam2 = 0U;
        lambdamgm_DWork.is_VLambdaState2 = lambdamgm_IN_VLAM2RDY;
        lambdamgm_DWork.is_Lam_Trans_hpo = lambdamgm_IN_VLAM2RICH;

        /* Entry 'VLAM2RICH': '<S11>:335' */
        VLambdaState2 = ((uint8_T)VLAMRICH);

        /* Entry Internal 'VLAM2RICH': '<S11>:335' */
        /* Transition: '<S11>:423' */
        /* Transition: '<S11>:425' */
        lambdamgm_DWork.setLam2Liveness = 1U;
      } else {
        /* Transition: '<S11>:327' */
        if (((CntTdcCrk - lambdamgm_DWork.oldTdcCrk2) >
             lambdamgm_DWork.DataTypeConversion1) || (StLamHeater2 == LAM_HEAT_D))
        {
          /* Transition: '<S11>:322' */
          FlgLam2Ready = 1U;
          VMinLam2 = ((uint16_T)MAX_LAM_V);
          VMaxLam2 = 0U;
          CntMinMaxLam2 = 0U;
          lambdamgm_DWork.is_VLambdaState2 = lambdamgm_IN_VLAM2RDY;
          lambdamgm_DWork.is_Lam_Trans_hpo = lambdamgm_IN_VLAM2POOR;

          /* Entry 'VLAM2POOR': '<S11>:361' */
          VLambdaState2 = ((uint8_T)VLAMPOOR);

          /* Entry Internal 'VLAM2POOR': '<S11>:361' */
          /* Transition: '<S11>:366' */
          if (((int32_T)lambdamgm_DWork.setLam2Liveness) != 0) {
            /* Transition: '<S11>:368' */
            lambdamgm_DWork.setLam2Liveness = 0U;
            rtb_DataTypeConversion = ((int32_T)CntLam2Liveness) + 1;
            if (rtb_DataTypeConversion > 255) {
              rtb_DataTypeConversion = 255;
            }

            CntLam2Liveness = (uint8_T)rtb_DataTypeConversion;
            if (((int32_T)CntLam2Liveness) > 250) {
              /* Transition: '<S11>:371' */
              CntLam2Liveness = 0U;
            } else {
              /* Transition: '<S11>:369' */
            }
          } else {
            /* Transition: '<S11>:367' */
          }
        } else {
          /* Transition: '<S11>:328' */
        }
      }
    } else {
      /* During 'VLAM2RDY': '<S11>:329' */
      if (((int32_T)Rpm) == 0) {
        /* Transition: '<S11>:320' */
        /* Exit Internal 'VLAM2RDY': '<S11>:329' */
        /* Exit Internal 'Lam_Trans': '<S11>:332' */
        lambdamgm_DWork.is_Lam_Trans_hpo = lambdamgm_IN_NO_ACTIVE_CHILD;
        lambdamgm_DWork.is_VLambdaState2 = lambdamgm_IN_VLAM2INIT;

        /* Entry 'VLAM2INIT': '<S11>:323' */
        VLambdaState2 = ((uint8_T)VLAMINIT);
        CntLam2Liveness = 0U;
        FlgLam2Ready = 0U;
        lambdamgm_DWork.setLam2Liveness = 0U;
        lambdamgm_DWork.oldTdcCrk2 = CntTdcCrk;
      } else {
        /* During 'Lam_Trans': '<S11>:332' */
        if (((uint32_T)lambdamgm_DWork.is_Lam_Trans_hpo) ==
            lambdamgm_IN_VLAM2POOR) {
          VLambdaState2 = ((uint8_T)VLAMPOOR);

          /* During 'VLAM2POOR': '<S11>:361' */
          if (((uint32_T)VLambda2) > (((uint32_T)ThrVLam2P2R) << ((uint32_T)4)))
          {
            /* Transition: '<S11>:334' */
            lambdamgm_DWork.is_Lam_Trans_hpo = lambdamgm_IN_VLAM2RICH;

            /* Entry 'VLAM2RICH': '<S11>:335' */
            VLambdaState2 = ((uint8_T)VLAMRICH);

            /* Entry Internal 'VLAM2RICH': '<S11>:335' */
            /* Transition: '<S11>:423' */
            /* Transition: '<S11>:425' */
            lambdamgm_DWork.setLam2Liveness = 1U;
          }
        } else {
          VLambdaState2 = ((uint8_T)VLAMRICH);

          /* During 'VLAM2RICH': '<S11>:335' */
          if (((uint32_T)VLambda2) < (((uint32_T)ThrVLam2R2P) << ((uint32_T)4)))
          {
            /* Transition: '<S11>:333' */
            lambdamgm_DWork.is_Lam_Trans_hpo = lambdamgm_IN_VLAM2POOR;

            /* Entry 'VLAM2POOR': '<S11>:361' */
            VLambdaState2 = ((uint8_T)VLAMPOOR);

            /* Entry Internal 'VLAM2POOR': '<S11>:361' */
            /* Transition: '<S11>:366' */
            if (((int32_T)lambdamgm_DWork.setLam2Liveness) != 0) {
              /* Transition: '<S11>:368' */
              lambdamgm_DWork.setLam2Liveness = 0U;
              rtb_DataTypeConversion = ((int32_T)CntLam2Liveness) + 1;
              if (rtb_DataTypeConversion > 255) {
                rtb_DataTypeConversion = 255;
              }

              CntLam2Liveness = (uint8_T)rtb_DataTypeConversion;
              if (((int32_T)CntLam2Liveness) > 250) {
                /* Transition: '<S11>:371' */
                CntLam2Liveness = 0U;
              } else {
                /* Transition: '<S11>:369' */
              }
            } else {
              /* Transition: '<S11>:367' */
            }
          }
        }

        /* During 'LAM2_FUNC': '<S11>:439' */
        /* Transition: '<S11>:441' */
        if (((int32_T)Lam2DiagCond) != 0) {
          /* Transition: '<S11>:469' */
          if (VMinLam2 < VLambda2) {
          } else {
            VMinLam2 = VLambda2;
          }

          if (VMaxLam2 > VLambda2) {
          } else {
            VMaxLam2 = VLambda2;
          }

          if ((CntMinMaxLam2 >= TIMLAM2MINMAX) && (((int32_T)
                lambdamgm_DWork.trigLD2Inc) == 1)) {
            /* Transition: '<S11>:442' */
            if ((VMaxLam2 - VMinLam2) > THRLAM2MINMAX) {
              /* Transition: '<S11>:445' */
              FlgO22DiagOn = 1U;
              lambdamgm_DWork.pt_fault_l2 = NO_PT_FAULT;

              /* Outputs for Function Call SubSystem: '<S4>/Diag_Func2' */
              /* S-Function (DiagMgm_SetDiagState): '<S80>/DiagMgm_SetDiagState' incorporates:
               *  Constant: '<S7>/DIAG_LAM_FUNC_2'
               */
              /* Event: '<S11>:467' */
              DiagMgm_SetDiagState( DIAG_LAM_FUNC_2, lambdamgm_DWork.pt_fault_l2,
                                   &rtb_DiagMgm_SetDiagState);

              /* End of Outputs for SubSystem: '<S4>/Diag_Func2' */
              /* Transition: '<S11>:453' */
            } else {
              /* Transition: '<S11>:446' */
              FlgO22DiagOn = 1U;
              lambdamgm_DWork.pt_fault_l2 = SIGNAL_STUCK;

              /* Outputs for Function Call SubSystem: '<S4>/Diag_Func2' */
              /* S-Function (DiagMgm_SetDiagState): '<S80>/DiagMgm_SetDiagState' incorporates:
               *  Constant: '<S7>/DIAG_LAM_FUNC_2'
               */
              /* Event: '<S11>:467' */
              DiagMgm_SetDiagState( DIAG_LAM_FUNC_2, lambdamgm_DWork.pt_fault_l2,
                                   &rtb_DiagMgm_SetDiagState);

              /* End of Outputs for SubSystem: '<S4>/Diag_Func2' */
              lambdamgm_DWork.trigLD2Inc = 0U;

              /* Transition: '<S11>:452' */
            }

            /* Transition: '<S11>:454' */
            CntMinMaxLam2 = 0U;
          } else {
            /* Transition: '<S11>:443' */
            rtb_DataTypeConversion = ((int32_T)CntMinMaxLam2) + 1;
            if (rtb_DataTypeConversion > 65535) {
              rtb_DataTypeConversion = 65535;
            }

            CntMinMaxLam2 = (uint16_T)rtb_DataTypeConversion;
          }
        } else {
          /* Transition: '<S11>:470' */
        }
      }
    }

    /* During 'FlgVeryRichPoor': '<S11>:75' */
    /* Transition: '<S11>:152' */
    if ((thrvlamr2p_crank == 0) && (((VLambdaState == ((uint8_T)VLAMRICH)) &&
          (((uint32_T)VLambda) > (((uint32_T)THRVLAMVERYRICH) << ((uint32_T)4))))
         || ((VLambdaState == ((uint8_T)VLAMPOOR)) && (((uint32_T)VLambda) <
           (((uint32_T)THRVLAMVERYPOOR) << ((uint32_T)4)))))) {
      /* Transition: '<S11>:78' */
      FlgVeryRichPoor = 1U;
    } else {
      /* Transition: '<S11>:77' */
      FlgVeryRichPoor = 0U;
    }

    /* During 'LamCycle': '<S11>:151' */
    /* Transition: '<S11>:155' */
    if (((int32_T)CntLamNoDiag) >= ((int32_T)((uint32_T)(((uint32_T)TIMNOSCLAM) >>
           ((uint32_T)1))))) {
      /* Transition: '<S11>:153' */
      CntLamNoDiag = 0U;
      lambdamgm_DWork.trigLDDec = 1U;
    } else {
      /* Transition: '<S11>:195' */
      rtb_DataTypeConversion = ((int32_T)CntLamNoDiag) + ((int32_T)(((((int32_T)
        FlgLamReady) != 0) && (((int32_T)FlgClosedLoopOnOff) != 0)) ? 1 : 0));
      if (rtb_DataTypeConversion > 65535) {
        rtb_DataTypeConversion = 65535;
      }

      CntLamNoDiag = (uint16_T)rtb_DataTypeConversion;
    }

    if ((((int32_T)lambdamgm_DWork.tmpDiag) == 2) && (((int32_T)
          lambdamgm_DWork.trigLDDec) == 1)) {
      /* Transition: '<S11>:167' */
      lambdamgm_DWork.trigLDDec = 0U;
      lambdamgm_DWork.tmpDiag = 0U;
      lambdamgm_DWork.ptfault_lambda = NO_PT_FAULT;

      /* Outputs for Function Call SubSystem: '<S4>/Diag_Func' */
      /* S-Function (DiagMgm_SetDiagState): '<S79>/DiagMgm_SetDiagState' incorporates:
       *  Constant: '<S6>/DIAG_LAM_FUNC'
       */
      /* Event: '<S11>:205' */
      DiagMgm_SetDiagState( DIAG_LAM_FUNC, lambdamgm_DWork.ptfault_lambda,
                           &rtb_DiagMgm_SetDiagState_ftl);

      /* End of Outputs for SubSystem: '<S4>/Diag_Func' */
    } else {
      /* Transition: '<S11>:169' */
    }

    /* Transition: '<S11>:184' */
    /* Transition: '<S11>:183' */
    if (DrivingCycle == DRVC_ENDED) {
      /* Transition: '<S11>:172' */
      lambdamgm_DWork.trigLDInc = 1U;
      lambdamgm_DWork.trigLD2Inc = 1U;
      lambdamgm_DWork.FlgO2DiagOn_o2g = 0U;
      FlgO22DiagOn = 0U;
    } else {
      /* Transition: '<S11>:173' */
    }

    /* Transition: '<S11>:295' */
  }

  /* End of Chart: '<S4>/VLambdaState_Calc' */

  /* Sum: '<S5>/Add2' */
  VLambdaCrk = (uint16_T)((int32_T)(((int32_T)VLambda0) + (((int32_T)
    thrvlamr2p_crank) << ((uint32_T)4))));

  /* Switch: '<S13>/Switch' incorporates:
   *  Constant: '<S13>/CC_TO_VBAT'
   *  Constant: '<S13>/LAM_HEAT_D'
   *  Constant: '<S13>/NO_PT_FAULT'
   *  Constant: '<S13>/VLAMMAX'
   *  Constant: '<S13>/VLAMOCMIN'
   *  Inport: '<Root>/StLamHeater'
   *  Inport: '<Root>/VLamM'
   *  Inport: '<Root>/VLamP'
   *  Logic: '<S13>/Logical Operator'
   *  Logic: '<S13>/Logical Operator1'
   *  Logic: '<S13>/Logical Operator2'
   *  RelationalOperator: '<S13>/Relational Operator'
   *  RelationalOperator: '<S13>/Relational Operator1'
   *  RelationalOperator: '<S13>/Relational Operator2'
   *  RelationalOperator: '<S13>/Relational Operator3'
   *  RelationalOperator: '<S13>/Relational Operator4'
   *  Switch: '<S13>/Switch1'
   */
  if ((VLamP > VLAMMAX) || (VLamM > VLAMMAX)) {
    rtb_Conversion4 = CC_TO_VBAT;
  } else if (((VLamP > VLAMOCMIN) || (VLamM > VLAMOCMIN)) && (StLamHeater ==
              LAM_HEAT_D)) {
    /* Switch: '<S13>/Switch1' incorporates:
     *  Constant: '<S13>/OPEN_CIRCUIT'
     */
    rtb_Conversion4 = OPEN_CIRCUIT;
  } else {
    rtb_Conversion4 = NO_PT_FAULT;
  }

  /* End of Switch: '<S13>/Switch' */

  /* S-Function (DiagMgm_SetDiagState): '<S19>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S13>/DIAG_LAM_EL'
   */
  DiagMgm_SetDiagState( DIAG_LAM_EL, rtb_Conversion4,
                       &rtb_DiagMgm_SetDiagState_k3o);

  /* Memory: '<S17>/Memory' */
  rtb_Conversion4 = lambdamgm_DWork.Memory_PreviousInput_c24;

  /* Product: '<S52>/Product' incorporates:
   *  Constant: '<S60>/Constant'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/IdxCtfFlg'
   *  Logic: '<S52>/Logical Operator'
   *  Logic: '<S52>/Logical Operator1'
   *  Logic: '<S52>/Logical Operator2'
   *  Memory: '<S52>/Memory'
   *  RelationalOperator: '<S60>/Compare'
   *  Sum: '<S52>/Add'
   */
  rtb_Product = ((((int32_T)IdxCtfFlg) == 0) && (((int32_T)rtb_Conversion4) == 0))
    ? (((uint32_T)((((int32_T)CntTdcCrk) != 0) ? 1 : 0)) +
       lambdamgm_DWork.Memory_PreviousInput) : 0U;

  /* Switch: '<S52>/Switch1' incorporates:
   *  Constant: '<S52>/LAMOBD2TIMDIAG'
   *  Inport: '<Root>/CntTdcCrk'
   */
  if (((int32_T)CntTdcCrk) != 0) {
    /* Switch: '<S52>/Switch' incorporates:
     *  Constant: '<S52>/Constant'
     *  Memory: '<S52>/Memory1'
     */
    if (((int32_T)rtb_Conversion4) != 0) {
      rtb_Switch1 = 0U;
    } else {
      rtb_Switch1 = lambdamgm_DWork.Memory1_PreviousInput;
    }

    /* End of Switch: '<S52>/Switch' */
  } else {
    rtb_Switch1 = LAMOBD2TIMDIAG;
  }

  /* End of Switch: '<S52>/Switch1' */

  /* Chart: '<S50>/Chart' incorporates:
   *  Inport: '<Root>/CntTdcCrk'
   *  Memory: '<S4>/Memory'
   */
  /* Gateway: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc/Chart */
  /* During: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc/Chart */
  if (((uint32_T)lambdamgm_DWork.is_active_c2_lambdamgm) == 0U) {
    /* Entry: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc/Chart */
    lambdamgm_DWork.is_active_c2_lambdamgm = 1U;

    /* Entry Internal: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Diagnosis_Condition/CounterTdc/Chart */
    /* Transition: '<S58>:2' */
    lambdamgm_DWork.is_c2_lambdamgm = lambdamgm_IN_STOP;
  } else if (((uint32_T)lambdamgm_DWork.is_c2_lambdamgm) == lambdamgm_IN_GO) {
    /* During 'GO': '<S58>:3' */
    /* Transition: '<S58>:7' */
    if (((int32_T)CntTdcCrk) == 0) {
      /* Transition: '<S58>:8' */
      lambdamgm_DWork.is_c2_lambdamgm = lambdamgm_IN_STOP;
    } else {
      /* Transition: '<S58>:11' */
      rtb_DataTypeConversion = ((int32_T)CntTdcCrk) - ((int32_T)
        lambdamgm_DWork.offCntTdcCrk_iga);
      if (rtb_DataTypeConversion < 0) {
        rtb_DataTypeConversion = 0;
      }

      lambdamgm_DWork.out_ns4 = (uint16_T)rtb_DataTypeConversion;
    }
  } else {
    /* During 'STOP': '<S58>:1' */
    /* Transition: '<S58>:9' */
    if (((int32_T)lambdamgm_DWork.Memory_PreviousInput_d3k) != 0) {
      /* Transition: '<S58>:5' */
      lambdamgm_DWork.offCntTdcCrk_iga = CntTdcCrk;
      lambdamgm_DWork.is_c2_lambdamgm = lambdamgm_IN_GO;
    } else {
      /* Transition: '<S58>:10' */
    }
  }

  /* End of Chart: '<S50>/Chart' */

  /* Logic: '<S36>/Logical Operator' incorporates:
   *  Constant: '<S40>/Constant'
   *  Constant: '<S41>/Constant'
   *  Constant: '<S42>/Constant'
   *  Constant: '<S43>/Constant'
   *  Constant: '<S49>/Constant'
   *  Inport: '<Root>/StLamHeater'
   *  Memory: '<S4>/Memory'
   *  RelationalOperator: '<S40>/Compare'
   *  RelationalOperator: '<S41>/Compare'
   *  RelationalOperator: '<S42>/Compare'
   *  RelationalOperator: '<S43>/Compare'
   *  RelationalOperator: '<S49>/Compare'
   *  Sum: '<S52>/Add1'
   */
  LamDiagCond = (uint8_T)(((((((rtb_Compare_lc0 && (StLamHeater == LAM_HEAT_D)) &&
    rtb_Compare_lix) && ((rtb_Product + rtb_Switch1) > LAMOBD2TIMDIAG)) &&
    (rtb_DiagMgm_SetDiagState_k3o != FAULT)) && (lambdamgm_DWork.out_ns4 >
    LAMOBD2TDCCRK)) && (((int32_T)lambdamgm_DWork.Memory_PreviousInput_d3k) != 0))
    ? 1 : 0);

  /* Chart: '<S17>/Calc_Transitions' incorporates:
   *  Inport: '<Root>/DrivingCycle'
   *  Inport: '<Root>/FlgClosedLoopOnOff'
   */
  /* Gateway: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Calc_Transitions */
  /* During: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Calc_Transitions */
  if (((uint32_T)lambdamgm_DWork.is_active_c3_lambdamgm) == 0U) {
    /* Entry: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Calc_Transitions */
    lambdamgm_DWork.is_active_c3_lambdamgm = 1U;

    /* Entry Internal: lambdamgm/T5ms/Lambda_T5ms/Calc_Threshold/OBD2_Diagnosis/Calc_Transitions */
    /* Entry Internal 'LAM_TIM_TRANSITIONS': '<S35>:51' */
    /* Transition: '<S35>:50' */
    CntOBD2LamR2LMin = MAX_uint16_T;
    CntOBD2LamL2RMin = MAX_uint16_T;
    lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_LEAN;

    /* Entry Internal 'LAM_DISTURBANCE': '<S35>:52' */
    /* Transition: '<S35>:55' */
    LamObjOBD2 = 0U;
    lambdamgm_DWork.LamOBD2End_abf = 0U;
    lambdamgm_DWork.is_LAM_DISTURBANCE = lambdamgm_IN_NORMAL_OBJ;

    /* Entry Internal 'LAM_DIAG_OBD2': '<S35>:94' */
    /* Transition: '<S35>:97' */
    lambdamgm_DWork.FlgO2DiagOn_dta = 0U;
    lambdamgm_DWork.endR2L = 0U;
    lambdamgm_DWork.endL2R = 0U;
    CntOBD2TstL2R = 0U;
    CntOBD2TstR2L = 0U;
    lambdamgm_DWork.is_LAM_DIAG_OBD2 = lambdamgm_IN_DIAGNOSABLE;
  } else {
    /* During 'LAM_TIM_TRANSITIONS': '<S35>:51' */
    switch (lambdamgm_DWork.is_LAM_TIM_TRANSITIONS) {
     case lambdamgm_IN_L2R_TR_TIME:
      /* During 'L2R_TR_TIME': '<S35>:31' */
      /* Transition: '<S35>:40' */
      rtb_DataTypeConversion = ((int32_T)lambdamgm_DWork.cnt) + 1;
      if (rtb_DataTypeConversion > 65535) {
        rtb_DataTypeConversion = 65535;
      }

      lambdamgm_DWork.cnt = (uint16_T)rtb_DataTypeConversion;
      if (VLambda > THROBD2LAMRICH) {
        /* Transition: '<S35>:41' */
        CntOBD2LamL2R = lambdamgm_DWork.cnt;
        if (CntOBD2LamL2R > CntOBD2LamL2RMax) {
          CntOBD2LamL2RMax = CntOBD2LamL2R;
        }

        if (CntOBD2LamL2R < CntOBD2LamL2RMin) {
          CntOBD2LamL2RMin = CntOBD2LamL2R;
        }

        StLamOBD2DIag = 2U;
        lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_RICH;
      } else {
        /* Transition: '<S35>:43' */
        if (VLambda < THROBD2LAMLEAN) {
          /* Transition: '<S35>:44' */
          StLamOBD2DIag = 0U;
          lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_LEAN;
        } else {
          /* Transition: '<S35>:45' */
        }
      }
      break;

     case lambdamgm_IN_LEAN:
      /* During 'LEAN': '<S35>:20' */
      /* Transition: '<S35>:34' */
      if (VLambda > THROBD2LAMLEAN) {
        /* Transition: '<S35>:26' */
        StLamOBD2DIag = 1U;
        lambdamgm_DWork.cnt = 0U;
        lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_L2R_TR_TIME;
      } else {
        /* Transition: '<S35>:32' */
      }
      break;

     case lambdamgm_IN_R2L_TR_TIME:
      /* During 'R2L_TR_TIME': '<S35>:4' */
      /* Transition: '<S35>:10' */
      rtb_DataTypeConversion = ((int32_T)lambdamgm_DWork.cnt) + 1;
      if (rtb_DataTypeConversion > 65535) {
        rtb_DataTypeConversion = 65535;
      }

      lambdamgm_DWork.cnt = (uint16_T)rtb_DataTypeConversion;
      if (VLambda < THROBD2LAMLEAN) {
        /* Transition: '<S35>:12' */
        CntOBD2LamR2L = lambdamgm_DWork.cnt;
        if (CntOBD2LamR2L > CntOBD2LamR2LMax) {
          CntOBD2LamR2LMax = CntOBD2LamR2L;
        }

        if (CntOBD2LamR2L < CntOBD2LamR2LMin) {
          CntOBD2LamR2LMin = CntOBD2LamR2L;
        }

        StLamOBD2DIag = 0U;
        lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_LEAN;
      } else {
        /* Transition: '<S35>:14' */
        if (VLambda > THROBD2LAMRICH) {
          /* Transition: '<S35>:17' */
          StLamOBD2DIag = 2U;
          lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_RICH;
        } else {
          /* Transition: '<S35>:15' */
        }
      }
      break;

     default:
      /* During 'RICH': '<S35>:37' */
      /* Transition: '<S35>:47' */
      if (VLambda < THROBD2LAMRICH) {
        /* Transition: '<S35>:48' */
        StLamOBD2DIag = 3U;
        lambdamgm_DWork.cnt = 0U;
        lambdamgm_DWork.is_LAM_TIM_TRANSITIONS = lambdamgm_IN_R2L_TR_TIME;
      } else {
        /* Transition: '<S35>:49' */
      }
      break;
    }

    /* During 'LAM_DISTURBANCE': '<S35>:52' */
    switch (lambdamgm_DWork.is_LAM_DISTURBANCE) {
     case lambdamgm_IN_DONE:
      /* During 'DONE': '<S35>:89' */
      /* Transition: '<S35>:93' */
      /* Transition: '<S35>:92' */
      lambdamgm_DWork.LamOBD2End_abf = 0U;
      lambdamgm_DWork.is_LAM_DISTURBANCE = lambdamgm_IN_NORMAL_OBJ;
      break;

     case lambdamgm_IN_LAM_OBD2_DIAG:
      /* During 'LAM_OBD2_DIAG': '<S35>:59' */
      if (((int32_T)LamDiagCond) == 0) {
        /* Transition: '<S35>:84' */
        LamObjOBD2 = 0U;

        /* Exit Internal 'LAM_OBD2_DIAG': '<S35>:59' */
        lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_NO_ACTIVE_CHILD;
        lambdamgm_DWork.is_LAM_DISTURBANCE = lambdamgm_IN_NORMAL_OBJ;
      } else if ((((int32_T)lambdamgm_DWork.flgDone) != 0) ||
                 (lambdamgm_DWork.wdt > TIMOBD2LAMWDT)) {
        /* Transition: '<S35>:90' */
        LamObjOBD2 = 0U;
        lambdamgm_DWork.LamOBD2End_abf = 1U;

        /* Exit Internal 'LAM_OBD2_DIAG': '<S35>:59' */
        lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_NO_ACTIVE_CHILD;
        lambdamgm_DWork.is_LAM_DISTURBANCE = lambdamgm_IN_DONE;
      } else {
        switch (lambdamgm_DWork.is_LAM_OBD2_DIAG) {
         case lambdamgm_IN_GO_LEAN:
          /* During 'GO_LEAN': '<S35>:67' */
          /* Transition: '<S35>:80' */
          LamObjOBD2 = LAMOBD2LEAN;
          rtb_DataTypeConversion = ((int32_T)lambdamgm_DWork.wdt) + 1;
          if (rtb_DataTypeConversion > 65535) {
            rtb_DataTypeConversion = 65535;
          }

          lambdamgm_DWork.wdt = (uint16_T)rtb_DataTypeConversion;
          if (((int32_T)StLamOBD2DIag) == 0) {
            /* Transition: '<S35>:79' */
            lambdamgm_DWork.cnt2 = 0U;
            lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_IN_LEAN;
          } else {
            /* Transition: '<S35>:81' */
          }
          break;

         case lambdamgm_IN_GO_RICH:
          /* During 'GO_RICH': '<S35>:62' */
          /* Transition: '<S35>:64' */
          rtb_DataTypeConversion = ((int32_T)lambdamgm_DWork.wdt) + 1;
          if (rtb_DataTypeConversion > 65535) {
            rtb_DataTypeConversion = 65535;
          }

          lambdamgm_DWork.wdt = (uint16_T)rtb_DataTypeConversion;
          if (((int32_T)StLamOBD2DIag) == 2) {
            /* Transition: '<S35>:65' */
            lambdamgm_DWork.cnt2 = 0U;
            lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_IN_RICH;
          } else {
            /* Transition: '<S35>:71' */
          }
          break;

         case lambdamgm_IN_IN_LEAN:
          /* During 'IN_LEAN': '<S35>:77' */
          /* Transition: '<S35>:86' */
          if (lambdamgm_DWork.cnt2 >= ENDOBD2LAML2R) {
            /* Transition: '<S35>:88' */
            LamObjOBD2 = LAMOBD2RICH;
            lambdamgm_DWork.wdt = 0U;
            lambdamgm_DWork.wdtlr = 1U;
            lambdamgm_DWork.end = 1U;
            lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_GO_RICH;
          } else {
            /* Transition: '<S35>:87' */
            rtb_DataTypeConversion = ((int32_T)lambdamgm_DWork.cnt2) + 1;
            if (rtb_DataTypeConversion > 65535) {
              rtb_DataTypeConversion = 65535;
            }

            lambdamgm_DWork.cnt2 = (uint16_T)rtb_DataTypeConversion;
          }
          break;

         default:
          /* During 'IN_RICH': '<S35>:61' */
          /* Transition: '<S35>:72' */
          if (lambdamgm_DWork.cnt2 >= ENDOBD2LAMR2L) {
            /* Transition: '<S35>:70' */
            lambdamgm_DWork.flgDone = lambdamgm_DWork.end;
            lambdamgm_DWork.wdt = 0U;
            lambdamgm_DWork.wdtlr = 2U;
            lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_GO_LEAN;
          } else {
            /* Transition: '<S35>:73' */
            rtb_DataTypeConversion = ((int32_T)lambdamgm_DWork.cnt2) + 1;
            if (rtb_DataTypeConversion > 65535) {
              rtb_DataTypeConversion = 65535;
            }

            lambdamgm_DWork.cnt2 = (uint16_T)rtb_DataTypeConversion;
          }
          break;
        }
      }
      break;

     default:
      /* During 'NORMAL_OBJ': '<S35>:54' */
      /* Transition: '<S35>:57' */
      if ((((int32_T)LamDiagCond) != 0) && (((int32_T)FlgClosedLoopOnOff) != 0))
      {
        /* Transition: '<S35>:60' */
        lambdamgm_DWork.end = 0U;
        lambdamgm_DWork.wdt = 0U;
        lambdamgm_DWork.wdtlr = 1U;
        lambdamgm_DWork.flgDone = 0U;
        LamObjOBD2 = LAMOBD2RICH;
        lambdamgm_DWork.is_LAM_DISTURBANCE = lambdamgm_IN_LAM_OBD2_DIAG;
        lambdamgm_DWork.is_LAM_OBD2_DIAG = lambdamgm_IN_GO_RICH;
      } else {
        /* Transition: '<S35>:58' */
      }
      break;
    }

    /* During 'LAM_DIAG_OBD2': '<S35>:94' */
    if (((uint32_T)lambdamgm_DWork.is_LAM_DIAG_OBD2) == lambdamgm_IN_DIAGNOSABLE)
    {
      /* During 'DIAGNOSABLE': '<S35>:181' */
      /* Transition: '<S35>:182' */
      if (((int32_T)lambdamgm_DWork.LamOBD2End_abf) != 0) {
        /* Transition: '<S35>:106' */
        if ((CntOBD2LamL2R > TIMOBD2LAML2R) || ((lambdamgm_DWork.wdt >
              TIMOBD2LAMWDT) && (((int32_T)lambdamgm_DWork.wdtlr) == 1))) {
          /* Transition: '<S35>:142' */
          lambdamgm_DWork.ptFault_lean = SIGNAL_TOO_SLOW;
          rtb_DataTypeConversion = ((int32_T)CntOBD2TstL2R) + ((int32_T)
            NOBD2LAMINC);
          if (rtb_DataTypeConversion > 255) {
            rtb_DataTypeConversion = 255;
          }

          CntOBD2TstL2R = (uint8_T)rtb_DataTypeConversion;

          /* Transition: '<S35>:147' */
          /* Transition: '<S35>:168' */
        } else {
          /* Transition: '<S35>:144' */
          lambdamgm_DWork.ptFault_lean = NO_PT_FAULT;
          rtb_DataTypeConversion = ((int32_T)CntOBD2TstL2R) - ((int32_T)
            NOBD2LAMDEC);
          if (rtb_DataTypeConversion < 0) {
            rtb_DataTypeConversion = 0;
          }

          CntOBD2TstL2R = (uint8_T)rtb_DataTypeConversion;
        }

        if ((((int32_T)lambdamgm_DWork.endL2R) == 0) && ((CntOBD2TstL2R >
              NOBD2LAMTST) || (((int32_T)CntOBD2TstL2R) == 0))) {
          /* Transition: '<S35>:169' */
          lambdamgm_DWork.endL2R = 1U;

          /* Outputs for Function Call SubSystem: '<S17>/fc_SlowLean' */
          /* S-Function (DiagMgm_SetDiagState): '<S74>/DiagMgm_SetDiagState' incorporates:
           *  Constant: '<S38>/DIAG_O2_SLOW_L2R'
           */
          /* Event: '<S35>:166' */
          DiagMgm_SetDiagState( DIAG_O2_SLOW_L2R, lambdamgm_DWork.ptFault_lean,
                               &rtb_DiagMgm_SetDiagState_hip);

          /* End of Outputs for SubSystem: '<S17>/fc_SlowLean' */
          /* Transition: '<S35>:232' */
        } else {
          /* Transition: '<S35>:173' */
        }

        /* Transition: '<S35>:233' */
        if ((CntOBD2LamR2L > TIMOBD2LAMR2L) || ((lambdamgm_DWork.wdt >
              TIMOBD2LAMWDT) && (((int32_T)lambdamgm_DWork.wdtlr) == 2))) {
          /* Transition: '<S35>:152' */
          lambdamgm_DWork.ptFault_rich = SIGNAL_TOO_SLOW;
          rtb_DataTypeConversion = ((int32_T)CntOBD2TstR2L) + ((int32_T)
            NOBD2LAMINC);
          if (rtb_DataTypeConversion > 255) {
            rtb_DataTypeConversion = 255;
          }

          CntOBD2TstR2L = (uint8_T)rtb_DataTypeConversion;

          /* Transition: '<S35>:161' */
          /* Transition: '<S35>:174' */
        } else {
          /* Transition: '<S35>:157' */
          lambdamgm_DWork.ptFault_rich = NO_PT_FAULT;
          rtb_DataTypeConversion = ((int32_T)CntOBD2TstR2L) - ((int32_T)
            NOBD2LAMDEC);
          if (rtb_DataTypeConversion < 0) {
            rtb_DataTypeConversion = 0;
          }

          CntOBD2TstR2L = (uint8_T)rtb_DataTypeConversion;
        }

        if ((((int32_T)lambdamgm_DWork.endR2L) == 0) && ((CntOBD2TstR2L >
              NOBD2LAMTST) || (((int32_T)CntOBD2TstR2L) == 0))) {
          /* Transition: '<S35>:176' */
          lambdamgm_DWork.endR2L = 1U;

          /* Outputs for Function Call SubSystem: '<S17>/fc_SlowRich' */
          /* S-Function (DiagMgm_SetDiagState): '<S75>/DiagMgm_SetDiagState' incorporates:
           *  Constant: '<S39>/DIAG_O2_SLOW_R2L'
           */
          /* Event: '<S35>:165' */
          DiagMgm_SetDiagState( DIAG_O2_SLOW_R2L, lambdamgm_DWork.ptFault_rich,
                               &rtb_DiagMgm_SetDiagState_aih);

          /* End of Outputs for SubSystem: '<S17>/fc_SlowRich' */
          /* Transition: '<S35>:238' */
        } else {
          /* Transition: '<S35>:179' */
        }

        /* Transition: '<S35>:160' */
        lambdamgm_DWork.FlgO2DiagOn_dta = (uint8_T)(((((int32_T)
          lambdamgm_DWork.endR2L) != 0) && (((int32_T)lambdamgm_DWork.endL2R) !=
          0)) ? 1 : 0);
      } else {
        /* Transition: '<S35>:99' */
        /*  Skip diag */
      }

      if (((int32_T)lambdamgm_DWork.FlgO2DiagOn_dta) != 0) {
        /* Transition: '<S35>:184' */
        lambdamgm_DWork.is_LAM_DIAG_OBD2 = lambdamgm_IN_END;
      } else {
        /* Transition: '<S35>:185' */
      }
    } else {
      /* During 'END': '<S35>:183' */
      /* Transition: '<S35>:187' */
      if (((int32_T)lambdamgm_DWork.FlgO2DiagOn_dta) == 0) {
        /* Transition: '<S35>:188' */
        lambdamgm_DWork.endL2R = 0U;
        lambdamgm_DWork.endR2L = 0U;
        CntOBD2TstL2R = 0U;
        CntOBD2TstR2L = 0U;
        lambdamgm_DWork.is_LAM_DIAG_OBD2 = lambdamgm_IN_DIAGNOSABLE;
      } else {
        /* Transition: '<S35>:258' */
      }
    }

    /* During 'LAM_CYCLE': '<S35>:197' */
    /* Transition: '<S35>:257' */
    if (DrivingCycle == DRVC_ENDED) {
      /* Transition: '<S35>:253' */
      lambdamgm_DWork.FlgO2DiagOn_dta = 0U;
    } else {
      /* Transition: '<S35>:254' */
    }

    /* Transition: '<S35>:256' */
  }

  /* End of Chart: '<S17>/Calc_Transitions' */

  /* DataTypeConversion: '<S36>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  thrvlamr2p_crank = (int16_T)Rpm;

  /* DataTypeConversion: '<S36>/Data Type Conversion1' */
  rtb_Switch_eoa = (int16_T)rtb_MultiportSwitch;

  /* Switch: '<S53>/Switch' incorporates:
   *  Constant: '<S53>/Constant'
   *  Constant: '<S53>/LAMOBD2LOADHYS'
   *  Memory: '<S53>/Memory'
   */
  if (lambdamgm_DWork.Memory_PreviousInput_oab) {
    rtb_Add_m3n = LAMOBD2LOADHYS;
  } else {
    rtb_Add_m3n = 0;
  }

  /* End of Switch: '<S53>/Switch' */

  /* Sum: '<S53>/Add' incorporates:
   *  Constant: '<S53>/LAMOBD2LOADMAX'
   */
  rtb_Add_dvn = (int16_T)(LAMOBD2LOADMAX + rtb_Add_m3n);

  /* Switch: '<S62>/Switch' incorporates:
   *  RelationalOperator: '<S62>/u_GTE_up'
   */
  if (rtb_Switch_eoa >= rtb_Add_dvn) {
  } else {
    /* Sum: '<S53>/Add1' incorporates:
     *  Constant: '<S53>/LAMOBD2LOADMIN'
     */
    rtb_Add_m3n = (int16_T)(LAMOBD2LOADMIN - rtb_Add_m3n);

    /* Switch: '<S62>/Switch1' incorporates:
     *  RelationalOperator: '<S62>/u_GT_lo'
     */
    if (rtb_Switch_eoa > rtb_Add_m3n) {
      rtb_Add_dvn = rtb_Switch_eoa;
    } else {
      rtb_Add_dvn = rtb_Add_m3n;
    }

    /* End of Switch: '<S62>/Switch1' */
  }

  /* End of Switch: '<S62>/Switch' */

  /* RelationalOperator: '<S61>/Compare' incorporates:
   *  Sum: '<S62>/Diff'
   */
  rtb_Compare_lix = (((int16_T)(rtb_Switch_eoa - rtb_Add_dvn)) == 0);

  /* Switch: '<S55>/Switch' incorporates:
   *  Constant: '<S55>/Constant'
   *  Constant: '<S55>/LAMOBD2RPMHYS'
   *  Memory: '<S55>/Memory'
   */
  if (lambdamgm_DWork.Memory_PreviousInput_i3l) {
    rtb_Switch_eoa = LAMOBD2RPMHYS;
  } else {
    rtb_Switch_eoa = 0;
  }

  /* End of Switch: '<S55>/Switch' */

  /* Sum: '<S55>/Add' incorporates:
   *  Constant: '<S55>/LAMOBD2RMPMAX'
   */
  rtb_Add_m3n = (int16_T)(LAMOBD2RMPMAX + rtb_Switch_eoa);

  /* Switch: '<S66>/Switch' incorporates:
   *  RelationalOperator: '<S66>/u_GTE_up'
   */
  if (thrvlamr2p_crank >= rtb_Add_m3n) {
  } else {
    /* Sum: '<S55>/Add1' incorporates:
     *  Constant: '<S55>/LAMOBD2RMPMIN'
     */
    rtb_Switch_eoa = (int16_T)(LAMOBD2RMPMIN - rtb_Switch_eoa);

    /* Switch: '<S66>/Switch1' incorporates:
     *  RelationalOperator: '<S66>/u_GT_lo'
     */
    if (thrvlamr2p_crank > rtb_Switch_eoa) {
      rtb_Add_m3n = thrvlamr2p_crank;
    } else {
      rtb_Add_m3n = rtb_Switch_eoa;
    }

    /* End of Switch: '<S66>/Switch1' */
  }

  /* End of Switch: '<S66>/Switch' */

  /* RelationalOperator: '<S65>/Compare' incorporates:
   *  Constant: '<S65>/Constant'
   *  Sum: '<S66>/Diff'
   */
  rtb_Compare_lc0 = (((int16_T)(thrvlamr2p_crank - rtb_Add_m3n)) == 0);

  /* Logic: '<S17>/Logical Operator' */
  LamOBD2End = (uint8_T)(((((int32_T)lambdamgm_DWork.LamOBD2End_abf) != 0) ||
    (((int32_T)lambdamgm_DWork.FlgO2DiagOn_dta) != 0)) ? 1 : 0);

  /* Constant: '<S5>/ID_LAMBDA_MGM' */
  IDLambdaMgm = ID_LAMBDA_MGM;

  /* Chart: '<S9>/Chart' incorporates:
   *  Constant: '<S9>/MINTWCAT'
   *  Constant: '<S9>/NTDCCAT'
   *  Constant: '<S9>/THRCATDIAG'
   *  Constant: '<S9>/THRRATIOCATDIAG'
   *  Inport: '<Root>/CntAbsTdc'
   *  Inport: '<Root>/DrivingCycle'
   *  Inport: '<Root>/FlgClosedLoopOnOff'
   *  Inport: '<Root>/FlgRpmStab'
   *  Inport: '<Root>/IdleFlg'
   *  Inport: '<Root>/IdxCtfFlg'
   *  Inport: '<Root>/KeySignal'
   *  Inport: '<Root>/Odometer'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/TWater'
   *  Inport: '<Root>/VehSpeed'
   */
  /* Gateway: lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Chart */
  /* During: lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Chart */
  if (((uint32_T)lambdamgm_DWork.is_active_c4_lambdamgm) == 0U) {
    /* Entry: lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Chart */
    lambdamgm_DWork.is_active_c4_lambdamgm = 1U;

    /* Entry Internal: lambdamgm/T5ms/Lambda_T5ms/Lam2_Diag/Chart */
    /* Transition: '<S83>:2' */
    CatDiagCond = 0U;
    CntLam1Trs = 0U;
    CntLam2Trs = 0U;
    CntLam2TrsMin = MAX_uint16_T;
    CntLam2TrsMax = 0U;
    StCatDiag = ((uint8_T)INIT_CAT);
    lambdamgm_DWork.is_c4_lambdamgm = lambdamgm_IN_INIT_CAT;
  } else {
    switch (lambdamgm_DWork.is_c4_lambdamgm) {
     case lambdamgm_IN_DIAG_CAT:
      /* During 'DIAG_CAT': '<S83>:16' */
      if (((((((int32_T)FlgLamReady) == 0) || (((int32_T)FlgLam2Ready) == 0)) ||
            (Odometer <= MINODOCAT)) || (((int32_T)Rpm) == 0)) || (((int32_T)
            FlgCatDiagOn) != 0)) {
        /* Transition: '<S83>:98' */
        StCatDiag = ((uint8_T)WAIT_READY_CAT);
        CatDiagCond = 0U;

        /* Exit Internal 'DIAG_CAT': '<S83>:16' */
        lambdamgm_DWork.is_DIAG_CAT = lambdamgm_IN_NO_ACTIVE_CHILD;
        lambdamgm_DWork.is_c4_lambdamgm = lambdamgm_IN_WAIT_READY_CAT;
      } else {
        switch (lambdamgm_DWork.is_DIAG_CAT) {
         case lambdamgm_IN_READY_CAT:
          /* During 'READY_CAT': '<S83>:24' */
          /* Transition: '<S83>:30' */
          if ((((((TWater <= MINTWCAT) || (((int32_T)IdxCtfFlg) != 0)) ||
                 (((int32_T)FlgRpmStab) == 0)) || (((int32_T)IdleFlg) != 0)) ||
               (VehSpeed <= MINCATSPEED)) || (((int32_T)FlgClosedLoopOnOff) == 0))
          {
            /* Transition: '<S83>:32' */
            lambdamgm_DWork.oldCntLamLiveness = CntLamLiveness;
            StCatDiag = ((uint8_T)WAIT_COND_CAT);

            /* Transition: '<S83>:33' */
            lambdamgm_DWork.is_DIAG_CAT = lambdamgm_IN_WAIT_COND_CAT;
          } else {
            /* Transition: '<S83>:35' */
            if (lambdamgm_DWork.oldCntLamLiveness != CntLamLiveness) {
              /* Transition: '<S83>:37' */
              lambdamgm_DWork.oldCntLamLiveness = CntLamLiveness;
              rtb_DataTypeConversion = ((int32_T)CntLam1Trs) + 1;
              if (rtb_DataTypeConversion > 65535) {
                rtb_DataTypeConversion = 65535;
              }

              CntLam1Trs = (uint16_T)rtb_DataTypeConversion;
            } else {
              /* Transition: '<S83>:38' */
            }

            if (lambdamgm_DWork.oldCntLam2Liveness != CntLam2Liveness) {
              /* Transition: '<S83>:40' */
              lambdamgm_DWork.oldCntLam2Liveness = CntLam2Liveness;
              rtb_DataTypeConversion = ((int32_T)CntLam2Trs) + 1;
              if (rtb_DataTypeConversion > 65535) {
                rtb_DataTypeConversion = 65535;
              }

              CntLam2Trs = (uint16_T)rtb_DataTypeConversion;
            } else {
              /* Transition: '<S83>:39' */
            }

            if (CntLam1Trs >= THRCATDIAG) {
              /* Transition: '<S83>:45' */
              if (((int32_T)CntLam1Trs) == 0) {
                /* Transition: '<S83>:47' */
                RatioCatDiag = 0U;

                /* Transition: '<S83>:56' */
                /* Transition: '<S83>:57' */
              } else {
                /* Transition: '<S83>:52' */
                qY = ((uint32_T)CntLam2Trs) * ((uint32_T)((uint16_T)ONE_RES_LAM));
                if (qY > 2147483647U) {
                  qY = 2147483647U;
                }

                rtb_DataTypeConversion = ((int32_T)qY) / ((int32_T)CntLam1Trs);
                if (rtb_DataTypeConversion < 0) {
                  rtb_DataTypeConversion = 0;
                }

                RatioCatDiag = (uint32_T)rtb_DataTypeConversion;

                /* Transition: '<S83>:54' */
              }

              if (RatioCatDiag > THRRATIOCATDIAG) {
                /* Transition: '<S83>:59' */
                lambdamgm_DWork.ptfault_cat = SIG_NOT_PLAUSIBLE;
                FlgCatDiagOn = 1U;

                /* Outputs for Function Call SubSystem: '<S9>/Diag_CAT' */
                /* S-Function (DiagMgm_SetDiagState): '<S85>/DiagMgm_SetDiagState' incorporates:
                 *  Constant: '<S84>/DIAG_CAT'
                 */
                /* Event: '<S83>:102' */
                DiagMgm_SetDiagState( DIAG_CAT, lambdamgm_DWork.ptfault_cat,
                                     &rtb_DiagMgm_SetDiagState_h05);

                /* End of Outputs for SubSystem: '<S9>/Diag_CAT' */
              } else {
                /* Transition: '<S83>:60' */
                lambdamgm_DWork.ptfault_cat = NO_PT_FAULT;
                FlgCatDiagOn = 1U;

                /* Outputs for Function Call SubSystem: '<S9>/Diag_CAT' */
                /* S-Function (DiagMgm_SetDiagState): '<S85>/DiagMgm_SetDiagState' incorporates:
                 *  Constant: '<S84>/DIAG_CAT'
                 */
                /* Event: '<S83>:102' */
                DiagMgm_SetDiagState( DIAG_CAT, lambdamgm_DWork.ptfault_cat,
                                     &rtb_DiagMgm_SetDiagState_h05);

                /* End of Outputs for SubSystem: '<S9>/Diag_CAT' */
              }

              /* Transition: '<S83>:61' */
              if (CntLam2TrsMin < CntLam2Trs) {
              } else {
                CntLam2TrsMin = CntLam2Trs;
              }

              if (CntLam2TrsMax > CntLam2Trs) {
              } else {
                CntLam2TrsMax = CntLam2Trs;
              }

              CntLam1Trs = 0U;
              CntLam2Trs = 0U;
            } else {
              /* Transition: '<S83>:43' */
              /* Transition: '<S83>:49' */
            }

            /* Transition: '<S83>:50' */
          }
          break;

         case lambdamgm_IN_WAIT_COND_CAT:
          /* During 'WAIT_COND_CAT': '<S83>:22' */
          /* Transition: '<S83>:28' */
          if (((((((TWater > MINTWCAT) && (((int32_T)IdxCtfFlg) == 0)) &&
                  (lambdamgm_DWork.oldCntLamLiveness != CntLamLiveness)) &&
                 (((int32_T)FlgRpmStab) != 0)) && (((int32_T)IdleFlg) == 0)) &&
               (VehSpeed > MINCATSPEED)) && (((int32_T)FlgClosedLoopOnOff) != 0))
          {
            /* Transition: '<S83>:26' */
            lambdamgm_DWork.oldCntLamLiveness = CntLamLiveness;
            lambdamgm_DWork.oldCntLam2Liveness = CntLam2Liveness;
            StCatDiag = ((uint8_T)READY_CAT);
            lambdamgm_DWork.is_DIAG_CAT = lambdamgm_IN_READY_CAT;
          } else {
            /* Transition: '<S83>:27' */
          }
          break;

         default:
          /* During 'WAIT_TDC_CAT': '<S83>:18' */
          /* Transition: '<S83>:20' */
          qY = CntAbsTdc - /*MW:OvSatOk*/ lambdamgm_DWork.tmpCntAbsTdc;
          if (qY > CntAbsTdc) {
            qY = 0U;
          }

          if (qY > 65535U) {
            qY = 65535U;
          }

          if (qY > NTDCCAT) {
            /* Transition: '<S83>:23' */
            lambdamgm_DWork.oldCntLamLiveness = CntLamLiveness;
            StCatDiag = ((uint8_T)WAIT_COND_CAT);
            CatDiagCond = 1U;
            lambdamgm_DWork.is_DIAG_CAT = lambdamgm_IN_WAIT_COND_CAT;
          } else {
            /* Transition: '<S83>:21' */
          }
          break;
        }
      }
      break;

     case lambdamgm_IN_INIT_CAT:
      /* During 'INIT_CAT': '<S83>:1' */
      /* Transition: '<S83>:5' */
      if ((((int32_T)KeySignal) != 0) && (Odometer > MINODOCAT)) {
        /* Transition: '<S83>:7' */
        StCatDiag = ((uint8_T)WAIT_READY_CAT);
        lambdamgm_DWork.is_c4_lambdamgm = lambdamgm_IN_WAIT_READY_CAT;
      } else {
        /* Transition: '<S83>:106' */
        if (DrivingCycle == DRVC_OFF) {
          /* Transition: '<S83>:107' */
          FlgCatDiagOn = 0U;
        } else {
          /* Transition: '<S83>:6' */
        }
      }
      break;

     default:
      /* During 'WAIT_READY_CAT': '<S83>:3' */
      /* Transition: '<S83>:9' */
      if ((((int32_T)KeySignal) == 0) || (Odometer <= MINODOCAT)) {
        /* Transition: '<S83>:10' */
        CntLam1Trs = 0U;
        CntLam2Trs = 0U;
        StCatDiag = ((uint8_T)INIT_CAT);
        lambdamgm_DWork.is_c4_lambdamgm = lambdamgm_IN_INIT_CAT;
      } else {
        /* Transition: '<S83>:12' */
        /* Transition: '<S83>:15' */
        if ((((((int32_T)FlgLamReady) != 0) && (((int32_T)FlgLam2Ready) != 0)) &&
             (((int32_T)Rpm) != 0)) && (((int32_T)FlgCatDiagOn) == 0)) {
          /* Transition: '<S83>:62' */
          lambdamgm_DWork.tmpCntAbsTdc = CntAbsTdc;
          StCatDiag = ((uint8_T)WAIT_TDC_CAT);
          lambdamgm_DWork.is_c4_lambdamgm = lambdamgm_IN_DIAG_CAT;
          lambdamgm_DWork.is_DIAG_CAT = lambdamgm_IN_WAIT_TDC_CAT;
        } else {
          /* Transition: '<S83>:14' */
        }
      }
      break;
    }
  }

  /* End of Chart: '<S9>/Chart' */

  /* Logic: '<S4>/Logical Operator' */
  FlgO2DiagOn = (uint8_T)(((((int32_T)lambdamgm_DWork.FlgO2DiagOn_o2g) != 0) ||
    (((int32_T)lambdamgm_DWork.FlgO2DiagOn_dta) != 0)) ? 1 : 0);

  /* Update for Memory: '<S37>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_lku = rtb_RateLimiter_S32;

  /* Update for Memory: '<S4>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_d3k = FlgLamReady;

  /* Update for Memory: '<S71>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_nqz = rtb_LogicalOperator;

  /* Update for Memory: '<S18>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_gja = rtb_RateLimiter_S32_dg2;

  /* Update for Memory: '<S4>/Memory1' */
  lambdamgm_DWork.Memory1_PreviousInput_jee = FlgLam2Ready;

  /* Update for Memory: '<S76>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_dy5 = rtb_LogicalOperator_bcs;

  /* Update for Memory: '<S55>/Memory1' incorporates:
   *  Logic: '<S55>/Logical Operator'
   *  Logic: '<S55>/Logical Operator2'
   */
  lambdamgm_DWork.Memory1_PreviousInput_pf2 = ((rtb_Compare_lc0 && (((int32_T)
    rtb_SteadyStateDetect_o1) != 0)) || rtb_LogicalOperator1_cyl);

  /* Update for Memory: '<S56>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_mhv = rtb_SteadyStateDetect_o2;

  /* Update for Memory: '<S68>/Memory1' */
  lambdamgm_DWork.Memory1_PreviousInput_nvw = rtb_SteadyStateDetect_o3;

  /* Update for Memory: '<S68>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_bxf = rtb_SteadyStateDetect_o4;

  /* Update for Memory: '<S53>/Memory1' incorporates:
   *  Logic: '<S53>/Logical Operator'
   *  Logic: '<S53>/Logical Operator2'
   */
  lambdamgm_DWork.Memory1_PreviousInput_pwi = ((rtb_Compare_lix && (((int32_T)
    rtb_SteadyStateDetect_o1_cui) != 0)) || rtb_LogicalOperator1_amu);

  /* Update for Memory: '<S54>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_cqm = rtb_SteadyStateDetect_o2_orv;

  /* Update for Memory: '<S64>/Memory1' */
  lambdamgm_DWork.Memory1_PreviousInput_mg1 = rtb_SteadyStateDetect_o3_kfe;

  /* Update for Memory: '<S64>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_gnc = rtb_SteadyStateDetect_o4_bue;

  /* Update for Memory: '<S57>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_idy = rtb_Compare;

  /* Update for Memory: '<S17>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_c24 = LamOBD2End;

  /* Update for Memory: '<S52>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput = rtb_Product;

  /* Update for Memory: '<S52>/Memory1' */
  lambdamgm_DWork.Memory1_PreviousInput = rtb_Switch1;

  /* Update for Memory: '<S53>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_oab = rtb_Compare_lix;

  /* Update for Memory: '<S55>/Memory' */
  lambdamgm_DWork.Memory_PreviousInput_i3l = rtb_Compare_lc0;
}

/* System initialize for function-call system: '<S2>/T5ms' */
void lambdamgm_T5ms_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S3>/Lambda_T5ms' */
  lambdamgm_Lambda_T5ms_Init();

  /* End of SystemInitialize for SubSystem: '<S3>/Lambda_T5ms' */
}

/* Output and update for function-call system: '<S2>/T5ms' */
void lambdamgm_T5ms(void)
{
  /* Outputs for Atomic SubSystem: '<S3>/Lambda_T5ms' */
  lambdamgm_Lambda_T5ms();

  /* End of Outputs for SubSystem: '<S3>/Lambda_T5ms' */
}

/* Model step function */
void Trig_lambdamgm_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<S2>/T5ms' */
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
  lambdamgm_T5ms();

  /* End of Outputs for SubSystem: '<S2>/T5ms' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
}

/* Model step function */
void Trig_lambdamgm_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/T5ms'
   */
  lambdamgm_T5ms();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void Trig_lambdamgm_T5(void)
{
  /* Outputs for Function Call SubSystem: '<S2>/T5ms' */
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_T5ms' */
  lambdamgm_T5ms();

  /* End of Outputs for SubSystem: '<S2>/T5ms' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_T5ms' */
}

/* Model initialize function */
void lambdamgm_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  AbsCntTransLam = 0U;
  PerOscLambda = 0U;
  PerOscLambdaMax = 0U;
  PerOscLambdaMin = 0U;
  IDLambdaMgm = 0U;
  RatioCatDiag = 0U;
  ThrVLamR2P = 0U;
  ThrVLamP2R = 0U;
  ThrVLam2R2P = 0U;
  ThrVLam2P2R = 0U;
  CntLamFunDiag = 0U;
  CntLamNoDiag = 0U;
  CntLamWatchdog = 0U;
  CntMinMaxLam2 = 0U;
  CntLam1Trs = 0U;
  CntLam2Trs = 0U;
  CntLam2TrsMax = 0U;
  CntLam2TrsMin = 0U;
  CntOBD2LamL2RMax = 0U;
  CntOBD2LamL2RMin = 0U;
  CntOBD2LamR2LMax = 0U;
  CntOBD2LamR2LMin = 0U;
  CntOBD2LamL2R = 0U;
  CntOBD2LamR2L = 0U;
  VLambda0 = 0U;
  VLambda = 0U;
  VLambda20 = 0U;
  VLambda2 = 0U;
  VLambdaCrk = 0U;
  VMaxLam2 = 0U;
  VMinLam2 = 0U;
  FreqOscLambda = 0U;
  LamObjOBD2 = 0U;
  Lam2DiagCond = 0U;
  LamDiagCond = 0U;
  LamOBD2End = 0U;
  FlgO2DiagOn = 0U;
  VLambdaState = 0U;
  StLamFuncDiag = 0U;
  FlgLamNotCoherent = 0U;
  FlgVeryRichPoor = 0U;
  FlgLamReady = 0U;
  FlgLam2Ready = 0U;
  VLambdaState2 = 0U;
  CntLamLiveness = 0U;
  CntLam2Liveness = 0U;
  FlgO22DiagOn = 0U;
  StCatDiag = 0U;
  FlgCatDiagOn = 0U;
  CatDiagCond = 0U;
  CntOBD2TstL2R = 0U;
  CntOBD2TstR2L = 0U;
  StLamOBD2DIag = 0U;

  /* states (dwork) */
  (void) memset((void *)&lambdamgm_DWork, 0,
                sizeof(D_Work_lambdamgm_T));

  /* SystemInitialize for Function Call SubSystem: '<S2>/T5ms' */
  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
  lambdamgm_T5ms_Init();

  /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_NoSync' */
  /* End of SystemInitialize for SubSystem: '<S2>/T5ms' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
void LambdaMgm_Init(void)
{
  lambdamgm_initialize();
  lambdamgm_Lambda_T5ms();
}

void LambdaMgm_NoSync(void)
{
  lambdamgm_Lambda_T5ms();
}

void LambdaMgm_T5ms(void)
{
  uint64_T ticksTimer;
  uint64_T msTimer;
  TIMING_GetAbsTimer(&(ticksTimer));
  TIMING_TicksToMilliSeconds(ticksTimer, &msTimer);
  LambdaMgmTimer = (uint32_T)(msTimer & 0x00000000FFFFFFFF);
  lambdamgm_Lambda_T5ms();
}

#else                                  // _BUILD_LAMBDAMGM_

void LambdaMgm_Init(void);
void LambdaMgm_NoSync(void);
void LambdaMgm_T5ms(void);

//uscite
uint16_T VLambda;
uint8_T VLambdaState;
void LambdaMgm_Init(void)
{
  VLambda = 0;
  VLambdaState = VLAMINIT;
}

void LambdaMgm_NoSync(void)
{
}

void LambdaMgm_T5ms(void)
{
}

#endif                                 // _BUILD_LAMBDAMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
