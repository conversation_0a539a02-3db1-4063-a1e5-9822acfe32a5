/*******************************************************************
 *
 *    DESCRIPTION:
 *
 *    AUTHOR:
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _KNOCK_CORR_H_
#define _KNOCK_CORR_H_

/** public data **/
extern int16_T SAKnock[8];             /* Knock correction on Spark Advance */
extern uint8_T FlgCntKnockCohInc[8];
extern  uint8_T KCohDiagCnt[8];

/** public functions **/
extern void KnockCorr_Init(void);
extern void KnockCorr_NoSync(void);
extern void KnockCorr_EOA(void);
extern void KnockCorr_T10ms(void);

#endif
