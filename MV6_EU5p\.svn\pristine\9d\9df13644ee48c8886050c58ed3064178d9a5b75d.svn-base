/*****************************************************************************************************************/
/* To be updated only in model's SVN                                                                             */
/* $HeadURL: file:///E:/Archivi/SVN_Repository/Device_Drivers/DBWMGM/main_trunk/DbwMgm_ert_rtw/DbwMgm_private.h $ */
/* $Description:  $ */
/* $Revision: 5140 $ */
/* $Date: 2012-10-29 12:10:55 +0100 (lun, 29 ott 2012) $ */
/* $Author: lanal $ */
/*****************************************************************************************************************/
/*
 * File: DbwMgm_private.h
 *
 * Real-Time Workshop code generated for Simulink model DbwMgm.
 *
 * Model version                        : 1.705
 * Real-Time Workshop file version      : 7.2  (R2008b)  04-Aug-2008
 * Real-Time Workshop file generated on : Wed Oct 24 13:36:13 2012
 * TLC version                          : 7.2 (Aug  5 2008)
 * C/C++ source code generated on       : Wed Oct 24 13:36:15 2012
 */
#ifndef RTW_HEADER_DbwMgm_private_h_
#define RTW_HEADER_DbwMgm_private_h_
#include "rtwtypes.h"

/* Includes for objects with custom storage classes. */
#include "Throttle_Target.h"
#include "ThrPosMgm.h"
#include "diagmgm_out.h"
#include "ActiveDiag.h"
#include "SelfMgm.h"
#include "Digitalin.h"
#include "recmgm.h"
#include "Syncmgm.h"
#include "Saf2Mgm.h"
#include "null.h"
#include "Saf3_mgm.h"
#include "Analogin.h"
#define CALL_EVENT                     (MAX_uint8_T)
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error "Code was generated for compiler with different sized uchar/char. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error "Code was generated for compiler with different sized ushort/short. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized uint/int. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error "Code was generated for compiler with different sized ulong/long. Consider adjusting Emulation Hardware word size settings on the Hardware Implementation pane to match your compiler word sizes as defined in the compilers limits.h header file. Alternatively, you can select 'None' for Emulation Hardware and select the 'Enable portable word sizes' option for ERT based targets, this will disable the preprocessor word size checks."
#endif

#ifndef __RTWTYPES_H__
#error This file requires rtwtypes.h to be included
#else
#ifdef TMWTYPES_PREVIOUSLY_INCLUDED
#error This file requires rtwtypes.h to be included before tmwtypes.h
#else

/* Check for inclusion of an incorrect version of rtwtypes.h */
#ifndef RTWTYPES_ID_C08S16I32L32N32F0
#error This code was generated with a different "rtwtypes.h" than the file included
#endif                                 /* RTWTYPES_ID_C08S16I32L32N32F0 */
#endif                                 /* TMWTYPES_PREVIOUSLY_INCLUDED */
#endif                                 /* __RTWTYPES_H__ */

/* Imported (extern) block parameters */
extern uint16_T DBWTIMEOUT;            /* Variable: DBWTIMEOUT
                                        * '<S78>/DBWTIMEOUT'
                                        * DBW switch-off timeout when engine is off
                                        */
extern int16_T FORCEDBWVOUT;           /* Variable: FORCEDBWVOUT
                                        * '<S16>/DbwMgm'
                                        * Manual DBW output command voltage
                                        */
extern int16_T VDBWOUTDEC;             /* Variable: VDBWOUTDEC
                                        * Referenced by blocks:
                                        * '<S23>/VDBWOUTDEC'
                                        * '<S26>/VDBWOUTDEC'
                                        * (SR) VDbwOut decremental step for REC_FORCE_LH
                                        */
extern int16_T VDBWOUTINC;             /* Variable: VDBWOUTINC
                                        * Referenced by blocks:
                                        * '<S23>/VDBWOUTINC'
                                        * '<S26>/VDBWOUTINC'
                                        * (SR) VDbwOut incremental step for REC_FORCE_LH
                                        */
extern int16_T VTDBWFEEDFWD[6];        /* Variable: VTDBWFEEDFWD
                                        * '<S29>/VTDBWFEEDFWD'
                                        * Dbw output feedforward voltage vector
                                        */
extern int16_T ANGTHRLHDB;             /* Variable: ANGTHRLHDB
                                        * '<S35>/ANGTHRLHDB'
                                        * Center value limp-home zone for dead band calculation
                                        */
extern int16_T ANGTHRMAXDB;            /* Variable: ANGTHRMAXDB
                                        * '<S35>/ANGTHRMAXDB'
                                        * Center value max opening zone for dead band calculation
                                        */
extern int16_T ANGTHRMINDB;            /* Variable: ANGTHRMINDB
                                        * '<S35>/ANGTHRMINDB'
                                        * Center value min opening zone for dead band calculation
                                        */
extern int16_T BKANGTHRERR[9];         /* Variable: BKANGTHRERR
                                        * '<S29>/BKANGTHRERR'
                                        * BK Throttle angle error at current time step
                                        */
extern int16_T BKANGTHROBJ[6];         /* Variable: BKANGTHROBJ
                                        * '<S29>/BKANGTHROBJ'
                                        * AngThrObj breakpoint vector for VTDBWFEEDFWD
                                        */
extern int16_T HITHRANGSLOW;           /* Variable: HITHRANGSLOW
                                        * '<S59>/HITHRANGSLOW'
                                        * Lower throttle angle target threshold for min. slope selection
                                        */
extern int16_T LOTHRANGSLOW;           /* Variable: LOTHRANGSLOW
                                        * '<S59>/LOTHRANGSLOW'
                                        * Lower throttle angle target threshold for min. slope selection
                                        */
extern int16_T MAXANGINTERR;           /* Variable: MAXANGINTERR
                                        * Referenced by blocks:
                                        * '<S32>/MAXANGINTERR'
                                        * '<S33>/MAXANGINTERR'
                                        * Max. Throttle Angle integral error for diagnosis
                                        */
extern int16_T MAXANGLHDB;             /* Variable: MAXANGLHDB
                                        * '<S35>/MAXANGLHDB'
                                        * Max. limp-home zone for dead band calculation
                                        */
extern int16_T MAXANGMAXDB;            /* Variable: MAXANGMAXDB
                                        * '<S35>/MAXANGMAXDB'
                                        * Max. max opening zone for dead band calculation
                                        */
extern int16_T MAXANGMINDB;            /* Variable: MAXANGMINDB
                                        * '<S35>/MAXANGMINDB'
                                        * Max. min opening zone for dead band calculation
                                        */
extern int16_T MAXANGTHRERR;           /* Variable: MAXANGTHRERR
                                        * '<S32>/MAXANGTHRERR'
                                        * Max. Throttle Angle error for diagnosis
                                        */
extern int16_T MINANGTHRERR;           /* Variable: MINANGTHRERR
                                        * Referenced by blocks:
                                        * '<S43>/MINANGTHRERR'
                                        * '<S44>/MINANGTHRERR'
                                        * '<S45>/MINANGTHRERR'
                                        * Min. AngThrErr dead zone value
                                        */
extern int16_T VTMAXANGSLOPE[5];       /* Variable: VTMAXANGSLOPE
                                        * '<S59>/VTMAXANGSLOPE'
                                        * Max throttle angle target slope near to UMS
                                        */
extern int16_T VTMAXANGSLOPENEG[5];    /* Variable: VTMAXANGSLOPENEG
                                        * '<S59>/VTMAXANGSLOPENEG'
                                        * Max. throttle angle target slope negative
                                        */
extern int16_T VTMAXANGSLOPEPOS[5];    /* Variable: VTMAXANGSLOPEPOS
                                        * '<S59>/VTMAXANGSLOPEPOS'
                                        * Max. throttle angle target slope positive
                                        */
extern int16_T VTMINANGSLOPE[5];       /* Variable: VTMINANGSLOPE
                                        * '<S59>/VTMINANGSLOPE'
                                        * Max throttle angle target slope near to LMS
                                        */
extern uint16_T BKVBATANGSLOPE[5];     /* Variable: BKVBATANGSLOPE
                                        * Referenced by blocks:
                                        * '<S30>/BKVBATANGSLOPE'
                                        * '<S59>/BKVBATANGSLOPE'
                                        * VBattery vector for VTMAX/MinAngErr
                                        */
extern uint16_T MAXANGOBJ;             /* Variable: MAXANGOBJ
                                        * '<S58>/MAXANGOBJ'
                                        * Max. throttle angle target
                                        */
extern uint16_T MAXVDBWOUT;            /* Variable: MAXVDBWOUT
                                        * '<S29>/MAXVDBWOUT'
                                        * Max. Dbw output command voltage
                                        */
extern uint16_T MINANGOBJ;             /* Variable: MINANGOBJ
                                        * '<S58>/MINANGOBJ'
                                        * Min. throttle angle target
                                        */
extern uint16_T GAINKDDBW;             /* Variable: GAINKDDBW
                                        * '<S29>/GAINKDDBW'
                                        * PID regulator derivative gain factor
                                        */
extern uint16_T GAINKIDBW;             /* Variable: GAINKIDBW
                                        * '<S29>/GAINKIDBW'
                                        * PID regulator integral gain factor
                                        */
extern uint16_T VTGAINKPDBW[9];        /* Variable: VTGAINKPDBW
                                        * '<S29>/VTGAINKPDBW'
                                        * PID regulator proportional gain factor
                                        */
extern int16_T KFILTANGTHROBJ;         /* Variable: KFILTANGTHROBJ
                                        * '<S57>/KFILTANGTHROBJ'
                                        * AngThrObj filter constant
                                        */
extern int16_T VTKDBWDYNANEG[5];       /* Variable: VTKDBWDYNANEG
                                        * '<S30>/VTKDBWDYNANEG'
                                        * DBW dynamic constant for diagnosis in falling
                                        */
extern int16_T VTKDBWDYNAPOS[5];       /* Variable: VTKDBWDYNAPOS
                                        * '<S30>/VTKDBWDYNAPOS'
                                        * DBW dynamic constant for diagnosis in rising
                                        */
extern uint8_T DBWDELAY;               /* Variable: DBWDELAY
                                        * '<S30>/DBWDELAY'
                                        * DBW dynamic delay
                                        */
extern uint8_T DBWDISDIAGCALC;         /* Variable: DBWDISDIAGCALC
                                        * '<S22>/DBWDISDIAGCALC'
                                        * Diagnosis calculation when dbw is disabled
                                        */
extern uint8_T FLGFORCEDBWOUT;         /* Variable: FLGFORCEDBWOUT
                                        * Referenced by blocks:
                                        * '<S16>/DbwMgm'
                                        * '<S26>/FORCEDBWVOUT'
                                        * flag to force DBW Vout
                                        */
void DbwMgm_Init(void);
void DbwMgm_ForceLH(void);
void DbwMgm_ActiveDiag(void);
void DbwMgm_SelfLearning(void);
void DbwMgm_Disabled(void);
void DbwMgm_TestDisLoads(void);
void DbwMgm_ErrorPositionDiagnosis(void);
void DbwMgm_minangerr_select(int16_T rtu_In2, int16_T rtu_In3, int16_T rtu_In1,
  int16_T rtu_In4, rtB_minangerr_select_DbwMgm *localB);
void DbwMgm_minangerr_select1(int16_T rtu_In2, int16_T rtu_In3, int16_T rtu_In1,
  int16_T rtu_In4, rtB_minangerr_select1_DbwMgm *localB);
void DbwMgm_PID_Regulator(void);
void DbwMgm_SysDynamics(void);
void DbwMgm_Target_Calculation(void);
void DbwMgm_ClosedLoop(void);
void DbwMgm_TestCond_Init(void);
void DbwMgm_TestCond(void);
void DbwMgm_HBLogicEnable(void);
void DbwMgm_T5ms_Init(void);
void DbwMgm_T5ms(void);

#endif                                 /* RTW_HEADER_DbwMgm_private_h_ */

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
