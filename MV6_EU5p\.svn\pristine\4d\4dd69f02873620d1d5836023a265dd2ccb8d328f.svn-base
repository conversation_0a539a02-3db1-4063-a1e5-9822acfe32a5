/** #############################################################################
**     Filename  : SCI.C
**     Project   : ....
**     Processor : MPC5554
**        
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 27/06/2005, 14.25
**     Abstract  :
**          This file implements the Enhanced Serial Communication Interface 
**          (eSCI_A and eSCI_B) module initialization according to the 
**          Functional Specification "PAEO0001_17_SCI.doc".
**     Settings  :
**          Device                                         : eSCI_A/eSCI_B
**          Communication mode                             : SCI
**          Baud rate divisor = 4, SCI baud rate = 2000000 Bd/s
**          Data format                                    : 8 bits
**          Parity                                         : even
**          Wake up                                        : by idle line
**          RxD pin                                        : Enabled
**          TxD pin                                        : Enabled
**          Combined Interrupt 
**            Interrupt                                    : INT_ESCI_A/INT_ESCI_B
**            Interrupt priority                           : 12/12
**            Transmission complete                        : Disabled
**            Receiver full                                : Disabled
** ##############################################################################*/

/* Start SCI Module */
#include "OS_api.h"
#include "tasksdefs.h"

#include "typedefs.h"
#include "task.h"
#include "SCI.h"


#ifdef _BUILD_SCI_


#define SCI_CHAN_NUM  2

#define SCI_MAX_SHIFT_LEFT 8

#define SCI_PARITY_MASK_AND  0x0001


typedef union {
        struct {
          uint8_t    SCI_exEnableTxComplete   : 1;    
          uint8_t    SCI_exEnableRxComplete   : 1;    
          uint8_t    SCI_exEnableOverrunError : 1;      
          uint8_t    SCI_Dummy                : 5;
        } B;
        uint8_t R;
 } sci_status_t;
 
typedef struct 
{
  TaskType  ExStopTxTaskID;
  TaskType  ExStopRxTaskID;
  TaskType  ExOverrunErrorTaskID;
} sci_tasks_t;

sci_status_t    SCI_exStatus[SCI_CHAN_NUM];

/* Pointer to the SCI Push Registers */
const vuint32_t DSCI_PUSHR[]=
{
    DSCIA_PUSHR,
    DSCIB_PUSHR
};

static vuint32_t DSCI_TX_BUFFER_A [SCI_LIN_TX_BUFFER_SIZE];
static vuint32_t DSCI_RX_BUFFER_A [SCI_LIN_RX_BUFFER_SIZE];
static vuint32_t DSCI_TX_BUFFER_B [SCI_LIN_TX_BUFFER_SIZE];
static vuint32_t DSCI_RX_BUFFER_B [SCI_LIN_RX_BUFFER_SIZE];

uint8_t  SCI_ConfigurationStatus;

uint16_t SCI_rxData[SCI_CHAN_NUM];

uint8_t  SCI_parityBit[SCI_CHAN_NUM];

uint8_t  SCI_flagOverrun[SCI_CHAN_NUM];

sci_tasks_t SCI_IsrTasks[SCI_CHAN_NUM];

/* SCI LIN */
uint8_t  SCI_LINConfigurationStatus;

/* dummy word to clean the warning cases */
extern uint32_t  dummy32;
/*
** ===================================================================
**     Method      :  SCI_Config(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to initialize the module.
**     Parameters  : chIdentifier
**                      channel A or B
**                   line_dir
**                      direction of trasmission RX or TX
**     Returns     : SCI Error Code
** ===================================================================
*/

int16_t SCI_ControlStatus(uint8_t chIdentifier)
{
    int16_t errorReturn = NO_ERROR;

    switch (chIdentifier) 
    {
        case SCI_CH_A:
            if((ESCI_A.SR.B.OR == 1) || (ESCI_A.SR.B.NF == 1) || (ESCI_A.SR.B.FE == 1) || (ESCI_A.SR.B.PF == 1) || (ESCI_A.SR.B.BERR == 1)
            || (ESCI_A.SR.B.PBERR == 1)|| (ESCI_A.SR.B.CERR == 1)|| (ESCI_A.SR.B.CKERR == 1))
            {
                SCI_Reset(SCI_CH_A,SCI_TX);
                errorReturn = SCI_DATA_REGISTER_FAULT;
            }
        break;

        case SCI_CH_B:
        if((ESCI_B.SR.B.OR == 1) || (ESCI_B.SR.B.NF == 1) || (ESCI_B.SR.B.FE == 1) || (ESCI_B.SR.B.PF == 1) || (ESCI_B.SR.B.BERR == 1)
            || (ESCI_B.SR.B.PBERR == 1)|| (ESCI_B.SR.B.CERR == 1)|| (ESCI_B.SR.B.CKERR == 1))
        {
            SCI_Reset(SCI_CH_B,SCI_TX);
            errorReturn = SCI_DATA_REGISTER_FAULT;
        }                
        break;
        default:
        break;
    }
     
    return errorReturn;
}


int16_t SCI_Reset(uint8_t chIdentifier, uint8_t line_dir)
{
  int16_t errorReturn = NO_ERROR;

    SCI_exStatus[chIdentifier].R = 0;
    SCI_rxData[chIdentifier]= 0; 
    SCI_flagOverrun[chIdentifier] = 0;
    SCI_parityBit[chIdentifier] = 0; 

    switch (chIdentifier) 
    {
        case SCI_CH_A:
        switch (line_dir) 
        {
            case SCI_TX:
                if(!ESCI_A.SR.B.TDRE)
                {
                    ESCI_A.DR.B.D = 0xAC;
                }        
                ESCI_A.SR.R   = 0x7FFFBFFF; 
            break;
            case SCI_RX:
                dummy32= ESCI_A.DR.R;
                ESCI_A.SR.R   = 0x7FFFBFFF; 
                break;
                default:
            break;
        }          
        break;
        case SCI_CH_B:
        switch (line_dir) 
        {
            case SCI_TX:
                if(!ESCI_B.SR.B.TDRE)
                {
                    ESCI_B.DR.B.D = 0xAC;
                }        
                ESCI_B.SR.R   = 0x7FFFBFFF; 
            break;
            case SCI_RX:
                dummy32= ESCI_B.DR.R;
                ESCI_B.SR.R   = 0x7FFFBFFF;
            break;
                default:
                break;
        }          
        break;
        default:
        break;
    }          
  
  return errorReturn;
}

int16_t SCI_Config(void)
{
  int16_t errorReturn = PERIPHERAL_NOT_CONFIGURED;

    if (SCI_ConfigurationStatus ==0) 
    {
        SCI_ConfigurationStatus = 1;

#if(SCI_CH_A_ENABLED == 1)

        SCI_exStatus[SCI_CH_A].R = 0;
        SCI_rxData[SCI_CH_A]= 0; 
        SCI_flagOverrun[SCI_CH_A] = 0;
        SCI_parityBit[SCI_CH_A] = 0; 
         
        /******** eSCI_A Configuration ********/ 
        ESCI_A.CR2.R = SCI_ENABLE_MODULE; /* Enable eSCI module - MDIS = 0 */ 
             
        ESCI_A.CR1.B.SBR   = SCIA_SBR_BAUD_RATE(SCIA_BAUD_RATE);       /* SBR   = 0xXXXX */
        ESCI_A.CR1.B.LOOPS = SCIA_LOOPS_DISABLE;       /* LOOPS = 0   */
        ESCI_A.CR1.B.RSRC  = SCIA_RSRC_DISABLE;        /* RSRC  = 0   */
        ESCI_A.CR1.B.M     = SCIA_BIT_NUMBER;           /* M     = 0/1 - 8bit/9bit  */
        ESCI_A.CR1.B.WAKE  = SCIA_WAKE_DISABLE;        /* WAKE  = 0   */
        ESCI_A.CR1.B.ILT   = SCIA_ILT;                 /* ILT   = 1   */
        ESCI_A.CR1.B.PE    = SCIA_PARITY;              /* PE    = 0/1 - DISABLE/ENABLE   */
        ESCI_A.CR1.B.PT    = SCIA_PARITY_TYPE;         /* PT    = 0/1 - EVEN/ODD         */
        ESCI_A.CR1.B.TIE   = SCIA_TIE_DISABLE;          /* TIE   = 0   */
        ESCI_A.CR1.B.TCIE  = SCIA_TCIE_ENABLE;         /* TCIE  = 1   */
        ESCI_A.CR1.B.RIE   = SCIA_RIE_ENABLE;          /* RIE   = 1   */
        ESCI_A.CR1.B.ILIE  = SCIA_ILIE_DISABLE;        /* ILIE  = 0   */
        ESCI_A.CR1.B.TE    = SCIA_TE_ENABLE;            /* TE    = 1   */
        ESCI_A.CR1.B.RE    = SCIA_RE_ENABLE;            /* RE    = 1   */
        ESCI_A.CR1.B.RWU   = SCIA_RWU_DISABLE;          /* RWU   = 0   */ 
        ESCI_A.CR1.B.SBK   = SCIA_SBK_DISABLE;          /* SBK   = 0   */ 
        /* ESCIA_CR2: MDIS=0,FBR=0,BSTP=1,IEBERR=0,RXDMA=0,TXDMA=0,BRK13=0,BESM13=0,SBSTP=1,ORIE=1,NFIE=1,FEIE=1,PFIE=1 */       
         
        ESCI_A.CR2.B.ORIE = 0x01; /* ORIE=1,NFIE=0,FEIE=0,PFIE=0 */
         
             
        /* ESCIA_LCR: LRES=0,WU=0,WUD=0,LDBG=0,DSF=0,PRTY=0,LIN=0,RXIE=0,TXIE=0,WUIE=0,STIE=0,PBIE=0,CIE=0,CKIE=0,FCIE=0,OFIE=0 */
        ESCI_A.LCR.R = 0x00; /* LIN Register - LIN = 0 - eSCI module in SCI mode */

        /* ESCIA_LPR: P15=1,P14=1,P13=0,P12=0,P11=0,P10=1,P9=0,P8=1,P7=1,P6=0,P5=0,P4=1,P3=1,P2=0,P1=0,P0=1 */
        ESCI_A.LPR.R = 0xC5990000; 
         
        dummy32 = ESCI_A.SR.R;      /* clear SR */
        dummy32 = ESCI_A.DR.R;
         
        /********** SIU Configuration *********/ 

        /* SCIA pin configuration */ 
        SIU.PCR[TXDA].B.PA  = SCIA_SIU_PA_CONFIG; /* SIU_PCR89: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
        SIU.PCR[TXDA].B.OBE = SCIA_SIU_OBE_CONFIG;
        SIU.PCR[TXDA].B.IBE = SCIA_SIU_IBE_CONFIG;
        SIU.PCR[TXDA].B.ODE = SCIA_SIU_ODE_CONFIG;
        SIU.PCR[TXDA].B.HYS = SCIA_SIU_HYS_CONFIG;
        SIU.PCR[TXDA].B.SRC = SCIA_SIU_SRC_CONFIG;
        SIU.PCR[TXDA].B.WPE = SCIA_SIU_WPE_CONFIG;
        SIU.PCR[TXDA].B.WPS = SCIA_SIU_WPS_CONFIG;

        SIU.PCR[RXDA].B.PA  = SCIA_SIU_PA_CONFIG; /* SIU_PCR90: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
        SIU.PCR[RXDA].B.OBE = SCIA_SIU_OBE_CONFIG;
        SIU.PCR[RXDA].B.IBE = SCIA_SIU_IBE_CONFIG;
        SIU.PCR[RXDA].B.ODE = SCIA_SIU_ODE_CONFIG;
        SIU.PCR[RXDA].B.HYS = SCIA_SIU_HYS_CONFIG;
        SIU.PCR[RXDA].B.SRC = SCIA_SIU_SRC_CONFIG;
        SIU.PCR[RXDA].B.WPE = SCIA_SIU_WPE_CONFIG;
        SIU.PCR[RXDA].B.WPS = SCIA_SIU_WPS_CONFIG;
#endif /* SCI_CH_A_ENABLED == 1 */
         
                 
#if(SCI_CH_B_ENABLED == 1)

        SCI_exStatus[SCI_CH_B].R = 0;
        SCI_rxData[SCI_CH_B]= 0; 
        SCI_flagOverrun[SCI_CH_B] = 0;
        SCI_parityBit[SCI_CH_B] = 0; 

        /******** eSCI_B Configuration ********/ 
        ESCI_B.CR2.R = SCI_ENABLE_MODULE;/* Enable eSCI module - MDIS = 0 */ 
                     
        ESCI_B.CR1.B.SBR   = SCIB_SBR_BAUD_RATE(SCIB_BAUD_RATE);       /* SBR   = 0xXXXX */
        ESCI_B.CR1.B.LOOPS = SCIB_LOOPS_DISABLE;       /* LOOPS = 0   */
        ESCI_B.CR1.B.RSRC  = SCIB_RSRC_DISABLE;        /* RSRC  = 0   */
        ESCI_B.CR1.B.M     = SCIB_BIT_NUMBER;           /* M     = 0/1 - 8bit/9bit       */
        ESCI_B.CR1.B.WAKE  = SCIB_WAKE_DISABLE;        /* WAKE  = 0   */
        ESCI_B.CR1.B.ILT   = SCIB_ILT;                 /* ILT   = 1   */
        ESCI_B.CR1.B.PE    = SCIB_PARITY;              /* PE    = 0/1 - DISABLE/ENABLE   */
        ESCI_B.CR1.B.PT    = SCIB_PARITY_TYPE;         /* PT    = 0/1 - EVEN/ODD         */
        ESCI_B.CR1.B.TIE   = SCIB_TIE_DISABLE;          /* TIE   = 0   */
        ESCI_B.CR1.B.TCIE  = SCIB_TCIE_ENABLE;         /* TCIE  = 1   */
        ESCI_B.CR1.B.RIE   = SCIB_RIE_ENABLE;           /* RIE   = 1   */
        ESCI_B.CR1.B.ILIE  = SCIB_ILIE_DISABLE;        /* ILIE  = 0   */
        ESCI_B.CR1.B.TE    = SCIB_TE_ENABLE;            /* TE    = 1   */
        ESCI_B.CR1.B.RE    = SCIB_RE_ENABLE;            /* RE    = 1   */
        ESCI_B.CR1.B.RWU   = SCIB_RWU_DISABLE;          /* RWU   = 0   */ 
        ESCI_B.CR1.B.SBK   = SCIB_SBK_DISABLE;          /* SBK   = 0   */
         
        /* ESCIB_CR2: MDIS=0,FBR=0,BSTP=1,IEBERR=0,RXDMA=0,TXDMA=0,BRK13=0,BESM13=0,SBSTP=1,ORIE=1,NFIE=1,FEIE=1,PFIE=1 */
         
        ESCI_B.CR2.B.ORIE = 0x01;  /* ORIE=1,NFIE=0,FEIE=0,PFIE=0 */
         
         
        /* ESCIB_LCR: LRES=0,WU=0,WUD=0,LDBG=0,DSF=0,PRTY=0,LIN=0,RXIE=0,TXIE=0,WUIE=0,STIE=0,PBIE=0,CIE=0,CKIE=0,FCIE=0,OFIE=0 */
        ESCI_B.LCR.R = 0x00;  

        /* ESCIB_LPR: P15=1,P14=1,P13=0,P12=0,P11=0,P10=1,P9=0,P8=1,P7=1,P6=0,P5=0,P4=1,P3=1,P2=0,P1=0,P0=1 */
        ESCI_B.LPR.R = 0xC5990000;
                     
        dummy32 = ESCI_B.SR.R;      /* clear SR */
        dummy32 = ESCI_B.DR.R;
             
         /********** SIU Configuration *********/ 

        /* SCIB pin configuration */ 
        SIU.PCR[TXDB_PCSD1].B.PA  = SCIB_SIU_PA_CONFIG; /* SIU_PCR91: PA=3,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
        SIU.PCR[TXDB_PCSD1].B.OBE = SCIB_SIU_OBE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.IBE = SCIB_SIU_IBE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.ODE = SCIB_SIU_ODE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.HYS = SCIB_SIU_HYS_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.SRC = SCIB_SIU_SRC_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.WPE = SCIB_SIU_WPE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.WPS = SCIB_SIU_WPS_CONFIG;

        SIU.PCR[RXDB_PCSD5].B.PA  = SCIB_SIU_PA_CONFIG; /* SIU_PCR92: PA=3,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
        SIU.PCR[RXDB_PCSD5].B.OBE = SCIB_SIU_OBE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.IBE = SCIB_SIU_IBE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.ODE = SCIB_SIU_ODE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.HYS = SCIB_SIU_HYS_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.SRC = SCIB_SIU_SRC_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.WPE = SCIB_SIU_WPE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.WPS = SCIB_SIU_WPS_CONFIG;
         /***************************************/
#endif /* SCI_CH_B_ENABLED == 1 */
          
        errorReturn = NO_ERROR;
    } 
    else 
    {
        errorReturn = PERIPHERAL_ALREADY_CONFIGURED;
    } /* End ConfigurationStatus */

    return errorReturn;
} /* End SCI_Config method */

int16_t SCI_SetIsrTasks(uint8_t chIdentifier, TaskType stopTx, TaskType stopRx, TaskType overrunError)
{
  SCI_IsrTasks[chIdentifier].ExStopTxTaskID = stopTx;
  SCI_IsrTasks[chIdentifier].ExStopRxTaskID = stopRx;
  SCI_IsrTasks[chIdentifier].ExOverrunErrorTaskID = overrunError;
  
  return NO_ERROR;
}

/*
** ===========================================================================
**     Method      :  SCI_TxEnable(...)
**
**     Description :
**         This method enables the transmitter of the eSCI peripherals(A and B)
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to enable the A or B transmitter
**         module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===========================================================================
*/

int16_t SCI_TxEnable(uint8_t chIdentifier, uint8_t txSwitch)
{
  int16_t errorReturn = SCI_PARAMETER_NOT_OK;
  
    if (SCI_ConfigurationStatus == 1) 
    {
        switch (chIdentifier) 
        {
            case SCI_CH_A:
                if (txSwitch == TX_ENABLE) 
                { 
                    ESCI_A.CR1.B.TE = 1;     /* Transmitter enabled */
                } 
                else 
                {
                    ESCI_A.CR1.B.TE = 0;     /* Transmitter disabled */
                }
                errorReturn = NO_ERROR;       
            break;

            case SCI_CH_B:
                if (txSwitch == TX_ENABLE) 
                {
                    ESCI_B.CR1.B.TE = 1;    /* Transmitter enabled */ 
                } 
                else 
                {
                    ESCI_B.CR1.B.TE = 0;    /* Transmitter disabled */
                }

                errorReturn = NO_ERROR;    
          break;
          default:
            break;       
  
        }  /* End switch (chIdentifier) */
    } 
    else 
    {
        errorReturn = PERIPHERAL_NOT_CONFIGURED;
    }
   
    return errorReturn;

} /* End SCI_TxEnable method */


/*
** ==========================================================================
**     Method      :  SCI_RxEnable(...)
**
**     Description :
**         This method enables receiver of the eSCI peripherals(A and B)
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to enable the A or B receiver 
**         module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_RxEnable(uint8_t chIdentifier, uint8_t rxSwitch)
{
    int16_t errorReturn = SCI_PARAMETER_NOT_OK;

    if (SCI_ConfigurationStatus == 1) 
    {
        switch (chIdentifier) 
        {
            case SCI_CH_A:
                if (rxSwitch == RX_ENABLE) 
                {
                    ESCI_A.CR1.B.RE = 1;   /* Receiver enabled */ 
                } 
                else 
                {
                    ESCI_A.CR1.B.RE = 0;     /* Receiver disabled */
                }

                errorReturn = NO_ERROR;       
            break;

            case SCI_CH_B:
                if (rxSwitch == RX_ENABLE) 
                {
                    ESCI_B.CR1.B.RE = 1;   /* Transmitter enabled */ 
                } 
                else 
                {
                    ESCI_B.CR1.B.RE = 0;     /* Transmitter disabled */
                }

                errorReturn = NO_ERROR;    
            break;       
            default:
            break;
        }  /* End switch (chIdentifier) */
    } 
    else 
    {
        errorReturn = PERIPHERAL_NOT_CONFIGURED;
    }

    return errorReturn;

} /* End SCI_RxEnable method */


/*
** ==========================================================================
**     Method      :  SCI_TxData(...)
**
**     Description :
**         This method performs the data transmission through the eSCI modules
**         (A and B) according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to transmit data.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_TxData(uint8_t chIdentifier, uint16_t txData)
{
    int16_t errorReturn = SCI_PARAMETER_NOT_OK;
    uint16_t data;
    uint16_t dataT8;

    if (SCI_ConfigurationStatus == 1) 
    {

        switch (chIdentifier) 
        {
            case SCI_CH_A:
                if (ESCI_A.CR1.B.TE == 1) 
                {      
                    /* Transmitter enabled? */
                    if ((ESCI_A.SR.B.TDRE == 1)/*&&(ESCI_A.SR.B.FE != 1)*/) 
                    {   /* Data Register empty? */                  
                        if (txData <= SCIA_RX_DATA_MASK) 
                        {
                            dataT8 = txData & MASK_T8_BIT_AND;
                            data = (txData & 0xff) + (dataT8 << 6);                  

                            ESCI_A.DR.R = data;                               
                            errorReturn = NO_ERROR;
                        } 
                        else 
                        {
                            errorReturn = SCI_PARAMETER_NOT_OK;
                        }
                    } 
                    else 
                    {
                        errorReturn = SCI_DATA_REGISTER_FAULT;
                    }
                } 
                else 
                {
                    errorReturn = SCI_TRANSMITTER_DISABLE;
                }
            break;

            case SCI_CH_B:
                if (ESCI_B.CR1.B.TE == 1) 
                {      
                    /* Transmitter enabled? */
                    if ((ESCI_B.SR.B.TDRE == 1)/*&&(ESCI_B.SR.B.FE != 1)*/) 
                    {   /* Data Register empty? */                   
                        if (txData <= SCIB_RX_DATA_MASK) 
                        {
                            dataT8 = txData & MASK_T8_BIT_AND;
                            data = (txData & 0xff) + (dataT8 << 6);

                            ESCI_B.DR.R = data;
                            errorReturn = NO_ERROR;
                        } 
                        else 
                        {  
                            errorReturn = SCI_PARAMETER_NOT_OK;
                        }
                    } 
                    else 
                    {
                    errorReturn = SCI_DATA_REGISTER_FAULT;
                    }
                }
                else 
                {
                    errorReturn = SCI_TRANSMITTER_DISABLE;
                }    
            break;
            default:
            break;
        } /* End switch(chIdentifier) */    
    } 
    else 
    {
        errorReturn = PERIPHERAL_NOT_CONFIGURED;
    } /* End Configured Status */

    return errorReturn;
} /* End SCI_TxData method */


/*
** ==========================================================================
**     Method      :  SCI_RxData(...)
**
**     Description :
**         This method performs the data read from the Data Register of the 
**         eSCI modules (A and B) according to the Functional Specification 
**         "PAEO001_17_SCI.doc"
**         Call this method in the user code to read from the Data Register the 
**         received data.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_RxData(uint8_t chIdentifier, uint16_t *rxData)
{
    int16_t  errorReturn = SCI_PARAMETER_NOT_OK;
    uint16_t data;
    uint16_t dataR8;  

    if (SCI_ConfigurationStatus == 1) 
    {
        switch (chIdentifier) 
        {
            case SCI_CH_A:
                if (ESCI_A.CR1.B.RE == 1)
                {                /* Receiver enabled? */

                    if (SCI_flagOverrun[SCI_CH_A] == 0)
                    {    /* Data Register Empty? */
                        *rxData = 0xFFFF;               
                        errorReturn = SCI_DATA_REGISTER_FAULT;
                    }
                    else 
                    {                                 /* Data Register Full */

                        data = SCI_rxData[chIdentifier];       /* read data */
                                    
                        dataR8 = data & MASK_R8_BIT_AND;       /* get 9th bit */
                        data = (data & 0x00ff) + (dataR8 >> 7);

                        if (SCI_parityBit[SCI_CH_A] == 0) 
                        {               
                            errorReturn = NO_ERROR;               
                        } 
                        else 
                        {                  
                            errorReturn = SCI_PARITY_ERROR;                
                        }
                        data &= SCIA_RX_DATA_MASK;    /* Clear parity bit from the Rx Data */

                        *rxData = data;               /* Read Received Data */                    
                        SCI_flagOverrun[SCI_CH_A] = 0;                                              
                    } /* End Else - SCI_flagOverrun[SCI_CH_A] */ 
                }
                else 
                {
                    errorReturn = SCI_RECEIVER_DISABLE;
                }
            break;

            case SCI_CH_B:
                if (ESCI_B.CR1.B.RE == 1) 
                {                 /* Receiver enabled? */
                    if (SCI_flagOverrun[SCI_CH_B] == 0) 
                    {     /* Data Register Empty */
                        *rxData = 0xFFFF;                   
                        errorReturn = SCI_DATA_REGISTER_FAULT;
                    }
                    else 
                    {                                  /* Data Register Full */

                        data = SCI_rxData[chIdentifier];        /* read data */
                                    
                        dataR8 = data & MASK_R8_BIT_AND;         /* get 9th bit */
                        data = (data & 0x00ff) + (dataR8 >> 7);

                        if (SCI_parityBit[SCI_CH_B] == 0) 
                        {               
                            errorReturn = NO_ERROR;               
                        } 
                        else 
                        {                  
                            errorReturn = SCI_PARITY_ERROR;                 
                        }

                        data &= SCIB_RX_DATA_MASK;     /* Clear parity bit from the Rx Data */

                        *rxData = data;                /* Read Received Data */                 
                        SCI_flagOverrun[SCI_CH_B] = 0;             
                    } /* End Else - SCI_flagOverrun[SCI_CH_B] */ 
                } 
                else 
                {
                    errorReturn = SCI_RECEIVER_DISABLE;
                }
            break;
            default:
            break;
        } /* End switch(chIdentifier) */    
    } 
    else 
    {
        errorReturn = PERIPHERAL_NOT_CONFIGURED;
    } /* End Configured Status */

    return errorReturn;      

} /* End SCI_RxData method */


/*
** ==========================================================================
**     Method      :  SCI_ProgramException(...)
**
**     Description :
**         This method performs the exception activation/deactivation of a 
**         particular exception of the eSCI modules (A and B) according to the
**         Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to activate/deactivate an exception.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_ProgramException(uint8_t chIdentifier, uint8_t exceptionType, uint8_t exSwitch)
{
  int16_t errorReturn = SCI_PARAMETER_NOT_OK;

    if (SCI_ConfigurationStatus == 1) 
    {
        switch (exceptionType) 
        {
            case SCI_STOP_TX:
                SCI_exStatus[chIdentifier].B.SCI_exEnableTxComplete = exSwitch;
                errorReturn = NO_ERROR;
            break;
            case SCI_STOP_RX:
                SCI_exStatus[chIdentifier].B.SCI_exEnableRxComplete = exSwitch;   
                errorReturn = NO_ERROR;
            break;
            case SCI_OVERRUN_ERROR:
                SCI_exStatus[chIdentifier].B.SCI_exEnableOverrunError = exSwitch;
                errorReturn = NO_ERROR;
                break;
            default:
            break;
        } /* End switch(exceptionType) */                                      
    } 
    else 
    {
        errorReturn = PERIPHERAL_NOT_CONFIGURED;
    } /* End Configured Status */
  
    return errorReturn;
  
} /* End SCI_ProgramException method */ 


/*
** ==========================================================================
**     Method      :  SCI_ReadTxStatus(...)
**
**     Description :
**         This method checks the transmission status of the eSCI modules(a and B)
**         according to the Functional Specification "PAEO001_17_SCI.doc"
**         Call this method in the user code to activate/deactivate an exception.
**     Parameters  : None
**     Returns     : SCI Error Code
** ==========================================================================
*/

int16_t SCI_ReadTxStatus(uint8_t chIdentifier, uint8_t *returnStatus)
{
    int16_t errorReturn = SCI_PARAMETER_NOT_OK;

    *returnStatus = 0xFF;

    if (SCI_ConfigurationStatus == 1) 
    {
        switch(chIdentifier) 
        {
            case SCI_CH_A:
                if (ESCI_A.SR.B.TC == 1) 
                {
                    *returnStatus = SCI_TX_IDLE;        
                } 
                else 
                {
                    *returnStatus = SCI_TX_BUSY;
                }
                errorReturn = NO_ERROR;
            break;

            case SCI_CH_B:
                if (ESCI_B.SR.B.TC == 1) 
                {
                    *returnStatus = SCI_TX_IDLE;
                } 
                else 
                {
                    *returnStatus = SCI_TX_BUSY;
                }
                errorReturn = NO_ERROR;
            break;
            default:
                break;
        } /* End switch(chIdentifier) */
    } 
    else 
    {
        errorReturn = PERIPHERAL_NOT_CONFIGURED;
    }

    return errorReturn;
} /* End SCI_ReadTxStatus method */



/**********************************************/
/*        Interrupt Service Routine           */
/**********************************************/
#if 1
void SCI_A_INT_ISR(void) 
{
#if(SCI_CH_A_ENABLED == 1)
    if ((ESCI_A.SR.B.TDRE == 1) && (ESCI_A.SR.B.TC == 1)) 
    { /* Check for STOP_TX interrupt */               
        ESCI_A.SR.R = 0x40000000;                             /* Clear the TC bit */
        if (SCI_exStatus[SCI_CH_A].B.SCI_exEnableTxComplete == 1) 
        {
            ActivateTask(SCI_IsrTasks[SCI_CH_A].ExStopTxTaskID);
            SCI_exStatus[SCI_CH_A].B.SCI_exEnableTxComplete = 0;              
        }
    }
    if ((ESCI_A.SR.B.RDRF == 1) && (ESCI_A.SR.B.OR == 0)) 
    { /* Check for STOP_RX interrupt */
        ESCI_A.SR.R = 0x20000000;                             /* Clear RDRF bit */
        if (SCI_flagOverrun[SCI_CH_A] == 0) 
        {
            if (SCI_exStatus[SCI_CH_A].B.SCI_exEnableRxComplete == 1) 
            {
                SCI_rxData[SCI_CH_A] = ESCI_A.DR.R;                 /* Copy DR register */

                SCI_parityBit[SCI_CH_A] = ESCI_A.SR.B.PF;           /* Store the Parity Error Flag */
                ESCI_A.SR.R = 0x01000000;                           /* Clear PF bit */

                SCI_flagOverrun[SCI_CH_A] = 1;           
                ActivateTask(SCI_IsrTasks[SCI_CH_A].ExStopRxTaskID);
                SCI_exStatus[SCI_CH_A].B.SCI_exEnableRxComplete = 0;          
            }
            else
            {
                dummy32 = ESCI_A.DR.R;                                  /* Copy DR register */
            }
        } 
        else 
        {                                              /* Check for OVERRUN_ERROR interrupt */
            dummy32 = ESCI_A.DR.R;                                  /* Copy DR register */
            if (SCI_exStatus[SCI_CH_A].B.SCI_exEnableOverrunError == 1) 
            {
                ActivateTask(SCI_IsrTasks[SCI_CH_A].ExOverrunErrorTaskID);
                SCI_exStatus[SCI_CH_A].B.SCI_exEnableOverrunError = 0;
            }
        } 
    }
#endif /* SCI_CH_A_ENABLED == 1 */
#if(LIN_CH_A_ENABLED == 1)
    if (ESCI_A.SR.B.BERR)
    {   /* Bit Error */
        ESCI_A.SR.B.BERR = 1;
    }
    if (ESCI_A.SR.B.STO)
    {   /* Slave Timout */
        ESCI_A.SR.B.STO = 1;
    }
    if (ESCI_A.SR.B.PBERR)
    {   /* Physical Bus Error */
        ESCI_A.SR.B.PBERR = 1;
    }
    if (ESCI_A.SR.B.CERR)
    {   /* CRC Error */
        ESCI_A.SR.B.CERR= 1;
    }
    if (ESCI_A.SR.B.CKERR)
    {   /* Checksum Error */
        ESCI_A.SR.B.CKERR = 1;
    }
    if (ESCI_A.SR.B.OVFL)
    {   /* LIN rx register overflow error */
        ESCI_A.SR.B.OVFL = 1;
    }

#endif
} /* End SCI_A_INT_ISR */
#endif

void SCI_B_INT_ISR(void) 
{
#if(SCI_CH_B_ENABLED == 1)
    if ((ESCI_B.SR.B.TDRE == 1) && (ESCI_B.SR.B.TC == 1)) 
    { /* Check for STOP_TX interrupt */
        ESCI_B.SR.R = 0x40000000;                             /* Clear the TC bit */
        if (SCI_exStatus[SCI_CH_B].B.SCI_exEnableTxComplete == 1) 
        {
            ActivateTask(SCI_IsrTasks[SCI_CH_B].ExStopTxTaskID);
            SCI_exStatus[SCI_CH_B].B.SCI_exEnableTxComplete = 0;                
        }
    }
    if ((ESCI_B.SR.B.RDRF == 1) && (ESCI_B.SR.B.OR == 0)) 
    { /* Check for STOP_RX interrupt */
        ESCI_B.SR.R = 0x20000000;                             /* Clear RDRF bit */
        if (SCI_flagOverrun[SCI_CH_B] == 0) 
        {
            if (SCI_exStatus[SCI_CH_B].B.SCI_exEnableRxComplete == 1) 
            {
                SCI_rxData[SCI_CH_B] = ESCI_B.DR.R;                 /* Copy DR register */

                SCI_parityBit[SCI_CH_B] = ESCI_B.SR.B.PF;           /* Store the Parity Error Flag */
                ESCI_B.SR.R = 0x01000000;                           /* Clear PF bit */       

                SCI_flagOverrun[SCI_CH_B] = 1;           
                ActivateTask(SCI_IsrTasks[SCI_CH_B].ExStopRxTaskID);
                SCI_exStatus[SCI_CH_B].B.SCI_exEnableRxComplete = 0;          
            }
            else
            {
                dummy32 = ESCI_B.DR.R;                                  /* Copy DR register */
            }
        } 
        else 
        {                                              /* Check for OVERRUN_ERROR interrupt */
            dummy32 = ESCI_B.DR.R;                                  /* Copy DR register */
            if (SCI_exStatus[SCI_CH_B].B.SCI_exEnableOverrunError == 1) 
            {
                ActivateTask(SCI_IsrTasks[SCI_CH_B].ExOverrunErrorTaskID);
                SCI_exStatus[SCI_CH_B].B.SCI_exEnableOverrunError = 0;
            }
        } 
      
    }
#endif /* SCI_CH_B_ENABLED == 1 */
#if(LIN_CH_B_ENABLED == 1)
    if (ESCI_B.SR.B.BERR)
    {   /* Bit Error */
        ESCI_B.SR.B.BERR = 1;
    }
    if (ESCI_B.SR.B.STO)
    {   /* Slave Timout */
        ESCI_B.SR.B.STO = 1;
    }
    if (ESCI_B.SR.B.PBERR)
    {   /* Physical Bus Error */
        ESCI_B.SR.B.PBERR = 1;
    }
    if (ESCI_B.SR.B.CERR)
    {   /* CRC Error */
        ESCI_B.SR.B.CERR= 1;
    }
    if (ESCI_B.SR.B.CKERR)
    {   /* Checksum Error */
        ESCI_B.SR.B.CKERR = 1;
    }
    if (ESCI_B.SR.B.OVFL)
    {   /* LIN rx register overflow error */
        ESCI_B.SR.B.OVFL = 1;
    }

#endif
} /* End SCI_B_INT_ISR */

          

int16_t SCI_Config_Baud(uint8_t chIdentifier,uint32_t baud)
{
    int16_t errorReturn = NO_ERROR;
    uint8_t numSciChan;
    uint32_t baud_rate;

    if (SCI_ConfigurationStatus == 1) 
    {
        SCI_ConfigurationStatus = 0;

        SCI_exStatus[chIdentifier].R = 0;
        SCI_rxData[chIdentifier]= 0; 
        SCI_flagOverrun[chIdentifier] = 0;
        SCI_parityBit[chIdentifier] = 0; 
         
        switch(baud) 
        {          
            case SCI_300_BR:
                baud_rate=SCI_300_BR;
            break;
            case SCI_600_BR:
               baud_rate=SCI_600_BR;
            break;
            case SCI_1200_BR:
                baud_rate=SCI_1200_BR;
            break;
            case SCI_2400_BR:
                baud_rate=SCI_2400_BR;
            break;
            case SCI_4800_BR:
                baud_rate=SCI_4800_BR;
            break;
            case SCI_9600_BR:
                baud_rate=SCI_9600_BR;
            break;
            case SCI_10400_BR:
                baud_rate=SCI_10400_BR;
            break;
            case SCI_19200_BR:
                baud_rate=SCI_19200_BR;
            break;
            case SCI_38400_BR:
                baud_rate=SCI_38400_BR;
            break;
            case SCI_57600_BR:
                baud_rate=SCI_57600_BR;
            break;
            case SCI_115200_BR:
                baud_rate=SCI_115200_BR;
            break;            
            
            default:
      errorReturn = PERIPHERAL_NOT_CONFIGURED;
      break;
        }
             
  if (errorReturn == NO_ERROR)
  {
        switch(chIdentifier) 
        {
#if(SCI_CH_A_ENABLED == 1)
            case SCI_CH_A:
            {

                /******** eSCI_A Configuration ********/ 
                ESCI_A.CR2.R = SCI_ENABLE_MODULE; /* Enable eSCI module - MDIS = 0 */ 
                     
                ESCI_A.CR1.B.SBR   = SCIA_SBR_BAUD_RATE(baud_rate);       /* SBR   = 0xXXXX */
                ESCI_A.CR1.B.LOOPS = SCIA_LOOPS_DISABLE;       /* LOOPS = 0   */
                ESCI_A.CR1.B.RSRC  = SCIA_RSRC_DISABLE;        /* RSRC  = 0   */
                ESCI_A.CR1.B.M     = SCIA_BIT_NUMBER;           /* M     = 0/1 - 8bit/9bit  */
                ESCI_A.CR1.B.WAKE  = SCIA_WAKE_DISABLE;        /* WAKE  = 0   */
                ESCI_A.CR1.B.ILT   = SCIA_ILT;                 /* ILT   = 1   */
                ESCI_A.CR1.B.PE    = SCIA_PARITY;              /* PE    = 0/1 - DISABLE/ENABLE   */
                ESCI_A.CR1.B.PT    = SCIA_PARITY_TYPE;         /* PT    = 0/1 - EVEN/ODD         */
                ESCI_A.CR1.B.TIE   = SCIA_TIE_DISABLE;          /* TIE   = 0   */
                ESCI_A.CR1.B.TCIE  = SCIA_TCIE_ENABLE;         /* TCIE  = 1   */
                ESCI_A.CR1.B.RIE   = SCIA_RIE_ENABLE;          /* RIE   = 1   */
                ESCI_A.CR1.B.ILIE  = SCIA_ILIE_DISABLE;        /* ILIE  = 0   */
                ESCI_A.CR1.B.TE    = SCIA_TE_ENABLE;            /* TE    = 1   */
                ESCI_A.CR1.B.RE    = SCIA_RE_ENABLE;            /* RE    = 1   */
                ESCI_A.CR1.B.RWU   = SCIA_RWU_DISABLE;          /* RWU   = 0   */ 
                ESCI_A.CR1.B.SBK   = SCIA_SBK_DISABLE;          /* SBK   = 0   */ 
                /* ESCIA_CR2: MDIS=0,FBR=0,BSTP=1,IEBERR=0,RXDMA=0,TXDMA=0,BRK13=0,BESM13=0,SBSTP=1,ORIE=1,NFIE=1,FEIE=1,PFIE=1 */       
                 
                ESCI_A.CR2.B.ORIE = 0x01; /* ORIE=1,NFIE=0,FEIE=0,PFIE=0 */
                 
                     
                /* ESCIA_LCR: LRES=0,WU=0,WUD=0,LDBG=0,DSF=0,PRTY=0,LIN=0,RXIE=0,TXIE=0,WUIE=0,STIE=0,PBIE=0,CIE=0,CKIE=0,FCIE=0,OFIE=0 */
                ESCI_A.LCR.R = 0x00; /* LIN Register - LIN = 0 - eSCI module in SCI mode */

                /* ESCIA_LPR: P15=1,P14=1,P13=0,P12=0,P11=0,P10=1,P9=0,P8=1,P7=1,P6=0,P5=0,P4=1,P3=1,P2=0,P1=0,P0=1 */
                ESCI_A.LPR.R = 0xC5990000; 
                 
                dummy32 = ESCI_A.SR.R;        /* clear SR */
                dummy32 = ESCI_A.DR.R;
                 
                 
                /* SCIA pin configuration */ 
                SIU.PCR[TXDA].B.PA  = SCIA_SIU_PA_CONFIG; /* SIU_PCR89: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
                SIU.PCR[TXDA].B.OBE = SCIA_SIU_OBE_CONFIG;
                SIU.PCR[TXDA].B.IBE = SCIA_SIU_IBE_CONFIG;
                SIU.PCR[TXDA].B.ODE = SCIA_SIU_ODE_CONFIG;
                SIU.PCR[TXDA].B.HYS = SCIA_SIU_HYS_CONFIG;
                SIU.PCR[TXDA].B.SRC = SCIA_SIU_SRC_CONFIG;
                SIU.PCR[TXDA].B.WPE = SCIA_SIU_WPE_CONFIG;
                SIU.PCR[TXDA].B.WPS = SCIA_SIU_WPS_CONFIG;

                SIU.PCR[RXDA].B.PA  = SCIA_SIU_PA_CONFIG; /* SIU_PCR90: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
                SIU.PCR[RXDA].B.OBE = SCIA_SIU_OBE_CONFIG;
                SIU.PCR[RXDA].B.IBE = SCIA_SIU_IBE_CONFIG;
                SIU.PCR[RXDA].B.ODE = SCIA_SIU_ODE_CONFIG;
                SIU.PCR[RXDA].B.HYS = SCIA_SIU_HYS_CONFIG;
                SIU.PCR[RXDA].B.SRC = SCIA_SIU_SRC_CONFIG;
                SIU.PCR[RXDA].B.WPE = SCIA_SIU_WPE_CONFIG;
                SIU.PCR[RXDA].B.WPS = SCIA_SIU_WPS_CONFIG;

            }

            break;
#endif /* SCI_CH_A_ENABLED == 1 */
         
#if(SCI_CH_B_ENABLED == 1)
            case SCI_CH_B:
            {
                /******** eSCI_B Configuration ********/ 
                ESCI_B.CR2.R = SCI_ENABLE_MODULE;/* Enable eSCI module - MDIS = 0 */ 
                         
                ESCI_B.CR1.B.SBR   = SCIB_SBR_BAUD_RATE(baud_rate);       /* SBR   = 0xXXXX */
                ESCI_B.CR1.B.LOOPS = SCIB_LOOPS_DISABLE;       /* LOOPS = 0   */
                ESCI_B.CR1.B.RSRC  = SCIB_RSRC_DISABLE;        /* RSRC  = 0   */
                ESCI_B.CR1.B.M     = SCIB_BIT_NUMBER;           /* M     = 0/1 - 8bit/9bit       */
                ESCI_B.CR1.B.WAKE  = SCIB_WAKE_DISABLE;        /* WAKE  = 0   */
                ESCI_B.CR1.B.ILT   = SCIB_ILT;                 /* ILT   = 1   */
                ESCI_B.CR1.B.PE    = SCIB_PARITY;              /* PE    = 0/1 - DISABLE/ENABLE   */
                ESCI_B.CR1.B.PT    = SCIB_PARITY_TYPE;         /* PT    = 0/1 - EVEN/ODD         */
                ESCI_B.CR1.B.TIE   = SCIB_TIE_DISABLE;          /* TIE   = 0   */
                ESCI_B.CR1.B.TCIE  = SCIB_TCIE_ENABLE;         /* TCIE  = 1   */
                ESCI_B.CR1.B.RIE   = SCIB_RIE_ENABLE;           /* RIE   = 1   */
                ESCI_B.CR1.B.ILIE  = SCIB_ILIE_DISABLE;        /* ILIE  = 0   */
                ESCI_B.CR1.B.TE    = SCIB_TE_ENABLE;            /* TE    = 1   */
                ESCI_B.CR1.B.RE    = SCIB_RE_ENABLE;            /* RE    = 1   */
                ESCI_B.CR1.B.RWU   = SCIB_RWU_DISABLE;          /* RWU   = 0   */ 
                ESCI_B.CR1.B.SBK   = SCIB_SBK_DISABLE;          /* SBK   = 0   */

                /* ESCIB_CR2: MDIS=0,FBR=0,BSTP=1,IEBERR=0,RXDMA=0,TXDMA=0,BRK13=0,BESM13=0,SBSTP=1,ORIE=1,NFIE=1,FEIE=1,PFIE=1 */

                ESCI_B.CR2.B.ORIE = 0x01;  /* ORIE=1,NFIE=0,FEIE=0,PFIE=0 */


                /* ESCIB_LCR: LRES=0,WU=0,WUD=0,LDBG=0,DSF=0,PRTY=0,LIN=0,RXIE=0,TXIE=0,WUIE=0,STIE=0,PBIE=0,CIE=0,CKIE=0,FCIE=0,OFIE=0 */
                ESCI_B.LCR.R = 0x00;  

                /* ESCIB_LPR: P15=1,P14=1,P13=0,P12=0,P11=0,P10=1,P9=0,P8=1,P7=1,P6=0,P5=0,P4=1,P3=1,P2=0,P1=0,P0=1 */
                ESCI_B.LPR.R = 0xC5990000;
                         
                dummy32 = ESCI_B.SR.R;        /* clear SR */
                dummy32 = ESCI_B.DR.R;
                 

                /* SCIB pin configuration */ 
                SIU.PCR[TXDB_PCSD1].B.PA  = SCIB_SIU_PA_CONFIG; /* SIU_PCR91: PA=3,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
                SIU.PCR[TXDB_PCSD1].B.OBE = SCIB_SIU_OBE_CONFIG;
                SIU.PCR[TXDB_PCSD1].B.IBE = SCIB_SIU_IBE_CONFIG;
                SIU.PCR[TXDB_PCSD1].B.ODE = SCIB_SIU_ODE_CONFIG;
                SIU.PCR[TXDB_PCSD1].B.HYS = SCIB_SIU_HYS_CONFIG;
                SIU.PCR[TXDB_PCSD1].B.SRC = SCIB_SIU_SRC_CONFIG;
                SIU.PCR[TXDB_PCSD1].B.WPE = SCIB_SIU_WPE_CONFIG;
                SIU.PCR[TXDB_PCSD1].B.WPS = SCIB_SIU_WPS_CONFIG;

                SIU.PCR[RXDB_PCSD5].B.PA  = SCIB_SIU_PA_CONFIG; /* SIU_PCR92: PA=3,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */ 
                SIU.PCR[RXDB_PCSD5].B.OBE = SCIB_SIU_OBE_CONFIG;
                SIU.PCR[RXDB_PCSD5].B.IBE = SCIB_SIU_IBE_CONFIG;
                SIU.PCR[RXDB_PCSD5].B.ODE = SCIB_SIU_ODE_CONFIG;
                SIU.PCR[RXDB_PCSD5].B.HYS = SCIB_SIU_HYS_CONFIG;
                SIU.PCR[RXDB_PCSD5].B.SRC = SCIB_SIU_SRC_CONFIG;
                SIU.PCR[RXDB_PCSD5].B.WPE = SCIB_SIU_WPE_CONFIG;
                SIU.PCR[RXDB_PCSD5].B.WPS = SCIB_SIU_WPS_CONFIG;
                /***************************************/
            }
            break;
#endif /* SCI_CH_B_ENABLED == 1 */
  
            default:
           errorReturn = PERIPHERAL_NOT_CONFIGURED;
       break;
      }
      if (errorReturn == NO_ERROR)
      {
         SCI_ConfigurationStatus = 1;
      }
    }
  }
  else 
  {
     errorReturn = PERIPHERAL_ALREADY_CONFIGURED;
  } /* End ConfigurationStatus */
  
  return errorReturn;
} /* End SCI_Config method */

#ifdef _BUILD_LINMGM_
/* LIN data receive structure  */
typedef struct LIN_rxMsg
{
    uint8_t IDFrame;
    uint8_t data[SCI_LIN_SLAVE_BUFFER_SIZE];
    uint8_t crc;
    uint8_t FlgDataOk;
} LIN_RxMessage;

LIN_RxMessage   LIN_rx;

/*
** ===================================================================
**     Method      :  SCI_LINConfig(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t  SCI_LINConfig(void)
{
    int16_t errorReturn = /*PERIPHERAL_NOT_CONFIGURED*/NO_ERROR;

    if (SCI_LINConfigurationStatus == 0)
    {
        SCI_LINConfigurationStatus = 1;
    }
    else
    {   
        errorReturn = PERIPHERAL_ALREADY_CONFIGURED;
//        return PERIPHERAL_ALREADY_CONFIGURED;
    }
    if (errorReturn == NO_ERROR)
    {
#if(LIN_CH_A_ENABLED == 1)
        /******** eSCI_A Configuration ********/
        /*  ESCI_A.CR2.R = 0x0104000C */
        ESCI_A.CR2.R = SCI_ENABLE_MODULE; /* Enable eSCI module - MDIS = 0 */
        ESCI_A.CR1.B.SBR   = SCIA_SBR_BAUD_RATE(SCILIN_BAUD_RATE);  /* SBR   = 19200 baud */

        ESCI_A.CR1.B.M     = SCIA_BIT_NUMBER;           /* M     = 0/1 - 8bit/9bit  */
        ESCI_A.CR1.B.PE    = SCIA_PE_DISABLE;           /* PE    = 0   */
        ESCI_A.CR1.B.TE    = SCIA_TE_ENABLE;            /* TE    = 1   */
        ESCI_A.CR1.B.RE    = SCIA_RE_ENABLE;            /* RE    = 1   */
        ESCI_A.CR1.B.TIE   = SCIA_TIE_DISABLE;          /* TIE   = 0   */
        ESCI_A.CR1.B.TCIE  = SCIA_TCIE_DISABLE;         /* TCIE  = 0   */
        ESCI_A.CR1.B.RIE   = SCIA_RIE_DISABLE;          /* RIE   = 0   */

        /* ESCI_A.CR2.R = 0x62C0 */
        ESCI_A.CR2.B.BRCL = SCIA_LIN_13BRK_ENABLE;     /* BRK13 = 1   */
        ESCI_A.CR2.B.SBSTP = SCIA_LIN_SBSTP_ENABLE;     /* SBSTP = 1   */
        ESCI_A.CR2.B.BESM = SCIA_LIN_BESM13_ENABLE;   /* BESM13 = 1  */

        ESCI_A.CR2.B.BSTP = SCIA_LIN_BSTP_ENABLE;   /* BSTP = 1  */
        ESCI_A.CR2.B.FBR = SCIA_LIN_FBR_ENABLE;   /* FBR = 1  */

        /* enable DMA node - ESCI_A.CR2.R = 0x6EC0*/
        ESCI_A.CR2.B.RXDMA = SCIA_LIN_DMA_ENABLE;   /* DMA RX ENABLE  */
        ESCI_A.CR2.B.TXDMA = SCIA_LIN_DMA_ENABLE;   /* DMA TX ENABLE  */

        /* interrut enable - ESCI_A.CR2.R = 0x7E66*/
        ESCI_A.CR2.B.NFIE   = SCIA_LIN_IRQ_ENABLE;
        ESCI_A.CR2.B.FEIE   = SCIA_LIN_IRQ_ENABLE;

        /* ESCI_A.LCR.R = 0x531F0100 */
        ESCI_A.LCR.B.LIN   = SCIA_LIN_ENABLE;           /* LIN   = 1   */
        ESCI_A.LCR.B.LDBG  = SCIA_LIN_LDBG_DISABLE;     /* LDBG  = 01   */
        ESCI_A.LCR.B.PRTY  = SCIA_LIN_PRTY_ENABLE;      /* PRTY  = 1   */
        ESCI_A.LCR.B.WUD0 = 0;
        ESCI_A.LCR.B.WUD1 = 1;

        ESCI_A.LCR.B.CKIE   = SCIA_LIN_IRQ_ENABLE;
        ESCI_A.LCR.B.OFIE   = SCIA_LIN_IRQ_ENABLE;
        /* wake-up signal generation */
        ESCI_A.LCR.B.WU     = SCIA_LIN_WU_ENABLE;

        /* SCIA pin configuration */
        SIU.PCR[TXDA].B.PA  = SCIA_SIU_PA_CONFIG; /* SIU_PCR89: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */
        SIU.PCR[TXDA].B.OBE = SCIA_SIU_OBE_CONFIG;
        SIU.PCR[TXDA].B.IBE = SCIA_SIU_IBE_CONFIG;
        SIU.PCR[TXDA].B.ODE = SCIA_SIU_ODE_CONFIG;
        SIU.PCR[TXDA].B.HYS = SCIA_SIU_HYS_CONFIG;
        SIU.PCR[TXDA].B.SRC = SCIA_SIU_SRC_CONFIG;
        SIU.PCR[TXDA].B.WPE = SCIA_SIU_WPE_CONFIG;
        SIU.PCR[TXDA].B.WPS = SCIA_SIU_WPS_CONFIG;

        SIU.PCR[RXDA].B.PA  = SCIA_SIU_PA_CONFIG; /* SIU_PCR90: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */
        SIU.PCR[RXDA].B.OBE = SCIA_SIU_OBE_CONFIG;
        SIU.PCR[RXDA].B.IBE = SCIA_SIU_IBE_CONFIG;
        SIU.PCR[RXDA].B.ODE = SCIA_SIU_ODE_CONFIG;
        SIU.PCR[RXDA].B.HYS = SCIA_SIU_HYS_CONFIG;
        SIU.PCR[RXDA].B.SRC = SCIA_SIU_SRC_CONFIG;
        SIU.PCR[RXDA].B.WPE = SCIA_SIU_WPE_CONFIG;
        SIU.PCR[RXDA].B.WPS = SCIA_SIU_WPS_CONFIG;
#endif
#if(LIN_CH_B_ENABLED == 1)
        /******** eSCI_B Configuration ********/
        /*  ESCI_B.CR2.R = 0x0104000C */
        ESCI_B.CR2.R = SCI_ENABLE_MODULE; /* Enable eSCI module - MDIS = 0 */
        ESCI_B.CR1.B.SBR   = SCIB_SBR_BAUD_RATE(SCILIN_BAUD_RATE);  /* SBR   = 19200 baud */

        ESCI_B.CR1.B.M     = SCIB_BIT_NUMBER;           /* M     = 0/1 - 8bit/9bit  */
        ESCI_B.CR1.B.PE    = SCIB_PE_DISABLE;           /* PE    = 0   */
        ESCI_B.CR1.B.TE    = SCIB_TE_ENABLE;            /* TE    = 1   */
        ESCI_B.CR1.B.RE    = SCIB_RE_ENABLE;            /* RE    = 1   */
        ESCI_B.CR1.B.TIE   = SCIB_TIE_DISABLE;          /* TIE   = 0   */
        ESCI_B.CR1.B.TCIE  = SCIB_TCIE_DISABLE;         /* TCIE  = 0   */
        ESCI_B.CR1.B.RIE   = SCIB_RIE_DISABLE;          /* RIE   = 0   */

        /* ESCI_B.CR2.R = 0x62C0 */
        ESCI_B.CR2.B.BRCL = SCIB_LIN_13BRK_ENABLE;     /* BRK13 = 1   */
        ESCI_B.CR2.B.BSTP = SCIB_LIN_SBSTP_ENABLE;     /* SBSTP = 1   */
        ESCI_B.CR2.B.BESM = SCIB_LIN_BESM13_ENABLE;    /* BESM13 = 1  */

        ESCI_B.CR2.B.BSTP = SCIB_LIN_BSTP_ENABLE;   /* BSTP = 1  */
        ESCI_B.CR2.B.FBR = SCIB_LIN_FBR_ENABLE;     /* FBR = 1  */

        /* enable DMA node - ESCI_A.CR2.R = 0x6EC0*/
        ESCI_B.CR2.B.RXDMA = SCIB_LIN_DMA_ENABLE;   /* DMA RX ENABLE  */
        ESCI_B.CR2.B.TXDMA = SCIB_LIN_DMA_ENABLE;   /* DMA TX ENABLE  */

        /* interrut enable - ESCI_A.CR2.R = 0x7E66*/
        ESCI_B.CR2.B.NFIE   = SCIB_LIN_IRQ_ENABLE;
        ESCI_B.CR2.B.FEIE   = SCIB_LIN_IRQ_ENABLE;

        /* ESCI_A.LCR.R = 0x531F0100 */
        ESCI_B.LCR.B.LIN   = SCIB_LIN_ENABLE;           /* LIN   = 1   */
        ESCI_B.LCR.B.LDBG  = SCIB_LIN_LDBG_DISABLE;     /* LDBG  = 01   */
        ESCI_B.LCR.B.PRTY  = SCIB_LIN_PRTY_ENABLE;      /* PRTY  = 1   */
        ESCI_B.LCR.B.WUD0 = 0;
        ESCI_B.LCR.B.WUD1 = 1;

        ESCI_B.LCR.B.CKIE   = SCIB_LIN_IRQ_ENABLE;
        ESCI_B.LCR.B.OFIE   = SCIB_LIN_IRQ_ENABLE;
        /* wake-up signal generation */
        ESCI_B.LCR.B.WU     = SCIB_LIN_WU_ENABLE;

        /* SCIA pin configuration */
        SIU.PCR[TXDB_PCSD1].B.PA  = SCIB_SIU_PA_CONFIG; /* SIU_PCR89: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */
        SIU.PCR[TXDB_PCSD1].B.OBE = SCIB_SIU_OBE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.IBE = SCIB_SIU_IBE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.ODE = SCIB_SIU_ODE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.HYS = SCIB_SIU_HYS_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.SRC = SCIB_SIU_SRC_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.WPE = SCIB_SIU_WPE_CONFIG;
        SIU.PCR[TXDB_PCSD1].B.WPS = SCIB_SIU_WPS_CONFIG;

        SIU.PCR[RXDB_PCSD5].B.PA  = SCIB_SIU_PA_CONFIG; /* SIU_PCR90: PA=1,IBE=0,ODE=0,HYS=0,SRC=0,WPE=0,WPS=0 */
        SIU.PCR[RXDB_PCSD5].B.OBE = SCIB_SIU_OBE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.IBE = SCIB_SIU_IBE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.ODE = SCIB_SIU_ODE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.HYS = SCIB_SIU_HYS_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.SRC = SCIB_SIU_SRC_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.WPE = SCIB_SIU_WPE_CONFIG;
        SIU.PCR[RXDB_PCSD5].B.WPS = SCIB_SIU_WPS_CONFIG;
#endif
    }
    return errorReturn;
}

/*
** ===================================================================
**     Method      :  SCI_LINTransmitFrame(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t  SCI_LINTransmitFrame(uint8_t * pTxBuffer, uint8_t size)
{
    int16_t errorReturn = NO_ERROR;

#if(LIN_CH_A_ENABLED == 1)
    SCI_Write(SCI_CH_A, (uint8_t *)pTxBuffer, size);
#elif(LIN_CH_B_ENABLED == 1)
    SCI_Write(SCI_CH_B, (uint8_t *)pTxBuffer, size);
#endif

    return errorReturn;
}

/*
** ===================================================================
**     Method      :  SCI_LINTxHeaderRxBuffer(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t  SCI_LINTxHeaderRxBuffer(uint8_t * pTxBuffer)
{
    int16_t errorReturn = NO_ERROR;
    
#if(LIN_CH_A_ENABLED == 1)
    SCI_Read(SCI_CH_A, pTxBuffer);
#elif(LIN_CH_B_ENABLED == 1)
    SCI_Read(SCI_CH_B, pTxBuffer);
#endif

    return errorReturn;
}


/*
** ===================================================================
**     Method      :  SCI_LINRxEx(...)
**
**     Description :
**         This method it is intended to manage the LIN receive exception
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t SCI_LINRxEx(void)
{
    uint8_t cnt;
    uint16_t crc_check;
    vuint32_t *DSCI_RX_BUFFER;

    crc_check = 0;
    LIN_rx.FlgDataOk = FALSE;

#if(LIN_CH_A_ENABLED == 1)
    DSCI_RX_BUFFER = DSCI_RX_BUFFER_A;
#elif(LIN_CH_B_ENABLED == 1)
    DSCI_RX_BUFFER = DSCI_RX_BUFFER_B;
#endif

    /* receive data */
    /* I receive 8 data byte + 1 CRC byte */
    for (cnt=0;cnt<SCI_LIN_SLAVE_BUFFER_SIZE;cnt++)
    {
        LIN_rx.data[cnt] = (uint8_t)(DSCI_RX_BUFFER[cnt] >> SCI_LIN_SHL_VAL);
        crc_check += LIN_rx.data[cnt];
        if (crc_check > 255)
        {
            crc_check -= 255;
        }
    }
    LIN_rx.crc = (uint8_t)(DSCI_RX_BUFFER[SCI_LIN_SLAVE_BUFFER_SIZE] >> SCI_LIN_SHL_VAL);

    /* crc control */
    if (crc_check + LIN_rx.crc == 0xFF)
    {
       LIN_rx.FlgDataOk = TRUE;
    }

    return NO_ERROR;
}


/*
** ===================================================================
**     Method      :  SCI_LINWakeUp...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t  SCI_LINWakeUp(void)
{
    int16_t errorReturn = NO_ERROR;

    /* wake-up signal generation */
#if(LIN_CH_A_ENABLED == 1)
    ESCI_A.LCR.B.WU     = SCIA_LIN_WU_ENABLE;
#elif(LIN_CH_B_ENABLED == 1)
    ESCI_B.LCR.B.WU     = SCIB_LIN_WU_ENABLE;
#endif

    return errorReturn;
}

/*
** ===================================================================
**     Method      :  SCI_LINWakeUp...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t  SCI_LINReadExceptionType(uint16_t * pLINException)
{
    pLINException = NO_EXCEPTION;

#if(LIN_CH_A_ENABLED == 1)
    if (ESCI_A.SR.B.BERR & SCI_LIN_EX_BERR)
        (*pLINException) |= SCI_LIN_BIT_DETECTED_ERROR;

    if (ESCI_A.SR.B.STO & SCI_LIN_EX_STO)
        (*pLINException) |= SCI_LIN_SLAVE_TIMEOUT_ERROR;

    if (ESCI_A.SR.B.PBERR & SCI_LIN_EX_PBERR)
        (*pLINException) |= SCI_LIN_PHYSICAL_BUS_ERROR;

    if (ESCI_A.SR.B.OVFL & SCI_LIN_EX_OVFL)
        (*pLINException) |= SCI_LIN_RECEIVE_OVERFLOW_ERROR;

    if (ESCI_A.SR.B.NF & SCI_LIN_EX_NF)
        (*pLINException) |= SCI_LIN_RECEIVE_NOISE_ERROR;

    if (ESCI_A.SR.B.FE & SCI_LIN_EX_FE)
        (*pLINException) |= SCI_LIN_RECEIVE_FRAMING_ERROR;

    if (ESCI_A.SR.B.CERR & SCI_LIN_EX_CRC_ERROR)
        (*pLINException) |= SCI_LIN_RECEIVE_CRC_ERROR;

    if (ESCI_A.SR.B.CKERR & SCI_LIN_EX_CHKSUM_ERROR)
        (*pLINException) |= SCI_LIN_RECEIVE_CHKSUM_ERROR;

    /* this last condition is not an error */
    if (ESCI_A.SR.B.FRC & SCI_LIN_EX_FRAME_CORRECTLY_RX)
        (*pLINException) |= SCI_LIN_RECEIVE_FRAME_COMPLETED;
#elif(LIN_CH_B_ENABLED == 1)
    if (ESCI_B.SR.B.BERR & SCI_LIN_EX_BERR)
        (*pLINException) |= SCI_LIN_BIT_DETECTED_ERROR;

    if (ESCI_B.SR.B.STO & SCI_LIN_EX_STO)
        (*pLINException) |= SCI_LIN_SLAVE_TIMEOUT_ERROR;

    if (ESCI_B.SR.B.PBERR & SCI_LIN_EX_PBERR)
        (*pLINException) |= SCI_LIN_PHYSICAL_BUS_ERROR;

    if (ESCI_B.SR.B.OVFL & SCI_LIN_EX_OVFL)
        (*pLINException) |= SCI_LIN_RECEIVE_OVERFLOW_ERROR;

    if (ESCI_B.SR.B.NF & SCI_LIN_EX_NF)
        (*pLINException) |= SCI_LIN_RECEIVE_NOISE_ERROR;

    if (ESCI_B.SR.B.FE & SCI_LIN_EX_FE)
        (*pLINException) |= SCI_LIN_RECEIVE_FRAMING_ERROR;

    if (ESCI_B.SR.B.CERR & SCI_LIN_EX_CRC_ERROR)
        (*pLINException) |= SCI_LIN_RECEIVE_CRC_ERROR;

    if (ESCI_B.SR.B.CKERR & SCI_LIN_EX_CHKSUM_ERROR)
        (*pLINException) |= SCI_LIN_RECEIVE_CHKSUM_ERROR;

    /* this last condition is not an error */
    if (ESCI_B.SR.B.FRC & SCI_LIN_EX_FRAME_CORRECTLY_RX)
        (*pLINException) |= SCI_LIN_RECEIVE_FRAME_COMPLETED;
#endif

    return NO_ERROR;
}


/*
** ===================================================================
**     Method      :  SCI_LINSetSleep(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
int16_t  SCI_LINSetSleep(uint8_t * pBuffer)
{
    int16_t errorReturn = NO_ERROR;
 
#if(LIN_CH_A_ENABLED == 1)
    SCI_Write(SCI_CH_A,pBuffer,SCI_LIN_BUFFER_LENGTH-1);
#elif(LIN_CH_B_ENABLED == 1)
    SCI_Write(SCI_CH_B,pBuffer,SCI_LIN_BUFFER_LENGTH-1);
#endif

    return errorReturn;
}

/*
** ===================================================================
**     Method      :  SCI_Write(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
void SCI_Write(uint8_t Channel,uint8_t *txbuffer,uint8_t word_number)
{
    uint8_t index;
    vuint32_t *DSCI_TX_BUFFER;
    vuint32_t contPCS;

    switch(Channel)
    {
        case SCI_CH_A:
            DSCI_TX_BUFFER = DSCI_TX_BUFFER_A;

            EDMA.CERQR.R = DMA_ESCIA_COMBTX;

            /* int16_t DMA_Init(uint16_t channelNumber,
                           uint32_t *dest, uint16_t destOffset,
                           uint32_t *src,  uint16_t srcOffset,
                           uint16_t innerLoopSize, uint16_t outerLoopSize,
                           uint16_t interruptEnabled)
            */
            DMA_Init     (DMA_ESCIA_COMBTX,
                          (uint32_t *)(DSCI_PUSHR[Channel]), 0,
                          (uint32_t *) DSCI_TX_BUFFER,  4,
                          4, word_number,
                          1);

            ESCI_A.SR.B.RXRDY = 1;
            ESCI_A.SR.B.TXRDY = 1;

            /* Solinas: Prepare the buffer  adding  EOQ=0 PCSn=active Non continuos SCK |Data */
            for (index=0;index<word_number;index++)
            {
                DSCI_TX_BUFFER[index]= (txbuffer[index] & SCI_LIN_BYTE_MASK) << SCI_LIN_SHL_VAL;
            }

            DMA_Enable(DMA_ESCIA_COMBTX);
        break;
        case SCI_CH_B:
            DSCI_TX_BUFFER = DSCI_TX_BUFFER_B;

            EDMA.CERQR.R = DMA_ESCIB_COMBTX;

            /* int16_t DMA_Init(uint16_t channelNumber,
                           uint32_t *dest, uint16_t destOffset,
                           uint32_t *src,  uint16_t srcOffset,
                           uint16_t innerLoopSize, uint16_t outerLoopSize,
                           uint16_t interruptEnabled)
            */
            DMA_Init     (DMA_ESCIB_COMBTX,
                          (uint32_t *)(DSCI_PUSHR[Channel]), 0,
                          (uint32_t *) DSCI_TX_BUFFER,  4,
                          4, word_number,
                          1);

            ESCI_B.SR.B.RXRDY = 1;
            ESCI_B.SR.B.TXRDY = 1;

            /* Solinas: Prepare the buffer  adding  EOQ=0 PCSn=active Non continuos SCK |Data */
            for (index=0;index<word_number;index++)
            {
                DSCI_TX_BUFFER[index]= (txbuffer[index] & SCI_LIN_BYTE_MASK) << SCI_LIN_SHL_VAL;
            }

            DMA_Enable(DMA_ESCIB_COMBTX);
        break;
        default:
            break;
    }
}


/*
** ===================================================================
**     Method      :  SCI_Read(...)
**
**     Description :
**         This method initializes registers of the eSCI modules
**         according to the Functional Specification "PAEO003_06_LINArchitecture.doc"
**         Call this method in the user code to initialize the eSCI LIN module.
**     Parameters  : None
**     Returns     : SCI Error Code
** ===================================================================
*/
void SCI_Read(uint8_t Channel, uint8_t *txbuffer)
{
    uint8_t index;
    vuint32_t *DSCI_RX_BUFFER;
    vuint32_t *DSCI_TX_BUFFER;

    switch(Channel)
    {
        case SCI_CH_A:
            DSCI_TX_BUFFER = DSCI_TX_BUFFER_A;
            DSCI_RX_BUFFER = DSCI_RX_BUFFER_A;

            EDMA.CERQR.R = DMA_ESCIA_COMBTX;
            EDMA.CERQR.R = DMA_ESCIA_COMBRX;

            /* int16_t DMA_Init(uint16_t channelNumber,
                           uint32_t *dest, uint16_t destOffset,
                           uint32_t *src,  uint16_t srcOffset,
                           uint16_t innerLoopSize, uint16_t outerLoopSize,
                           uint16_t interruptEnabled)
            */

            DMA_Init     (DMA_ESCIA_COMBTX,
                          (uint32_t *)(DSCI_PUSHR[Channel]), 0,
                          (uint32_t *) DSCI_TX_BUFFER,  4,
                          4, SCI_LIN_HEADER_RX_BUFFER_SIZE,
                          1);

            DMA_Init     (DMA_ESCIA_COMBRX,
                          (uint32_t *) DSCI_RX_BUFFER,  4,
                          (uint32_t *) (DSCIA_POPR), 0,
                          4, SCI_LIN_SLAVE_BUFFER_SIZE,
                          1);

            ESCI_A.SR.B.RXRDY = 1;
            ESCI_A.SR.B.TXRDY = 1;

            /* Solinas: Prepare the buffer  adding  EOQ=0 PCSn=active Non continuos SCK |Data */
            for (index=0;index < SCI_LIN_HEADER_RX_BUFFER_SIZE; index++)
            {
                DSCI_TX_BUFFER[index] = (txbuffer[index] & SCI_LIN_BYTE_MASK) << SCI_LIN_SHL_VAL;
            }

            DMA_Enable(DMA_ESCIA_COMBTX);
            DMA_Enable(DMA_ESCIA_COMBRX);
        break;

        case SCI_CH_B:
            DSCI_TX_BUFFER = DSCI_TX_BUFFER_B;
            DSCI_RX_BUFFER = DSCI_RX_BUFFER_B;

            EDMA.CERQR.R = DMA_ESCIB_COMBTX;
            EDMA.CERQR.R = DMA_ESCIB_COMBRX;

            /* int16_t DMA_Init(uint16_t channelNumber,
                           uint32_t *dest, uint16_t destOffset,
                           uint32_t *src,  uint16_t srcOffset,
                           uint16_t innerLoopSize, uint16_t outerLoopSize,
                           uint16_t interruptEnabled)
            */

            DMA_Init     (DMA_ESCIB_COMBTX,
                          (uint32_t *)(DSCI_PUSHR[Channel]), 0,
                          (uint32_t *) DSCI_TX_BUFFER,  4,
                          4, SCI_LIN_HEADER_RX_BUFFER_SIZE,
                          1);

            DMA_Init     (DMA_ESCIB_COMBRX,
                          (uint32_t *) DSCI_RX_BUFFER,  4,
                          (uint32_t *) (DSCIB_POPR), 0,
                          4, SCI_LIN_SLAVE_BUFFER_SIZE,
                          1);

            ESCI_B.SR.B.RXRDY = 1;
            ESCI_B.SR.B.TXRDY = 1;

            /* Solinas: Prepare the buffer  adding  EOQ=0 PCSn=active Non continuos SCK |Data */
            for (index=0;index < SCI_LIN_HEADER_RX_BUFFER_SIZE; index++)
            {
                DSCI_TX_BUFFER[index] = (txbuffer[index] & SCI_LIN_BYTE_MASK) << SCI_LIN_SHL_VAL;
            }

            DMA_Enable(DMA_ESCIB_COMBTX);
            DMA_Enable(DMA_ESCIB_COMBRX);        
        break;
        default:
            break;
    }

}

#endif /* _BUILD_LINMGM_ */

#endif /* _BUILD_SCI_ */

/* End SCI Module */






