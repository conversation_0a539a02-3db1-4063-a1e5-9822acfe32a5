/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           C:\localmodules\local_TrqEst\slprj\ert\_sharedutils\div_repeat_ssu32_sat.c
 **  File Creation Date: 22-May-2019
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TrqEst
 **  Model Description:
 **  Model Version:      1.1898
 **  Model Author:       <PERSON> - Wed Jul 01 12:50:43 2009
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: matlab_bo - Wed May 22 09:04:55 2019
 **
 **  Last Saved Modification:  matlab_bo - Wed May 22 09:04:06 2019
 **
 **
 *******************************************************************************
 **/

/*  Defines */

/*  Data Types */

/**************************** GLOBAL DATA *************************************/
/*  Definitions */

/*  Declarations  */

/***************************** FILE SCOPE DATA ********************************/

/*************************** FUNCTIONS ****************************************/
#include "rtwtypes.h"
#include "rtw_shared_utils.h"

int32_T div_repeat_ssu32_sat(int32_T numerator, uint32_T denominator, uint32_T
  nRepeatSub)
{
  int32_T quotient;
  uint32_T tempAbsQuotient;
  uint32_T quotientNeedsNegation;
  if (denominator == 0) {
    quotient = (numerator >= 0) ? MAX_int32_T : MIN_int32_T;

    /* Divide by zero handler */
  } else {
    quotientNeedsNegation = (numerator < 0);
    tempAbsQuotient = div_repeat_u32_sat((uint32_T)((numerator >= 0) ? numerator
      : (-numerator)), denominator, nRepeatSub);
    if (quotientNeedsNegation) {
      quotient = (tempAbsQuotient <= 2147483647U) ? (-((int32_T)tempAbsQuotient))
        : MIN_int32_T;
    } else {
      quotient = (tempAbsQuotient <= 2147483647U) ? ((int32_T)tempAbsQuotient) :
        MAX_int32_T;
    }
  }

  return quotient;
}

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 8.0 (R2012b)20-Jul-2012                                             *
 * Simulink 8.0 (R2012b)20-Jul-2012                                           *
 * Simulink Coder 8.3 (R2012b)20-Jul-2012                                     *
 * Embedded Coder 6.3 (R2012b)20-Jul-2012                                     *
 * Stateflow 8.0 (R2012b)20-Jul-2012                                          *
 * Simulink Fixed Point 7.2 (R2012b)20-Jul-2012                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed-point_blocks                                                         *
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
