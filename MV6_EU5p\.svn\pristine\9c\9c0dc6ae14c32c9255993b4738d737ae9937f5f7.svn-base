/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/SYNC#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1427   $                                                                                          */
/* $Date:: 2009-06-29 17:01:47 +0200 (lun, 29 giu 2009)   $                                                      */
/* $Author:: girasoleg               $                                                                       */
/*****************************************************************************************************************/

#include "syncmgm.h"
/*******************************************************************************/
/*************** Configurazione per motore 4 cilindri MV_AGUSTA_4C *************/
/*******************************************************************************/
// Tooth Counter       :  6,  18,  30,  42,   
// Crank Angle Degree  : 90, 270, 450, 630,    
// Firing order        :  1,   3,   4,   2, 

#if (ENGINE_TYPE == MV_AGUSTA_4C_TDC_0_9)
const EVENT_STRUCT SYNCMGM_TAB[MAX_INDEX] = 
{
  0,  3, (EV_FIRST_TOOTH | EV_ANGLE | EV_INJ_PRG),                      // 0  
  9,  0, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 1  INDEX_GAP0
  18, 0, (EV_INJ_PRG),                                                  // 2  
  21, 0, (EV_CAMTEST),                                                  // 3  INDEX_CAM0
  27, 1, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 4  
  36, 1, (EV_ANGLE | EV_INJ_PRG),                                       // 5  
  45, 2, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 6  INDEX_GAP1 
  54, 2, (EV_INJ_PRG),                                                  // 7  
  57, 2, (EV_CAMTEST),                                                  // 8  INDEX_CAM1
  63, 3, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 9  
  69, 3, (EV_LAST_TOOTH | EV_ANGLE)                                     // 10 
};                                                                      // 11 MAX_INDEX

const uint16_T TDC_ANGLE[N_CYLINDER] = 
{
    (9*TOOTH_ANGLE16),
    (27*TOOTH_ANGLE16),
    (45*TOOTH_ANGLE16),
    (63*TOOTH_ANGLE16)
};  //2^-16 TDC Angle

const uint16_T TDC_ANGLE_MAP = (27*TOOTH_ANGLE16);

const uint8_T CYL_ID_TABLE[N_CYLINDER] = 
{ 
    0, 2, 3, 1
}; // Engine cylinder firing order (zero-based) 
#endif

#if (ENGINE_TYPE == MV_AGUSTA_4C)
const EVENT_STRUCT SYNCMGM_TAB[MAX_INDEX] = 
{
  0,  3, (EV_FIRST_TOOTH | EV_ANGLE | EV_INJ_PRG),                      // 0  
  6,  0, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 1  INDEX_GAP0
  12, 0, (EV_INJ_PRG),                                                  // 2  
  14, 0, (EV_CAMTEST),                                                  // 3  INDEX_CAM0
  18, 1, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 4  
  24, 1, (EV_ANGLE | EV_INJ_PRG),                                       // 5  
  30, 2, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 6  INDEX_GAP1 
  36, 2, (EV_INJ_PRG),                                                  // 7  
  38, 2, (EV_CAMTEST),                                                  // 8  INDEX_CAM1
  42, 3, (EV_TDC  | EV_HTDC | EV_PRE_TDC | EV_PRE_HTDC | EV_ANGLE),     // 9  
  45, 3, (EV_LAST_TOOTH | EV_ANGLE)                                     // 10 
};                                                                      // 11 MAX_INDEX

const uint16_T TDC_ANGLE[N_CYLINDER] = 
{
    (6*TOOTH_ANGLE16),
    (18*TOOTH_ANGLE16),
    (30*TOOTH_ANGLE16),
    (42*TOOTH_ANGLE16)
};  //2^-16 TDC Angle

const uint16_T TDC_ANGLE_MAP = (18*TOOTH_ANGLE16);

const uint8_T CYL_ID_TABLE[N_CYLINDER] = 
{ 
    0, 2, 3, 1
}; // Engine cylinder firing order (zero-based) 
#endif
/*****************************************************************************/
