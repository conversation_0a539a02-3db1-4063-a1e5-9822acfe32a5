/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "OS_api.h"

#include "spi.h"
#include "pwrsply.h"
#include "string.h"
#include "diagmgm_out.h"

#undef _TEST_SPI_

#if defined(_BUILD_PWRSPLY_) && defined(_BUILD_SPI_)

uint8_t     bufini;
uint16_t    SPIpowOakCom;    // command word to pow oak
uint16_t    SPIpowOakAns;    // answer word from pow oak - initial value not significant 

uint16_t rxbuffer_a [8];
uint16_t rxbuffer_b [6];
uint16_t txbuffer_a [6] = 
	{
	    0x00ff 
	    ,0xff00
	};
uint16_t txbuffer_b [8] = 
	{
	    0x1234,
	    0x5678,
	    0x90ab,
	    0xcdef,
	    0x0102,
	    0x0304,
	    0x0506,
	    0x0708
	};

void PWRSPLY_Init(void) 
{
#ifdef _TEST_SPI_
    bufini = 0;
#endif  

  // inizializzazioni PowerOak
  SPIpowOakAns = 0x5555;
  SPIpowOakCom = 0x00ff;
} // end PWRSPLY_Init()

void PWRSPLY_T100ms(void)
{
#ifdef _TEST_SPI_
  PWRSPLY_TestLoop();
#else
  PWRSPLY_PowOak();
#endif
} // end PWRSPLY_T100ms()

#ifdef _TEST_SPI_
void PWRSPLY_TestLoop(void)
{
    /*
        This function transmit a 4-words buffer from channel A to channel B and viceversa 
        via SPI; the two channel, configured one as Master and the other as Slave, must be 
        connected by wire externally from the micro, on the board; 
    */
  
            int8_t      status;

    memset(rxbuffer_a,0,sizeof(rxbuffer_a));
    memset(rxbuffer_b,0,sizeof(rxbuffer_b));
    
    SPI_RxTxBuffer (SPI_CH_B, txbuffer_b + bufini,4,0);
	  SPI_RxTxBuffer (SPI_CH_A, txbuffer_a,4,0);
  
    do {
        status=SPI_GetChannelStatus(SPI_CH_A);
    } while (status==SPI_RUNNING);
  
    SPI_GetRxData (SPI_CH_A, rxbuffer_a,4); 
    SPI_GetRxData (SPI_CH_B, rxbuffer_b,4); 

    bufini += 4;
    if (bufini>7) bufini = 0;
     
}   // end PWRSPLY_TestLoop()
#endif

void PWRSPLY_PowOak(void) 
{
    // This function sends a command word to MC33394 (Power Oak);
    // 
    // output: pPowOakAns = ptr to answer from Power Oak
    //                                                                                      
    
    SPI_RxTxBuffer (SPI_CH_A ,&SPIpowOakCom,1,5); // CSA5
} // end PWRSPLY_PowOak()

void PWRSPLY_PowOakExRxDone(void) 
{
    uint8_T tmpPtFault = NO_PT_FAULT;
    uint8_T	tmpStDiag = NO_FAULT;

    // Note: In this case, the only slave selected to communicate to ch A
    //       is slave#5, i.e. MC33394
    //
    // cnt00++;
        
    SPI_GetRxData (SPI_CH_A, &SPIpowOakAns , 1); 
    				
#ifdef EN_DIAG_POW_OAK
  tmpPtFault = NO_PT_FAULT;					  
  if(SPIpowOakAns != 0) 
  {
    tmpPtFault = SIGNAL_NOT_VALID;
  }    
  DiagMgm_SetDiagState(DIAG_POW_OAK, tmpPtFault, &tmpStDiag);
#endif // DIAG_POW_OAK 
  tmpPtFault = NO_PT_FAULT;			  
  if(SPIpowOakAns & MASK_VREF_2) 
  {
    tmpPtFault = SIGNAL_NOT_VALID;
  }
  DiagMgm_SetDiagState(DIAG_VSENS1, tmpPtFault, &tmpStDiag);
  tmpPtFault = NO_PT_FAULT;			  
  if(SPIpowOakAns & MASK_VREF_3) 
  {
    tmpPtFault = SIGNAL_NOT_VALID;
  }
  DiagMgm_SetDiagState(DIAG_VSENS2, tmpPtFault, &tmpStDiag);
} // end PWRSPLY_PowOakExRxDone()

#endif // _BUILD_PWRSPLY_
