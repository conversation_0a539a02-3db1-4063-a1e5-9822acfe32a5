/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef __TPE_H
#define __TPE_H


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "tpe_utils.h"


/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/***********************************************
 * TPE CONFIG                                  *
 * =========================================== *
 * TPE can work following  FIAT or ISO  rules: *
 * use one of the two following defines to set *
 * operating mode:                             *
 *                                             *
 *    #define      TPE_ISO                     *
 *    #define      TPE_FIAT                    *
 *                                             *
 ***********************************************/
#define TPE_ISO
 /* #define TPE_FIAT */


 /* CHECKING TPE ISO or FIAT CONFIGURED */
 #ifdef TPE_ISO
    /* TPE_ISO */
 #else
    #ifdef TPE_FIAT
        /* TPE_FIAT */
    #else
        #error ERROR: TPE working mode not set 
    #endif
 #endif
 /***************************************/


// TPE return codes
#define TPE_NOTSUCCESS                0x01 /* No more unsigned:       */
#define TPE_ERROR_TIMEOUT             0x02 /* apply negative codes ?  */
#define TPE_PENDING                   0x03 /* TBD                     */
#define TPE_SUCCESS                   NO_ERROR


/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

extern tpeRx_t tpeRx;
extern tpeTx_t tpeTx;
extern uint8_t fSincronize;         /* flag to sincronize (mngrs and exception) execution */


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * tpeInit - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     tpeInit(void);

/*--------------------------------------------------------------------------*
 * tpeReceive - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t  tpeReceive(uint8_t *data,
                     uint8_t *data_len);

/*--------------------------------------------------------------------------*
 * tpeSend - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t  tpeSend(uint8_t *data, 
                  uint8_t data_len);

/*--------------------------------------------------------------------------*
 * tpeSendStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t  tpeSendStatus(void);

/*--------------------------------------------------------------------------*
 * tpeReceiveStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 int16_t  tpeReceiveStatus(void);

/*--------------------------------------------------------------------------*
 * tpeTickTimer - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void     tpeTickTimer(void);

/*--------------------------------------------------------------------------*
 * TPE_TxDataEx - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void TPE_TxDataEx(void);

/*--------------------------------------------------------------------------*
 * TPE_RxDataEx - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void TPE_RxDataEx(void);

void tpeReInit(void);



#endif /* __TPE_H */
