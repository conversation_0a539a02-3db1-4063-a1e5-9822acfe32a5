/*******************************************************************
 *
 *    DESCRIPTION:
 *
 *    AUTHOR:
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _WATPUMP_CTRL_H_
#define _WATPUMP_CTRL_H_

/** include files **/
#include "typedefs.h"

/** local definitions **/

/** default settings **/

/** external functions **/

/** external data **/
// extern uint8_T EnWatPumpMgm;
// extern uint16_T WatPumpPWMDuty;

/** internal functions **/

/** public data **/
// extern uint8_T FlgWatPumpCtrlOn;
// extern uint16_T WatPumpPWMDuty;

/** private data **/

/** public functions **/
extern void WatPumpCtrl_Init(void);
extern void WatPumpCtrl_100ms(void);

/** private functions **/

#endif
