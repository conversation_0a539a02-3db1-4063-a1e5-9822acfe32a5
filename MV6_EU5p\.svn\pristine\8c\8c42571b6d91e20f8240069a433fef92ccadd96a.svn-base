/*
 * File: Trc2WZero.h
 *
 * Code generated for Simulink model 'Trc2WZero'.
 *
 * Model version                  : 1.252
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Sep 30 10:26:41 2020
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_Trc2WZero_h_
#define RTW_HEADER_Trc2WZero_h_
#include <string.h>
#ifndef Trc2WZero_COMMON_INCLUDES_
# define Trc2WZero_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* Trc2WZero_COMMON_INCLUDES_ */

#include "Trc2WZero_types.h"

/* Includes for objects with custom storage classes. */
#include "trc2wzero_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define FRONT_WHEEL                    0U                        /* Referenced by: '<S26>/TPMS_Ack' */

/* status */
#define ID_TRC_2W_ZERO                 21005074U                 /* Referenced by: '<S2>/ID_TRC_2W_ZERO' */

/* mask */
#define PRES_WHEEL_K_CONV              11U                       /* Referenced by: '<S26>/PRES_WHEEL_K_CONV' */

/* gain */
#define REAR_WHEEL                     16U                       /* Referenced by: '<S26>/TPMS_Ack' */

/* status */
#define TOLL_TC_ZERO_0_98              1004U                     /* Referenced by:
                                                                  * '<S2>/Sat'
                                                                  * '<S12>/Sat'
                                                                  */

/* gain */
#define TOLL_TC_ZERO_1_02              1044U                     /* Referenced by:
                                                                  * '<S2>/Sat'
                                                                  * '<S12>/Sat'
                                                                  */

/* gain */
#define TPMS_ABSENT                    4U                        /* Referenced by: '<S26>/TPMS_Ack' */

/* status */
#define TPMS_ACK                       3U                        /* Referenced by: '<S26>/TPMS_Ack' */

/* status */
#define TPMS_FRONT                     2U                        /* Referenced by: '<S26>/TPMS_Ack' */

/* status */
#define TPMS_INIT                      0U                        /* Referenced by: '<S26>/TPMS_Ack' */

/* status */
#define TPMS_PRE_150KP                 1200U                     /* Referenced by: '<S26>/TPMS_Ack' */

/* KPa */
#define TPMS_REAR                      1U                        /* Referenced by: '<S26>/TPMS_Ack' */

/* status */

/* Block signals and states (default storage) for system '<S4>/Calc_Ack' */
typedef struct {
  uint16_T Memory1_PreviousInput;      /* '<S24>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S24>/Memory' */
  uint8_T Memory_PreviousInput_epk;    /* '<S16>/Memory' */
} rtDW_Calc_Ack_Trc2WZero_T;

/* Block signals and states (default storage) for system '<S4>/Calc_Diag' */
typedef struct {
  uint16_T cntNode;                    /* '<S26>/TPMS_Ack' */
  uint8_T is_active_c4_Trc2WZero;      /* '<S26>/TPMS_Ack' */
  uint8_T is_c4_Trc2WZero;             /* '<S26>/TPMS_Ack' */
} rtDW_Calc_Diag_Trc2WZero_T;

/* Block signals and states (default storage) for system '<S4>/Calc_VSFGain' */
typedef struct {
  uint16_T Memory1_PreviousInput;      /* '<S39>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S39>/Memory' */
  uint8_T Memory_PreviousInput_d5y;    /* '<S12>/Memory' */
} rtDW_Calc_VSFGain_Trc2WZero_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  rtDW_Calc_VSFGain_Trc2WZero_T Calc_VSFGain;/* '<S4>/Calc_VSFGain' */
  rtDW_Calc_Diag_Trc2WZero_T Calc_Diag;/* '<S4>/Calc_Diag' */
  rtDW_Calc_Ack_Trc2WZero_T Calc_Ack;  /* '<S4>/Calc_Ack' */
  uint16_T y;                          /* '<S21>/bit_shift' */
  uint8_T resetAck;                    /* '<S4>/Chart_Trc2WZero' */
  uint8_T SlTc2W_ml3;                  /* '<S4>/Chart_Trc2WZero' */
  uint8_T EnTC2WZeroCC_e4f;            /* '<S4>/Chart_Trc2WZero' */
  uint8_T LogicalOperator;             /* '<S15>/Logical Operator' */
  uint8_T LogicalOperator2;            /* '<S9>/Logical Operator2' */
  uint8_T is_active_c3_Trc2WZero;      /* '<S4>/Chart_Trc2WZero' */
  uint8_T is_TRC_2_W_Z_CONTROL;        /* '<S4>/Chart_Trc2WZero' */
  uint8_T interlock;                   /* '<S4>/Chart_Trc2WZero' */
  boolean_T LogicalOperator_noc;       /* '<S10>/Logical Operator' */
  boolean_T LogicalOperator3;          /* '<S9>/Logical Operator3' */
} D_Work_Trc2WZero_T;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc3_Trig_ZCE;     /* '<S1>/trig_to_fc3' */
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
} PrevZCSigStates_Trc2WZero_T;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PreTDC;                   /* '<Root>/ev_PreTDC' */
} ExternalInputs_Trc2WZero_T;

/* Block signals and states (default storage) */
extern D_Work_Trc2WZero_T Trc2WZero_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_Trc2WZero_T Trc2WZero_U;

/* Model entry point functions */
extern void Trc2WZero_initialize(void);
extern void Trc2WZero_step(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion5' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('Trc2WZero_gen/Trc2WZero')    - opens subsystem Trc2WZero_gen/Trc2WZero
 * hilite_system('Trc2WZero_gen/Trc2WZero/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'Trc2WZero_gen'
 * '<S1>'   : 'Trc2WZero_gen/Trc2WZero'
 * '<S2>'   : 'Trc2WZero_gen/Trc2WZero/Init'
 * '<S3>'   : 'Trc2WZero_gen/Trc2WZero/PreTDC'
 * '<S4>'   : 'Trc2WZero_gen/Trc2WZero/T10ms'
 * '<S5>'   : 'Trc2WZero_gen/Trc2WZero/trig_to_fc1'
 * '<S6>'   : 'Trc2WZero_gen/Trc2WZero/trig_to_fc2'
 * '<S7>'   : 'Trc2WZero_gen/Trc2WZero/trig_to_fc3'
 * '<S8>'   : 'Trc2WZero_gen/Trc2WZero/Init/Sat'
 * '<S9>'   : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack'
 * '<S10>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag'
 * '<S11>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Tst'
 * '<S12>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain'
 * '<S13>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Chart_Trc2WZero'
 * '<S14>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Reset_Gain'
 * '<S15>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ack'
 * '<S16>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ackStab'
 * '<S17>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ack/Compare To Constant'
 * '<S18>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ack/Compare To Constant1'
 * '<S19>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ack/Compare To Constant2'
 * '<S20>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ack/Compare To Constant5'
 * '<S21>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ackStab/Bit Shift'
 * '<S22>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ackStab/Compare To Constant3'
 * '<S23>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ackStab/Dead Zone Dynamic'
 * '<S24>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ackStab/Signal_Stability'
 * '<S25>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Ack/Calc_ackStab/Bit Shift/bit_shift'
 * '<S26>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Calc_TPMS'
 * '<S27>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Compare To Constant1'
 * '<S28>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Compare To Constant2'
 * '<S29>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Compare To Constant3'
 * '<S30>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Compare To Constant4'
 * '<S31>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Compare To Constant8'
 * '<S32>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Diag/Calc_TPMS/TPMS_Ack'
 * '<S33>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Tst/Compare To Constant'
 * '<S34>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Tst/Compare To Constant1'
 * '<S35>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_Tst/Compare To Constant2'
 * '<S36>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain/Calc_Gain'
 * '<S37>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain/Compare To Constant'
 * '<S38>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain/Sat'
 * '<S39>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain/Signal_Stability'
 * '<S40>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain/Calc_Gain/Compare To Constant1'
 * '<S41>'  : 'Trc2WZero_gen/Trc2WZero/T10ms/Calc_VSFGain/Calc_Gain/Compare To Constant2'
 */

/*-
 * Requirements for '<Root>': Trc2WZero
 */
#endif                                 /* RTW_HEADER_Trc2WZero_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
