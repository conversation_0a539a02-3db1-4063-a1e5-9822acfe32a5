/*
 * File: AnalogQS_types.h
 *
 * Code generated for Simulink model 'AnalogQS'.
 *
 * Model version                  : 1.655
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Nov 16 11:29:39 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_AnalogQS_types_h_
#define RTW_HEADER_AnalogQS_types_h_
#endif                                 /* RTW_HEADER_AnalogQS_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
