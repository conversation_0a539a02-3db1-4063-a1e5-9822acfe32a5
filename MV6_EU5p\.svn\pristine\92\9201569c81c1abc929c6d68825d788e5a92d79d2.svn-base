; Initializes internal SRAM and sets MMU
;
; Dec 11 2003 <PERSON><PERSON><PERSON>
; 

; initialize internam SRAM
d.s a:0x40000000--0x4000BFFF %quad 0


; This script initializes the MPC5633M/SPC563M MMU the same as the BAM.

MMU.TLB1.SET 0. 0xC0000500 0xFFF0000A 0xFFF0003F
MMU.TLB1.SET 1. 0xC0000700 0x00000000 0x0000003F
MMU.TLB1.SET 2. 0xC0000700 0x20000000 0x2000003F
MMU.TLB1.SET 3. 0xC0000400 0x40000008 0x4000003F
MMU.TLB1.SET 4. 0xC0000500 0xC3F0000A 0xC3F0003F
MMU.TLB1.SET 5. 0x00000000 0x00000000 0x00000000
MMU.TLB1.SET 6. 0x00000000 0x00000000 0x00000000
MMU.TLB1.SET 7. 0x00000000 0x00000000 0x00000000

print "The MMU has been set to the same mapping as the BAM"

MMU.tlbscan
MMU.ON

