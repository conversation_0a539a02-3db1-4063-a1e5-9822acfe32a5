/*
 * File: CmiDriverMgm.c
 *
 * Code generated for Simulink model 'CmiDriverMgm'.
 *
 * Model version                  : 1.2254
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Nov 12 11:51:07 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (22), Warnings (3), Errors (8)
 */

#include "CmiDriverMgm.h"
#include "CmiDriverMgm_private.h"

/* user code (top of source file) */
/* System '<S1>/T10ms' */
#ifdef _BUILD_CMIDRIVERMGM_

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_CmiDriverMgm CmiDriverMgm_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_CmiDriverMgm CmiDriverMgm_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T CmiDriverI;

/* CMI fast */
int16_T CmiDriverIQS;

/* CMI fast filtered */
int16_T CmiDriverP;

/* CMI slow */
uint32_T IDCmiDriverMgm;

/* ID Version */

/* Output and update for function-call system: '<S1>/T10ms' */
void CmiDriverMgm_T10ms(void)
{
  int16_T rtb_Switch3;
  int16_T rtb_Add;

  /* Outputs for Atomic SubSystem: '<S4>/Cme2Cmi' */
  /* Switch: '<S8>/Switch1' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   *  Inport: '<Root>/CmeQsPFilt'
   *  Inport: '<Root>/QuickGearShiftBlp'
   */
  if (QuickGearShiftBlp != 0) {
    rtb_Switch3 = CmeQsPFilt;
  } else {
    rtb_Switch3 = CmeDriverP;
  }

  /* End of Switch: '<S8>/Switch1' */

  /* Sum: '<S8>/Add' incorporates:
   *  Inport: '<Root>/CmfP'
   */
  rtb_Add = (int16_T)(rtb_Switch3 + CmfP);

  /* Switch: '<S8>/Switch3' incorporates:
   *  Constant: '<S8>/ZERO_CMII'
   *  Inport: '<Root>/CmfP'
   *  Inport: '<Root>/CtfGearShift'
   *  Inport: '<Root>/QuickGearShiftBlp'
   *  Inport: '<Root>/QuickGearShiftCtf'
   *  Logic: '<S8>/Logical Operator'
   *  Sum: '<S8>/Add1'
   *  Switch: '<S8>/Switch'
   */
  if (CtfGearShift != 0) {
    rtb_Switch3 = 0;
  } else {
    if ((QuickGearShiftBlp != 0) || (QuickGearShiftCtf != 0)) {
      /* Switch: '<S8>/Switch' incorporates:
       *  Inport: '<Root>/CmeQsIFilt'
       */
      rtb_Switch3 = CmeQsIFilt;
    } else {
      /* Switch: '<S8>/Switch' incorporates:
       *  Inport: '<Root>/CmeDriverI'
       */
      rtb_Switch3 = CmeDriverI;
    }

    rtb_Switch3 += CmfP;
  }

  /* End of Switch: '<S8>/Switch3' */

  /* MinMax: '<S8>/MinMax' incorporates:
   *  Constant: '<S8>/ZERO_CMII'
   *  DataStoreWrite: '<S4>/Data Store Write4'
   */
  if (0 > rtb_Switch3) {
    CmiDriverIQS = 0;
  } else {
    CmiDriverIQS = rtb_Switch3;
  }

  /* End of MinMax: '<S8>/MinMax' */

  /* Product: '<S8>/Divide' incorporates:
   *  DataStoreWrite: '<S4>/Data Store Write3'
   *  DataStoreWrite: '<S4>/Data Store Write4'
   *  Inport: '<Root>/CutoffGain'
   */
  CmiDriverI = (int16_T)((CmiDriverIQS * CutoffGain) >> 10);

  /* MinMax: '<S8>/MinMax1' incorporates:
   *  Constant: '<S8>/ZERO_CMII'
   *  DataStoreWrite: '<S4>/Data Store Write2'
   */
  if (rtb_Add > 0) {
    CmiDriverP = rtb_Add;
  } else {
    CmiDriverP = 0;
  }

  /* End of MinMax: '<S8>/MinMax1' */
  /* End of Outputs for SubSystem: '<S4>/Cme2Cmi' */
}

/* Output and update for function-call system: '<S1>/Init' */
void CmiDriverMgm_Init(void)
{
  /* DataStoreWrite: '<S2>/Data Store Write1' incorporates:
   *  Constant: '<S2>/ZERO1'
   */
  CmiDriverI = 0;

  /* DataStoreWrite: '<S2>/Data Store Write7' incorporates:
   *  Constant: '<S2>/ZERO'
   */
  CmiDriverP = 0;

  /* DataStoreWrite: '<S2>/Data Store Write8' incorporates:
   *  Constant: '<S2>/ZERO4'
   */
  CmiDriverIQS = 0;

  /* Constant: '<S2>/ID_CMIDRIVER_MGM' */
  IDCmiDriverMgm = ID_CMIDRIVER_MGM;

  /* user code (Output function Trailer) */

  /* System '<S1>/Init' */

  /* PILOTAGGIO USCITE - INIT */
  /* Read static variables */
  /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
  CmiDriverMgm_initialize();
}

/* Output and update for function-call system: '<S1>/Off' */
void CmiDriverMgm_Off(void)
{
  /* user code (Output function Trailer) */

  /* System '<S1>/Off' */
  /* PILOTAGGIO USCITE - OFF */
}

/* Model step function */
void CmiDriverMgm_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/CmiDriverMgm' */
  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((CmiDriverMgm_U.ev_PowerOn > 0) &&
      (CmiDriverMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    CmiDriverMgm_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  CmiDriverMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (CmiDriverMgm_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((CmiDriverMgm_U.ev_T10ms > 0) &&
      (CmiDriverMgm_PrevZCSigState.trig_to_fc_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    CmiDriverMgm_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  CmiDriverMgm_PrevZCSigState.trig_to_fc_Trig_ZCE = (ZCSigState)
    (CmiDriverMgm_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOff' */
  if ((CmiDriverMgm_U.ev_PowerOff > 0) &&
      (CmiDriverMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Off'
     */
    CmiDriverMgm_Off();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  CmiDriverMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (CmiDriverMgm_U.ev_PowerOff > 0);

  /* End of Inport: '<Root>/ev_PowerOff' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */
  /* End of Outputs for SubSystem: '<Root>/CmiDriverMgm' */
}

/* Model initialize function */
void CmiDriverMgm_initialize(void)
{
  CmiDriverMgm_PrevZCSigState.trig_to_fc_Trig_ZCE = POS_ZCSIG;
  CmiDriverMgm_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  CmiDriverMgm_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<S1>/T10ms' */
#endif                                 // _BUILD_CMIDRIVERMGM_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
