/*
 * File: IonLambda_data.c
 *
 * Real-Time Workshop code generated for Simulink model IonLambda.
 *
 * Model version                        : 1.657
 * Real-Time Workshop file version      : 6.3  (R14SP3)  26-Jul-2005
 * Real-Time Workshop file generated on : Wed May 23 15:23:33 2007
 * TLC version                          : 6.3 (Aug  5 2005)
 * C source code generated on           : Wed May 23 15:23:38 2007
 */

#include "IonLambda_Y.h"
#include "IonLambda_private_Y.h"

#ifdef _BUILD_IONLAMBDA_

/* Invariant block signals (auto storage) */
ConstBlockIO_IonLambda IonLambda_ConstB = {
  16777216 ,                            /* <S3>/Data Type Conversion */
  0U ,                                  /* <S3>/Data Type Conversion1 */
  0U ,                                  /* <S3>/Data Type Conversion2 */
  0U ,                                  /* <S93>/Conversion4 */
  0U                                    /* <S92>/Conversion4 */
};

/* Constant parameters (auto storage) */
const ConstParam_IonLambda IonLambda_ConstP = {
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_16'
   * '<S113>/Memory2'
   */
  { 16777216, 16777216, 16777216, 16777216, 16777216, 16777216, 16777216,
    16777216 } ,
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_18'
   * '<Root>/_DataStoreBlk_3'
   * '<Root>/_DataStoreBlk_6'
   * '<Root>/_DataStoreBlk_9'
   */
  { 65536U, 65536U, 65536U, 65536U, 65536U, 65536U, 65536U, 65536U } ,
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_4'
   * '<Root>/_DataStoreBlk_5'
   * '<Root>/_DataStoreBlk_7'
   * '<Root>/_DataStoreBlk_8'
   */
  { 1048576U, 1048576U, 1048576U, 1048576U, 1048576U, 1048576U, 1048576U,
    1048576U } ,
  /* Computed Parameter: InitialValue
   * '<Root>/_DataStoreBlk_2'
   */
  { 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
    1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
    1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
    1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U } ,
  /* Computed Parameter: InitialValue
   * Referenced by blocks:
   * '<Root>/_DataStoreBlk_15'
   * '<Root>/_DataStoreBlk_17'
   * '<S113>/Memory1'
   */
  { 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U } ,
  /* Computed Parameter: Value
   * Referenced by blocks:
   * '<S108>/ONES_N_CYL_MAX'
   * '<S108>/DelayOut3'
   * '<S108>/DelayOut4'
   */
  { 1U, 1U, 1U, 1U, 1U, 1U, 1U, 1U } ,
  /* Computed Parameter: X0
   * '<S106>/DelayOut1'
   */
  { 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U } ,
  /* Computed Parameter: X0
   * '<S108>/DelayOut1'
   */
  { 1, 1, 1, 1, 1, 1, 1, 1 }
};

/* File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */

#endif // _BUILD_IONLAMBDA_
