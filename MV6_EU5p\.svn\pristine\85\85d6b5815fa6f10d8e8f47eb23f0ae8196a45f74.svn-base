// Include 
#include "typedefs.h"
#include "ETPU_VrsDefs.h"
#include "sys.h"
#include "adc.h"
#include "mathlib.h"
#include "sync.h"


/* Tasks */
#include "OS_api.h"
#include "tasksdefs.h"

#ifdef _BUILD_ANGTRIGTEST_





uint32_t isr_cntr = 0;

uint16_t syncAngle = 270*DEGREE_PRECISION;

#define AN_CH AN_6

extern volatile uint8_T  StSync;

void AngAcqDD_Init(void)
{
    SYS_ADC_SourceConfig(ANGULAR_SAMPLING, TIME_BASE);
    AngAcqDD_StartOfAcq();
}

void AngAcqDD_EndOfAcq(void)
{
    SYS_ADC_SetStatus(ANGULAR_SAMPLING, TRIGGER_OFF);
}

void FuncMapAngAcq_EndOfAcq(void)
{
    AngAcqDD_EndOfAcq();
    isr_cntr++;

    if(StSync!=SYNCH)
    {
        AngAcqDD_StartOfAcq();
    }


    TerminateTask();
}

void AngAcq_StartOAcq_Ex_Angle(void)
{
    AngAcqDD_StartOfAcq();

    SYNC_SetNextAngle(syncAngle);
}

void AngAcqDD_StartOfAcq(void)
{
    int16_T errtmp;

    if (StSync != SYNCH)
    {
        errtmp = SYS_ADC_SetAngTrigger(startAngle_T, trigPeriod);
    }
    else
    {
        errtmp = SYS_ADC_SetAngTrigger(startAngle_T, 0);
    }
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 47;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
       //Inizializzo ed abilito canale AD
        errtmp = ADC_Init(AN_CH, sampleBuff, NULL, 3, ANGULAR_SAMPLING);
        if (errtmp < 0)
        {
            SETBIT(EE_BiosErr,ADC_IDX);
            debugADC_Error_2 = 48;
            debugADC_ErrorCnt_2++;
        }
        else /* no error */
        {
            errtmp = ADC_Enable(ANGULAR_SAMPLING);

            if (errtmp < 0)
            {
                SETBIT(EE_BiosErr,ADC_IDX);
                debugADC_Error_2 = 49;
                debugADC_ErrorCnt_2++;
            }
            else /* no error */
            {
                //Inizializzo ed abilito trigger del canale AD
                errtmp = SYS_ADC_SetStatus(ANGULAR_SAMPLING, TRIGGER_ON);    
                if (errtmp < 0)
                {
                    SETBIT(EE_BiosErr,ADC_IDX);
                    debugADC_Error_2 = 50;
                    debugADC_ErrorCnt_2++;
                }
            }
        }
    }
}

#endif // _BUILD_ANGTRIGTEST_
