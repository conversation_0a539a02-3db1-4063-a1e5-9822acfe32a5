/*
 * File: SpeedLimCtrl.h
 *
 * Code generated for Simulink model 'SpeedLimCtrl'.
 *
 * Model version                  : 1.725
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Feb 17 15:04:15 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Passed (30), Warnings (3), Error (0)
 */

#ifndef RTW_HEADER_SpeedLimCtrl_h_
#define RTW_HEADER_SpeedLimCtrl_h_
#ifndef SpeedLimCtrl_COMMON_INCLUDES_
# define SpeedLimCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "mathlib.h"
#endif                                 /* SpeedLimCtrl_COMMON_INCLUDES_ */

#include "SpeedLimCtrl_types.h"

/* Includes for objects with custom storage classes. */
#include "SpeedLimCtrl_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKGNSPDLIMACC_dim              3U                        /* Referenced by: '<S49>/BKGNSPDLIMACC_dim' */

/* Dim of BKGNSPDLIMACC */
#define BKGNSPDLIMSAT_dim              5U                        /* Referenced by: '<S49>/BKGNSPDLIMSAT_dim' */

/* Dim of BKGNSPDLIMSAT */
#define BKKISPDLIM_dim                 6U                        /* Referenced by: '<S34>/BKKISPDLIM_dim' */

/* Dim of BKKISPDLIM */
#define BKOFFCMISPDLIMI_dim            3U                        /* Referenced by: '<S34>/BKOFFCMISPDLIMI_dim' */

/* Dim of BKOFFCMISPDLIMI */
#define ID_SPEED_LIM_CTRL              28940698U                 /* Referenced by: '<S3>/ID_SPEED_LIM_CTRL' */

/* mask */

/* Block signals and states (default storage) for system '<S12>/fc_Ctrl' */
typedef struct {
  int32_T Memory1_PreviousInput;       /* '<S17>/Memory1' */
  int32_T Memory_PreviousInput;        /* '<S23>/Memory' */
  int32_T Memory_PreviousInput_kjp;    /* '<S17>/Memory' */
  int32_T oldSat;                      /* '<S25>/Dir' */
  uint8_T is_active_c1_SpeedLimCtrl;   /* '<S25>/Dir' */
} rtDW_fc_Ctrl_SpeedLimCtrl_T;

/* Block signals and states (default storage) for system '<S12>/fc_Smooth' */
typedef struct {
  int32_T Memory_PreviousInput;        /* '<S21>/Memory' */
  int32_T Memory1_PreviousInput;       /* '<S21>/Memory1' */
} rtDW_fc_Smooth_SpeedLimCtrl_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  rtDW_fc_Smooth_SpeedLimCtrl_T fc_Smooth;/* '<S12>/fc_Smooth' */
  rtDW_fc_Ctrl_SpeedLimCtrl_T fc_Ctrl; /* '<S12>/fc_Ctrl' */
  int16_T MinMax1;                     /* '<S20>/MinMax1' */
  int16_T MinMax;                      /* '<S20>/MinMax' */
  int16_T Memory_PreviousInput;        /* '<S8>/Memory' */
  int16_T Memory1_PreviousInput;       /* '<S8>/Memory1' */
  uint8_T StSpL_gkb;                   /* '<S5>/Speed_Lim_Chart' */
  uint8_T Memory_INIT_ONE_PreviousInput;/* '<S9>/Memory_INIT_ONE' */
  uint8_T is_active_c3_SpeedLimCtrl;   /* '<S5>/Speed_Lim_Chart' */
  uint8_T is_SPL_MGM;                  /* '<S5>/Speed_Lim_Chart' */
  uint8_T is_SPL_ENABLE;               /* '<S5>/Speed_Lim_Chart' */
} D_Work_SpeedLimCtrl_T;

/* Block signals and states (default storage) */
extern D_Work_SpeedLimCtrl_T SpeedLimCtrl_DWork;

/* Model entry point functions */
extern void SpeedLimCtrl_initialize(void);

/* Exported entry point function */
extern void Trig_SpeedLimCtrl_Init(void);

/* Exported entry point function */
extern void Trig_SpeedLimCtrl_T10(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S29>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Duplicate' : Unused code path elimination
 * Block '<S41>/Data Type Duplicate' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Propagation' : Unused code path elimination
 * Block '<S47>/Data Type Duplicate' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate' : Unused code path elimination
 * Block '<S46>/Data Type Duplicate' : Unused code path elimination
 * Block '<S52>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S29>/Conversion' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S38>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S38>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S38>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S38>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S39>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S39>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S39>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S39>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion' : Eliminate redundant data type conversion
 * Block '<S37>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S37>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S37>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S37>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion' : Eliminate redundant data type conversion
 * Block '<S49>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion' : Eliminate redundant data type conversion
 * Block '<S51>/Reshape' : Reshape block reduction
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'SpeedLimCtrl'
 * '<S1>'   : 'SpeedLimCtrl/Model Info'
 * '<S2>'   : 'SpeedLimCtrl/SpeedLimCtrl'
 * '<S3>'   : 'SpeedLimCtrl/SpeedLimCtrl/Init'
 * '<S4>'   : 'SpeedLimCtrl/SpeedLimCtrl/Merger'
 * '<S5>'   : 'SpeedLimCtrl/SpeedLimCtrl/T10ms'
 * '<S6>'   : 'SpeedLimCtrl/SpeedLimCtrl/TP'
 * '<S7>'   : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/Calc_Err'
 * '<S8>'   : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/Calc_Threshold'
 * '<S9>'   : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/Frz_Clutch'
 * '<S10>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/SpL_Disable'
 * '<S11>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/Speed_Lim_Chart'
 * '<S12>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call'
 * '<S13>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/SpL_Disable/Compare To Constant'
 * '<S14>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/SpL_Disable/Compare To Constant1'
 * '<S15>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/SpL_Disable/Compare To Zero'
 * '<S16>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/Merge'
 * '<S17>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl'
 * '<S18>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_NoSat'
 * '<S19>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Sat'
 * '<S20>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_SatOut'
 * '<S21>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Smooth'
 * '<S22>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_ThrSat'
 * '<S23>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain'
 * '<S24>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor'
 * '<S25>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Saturation_Dynamic'
 * '<S26>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem'
 * '<S27>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem1'
 * '<S28>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem2'
 * '<S29>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem1/RateLimiter_S32_1'
 * '<S30>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem1/RateLimiter_S32_1/Data Type Conversion Inherited1'
 * '<S31>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem2/RateLimiter_S32_1'
 * '<S32>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Gain/If Action Subsystem2/RateLimiter_S32_1/Data Type Conversion Inherited1'
 * '<S33>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/Calc_Post_Gain'
 * '<S34>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/Calc_Ratio'
 * '<S35>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/LookUp_IR_S16_1'
 * '<S36>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/LookUp_IR_S16_2'
 * '<S37>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/LookUp_IR_S16_3'
 * '<S38>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/Calc_Ratio/PreLookUpIdSearch_S16_1'
 * '<S39>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/Calc_Ratio/PreLookUpIdSearch_S16_2'
 * '<S40>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S41>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/LookUp_IR_S16_2/Data Type Conversion Inherited3'
 * '<S42>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Calc_Torque_Factor/LookUp_IR_S16_3/Data Type Conversion Inherited3'
 * '<S43>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Saturation_Dynamic/Dir'
 * '<S44>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Ctrl/Saturation_Dynamic/Saturation'
 * '<S45>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Smooth/RateLimiter_S32_1'
 * '<S46>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Smooth/RateLimiter_S32_3'
 * '<S47>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Smooth/RateLimiter_S32_1/Data Type Conversion Inherited1'
 * '<S48>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_Smooth/RateLimiter_S32_3/Data Type Conversion Inherited1'
 * '<S49>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_ThrSat/Calc_GnSpdLimSat'
 * '<S50>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_ThrSat/Set_GearRatio'
 * '<S51>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_ThrSat/Calc_GnSpdLimSat/Look2D_U16_S16_S16_1'
 * '<S52>'  : 'SpeedLimCtrl/SpeedLimCtrl/T10ms/func_Call/fc_ThrSat/Calc_GnSpdLimSat/Look2D_U16_S16_S16_1/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': SpeedLimCtrl
 */
#endif                                 /* RTW_HEADER_SpeedLimCtrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
