/** #############################################################################
**     Filename  : VSRAM.H
**     Processor : MPC5554 / MPC5534
**        
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 11/04/2007
**     Abstract  :
**          This file implements the low level methods for Virtual Standby RAM
**          management.          
**     
**     Settings  :          
**          
** ##############################################################################*/

#ifndef _VSRAM_H_
#define _VSRAM_H_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

#define VSRAM_CHECKSUM_ERROR      -1 
  

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*
** ===================================================================
**     Method      :  VSRAM_ComputeCheckSum(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : VSRAM Error Code
** ===================================================================
*/  
int16_t VSRAM_ComputeCheckSum(uint32_t *checkSumVal);

/*
** ===================================================================
**     Method      :  int16_t VSRAM_InitAllMemory(...)
**
**     Description :
**         
**         
**     Parameters  : None
**     Returns     : VSRAM Error Code
** ===================================================================
*/

int16_t VSRAM_InitAllMemory(uint32_t vsramInitWord);

/*
** ===================================================================
**     Method      :  VSRAM_WriteCheckSum(...)
**
**     Description :
**
**         
**     Parameters  : None
**     Returns     : VSRAM Error Code
** ===================================================================
*/

int16_t VSRAM_WriteCheckSum(uint32_t checkSumVal);

/*
** ===================================================================
**     Method      :  VSRAM_ReadCheckSum(...)
**
**     Description :
**
**
**     Parameters  : None
**     Returns     : VSRAM Error Code
** ===================================================================
*/

int16_t VSRAM_ReadCheckSum(uint32_t *checkSumVal);


#endif  /* _VSRAM_H_ */
