#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#ifdef _BUILD_FLASHMGM_

#ifdef __MWERKS__
#pragma force_active on
#pragma section RW ".calib" ".calib"
#else
#pragma ghs section rodata=".calib"
#endif

//time to start crc count in the background task (ms)
__declspec(section ".calib") uint16_t TESTCRCPERIODCNT =  600;   // (600*100) ms
// waiting time before restarting the new partial CRC calculation
__declspec(section ".calib") uint8_t  WAITCNT =  100;
// Flash test Rpm threshold
__declspec(section ".calib") uint16_t THRRPMTFLASH = 15000;

#endif /* _BUILD_FLASHMGM_ */

