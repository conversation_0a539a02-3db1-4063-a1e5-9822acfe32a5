/** ###################################################################
**     Filename  : TASK.H
**     Project   :
**     Processor : MPC5554
**     Version   :
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 28/02/2005, 12.24
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ###################################################################*/

#ifndef __TASK_H__
#define __TASK_H__


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "OS_api.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* ------------------------------------------------------------------------- */
/* INTC internal defines                                                     */
/* ------------------------------------------------------------------------- */
#define FIFO_HANDLER_DIM	     6	 /* max dim. of software FIFO ISR list   */
#define SOFT_SET_INTERRUPT_NUM   8   /* max number of software settable 	 */
                                     /* interrupt request                    */

#define PSR_VECTOR_DIM         308   /* number of effective Interrupt Request*/ 
                                     /* Source                               */

#define S_IRQ0  0					 /* Software settable interrupts Request */
#define S_IRQ1  1					 /* index                                */
#define S_IRQ2  2
#define S_IRQ3  3
#define S_IRQ4  4
#define S_IRQ5  5
#define S_IRQ6  6
#define S_IRQ7  7
/* ------------------------------------------------------------------------- */

#define CHANNEL_ALREADY_CONFIGURED  255
#define MAX_DELAY_TIME              150000 /* usec before  */
#define MIN_DELAY_TIME              20     /* usec */
#define MAX_DELAY_TIME_EXCEEDED     254
#define MIN_DELAY_TIME_FAILED       253

#define TASKDELAY_CHANNEL_0  TASKDELAY_0_EMIOS_CHANNEL
#define TASKDELAY_CHANNEL_1  TASKDELAY_1_EMIOS_CHANNEL
#define TASKDELAY_CHANNEL_2  TASKDELAY_2_EMIOS_CHANNEL
#define TASKDELAY_CHANNEL_3  TASKDELAY_3_EMIOS_CHANNEL
#define TASKDELAY_CHANNEL_4  TASKDELAY_4_EMIOS_CHANNEL

#define TASKDELAY_CNT_BUS_CH_0_3  EMIOS_UC0
#define TASKDELAY_CNT_BUS_CH_4    EMIOS_UC8

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* ------------------------------------------------------------------------- */
/* Macro definition for INTC Registers - (a)represents the vector            */
/*                                       index,i.e. the interrupt            */
/*                                       request sources.                    */
/* ------------------------------------------------------------------------- */
#define SET_SSCIR(a)  INTC.SSCIR[a].B.SET/* Set software Interrupt set bit	 */
#define CLR_SSCIR(a)  INTC.SSCIR[a].B.CLR/* Set software Interrupt clear bit */
#define INTC_PSR(a)   INTC.PSR[a].B.PRI	 /* Set INTC priority select register*/
/* ------------------------------------------------------------------------- */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

typedef void (*tFunc)(void);

typedef struct
{
  tFunc bufferISR[FIFO_HANDLER_DIM];
  uint8_t   r_index;
  uint8_t   w_index;
} tBuff;

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/

/* ------------------------------------------------------------------------- */
/* Interrupt Service Routine prototype definitions                           */
/* ------------------------------------------------------------------------- */
void RoutineISR0(void);
void RoutineISR1(void);
void RoutineISR2(void);
void RoutineISR3(void);
void RoutineISR4(void);
void RoutineISR5(void);
void RoutineISR6(void);
void RoutineISR7(void);
/* ------------------------------------------------------------------------- */

/* ------------------------------------------------------------------------- */
/* TASK Service Function                                                     */
/* ------------------------------------------------------------------------- */

/*
** ============================================================================
**     Method      :  TASK_Init
**
**     Description :
**         This method will setup the FIFO handler.
**
**     Parameters  : None
**     Returns     : None
** ============================================================================
*/
void TASK_Init(void);

/*
** ============================================================================
**     Method      :  TASK_Config
**
**     Description :
**         This method will setup the ISR on the vector table and it will
**         also sets the Priority Status Register for the given source
**
**     Parameters  : None
**     Returns     : None
** ============================================================================
*/
void TASK_Config(void);

/*
** ============================================================================
**     Method      :  TASK_TaskHandlingFunctionDelayed
**
**     Description :
**         
**     Parameters  : 
**     Returns     : 
** ============================================================================
*/
int16_t TASK_TaskHandlingFunctionDelayed(TaskType tFun,
                                         uint32_t delay);

/*
** ============================================================================
**     Method      :  TASK_DisablePeripherals
**
**     Description :
**
**     Parameters  : None
**     Returns     : None
** ============================================================================
*/
void TASK_DisablePeripherals(void);


/*--------------------------------------------------------------------------*
 * InitTaskDelay - Initialize TaskDelay Big channels of EMIOS in order to control relative Buses
 *                      used from Medium channel
 *                      TASK_DELAY 0 - 3 uses Big Channel Bus B
 *                      TASK_DELAY 4      uses Big Channel Bus C
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_t InitTaskDelay(void);


/*
** ============================================================================
**     Method      :  SetTaskDelay
**
**     Description : Sets delay to emios channel and task to be executed on the 
**                   same channel
**
**     Parameters  : EMIOS channel, TASK_ID, delay in usec
**     Returns     : return error if channel is already used/not free and if 
**                   delay > MAX_DELAY_TIME or delay < MIN_DELAY_TIME
**	  Example: taskDelayError = SetTaskDelay(TASKDELAY_CHANNEL_0, TaskDelayedEmios0ID, 100);
** ============================================================================

*/
int16_t SetTaskDelay(uint8_t _emiosChannel,
                     TaskType _TaskID, 
                     uint32_t _delay_in_us);


#endif /* __TASK_H__ */
