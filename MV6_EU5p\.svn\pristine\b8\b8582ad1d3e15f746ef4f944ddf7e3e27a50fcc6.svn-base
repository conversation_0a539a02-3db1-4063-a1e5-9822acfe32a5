/*  File    : thrposmgm.h
 *  Author  : <PERSON><PERSON><PERSON>
 *  Date    : 19/01/2007 13.58
 *  Revision: ThrPosMgm 1.0
 *    Note      : prima versione
 * 
 *  Copyright 2006 Eldor Corporation
 */
#ifndef _THRPOSMGM_H_
#define _THRPOSMGM_H_

#define USE_ANGTHROTTLE_CAN        0
#ifndef MAX_PERC
#define MAX_PERC                            1600    //[0-100]     %         res 1/16         [0-1600]
#endif
#define MAX_VANGTHROTTLE            1024    // 5000 mV

// StThrActive
#define USE_THR_1            0
#define USE_THR_2            1
#define USE_THR1_OLD    2
#define USE_THR2_OLD    3

// RecThrReq
#define NO_THRREC                0
#define TRQ_THRREC            1
#define DBWOFF_THRREC        2

//INPUT
extern uint8_T  VDAngThrottleCAN;
extern uint16_T AngThrottleCAN;
extern uint16_T VAngThrottle1;
extern uint16_T VAngThrottle2;
extern uint16_T VAngThrLh1;
extern uint16_T VAngThrLh2;
extern uint16_T VAngThrMin1;
extern uint16_T VAngThrMin2;
extern uint8_T  FlgPresCoh1;
extern uint8_T  FlgPresCoh2;

/* public variables */
extern uint16_T AngThrottle0;                      // 2^-4
extern uint16_t AngThrottle;
extern uint8_T  recthrreq_freeze;
extern uint8_T  RecThrReq;
extern uint8_T  StThrRec;
extern uint8_T  StThrActive;                    // 2^0
extern uint16_T AngThrottle1;              // 2^-4
extern uint16_T AngThrottle2;                      // 2^-4
extern int16_T  DAngThrottle;              // 2^-4
extern uint16_T VAngThrottle;

//EVENTS PROTOTYPES
void ThrPosMgm_Init(void);
void ThrPosMgm_T5ms(void);
void ThrPosMgm_RecoveryTable(uint8_T StDiagVAngThr1, uint8_T StDiagVAngThr2, uint8_T StDiagCohVAngThr);

#endif
