/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _OSEK_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "OS_api.h"
#include "OS_resources.h"
#include "OS_alarms.h"
#include "OS_Hooks.h"
#include "OS_errors.h"
//#include "digio.h"
#include "utils.h"
#include "Tasksdefs.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
TaskCBS  OsTaskTable[OSNUMTSKS];         /* Tasks table     */
AlmCBS   OsAlmTable[OSNUMALMS];          /* Alarms table    */

uint32_t terminationAddress;
StatusType runningTaskId;

AppModeType  OS_ActiveMode;
vuint8_t   OsrtiRunningServiceId;
vuint8_t       OsrtiOldServiceId;
OSSERVICEIDTYPE        OsService;        /* for OSErrorGetServiceId() from ErrorHook */
uint32_t                 OsObjId;        /* for first parameter                      */
uint8_t           OsSuspendLevel;


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define STCKPTRDFLTVAL  0


/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * GetApplicationTaskTable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void GetApplicationTaskTable (void ** taskTable, 
                                      uint32_t * tableSize); 

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/***************************************************************************
 * Function : StartOS
 *
 * Description: OS data initialization from configuration tables
 *
 * Returns: none
 *
 * Notes:
 *
 **************************************************************************/
void StartOS( AppModeType mode )
{
    uint8_t  taskCounter,resCounter,almCounter;
    uint32_t taskTableSize;
    TaskCfg *taskTable;


    OS_ActiveMode = mode;
    OSCLEARPARAM();
    OsSuspendLevel  = 0;

    /* boot_data_section_init(); */
    app_data_section_init();

    /* -------- Resources section ---------- */
    for (resCounter = 0; resCounter < OSNUMRES; resCounter++)
    {
        OSRESID(resCounter)       = OsResCfgTable[resCounter].ResID;      
        OSRESTASKID(resCounter)   = NULLTASK;
        OSRESPRI(resCounter)      = OsResCfgTable[resCounter].ResPri;
        OSRESSTATE(resCounter)    = RES_FREE;
        OSRESCURRPRI(resCounter)  = DEFAULT_CURR_PRI;
    }

    if (mode == OSAPPMODE)
    {
        /* -------- Alarms section ---------- */
        for (almCounter = 0; almCounter < OSNUMALMS; almCounter++)
        {
            OSALMTASKID(almCounter)  =  OsAlmCfgTable[almCounter].TaskId;
            OSALMCNTRID(almCounter)  =  OsAlmCfgTable[almCounter].cntrId;
            OSALMSTATE(almCounter)   =  ALM_FREE; 
            OSALMACTION(almCounter)  =  OsAlmCfgTable[almCounter].action;
        }

        for (almCounter = 0; almCounter < OSNUMALMS; almCounter++)
        {
            SetRelAlarm(OsAlmTable+almCounter,  TIMING_RESOLUTION, OsAlmCfgCycleTable[almCounter]);
        }


        GetApplicationTaskTable((void**)&taskTable, &taskTableSize);

        for (taskCounter = 0; taskCounter < taskTableSize; taskCounter++)
        {
            OSTASKENTRY(taskCounter)    = taskTable[taskCounter].entry; /* entry point of task */
            OSTASKID(taskCounter)       = taskTable[taskCounter].tskId;
            OSTASKPRI(taskCounter)      = taskTable[taskCounter].pri;
            OSTASKSTATUS(taskCounter)   = SUSPENDED;
            OSTASKSTACKPTR(taskCounter) = STCKPTRDFLTVAL;
        }
    }

    /* -------- Tasks section ---------- */
    OSInitHandlerManager();
    
    runningTaskId = NULLTASK;
    terminationAddress = 0;   

    StartupHook();

}


/***************************************************************************
 * Function : ShutdownOS
 *
 * Description: 
 *
 * Returns: none
 *
 * Notes:
 *
 **************************************************************************/
void ShutdownOS(StatusType error)
{
    DisableAllInterrupts(); /* Disable external interrupts      */
    ShutdownHook(error);
    EnableAllInterrupts();  /* Re-enable external interrupts    */

    {
        Delay_ms(100);
        SYS_SwRST();
    }
}


/***************************************************************************
 * Function : ShutdownOSerrorHandler
 *
 * Description: This method handle different types of shutdown, avoiding 
 *              recursive calls of ShutdownOS function 
 *
 * Returns: none
 *
 * Notes:
 *
 **************************************************************************/
void ShutdownOSerrorHandler( StatusType error)
{
	switch (error)
	{
		case(E_OS_SYS_KEY_OFF):
			EEMGM_EETaskCmd();
			TaskPowerOff();
			break;
		case E_OS_TIMEDOUT_KEY_OFF:
			TaskPowerOff();
			break;
		case(E_OS_SYS_IVOR_ERROR):
			SYS_IvorExHandling();
#ifdef  _BUILD_VSRAMMGM_
			VSRAMMGM_Update();
#endif /* _BUILD_VSRAMMGM_ */ 
			EEMGM_EETaskCmd();
			SYS_SwRST();
			break; 
#ifdef _BUILD_SAF2MGM_
		case(E_OS_SYS_SAF2_SHTDWN):
#endif /* _BUILD_SAF2MGM_ */
		case(E_OS_ATI_SW_DWLOAD):
		case(E_OS_KWP2000_SW_DWLOAD):
			EEMGM_EETaskCmd();
			SYS_SwRST();
			break;
		default:
			SYS_SwRST();
			break;
	}
}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/***************************************************************************
 * Function : GetApplicationTaskTable
 *
 * Description: 
 *
 * Returns: none
 *
 * Notes:
 *
 **************************************************************************/
static void GetApplicationTaskTable (void ** taskTable,
                                     uint32_t * tableSize) 
{
    *taskTable = (void*)OsTaskCfgTable;
    *tableSize = MAX_NUM_TASK;  /* from enum TaskID */
}

#endif /* _OSEK_ */  
