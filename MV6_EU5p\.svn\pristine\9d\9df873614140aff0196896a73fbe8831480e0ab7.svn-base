/*
 * File: Trc2WZero_private.h
 *
 * Code generated for Simulink model 'Trc2WZero'.
 *
 * Model version                  : 1.252
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Sep 30 10:26:41 2020
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_Trc2WZero_private_h_
#define RTW_HEADER_Trc2WZero_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "Trc2WZero.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "canmgm.h"
#include "digitalin.h"
#include "Diagcanmgm.h"
#include "gaspos_mgm.h"
#include "trq_driver.h"
#include "gear_mgm.h"
#include "syncmgm.h"
#include "PTrain_Diag.h"
#include "vspeed_mgm.h"
#include "gasposfilt_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern uint16_T DEFTCZERO;             /* Variable: DEFTCZERO
                                        * Referenced by:
                                        *   '<S2>/Sat'
                                        *   '<S4>/Chart_Trc2WZero'
                                        *   '<S14>/DEFTCZERO'
                                        * gain
                                        */
extern uint16_T TOLLTCZEROMAX;         /* Variable: TOLLTCZEROMAX
                                        * Referenced by: '<S12>/Sat'
                                        * tollerance gain max
                                        */
extern uint16_T TOLLTCZEROMIN;         /* Variable: TOLLTCZEROMIN
                                        * Referenced by: '<S12>/Sat'
                                        * tollerance gain min
                                        */
extern uint16_T DBTC2WZERO;            /* Variable: DBTC2WZERO
                                        * Referenced by: '<S16>/DBTC2WZERO'
                                        * Db stab
                                        */
extern uint16_T DBTC2WZERO2;           /* Variable: DBTC2WZERO2
                                        * Referenced by: '<S12>/DBTC2WZERO2'
                                        * Db stab
                                        */
extern uint16_T THRTC2WZEROHI;         /* Variable: THRTC2WZEROHI
                                        * Referenced by:
                                        *   '<S15>/THRTC2WZEROHI'
                                        *   '<S16>/THRTC2WZEROHI'
                                        * Thr max
                                        */
extern uint16_T THRTC2WZEROLO;         /* Variable: THRTC2WZEROLO
                                        * Referenced by: '<S16>/THRTC2WZEROLO'
                                        * Thr min
                                        */
extern uint16_T MAXROLLTC2WZ;          /* Variable: MAXROLLTC2WZ
                                        * Referenced by: '<S15>/MAXROLLTC2WZ'
                                        * roll max
                                        */
extern uint16_T TIMTC2WZSTAB;          /* Variable: TIMTC2WZSTAB
                                        * Referenced by: '<S16>/TIMTC2WZSTAB'
                                        * counter
                                        */
extern uint16_T TIMTC2WZSTAB2;         /* Variable: TIMTC2WZSTAB2
                                        * Referenced by: '<S12>/TIMTC2WZSTAB2'
                                        * counter
                                        */
extern uint16_T TIMTPMSNODE;           /* Variable: TIMTPMSNODE
                                        * Referenced by: '<S26>/TPMS_Ack'
                                        * tim
                                        */
extern uint8_T ENTC2WZERORET;          /* Variable: ENTC2WZERORET
                                        * Referenced by: '<S4>/Chart_Trc2WZero'
                                        * selector
                                        */
extern uint8_T FORCETC2WZTST;          /* Variable: FORCETC2WZTST
                                        * Referenced by: '<S11>/FORCETC2WZTST'
                                        * selector
                                        */
extern uint8_T TIMTC2WZERO;            /* Variable: TIMTC2WZERO
                                        * Referenced by: '<S4>/Chart_Trc2WZero'
                                        * counter
                                        */
extern void Trc2WZero_Calc_Ack(uint8_T rtu_resetAck, uint8_T rtu_GearPos,
  uint16_T rtu_VehSpeed, uint8_T rtu_ClutchSignal, uint16_T rtu_Rpm, uint16_T
  rtu_AbsRollCAN, uint16_T rtu_GasPos, uint16_T rtu_GasPosCC, uint8_T
  rtu_BrakeSignal, uint8_T rtu_BrakeSignalCAN, boolean_T *rty_ackOn, uint8_T
  *rty_ackCond, uint8_T *rty_stabReset, uint16_T *rty_VehSpeedTC2WZero,
  rtDW_Calc_Ack_Trc2WZero_T *localDW);
extern void Trc2WZero_Calc_Diag_Init(rtDW_Calc_Diag_Trc2WZero_T *localDW);
extern void Trc2WZero_Calc_Diag(const uint8_T rtu_StDiag[80], uint8_T
  rtu_TPMSPre, uint8_T rtu_TPMSLoc, uint8_T rtu_TPMSSda, uint8_T rtu_TPMSRst,
  uint8_T rtu_TPMSWfc, uint8_T rtu_TPMSNode, uint8_T rtu_FlgYawRec, boolean_T
  *rty_diag, rtDW_Calc_Diag_Trc2WZero_T *localDW);
extern void Trc2WZero_Calc_VSFGain(uint8_T rtu_resetOff, uint8_T rtu_stabReset,
  uint16_T rtu_VehSpeedRear, uint16_T rtu_VehSpeedFrontNz,
  rtDW_Calc_VSFGain_Trc2WZero_T *localDW);
extern void Trc2WZero_Init(void);
extern void Trc2WZero_T10ms_Init(void);
extern void Trc2WZero_T10ms(void);
extern void Trc2WZero_PreTDC(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint16_T EEGnVehSpeedFront;     /* '<Root>/_DataStoreBlk_3' */

/* VehSpeedFront gain */
extern uint16_T EEGnVehSpeedFrontMax;  /* '<Root>/_DataStoreBlk_7' */

/* VehSpeedFront gain */
extern uint16_T EEGnVehSpeedFrontMin;  /* '<Root>/_DataStoreBlk_8' */

/* VehSpeedFront gain */
#endif                                 /* RTW_HEADER_Trc2WZero_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
