/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#           $   */
/* $ Description:                                                                                                */
/* $Revision:: 1394   $                                                                                          */
/* $Date:: 2009-06-24 17:47:22 +0200 (mer, 24 giu 2009)   $                                                      */
/* $Author:: GelmettiA               $                                                                       */
/*****************************************************************************************************************/


#ifdef _BUILD_DIAGCANMGM_



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "mpc5500_spr_macros.h"
#include "diagcanmgm.h"
#include "tpe.h"
#include "tpe_utils.h"
#include "diagcanee.h"
#include "vsrammgm.h"
#include "TasksDefs.h" //needed for task functions 
#include "digio.h"
#include "digitalin.h"
#include "ee.h"
#include "wdt.h"
#include "activeDiag.h"
#include "dtc.h"
#include "rli.h"
#include "pwrmgm.h"
#include "fuel_mgm.h"
#include "selfmgm.h"
#include "activeDiag.h"
#include "kline_utils.h"
#include "kline_timer.h"
#include "kline_init.h"
#include "PTrain_Diag.h"
#include "eemgm.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

int16_t   response;
uint8_t   control;
uint32_t  flashAddress;
uint32_t  startAddress;
uint32_t  endAddress;
uint8_t   kwp_com_enabled;
uint8_T   COBStartFB;


/* Global Buffers */
T_DataInd_tag  T_DataIND;
T_DataREG_tag  T_DataREQ;


struct    DecAnswer Decode;
struct    message_download_struct  transferRequestParameter;



KWP_ServEraseReqSts_t routineEraseRequested = NO_ERASE_REQ;
routinesByLocalID_t     routinesByLocalID[ROUTINES_NUMBER];

/*  Externals */
extern union DownloadStruct_tag  KWPdownloadStruct;


#ifdef _BUILD_OBD_
extern int8_t OBD_service_error;// = NO_ERROR;
extern uint32_t OBD_services;
extern uint8_t tpe_message_type;
#endif

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * DIAGCANMGM_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void DIAGCANMGM_Init(void);

/*--------------------------------------------------------------------------*
 * DIAGCANMGM_APP_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void DIAGCANMGM_APP_Init(void);

/*--------------------------------------------------------------------------*
 * DIAGCANMGM_SendStatus - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t DIAGCANMGM_SendStatus (void);

/*--------------------------------------------------------------------------*
 * DIAGCANMGM_Receive - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static int16_t DIAGCANMGM_Receive(uint8_t *data, 
                                   uint8_t *data_len);

/*--------------------------------------------------------------------------*
 * DIAGCANMGM_Send - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/ 
 static int16_t DIAGCANMGM_Send (uint8_t *data, 
                                 uint8_t data_len);

/*--------------------------------------------------------------------------*
 * Decoding - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void Decoding (void);

/*--------------------------------------------------------------------------*
 * ResponseEvaluation - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void ResponseEvaluation (uint8_t messageSession);

/*--------------------------------------------------------------------------*
 * DST - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void DST (uint8_t MessageSession);

/*--------------------------------------------------------------------------*
 * EndOfLine - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void EndOfLine(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
#ifdef _BUILD_OBD_
uint32_t SessionEnabledServices = DEFAULT_ENABLED_SERVICES; // variable shall be available for other modules
#else
static uint32_t SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
#endif



/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/

//=============================================================================
//  Method      :  DiagApl
//  Description :  
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : 
//  Funzioni richiamate : 
//=============================================================================
// CAN diagnostic management call this function each 10ms (? correct ?)
void DIAGCANMGM_DiagApl(void)
{
    int16_t status = DIAGCANMGM_SendStatus();



    if(status != DIAGCANMGM_PENDING)
    {
        Decode.message_session = 0;
        Decode.message = 0;

        if(kwp_com_enabled == KLINE_COMM)
        {
            if(status == DIAGCANMGM_ERROR_TIMEOUT_P3) 
            {
                kwp_com_enabled = NO_COMM;
                Reset_All_DIAG();
            }
        }
        else
        {
            if(status == DIAGCANMGM_ERROR_TIMEOUT)     
            {
                kwp_com_enabled = NO_COMM;
                Reset_All_DIAG();
            }
        }

        if((status == DIAGCANMGM_SUCCESS)||(status == DIAGCANMGM_ERROR_TIMEOUT)
        ||((status == DIAGCANMGM_ERROR_TIMEOUT_P3)&&(kwp_com_enabled == KLINE_COMM))
        )
        {
            response = NO_RESPONSE;
        }

        if ((!(DIAGCANMGM_Receive (T_DataIND.Data,&T_DataIND.DataLength))) && (response == NO_RESPONSE))
        {
#ifdef _BUILD_OBD_
            if(tpe_message_type == OBDTYPE)
            {
                OBD_Decoding(); 
                if ((OBD_service_error != RESPONSE_NOT_ALLOWED))// && (OBD_services != PIDS_NOT_SUPPORTED))
                {
                    ResponseEvaluation(Decode.message_session);
                    if (response == POSITIVE_RESPONSE)
                    {
                        control = Decode.message;
                    }
                    else 
                    {
                        //nothing
                    }
                }
                else
                {
                    /*ECU shall not answer!!!*/
                    OBD_service_error = NO_ERROR; // for the next communication
                }
            }
            else if ((tpe_message_type == KWPTYPE))
            {
#endif
                Decoding();
                ResponseEvaluation(Decode.message_session);
#ifdef _BUILD_OBD_
            }
            else
            {
                /* DO NOTHING */
            }
#endif

            if (response == POSITIVE_RESPONSE)
            {
                control = Decode.message;
            }
        }
        
        AnswerFunction();
    }

    DST (Decode.message_session);

    /*Buffer reset*/
    memset(&T_DataIND,0,sizeof(T_DataIND));
}

 
//=============================================================================
//  Method      :  DIAGCANMGM_Init
//  Description :  Initialize KWP comm, as routines, flashing parameters...
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================
void DIAGCANMGM_Init(void)
{    
    Decode.message = 0;
    Decode.message_session = 0;
    response = NO_RESPONSE;
    control = 0;
    transferRequestParameter.memoryAddress = 0 ;
    transferRequestParameter.dataFormatIdentifier = 0;
    transferRequestParameter.unCompressedMemorySize = 0;
    flashAddress = 0;
    startAddress = 0;
    endAddress = 0;


    routinesByLocalID[0].ID      = FLASH_BLOCK_ERASE;
    routinesByLocalID[0].status  = ROUTINE_HALTED;
    routinesByLocalID[0].result  = NO_ERROR;
    routinesByLocalID[0].command = ROUTINE_STOP;

    routinesByLocalID[1].ID      = END_OF_LINE;
    routinesByLocalID[1].status  = ROUTINE_HALTED;
    routinesByLocalID[1].result  = ROUTINE_KO;
    routinesByLocalID[1].command = ROUTINE_STOP;
    FlgEOL=0;

    routinesByLocalID[2].ID      = E_LEAN_CALIB;
    routinesByLocalID[2].status  = ROUTINE_HALTED;
    routinesByLocalID[2].result  = ROUTINE_KO;
    routinesByLocalID[2].command = ROUTINE_STOP;
    COBStartFB = routinesByLocalID[2].status;
}
 
 
//=============================================================================
//  Method      :  DIAGCANMGM_PowerOn
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================
void DIAGCANMGM_PowerOn(void)
{
    DIAGCANMGM_EE_PowerOn();
    DIAGCANMGM_Init();
    DIAGCANMGM_APP_Init();
}


#ifdef _BUILD_KLINE_
//=============================================================================
//  Method      :  startAppDecodeTask
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================
void startAppDecodeTask(void)
{
    ActivateTask(KWPDecodingID); 
}
#endif

//=============================================================================
//  Method      :  startAppDecodeTask
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================
void FuncKWPDecoding(void)
{  
    DIAGCANMGM_DiagApl();  
    TerminateTask();  
}

//=============================================================================
//  Method      :  startAppDecodeTask
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================
void FuncTaskEndOfLine(void)
{  
    FlgEOL=1;
    EndOfLine();  
    TerminateTask();
}



//=============================================================================
//  Method      :  Get_ELean_Calib_Status
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================

uint8_t Get_ELean_Calib_Status(void)
{
  return routinesByLocalID[2].status ;
}


//=============================================================================
//  Method      :  Set_ELean_Calib_Status
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================

void Set_ELean_Calib_Status(uint8_t value)
{
   routinesByLocalID[2].status  = value;
   COBStartFB = value ;
}


//=============================================================================
//  Method      :  ELeanCalib_Mgm
//  Description :  
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : TBD
//  Funzioni richiamate : tpeSend
//=============================================================================
void ELeanCalib_Mgm(void)
{
    switch(routinesByLocalID[2].status)
    {
      case(ROUTINE_HALTED):
      {
          routinesByLocalID[2].status = ROUTINE_IN_EXECUTION;
          routinesByLocalID[2].result = ROUTINE_OK;
      break;
      }

      case(ROUTINE_IN_EXECUTION):
      {
          routinesByLocalID[2].status = ROUTINE_IN_EXECUTION;
          routinesByLocalID[2].result = ROUTINE_KO;
          
      break;
      }

      case(ROUTINE_COMPLETED):
      {
          routinesByLocalID[2].status = ROUTINE_IN_EXECUTION;
          routinesByLocalID[2].result = ROUTINE_OK;
      break;
      }

      
      case(ROUTINE_HALTED_TIMEOUT):
      {
          routinesByLocalID[2].status = ROUTINE_IN_EXECUTION;
          routinesByLocalID[2].result = ROUTINE_OK;
      break;
      }
      default:
      break;        
    }

    COBStartFB = routinesByLocalID[2].status;
    
}


//=============================================================================
//  Method      :  ELeanCalib_Mgm
//  Description :  
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : TBD
//  Funzioni richiamate : tpeSend
//=============================================================================
void ELeanCalib_Result_Response(void)
{
    switch(routinesByLocalID[2].status)
    {
        case(ROUTINE_HALTED):
            NegativeResponseRoutine(CONDITIONS_NOT_CORRECT);
        break;
        
        case(ROUTINE_IN_EXECUTION):
            NegativeResponseRoutine(ROUTINE_NOT_COMPLETE);
        break;
                
        case(ROUTINE_COMPLETED):
            T_DataREQ.Data[1] = T_DataIND.Data[1];
            T_DataREQ.DataLength = 3;
            T_DataREQ.Data[2] = ROUTINE_COMPLETE_OK ;
        break;
        
        case(ROUTINE_HALTED_TIMEOUT):
            T_DataREQ.Data[1] = T_DataIND.Data[1];
            T_DataREQ.DataLength = 3;
            T_DataREQ.Data[2] = ROUTINE_COMPLETE_NOT_OK ;
        break;
        
        default:
        break;
     }
}

//=============================================================================
//  Method      :  AnswerFunction
//  Description :  
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : TBD
//  Funzioni richiamate : tpeSend
//=============================================================================
void AnswerFunction(void)
{
    if (response != NO_RESPONSE)
    {
        DIAGCANMGM_Send(T_DataREQ.Data,T_DataREQ.DataLength);
    }
}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
//=============================================================================
//  Method      :  DIAGCANMGM_Init
//  Description :  
//                 
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : None
//  Funzioni richiamate : None
//=============================================================================
static void DIAGCANMGM_APP_Init(void)
{
    KWPsession = DIAG_OFF_SESSION;   //sessione corrente di diagnosi
    KWPdownloadStruct.CF = 0;
    routineEraseRequested = NO_ERASE_REQ; /* New erase request needed from here */
}

//=============================================================================
//  Method      :  DIAGCANMGM_SendStatus
//  Description :  
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : 
//  Funzioni richiamate : 
//=============================================================================
static int16_t DIAGCANMGM_SendStatus (void)
{
    int16_t returncode=DIAGCANMGM_NOTSUCCESS;

    if(kwp_com_enabled == TPE_COMM)
    {
        returncode= tpeSendStatus();
    }
    if(kwp_com_enabled == KLINE_COMM)
    {
        returncode= klineSendStatus();
    }

    return returncode;
}

//=============================================================================
//  Method      :  DIAGCANMGM_Send
//  Description :  This method sends a KWP message response over K-Lien or CAN bus
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : 
//  Funzioni richiamate : 
//=============================================================================
static int16_t DIAGCANMGM_Send (uint8_t *data, uint8_t data_len)
{
    int16_t returncode=DIAGCANMGM_NOTSUCCESS;

    if(kwp_com_enabled == TPE_COMM) 
    {
        returncode= tpeSend(data, data_len);
    }
    if(kwp_com_enabled == KLINE_COMM)
    {
        returncode= klineSend(data, data_len);
    }

    return returncode;
}

//=============================================================================
//  Method      :  DIAGCANMGM_Receive
//  Description :  This method receives a KWP message request over K-Lien or CAN bus
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : 
//  Funzioni richiamate : 
//=============================================================================
static int16_t DIAGCANMGM_Receive(uint8_t *data, uint8_t *data_len)
{
    int16_t returncode=DIAGCANMGM_NOTSUCCESS;

    if(kwp_com_enabled == TPE_COMM)
    {
        returncode= tpeReceive(data, data_len);
    }
    if(kwp_com_enabled == KLINE_COMM)
    {
        returncode= klineReceive(data, data_len);
    }

    return returncode;
}


//=============================================================================
//  Method      :  Decoding
//  Description :  Questo modulo esegue la decodifica del messaggio di ricevuto
//                 dal tester e la codifica della risposta positiva
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : DiagApl
//  Funzioni richiamate : TBD
//=============================================================================
// Decoding answer section
static void Decoding (void)
{
    Decode.message = 0;
    Decode.message_session = DIAG_MESSAGE_RECEIVED;

    T_DataREQ.Data[0] = T_DataIND.Data[0] + 0x40;  // Positive Response Service Id  0x40 

    switch (T_DataIND.Data[0])              // Request Service Id
    {

        //-----> START_COMMUNICATION
        case SERVICE_START_COMMUNICATION :     // 0x81=> startCommunication  response 0xC1
        {
            if ((SessionEnabledServices & SERVICE_START_COMUNICATION_EN)&&(kwp_com_enabled == KLINE_COMM))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED
                |DIAG_OFF_SESSION
                |DIAG_DEFAULT_SESSION
                |DIAG_DOWNLOAD_SESSION
                |DIAG_SUPPLIER_SESSION;

                if(T_DataIND.DataLength == 1)
                {
                    /****************************************/
                    serviceStartComunication(); 
                    /****************************************/
                } 
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED);
                    KWPdownloadStruct.BF.negativeResponse = ON; 
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }
        //-----> STOP_COMMUNICATION
        case SERVICE_STOP_COMMUNICATION :     // 0x82=> startCommunication  response 0xC2
        {
            if ((SessionEnabledServices & SERVICE_STOP_COMUNICATION_EN)&&(kwp_com_enabled == KLINE_COMM))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;

                if (T_DataIND.DataLength == 1) // Length control
                {
                    /*******************************************/
                    serviceStopComunication();
                    /*******************************************/
                } 
                else 
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED);
                    KWPdownloadStruct.BF.negativeResponse = ON; 
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> START_SESSION
        case SERVICE_START_SESSION :     // 0x10=> startDiagnosticSession  response 0x50
        {
            if ((SessionEnabledServices & SERVICE_START_SESSION_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED | DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;
                /********************************************/
                serviceStartSession();
                /*******************************************/
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }

            break;
        }

        //-----> STOP_SESSION
        case SERVICE_STOP_SESSION :   // 0x20=> stopDiagnosticSession  
        {
            if ((SessionEnabledServices & SERVICE_STOP_SESSION_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;

                if (T_DataIND.DataLength == 1) // Length control
                {
                    /**********************************************************************/
                    serviceStopSession();
                    /*******************************************************************************/
                }
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED);
                    KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;                
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> TESTER_PRESENT
        case SERVICE_TESTER_PRESENT:  // 0x3E=> testerPresent
        {
            if((SessionEnabledServices & SERVICE_TESTER_PRESENT_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;

                if (T_DataIND.DataLength == 1) // Length control
                {
                    Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;
                    /*****************************************/
                    serviceTesterPresent();
                    /****************************************/
                } 
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED);  
                    KWPdownloadStruct.BF.negativeResponse = ON; 
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }

            break;
        }

        //-----> READ_ECU_IDENTIFICATION
        case SERVICE_READ_ECU_IDENTIFICATION:       // 0x1A=> readECUIdentification
        {
            if ((SessionEnabledServices & SERVICE_READ_ECU_IDENTIFICATION_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;


                if (T_DataIND.DataLength == 2)  // Controllo lunghezza
                {
                    /*************************************************/
                    serviceReadEcuIdentification();
                    /*************************************************/
                }
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED);  // 0x33 Security access requested
                    KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> READ_DIAG_TROUBLE_CODE
        case SERVICE_READ_DIAG_TROUBLE_CODE :         // 0x17=> readStatusOfDiagnosticTroubleCodes
        {
            if ((SessionEnabledServices & SERVICE_READ_DIAG_TROUBLE_CODE_EN))
            {         
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION ;
                

                if (T_DataIND.DataLength == 3)       // Controllo lunghezza
                {
                    /**************************************************************/
                    serviceReadDTCStatus();
                    /**************************************************************/
                } 
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED); 
                    KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
                }

                }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }

            break;
        }

        //-----> READ_DIAG_TROUBLE_CODE_STATUS
        case SERVICE_READ_DIAG_TROUBLE_CODE_STATUS :   // 0x18=> readDiagnosticTroubleCodesByStatus
        {
            if ((SessionEnabledServices & SERVICE_READ_DIAG_TROUBLE_CODE_STATUS_EN))
            {
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION;


                if (KWPdownloadStruct.BF.sessionDownloadActive  != ON)
                {
                    if (T_DataIND.DataLength == 4)       // Controllo lunghezza
                    {
                        /********************************************************************/
                        serviceReadDTCbyStatus();
                        /********************************************************************/
                    }
                    else
                    {
                        NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED); 
                        KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
                    }
                }
                else
                {
                    NegativeResponseRoutine(SERVICE_NOT_SUPPORTED);  // 0x33 Security access requested
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> CLEAR_DIAGNOSTIC_INFORMATION
        case SERVICE_CLEAR_DIAGNOSTIC_INFORMATION :   // 0x14=> clearDiagnosticInformation
        {
            if ((SessionEnabledServices & SERVICE_CLEAR_DIAGNOSTIC_INFORMATION_EN)) 
            {         
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION;
                if((Decode.message_session & KWPsession) == 0)
                {
                    NegativeResponseRoutine(SERVICE_NOT_SUPPORTED);  
                    KWPdownloadStruct.BF.negativeResponse = ON; 
                    break;
                }


                if (T_DataIND.DataLength == 3)       // Controllo lunghezza
                {
                    /*******************************************************************/
                    serviceClearDiagnosticInformation();
                    /*******************************************************************/
                }
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED);
                    KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;                
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;     
        }

        //-----> IOCONTROL
        case SERVICE_IOCONTROL : // 0x30=> inputOutputControlByLocalIdentifier
        {
            if ((SessionEnabledServices & SERVICE_IOCONTROL_EN)) 
            {      
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION;

                if (T_DataIND.DataLength == 3 || ((T_DataIND.DataLength == 4) && (T_DataIND.Data[1]== CMD_EXHVALV)))       // Controllo lunghezza
                {
                    /***************************************************************************/
                    serviceIOControlByLocalId();
                    /***************************************************************************/
                }
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED); 
                    KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break; 
        }

        /*******************************************/
        /************  DOWNLOAD SECTION  ***********/
        /*******************************************/
        //-----> REQUEST_DOWNLOAD
        case SERVICE_REQUEST_DOWNLOAD : //0x34=> requestDownload
        {
            if ((SessionEnabledServices & SERVICE_REQUEST_DOWNLOAD_EN))               
            {

                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;

                /************************************************/
                serviceRequestDownload();
                /************************************************/
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> WRITE_DATA_BY_LOCAL_ID
        case SERVICE_WRITE_DATA_BY_LOCAL_ID:  // 0x3B=> writeDataByLocalIdentifier
        {
            if ((SessionEnabledServices & SERVICE_WRITE_DATA_BY_LOCAL_ID_EN))
            {         
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_DOWNLOAD_SESSION | DIAG_SUPPLIER_SESSION;

                if(T_DataIND.DataLength!=1)
                {
                    /**********************************************************************/
                    serviceWriteDataByLocalId();
                    /**********************************************************************/
                }
                else
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED); 
                    KWPdownloadStruct.BF.negativeResponse = ON;// Negative response;
                }
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        
        //-----> READ_DATA_BY_LOCAL_ID
        case SERVICE_READ_DATA_BY_LOCAL_ID:  // 0x21=> readDataByLocalIdentifier
        {
            if ((SessionEnabledServices & SERVICE_READ_DATA_BY_LOCAL_ID_EN))
            {    
                /* Non e' necessario il security access
                * se non per i dati riservati
                */
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION| DIAG_DOWNLOAD_SESSION;



                if (T_DataIND.DataLength == 2)       // Controllo lunghezza
                {  
                    /***********************************************************************/
                    serviceReadDataByLocalId();
                    /***********************************************************************/       
                }
                else /* invalid format */
                {
                    NegativeResponseRoutine(SUBFUNCTION_NOT_SUPPORTED); // 0x33 Security access requested
                    KWPdownloadStruct.BF.negativeResponse = ON;// Negative response;
                }                  
                }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }


        //-----> SECURITY_ACCESS
        case SERVICE_SECURITY_ACCESS:  // 0x27=> securityAccess
        {
            if ((SessionEnabledServices & SERVICE_SECURITY_ACCESS_EN))
            {    
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;

                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_DOWNLOAD_SESSION | DIAG_SUPPLIER_SESSION;
                /*******************************************************************/
                serviceSecurityAccess();        
                /********************************************************************************/
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }


        //-----> START_ROUTINE_BYID
        case SERVICE_START_ROUTINE_BYID:   // 0x31=> startRoutineByLocalIdentifier
        {
            if ((SessionEnabledServices & SERVICE_START_ROUTINE_BYID_EN))
            {    
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;
                /*******************************************/
                serviceStartRoutineByLocalId();
                /********************************************/
                }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> STOP_ROUTINE_BYID
        case SERVICE_STOP_ROUTINE_BYID:  // 0x32=> stopRoutineByLocalIdentifier
        {
            if ((SessionEnabledServices & SERVICE_STOP_ROUTINE_BYID_EN))
            {    

                Decode.message_session |= DIAG_SERVICE_SUPPORTED; 
                Decode.message_session |= DIAG_DEFAULT_SESSION |DIAG_SUPPLIER_SESSION |DIAG_DOWNLOAD_SESSION ;


                /*******************************************************/
                serviceStopRoutineByLocalId();        
                /*******************************************************/
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }
            break;
        }

        //-----> ROUTINE_RES_BYID      
        case SERVICE_REQ_ROUTINE_RES:  // 0x33=> requestRoutineResultsByLocalIdentifier
        {
            if ((SessionEnabledServices & SERVICE_REQ_ROUTINE_RES_EN))
            {    
                Decode.message_session |= DIAG_SERVICE_SUPPORTED;
                Decode.message_session |= DIAG_DEFAULT_SESSION | DIAG_SUPPLIER_SESSION | DIAG_DOWNLOAD_SESSION;

                /**********************************************************/
                serviceRequestRoutineResponse();
                /***********************************************************/
            }
            else
            {
                NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE);  // 0x80 service not supported in current active session
                KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            }

            break;
        }

        default:  // Default dello switch T_DataIND.Data[0] : request Service Id
        {
            NegativeResponseRoutine(SERVICE_NOT_SUPPORTED);  // 0x11 Service not supported in any available session
            KWPdownloadStruct.BF.negativeResponse = ON; // Negative response;
            break;
        }
    }// End switch T_DataIND.Data[0] : request Service Id
}  



//=============================================================================
//  Method      :  ResponseEvaluation
//  Description :  Questo modulo valuta la risposta (nessuna risposta/positiva
//                 /negativa) sulla base dei parametri seguenti
//  Parameters  :  - messageSession: servizio e sessione a cui appartiene il
//                   messaggio
//                 - messageType: tipo di messaggio ricevuto
//                 - KWPsession:  sessione corrente di diagnosi
//  Returns     : uint8_t (i.e. il tipo di risposta)
//  Funzione chiamante  : DiagApl
//  Funzioni richiamate : None
//=============================================================================
static void ResponseEvaluation (uint8_t messageSession)
{
    response = NO_RESPONSE;

#ifndef _BUILD_OBD_ 
    if (KWPdownloadStruct.BF.negativeResponse == ON)
    {
        KWPdownloadStruct.BF.negativeResponse = OFF;
        response = NEGATIVE_RESPONSE;
        // Set OFF download session
        //  TBD!!
        //checkAndStopDownloadSession();
    }
    else if((messageSession & DIAG_NO_RESP_ERASING_FLASH) != 0)
    {
        response = NO_RESPONSE; /*Response will be issued in boot after rst*/
    }
    else if ((messageSession & DIAG_SERVICE_SUPPORTED) == 0)
    {
        NegativeResponseRoutine(SERVICE_NOT_SUPPORTED); // [0x11] serviceNotSupported  
        response = NEGATIVE_RESPONSE;
    }
    else if (((messageSession & 0x0F) != 0) && ((messageSession & KWPsession) == 0) &&(KWPsession == DIAG_OFF_SESSION))
    {                                   // Risposta negativa
        response = NO_RESPONSE;
    }
    else if (((messageSession & 0x0F) != 0) && ((messageSession & KWPsession) == 0))
    {                                   // Risposta negativa
        NegativeResponseRoutine(SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE); // [0x11] serviceNotSupported  
        response = NEGATIVE_RESPONSE;
    }
    else if ((messageSession & 0x0F) == 0)
    {                                   // Risposta negativa
        NegativeResponseRoutine(SERVICE_NOT_SUPPORTED); // [0x12] subFunctionNotSupported 
        response = NEGATIVE_RESPONSE;
    }
    else if((T_DataIND.Data[0] == SERVICE_IOCONTROL) &&(VehSpeed > 0)) 
    {
        NegativeResponseRoutine(CONDITIONS_NOT_CORRECT); // [0x12] subFunctionNotSupported 
        response = NEGATIVE_RESPONSE;
    } 
    else
    {
        response = POSITIVE_RESPONSE;
    }
#else  //_BUILD_OBD_ defined
if ((messageSession & DIAG_SERVICE_SUPPORTED) == 0)
    {
        NegativeResponseRoutine(SERVICE_NOT_SUPPORTED); // [0x11] serviceNotSupported  
        response = NEGATIVE_RESPONSE;
    }
    else if (((messageSession & 0x0F) != 0) && ((messageSession & KWPsession) == 0) && (KWPsession == DIAG_OFF_SESSION))
    {                                   // Risposta negativa
        response = NO_RESPONSE;
    }
    else if ((messageSession & 0x8F) == 0) // OK for OBD and KWP
    {                                   // Risposta negativa
        NegativeResponseRoutine(SERVICE_NOT_SUPPORTED); // [0x12] subFunctionNotSupported 
        response = NEGATIVE_RESPONSE;
    }
    /*  else if((T_DataIND.Data[0] == SERVICE_IOCONTROL) && 
          (VehSpeedSense > 0)) 
    {
        NegativeResponseRoutine(CONDITIONS_NOT_CORRECT); // [0x12] subFunctionNotSupported 
        response = NEGATIVE_RESPONSE;
    } 
    */
    else
    {
        response = POSITIVE_RESPONSE;
    }
#endif  // _BUILD_OBD_
}

//=============================================================================
//  Method      :  DST
//  Description :  Questo modulo esegue le transizioni degli stati di diagnosi
//                 sulla base parametri sotto indicati
//  Parameters  :  - control: comandi ricevuti
//                 - MessageSession:  sessione a cui appartiene il messaggio
//                 - KeySignal:  stato della chiave
//                 - diagtimer: timout
//  Returns     :  None
//  Funzione chiamante  : DiagApl
//  Funzioni richiamate : None
//=============================================================================
static void DST (uint8_t MessageSession)
{
    // Definizione variabili statiche
    static uint16_t diagtimer10s = TIMER10S;
    static uint16_t diagtimer30s = TIMER30S;


    if (diagtimer10s) 
    {
        diagtimer10s--;
    }
    if (diagtimer30s) 
    {
        diagtimer30s--;
    }

    if (MessageSession & DIAG_MESSAGE_RECEIVED)
    {
        diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
        MessageSession &= ~DIAG_MESSAGE_RECEIVED;
    }


    if(((kwp_com_enabled == TPE_COMM)&&(control == STOP_DIAG_SESSION)&&(KWPsession == DIAG_DEFAULT_SESSION))) 
    {
        klineInit();  
        kwp_com_enabled = NO_COMM;
    }

    // Implementazione dello STD

    switch (KWPsession)
    {
        // Stato diagnostico DIAG_OFF_SESSION
        case DIAG_OFF_SESSION:
        {
            if ((control == START_DIAG_DEFAULT_SESSION))
            {                                      
                diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                diagtimer30s = TIMER30S;
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                control = 0;
            }
            else if ((control == START_COMMUNICATION) || (flg_Init_5Baud_Done==COMUNICATION_ON))
            {                                      
                diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                diagtimer30s = TIMER30S;
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                control = 0;
                flg_Init_5Baud_Done=COMUNICATION_OFF;
            }
            else
            {
                /* DO NOTHING */
            }

            routineEraseRequested = NO_ERASE_REQ; /* New erase request needed from here */
            break;
        }

        // Stato diagnostico DIAG_DEFAULT_SESSION
        case DIAG_DEFAULT_SESSION:
        {
            if (control == START_DIAG_SUPPLIER_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_SUPPLIER_SESSION;
                SessionEnabledServices = SUPPLIER_ENABLED_SERVICES;
                control = 0;
            }
            else if (control == START_DIAG_ECUPROG_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_DOWNLOAD_SESSION;
                SessionEnabledServices = DOWNLOAD_ENABLED_SERVICES;
                control = 0;
            }
            else if (control == START_DIAG_DEFAULT_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                control = 0;
            }
            else
            {
                /* MISRA 14.10 */
            }

            if ((control == CLEAR_DIAGNOSTIC_INFO))
            {
                control = 0;
            }

            if (control == INPUT_OUTPUT_CONTROL)
            {
                diagtimer30s = TIMER30S;
                // Inserire gestione input/output
                control = 0;
            }

            if (((control == STOP_DIAG_SESSION) || (KeySignal == KEY_STS_OFF)) || (diagtimer10s == 0))     
            {                                       
                if(kwp_com_enabled == KLINE_COMM)
                {
                    control = 0;
                    KWPsession = DIAG_DEFAULT_SESSION;
                    SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                    klineResetTiming();
                    klineNewbaudLL(SCI_10400_BR);
                    Reset_All_DIAG(); 
                }
                else
                {
                    kwp_com_enabled = NO_COMM;
                    KWPsession = DIAG_OFF_SESSION;
                    control = 0;
                    KWPdownloadStruct.BF.securityAccessOK = 0; // EBO-1061
                    routineEraseRequested = NO_ERASE_REQ; /* New erase request needed from here */
                }
            }

            if ((control == STOP_COMMUNICATION))
            {                                      
                // Abort servizi pendenti
                KWPsession = DIAG_OFF_SESSION;
                klineResetTiming();
                klineNewbaudLL(SCI_10400_BR);

                control = 0;
            }

            break;
        }


        // Stato diagnostico DIAG_DOWNLOAD_SESSION
        case DIAG_DOWNLOAD_SESSION :
        {
            if ((diagtimer10s == 0)|| (KeySignal == KEY_STS_OFF))
            {                                     
                // Abort servizi pendenti
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                KWPdownloadStruct.BF.securityAccessOK = 0; // EBO-1061
                routineEraseRequested = NO_ERASE_REQ; /* New erase request needed from here */
            }

            if (control == START_DIAG_SUPPLIER_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                klineResetTiming();
                klineNewbaudLL(SCI_10400_BR);
                KWPsession = DIAG_SUPPLIER_SESSION;
                SessionEnabledServices = SUPPLIER_ENABLED_SERVICES;
                control = 0;
            }
            else if (control == START_DIAG_DEFAULT_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                klineResetTiming();
                klineNewbaudLL(SCI_10400_BR);
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                control = 0;
            }
            else if (control == START_DIAG_ECUPROG_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_DOWNLOAD_SESSION;
                SessionEnabledServices = DOWNLOAD_ENABLED_SERVICES;
                control = 0;
            }
            else
            {
                /* MISRA 14.10 */
            }

            if ((control == STOP_DIAG_ECUPROG_SESSION)||(control == STOP_DIAG_SESSION))
            {                                     
                // Abort servizi pendenti
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
            }


            if ((control == STOP_COMMUNICATION))
            {                                      
                // Abort servizi pendenti
                KWPsession = DIAG_OFF_SESSION;
                KWPdownloadStruct.BF.sessionDownloadActive = OFF;
                klineResetTiming();
                klineNewbaudLL(SCI_10400_BR);
                control = 0;
            }

            break;
        }
        
        // Stato diagnostico DIAG_SUPPLIER_SESSION
        case DIAG_SUPPLIER_SESSION :
        {
            if ((control == STOP_DIAG_SESSION) || (KeySignal == KEY_STS_OFF) || (diagtimer10s == 0)) 
            {                                       
                // Abort servizi pendenti
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                control = 0;
                KWPdownloadStruct.BF.securityAccessOK = 0; // EBO-1061
                routineEraseRequested = NO_ERASE_REQ; /* New erase request needed from here */
            }
            if (control == START_DIAG_ECUPROG_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_DOWNLOAD_SESSION;
                SessionEnabledServices = DOWNLOAD_ENABLED_SERVICES;
                control = 0;
            }
            else if (control == START_DIAG_DEFAULT_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_DEFAULT_SESSION;
                SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
                control = 0;
            }
            else if (control == START_DIAG_SUPPLIER_SESSION)
            {                                       
                if(kwp_com_enabled == TPE_COMM)
                {
                    diagtimer10s = TIMER10S;           // Inizializzazione di diagtimer
                    diagtimer30s = TIMER30S;
                }
                KWPsession = DIAG_SUPPLIER_SESSION;
                SessionEnabledServices = SUPPLIER_ENABLED_SERVICES;
                control = 0;
            }
            else
            {
                /* MISRA 14.10*/
            }

            break;
        }
        default:
        {
            break;
        }
    }
}


//=============================================================================
//  Method      :  EndOfLine
//  Description :  
//  Parameters  :  None
//  Returns     :  None
//  Funzione chiamante  : TBD
//  Funzioni richiamate : tpeSend
//=============================================================================
static void EndOfLine(void)
{
    while(FlgEOL)
    {
        ActivateTask(BackgroundTaskID);
        if(KeySignal)
        {
            switch(routinesByLocalID[1].command)
            {    
                case ROUTINE_EXECUTE:
                    routinesByLocalID[1].status = ROUTINE_IN_EXECUTION;
                    break;    
                case ROUTINE_STOP:
                    routinesByLocalID[1].status = ROUTINE_HALTED;
                    FlgEOL=0;
                    break;    
                default:
                    break;
            }
        }
        else
        {
            routinesByLocalID[1].status = ROUTINE_HALTED;
            FlgEOL=0;
        }
    }    

    if((routinesByLocalID[1].status == ROUTINE_IN_EXECUTION))
    {        
        routinesByLocalID[1].status = ROUTINE_COMPLETED;
        routinesByLocalID[1].result = ROUTINE_OK;
    }
    else
    {
        routinesByLocalID[1].status = ROUTINE_HALTED;
        routinesByLocalID[1].result = ROUTINE_KO;
    }
}

#else

#include "typedefs.h"
#include "diagcanmgm.h"

/*stubs section*/
uint8_t kwp_com_enabled;

T_DataInd_tag  T_DataIND;
T_DataREG_tag  T_DataREQ;

void FuncKWPDecoding(void)
{
  TerminateTask();
}

void FuncTaskEndOfLine(void)
{
  TerminateTask();
}


void   startAppDecodeTask(void)
{
}


#endif /* _BUILD_DIAGCANMGM_ */

