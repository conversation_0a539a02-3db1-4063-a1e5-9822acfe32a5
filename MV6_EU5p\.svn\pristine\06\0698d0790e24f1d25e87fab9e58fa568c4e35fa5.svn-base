/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

/*--------------------------------------------------------------------+
|                           Software Build Options                    |
+--------------------------------------------------------------------*/
#pragma ETPU_function angleExGenerator, standard;


/*--------------------------------------------------------------------+
|                           Include Header Files                      |
+--------------------------------------------------------------------*/
#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

//#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_Shared.h"
#include "..\include\angleExGenerator.h"
#include "..\..\common\ETPU_SharedTypes.h"

/*--------------------------------------------------------------------+
|                       Global Variable Definitions                   |
+--------------------------------------------------------------------*/
unsigned int        exAngleNum  = 0xffffff;

int angleExGeneratorUnexpectedEvent = 0;

unsigned int captureRegA= 0xFFFFFF;
//unsigned int captureRegB= 0xFFFFFF;

#pragma library;
#pragma option +l;  // List the library
#pragma option v;


/********************************************************************************/
/** angleExGenerator:                                                           *
This function rise an ISR to the host at the angle defined by                   *
 * CrankAngle parameter.                                                        *
 *                                                                              *
 *  \param \c void
 *  \return \c void
 *  \warning N/A
 *  \todo N/A                                                                   */
/********************************************************************************/
void angleExGenerator(unsigned int angleExGeneratorChanFlags)
{
#ifdef _BUILD_ANGLEEX_
    if (HSR_INIT_ANGLE_EX_GENERATOR)   // Required to initialize
    {
        if(angleExGeneratorChanFlags & EXCEPTIONS_ENABLED)
        {
            captureRegA = 0xFFFFFF;
            captureRegB = 0xFFFFFF;

            SetChannelMode(sm_st);
            SetupMatch_A(exAngleNum, Mtcr2_Ctcr2_eq, NoChange);
        }
    }
    else if (HSR_DISABLE_ANGLE_EX_GENERATOR)
    {
        DisableMatchAndTransitionEventHandling();
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
    }
    else if (MatchA)   
    {
//      Match is detected :
      if(angleExGeneratorChanFlags & EXCEPTIONS_ENABLED)
      {
         captureRegA = GetCapRegA();
         captureRegB = GetCapRegB();

         angleExGeneratorChanFlags = EX_ANGLE;
         SetChannelInterrupt();     ///Inform the host that the crank status is updated
      }
      ClearLSRLatch();
      ClearMatchALatch();
      ClearMatchBLatch();
      ClearTransLatch();
    }
    else
    {
        /**This else statement is used to catch all unspecified entry table conditions
        Clear all possible event sources
         And set the unexpected event error indicator */
        ClearLSRLatch();
        ClearMatchALatch();
        ClearMatchBLatch();
        ClearTransLatch();
        angleExGeneratorUnexpectedEvent = 1;
    }
#endif /* _BUILD_ANGLEEX_ */
}

#pragma endlibrary;

