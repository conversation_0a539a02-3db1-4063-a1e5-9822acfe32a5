/*
    Product            FREQ                      FLASH       RAM           eTPU2        EMIOS           ADC                      Package
    MPC5634M    |  60 MHz, 80 MHz           |    1.5M   |    94      |    1x32ch    |  1x16ch   |   34ch dual 12-bit    |   144LQFP, 176LQFP, 208MAPBGA
--> MPC5633M    |  40 MHz, 60 MHz, 80 MHz   |    1M     |    64      |    1x32ch    |  1x16ch   |   32ch dual 12-bit    |   144LQFP, 176LQFP, 208MAPBGA
    MPC5632M    |  40 MHz, 60 MHz           |    768K   |    48      |    1x32ch    |  1x8ch    |   32ch dual 12-bit    |   144LQFP
*/
MEMORY
{
/*  1 MB Internal Flash, organized in several memory regions. */
/***************************************************************/
/*      |-----------------------------------------------|       */
/*      |               (RCW + mBOOT) - 16kB            |       */
/*      |-----------------------------------------------|       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                 EEPROM - 96 KB                |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |-----------------------------------------------|       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                  BOOT - 80kB                  |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |-----------------------------------------------|       */
/*      |                 CALIB - 16kB                  |       */
/*      |-----------------------------------------------|       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                  APP - 512kB                  |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |                                               |       */
/*      |-----------------------------------------------|       */
/*      |                                               |       */
/*      |                  BKP - 128kB                  |       */
/*      |                                               |       */
/*      |-----------------------------------------------|       */
                                                            
/*  1 MB Internal Flash, organized in several memory regions. */
  flash_rcw          :        org = 0x00000000, len = 0x8
  miniboot_flash     :        org = 0x00000008, len = 0x00003FF8
  EEPROM_flash       :        org = 0x00004000, len = 0x00018000
  boot_flash         :        org = 0x0001C000, len = 0x00014000
  calibration_flash  :        org = 0x00030000, len = 0x00006000
  application_flash  :        org = 0x00040000, len = 0x00120000 
  backup_flash       :        org = 0x00160000, len = 0x00020000

/*  64kB General Purpose System SRAM */
/* MEMORY MAP WITH STACK AT THE TOP */
/***************************************************************/
/*        --------------------------------------------         */
/*       |                                            |        */
/*       |               STACK RAM - 4kB              |        */
/*       |                                            |        */
/*       |--------------------------------------------|        */
/*       |                     |    VSRAM - 512B      |        */
/*       |                     | - - - - - - - - - - -|        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |   INT SRAM - 66kB   |    DATA - 65.5kB     |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |                     |                      |        */
/*       |--------------------------------------------|        */
/*       |                                            |        */
/*       |           VIRTUAL CALIB RAM - 24kB         |        */
/*       |                                            |        */
/*       |--------------------------------------------|        */
/*  64kB Internal SRAM */
/* MEMORY MAP WITH STACK AT THE TOP */
  stack_ram   :   org = 0x40000000,  len = 0x1000
  int_sram    :   org = 0x40001000,  len = 0x10800   /* AM - was len = 0x8000 */

/* memory region without VCALIB enabled using 5633M rev.0 */
  vcalib_cache    :   org = 0x40011800, len = 0x0000    /* null length!!! */
  virtual_calib   :   org = 0x40011800, len = 0x6000
  
/* memory region without VCALIB enabled using 5633M rev.1
  vcalib_cache    :   org = 0x4000B000, len = 0x1000
  virtual_calib   :   org = 0x4000C000, len = 0x4000
*/
 /*  512K External SRAM (unused) */
  ext_ram     : org = 0x3ff80000,    len = 0x80000


}


SECTIONS
{
__FLASH_START = ADDR(miniboot_flash);
__FLASH_END = ADDR(backup_flash);

__MINIBOOT_START = ADDR(miniboot_flash);
__MINIBOOT_SIZE = SIZEOF(miniboot_flash);
__BOOT_START = ADDR(boot_flash);
__BOOT_SIZE = SIZEOF(boot_flash);
__EEPROM_START = ADDR(EEPROM_flash);
__EEPROM_SIZE = SIZEOF(EEPROM_flash);
__EEPROM_END = __EEPROM_START + __EEPROM_SIZE;
__CALIB_ROM_START = ADDR(calibration_flash);
__CALIB_ROM_SIZE = SIZEOF(calibration_flash);
__APP_START = ADDR(application_flash);
__APP_SIZE = SIZEOF(application_flash);
__BACKUP_START = ADDR(backup_flash);
__BACKUP_SIZE = SIZEOF(backup_flash);

__VCALIB_START = ADDR(virtual_calib);
__VCALIB_SIZE = SIZEOF(virtual_calib);
//__CALIB_RAM_SIZE = SIZEOF(virtual_calib);  /* This value is static and it is the max: it comprises ELDOR + SKYHOOK calib sections */


__VCALIB_CACHE_START = ADDR(vcalib_cache);
__VCALIB_CACHE_SIZE = SIZEOF(vcalib_cache);
__VCALIB_CACHE_END = __VCALIB_CACHE_START + __VCALIB_CACHE_SIZE;

//__CALIB_RAM_START = __VCALIB_START;
//__CALIB_RAM_END = __CALIB_RAM_START + __VCALIB_SIZE;

__RAM_START = ADDR(stack_ram);
__RAM_END =ADDR(stack_ram) + SIZEOF(stack_ram) + SIZEOF(int_sram) + SIZEOF(vcalib_cache);
/* __RAM_END = fine della RAM a disposizione */
//__RAM_END = ADDR(stack_ram) + SIZEOF(stack_ram) + SIZEOF(int_sram) + SIZEOF(virtual_calib) + SIZEOF(vcalib_cache);


__GLOBAL_START = 0x00030000;
__GLOBAL_END = __BACKUP_START;
__GLOBAL_SIZE = __GLOBAL_END - __GLOBAL_START;
__HEAP_SIZE = 0x10;

__VSRAM_SIZE   = 0x200;

__TOTAL_AVAILABLE_RAM = SIZEOF(stack_ram) + SIZEOF(int_sram) + SIZEOF(virtual_calib) + SIZEOF(vcalib_cache); /* dovrebbe essere calcolata: __RAM_END - __RAM_START */ 

/* ROM data */
    .rcw  : { *(.rcw) } > flash_rcw

/* BOOT section */


/* calibration_flash section */

    .calib_check MAX_ENDADDRESS(__APP_START) LOAD(.) : {
                            calib_checkVersion.o (.rodata)
                           } > calibration_flash 
//    __LOCAL_CALIB_RAM_START = ADDR(calibration_flash);

    .= ((. + 7) & ~7); /* align memory region end */

    //.ROM.calib MAX_ENDADDRESS(__APP_START) ROM(.calib) ALIGN(16):>.
    .ROM.calib      ROM(.calib)            ALIGN(16)        : {}   >.   
    .= ((. + 31) & ~31); /* align memory region end */
    __CALIB_ROM_END = . ; 

    /* Region Tagging */
    . = __CALIB_ROM_START+__CALIB_ROM_SIZE - SIZEOF(.calib_tag);
    .calib_tag MAX_ENDADDRESS(__APP_START) LOAD(.) : {calib_tag.o (.rodata)} >.
    __CCP_CALIB_ROM_END = . ;   

    __CALIB_TAG_START = ADDR(.calib_tag);
    __CALIB_TAG_SIZE = SIZEOF(.calib_tag);

/* ROM data */    
    .get_app_startup MAX_ENDADDRESS(__BACKUP_START )  LOAD(.):  {
                            get_app_startup.o (.rodata)
                                 } > application_flash

    .app_flash MAX_ENDADDRESS(__BACKUP_START ) LOAD(.) : {
    vcalib_callback.o (.text)
    vcalib_callback.o (.rodata)
    recovery.o (.text)
    recovery.o (.rodata)
    } > .

    .app_lib MAX_ENDADDRESS(__BACKUP_START ) ALIGN(0x08): {
                            libstartup.a (ind_mcpy.o) (.text)
                            libstartup.a(ind_mset.o) (.text)
                               } > .
    .init MAX_ENDADDRESS(__BACKUP_START ) LOAD(.) ALIGN(0x08): {
                            *(.init)
                               } > .
    .flash_data : {} > .
    .xcptn MAX_ENDADDRESS(__BACKUP_START ) LOAD(.) ALIGN(0x30)   : {}  > .

    .text MAX_ENDADDRESS(__BACKUP_START ) LOAD(.) ALIGN(0x08): {}  > .
    
    .rodata MAX_ENDADDRESS(__BACKUP_START )  LOAD(.) ALIGN(0x08): {
         *(.rdata) 
         *(.rodata) 
         } > .  
    .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
    __DATA_ROM =.;         /* Start of .data in ROM */   
//         . = . + SIZEOF(.data); /* make space for .data */
     .ROM.data	MAX_ENDADDRESS(__BACKUP_START )	ROM(.data): > .

     /*******************************************************/
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID0_DATA_START = .;
         .ROM.ee_id0_data       ROM(.ee_id0_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
     
         __EE_ID0_DATA_END = .;
     
/*******************************************************/
    .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
    __EE_ID1_DATA_START = .;
    .ROM.ee_id1_data        ROM(.ee_id1_data):>.
    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID1_DATA_END = .;

    .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
    __EE_ID2_DATA_START = .;
    .ROM.ee_id2_data        ROM(.ee_id2_data):>.
    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID2_DATA_END = .;

/*******************************************************/

         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID3_DATA_START = .;
         .ROM.ee_id3_data       ROM(.ee_id3_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID3_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID4_DATA_START = .;
         .ROM.ee_id4_data       ROM(.ee_id4_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID4_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID5_DATA_START = .;
         .ROM.ee_id5_data       ROM(.ee_id5_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID5_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID6_DATA_START = .;
         .ROM.ee_id6_data       ROM(.ee_id6_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID6_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID7_DATA_START = .;
         .ROM.ee_id7_data       ROM(.ee_id7_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID7_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID8_DATA_START = .;
         .ROM.ee_id8_data       ROM(.ee_id8_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID8_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID9_DATA_START = .;
         .ROM.ee_id9_data       ROM(.ee_id9_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID9_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID10_DATA_START = .;
         .ROM.ee_id10_data      ROM(.ee_id10_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID10_DATA_END = .;
     
/*******************************************************/

      __SDATA_ROM = .;        /* Start of .sdata in ROM */
//         . = . + SIZEOF(.sdata); /* make space for .sdata */
     .ROM.sdata	MAX_ENDADDRESS(__BACKUP_START )  ROM(.sdata): > .

     __DATA_ROM_END = .;  /* end of __DATA_ROM */
    .ctors MAX_ENDADDRESS(__BACKUP_START ) LOAD(.): {} > .
    .dtors MAX_ENDADDRESS(__BACKUP_START ) LOAD(.): {} > .

    extab ALIGN(0x10): {} > .
   .isrvectbl MAX_ENDADDRESS(__BACKUP_START ) LOAD(.)  ALIGN(0x800) : {} > .
    extabindex LOAD(.) : {} > .
    .= ((. + 31) & ~31); /* align memory region end */
    __APP_CODE_END = . ;

    /* Region Tagging */
    . = __APP_START+__APP_SIZE - SIZEOF(.app_tag);
    __APP_TAG_START = .;
    __APP_TAG_SIZE  = SIZEOF(.app_tag);
    .app_tag MAX_ENDADDRESS(__BACKUP_START ) LOAD(.) : {app_tag.o (.rodata)} > .
    __APP_END = .;

/* SRAM data */

    __VSRAM_START = ADDR(int_sram);
    .vsram :{
                      vsram_checksum.o (.bss)
                      vsram_shared_content.o  (.bss)
                      vsram_content.o  (.bss)
            } > int_sram
    __VSRAM_END = __VSRAM_START + __VSRAM_SIZE;
    .= __VSRAM_END;
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sdata  /*LOAD(__SDATA_ROM)*/ ALIGN(0x08): {} >.
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sbss   : {} >.
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sdata2 : {} >.
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sbss2  : {} >.
   .= ((. + 7) & ~7); /* align 8 bytes */
    .heap   : {} >.
   .= ((. + 7) & ~7); /* align 8 bytes */
                

                       
    .= ((. + 31) & ~31); /* align memory region end */
    .data   /*LOAD(__DATA_ROM)*/ ALIGN(0x08): {} >.

    .= ((. + 7) & ~7); /* align 8 bytes */

    __EE_ID0_START = .;
    .ee_id0_data  :{
                              ee_ID0.o (.ee_id0_data)
                   } >.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID0_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID1_START = .;
    .ee_id1_data :{
                              ee_ID1.o (.ee_id1_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID1_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID2_START = .;
    .ee_id2_data  :{
                              ee_ID2.o (.ee_id2_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID2_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID3_START = .;
    .ee_id3_data :{
                              ee_ID3.o (.ee_id3_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID3_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID4_START = .;
    .ee_id4_data :{
                              ee_ID4.o (.ee_id4_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID4_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID5_START = .;
    .ee_id5_data :{
                              ee_ID5.o (.ee_id5_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID5_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID6_START = .;
    .ee_id6_data :{
                              ee_ID6.o (.ee_id6_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID6_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID7_START = .;
    .ee_id7_data :{
                              ee_ID7.o (.ee_id7_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID7_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID8_START = .;
    .ee_id8_data :{
                              ee_ID8.o (.ee_id8_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID8_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID9_START = .;
    .ee_id9_data :{
                              ee_ID9.o (.ee_id9_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID9_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID10_START = .;
    .ee_id10_data :{
                              ee_ID10.o (.ee_id10_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID10_END = .;

    .bss    : {} > .
    .= ((. + 7) & ~7); /* align 8 bytes */
__END_OF_RAM = .;
    . = .+ SIZEOF(.calib_check); /*  make room for build signature */
    .vcalib_check  ALIGN(0x08): {} > virtual_calib
     __CALIB_RAM_START = .;
     __LOCAL_CALIB_RAM_START = .;
    . = .+ SIZEOF(.calib_check); /*  make room for build signature */
    .= ((. + 7) & ~7); /* align memory region end */
    .calib   ALIGN(0x16): {
                            *(.calib)
			    .= ((. + 31) & ~31); 
                          } > .
//     .= __VCALIB_START + (__CALIB_ROM_END - __CALIB_ROM_START);

                  
    .= ((. + 31) & ~31); /* align memory region end */
__CALIB_RAM_END = .;
    // __CALIB_RAM_END = .;
    
 /* Stack area */
    .stack  : {} > stack_ram

/* Stack Address Parameters */
__SP_INIT      = ADDR(stack_ram) + SIZEOF(stack_ram); 
__SP_END       = ADDR(stack_ram); 
__STACK_SIZE   = SIZEOF(stack_ram); 

/* SRAM Address Parameters */

__SRAM_CPY_START = ADDR(.data);
__ROM_COPY_SIZE  = (SIZEOF(.data) + SIZEOF(.sdata));
__SRAM_LOAD      = ADDR(.heap);
__SRAM_LOAD_SIZE = (SIZEOF(.flash_data)/4);

/* The EABI defines the location of _SDA_BASE_ and _SDA2_BASE_  */
/*  cfg_PNTRS places _SDA_BASE_ into R13 and _SDA2_BASE into R2 */
/*_SDA_BASE_ = ADDR(.sdata) + 0x8000 (+0x7FF0 for WindRiver)    */
/*_SDA2_BASE_ = ADDR(.sdata2) + 0x8000 (+0x7FF0 for WindRiver)  */

/* Interrupt Handler Parameters */
__IV_ADDR      = ADDR(.xcptn);




/******* VSRAM Address Parameters *******/
  __KEYWORD1     = 0x55555555;
  __KEYWORD2     = 0xAAAAAAAA;
  __TEST_KEYWORD = 0x5A5A5A5A;
  __CLEAR_KEYWORD= 0x00000000;
  __VSRAM_SRAM_START =  __VSRAM_START;
  __VSRAM_SRAM_SIZE  =  (__VSRAM_SIZE/4);
  __VSRAM_SRAM_END   =  __VSRAM_END;
  __VSRAM_START_ADDR =  __VSRAM_SRAM_START ;
  
  __SRAM_START_ADDR   = ADDR(int_sram);
  __SRAM_SIZE         = (SIZEOF(int_sram)/4);
  __SRAM_SIZE_1       = (__SRAM_SIZE - __VSRAM_SRAM_SIZE);
  __SRAM_START_ADDR_1 = (__SRAM_START_ADDR + __VSRAM_SIZE);
  __SRAM_END_ADDR     = __SRAM_START_ADDR + SIZEOF(int_sram);
/***************************************/ 

/***************************************/ 
/*         A2L Modified labels         */

  __2APP_START = __APP_START;
  __2APP_END = __APP_END;
  __3CALIB_ROM_START = __CALIB_ROM_START;
  __3CALIB_ROM_END = __CCP_CALIB_ROM_END;   
  __CALIB_ROM_OFFLINE_START = __CALIB_ROM_START;
  __CALIB_ROM_OFFLINE_END = __CALIB_ROM_START + (__CALIB_RAM_END - __CALIB_RAM_START);
/*  __CALIB_RAM_SIZE = (__CALIB_RAM_END -  __CALIB_RAM_START );*/
  __CALIB_DATA_START =  __CALIB_ROM_START;
  __CALIB_DATA_SIZE = __CALIB_ROM_END - __CALIB_ROM_START;
  __APP_CODE_SIZE = (__APP_CODE_END - __APP_START);

/***************************************/ 

/***************************************/ 
/*         KWP labels                  */
  
  __KWP_START_BOOT_ADD = ADDR(boot_flash);
  __KWP_CODE_BOOT_SIZE = SIZEOF(boot_flash);
  __KWP_START_APPL_ADD = ADDR(calibration_flash);
  __KWP_CODE_APPL_SIZE = ADDR(backup_flash) - ADDR(calibration_flash);
  __KWP_START_CALIB_ADD = ADDR(calibration_flash);
  __KWP_CODE_CALIB_SIZE = SIZEOF(calibration_flash);


/***************************************/ 


/* These special symbols mark the bounds of RAM and ROM memory. */
/* They are used by the MULTI debugger.                         */

    __ghs_ramstart  = MEMADDR(stack_ram);
    __ghs_ramend    = MEMENDADDR(int_sram);
    __ghs_romstart  = MEMADDR(miniboot_flash);
    __ghs_romend    = MEMENDADDR(application_flash);

//    __ghs_rambootcodestart = 0;           /* zero for ROM image */
//    __ghs_rambootcodeend = 0;             /* zero for ROM image */
//    __ghs_rombootcodestart = MEMADDR(miniboot_flash);
//    __ghs_rombootcodeend = MEMENDADDR(miniboot_flash);

/* Metrowerks Code Warrior compiler address designations 
_stack_addr = ADDR(stack_ram)+SIZEOF(stack_ram);
_stack_end = ADDR(stack_ram);
_heap_addr = ADDR(.bss)+SIZEOF(.bss);
_heap_end = ADDR(int_sram)+SIZEOF(int_sram);
_init_addr = ADDR(.init);
_init_end = ADDR(.init)+SIZEOF(.init);
*/

}
