/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

#include "..\..\common\ETPU_EngineTypes.h"
#include "..\include\build_opt.h"
#include "..\..\common\ETPU_EngineDefs.h"
#include "..\..\common\ETPU_VrsDefs.h"

/******************************************************/
/* Phase signal management                            */
#ifdef _BUILD_ETPUPHASE_
unsigned int    PhMinRatio[10];
unsigned int    PhMaxRatio[10];
unsigned int    PhTransition[10];
unsigned int    PhAngle[10];
#endif

/******************************************************/
/* Angle stamps management                            */

unsigned int    sampleNumber[40];
unsigned int    angleValue[40];
unsigned int    zeroToothTime;
/******************************************************/
/* Globals in common between PHASE and Angle Clock */

unsigned int     TeethPerRev;             /* To be defined by host side */
unsigned int     TicksPerTooth;           /* To be defined by host side */
unsigned int     PreviousToothPeriod;
unsigned int     PreviousEdgeTime;
unsigned int     NumRevPerCycle;          /* To be defined by host side */

unsigned int     LastTooth_1;
unsigned int     LastTooth_2;

unsigned int     captureRegB = 0xFFFFFF;

/**********************************************************************/
/* Global for Angle Clock gain management for LAST_TOOTH_1 and LAST_TOOTH_2 */
unsigned int     angleClockGain = 16;
/* Global for Angle Clock gain management for TDC Tooths - 15 degree */
unsigned int     angleClockGain_m15d = 16;
/* Global for Angle Clock gain management for TDC Tooths */
unsigned int     angleClockGain_Tdc = 16;                        
/* Global for Angle Clock gain management for TDC Tooths + 15 degree */
unsigned int angleClockGain_15d = 16;
/* Global for Angle Clock gain management for TDC Tooths + 30 degree */
unsigned int angleClockGain_30d = 16;
/* Global for Pulse, divider for frequency of prediction wakeup */
unsigned int event_divider = 2;

 
/******************************************************/
/* FastLinked channels globals                        */

#ifdef _BUILD_FASTLINKEDCHAN_
unsigned int     FLCHAN_IN_level;
#endif
