/*
 * File: C:\localmodules\local_AirMgm_PI6\slprj\ert\_sharedutils\div_repeat_s32_sat.c
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2438
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Thu Feb 14 08:15:05 2019
 */

#include "rtwtypes.h"
#include "rtw_shared_utils.h"

int32_T div_repeat_s32_sat(int32_T numerator, int32_T denominator, uint32_T
  nRepeatSub)
{
  int32_T quotient;
  uint32_T tempAbsQuotient;
  uint32_T quotientNeedsNegation;
  if (denominator == 0) {
    quotient = (numerator >= 0) ? MAX_int32_T : MIN_int32_T;

    /* Divide by zero handler */
  } else {
    quotientNeedsNegation = ((numerator < 0) != (denominator < 0));
    tempAbsQuotient = div_repeat_u32_sat((uint32_T)((numerator >= 0) ? numerator
      : (-numerator)), (uint32_T)((denominator >= 0) ? denominator :
      (-denominator)), nRepeatSub);
    if (quotientNeedsNegation) {
      quotient = (tempAbsQuotient <= 2147483647U) ? (-((int32_T)tempAbsQuotient))
        : MIN_int32_T;
    } else {
      quotient = (tempAbsQuotient <= 2147483647U) ? ((int32_T)tempAbsQuotient) :
        MAX_int32_T;
    }
  }

  return quotient;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
