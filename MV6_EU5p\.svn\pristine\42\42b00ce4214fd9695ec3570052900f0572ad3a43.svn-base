/*
 * File: TrqDriver_private.h
 *
 * Code generated for Simulink model 'TrqDriver'.
 *
 * Model version                  : 1.2235
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Aug  5 15:33:05 2024
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TrqDriver_private_h_
#define RTW_HEADER_TrqDriver_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "TrqDriver.h"

/* Includes for objects with custom storage classes. */
#include "engflag.h"
#include "digitalin.h"
#include "canmgm.h"
#include "trq_est.h"
#include "trc2wzero_out.h"
#include "diagcanmgm.h"
#include "gaspos_mgm.h"
#include "PTrain_Diag.h"
#include "gasposfilt_mgm.h"
#include "GearPosClu_mgm.h"
#include "fuel_mgm.h"
#include "Engflag.h"
#include "syncmgm.h"
#include "recmgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T VTVSCINT[11];           /* Variable: VTVSCINT
                                        * Referenced by: '<S72>/VTVSCINT'
                                        * Integral term for vehicle speed control
                                        */
extern int16_T BKVSCERROR[11];         /* Variable: BKVSCERROR
                                        * Referenced by: '<S72>/BKVSCERROR'
                                        * VSCError breakpoint
                                        */
extern int16_T VTVSCPROP[11];          /* Variable: VTVSCPROP
                                        * Referenced by: '<S72>/VTVSCPROP'
                                        * Proportional term for vehicle speed control
                                        */
extern int16_T CMECANDELTA;            /* Variable: CMECANDELTA
                                        * Referenced by: '<S9>/CMECANDELTA'
                                        * Max Cme gradient after punctual error
                                        */
extern int16_T CMEGASRPMOFF;           /* Variable: CMEGASRPMOFF
                                        * Referenced by: '<S20>/CMEGASRPMOFF'
                                        * CmeGasRpm gain for CmeTargetCAN coherence
                                        */
extern int16_T TBCMEDRIV[160];         /* Variable: TBCMEDRIV
                                        * Referenced by: '<S24>/TBCMEDRIV'
                                        * (SR) Driver torque request
                                        */
extern int16_T MAXROLLVEHCTRL;         /* Variable: MAXROLLVEHCTRL
                                        * Referenced by: '<S15>/MAXROLLVEHCTRL'
                                        * Max RollCAN to keep vehicle speed control enabled
                                        */
extern uint16_T GASPOSCCFILT;          /* Variable: GASPOSCCFILT
                                        * Referenced by: '<S19>/GASPOSCCFILT'
                                        * GasPosCC filter gain while pausing/disabling
                                        */
extern uint16_T KFCMEDRIVER;           /* Variable: KFCMEDRIVER
                                        * Referenced by: '<S12>/KFCMEDRIVER'
                                        * filter
                                        */
extern uint16_T VTVSCKFILT[8];         /* Variable: VTVSCKFILT
                                        * Referenced by: '<S55>/VTVSCKFILT'
                                        * Vehicle speed set-up filter gain
                                        */
extern uint16_T BKGASDRIV[10];         /* Variable: BKGASDRIV
                                        * Referenced by: '<S36>/BKGASDRIV'
                                        * GasPos breakpoints for TBCMEDRIV (AccSens = 0)
                                        */
extern uint16_T BKGASDRIV1[10];        /* Variable: BKGASDRIV1
                                        * Referenced by: '<S36>/BKGASDRIV1'
                                        * GasPos breakpoints for TBCMEDRIV (AccSens = 1)
                                        */
extern uint16_T BKGASDRIV2[10];        /* Variable: BKGASDRIV2
                                        * Referenced by: '<S36>/BKGASDRIV2'
                                        * GasPos breakpoints for TBCMEDRIV (AccSens = 2)
                                        */
extern uint16_T GASPOSMINTHR;          /* Variable: GASPOSMINTHR
                                        * Referenced by: '<S20>/GASPOSMINTHR'
                                        * Min GasPos to test CmeTargetCANF value
                                        */
extern uint16_T VTGASPOSCCINIT[4];     /* Variable: VTGASPOSCCINIT
                                        * Referenced by: '<S17>/VTGASPOSCCINIT'
                                        * GasPos init value on first activation
                                        */
extern uint16_T BKRPMDRIV[16];         /* Variable: BKRPMDRIV
                                        * Referenced by: '<S27>/BKRPMDRIV'
                                        * RpmF breakpoints
                                        */
extern uint16_T BKVEHSPSETUPCAN[4];    /* Variable: BKVEHSPSETUPCAN
                                        * Referenced by: '<S17>/BKVEHSPSETUPCAN'
                                        * VehSpeedLim breakpoint
                                        */
extern int8_T BKDVEHSPEEDSETUP[8];     /* Variable: BKDVEHSPEEDSETUP
                                        * Referenced by: '<S55>/BKDVEHSPEEDSETUP'
                                        * DVehSpeedSetUp breakpoint
                                        */
extern uint8_T VTVSCGAINGEAR[6];       /* Variable: VTVSCGAINGEAR
                                        * Referenced by: '<S72>/VTVSCGAINGEAR'
                                        * VSC PI term gain
                                        */
extern uint8_T VEHSPEEDSETUPRATE;      /* Variable: VEHSPEEDSETUPRATE
                                        * Referenced by: '<S51>/VEHSPEEDSETUPRATE'
                                        * Rate limiter constant applied to VehSpeedLim
                                        */
extern uint8_T ENTORQUELAW;            /* Variable: ENTORQUELAW
                                        * Referenced by:
                                        *   '<S7>/ENTORQUELAW'
                                        *   '<S12>/ENTORQUELAW'
                                        * Torque law selected
                                        */
extern uint8_T ENVEHSPEEDCTRL;         /* Variable: ENVEHSPEEDCTRL
                                        * Referenced by: '<S3>/CmeDriver_Management'
                                        * Vehicle speed control enabled
                                        */
extern uint8_T MAXVEHSPEEDSTEP;        /* Variable: MAXVEHSPEEDSTEP
                                        * Referenced by: '<S3>/CmeDriver_Management'
                                        * Maximum step of VehSpeedLim allowed with no filter init
                                        */
extern uint8_T RPMFSEL;                /* Variable: RPMFSEL
                                        * Referenced by: '<S27>/RPMFSEL'
                                        * RpmF selected as index variable
                                        */
extern uint8_T SELBKGASDRIV;           /* Variable: SELBKGASDRIV
                                        * Referenced by: '<S36>/SELBKGASDRIV'
                                        * Select BKGASDRIV
                                        */
extern uint8_T THVSCSETUPCTF;          /* Variable: THVSCSETUPCTF
                                        * Referenced by: '<S53>/THVSCSETUPCTF'
                                        * VehSpeedSetupCAN offset during cutoff
                                        */
extern uint8_T USECMECAN;              /* Variable: USECMECAN
                                        * Referenced by:
                                        *   '<S3>/USECMECAN'
                                        *   '<S7>/USECMECAN'
                                        * Use Cme form CAN
                                        */
extern uint8_T VEHSPEEDSETUPFILTDELAY; /* Variable: VEHSPEEDSETUPFILTDELAY
                                        * Referenced by:
                                        *   '<S17>/VEHSPEEDSETUPFILTDELAY'
                                        *   '<S49>/VEHSPEEDSETUPFILTDELAY'
                                        *   '<S50>/VEHSPEEDSETUPFILTDELAY'
                                        * Delay before using Filt after a new VehSpeedLim
                                        */
extern uint8_T VEHSPEEDSETUPMAX;       /* Variable: VEHSPEEDSETUPMAX
                                        * Referenced by: '<S15>/VEHSPEEDSETUPMAX'
                                        * Max VehSpeedSetupCAN to exit pause
                                        */
extern uint8_T VEHSPEEDSETUPMIN;       /* Variable: VEHSPEEDSETUPMIN
                                        * Referenced by: '<S15>/VEHSPEEDSETUPMIN'
                                        * Min VehSpeedSetupCAN to exit pause
                                        */
extern uint8_T VEHSPEEDTC2WZMIN;       /* Variable: VEHSPEEDTC2WZMIN
                                        * Referenced by: '<S15>/VEHSPEEDTC2WZMIN'
                                        * Min VehSpeedSetupCAN to exit pause
                                        */
extern uint8_T VSCMINGEAR;             /* Variable: VSCMINGEAR
                                        * Referenced by: '<S13>/VSCMINGEAR'
                                        * Min gear to enable vehicle speed control
                                        */
extern uint8_T VTCMETARGETWOT[16];     /* Variable: VTCMETARGETWOT
                                        * Referenced by: '<S25>/VTCMETARGETWOT'
                                        * Cme limit in economy mode
                                        */
extern uint8_T VTMAXCMEMTRQ[16];       /* Variable: VTMAXCMEMTRQ
                                        * Referenced by: '<S26>/VTMAXCMEMTRQ'
                                        * Cme limit in economy mode
                                        */
extern uint8_T VTMAXCMERAIN[16];       /* Variable: VTMAXCMERAIN
                                        * Referenced by: '<S26>/VTMAXCMERAIN'
                                        * Cme limit in rain mode
                                        */
extern uint8_T VTMAXGASPOSCCINT[6];    /* Variable: VTMAXGASPOSCCINT
                                        * Referenced by: '<S73>/VTMAXGASPOSCCINT'
                                        * Max integral term for vehicle speed control
                                        */
extern uint8_T VTMINGASPOSCCINT[6];    /* Variable: VTMINGASPOSCCINT
                                        * Referenced by: '<S73>/VTMINGASPOSCCINT'
                                        * Min integral term for vehicle speed control
                                        */
extern void TrqDrive_CmeDriverCAN_Diagnosis(void);
extern void TrqDrive_CmeDriverCAN_Selection(void);
extern void TrqDriver_CmeGasRpm_Selection(void);
extern void TrqDriver_CmeGasRpm_Calc(void);
extern void TrqDriver_VehCtrl_CL(void);
extern void TrqDriver_VehCtrl_Reset(void);
extern void TrqDriver_FlgVSCFilt_Calc_Init(void);
extern void TrqDriver_FlgVSCFilt_Calc(void);
extern void TrqDriver_VehCtrl_CL_Init_k(void);
extern void TrqDriver_VehCtrl_CL_Suspend(void);
extern void TrqDriver_CmeDriverCAN_Smooth(void);
extern void TrqDriver_FlgVSCDisable_Calc(void);
extern void TrqDriver_FlgVSCPause_Calc(void);
extern void TrqDriver_T10ms_Init(void);
extern void TrqDriver_T10ms(void);
extern void TrqDriver_Init(void);
extern void TrqDriver_T10ms_initialize(void);
extern void TrqDriver_Init_initialize(void);

#endif                                 /* RTW_HEADER_TrqDriver_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
