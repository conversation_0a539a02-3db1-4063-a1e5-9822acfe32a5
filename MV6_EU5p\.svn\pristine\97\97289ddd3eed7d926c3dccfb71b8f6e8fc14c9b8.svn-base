/** ###################################################################
**     Filename  : WDT.H
**     Project   :
**     Processor : MPC5554
**     Version   :
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 05/06/2005
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ###################################################################*/

#ifndef __WDT_H__
#define __WDT_H__

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
//#include "typedefs.h"
#include "mpc5500_spr_macros.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

/* ------------------------------------------------------------------------- */
/* WDT internal defines                                                      */
/* ------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------- */

/* ------------------------------------------------------------------------- */
/* WDT Timeout Period Selection Macros                                       */
/* ------------------------------------------------------------------------- */
#if (FSYS==80)
    #if (WDT_PERIOD_1ms == 1)   
        #define WPEXT_BIT_FIELD   0xC
        #define WP_BIT_FIELD      0x0
    #elif  (WDT_PERIOD_2ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x3
    #elif  (WDT_PERIOD_3ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x2
    #elif  (WDT_PERIOD_6ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x1
    #elif  (WDT_PERIOD_13ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x0
    #elif  (WDT_PERIOD_26ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x3
    #elif  (WDT_PERIOD_52ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x2
    #elif  (WDT_PERIOD_104ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x1
    #elif  (WDT_PERIOD_208ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x0
    #elif (WDT_PERIOD_1600ms == 1)
        #define WPEXT_BIT_FIELD   0x9
        #define WP_BIT_FIELD      0x1
    #endif     
#elif (FSYS==60)
    #if (WDT_PERIOD_1ms == 1)   
        #define WPEXT_BIT_FIELD   0xC
        #define WP_BIT_FIELD      0x0
    #elif  (WDT_PERIOD_2ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x3
    #elif  (WDT_PERIOD_4ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x2
    #elif  (WDT_PERIOD_9ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x1
    #elif  (WDT_PERIOD_17ms == 1)         
        #define WPEXT_BIT_FIELD   0xB
        #define WP_BIT_FIELD      0x0
    #elif  (WDT_PERIOD_35ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x3
    #elif  (WDT_PERIOD_70ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x2
    #elif  (WDT_PERIOD_140ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x1
    #elif  (WDT_PERIOD_280ms == 1)         
        #define WPEXT_BIT_FIELD   0xA
        #define WP_BIT_FIELD      0x0
    #endif    
#else
    #error WDT period macros not defined for used system frequency

#endif /* FSYS==80 */ 

/*used during Flashing*/
#define TCR_WPEXT_1600ms   ((uint8_T)0x9) /* AM - was 0x8, wrong!*/
#define TCR_WP_1600ms      ((uint8_T)0x1)

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/

#ifdef WDT_PSR_ONLY                       /* see WDT.cfg file  */
#define WDT_PeriodicServiceRoutine()                                              \
{                                                                                                                                                               \
                                                                                                                                                                    \
    union SPR_TSRVAL tmp;                                                                                                                   \
                                                                                                                                                                    \
    tmp.R = getSpecReg32(SPR_TSR); /* retrieve Timer Status Register contents*/     \
                                                                                                                                                                \
    tmp.B.WIS = 1;                       /* prevent a watchdog timeout reset by      */   \
                                                 /* clearing the watchdog interrupt status */   \
                                                 /* bit [bit 1 of TSR register]            */   \
    setSpecReg32(SPR_TSR,tmp.R);   /* set  Timer Status Register             */     \
}
#else
   #ifdef WDT_PSR_INTH                 /* see WDT.cfg file                             */
#define WDT_PeriodicServiceRoutine()                                              \
{                                                                                                                                                               \
     union SPR_TSRVAL tmp;                                                                                                              \
                                                                                                                                                                    \
     tmp.R = getSpecReg32(SPR_TSR);/* retrieve Timer Status Register contents*/     \
                                                                                                                                                            \
     tmp.B.ENW = 1;                      /* prevent an initial watchdog timeout by */   \
                                                 /* clearing the next watchdog bit         */   \
                                                                                                                                                            \
     setSpecReg32(SPR_TSR,tmp.R);  /* set Timer Status Register              */     \
}
   #else
#define WDT_PeriodicServiceRoutine()
   #endif /* WDT_PSR_INTH*/
#endif /* WDT_PSR_ONLY*/

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/


/* ------------------------------------------------------------------------- */
/* WDT Service Function                                                      */
/* ------------------------------------------------------------------------- */

/*
** ============================================================================
**     Method      :    WDT_Config 
**
**     Description :
**         This method configures the key registers associated with the control
**         of the watch dog timer, i.e. timer control register [TCR] and also
**         enables the time base HID0[TBen] bit register.
**
**     Parameters  : None
**     Returns     : None
** ============================================================================
*/
void WDT_Config(void);

/*
** ============================================================================
**     Method      :    WDT_Disable 
**
**     Description :
**         This method disables WDT IRS.
**
**     Parameters  : None
**     Returns     : None
** ============================================================================
*/
void WDT_Disable(void);

/*
** ============================================================================
**     Method      :    WDT_SetTimeout 
**
**     Description :
**         This method sets WDT desired timeout 
**
**     Parameters  : None
**     Returns     : None
** ============================================================================
*/
void WDT_SetTimeout(uint8_T wp, uint8_T wpext);


#endif /* __WDT_H__ */
