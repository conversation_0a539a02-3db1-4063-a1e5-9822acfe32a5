/*  File    : knock_corr.c
 *  Author  : <PERSON><PERSON>
 *  Date    : 10/07/2006 16.59
 *  Revision: KnockCorr
 *  Note      : Questo file include il codice generato in funzione del progetto.
 * 
 *  Copyright 2006 Eldor Corporation
 */

/**
#if (ENGINE_TYPE==FE_4300_8C_32V_GT2) 
// su Michelotto Corse si utilizza knockcorr statistico
#include "..\KNOCKCORR_F\src\KnockCorr_data.c"
#include "..\KNOCKCORR_F\src\knockcorr_calib.c"
#include "..\KNOCKCORR_F\src\KnockCorr.c"
***/
#if (ENGINE_TYPE==FE_4300_8C_32V)  || (ENGINE_TYPE==FE_4300_8C_32V_TDN) || (ENGINE_TYPE==FE_4300_8C_32V_GT2) 
// Solo per progetti Ferrari 8 cilindri si usa il controllo statistico della detonazione
//#include "..\KNOCKCORR_F\src\KnockCorr_data.c"
//#include "..\KNOCKCORR_F\src\knockcorr_calib.c"
//#include "..\KNOCKCORR_F\src\KnockCorr.c"
// Al momento anche su F430 si utilizza il controllo della detonazione standard.
#include "..\KNOCKCORR_E\KnockCorr_data.c"
#include "..\KNOCKCORR_E\KnockCorr.c"
                                         
#else
// Altrimenti si utilizza il controllo della detonazione standard.
#include "KNOCKCORR_E\KnockCorr_data.c"
#include "KNOCKCORR_E\KnockCorr.c"

#endif
