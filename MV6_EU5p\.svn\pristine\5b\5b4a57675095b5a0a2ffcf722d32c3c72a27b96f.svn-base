/*  File    : trqextreq.c
 *  Author  : <PERSON><PERSON><PERSON>
 *  Date    : 05/10/2006 16.43
 *  Revision: TrqExtReq 1.0
 *	Note	  : stubb
 * 
 *  Copyright 2006 Eldor Corporation
 */
#include "typedefs.h"
#include "Engflag.h"
#include "trq_drivmgm.h"
#include "trq_est.h"
#include "awheeling_ctrl.h"
#include "cmidriver_mgm.h"
#include "diagmgm_out.h"
#include "recmgm.h"
#include "Trqext_req.h"

// Uscite
int16_T  CmiReqP; // 2^-5
int16_T  CmiReqI; // 2^-5

#ifdef _BUILD_TRQEXTREQ_

void TrqExtReq_Init(void)
{
    CmiReqP = 0;
    CmiReqI = 0;
}

void TrqExtReq_T10ms(void)
{
    CmiReqP = CmiAwP;
    CmiReqI = CmiAwI;
}

#endif // _BUILD_TRQEXTREQ_

