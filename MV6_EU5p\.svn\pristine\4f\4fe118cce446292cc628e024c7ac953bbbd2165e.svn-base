/*
 * File: air_mgm.h
 *
 * Code generated for Simulink model 'AirMgm'.
 *
 * Model version                  : 1.2274
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Feb 25 13:57:17 2022
 */

#ifndef RTW_HEADER_air_mgm_h_
#define RTW_HEADER_air_mgm_h_
#include "rtwtypes.h"

/** local definitions **/
#ifdef  MATLAB_MEX_FILE
#define USE_MAP_SENSOR			1
#define USE_HFM_SENSOR			0
#define INTAKE_DELAY			0

#elif (ENGINE_TYPE==VW_1400_4C_16V)
#define USE_MAP_SENSOR			1
#define USE_HFM_SENSOR			0
#define INTAKE_DELAY			0

#elif (ENGINE_TYPE==EM_VW_1400_4C_16V) 
#define USE_MAP_SENSOR			1
#define USE_HFM_SENSOR			0
#define INTAKE_DELAY			0

#elif (ENGINE_TYPE==PI_500_1C_4V)
#define USE_MAP_SENSOR			0
#define USE_HFM_SENSOR			0
#define INTAKE_DELAY			0

#elif (ENGINE_TYPE==YP_250_G)
#define USE_MAP_SENSOR			1
#define USE_HFM_SENSOR			0
#define INTAKE_DELAY			0

#elif (ENGINE_TYPE==FE_6300_12C_48V)
#define USE_MAP_SENSOR			1
#define USE_HFM_SENSOR			0
#define INTAKE_DELAY			0

#elif (ENGINE_TYPE==FE_4300_8C_32V) || (ENGINE_TYPE==FE_4300_8C_32V_TDN)  || (ENGINE_TYPE==FE_4300_8C_32V_GT2)
#define USE_MAP_SENSOR          1
#define USE_HFM_SENSOR          0
#define INTAKE_DELAY            0

#elif (ENGINE_TYPE==MA_MC12_12C)
#define USE_MAP_SENSOR          1
#define USE_HFM_SENSOR          0
#define INTAKE_DELAY            0

#elif (ENGINE_TYPE==MV_AGUSTA_4C) || (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_08) ||  (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_20) ||  (ENGINE_TYPE==MV_AGUSTA_3C_TDC_0_30) || (ENGINE_TYPE==MV_AGUSTA_4C_TDC_0_9)
#define USE_MAP_SENSOR          1
#define USE_HFM_SENSOR          0
#define INTAKE_DELAY            0

#elif (ENGINE_TYPE==PI_250_1C_DBW) || (ENGINE_TYPE==PI_250_1C_HYBRID)
#define USE_MAP_SENSOR          1
#define USE_HFM_SENSOR          0
#define INTAKE_DELAY            0

#else
#error ATTENZIONE: Combinazione proibita!!!
#endif

#define N_QA_DEL_BUF 7

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T AbsTdcCalcAir;

/* index */
extern uint16_T AccQAirAvg;

/* AIRMGM.AccQAirAvg: QAir element sum */
extern uint16_T AccQAirBaseAvg;

/* AIRMGM.AccQAirBaseAvg: QAirBase element sum */
extern uint8_T AirFiltStartFlg;

/* K filt start enable */
extern uint8_T CntQAFIdle;

/* Tdc conuter stab */
extern int16_T DQAirTarget0;

/* AIRMGM.DQAirTarget0: QAirTarget0 correction for barometric pressure */
extern uint32_T IDAirMgm;

/* ID Version */
extern uint16_T KFiltPres;

/* AIRMGM.KFiltPres: Intake Pressure Filtering Gain */
extern uint8_T NDelayTrg;

/* AIRMGM.NDelayTrg: Size of dynamic QAirTargetBuffer delay */
extern uint8_T NFiltTrg;

/* AIRMGM.NFiltTrg: Size of dynamic QAirTargetBuffer */
extern int16_T PAtmCorr;

/* AIRMGM.PAtmCorr: Air Flow Atmospheric Pressure Correction */
extern uint16_T PAtmCorrGainCyl0;

/* Gain */
extern uint16_T PAtmCorrGainMaxCyl;

/* Gain */
extern uint16_T PAtmCorrGainMeas;

/* Gain */
extern uint16_T PAtmCorrGainMeas0;

/* Gain */
extern uint16_T PAtmCorrGainMinCyl;

/* Gain */
extern uint16_T PAtmCorrGainObj0;

/* Gain */
extern uint16_T PAtmCorrGainTrg0;

/* Gain */
extern uint16_T PresAtmRatio;

/* AIRMGM.PresAtmRatio: PresAtm/PRESATM0 ratio */
extern uint16_T PresIntake;

/* Intake pressure */
extern uint16_T PresIntk0;

/* AIRMGM.PresIntk0: Static Model Intake Pressure */
extern uint16_T PresIntk01;

/* AIRMGM.PresIntk01: Static Model Intake Pressure (throttle 1) */
extern uint16_T PresIntk02;

/* AIRMGM.PresIntk02: Static Model Intake Pressure (throttle 2) */
extern int16_T PresIntk0F;

/* AIRMGM.PresIntk0F: Dynamic Model Intake Pressure */
extern int16_T PresIntk0FTime;

/* AIRMGM.PresIntk0FTime: PresIntk0 filtered every 5ms */
extern int16_T PresIntkF1;

/* Dynamic model intake pressure */
extern int16_T PresIntkF2;

/* Dynamic model intake pressure */
extern uint16_T QAir[4];

/* Intake air mass */
extern uint16_T QAirAvg;

/* QAir Mean */
extern uint16_T QAirBase;

/* AIRMGM.QAirBase: Intake Air Mass target for observer */
extern uint16_T QAirBaseAvg;

/* QAir base mean */
extern uint16_T QAirBaseVet[4];

/* Intake air mass before predictor */
extern uint16_T QAirBuffer[16];

/* Intake air mass for MAV filter */
extern uint16_T QAirCyl;

/* AIRMGM.QAirCyl: Intake Estimated Air Mass */
extern uint16_T QAirCyl0;

/* AIRMGM.QAirCyl0: Intake Estimated Air Mass f(PresIntk0) */
extern uint16_T QAirCyl0F;

/* AIRMGM.QAirCyl0F: Filtered Intake Estimated Air Mass f(PresIntk0) */
extern int32_T QAirCyl0FHiR;

/* AIRMGM.QAirCyl0FHiR: Filtered Intake Estimated Air Mass f(PresIntk0) */
extern uint16_T QAirCylGain;

/* AIRMGM.QAirCylGain: Gain applied to QAirBase to obtain QAirCyl */
extern uint16_T QAirCylGainFuel;

/* AIRMGM.QAirCylGainFuel: Gain applied to QAirBase to obtain QAirFuel */
extern uint16_T QAirCylMeas;

/* AIRMGM.QAirCylMeas: Intake Measured Air Mass Filtered */
extern uint16_T QAirCylMeas0;

/* AIRMGM.QAirCylMeas0: QAirCylMeas without PAtmCorr */
extern uint16_T QAirCylRatio;

/* AIRMGM.QAirCylRatio: Ratio between QairCyl0 and QairCyl0F */
extern uint16_T QAirCylRatioFuel;

/* AIRMGM.QAirCylRatioFuel: Ratio beetween QAirTarget0 and QAirCyl0F */
extern uint16_T QAirFactor;

/* Air flow factor PresObj */
extern uint16_T QAirFuel[4];

/* Intake air mass */
extern uint16_T QAirFuelRatio;

/* AIRMGM.QAirFuelRatio: QAirFuelRatio */
extern uint8_T QAirIndex;

/* AIRMGM.QAirIndex: QAirBuffer index */
extern uint16_T QAirMaxCyl;

/* AIRMGM.QAirMaxCyl: Maximum Intake Air Mass */
extern uint16_T QAirMinCyl;

/* AIRMGM.QAirMaxCyl: Minimum Intake Air Mass */
extern uint16_T QAirObj0;

/* Intake air mass function of GasPos */
extern uint16_T QAirRatio[4];

/* Intake air mass relative ratio */
extern uint16_T QAirRatio0;

/* Intake air mass absolute ratio */
extern uint16_T QAirRatioTarget;

/* AIRMGM.QAirRatioTarget: Intake Air Mass Absolute Ratio (computed with QAirTargetMean) */
extern uint16_T QAirRatioTargetMax;

/* Intake air mass ratio */
extern uint16_T QAirRef;

/* QAIRREF */
extern uint16_T QAirTarget0F;

/* QAirTarget0 filtered */
extern int32_T QAirTarget0FHiR;

/* AIRMGM.QAirTarget0FHiR: Filtered Target Estimated Air Mass f(PresIntk0) */
extern uint16_T QAirTargetBuffer[16];

/* Target air mass for MAV filter */
extern uint8_T QAirTargetIndex;

/* AIRMGM.QAirTargetIndex: QAirTargetBuffer index */
extern uint16_T QAirTrg0Buff[7];

/* Intake air mass for MAV filter */
extern uint8_T StQAirFuel;

/* Status QAirFuel */
extern uint16_T TAirCorr;

/* AIRMGM.TAirCorr: Air Flow Temperature Correction */
extern uint16_T TAirFactCorr;

/* Output TAirFactCorr */
extern uint16_T TbDQAirTarget0;

/* Output TbDQAirTarget0 */
extern uint16_T TbQAirCyl0;

/* Output TbQAirObj0 */
extern uint16_T TbQAirCylMeas;

/* Output TbQAirCylMeas */
extern uint16_T TbQAirCylMeas0;

/* Output TbQAirCylMeas0 */
extern uint16_T TbQAirGain1;

/* Output of TBQAIRGAIN1 */
extern uint16_T TbQAirGain2;

/* Output of TBQAIRGAIN2 */
extern uint16_T TbQAirGain3;

/* Output of TBQAIRGAIN3 */
extern uint16_T TbQAirGainCorr;

/* Gain to transform reference cyl gain in whole cycle gain */
extern uint16_T TbQAirMaxCyl;

/* Output TbQAirMaxCyl */
extern uint16_T TbQAirMinCyl;

/* Output TbQAirMinCyl */
extern uint16_T TbQAirObj0;

/* Output TbQAirObj0 */
extern uint16_T TbQAirSelect;

/* AIRMGM.TbQAirSelect: TBQAIRSELECT */
extern uint16_T TbWaterCorr;

/* Output TbWaterCorr */
extern uint16_T VtTbQAirGain[4];

/* Array of TbQAirGain */

/** public functions **/
extern void AirMgm_Init(void);
extern void AirMgm_TDC(void);
extern void AirMgm_HTDC(void);
extern void AirMgm_PreTDC(void);
extern void AirMgm_NoSync(void);
extern void AirMgm_T100ms(void);
extern void AirMgm_T10ms(void);
extern void AirMgm_T5ms(void);
extern void AirMgm_Sync(void);

#endif                                 /* RTW_HEADER_air_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
