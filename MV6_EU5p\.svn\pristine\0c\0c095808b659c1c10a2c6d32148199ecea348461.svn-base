/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/ST_MV1_25_DBW/tree/APPLICATI#$   */
/* $ Description:                                                                                                */
/* $Revision:: 2801   $                                                                                          */
/* $Date:: 2010-06-11 17:03:38 +0200 (ven, 11 giu 2010)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

#ifndef __PT_TRAIN_DIAG_H_
#define __PT_TRAIN_DIAG_H_

extern uint16_T VehSpeed;
extern uint16_T VehSpeedCC;
extern uint16_T VehSpeedRearRbNc;
extern int16_T  DVehSpeedRearRb;
extern int16_T  DVehSpeedRearRbLc;
extern uint8_T  FlgVehStop;
extern uint8_T  FlgSpringUp;
extern uint8_T  FlgDriftUp;

void PTDiag_Init (void);
void PTDiag_T10ms (void);
void PTDiag_T100ms (void);

#endif
