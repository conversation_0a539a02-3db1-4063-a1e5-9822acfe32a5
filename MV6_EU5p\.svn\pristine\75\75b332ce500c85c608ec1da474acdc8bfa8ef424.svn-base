/*
 * File: MisfOBD2_out.h
 *
 * Code generated for Simulink model 'MisfOBD2'.
 *
 * Model version                  : 1.582
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Tue Jan  5 14:49:01 2021
 */

#ifndef RTW_HEADER_MisfOBD2_out_h_
#define RTW_HEADER_MisfOBD2_out_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint16_T CntEmissOBD2;

/* Emission misfire counter window */
extern uint8_T EnMisfCATOBD2;

/* CAT misfire enable */
extern uint8_T EnMisfEmissOBD2;

/* Emissions misfire enable */
extern uint8_T EnMisfOBD2;

/* misfire enable */
extern uint8_T FlgCatMisfOBD2;

/* Flg Catalyst damage OBD2 */
extern uint8_T FlgEmissMisfOBD2;

/* Flg Emissions damage OBD2 */
extern uint8_T FlgMisfDiagOn;

/* Flg Misfire driving cycle exec */
extern uint8_T FlgOBD2MILFlashing;

/* MIL in flashing mode */
extern uint32_T IDMisfOBD2;

/* ID Version */
extern int8_T MisfCylCAT;

/* Cylinder in misfire */
extern int8_T MisfCylEmiss;

/* Cylinder in misfire */
extern uint16_T NRevCAT;

/* window counter */
extern uint16_T NRevEmiss;

/* window counter */
extern uint16_T NRevOBD2Emiss;

/* Emission misfire window */
extern uint16_T SumCatMisfOBD2;

/* threshold counter */
extern uint16_T SumEmissMisfOBD2;

/* threshold counter */
extern uint16_T VtCntMisfCAT[4];

/* Misfire counter */
extern uint16_T VtCntMisfEmiss[4];

/* Misfire counter */

void MisfOBD2_Init(void);
void MisfOBD2_T10ms(void);
void MisfOBD2_PreTdc(void);

#endif                                 /* RTW_HEADER_MisfOBD2_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
