/***********************************************************************
*
*    DESCRIPTION: IMMOBILIZER HEADER FILE
*				   
*
*
*    AUTHOR:	   Solinas
*
*    HISTORY:     01/03/2007 First Version
*                 17/12/2007 Luca Garau: add increment factor error based
*  
*
*
* 
*
***********************************************************************/
 
 #ifdef   _BUILD_IMMO_ 
 		
 #ifndef IMMO_APP_H
 #define IMMO_APP_H

 #include "..\..\..\DD\LINMGM\include\PCF79xx.h"
 

/* ImmoCtxNRetry step */
 #define IMMO_INCR_FOR_XCVR_ERR           1 /*LG:20071217*/
 #define IMMO_INCR_FOR_ANTENNA_ERR        1 /*LG:20071217*/
 #define IMMO_INCR_FOR_XPDR_ERR           1 /*LG:20071217*/
 #define IMMO_INCR_FOR_IMMO_KEYNOTVLD_ERR 1 /*LG:20071217*/
 
 #define IMMO_MAXRETRY      10
 
/* ImmoCtxState */
 #define IMMO_IDLE          0 
 #define IMMO_START         1 
 #define IMMO_WAKEUP        2 
 #define IMMO_RESETXCVR     3 
 #define IMMO_DISXTO        4 
 #define IMMO_WRCFG1_1      5 
 #define IMMO_WRCFG1_2      6 
 #define IMMO_WRCFG1_3      7 
 #define IMMO_WRCFG2_1      8 
 #define IMMO_WRCFG2_2      9 
 #define IMMO_RDCFG         10 
 #define IMMO_RDPHMEAS      11 
 #define IMMO_WRCFG3        12 
 #define IMMO_WRSMPLT       13
 #define IMMO_ANTENNAEN     14
 #define IMMO_WRRDBUF       15
 #define IMMO_CHECKDATA     16
 #define IMMO_BLOCK         17
 #define IMMO_UNBLOCK       18
 #define IMMO_END           19
 #define IMMO_STOREKEY      20
 #define IMMO_BR9600        21
 #define IMMO_WAKEUP9600    22
 
/* ImmoEOLState */
#define IMMO_NOEOL    0
#define IMMO_WAITEOL    1
#define IMMO_EOL    2
 
/* ImmoCtxError */
 #define IMMO_NOERROR       1
 #define IMMO_KEYNOTVALID   2
 #define IMMO_XPDR_ERROR    3
 #define IMMO_XCVR_ERROR    4
 #define IMMO_ANTENNA_ERROR 5
 
/* waitCounter */
 #define WAIT5ms  1
 #define WAIT10ms (WAIT5ms<<1)
 #define WAIT15ms (WAIT5ms*3)
 #define WAIT20ms (WAIT5ms<<2)
 #define WAIT40ms (WAIT5ms<<3)
 #define WAIT80ms (WAIT5ms<<4)
 #define WAIT120ms (WAIT5ms*24)
 #define WAIT130ms (WAIT5ms*26)
 #define WAIT160ms (WAIT5ms<<5)
 
/* ImmoMainState */
#define IMMO_KEY_IDLE              0
#define IMMO_WAIT_KEY_ON_STORING   1
#define IMMO_KEY_ON_NORMAL         2  
#define IMMO_KEY_ON_STORING        3 
#define IMMO_KEY_OFF_NORMAL        4 
#define IMMO_KEY_OFF_STORING       5
#define IMMO_UNIVERSAL_KEY         6

/* Timer threshold */
#define SECONDS_15   150
#define SECONDS_3    70
#define IMMO_WD_TH	 155

 #define IMMO_LED_SIG1 0
 #define IMMO_LED_SIG2 1
 #define IMMO_LED_SIG3 2
 #define IMMO_LED_SIG4 3
 #define IMMO_LED_IDLE 4

 #define IMMO_LED_END      2
 #define IMMO_LED_RUNNING  1


#define STATE_IMMO 1
#define STATE_IMMO_ERROR 2
#define STATE_IMMO_nRETRY 3
#define STATE_IMMO_LED 4
#define STATE_IMMO_ENG 5

#define IMMO_DIAG_KEY_OK        0x01
#define IMMO_DIAG_ECU_BLANK     0x02
#define IMMO_DIAG_UNVALID_KEY   0x04
#define IMMO_DIAG_ANTENNA_ERROR 0x08
#define IMMO_DIAG_COMM_TIMEOUT  0x10

extern uint8_t ImmoCtxState;
extern uint8_t ImmoCtxError;
extern uint8_t ImmoCtxNRetry;
extern uint8_t ImmoCtxLastKey[6];
extern uint8_t ImmoCtxLedState;

extern uint8_t LINrxBuffer[40];
extern uint8_t txBuffer[8];
 
extern uint8_t ImmoMainState;
extern uint8_t ImmoE2RamCopyKeyStored[8][6];
extern uint8_t ImmoE2RamCopyKeyNumber;
 
extern uint8_t ImmoE2KeyStored [8][6];
extern uint8_t FlgUnivKey;



/* Tuning parameters */
extern uint8_T ENIMMOBLOCK;
extern uint8_T ENUNIVERSALKEY;
extern uint8_T UNIVERSALKEYVALUE[6];
extern uint8_T IMMOSMPLC;
extern uint8_T IMMOPHMEAS;
extern uint8_T IMMOPHMEASMIN;
extern uint8_T IMMOPHMEASMAX;

void IMMO_T5ms (void);
int16_t IMMO_FindKey (uint8_t* buffer,uint8_t*key);
int16_t IMMO_FindKeyM (uint8_t* buffer,uint8_t*key);

int8_t IMMO_KeyCmp(uint8_t* key);
int8_t IMMO_KeyCmpRam(uint8_t* key);
void IMMO_PowerOn (void);
void IMMO_KeyStoring (void);
void IMMO_BlockEngine (void);
void IMMO_UnblockEngine (void);
uint8_T IMMO_CheckEngine (void);

#ifdef   _BUILD_IMMO_ 
void IMMO_LedMgm (void);
uint8_t IMMO_LedSig1 (void);
uint8_t IMMO_LedSig2 (void);
uint8_t IMMO_LedSig3 (void);

uint8_t Get_LEDState(void);
void Set_LEDState(uint8_t );
uint8_t Get_ImmoState(void);
void Set_ImmoState(uint8_t );
 uint8_t Get_ImmoctxState(uint8_t );
uint8_t IMMO_GetDiagState (void);
void Immo_Diag(void); 
uint8_t Immo_SampleTimeCalc(uint8_t);
 
#endif

#endif //IMMO_APP_H

#else  //_BUILD_IMMO_
/* Tuning parameters */
extern uint8_T ENIMMOBLOCK;
#define IMMO_LED_IDLE 4

#endif //_BUILD_IMMO_
