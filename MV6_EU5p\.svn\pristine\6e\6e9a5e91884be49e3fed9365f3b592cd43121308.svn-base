#ifndef _OS_TASKS_H
#define _OS_TASKS_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "task.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ActivateTask - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 StatusType ActivateTask( TaskType taskId);

/*--------------------------------------------------------------------------*
 * TerminateTask - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 StatusType TerminateTask( void );

/*--------------------------------------------------------------------------*
 * ChainTask - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 StatusType ChainTask( TaskType TaskId);

/*--------------------------------------------------------------------------*
 * Schedule - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 StatusType Schedule( void );

/*--------------------------------------------------------------------------*
 * GetTaskID - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 StatusType GetTaskID( TaskRefType ptrTaskId);

/*--------------------------------------------------------------------------*
 * GetTaskState - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 StatusType GetTaskState( TaskType TaskId,
                          TaskStateRefType ptrState);

/*--------------------------------------------------------------------------*
 * OSInitHandlerManager - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 void OSInitHandlerManager(void);


#endif /* _OS_TASKS_H */
