#include "typedefs.h"
#include "canmgm_mv_f4.h"

#ifdef _BUILD_CANMGM_

#pragma ghs section rodata=".calib"

__declspec(section ".calib") uint8_T SELRPMCAN = 0;
__declspec(section ".calib") uint8_T ENFRZVEHCANENGSTR = 0;
__declspec(section ".calib") int8_T FOVDPITCHCAN = -1;
__declspec(section ".calib") int8_T FOVDROLLCAN = -1;
__declspec(section ".calib") uint8_T SELPITCH = 0;
__declspec(section ".calib") uint8_T SELROLL = 0;

__declspec(section ".calib") int8_T FOLCLEVEL = -1;

__declspec(section ".calib") int8_T FOAWLEVEL = -1;
__declspec(section ".calib") uint8_T ENVGEARPOSCAN = 0;

// Time delay after key on for CAN diagnosis
__declspec(section ".calib") uint16_T TNOCANDIAGAFTKEYON = 100;
// Minimum Vbat value to check other CAN nodes presence
__declspec(section ".calib") uint16_T CANVBATTHRMIN = 10.25*16;  /* min. VBat to check other CAN nodes' presence */

__declspec(section ".calib") uint16_T LAMCANGAIN[N_MSG_LAM] = 
    {512, 512, 512, 512};    /* 512 = 2/65535*2^24 */
__declspec(section ".calib") uint16_T LAMCANOFS[N_MSG_LAM] = 
    {716, 716, 716, 716};    /* 716 = 0.7*2^10 */
__declspec(section ".calib") uint8_T TIMGPCOUNTER = 20;
__declspec(section ".calib") uint8_T TIMGPDEBOUNCE = 1; 

__declspec(section ".calib") uint8_T IDMODELV = 0;

__declspec(section ".calib") uint8_T IDMODELYEAR = 0;

__declspec(section ".calib") uint16_T NMSGCANNODE2EN = 200;
__declspec(section ".calib") uint16_T NMSGCANNODE3EN = 200;

__declspec(section ".calib") uint8_T MINFWPRESBRAKE = 10;

__declspec(section ".calib") uint16_T FCELEANTO = 1200;

__declspec(section ".calib") uint8_T NUMINJDISIMMOCAN = 7;
__declspec(section ".calib") uint8_T DEBINJDISIMMOCAN = 6;

__declspec(section ".calib") uint8_T TIMCRPFREQ = 99;
__declspec(section ".calib") uint8_T TIMPBRFREQ = 99;
__declspec(section ".calib") uint8_T KITMAP = 0;

#endif  // _BUILD_CANMGM_
