/*
 * File: gaspos_mgm.h
 *
 * Code generated for Simulink model 'GasPosMgm'.
 *
 * Model version                  : 1.896
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Jul  6 10:19:29 2022
 */

#ifndef RTW_HEADER_gaspos_mgm_h_
#define RTW_HEADER_gaspos_mgm_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define FORCE_IDLE                     3U

/* RecLimTorqueGas 3: Force idle */
#define GAS_IDLE                       0U

/* Idle Switch State: Idle */
#define GAS_NOT_PLAUSIBLE              2U

/* Idle Switch State: Not plausible */
#define GAS_OUT_IDLE                   1U

/* Idle Switch State: Out of Idle */
#define HEAVY_TRQ_LIM                  2U

/* RecLimTorqueGas 2: Heavy torque limitation */
#define NO_TRQ_LIM                     0U

/* RecLimTorqueGas 1: Slight torque limitation */
#define SLIGHT_TRQ_LIM                 1U

/* RecLimTorqueGas 0: No torque limitation */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T FlgFreezeRecGas;

/* Freezes the current gas recovery until idling (=1) */
extern uint8_T FlgGasNeg;

/* Negative gas flag */
extern uint8_T FlgGasZero;

/* Most working gas sensors got 0 (=1) */
extern uint8_T GasCoh_DiagEn;

/* GasPos Coherence Diagnosis enable condition  */
extern uint16_T GasPos0;

/* Accelerator position */
extern uint16_T GasPos1;

/* Accelerator 1 position */
extern int16_T GasPos1Tot;

/* Total gas position */
extern uint16_T GasPos2;

/* Accelerator 2 position */
extern uint16_T GasPos_tmp;

/* Accelerator position temporary value */
extern uint8_T GasSwCoh_DiagEn;

/* Idle Switch Coherence Diagnosis enable condition  */
extern uint32_T IDGasPosMgm;

/* ID Version */
extern uint8_T IndUsedSens;

/* Accelerator sensor in use */
extern uint8_T RecGasType;

/* Method to calculate GasPos */
extern uint8_T RecGasType_tmp;

/* Method to calculate GasPos */
extern uint8_T RecLimTorqueGas;

/* Torque limitation request for gas */
extern uint8_T RecLimTorqueGas_Freeze;

/* Frozen torque limitation request for gas */
extern uint8_T RecLimTorqueGas_tmp;

/* Torque limitation request for gas */
extern uint8_T StDiagGasSwCoh;

/* Idle Switch Coheren Diagnosis state */
extern uint8_T StIdleSwitch;

/* Idle Switch state */
#endif                                 /* RTW_HEADER_gaspos_mgm_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
