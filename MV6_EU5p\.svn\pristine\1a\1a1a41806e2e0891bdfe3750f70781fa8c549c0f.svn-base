#ifndef _RECOVERY_SECURE_H_
#define _RECOVERY_SECURE_H_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PU<PERSON>IC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * RECOVERY_SECURE_Init - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
uint16_t RECOVERY_SECURE_Init(void);

#endif /* _RECOVERY_SECURE_H_ */
