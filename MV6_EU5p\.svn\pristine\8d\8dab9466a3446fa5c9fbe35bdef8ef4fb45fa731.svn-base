/*
 * File: qair_target.h
 *
 * Code generated for Simulink model 'QAirTargetMgm'.
 *
 * Model version                  : 1.715
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Sep 12 13:11:50 2022
 */

#ifndef RTW_HEADER_qair_target_h_
#define RTW_HEADER_qair_target_h_
#include "rtwtypes.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern int16_T GnRtQAirTarget0;

/* Gain */
extern uint32_T IDQAirTarget;

/* ID Version */
extern uint16_T QAirTarget;

/* QAIRTARGETMGM.QAirTarget: Air flow rate target (overall engine cycle) */
extern uint16_T QAirTarget0;

/* QAIRTARGETMGM.QAirTarget0: Air flow rate target (reference cylinder) */
extern uint16_T QAirTargetA;

/* QAIRTARGETMGM.QAirTarget: Air flow rate target frefiltered (overall engine cycle) */
extern uint16_T QAirTargetMean;

/* QAIRTARGETMGM.QAirTargetMean: Air flow rate target (mean cylinder) */

extern void QAirTargetMgm_Init(void);
extern void QAirTargetMgm_NoSync(void);
extern void QAirTargetMgm_T10ms(void);

#endif                                 /* RTW_HEADER_qair_target_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
