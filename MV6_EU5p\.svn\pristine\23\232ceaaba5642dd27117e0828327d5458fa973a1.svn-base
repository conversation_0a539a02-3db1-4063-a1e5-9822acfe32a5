#ifndef _EQADC_MESSAGES_H_
#define _EQADC_MESSAGES_H_

#include "typedefs.h"
/********************************************************************
 *    eQADC Message formats																					*
 ********************************************************************/
union eQADC_Message_tag {
    uint32_t R;
    struct {
        uint32_t EOQ            :1;
        uint32_t PAUSE          :1;
        uint32_t                :3;
        uint32_t EB             :1;
        uint32_t BN             :1;
        uint32_t CAL            :1;
        uint32_t MESSAGE_TAG    :4;
        uint32_t LST            :2;
        uint32_t TSR            :1;
        uint32_t FMT            :1;
        uint32_t CHANNEL_NUMBER :8;
        uint32_t                :8;
    } conversion_command;
    struct {
        uint32_t EOQ            :1;
        uint32_t PAUSE          :1;
        uint32_t                :3;
        uint32_t EB             :1;
        uint32_t BN             :1;
        uint32_t R_W            :1;
        uint32_t ADC_REG_HI     :8;
        uint32_t ADC_REG_LO     :8;
        uint32_t ADC_REG_ADDR   :8;
    } write_configuration_command;
    struct {
        uint32_t EOQ            :1;
        uint32_t PAUSE          :1;
        uint32_t                :3;
        uint32_t EB             :1;
        uint32_t BN             :1;
        uint32_t R_W            :1;
        uint32_t MESSAGE_TAG    :4;
        uint32_t                :12;
        uint32_t ADC_REG_ADDR   :8;
    } read_configuration_command;
};

typedef union eQADC_Message_tag eQADC_Message;

#endif /* _EQADC_MESSAGES_H_ */