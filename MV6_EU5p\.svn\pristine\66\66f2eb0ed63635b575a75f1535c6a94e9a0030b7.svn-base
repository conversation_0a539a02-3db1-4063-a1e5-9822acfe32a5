/** #########################################################################
**     Filename  : Saf2Mgm_eep.h
**     Project   : ELDOR ECU
**     Processor : SPC5533
**     Version   : 1.0
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 06/07/2007
**     Abstract  : Safety 2 NVM variables to help with calibration
**     (c) Copyright 2007 Eldor Corporation 
** ######################################################################### */
/* MODULE Saf2Mgm_eep */
#include "saf2mgm.h"

#ifdef _BUILD_SAF2MGM_

#ifdef _BUILD_S2_STORE_RESULTS_

    extern uint16_t   EES2AEstPresIntake;
    extern uint16_t   EEPresIntake_10ms;

    extern uint16_t   EEGasPos_10ms;
    extern uint16_t   EES2AEstGasPos;

    extern int16_t   EES2DEstCmeGasRpm;
    extern int16_t   EECmeGasRpm_10ms;

    extern int16_t   EES2EEstCmeTargCAN;
    extern int16_t   EECmeDriverCANF_10ms;

    extern uint16_t  EES2GEstQAirAvg;
    extern uint16_t  EEQAirAvg_10ms;

    extern int16_t   EESAopt_10ms;
    extern int16_t   EES2GEstSAopt;

    extern uint16_t  EES2GEstEffSAreal;
    extern uint16_t  EEEffSAReal_10ms;

    extern uint16_t  EEEffLambda_10ms;
    extern uint16_t  EES2GEstEffLambda;

    extern uint16_t  EEEffCutoff_10ms;
    extern uint16_t  EES2GEstEffCutoff;

    extern int16_t EES2GEstCmiEst;
    extern int16_t EECmiEst_10ms;

    extern int16_t EECmiDriverP_10ms;
    extern int16_t EES2HEstCmiDriverP;

    extern int16_t EECmfP_10ms;
    extern int16_t EES2HEstCmfP;

    extern uint16_t EES2FrzNEvents[SIZE_S2STORE];

    extern uint16_t EES2FrzRpm[SIZE_S2STORE];
    extern uint16_t EES2FrzLoad[SIZE_S2STORE];
    extern uint16_t EES2FrzPresIntake[SIZE_S2STORE];
    extern uint16_t EES2FrzGasPos[SIZE_S2STORE];
    extern uint16_t EES2FrzAngThrottle[SIZE_S2STORE];
    extern int16_t EES2FrzTWater[SIZE_S2STORE];

#endif

extern uint8_T S2PtFault;

#endif /* _BUILD_SAF2MGM_ */
