;CANoe Version |4|6|3|53797 IntegrationTestConfig 
Version: 8.1.75 Build 75
32 PRO
5
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1.0.1
VGlobalConfiguration 1 Begin_Of_Object
17
VGlobalParameters 2 Begin_Of_Object
15
2
3,100,200,500
1000000 10.000000 0 1000 1 1 0 0 1 1 1 0 0 0 1 0 0 0
1
0
1 1
ResetSignalsOnMeasurementStart=1
VDatabaseContainerStreamer 3 Begin_Of_Object
3
<VFileName V4 QL> 1 "DBC_Dac.dbc" 
DBC_Dac

1 1
0
1
0
1
<VFileName V4 QL> 1 "CanMgm_Taper.dbc" 
CanMgm_Taper

1 1
0
1
0
1
End_Of_Object VDatabaseContainerStreamer 3
0
0
1
<VFileName V4 QL> 1 "IntegrationTestConfig.cfg" 
0
0
0
1
VPersistentEnvarSelectionData 3 Begin_Of_Object
1
1 1 0 0
~
~
End_Of_Object VPersistentEnvarSelectionData 3
VPersistentExtensionData 3 Begin_Of_Object
3
VPersistentRelation 4 Begin_Of_Object
1
HookDLLActivations
1
1
End_Of_Object VPersistentRelation 4
End_Of_Object VPersistentExtensionData 3
VPersistentTreeStateInfo 3 Begin_Of_Object
1
Version 
5
DialogBegin
1
84 188 612 721
SymbolExplorerDialogBegin

1
HistoryBegin
1 0
HistoryEnd
FiltersBegin
Begin
3 247 0
4
CANMGM_K1KT1
 ( 1 ( 0 ) 0 ) 
DBC_Dac
 ( 1 ( 0 ) 2 ( 1 ( 0 ) 2 ( 0 ) 3 ( 0 ) 0 ) 0 ) 
____V100_draft
 ( 1 ( 0 ) 2 ( 0 ) 0 ) 
easy
 ( 1 ( 0 ) 2 ( 2 ( 0 ) 0 ) 0 ) 
SymbSelHeaderMgrBegin
1 6
0 1 255 0 0
1 1 100 0 0
2 1 171 0 0
3 1 100 1 1
5 1 100 1 1
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
1
DBC_Dac

SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
10 1 100 0 0
11 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 2 0
4
DBC_Dac
 ( 1 ( 0 ) 2 ( 0 ) 0 ) 
____V100_draft
 ( 1 ( 0 ) 0 ) 
____V100_draft_IDnew
 ( 1 ( 0 ) 0 ) 
____V111
 ( 0 ) 
SymbSelHeaderMgrBegin
1 4
0 1 194 0 0
1 1 100 0 0
3 1 100 1 1
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 1
0 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 1
0 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
6 1 100 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 3
0 1 200 0 0
7 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
3
CANMGM_K1KT1
 ( 1 ( 0 ) 0 ) 
LC8_V1_30
 ( 1 ( 0 ) 2 ( 0 ) 0 ) 
Predefined events
 ( 0 ) 
SymbSelHeaderMgrBegin
1 4
0 1 283 0 0
1 1 100 0 0
3 1 75 1 1
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
1 1 100 0 0
3 1 75 1 1
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
2
DBC_Dac

System variables
 ( 3 ( 0 ) 0 ) 
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
3
DBC_Dac

____V111

System variables
 ( 4 ( 0 ) 0 ) 
SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
10 1 75 0 0
11 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 6
0 1 200 0 0
1 1 100 0 0
2 1 100 0 0
3 1 75 1 1
5 1 75 1 1
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 3
0 1 200 0 0
12 1 100 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
13 1 75 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
6 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 1
0 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
13 1 75 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 2
0 1 200 0 0
13 1 75 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 1
0 1 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 1
0 1 200 0 0
SymbSelHeaderMgrEnd
End

FiltersEnd
1 12
SymbolExplorerDialogEnd

DialogEnd
End_Of_Object VPersistentTreeStateInfo 3
VPrintSettings 3 Begin_Of_Object
1
0 0 0 0 0
0 0 0 0 0 0 0 0 0 1
<VFileName V4 QL> 1 "" 
@@@@
Print page: {PAGE}    {DATE}  {TIME}
Licensename: {LICENSENAME}
Serial number: {LICENSENO}
@@@@
0

End_Of_Object VPrintSettings 3
<VFileName V4 QL> 1 "portlink1.pre" 
1
VPortlinkConfigurationStreamer 3 Begin_Of_Object
1
1
0
0
0
END_OF_DRIVER
END_OF_PORT_CONFIGURATION_STREAM
End_Of_Object VPortlinkConfigurationStreamer 3
0
1
VWTP20ObsParameters-1:0:0:
FlexRayTP2ObsParameters: 2 0x3 37 VFrTPParams 2 0 1 0 1 1 -1 0 8 255 0 :
FlexRayTP2ObsParametersEnd
VDoIPObserverParams 3 Begin_Of_Object
1
1
End_Of_Object VDoIPObserverParams 3
VISOTPParameters-1:active:onlyknown:interleave:firststmin:stmin:seqnr:unexpected:sender:extended:baseaddr=1024:rxmask=255:storedata:maxlen=4095
EOO
DiagnosticsSettingsV1.1
EOO
DiagnosticsSettingsV2.0
EOO
0
0
<VMultibusFilterDialogSettings> V2 233
<VWidthInfoContainer> 6
<VWidthInfo> V2 20 1 -1 36 
<VWidthInfo> V2 1 1 -1 47 
<VWidthInfo> V2 0 1 -1 40 
<VWidthInfo> V2 2 1 -1 47 
<VWidthInfo> V2 4 1 -1 33 
<VWidthInfo> V2 5 3 0 24 1 24 2 24 
<VBusWidthInfoSet> 2
1 0 <VWidthInfoContainer> 1
<VWidthInfo> V2 6 2 0 36 1 36 
1 1 <VWidthInfoContainer> 4
<VWidthInfo> V2 8 1 -1 30 
<VWidthInfo> V2 9 1 -1 30 
<VWidthInfo> V2 7 1 -1 45 
<VWidthInfo> V2 6 2 0 24 1 32 
EndOf <VMultibusFilterDialogSettings>
VGlobalActionsStreamer 3 Begin_Of_Object
2
2
0
End_Of_Object VGlobalActionsStreamer 3
VEventSortingConfigStreamer 3 Begin_Of_Object
1
0
0
0
1
1
1
0
End_Of_Object VEventSortingConfigStreamer 3
FlexRayOptionParameters: 1 1 1 0 1 1 :
FlexRayOptionParametersEnd
VCaplOptionsStreamer 3 Begin_Of_Object
1
15
1448
0
2001
1
2002
0
2005
0
2008
1
2013
1
2020
1
2032
1
2040
1
2041
1
2054
0
2055
1
2065
0
2135
1
2201
0
1
End_Of_Object VCaplOptionsStreamer 3
VSVConfigurationStreamer 3 Begin_Of_Object
1
1904
<?xml version="1.0" encoding="Windows-1252"?>
<systemvariables version="4">
  <namespace name="" comment="">
    <namespace name="BikeLoad" comment="">
      <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="BikeLoad" comment="" bitcount="32" isSigned="true" encoding="65001" type="int" />
    </namespace>
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="AccFrontBody" comment="" bitcount="64" isSigned="true" encoding="65001" type="float" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="FrontCurrent" comment="" bitcount="64" isSigned="true" encoding="65001" type="float" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="pippo" comment="" bitcount="64" isSigned="true" encoding="65001" type="floatarray" arrayLength="200" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="EngineSpeed" comment="" bitcount="32" isSigned="true" encoding="65001" type="int" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="RearCurrent" comment="" bitcount="64" isSigned="true" encoding="65001" type="float" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="AccRearBody" comment="" bitcount="64" isSigned="true" encoding="65001" type="float" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="RearStroke" comment="" bitcount="64" isSigned="true" encoding="65001" type="float" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="FrontStroke" comment="" bitcount="64" isSigned="true" encoding="65001" type="float" />
    <variable anlyzLocal="2" readOnly="false" valueSequence="false" unit="" name="VehicleSpeed" comment="" bitcount="32" isSigned="true" encoding="65001" type="int" />
  </namespace>
</systemvariables>
2
0

End_Of_Object VSVConfigurationStreamer 3
VOfflineBusStatisticSettings 3 Begin_Of_Object
1
1
0
1 1
1 500000
1 2
1 100000
0 3
1 0
0 4
1 0
0 5
1 0
0 6
1 0
0 7
1 0
0 8
1 0
0 9
1 0
0 10
1 0
0 11
1 0
0 12
1 0
0 13
1 0
0 14
1 0
0 15
1 0
0 16
1 0
0 17
1 0
0 18
1 0
0 19
1 0
0 20
1 0
0 21
1 0
0 22
1 0
0 23
1 0
0 24
1 0
0 25
1 0
0 26
1 0
0 27
1 0
0 28
1 0
0 29
1 0
0 30
1 0
0 31
1 0
0 32
1 0
End_Of_Object VOfflineBusStatisticSettings 3
VNETOptionsStreamer 3 Begin_Of_Object
1
0
End_Of_Object VNETOptionsStreamer 3
0
1
VUserFileMgrAnlyz 3 Begin_Of_Object
1
0
End_Of_Object VUserFileMgrAnlyz 3
VBasicDiagnosticStreamer 3 Begin_Of_Object
1
0
End_Of_Object VBasicDiagnosticStreamer 3
VPersistentCLibraryOptions 3 Begin_Of_Object
1
0
End_Of_Object VPersistentCLibraryOptions 3
NValueObjectDisplay::VNameDisplaySettings 3 Begin_Of_Object
3
13
0
4
1
4
2
4
3
4
4
1
5
1
6
4
7
4
8
4
9
6
10
4
11
4
12
4
0
13
0
0
1
0
2
0
3
0
4
0
5
0
6
0
7
0
8
0
9
0
10
0
11
0
12
0
13
0
1
1
3
2
3
3
3
4
1
5
1
6
1
7
1
8
1
9
1
10
1
11
1
12
3
13
0
255
1
255
2
255
3
255
4
255
5
255
6
255
7
255
8
255
9
128
10
255
11
255
12
255
0
End_Of_Object NValueObjectDisplay::VNameDisplaySettings 3
ConfigurationSavedByCANwBeginner 0
VGlobalExportSettings 3 Begin_Of_Object
2
2
1
0
0
6
1
0.10000000000000001
2
0
0.10000000000000001
2
19
0
0
0
1
0
::
,
.
<VFileName V4 QL> 1 "" 
0
End_Of_Object VGlobalExportSettings 3
0
VPersistentRTFilterOptions 3 Begin_Of_Object
2
0
0
0
0
End_Of_Object VPersistentRTFilterOptions 3
VPersistentRTTxBufferOptions 3 Begin_Of_Object
2
1
1
500
End_Of_Object VPersistentRTTxBufferOptions 3
VPersistentRTIRQReductionOptions 3 Begin_Of_Object
2
500
End_Of_Object VPersistentRTIRQReductionOptions 3
VPersistentDebuggerOptions 3 Begin_Of_Object
1
64
10000
End_Of_Object VPersistentDebuggerOptions 3
7
0
0
0
0
0
0
1
End_Of_Object VGlobalParameters 2
VDesktopManager 2 Begin_Of_Object
1
0
3
VDesktop 3 Begin_Of_Object
1
Trace
{C98DA121-0E28-4A45-9AB8-22D0B21CA43F}

End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Configuration
{A83F8E23-2E62-43EC-91DA-D8A7BA9D692B}

End_Of_Object VDesktop 3
VDesktop 3 Begin_Of_Object
1
Analysis
{EB706A5A-BA08-4372-9F95-6A7A47B199B2}

End_Of_Object VDesktop 3
4294967295
4294967295
4294967295
End_Of_Object VDesktopManager 2
0
VGBAnlyzBox 2 Begin_Of_Object
2
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 1 0 1 -1 -1 -1 -1 40 0 835 469

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 40 0 835 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
938 870
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 -232 -96 434 284
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
4
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 0 0 795 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 795 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
4
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{79EA2911-A854-4954-B3CA-C066929768FE}
0
End_Of_Object VBoxRoot 5
0 2 0 1 -1 -1 -1 -1 0 0 795 469
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
2
1
74
VDAOGBFunctionBlock 4 Begin_Of_Object
1
1
0
TABPredecessor:
0
TABSuccessor:
39
VPlugConf 5 Begin_Of_Object
1
End_Of_Object VPlugConf 5
VDAOSwitch 5 Begin_Of_Object
1
39
0
TABPredecessor:
1
TABSuccessor:
40
VDAOGBHSStd 6 Begin_Of_Object
1
40
0
0 0
TABPredecessor:
39
TABSuccessor:
42
VDODynamicLine 7 Begin_Of_Object
1
41
0
0
VDOFRamification 8 Begin_Of_Object
1
42
0
TABPredecessor:
40
TABSuccessor:
44
6
VDORefinement 9 Begin_Of_Object
1
43
0
1
VDAOGBHSStd 10 Begin_Of_Object
1
44
0
0 0
TABPredecessor:
42
TABSuccessor:
46
VDODynamicLine 11 Begin_Of_Object
1
45
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
46
0
TABPredecessor:
44
TABSuccessor:
49
VStatisticMainConfiguration 13 Begin_Of_Object
1
VStatWinMgr 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
1
1 0 0 1 -1 -1 -1 -1 0 0 795 354

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 0 0 795 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 795 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{A62E16EC-F21A-4A17-A81B-EBFB7632A631}
0
End_Of_Object VBoxRoot 15
End_Of_Object VStatWinMgr 14
VSWConfiguration 14 Begin_Of_Object
2
VSWStatisticConfiguration 15 Begin_Of_Object
1
0
0
200000
1
0
VSWSplitterViewConfiguration 16 Begin_Of_Object
1
0
0
NULL
End_Of_Object VSWSplitterViewConfiguration 16
0
0
End_Of_Object VSWStatisticConfiguration 15
End_Of_Object VSWConfiguration 14
0
1
<VFileName V4 QL> 0 "" 
<VFileName V4 QL> 0 "" 
End_Of_Object VStatisticMainConfiguration 13
VDOLine 13 Begin_Of_Object
1
47
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
48
0
2
VDAOGBHSStd 10 Begin_Of_Object
1
49
0
0 0
TABPredecessor:
46
TABSuccessor:
51
VDODynamicLine 11 Begin_Of_Object
1
50
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
51
0
TABPredecessor:
49
TABSuccessor:
54
VBusStatisticConfiguration 13 Begin_Of_Object
1
VBusStatisticBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
1
1 1 0 1 -1 -1 -1 -1 795 0 1591 354

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 795 0 1591 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 795 0 1591 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{D1EB7923-F3C1-460E-B61C-B84AEC52F499}
0
End_Of_Object VBoxRoot 15
8000
3
118
100
100
End_Of_Object VBusStatisticBox 14
End_Of_Object VBusStatisticConfiguration 13
VDOLine 13 Begin_Of_Object
1
52
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
53
0
7
VDAOGBHSStd 10 Begin_Of_Object
1
54
0
0 0
TABPredecessor:
51
TABSuccessor:
56
VDODynamicLine 11 Begin_Of_Object
1
55
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
56
0
TABPredecessor:
54
TABSuccessor:
59
VTraceConfiguration 13 Begin_Of_Object
1
VTraceControlCfg 14 Begin_Of_Object
8
VTraceSearchCfg 15 Begin_Of_Object
1
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
End_Of_Object VTraceSearchCfg 15
VTraceFilterCfg 15 Begin_Of_Object
1
0
1
VTraceAnalysisFilterGroup 16 Begin_Of_Object
2
1
Filter group 0
2
VTraceAnalysisSingleFilter 17 Begin_Of_Object
2
1
1
0
End_Of_Object VTraceAnalysisSingleFilter 17
VTraceAnalysisSingleFilter 17 Begin_Of_Object
2
0
0
0
End_Of_Object VTraceAnalysisSingleFilter 17
1
End_Of_Object VTraceAnalysisFilterGroup 16
End_Of_Object VTraceFilterCfg 15
0
0
0
0
17
0
0
1
1
14
ver=2: FT FT FT FT FT FT
End_Of_Serialized_Data 14
2
0
3
0
4
0
5
0
6
1
14
ver=2: FT TF TF FT FT FF;F T BikeLoad;T F _Statistics
End_Of_Serialized_Data 14
7
0
8
0
9
0
10
0
11
1
14
ver=2: FT FT FT FT
End_Of_Serialized_Data 14
12
0
13
0
14
1
14
ver=3: FT FT FT FT
End_Of_Serialized_Data 14
15
0
16
0
0
1
VTraceColumnConfiguration 15 Begin_Of_Object
3
1
Initial
80
VTNColumnData 16 Begin_Of_Object
2
0
196
0
Time
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
1
114
1
Chn
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
2
351
2
ID
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
3
239
3
Name
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
4
170
-1
ID / name
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
5
56
4
Dir
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
6
33
5
DLC
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
7
375
6
Data
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
8
37
-1
Attr
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
9
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
10
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
11
100
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
12
100
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
13
100
-1
Frame Duration
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
14
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
15
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
16
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
17
100
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
18
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
19
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
20
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
21
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
22
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
23
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
24
100
-1
Bus Idle
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
25
100
-1
Bus Busy
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
26
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
27
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
28
110
-1
HH:MM:SS
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
29
85
-1
Diff time
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
30
50
-1
Bustype
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
31
120
-1
Send node
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
32
50
-1
Bus
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
33
80
-1
Database
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
34
50
-1
Counter
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
35
100
-1
Start of Frame
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
36
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
37
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
38
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
39
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
40
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
41
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
42
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
43
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
44
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
45
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
46
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
47
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
48
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
49
50
-1
---
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
50
150
-1
Data ASCII
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
51
100
-1
Comment
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
52
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
53
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
54
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
55
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
56
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
57
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
58
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
59
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
60
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
61
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
62
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
63
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
64
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
65
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
66
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
67
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
68
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
69
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
70
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
71
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
72
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
73
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
74
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
75
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
76
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
77
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
78
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
VTNColumnData 16 Begin_Of_Object
2
79
10
-1
 
1
0
1
0
End_Of_Object VTNColumnData 16
End_Of_Object VTraceColumnConfiguration 15
0
0
VTraceControlFixedModeExpansionItems 15 Begin_Of_Object
3
6
VConfigMessage 16 Begin_Of_Object
4
VConfigBusObject 17 Begin_Of_Object
1
VConfigBusEvent 18 Begin_Of_Object
1
VConfigEvent 19 Begin_Of_Object
1
End_Of_Object VConfigEvent 19
1
1
End_Of_Object VConfigBusEvent 18
End_Of_Object VConfigBusObject 17
10
1
VDatabaseBusMessageId 17 Begin_Of_Object
2
608
1
1
0
0
0
ABS1
0
CanMgm_Taper
0
End_Of_Object VDatabaseBusMessageId 17
NULL
0
0
0
End_Of_Object VConfigMessage 16
VConfigMessage 16 Begin_Of_Object
4
VConfigBusObject 17 Begin_Of_Object
1
VConfigBusEvent 18 Begin_Of_Object
1
VConfigEvent 19 Begin_Of_Object
1
End_Of_Object VConfigEvent 19
1
1
End_Of_Object VConfigBusEvent 18
End_Of_Object VConfigBusObject 17
10
1
VDatabaseBusMessageId 17 Begin_Of_Object
2
752
1
1
0
0
0
AxisPosition1
0
CanMgm_Taper
0
End_Of_Object VDatabaseBusMessageId 17
NULL
0
0
0
End_Of_Object VConfigMessage 16
VConfigMessage 16 Begin_Of_Object
4
VConfigBusObject 17 Begin_Of_Object
1
VConfigBusEvent 18 Begin_Of_Object
1
VConfigEvent 19 Begin_Of_Object
1
End_Of_Object VConfigEvent 19
1
1
End_Of_Object VConfigBusEvent 18
End_Of_Object VConfigBusObject 17
10
1
VDatabaseBusMessageId 17 Begin_Of_Object
2
760
1
1
0
0
0
AxisPosition3
0
CanMgm_Taper
0
End_Of_Object VDatabaseBusMessageId 17
NULL
0
0
0
End_Of_Object VConfigMessage 16
VConfigMessage 16 Begin_Of_Object
4
VConfigBusObject 17 Begin_Of_Object
1
VConfigBusEvent 18 Begin_Of_Object
1
VConfigEvent 19 Begin_Of_Object
1
End_Of_Object VConfigEvent 19
1
1
End_Of_Object VConfigBusEvent 18
End_Of_Object VConfigBusObject 17
10
1
VDatabaseBusMessageId 17 Begin_Of_Object
2
1
1
1
0
0
0
DAC
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 17
NULL
0
0
0
End_Of_Object VConfigMessage 16
VConfigMessage 16 Begin_Of_Object
4
VConfigBusObject 17 Begin_Of_Object
1
VConfigBusEvent 18 Begin_Of_Object
1
VConfigEvent 19 Begin_Of_Object
1
End_Of_Object VConfigEvent 19
1
1
End_Of_Object VConfigBusEvent 18
End_Of_Object VConfigBusObject 17
9
1
VDatabaseBusMessageId 17 Begin_Of_Object
2
3
1
1
0
0
0
DAC_READ
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 17
NULL
0
0
0
End_Of_Object VConfigMessage 16
VConfigMessage 16 Begin_Of_Object
4
VConfigBusObject 17 Begin_Of_Object
1
VConfigBusEvent 18 Begin_Of_Object
1
VConfigEvent 19 Begin_Of_Object
1
End_Of_Object VConfigEvent 19
1
1
End_Of_Object VConfigBusEvent 18
End_Of_Object VConfigBusObject 17
9
1
VDatabaseBusMessageId 17 Begin_Of_Object
2
4
1
1
0
0
0
PRELOAD
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 17
NULL
0
0
0
End_Of_Object VConfigMessage 16
0
0
0
0
0
0
End_Of_Object VTraceControlFixedModeExpansionItems 15
14
C:\Codice\MV\MV5\APPL\AM_MV5_01\Test_Canoe
End_Of_Serialized_Data 14
14
Trace Window
End_Of_Serialized_Data 14
14
C:\Codice\MV\MV5\APPL\AM_MV5_01\Test_Canoe\Trace WindowTrace WindowTrace WindowTrace WindowTrace WindowTrace Window
End_Of_Serialized_Data 14
0
2
0
0
14
VLogExportPersister 15 Begin_Of_Object
3
1416
44616685
Trace Window
<VFileName V4 QL> 1 "Trace WindowTrace WindowTrace WindowTrace WindowTrace WindowTrace Window" 
<VFileName V4 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
0
0
2
19
0.10000000000000001
1
0
End_Of_Object VLogExportPersister 15

End_Of_Serialized_Data 14
0
0
0
290
1
64
<VFileName V4 QL> 1 "" 
End_Of_Object VTraceControlCfg 14
VNETTraceControlBox 14 Begin_Of_Object
1
VNETControlBox 15 Begin_Of_Object
2
VUniqueBox 16 Begin_Of_Object
1
VBoxRoot 17 Begin_Of_Object
1
1
0 4 0 1 -1 -1 -9 -36 -34 173 1344 666

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -9 -36 -34 173 1344 666
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
4
0
0
1
938 870
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 214 127 1229 497
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{BC0F97FA-A2C8-4D59-B477-532E2F81F837}
0
End_Of_Object VBoxRoot 17
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 16
0
0
End_Of_Object VNETControlBox 15
End_Of_Object VNETTraceControlBox 14
End_Of_Object VTraceConfiguration 13
VDOLine 13 Begin_Of_Object
1
57
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
58
0
4
VDAOGBHSStd 10 Begin_Of_Object
1
59
0
0 0
TABPredecessor:
56
TABSuccessor:
61
VDODynamicLine 11 Begin_Of_Object
1
60
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
61
0
TABPredecessor:
59
TABSuccessor:
64
VDataListControlHost 13 Begin_Of_Object
1
VConfigurationRoot 14 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 14
VDataBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
1
1 2 0 1 -1 -1 -1 -1 795 354 1591 708

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 795 354 1591 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 795 354 1591 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{8748B1AE-1BF9-47C6-994C-2576BFD58FDE}
0
End_Of_Object VBoxRoot 15
End_Of_Object VDataBox 14
1
5
1
VSignalObject 14 Begin_Of_Object
1
VSignalObjectBase 15 Begin_Of_Object
1
VHostSignal 16 Begin_Of_Object
2
0
AccFrontBody
0
End_Of_Object VHostSignal 16
2
VSignalState 16 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 16
End_Of_Object VSignalObjectBase 15
14
ValueObjectConfiguration::VConfiguredDBSignal 15 Begin_Of_Object
3
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 16 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 17 Begin_Of_Object
1
VConfigMessage 18 Begin_Of_Object
4
VConfigBusObject 19 Begin_Of_Object
1
VConfigBusEvent 20 Begin_Of_Object
1
VConfigEvent 21 Begin_Of_Object
1
End_Of_Object VConfigEvent 21
1
1
End_Of_Object VConfigBusEvent 20
End_Of_Object VConfigBusObject 19
31
1
VDatabaseBusMessageId 19 Begin_Of_Object
2
1
1
1
0
0
0
DAC
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 19
NULL
0
0
0
End_Of_Object VConfigMessage 18
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 17
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 16
AccFrontBody
1
1
1
CAN
DBC_Dac
Vector_XXX
1
DAC
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 15

End_Of_Serialized_Data 14
End_Of_Object VSignalObject 14
[Begin_of_Item]
2 1
1 1 2 0 0 16777215
0 79998.8 0 0
[End_of_Item]
20 150 16 100 75 75 50 100 100 100 1
35 35
1 1 0 1 0 0 1 1 1 0 1
0 0
1 0
5000 0 10000 0 10000
1 0
<VFileName V4 QL> 1 "CANoe_Data.mdf" 
0 0 0 0 0 0 0 0
1416 11060201
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0 2 1
::
,

4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
0 1024 0 60
1 1
0 0 0 0 0 2 0 0
1
[End_of_Control]
End_Of_Object VDataListControlHost 13
VDOLine 13 Begin_Of_Object
1
62
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
63
0
5
VDAOGBHSStd 10 Begin_Of_Object
1
64
0
0 0
TABPredecessor:
61
TABSuccessor:
66
VDODynamicLine 11 Begin_Of_Object
1
65
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
66
0
TABPredecessor:
64
TABSuccessor:
69
VGraphBoxConf 13 Begin_Of_Object
1
VGraphBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
1
1 3 0 1 -1 -1 -1 -1 73 95 1031 511

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 -180 2 615 356
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
663 560
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 73 95 1031 511
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 354 795 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{710EEA67-A1A9-45AD-B43B-30EFA66F0CF1}
0
End_Of_Object VBoxRoot 15
End_Of_Object VGraphBox 14
VSignalObjectStreamer 14 Begin_Of_Object
1
2
VSignalObject 15 Begin_Of_Object
1
VSignalObjectBase 16 Begin_Of_Object
1
VHostSignal 17 Begin_Of_Object
2
0
DacRead
0
End_Of_Object VHostSignal 17
2
VSignalState 17 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 17
End_Of_Object VSignalObjectBase 16
15
ValueObjectConfiguration::VConfiguredDBSignal 16 Begin_Of_Object
3
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 17 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 18 Begin_Of_Object
1
VConfigMessage 19 Begin_Of_Object
4
VConfigBusObject 20 Begin_Of_Object
1
VConfigBusEvent 21 Begin_Of_Object
1
VConfigEvent 22 Begin_Of_Object
1
End_Of_Object VConfigEvent 22
1
1
End_Of_Object VConfigBusEvent 21
End_Of_Object VConfigBusObject 20
31
1
VDatabaseBusMessageId 20 Begin_Of_Object
2
3
1
1
0
0
0
DAC_READ
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 20
NULL
0
0
0
End_Of_Object VConfigMessage 19
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 18
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 17
DacRead
0
1
1
CAN
DBC_Dac

3
DAC_READ
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 16

End_Of_Serialized_Data 15
End_Of_Object VSignalObject 15
[MeasurementObject]
DacRead
"" 159 ffff -1. 1. -100. 100. 1 0 0 1 36000000 0 1 0 0
VSignalObject 15 Begin_Of_Object
1
VSignalObjectBase 16 Begin_Of_Object
1
VHostSignal 17 Begin_Of_Object
2
0
FrontCurrent
0
End_Of_Object VHostSignal 17
2
VSignalState 17 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 17
End_Of_Object VSignalObjectBase 16
15
ValueObjectConfiguration::VConfiguredDBSignal 16 Begin_Of_Object
3
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 17 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 18 Begin_Of_Object
1
VConfigMessage 19 Begin_Of_Object
4
VConfigBusObject 20 Begin_Of_Object
1
VConfigBusEvent 21 Begin_Of_Object
1
VConfigEvent 22 Begin_Of_Object
1
End_Of_Object VConfigEvent 22
1
1
End_Of_Object VConfigBusEvent 21
End_Of_Object VConfigBusObject 20
31
1
VDatabaseBusMessageId 20 Begin_Of_Object
2
2
1
1
0
0
0
DAC_Fast
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 20
NULL
0
0
0
End_Of_Object VConfigMessage 19
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 18
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 17
FrontCurrent
1
1
1
CAN
DBC_Dac
Vector_XXX
2
DAC_Fast
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 16

End_Of_Serialized_Data 15
End_Of_Object VSignalObject 15
[MeasurementObject]
FrontCurrent
"" 1 cc99ff -31. -29. -100. 100. 0.2 0 0 0 36000000 1 1 0 0
[GraphWindow:x_x_x_x_x_x_WindowBk_Grid_AxisBk_XAxisFr_YAxisFr_x_x_x_x_x_x]
0 73382.199999999997 73382.199999999997 200000 36000000 1 0 8000 0 ffffff ffffff 0 0 1 1 1 0
0 30 5000
0
0 100
0
0
0
2
0
1
41943040
0
1416 25200253
Graphics Window
<VFileName V4 QL> 1 "" 
0 2 1
::
,

4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
12
18 16 130 20 18 55 55 55 55 55 57 13 
206
0
0
0
0
0
0
1
1
0
0
11711154
32768
0
0
0
0
0
0
0
0
0
300
0 10
1
2
1
0 0

DacRead
65535 0
1
1 1

FrontCurrent
16711935 0
0
<VFileName V4 QL> 1 "CANoe_Graphics.mdf" 
0 0 0 0 0 0 0 0
1416 11060201
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0 2 1
::
,

4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
0 1024 0 60
1 1
0 0 0 0 0 2 0 0
0 128
637 147 1366 582
1 40 20 15
0 -1
0
0
0
0
0
End_Of_Object VSignalObjectStreamer 14
End_Of_Object VGraphBoxConf 13
VDOLine 13 Begin_Of_Object
1
67
0
130 0
NULL
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
VDORefinement 9 Begin_Of_Object
1
68
0
6
VDAOGBHSStd 10 Begin_Of_Object
1
69
0
1 0
TABPredecessor:
66
TABSuccessor:
71
VDODynamicLine 11 Begin_Of_Object
1
70
0
0
VDAOGBFunctionBlock 12 Begin_Of_Object
1
71
0
TABPredecessor:
69
TABSuccessor:
73
VTriggerConfiguration 13 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 14 Begin_Of_Object
1
VTriggerCfgData 15 Begin_Of_Object
2
2
0
0
0
0
0
0
0
0
0
0
0
200000
1
1
1
1
1000
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
VEvCondBlock 16 Begin_Of_Object
1
VEvCondGroup 17 Begin_Of_Object
2
VEvCondPrimitive 18 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 18
1
0
0
End_Of_Object VEvCondGroup 17
End_Of_Object VEvCondBlock 16
0
0
0
116
0
End_Of_Object VTriggerCfgData 15
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 14
VTriggerBox 14 Begin_Of_Object
1
VBoxRoot 15 Begin_Of_Object
1
1
1 -1 0 1 0 0 0 0 319 138 1277 554

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{1B89ED6F-C4E3-4DE5-B7BE-D8FA34BE81F3}
0
End_Of_Object VBoxRoot 15
End_Of_Object VTriggerBox 14
0
End_Of_Object VTriggerConfiguration 13
VDOLine 13 Begin_Of_Object
1
72
0
10 0
VDAOGBFunctionBlock 14 Begin_Of_Object
1
73
0
TABPredecessor:
71
TABSuccessor:
0
VLoggingConfiguration 15 Begin_Of_Object
2
VMigratedGenericConfiguration<class_VLogCfgData> 16 Begin_Of_Object
1
VLogCfgData 17 Begin_Of_Object
5
1
1
1
1
0
0
0
0
1024
60
1
0
1
1
0
0
0
0
0
2
0
0
17
VLogExportPersister 18 Begin_Of_Object
3
1416
11060201
<VFileName V4 QL> 1 "CANOE.blf" 
<VFileName V4 QL> 1 "prova.csv" 
<VFileName V4 QL> 1 "" 
0
2
1
::
,
.
None
0
2
0
0.10000000000000001
6
0
0
2
19
0.10000000000000001
1
0
End_Of_Object VLogExportPersister 18

End_Of_Serialized_Data 17
<VFileName V4 QL> 1 "CANOE.blf" 
0
0
0
10
80
0
End_Of_Object VLogCfgData 17
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 16
End_Of_Object VLoggingConfiguration 15
VDOLine 15 Begin_Of_Object
1
74
0
60 0
NULL
End_Of_Object VDOLine 15

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 14
End_Of_Object VDOLine 13

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 12
End_Of_Object VDODynamicLine 11
End_Of_Object VDAOGBHSStd 10
NULL
End_Of_Object VDORefinement 9
End_Of_Object VDOFRamification 8
End_Of_Object VDODynamicLine 7
End_Of_Object VDAOGBHSStd 6
End_Of_Object VDAOSwitch 5

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
VDAOGBFunctionBlock 4 Begin_Of_Object
1
38
0
TABPredecessor:
0
TABSuccessor:
39
VOfflineSrcConfiguration 5 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VOfflineCfgData> 6 Begin_Of_Object
1
VOfflineCfgData 7 Begin_Of_Object
1
VReplayCfgBase 8 Begin_Of_Object
1
1
<VFileName V4 QL> 1 "CANOE.blf" 
1
End_Of_Object VReplayCfgBase 8
VCfgBreakCondition 8 Begin_Of_Object
1
VDataBreakCondition 9 Begin_Of_Object
1
0
VEvCondBlock 10 Begin_Of_Object
1
VEvCondGroup 11 Begin_Of_Object
2
VEvCondPrimitive 12 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 12
1
0
0
End_Of_Object VEvCondGroup 11
End_Of_Object VEvCondBlock 10
End_Of_Object VDataBreakCondition 9
End_Of_Object VCfgBreakCondition 8
0
0
0
0
0
End_Of_Object VOfflineCfgData 7
End_Of_Object VMigratedGenericConfiguration<struct_VOfflineCfgData> 6
End_Of_Object VOfflineSrcConfiguration 5
NULL

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
End_Of_Object VDOLocalInfoStruct 3
67239948
0
0
2
VDOLocalInfoStruct 3 Begin_Of_Object
1
1
36
VDAOGBFunctionBlock 4 Begin_Of_Object
1
1
0
TABPredecessor:
0
TABSuccessor:
2
VOfflineSrcConfiguration 5 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VOfflineCfgData> 6 Begin_Of_Object
1
VOfflineCfgData 7 Begin_Of_Object
1
VReplayCfgBase 8 Begin_Of_Object
1
0
1
End_Of_Object VReplayCfgBase 8
VCfgBreakCondition 8 Begin_Of_Object
1
VDataBreakCondition 9 Begin_Of_Object
1
0
VEvCondBlock 10 Begin_Of_Object
1
VEvCondGroup 11 Begin_Of_Object
1
VEvCondPrimitive 12 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 12
1
0
End_Of_Object VEvCondGroup 11
End_Of_Object VEvCondBlock 10
End_Of_Object VDataBreakCondition 9
End_Of_Object VCfgBreakCondition 8
0
0
0
0
0
End_Of_Object VOfflineCfgData 7
End_Of_Object VMigratedGenericConfiguration<struct_VOfflineCfgData> 6
End_Of_Object VOfflineSrcConfiguration 5
VDAOGBHSStd 5 Begin_Of_Object
1
2
0
0 0
TABPredecessor:
1
TABSuccessor:
4
VDODynamicLine 6 Begin_Of_Object
1
3
0
0
VDOFRamification 7 Begin_Of_Object
1
4
0
TABPredecessor:
2
TABSuccessor:
6
6
VDORefinement 8 Begin_Of_Object
1
5
0
1
VDAOGBHSStd 9 Begin_Of_Object
1
6
0
0 0
TABPredecessor:
4
TABSuccessor:
8
VDODynamicLine 10 Begin_Of_Object
1
7
0
0
VDAOGBFunctionBlock 11 Begin_Of_Object
1
8
0
TABPredecessor:
6
TABSuccessor:
11
VStatisticMainConfiguration 12 Begin_Of_Object
1
VStatWinMgr 13 Begin_Of_Object
1
VBoxRoot 14 Begin_Of_Object
1
2
1 0 0 1 -1 -1 -1 -1 0 0 795 354

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 0 0 795 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 795 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
End_Of_Object VBoxRoot 14
End_Of_Object VStatWinMgr 13
VSWConfiguration 13 Begin_Of_Object
2
VSWStatisticConfiguration 14 Begin_Of_Object
1
0
0
200000
1
0
VSWSplitterViewConfiguration 15 Begin_Of_Object
1
0
0
NULL
End_Of_Object VSWSplitterViewConfiguration 15
0
0
End_Of_Object VSWStatisticConfiguration 14
End_Of_Object VSWConfiguration 13
0
1
<VFileName V4 QL> 0 "" 
<VFileName V4 QL> 0 "" 
End_Of_Object VStatisticMainConfiguration 12
VDOLine 12 Begin_Of_Object
1
9
0
130 0
NULL
End_Of_Object VDOLine 12

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDAOGBHSStd 9
NULL
End_Of_Object VDORefinement 8
VDORefinement 8 Begin_Of_Object
1
10
0
2
VDAOGBHSStd 9 Begin_Of_Object
1
11
0
0 0
TABPredecessor:
8
TABSuccessor:
13
VDODynamicLine 10 Begin_Of_Object
1
12
0
0
VDAOGBFunctionBlock 11 Begin_Of_Object
1
13
0
TABPredecessor:
11
TABSuccessor:
16
VBusStatisticConfiguration 12 Begin_Of_Object
1
VBusStatisticBox 13 Begin_Of_Object
1
VBoxRoot 14 Begin_Of_Object
1
2
1 1 0 1 -1 -1 -1 -1 795 0 1591 354

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 795 0 1591 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 795 0 1591 354
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
End_Of_Object VBoxRoot 14
8000
3
118
100
100
End_Of_Object VBusStatisticBox 13
End_Of_Object VBusStatisticConfiguration 12
VDOLine 12 Begin_Of_Object
1
14
0
130 0
NULL
End_Of_Object VDOLine 12

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDAOGBHSStd 9
NULL
End_Of_Object VDORefinement 8
VDORefinement 8 Begin_Of_Object
1
15
0
7
VDAOGBHSStd 9 Begin_Of_Object
1
16
0
0 0
TABPredecessor:
13
TABSuccessor:
18
VDODynamicLine 10 Begin_Of_Object
1
17
0
0
VDAOGBFunctionBlock 11 Begin_Of_Object
1
18
0
TABPredecessor:
16
TABSuccessor:
21
VTNBoxConf 12 Begin_Of_Object
2
1
2
1
1
2000
500
500
1
0
0
VTNGUIConf 13 Begin_Of_Object
2
1
Default
52
VTNColumnData 14 Begin_Of_Object
1
0
104
0
Time
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
1
114
1
Chn
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
2
91
2
ID
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
3
239
3
Name
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
4
170
-1
ID / name
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
5
43
4
Dir
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
6
33
5
DLC
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
7
375
6
Data
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
8
37
-1
Attr
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
9
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
10
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
11
100
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
12
100
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
13
100
-1
Frame Duration
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
14
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
15
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
16
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
17
100
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
18
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
19
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
20
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
21
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
22
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
23
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
24
100
-1
Bus Idle
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
25
100
-1
Bus Busy
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
26
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
27
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
28
110
-1
HH:MM:SS
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
29
85
-1
Diff time
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
30
50
-1
Bustype
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
31
120
-1
Send node
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
32
50
-1
Bus
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
33
80
-1
Database
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
34
50
-1
Counter
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
35
100
-1
Start of Frame
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
36
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
37
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
38
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
39
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
40
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
41
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
42
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
43
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
44
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
45
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
46
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
47
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
48
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
49
50
-1
---
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
50
150
-1
Data ASCII
1
End_Of_Object VTNColumnData 14
VTNColumnData 14 Begin_Of_Object
1
51
100
-1
Comment
1
End_Of_Object VTNColumnData 14
End_Of_Object VTNGUIConf 13
VTNBox 13 Begin_Of_Object
1
VBoxRoot 14 Begin_Of_Object
1
2
0 3 0 1 -1 -1 -1 -1 -2 470 797 853

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 1378 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 -2 470 797 853
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 -1 -1 -1 -1 0 0 1378 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 -2 470 797 853
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
4
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
End_Of_Object VBoxRoot 14
VExternalTraceFilterStreamer 14 Begin_Of_Object
1
0
End_Of_Object VExternalTraceFilterStreamer 14
1
1
VTNExpandHash 14 Begin_Of_Object
1
4
VConfigMessage 15 Begin_Of_Object
2
VConfigBusObject 16 Begin_Of_Object
1
VConfigBusEvent 17 Begin_Of_Object
1
VConfigEvent 18 Begin_Of_Object
1
End_Of_Object VConfigEvent 18
1
1
End_Of_Object VConfigBusEvent 17
End_Of_Object VConfigBusObject 16
10
1
VDatabaseBusMessageId 16 Begin_Of_Object
1
2
1
1
0
0
0
DAC_Fast
0
DBC_Dac
End_Of_Object VDatabaseBusMessageId 16
NULL
0
End_Of_Object VConfigMessage 15
VConfigMessage 15 Begin_Of_Object
2
VConfigBusObject 16 Begin_Of_Object
1
VConfigBusEvent 17 Begin_Of_Object
1
VConfigEvent 18 Begin_Of_Object
1
End_Of_Object VConfigEvent 18
1
1
End_Of_Object VConfigBusEvent 17
End_Of_Object VConfigBusObject 16
9
1
VDatabaseBusMessageId 16 Begin_Of_Object
1
3
1
1
0
0
0
DAC_READ
0
DBC_Dac
End_Of_Object VDatabaseBusMessageId 16
NULL
0
End_Of_Object VConfigMessage 15
VConfigMessage 15 Begin_Of_Object
2
VConfigBusObject 16 Begin_Of_Object
1
VConfigBusEvent 17 Begin_Of_Object
1
VConfigEvent 18 Begin_Of_Object
1
End_Of_Object VConfigEvent 18
1
1
End_Of_Object VConfigBusEvent 17
End_Of_Object VConfigBusObject 16
10
1
VDatabaseBusMessageId 16 Begin_Of_Object
1
372
1
1
0
0
0
LAS_DRS_TxlD1
0
____V100_draft_IDnew
End_Of_Object VDatabaseBusMessageId 16
NULL
0
End_Of_Object VConfigMessage 15
VConfigMessage 15 Begin_Of_Object
2
VConfigBusObject 16 Begin_Of_Object
1
VConfigBusEvent 17 Begin_Of_Object
1
VConfigEvent 18 Begin_Of_Object
1
End_Of_Object VConfigEvent 18
1
1
End_Of_Object VConfigBusEvent 17
End_Of_Object VConfigBusObject 16
10
1
VDatabaseBusMessageId 16 Begin_Of_Object
1
376
1
1
0
0
0
LAS_DRS_TxlD2
0
____V100_draft_IDnew
End_Of_Object VDatabaseBusMessageId 16
NULL
0
End_Of_Object VConfigMessage 15
End_Of_Object VTNExpandHash 14
VTNWatchCfg 14 Begin_Of_Object
1
100 59422 2664204 311531352 4294967295 309828283 207688664 10 309986944 311535361 2678488
0
0
End_Of_Object VTNWatchCfg 14

EOF_DOCKBARDATA
End_Of_Object VTNBox 13
VEvCondBlock 13 Begin_Of_Object
1
VEvCondGroup 14 Begin_Of_Object
1
VEvCondPrimitive 15 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 15
1
0
End_Of_Object VEvCondGroup 14
End_Of_Object VEvCondBlock 13
12
VLogExportPersister 13 Begin_Of_Object
1
1424
11062245
Trace Window
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0
0
0
::
,
.

0
2
0
0.10000000000000001
6
1
0
2
19
0.10000000000000001
1
End_Of_Object VLogExportPersister 13

End_Of_Serialized_Data 12
3
1
1
12
ver=1: F F F F F F
End_Of_Serialized_Data 12
3
1
12
ver=1:T
End_Of_Serialized_Data 12
6
1
12
ver=1: F T T F F T
End_Of_Serialized_Data 12
0
End_Of_Object VTNBoxConf 12
VDOLine 12 Begin_Of_Object
1
19
0
130 0
NULL
End_Of_Object VDOLine 12

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDAOGBHSStd 9
NULL
End_Of_Object VDORefinement 8
VDORefinement 8 Begin_Of_Object
1
20
0
4
VDAOGBHSStd 9 Begin_Of_Object
1
21
0
0 0
TABPredecessor:
18
TABSuccessor:
23
VDODynamicLine 10 Begin_Of_Object
1
22
0
0
VDAOGBFunctionBlock 11 Begin_Of_Object
1
23
0
TABPredecessor:
21
TABSuccessor:
26
VDataListControlHost 12 Begin_Of_Object
1
VConfigurationRoot 13 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 13
VDataBox 13 Begin_Of_Object
1
VBoxRoot 14 Begin_Of_Object
1
2
1 2 0 1 -1 -1 -1 -1 795 354 1591 708

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 795 354 1591 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 795 354 1591 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
End_Of_Object VBoxRoot 14
End_Of_Object VDataBox 13
1
4
1
VSignalObject 13 Begin_Of_Object
1
VSignalObjectBase 14 Begin_Of_Object
1
VHostSignal 15 Begin_Of_Object
1
0
AccFrontBody
End_Of_Object VHostSignal 15
2
VSignalState 15 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 15
End_Of_Object VSignalObjectBase 14
13
ValueObjectConfiguration::VConfiguredDBSignal 14 Begin_Of_Object
1
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::VConfiguredDBSignal,class_ValueObjectConfiguration::IConfiguredDBSignal> 15 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 16 Begin_Of_Object
1
VConfigMessage 17 Begin_Of_Object
2
VConfigBusObject 18 Begin_Of_Object
1
VConfigBusEvent 19 Begin_Of_Object
1
VConfigEvent 20 Begin_Of_Object
1
End_Of_Object VConfigEvent 20
1
1
End_Of_Object VConfigBusEvent 19
End_Of_Object VConfigBusObject 18
31
1
VDatabaseBusMessageId 18 Begin_Of_Object
1
1
1
1
0
0
0
DAC
0
DBC_Dac
End_Of_Object VDatabaseBusMessageId 18
NULL
0
End_Of_Object VConfigMessage 17
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 16
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::VConfiguredDBSignal,class_ValueObjectConfiguration::IConfiguredDBSignal> 15
AccFrontBody
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 14

End_Of_Serialized_Data 13
End_Of_Object VSignalObject 13
[Begin_of_Item]
2 1
1 1 2 0 0 16777215
0 79998.8 0 0
[End_of_Item]
20 150 16 100 75 75 50 100 100 100 1
1 1 0 1 0 0 1 1 1 0 1 
1 0
5000 0 10000 0 10000
1 0
<VFileName V4 QL> 1 "CANoe_Data.mdf" 
0 0 0 0 0 0 0 0
1424 11060193
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0 2 1
::
,

4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
1 0
2 19
0.1
1
[End_of_Export]
0 1024 0 60
1 1
0 0 0 0 0 2 0 0
1
[End_of_Control]
End_Of_Object VDataListControlHost 12
VDOLine 12 Begin_Of_Object
1
24
0
130 0
NULL
End_Of_Object VDOLine 12

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDAOGBHSStd 9
NULL
End_Of_Object VDORefinement 8
VDORefinement 8 Begin_Of_Object
1
25
0
5
VDAOGBHSStd 9 Begin_Of_Object
1
26
0
0 0
TABPredecessor:
23
TABSuccessor:
28
VDODynamicLine 10 Begin_Of_Object
1
27
0
0
VDAOGBFunctionBlock 11 Begin_Of_Object
1
28
0
TABPredecessor:
26
TABSuccessor:
31
VGraphBoxConf 12 Begin_Of_Object
1
VGraphBox 13 Begin_Of_Object
1
VBoxRoot 14 Begin_Of_Object
1
2
1 3 0 1 -1 -1 -1 -1 73 95 1031 511

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 -180 2 615 356
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
663 560
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 73 95 1031 511
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 354 795 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
End_Of_Object VBoxRoot 14
End_Of_Object VGraphBox 13
VSignalObjectStreamer 13 Begin_Of_Object
1
2
VSignalObject 14 Begin_Of_Object
1
VSignalObjectBase 15 Begin_Of_Object
1
VHostSignal 16 Begin_Of_Object
1
0
DacRead
End_Of_Object VHostSignal 16
2
VSignalState 16 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 16
End_Of_Object VSignalObjectBase 15
14
ValueObjectConfiguration::VConfiguredDBSignal 15 Begin_Of_Object
1
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::VConfiguredDBSignal,class_ValueObjectConfiguration::IConfiguredDBSignal> 16 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 17 Begin_Of_Object
1
VConfigMessage 18 Begin_Of_Object
2
VConfigBusObject 19 Begin_Of_Object
1
VConfigBusEvent 20 Begin_Of_Object
1
VConfigEvent 21 Begin_Of_Object
1
End_Of_Object VConfigEvent 21
1
1
End_Of_Object VConfigBusEvent 20
End_Of_Object VConfigBusObject 19
31
1
VDatabaseBusMessageId 19 Begin_Of_Object
1
3
1
1
0
0
0
DAC_READ
0
DBC_Dac
End_Of_Object VDatabaseBusMessageId 19
NULL
0
End_Of_Object VConfigMessage 18
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 17
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::VConfiguredDBSignal,class_ValueObjectConfiguration::IConfiguredDBSignal> 16
DacRead
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 15

End_Of_Serialized_Data 14
End_Of_Object VSignalObject 14
[MeasurementObject]
DacRead
"" 159 ffff -1. 1. -100. 100. 1 0 0 1 36000000 0 1 0 0
VSignalObject 14 Begin_Of_Object
1
VSignalObjectBase 15 Begin_Of_Object
1
VHostSignal 16 Begin_Of_Object
1
0
FrontCurrent
End_Of_Object VHostSignal 16
2
VSignalState 16 Begin_Of_Object
1
0
1
End_Of_Object VSignalState 16
End_Of_Object VSignalObjectBase 15
14
ValueObjectConfiguration::VConfiguredDBSignal 15 Begin_Of_Object
1
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::VConfiguredDBSignal,class_ValueObjectConfiguration::IConfiguredDBSignal> 16 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 17 Begin_Of_Object
1
VConfigMessage 18 Begin_Of_Object
2
VConfigBusObject 19 Begin_Of_Object
1
VConfigBusEvent 20 Begin_Of_Object
1
VConfigEvent 21 Begin_Of_Object
1
End_Of_Object VConfigEvent 21
1
1
End_Of_Object VConfigBusEvent 20
End_Of_Object VConfigBusObject 19
31
1
VDatabaseBusMessageId 19 Begin_Of_Object
1
2
1
1
0
0
0
DAC_Fast
0
DBC_Dac
End_Of_Object VDatabaseBusMessageId 19
NULL
0
End_Of_Object VConfigMessage 18
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 17
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::VConfiguredDBSignal,class_ValueObjectConfiguration::IConfiguredDBSignal> 16
FrontCurrent
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 15

End_Of_Serialized_Data 14
End_Of_Object VSignalObject 14
[MeasurementObject]
FrontCurrent
"" 1 cc99ff -31. -29. -100. 100. 0.2 0 0 0 36000000 1 1 0 0
[GraphWindow:x_x_x_x_x_x_WindowBk_Grid_AxisBk_XAxisFr_YAxisFr_x_x_x_x_x_x]
0 73382.2 73382.2 200000 36000000 1 0 8000 0 ffffff ffffff 0 0 1 1 1 0
0 30 5000
0
0 100
0
0
0
2
0
1
20971520
0
1424 8423029
Graphics Window
<VFileName V4 QL> 1 "" 
0 0 0
::
,

4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
0
[End_of_Export]
12
18 16 130 20 18 55 55 55 55 55 57 13 
206
0
0
0
0
0
0
1
1
0
0
11711154
32768
0
0
0
0
0
0
0
0
0
300
0 10
1
2
1
0 0

DacRead
65535 0
1
1 1

FrontCurrent
16711935 0
0
<VFileName V4 QL> 1 "CANoe_Graphics.mdf" 
0 0 0 0 0 0 0 0
1424 11060193
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0 2 1
::
,

4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
1 0
2 19
0.1
1
[End_of_Export]
0 1024 0 60
1 1
0 0 0 0 0 2 0 0
0 128
637 147 1366 582
1 40 20 15
End_Of_Object VSignalObjectStreamer 13
End_Of_Object VGraphBoxConf 12
VDOLine 12 Begin_Of_Object
1
29
0
130 0
NULL
End_Of_Object VDOLine 12

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDAOGBHSStd 9
NULL
End_Of_Object VDORefinement 8
VDORefinement 8 Begin_Of_Object
1
30
0
6
VDAOGBHSStd 9 Begin_Of_Object
1
31
0
0 0
TABPredecessor:
28
TABSuccessor:
33
VDODynamicLine 10 Begin_Of_Object
1
32
0
0
VDAOGBFunctionBlock 11 Begin_Of_Object
1
33
0
TABPredecessor:
31
TABSuccessor:
35
VTriggerConfiguration 12 Begin_Of_Object
2
VMigratedGenericConfiguration<struct_VTriggerCfgData> 13 Begin_Of_Object
1
VTriggerCfgData 14 Begin_Of_Object
1
2
0
0
0
0
0
0
0
0
0
0
0
200000
1
1
1
1
1000
VEvCondBlock 15 Begin_Of_Object
1
VEvCondGroup 16 Begin_Of_Object
1
VEvCondPrimitive 17 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 17
1
0
End_Of_Object VEvCondGroup 16
End_Of_Object VEvCondBlock 15
VEvCondBlock 15 Begin_Of_Object
1
VEvCondGroup 16 Begin_Of_Object
1
VEvCondPrimitive 17 Begin_Of_Object
1
1
End_Of_Object VEvCondPrimitive 17
1
0
End_Of_Object VEvCondGroup 16
End_Of_Object VEvCondBlock 15
0
0
0
116
End_Of_Object VTriggerCfgData 14
End_Of_Object VMigratedGenericConfiguration<struct_VTriggerCfgData> 13
VTriggerBox 13 Begin_Of_Object
1
VBoxRoot 14 Begin_Of_Object
1
2
1 -1 0 1 0 0 0 0 319 138 1277 554

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
End_Of_Object VBoxRoot 14
End_Of_Object VTriggerBox 13
0
End_Of_Object VTriggerConfiguration 12
VDOLine 12 Begin_Of_Object
1
34
0
10 0
VDAOGBFunctionBlock 13 Begin_Of_Object
1
35
0
TABPredecessor:
33
TABSuccessor:
0
VLoggingConfiguration 14 Begin_Of_Object
2
VMigratedGenericConfiguration<class_VLogCfgData> 15 Begin_Of_Object
1
VLogCfgData 16 Begin_Of_Object
1
1
1
1
1
0
0
0
0
1024
60
1
0
1
1
0
0
0
0
0
2
0
0
16
VLogExportPersister 17 Begin_Of_Object
1
1424
11060193
<VFileName V4 QL> 1 "CANOE.blf" 
<VFileName V4 QL> 1 "prova.csv" 
<VFileName V4 QL> 1 "" 
0
2
1
::
,
.
None
0
2
0
0.10000000000000001
6
1
0
2
19
0.10000000000000001
1
End_Of_Object VLogExportPersister 17

End_Of_Serialized_Data 16
<VFileName V4 QL> 1 "CANOE.blf" 
End_Of_Object VLogCfgData 16
End_Of_Object VMigratedGenericConfiguration<class_VLogCfgData> 15
End_Of_Object VLoggingConfiguration 14
VDOLine 14 Begin_Of_Object
1
36
0
60 0
NULL
End_Of_Object VDOLine 14

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 13
End_Of_Object VDOLine 12

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 11
End_Of_Object VDODynamicLine 10
End_Of_Object VDAOGBHSStd 9
NULL
End_Of_Object VDORefinement 8
End_Of_Object VDOFRamification 7
End_Of_Object VDODynamicLine 6
End_Of_Object VDAOGBHSStd 5

EndOfComment
1
1
End_Of_Object VDAOGBFunctionBlock 4
End_Of_Object VDOLocalInfoStruct 3
End_Of_Serialized_Data 2
0.000000
0 0
End_Of_Object VGBAnlyzBox 2
VGBRealTimeBox 2 Begin_Of_Object
1
VGrMnBox 3 Begin_Of_Object
1
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
0 0 0 1 -1 -1 -1 -1 543 147 1714 666

1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 543 147 1714 666
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
938 870
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 434 -96 1338 284
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 -1 -1 -1 -1 795 0 1591 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 795 0 1869 469
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{B3C95C2C-5A7B-4529-94B8-CCB71903720D}
0
End_Of_Object VBoxRoot 5
0 1 0 1 -1 -1 -1 -1 795 0 1869 469
End_Of_Object VUniqueBox 4
End_Of_Object VGrMnBox 3
VDOLocalInfoStruct 3 Begin_Of_Object
2
1
178
VDAOBus 4 Begin_Of_Object
1
1
2
VDAOGBFunctionBlock 5 Begin_Of_Object
1
2
3
TABPredecessor:
1
TABSuccessor:
3
VOnlineGenBlockCfg 6 Begin_Of_Object
2
VMigratedGenericConfiguration<class_VOnlineGenBlockCfgData> 7 Begin_Of_Object
1
VOnlineGenBlockCfgData 8 Begin_Of_Object
1
1
1
0
VOGPersistentDialogData 9 Begin_Of_Object
1
0
0
0
VOGSplitterData 10 Begin_Of_Object
1
0.5
End_Of_Object VOGSplitterData 10
1
-1
-1
-1
-1
0
0
475
805
1
0
PersistentColumnWidth 10 Begin_Of_Object
1
1
15
15
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
290
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
60
60
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
50
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
50
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
22
22
22
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
40
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
85
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
End_Of_Object PersistentColumnWidth 10
PersistentColumnWidth 10 Begin_Of_Object
1
1
11
11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
223
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
72
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
22
22
22
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
80
80
80
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
80
80
80
End_Of_Object PersistentColumnWidth::ColumnData 11
End_Of_Object PersistentColumnWidth 10
End_Of_Object VOGPersistentDialogData 9
0
12
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
10000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 129 Engine_sim  CanMgm_Taper  0 1 0 0 8 34 187 204 221 238 223 253 161 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
6
1
CMEEstCAN_sim
55 16 0
GT_RECORD_END
1
CMEDriverCAN_sim
39 16 0
GT_RECORD_END
1
ClutchCAN_sim
0 1 0
GT_RECORD_END
1
RpmCAN_sim
23 16 0
GT_RECORD_END
1
GearPosCAN_sim
15 8 0
GT_RECORD_END
1
GasPosCAN_sim
7 7 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
500
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 608 ABS1  CanMgm_Taper  0 1 0 0 8 0 0 0 0 5 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
8
1
VehSpeedFrontCAN
7 16 0
GT_RECORD_END
1
VDVehSpeedFrontCAN
32 1 0
GT_RECORD_END
1
VehSpeedRearCAN
23 16 0
GT_RECORD_END
1
VDVehSpeedRearCAN
34 1 0
GT_RECORD_END
1
ABSWarningLamp
39 2 0
GT_RECORD_END
1
ABSPresent
37 1 0
GT_RECORD_END
1
VDFrontWheelPressure
48 1 0
GT_RECORD_END
1
Front_Wheel_Pressure
47 8 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 200 6 180 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
8
1
VehSpeedFrontCAN
7 16 0
GT_RECORD_END
1
VDVehSpeedFrontCAN
32 1 0
GT_RECORD_END
1
VehSpeedRearCAN
23 16 0
GT_RECORD_END
1
VDVehSpeedRearCAN
34 1 0
GT_RECORD_END
1
ABSWarningLamp
39 2 0
GT_RECORD_END
1
ABSPresent
37 1 0
GT_RECORD_END
1
VDFrontWheelPressure
48 1 0
GT_RECORD_END
1
Front_Wheel_Pressure
47 8 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 0 0 0 0 2 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
2000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 3 232 173 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
400
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 208 7 247 6 71 2 80 9 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 49 13 0 0 65 109 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
10000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 0 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 0 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 252 24 0 0 85 95 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
10000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 2 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 0 0 0 0 72 34 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
2
End_Of_Object VOnlineGenBlockCfgData 8
End_Of_Object VMigratedGenericConfiguration<class_VOnlineGenBlockCfgData> 7
4

BOF_MBSSDATA
1 0 2 0 
SS_BEGIN_COMMON_INFO
1
3
Behavior
1
Buses
1
Misc
1
SS_END_COMMON_INFO

EOF_MBSSDATA
1
0 0
1
1
VConfigurationBox 7 Begin_Of_Object
1
VUniqueBox 8 Begin_Of_Object
1
VBoxRoot 9 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 379 177 1520 711
IG  ---   Switched off
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 379 177 1520 711
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -32000 -32000 -1 -1 741 189 1564 709
0
1
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 379 177 1520 711
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
-1
1
0 1 -1 -1 -1 -1 65 38 1111 741
0
1
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 379 177 1520 711
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 480 260 1440 780
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 379 177 1520 711
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 480 260 1440 780
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 379 177 1520 711
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 157 337 1203 1040
0
1
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 379 177 1520 711
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 480 260 1440 780
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{2226E769-EC3B-4A01-BE40-778243A2C54E}
0
End_Of_Object VBoxRoot 9
1 -1 0 1 0 0 0 0 379 177 1520 711
End_Of_Object VUniqueBox 8
1
End_Of_Object VConfigurationBox 7
End_Of_Object VOnlineGenBlockCfg 6
NULL

EndOfComment
0
0
End_Of_Object VDAOGBFunctionBlock 5
VDAOGBFunctionBlock 5 Begin_Of_Object
1
81
3
TABPredecessor:
3
TABSuccessor:
166
VOnlineGenBlockCfg 6 Begin_Of_Object
2
VMigratedGenericConfiguration<class_VOnlineGenBlockCfgData> 7 Begin_Of_Object
1
VOnlineGenBlockCfgData 8 Begin_Of_Object
1
1
1
0
VOGPersistentDialogData 9 Begin_Of_Object
1
0
0
0
VOGSplitterData 10 Begin_Of_Object
1
0.67244702577590942
End_Of_Object VOGSplitterData 10
1
-1
-1
-1
-1
0
0
698
1137
1
0
PersistentColumnWidth 10 Begin_Of_Object
1
1
15
15
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
426
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
60
60
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
155
50
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
50
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
22
22
22
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
40
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
85
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
End_Of_Object PersistentColumnWidth 10
PersistentColumnWidth 10 Begin_Of_Object
1
1
11
11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
185
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
72
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
30
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
22
22
22
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
120
-1
-1
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
80
80
80
End_Of_Object PersistentColumnWidth::ColumnData 11
PersistentColumnWidth::ColumnData 11 Begin_Of_Object
1
-1
80
80
80
End_Of_Object PersistentColumnWidth::ColumnData 11
End_Of_Object PersistentColumnWidth 10
End_Of_Object VOGPersistentDialogData 9
0
12
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
500
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 129 Engine_sim  CanMgm_Taper  0 1 0 0 8 0 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
6
1
CMEEstCAN_sim
55 16 0
GT_RECORD_END
1
CMEDriverCAN_sim
39 16 0
GT_RECORD_END
1
ClutchCAN_sim
0 1 0
GT_RECORD_END
1
RpmCAN_sim
23 16 0
GT_RECORD_END
1
GearPosCAN_sim
15 8 0
GT_RECORD_END
1
GasPosCAN_sim
7 7 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
2000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 32 DashBoard  CanMgm_Taper  0 1 0 0 8 0 0 8 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
22
1
ImmobilizerFlag
22 1 0
GT_RECORD_END
1
DashboardFrameCounter
20 4 0
GT_RECORD_END
1
LowFuelFlag
23 1 0
GT_RECORD_END
1
TractionControlStatus
31 4 0
GT_RECORD_END
1
GasSensitivity
39 2 0
GT_RECORD_END
1
MaxEngineTorque
37 2 0
GT_RECORD_END
1
EngineBrake
35 1 0
GT_RECORD_END
1
EngineResponse
34 1 0
GT_RECORD_END
1
RpmLimiter
33 1 0
GT_RECORD_END
1
VehSpeedLimCAN
32 8 0
GT_RECORD_END
1
EnVSpeedLimCAN
40 1 0
GT_RECORD_END
1
QuickShiftEN
55 1 0
GT_RECORD_END
1
HeatedGripStatus
54 2 0
GT_RECORD_END
1
AirTemp
63 8 0
GT_RECORD_END
1
AitTempVD
16 1 0
GT_RECORD_END
1
BKL_Status
52 1 0
GT_RECORD_END
1
BikeLoadStatus
51 2 0
GT_RECORD_END
1
VehicleSpeedSetUp
0 9 0
GT_RECORD_END
1
VehicleSpeedControlStatus
2 2 0
GT_RECORD_END
1
DRLStatus
49 1 0
GT_RECORD_END
1
SuspensionModeRequest
26 3 0
GT_RECORD_END
1
QuickShiftType
48 1 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 760 AxisPosition3  CanMgm_Taper  0 1 0 0 8 205 12 0 0 233 220 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
5
1
RollCAN
23 16 0
GT_RECORD_END
1
PitchCAN
7 16 0
GT_RECORD_END
1
VehSpeedAbsetCAN
35 12 0
GT_RECORD_END
1
TempExtCAN
55 8 0
GT_RECORD_END
1
TempIntCAN
63 8 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 752 AxisPosition1  CanMgm_Taper  0 1 0 0 8 0 0 0 0 233 220 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
3
1
AyCAN
23 16 0
GT_RECORD_END
1
AxCAN
39 16 0
GT_RECORD_END
1
AzCAN
7 16 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 756 AxisPosition2  CanMgm_Taper  0 1 0 0 8 0 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
3
1
WzCAN
7 16 0
GT_RECORD_END
1
WyCAN
23 16 0
GT_RECORD_END
1
WxCAN
39 16 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
500
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 608 ABS1  CanMgm_Taper  0 1 0 0 8 0 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
8
1
VehSpeedFrontCAN
7 16 0
GT_RECORD_END
1
VDVehSpeedFrontCAN
32 1 0
GT_RECORD_END
1
VehSpeedRearCAN
23 16 0
GT_RECORD_END
1
VDVehSpeedRearCAN
34 1 0
GT_RECORD_END
1
ABSWarningLamp
39 2 0
GT_RECORD_END
1
ABSPresent
37 1 0
GT_RECORD_END
1
VDFrontWheelPressure
48 1 0
GT_RECORD_END
1
Front_Wheel_Pressure
47 8 0
GT_RECORD_END

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 208 7 208 7 117 6 136 6 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 0 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
10000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 2 0 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
5000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 16 100 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
1
1000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 252 24 0 0 220 233 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
VOnlineGenBlockMsgData 9 Begin_Of_Object
1
0
5000
0
116
0
1
1
0
0
1
0
Begin_Of_MBSS_Data
9
* 65535 ~  ~  1 1 0 0 8 0 100 0 0 0 0 0 0 EventType 0 2
0
1416 8451049
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "Interactive Generator Block" 
0 2 1
::
,
4
4
.
0 2 0
0.1
6
<VFileName V4 QL> 1 "" 
0 0
2 19
0.1
1
[End_of_Export]
VG_END_OF_EXPORT
VG_END_OF_GEN_COLL
1
0
0

GT_GATEWAY_TRIGGER_END

End_Of_Serialized_Data 9
End_Of_Object VOnlineGenBlockMsgData 9
6
End_Of_Object VOnlineGenBlockCfgData 8
End_Of_Object VMigratedGenericConfiguration<class_VOnlineGenBlockCfgData> 7
4
TractionCtrl Signals
BOF_MBSSDATA
1 0 0 0 
SS_BEGIN_COMMON_INFO
1
3
Behavior
1
Buses
1
Misc
1
SS_END_COMMON_INFO

EOF_MBSSDATA
1
0 0
1
1
VConfigurationBox 7 Begin_Of_Object
1
VUniqueBox 8 Begin_Of_Object
1
VBoxRoot 9 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 269 117 1076 471
TractionCtrl Signals  ---   Switched off
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 0 0 269 117 1076 471
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -32000 -32000 -1 -1 568 169 1723 912
0
1
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 269 117 1076 471
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 341 182 1025 546
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 269 117 1076 471
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 341 182 1025 546
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 0 0 269 117 1076 471
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 341 182 1025 546
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 269 117 1076 471
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 341 182 1025 546
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 0 0 269 117 1076 471
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 341 182 1025 546
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{02B04E69-FB8B-4255-AC2B-7BA7ED316ED9}
0
End_Of_Object VBoxRoot 9
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 8
1
End_Of_Object VConfigurationBox 7
End_Of_Object VOnlineGenBlockCfg 6
NULL

EndOfComment
0
0
End_Of_Object VDAOGBFunctionBlock 5
2
VDAOGBFunctionBlock 5 Begin_Of_Object
1
3
3
TABPredecessor:
2
TABSuccessor:
81
VSimulationNode 6 Begin_Of_Object
3
VProgrammedNode 7 Begin_Of_Object
4
VConfigurationRoot 8 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 8
<VFileName V4 QL> 1 "CAPL.can" 
1
C:\Codice\MV\MV5\APPL\AM_MV5_01\Test_Canoe\CAPL.cbf
<< default >>

5
ECU 1
ECU 1
EOF_TITLE_INFO
<< default >>
1
0
2
1
0
1
0
1 2 1 0 
SS_BEGIN_COMMON_INFO
1
4
Behavior
1
Buses
1
Misc
1
Timing
1
SS_END_COMMON_INFO

EOF_MBSSDATA
1
0 0
0
EOF_NLDATA
0
EOF_ASSEMBLYDATA
<VFileName V4 QL> 1 "CAPL.cbf" 
VIPBStackSetting 8 Begin_Of_Object
3
0
0
0
1
0
End_Of_Object VIPBStackSetting 8
7
NULL
End_Of_Serialized_Data 7
End_Of_Object VProgrammedNode 7
0
0
Startdelay 0 0 0
Jitter 0 0 1 0 0 0 0
0
EOF_NLDATA
3
VSimulinkModelViewerConfiguration 7 Begin_Of_Object
2
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
150
0
End_Of_Object VSimulinkModelViewerConfiguration 7
1
0
1568959368
0
NodeSignalPanelBustypeCount 1
CAN
NodeSignalPanelCount 1
VNETStandaloneComponent 7 Begin_Of_Object
1
VNETControlBox 8 Begin_Of_Object
2
VUniqueBox 9 Begin_Of_Object
1
VBoxRoot 10 Begin_Of_Object
1
3
1 2 0 1 -1 -1 -1 -1 419 173 1006 707
Node Panel - CAN - << default >>
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 419 173 1006 707
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
938 870
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 379 173 1516 694
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 379 173 1516 694
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 379 173 1516 694
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 379 173 1516 694
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 379 173 1516 694
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{9C7B4C08-B4B0-4A8B-B8C6-BC373EC346B5}
0
End_Of_Object VBoxRoot 10
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 9
1
1 -1 0 0 0 0 0 0 0 0 0 0
0
End_Of_Object VNETControlBox 8
481
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VMainApplication
1
1
Int32
AppChannel
1
String
DatabaseName
1

String
TxECUName
1
<< default >>
Boolean
BusContext
False
Int32
BusType
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VMainView
3
MainView
3
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VSignalView
5
SignalView
5
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VMessageView
6
MessageView
6
APPDIR Vector.CANoe.NodeSignalPanel.DLL
Vector.CANoe.NodeSignalPanel, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.NodeSignalPanel.VRxNodeView
7
RxNodeView
7
Int32
SelectedView
2
TypeRef:4
SerializationVersion
8
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:5
5
Int32
Grouping
0
Boolean
SignalColumn_Visible
True
Boolean
MuxColumn_Visible
False
Boolean
SubframeColumn_Visible
False
Boolean
PhysColumn_Visible
True
Boolean
RawColumn_Visible
True
Boolean
MessageColumn_Visible
False
Boolean
UnitColumn_Visible
False
Boolean
RxNodeColumn_Visible
False
Boolean
TxNodeColumn_Visible
False
Boolean
SymbolicValueColumn_Visible
True
Boolean
TxAckColumn_Visible
False
Boolean
SigLenColumn_Visible
False
Int32
SignalColumn_VisibleIndex
0
Int32
MuxColumn_VisibleIndex
-1
Int32
SubframeColumn_VisibleIndex
-1
Int32
PhysColumn_VisibleIndex
1
Int32
RawColumn_VisibleIndex
2
Int32
MessageColumn_VisibleIndex
-1
Int32
UnitColumn_VisibleIndex
-1
Int32
RxNodeColumn_VisibleIndex
-1
Int32
TxNodeColumn_VisibleIndex
-1
Int32
SymbolicValueColumn_VisibleIndex
3
Int32
TxAckColumn_VisibleIndex
-1
Int32
SigLenColumn_VisibleIndex
-1
Int32
SignalColumn_Width
115
Int32
MuxColumn_Width
75
Int32
SubframeColumn_Width
75
Int32
PhysColumn_Width
100
Int32
RawColumn_Width
100
Int32
MessageColumn_Width
163
Int32
UnitColumn_Width
75
Int32
RxNodeColumn_Width
168
Int32
TxNodeColumn_Width
168
Int32
SymbolicValueColumn_Width
252
Int32
TxAckColumn_Width
25
Int32
SigLenColumn_Width
75
TypeRef:4
SerializationVersion
9
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Int32
Grouping
0
Boolean
MessageColumn_Visible
True
Boolean
RxNodeColumn_Visible
False
Boolean
TxNodeColumn_Visible
False
Boolean
ButtonColumn_Visible
True
Boolean
SignalColumn_Visible
True
Boolean
MuxColumn_Visible
False
Boolean
SubframeColumn_Visible
False
Boolean
PhysColumn_Visible
True
Boolean
RawColumn_Visible
True
Boolean
UnitColumn_Visible
False
Boolean
SymbolicValueColumn_Visible
True
Boolean
TxAckColumn_Visible
False
Boolean
SigLenColumn_Visible
False
Int32
MessageColumn_VisibleIndex
0
Int32
RxNodeColumn_VisibleIndex
-1
Int32
TxNodeColumn_VisibleIndex
-1
Int32
ButtonColumn_VisibleIndex
1
Int32
SignalColumn_VisibleIndex
0
Int32
MuxColumn_VisibleIndex
-1
Int32
SubframeColumn_VisibleIndex
-1
Int32
PhysColumn_VisibleIndex
1
Int32
RawColumn_VisibleIndex
2
Int32
UnitColumn_VisibleIndex
-1
Int32
SymbolicValueColumn_VisibleIndex
3
Int32
TxAckColumn_VisibleIndex
-1
Int32
SigLenColumn_VisibleIndex
-1
Int32
MessageColumn_Width
503
Int32
RxNodeColumn_Width
432
Int32
TxNodeColumn_Width
164
Int32
ButtonColumn_Width
64
Int32
SignalColumn_Width
75
Int32
MuxColumn_Width
75
Int32
SubframeColumn_Width
75
Int32
PhysColumn_Width
75
Int32
RawColumn_Width
75
Int32
UnitColumn_Width
75
Int32
SymbolicValueColumn_Width
75
Int32
TxAckColumn_Width
25
Int32
SigLenColumn_Width
75
TypeRef:4
SerializationVersion
10
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:7
7
Int32
Grouping
0
Boolean
RxNodeColumn_Visible
True
Boolean
SignalColumn_Visible
True
Boolean
MuxColumn_Visible
False
Boolean
SubframeColumn_Visible
False
Boolean
PhysColumn_Visible
True
Boolean
RawColumn_Visible
True
Boolean
UnitColumn_Visible
False
Boolean
MessageColumn_Visible
False
Boolean
TxNodeColumn_Visible
True
Boolean
SymbolicValueColumn_Visible
True
Boolean
TxAckColumn_Visible
False
Boolean
SigLenColumn_Visible
False
Int32
RxNodeColumn_VisibleIndex
0
Int32
SignalColumn_VisibleIndex
0
Int32
MuxColumn_VisibleIndex
-1
Int32
SubframeColumn_VisibleIndex
-1
Int32
PhysColumn_VisibleIndex
1
Int32
RawColumn_VisibleIndex
2
Int32
UnitColumn_VisibleIndex
-1
Int32
MessageColumn_VisibleIndex
-1
Int32
TxNodeColumn_VisibleIndex
4
Int32
SymbolicValueColumn_VisibleIndex
3
Int32
TxAckColumn_VisibleIndex
-1
Int32
SigLenColumn_VisibleIndex
-1
Int32
RxNodeColumn_Width
567
Int32
SignalColumn_Width
75
Int32
MuxColumn_Width
75
Int32
SubframeColumn_Width
75
Int32
PhysColumn_Width
75
Int32
RawColumn_Width
75
Int32
UnitColumn_Width
75
Int32
MessageColumn_Width
75
Int32
TxNodeColumn_Width
75
Int32
SymbolicValueColumn_Width
75
Int32
TxAckColumn_Width
25
Int32
SigLenColumn_Width
75
TypeRef:4
SerializationVersion
11
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 7
End_Of_Object VSimulationNode 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
VDAOGBFunctionBlock 5 Begin_Of_Object
1
166
3
TABPredecessor:
81
TABSuccessor:
4
VSimulationNode 6 Begin_Of_Object
3
VProgrammedNode 7 Begin_Of_Object
4
VConfigurationRoot 8 Begin_Of_Object
1
End_Of_Object VConfigurationRoot 8
<VFileName V4 QL> 1 "CAPL_LL.can" 
1
C:\Codice\MV\MV5\APPL\AM_MV5_01\Test_Canoe\CAPL_LL.cbf


5
ECU 2
ECU 2
EOF_TITLE_INFO

1
0
1
1
0
1
0
1 2 0 0 
SS_BEGIN_COMMON_INFO
1
4
Behavior
1
Buses
1
Misc
1
Timing
1
SS_END_COMMON_INFO

EOF_MBSSDATA
1
0 1
0
EOF_NLDATA
0
EOF_ASSEMBLYDATA
<VFileName V4 QL> 1 "CAPL_LL.cbf" 
VIPBStackSetting 8 Begin_Of_Object
3
2
0
0
1
0
End_Of_Object VIPBStackSetting 8
7
NULL
End_Of_Serialized_Data 7
End_Of_Object VProgrammedNode 7
0
0
Startdelay 0 0 0
Jitter 0 0 1 0 0 0 0
0
EOF_NLDATA
3
VSimulinkModelViewerConfiguration 7 Begin_Of_Object
2
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
150
0
End_Of_Object VSimulinkModelViewerConfiguration 7
1
0
1332632263
0
NodeSignalPanelBustypeCount 0
End_Of_Object VSimulationNode 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
TABPredecessor:
0
TABSuccessor:
2
VDAOGBFunctionBlock 5 Begin_Of_Object
1
4
0
TABPredecessor:
166
TABSuccessor:
0
VCardConf 6 Begin_Of_Object
1
End_Of_Object VCardConf 6
NULL

EndOfComment
0
1
End_Of_Object VDAOGBFunctionBlock 5
End_Of_Object VDAOBus 4
NULL
End_Of_Object VDOLocalInfoStruct 3
0.000000
0 0
1 0 2 59421 1 280 1 2882400001 2394 2674 281 757 2882400002  0 0 0 0 0 0 1 2882400001 2389 2389 283 283 2882400002  0 0 0 597626192 136115368 511376532   3 
SS_BEGIN_COMMON_INFO
1
0
SS_END_COMMON_INFO

EOF_MBSSDATA
1
CAN
1
1
1
513136240 1 0 1 0 1 1 0 0 1 2000 1 
SS_BEGIN_COMMON_INFO
1
3
Channels
1
Databases
1
Misc
1
SS_END_COMMON_INFO

EOF_BUSDATA
1
_Start_VPRBSManager 1 
0 0x32 0x1 
_End_VPRBSManager
NodeSignalPanelBustypeCount 0
EOF_BUS

EOF_MBSSDATA
End_Of_Object VGBRealTimeBox 2
VWriteBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
0 3 0 1 -1 -1 -1 -1 256 503 1634 897
Write
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 256 503 1634 897
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
938 870
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 153 285 1288 628
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 -1 -1 -1 -1 0 469 1378 863
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
2
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 462 470 1809 887
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
3
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
2
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{3031BA73-D460-4DB2-A124-BE7A1F6DDDD4}
0
End_Of_Object VBoxRoot 4
0 3 0 1 -1 -1 -1 -1 462 470 1809 887
End_Of_Object VUniqueBox 3
2
VWriteControlAdapter 3 Begin_Of_Object
1
VControlAdapter 4 Begin_Of_Object
1
End_Of_Object VControlAdapter 4
6
3
WListVer 1
<VFileName V4 QL> 1 "" 
 100 0 73 1134
End_Of_Serialized_Data 3
3
WListVer 1
<VFileName V4 QL> 1 "" 
 100 0 500 0
End_Of_Serialized_Data 3
3
WListVer 1
<VFileName V4 QL> 1 "" 
 100 0 500 0
End_Of_Serialized_Data 3
3
WDebugVer 2
100 200

WTreeStateMark
V1
0 0
WDebugMgrMark
























WSelTreeStateMark
End_Of_Serialized_Data 3
3
WListVer 1
<VFileName V4 QL> 1 ""
 100 0 500 0
End_Of_Serialized_Data 3
3
WListVer 1
<VFileName V4 QL> 1 "" 
 100 0 500 0
End_Of_Serialized_Data 3
End_Of_Object VWriteControlAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VWriteBox 2
VWinStore 2 Begin_Of_Object
1
22 0 1 -32000 -32000 -1 -1 960 0 1916 1031
End_Of_Child_List
End_Of_Object VWinStore 2
VWinStore 2 Begin_Of_Object
1
22 2 3 -1 -1 -1 -1 508 16 1476 742
End_Of_Child_List
End_Of_Object VWinStore 2
VChipMultibusConfig 2 Begin_Of_Object
1
Version 7 9
5 32
0
9 0
11 0
1
14 0
1
12 1
1
0 127 0 0 1 2900 10
1 1
3
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
13 0
1
15 0
7 0
End_Of_Object VChipMultibusConfig 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 0
0
End_Of_Object VChipConfigC200 2
VChipConfigC200 2 Begin_Of_Object
1
0
200 16000 0 0
0 58 250 0 255 0 0
1 1000 1
0
End_Of_Object VChipConfigC200 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC005 2
VChipConfigC005 2 Begin_Of_Object
1
0
5 16000 0 0
0 35 96 0 2047 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC005 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 0
0
End_Of_Object VChipConfigC527 2
VChipConfigC527 2 Begin_Of_Object
1
0
527 16000 0 0
1 35 0 0 0 0 0 0 0 0
1 1000 1
0
End_Of_Object VChipConfigC527 2
VChipConfigC1000 2 Begin_Of_Object
1
1
1000 16000 0 0
65 20 1 0 2 0 0 0 0 0 0
1 1000 0
1
55 24 0
2 2 27 12 2 2
End_Of_Object VChipConfigC1000 2
VChipConfigC1000 2 Begin_Of_Object
1
2
1000 16000 0 0
65 20 1 0 2 0 0 0 0 0 0
1 1000 1
1
55 24 0
2 2 27 12 2 2
End_Of_Object VChipConfigC1000 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 0
0
End_Of_Object VChipConfigC462 2
VChipConfigC462 2 Begin_Of_Object
1
462 16000 0 0
125000 0 0 1 3 0 0 0 0 0 0 28 28 28 28 8 0 0 10
1 1000 1
0
End_Of_Object VChipConfigC462 2
0
9
3 0
5 0
6 0
7 0
8 0
9 0
11 0
13 0
14 0
VScanBaudrateConfiguration 2 Begin_Of_Object
1
2
256
1000
5
1000
1
0
1
256
1000
5
1000
0
0
0
End_Of_Object VScanBaudrateConfiguration 2
4
3
VPanelBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 379 177 1520 711
Panel2
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 466 358
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 0 0 470 362
1
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 2 22 468 380
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 683 557 1003 799
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 4 44 470 402
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 4 44 474 406
1
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 -1 -1 -1 -1 0 0 466 358
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 0 0 470 362
1
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 2 22 468 380
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 683 557 1003 799
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 4 44 470 402
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 4 44 474 406
1
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{C5738917-6F79-4420-8A66-21C05107E8DA}
0
End_Of_Object VBoxRoot 4
1 -1 0 1 0 0 0 0 379 177 1520 711
End_Of_Object VUniqueBox 3
2
VPanelAdapter 3 Begin_Of_Object
2
<VFileName V4 QL> 1 "Panel2.xvp" 
1
0
0
VPanelInfo 4 Begin_Of_Object
1
VPanelActiveX 5 Begin_Of_Object
1
4294967295
0
<VFileName V4 QL> 1 "" 
0
End_Of_Object VPanelActiveX 5
End_Of_Object VPanelInfo 4
1
End_Of_Object VPanelAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VPanelBox 2
VPanelBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 379 177 1520 711
Panel1
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 0 0 1198 994
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 544 36 1295 495
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 1198 994
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 2 22 1204 1020
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 1198 994
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 4 44 1206 1042
1
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 -1 -1 -1 -1 0 0 1198 994
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 0 0 1202 998
1
0
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 1198 994
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 2 22 1204 1020
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 0 1198 994
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 4 44 1206 1042
1
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{9FBB6254-30DE-4704-A3B4-877C33282E17}
0
End_Of_Object VBoxRoot 4
1 -1 0 1 0 0 0 0 379 177 1520 711
End_Of_Object VUniqueBox 3
2
VPanelAdapter 3 Begin_Of_Object
2
<VFileName V4 QL> 1 "Panel1.xvp" 
1
0
0
VPanelInfo 4 Begin_Of_Object
1
VPanelActiveX 5 Begin_Of_Object
1
4294967295
0
<VFileName V4 QL> 1 "" 
0
End_Of_Object VPanelActiveX 5
End_Of_Object VPanelInfo 4
1
End_Of_Object VPanelAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VPanelBox 2
VPanelBox 2 Begin_Of_Object
2
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 383 177 1533 711
PreLoad
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 2 22 844 478
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 960 341 1288 579
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 2 22 844 478
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 409 329 731 578
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 2 22 844 478
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 960 341 1840 1004
1
1
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 -1 -1 -1 -1 2 22 844 478
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 960 341 1840 1004
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 2 22 844 478
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
-1
1
0 1 -1 -1 -1 -1 409 329 1289 992
1
1
0 0
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 2 22 844 478
6 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
1
0 1 -1 -1 -1 -1 960 341 1840 1004
1
1
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{57C62D8D-C43B-4F4A-A523-EB5110CA0F58}
0
End_Of_Object VBoxRoot 4
0 -1 0 1 0 0 0 0 383 177 1533 711
End_Of_Object VUniqueBox 3
2
VPanelAdapter 3 Begin_Of_Object
2
<VFileName V4 QL> 1 "..\PreLoad.xvp" 
1
0
0
VPanelInfo 4 Begin_Of_Object
1
VPanelActiveX 5 Begin_Of_Object
1
4294967295
0
<VFileName V4 QL> 1 "" 
0
End_Of_Object VPanelActiveX 5
End_Of_Object VPanelInfo 4
1
End_Of_Object VPanelAdapter 3

End_Of_Serialized_Data 2
End_Of_Object VPanelBox 2
VPersistentPath 2 Begin_Of_Object
1
<VFileName V4 QL> 1 "IntegrationTestConfig.cpd" 
End_Of_Object VPersistentPath 2
0
3
0
1
VTestSetupBox 2 Begin_Of_Object
1
VUniqueBox 3 Begin_Of_Object
1
VBoxRoot 4 Begin_Of_Object
1
3
1 0 0 1 -1 -1 -1 -1 -232 284 925 481

1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 0 469 1591 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 -232 284 925 481
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1362 576
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 0 469 1591 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
1
1596 713
END_OF_DESKTOP_DATA
6
0 1 -1 -1 -1 -1 0 469 1378 708
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
1
0
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
0
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{614C8AE6-248B-4CB7-920F-C9CA9C73D79D}
0
End_Of_Object VBoxRoot 4
0 0 0 1 -1 -1 -1 -1 0 469 1378 708
End_Of_Object VUniqueBox 3
1
2 0
0

END_OF_TEST_SETUP_DATA
END_OF_TEST_SETUP_BOX
End_Of_Object VTestSetupBox 2
VPlugInsPersistentWrapper 2 Begin_Of_Object
1
<PlugIns/>
End_Of_Object VPlugInsPersistentWrapper 2
0
0
VMacroStreamer 2 Begin_Of_Object
2
VMacroManager 3 Begin_Of_Object
3
1
VMacro 4 Begin_Of_Object
2
<VFileName V4 QL> 1 "Macro.asc" 
Macro
100
0
0
0
0
0
1
2
0
1
1
End_Of_Object VMacro 4
0
0
0
End_Of_Object VMacroManager 3
End_Of_Object VMacroStreamer 2
VSignalGeneratorStreamer 2 Begin_Of_Object
1
VAnlyzSigGeneratorManager 3 Begin_Of_Object
2
1
VAnlyzSignalGenerator 4 Begin_Of_Object
2
3
4
ValueObjectConfiguration::VConfiguredDBSignal 5 Begin_Of_Object
3
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 6 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 7 Begin_Of_Object
1
VConfigMessage 8 Begin_Of_Object
4
VConfigBusObject 9 Begin_Of_Object
1
VConfigBusEvent 10 Begin_Of_Object
1
VConfigEvent 11 Begin_Of_Object
1
End_Of_Object VConfigEvent 11
1
1
End_Of_Object VConfigBusEvent 10
End_Of_Object VConfigBusObject 9
31
1
VDatabaseBusMessageId 9 Begin_Of_Object
2
1
1
1
0
0
0
DAC
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 9
NULL
0
0
0
End_Of_Object VConfigMessage 8
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 7
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 6
RearStroke
1
1
1
CAN
DBC_Dac
Vector_XXX
1
DAC
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 5

End_Of_Serialized_Data 4
VSignalGeneratorSine 5 Begin_Of_Object
1
VPersistentSignalGenerator 6 Begin_Of_Object
1
End_Of_Object VPersistentSignalGenerator 6
1000000000
500
0
500
1
End_Of_Object VSignalGeneratorSine 5
100000000
1
0
0
0
0
0
End_Of_Object VAnlyzSignalGenerator 4
1
VAnlyzSignalGenerator 4 Begin_Of_Object
2
3
4
ValueObjectConfiguration::VConfiguredDBSignal 5 Begin_Of_Object
3
ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 6 Begin_Of_Object
1
ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 7 Begin_Of_Object
1
VConfigMessage 8 Begin_Of_Object
4
VConfigBusObject 9 Begin_Of_Object
1
VConfigBusEvent 10 Begin_Of_Object
1
VConfigEvent 11 Begin_Of_Object
1
End_Of_Object VConfigEvent 11
1
1
End_Of_Object VConfigBusEvent 10
End_Of_Object VConfigBusObject 9
31
1
VDatabaseBusMessageId 9 Begin_Of_Object
2
1
1
1
0
0
0
DAC
0
DBC_Dac
0
End_Of_Object VDatabaseBusMessageId 9
NULL
0
0
0
End_Of_Object VConfigMessage 8
End_Of_Object ValueObjectConfiguration::Detail::AbstractConfiguredValueObject 7
End_Of_Object ValueObjectConfiguration::Detail::VConfiguredValueObjectBase<class_ValueObjectConfiguration::IConfiguredDBSignal> 6
RearStroke
1
1
1
CAN
DBC_Dac
Vector_XXX
1
DAC
0
4294967295
End_Of_Object ValueObjectConfiguration::VConfiguredDBSignal 5

End_Of_Serialized_Data 4
VSignalGeneratorSine 5 Begin_Of_Object
1
VPersistentSignalGenerator 6 Begin_Of_Object
1
End_Of_Object VPersistentSignalGenerator 6
1000000000
500
0
500
1
End_Of_Object VSignalGeneratorSine 5
100000000
1
0
0
0
0
0
End_Of_Object VAnlyzSignalGenerator 4
End_Of_Object VAnlyzSigGeneratorManager 3
End_Of_Object VSignalGeneratorStreamer 2
SignalGeneratorsReplay 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
0 3 0 1 -1 -1 -1 -1 336 104 1131 458
Signal Generators and Signal Replay
1

MDI_DOCK_INFO_END
5
1
6
0 1 -1 -1 -1 -1 336 104 1131 458
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
3
0
0
1
1337 781
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 564 299 1522 715
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
1
0
0
1
1916 888
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{5A6816F0-BF62-4C7C-97CC-2F29B9F1D081}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
0
0
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANoe.SignalGenerators.DLL
Vector.CANoe.SignalGenerators, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.SignalGenerators.ComponentWrapper
1
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
2
1
1
1
1
HistoryBegin
1 0
HistoryEnd
FiltersBegin
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 6
0 1 200 0 0
1 1 100 0 0
2 0 100 0 0
3 0 75 1 1
4 0 75 1 1
5 0 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 4
0 1 200 0 0
9 0 75 0 0
10 0 100 0 0
5 0 200 0 0
SymbSelHeaderMgrEnd
End
Begin
3 0 0
-1
SymbSelHeaderMgrBegin
1 3
0 1 200 0 0
6 0 100 0 0
5 0 200 0 0
SymbSelHeaderMgrEnd
End

FiltersEnd
0 0




























END_OF_WORKSPACE_MEMBER_DATA
END_OF_WORKSPACE_MEMBER
1
0
0

END_OF_WORKSPACE_DATA

END_OF_WORKSPACE_CONFIGURATION
LinNMWindow 0
LinScopeWindow 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 125 802 504
Scope
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{FFF708B8-D9AA-4C4B-8E7B-E50AD6F0158B}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
1240
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.VScopeCANoeControl
1
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
mApplication
2
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.ScopeConfiguration
3
mConfig
3
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
4
SerializationVersion
4
UInt16
mMajor
1
UInt16
mMinor
1
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.ConfigDevicesRoot
5
mDeviceConfigDataRoot
5
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.TriggerModeType
6
mTriggerMode
6
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.CapturedDataConfigSettings
7
mCapturedDataConfigSettings
7
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.AcquisitionConfigSettings
8
mAcquisitionConfigSettings
8
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.GeneralSettings
9
mGeneralSettings
9
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.ChartViewSettings
10
mChartViewSettings
10
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.FRProtocolSettings
11
mFRProtocolSettings
11
TypeRef:4
SerializationVersion
12
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:5
5
String
mName
1
Scopes
Boolean
mCanActivate
True
Boolean
mActivated
True
Int32
mNodeId
-1

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Object
12
mActivatedNode
0
Int32
Count
1
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.ConfigDevice
13
0
13
TypeRef:4
SerializationVersion
14
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Int32
Type
1
TypeRef:4
SerializationVersion
15
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:7
7
Boolean
mIsNewDataAutomaticallySelected
False
Boolean
mIsBufferSizeUserDefined
False
Int32
mBufferSize
100
Int32
mDataCleanupMode
1
TypeRef:4
SerializationVersion
16
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:8
8
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.AcquisitionTimeModel
14
TimeModel
17
Double
AcquisitionTime
10
UInt32
PreTrigger
50
UInt32
MinSamplesPerBit
10
Boolean
AllowRapidMode
False
Int32
AssuredBufferPhase
1
TypeRef:4
SerializationVersion
18
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:9
9
Boolean
mIsTriggerActivatedOnMeasurementStart
True
Boolean
mIsBufferSizeUserDefined
False
Boolean
mAreChannelsWithoutTriggerIgnored
False
Boolean
mIsDataClearedOnMeasurmentStart
True
Boolean
mIsChartDataAutoDisplayActive
True
Double
mBufferSize
400
Boolean
mIsRingModeBufferActive
False
TypeRef:4
SerializationVersion
19
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:10
10

System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a
System.Drawing.Color
15
mGridLineColor
20
String
name
0
Int64
value
0
Int16
knownColor
78
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mAxisLineColor
21
String
name
0
Int64
value
0
Int16
knownColor
78
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mAxisTicksColor
22
String
name
0
Int64
value
0
Int16
knownColor
78
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mChartViewBackground
23
String
name
0
Int64
value
0
Int16
knownColor
164
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mCANSignalColor
24
String
name
0
Int64
value
4278223103
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mLINSignalColor
25
String
name
0
Int64
value
4278222848
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mIOSignalColor
26
String
name
0
Int64
value
4288432654
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mFrameHighlightColor
27
String
name
0
Int64
value
4286611584
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mFrameItemHighlightColor
28
String
name
0
Int64
value
4294934528
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mCANStuffBitColor
29
String
name
0
Int64
value
4294934528
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mLINInterbyteSpaceColor
30
String
name
0
Int64
value
2104524944
Int16
knownColor
0
Int16
state
2
--TextFormatter: End of Object--
TypeRef:15
mIOExternSignalColor
31
String
name
0
Int64
value
0
Int16
knownColor
104
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mFRHighSignalColor
32
String
name
0
Int64
value
0
Int16
knownColor
48
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mFRLowSignalColor
33
String
name
0
Int64
value
0
Int16
knownColor
107
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mFRDiffSignalColor
34
String
name
0
Int64
value
0
Int16
knownColor
35
Int16
state
1
--TextFormatter: End of Object--
TypeRef:15
mLINSignalColorNew
35
String
name
0
Int64
value
0
Int16
knownColor
53
Int16
state
1
--TextFormatter: End of Object--
Boolean
mShowCursorToolTips
True
Boolean
mShowMultipleSignalsAsStack
True
Boolean
mShowGridLines
True
Double
mYDistanceMainTicks
1000
Double
mYDistanceMainTicksMin
100
Double
mYDistanceMainTicksMax
10000
Boolean
mUseSmartLabels
True
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.TimeUnitType
16
mTimeUnitMode
36
Int32
mDisplayedMode
0
Boolean
CANHighAutomaticallyVisible
True
Boolean
CANLowAutomaticallyVisible
True
Boolean
FRPlusAutomaticallyVisible
True
Boolean
FRMinusAutomaticallyVisible
True
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Impl.CalculationMap
17
GlobalCalculationsStrategy
37
TypeRef:15
mCANCalculatedSignalColor
38
String
name
0
Int64
value
0
Int16
knownColor
141
Int16
state
1
--TextFormatter: End of Object--
TypeRef:4
SerializationVersion
39
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
6
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:11
11
Int32
mFRLowLevelMaxLogicalRecessive
-150
Int32
mFRHighLevelLogicalDominant
150
Double
mSamplingPoint
0.7
Boolean
mConsiderErrorFramesForDriftComp
True
TypeRef:4
SerializationVersion
40
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:13
13
String
mName
1
Scope_1
Boolean
mCanActivate
True
Boolean
mActivated
True
Int32
mNodeId
1
TypeRef:12
mActivatedNode
0
Int32
Count
0
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.ScopeDevicePicoTech_4227
18
mDevice
41
TypeRef:4
SerializationVersion
42
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:14
17
Int32
Type
1
TypeRef:4
SerializationVersion
43
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:16
36
Int32
Type
0
TypeRef:4
SerializationVersion
44
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:17
37
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.CapturedData.DataCollection`1[[Vector.CANalyzer.Scope.Misc.SerializableKeyValuePair`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Vector.CANalyzer.Scope.CapturedData.DataCollection`1[[Vector.CANalyzer.Scope.Interfaces.IBusSignalCalculation, Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]], Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]], Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]]
19
CalculationData
45
TypeRef:4
SerializationVersion
46
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:18
41
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.ScopeType
20
cScopeType
47
Boolean
mIsAutomaticSamplingPeriodConfig
True
UInt32
mSamplingPeriod
192
Int32
NumberOfBNC
3
Double
mSamplingPeriodDoubleNs
192
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.ScopeBNCMapping
21
BNCMapping_0
48
TypeRef:21
BNCMapping_1
49
TypeRef:21
BNCMapping_2
50
Int64
TimeBase4227
8
TypeRef:4
SerializationVersion
51
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:19
45
Int32
Count
1
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Misc.SerializableKeyValuePair`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Vector.CANalyzer.Scope.CapturedData.DataCollection`1[[Vector.CANalyzer.Scope.Interfaces.IBusSignalCalculation, Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]], Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]]
22
Item_0
52
--TextFormatter: End of Object--
TypeRef:20
47
Int32
Type
2
TypeRef:4
SerializationVersion
53
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:21
48
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.BNCChannelType
23
BNC_CHANNEL_TYPE
54
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.ProbeType
24
PROBE
55
String
mLabel
1

APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Configuration.BNCSignalSourceInfo
25
SignalSource
56
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.VPS4000+Range
26
mVoltageMeasurementRangeScope
57
Int32
value__
5
--TextFormatter: End of Object--
TypeRef:4
SerializationVersion
58
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:21
49
TypeRef:23
BNC_CHANNEL_TYPE
59
TypeRef:24
PROBE
60
String
mLabel
1

TypeRef:25
SignalSource
61
TypeRef:26
mVoltageMeasurementRangeScope
62
Int32
value__
5
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:21
50
TypeRef:23
BNC_CHANNEL_TYPE
63
TypeRef:24
PROBE
64
String
mLabel
1

TypeRef:25
SignalSource
65
TypeRef:26
mVoltageMeasurementRangeScope
66
Int32
value__
8
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:22
52
Int32
mKey
1
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.CapturedData.DataCollection`1[[Vector.CANalyzer.Scope.Interfaces.IBusSignalCalculation, Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]]
27
mValue
67
TypeRef:4
SerializationVersion
68
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:23
54
Int32
Type
0
TypeRef:4
SerializationVersion
69
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:24
55
Int32
Type
10
TypeRef:4
SerializationVersion
70
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:25
56
Int32
mBusType
1
UInt32
mChannelIndex
1
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.BNCSignalType
28
mSignalType
71
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.BNCSignalSource
29
mSignalSource
72
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Interfaces.FlexRayChannelMaskWrapper
30
mChannelMaskFR
73
TypeRef:4
SerializationVersion
74
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
2
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:23
59
Int32
Type
1
--TextFormatter: End of Object--
TypeRef:24
60
Int32
Type
10
--TextFormatter: End of Object--
TypeRef:25
61
Int32
mBusType
1
UInt32
mChannelIndex
1
TypeRef:28
mSignalType
75
TypeRef:29
mSignalSource
76
TypeRef:30
mChannelMaskFR
77
--TextFormatter: End of Object--
TypeRef:23
63
Int32
Type
4
--TextFormatter: End of Object--
TypeRef:24
64
Int32
Type
0
--TextFormatter: End of Object--
TypeRef:25
65
Int32
mBusType
0
UInt32
mChannelIndex
65535
TypeRef:28
mSignalType
78
TypeRef:29
mSignalSource
79
TypeRef:30
mChannelMaskFR
80
--TextFormatter: End of Object--
TypeRef:27
67
Int32
Count
3
APPDIR Vector.CANalyzer.Scope.DLL
Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.Scope.Impl.CalculationStrategy`1[[Vector.CANalyzer.Scope.Interfaces.ECANCalculationType, Vector.CANalyzer.Scope, Version=********, Culture=neutral, PublicKeyToken=null]]
31
Item_0
81
TypeRef:31
Item_1
82
TypeRef:31
Item_2
83
--TextFormatter: End of Object--
TypeRef:28
71
Int32
Value
3
TypeRef:4
SerializationVersion
84
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:29
72
Int32
Value
1
TypeRef:4
SerializationVersion
85
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:30
73
Int32
mValue
0
TypeRef:4
SerializationVersion
86
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:28
75
Int32
Value
2
--TextFormatter: End of Object--
TypeRef:29
76
Int32
Value
1
--TextFormatter: End of Object--
TypeRef:30
77
Int32
mValue
0
--TextFormatter: End of Object--
TypeRef:28
78
Int32
Value
1
--TextFormatter: End of Object--
TypeRef:29
79
Int32
Value
3
--TextFormatter: End of Object--
TypeRef:30
80
Int32
mValue
0
--TextFormatter: End of Object--
TypeRef:31
81
Int32
Type
1
Boolean
ActiveState
True
APPDIR Components\Vector.CANalyzer.BasicTypes\1.2.7.0\Vector.CANalyzer.BasicTypes.dll
Vector.CANalyzer.BasicTypes, Version=1.2.7.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.BusType
32
busType
87
Int32
value__
1
--TextFormatter: End of Object--
TypeRef:4
SerializationVersion
88
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:31
82
Int32
Type
2
Boolean
ActiveState
False
TypeRef:32
busType
89
Int32
value__
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:31
83
Int32
Type
3
Boolean
ActiveState
False
TypeRef:32
busType
90
Int32
value__
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
CANBusyCalculation 1
CANDefaultILActivation 0
0
1
0
<End_of_SimulinkController>
StartOfComment
EndOfComment
8.1 SP3
VHILInterfaceMgrAnlyz 2 Begin_Of_Object
4
0
0
2809
0
3030
End_Of_Object VHILInterfaceMgrAnlyz 2
0
BasicDiagnosticsEditor 1
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 9 0 0 0 0 52 52 847 406
Basic Diagnostics
1

MDI_DOCK_INFO_END
5
1
6
0 9 0 0 -1 -1 52 52 847 406
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 319 138 1277 554
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 0
END_OF_DESKTOP_MEMBER
{186B109D-58AC-4AA5-B8CD-7C8644BFBE14}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
0
0
End_Of_Object VNETControlBox 3
31
APPDIR Vector.CANalyzer.BasicDiagnosticsEditor.DLL
Vector.CANalyzer.BasicDiagnosticsEditor, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.BasicDiagnosticsEditor.VBasicDiagnosticsEditorWrapper
1
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
1
IO::VIOConfiguration 2 Begin_Of_Object
1
2
<?xml version="1.0" encoding="UTF-16" standalone="yes" ?>
<ioConfiguration version="1">

  <configList>
    <config version="1">
      <ioPiggyConfig number="1" type="Disabled">
        <digitalPins>
          <pin mode="Disabled" name="DIO0"/>
          <pin mode="Disabled" name="DIO1"/>
          <pin mode="Disabled" name="DIO2"/>
          <pin mode="Disabled" name="DIO3"/>
          <pin mode="Disabled" name="DIO4"/>
          <pin mode="Disabled" name="DIO5"/>
          <pin mode="Disabled" name="DIO6"/>
          <pin mode="Disabled" name="DIO7"/>
        </digitalPins>
        <pwmPin>
          <pin mode="Disabled" name="PWM"/>
        </pwmPin>
        <analogPins>
          <pin mode="Disabled" name="AIO0"/>
          <pin mode="Disabled" name="AIO1"/>
          <pin mode="Disabled" name="AIO2"/>
          <pin mode="Disabled" name="AIO3"/>
        </analogPins>
        <digitalSettings>
          <highLevel>5V</highLevel>
          <inputThreshold>2.5</inputThreshold>
          <outputLowHoldTime>0.001</outputLowHoldTime>
          <outputHighHoldTime>0.001</outputHighHoldTime>
        </digitalSettings>
        <pwmSettings>
          <frequency>100</frequency>
          <dutyCycle>50</dutyCycle>
          <captureTimeout>1</captureTimeout>
        </pwmSettings>
        <triggerSettings>
          <cycleTime>100000</cycleTime>
          <digitalTriggerMask>DIO0</digitalTriggerMask>
          <digitalTriggerEdge>Rising</digitalTriggerEdge>
          <analogTriggerLevel>2.5</analogTriggerLevel>
          <analogTriggerEdge>Rising</analogTriggerEdge>
          <digitalAcquisition>Cyclic</digitalAcquisition>
          <pwmAcquisition>Cyclic</pwmAcquisition>
          <analogAcquisition>Cyclic</analogAcquisition>
        </triggerSettings>
      </ioPiggyConfig>
    </config>
  </configList>

</ioConfiguration>

End_Of_Serialized_Data 2
End_Of_Object IO::VIOConfiguration 2
CalculateExtendedStatistics 1
0
0
25
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.SymbolSelectionListBox.Data.SymbolMRUList
1
1
Int32
Count
0
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
J1939::VGlobalSettings 2 Begin_Of_Object
2
1
0
End_Of_Object J1939::VGlobalSettings 2
VNETStandaloneComponent 2 Begin_Of_Object
1
VNETControlBox 3 Begin_Of_Object
2
VUniqueBox 4 Begin_Of_Object
1
VBoxRoot 5 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 125 802 504
Start Values
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{CB5B5008-A918-42AF-9091-17F3B24B8971}
0
End_Of_Object VBoxRoot 5
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 4
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 3
258
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.StartValuesController
1
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValuesModel
3
StartValuesModel
3
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.GUISettings
4
GUISettings
4
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
5
SerializationVersion
5
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
TypeRef:3
3
Boolean
SetValuesOnMeasurementStart
True

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.Model.StartValue, Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null]]
6
StartValues
6
TypeRef:5
SerializationVersion
7
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:4
4

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Collections.Generic.List`1[[Vector.CANalyzer.StartValues.GUI.ColumnSettings, Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null]]
7
ColumnSettings
8
TypeRef:5
SerializationVersion
9
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:6
6
Array
_items
10
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.Model.StartValue
8
1
0
-1
Int32
_size
0
Int32
_version
0
--TextFormatter: End of Object--
TypeRef:7
8
Array
_items
11
APPDIR Vector.CANalyzer.StartValues.DLL
Vector.CANalyzer.StartValues, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.StartValues.GUI.ColumnSettings
9
1
0
7
TypeRef:9
ArrayElement
12
TypeRef:9
ArrayElement
13
TypeRef:9
ArrayElement
14
TypeRef:9
ArrayElement
15
TypeRef:9
ArrayElement
16
TypeRef:9
ArrayElement
17

mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
System.Object
10
ArrayElement
0
TypeRef:10
ArrayElement
0
Int32
_size
6
Int32
_version
6
--TextFormatter: End of Object--
TypeRef:9
12
Int32
mHandle
0
Int32
mDefaultWidth
25
Int32
mWidth
25
Int32
SortOrderInt
0
TypeRef:5
SerializationVersion
18
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
1
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:9
13
Int32
mHandle
1
Int32
mDefaultWidth
150
Int32
mWidth
150
Int32
SortOrderInt
0
--TextFormatter: End of Object--
TypeRef:9
14
Int32
mHandle
2
Int32
mDefaultWidth
75
Int32
mWidth
75
Int32
SortOrderInt
0
--TextFormatter: End of Object--
TypeRef:9
15
Int32
mHandle
3
Int32
mDefaultWidth
75
Int32
mWidth
75
Int32
SortOrderInt
0
--TextFormatter: End of Object--
TypeRef:9
16
Int32
mHandle
4
Int32
mDefaultWidth
75
Int32
mWidth
75
Int32
SortOrderInt
0
--TextFormatter: End of Object--
TypeRef:9
17
Int32
mHandle
5
Int32
mDefaultWidth
50
Int32
mWidth
50
Int32
SortOrderInt
0
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 2
VStandaloneLoggingUserConfig 2 Begin_Of_Object
1
0
VLogCfgData 3 Begin_Of_Object
5
1
1
0
1
0
0
0
0
1024
60
1
0
1
1
0
0
0
0
0
2
0
0
3
VLogExportPersister 4 Begin_Of_Object
3
1416
11060201
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
0
0
2
19
0.10000000000000001
1
0
End_Of_Object VLogExportPersister 4

End_Of_Serialized_Data 3
<VFileName V4 QL> 1 "CANOE.blf" 
0
0
0
10
80
0
End_Of_Object VLogCfgData 3
0
VAutoRunPreLoggingCaplBox 3 Begin_Of_Object
1
<VFileName V4 QL> 0 "" 
0
End_Of_Object VAutoRunPreLoggingCaplBox 3
End_Of_Object VStandaloneLoggingUserConfig 2
Mapping::VMappingManager 2 Begin_Of_Object
1
0
End_Of_Object Mapping::VMappingManager 2
VTSystemControl 0
TestConfigurationSetup
VTestConfigurationSetupWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 125 802 504
Test Configurations for Test Units
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{97E7E65F-662F-414F-BBC0-D425315D495A}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
22
APPDIR Vector.CANoe.TestConfigurationSetup.DLL
Vector.CANoe.TestConfigurationSetup, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANoe.TestConfigurationSetup.TestConfigurationSetup
1
1
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
2
SerializationVersion
2
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
End_Of_Object VTestConfigurationSetupWrapper 2
AFDXVLStatisticSysVars
NAFDX::NStatisticsMonitor::VSVClient 2 Begin_Of_Object
1
Begin_Of_Multi_Line_String
2
<?xml version="1.0" encoding="Windows-1252"?>
<systemvariables version="4" />
End_Of_Serialized_Data 2
End_Of_Object NAFDX::NStatisticsMonitor::VSVClient 2
DocumentViewer
VDocumentViewerWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 125 802 504
Documents
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{F35DBA59-40E9-4FF3-8E2C-B0E72BA8A9BE}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
37
APPDIR Vector.CANalyzer.DocumentViewer.DLL
Vector.CANalyzer.DocumentViewer, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.DocumentViewer.ComponentWrapper
1
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Boolean
SplitterExpanded
True
Int32
DocumentListHeight
150
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
0
0
End_Of_Object VDocumentViewerWrapper 2
AutomationSequences
VAutomationSequencesWrapper 2 Begin_Of_Object
1
VNETStandaloneComponent 3 Begin_Of_Object
1
VNETControlBox 4 Begin_Of_Object
2
VUniqueBox 5 Begin_Of_Object
1
VBoxRoot 6 Begin_Of_Object
1
3
1 -1 0 1 0 0 0 0 200 125 802 504
Automation Sequences
1

MDI_DOCK_INFO_END
5
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
0 1
1
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
6
0 1 0 0 -1 -1 200 125 802 504
6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 32767 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 0 
END_OF_DOCK_INFO
0
-1
0
0
0
0 0
END_OF_DESKTOP_DATA
END_OF_DESKTOP_DATA_COLLECTION
0
END_OF_DESKTOP_MEMBER
{4A7E899B-E112-4665-B091-669566A37B68}
0
End_Of_Object VBoxRoot 6
1 -1 0 0 0 0 0 0 0 0 0 0
End_Of_Object VUniqueBox 5
1
1 -1 0 0 0 0 0 0 0 0 0 0
1
End_Of_Object VNETControlBox 4
34
APPDIR Vector.CANalyzer.AutomationSequences.DLL
Vector.CANalyzer.AutomationSequences, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.AutomationSequences.ComponentWrapper
1
1
APPDIR CANoe_Net.DLL
CANoe_Net, Version=********, Culture=neutral, PublicKeyToken=null
Vector.CANalyzer.ApplicationSerializer
2
Application
2
Int32
SelectedTabPage
0
APPDIR Components\Vector.CANalyzer.Serialization\1.5.0.0\Vector.CANalyzer.Serialization.dll
Vector.CANalyzer.Serialization, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b273882a063429a6
Vector.CANalyzer.Serialization.SerializationVersion
3
SerializationVersion
3
UInt16
mMajor
1
UInt16
mMinor
0
UInt16
mPatch
0
--TextFormatter: End of Object--
--TextFormatter: End of Object--
TypeRef:2
2
--TextFormatter: End of Object--
End_Of_Object VNETStandaloneComponent 3
End_Of_Object VAutomationSequencesWrapper 2
LogFileConverter
VLogFileConverter 2 Begin_Of_Object
1
2
VLogExportPersister 3 Begin_Of_Object
3
1416
11060201
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
<VFileName V4 QL> 1 "" 
0
2
1
::
,
.

0
2
0
0.10000000000000001
6
0
0
2
19
0.10000000000000001
1
0
End_Of_Object VLogExportPersister 3

End_Of_Serialized_Data 2
End_Of_Object VLogFileConverter 2
End_Of_Object VGlobalConfiguration 1
