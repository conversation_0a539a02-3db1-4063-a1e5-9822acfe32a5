/*
 * File: rtw_shared_utils.h
 *
 * Code generated for Simulink model 'TractionMV_F4_Eldor'.
 *
 * Model version                  : 1.431
 * Simulink Coder version         : 8.3 (R2012b) 20-Jul-2012
 * TLC version                    : 8.3 (Jul 21 2012)
 * C/C++ source code generated on : Fri Nov 20 13:18:51 2015
 */

#ifndef RTW_HEADER_rtw_shared_utils_h_
#define RTW_HEADER_rtw_shared_utils_h_
#ifndef rtw_shared_utils_h_
# define rtw_shared_utils_h_

/* Shared utilities general include header file.*/
#include "look1_iflf_binlcapw.h"
#include "look1_iflf_binlcpw.h"
#include "look1_iflf_binlxpw.h"
#include "rtGetInf.h"
#include "rtGetNaN.h"
#include "rt_look1d32.h"
#include "rt_look32.h"
#include "rt_powf_snf.h"
#include "rtwtypes.h"

/* Shared utilities TFL include header file.*/
#include <math.h>
#include "rt_nonfinite.h"
#endif                                 /* rtw_shared_utils_h_ */
#endif                                 /* RTW_HEADER_rtw_shared_utils_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
