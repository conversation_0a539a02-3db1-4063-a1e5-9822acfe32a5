/*
 * File: idxctfctrl_out.h
 *
 * Code generated for Simulink model 'IdxCtfCtrl'.
 *
 * Model version                  : 1.469
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Mar 30 08:55:55 2020
 */

#ifndef RTW_HEADER_idxctfctrl_out_h_
#define RTW_HEADER_idxctfctrl_out_h_
#include "rtwtypes.h"

/* Exported data define */
/* Definition for custom storage class: Define */
#define CTF_NUM_COIDX                  7U

/* Max plaier */
#define CTF_NUM_COSAMP                 11U

/* Max pattern */

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T CntCtfTDC;

/* counter */
extern uint8_T IdxCtfFlg;

/* IdxCtfCtrl */
extern uint8_T IdxCutoff;

/* IdxCtfCtrl */
extern uint8_T IdxSpareCutOff;

/* IdxCtfCtrl */
extern uint8_T IdxSpareCutOff0;

/* IdxCtfCtrl */
extern uint8_T PatternCtfTDC;

/* counter */
extern uint8_T VtIdxCtfFlg[4];

/* Cutoff selective */
extern uint8_T VtIdxCtfFlgBuff[4];

/* Cutoff selective */
#endif                                 /* RTW_HEADER_idxctfctrl_out_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
