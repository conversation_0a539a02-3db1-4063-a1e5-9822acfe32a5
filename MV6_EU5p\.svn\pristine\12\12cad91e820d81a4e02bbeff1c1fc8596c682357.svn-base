/*  File    : ionknock.h
 *  Author  : <PERSON> & <PERSON>
 *  Date    : 31/01/2007 11.14
 *  Revision: IonKnock 3.5
 *	Note	  : nuova scalatura IonBuffer e rimosso filtro.
 *
 *  Copyright 2006 Eldor Corporation
 */
#ifndef _IONKNOCK_H_
#define _IONKNOCK_H_

/** include files **/
#include "typedefs.h"
#include "ETPU_EngineDefs.h"
#include "ionacq.h"
#include "ion_misf.h"
#include "ionmgm.h"

/* Compilazione spike detector solo per applicazioni EI 
    AL MOMENTO E' SEMPRE COMPILATO */
#if ((ENGINE_TYPE == FE_142_8C) || (ENGINE_TYPE == FE_151_12C) || (ENGINE_TYPE == FE_A1GP_8C) || (ENGINE_TYPE == FE_599_12C_TDN) || (ENGINE_TYPE == LA_L539_12C))
#define EN_SPIKEDETECT_IONKNOCK
#else
#define EN_SPIKEDETECT_IONKNOCK
#endif

/** definitions **/
/* Definizione costanti */
#define	FFT_LOG2_SAMPLE		7
#define	FFT_SAMPLE			(1<<FFT_LOG2_SAMPLE)
#define	FFT_SAMPLE_DIV2		(FFT_SAMPLE/2)
#define	FFT_SAMPLE_D2_M1	(FFT_SAMPLE_DIV2-1)
#define FFT_ONLY_REAL		// Commentare per ottenere FFT di numeri complessi

#ifndef	FFT_ONLY_REAL
#define	FFT_BUFF_SIZE	(2*FFT_SAMPLE+2)
#else
#define	FFT_BUFF_SIZE	(FFT_SAMPLE+3)
#endif

/** definizioni prelevate da "Ionknock_private.h"  *************/

// knock_states:
#define NO_KNOCK		0U
#define WAIT_KNOCK		1U
#define ACTIVE_KNOCK		2U
#define WAIT_HEAVY_KNOCK	3U
#define HEAVY_KNOCK		4U

/* Dimensioni Vettori di Calibrazione */
#define	BKRPMIONKNOCK_dim	12
#define	BKLOADIONKNOCK_dim	5
#define	BKTAIRION_dim 		4
#define	BKTWATION_dim 		6
#define	BKIONSQRT_dim		21
#define	BKRPMKNOCK4_dim		7
#define BKRPMKNOCK4F_dim	7
#define BKRPMHKNOCKGAIN_dim	9
#define BKRPMDERDT_dim      4
#define BKRPMTHRPKREC_dim   20

/** default settings **/

/** external functions **/

/** external data **/

/** internal functions **/

/** public data **/
/* Output Variables */
extern uint32_T KnockInt[N_CYL_MAX];
extern int8_T 	KnockCnt[N_CYL_MAX];
extern int16_T  DeltaKnockNPow[N_CYL_MAX];
extern uint8_T  KnockState[N_CYL_MAX];
extern uint8_T  FlgHeavyKnock[N_CYL_MAX];
extern uint8_T  IonKnockEnabled;
extern uint8_T  TypeKnockDist;
extern uint16_T IdStartDist;
extern uint16_T IdStopDist;
extern int16_T  StartFftKnockId;
extern uint16_T IonBufferMod[FFT_SAMPLE];

/* Testpoint */
extern uint32_T ThrIntKnock;
extern uint16_T NSampIonPower;
extern int16_T  NfThPeakId;
extern uint32_T IonKnockPow;
extern uint32_T IonPow;
extern int32_T  KnockIntF[N_CYL_MAX];
extern int8_T	HKnockCnt[N_CYL_MAX];
extern uint16_T BaseFreq;			// [Hz]		Range 0..65535 Res 2^0
extern uint16_T IdFrqStart1;			// [counter]	Range 0..65535 Res 2^0
extern uint16_T IdFrqRange1;			// [counter]	Range 0..65535 Res 2^0
extern uint16_T IdFrqStart2;			// [counter]	Range 0..65535 Res 2^0
extern uint16_T IdFrqRange2;			// [counter]	Range 0..65535 Res 2^0
extern uint32_T FftPeak[FFT_SAMPLE_DIV2];
extern uint32_T FftMag2Int1;			// [coeff]	Range 0..2^22  Res 2^-10
extern uint32_T FftMag2Int2;			// [coeff]	Range 0..2^22  Res 2^-10
extern uint32_T KnPowNormFft;
extern int32_T	FftTest[];
extern uint32_T	HKnockIntThr;
extern uint32_T IonSampFreq;

/* Imported (extern) block signals */
extern uint16_T ThPeakId;               /* '<Root>/ThPeakId' */
extern uint16_T Start_Th[];
extern int16_T  TWater;                  /* '<Root>/TWater' */
extern int16_T  TAir;                    /* '<Root>/TAir' */
extern uint8_T  IonAbsTdc;                  /* '<Root>/AbsTdc' */
extern uint8_T	StMisf[];
extern uint16_T IntIon[];
extern uint16_T ThInt[];

/** private data **/

/** public functions **/
void IonKnock_Init(void);
void IonKnock_EOA(void);

#endif
